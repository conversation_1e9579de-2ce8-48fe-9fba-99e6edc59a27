2025-05-16 08:00:03,621 - INFO - ==================================================
2025-05-16 08:00:03,621 - INFO - 程序启动 - 版本 v1.0.0
2025-05-16 08:00:03,621 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250516.log
2025-05-16 08:00:03,621 - INFO - ==================================================
2025-05-16 08:00:03,621 - INFO - 程序入口点: __main__
2025-05-16 08:00:03,621 - INFO - ==================================================
2025-05-16 08:00:03,621 - INFO - 程序启动 - 版本 v1.0.1
2025-05-16 08:00:03,621 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250516.log
2025-05-16 08:00:03,621 - INFO - ==================================================
2025-05-16 08:00:03,949 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-16 08:00:03,949 - INFO - sales_data表已存在，无需创建
2025-05-16 08:00:03,949 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-16 08:00:03,949 - INFO - DataSyncManager初始化完成
2025-05-16 08:00:03,949 - INFO - 未提供日期参数，使用默认值
2025-05-16 08:00:03,949 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-16 08:00:03,949 - INFO - 开始综合数据同步流程...
2025-05-16 08:00:03,949 - INFO - 正在获取数衍平台日销售数据...
2025-05-16 08:00:03,949 - INFO - 查询数衍平台数据，时间段为: 2025-03-16, 2025-05-15
2025-05-16 08:00:03,949 - INFO - 正在获取********至********的数据
2025-05-16 08:00:03,949 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:03,949 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1D53E951D09D23E072102A4B369518DE'}
2025-05-16 08:00:06,777 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:06,792 - INFO - 过滤后保留 1554 条记录
2025-05-16 08:00:08,808 - INFO - 正在获取********至********的数据
2025-05-16 08:00:08,808 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:08,808 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9C1F1CEFB28E991FF72E3D9854892C52'}
2025-05-16 08:00:11,511 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:11,527 - INFO - 过滤后保留 1564 条记录
2025-05-16 08:00:13,542 - INFO - 正在获取********至********的数据
2025-05-16 08:00:13,542 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:13,542 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '646137ED26B29B48668CBA6588079C1B'}
2025-05-16 08:00:15,339 - INFO - Response: {'rescode': 'OPEN_FAIL', 'resmsg': '调用内部服务异常:java.lang.RuntimeException: java.net.SocketException: Connection reset'}
2025-05-16 08:00:15,355 - ERROR - 获取********至********的数据失败: {'rescode': 'OPEN_FAIL', 'resmsg': '调用内部服务异常:java.lang.RuntimeException: java.net.SocketException: Connection reset'}
2025-05-16 08:00:17,370 - INFO - 正在获取********至********的数据
2025-05-16 08:00:17,370 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:17,370 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4E5713F6A454330DD9A1F3F536BBD7E1'}
2025-05-16 08:00:19,511 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:19,527 - INFO - 过滤后保留 1496 条记录
2025-05-16 08:00:21,542 - INFO - 正在获取********至********的数据
2025-05-16 08:00:21,542 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:21,542 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '637D00046FE1D4BBDE3C787B7F333A80'}
2025-05-16 08:00:23,308 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:23,324 - INFO - 过滤后保留 1509 条记录
2025-05-16 08:00:25,339 - INFO - 正在获取********至********的数据
2025-05-16 08:00:25,339 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:25,339 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '67FE111C7355C302116551D799454059'}
2025-05-16 08:00:27,011 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:27,027 - INFO - 过滤后保留 1482 条记录
2025-05-16 08:00:29,042 - INFO - 正在获取********至********的数据
2025-05-16 08:00:29,042 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:29,042 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5F54A4A48289EBAC7987AB0EA7BFBCA9'}
2025-05-16 08:00:30,870 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:30,886 - INFO - 过滤后保留 1492 条记录
2025-05-16 08:00:32,902 - INFO - 正在获取********至********的数据
2025-05-16 08:00:32,902 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:32,902 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DBDAD5D87A6B485EF088BFD0DB32B5E5'}
2025-05-16 08:00:34,714 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:34,730 - INFO - 过滤后保留 1470 条记录
2025-05-16 08:00:36,745 - INFO - 正在获取********至********的数据
2025-05-16 08:00:36,745 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-16 08:00:36,745 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2902D2C60B047B8BE6D42D7B6BDDEA1B'}
2025-05-16 08:00:38,292 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-16 08:00:38,308 - INFO - 过滤后保留 1055 条记录
2025-05-16 08:00:40,323 - INFO - 开始保存数据到SQLite数据库，共 11622 条记录待处理
2025-05-16 08:00:40,620 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-04-11
2025-05-16 08:00:40,620 - INFO - 变更字段: amount: 5433 -> 7856, count: 7 -> 8, instore_amount: 5433.4 -> 7856.8, instore_count: 7 -> 8
2025-05-16 08:00:40,683 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-04-13
2025-05-16 08:00:40,683 - INFO - 变更字段: amount: 301 -> 1167, count: 1 -> 2, instore_amount: 301.0 -> 1167.0, instore_count: 1 -> 2
2025-05-16 08:00:40,886 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-14
2025-05-16 08:00:40,886 - INFO - 变更字段: recommend_amount: 0.0 -> 7585.4, daily_bill_amount: 0.0 -> 7585.4
2025-05-16 08:00:40,902 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-14
2025-05-16 08:00:40,902 - INFO - 变更字段: recommend_amount: 0.0 -> 5985.25, daily_bill_amount: 0.0 -> 5985.25
2025-05-16 08:00:40,917 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-14
2025-05-16 08:00:40,917 - INFO - 变更字段: amount: 2367 -> 2774, count: 4 -> 5, instore_amount: 2367.0 -> 2774.0, instore_count: 4 -> 5
2025-05-16 08:00:40,917 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-05-14
2025-05-16 08:00:40,917 - INFO - 变更字段: recommend_amount: 0.0 -> 1779.0, daily_bill_amount: 0.0 -> 1779.0
2025-05-16 08:00:40,917 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-14
2025-05-16 08:00:40,917 - INFO - 变更字段: recommend_amount: 6647.49 -> 6711.09, amount: 6647 -> 6711, count: 134 -> 135, instore_amount: 6500.59 -> 6564.19, instore_count: 130 -> 131
2025-05-16 08:00:40,917 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-14
2025-05-16 08:00:40,917 - INFO - 变更字段: recommend_amount: 0.0 -> 16517.27, daily_bill_amount: 0.0 -> 16517.27
2025-05-16 08:00:40,933 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-14
2025-05-16 08:00:40,933 - INFO - 变更字段: instore_amount: 5564.96 -> 5572.88, instore_count: 372 -> 373, online_amount: 1399.89 -> 1391.97, online_count: 88 -> 87
2025-05-16 08:00:40,933 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-05-14
2025-05-16 08:00:40,933 - INFO - 变更字段: amount: 1519 -> 1528, count: 42 -> 43, instore_amount: 1151.21 -> 1160.04, instore_count: 33 -> 34
2025-05-16 08:00:40,948 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-13
2025-05-16 08:00:40,948 - INFO - 变更字段: recommend_amount: 0.0 -> 4713.15, daily_bill_amount: 0.0 -> 4713.15
2025-05-16 08:00:41,167 - INFO - SQLite数据保存完成，统计信息：
2025-05-16 08:00:41,167 - INFO - - 总记录数: 11622
2025-05-16 08:00:41,167 - INFO - - 成功插入: 208
2025-05-16 08:00:41,167 - INFO - - 成功更新: 11
2025-05-16 08:00:41,167 - INFO - - 无需更新: 11403
2025-05-16 08:00:41,167 - INFO - - 处理失败: 0
2025-05-16 08:00:46,105 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250516.xlsx
2025-05-16 08:00:46,105 - INFO - 成功获取数衍平台数据，共 11622 条记录
2025-05-16 08:00:46,105 - INFO - 正在更新SQLite月度汇总数据...
2025-05-16 08:00:46,120 - INFO - 月度数据sqllite清空完成
2025-05-16 08:00:46,370 - INFO - 月度汇总数据更新完成，处理了 1189 条汇总记录
2025-05-16 08:00:46,370 - INFO - 成功更新月度汇总数据，共 1189 条记录
2025-05-16 08:00:46,370 - INFO - 正在获取宜搭日销售表单数据...
2025-05-16 08:00:46,370 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-16 00:00:00 至 2025-05-15 23:59:59
2025-05-16 08:00:46,370 - INFO - 查询分段 1: 2025-03-16 至 2025-03-22
2025-05-16 08:00:46,370 - INFO - 查询日期范围: 2025-03-16 至 2025-03-22，使用分页查询，每页 100 条记录
2025-05-16 08:00:46,370 - INFO - Request Parameters - Page 1:
2025-05-16 08:00:46,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:46,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:49,120 - INFO - API请求耗时: 2750ms
2025-05-16 08:00:49,120 - INFO - Response - Page 1
2025-05-16 08:00:49,136 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:00:49,652 - INFO - Request Parameters - Page 2:
2025-05-16 08:00:49,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:49,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:53,542 - INFO - API请求耗时: 3891ms
2025-05-16 08:00:53,542 - INFO - Response - Page 2
2025-05-16 08:00:53,542 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:00:54,058 - INFO - Request Parameters - Page 3:
2025-05-16 08:00:54,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:54,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:54,698 - INFO - API请求耗时: 641ms
2025-05-16 08:00:54,698 - INFO - Response - Page 3
2025-05-16 08:00:54,698 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:00:55,198 - INFO - Request Parameters - Page 4:
2025-05-16 08:00:55,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:55,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:55,948 - INFO - API请求耗时: 750ms
2025-05-16 08:00:55,948 - INFO - Response - Page 4
2025-05-16 08:00:55,948 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:00:56,464 - INFO - Request Parameters - Page 5:
2025-05-16 08:00:56,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:56,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:57,089 - INFO - API请求耗时: 625ms
2025-05-16 08:00:57,089 - INFO - Response - Page 5
2025-05-16 08:00:57,089 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:00:57,589 - INFO - Request Parameters - Page 6:
2025-05-16 08:00:57,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:57,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:58,167 - INFO - API请求耗时: 578ms
2025-05-16 08:00:58,167 - INFO - Response - Page 6
2025-05-16 08:00:58,167 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:00:58,667 - INFO - Request Parameters - Page 7:
2025-05-16 08:00:58,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:00:58,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:00:59,542 - INFO - API请求耗时: 875ms
2025-05-16 08:00:59,542 - INFO - Response - Page 7
2025-05-16 08:00:59,542 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:01:00,042 - INFO - Request Parameters - Page 8:
2025-05-16 08:01:00,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:00,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:00,683 - INFO - API请求耗时: 641ms
2025-05-16 08:01:00,683 - INFO - Response - Page 8
2025-05-16 08:01:00,683 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:01:01,183 - INFO - Request Parameters - Page 9:
2025-05-16 08:01:01,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:01,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:01,870 - INFO - API请求耗时: 687ms
2025-05-16 08:01:01,870 - INFO - Response - Page 9
2025-05-16 08:01:01,870 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:01:02,386 - INFO - Request Parameters - Page 10:
2025-05-16 08:01:02,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:02,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:04,058 - INFO - API请求耗时: 1672ms
2025-05-16 08:01:04,058 - INFO - Response - Page 10
2025-05-16 08:01:04,058 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:01:04,558 - INFO - Request Parameters - Page 11:
2025-05-16 08:01:04,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:04,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:05,183 - INFO - API请求耗时: 625ms
2025-05-16 08:01:05,183 - INFO - Response - Page 11
2025-05-16 08:01:05,183 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:01:05,683 - INFO - Request Parameters - Page 12:
2025-05-16 08:01:05,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:05,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:06,276 - INFO - API请求耗时: 594ms
2025-05-16 08:01:06,276 - INFO - Response - Page 12
2025-05-16 08:01:06,276 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:01:06,776 - INFO - Request Parameters - Page 13:
2025-05-16 08:01:06,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:06,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:07,386 - INFO - API请求耗时: 609ms
2025-05-16 08:01:07,386 - INFO - Response - Page 13
2025-05-16 08:01:07,386 - INFO - 第 13 页获取到 100 条记录
2025-05-16 08:01:07,886 - INFO - Request Parameters - Page 14:
2025-05-16 08:01:07,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:07,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400370, 1742572800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:08,370 - INFO - API请求耗时: 484ms
2025-05-16 08:01:08,370 - INFO - Response - Page 14
2025-05-16 08:01:08,370 - INFO - 第 14 页获取到 34 条记录
2025-05-16 08:01:08,370 - INFO - 查询完成，共获取到 1334 条记录
2025-05-16 08:01:08,370 - INFO - 分段 1 查询成功，获取到 1334 条记录
2025-05-16 08:01:09,370 - INFO - 查询分段 2: 2025-03-23 至 2025-03-29
2025-05-16 08:01:09,370 - INFO - 查询日期范围: 2025-03-23 至 2025-03-29，使用分页查询，每页 100 条记录
2025-05-16 08:01:09,370 - INFO - Request Parameters - Page 1:
2025-05-16 08:01:09,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:09,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:10,026 - INFO - API请求耗时: 656ms
2025-05-16 08:01:10,026 - INFO - Response - Page 1
2025-05-16 08:01:10,026 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:01:10,526 - INFO - Request Parameters - Page 2:
2025-05-16 08:01:10,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:10,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:11,151 - INFO - API请求耗时: 625ms
2025-05-16 08:01:11,151 - INFO - Response - Page 2
2025-05-16 08:01:11,151 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:01:11,667 - INFO - Request Parameters - Page 3:
2025-05-16 08:01:11,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:11,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:12,339 - INFO - API请求耗时: 672ms
2025-05-16 08:01:12,339 - INFO - Response - Page 3
2025-05-16 08:01:12,339 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:01:12,839 - INFO - Request Parameters - Page 4:
2025-05-16 08:01:12,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:12,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:13,433 - INFO - API请求耗时: 594ms
2025-05-16 08:01:13,433 - INFO - Response - Page 4
2025-05-16 08:01:13,433 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:01:13,933 - INFO - Request Parameters - Page 5:
2025-05-16 08:01:13,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:13,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:14,620 - INFO - API请求耗时: 688ms
2025-05-16 08:01:14,620 - INFO - Response - Page 5
2025-05-16 08:01:14,620 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:01:15,136 - INFO - Request Parameters - Page 6:
2025-05-16 08:01:15,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:15,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:15,761 - INFO - API请求耗时: 625ms
2025-05-16 08:01:15,761 - INFO - Response - Page 6
2025-05-16 08:01:15,761 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:01:16,261 - INFO - Request Parameters - Page 7:
2025-05-16 08:01:16,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:16,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:16,870 - INFO - API请求耗时: 609ms
2025-05-16 08:01:16,870 - INFO - Response - Page 7
2025-05-16 08:01:16,870 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:01:17,370 - INFO - Request Parameters - Page 8:
2025-05-16 08:01:17,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:17,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:17,979 - INFO - API请求耗时: 609ms
2025-05-16 08:01:17,979 - INFO - Response - Page 8
2025-05-16 08:01:17,979 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:01:18,495 - INFO - Request Parameters - Page 9:
2025-05-16 08:01:18,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:18,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:19,089 - INFO - API请求耗时: 594ms
2025-05-16 08:01:19,089 - INFO - Response - Page 9
2025-05-16 08:01:19,089 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:01:19,604 - INFO - Request Parameters - Page 10:
2025-05-16 08:01:19,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:19,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:20,261 - INFO - API请求耗时: 656ms
2025-05-16 08:01:20,261 - INFO - Response - Page 10
2025-05-16 08:01:20,261 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:01:20,776 - INFO - Request Parameters - Page 11:
2025-05-16 08:01:20,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:20,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:21,495 - INFO - API请求耗时: 719ms
2025-05-16 08:01:21,495 - INFO - Response - Page 11
2025-05-16 08:01:21,495 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:01:21,995 - INFO - Request Parameters - Page 12:
2025-05-16 08:01:21,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:21,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:22,698 - INFO - API请求耗时: 703ms
2025-05-16 08:01:22,698 - INFO - Response - Page 12
2025-05-16 08:01:22,698 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:01:23,198 - INFO - Request Parameters - Page 13:
2025-05-16 08:01:23,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:23,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:23,964 - INFO - API请求耗时: 766ms
2025-05-16 08:01:23,964 - INFO - Response - Page 13
2025-05-16 08:01:23,964 - INFO - 第 13 页获取到 100 条记录
2025-05-16 08:01:24,464 - INFO - Request Parameters - Page 14:
2025-05-16 08:01:24,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:24,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200370, 1743177600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:24,948 - INFO - API请求耗时: 484ms
2025-05-16 08:01:24,948 - INFO - Response - Page 14
2025-05-16 08:01:24,948 - INFO - 第 14 页获取到 37 条记录
2025-05-16 08:01:24,948 - INFO - 查询完成，共获取到 1337 条记录
2025-05-16 08:01:24,948 - INFO - 分段 2 查询成功，获取到 1337 条记录
2025-05-16 08:01:25,964 - INFO - 查询分段 3: 2025-03-30 至 2025-04-05
2025-05-16 08:01:25,964 - INFO - 查询日期范围: 2025-03-30 至 2025-04-05，使用分页查询，每页 100 条记录
2025-05-16 08:01:25,964 - INFO - Request Parameters - Page 1:
2025-05-16 08:01:25,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:25,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:26,589 - INFO - API请求耗时: 625ms
2025-05-16 08:01:26,589 - INFO - Response - Page 1
2025-05-16 08:01:26,589 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:01:27,089 - INFO - Request Parameters - Page 2:
2025-05-16 08:01:27,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:27,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:27,714 - INFO - API请求耗时: 625ms
2025-05-16 08:01:27,714 - INFO - Response - Page 2
2025-05-16 08:01:27,714 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:01:28,229 - INFO - Request Parameters - Page 3:
2025-05-16 08:01:28,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:28,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:28,917 - INFO - API请求耗时: 687ms
2025-05-16 08:01:28,917 - INFO - Response - Page 3
2025-05-16 08:01:28,917 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:01:29,417 - INFO - Request Parameters - Page 4:
2025-05-16 08:01:29,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:29,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:30,058 - INFO - API请求耗时: 641ms
2025-05-16 08:01:30,058 - INFO - Response - Page 4
2025-05-16 08:01:30,058 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:01:30,573 - INFO - Request Parameters - Page 5:
2025-05-16 08:01:30,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:30,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:31,292 - INFO - API请求耗时: 719ms
2025-05-16 08:01:31,292 - INFO - Response - Page 5
2025-05-16 08:01:31,292 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:01:31,808 - INFO - Request Parameters - Page 6:
2025-05-16 08:01:31,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:31,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:32,448 - INFO - API请求耗时: 641ms
2025-05-16 08:01:32,448 - INFO - Response - Page 6
2025-05-16 08:01:32,448 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:01:32,964 - INFO - Request Parameters - Page 7:
2025-05-16 08:01:32,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:32,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:33,636 - INFO - API请求耗时: 672ms
2025-05-16 08:01:33,636 - INFO - Response - Page 7
2025-05-16 08:01:33,636 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:01:34,151 - INFO - Request Parameters - Page 8:
2025-05-16 08:01:34,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:34,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:34,745 - INFO - API请求耗时: 594ms
2025-05-16 08:01:34,745 - INFO - Response - Page 8
2025-05-16 08:01:34,745 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:01:35,245 - INFO - Request Parameters - Page 9:
2025-05-16 08:01:35,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:35,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:35,948 - INFO - API请求耗时: 703ms
2025-05-16 08:01:35,948 - INFO - Response - Page 9
2025-05-16 08:01:35,948 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:01:36,464 - INFO - Request Parameters - Page 10:
2025-05-16 08:01:36,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:36,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:37,198 - INFO - API请求耗时: 734ms
2025-05-16 08:01:37,198 - INFO - Response - Page 10
2025-05-16 08:01:37,198 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:01:37,698 - INFO - Request Parameters - Page 11:
2025-05-16 08:01:37,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:37,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:38,323 - INFO - API请求耗时: 625ms
2025-05-16 08:01:38,323 - INFO - Response - Page 11
2025-05-16 08:01:38,323 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:01:38,839 - INFO - Request Parameters - Page 12:
2025-05-16 08:01:38,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:38,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:39,464 - INFO - API请求耗时: 625ms
2025-05-16 08:01:39,464 - INFO - Response - Page 12
2025-05-16 08:01:39,464 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:01:39,979 - INFO - Request Parameters - Page 13:
2025-05-16 08:01:39,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:39,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:40,745 - INFO - API请求耗时: 766ms
2025-05-16 08:01:40,745 - INFO - Response - Page 13
2025-05-16 08:01:40,745 - INFO - 第 13 页获取到 100 条记录
2025-05-16 08:01:41,245 - INFO - Request Parameters - Page 14:
2025-05-16 08:01:41,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:41,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000370, 1743782400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:41,542 - INFO - API请求耗时: 297ms
2025-05-16 08:01:41,542 - INFO - Response - Page 14
2025-05-16 08:01:41,542 - INFO - 第 14 页没有数据，已到达最后一页
2025-05-16 08:01:41,542 - INFO - 查询完成，共获取到 1300 条记录
2025-05-16 08:01:41,542 - INFO - 分段 3 查询成功，获取到 1300 条记录
2025-05-16 08:01:42,542 - INFO - 查询分段 4: 2025-04-06 至 2025-04-12
2025-05-16 08:01:42,542 - INFO - 查询日期范围: 2025-04-06 至 2025-04-12，使用分页查询，每页 100 条记录
2025-05-16 08:01:42,542 - INFO - Request Parameters - Page 1:
2025-05-16 08:01:42,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:42,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:43,182 - INFO - API请求耗时: 641ms
2025-05-16 08:01:43,182 - INFO - Response - Page 1
2025-05-16 08:01:43,182 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:01:43,682 - INFO - Request Parameters - Page 2:
2025-05-16 08:01:43,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:43,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:44,292 - INFO - API请求耗时: 609ms
2025-05-16 08:01:44,292 - INFO - Response - Page 2
2025-05-16 08:01:44,292 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:01:44,807 - INFO - Request Parameters - Page 3:
2025-05-16 08:01:44,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:44,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:45,448 - INFO - API请求耗时: 641ms
2025-05-16 08:01:45,448 - INFO - Response - Page 3
2025-05-16 08:01:45,448 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:01:45,948 - INFO - Request Parameters - Page 4:
2025-05-16 08:01:45,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:45,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:46,526 - INFO - API请求耗时: 578ms
2025-05-16 08:01:46,526 - INFO - Response - Page 4
2025-05-16 08:01:46,526 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:01:47,026 - INFO - Request Parameters - Page 5:
2025-05-16 08:01:47,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:47,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:47,667 - INFO - API请求耗时: 641ms
2025-05-16 08:01:47,667 - INFO - Response - Page 5
2025-05-16 08:01:47,667 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:01:48,182 - INFO - Request Parameters - Page 6:
2025-05-16 08:01:48,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:48,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:48,792 - INFO - API请求耗时: 609ms
2025-05-16 08:01:48,792 - INFO - Response - Page 6
2025-05-16 08:01:48,807 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:01:49,323 - INFO - Request Parameters - Page 7:
2025-05-16 08:01:49,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:49,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:49,979 - INFO - API请求耗时: 656ms
2025-05-16 08:01:49,979 - INFO - Response - Page 7
2025-05-16 08:01:49,979 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:01:50,479 - INFO - Request Parameters - Page 8:
2025-05-16 08:01:50,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:50,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:51,136 - INFO - API请求耗时: 656ms
2025-05-16 08:01:51,136 - INFO - Response - Page 8
2025-05-16 08:01:51,136 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:01:51,651 - INFO - Request Parameters - Page 9:
2025-05-16 08:01:51,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:51,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:52,229 - INFO - API请求耗时: 578ms
2025-05-16 08:01:52,229 - INFO - Response - Page 9
2025-05-16 08:01:52,229 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:01:52,729 - INFO - Request Parameters - Page 10:
2025-05-16 08:01:52,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:52,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:53,432 - INFO - API请求耗时: 703ms
2025-05-16 08:01:53,432 - INFO - Response - Page 10
2025-05-16 08:01:53,432 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:01:53,932 - INFO - Request Parameters - Page 11:
2025-05-16 08:01:53,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:53,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:54,542 - INFO - API请求耗时: 609ms
2025-05-16 08:01:54,542 - INFO - Response - Page 11
2025-05-16 08:01:54,542 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:01:55,057 - INFO - Request Parameters - Page 12:
2025-05-16 08:01:55,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:55,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:55,651 - INFO - API请求耗时: 594ms
2025-05-16 08:01:55,651 - INFO - Response - Page 12
2025-05-16 08:01:55,651 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:01:56,151 - INFO - Request Parameters - Page 13:
2025-05-16 08:01:56,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:56,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800370, 1744387200370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:56,745 - INFO - API请求耗时: 594ms
2025-05-16 08:01:56,745 - INFO - Response - Page 13
2025-05-16 08:01:56,745 - INFO - 第 13 页获取到 81 条记录
2025-05-16 08:01:56,745 - INFO - 查询完成，共获取到 1281 条记录
2025-05-16 08:01:56,745 - INFO - 分段 4 查询成功，获取到 1281 条记录
2025-05-16 08:01:57,745 - INFO - 查询分段 5: 2025-04-13 至 2025-04-19
2025-05-16 08:01:57,745 - INFO - 查询日期范围: 2025-04-13 至 2025-04-19，使用分页查询，每页 100 条记录
2025-05-16 08:01:57,745 - INFO - Request Parameters - Page 1:
2025-05-16 08:01:57,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:57,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:58,464 - INFO - API请求耗时: 719ms
2025-05-16 08:01:58,464 - INFO - Response - Page 1
2025-05-16 08:01:58,464 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:01:58,964 - INFO - Request Parameters - Page 2:
2025-05-16 08:01:58,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:01:58,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:01:59,604 - INFO - API请求耗时: 641ms
2025-05-16 08:01:59,604 - INFO - Response - Page 2
2025-05-16 08:01:59,604 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:02:00,104 - INFO - Request Parameters - Page 3:
2025-05-16 08:02:00,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:00,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:00,667 - INFO - API请求耗时: 562ms
2025-05-16 08:02:00,667 - INFO - Response - Page 3
2025-05-16 08:02:00,667 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:02:01,167 - INFO - Request Parameters - Page 4:
2025-05-16 08:02:01,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:01,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:01,698 - INFO - API请求耗时: 531ms
2025-05-16 08:02:01,698 - INFO - Response - Page 4
2025-05-16 08:02:01,714 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:02:02,214 - INFO - Request Parameters - Page 5:
2025-05-16 08:02:02,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:02,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:02,917 - INFO - API请求耗时: 703ms
2025-05-16 08:02:02,917 - INFO - Response - Page 5
2025-05-16 08:02:02,917 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:02:03,432 - INFO - Request Parameters - Page 6:
2025-05-16 08:02:03,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:03,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:04,057 - INFO - API请求耗时: 625ms
2025-05-16 08:02:04,057 - INFO - Response - Page 6
2025-05-16 08:02:04,057 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:02:04,573 - INFO - Request Parameters - Page 7:
2025-05-16 08:02:04,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:04,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:05,151 - INFO - API请求耗时: 578ms
2025-05-16 08:02:05,151 - INFO - Response - Page 7
2025-05-16 08:02:05,151 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:02:05,651 - INFO - Request Parameters - Page 8:
2025-05-16 08:02:05,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:05,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:06,307 - INFO - API请求耗时: 656ms
2025-05-16 08:02:06,307 - INFO - Response - Page 8
2025-05-16 08:02:06,307 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:02:06,823 - INFO - Request Parameters - Page 9:
2025-05-16 08:02:06,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:06,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:07,448 - INFO - API请求耗时: 625ms
2025-05-16 08:02:07,448 - INFO - Response - Page 9
2025-05-16 08:02:07,448 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:02:07,948 - INFO - Request Parameters - Page 10:
2025-05-16 08:02:07,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:07,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:08,620 - INFO - API请求耗时: 672ms
2025-05-16 08:02:08,620 - INFO - Response - Page 10
2025-05-16 08:02:08,620 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:02:09,135 - INFO - Request Parameters - Page 11:
2025-05-16 08:02:09,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:09,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:09,714 - INFO - API请求耗时: 578ms
2025-05-16 08:02:09,714 - INFO - Response - Page 11
2025-05-16 08:02:09,714 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:02:10,214 - INFO - Request Parameters - Page 12:
2025-05-16 08:02:10,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:10,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:10,917 - INFO - API请求耗时: 703ms
2025-05-16 08:02:10,917 - INFO - Response - Page 12
2025-05-16 08:02:10,917 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:02:11,432 - INFO - Request Parameters - Page 13:
2025-05-16 08:02:11,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:11,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600370, 1744992000370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:12,057 - INFO - API请求耗时: 625ms
2025-05-16 08:02:12,057 - INFO - Response - Page 13
2025-05-16 08:02:12,057 - INFO - 第 13 页获取到 90 条记录
2025-05-16 08:02:12,057 - INFO - 查询完成，共获取到 1290 条记录
2025-05-16 08:02:12,057 - INFO - 分段 5 查询成功，获取到 1290 条记录
2025-05-16 08:02:13,057 - INFO - 查询分段 6: 2025-04-20 至 2025-04-26
2025-05-16 08:02:13,057 - INFO - 查询日期范围: 2025-04-20 至 2025-04-26，使用分页查询，每页 100 条记录
2025-05-16 08:02:13,057 - INFO - Request Parameters - Page 1:
2025-05-16 08:02:13,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:13,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:13,745 - INFO - API请求耗时: 687ms
2025-05-16 08:02:13,745 - INFO - Response - Page 1
2025-05-16 08:02:13,745 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:02:14,260 - INFO - Request Parameters - Page 2:
2025-05-16 08:02:14,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:14,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:14,885 - INFO - API请求耗时: 625ms
2025-05-16 08:02:14,885 - INFO - Response - Page 2
2025-05-16 08:02:14,885 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:02:15,401 - INFO - Request Parameters - Page 3:
2025-05-16 08:02:15,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:15,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:16,057 - INFO - API请求耗时: 656ms
2025-05-16 08:02:16,057 - INFO - Response - Page 3
2025-05-16 08:02:16,057 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:02:16,573 - INFO - Request Parameters - Page 4:
2025-05-16 08:02:16,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:16,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:17,182 - INFO - API请求耗时: 609ms
2025-05-16 08:02:17,182 - INFO - Response - Page 4
2025-05-16 08:02:17,182 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:02:17,698 - INFO - Request Parameters - Page 5:
2025-05-16 08:02:17,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:17,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:18,448 - INFO - API请求耗时: 750ms
2025-05-16 08:02:18,448 - INFO - Response - Page 5
2025-05-16 08:02:18,448 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:02:18,963 - INFO - Request Parameters - Page 6:
2025-05-16 08:02:18,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:18,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:19,604 - INFO - API请求耗时: 641ms
2025-05-16 08:02:19,604 - INFO - Response - Page 6
2025-05-16 08:02:19,604 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:02:20,120 - INFO - Request Parameters - Page 7:
2025-05-16 08:02:20,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:20,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:20,776 - INFO - API请求耗时: 656ms
2025-05-16 08:02:20,776 - INFO - Response - Page 7
2025-05-16 08:02:20,776 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:02:21,292 - INFO - Request Parameters - Page 8:
2025-05-16 08:02:21,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:21,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:21,995 - INFO - API请求耗时: 703ms
2025-05-16 08:02:21,995 - INFO - Response - Page 8
2025-05-16 08:02:21,995 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:02:22,510 - INFO - Request Parameters - Page 9:
2025-05-16 08:02:22,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:22,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:23,120 - INFO - API请求耗时: 609ms
2025-05-16 08:02:23,120 - INFO - Response - Page 9
2025-05-16 08:02:23,120 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:02:23,620 - INFO - Request Parameters - Page 10:
2025-05-16 08:02:23,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:23,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:24,245 - INFO - API请求耗时: 625ms
2025-05-16 08:02:24,245 - INFO - Response - Page 10
2025-05-16 08:02:24,245 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:02:24,745 - INFO - Request Parameters - Page 11:
2025-05-16 08:02:24,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:24,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:25,417 - INFO - API请求耗时: 672ms
2025-05-16 08:02:25,417 - INFO - Response - Page 11
2025-05-16 08:02:25,417 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:02:25,917 - INFO - Request Parameters - Page 12:
2025-05-16 08:02:25,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:25,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:26,573 - INFO - API请求耗时: 656ms
2025-05-16 08:02:26,573 - INFO - Response - Page 12
2025-05-16 08:02:26,573 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:02:27,073 - INFO - Request Parameters - Page 13:
2025-05-16 08:02:27,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:27,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400370, 1745596800370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:27,667 - INFO - API请求耗时: 594ms
2025-05-16 08:02:27,667 - INFO - Response - Page 13
2025-05-16 08:02:27,667 - INFO - 第 13 页获取到 66 条记录
2025-05-16 08:02:27,667 - INFO - 查询完成，共获取到 1266 条记录
2025-05-16 08:02:27,667 - INFO - 分段 6 查询成功，获取到 1266 条记录
2025-05-16 08:02:28,667 - INFO - 查询分段 7: 2025-04-27 至 2025-05-03
2025-05-16 08:02:28,667 - INFO - 查询日期范围: 2025-04-27 至 2025-05-03，使用分页查询，每页 100 条记录
2025-05-16 08:02:28,667 - INFO - Request Parameters - Page 1:
2025-05-16 08:02:28,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:28,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:29,260 - INFO - API请求耗时: 594ms
2025-05-16 08:02:29,260 - INFO - Response - Page 1
2025-05-16 08:02:29,260 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:02:29,776 - INFO - Request Parameters - Page 2:
2025-05-16 08:02:29,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:29,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:30,385 - INFO - API请求耗时: 609ms
2025-05-16 08:02:30,385 - INFO - Response - Page 2
2025-05-16 08:02:30,385 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:02:30,885 - INFO - Request Parameters - Page 3:
2025-05-16 08:02:30,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:30,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:31,495 - INFO - API请求耗时: 609ms
2025-05-16 08:02:31,495 - INFO - Response - Page 3
2025-05-16 08:02:31,495 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:02:31,995 - INFO - Request Parameters - Page 4:
2025-05-16 08:02:31,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:31,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:32,557 - INFO - API请求耗时: 562ms
2025-05-16 08:02:32,557 - INFO - Response - Page 4
2025-05-16 08:02:32,557 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:02:33,057 - INFO - Request Parameters - Page 5:
2025-05-16 08:02:33,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:33,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:33,698 - INFO - API请求耗时: 641ms
2025-05-16 08:02:33,698 - INFO - Response - Page 5
2025-05-16 08:02:33,698 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:02:34,213 - INFO - Request Parameters - Page 6:
2025-05-16 08:02:34,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:34,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:34,870 - INFO - API请求耗时: 656ms
2025-05-16 08:02:34,870 - INFO - Response - Page 6
2025-05-16 08:02:34,870 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:02:35,385 - INFO - Request Parameters - Page 7:
2025-05-16 08:02:35,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:35,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:36,057 - INFO - API请求耗时: 672ms
2025-05-16 08:02:36,057 - INFO - Response - Page 7
2025-05-16 08:02:36,057 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:02:36,573 - INFO - Request Parameters - Page 8:
2025-05-16 08:02:36,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:36,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:37,182 - INFO - API请求耗时: 609ms
2025-05-16 08:02:37,182 - INFO - Response - Page 8
2025-05-16 08:02:37,182 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:02:37,682 - INFO - Request Parameters - Page 9:
2025-05-16 08:02:37,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:37,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:38,385 - INFO - API请求耗时: 703ms
2025-05-16 08:02:38,385 - INFO - Response - Page 9
2025-05-16 08:02:38,385 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:02:38,885 - INFO - Request Parameters - Page 10:
2025-05-16 08:02:38,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:38,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:39,479 - INFO - API请求耗时: 594ms
2025-05-16 08:02:39,479 - INFO - Response - Page 10
2025-05-16 08:02:39,479 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:02:39,995 - INFO - Request Parameters - Page 11:
2025-05-16 08:02:39,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:39,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:40,620 - INFO - API请求耗时: 625ms
2025-05-16 08:02:40,620 - INFO - Response - Page 11
2025-05-16 08:02:40,620 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:02:41,120 - INFO - Request Parameters - Page 12:
2025-05-16 08:02:41,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:41,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:41,823 - INFO - API请求耗时: 703ms
2025-05-16 08:02:41,823 - INFO - Response - Page 12
2025-05-16 08:02:41,823 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:02:42,338 - INFO - Request Parameters - Page 13:
2025-05-16 08:02:42,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:42,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:42,916 - INFO - API请求耗时: 578ms
2025-05-16 08:02:42,916 - INFO - Response - Page 13
2025-05-16 08:02:42,916 - INFO - 第 13 页获取到 100 条记录
2025-05-16 08:02:43,432 - INFO - Request Parameters - Page 14:
2025-05-16 08:02:43,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:43,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:44,057 - INFO - API请求耗时: 625ms
2025-05-16 08:02:44,057 - INFO - Response - Page 14
2025-05-16 08:02:44,057 - INFO - 第 14 页获取到 100 条记录
2025-05-16 08:02:44,573 - INFO - Request Parameters - Page 15:
2025-05-16 08:02:44,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:44,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:45,229 - INFO - API请求耗时: 656ms
2025-05-16 08:02:45,229 - INFO - Response - Page 15
2025-05-16 08:02:45,229 - INFO - 第 15 页获取到 100 条记录
2025-05-16 08:02:45,745 - INFO - Request Parameters - Page 16:
2025-05-16 08:02:45,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:45,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:46,432 - INFO - API请求耗时: 687ms
2025-05-16 08:02:46,432 - INFO - Response - Page 16
2025-05-16 08:02:46,432 - INFO - 第 16 页获取到 100 条记录
2025-05-16 08:02:46,932 - INFO - Request Parameters - Page 17:
2025-05-16 08:02:46,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:46,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:47,526 - INFO - API请求耗时: 594ms
2025-05-16 08:02:47,526 - INFO - Response - Page 17
2025-05-16 08:02:47,526 - INFO - 第 17 页获取到 100 条记录
2025-05-16 08:02:48,041 - INFO - Request Parameters - Page 18:
2025-05-16 08:02:48,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:48,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:48,713 - INFO - API请求耗时: 672ms
2025-05-16 08:02:48,713 - INFO - Response - Page 18
2025-05-16 08:02:48,713 - INFO - 第 18 页获取到 100 条记录
2025-05-16 08:02:49,213 - INFO - Request Parameters - Page 19:
2025-05-16 08:02:49,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:49,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:49,838 - INFO - API请求耗时: 625ms
2025-05-16 08:02:49,838 - INFO - Response - Page 19
2025-05-16 08:02:49,838 - INFO - 第 19 页获取到 100 条记录
2025-05-16 08:02:50,338 - INFO - Request Parameters - Page 20:
2025-05-16 08:02:50,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:50,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200370, 1746201600370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:50,682 - INFO - API请求耗时: 344ms
2025-05-16 08:02:50,682 - INFO - Response - Page 20
2025-05-16 08:02:50,682 - INFO - 第 20 页获取到 7 条记录
2025-05-16 08:02:50,682 - INFO - 查询完成，共获取到 1907 条记录
2025-05-16 08:02:50,682 - INFO - 分段 7 查询成功，获取到 1907 条记录
2025-05-16 08:02:51,682 - INFO - 查询分段 8: 2025-05-04 至 2025-05-10
2025-05-16 08:02:51,682 - INFO - 查询日期范围: 2025-05-04 至 2025-05-10，使用分页查询，每页 100 条记录
2025-05-16 08:02:51,682 - INFO - Request Parameters - Page 1:
2025-05-16 08:02:51,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:51,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:52,276 - INFO - API请求耗时: 594ms
2025-05-16 08:02:52,276 - INFO - Response - Page 1
2025-05-16 08:02:52,276 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:02:52,776 - INFO - Request Parameters - Page 2:
2025-05-16 08:02:52,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:52,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:53,573 - INFO - API请求耗时: 797ms
2025-05-16 08:02:53,573 - INFO - Response - Page 2
2025-05-16 08:02:53,573 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:02:54,073 - INFO - Request Parameters - Page 3:
2025-05-16 08:02:54,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:54,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:54,682 - INFO - API请求耗时: 609ms
2025-05-16 08:02:54,682 - INFO - Response - Page 3
2025-05-16 08:02:54,698 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:02:55,198 - INFO - Request Parameters - Page 4:
2025-05-16 08:02:55,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:55,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:55,932 - INFO - API请求耗时: 734ms
2025-05-16 08:02:55,932 - INFO - Response - Page 4
2025-05-16 08:02:55,932 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:02:56,432 - INFO - Request Parameters - Page 5:
2025-05-16 08:02:56,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:56,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:57,057 - INFO - API请求耗时: 625ms
2025-05-16 08:02:57,057 - INFO - Response - Page 5
2025-05-16 08:02:57,057 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:02:57,573 - INFO - Request Parameters - Page 6:
2025-05-16 08:02:57,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:57,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:58,213 - INFO - API请求耗时: 641ms
2025-05-16 08:02:58,213 - INFO - Response - Page 6
2025-05-16 08:02:58,213 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:02:58,729 - INFO - Request Parameters - Page 7:
2025-05-16 08:02:58,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:58,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:02:59,354 - INFO - API请求耗时: 625ms
2025-05-16 08:02:59,354 - INFO - Response - Page 7
2025-05-16 08:02:59,354 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:02:59,854 - INFO - Request Parameters - Page 8:
2025-05-16 08:02:59,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:02:59,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:00,494 - INFO - API请求耗时: 641ms
2025-05-16 08:03:00,494 - INFO - Response - Page 8
2025-05-16 08:03:00,494 - INFO - 第 8 页获取到 100 条记录
2025-05-16 08:03:00,994 - INFO - Request Parameters - Page 9:
2025-05-16 08:03:00,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:00,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:01,635 - INFO - API请求耗时: 641ms
2025-05-16 08:03:01,635 - INFO - Response - Page 9
2025-05-16 08:03:01,635 - INFO - 第 9 页获取到 100 条记录
2025-05-16 08:03:02,135 - INFO - Request Parameters - Page 10:
2025-05-16 08:03:02,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:02,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:02,823 - INFO - API请求耗时: 687ms
2025-05-16 08:03:02,823 - INFO - Response - Page 10
2025-05-16 08:03:02,823 - INFO - 第 10 页获取到 100 条记录
2025-05-16 08:03:03,338 - INFO - Request Parameters - Page 11:
2025-05-16 08:03:03,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:03,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:03,963 - INFO - API请求耗时: 625ms
2025-05-16 08:03:03,963 - INFO - Response - Page 11
2025-05-16 08:03:03,963 - INFO - 第 11 页获取到 100 条记录
2025-05-16 08:03:04,463 - INFO - Request Parameters - Page 12:
2025-05-16 08:03:04,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:04,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:05,104 - INFO - API请求耗时: 641ms
2025-05-16 08:03:05,104 - INFO - Response - Page 12
2025-05-16 08:03:05,104 - INFO - 第 12 页获取到 100 条记录
2025-05-16 08:03:05,604 - INFO - Request Parameters - Page 13:
2025-05-16 08:03:05,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:05,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000370, 1746806400370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:06,166 - INFO - API请求耗时: 563ms
2025-05-16 08:03:06,166 - INFO - Response - Page 13
2025-05-16 08:03:06,166 - INFO - 第 13 页获取到 56 条记录
2025-05-16 08:03:06,166 - INFO - 查询完成，共获取到 1256 条记录
2025-05-16 08:03:06,166 - INFO - 分段 8 查询成功，获取到 1256 条记录
2025-05-16 08:03:07,182 - INFO - 查询分段 9: 2025-05-11 至 2025-05-15
2025-05-16 08:03:07,182 - INFO - 查询日期范围: 2025-05-11 至 2025-05-15，使用分页查询，每页 100 条记录
2025-05-16 08:03:07,182 - INFO - Request Parameters - Page 1:
2025-05-16 08:03:07,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:07,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:07,807 - INFO - API请求耗时: 625ms
2025-05-16 08:03:07,807 - INFO - Response - Page 1
2025-05-16 08:03:07,807 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:03:08,323 - INFO - Request Parameters - Page 2:
2025-05-16 08:03:08,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:08,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:08,963 - INFO - API请求耗时: 641ms
2025-05-16 08:03:08,963 - INFO - Response - Page 2
2025-05-16 08:03:08,963 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:03:09,463 - INFO - Request Parameters - Page 3:
2025-05-16 08:03:09,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:09,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:10,057 - INFO - API请求耗时: 594ms
2025-05-16 08:03:10,057 - INFO - Response - Page 3
2025-05-16 08:03:10,057 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:03:10,557 - INFO - Request Parameters - Page 4:
2025-05-16 08:03:10,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:10,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:11,119 - INFO - API请求耗时: 562ms
2025-05-16 08:03:11,119 - INFO - Response - Page 4
2025-05-16 08:03:11,119 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:03:11,619 - INFO - Request Parameters - Page 5:
2025-05-16 08:03:11,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:11,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:12,213 - INFO - API请求耗时: 594ms
2025-05-16 08:03:12,213 - INFO - Response - Page 5
2025-05-16 08:03:12,229 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:03:12,744 - INFO - Request Parameters - Page 6:
2025-05-16 08:03:12,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:12,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:13,338 - INFO - API请求耗时: 594ms
2025-05-16 08:03:13,338 - INFO - Response - Page 6
2025-05-16 08:03:13,338 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:03:13,838 - INFO - Request Parameters - Page 7:
2025-05-16 08:03:13,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:03:13,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800370, 1747324799370], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:03:14,307 - INFO - API请求耗时: 469ms
2025-05-16 08:03:14,307 - INFO - Response - Page 7
2025-05-16 08:03:14,307 - INFO - 第 7 页获取到 30 条记录
2025-05-16 08:03:14,307 - INFO - 查询完成，共获取到 630 条记录
2025-05-16 08:03:14,307 - INFO - 分段 9 查询成功，获取到 630 条记录
2025-05-16 08:03:15,322 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 11601 条记录，失败 0 次
2025-05-16 08:03:15,322 - INFO - 成功获取宜搭日销售表单数据，共 11601 条记录
2025-05-16 08:03:15,322 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-16 08:03:15,322 - INFO - 开始对比和同步日销售数据...
2025-05-16 08:03:15,635 - INFO - 成功创建宜搭日销售数据索引，共 10970 条记录
2025-05-16 08:03:15,635 - INFO - 开始处理数衍数据，共 11622 条记录
2025-05-16 08:03:16,135 - INFO - 更新表单数据成功: FINST-BCC66FB15BGV25PJEF1HY4IKYJGE306249PAMX2
2025-05-16 08:03:16,151 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_20250411, 变更字段: [{'field': 'amount', 'old_value': 5433.4, 'new_value': 7856.8}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 5433.4, 'new_value': 7856.8}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-05-16 08:03:16,666 - INFO - 更新表单数据成功: FINST-3PF66X61S01V2920CISPK6CYT9HL26NVRG7AMWE
2025-05-16 08:03:16,666 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9626.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9626.0}]
2025-05-16 08:03:17,088 - INFO - 更新表单数据成功: FINST-3PF66X61S01V2920CISPK6CYT9HL26NVRG7AMLF
2025-05-16 08:03:17,088 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250501, 变更字段: [{'field': 'amount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-16 08:03:17,619 - INFO - 更新表单数据成功: FINST-VYC66OB16S0VCC238NMA98TNUOJ12701SG7AM491
2025-05-16 08:03:17,619 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250501, 变更字段: [{'field': 'amount', 'old_value': 6249.22, 'new_value': 6358.820000000001}, {'field': 'count', 'old_value': 167, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 6246.12, 'new_value': 6355.72}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 163}]
2025-05-16 08:03:18,072 - INFO - 更新表单数据成功: FINST-VYC66OB16S0VCC238NMA98TNUOJ12701SG7AMM91
2025-05-16 08:03:18,072 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 12376.85, 'new_value': 12478.25}, {'field': 'amount', 'old_value': 12376.85, 'new_value': 12478.25}, {'field': 'count', 'old_value': 198, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 11714.91, 'new_value': 11816.31}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 188}]
2025-05-16 08:03:18,494 - INFO - 更新表单数据成功: FINST-VYC66OB16S0VCC238NMA98TNUOJ12701SG7AMR91
2025-05-16 08:03:18,494 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9242.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9242.69}]
2025-05-16 08:03:18,979 - INFO - 更新表单数据成功: FINST-VYC66OB16S0VCC238NMA98TNUOJ12801SG7AMRA1
2025-05-16 08:03:18,979 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 6532.11, 'new_value': 6544.51}, {'field': 'amount', 'old_value': 6532.110000000001, 'new_value': 6544.51}, {'field': 'count', 'old_value': 258, 'new_value': 259}, {'field': 'onlineAmount', 'old_value': 4291.19, 'new_value': 4303.59}, {'field': 'onlineCount', 'old_value': 160, 'new_value': 161}]
2025-05-16 08:03:19,401 - INFO - 更新表单数据成功: FINST-VYC66OB16S0VCC238NMA98TNUOJ12801SG7AM0B1
2025-05-16 08:03:19,401 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 28207.28, 'new_value': 28240.76}, {'field': 'dailyBillAmount', 'old_value': 28207.28, 'new_value': 28240.76}]
2025-05-16 08:03:19,885 - INFO - 更新表单数据成功: FINST-WBF66B811JZUKRLX6BARY5WL121J2WP3SG7AMIP
2025-05-16 08:03:19,885 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6011.59}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6011.59}, {'field': 'amount', 'old_value': 3199.7200000000003, 'new_value': 3780.9199999999996}, {'field': 'count', 'old_value': 272, 'new_value': 324}, {'field': 'instoreAmount', 'old_value': 3385.82, 'new_value': 3971.22}, {'field': 'instoreCount', 'old_value': 272, 'new_value': 324}]
2025-05-16 08:03:20,369 - INFO - 更新表单数据成功: FINST-VME66K81I71VA0KP76E8ICHLVDR13WG6SG7AMY9
2025-05-16 08:03:20,369 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_20250501, 变更字段: [{'field': 'amount', 'old_value': 1895.7, 'new_value': 1907.5}, {'field': 'count', 'old_value': 17, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 1890.6, 'new_value': 1902.4}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-05-16 08:03:20,854 - INFO - 更新表单数据成功: FINST-RTA66X610FZUQAVM8BC2QB3PCWO92P69SG7AM2H
2025-05-16 08:03:20,854 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250501, 变更字段: [{'field': 'amount', 'old_value': 52072.530000000006, 'new_value': 52131.93}, {'field': 'count', 'old_value': 1166, 'new_value': 1168}, {'field': 'instoreAmount', 'old_value': 49767.94, 'new_value': 49827.34}, {'field': 'instoreCount', 'old_value': 1121, 'new_value': 1123}]
2025-05-16 08:03:21,354 - INFO - 更新表单数据成功: FINST-RTA66X610FZUQAVM8BC2QB3PCWO92P69SG7AM7H
2025-05-16 08:03:21,354 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 21282.2, 'new_value': 22246.2}, {'field': 'amount', 'old_value': 21282.199999999997, 'new_value': 22246.199999999997}, {'field': 'count', 'old_value': 82, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 21511.1, 'new_value': 22475.1}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 84}]
2025-05-16 08:03:21,791 - INFO - 更新表单数据成功: FINST-7PF66BA1F11VTFHR73LN29AFOIHR23WBSG7AM2L
2025-05-16 08:03:21,791 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_20250501, 变更字段: [{'field': 'amount', 'old_value': 48651.0, 'new_value': 49331.0}, {'field': 'count', 'old_value': 162, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 48751.0, 'new_value': 49431.0}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 163}]
2025-05-16 08:03:22,260 - INFO - 更新表单数据成功: FINST-2FD66I71B20VG4L76SO759BUR3GF2ZMESG7AMIV
2025-05-16 08:03:22,260 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_20250501, 变更字段: [{'field': 'count', 'old_value': 52, 'new_value': 64}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 52}]
2025-05-16 08:03:22,744 - INFO - 更新表单数据成功: FINST-2FD66I71B20VG4L76SO759BUR3GF2ZMESG7AMSV
2025-05-16 08:03:22,744 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 1630.55, 'new_value': 1718.55}, {'field': 'dailyBillAmount', 'old_value': 1630.55, 'new_value': 1718.55}]
2025-05-16 08:03:23,182 - INFO - 更新表单数据成功: FINST-CPC66T91OBGVET6G9E4SF9BVXQ8O3N7S89PAMR
2025-05-16 08:03:23,182 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7585.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7585.4}]
2025-05-16 08:03:23,635 - INFO - 更新表单数据成功: FINST-V7966QC1EBGVX1HOADPOB7VXUH1R2N3099PAM6
2025-05-16 08:03:23,651 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5985.25}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5985.25}]
2025-05-16 08:03:24,104 - INFO - 更新表单数据成功: FINST-V7966QC1EBGVX1HOADPOB7VXUH1R2N3099PAMT
2025-05-16 08:03:24,104 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250514, 变更字段: [{'field': 'amount', 'old_value': 2367.0, 'new_value': 2774.0}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 2367.0, 'new_value': 2774.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-05-16 08:03:24,557 - INFO - 更新表单数据成功: FINST-V7966QC1EBGVX1HOADPOB7VXUH1R2N3099PAMH1
2025-05-16 08:03:24,557 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1779.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1779.0}]
2025-05-16 08:03:25,010 - INFO - 更新表单数据成功: FINST-RI766091IBGVQL11CTY5S7UWTO6S2ZQ299PAMF1
2025-05-16 08:03:25,010 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 6647.49, 'new_value': 6711.09}, {'field': 'amount', 'old_value': 6647.49, 'new_value': 6711.09}, {'field': 'count', 'old_value': 134, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 6500.59, 'new_value': 6564.19}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 131}]
2025-05-16 08:03:25,479 - INFO - 更新表单数据成功: FINST-W4G66DA1CYFVU7R6E7XYM9LI10RK3YD599PAMR2
2025-05-16 08:03:25,479 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16517.27}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16517.27}]
2025-05-16 08:03:25,963 - INFO - 更新表单数据成功: FINST-XL666BD15CGVYUD8E6EYH6MLZX623VNA99PAM52
2025-05-16 08:03:25,963 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250514, 变更字段: [{'field': 'instoreAmount', 'old_value': 5564.96, 'new_value': 5572.88}, {'field': 'instoreCount', 'old_value': 372, 'new_value': 373}, {'field': 'onlineAmount', 'old_value': 1399.89, 'new_value': 1391.97}, {'field': 'onlineCount', 'old_value': 88, 'new_value': 87}]
2025-05-16 08:03:26,432 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13T9D99PAM23
2025-05-16 08:03:26,432 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_20250514, 变更字段: [{'field': 'amount', 'old_value': 1519.6, 'new_value': 1528.43}, {'field': 'count', 'old_value': 42, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 1151.21, 'new_value': 1160.04}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 34}]
2025-05-16 08:03:26,838 - INFO - 更新表单数据成功: FINST-UW966371I6GVC3QTBR95M6HHUF0K3FHI99PAML5
2025-05-16 08:03:26,838 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_20250513, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4713.15}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4713.15}]
2025-05-16 08:03:26,916 - INFO - 正在批量插入每日数据，批次 1/20，共 100 条记录
2025-05-16 08:03:27,401 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-16 08:03:30,416 - INFO - 正在批量插入每日数据，批次 2/20，共 100 条记录
2025-05-16 08:03:30,916 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-16 08:03:33,947 - INFO - 正在批量插入每日数据，批次 3/20，共 100 条记录
2025-05-16 08:03:34,338 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-16 08:03:37,354 - INFO - 正在批量插入每日数据，批次 4/20，共 100 条记录
2025-05-16 08:03:37,760 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-16 08:03:40,775 - INFO - 正在批量插入每日数据，批次 5/20，共 100 条记录
2025-05-16 08:03:41,166 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-16 08:03:44,182 - INFO - 正在批量插入每日数据，批次 6/20，共 100 条记录
2025-05-16 08:03:44,557 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-16 08:03:47,572 - INFO - 正在批量插入每日数据，批次 7/20，共 100 条记录
2025-05-16 08:03:48,010 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-16 08:03:51,025 - INFO - 正在批量插入每日数据，批次 8/20，共 100 条记录
2025-05-16 08:03:51,416 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-16 08:03:54,432 - INFO - 正在批量插入每日数据，批次 9/20，共 100 条记录
2025-05-16 08:03:54,869 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-16 08:03:57,885 - INFO - 正在批量插入每日数据，批次 10/20，共 100 条记录
2025-05-16 08:03:58,353 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-16 08:04:01,369 - INFO - 正在批量插入每日数据，批次 11/20，共 100 条记录
2025-05-16 08:04:01,807 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-16 08:04:04,822 - INFO - 正在批量插入每日数据，批次 12/20，共 100 条记录
2025-05-16 08:04:05,228 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-16 08:04:08,244 - INFO - 正在批量插入每日数据，批次 13/20，共 100 条记录
2025-05-16 08:04:08,713 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-16 08:04:11,728 - INFO - 正在批量插入每日数据，批次 14/20，共 100 条记录
2025-05-16 08:04:12,103 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-16 08:04:15,119 - INFO - 正在批量插入每日数据，批次 15/20，共 100 条记录
2025-05-16 08:04:15,603 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-16 08:04:18,619 - INFO - 正在批量插入每日数据，批次 16/20，共 100 条记录
2025-05-16 08:04:19,072 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-16 08:04:22,088 - INFO - 正在批量插入每日数据，批次 17/20，共 100 条记录
2025-05-16 08:04:22,572 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-16 08:04:25,588 - INFO - 正在批量插入每日数据，批次 18/20，共 100 条记录
2025-05-16 08:04:26,056 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-16 08:04:29,072 - INFO - 正在批量插入每日数据，批次 19/20，共 100 条记录
2025-05-16 08:04:29,509 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-16 08:04:32,525 - INFO - 正在批量插入每日数据，批次 20/20，共 52 条记录
2025-05-16 08:04:32,791 - INFO - 批量插入每日数据成功，批次 20，52 条记录
2025-05-16 08:04:35,806 - INFO - 批量插入每日数据完成: 总计 1952 条，成功 1952 条，失败 0 条
2025-05-16 08:04:35,806 - INFO - 批量插入日销售数据完成，共 1952 条记录
2025-05-16 08:04:35,806 - INFO - 日销售数据同步完成！更新: 24 条，插入: 1952 条，错误: 0 条，跳过: 9646 条
2025-05-16 08:04:35,806 - INFO - 正在获取宜搭月销售表单数据...
2025-05-16 08:04:35,806 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-16 08:04:35,806 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-16 08:04:35,806 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:35,806 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:35,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:35,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:36,353 - INFO - API请求耗时: 547ms
2025-05-16 08:04:36,353 - INFO - Response - Page 1
2025-05-16 08:04:36,353 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:36,353 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:36,353 - WARNING - 月度分段 1 查询返回空数据
2025-05-16 08:04:36,353 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-16 08:04:36,353 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:36,353 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:36,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:36,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:36,603 - INFO - API请求耗时: 250ms
2025-05-16 08:04:36,603 - INFO - Response - Page 1
2025-05-16 08:04:36,603 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:36,603 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:36,603 - WARNING - 单月查询返回空数据: 2024-05
2025-05-16 08:04:37,119 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-16 08:04:37,119 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:37,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:37,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:37,478 - INFO - API请求耗时: 359ms
2025-05-16 08:04:37,478 - INFO - Response - Page 1
2025-05-16 08:04:37,494 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:37,494 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:37,494 - WARNING - 单月查询返回空数据: 2024-06
2025-05-16 08:04:37,994 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:37,994 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:37,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:37,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:38,213 - INFO - API请求耗时: 219ms
2025-05-16 08:04:38,213 - INFO - Response - Page 1
2025-05-16 08:04:38,213 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:38,213 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:38,213 - WARNING - 单月查询返回空数据: 2024-07
2025-05-16 08:04:39,744 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-16 08:04:39,744 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:39,744 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:39,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:39,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:39,947 - INFO - API请求耗时: 203ms
2025-05-16 08:04:39,947 - INFO - Response - Page 1
2025-05-16 08:04:39,947 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:39,947 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:39,947 - WARNING - 月度分段 2 查询返回空数据
2025-05-16 08:04:39,947 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-16 08:04:39,947 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:39,947 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:39,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:39,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:40,181 - INFO - API请求耗时: 234ms
2025-05-16 08:04:40,181 - INFO - Response - Page 1
2025-05-16 08:04:40,181 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:40,181 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:40,181 - WARNING - 单月查询返回空数据: 2024-08
2025-05-16 08:04:40,681 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-16 08:04:40,681 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:40,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:40,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:40,869 - INFO - API请求耗时: 187ms
2025-05-16 08:04:40,869 - INFO - Response - Page 1
2025-05-16 08:04:40,869 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:40,869 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:40,869 - WARNING - 单月查询返回空数据: 2024-09
2025-05-16 08:04:41,384 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:41,384 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:41,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:41,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:41,603 - INFO - API请求耗时: 219ms
2025-05-16 08:04:41,603 - INFO - Response - Page 1
2025-05-16 08:04:41,603 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-16 08:04:41,603 - INFO - 查询完成，共获取到 0 条记录
2025-05-16 08:04:41,603 - WARNING - 单月查询返回空数据: 2024-10
2025-05-16 08:04:43,119 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-16 08:04:43,119 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:43,119 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:43,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:43,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:43,713 - INFO - API请求耗时: 594ms
2025-05-16 08:04:43,713 - INFO - Response - Page 1
2025-05-16 08:04:43,713 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:04:44,228 - INFO - Request Parameters - Page 2:
2025-05-16 08:04:44,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:44,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:44,775 - INFO - API请求耗时: 547ms
2025-05-16 08:04:44,775 - INFO - Response - Page 2
2025-05-16 08:04:44,775 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:04:45,275 - INFO - Request Parameters - Page 3:
2025-05-16 08:04:45,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:45,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:45,775 - INFO - API请求耗时: 500ms
2025-05-16 08:04:45,775 - INFO - Response - Page 3
2025-05-16 08:04:45,775 - INFO - 第 3 页获取到 48 条记录
2025-05-16 08:04:45,775 - INFO - 查询完成，共获取到 248 条记录
2025-05-16 08:04:45,775 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-16 08:04:46,775 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-16 08:04:46,775 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-16 08:04:46,775 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:46,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:46,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:47,369 - INFO - API请求耗时: 594ms
2025-05-16 08:04:47,369 - INFO - Response - Page 1
2025-05-16 08:04:47,369 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:04:47,869 - INFO - Request Parameters - Page 2:
2025-05-16 08:04:47,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:47,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:48,556 - INFO - API请求耗时: 687ms
2025-05-16 08:04:48,556 - INFO - Response - Page 2
2025-05-16 08:04:48,556 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:04:49,056 - INFO - Request Parameters - Page 3:
2025-05-16 08:04:49,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:49,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:49,681 - INFO - API请求耗时: 625ms
2025-05-16 08:04:49,681 - INFO - Response - Page 3
2025-05-16 08:04:49,681 - INFO - 第 3 页获取到 100 条记录
2025-05-16 08:04:50,197 - INFO - Request Parameters - Page 4:
2025-05-16 08:04:50,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:50,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:50,744 - INFO - API请求耗时: 547ms
2025-05-16 08:04:50,744 - INFO - Response - Page 4
2025-05-16 08:04:50,744 - INFO - 第 4 页获取到 100 条记录
2025-05-16 08:04:51,244 - INFO - Request Parameters - Page 5:
2025-05-16 08:04:51,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:51,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:51,744 - INFO - API请求耗时: 500ms
2025-05-16 08:04:51,744 - INFO - Response - Page 5
2025-05-16 08:04:51,744 - INFO - 第 5 页获取到 100 条记录
2025-05-16 08:04:52,244 - INFO - Request Parameters - Page 6:
2025-05-16 08:04:52,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:52,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:52,900 - INFO - API请求耗时: 656ms
2025-05-16 08:04:52,900 - INFO - Response - Page 6
2025-05-16 08:04:52,900 - INFO - 第 6 页获取到 100 条记录
2025-05-16 08:04:53,416 - INFO - Request Parameters - Page 7:
2025-05-16 08:04:53,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:53,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:53,978 - INFO - API请求耗时: 562ms
2025-05-16 08:04:53,978 - INFO - Response - Page 7
2025-05-16 08:04:53,978 - INFO - 第 7 页获取到 100 条记录
2025-05-16 08:04:54,494 - INFO - Request Parameters - Page 8:
2025-05-16 08:04:54,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:54,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:54,853 - INFO - API请求耗时: 359ms
2025-05-16 08:04:54,853 - INFO - Response - Page 8
2025-05-16 08:04:54,853 - INFO - 第 8 页获取到 16 条记录
2025-05-16 08:04:54,853 - INFO - 查询完成，共获取到 716 条记录
2025-05-16 08:04:54,853 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-16 08:04:55,869 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-16 08:04:55,869 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-16 08:04:55,869 - INFO - Request Parameters - Page 1:
2025-05-16 08:04:55,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:55,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:56,416 - INFO - API请求耗时: 547ms
2025-05-16 08:04:56,416 - INFO - Response - Page 1
2025-05-16 08:04:56,416 - INFO - 第 1 页获取到 100 条记录
2025-05-16 08:04:56,916 - INFO - Request Parameters - Page 2:
2025-05-16 08:04:56,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:56,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:57,494 - INFO - API请求耗时: 578ms
2025-05-16 08:04:57,494 - INFO - Response - Page 2
2025-05-16 08:04:57,494 - INFO - 第 2 页获取到 100 条记录
2025-05-16 08:04:57,994 - INFO - Request Parameters - Page 3:
2025-05-16 08:04:57,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 08:04:57,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 08:04:58,384 - INFO - API请求耗时: 391ms
2025-05-16 08:04:58,384 - INFO - Response - Page 3
2025-05-16 08:04:58,400 - INFO - 第 3 页获取到 24 条记录
2025-05-16 08:04:58,400 - INFO - 查询完成，共获取到 224 条记录
2025-05-16 08:04:58,400 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-16 08:04:59,416 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-16 08:04:59,416 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-16 08:04:59,416 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-16 08:04:59,416 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-16 08:04:59,416 - INFO - 成功获取SQLite月度汇总数据，共 1189 条记录
2025-05-16 08:04:59,478 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-16 08:05:00,072 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-16 08:05:00,072 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92338.54, 'new_value': 98102.51}, {'field': 'dailyBillAmount', 'old_value': 92338.54, 'new_value': 98102.51}, {'field': 'amount', 'old_value': 2756.0, 'new_value': 2978.5}, {'field': 'count', 'old_value': 41, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 2832.0, 'new_value': 3054.5}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 45}]
2025-05-16 08:05:00,541 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-16 08:05:00,541 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 259063.08, 'new_value': 268379.08}, {'field': 'dailyBillAmount', 'old_value': 259063.08, 'new_value': 268379.08}, {'field': 'amount', 'old_value': 137377.3, 'new_value': 144283.9}, {'field': 'count', 'old_value': 1251, 'new_value': 1327}, {'field': 'instoreAmount', 'old_value': 56924.9, 'new_value': 58824.3}, {'field': 'instoreCount', 'old_value': 409, 'new_value': 428}, {'field': 'onlineAmount', 'old_value': 80743.8, 'new_value': 85751.0}, {'field': 'onlineCount', 'old_value': 842, 'new_value': 899}]
2025-05-16 08:05:01,009 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-16 08:05:01,009 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 164911.88, 'new_value': 172482.91}, {'field': 'dailyBillAmount', 'old_value': 164911.88, 'new_value': 172482.91}, {'field': 'amount', 'old_value': 166273.8, 'new_value': 174212.9}, {'field': 'count', 'old_value': 1110, 'new_value': 1170}, {'field': 'instoreAmount', 'old_value': 156407.3, 'new_value': 163524.6}, {'field': 'instoreCount', 'old_value': 965, 'new_value': 1012}, {'field': 'onlineAmount', 'old_value': 10084.7, 'new_value': 10906.5}, {'field': 'onlineCount', 'old_value': 145, 'new_value': 158}]
2025-05-16 08:05:01,541 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-16 08:05:01,541 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 384557.6, 'new_value': 404886.88}, {'field': 'dailyBillAmount', 'old_value': 384557.6, 'new_value': 404886.88}, {'field': 'amount', 'old_value': 292626.8, 'new_value': 306743.0}, {'field': 'count', 'old_value': 1394, 'new_value': 1463}, {'field': 'instoreAmount', 'old_value': 292626.8, 'new_value': 306743.0}, {'field': 'instoreCount', 'old_value': 1394, 'new_value': 1463}]
2025-05-16 08:05:02,056 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-16 08:05:02,056 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 299882.72, 'new_value': 311488.14}, {'field': 'dailyBillAmount', 'old_value': 299882.72, 'new_value': 311488.14}, {'field': 'amount', 'old_value': 460129.0, 'new_value': 489017.0}, {'field': 'count', 'old_value': 1582, 'new_value': 1684}, {'field': 'instoreAmount', 'old_value': 461379.0, 'new_value': 490267.0}, {'field': 'instoreCount', 'old_value': 1582, 'new_value': 1684}]
2025-05-16 08:05:02,619 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-16 08:05:02,619 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46942.7, 'new_value': 46976.7}, {'field': 'dailyBillAmount', 'old_value': 46942.7, 'new_value': 46976.7}, {'field': 'amount', 'old_value': 61321.61, 'new_value': 61394.51}, {'field': 'count', 'old_value': 167, 'new_value': 169}, {'field': 'onlineAmount', 'old_value': 27103.02, 'new_value': 27175.920000000002}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 138}]
2025-05-16 08:05:03,072 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-16 08:05:03,072 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144922.9, 'new_value': 151308.9}, {'field': 'amount', 'old_value': 144922.9, 'new_value': 151308.9}, {'field': 'count', 'old_value': 79, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 144922.9, 'new_value': 151308.9}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 84}]
2025-05-16 08:05:03,650 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-16 08:05:03,650 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 428147.22, 'new_value': 452627.01}, {'field': 'dailyBillAmount', 'old_value': 428147.22, 'new_value': 452627.01}, {'field': 'amount', 'old_value': 436069.76, 'new_value': 449509.16000000003}, {'field': 'count', 'old_value': 3185, 'new_value': 3293}, {'field': 'instoreAmount', 'old_value': 342391.16000000003, 'new_value': 353575.36}, {'field': 'instoreCount', 'old_value': 1435, 'new_value': 1480}, {'field': 'onlineAmount', 'old_value': 96483.17, 'new_value': 98941.67}, {'field': 'onlineCount', 'old_value': 1750, 'new_value': 1813}]
2025-05-16 08:05:04,056 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-16 08:05:04,056 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 432227.86, 'new_value': 458946.16}, {'field': 'dailyBillAmount', 'old_value': 432227.86, 'new_value': 458946.16}, {'field': 'amount', 'old_value': 95303.71, 'new_value': 100963.71}, {'field': 'count', 'old_value': 486, 'new_value': 517}, {'field': 'instoreAmount', 'old_value': 95303.71, 'new_value': 100963.71}, {'field': 'instoreCount', 'old_value': 486, 'new_value': 517}]
2025-05-16 08:05:04,509 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-16 08:05:04,509 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 340344.09, 'new_value': 367778.64}, {'field': 'dailyBillAmount', 'old_value': 340344.09, 'new_value': 367778.64}, {'field': 'amount', 'old_value': 14481.0, 'new_value': 15144.0}, {'field': 'count', 'old_value': 16, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 14481.0, 'new_value': 15144.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 18}]
2025-05-16 08:05:04,978 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-16 08:05:04,978 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 66597.3, 'new_value': 71594.3}, {'field': 'count', 'old_value': 207, 'new_value': 216}, {'field': 'instoreAmount', 'old_value': 66598.1, 'new_value': 71595.1}, {'field': 'instoreCount', 'old_value': 207, 'new_value': 216}]
2025-05-16 08:05:05,478 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-16 08:05:05,478 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22363.9, 'new_value': 22670.9}, {'field': 'amount', 'old_value': 22363.9, 'new_value': 22670.9}, {'field': 'count', 'old_value': 18, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 23759.9, 'new_value': 24066.9}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 20}]
2025-05-16 08:05:05,931 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-16 08:05:05,931 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 580141.26, 'new_value': 612863.6}, {'field': 'dailyBillAmount', 'old_value': 580141.26, 'new_value': 612863.6}, {'field': 'amount', 'old_value': -260716.37, 'new_value': -286534.48}, {'field': 'count', 'old_value': 643, 'new_value': 654}, {'field': 'instoreAmount', 'old_value': 387669.34, 'new_value': 392935.24}, {'field': 'instoreCount', 'old_value': 643, 'new_value': 654}]
2025-05-16 08:05:06,400 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-16 08:05:06,400 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174379.0, 'new_value': 181751.0}, {'field': 'amount', 'old_value': 174379.0, 'new_value': 181751.0}, {'field': 'count', 'old_value': 739, 'new_value': 762}, {'field': 'instoreAmount', 'old_value': 174379.0, 'new_value': 181751.0}, {'field': 'instoreCount', 'old_value': 739, 'new_value': 762}]
2025-05-16 08:05:06,822 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-16 08:05:06,822 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 235270.82, 'new_value': 247789.98}, {'field': 'dailyBillAmount', 'old_value': 201714.01, 'new_value': 213211.17}, {'field': 'amount', 'old_value': 235270.82, 'new_value': 247789.98}, {'field': 'count', 'old_value': 825, 'new_value': 866}, {'field': 'instoreAmount', 'old_value': 235270.82, 'new_value': 247789.98}, {'field': 'instoreCount', 'old_value': 825, 'new_value': 866}]
2025-05-16 08:05:07,275 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-16 08:05:07,275 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 108016.3, 'new_value': 114001.55}, {'field': 'dailyBillAmount', 'old_value': 108016.3, 'new_value': 114001.55}, {'field': 'amount', 'old_value': 8674.4, 'new_value': 9120.1}, {'field': 'count', 'old_value': 67, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 10285.2, 'new_value': 10730.9}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 69}]
2025-05-16 08:05:07,666 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-16 08:05:07,666 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61791.549999999996, 'new_value': 65153.119999999995}, {'field': 'dailyBillAmount', 'old_value': 61791.549999999996, 'new_value': 65153.119999999995}, {'field': 'amount', 'old_value': 41181.51, 'new_value': 42766.81}, {'field': 'count', 'old_value': 596, 'new_value': 621}, {'field': 'instoreAmount', 'old_value': 42727.71, 'new_value': 44313.01}, {'field': 'instoreCount', 'old_value': 596, 'new_value': 621}]
2025-05-16 08:05:08,134 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-16 08:05:08,134 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82195.95, 'new_value': 87772.87}, {'field': 'amount', 'old_value': 82195.09, 'new_value': 87772.01}, {'field': 'count', 'old_value': 2831, 'new_value': 3009}, {'field': 'instoreAmount', 'old_value': 72747.63, 'new_value': 77622.63}, {'field': 'instoreCount', 'old_value': 2576, 'new_value': 2734}, {'field': 'onlineAmount', 'old_value': 9448.32, 'new_value': 10150.24}, {'field': 'onlineCount', 'old_value': 255, 'new_value': 275}]
2025-05-16 08:05:08,650 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-16 08:05:08,650 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 136734.22, 'new_value': 146193.22}, {'field': 'dailyBillAmount', 'old_value': 133276.0, 'new_value': 142735.0}, {'field': 'amount', 'old_value': 113003.22, 'new_value': 117939.22}, {'field': 'count', 'old_value': 112, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 112874.0, 'new_value': 117810.0}, {'field': 'instoreCount', 'old_value': 111, 'new_value': 119}]
2025-05-16 08:05:09,087 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-16 08:05:09,087 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 281588.47, 'new_value': 317912.3}, {'field': 'dailyBillAmount', 'old_value': 281083.92, 'new_value': 317407.75}, {'field': 'amount', 'old_value': 281588.47, 'new_value': 317912.3}, {'field': 'count', 'old_value': 264, 'new_value': 294}, {'field': 'instoreAmount', 'old_value': 281589.47, 'new_value': 317913.3}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 294}]
2025-05-16 08:05:09,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-16 08:05:09,540 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 29587.0, 'new_value': 30725.0}, {'field': 'count', 'old_value': 44, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 29587.0, 'new_value': 30725.0}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 48}]
2025-05-16 08:05:10,275 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-16 08:05:10,275 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64986.4, 'new_value': 67769.8}, {'field': 'dailyBillAmount', 'old_value': 64986.4, 'new_value': 67769.8}, {'field': 'amount', 'old_value': 74741.2, 'new_value': 77524.6}, {'field': 'count', 'old_value': 185, 'new_value': 195}, {'field': 'instoreAmount', 'old_value': 74745.8, 'new_value': 77529.2}, {'field': 'instoreCount', 'old_value': 185, 'new_value': 195}]
2025-05-16 08:05:10,759 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-16 08:05:10,759 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95392.87, 'new_value': 102604.87}, {'field': 'amount', 'old_value': 95392.87, 'new_value': 102604.87}, {'field': 'count', 'old_value': 113, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 95392.87, 'new_value': 102731.87}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 120}]
2025-05-16 08:05:11,181 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-16 08:05:11,181 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'amount', 'old_value': 133634.35, 'new_value': 139066.35}, {'field': 'count', 'old_value': 886, 'new_value': 931}, {'field': 'instoreAmount', 'old_value': 134128.35, 'new_value': 139664.35}, {'field': 'instoreCount', 'old_value': 886, 'new_value': 931}]
2025-05-16 08:05:11,775 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-16 08:05:11,775 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81580.13, 'new_value': 88410.82}, {'field': 'dailyBillAmount', 'old_value': 81580.13, 'new_value': 88410.82}, {'field': 'amount', 'old_value': 8190.96, 'new_value': 8768.98}, {'field': 'count', 'old_value': 754, 'new_value': 811}, {'field': 'instoreAmount', 'old_value': 11474.619999999999, 'new_value': 12153.34}, {'field': 'instoreCount', 'old_value': 754, 'new_value': 811}]
2025-05-16 08:05:12,165 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-16 08:05:12,165 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 145126.86, 'new_value': 153210.16}, {'field': 'amount', 'old_value': 145124.17, 'new_value': 153207.47}, {'field': 'count', 'old_value': 3661, 'new_value': 3898}, {'field': 'instoreAmount', 'old_value': 140512.51, 'new_value': 148403.51}, {'field': 'instoreCount', 'old_value': 3526, 'new_value': 3758}, {'field': 'onlineAmount', 'old_value': 6917.03, 'new_value': 7109.33}, {'field': 'onlineCount', 'old_value': 135, 'new_value': 140}]
2025-05-16 08:05:12,556 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-16 08:05:12,556 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127890.83, 'new_value': 134525.97}, {'field': 'dailyBillAmount', 'old_value': 127890.83, 'new_value': 134525.97}, {'field': 'amount', 'old_value': 127890.83, 'new_value': 134525.97}, {'field': 'count', 'old_value': 408, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 127890.83, 'new_value': 134525.97}, {'field': 'instoreCount', 'old_value': 408, 'new_value': 427}]
2025-05-16 08:05:13,025 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-16 08:05:13,025 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109859.0, 'new_value': 117164.43000000001}, {'field': 'dailyBillAmount', 'old_value': 109859.0, 'new_value': 117164.43000000001}, {'field': 'amount', 'old_value': 31791.2, 'new_value': 34136.8}, {'field': 'count', 'old_value': 79, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 31791.2, 'new_value': 34136.8}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 86}]
2025-05-16 08:05:13,494 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-16 08:05:13,494 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 234364.75, 'new_value': 246562.49}, {'field': 'dailyBillAmount', 'old_value': 234364.75, 'new_value': 246562.49}, {'field': 'amount', 'old_value': 95841.9, 'new_value': 100725.7}, {'field': 'count', 'old_value': 369, 'new_value': 386}, {'field': 'instoreAmount', 'old_value': 95842.16, 'new_value': 100725.96}, {'field': 'instoreCount', 'old_value': 369, 'new_value': 386}]
2025-05-16 08:05:13,994 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-16 08:05:13,994 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51019.64, 'new_value': 55140.6}, {'field': 'dailyBillAmount', 'old_value': 51019.64, 'new_value': 55140.6}, {'field': 'amount', 'old_value': 16008.74, 'new_value': 16909.67}, {'field': 'count', 'old_value': 584, 'new_value': 616}, {'field': 'instoreAmount', 'old_value': 3840.99, 'new_value': 4176.39}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 101}, {'field': 'onlineAmount', 'old_value': 12406.77, 'new_value': 12972.3}, {'field': 'onlineCount', 'old_value': 491, 'new_value': 515}]
2025-05-16 08:05:14,447 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-16 08:05:14,447 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 79471.31, 'new_value': 86515.83}, {'field': 'dailyBillAmount', 'old_value': 79471.31, 'new_value': 86515.83}, {'field': 'amount', 'old_value': 12750.1, 'new_value': 13977.84}, {'field': 'count', 'old_value': 312, 'new_value': 342}, {'field': 'instoreAmount', 'old_value': 10761.56, 'new_value': 11777.61}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 299}, {'field': 'onlineAmount', 'old_value': 1989.23, 'new_value': 2200.92}, {'field': 'onlineCount', 'old_value': 38, 'new_value': 43}]
2025-05-16 08:05:14,900 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-16 08:05:14,900 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9829.03, 'new_value': 10153.03}, {'field': 'dailyBillAmount', 'old_value': 9829.03, 'new_value': 10153.03}, {'field': 'amount', 'old_value': 9768.18, 'new_value': 10102.18}, {'field': 'count', 'old_value': 343, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 10135.78, 'new_value': 10469.78}, {'field': 'instoreCount', 'old_value': 343, 'new_value': 360}]
2025-05-16 08:05:15,353 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-16 08:05:15,353 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'amount', 'old_value': 17191.95, 'new_value': 17945.77}, {'field': 'count', 'old_value': 959, 'new_value': 998}, {'field': 'instoreAmount', 'old_value': 8710.74, 'new_value': 8984.36}, {'field': 'instoreCount', 'old_value': 370, 'new_value': 385}, {'field': 'onlineAmount', 'old_value': 8868.52, 'new_value': 9348.72}, {'field': 'onlineCount', 'old_value': 589, 'new_value': 613}]
2025-05-16 08:05:15,837 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-16 08:05:15,837 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 177294.54, 'new_value': 189780.26}, {'field': 'dailyBillAmount', 'old_value': 177294.54, 'new_value': 189780.26}, {'field': 'amount', 'old_value': 80493.91, 'new_value': 88260.41}, {'field': 'count', 'old_value': 329, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 83381.52, 'new_value': 91443.52}, {'field': 'instoreCount', 'old_value': 329, 'new_value': 360}]
2025-05-16 08:05:16,244 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-16 08:05:16,244 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 10793.51, 'new_value': 11335.78}, {'field': 'count', 'old_value': 78, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 10867.75, 'new_value': 11410.02}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 85}]
2025-05-16 08:05:16,665 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-16 08:05:16,665 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115257.04, 'new_value': 121819.92}, {'field': 'dailyBillAmount', 'old_value': 115257.04, 'new_value': 121819.92}, {'field': 'amount', 'old_value': 54747.42, 'new_value': 58530.61}, {'field': 'count', 'old_value': 2342, 'new_value': 2522}, {'field': 'instoreAmount', 'old_value': 55843.22, 'new_value': 59758.74}, {'field': 'instoreCount', 'old_value': 2342, 'new_value': 2522}]
2025-05-16 08:05:17,259 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-16 08:05:17,259 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 257875.9, 'new_value': 269217.9}, {'field': 'dailyBillAmount', 'old_value': 257875.9, 'new_value': 269217.9}, {'field': 'amount', 'old_value': 257875.9, 'new_value': 269217.9}, {'field': 'count', 'old_value': 314, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 257875.9, 'new_value': 269217.9}, {'field': 'instoreCount', 'old_value': 314, 'new_value': 330}]
2025-05-16 08:05:17,775 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-16 08:05:17,775 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124934.77, 'new_value': 130355.49}, {'field': 'dailyBillAmount', 'old_value': 124934.77, 'new_value': 130355.49}, {'field': 'amount', 'old_value': 74225.81, 'new_value': 76178.11}, {'field': 'count', 'old_value': 191, 'new_value': 196}, {'field': 'instoreAmount', 'old_value': 75277.61, 'new_value': 77229.91}, {'field': 'instoreCount', 'old_value': 191, 'new_value': 196}]
2025-05-16 08:05:18,244 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-16 08:05:18,244 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27561.0, 'new_value': 30352.0}, {'field': 'dailyBillAmount', 'old_value': 27561.0, 'new_value': 30352.0}, {'field': 'amount', 'old_value': 27561.0, 'new_value': 30352.0}, {'field': 'count', 'old_value': 536, 'new_value': 592}, {'field': 'instoreAmount', 'old_value': 27600.0, 'new_value': 30391.0}, {'field': 'instoreCount', 'old_value': 536, 'new_value': 592}]
2025-05-16 08:05:18,681 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-16 08:05:18,681 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49886.75, 'new_value': 53063.52}, {'field': 'dailyBillAmount', 'old_value': 49886.75, 'new_value': 53063.52}, {'field': 'amount', 'old_value': 52237.41, 'new_value': 55514.08}, {'field': 'count', 'old_value': 2747, 'new_value': 2905}, {'field': 'instoreAmount', 'old_value': 24269.47, 'new_value': 25896.39}, {'field': 'instoreCount', 'old_value': 1211, 'new_value': 1281}, {'field': 'onlineAmount', 'old_value': 28725.08, 'new_value': 30374.83}, {'field': 'onlineCount', 'old_value': 1536, 'new_value': 1624}]
2025-05-16 08:05:19,134 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-16 08:05:19,134 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17549.829999999998, 'new_value': 18808.829999999998}, {'field': 'dailyBillAmount', 'old_value': 17549.829999999998, 'new_value': 18808.829999999998}, {'field': 'amount', 'old_value': 24408.26, 'new_value': 26198.0}, {'field': 'count', 'old_value': 722, 'new_value': 777}, {'field': 'instoreAmount', 'old_value': 21771.63, 'new_value': 23465.23}, {'field': 'instoreCount', 'old_value': 618, 'new_value': 667}, {'field': 'onlineAmount', 'old_value': 2659.43, 'new_value': 2755.57}, {'field': 'onlineCount', 'old_value': 104, 'new_value': 110}]
2025-05-16 08:05:19,697 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-16 08:05:19,697 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36928.72, 'new_value': 40116.18}, {'field': 'dailyBillAmount', 'old_value': 36928.72, 'new_value': 40116.18}, {'field': 'amount', 'old_value': 36909.02, 'new_value': 40096.62}, {'field': 'count', 'old_value': 1412, 'new_value': 1532}, {'field': 'instoreAmount', 'old_value': 23966.8, 'new_value': 26062.02}, {'field': 'instoreCount', 'old_value': 837, 'new_value': 908}, {'field': 'onlineAmount', 'old_value': 13029.64, 'new_value': 14122.02}, {'field': 'onlineCount', 'old_value': 575, 'new_value': 624}]
2025-05-16 08:05:20,150 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-16 08:05:20,150 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 39486.8, 'new_value': 40660.61}, {'field': 'count', 'old_value': 460, 'new_value': 479}, {'field': 'instoreAmount', 'old_value': 39913.7, 'new_value': 41087.51}, {'field': 'instoreCount', 'old_value': 460, 'new_value': 479}]
2025-05-16 08:05:20,587 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-16 08:05:20,587 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43842.1, 'new_value': 45845.7}, {'field': 'amount', 'old_value': 43841.6, 'new_value': 45845.2}, {'field': 'count', 'old_value': 1067, 'new_value': 1112}, {'field': 'instoreAmount', 'old_value': 44327.6, 'new_value': 46493.9}, {'field': 'instoreCount', 'old_value': 1067, 'new_value': 1112}]
2025-05-16 08:05:21,025 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-16 08:05:21,025 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 201140.52, 'new_value': 208216.52}, {'field': 'dailyBillAmount', 'old_value': 201140.52, 'new_value': 208216.52}, {'field': 'amount', 'old_value': 55234.82, 'new_value': 58422.82}, {'field': 'count', 'old_value': 205, 'new_value': 216}, {'field': 'instoreAmount', 'old_value': 55234.82, 'new_value': 58422.82}, {'field': 'instoreCount', 'old_value': 205, 'new_value': 216}]
2025-05-16 08:05:21,494 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-16 08:05:21,494 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63167.88, 'new_value': 65824.88}, {'field': 'dailyBillAmount', 'old_value': 63167.88, 'new_value': 65824.88}, {'field': 'amount', 'old_value': 61358.08, 'new_value': 63697.08}, {'field': 'count', 'old_value': 198, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 63253.71, 'new_value': 65592.71}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 200}]
2025-05-16 08:05:21,947 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-16 08:05:21,947 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33561.0, 'new_value': 35499.0}, {'field': 'dailyBillAmount', 'old_value': 33561.0, 'new_value': 35499.0}, {'field': 'amount', 'old_value': 42285.0, 'new_value': 44424.0}, {'field': 'count', 'old_value': 74, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 45353.0, 'new_value': 47771.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 80}]
2025-05-16 08:05:22,478 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-16 08:05:22,478 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53463.85, 'new_value': 55074.15}, {'field': 'dailyBillAmount', 'old_value': 50970.65, 'new_value': 52580.95}, {'field': 'amount', 'old_value': 53463.15, 'new_value': 55073.45}, {'field': 'count', 'old_value': 145, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 58892.15, 'new_value': 60901.45}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 154}]
2025-05-16 08:05:22,947 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-16 08:05:22,947 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70724.83, 'new_value': 73104.23}, {'field': 'dailyBillAmount', 'old_value': 70724.83, 'new_value': 73104.23}, {'field': 'amount', 'old_value': 39517.04, 'new_value': 40706.58}, {'field': 'count', 'old_value': 1084, 'new_value': 1125}, {'field': 'instoreAmount', 'old_value': 33670.729999999996, 'new_value': 34700.92}, {'field': 'instoreCount', 'old_value': 915, 'new_value': 950}, {'field': 'onlineAmount', 'old_value': 6022.61, 'new_value': 6181.96}, {'field': 'onlineCount', 'old_value': 169, 'new_value': 175}]
2025-05-16 08:05:23,322 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-16 08:05:23,322 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98329.95, 'new_value': 105593.55}, {'field': 'dailyBillAmount', 'old_value': 94245.66, 'new_value': 101390.16}, {'field': 'amount', 'old_value': 98329.95, 'new_value': 105593.55}, {'field': 'count', 'old_value': 1208, 'new_value': 1301}, {'field': 'instoreAmount', 'old_value': 94408.0, 'new_value': 101106.0}, {'field': 'instoreCount', 'old_value': 1166, 'new_value': 1253}, {'field': 'onlineAmount', 'old_value': 3921.95, 'new_value': 4487.55}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 48}]
2025-05-16 08:05:23,775 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-16 08:05:23,775 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43016.23, 'new_value': 44805.39}, {'field': 'dailyBillAmount', 'old_value': 43016.23, 'new_value': 44805.39}, {'field': 'amount', 'old_value': 62679.33, 'new_value': 64832.659999999996}, {'field': 'count', 'old_value': 255, 'new_value': 265}, {'field': 'instoreAmount', 'old_value': 60675.39, 'new_value': 62777.32}, {'field': 'instoreCount', 'old_value': 229, 'new_value': 238}, {'field': 'onlineAmount', 'old_value': 2003.94, 'new_value': 2055.34}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-05-16 08:05:24,181 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-16 08:05:24,181 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118651.6, 'new_value': 124319.5}, {'field': 'dailyBillAmount', 'old_value': 118651.6, 'new_value': 124319.5}, {'field': 'amount', 'old_value': 123764.8, 'new_value': 130508.7}, {'field': 'count', 'old_value': 451, 'new_value': 474}, {'field': 'instoreAmount', 'old_value': 125692.8, 'new_value': 132436.7}, {'field': 'instoreCount', 'old_value': 451, 'new_value': 474}]
2025-05-16 08:05:24,697 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-16 08:05:24,697 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29754.0, 'new_value': 32036.0}, {'field': 'dailyBillAmount', 'old_value': 29754.0, 'new_value': 32036.0}, {'field': 'amount', 'old_value': 28659.0, 'new_value': 30941.0}, {'field': 'count', 'old_value': 63, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 29015.0, 'new_value': 31297.0}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 67}]
2025-05-16 08:05:25,165 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-16 08:05:25,165 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 13393.28, 'new_value': 14161.28}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 13674.68, 'new_value': 14442.68}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-05-16 08:05:25,650 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-16 08:05:25,650 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18300.010000000002, 'new_value': 19509.74}, {'field': 'dailyBillAmount', 'old_value': 18300.010000000002, 'new_value': 19509.74}, {'field': 'amount', 'old_value': 32160.16, 'new_value': 33459.87}, {'field': 'count', 'old_value': 168, 'new_value': 177}, {'field': 'instoreAmount', 'old_value': 30982.8, 'new_value': 32187.8}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 138}, {'field': 'onlineAmount', 'old_value': 1992.51, 'new_value': 2087.2200000000003}, {'field': 'onlineCount', 'old_value': 37, 'new_value': 39}]
2025-05-16 08:05:26,087 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-16 08:05:26,087 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13958.5, 'new_value': 14858.5}, {'field': 'dailyBillAmount', 'old_value': 13958.5, 'new_value': 14858.5}, {'field': 'amount', 'old_value': 10981.81, 'new_value': 11573.31}, {'field': 'count', 'old_value': 498, 'new_value': 532}, {'field': 'instoreAmount', 'old_value': 11127.66, 'new_value': 11719.16}, {'field': 'instoreCount', 'old_value': 498, 'new_value': 532}]
2025-05-16 08:05:26,556 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-16 08:05:26,556 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22525.34, 'new_value': 25208.26}, {'field': 'amount', 'old_value': 22523.87, 'new_value': 25206.79}, {'field': 'count', 'old_value': 1241, 'new_value': 1373}, {'field': 'instoreAmount', 'old_value': 27464.420000000002, 'new_value': 30375.920000000002}, {'field': 'instoreCount', 'old_value': 1241, 'new_value': 1373}]
2025-05-16 08:05:27,040 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-16 08:05:27,040 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77116.65, 'new_value': 80526.75}, {'field': 'dailyBillAmount', 'old_value': 77116.65, 'new_value': 80526.75}, {'field': 'amount', 'old_value': 62097.5, 'new_value': 64234.2}, {'field': 'count', 'old_value': 244, 'new_value': 254}, {'field': 'instoreAmount', 'old_value': 62097.5, 'new_value': 64234.2}, {'field': 'instoreCount', 'old_value': 244, 'new_value': 254}]
2025-05-16 08:05:27,619 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-16 08:05:27,634 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 217298.38, 'new_value': 231667.22999999998}, {'field': 'dailyBillAmount', 'old_value': 217298.38, 'new_value': 231667.22999999998}, {'field': 'amount', 'old_value': 141570.86, 'new_value': 149290.66}, {'field': 'count', 'old_value': 1647, 'new_value': 1745}, {'field': 'instoreAmount', 'old_value': 54476.02, 'new_value': 58460.12}, {'field': 'instoreCount', 'old_value': 629, 'new_value': 685}, {'field': 'onlineAmount', 'old_value': 87094.84, 'new_value': 90830.54}, {'field': 'onlineCount', 'old_value': 1018, 'new_value': 1060}]
2025-05-16 08:05:28,040 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-16 08:05:28,040 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 142068.96, 'new_value': 145744.96}, {'field': 'dailyBillAmount', 'old_value': 142068.96, 'new_value': 145744.96}, {'field': 'amount', 'old_value': 147987.8, 'new_value': 151492.8}, {'field': 'count', 'old_value': 870, 'new_value': 893}, {'field': 'instoreAmount', 'old_value': 148767.69999999998, 'new_value': 152272.69999999998}, {'field': 'instoreCount', 'old_value': 870, 'new_value': 893}]
2025-05-16 08:05:28,462 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-16 08:05:28,462 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40233.89, 'new_value': 42732.89}, {'field': 'amount', 'old_value': 40233.89, 'new_value': 42732.89}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 40233.89, 'new_value': 42732.89}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-05-16 08:05:28,931 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-16 08:05:28,931 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'amount', 'old_value': 63811.74, 'new_value': 66876.66}, {'field': 'count', 'old_value': 739, 'new_value': 776}, {'field': 'instoreAmount', 'old_value': 56732.07, 'new_value': 59474.48}, {'field': 'instoreCount', 'old_value': 502, 'new_value': 528}, {'field': 'onlineAmount', 'old_value': 7741.84, 'new_value': 8064.35}, {'field': 'onlineCount', 'old_value': 237, 'new_value': 248}]
2025-05-16 08:05:29,369 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-16 08:05:29,369 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98212.99, 'new_value': 102810.74}, {'field': 'amount', 'old_value': 98212.99, 'new_value': 102810.74}, {'field': 'count', 'old_value': 423, 'new_value': 439}, {'field': 'instoreAmount', 'old_value': 98212.99, 'new_value': 102810.74}, {'field': 'instoreCount', 'old_value': 423, 'new_value': 439}]
2025-05-16 08:05:29,775 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-16 08:05:29,775 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 12840.2, 'new_value': 13695.38}, {'field': 'dailyBillAmount', 'old_value': 12840.2, 'new_value': 13695.38}, {'field': 'amount', 'old_value': 15449.9, 'new_value': 16352.380000000001}, {'field': 'count', 'old_value': 456, 'new_value': 483}, {'field': 'instoreAmount', 'old_value': 15449.9, 'new_value': 16352.380000000001}, {'field': 'instoreCount', 'old_value': 456, 'new_value': 483}]
2025-05-16 08:05:30,228 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-16 08:05:30,243 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156349.2, 'new_value': 172651.2}, {'field': 'amount', 'old_value': 156349.2, 'new_value': 172651.2}, {'field': 'count', 'old_value': 237, 'new_value': 256}, {'field': 'instoreAmount', 'old_value': 156349.2, 'new_value': 172651.2}, {'field': 'instoreCount', 'old_value': 237, 'new_value': 256}]
2025-05-16 08:05:30,665 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-16 08:05:30,681 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28637.7, 'new_value': 30680.9}, {'field': 'amount', 'old_value': 28637.7, 'new_value': 30680.9}, {'field': 'count', 'old_value': 240, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 28637.7, 'new_value': 30680.9}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 249}]
2025-05-16 08:05:31,150 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-16 08:05:31,150 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 177960.0, 'new_value': 221255.0}, {'field': 'amount', 'old_value': 177960.0, 'new_value': 221255.0}, {'field': 'count', 'old_value': 41, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 177960.0, 'new_value': 221255.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 44}]
2025-05-16 08:05:31,540 - INFO - 更新表单数据成功: FINST-LLF66FD1Y3AVWSRCCPLOLDIRPAOX3YH3YGHAMGB
2025-05-16 08:05:31,540 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_7930744A602B4DF1A0EB88515999F5E5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 4991.27, 'new_value': 6359.27}, {'field': 'dailyBillAmount', 'old_value': 16474.08, 'new_value': 19522.92}, {'field': 'amount', 'old_value': 4991.27, 'new_value': 6359.27}, {'field': 'count', 'old_value': 39, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 4991.27, 'new_value': 6359.27}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 45}]
2025-05-16 08:05:32,040 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-16 08:05:32,040 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 22236.4, 'new_value': 22823.600000000002}, {'field': 'count', 'old_value': 294, 'new_value': 303}, {'field': 'instoreAmount', 'old_value': 22236.4, 'new_value': 22823.600000000002}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 303}]
2025-05-16 08:05:32,509 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-16 08:05:32,509 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28964.4, 'new_value': 30410.4}, {'field': 'dailyBillAmount', 'old_value': 28964.4, 'new_value': 30410.4}, {'field': 'amount', 'old_value': 28964.4, 'new_value': 30410.4}, {'field': 'count', 'old_value': 33, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 28964.4, 'new_value': 30410.4}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 36}]
2025-05-16 08:05:33,009 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-16 08:05:33,009 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 258614.96, 'new_value': 272004.96}, {'field': 'dailyBillAmount', 'old_value': 258614.96, 'new_value': 272004.96}, {'field': 'amount', 'old_value': 271245.96, 'new_value': 284635.96}, {'field': 'count', 'old_value': 860, 'new_value': 899}, {'field': 'instoreAmount', 'old_value': 271245.96, 'new_value': 284635.96}, {'field': 'instoreCount', 'old_value': 860, 'new_value': 899}]
2025-05-16 08:05:33,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-16 08:05:33,540 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 576375.14, 'new_value': 610813.31}, {'field': 'count', 'old_value': 754, 'new_value': 797}, {'field': 'instoreAmount', 'old_value': 576375.31, 'new_value': 610813.48}, {'field': 'instoreCount', 'old_value': 754, 'new_value': 797}]
2025-05-16 08:05:34,009 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-16 08:05:34,009 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80997.0, 'new_value': 88582.4}, {'field': 'dailyBillAmount', 'old_value': 80997.0, 'new_value': 88582.4}, {'field': 'amount', 'old_value': 19356.2, 'new_value': 20270.2}, {'field': 'count', 'old_value': 76, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 19357.7, 'new_value': 20271.7}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 79}]
2025-05-16 08:05:34,415 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MGA
2025-05-16 08:05:34,431 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-04, 变更字段: [{'field': 'amount', 'old_value': 47591.0, 'new_value': 48457.0}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 47857.0, 'new_value': 48723.0}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 114}]
2025-05-16 08:05:34,900 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MPA
2025-05-16 08:05:34,900 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 141546.8, 'new_value': 143969.4}, {'field': 'count', 'old_value': 182, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 147908.7, 'new_value': 150332.1}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 183}]
2025-05-16 08:05:35,337 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-16 08:05:35,337 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126413.41, 'new_value': 129046.89}, {'field': 'amount', 'old_value': 126412.94, 'new_value': 129046.42}, {'field': 'count', 'old_value': 1249, 'new_value': 1286}, {'field': 'instoreAmount', 'old_value': 83658.18, 'new_value': 84730.37}, {'field': 'instoreCount', 'old_value': 740, 'new_value': 758}, {'field': 'onlineAmount', 'old_value': 44461.35, 'new_value': 46022.64}, {'field': 'onlineCount', 'old_value': 509, 'new_value': 528}]
2025-05-16 08:05:35,790 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-16 08:05:35,790 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 181708.07, 'new_value': 193101.0}, {'field': 'dailyBillAmount', 'old_value': 181708.07, 'new_value': 193101.0}, {'field': 'amount', 'old_value': 16913.25, 'new_value': 17873.57}, {'field': 'count', 'old_value': 554, 'new_value': 591}, {'field': 'instoreAmount', 'old_value': 19186.13, 'new_value': 20165.35}, {'field': 'instoreCount', 'old_value': 554, 'new_value': 591}]
2025-05-16 08:05:36,259 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-16 08:05:36,259 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 198292.42, 'new_value': 204147.3}, {'field': 'dailyBillAmount', 'old_value': 198292.42, 'new_value': 204147.3}, {'field': 'amount', 'old_value': 98312.41, 'new_value': 101313.62}, {'field': 'count', 'old_value': 2168, 'new_value': 2229}, {'field': 'instoreAmount', 'old_value': 83544.39, 'new_value': 85975.90000000001}, {'field': 'instoreCount', 'old_value': 1845, 'new_value': 1892}, {'field': 'onlineAmount', 'old_value': 16428.47, 'new_value': 17028.07}, {'field': 'onlineCount', 'old_value': 323, 'new_value': 337}]
2025-05-16 08:05:36,697 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-16 08:05:36,697 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 190431.8, 'new_value': 196932.3}, {'field': 'amount', 'old_value': 190430.2, 'new_value': 196930.7}, {'field': 'count', 'old_value': 737, 'new_value': 763}, {'field': 'instoreAmount', 'old_value': 192849.3, 'new_value': 199659.6}, {'field': 'instoreCount', 'old_value': 737, 'new_value': 763}]
2025-05-16 08:05:37,118 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-16 08:05:37,118 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 330254.82, 'new_value': 341196.47}, {'field': 'dailyBillAmount', 'old_value': 330254.82, 'new_value': 341196.47}, {'field': 'amount', 'old_value': 317634.31, 'new_value': 321012.91}, {'field': 'count', 'old_value': 5998, 'new_value': 6063}, {'field': 'instoreAmount', 'old_value': 297466.05, 'new_value': 300844.65}, {'field': 'instoreCount', 'old_value': 5605, 'new_value': 5670}]
2025-05-16 08:05:37,587 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-16 08:05:37,587 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156363.17, 'new_value': 165004.27}, {'field': 'amount', 'old_value': 124077.9, 'new_value': 132719.0}, {'field': 'count', 'old_value': 2942, 'new_value': 3183}, {'field': 'instoreAmount', 'old_value': 111481.1, 'new_value': 119205.1}, {'field': 'instoreCount', 'old_value': 2374, 'new_value': 2570}, {'field': 'onlineAmount', 'old_value': 12756.6, 'new_value': 13673.7}, {'field': 'onlineCount', 'old_value': 568, 'new_value': 613}]
2025-05-16 08:05:38,025 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-16 08:05:38,025 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 36849.27, 'new_value': 36637.27}, {'field': 'count', 'old_value': 807, 'new_value': 809}, {'field': 'instoreAmount', 'old_value': 1282.0, 'new_value': 1377.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 30}]
2025-05-16 08:05:38,493 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-16 08:05:38,493 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 90028.47, 'new_value': 96676.23}, {'field': 'count', 'old_value': 6057, 'new_value': 6454}, {'field': 'instoreAmount', 'old_value': 74450.72, 'new_value': 79617.78}, {'field': 'instoreCount', 'old_value': 4864, 'new_value': 5161}, {'field': 'onlineAmount', 'old_value': 17776.510000000002, 'new_value': 19323.34}, {'field': 'onlineCount', 'old_value': 1193, 'new_value': 1293}]
2025-05-16 08:05:38,962 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-16 08:05:38,962 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165749.79, 'new_value': 171565.86}, {'field': 'dailyBillAmount', 'old_value': 165749.79, 'new_value': 171565.86}, {'field': 'amount', 'old_value': 161286.01, 'new_value': 166402.54}, {'field': 'count', 'old_value': 4656, 'new_value': 4825}, {'field': 'instoreAmount', 'old_value': 162476.12, 'new_value': 167592.65}, {'field': 'instoreCount', 'old_value': 4656, 'new_value': 4825}]
2025-05-16 08:05:39,431 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-16 08:05:39,431 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44561.67, 'new_value': 47246.340000000004}, {'field': 'amount', 'old_value': 44561.67, 'new_value': 47246.340000000004}, {'field': 'count', 'old_value': 2469, 'new_value': 2604}, {'field': 'instoreAmount', 'old_value': 29954.31, 'new_value': 30722.68}, {'field': 'instoreCount', 'old_value': 1660, 'new_value': 1719}, {'field': 'onlineAmount', 'old_value': 14607.36, 'new_value': 16523.66}, {'field': 'onlineCount', 'old_value': 809, 'new_value': 885}]
2025-05-16 08:05:39,853 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-16 08:05:39,853 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81010.46, 'new_value': 84565.04}, {'field': 'dailyBillAmount', 'old_value': 81010.46, 'new_value': 84565.04}, {'field': 'amount', 'old_value': 16317.85, 'new_value': 17434.260000000002}, {'field': 'count', 'old_value': 587, 'new_value': 621}, {'field': 'instoreAmount', 'old_value': 16778.18, 'new_value': 17926.88}, {'field': 'instoreCount', 'old_value': 587, 'new_value': 621}]
2025-05-16 08:05:40,275 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-16 08:05:40,275 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65765.31, 'new_value': 70470.47}, {'field': 'dailyBillAmount', 'old_value': 65765.31, 'new_value': 70470.47}, {'field': 'amount', 'old_value': 55976.49, 'new_value': 59488.9}, {'field': 'count', 'old_value': 2773, 'new_value': 2944}, {'field': 'instoreAmount', 'old_value': 13130.0, 'new_value': 13620.2}, {'field': 'instoreCount', 'old_value': 915, 'new_value': 963}, {'field': 'onlineAmount', 'old_value': 43731.43, 'new_value': 46768.14}, {'field': 'onlineCount', 'old_value': 1858, 'new_value': 1981}]
2025-05-16 08:05:40,681 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-16 08:05:40,681 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59468.91, 'new_value': 61678.85}, {'field': 'amount', 'old_value': 59468.1, 'new_value': 61678.04}, {'field': 'count', 'old_value': 1659, 'new_value': 1707}, {'field': 'instoreAmount', 'old_value': 57444.76, 'new_value': 59569.6}, {'field': 'instoreCount', 'old_value': 1617, 'new_value': 1664}, {'field': 'onlineAmount', 'old_value': 2652.29, 'new_value': 2753.29}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 43}]
2025-05-16 08:05:41,150 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-16 08:05:41,150 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28808.510000000002, 'new_value': 39044.28}, {'field': 'dailyBillAmount', 'old_value': 28808.510000000002, 'new_value': 39044.28}, {'field': 'amount', 'old_value': 81995.24, 'new_value': 89296.05}, {'field': 'count', 'old_value': 2872, 'new_value': 3155}, {'field': 'instoreAmount', 'old_value': 82531.08, 'new_value': 89813.67}, {'field': 'instoreCount', 'old_value': 2846, 'new_value': 3126}, {'field': 'onlineAmount', 'old_value': 1305.31, 'new_value': 1470.91}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 29}]
2025-05-16 08:05:41,603 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-16 08:05:41,603 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111111.95, 'new_value': 115841.2}, {'field': 'dailyBillAmount', 'old_value': 111111.95, 'new_value': 115841.2}, {'field': 'amount', 'old_value': 71382.18000000001, 'new_value': 75386.48}, {'field': 'count', 'old_value': 6468, 'new_value': 6706}, {'field': 'instoreAmount', 'old_value': 4544.27, 'new_value': 4858.91}, {'field': 'instoreCount', 'old_value': 229, 'new_value': 246}, {'field': 'onlineAmount', 'old_value': 71010.31999999999, 'new_value': 74767.58}, {'field': 'onlineCount', 'old_value': 6239, 'new_value': 6460}]
2025-05-16 08:05:42,040 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-16 08:05:42,056 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99029.9, 'new_value': 102337.74}, {'field': 'dailyBillAmount', 'old_value': 99029.9, 'new_value': 102337.74}, {'field': 'amount', 'old_value': 83624.75, 'new_value': 86765.28}, {'field': 'count', 'old_value': 2865, 'new_value': 2966}, {'field': 'instoreAmount', 'old_value': 48064.229999999996, 'new_value': 49660.95}, {'field': 'instoreCount', 'old_value': 2102, 'new_value': 2173}, {'field': 'onlineAmount', 'old_value': 41250.200000000004, 'new_value': 43059.41}, {'field': 'onlineCount', 'old_value': 763, 'new_value': 793}]
2025-05-16 08:05:42,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-16 08:05:42,540 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31213.7, 'new_value': 33699.15}, {'field': 'dailyBillAmount', 'old_value': 31213.7, 'new_value': 33699.15}, {'field': 'amount', 'old_value': 45362.18, 'new_value': 48421.84}, {'field': 'count', 'old_value': 1777, 'new_value': 1887}, {'field': 'instoreAmount', 'old_value': 15140.369999999999, 'new_value': 15619.61}, {'field': 'instoreCount', 'old_value': 638, 'new_value': 668}, {'field': 'onlineAmount', 'old_value': 30876.82, 'new_value': 33457.24}, {'field': 'onlineCount', 'old_value': 1139, 'new_value': 1219}]
2025-05-16 08:05:42,978 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-16 08:05:42,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58754.5, 'new_value': 60193.270000000004}, {'field': 'dailyBillAmount', 'old_value': 58754.5, 'new_value': 60193.270000000004}, {'field': 'amount', 'old_value': 60396.62, 'new_value': 61920.979999999996}, {'field': 'count', 'old_value': 2143, 'new_value': 2207}, {'field': 'instoreAmount', 'old_value': 60396.62, 'new_value': 61920.979999999996}, {'field': 'instoreCount', 'old_value': 2143, 'new_value': 2207}]
2025-05-16 08:05:43,447 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-16 08:05:43,447 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 214109.0, 'new_value': 218531.0}, {'field': 'dailyBillAmount', 'old_value': 214109.0, 'new_value': 218531.0}, {'field': 'amount', 'old_value': 221927.0, 'new_value': 226637.0}, {'field': 'count', 'old_value': 181, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 241653.0, 'new_value': 246363.0}, {'field': 'instoreCount', 'old_value': 181, 'new_value': 188}]
2025-05-16 08:05:43,868 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-16 08:05:43,868 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 170556.76, 'new_value': 174121.16}, {'field': 'count', 'old_value': 309, 'new_value': 316}, {'field': 'instoreAmount', 'old_value': 172155.16, 'new_value': 176036.86}, {'field': 'instoreCount', 'old_value': 309, 'new_value': 316}]
2025-05-16 08:05:44,306 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-16 08:05:44,306 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24595.0, 'new_value': 26013.0}, {'field': 'amount', 'old_value': 24595.0, 'new_value': 26013.0}, {'field': 'count', 'old_value': 51, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 24595.0, 'new_value': 26013.0}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 54}]
2025-05-16 08:05:44,822 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-16 08:05:44,822 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49370.0, 'new_value': 51670.0}, {'field': 'dailyBillAmount', 'old_value': 49370.0, 'new_value': 51670.0}, {'field': 'amount', 'old_value': 25124.0, 'new_value': 26257.0}, {'field': 'count', 'old_value': 68, 'new_value': 71}, {'field': 'instoreAmount', 'old_value': 26287.0, 'new_value': 27420.0}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 71}]
2025-05-16 08:05:45,259 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-16 08:05:45,259 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43330.0, 'new_value': 44583.0}, {'field': 'amount', 'old_value': 41514.0, 'new_value': 42767.0}, {'field': 'count', 'old_value': 51, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 41514.0, 'new_value': 42767.0}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 53}]
2025-05-16 08:05:45,697 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-16 08:05:45,697 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46109.7, 'new_value': 47173.7}, {'field': 'amount', 'old_value': 46107.5, 'new_value': 47171.5}, {'field': 'count', 'old_value': 126, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 46109.7, 'new_value': 47173.7}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 129}]
2025-05-16 08:05:46,150 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-16 08:05:46,150 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 319633.0, 'new_value': 331330.0}, {'field': 'dailyBillAmount', 'old_value': 319633.0, 'new_value': 331330.0}, {'field': 'amount', 'old_value': 382062.0, 'new_value': 393759.0}, {'field': 'count', 'old_value': 47, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 382062.0, 'new_value': 393759.0}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 50}]
2025-05-16 08:05:46,665 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-16 08:05:46,665 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 12265.0, 'new_value': 14028.0}, {'field': 'amount', 'old_value': 12265.0, 'new_value': 14028.0}, {'field': 'count', 'old_value': 18, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 12265.0, 'new_value': 14028.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 21}]
2025-05-16 08:05:47,118 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-16 08:05:47,134 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43784.0, 'new_value': 46182.0}, {'field': 'amount', 'old_value': 43784.0, 'new_value': 46182.0}, {'field': 'count', 'old_value': 53, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 43784.0, 'new_value': 46182.0}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 55}]
2025-05-16 08:05:47,618 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-16 08:05:47,618 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118183.9, 'new_value': 125542.6}, {'field': 'dailyBillAmount', 'old_value': 118183.9, 'new_value': 125542.6}, {'field': 'amount', 'old_value': 210241.8, 'new_value': 217599.6}, {'field': 'count', 'old_value': 268, 'new_value': 276}, {'field': 'instoreAmount', 'old_value': 216744.26, 'new_value': 224102.06}, {'field': 'instoreCount', 'old_value': 268, 'new_value': 276}]
2025-05-16 08:05:48,087 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-16 08:05:48,087 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68849.61, 'new_value': 71338.81}, {'field': 'dailyBillAmount', 'old_value': 68849.61, 'new_value': 71338.81}, {'field': 'amount', 'old_value': 9772.74, 'new_value': 9798.640000000001}, {'field': 'count', 'old_value': 90, 'new_value': 93}, {'field': 'onlineAmount', 'old_value': 1858.3, 'new_value': 2142.1}, {'field': 'onlineCount', 'old_value': 32, 'new_value': 35}]
2025-05-16 08:05:48,525 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-16 08:05:48,525 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 8545.0, 'new_value': 9139.0}, {'field': 'amount', 'old_value': 8545.0, 'new_value': 9139.0}, {'field': 'count', 'old_value': 22, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 8545.0, 'new_value': 9139.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 24}]
2025-05-16 08:05:50,134 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-16 08:05:50,134 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23616.0, 'new_value': 23795.0}, {'field': 'dailyBillAmount', 'old_value': 23616.0, 'new_value': 23795.0}, {'field': 'amount', 'old_value': 27269.0, 'new_value': 27448.0}, {'field': 'count', 'old_value': 87, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 27269.0, 'new_value': 27448.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 88}]
2025-05-16 08:05:50,571 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-16 08:05:50,571 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20607.2, 'new_value': 21238.2}, {'field': 'amount', 'old_value': 20607.2, 'new_value': 21238.2}, {'field': 'count', 'old_value': 121, 'new_value': 124}, {'field': 'instoreAmount', 'old_value': 20945.2, 'new_value': 21576.2}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 124}]
2025-05-16 08:05:51,009 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-16 08:05:51,009 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 4699.0, 'new_value': 4966.0}, {'field': 'dailyBillAmount', 'old_value': 4699.0, 'new_value': 4966.0}, {'field': 'amount', 'old_value': 27207.0, 'new_value': 28540.0}, {'field': 'count', 'old_value': 80, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 27982.0, 'new_value': 29315.0}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 85}]
2025-05-16 08:05:51,478 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-16 08:05:51,478 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 498267.15, 'new_value': 513327.12}, {'field': 'dailyBillAmount', 'old_value': 498267.15, 'new_value': 513327.12}, {'field': 'amount', 'old_value': 33432.84, 'new_value': 34485.93}, {'field': 'count', 'old_value': 300, 'new_value': 312}, {'field': 'instoreAmount', 'old_value': 27915.86, 'new_value': 28641.53}, {'field': 'instoreCount', 'old_value': 212, 'new_value': 219}, {'field': 'onlineAmount', 'old_value': 6329.5, 'new_value': 6656.92}, {'field': 'onlineCount', 'old_value': 88, 'new_value': 93}]
2025-05-16 08:05:51,978 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-16 08:05:51,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44511.0, 'new_value': 52467.0}, {'field': 'amount', 'old_value': 44311.0, 'new_value': 52267.0}, {'field': 'count', 'old_value': 60, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 44810.0, 'new_value': 52766.0}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 63}]
2025-05-16 08:05:52,400 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-16 08:05:52,400 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 11948.0, 'new_value': 13102.0}, {'field': 'amount', 'old_value': 11948.0, 'new_value': 13102.0}, {'field': 'count', 'old_value': 19, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 11948.0, 'new_value': 13102.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 21}]
2025-05-16 08:05:52,868 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-16 08:05:52,868 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14432.76, 'new_value': 14867.36}, {'field': 'amount', 'old_value': 14432.06, 'new_value': 14866.66}, {'field': 'count', 'old_value': 56, 'new_value': 58}, {'field': 'instoreAmount', 'old_value': 14432.76, 'new_value': 14867.36}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 58}]
2025-05-16 08:05:53,290 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-16 08:05:53,290 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28515.0, 'new_value': 29345.0}, {'field': 'dailyBillAmount', 'old_value': 28515.0, 'new_value': 29345.0}, {'field': 'amount', 'old_value': 28541.0, 'new_value': 29371.0}, {'field': 'count', 'old_value': 70, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 28991.0, 'new_value': 29821.0}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 72}]
2025-05-16 08:05:53,712 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-16 08:05:53,712 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 225359.13, 'new_value': 231180.63}, {'field': 'dailyBillAmount', 'old_value': 210249.27, 'new_value': 215857.66}, {'field': 'amount', 'old_value': 223582.6, 'new_value': 229404.1}, {'field': 'count', 'old_value': 431, 'new_value': 447}, {'field': 'instoreAmount', 'old_value': 225614.72, 'new_value': 231436.22}, {'field': 'instoreCount', 'old_value': 431, 'new_value': 447}]
2025-05-16 08:05:54,181 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-16 08:05:54,181 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6557.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6557.5}, {'field': 'amount', 'old_value': 4270.52, 'new_value': 9820.02}, {'field': 'count', 'old_value': 23, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 4270.52, 'new_value': 9820.02}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 57}]
2025-05-16 08:05:54,650 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-16 08:05:54,650 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57722.05, 'new_value': 61502.97}, {'field': 'dailyBillAmount', 'old_value': 57722.05, 'new_value': 61502.97}, {'field': 'amount', 'old_value': 24344.67, 'new_value': 24924.07}, {'field': 'count', 'old_value': 2324, 'new_value': 2414}, {'field': 'instoreAmount', 'old_value': 26001.62, 'new_value': 26623.82}, {'field': 'instoreCount', 'old_value': 2324, 'new_value': 2414}]
2025-05-16 08:05:55,118 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-16 08:05:55,118 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 371942.39, 'new_value': 385029.7}, {'field': 'dailyBillAmount', 'old_value': 371942.39, 'new_value': 385029.7}, {'field': 'amount', 'old_value': 385016.21, 'new_value': 397956.05}, {'field': 'count', 'old_value': 3397, 'new_value': 3574}, {'field': 'instoreAmount', 'old_value': 297718.73, 'new_value': 306667.17}, {'field': 'instoreCount', 'old_value': 1391, 'new_value': 1440}, {'field': 'onlineAmount', 'old_value': 89968.14, 'new_value': 94012.64}, {'field': 'onlineCount', 'old_value': 2006, 'new_value': 2134}]
2025-05-16 08:05:55,587 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-16 08:05:55,587 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116013.36, 'new_value': 118728.76}, {'field': 'amount', 'old_value': 116013.36, 'new_value': 118728.76}, {'field': 'count', 'old_value': 762, 'new_value': 783}, {'field': 'instoreAmount', 'old_value': 116122.36, 'new_value': 118837.76}, {'field': 'instoreCount', 'old_value': 762, 'new_value': 783}]
2025-05-16 08:05:56,025 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-16 08:05:56,025 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51480.34, 'new_value': 55456.83}, {'field': 'dailyBillAmount', 'old_value': 51480.34, 'new_value': 55456.83}, {'field': 'amount', 'old_value': 61119.23, 'new_value': 65788.01}, {'field': 'count', 'old_value': 2508, 'new_value': 2736}, {'field': 'instoreAmount', 'old_value': 31580.89, 'new_value': 33502.68}, {'field': 'instoreCount', 'old_value': 1414, 'new_value': 1543}, {'field': 'onlineAmount', 'old_value': 30042.88, 'new_value': 32859.62}, {'field': 'onlineCount', 'old_value': 1094, 'new_value': 1193}]
2025-05-16 08:05:56,431 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-16 08:05:56,431 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21319.0, 'new_value': 21449.0}, {'field': 'amount', 'old_value': 21319.0, 'new_value': 21449.0}, {'field': 'count', 'old_value': 12, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 21319.0, 'new_value': 21449.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 14}]
2025-05-16 08:05:56,931 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-16 08:05:56,931 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 79284.04, 'new_value': 82049.16}, {'field': 'dailyBillAmount', 'old_value': 79284.04, 'new_value': 82049.16}, {'field': 'amount', 'old_value': 35903.69, 'new_value': 37797.9}, {'field': 'count', 'old_value': 2314, 'new_value': 2417}, {'field': 'instoreAmount', 'old_value': 5967.1, 'new_value': 5980.9}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 240}, {'field': 'onlineAmount', 'old_value': 29936.59, 'new_value': 31817.0}, {'field': 'onlineCount', 'old_value': 2078, 'new_value': 2177}]
2025-05-16 08:05:57,353 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-16 08:05:57,353 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204122.21, 'new_value': 212070.74}, {'field': 'dailyBillAmount', 'old_value': 204122.21, 'new_value': 212070.74}, {'field': 'amount', 'old_value': 192367.35, 'new_value': 199759.36}, {'field': 'count', 'old_value': 1545, 'new_value': 1633}, {'field': 'instoreAmount', 'old_value': 143416.35, 'new_value': 148565.15}, {'field': 'instoreCount', 'old_value': 684, 'new_value': 715}, {'field': 'onlineAmount', 'old_value': 48951.2, 'new_value': 51194.409999999996}, {'field': 'onlineCount', 'old_value': 861, 'new_value': 918}]
2025-05-16 08:05:57,759 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-16 08:05:57,759 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 237877.55, 'new_value': 245660.68}, {'field': 'dailyBillAmount', 'old_value': 237877.55, 'new_value': 245660.68}, {'field': 'amount', 'old_value': 243597.8, 'new_value': 251795.9}, {'field': 'count', 'old_value': 1431, 'new_value': 1490}, {'field': 'instoreAmount', 'old_value': 223357.0, 'new_value': 229772.7}, {'field': 'instoreCount', 'old_value': 1211, 'new_value': 1254}, {'field': 'onlineAmount', 'old_value': 24741.0, 'new_value': 26523.4}, {'field': 'onlineCount', 'old_value': 220, 'new_value': 236}]
2025-05-16 08:05:58,150 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-16 08:05:58,150 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'amount', 'old_value': 653620.9, 'new_value': 685856.84}, {'field': 'count', 'old_value': 3321, 'new_value': 3542}, {'field': 'instoreAmount', 'old_value': 497573.28, 'new_value': 520544.02}, {'field': 'instoreCount', 'old_value': 1922, 'new_value': 2024}, {'field': 'onlineAmount', 'old_value': 160338.44, 'new_value': 169853.64}, {'field': 'onlineCount', 'old_value': 1399, 'new_value': 1518}]
2025-05-16 08:05:58,650 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-16 08:05:58,650 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 179001.36, 'new_value': 184344.31}, {'field': 'dailyBillAmount', 'old_value': 179001.36, 'new_value': 184344.31}, {'field': 'amount', 'old_value': 250692.38999999998, 'new_value': 259039.38999999998}, {'field': 'count', 'old_value': 1194, 'new_value': 1244}, {'field': 'instoreAmount', 'old_value': 235387.4, 'new_value': 243131.2}, {'field': 'instoreCount', 'old_value': 947, 'new_value': 985}, {'field': 'onlineAmount', 'old_value': 15509.79, 'new_value': 16112.99}, {'field': 'onlineCount', 'old_value': 247, 'new_value': 259}]
2025-05-16 08:05:59,087 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-16 08:05:59,087 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 240635.42, 'new_value': 245296.43000000002}, {'field': 'dailyBillAmount', 'old_value': 240635.42, 'new_value': 245296.43000000002}, {'field': 'amount', 'old_value': 227814.8, 'new_value': 231996.6}, {'field': 'count', 'old_value': 976, 'new_value': 1008}, {'field': 'instoreAmount', 'old_value': 231712.5, 'new_value': 235894.3}, {'field': 'instoreCount', 'old_value': 976, 'new_value': 1008}]
2025-05-16 08:05:59,712 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-16 08:05:59,712 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 485607.66000000003, 'new_value': 500436.92}, {'field': 'amount', 'old_value': 485607.66000000003, 'new_value': 500436.92}, {'field': 'count', 'old_value': 3722, 'new_value': 3880}, {'field': 'instoreAmount', 'old_value': 485607.66000000003, 'new_value': 500436.92}, {'field': 'instoreCount', 'old_value': 3722, 'new_value': 3880}]
2025-05-16 08:06:00,150 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-16 08:06:00,150 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 378291.87, 'new_value': 395710.58}, {'field': 'dailyBillAmount', 'old_value': 378291.87, 'new_value': 395710.58}, {'field': 'amount', 'old_value': 459946.77, 'new_value': 480313.74}, {'field': 'count', 'old_value': 3082, 'new_value': 3246}, {'field': 'instoreAmount', 'old_value': 259513.9, 'new_value': 267404.7}, {'field': 'instoreCount', 'old_value': 1314, 'new_value': 1358}, {'field': 'onlineAmount', 'old_value': 205645.9, 'new_value': 218502.9}, {'field': 'onlineCount', 'old_value': 1768, 'new_value': 1888}]
2025-05-16 08:06:00,556 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-16 08:06:00,556 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 227266.41, 'new_value': 236626.33}, {'field': 'dailyBillAmount', 'old_value': 227266.41, 'new_value': 236626.33}, {'field': 'amount', 'old_value': 300322.98, 'new_value': 308928.34}, {'field': 'count', 'old_value': 3090, 'new_value': 3245}, {'field': 'instoreAmount', 'old_value': 214599.62, 'new_value': 218552.82}, {'field': 'instoreCount', 'old_value': 1408, 'new_value': 1448}, {'field': 'onlineAmount', 'old_value': 86703.92, 'new_value': 91356.08}, {'field': 'onlineCount', 'old_value': 1682, 'new_value': 1797}]
2025-05-16 08:06:00,978 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-16 08:06:00,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 300900.38, 'new_value': 315640.13}, {'field': 'dailyBillAmount', 'old_value': 300900.38, 'new_value': 315640.13}, {'field': 'amount', 'old_value': 304849.46, 'new_value': 319818.69}, {'field': 'count', 'old_value': 2795, 'new_value': 2980}, {'field': 'instoreAmount', 'old_value': 266395.62, 'new_value': 278707.68}, {'field': 'instoreCount', 'old_value': 1395, 'new_value': 1491}, {'field': 'onlineAmount', 'old_value': 39228.5, 'new_value': 41885.67}, {'field': 'onlineCount', 'old_value': 1400, 'new_value': 1489}]
2025-05-16 08:06:01,415 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-16 08:06:01,415 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80218.8, 'new_value': 84693.8}, {'field': 'amount', 'old_value': 80218.3, 'new_value': 84693.3}, {'field': 'count', 'old_value': 352, 'new_value': 369}, {'field': 'instoreAmount', 'old_value': 80218.8, 'new_value': 84693.8}, {'field': 'instoreCount', 'old_value': 352, 'new_value': 369}]
2025-05-16 08:06:01,853 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-16 08:06:01,853 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228460.98, 'new_value': 233067.59}, {'field': 'dailyBillAmount', 'old_value': 228460.98, 'new_value': 233067.59}, {'field': 'amount', 'old_value': -160355.19, 'new_value': -163976.68}, {'field': 'count', 'old_value': 622, 'new_value': 651}, {'field': 'instoreAmount', 'old_value': 4607.7, 'new_value': 4753.6}, {'field': 'instoreCount', 'old_value': 197, 'new_value': 207}, {'field': 'onlineAmount', 'old_value': 13078.61, 'new_value': 13669.72}, {'field': 'onlineCount', 'old_value': 425, 'new_value': 444}]
2025-05-16 08:06:02,353 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-16 08:06:02,353 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 365844.26, 'new_value': 382361.52999999997}, {'field': 'dailyBillAmount', 'old_value': 365844.26, 'new_value': 382361.52999999997}, {'field': 'amount', 'old_value': 298726.82, 'new_value': 308103.94}, {'field': 'count', 'old_value': 1204, 'new_value': 1247}, {'field': 'instoreAmount', 'old_value': 298726.82, 'new_value': 308103.94}, {'field': 'instoreCount', 'old_value': 1204, 'new_value': 1247}]
2025-05-16 08:06:02,759 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-16 08:06:02,759 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'amount', 'old_value': 103318.5, 'new_value': 106674.1}, {'field': 'count', 'old_value': 431, 'new_value': 445}, {'field': 'instoreAmount', 'old_value': 108419.2, 'new_value': 111762.8}, {'field': 'instoreCount', 'old_value': 416, 'new_value': 429}, {'field': 'onlineAmount', 'old_value': 1187.8999999999999, 'new_value': 1199.8999999999999}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 16}]
2025-05-16 08:06:03,181 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-16 08:06:03,181 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 190776.45, 'new_value': 194877.30000000002}, {'field': 'dailyBillAmount', 'old_value': 190776.45, 'new_value': 194877.30000000002}, {'field': 'amount', 'old_value': 186662.72, 'new_value': 190370.35}, {'field': 'count', 'old_value': 1176, 'new_value': 1223}, {'field': 'instoreAmount', 'old_value': 176812.21, 'new_value': 180050.52}, {'field': 'instoreCount', 'old_value': 929, 'new_value': 958}, {'field': 'onlineAmount', 'old_value': 9993.27, 'new_value': 10462.59}, {'field': 'onlineCount', 'old_value': 247, 'new_value': 265}]
2025-05-16 08:06:03,603 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-16 08:06:03,603 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204762.97, 'new_value': 212106.27}, {'field': 'dailyBillAmount', 'old_value': 204762.97, 'new_value': 212106.27}, {'field': 'amount', 'old_value': 90883.4, 'new_value': 93511.9}, {'field': 'count', 'old_value': 1318, 'new_value': 1385}, {'field': 'instoreAmount', 'old_value': 54280.94, 'new_value': 55496.96}, {'field': 'instoreCount', 'old_value': 371, 'new_value': 386}, {'field': 'onlineAmount', 'old_value': 36604.2, 'new_value': 38016.68}, {'field': 'onlineCount', 'old_value': 947, 'new_value': 999}]
2025-05-16 08:06:03,962 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-16 08:06:03,962 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76436.37, 'new_value': 82157.69}, {'field': 'amount', 'old_value': 76430.97, 'new_value': 82152.29000000001}, {'field': 'count', 'old_value': 3396, 'new_value': 3658}, {'field': 'instoreAmount', 'old_value': 30150.39, 'new_value': 31726.35}, {'field': 'instoreCount', 'old_value': 1148, 'new_value': 1218}, {'field': 'onlineAmount', 'old_value': 49914.06, 'new_value': 54303.9}, {'field': 'onlineCount', 'old_value': 2248, 'new_value': 2440}]
2025-05-16 08:06:04,431 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-16 08:06:04,431 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28043.9, 'new_value': 29033.9}, {'field': 'amount', 'old_value': 28043.9, 'new_value': 29033.9}, {'field': 'count', 'old_value': 126, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 28043.9, 'new_value': 29033.9}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 130}]
2025-05-16 08:06:04,931 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-16 08:06:04,931 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 240049.37, 'new_value': 249368.77}, {'field': 'dailyBillAmount', 'old_value': 240049.37, 'new_value': 249368.77}, {'field': 'amount', 'old_value': 93604.1, 'new_value': 97443.0}, {'field': 'count', 'old_value': 1749, 'new_value': 1814}, {'field': 'instoreAmount', 'old_value': 94517.9, 'new_value': 98378.4}, {'field': 'instoreCount', 'old_value': 1749, 'new_value': 1814}]
2025-05-16 08:06:05,431 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-16 08:06:05,431 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 97094.36, 'new_value': 99778.36}, {'field': 'amount', 'old_value': 97094.36, 'new_value': 99778.36}, {'field': 'count', 'old_value': 2297, 'new_value': 2371}, {'field': 'instoreAmount', 'old_value': 97094.36, 'new_value': 99778.36}, {'field': 'instoreCount', 'old_value': 2297, 'new_value': 2371}]
2025-05-16 08:06:05,884 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-16 08:06:05,884 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16082.42, 'new_value': 16839.97}, {'field': 'amount', 'old_value': 16080.88, 'new_value': 16838.43}, {'field': 'count', 'old_value': 915, 'new_value': 959}, {'field': 'instoreAmount', 'old_value': 9815.16, 'new_value': 10026.96}, {'field': 'instoreCount', 'old_value': 481, 'new_value': 492}, {'field': 'onlineAmount', 'old_value': 6458.46, 'new_value': 7021.21}, {'field': 'onlineCount', 'old_value': 434, 'new_value': 467}]
2025-05-16 08:06:06,321 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-16 08:06:06,321 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28482.8, 'new_value': 28743.600000000002}, {'field': 'amount', 'old_value': 28482.8, 'new_value': 28743.600000000002}, {'field': 'count', 'old_value': 71, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 28482.8, 'new_value': 28743.600000000002}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 72}]
2025-05-16 08:06:06,790 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG81
2025-05-16 08:06:06,790 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-05, 变更字段: [{'field': 'amount', 'old_value': 49211.44, 'new_value': 52270.33}, {'field': 'count', 'old_value': 1881, 'new_value': 2014}, {'field': 'instoreAmount', 'old_value': 49457.94, 'new_value': 52586.23}, {'field': 'instoreCount', 'old_value': 1881, 'new_value': 2014}]
2025-05-16 08:06:07,212 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-16 08:06:07,212 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104918.34, 'new_value': 110501.70999999999}, {'field': 'dailyBillAmount', 'old_value': 88239.9, 'new_value': 92519.5}, {'field': 'amount', 'old_value': 104917.66, 'new_value': 110501.03}, {'field': 'count', 'old_value': 1457, 'new_value': 1545}, {'field': 'instoreAmount', 'old_value': 101425.7, 'new_value': 106763.3}, {'field': 'instoreCount', 'old_value': 1291, 'new_value': 1367}, {'field': 'onlineAmount', 'old_value': 3671.14, 'new_value': 3925.61}, {'field': 'onlineCount', 'old_value': 166, 'new_value': 178}]
2025-05-16 08:06:07,712 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-16 08:06:07,712 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16563.36, 'new_value': 17125.16}, {'field': 'amount', 'old_value': 16562.56, 'new_value': 17124.36}, {'field': 'count', 'old_value': 705, 'new_value': 729}, {'field': 'instoreAmount', 'old_value': 14315.56, 'new_value': 14643.16}, {'field': 'instoreCount', 'old_value': 643, 'new_value': 660}, {'field': 'onlineAmount', 'old_value': 2288.0, 'new_value': 2522.2}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 69}]
2025-05-16 08:06:08,196 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-16 08:06:08,196 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221789.12, 'new_value': 234118.63}, {'field': 'dailyBillAmount', 'old_value': 221789.12, 'new_value': 234118.63}, {'field': 'amount', 'old_value': 282326.12, 'new_value': 299570.94}, {'field': 'count', 'old_value': 2800, 'new_value': 3029}, {'field': 'instoreAmount', 'old_value': 267231.47000000003, 'new_value': 283348.84}, {'field': 'instoreCount', 'old_value': 1913, 'new_value': 2078}, {'field': 'onlineAmount', 'old_value': 22471.22, 'new_value': 23993.75}, {'field': 'onlineCount', 'old_value': 887, 'new_value': 951}]
2025-05-16 08:06:08,665 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK81
2025-05-16 08:06:08,665 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26768.1, 'new_value': 27413.0}, {'field': 'dailyBillAmount', 'old_value': 26768.1, 'new_value': 27413.0}, {'field': 'amount', 'old_value': 26659.4, 'new_value': 27304.3}, {'field': 'count', 'old_value': 139, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 26659.4, 'new_value': 27304.3}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 142}]
2025-05-16 08:06:09,134 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-16 08:06:09,134 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93627.58, 'new_value': 96444.44}, {'field': 'dailyBillAmount', 'old_value': 93627.58, 'new_value': 96444.44}, {'field': 'amount', 'old_value': 22308.13, 'new_value': 23432.49}, {'field': 'count', 'old_value': 347, 'new_value': 372}, {'field': 'instoreAmount', 'old_value': 14093.74, 'new_value': 14296.73}, {'field': 'instoreCount', 'old_value': 183, 'new_value': 193}, {'field': 'onlineAmount', 'old_value': 8853.56, 'new_value': 9779.05}, {'field': 'onlineCount', 'old_value': 164, 'new_value': 179}]
2025-05-16 08:06:09,603 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-16 08:06:09,603 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87629.79, 'new_value': 93657.86}, {'field': 'dailyBillAmount', 'old_value': 81203.75, 'new_value': 87288.19}, {'field': 'amount', 'old_value': 87629.2, 'new_value': 93657.27}, {'field': 'count', 'old_value': 5045, 'new_value': 5374}, {'field': 'instoreAmount', 'old_value': 54490.09, 'new_value': 58076.71}, {'field': 'instoreCount', 'old_value': 3116, 'new_value': 3309}, {'field': 'onlineAmount', 'old_value': 34487.47, 'new_value': 36928.92}, {'field': 'onlineCount', 'old_value': 1929, 'new_value': 2065}]
2025-05-16 08:06:10,087 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-16 08:06:10,087 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45651.33, 'new_value': 48702.12}, {'field': 'amount', 'old_value': 45650.07, 'new_value': 48700.86}, {'field': 'count', 'old_value': 2760, 'new_value': 2940}, {'field': 'instoreAmount', 'old_value': 22245.87, 'new_value': 23455.46}, {'field': 'instoreCount', 'old_value': 1225, 'new_value': 1298}, {'field': 'onlineAmount', 'old_value': 24559.96, 'new_value': 26401.16}, {'field': 'onlineCount', 'old_value': 1535, 'new_value': 1642}]
2025-05-16 08:06:10,524 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-16 08:06:10,524 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 91402.46, 'new_value': 94016.26}, {'field': 'count', 'old_value': 881, 'new_value': 917}, {'field': 'instoreAmount', 'old_value': 91492.96, 'new_value': 94130.76}, {'field': 'instoreCount', 'old_value': 881, 'new_value': 917}]
2025-05-16 08:06:10,978 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-16 08:06:10,978 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76684.76, 'new_value': 80174.82}, {'field': 'dailyBillAmount', 'old_value': 79159.18, 'new_value': 82716.84}, {'field': 'amount', 'old_value': 76683.88, 'new_value': 80173.94}, {'field': 'count', 'old_value': 1473, 'new_value': 1543}, {'field': 'instoreAmount', 'old_value': 73483.41, 'new_value': 76826.58}, {'field': 'instoreCount', 'old_value': 1247, 'new_value': 1303}, {'field': 'onlineAmount', 'old_value': 3235.71, 'new_value': 3382.6}, {'field': 'onlineCount', 'old_value': 226, 'new_value': 240}]
2025-05-16 08:06:11,399 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-16 08:06:11,399 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 103311.69, 'new_value': 115053.42}, {'field': 'dailyBillAmount', 'old_value': 103311.69, 'new_value': 115053.42}, {'field': 'amount', 'old_value': 13548.300000000001, 'new_value': 14969.85}, {'field': 'count', 'old_value': 522, 'new_value': 577}, {'field': 'instoreAmount', 'old_value': 16016.45, 'new_value': 17584.38}, {'field': 'instoreCount', 'old_value': 522, 'new_value': 577}]
2025-05-16 08:06:11,899 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-16 08:06:11,899 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 268630.79, 'new_value': 287965.39}, {'field': 'dailyBillAmount', 'old_value': 268630.79, 'new_value': 287965.39}, {'field': 'amount', 'old_value': 25701.0, 'new_value': 27299.7}, {'field': 'count', 'old_value': 123, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 25881.8, 'new_value': 27480.5}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 132}]
2025-05-16 08:06:12,337 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-16 08:06:12,337 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 10007.23, 'new_value': 10725.88}, {'field': 'count', 'old_value': 512, 'new_value': 549}, {'field': 'onlineAmount', 'old_value': 10093.49, 'new_value': 10812.14}, {'field': 'onlineCount', 'old_value': 512, 'new_value': 549}]
2025-05-16 08:06:12,743 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-16 08:06:12,743 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 138441.47, 'new_value': 152912.15}, {'field': 'amount', 'old_value': 138287.69, 'new_value': 152758.37}, {'field': 'count', 'old_value': 1483, 'new_value': 1648}, {'field': 'instoreAmount', 'old_value': 131291.3, 'new_value': 144769.5}, {'field': 'instoreCount', 'old_value': 1260, 'new_value': 1400}, {'field': 'onlineAmount', 'old_value': 8917.56, 'new_value': 10048.04}, {'field': 'onlineCount', 'old_value': 223, 'new_value': 248}]
2025-05-16 08:06:13,181 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-16 08:06:13,181 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95876.29, 'new_value': 105281.53}, {'field': 'dailyBillAmount', 'old_value': 92075.81999999999, 'new_value': 101481.06}, {'field': 'amount', 'old_value': 81467.35, 'new_value': 86732.24}, {'field': 'count', 'old_value': 2979, 'new_value': 3191}, {'field': 'instoreAmount', 'old_value': 40409.73, 'new_value': 41999.28}, {'field': 'instoreCount', 'old_value': 1390, 'new_value': 1460}, {'field': 'onlineAmount', 'old_value': 42085.5, 'new_value': 45760.840000000004}, {'field': 'onlineCount', 'old_value': 1589, 'new_value': 1731}]
2025-05-16 08:06:13,665 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-16 08:06:13,665 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15170.75, 'new_value': 26801.149999999998}, {'field': 'dailyBillAmount', 'old_value': 15170.75, 'new_value': 26801.149999999998}, {'field': 'amount', 'old_value': 647.62, 'new_value': 1092.11}, {'field': 'count', 'old_value': 25, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 647.62, 'new_value': 1092.11}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 45}]
2025-05-16 08:06:14,118 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-16 08:06:14,118 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 3625.08, 'new_value': 3863.02}, {'field': 'count', 'old_value': 153, 'new_value': 166}, {'field': 'onlineAmount', 'old_value': 3625.08, 'new_value': 3863.02}, {'field': 'onlineCount', 'old_value': 153, 'new_value': 166}]
2025-05-16 08:06:14,540 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-16 08:06:14,540 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61107.36, 'new_value': 64290.46}, {'field': 'dailyBillAmount', 'old_value': 32368.920000000002, 'new_value': 33534.82}, {'field': 'amount', 'old_value': 61107.36, 'new_value': 64290.46}, {'field': 'count', 'old_value': 1494, 'new_value': 1575}, {'field': 'instoreAmount', 'old_value': 34509.37, 'new_value': 35585.340000000004}, {'field': 'instoreCount', 'old_value': 821, 'new_value': 858}, {'field': 'onlineAmount', 'old_value': 28186.77, 'new_value': 30342.9}, {'field': 'onlineCount', 'old_value': 673, 'new_value': 717}]
2025-05-16 08:06:15,009 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-16 08:06:15,009 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15606.1, 'new_value': 20099.77}, {'field': 'amount', 'old_value': 15606.099999999999, 'new_value': 20099.77}, {'field': 'count', 'old_value': 592, 'new_value': 749}, {'field': 'instoreAmount', 'old_value': 15784.5, 'new_value': 20309.97}, {'field': 'instoreCount', 'old_value': 592, 'new_value': 749}]
2025-05-16 08:06:15,524 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-16 08:06:15,524 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28518.63, 'new_value': 30776.739999999998}, {'field': 'dailyBillAmount', 'old_value': 28518.63, 'new_value': 30776.739999999998}, {'field': 'amount', 'old_value': 25361.8, 'new_value': 26750.37}, {'field': 'count', 'old_value': 1091, 'new_value': 1159}, {'field': 'instoreAmount', 'old_value': 15589.05, 'new_value': 16382.02}, {'field': 'instoreCount', 'old_value': 536, 'new_value': 561}, {'field': 'onlineAmount', 'old_value': 9797.86, 'new_value': 10393.46}, {'field': 'onlineCount', 'old_value': 555, 'new_value': 598}]
2025-05-16 08:06:16,056 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-16 08:06:16,056 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48768.71, 'new_value': 52753.81}, {'field': 'amount', 'old_value': 48768.71, 'new_value': 52753.81}, {'field': 'count', 'old_value': 1417, 'new_value': 1541}, {'field': 'instoreAmount', 'old_value': 20295.11, 'new_value': 21830.75}, {'field': 'instoreCount', 'old_value': 692, 'new_value': 747}, {'field': 'onlineAmount', 'old_value': 28506.6, 'new_value': 30956.06}, {'field': 'onlineCount', 'old_value': 725, 'new_value': 794}]
2025-05-16 08:06:16,571 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-16 08:06:16,571 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30375.36, 'new_value': 32706.3}, {'field': 'amount', 'old_value': 30374.46, 'new_value': 32705.4}, {'field': 'count', 'old_value': 712, 'new_value': 749}, {'field': 'instoreAmount', 'old_value': 25087.2, 'new_value': 26409.5}, {'field': 'instoreCount', 'old_value': 577, 'new_value': 609}, {'field': 'onlineAmount', 'old_value': 5582.69, 'new_value': 6629.33}, {'field': 'onlineCount', 'old_value': 135, 'new_value': 140}]
2025-05-16 08:06:17,056 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-16 08:06:17,056 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 138912.16, 'new_value': 149873.59}, {'field': 'dailyBillAmount', 'old_value': 138912.16, 'new_value': 149873.59}, {'field': 'amount', 'old_value': 92099.31, 'new_value': 98997.59}, {'field': 'count', 'old_value': 2287, 'new_value': 2458}, {'field': 'instoreAmount', 'old_value': 59437.72, 'new_value': 64108.12}, {'field': 'instoreCount', 'old_value': 1172, 'new_value': 1258}, {'field': 'onlineAmount', 'old_value': 39709.42, 'new_value': 42541.19}, {'field': 'onlineCount', 'old_value': 1115, 'new_value': 1200}]
2025-05-16 08:06:17,524 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-16 08:06:17,524 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 418982.84, 'new_value': 437097.46}, {'field': 'dailyBillAmount', 'old_value': 418982.84, 'new_value': 437097.46}, {'field': 'amount', 'old_value': 399805.2, 'new_value': 413328.1}, {'field': 'count', 'old_value': 2318, 'new_value': 2406}, {'field': 'instoreAmount', 'old_value': 298053.4, 'new_value': 306892.1}, {'field': 'instoreCount', 'old_value': 1845, 'new_value': 1912}, {'field': 'onlineAmount', 'old_value': 101753.7, 'new_value': 106437.9}, {'field': 'onlineCount', 'old_value': 473, 'new_value': 494}]
2025-05-16 08:06:17,931 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-16 08:06:17,931 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 610142.1, 'new_value': 647790.48}, {'field': 'amount', 'old_value': 610141.6, 'new_value': 647789.98}, {'field': 'count', 'old_value': 2117, 'new_value': 2244}, {'field': 'instoreAmount', 'old_value': 610142.1, 'new_value': 647790.48}, {'field': 'instoreCount', 'old_value': 2117, 'new_value': 2244}]
2025-05-16 08:06:18,478 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-16 08:06:18,478 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 353206.89, 'new_value': 376301.33999999997}, {'field': 'dailyBillAmount', 'old_value': 314419.73, 'new_value': 334836.8}, {'field': 'amount', 'old_value': 353206.89, 'new_value': 376301.34}, {'field': 'count', 'old_value': 2129, 'new_value': 2290}, {'field': 'instoreAmount', 'old_value': 321209.77, 'new_value': 342036.41000000003}, {'field': 'instoreCount', 'old_value': 1359, 'new_value': 1451}, {'field': 'onlineAmount', 'old_value': 32148.03, 'new_value': 34481.58}, {'field': 'onlineCount', 'old_value': 770, 'new_value': 839}]
2025-05-16 08:06:18,962 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-16 08:06:18,962 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 387653.32, 'new_value': 402887.35000000003}, {'field': 'dailyBillAmount', 'old_value': 372875.17, 'new_value': 387125.2}, {'field': 'amount', 'old_value': 387653.32, 'new_value': 402887.35000000003}, {'field': 'count', 'old_value': 838, 'new_value': 880}, {'field': 'instoreAmount', 'old_value': 366560.7, 'new_value': 380540.7}, {'field': 'instoreCount', 'old_value': 661, 'new_value': 693}, {'field': 'onlineAmount', 'old_value': 21219.9, 'new_value': 22473.93}, {'field': 'onlineCount', 'old_value': 177, 'new_value': 187}]
2025-05-16 08:06:19,446 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-16 08:06:19,446 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 405941.95, 'new_value': 428043.13}, {'field': 'amount', 'old_value': 405941.95, 'new_value': 428043.13}, {'field': 'count', 'old_value': 1927, 'new_value': 2079}, {'field': 'instoreAmount', 'old_value': 384917.85, 'new_value': 405637.2}, {'field': 'instoreCount', 'old_value': 1344, 'new_value': 1443}, {'field': 'onlineAmount', 'old_value': 21069.670000000002, 'new_value': 22451.5}, {'field': 'onlineCount', 'old_value': 583, 'new_value': 636}]
2025-05-16 08:06:19,978 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-16 08:06:19,993 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 475141.01, 'new_value': 507240.66000000003}, {'field': 'dailyBillAmount', 'old_value': 475141.01, 'new_value': 507240.66000000003}, {'field': 'amount', 'old_value': 422377.91000000003, 'new_value': 453829.28}, {'field': 'count', 'old_value': 2044, 'new_value': 2200}, {'field': 'instoreAmount', 'old_value': 389616.36, 'new_value': 419417.61}, {'field': 'instoreCount', 'old_value': 1714, 'new_value': 1850}, {'field': 'onlineAmount', 'old_value': 32989.57, 'new_value': 34666.54}, {'field': 'onlineCount', 'old_value': 330, 'new_value': 350}]
2025-05-16 08:06:20,462 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-16 08:06:20,462 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 91353.44, 'new_value': 95365.44}, {'field': 'dailyBillAmount', 'old_value': 90008.44, 'new_value': 94020.44}, {'field': 'amount', 'old_value': 89341.86, 'new_value': 93205.86}, {'field': 'count', 'old_value': 160, 'new_value': 169}, {'field': 'instoreAmount', 'old_value': 89341.86, 'new_value': 93205.86}, {'field': 'instoreCount', 'old_value': 160, 'new_value': 169}]
2025-05-16 08:06:20,946 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-16 08:06:20,946 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92742.38, 'new_value': 95210.78}, {'field': 'dailyBillAmount', 'old_value': 92742.38, 'new_value': 95210.78}, {'field': 'amount', 'old_value': 77495.52, 'new_value': 79526.52}, {'field': 'count', 'old_value': 136, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 75574.2, 'new_value': 77605.2}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 130}]
2025-05-16 08:06:21,415 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-16 08:06:21,415 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13345.17, 'new_value': 14224.23}, {'field': 'amount', 'old_value': 13345.17, 'new_value': 14224.23}, {'field': 'count', 'old_value': 280, 'new_value': 292}, {'field': 'instoreAmount', 'old_value': 13345.17, 'new_value': 14224.23}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 292}]
2025-05-16 08:06:22,024 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-16 08:06:22,024 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53101.91, 'new_value': 57074.5}, {'field': 'amount', 'old_value': 53101.91, 'new_value': 57074.5}, {'field': 'count', 'old_value': 440, 'new_value': 471}, {'field': 'instoreAmount', 'old_value': 53101.91, 'new_value': 57177.22}, {'field': 'instoreCount', 'old_value': 440, 'new_value': 471}]
2025-05-16 08:06:22,478 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-16 08:06:22,478 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165611.48, 'new_value': 176502.19}, {'field': 'dailyBillAmount', 'old_value': 165611.48, 'new_value': 176502.19}, {'field': 'amount', 'old_value': 184251.68, 'new_value': 195486.03}, {'field': 'count', 'old_value': 4718, 'new_value': 5048}, {'field': 'instoreAmount', 'old_value': 175087.27, 'new_value': 185735.82}, {'field': 'instoreCount', 'old_value': 4257, 'new_value': 4551}, {'field': 'onlineAmount', 'old_value': 12284.7, 'new_value': 13100.5}, {'field': 'onlineCount', 'old_value': 461, 'new_value': 497}]
2025-05-16 08:06:22,931 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-16 08:06:22,931 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46968.58, 'new_value': 52308.82}, {'field': 'dailyBillAmount', 'old_value': 46968.58, 'new_value': 52308.82}, {'field': 'amount', 'old_value': 47325.58, 'new_value': 52889.82}, {'field': 'count', 'old_value': 42, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 47325.58, 'new_value': 52889.82}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 48}]
2025-05-16 08:06:23,462 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-16 08:06:23,462 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 467612.64, 'new_value': 501626.02}, {'field': 'dailyBillAmount', 'old_value': 467612.64, 'new_value': 501626.02}, {'field': 'amount', 'old_value': 420583.91000000003, 'new_value': 454822.15}, {'field': 'count', 'old_value': 1094, 'new_value': 1170}, {'field': 'instoreAmount', 'old_value': 440181.08, 'new_value': 474194.46}, {'field': 'instoreCount', 'old_value': 908, 'new_value': 974}, {'field': 'onlineAmount', 'old_value': 4147.36, 'new_value': 4372.22}, {'field': 'onlineCount', 'old_value': 186, 'new_value': 196}]
2025-05-16 08:06:23,962 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-16 08:06:23,962 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 699243.29, 'new_value': 742834.46}, {'field': 'amount', 'old_value': 699243.29, 'new_value': 742834.46}, {'field': 'count', 'old_value': 2264, 'new_value': 2401}, {'field': 'instoreAmount', 'old_value': 700454.29, 'new_value': 744045.46}, {'field': 'instoreCount', 'old_value': 2264, 'new_value': 2401}]
2025-05-16 08:06:24,321 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-16 08:06:24,321 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 402775.21, 'new_value': 437439.76}, {'field': 'count', 'old_value': 1545, 'new_value': 1643}, {'field': 'instoreAmount', 'old_value': 392012.36, 'new_value': 425915.71}, {'field': 'instoreCount', 'old_value': 892, 'new_value': 961}, {'field': 'onlineAmount', 'old_value': 19673.12, 'new_value': 20464.420000000002}, {'field': 'onlineCount', 'old_value': 653, 'new_value': 682}]
2025-05-16 08:06:24,806 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-16 08:06:24,806 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1064754.29, 'new_value': 1135090.29}, {'field': 'dailyBillAmount', 'old_value': 1064754.29, 'new_value': 1135090.29}, {'field': 'amount', 'old_value': 1104542.0, 'new_value': 1176566.0}, {'field': 'count', 'old_value': 3250, 'new_value': 3403}, {'field': 'instoreAmount', 'old_value': 1104542.0, 'new_value': 1176566.0}, {'field': 'instoreCount', 'old_value': 3250, 'new_value': 3403}]
2025-05-16 08:06:25,243 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-16 08:06:25,259 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 163630.18, 'new_value': 174794.69}, {'field': 'dailyBillAmount', 'old_value': 163630.18, 'new_value': 174794.69}, {'field': 'amount', 'old_value': 161676.81, 'new_value': 172841.32}, {'field': 'count', 'old_value': 846, 'new_value': 911}, {'field': 'instoreAmount', 'old_value': 156664.6, 'new_value': 167149.6}, {'field': 'instoreCount', 'old_value': 703, 'new_value': 758}, {'field': 'onlineAmount', 'old_value': 8520.58, 'new_value': 9200.09}, {'field': 'onlineCount', 'old_value': 143, 'new_value': 153}]
2025-05-16 08:06:25,759 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-16 08:06:25,759 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 542383.16, 'new_value': 579239.97}, {'field': 'dailyBillAmount', 'old_value': 542383.16, 'new_value': 579239.97}, {'field': 'amount', 'old_value': 589304.59, 'new_value': 626161.7000000001}, {'field': 'count', 'old_value': 2364, 'new_value': 2533}, {'field': 'instoreAmount', 'old_value': 589305.04, 'new_value': 626162.15}, {'field': 'instoreCount', 'old_value': 2364, 'new_value': 2533}]
2025-05-16 08:06:26,102 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-16 08:06:26,102 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 230169.8, 'new_value': 247650.34}, {'field': 'dailyBillAmount', 'old_value': 230169.8, 'new_value': 247650.34}, {'field': 'amount', 'old_value': 375242.3, 'new_value': 403265.34}, {'field': 'count', 'old_value': 619, 'new_value': 669}, {'field': 'instoreAmount', 'old_value': 372493.54, 'new_value': 400361.78}, {'field': 'instoreCount', 'old_value': 597, 'new_value': 646}, {'field': 'onlineAmount', 'old_value': 3014.2, 'new_value': 3169.0}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 23}]
2025-05-16 08:06:26,540 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-16 08:06:26,556 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 141015.26, 'new_value': 146888.87}, {'field': 'dailyBillAmount', 'old_value': 141015.26, 'new_value': 146888.87}, {'field': 'amount', 'old_value': 170824.3, 'new_value': 176557.3}, {'field': 'count', 'old_value': 1184, 'new_value': 1233}, {'field': 'instoreAmount', 'old_value': 173410.3, 'new_value': 179143.3}, {'field': 'instoreCount', 'old_value': 1184, 'new_value': 1233}]
2025-05-16 08:06:26,962 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-16 08:06:26,962 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68031.89, 'new_value': 78890.44}, {'field': 'dailyBillAmount', 'old_value': 68031.89, 'new_value': 78890.44}, {'field': 'amount', 'old_value': 46705.56, 'new_value': 57125.96}, {'field': 'count', 'old_value': 315, 'new_value': 374}, {'field': 'instoreAmount', 'old_value': 46129.0, 'new_value': 56376.0}, {'field': 'instoreCount', 'old_value': 282, 'new_value': 334}, {'field': 'onlineAmount', 'old_value': 1717.56, 'new_value': 1890.96}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 40}]
2025-05-16 08:06:27,478 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-16 08:06:27,478 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24102.8, 'new_value': 26982.8}, {'field': 'dailyBillAmount', 'old_value': 24102.8, 'new_value': 26982.8}]
2025-05-16 08:06:27,931 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-16 08:06:27,931 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 86248.87, 'new_value': 91301.39}, {'field': 'count', 'old_value': 4044, 'new_value': 4306}, {'field': 'instoreAmount', 'old_value': 46135.58, 'new_value': 48902.09}, {'field': 'instoreCount', 'old_value': 2304, 'new_value': 2454}, {'field': 'onlineAmount', 'old_value': 42821.18, 'new_value': 45166.89}, {'field': 'onlineCount', 'old_value': 1740, 'new_value': 1852}]
2025-05-16 08:06:28,274 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-16 08:06:28,274 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 120217.36, 'new_value': 127902.36}, {'field': 'amount', 'old_value': 120213.64, 'new_value': 127898.55}, {'field': 'count', 'old_value': 2203, 'new_value': 2326}, {'field': 'instoreAmount', 'old_value': 114297.13, 'new_value': 121811.84}, {'field': 'instoreCount', 'old_value': 2090, 'new_value': 2211}, {'field': 'onlineAmount', 'old_value': 5920.2300000000005, 'new_value': 6090.52}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 115}]
2025-05-16 08:06:28,727 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-16 08:06:28,727 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18818.0, 'new_value': 18949.4}, {'field': 'amount', 'old_value': 18818.0, 'new_value': 18949.4}, {'field': 'count', 'old_value': 125, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 18818.0, 'new_value': 18949.4}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 128}]
2025-05-16 08:06:29,165 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-16 08:06:29,165 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9183.3, 'new_value': 9285.3}, {'field': 'dailyBillAmount', 'old_value': 9183.3, 'new_value': 9285.3}, {'field': 'amount', 'old_value': 30564.2, 'new_value': 31719.2}, {'field': 'count', 'old_value': 270, 'new_value': 281}, {'field': 'instoreAmount', 'old_value': 30783.5, 'new_value': 31938.5}, {'field': 'instoreCount', 'old_value': 270, 'new_value': 281}]
2025-05-16 08:06:29,681 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-16 08:06:29,681 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70651.9, 'new_value': 82618.4}, {'field': 'dailyBillAmount', 'old_value': 70651.9, 'new_value': 82618.4}, {'field': 'amount', 'old_value': 92521.67, 'new_value': 98509.84999999999}, {'field': 'count', 'old_value': 2806, 'new_value': 3030}, {'field': 'instoreAmount', 'old_value': 89579.67, 'new_value': 95429.05}, {'field': 'instoreCount', 'old_value': 2689, 'new_value': 2906}, {'field': 'onlineAmount', 'old_value': 4299.32, 'new_value': 4505.22}, {'field': 'onlineCount', 'old_value': 117, 'new_value': 124}]
2025-05-16 08:06:30,181 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-16 08:06:30,181 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29032.7, 'new_value': 29845.7}, {'field': 'dailyBillAmount', 'old_value': 29032.7, 'new_value': 29845.7}, {'field': 'amount', 'old_value': 28780.3, 'new_value': 29603.3}, {'field': 'count', 'old_value': 163, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 30565.5, 'new_value': 31388.5}, {'field': 'instoreCount', 'old_value': 163, 'new_value': 168}]
2025-05-16 08:06:30,665 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-16 08:06:30,665 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37799.07, 'new_value': 41132.82}, {'field': 'dailyBillAmount', 'old_value': 37799.07, 'new_value': 41132.82}]
2025-05-16 08:06:31,337 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-16 08:06:31,337 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26991.61, 'new_value': 29444.370000000003}, {'field': 'amount', 'old_value': 26991.61, 'new_value': 29444.37}, {'field': 'count', 'old_value': 1537, 'new_value': 1677}, {'field': 'instoreAmount', 'old_value': 27452.5, 'new_value': 29940.14}, {'field': 'instoreCount', 'old_value': 1537, 'new_value': 1677}]
2025-05-16 08:06:31,774 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-16 08:06:31,774 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42896.82, 'new_value': 46931.15}, {'field': 'dailyBillAmount', 'old_value': 42896.82, 'new_value': 46931.15}, {'field': 'amount', 'old_value': 44157.81, 'new_value': 48280.13}, {'field': 'count', 'old_value': 2124, 'new_value': 2331}, {'field': 'instoreAmount', 'old_value': 41082.5, 'new_value': 44958.1}, {'field': 'instoreCount', 'old_value': 1996, 'new_value': 2192}, {'field': 'onlineAmount', 'old_value': 3107.4, 'new_value': 3354.12}, {'field': 'onlineCount', 'old_value': 128, 'new_value': 139}]
2025-05-16 08:06:32,290 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-16 08:06:32,290 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30808.260000000002, 'new_value': 33049.23}, {'field': 'amount', 'old_value': 30808.260000000002, 'new_value': 33049.23}, {'field': 'count', 'old_value': 1478, 'new_value': 1591}, {'field': 'instoreAmount', 'old_value': 19687.89, 'new_value': 21068.78}, {'field': 'instoreCount', 'old_value': 992, 'new_value': 1062}, {'field': 'onlineAmount', 'old_value': 11181.37, 'new_value': 12041.45}, {'field': 'onlineCount', 'old_value': 486, 'new_value': 529}]
2025-05-16 08:06:32,665 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-16 08:06:32,665 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21804.5, 'new_value': 23427.1}, {'field': 'dailyBillAmount', 'old_value': 21804.5, 'new_value': 23427.1}, {'field': 'amount', 'old_value': 15458.53, 'new_value': 16665.03}, {'field': 'count', 'old_value': 621, 'new_value': 674}, {'field': 'instoreAmount', 'old_value': 15647.93, 'new_value': 16854.43}, {'field': 'instoreCount', 'old_value': 621, 'new_value': 674}]
2025-05-16 08:06:33,040 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-16 08:06:33,040 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41531.23, 'new_value': 43565.31}, {'field': 'amount', 'old_value': 41526.26, 'new_value': 43560.34}, {'field': 'count', 'old_value': 2536, 'new_value': 2651}, {'field': 'instoreAmount', 'old_value': 9846.38, 'new_value': 10563.75}, {'field': 'instoreCount', 'old_value': 583, 'new_value': 617}, {'field': 'onlineAmount', 'old_value': 32677.25, 'new_value': 33993.96}, {'field': 'onlineCount', 'old_value': 1953, 'new_value': 2034}]
2025-05-16 08:06:33,493 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-16 08:06:33,493 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72498.2, 'new_value': 78348.71}, {'field': 'dailyBillAmount', 'old_value': 72498.2, 'new_value': 78348.71}, {'field': 'amount', 'old_value': 60569.53, 'new_value': 65301.04}, {'field': 'count', 'old_value': 608, 'new_value': 652}, {'field': 'instoreAmount', 'old_value': 60569.53, 'new_value': 65301.04}, {'field': 'instoreCount', 'old_value': 608, 'new_value': 652}]
2025-05-16 08:06:33,946 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-16 08:06:33,946 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57010.89, 'new_value': 59520.89}, {'field': 'dailyBillAmount', 'old_value': 57010.89, 'new_value': 59520.89}, {'field': 'amount', 'old_value': 61711.8, 'new_value': 64921.8}, {'field': 'count', 'old_value': 262, 'new_value': 275}, {'field': 'instoreAmount', 'old_value': 61711.8, 'new_value': 64921.8}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 275}]
2025-05-16 08:06:34,493 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-16 08:06:34,493 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42547.7, 'new_value': 45198.7}, {'field': 'dailyBillAmount', 'old_value': 42547.7, 'new_value': 45198.7}, {'field': 'amount', 'old_value': 36757.97, 'new_value': 37456.97}, {'field': 'count', 'old_value': 195, 'new_value': 199}, {'field': 'instoreAmount', 'old_value': 37500.97, 'new_value': 38199.97}, {'field': 'instoreCount', 'old_value': 195, 'new_value': 199}]
2025-05-16 08:06:34,993 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-16 08:06:34,993 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78380.0, 'new_value': 82029.0}, {'field': 'amount', 'old_value': 78380.0, 'new_value': 82029.0}, {'field': 'count', 'old_value': 807, 'new_value': 857}, {'field': 'instoreAmount', 'old_value': 78380.0, 'new_value': 82029.0}, {'field': 'instoreCount', 'old_value': 807, 'new_value': 857}]
2025-05-16 08:06:35,446 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-16 08:06:35,446 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15150.32, 'new_value': 17046.86}, {'field': 'dailyBillAmount', 'old_value': 15150.32, 'new_value': 17046.86}, {'field': 'amount', 'old_value': 1899.23, 'new_value': 1922.62}, {'field': 'count', 'old_value': 96, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 2232.86, 'new_value': 2303.51}, {'field': 'instoreCount', 'old_value': 96, 'new_value': 101}]
2025-05-16 08:06:35,899 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-16 08:06:35,899 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 12435.28, 'new_value': 14035.7}, {'field': 'dailyBillAmount', 'old_value': 12435.28, 'new_value': 14035.7}, {'field': 'amount', 'old_value': 13301.5, 'new_value': 14785.630000000001}, {'field': 'count', 'old_value': 355, 'new_value': 395}, {'field': 'instoreAmount', 'old_value': 13343.64, 'new_value': 14827.77}, {'field': 'instoreCount', 'old_value': 354, 'new_value': 394}]
2025-05-16 08:06:36,352 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-16 08:06:36,352 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30442.2, 'new_value': 32302.1}, {'field': 'dailyBillAmount', 'old_value': 30442.2, 'new_value': 32302.1}, {'field': 'amount', 'old_value': 41286.7, 'new_value': 43748.7}, {'field': 'count', 'old_value': 165, 'new_value': 173}, {'field': 'instoreAmount', 'old_value': 41538.7, 'new_value': 44000.7}, {'field': 'instoreCount', 'old_value': 165, 'new_value': 173}]
2025-05-16 08:06:36,806 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-16 08:06:36,806 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23682.0, 'new_value': 26298.0}, {'field': 'dailyBillAmount', 'old_value': 23682.0, 'new_value': 26298.0}, {'field': 'amount', 'old_value': 28984.0, 'new_value': 29821.0}, {'field': 'count', 'old_value': 141, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 28998.0, 'new_value': 29835.0}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 150}]
2025-05-16 08:06:37,227 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-16 08:06:37,227 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42609.83, 'new_value': 46625.48}, {'field': 'dailyBillAmount', 'old_value': 42609.83, 'new_value': 46625.48}, {'field': 'amount', 'old_value': 37265.4, 'new_value': 40931.32}, {'field': 'count', 'old_value': 1257, 'new_value': 1388}, {'field': 'instoreAmount', 'old_value': 34069.84, 'new_value': 37457.36}, {'field': 'instoreCount', 'old_value': 1110, 'new_value': 1227}, {'field': 'onlineAmount', 'old_value': 3232.0, 'new_value': 3510.4}, {'field': 'onlineCount', 'old_value': 147, 'new_value': 161}]
2025-05-16 08:06:37,649 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-16 08:06:37,649 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13450.9, 'new_value': 15258.9}, {'field': 'dailyBillAmount', 'old_value': 13450.9, 'new_value': 15258.9}, {'field': 'amount', 'old_value': 16379.01, 'new_value': 18375.01}, {'field': 'count', 'old_value': 96, 'new_value': 111}, {'field': 'instoreAmount', 'old_value': 16443.61, 'new_value': 18439.61}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 108}]
2025-05-16 08:06:38,118 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-16 08:06:38,118 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 112155.43000000001, 'new_value': 122304.22}, {'field': 'dailyBillAmount', 'old_value': 112155.43000000001, 'new_value': 122304.22}, {'field': 'amount', 'old_value': 118466.6, 'new_value': 128391.3}, {'field': 'count', 'old_value': 828, 'new_value': 918}, {'field': 'instoreAmount', 'old_value': 112760.0, 'new_value': 122912.7}, {'field': 'instoreCount', 'old_value': 734, 'new_value': 818}, {'field': 'onlineAmount', 'old_value': 6065.6, 'new_value': 6350.6}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 100}]
2025-05-16 08:06:38,118 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-16 08:06:38,118 - INFO - 正在批量插入月度数据，批次 1/1，共 1 条记录
2025-05-16 08:06:38,274 - INFO - 批量插入月度数据成功，批次 1，1 条记录
2025-05-16 08:06:41,290 - INFO - 批量插入月度数据完成: 总计 1 条，成功 1 条，失败 0 条
2025-05-16 08:06:41,290 - INFO - 批量插入月销售数据完成，共 1 条记录
2025-05-16 08:06:41,290 - INFO - 月销售数据同步完成！更新: 210 条，插入: 1 条，错误: 0 条，跳过: 978 条
2025-05-16 08:06:41,290 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-16 08:06:41,915 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250516.xlsx
2025-05-16 08:06:41,915 - INFO - 综合数据同步流程完成！
2025-05-16 08:06:41,962 - INFO - 综合数据同步完成
2025-05-16 08:06:41,962 - INFO - ==================================================
2025-05-16 08:06:41,962 - INFO - 程序退出
2025-05-16 08:06:41,962 - INFO - ==================================================
