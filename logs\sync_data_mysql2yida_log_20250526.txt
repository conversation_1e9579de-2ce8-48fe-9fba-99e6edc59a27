2025-05-26 00:30:33,755 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 00:30:33,770 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 00:30:33,770 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 00:30:33,833 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 0 条记录
2025-05-26 00:30:33,833 - ERROR - 未获取到MySQL数据
2025-05-26 00:31:33,844 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 00:31:33,844 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 00:31:33,844 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 00:31:33,906 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 96 条记录
2025-05-26 00:31:33,906 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 00:31:33,906 - INFO - 开始处理日期: 2025-05-25
2025-05-26 00:31:33,906 - INFO - Request Parameters - Page 1:
2025-05-26 00:31:33,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 00:31:33,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 00:31:42,031 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C92DEC7F-6F8C-756E-AC1D-9A2B773647AB Response: {'code': 'ServiceUnavailable', 'requestid': 'C92DEC7F-6F8C-756E-AC1D-9A2B773647AB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C92DEC7F-6F8C-756E-AC1D-9A2B773647AB)
2025-05-26 00:31:42,031 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 00:31:42,031 - INFO - 同步完成
2025-05-26 01:30:33,837 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 01:30:33,837 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 01:30:33,837 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 01:30:33,899 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 0 条记录
2025-05-26 01:30:33,899 - ERROR - 未获取到MySQL数据
2025-05-26 01:31:33,910 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 01:31:33,910 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 01:31:33,910 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 01:31:33,973 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 96 条记录
2025-05-26 01:31:33,973 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 01:31:33,973 - INFO - 开始处理日期: 2025-05-25
2025-05-26 01:31:33,973 - INFO - Request Parameters - Page 1:
2025-05-26 01:31:33,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 01:31:33,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 01:31:42,097 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F9ED67E0-5854-7E0E-B996-C70AD42AF2D4 Response: {'code': 'ServiceUnavailable', 'requestid': 'F9ED67E0-5854-7E0E-B996-C70AD42AF2D4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F9ED67E0-5854-7E0E-B996-C70AD42AF2D4)
2025-05-26 01:31:42,097 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 01:31:42,097 - INFO - 同步完成
2025-05-26 02:30:33,670 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 02:30:33,670 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 02:30:33,670 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 02:30:33,732 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 1 条记录
2025-05-26 02:30:33,732 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 02:30:33,732 - INFO - 开始处理日期: 2025-05-25
2025-05-26 02:30:33,732 - INFO - Request Parameters - Page 1:
2025-05-26 02:30:33,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 02:30:33,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 02:30:41,841 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 77D5DAA6-43D3-7BE1-97EC-DF9640C16129 Response: {'code': 'ServiceUnavailable', 'requestid': '77D5DAA6-43D3-7BE1-97EC-DF9640C16129', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 77D5DAA6-43D3-7BE1-97EC-DF9640C16129)
2025-05-26 02:30:41,841 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 02:31:41,852 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 02:31:41,852 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 02:31:41,852 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 02:31:41,914 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 97 条记录
2025-05-26 02:31:41,914 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 02:31:41,914 - INFO - 开始处理日期: 2025-05-25
2025-05-26 02:31:41,914 - INFO - Request Parameters - Page 1:
2025-05-26 02:31:41,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 02:31:41,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 02:31:42,820 - INFO - Response - Page 1:
2025-05-26 02:31:42,820 - INFO - 第 1 页获取到 96 条记录
2025-05-26 02:31:43,024 - INFO - 查询完成，共获取到 96 条记录
2025-05-26 02:31:43,024 - INFO - 获取到 96 条表单数据
2025-05-26 02:31:43,024 - INFO - 当前日期 2025-05-25 有 97 条MySQL数据需要处理
2025-05-26 02:31:43,024 - INFO - 开始批量插入 1 条新记录
2025-05-26 02:31:43,164 - INFO - 批量插入响应状态码: 200
2025-05-26 02:31:43,164 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 18:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '95C51247-0E31-7CC4-9308-0CB1CFEB6DDE', 'x-acs-trace-id': '9d8da123005a2a9b4d0553e13311dbcb', 'etag': '6TMzkvGAn9IYJmR+5mBEs8A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 02:31:43,164 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81ECQVDJR1EQBX8AQS6F4W2IW7WZ3BMZ2']}
2025-05-26 02:31:43,164 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-26 02:31:43,164 - INFO - 成功插入的数据ID: ['FINST-WBF66B81ECQVDJR1EQBX8AQS6F4W2IW7WZ3BMZ2']
2025-05-26 02:31:48,179 - INFO - 批量插入完成，共 1 条记录
2025-05-26 02:31:48,179 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-26 02:31:48,179 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-26 02:31:48,179 - INFO - 同步完成
2025-05-26 03:30:34,065 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 03:30:34,080 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 03:30:34,080 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 03:30:34,143 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 1 条记录
2025-05-26 03:30:34,143 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 03:30:34,143 - INFO - 开始处理日期: 2025-05-25
2025-05-26 03:30:34,143 - INFO - Request Parameters - Page 1:
2025-05-26 03:30:34,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 03:30:34,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 03:30:42,252 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C1624C91-D180-7819-A723-AC10F600F34E Response: {'code': 'ServiceUnavailable', 'requestid': 'C1624C91-D180-7819-A723-AC10F600F34E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C1624C91-D180-7819-A723-AC10F600F34E)
2025-05-26 03:30:42,252 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 03:31:42,263 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 03:31:42,263 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 03:31:42,263 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 03:31:42,325 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 97 条记录
2025-05-26 03:31:42,325 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 03:31:42,325 - INFO - 开始处理日期: 2025-05-25
2025-05-26 03:31:42,325 - INFO - Request Parameters - Page 1:
2025-05-26 03:31:42,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 03:31:42,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 03:31:50,434 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 92A6CB8B-2B19-76C6-BD6D-8C9A52A7B87B Response: {'code': 'ServiceUnavailable', 'requestid': '92A6CB8B-2B19-76C6-BD6D-8C9A52A7B87B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 92A6CB8B-2B19-76C6-BD6D-8C9A52A7B87B)
2025-05-26 03:31:50,434 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 03:31:50,434 - INFO - 同步完成
2025-05-26 04:30:33,351 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 04:30:33,351 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 04:30:33,351 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 04:30:33,413 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 1 条记录
2025-05-26 04:30:33,413 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 04:30:33,413 - INFO - 开始处理日期: 2025-05-25
2025-05-26 04:30:33,429 - INFO - Request Parameters - Page 1:
2025-05-26 04:30:33,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 04:30:33,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 04:30:41,553 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E5D79DDA-5533-7A47-AD0D-B64B0BDC4055 Response: {'code': 'ServiceUnavailable', 'requestid': 'E5D79DDA-5533-7A47-AD0D-B64B0BDC4055', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E5D79DDA-5533-7A47-AD0D-B64B0BDC4055)
2025-05-26 04:30:41,553 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 04:31:41,564 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 04:31:41,564 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 04:31:41,564 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 04:31:41,627 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 97 条记录
2025-05-26 04:31:41,627 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 04:31:41,627 - INFO - 开始处理日期: 2025-05-25
2025-05-26 04:31:41,627 - INFO - Request Parameters - Page 1:
2025-05-26 04:31:41,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 04:31:41,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 04:31:49,751 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA279D62-2E8C-787E-9590-AFD967057AEA Response: {'code': 'ServiceUnavailable', 'requestid': 'AA279D62-2E8C-787E-9590-AFD967057AEA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA279D62-2E8C-787E-9590-AFD967057AEA)
2025-05-26 04:31:49,751 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 04:31:49,751 - INFO - 同步完成
2025-05-26 05:30:33,650 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 05:30:33,650 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 05:30:33,650 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 05:30:33,729 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 1 条记录
2025-05-26 05:30:33,729 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 05:30:33,729 - INFO - 开始处理日期: 2025-05-25
2025-05-26 05:30:33,729 - INFO - Request Parameters - Page 1:
2025-05-26 05:30:33,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 05:30:33,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 05:30:41,837 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C124676B-2882-724B-AECB-0F6397F86EE8 Response: {'code': 'ServiceUnavailable', 'requestid': 'C124676B-2882-724B-AECB-0F6397F86EE8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C124676B-2882-724B-AECB-0F6397F86EE8)
2025-05-26 05:30:41,837 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 05:31:41,849 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 05:31:41,849 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 05:31:41,849 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 05:31:41,912 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 97 条记录
2025-05-26 05:31:41,912 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 05:31:41,912 - INFO - 开始处理日期: 2025-05-25
2025-05-26 05:31:41,912 - INFO - Request Parameters - Page 1:
2025-05-26 05:31:41,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 05:31:41,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 05:31:50,005 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 25C33BD2-23E6-7286-9EC7-465EA94964D7 Response: {'code': 'ServiceUnavailable', 'requestid': '25C33BD2-23E6-7286-9EC7-465EA94964D7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 25C33BD2-23E6-7286-9EC7-465EA94964D7)
2025-05-26 05:31:50,005 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 05:31:50,005 - INFO - 同步完成
2025-05-26 06:31:33,169 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 06:31:33,169 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 06:31:33,169 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 06:31:33,232 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 1 条记录
2025-05-26 06:31:33,232 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 06:31:33,232 - INFO - 开始处理日期: 2025-05-25
2025-05-26 06:31:33,232 - INFO - Request Parameters - Page 1:
2025-05-26 06:31:33,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 06:31:33,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 06:31:41,351 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 998BD243-72B5-7A99-953D-93627D4FF3B8 Response: {'code': 'ServiceUnavailable', 'requestid': '998BD243-72B5-7A99-953D-93627D4FF3B8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 998BD243-72B5-7A99-953D-93627D4FF3B8)
2025-05-26 06:31:41,351 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 06:32:41,330 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 06:32:41,330 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 06:32:41,330 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 06:32:41,393 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 97 条记录
2025-05-26 06:32:41,393 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 06:32:41,393 - INFO - 开始处理日期: 2025-05-25
2025-05-26 06:32:41,393 - INFO - Request Parameters - Page 1:
2025-05-26 06:32:41,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 06:32:41,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 06:32:42,267 - INFO - Response - Page 1:
2025-05-26 06:32:42,267 - INFO - 第 1 页获取到 97 条记录
2025-05-26 06:32:42,470 - INFO - 查询完成，共获取到 97 条记录
2025-05-26 06:32:42,470 - INFO - 获取到 97 条表单数据
2025-05-26 06:32:42,470 - INFO - 当前日期 2025-05-25 有 97 条MySQL数据需要处理
2025-05-26 06:32:42,470 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 06:32:42,470 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 06:32:42,470 - INFO - 同步完成
2025-05-26 07:30:33,880 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 07:30:33,880 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 07:30:33,880 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 07:30:33,942 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 6 条记录
2025-05-26 07:30:33,942 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 07:30:33,942 - INFO - 开始处理日期: 2025-05-25
2025-05-26 07:30:33,942 - INFO - Request Parameters - Page 1:
2025-05-26 07:30:33,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 07:30:33,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 07:30:42,067 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E339445B-4B14-782F-B561-7197B47FF7D0 Response: {'code': 'ServiceUnavailable', 'requestid': 'E339445B-4B14-782F-B561-7197B47FF7D0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E339445B-4B14-782F-B561-7197B47FF7D0)
2025-05-26 07:30:42,067 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 07:31:42,082 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 07:31:42,082 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 07:31:42,082 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 07:31:42,145 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 103 条记录
2025-05-26 07:31:42,145 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 07:31:42,145 - INFO - 开始处理日期: 2025-05-25
2025-05-26 07:31:42,145 - INFO - Request Parameters - Page 1:
2025-05-26 07:31:42,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 07:31:42,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 07:31:43,114 - INFO - Response - Page 1:
2025-05-26 07:31:43,114 - INFO - 第 1 页获取到 97 条记录
2025-05-26 07:31:43,317 - INFO - 查询完成，共获取到 97 条记录
2025-05-26 07:31:43,317 - INFO - 获取到 97 条表单数据
2025-05-26 07:31:43,317 - INFO - 当前日期 2025-05-25 有 103 条MySQL数据需要处理
2025-05-26 07:31:43,317 - INFO - 开始批量插入 6 条新记录
2025-05-26 07:31:43,457 - INFO - 批量插入响应状态码: 200
2025-05-26 07:31:43,457 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 23:31:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '306', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-81E9-7A69-82B1-8B8EB8B6B107', 'x-acs-trace-id': 'b6e76dcfd5b579fa1378219278f80795', 'etag': '3H6VJvAhpuox5wUKAyJBIFg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 07:31:43,457 - INFO - 批量插入响应体: {'result': ['FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMBH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMCH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMDH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMEH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMFH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMGH1']}
2025-05-26 07:31:43,457 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-05-26 07:31:43,457 - INFO - 成功插入的数据ID: ['FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMBH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMCH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMDH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMEH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMFH1', 'FINST-5A966081X1KVB0JYES0264K9K7KG3F9WLA4BMGH1']
2025-05-26 07:31:48,473 - INFO - 批量插入完成，共 6 条记录
2025-05-26 07:31:48,473 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-05-26 07:31:48,473 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 0 条
2025-05-26 07:31:48,473 - INFO - 同步完成
2025-05-26 08:30:33,887 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 08:30:33,887 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 08:30:33,887 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 08:30:33,950 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 14 条记录
2025-05-26 08:30:33,950 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 08:30:33,950 - INFO - 开始处理日期: 2025-05-25
2025-05-26 08:30:33,950 - INFO - Request Parameters - Page 1:
2025-05-26 08:30:33,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:30:33,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:30:42,075 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 55914968-262F-751E-B014-6EF7B958221B Response: {'code': 'ServiceUnavailable', 'requestid': '55914968-262F-751E-B014-6EF7B958221B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 55914968-262F-751E-B014-6EF7B958221B)
2025-05-26 08:30:42,075 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 08:31:42,090 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 08:31:42,090 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 08:31:42,090 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 08:31:42,153 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 111 条记录
2025-05-26 08:31:42,153 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 08:31:42,153 - INFO - 开始处理日期: 2025-05-25
2025-05-26 08:31:42,153 - INFO - Request Parameters - Page 1:
2025-05-26 08:31:42,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:31:42,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:31:43,043 - INFO - Response - Page 1:
2025-05-26 08:31:43,043 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:31:43,246 - INFO - Request Parameters - Page 2:
2025-05-26 08:31:43,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:31:43,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:31:51,356 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: *************-7BFD-BD2F-262E1AEB747F Response: {'code': 'ServiceUnavailable', 'requestid': '*************-7BFD-BD2F-262E1AEB747F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: *************-7BFD-BD2F-262E1AEB747F)
2025-05-26 08:31:51,356 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 08:31:51,356 - INFO - 同步完成
2025-05-26 09:30:33,873 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 09:30:33,873 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 09:30:33,873 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 09:30:33,951 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 77 条记录
2025-05-26 09:30:33,951 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 09:30:33,951 - INFO - 开始处理日期: 2025-05-25
2025-05-26 09:30:33,951 - INFO - Request Parameters - Page 1:
2025-05-26 09:30:33,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 09:30:33,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 09:30:42,076 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6D37984F-8B26-72F5-BA27-0B1D4FA52B93 Response: {'code': 'ServiceUnavailable', 'requestid': '6D37984F-8B26-72F5-BA27-0B1D4FA52B93', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6D37984F-8B26-72F5-BA27-0B1D4FA52B93)
2025-05-26 09:30:42,076 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 09:31:42,091 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 09:31:42,091 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 09:31:42,091 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 09:31:42,153 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 410 条记录
2025-05-26 09:31:42,153 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 09:31:42,169 - INFO - 开始处理日期: 2025-05-25
2025-05-26 09:31:42,169 - INFO - Request Parameters - Page 1:
2025-05-26 09:31:42,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 09:31:42,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 09:31:50,294 - ERROR - 处理日期 2025-05-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 05DAACA0-BA4B-7D4A-A8BC-************ Response: {'code': 'ServiceUnavailable', 'requestid': '05DAACA0-BA4B-7D4A-A8BC-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 05DAACA0-BA4B-7D4A-A8BC-************)
2025-05-26 09:31:50,294 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-26 09:31:50,294 - INFO - 同步完成
2025-05-26 10:30:33,941 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 10:30:33,941 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 10:30:33,941 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 10:30:34,003 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 170 条记录
2025-05-26 10:30:34,003 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 10:30:34,019 - INFO - 开始处理日期: 2025-05-21
2025-05-26 10:30:34,019 - INFO - Request Parameters - Page 1:
2025-05-26 10:30:34,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:30:34,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:30:42,144 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F68DCDA7-26C0-7EBB-B0CD-76619AA88E0F Response: {'code': 'ServiceUnavailable', 'requestid': 'F68DCDA7-26C0-7EBB-B0CD-76619AA88E0F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F68DCDA7-26C0-7EBB-B0CD-76619AA88E0F)
2025-05-26 10:30:42,144 - INFO - 开始处理日期: 2025-05-23
2025-05-26 10:30:42,144 - INFO - Request Parameters - Page 1:
2025-05-26 10:30:42,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:30:42,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:30:50,253 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 870E0E69-A24D-75DC-A6F6-62B78B839BB6 Response: {'code': 'ServiceUnavailable', 'requestid': '870E0E69-A24D-75DC-A6F6-62B78B839BB6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 870E0E69-A24D-75DC-A6F6-62B78B839BB6)
2025-05-26 10:30:50,253 - INFO - 开始处理日期: 2025-05-24
2025-05-26 10:30:50,253 - INFO - Request Parameters - Page 1:
2025-05-26 10:30:50,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:30:50,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:30:51,144 - INFO - Response - Page 1:
2025-05-26 10:30:51,144 - INFO - 第 1 页获取到 100 条记录
2025-05-26 10:30:51,347 - INFO - Request Parameters - Page 2:
2025-05-26 10:30:51,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:30:51,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:30:59,472 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 49FA9AFB-A64C-7FD7-836F-DDAF0519BCFC Response: {'code': 'ServiceUnavailable', 'requestid': '49FA9AFB-A64C-7FD7-836F-DDAF0519BCFC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 49FA9AFB-A64C-7FD7-836F-DDAF0519BCFC)
2025-05-26 10:30:59,472 - INFO - 开始处理日期: 2025-05-25
2025-05-26 10:30:59,472 - INFO - Request Parameters - Page 1:
2025-05-26 10:30:59,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:30:59,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:31:00,316 - INFO - Response - Page 1:
2025-05-26 10:31:00,316 - INFO - 第 1 页获取到 100 条记录
2025-05-26 10:31:00,519 - INFO - Request Parameters - Page 2:
2025-05-26 10:31:00,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:31:00,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:31:01,034 - INFO - Response - Page 2:
2025-05-26 10:31:01,034 - INFO - 第 2 页获取到 3 条记录
2025-05-26 10:31:01,238 - INFO - 查询完成，共获取到 103 条记录
2025-05-26 10:31:01,238 - INFO - 获取到 103 条表单数据
2025-05-26 10:31:01,238 - INFO - 当前日期 2025-05-25 有 164 条MySQL数据需要处理
2025-05-26 10:31:01,238 - INFO - 开始批量插入 158 条新记录
2025-05-26 10:31:01,534 - INFO - 批量插入响应状态码: 200
2025-05-26 10:31:01,534 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 02:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4779', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '134B30F3-12A8-71AC-BB55-0A076A4E2284', 'x-acs-trace-id': '62348362e50774c67a5fa603f68b7e8f', 'etag': '4lib2/V4IF5bn0+TuY4kl2w9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 10:31:01,534 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM3', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM4', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM5', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM6', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM7', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM8', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM9', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMA', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMB', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMC', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMD', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BME', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMF', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMG', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMI', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMJ', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMK', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BML', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMM', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMN', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMO', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMP', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMQ', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMR', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMS', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMT', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMU', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMV', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMW', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMX', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMY', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMZ', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM01', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM11', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM21', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM31', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM41', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM51', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM61', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM71', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM81', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM91', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMA1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMB1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMC1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMD1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BME1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMF1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMG1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMI1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMJ1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMK1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BML1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMM1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMN1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMO1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMP1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMQ1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMR1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMS1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMT1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMU1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMV1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMW1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMX1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMY1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMZ1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM02', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM12', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM22', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM32', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM42', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM52', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM62', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM72', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM82', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM92', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMA2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMB2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMC2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMD2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BME2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMF2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMG2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMI2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMJ2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMK2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BML2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMM2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMN2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMO2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMP2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMQ2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMR2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMS2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMT2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMU2']}
2025-05-26 10:31:01,534 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-26 10:31:01,534 - INFO - 成功插入的数据ID: ['FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM3', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM4', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM5', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM6', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM7', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM8', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM9', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMA', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMB', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMC', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMD', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BME', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMF', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMG', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMI', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMJ', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMK', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BML', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMM', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMN', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMO', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMP', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMQ', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMR', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMS', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMT', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMU', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMV', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMW', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMX', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMY', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMZ', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM01', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM11', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM21', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM31', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM41', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM51', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM61', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM71', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM81', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM91', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMA1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMB1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMC1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMD1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BME1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMF1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMG1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMI1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMJ1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMK1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BML1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMM1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMN1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMO1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMP1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMQ1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMR1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMS1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMT1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMU1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMV1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMW1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMX1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMY1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMZ1', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM02', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM12', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM22', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM32', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM42', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM52', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM62', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM72', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM82', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BM92', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMA2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMB2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMC2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMD2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BME2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMF2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMG2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMI2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMJ2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMK2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BML2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMM2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMN2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMO2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMP2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMQ2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMR2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMS2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMT2', 'FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMU2']
2025-05-26 10:31:06,800 - INFO - 批量插入响应状态码: 200
2025-05-26 10:31:06,800 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 02:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2796', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A2EDC8BE-492A-73E7-829F-A4E913C2C639', 'x-acs-trace-id': 'ebb813cdec04a95b3945297fd1d0c720', 'etag': '2LbL3bd1Q+LEjzmwEYlbkBg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 10:31:06,800 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMZP', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM0Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM1Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM2Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM3Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM4Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM5Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM6Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM7Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM8Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM9Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMAQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMBQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMCQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMDQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMEQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMFQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMGQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMHQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMIQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMJQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMKQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMLQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMMQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMNQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMOQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMPQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMQQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMRQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMSQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMTQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMUQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMVQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMWQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMXQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMYQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMZQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM0R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM1R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM2R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM3R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM4R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM5R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM6R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM7R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM8R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM9R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMAR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMBR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMCR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMDR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMER', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMFR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMGR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMHR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMIR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMJR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMKR']}
2025-05-26 10:31:06,800 - INFO - 批量插入表单数据成功，批次 2，共 58 条记录
2025-05-26 10:31:06,800 - INFO - 成功插入的数据ID: ['FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMZP', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM0Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM1Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM2Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM3Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM4Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM5Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM6Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM7Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM8Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BM9Q', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMAQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMBQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMCQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMDQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMEQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMFQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMGQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMHQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMIQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMJQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMKQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMLQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMMQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMNQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMOQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMPQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMQQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMRQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMSQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMTQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMUQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMVQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMWQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMXQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMYQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMZQ', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM0R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM1R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM2R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM3R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM4R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM5R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM6R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM7R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM8R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM9R', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMAR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMBR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMCR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMDR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMER', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMFR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMGR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMHR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMIR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMJR', 'FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMKR']
2025-05-26 10:31:11,816 - INFO - 批量插入完成，共 158 条记录
2025-05-26 10:31:11,816 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 158 条，错误: 0 条
2025-05-26 10:31:11,816 - INFO - 数据同步完成！更新: 0 条，插入: 158 条，错误: 3 条
2025-05-26 10:32:11,831 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 10:32:11,831 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 10:32:11,831 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 10:32:11,909 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 497 条记录
2025-05-26 10:32:11,909 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 10:32:11,909 - INFO - 开始处理日期: 2025-05-25
2025-05-26 10:32:11,909 - INFO - Request Parameters - Page 1:
2025-05-26 10:32:11,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:32:11,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:32:12,815 - INFO - Response - Page 1:
2025-05-26 10:32:12,815 - INFO - 第 1 页获取到 100 条记录
2025-05-26 10:32:13,018 - INFO - Request Parameters - Page 2:
2025-05-26 10:32:13,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:32:13,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:32:13,940 - INFO - Response - Page 2:
2025-05-26 10:32:13,940 - INFO - 第 2 页获取到 100 条记录
2025-05-26 10:32:14,143 - INFO - Request Parameters - Page 3:
2025-05-26 10:32:14,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 10:32:14,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 10:32:14,925 - INFO - Response - Page 3:
2025-05-26 10:32:14,925 - INFO - 第 3 页获取到 61 条记录
2025-05-26 10:32:15,128 - INFO - 查询完成，共获取到 261 条记录
2025-05-26 10:32:15,128 - INFO - 获取到 261 条表单数据
2025-05-26 10:32:15,128 - INFO - 当前日期 2025-05-25 有 497 条MySQL数据需要处理
2025-05-26 10:32:15,128 - INFO - 开始批量插入 236 条新记录
2025-05-26 10:32:15,409 - INFO - 批量插入响应状态码: 200
2025-05-26 10:32:15,409 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 02:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '13D2C3C9-2CA0-726C-BE5A-CF14A1E5F215', 'x-acs-trace-id': 'ce47be2e91732bbc57a8e79c80d60893', 'etag': '4UASu9AWnrKj82bKB5xjm7A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 10:32:15,409 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMEM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMFM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMGM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMHM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMIM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMJM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMKM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMLM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMMM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMOM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMPM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMQM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMRM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMSM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMTM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMUM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMVM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMWM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMXM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMYM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMZM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM0N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM1N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM3N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM4N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM5N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM6N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM7N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM8N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM9N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMAN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMBN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMCN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMEN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMFN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMGN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMHN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMIN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMJN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMKN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMLN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMMN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMON', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMPN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMQN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMRN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMSN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMTN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMUN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMVN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMWN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMXN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMYN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMZN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM0O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM1O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM3O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM4O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM5O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM6O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM7O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM8O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM9O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMAO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMBO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMCO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMEO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMFO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMGO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMHO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMIO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMJO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMKO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMLO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMMO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMOO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMPO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMQO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMRO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMSO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMTO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMUO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMVO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMWO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMXO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMYO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMZO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM0P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM1P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM3P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM4P']}
2025-05-26 10:32:15,409 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-26 10:32:15,409 - INFO - 成功插入的数据ID: ['FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMEM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMFM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMGM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMHM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMIM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMJM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMKM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMLM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMMM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMOM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMPM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMQM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMRM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMSM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMTM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMUM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMVM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMWM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMXM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMYM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMZM', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM0N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM1N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM3N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM4N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM5N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM6N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM7N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM8N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM9N', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMAN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMBN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMCN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMEN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMFN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMGN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMHN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMIN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMJN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMKN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMLN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMMN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMON', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMPN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMQN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMRN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMSN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMTN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMUN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMVN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMWN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMXN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMYN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMZN', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM0O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM1O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM3O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM4O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM5O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM6O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM7O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM8O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM9O', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMAO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMBO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMCO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMEO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMFO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMGO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMHO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMIO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMJO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMKO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMLO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMMO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMOO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMPO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMQO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMRO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMSO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMTO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMUO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMVO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMWO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMXO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMYO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMZO', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM0P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM1P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM3P', 'FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM4P']
2025-05-26 10:32:20,690 - INFO - 批量插入响应状态码: 200
2025-05-26 10:32:20,690 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 02:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4781', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B74F197D-A45E-790C-B9A4-032230D61BB1', 'x-acs-trace-id': '6edac029cf52141027f4f0a4ade70df1', 'etag': '4y/54Uaj027vF//swoW0emg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 10:32:20,690 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM5', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM6', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM7', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM8', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM9', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMA', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMB', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMC', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMD', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BME', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMG', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMH', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMI', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMJ', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMK', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BML', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMN', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMO', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMP', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMQ', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMR', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMS', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMT', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMU', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMV', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMW', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMX', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMY', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMZ', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM01', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM11', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM21', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM31', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM41', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM51', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM61', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM71', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM81', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM91', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMA1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMB1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMC1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMD1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BME1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMG1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMH1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMI1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMJ1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMK1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BML1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMN1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMO1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMP1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMQ1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMR1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMS1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMT1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMU1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMV1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMW1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMX1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMY1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMZ1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM02', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM12', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM22', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM32', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM42', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM52', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM62', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM72', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM82', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM92', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMA2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMB2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMC2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMD2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BME2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMG2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMH2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMI2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMJ2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMK2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BML2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMN2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMO2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMP2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMQ2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMR2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMS2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMT2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMU2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMV2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMW2']}
2025-05-26 10:32:20,690 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-26 10:32:20,690 - INFO - 成功插入的数据ID: ['FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM5', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM6', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM7', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM8', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM9', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMA', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMB', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMC', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMD', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BME', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMG', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMH', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMI', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMJ', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMK', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BML', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMN', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMO', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMP', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMQ', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMR', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMS', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMT', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMU', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMV', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMW', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMX', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMY', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMZ', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM01', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM11', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM21', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM31', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM41', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM51', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM61', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM71', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM81', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM91', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMA1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMB1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMC1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMD1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BME1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMG1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMH1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMI1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMJ1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMK1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BML1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMN1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMO1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMP1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMQ1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMR1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMS1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMT1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMU1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMV1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMW1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMX1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMY1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMZ1', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM02', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM12', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM22', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM32', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM42', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM52', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM62', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM72', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM82', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BM92', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMA2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMB2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMC2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMD2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BME2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMG2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMH2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMI2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMJ2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMK2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BML2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMN2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMO2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMP2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMQ2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMR2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMS2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMT2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMU2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMV2', 'FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMW2']
2025-05-26 10:32:25,893 - INFO - 批量插入响应状态码: 200
2025-05-26 10:32:25,893 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 02:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1710', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EEB8E5FC-5CB1-7B3A-98F4-E57041654A79', 'x-acs-trace-id': '5ae5fa20070591608b2d2af6af6a2967', 'etag': '1Y2r7iNZaKmKlyNR7Qqq7Ug0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 10:32:25,893 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM6', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM7', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM8', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM9', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMA', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMB', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMC', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMD', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BME', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMF', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMG', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMH', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMI', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMJ', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMK', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BML', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMM', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMN', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMO', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMP', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMQ', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMR', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMS', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMT', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMU', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMV', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMW', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMX', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMY', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMZ', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM01', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM11', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM21', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM31', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM41', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM51']}
2025-05-26 10:32:25,893 - INFO - 批量插入表单数据成功，批次 3，共 36 条记录
2025-05-26 10:32:25,893 - INFO - 成功插入的数据ID: ['FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM6', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM7', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM8', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM9', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMA', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMB', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMC', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMD', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BME', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMF', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMG', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMH', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMI', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMJ', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMK', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BML', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMM', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMN', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMO', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMP', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMQ', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMR', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMS', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMT', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMU', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMV', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMW', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMX', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMY', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BMZ', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM01', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM11', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM21', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM31', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM41', 'FINST-3PF66X61Q5RVVAVGFMBVI544VZ5P30EA2H4BM51']
2025-05-26 10:32:30,909 - INFO - 批量插入完成，共 236 条记录
2025-05-26 10:32:30,909 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 236 条，错误: 0 条
2025-05-26 10:32:30,909 - INFO - 数据同步完成！更新: 0 条，插入: 236 条，错误: 0 条
2025-05-26 10:32:30,909 - INFO - 同步完成
2025-05-26 11:30:33,730 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 11:30:33,730 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 11:30:33,730 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 11:30:33,793 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 192 条记录
2025-05-26 11:30:33,793 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 11:30:33,808 - INFO - 开始处理日期: 2025-05-21
2025-05-26 11:30:33,808 - INFO - Request Parameters - Page 1:
2025-05-26 11:30:33,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:30:33,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:30:41,933 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C6C0BC1C-8628-79C9-B1A2-3CA81411D1D0 Response: {'code': 'ServiceUnavailable', 'requestid': 'C6C0BC1C-8628-79C9-B1A2-3CA81411D1D0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C6C0BC1C-8628-79C9-B1A2-3CA81411D1D0)
2025-05-26 11:30:41,933 - INFO - 开始处理日期: 2025-05-23
2025-05-26 11:30:41,933 - INFO - Request Parameters - Page 1:
2025-05-26 11:30:41,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:30:41,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:30:50,042 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8B2BFD3-7322-766B-8249-6DCB15997090 Response: {'code': 'ServiceUnavailable', 'requestid': 'B8B2BFD3-7322-766B-8249-6DCB15997090', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8B2BFD3-7322-766B-8249-6DCB15997090)
2025-05-26 11:30:50,042 - INFO - 开始处理日期: 2025-05-24
2025-05-26 11:30:50,042 - INFO - Request Parameters - Page 1:
2025-05-26 11:30:50,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:30:50,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:30:58,167 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 250241D6-F499-7A3E-AFF7-834C49926FC1 Response: {'code': 'ServiceUnavailable', 'requestid': '250241D6-F499-7A3E-AFF7-834C49926FC1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 250241D6-F499-7A3E-AFF7-834C49926FC1)
2025-05-26 11:30:58,167 - INFO - 开始处理日期: 2025-05-25
2025-05-26 11:30:58,167 - INFO - Request Parameters - Page 1:
2025-05-26 11:30:58,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:30:58,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:31:00,277 - INFO - Response - Page 1:
2025-05-26 11:31:00,277 - INFO - 第 1 页获取到 100 条记录
2025-05-26 11:31:00,480 - INFO - Request Parameters - Page 2:
2025-05-26 11:31:00,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:31:00,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:31:01,261 - INFO - Response - Page 2:
2025-05-26 11:31:01,261 - INFO - 第 2 页获取到 100 条记录
2025-05-26 11:31:01,464 - INFO - Request Parameters - Page 3:
2025-05-26 11:31:01,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:31:01,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:31:02,433 - INFO - Response - Page 3:
2025-05-26 11:31:02,433 - INFO - 第 3 页获取到 100 条记录
2025-05-26 11:31:02,636 - INFO - Request Parameters - Page 4:
2025-05-26 11:31:02,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:31:02,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:31:03,495 - INFO - Response - Page 4:
2025-05-26 11:31:03,511 - INFO - 第 4 页获取到 100 条记录
2025-05-26 11:31:03,714 - INFO - Request Parameters - Page 5:
2025-05-26 11:31:03,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:31:03,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:31:04,527 - INFO - Response - Page 5:
2025-05-26 11:31:04,527 - INFO - 第 5 页获取到 97 条记录
2025-05-26 11:31:04,730 - INFO - 查询完成，共获取到 497 条记录
2025-05-26 11:31:04,730 - INFO - 获取到 497 条表单数据
2025-05-26 11:31:04,730 - INFO - 当前日期 2025-05-25 有 186 条MySQL数据需要处理
2025-05-26 11:31:04,730 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDM
2025-05-26 11:31:05,339 - INFO - 更新表单数据成功: FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMDM
2025-05-26 11:31:05,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7057.0, 'new_value': 7957.1}, {'field': 'total_amount', 'old_value': 7057.0, 'new_value': 7957.1}, {'field': 'order_count', 'old_value': 203, 'new_value': 216}]
2025-05-26 11:31:05,339 - INFO - 开始批量插入 21 条新记录
2025-05-26 11:31:05,527 - INFO - 批量插入响应状态码: 200
2025-05-26 11:31:05,527 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 03:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '999', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1ECC190A-0B16-752E-8B96-055B969465B7', 'x-acs-trace-id': '5065e4bc3ea011a0c356a0ae2f2197d1', 'etag': '9kS8mr8xVhoPke7ik9aocmQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 11:31:05,527 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMD', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BME', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMF', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMG', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMH', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMI', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMJ', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMK', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BML', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMM', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMN', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMO', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMP', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMQ', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMR', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMS', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMT', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMU', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMV', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMW', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMX']}
2025-05-26 11:31:05,527 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-05-26 11:31:05,527 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMD', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BME', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMF', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMG', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMH', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMI', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMJ', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMK', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BML', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMM', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMN', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMO', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMP', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMQ', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMR', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMS', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMT', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMU', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMV', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMW', 'FINST-E3G66QA1X4RV8Y5DAMB4EDIM83T4366Q5J4BMX']
2025-05-26 11:31:10,542 - INFO - 批量插入完成，共 21 条记录
2025-05-26 11:31:10,542 - INFO - 日期 2025-05-25 处理完成 - 更新: 1 条，插入: 21 条，错误: 0 条
2025-05-26 11:31:10,542 - INFO - 数据同步完成！更新: 1 条，插入: 21 条，错误: 3 条
2025-05-26 11:32:10,558 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 11:32:10,558 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 11:32:10,558 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 11:32:10,636 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 530 条记录
2025-05-26 11:32:10,636 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 11:32:10,636 - INFO - 开始处理日期: 2025-05-25
2025-05-26 11:32:10,636 - INFO - Request Parameters - Page 1:
2025-05-26 11:32:10,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:32:10,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:32:11,542 - INFO - Response - Page 1:
2025-05-26 11:32:11,542 - INFO - 第 1 页获取到 100 条记录
2025-05-26 11:32:11,745 - INFO - Request Parameters - Page 2:
2025-05-26 11:32:11,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:32:11,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:32:12,511 - INFO - Response - Page 2:
2025-05-26 11:32:12,511 - INFO - 第 2 页获取到 100 条记录
2025-05-26 11:32:12,714 - INFO - Request Parameters - Page 3:
2025-05-26 11:32:12,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:32:12,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:32:13,557 - INFO - Response - Page 3:
2025-05-26 11:32:13,557 - INFO - 第 3 页获取到 100 条记录
2025-05-26 11:32:13,761 - INFO - Request Parameters - Page 4:
2025-05-26 11:32:13,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:32:13,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:32:14,573 - INFO - Response - Page 4:
2025-05-26 11:32:14,573 - INFO - 第 4 页获取到 100 条记录
2025-05-26 11:32:14,776 - INFO - Request Parameters - Page 5:
2025-05-26 11:32:14,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:32:14,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:32:15,573 - INFO - Response - Page 5:
2025-05-26 11:32:15,573 - INFO - 第 5 页获取到 100 条记录
2025-05-26 11:32:15,776 - INFO - Request Parameters - Page 6:
2025-05-26 11:32:15,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 11:32:15,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 11:32:16,354 - INFO - Response - Page 6:
2025-05-26 11:32:16,354 - INFO - 第 6 页获取到 18 条记录
2025-05-26 11:32:16,557 - INFO - 查询完成，共获取到 518 条记录
2025-05-26 11:32:16,557 - INFO - 获取到 518 条表单数据
2025-05-26 11:32:16,557 - INFO - 当前日期 2025-05-25 有 530 条MySQL数据需要处理
2025-05-26 11:32:16,573 - INFO - 开始批量插入 12 条新记录
2025-05-26 11:32:16,729 - INFO - 批量插入响应状态码: 200
2025-05-26 11:32:16,729 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 03:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '588', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '217686CA-BF8C-7AA9-9D2A-C9DDB38BAA02', 'x-acs-trace-id': '2f30b8addeda53d47a4b0e95b793dece', 'etag': '5eFauciOhwr9B3KUbqE/eUQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 11:32:16,729 - INFO - 批量插入响应体: {'result': ['FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMLA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMMA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMNA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMOA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMPA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMQA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMRA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMSA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMTA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMUA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMVA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMWA']}
2025-05-26 11:32:16,729 - INFO - 批量插入表单数据成功，批次 1，共 12 条记录
2025-05-26 11:32:16,729 - INFO - 成功插入的数据ID: ['FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMLA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMMA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMNA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMOA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMPA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMQA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMRA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMSA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMTA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMUA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMVA', 'FINST-LLF668810GPV5LHNDPFJIDS4CGL928497J4BMWA']
2025-05-26 11:32:21,745 - INFO - 批量插入完成，共 12 条记录
2025-05-26 11:32:21,745 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 12 条，错误: 0 条
2025-05-26 11:32:21,745 - INFO - 数据同步完成！更新: 0 条，插入: 12 条，错误: 0 条
2025-05-26 11:32:21,745 - INFO - 同步完成
2025-05-26 12:30:33,754 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 12:30:33,754 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 12:30:33,754 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 12:30:33,832 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 195 条记录
2025-05-26 12:30:33,832 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 12:30:33,832 - INFO - 开始处理日期: 2025-05-21
2025-05-26 12:30:33,832 - INFO - Request Parameters - Page 1:
2025-05-26 12:30:33,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:33,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:41,972 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 32A0B280-3B21-71B6-98CA-D50BC7E8BC97 Response: {'code': 'ServiceUnavailable', 'requestid': '32A0B280-3B21-71B6-98CA-D50BC7E8BC97', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 32A0B280-3B21-71B6-98CA-D50BC7E8BC97)
2025-05-26 12:30:41,972 - INFO - 开始处理日期: 2025-05-23
2025-05-26 12:30:41,972 - INFO - Request Parameters - Page 1:
2025-05-26 12:30:41,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:41,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:50,097 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F4CAA7A4-D920-7480-963C-3B8EB09F6534 Response: {'code': 'ServiceUnavailable', 'requestid': 'F4CAA7A4-D920-7480-963C-3B8EB09F6534', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F4CAA7A4-D920-7480-963C-3B8EB09F6534)
2025-05-26 12:30:50,097 - INFO - 开始处理日期: 2025-05-24
2025-05-26 12:30:50,097 - INFO - Request Parameters - Page 1:
2025-05-26 12:30:50,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:50,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:51,003 - INFO - Response - Page 1:
2025-05-26 12:30:51,003 - INFO - 第 1 页获取到 100 条记录
2025-05-26 12:30:51,207 - INFO - Request Parameters - Page 2:
2025-05-26 12:30:51,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:51,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:52,050 - INFO - Response - Page 2:
2025-05-26 12:30:52,050 - INFO - 第 2 页获取到 100 条记录
2025-05-26 12:30:52,253 - INFO - Request Parameters - Page 3:
2025-05-26 12:30:52,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:52,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:53,082 - INFO - Response - Page 3:
2025-05-26 12:30:53,082 - INFO - 第 3 页获取到 100 条记录
2025-05-26 12:30:53,285 - INFO - Request Parameters - Page 4:
2025-05-26 12:30:53,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:53,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:54,113 - INFO - Response - Page 4:
2025-05-26 12:30:54,113 - INFO - 第 4 页获取到 100 条记录
2025-05-26 12:30:54,316 - INFO - Request Parameters - Page 5:
2025-05-26 12:30:54,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:54,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:55,113 - INFO - Response - Page 5:
2025-05-26 12:30:55,113 - INFO - 第 5 页获取到 100 条记录
2025-05-26 12:30:55,316 - INFO - Request Parameters - Page 6:
2025-05-26 12:30:55,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:30:55,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:30:55,988 - INFO - Response - Page 6:
2025-05-26 12:30:55,988 - INFO - 第 6 页获取到 30 条记录
2025-05-26 12:30:56,191 - INFO - 查询完成，共获取到 530 条记录
2025-05-26 12:30:56,191 - INFO - 获取到 530 条表单数据
2025-05-26 12:30:56,191 - INFO - 当前日期 2025-05-24 有 4 条MySQL数据需要处理
2025-05-26 12:30:56,191 - INFO - 开始批量插入 4 条新记录
2025-05-26 12:30:56,363 - INFO - 批量插入响应状态码: 200
2025-05-26 12:30:56,363 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 04:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '200', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6FF3256E-A717-7844-8F56-73285DBFB7B6', 'x-acs-trace-id': 'ea20d622ff04c8a4c9ca56536617a67b', 'etag': '2lnwuYCKV1ScKK2P1WcWtng0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 12:30:56,363 - INFO - 批量插入响应体: {'result': ['FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BMC', 'FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BMD', 'FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BME', 'FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BMF']}
2025-05-26 12:30:56,363 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-26 12:30:56,363 - INFO - 成功插入的数据ID: ['FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BMC', 'FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BMD', 'FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BME', 'FINST-RN766181O5RV4171CUBXV81XHWX628WOAL4BMF']
2025-05-26 12:31:01,378 - INFO - 批量插入完成，共 4 条记录
2025-05-26 12:31:01,378 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-05-26 12:31:01,378 - INFO - 开始处理日期: 2025-05-25
2025-05-26 12:31:01,378 - INFO - Request Parameters - Page 1:
2025-05-26 12:31:01,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:31:01,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:31:02,222 - INFO - Response - Page 1:
2025-05-26 12:31:02,222 - INFO - 第 1 页获取到 100 条记录
2025-05-26 12:31:02,425 - INFO - Request Parameters - Page 2:
2025-05-26 12:31:02,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:31:02,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:31:03,285 - INFO - Response - Page 2:
2025-05-26 12:31:03,285 - INFO - 第 2 页获取到 100 条记录
2025-05-26 12:31:03,488 - INFO - Request Parameters - Page 3:
2025-05-26 12:31:03,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:31:03,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:31:04,285 - INFO - Response - Page 3:
2025-05-26 12:31:04,285 - INFO - 第 3 页获取到 100 条记录
2025-05-26 12:31:04,488 - INFO - Request Parameters - Page 4:
2025-05-26 12:31:04,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:31:04,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:31:05,316 - INFO - Response - Page 4:
2025-05-26 12:31:05,316 - INFO - 第 4 页获取到 100 条记录
2025-05-26 12:31:05,519 - INFO - Request Parameters - Page 5:
2025-05-26 12:31:05,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:31:05,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:31:06,363 - INFO - Response - Page 5:
2025-05-26 12:31:06,363 - INFO - 第 5 页获取到 100 条记录
2025-05-26 12:31:06,566 - INFO - Request Parameters - Page 6:
2025-05-26 12:31:06,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:31:06,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:31:07,253 - INFO - Response - Page 6:
2025-05-26 12:31:07,253 - INFO - 第 6 页获取到 30 条记录
2025-05-26 12:31:07,456 - INFO - 查询完成，共获取到 530 条记录
2025-05-26 12:31:07,456 - INFO - 获取到 530 条表单数据
2025-05-26 12:31:07,456 - INFO - 当前日期 2025-05-25 有 186 条MySQL数据需要处理
2025-05-26 12:31:07,456 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM8R
2025-05-26 12:31:07,925 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM8R
2025-05-26 12:31:07,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 868.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 868.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 25}]
2025-05-26 12:31:07,925 - INFO - 日期 2025-05-25 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-26 12:31:07,925 - INFO - 数据同步完成！更新: 1 条，插入: 4 条，错误: 2 条
2025-05-26 12:32:07,940 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 12:32:07,940 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 12:32:07,940 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 12:32:08,019 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 530 条记录
2025-05-26 12:32:08,019 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 12:32:08,019 - INFO - 开始处理日期: 2025-05-25
2025-05-26 12:32:08,019 - INFO - Request Parameters - Page 1:
2025-05-26 12:32:08,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:32:08,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:32:08,925 - INFO - Response - Page 1:
2025-05-26 12:32:08,925 - INFO - 第 1 页获取到 100 条记录
2025-05-26 12:32:09,128 - INFO - Request Parameters - Page 2:
2025-05-26 12:32:09,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:32:09,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:32:09,925 - INFO - Response - Page 2:
2025-05-26 12:32:09,925 - INFO - 第 2 页获取到 100 条记录
2025-05-26 12:32:10,128 - INFO - Request Parameters - Page 3:
2025-05-26 12:32:10,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:32:10,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:32:10,956 - INFO - Response - Page 3:
2025-05-26 12:32:10,956 - INFO - 第 3 页获取到 100 条记录
2025-05-26 12:32:11,159 - INFO - Request Parameters - Page 4:
2025-05-26 12:32:11,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:32:11,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:32:12,081 - INFO - Response - Page 4:
2025-05-26 12:32:12,081 - INFO - 第 4 页获取到 100 条记录
2025-05-26 12:32:12,284 - INFO - Request Parameters - Page 5:
2025-05-26 12:32:12,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:32:12,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:32:13,222 - INFO - Response - Page 5:
2025-05-26 12:32:13,222 - INFO - 第 5 页获取到 100 条记录
2025-05-26 12:32:13,425 - INFO - Request Parameters - Page 6:
2025-05-26 12:32:13,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 12:32:13,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 12:32:14,081 - INFO - Response - Page 6:
2025-05-26 12:32:14,081 - INFO - 第 6 页获取到 30 条记录
2025-05-26 12:32:14,284 - INFO - 查询完成，共获取到 530 条记录
2025-05-26 12:32:14,284 - INFO - 获取到 530 条表单数据
2025-05-26 12:32:14,284 - INFO - 当前日期 2025-05-25 有 530 条MySQL数据需要处理
2025-05-26 12:32:14,300 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 12:32:14,300 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 12:32:14,300 - INFO - 同步完成
2025-05-26 13:30:33,886 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 13:30:33,886 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 13:30:33,886 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 13:30:33,964 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 195 条记录
2025-05-26 13:30:33,964 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 13:30:33,964 - INFO - 开始处理日期: 2025-05-21
2025-05-26 13:30:33,980 - INFO - Request Parameters - Page 1:
2025-05-26 13:30:33,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:30:33,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:30:42,089 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D06DBA84-D4FC-7368-BAE2-52F40DEF5FAC Response: {'code': 'ServiceUnavailable', 'requestid': 'D06DBA84-D4FC-7368-BAE2-52F40DEF5FAC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D06DBA84-D4FC-7368-BAE2-52F40DEF5FAC)
2025-05-26 13:30:42,089 - INFO - 开始处理日期: 2025-05-23
2025-05-26 13:30:42,089 - INFO - Request Parameters - Page 1:
2025-05-26 13:30:42,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:30:42,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:30:50,214 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-9844-7C13-9649-BA511D168F21 Response: {'code': 'ServiceUnavailable', 'requestid': '********-9844-7C13-9649-BA511D168F21', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-9844-7C13-9649-BA511D168F21)
2025-05-26 13:30:50,214 - INFO - 开始处理日期: 2025-05-24
2025-05-26 13:30:50,214 - INFO - Request Parameters - Page 1:
2025-05-26 13:30:50,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:30:50,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:30:58,339 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 31547DF3-FB25-7C06-880B-6FC973D42B93 Response: {'code': 'ServiceUnavailable', 'requestid': '31547DF3-FB25-7C06-880B-6FC973D42B93', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 31547DF3-FB25-7C06-880B-6FC973D42B93)
2025-05-26 13:30:58,339 - INFO - 开始处理日期: 2025-05-25
2025-05-26 13:30:58,339 - INFO - Request Parameters - Page 1:
2025-05-26 13:30:58,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:30:58,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:30:59,574 - INFO - Response - Page 1:
2025-05-26 13:30:59,574 - INFO - 第 1 页获取到 100 条记录
2025-05-26 13:30:59,777 - INFO - Request Parameters - Page 2:
2025-05-26 13:30:59,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:30:59,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:31:00,730 - INFO - Response - Page 2:
2025-05-26 13:31:00,730 - INFO - 第 2 页获取到 100 条记录
2025-05-26 13:31:00,933 - INFO - Request Parameters - Page 3:
2025-05-26 13:31:00,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:31:00,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:31:01,699 - INFO - Response - Page 3:
2025-05-26 13:31:01,699 - INFO - 第 3 页获取到 100 条记录
2025-05-26 13:31:01,902 - INFO - Request Parameters - Page 4:
2025-05-26 13:31:01,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:31:01,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:31:02,683 - INFO - Response - Page 4:
2025-05-26 13:31:02,683 - INFO - 第 4 页获取到 100 条记录
2025-05-26 13:31:02,886 - INFO - Request Parameters - Page 5:
2025-05-26 13:31:02,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:31:02,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:31:03,683 - INFO - Response - Page 5:
2025-05-26 13:31:03,683 - INFO - 第 5 页获取到 100 条记录
2025-05-26 13:31:03,886 - INFO - Request Parameters - Page 6:
2025-05-26 13:31:03,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:31:03,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:31:04,667 - INFO - Response - Page 6:
2025-05-26 13:31:04,667 - INFO - 第 6 页获取到 30 条记录
2025-05-26 13:31:04,870 - INFO - 查询完成，共获取到 530 条记录
2025-05-26 13:31:04,870 - INFO - 获取到 530 条表单数据
2025-05-26 13:31:04,870 - INFO - 当前日期 2025-05-25 有 186 条MySQL数据需要处理
2025-05-26 13:31:04,870 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 13:31:04,870 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-26 13:32:04,886 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 13:32:04,886 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 13:32:04,886 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 13:32:04,964 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 530 条记录
2025-05-26 13:32:04,964 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 13:32:04,964 - INFO - 开始处理日期: 2025-05-25
2025-05-26 13:32:04,964 - INFO - Request Parameters - Page 1:
2025-05-26 13:32:04,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:32:04,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:32:06,104 - INFO - Response - Page 1:
2025-05-26 13:32:06,104 - INFO - 第 1 页获取到 100 条记录
2025-05-26 13:32:06,308 - INFO - Request Parameters - Page 2:
2025-05-26 13:32:06,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:32:06,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:32:07,073 - INFO - Response - Page 2:
2025-05-26 13:32:07,073 - INFO - 第 2 页获取到 100 条记录
2025-05-26 13:32:07,276 - INFO - Request Parameters - Page 3:
2025-05-26 13:32:07,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:32:07,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:32:08,042 - INFO - Response - Page 3:
2025-05-26 13:32:08,042 - INFO - 第 3 页获取到 100 条记录
2025-05-26 13:32:08,245 - INFO - Request Parameters - Page 4:
2025-05-26 13:32:08,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:32:08,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:32:08,995 - INFO - Response - Page 4:
2025-05-26 13:32:09,011 - INFO - 第 4 页获取到 100 条记录
2025-05-26 13:32:09,214 - INFO - Request Parameters - Page 5:
2025-05-26 13:32:09,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:32:09,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:32:10,417 - INFO - Response - Page 5:
2025-05-26 13:32:10,417 - INFO - 第 5 页获取到 100 条记录
2025-05-26 13:32:10,620 - INFO - Request Parameters - Page 6:
2025-05-26 13:32:10,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 13:32:10,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 13:32:11,323 - INFO - Response - Page 6:
2025-05-26 13:32:11,323 - INFO - 第 6 页获取到 30 条记录
2025-05-26 13:32:11,526 - INFO - 查询完成，共获取到 530 条记录
2025-05-26 13:32:11,526 - INFO - 获取到 530 条表单数据
2025-05-26 13:32:11,526 - INFO - 当前日期 2025-05-25 有 530 条MySQL数据需要处理
2025-05-26 13:32:11,542 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 13:32:11,542 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 13:32:11,542 - INFO - 同步完成
2025-05-26 14:30:33,894 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 14:30:33,894 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 14:30:33,894 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 14:30:33,972 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 201 条记录
2025-05-26 14:30:33,972 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 14:30:33,972 - INFO - 开始处理日期: 2025-05-21
2025-05-26 14:30:33,972 - INFO - Request Parameters - Page 1:
2025-05-26 14:30:33,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:33,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:42,128 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 315E38A7-B586-70D4-9057-7EEF85922016 Response: {'code': 'ServiceUnavailable', 'requestid': '315E38A7-B586-70D4-9057-7EEF85922016', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 315E38A7-B586-70D4-9057-7EEF85922016)
2025-05-26 14:30:42,128 - INFO - 开始处理日期: 2025-05-23
2025-05-26 14:30:42,128 - INFO - Request Parameters - Page 1:
2025-05-26 14:30:42,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:42,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:50,253 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5008F328-DE12-7154-9982-A2DB7057A559 Response: {'code': 'ServiceUnavailable', 'requestid': '5008F328-DE12-7154-9982-A2DB7057A559', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5008F328-DE12-7154-9982-A2DB7057A559)
2025-05-26 14:30:50,253 - INFO - 开始处理日期: 2025-05-24
2025-05-26 14:30:50,253 - INFO - Request Parameters - Page 1:
2025-05-26 14:30:50,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:50,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:52,863 - INFO - Response - Page 1:
2025-05-26 14:30:52,863 - INFO - 第 1 页获取到 100 条记录
2025-05-26 14:30:53,066 - INFO - Request Parameters - Page 2:
2025-05-26 14:30:53,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:53,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:53,894 - INFO - Response - Page 2:
2025-05-26 14:30:53,894 - INFO - 第 2 页获取到 100 条记录
2025-05-26 14:30:54,097 - INFO - Request Parameters - Page 3:
2025-05-26 14:30:54,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:54,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:54,941 - INFO - Response - Page 3:
2025-05-26 14:30:54,941 - INFO - 第 3 页获取到 100 条记录
2025-05-26 14:30:55,144 - INFO - Request Parameters - Page 4:
2025-05-26 14:30:55,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:55,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:55,972 - INFO - Response - Page 4:
2025-05-26 14:30:55,972 - INFO - 第 4 页获取到 100 条记录
2025-05-26 14:30:56,175 - INFO - Request Parameters - Page 5:
2025-05-26 14:30:56,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:56,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:56,941 - INFO - Response - Page 5:
2025-05-26 14:30:56,941 - INFO - 第 5 页获取到 100 条记录
2025-05-26 14:30:57,144 - INFO - Request Parameters - Page 6:
2025-05-26 14:30:57,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:57,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:57,800 - INFO - Response - Page 6:
2025-05-26 14:30:57,800 - INFO - 第 6 页获取到 34 条记录
2025-05-26 14:30:58,003 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 14:30:58,003 - INFO - 获取到 534 条表单数据
2025-05-26 14:30:58,003 - INFO - 当前日期 2025-05-24 有 4 条MySQL数据需要处理
2025-05-26 14:30:58,003 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 14:30:58,003 - INFO - 开始处理日期: 2025-05-25
2025-05-26 14:30:58,003 - INFO - Request Parameters - Page 1:
2025-05-26 14:30:58,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:58,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:58,800 - INFO - Response - Page 1:
2025-05-26 14:30:58,800 - INFO - 第 1 页获取到 100 条记录
2025-05-26 14:30:59,003 - INFO - Request Parameters - Page 2:
2025-05-26 14:30:59,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:30:59,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:30:59,863 - INFO - Response - Page 2:
2025-05-26 14:30:59,863 - INFO - 第 2 页获取到 100 条记录
2025-05-26 14:31:00,066 - INFO - Request Parameters - Page 3:
2025-05-26 14:31:00,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:31:00,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:31:00,925 - INFO - Response - Page 3:
2025-05-26 14:31:00,925 - INFO - 第 3 页获取到 100 条记录
2025-05-26 14:31:01,128 - INFO - Request Parameters - Page 4:
2025-05-26 14:31:01,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:31:01,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:31:01,925 - INFO - Response - Page 4:
2025-05-26 14:31:01,925 - INFO - 第 4 页获取到 100 条记录
2025-05-26 14:31:02,128 - INFO - Request Parameters - Page 5:
2025-05-26 14:31:02,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:31:02,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:31:03,019 - INFO - Response - Page 5:
2025-05-26 14:31:03,019 - INFO - 第 5 页获取到 100 条记录
2025-05-26 14:31:03,222 - INFO - Request Parameters - Page 6:
2025-05-26 14:31:03,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:31:03,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:31:03,909 - INFO - Response - Page 6:
2025-05-26 14:31:03,909 - INFO - 第 6 页获取到 30 条记录
2025-05-26 14:31:04,113 - INFO - 查询完成，共获取到 530 条记录
2025-05-26 14:31:04,113 - INFO - 获取到 530 条表单数据
2025-05-26 14:31:04,113 - INFO - 当前日期 2025-05-25 有 192 条MySQL数据需要处理
2025-05-26 14:31:04,113 - INFO - 开始批量插入 6 条新记录
2025-05-26 14:31:04,284 - INFO - 批量插入响应状态码: 200
2025-05-26 14:31:04,284 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 06:30:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '294', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E5D44F3E-66D5-7B2C-9C81-7C5AC8D19699', 'x-acs-trace-id': '33a4f8e5fbffe5777cb9287fcfd2d0a2', 'etag': '2FrgOt2K2fSuUOC9ia4nIlg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 14:31:04,284 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMC', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMD', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BME', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMF', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMG', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMH']}
2025-05-26 14:31:04,284 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-05-26 14:31:04,284 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMC', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMD', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BME', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMF', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMG', 'FINST-7PF66BA1F5RVEHLT7J1W1BUCFGYD2NL6LP4BMH']
2025-05-26 14:31:09,300 - INFO - 批量插入完成，共 6 条记录
2025-05-26 14:31:09,300 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-05-26 14:31:09,300 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 2 条
2025-05-26 14:32:09,315 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 14:32:09,315 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 14:32:09,315 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 14:32:09,393 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 543 条记录
2025-05-26 14:32:09,393 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 14:32:09,393 - INFO - 开始处理日期: 2025-05-25
2025-05-26 14:32:09,393 - INFO - Request Parameters - Page 1:
2025-05-26 14:32:09,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:32:09,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:32:10,378 - INFO - Response - Page 1:
2025-05-26 14:32:10,378 - INFO - 第 1 页获取到 100 条记录
2025-05-26 14:32:10,581 - INFO - Request Parameters - Page 2:
2025-05-26 14:32:10,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:32:10,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:32:11,378 - INFO - Response - Page 2:
2025-05-26 14:32:11,378 - INFO - 第 2 页获取到 100 条记录
2025-05-26 14:32:11,581 - INFO - Request Parameters - Page 3:
2025-05-26 14:32:11,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:32:11,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:32:12,331 - INFO - Response - Page 3:
2025-05-26 14:32:12,331 - INFO - 第 3 页获取到 100 条记录
2025-05-26 14:32:12,534 - INFO - Request Parameters - Page 4:
2025-05-26 14:32:12,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:32:12,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:32:13,362 - INFO - Response - Page 4:
2025-05-26 14:32:13,362 - INFO - 第 4 页获取到 100 条记录
2025-05-26 14:32:13,565 - INFO - Request Parameters - Page 5:
2025-05-26 14:32:13,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:32:13,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:32:14,393 - INFO - Response - Page 5:
2025-05-26 14:32:14,393 - INFO - 第 5 页获取到 100 条记录
2025-05-26 14:32:14,597 - INFO - Request Parameters - Page 6:
2025-05-26 14:32:14,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 14:32:14,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 14:32:15,268 - INFO - Response - Page 6:
2025-05-26 14:32:15,268 - INFO - 第 6 页获取到 36 条记录
2025-05-26 14:32:15,472 - INFO - 查询完成，共获取到 536 条记录
2025-05-26 14:32:15,472 - INFO - 获取到 536 条表单数据
2025-05-26 14:32:15,472 - INFO - 当前日期 2025-05-25 有 543 条MySQL数据需要处理
2025-05-26 14:32:15,487 - INFO - 开始批量插入 7 条新记录
2025-05-26 14:32:15,643 - INFO - 批量插入响应状态码: 200
2025-05-26 14:32:15,643 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 06:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '341', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '449D6E9F-465E-77A4-A07C-EACD20A28EDB', 'x-acs-trace-id': 'c159f4ffaef990683f582fcf029ca7f4', 'etag': '3FBBSPEolEfbyFIOsiYVGEw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 14:32:15,643 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMR', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMS', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMT', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMU', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMV', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMW', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMX']}
2025-05-26 14:32:15,643 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-26 14:32:15,643 - INFO - 成功插入的数据ID: ['FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMR', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMS', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMT', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMU', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMV', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMW', 'FINST-2PF66TC16HQV6ZM681ADP9WYHWUI2ONPMP4BMX']
2025-05-26 14:32:20,659 - INFO - 批量插入完成，共 7 条记录
2025-05-26 14:32:20,659 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-05-26 14:32:20,659 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-05-26 14:32:20,659 - INFO - 同步完成
2025-05-26 15:30:34,105 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 15:30:34,105 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 15:30:34,105 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 15:30:34,183 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 201 条记录
2025-05-26 15:30:34,183 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 15:30:34,183 - INFO - 开始处理日期: 2025-05-21
2025-05-26 15:30:34,183 - INFO - Request Parameters - Page 1:
2025-05-26 15:30:34,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:30:34,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:30:42,308 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C3DC109F-FF2E-7771-83E5-25347E1F0D11 Response: {'code': 'ServiceUnavailable', 'requestid': 'C3DC109F-FF2E-7771-83E5-25347E1F0D11', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C3DC109F-FF2E-7771-83E5-25347E1F0D11)
2025-05-26 15:30:42,308 - INFO - 开始处理日期: 2025-05-23
2025-05-26 15:30:42,308 - INFO - Request Parameters - Page 1:
2025-05-26 15:30:42,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:30:42,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:30:50,417 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 63CD2D92-70A1-7DC9-A406-77C8AC8D7078 Response: {'code': 'ServiceUnavailable', 'requestid': '63CD2D92-70A1-7DC9-A406-77C8AC8D7078', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 63CD2D92-70A1-7DC9-A406-77C8AC8D7078)
2025-05-26 15:30:50,417 - INFO - 开始处理日期: 2025-05-24
2025-05-26 15:30:50,417 - INFO - Request Parameters - Page 1:
2025-05-26 15:30:50,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:30:50,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:30:58,527 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4201A799-229E-7639-8DCC-35ADF1610721 Response: {'code': 'ServiceUnavailable', 'requestid': '4201A799-229E-7639-8DCC-35ADF1610721', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4201A799-229E-7639-8DCC-35ADF1610721)
2025-05-26 15:30:58,527 - INFO - 开始处理日期: 2025-05-25
2025-05-26 15:30:58,527 - INFO - Request Parameters - Page 1:
2025-05-26 15:30:58,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:30:58,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:31:04,199 - INFO - Response - Page 1:
2025-05-26 15:31:04,199 - INFO - 第 1 页获取到 100 条记录
2025-05-26 15:31:04,402 - INFO - Request Parameters - Page 2:
2025-05-26 15:31:04,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:31:04,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:31:05,261 - INFO - Response - Page 2:
2025-05-26 15:31:05,261 - INFO - 第 2 页获取到 100 条记录
2025-05-26 15:31:05,464 - INFO - Request Parameters - Page 3:
2025-05-26 15:31:05,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:31:05,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:31:06,292 - INFO - Response - Page 3:
2025-05-26 15:31:06,292 - INFO - 第 3 页获取到 100 条记录
2025-05-26 15:31:06,495 - INFO - Request Parameters - Page 4:
2025-05-26 15:31:06,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:31:06,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:31:07,386 - INFO - Response - Page 4:
2025-05-26 15:31:07,386 - INFO - 第 4 页获取到 100 条记录
2025-05-26 15:31:07,589 - INFO - Request Parameters - Page 5:
2025-05-26 15:31:07,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:31:07,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:31:08,495 - INFO - Response - Page 5:
2025-05-26 15:31:08,495 - INFO - 第 5 页获取到 100 条记录
2025-05-26 15:31:08,699 - INFO - Request Parameters - Page 6:
2025-05-26 15:31:08,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:31:08,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:31:09,355 - INFO - Response - Page 6:
2025-05-26 15:31:09,355 - INFO - 第 6 页获取到 43 条记录
2025-05-26 15:31:09,558 - INFO - 查询完成，共获取到 543 条记录
2025-05-26 15:31:09,558 - INFO - 获取到 543 条表单数据
2025-05-26 15:31:09,558 - INFO - 当前日期 2025-05-25 有 192 条MySQL数据需要处理
2025-05-26 15:31:09,558 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 15:31:09,558 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-26 15:32:09,573 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 15:32:09,573 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 15:32:09,573 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 15:32:09,651 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 561 条记录
2025-05-26 15:32:09,651 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 15:32:09,651 - INFO - 开始处理日期: 2025-05-25
2025-05-26 15:32:09,651 - INFO - Request Parameters - Page 1:
2025-05-26 15:32:09,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:32:09,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:32:10,464 - INFO - Response - Page 1:
2025-05-26 15:32:10,464 - INFO - 第 1 页获取到 100 条记录
2025-05-26 15:32:10,667 - INFO - Request Parameters - Page 2:
2025-05-26 15:32:10,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:32:10,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:32:11,448 - INFO - Response - Page 2:
2025-05-26 15:32:11,448 - INFO - 第 2 页获取到 100 条记录
2025-05-26 15:32:11,651 - INFO - Request Parameters - Page 3:
2025-05-26 15:32:11,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:32:11,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:32:12,370 - INFO - Response - Page 3:
2025-05-26 15:32:12,386 - INFO - 第 3 页获取到 100 条记录
2025-05-26 15:32:12,589 - INFO - Request Parameters - Page 4:
2025-05-26 15:32:12,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:32:12,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:32:13,448 - INFO - Response - Page 4:
2025-05-26 15:32:13,448 - INFO - 第 4 页获取到 100 条记录
2025-05-26 15:32:13,651 - INFO - Request Parameters - Page 5:
2025-05-26 15:32:13,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:32:13,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:32:14,511 - INFO - Response - Page 5:
2025-05-26 15:32:14,511 - INFO - 第 5 页获取到 100 条记录
2025-05-26 15:32:14,714 - INFO - Request Parameters - Page 6:
2025-05-26 15:32:14,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 15:32:14,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 15:32:15,417 - INFO - Response - Page 6:
2025-05-26 15:32:15,417 - INFO - 第 6 页获取到 43 条记录
2025-05-26 15:32:15,620 - INFO - 查询完成，共获取到 543 条记录
2025-05-26 15:32:15,620 - INFO - 获取到 543 条表单数据
2025-05-26 15:32:15,620 - INFO - 当前日期 2025-05-25 有 561 条MySQL数据需要处理
2025-05-26 15:32:15,636 - INFO - 开始批量插入 18 条新记录
2025-05-26 15:32:15,792 - INFO - 批量插入响应状态码: 200
2025-05-26 15:32:15,792 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 07:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '876', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6927C43C-4230-7A16-9FF9-CD9747E43C0B', 'x-acs-trace-id': 'bd57bd61ae1904891779b2c6b3097ef1', 'etag': '8eDAOxdK4X/2Ux9qDUHDARg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 15:32:15,792 - INFO - 批量插入响应体: {'result': ['FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM21', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM31', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM41', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM51', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM61', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM71', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM81', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM91', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMA1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMB1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMC1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMD1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BME1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMF1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMG1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMH1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMI1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMJ1']}
2025-05-26 15:32:15,792 - INFO - 批量插入表单数据成功，批次 1，共 18 条记录
2025-05-26 15:32:15,792 - INFO - 成功插入的数据ID: ['FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM21', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM31', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM41', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM51', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM61', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM71', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM81', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BM91', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMA1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMB1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMC1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMD1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BME1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMF1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMG1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMH1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMI1', 'FINST-R1A66H91H7RV101LEV3J0D8KDKNX2QKVRR4BMJ1']
2025-05-26 15:32:20,807 - INFO - 批量插入完成，共 18 条记录
2025-05-26 15:32:20,807 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 18 条，错误: 0 条
2025-05-26 15:32:20,807 - INFO - 数据同步完成！更新: 0 条，插入: 18 条，错误: 0 条
2025-05-26 15:32:20,807 - INFO - 同步完成
2025-05-26 16:30:34,191 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 16:30:34,191 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 16:30:34,191 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 16:30:34,269 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 205 条记录
2025-05-26 16:30:34,269 - INFO - 获取到 4 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25']
2025-05-26 16:30:34,269 - INFO - 开始处理日期: 2025-05-21
2025-05-26 16:30:34,269 - INFO - Request Parameters - Page 1:
2025-05-26 16:30:34,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:30:34,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:30:42,410 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6C2CF1C8-57AD-72A5-91C0-2EF723002E3E Response: {'code': 'ServiceUnavailable', 'requestid': '6C2CF1C8-57AD-72A5-91C0-2EF723002E3E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6C2CF1C8-57AD-72A5-91C0-2EF723002E3E)
2025-05-26 16:30:42,410 - INFO - 开始处理日期: 2025-05-23
2025-05-26 16:30:42,410 - INFO - Request Parameters - Page 1:
2025-05-26 16:30:42,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:30:42,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:30:46,097 - INFO - Response - Page 1:
2025-05-26 16:30:46,097 - INFO - 第 1 页获取到 100 条记录
2025-05-26 16:30:46,301 - INFO - Request Parameters - Page 2:
2025-05-26 16:30:46,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:30:46,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:30:54,425 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 28FC203A-2EA8-78BC-A7A0-C8DAE6990B5D Response: {'code': 'ServiceUnavailable', 'requestid': '28FC203A-2EA8-78BC-A7A0-C8DAE6990B5D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 28FC203A-2EA8-78BC-A7A0-C8DAE6990B5D)
2025-05-26 16:30:54,425 - INFO - 开始处理日期: 2025-05-24
2025-05-26 16:30:54,425 - INFO - Request Parameters - Page 1:
2025-05-26 16:30:54,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:30:54,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:02,535 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F7CC50BD-8B76-7302-8631-0622C45A1CD0 Response: {'code': 'ServiceUnavailable', 'requestid': 'F7CC50BD-8B76-7302-8631-0622C45A1CD0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F7CC50BD-8B76-7302-8631-0622C45A1CD0)
2025-05-26 16:31:02,535 - INFO - 开始处理日期: 2025-05-25
2025-05-26 16:31:02,535 - INFO - Request Parameters - Page 1:
2025-05-26 16:31:02,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:31:02,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:05,957 - INFO - Response - Page 1:
2025-05-26 16:31:05,957 - INFO - 第 1 页获取到 100 条记录
2025-05-26 16:31:06,160 - INFO - Request Parameters - Page 2:
2025-05-26 16:31:06,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:31:06,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:06,972 - INFO - Response - Page 2:
2025-05-26 16:31:06,972 - INFO - 第 2 页获取到 100 条记录
2025-05-26 16:31:07,175 - INFO - Request Parameters - Page 3:
2025-05-26 16:31:07,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:31:07,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:08,144 - INFO - Response - Page 3:
2025-05-26 16:31:08,144 - INFO - 第 3 页获取到 100 条记录
2025-05-26 16:31:08,347 - INFO - Request Parameters - Page 4:
2025-05-26 16:31:08,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:31:08,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:09,222 - INFO - Response - Page 4:
2025-05-26 16:31:09,222 - INFO - 第 4 页获取到 100 条记录
2025-05-26 16:31:09,425 - INFO - Request Parameters - Page 5:
2025-05-26 16:31:09,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:31:09,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:10,254 - INFO - Response - Page 5:
2025-05-26 16:31:10,254 - INFO - 第 5 页获取到 100 条记录
2025-05-26 16:31:10,457 - INFO - Request Parameters - Page 6:
2025-05-26 16:31:10,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:31:10,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:31:11,285 - INFO - Response - Page 6:
2025-05-26 16:31:11,285 - INFO - 第 6 页获取到 61 条记录
2025-05-26 16:31:11,488 - INFO - 查询完成，共获取到 561 条记录
2025-05-26 16:31:11,488 - INFO - 获取到 561 条表单数据
2025-05-26 16:31:11,488 - INFO - 当前日期 2025-05-25 有 195 条MySQL数据需要处理
2025-05-26 16:31:11,488 - INFO - 开始批量插入 3 条新记录
2025-05-26 16:31:11,660 - INFO - 批量插入响应状态码: 200
2025-05-26 16:31:11,660 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 08:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CCC88FE6-BC76-74CA-B7CC-0C264697C08C', 'x-acs-trace-id': '6a8e4ff97adc3a723a5fd679192db7fb', 'etag': '1FecIVwjRXjoPAeZRoDjs4A3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 16:31:11,660 - INFO - 批量插入响应体: {'result': ['FINST-OPC666D167RVUOMTAC6Y19M34ONT3LVNVT4BMH', 'FINST-OPC666D167RVUOMTAC6Y19M34ONT3LVNVT4BMI', 'FINST-OPC666D167RVUOMTAC6Y19M34ONT3LVNVT4BMJ']}
2025-05-26 16:31:11,660 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-26 16:31:11,660 - INFO - 成功插入的数据ID: ['FINST-OPC666D167RVUOMTAC6Y19M34ONT3LVNVT4BMH', 'FINST-OPC666D167RVUOMTAC6Y19M34ONT3LVNVT4BMI', 'FINST-OPC666D167RVUOMTAC6Y19M34ONT3LVNVT4BMJ']
2025-05-26 16:31:16,675 - INFO - 批量插入完成，共 3 条记录
2025-05-26 16:31:16,675 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-26 16:31:16,675 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 3 条
2025-05-26 16:32:16,691 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 16:32:16,691 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 16:32:16,691 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 16:32:16,769 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 564 条记录
2025-05-26 16:32:16,769 - INFO - 获取到 1 个日期需要处理: ['2025-05-25']
2025-05-26 16:32:16,769 - INFO - 开始处理日期: 2025-05-25
2025-05-26 16:32:16,769 - INFO - Request Parameters - Page 1:
2025-05-26 16:32:16,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:32:16,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:32:17,691 - INFO - Response - Page 1:
2025-05-26 16:32:17,691 - INFO - 第 1 页获取到 100 条记录
2025-05-26 16:32:17,894 - INFO - Request Parameters - Page 2:
2025-05-26 16:32:17,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:32:17,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:32:18,753 - INFO - Response - Page 2:
2025-05-26 16:32:18,753 - INFO - 第 2 页获取到 100 条记录
2025-05-26 16:32:18,956 - INFO - Request Parameters - Page 3:
2025-05-26 16:32:18,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:32:18,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:32:19,816 - INFO - Response - Page 3:
2025-05-26 16:32:19,816 - INFO - 第 3 页获取到 100 条记录
2025-05-26 16:32:20,019 - INFO - Request Parameters - Page 4:
2025-05-26 16:32:20,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:32:20,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:32:20,800 - INFO - Response - Page 4:
2025-05-26 16:32:20,800 - INFO - 第 4 页获取到 100 条记录
2025-05-26 16:32:21,019 - INFO - Request Parameters - Page 5:
2025-05-26 16:32:21,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:32:21,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:32:21,878 - INFO - Response - Page 5:
2025-05-26 16:32:21,878 - INFO - 第 5 页获取到 100 条记录
2025-05-26 16:32:22,081 - INFO - Request Parameters - Page 6:
2025-05-26 16:32:22,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 16:32:22,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 16:32:22,862 - INFO - Response - Page 6:
2025-05-26 16:32:22,862 - INFO - 第 6 页获取到 64 条记录
2025-05-26 16:32:23,066 - INFO - 查询完成，共获取到 564 条记录
2025-05-26 16:32:23,066 - INFO - 获取到 564 条表单数据
2025-05-26 16:32:23,066 - INFO - 当前日期 2025-05-25 有 564 条MySQL数据需要处理
2025-05-26 16:32:23,081 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 16:32:23,081 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 16:32:23,081 - INFO - 同步完成
2025-05-26 17:30:33,731 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 17:30:33,731 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 17:30:33,731 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 17:30:33,809 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 207 条记录
2025-05-26 17:30:33,809 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 17:30:33,809 - INFO - 开始处理日期: 2025-05-21
2025-05-26 17:30:33,824 - INFO - Request Parameters - Page 1:
2025-05-26 17:30:33,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:30:33,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:30:41,949 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B40F3D6F-FCEE-7DBB-B4B1-C422A69CF23D Response: {'code': 'ServiceUnavailable', 'requestid': 'B40F3D6F-FCEE-7DBB-B4B1-C422A69CF23D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B40F3D6F-FCEE-7DBB-B4B1-C422A69CF23D)
2025-05-26 17:30:41,949 - INFO - 开始处理日期: 2025-05-23
2025-05-26 17:30:41,949 - INFO - Request Parameters - Page 1:
2025-05-26 17:30:41,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:30:41,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:30:50,074 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 63805C62-68C7-7E03-890A-F6E05F61C2AF Response: {'code': 'ServiceUnavailable', 'requestid': '63805C62-68C7-7E03-890A-F6E05F61C2AF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 63805C62-68C7-7E03-890A-F6E05F61C2AF)
2025-05-26 17:30:50,074 - INFO - 开始处理日期: 2025-05-24
2025-05-26 17:30:50,074 - INFO - Request Parameters - Page 1:
2025-05-26 17:30:50,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:30:50,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:30:58,199 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FEECC288-E0BA-78BA-BCA5-A4913C2FC206 Response: {'code': 'ServiceUnavailable', 'requestid': 'FEECC288-E0BA-78BA-BCA5-A4913C2FC206', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FEECC288-E0BA-78BA-BCA5-A4913C2FC206)
2025-05-26 17:30:58,199 - INFO - 开始处理日期: 2025-05-25
2025-05-26 17:30:58,199 - INFO - Request Parameters - Page 1:
2025-05-26 17:30:58,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:30:58,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:00,918 - INFO - Response - Page 1:
2025-05-26 17:31:00,918 - INFO - 第 1 页获取到 100 条记录
2025-05-26 17:31:01,121 - INFO - Request Parameters - Page 2:
2025-05-26 17:31:01,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:31:01,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:01,887 - INFO - Response - Page 2:
2025-05-26 17:31:01,887 - INFO - 第 2 页获取到 100 条记录
2025-05-26 17:31:02,090 - INFO - Request Parameters - Page 3:
2025-05-26 17:31:02,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:31:02,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:02,871 - INFO - Response - Page 3:
2025-05-26 17:31:02,871 - INFO - 第 3 页获取到 100 条记录
2025-05-26 17:31:03,074 - INFO - Request Parameters - Page 4:
2025-05-26 17:31:03,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:31:03,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:03,934 - INFO - Response - Page 4:
2025-05-26 17:31:03,934 - INFO - 第 4 页获取到 100 条记录
2025-05-26 17:31:04,137 - INFO - Request Parameters - Page 5:
2025-05-26 17:31:04,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:31:04,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:04,980 - INFO - Response - Page 5:
2025-05-26 17:31:04,980 - INFO - 第 5 页获取到 100 条记录
2025-05-26 17:31:05,184 - INFO - Request Parameters - Page 6:
2025-05-26 17:31:05,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:31:05,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:06,012 - INFO - Response - Page 6:
2025-05-26 17:31:06,012 - INFO - 第 6 页获取到 64 条记录
2025-05-26 17:31:06,215 - INFO - 查询完成，共获取到 564 条记录
2025-05-26 17:31:06,215 - INFO - 获取到 564 条表单数据
2025-05-26 17:31:06,215 - INFO - 当前日期 2025-05-25 有 196 条MySQL数据需要处理
2025-05-26 17:31:06,215 - INFO - 开始批量插入 1 条新记录
2025-05-26 17:31:06,371 - INFO - 批量插入响应状态码: 200
2025-05-26 17:31:06,371 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 09:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C9689F14-4E37-7793-93CD-7E78ED8D4DDA', 'x-acs-trace-id': '8a7eb1f7ddf3660ab12c6d5d24645eae', 'etag': '5Z8KabChrrP3RheoREdoiXg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 17:31:06,371 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1QFRVDW6QA6HYLD0483ZJ3GLP0W4BM6']}
2025-05-26 17:31:06,371 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-26 17:31:06,371 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1QFRVDW6QA6HYLD0483ZJ3GLP0W4BM6']
2025-05-26 17:31:11,387 - INFO - 批量插入完成，共 1 条记录
2025-05-26 17:31:11,387 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-26 17:31:11,387 - INFO - 开始处理日期: 2025-05-26
2025-05-26 17:31:11,387 - INFO - Request Parameters - Page 1:
2025-05-26 17:31:11,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:31:11,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:31:11,887 - INFO - Response - Page 1:
2025-05-26 17:31:11,887 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 17:31:11,887 - INFO - 获取到 0 条表单数据
2025-05-26 17:31:11,887 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 17:31:11,887 - INFO - 开始批量插入 1 条新记录
2025-05-26 17:31:12,012 - INFO - 批量插入响应状态码: 200
2025-05-26 17:31:12,012 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 09:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3B39B54E-F026-7B8B-A7EC-3F9AF094FADC', 'x-acs-trace-id': '077657af6f2def25041582f26759046e', 'etag': '5e57AWgAZ+vhOZc3/mhL4aQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 17:31:12,012 - INFO - 批量插入响应体: {'result': ['FINST-0O9664D105RVQI3GFQN1W43BFOCH2KYT0W4BMJ']}
2025-05-26 17:31:12,012 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-26 17:31:12,012 - INFO - 成功插入的数据ID: ['FINST-0O9664D105RVQI3GFQN1W43BFOCH2KYT0W4BMJ']
2025-05-26 17:31:17,027 - INFO - 批量插入完成，共 1 条记录
2025-05-26 17:31:17,027 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-26 17:31:17,027 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 3 条
2025-05-26 17:32:17,042 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 17:32:17,042 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 17:32:17,042 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 17:32:17,121 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 566 条记录
2025-05-26 17:32:17,121 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 17:32:17,136 - INFO - 开始处理日期: 2025-05-25
2025-05-26 17:32:17,136 - INFO - Request Parameters - Page 1:
2025-05-26 17:32:17,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:17,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:18,058 - INFO - Response - Page 1:
2025-05-26 17:32:18,058 - INFO - 第 1 页获取到 100 条记录
2025-05-26 17:32:18,261 - INFO - Request Parameters - Page 2:
2025-05-26 17:32:18,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:18,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:19,136 - INFO - Response - Page 2:
2025-05-26 17:32:19,136 - INFO - 第 2 页获取到 100 条记录
2025-05-26 17:32:19,339 - INFO - Request Parameters - Page 3:
2025-05-26 17:32:19,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:19,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:20,199 - INFO - Response - Page 3:
2025-05-26 17:32:20,199 - INFO - 第 3 页获取到 100 条记录
2025-05-26 17:32:20,402 - INFO - Request Parameters - Page 4:
2025-05-26 17:32:20,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:20,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:21,183 - INFO - Response - Page 4:
2025-05-26 17:32:21,183 - INFO - 第 4 页获取到 100 条记录
2025-05-26 17:32:21,386 - INFO - Request Parameters - Page 5:
2025-05-26 17:32:21,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:21,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:22,246 - INFO - Response - Page 5:
2025-05-26 17:32:22,246 - INFO - 第 5 页获取到 100 条记录
2025-05-26 17:32:22,449 - INFO - Request Parameters - Page 6:
2025-05-26 17:32:22,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:22,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:23,214 - INFO - Response - Page 6:
2025-05-26 17:32:23,214 - INFO - 第 6 页获取到 65 条记录
2025-05-26 17:32:23,417 - INFO - 查询完成，共获取到 565 条记录
2025-05-26 17:32:23,417 - INFO - 获取到 565 条表单数据
2025-05-26 17:32:23,417 - INFO - 当前日期 2025-05-25 有 565 条MySQL数据需要处理
2025-05-26 17:32:23,433 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 17:32:23,433 - INFO - 开始处理日期: 2025-05-26
2025-05-26 17:32:23,433 - INFO - Request Parameters - Page 1:
2025-05-26 17:32:23,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 17:32:23,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 17:32:23,964 - INFO - Response - Page 1:
2025-05-26 17:32:23,964 - INFO - 第 1 页获取到 1 条记录
2025-05-26 17:32:24,167 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 17:32:24,167 - INFO - 获取到 1 条表单数据
2025-05-26 17:32:24,167 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 17:32:24,167 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 17:32:24,167 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 17:32:24,167 - INFO - 同步完成
2025-05-26 18:30:34,286 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 18:30:34,286 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 18:30:34,286 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 18:30:34,364 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 207 条记录
2025-05-26 18:30:34,364 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 18:30:34,364 - INFO - 开始处理日期: 2025-05-21
2025-05-26 18:30:34,379 - INFO - Request Parameters - Page 1:
2025-05-26 18:30:34,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:34,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:42,489 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1972A248-062C-756C-BAEF-A01C2D8B0FC6 Response: {'code': 'ServiceUnavailable', 'requestid': '1972A248-062C-756C-BAEF-A01C2D8B0FC6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1972A248-062C-756C-BAEF-A01C2D8B0FC6)
2025-05-26 18:30:42,489 - INFO - 开始处理日期: 2025-05-23
2025-05-26 18:30:42,489 - INFO - Request Parameters - Page 1:
2025-05-26 18:30:42,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:42,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:50,598 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 19D5DB52-1706-7CD5-93DA-343A0F64337B Response: {'code': 'ServiceUnavailable', 'requestid': '19D5DB52-1706-7CD5-93DA-343A0F64337B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 19D5DB52-1706-7CD5-93DA-343A0F64337B)
2025-05-26 18:30:50,598 - INFO - 开始处理日期: 2025-05-24
2025-05-26 18:30:50,598 - INFO - Request Parameters - Page 1:
2025-05-26 18:30:50,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:50,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:53,567 - INFO - Response - Page 1:
2025-05-26 18:30:53,567 - INFO - 第 1 页获取到 100 条记录
2025-05-26 18:30:53,770 - INFO - Request Parameters - Page 2:
2025-05-26 18:30:53,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:53,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:54,661 - INFO - Response - Page 2:
2025-05-26 18:30:54,661 - INFO - 第 2 页获取到 100 条记录
2025-05-26 18:30:54,864 - INFO - Request Parameters - Page 3:
2025-05-26 18:30:54,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:54,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:55,661 - INFO - Response - Page 3:
2025-05-26 18:30:55,661 - INFO - 第 3 页获取到 100 条记录
2025-05-26 18:30:55,864 - INFO - Request Parameters - Page 4:
2025-05-26 18:30:55,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:55,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:56,786 - INFO - Response - Page 4:
2025-05-26 18:30:56,786 - INFO - 第 4 页获取到 100 条记录
2025-05-26 18:30:56,989 - INFO - Request Parameters - Page 5:
2025-05-26 18:30:56,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:56,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:57,801 - INFO - Response - Page 5:
2025-05-26 18:30:57,801 - INFO - 第 5 页获取到 100 条记录
2025-05-26 18:30:58,004 - INFO - Request Parameters - Page 6:
2025-05-26 18:30:58,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:58,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:30:58,707 - INFO - Response - Page 6:
2025-05-26 18:30:58,707 - INFO - 第 6 页获取到 34 条记录
2025-05-26 18:30:58,911 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 18:30:58,911 - INFO - 获取到 534 条表单数据
2025-05-26 18:30:58,911 - INFO - 当前日期 2025-05-24 有 5 条MySQL数据需要处理
2025-05-26 18:30:58,911 - INFO - 开始更新记录 - 表单实例ID: FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMLH
2025-05-26 18:30:59,364 - INFO - 更新表单数据成功: FINST-AI8667811EOV5YIEE707J562BNJZ2ODPBC3BMLH
2025-05-26 18:30:59,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16000.0, 'new_value': 156000.0}, {'field': 'total_amount', 'old_value': 16000.0, 'new_value': 156000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 12}]
2025-05-26 18:30:59,364 - INFO - 日期 2025-05-24 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-26 18:30:59,364 - INFO - 开始处理日期: 2025-05-25
2025-05-26 18:30:59,364 - INFO - Request Parameters - Page 1:
2025-05-26 18:30:59,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:30:59,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:00,192 - INFO - Response - Page 1:
2025-05-26 18:31:00,192 - INFO - 第 1 页获取到 100 条记录
2025-05-26 18:31:00,395 - INFO - Request Parameters - Page 2:
2025-05-26 18:31:00,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:31:00,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:01,161 - INFO - Response - Page 2:
2025-05-26 18:31:01,161 - INFO - 第 2 页获取到 100 条记录
2025-05-26 18:31:01,364 - INFO - Request Parameters - Page 3:
2025-05-26 18:31:01,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:31:01,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:02,192 - INFO - Response - Page 3:
2025-05-26 18:31:02,192 - INFO - 第 3 页获取到 100 条记录
2025-05-26 18:31:02,395 - INFO - Request Parameters - Page 4:
2025-05-26 18:31:02,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:31:02,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:03,176 - INFO - Response - Page 4:
2025-05-26 18:31:03,176 - INFO - 第 4 页获取到 100 条记录
2025-05-26 18:31:03,379 - INFO - Request Parameters - Page 5:
2025-05-26 18:31:03,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:31:03,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:04,192 - INFO - Response - Page 5:
2025-05-26 18:31:04,192 - INFO - 第 5 页获取到 100 条记录
2025-05-26 18:31:04,395 - INFO - Request Parameters - Page 6:
2025-05-26 18:31:04,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:31:04,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:05,114 - INFO - Response - Page 6:
2025-05-26 18:31:05,114 - INFO - 第 6 页获取到 65 条记录
2025-05-26 18:31:05,317 - INFO - 查询完成，共获取到 565 条记录
2025-05-26 18:31:05,317 - INFO - 获取到 565 条表单数据
2025-05-26 18:31:05,317 - INFO - 当前日期 2025-05-25 有 196 条MySQL数据需要处理
2025-05-26 18:31:05,317 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 18:31:05,317 - INFO - 开始处理日期: 2025-05-26
2025-05-26 18:31:05,317 - INFO - Request Parameters - Page 1:
2025-05-26 18:31:05,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:31:05,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:31:05,832 - INFO - Response - Page 1:
2025-05-26 18:31:05,832 - INFO - 第 1 页获取到 1 条记录
2025-05-26 18:31:06,035 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 18:31:06,035 - INFO - 获取到 1 条表单数据
2025-05-26 18:31:06,035 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 18:31:06,035 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 18:31:06,035 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 2 条
2025-05-26 18:32:06,051 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 18:32:06,051 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 18:32:06,051 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 18:32:06,129 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 566 条记录
2025-05-26 18:32:06,129 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 18:32:06,129 - INFO - 开始处理日期: 2025-05-25
2025-05-26 18:32:06,129 - INFO - Request Parameters - Page 1:
2025-05-26 18:32:06,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:06,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:07,019 - INFO - Response - Page 1:
2025-05-26 18:32:07,019 - INFO - 第 1 页获取到 100 条记录
2025-05-26 18:32:07,223 - INFO - Request Parameters - Page 2:
2025-05-26 18:32:07,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:07,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:08,098 - INFO - Response - Page 2:
2025-05-26 18:32:08,098 - INFO - 第 2 页获取到 100 条记录
2025-05-26 18:32:08,301 - INFO - Request Parameters - Page 3:
2025-05-26 18:32:08,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:08,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:09,176 - INFO - Response - Page 3:
2025-05-26 18:32:09,176 - INFO - 第 3 页获取到 100 条记录
2025-05-26 18:32:09,379 - INFO - Request Parameters - Page 4:
2025-05-26 18:32:09,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:09,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:10,207 - INFO - Response - Page 4:
2025-05-26 18:32:10,207 - INFO - 第 4 页获取到 100 条记录
2025-05-26 18:32:10,410 - INFO - Request Parameters - Page 5:
2025-05-26 18:32:10,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:10,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:11,223 - INFO - Response - Page 5:
2025-05-26 18:32:11,223 - INFO - 第 5 页获取到 100 条记录
2025-05-26 18:32:11,426 - INFO - Request Parameters - Page 6:
2025-05-26 18:32:11,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:11,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:12,129 - INFO - Response - Page 6:
2025-05-26 18:32:12,129 - INFO - 第 6 页获取到 65 条记录
2025-05-26 18:32:12,332 - INFO - 查询完成，共获取到 565 条记录
2025-05-26 18:32:12,332 - INFO - 获取到 565 条表单数据
2025-05-26 18:32:12,332 - INFO - 当前日期 2025-05-25 有 565 条MySQL数据需要处理
2025-05-26 18:32:12,348 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 18:32:12,348 - INFO - 开始处理日期: 2025-05-26
2025-05-26 18:32:12,348 - INFO - Request Parameters - Page 1:
2025-05-26 18:32:12,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 18:32:12,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 18:32:12,894 - INFO - Response - Page 1:
2025-05-26 18:32:12,894 - INFO - 第 1 页获取到 1 条记录
2025-05-26 18:32:13,098 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 18:32:13,098 - INFO - 获取到 1 条表单数据
2025-05-26 18:32:13,098 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 18:32:13,098 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 18:32:13,098 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 18:32:13,098 - INFO - 同步完成
2025-05-26 19:30:34,297 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 19:30:34,297 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 19:30:34,297 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 19:30:34,360 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 207 条记录
2025-05-26 19:30:34,360 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 19:30:34,375 - INFO - 开始处理日期: 2025-05-21
2025-05-26 19:30:34,375 - INFO - Request Parameters - Page 1:
2025-05-26 19:30:34,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:30:34,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:30:42,470 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C87584EB-16BF-7C79-A0FA-67B566576F5A Response: {'code': 'ServiceUnavailable', 'requestid': 'C87584EB-16BF-7C79-A0FA-67B566576F5A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C87584EB-16BF-7C79-A0FA-67B566576F5A)
2025-05-26 19:30:42,470 - INFO - 开始处理日期: 2025-05-23
2025-05-26 19:30:42,470 - INFO - Request Parameters - Page 1:
2025-05-26 19:30:42,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:30:42,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:30:50,597 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 193D6177-BBCB-7E68-BD02-88DF8176ABB1 Response: {'code': 'ServiceUnavailable', 'requestid': '193D6177-BBCB-7E68-BD02-88DF8176ABB1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 193D6177-BBCB-7E68-BD02-88DF8176ABB1)
2025-05-26 19:30:50,597 - INFO - 开始处理日期: 2025-05-24
2025-05-26 19:30:50,597 - INFO - Request Parameters - Page 1:
2025-05-26 19:30:50,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:30:50,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:30:58,988 - INFO - Response - Page 1:
2025-05-26 19:30:58,988 - INFO - 第 1 页获取到 100 条记录
2025-05-26 19:30:59,192 - INFO - Request Parameters - Page 2:
2025-05-26 19:30:59,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:30:59,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:00,082 - INFO - Response - Page 2:
2025-05-26 19:31:00,082 - INFO - 第 2 页获取到 100 条记录
2025-05-26 19:31:00,286 - INFO - Request Parameters - Page 3:
2025-05-26 19:31:00,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:00,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:01,129 - INFO - Response - Page 3:
2025-05-26 19:31:01,129 - INFO - 第 3 页获取到 100 条记录
2025-05-26 19:31:01,333 - INFO - Request Parameters - Page 4:
2025-05-26 19:31:01,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:01,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:02,114 - INFO - Response - Page 4:
2025-05-26 19:31:02,114 - INFO - 第 4 页获取到 100 条记录
2025-05-26 19:31:02,317 - INFO - Request Parameters - Page 5:
2025-05-26 19:31:02,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:02,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:03,098 - INFO - Response - Page 5:
2025-05-26 19:31:03,098 - INFO - 第 5 页获取到 100 条记录
2025-05-26 19:31:03,302 - INFO - Request Parameters - Page 6:
2025-05-26 19:31:03,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:03,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:03,974 - INFO - Response - Page 6:
2025-05-26 19:31:03,974 - INFO - 第 6 页获取到 34 条记录
2025-05-26 19:31:04,177 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 19:31:04,177 - INFO - 获取到 534 条表单数据
2025-05-26 19:31:04,177 - INFO - 当前日期 2025-05-24 有 5 条MySQL数据需要处理
2025-05-26 19:31:04,177 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 19:31:04,177 - INFO - 开始处理日期: 2025-05-25
2025-05-26 19:31:04,177 - INFO - Request Parameters - Page 1:
2025-05-26 19:31:04,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:04,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:04,974 - INFO - Response - Page 1:
2025-05-26 19:31:04,974 - INFO - 第 1 页获取到 100 条记录
2025-05-26 19:31:05,177 - INFO - Request Parameters - Page 2:
2025-05-26 19:31:05,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:05,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:06,036 - INFO - Response - Page 2:
2025-05-26 19:31:06,036 - INFO - 第 2 页获取到 100 条记录
2025-05-26 19:31:06,240 - INFO - Request Parameters - Page 3:
2025-05-26 19:31:06,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:06,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:07,068 - INFO - Response - Page 3:
2025-05-26 19:31:07,068 - INFO - 第 3 页获取到 100 条记录
2025-05-26 19:31:07,271 - INFO - Request Parameters - Page 4:
2025-05-26 19:31:07,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:07,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:08,193 - INFO - Response - Page 4:
2025-05-26 19:31:08,193 - INFO - 第 4 页获取到 100 条记录
2025-05-26 19:31:08,396 - INFO - Request Parameters - Page 5:
2025-05-26 19:31:08,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:08,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:09,224 - INFO - Response - Page 5:
2025-05-26 19:31:09,224 - INFO - 第 5 页获取到 100 条记录
2025-05-26 19:31:09,428 - INFO - Request Parameters - Page 6:
2025-05-26 19:31:09,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:09,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:10,209 - INFO - Response - Page 6:
2025-05-26 19:31:10,209 - INFO - 第 6 页获取到 65 条记录
2025-05-26 19:31:10,412 - INFO - 查询完成，共获取到 565 条记录
2025-05-26 19:31:10,412 - INFO - 获取到 565 条表单数据
2025-05-26 19:31:10,412 - INFO - 当前日期 2025-05-25 有 196 条MySQL数据需要处理
2025-05-26 19:31:10,412 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 19:31:10,412 - INFO - 开始处理日期: 2025-05-26
2025-05-26 19:31:10,412 - INFO - Request Parameters - Page 1:
2025-05-26 19:31:10,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:31:10,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:31:10,912 - INFO - Response - Page 1:
2025-05-26 19:31:10,912 - INFO - 第 1 页获取到 1 条记录
2025-05-26 19:31:11,115 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 19:31:11,115 - INFO - 获取到 1 条表单数据
2025-05-26 19:31:11,115 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 19:31:11,115 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 19:31:11,115 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-26 19:32:11,140 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 19:32:11,140 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 19:32:11,140 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 19:32:11,219 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 566 条记录
2025-05-26 19:32:11,219 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 19:32:11,219 - INFO - 开始处理日期: 2025-05-25
2025-05-26 19:32:11,219 - INFO - Request Parameters - Page 1:
2025-05-26 19:32:11,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:11,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:12,047 - INFO - Response - Page 1:
2025-05-26 19:32:12,047 - INFO - 第 1 页获取到 100 条记录
2025-05-26 19:32:12,250 - INFO - Request Parameters - Page 2:
2025-05-26 19:32:12,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:12,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:13,047 - INFO - Response - Page 2:
2025-05-26 19:32:13,047 - INFO - 第 2 页获取到 100 条记录
2025-05-26 19:32:13,250 - INFO - Request Parameters - Page 3:
2025-05-26 19:32:13,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:13,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:14,078 - INFO - Response - Page 3:
2025-05-26 19:32:14,078 - INFO - 第 3 页获取到 100 条记录
2025-05-26 19:32:14,282 - INFO - Request Parameters - Page 4:
2025-05-26 19:32:14,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:14,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:15,079 - INFO - Response - Page 4:
2025-05-26 19:32:15,079 - INFO - 第 4 页获取到 100 条记录
2025-05-26 19:32:15,282 - INFO - Request Parameters - Page 5:
2025-05-26 19:32:15,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:15,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:16,079 - INFO - Response - Page 5:
2025-05-26 19:32:16,079 - INFO - 第 5 页获取到 100 条记录
2025-05-26 19:32:16,282 - INFO - Request Parameters - Page 6:
2025-05-26 19:32:16,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:16,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:17,032 - INFO - Response - Page 6:
2025-05-26 19:32:17,032 - INFO - 第 6 页获取到 65 条记录
2025-05-26 19:32:17,235 - INFO - 查询完成，共获取到 565 条记录
2025-05-26 19:32:17,235 - INFO - 获取到 565 条表单数据
2025-05-26 19:32:17,235 - INFO - 当前日期 2025-05-25 有 565 条MySQL数据需要处理
2025-05-26 19:32:17,251 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 19:32:17,251 - INFO - 开始处理日期: 2025-05-26
2025-05-26 19:32:17,251 - INFO - Request Parameters - Page 1:
2025-05-26 19:32:17,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 19:32:17,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 19:32:17,751 - INFO - Response - Page 1:
2025-05-26 19:32:17,751 - INFO - 第 1 页获取到 1 条记录
2025-05-26 19:32:17,954 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 19:32:17,954 - INFO - 获取到 1 条表单数据
2025-05-26 19:32:17,954 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 19:32:17,954 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 19:32:17,954 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 19:32:17,954 - INFO - 同步完成
2025-05-26 20:30:34,748 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 20:30:34,748 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 20:30:34,748 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 20:30:34,826 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 208 条记录
2025-05-26 20:30:34,826 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 20:30:34,826 - INFO - 开始处理日期: 2025-05-21
2025-05-26 20:30:34,826 - INFO - Request Parameters - Page 1:
2025-05-26 20:30:34,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:30:34,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:30:42,936 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7425CE7B-3E53-7D2D-8030-ADA5EBF5B3ED Response: {'code': 'ServiceUnavailable', 'requestid': '7425CE7B-3E53-7D2D-8030-ADA5EBF5B3ED', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7425CE7B-3E53-7D2D-8030-ADA5EBF5B3ED)
2025-05-26 20:30:42,936 - INFO - 开始处理日期: 2025-05-23
2025-05-26 20:30:42,936 - INFO - Request Parameters - Page 1:
2025-05-26 20:30:42,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:30:42,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:30:51,063 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CDB36264-1BD5-77D2-BBB7-18A6B388481F Response: {'code': 'ServiceUnavailable', 'requestid': 'CDB36264-1BD5-77D2-BBB7-18A6B388481F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CDB36264-1BD5-77D2-BBB7-18A6B388481F)
2025-05-26 20:30:51,063 - INFO - 开始处理日期: 2025-05-24
2025-05-26 20:30:51,063 - INFO - Request Parameters - Page 1:
2025-05-26 20:30:51,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:30:51,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:30:58,970 - INFO - Response - Page 1:
2025-05-26 20:30:58,970 - INFO - 第 1 页获取到 100 条记录
2025-05-26 20:30:59,173 - INFO - Request Parameters - Page 2:
2025-05-26 20:30:59,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:30:59,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:00,017 - INFO - Response - Page 2:
2025-05-26 20:31:00,033 - INFO - 第 2 页获取到 100 条记录
2025-05-26 20:31:00,236 - INFO - Request Parameters - Page 3:
2025-05-26 20:31:00,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:00,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:01,096 - INFO - Response - Page 3:
2025-05-26 20:31:01,096 - INFO - 第 3 页获取到 100 条记录
2025-05-26 20:31:01,299 - INFO - Request Parameters - Page 4:
2025-05-26 20:31:01,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:01,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:02,111 - INFO - Response - Page 4:
2025-05-26 20:31:02,111 - INFO - 第 4 页获取到 100 条记录
2025-05-26 20:31:02,315 - INFO - Request Parameters - Page 5:
2025-05-26 20:31:02,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:02,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:03,221 - INFO - Response - Page 5:
2025-05-26 20:31:03,221 - INFO - 第 5 页获取到 100 条记录
2025-05-26 20:31:03,424 - INFO - Request Parameters - Page 6:
2025-05-26 20:31:03,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:03,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:04,096 - INFO - Response - Page 6:
2025-05-26 20:31:04,096 - INFO - 第 6 页获取到 34 条记录
2025-05-26 20:31:04,299 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 20:31:04,299 - INFO - 获取到 534 条表单数据
2025-05-26 20:31:04,299 - INFO - 当前日期 2025-05-24 有 5 条MySQL数据需要处理
2025-05-26 20:31:04,299 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 20:31:04,299 - INFO - 开始处理日期: 2025-05-25
2025-05-26 20:31:04,299 - INFO - Request Parameters - Page 1:
2025-05-26 20:31:04,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:04,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:05,159 - INFO - Response - Page 1:
2025-05-26 20:31:05,159 - INFO - 第 1 页获取到 100 条记录
2025-05-26 20:31:05,362 - INFO - Request Parameters - Page 2:
2025-05-26 20:31:05,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:05,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:06,206 - INFO - Response - Page 2:
2025-05-26 20:31:06,206 - INFO - 第 2 页获取到 100 条记录
2025-05-26 20:31:06,409 - INFO - Request Parameters - Page 3:
2025-05-26 20:31:06,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:06,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:07,190 - INFO - Response - Page 3:
2025-05-26 20:31:07,190 - INFO - 第 3 页获取到 100 条记录
2025-05-26 20:31:07,394 - INFO - Request Parameters - Page 4:
2025-05-26 20:31:07,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:07,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:08,175 - INFO - Response - Page 4:
2025-05-26 20:31:08,175 - INFO - 第 4 页获取到 100 条记录
2025-05-26 20:31:08,378 - INFO - Request Parameters - Page 5:
2025-05-26 20:31:08,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:08,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:09,206 - INFO - Response - Page 5:
2025-05-26 20:31:09,206 - INFO - 第 5 页获取到 100 条记录
2025-05-26 20:31:09,409 - INFO - Request Parameters - Page 6:
2025-05-26 20:31:09,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:09,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:10,113 - INFO - Response - Page 6:
2025-05-26 20:31:10,113 - INFO - 第 6 页获取到 65 条记录
2025-05-26 20:31:10,316 - INFO - 查询完成，共获取到 565 条记录
2025-05-26 20:31:10,316 - INFO - 获取到 565 条表单数据
2025-05-26 20:31:10,316 - INFO - 当前日期 2025-05-25 有 197 条MySQL数据需要处理
2025-05-26 20:31:10,316 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMPQ
2025-05-26 20:31:10,800 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMPQ
2025-05-26 20:31:10,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 25463.0}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 25463.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-26 20:31:10,800 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMRQ
2025-05-26 20:31:11,253 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMRQ
2025-05-26 20:31:11,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1100.0, 'new_value': 1550.0}, {'field': 'total_amount', 'old_value': 1100.0, 'new_value': 1550.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 140}]
2025-05-26 20:31:11,253 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMSQ
2025-05-26 20:31:11,738 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMSQ
2025-05-26 20:31:11,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22000.0, 'new_value': 18727.81}, {'field': 'total_amount', 'old_value': 22000.0, 'new_value': 18727.81}, {'field': 'order_count', 'old_value': 1, 'new_value': 23}]
2025-05-26 20:31:11,738 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMTQ
2025-05-26 20:31:12,207 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMTQ
2025-05-26 20:31:12,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 500.0, 'new_value': 5038.0}, {'field': 'total_amount', 'old_value': 500.0, 'new_value': 5038.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-26 20:31:12,207 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMUQ
2025-05-26 20:31:12,707 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMUQ
2025-05-26 20:31:12,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 186000.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 186000.0}]
2025-05-26 20:31:12,707 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMVQ
2025-05-26 20:31:13,160 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMVQ
2025-05-26 20:31:13,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 21496.51}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 21496.51}, {'field': 'order_count', 'old_value': 1, 'new_value': 147}]
2025-05-26 20:31:13,160 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMWQ
2025-05-26 20:31:13,644 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMWQ
2025-05-26 20:31:13,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-26 20:31:13,644 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMYQ
2025-05-26 20:31:14,098 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3SCL0H4BMYQ
2025-05-26 20:31:14,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3500.0, 'new_value': 5736.0}, {'field': 'total_amount', 'old_value': 3500.0, 'new_value': 5736.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 199}]
2025-05-26 20:31:14,098 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMZQ
2025-05-26 20:31:14,473 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMZQ
2025-05-26 20:31:14,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2200.0, 'new_value': 8203.08}, {'field': 'total_amount', 'old_value': 2200.0, 'new_value': 8203.08}, {'field': 'order_count', 'old_value': 1, 'new_value': 75}]
2025-05-26 20:31:14,473 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM2R
2025-05-26 20:31:14,926 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM2R
2025-05-26 20:31:14,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3880.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3880.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-26 20:31:14,926 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM3R
2025-05-26 20:31:15,363 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM3R
2025-05-26 20:31:15,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 44607.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 44607.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-26 20:31:15,363 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM4R
2025-05-26 20:31:15,864 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM4R
2025-05-26 20:31:15,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34000.0, 'new_value': 60000.0}, {'field': 'total_amount', 'old_value': 34000.0, 'new_value': 60000.0}]
2025-05-26 20:31:15,864 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM6R
2025-05-26 20:31:16,317 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM6R
2025-05-26 20:31:16,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1300.0, 'new_value': 1876.0}, {'field': 'total_amount', 'old_value': 1300.0, 'new_value': 1876.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 155}]
2025-05-26 20:31:16,317 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM7R
2025-05-26 20:31:16,786 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM7R
2025-05-26 20:31:16,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 5723.98}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 5723.98}, {'field': 'order_count', 'old_value': 1, 'new_value': 336}]
2025-05-26 20:31:16,786 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM9R
2025-05-26 20:31:17,254 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BM9R
2025-05-26 20:31:17,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3700.0, 'new_value': 9819.15}, {'field': 'total_amount', 'old_value': 3700.0, 'new_value': 9819.15}, {'field': 'order_count', 'old_value': 1, 'new_value': 396}]
2025-05-26 20:31:17,254 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMAR
2025-05-26 20:31:17,739 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMAR
2025-05-26 20:31:17,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-26 20:31:17,739 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMBR
2025-05-26 20:31:18,176 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMBR
2025-05-26 20:31:18,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6500.0, 'new_value': 6762.53}, {'field': 'total_amount', 'old_value': 6500.0, 'new_value': 6762.53}, {'field': 'order_count', 'old_value': 1, 'new_value': 582}]
2025-05-26 20:31:18,176 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMCR
2025-05-26 20:31:18,630 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMCR
2025-05-26 20:31:18,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3240.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3240.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-26 20:31:18,630 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMDR
2025-05-26 20:31:19,083 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMDR
2025-05-26 20:31:19,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 90000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 90000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-26 20:31:19,083 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMER
2025-05-26 20:31:19,536 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMER
2025-05-26 20:31:19,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6598.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6598.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-26 20:31:19,536 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMFR
2025-05-26 20:31:20,036 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMFR
2025-05-26 20:31:20,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6989.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6989.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 10}]
2025-05-26 20:31:20,036 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMJR
2025-05-26 20:31:20,458 - INFO - 更新表单数据成功: FINST-AEF66BC15KPV172WA9GYPA0VZ8XB3TCL0H4BMJR
2025-05-26 20:31:20,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1155.1}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1155.1}, {'field': 'order_count', 'old_value': 0, 'new_value': 111}]
2025-05-26 20:31:20,458 - INFO - 开始批量插入 1 条新记录
2025-05-26 20:31:20,614 - INFO - 批量插入响应状态码: 200
2025-05-26 20:31:20,614 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 12:31:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0E49F8CC-994D-7F08-B53E-79A5A9A3C851', 'x-acs-trace-id': '8cd3433d10d20675b9068ad2d00995d2', 'etag': '5qZc9qRO6Pz1IigGUSUjyVg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 20:31:20,630 - INFO - 批量插入响应体: {'result': ['FINST-MLF662B1E6RV38NDEJIK19YJ76YN2J8LG25BMS']}
2025-05-26 20:31:20,630 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-26 20:31:20,630 - INFO - 成功插入的数据ID: ['FINST-MLF662B1E6RV38NDEJIK19YJ76YN2J8LG25BMS']
2025-05-26 20:31:25,646 - INFO - 批量插入完成，共 1 条记录
2025-05-26 20:31:25,646 - INFO - 日期 2025-05-25 处理完成 - 更新: 22 条，插入: 1 条，错误: 0 条
2025-05-26 20:31:25,646 - INFO - 开始处理日期: 2025-05-26
2025-05-26 20:31:25,646 - INFO - Request Parameters - Page 1:
2025-05-26 20:31:25,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:31:25,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:31:26,162 - INFO - Response - Page 1:
2025-05-26 20:31:26,162 - INFO - 第 1 页获取到 1 条记录
2025-05-26 20:31:26,365 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 20:31:26,365 - INFO - 获取到 1 条表单数据
2025-05-26 20:31:26,365 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 20:31:26,365 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 20:31:26,365 - INFO - 数据同步完成！更新: 22 条，插入: 1 条，错误: 2 条
2025-05-26 20:32:26,391 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 20:32:26,391 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 20:32:26,391 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 20:32:26,469 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 567 条记录
2025-05-26 20:32:26,469 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 20:32:26,469 - INFO - 开始处理日期: 2025-05-25
2025-05-26 20:32:26,469 - INFO - Request Parameters - Page 1:
2025-05-26 20:32:26,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:26,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:27,313 - INFO - Response - Page 1:
2025-05-26 20:32:27,313 - INFO - 第 1 页获取到 100 条记录
2025-05-26 20:32:27,516 - INFO - Request Parameters - Page 2:
2025-05-26 20:32:27,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:27,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:28,266 - INFO - Response - Page 2:
2025-05-26 20:32:28,266 - INFO - 第 2 页获取到 100 条记录
2025-05-26 20:32:28,469 - INFO - Request Parameters - Page 3:
2025-05-26 20:32:28,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:28,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:29,313 - INFO - Response - Page 3:
2025-05-26 20:32:29,313 - INFO - 第 3 页获取到 100 条记录
2025-05-26 20:32:29,516 - INFO - Request Parameters - Page 4:
2025-05-26 20:32:29,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:29,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:30,360 - INFO - Response - Page 4:
2025-05-26 20:32:30,360 - INFO - 第 4 页获取到 100 条记录
2025-05-26 20:32:30,563 - INFO - Request Parameters - Page 5:
2025-05-26 20:32:30,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:30,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:31,376 - INFO - Response - Page 5:
2025-05-26 20:32:31,376 - INFO - 第 5 页获取到 100 条记录
2025-05-26 20:32:31,579 - INFO - Request Parameters - Page 6:
2025-05-26 20:32:31,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:31,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:32,313 - INFO - Response - Page 6:
2025-05-26 20:32:32,313 - INFO - 第 6 页获取到 66 条记录
2025-05-26 20:32:32,516 - INFO - 查询完成，共获取到 566 条记录
2025-05-26 20:32:32,516 - INFO - 获取到 566 条表单数据
2025-05-26 20:32:32,516 - INFO - 当前日期 2025-05-25 有 566 条MySQL数据需要处理
2025-05-26 20:32:32,532 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 20:32:32,532 - INFO - 开始处理日期: 2025-05-26
2025-05-26 20:32:32,532 - INFO - Request Parameters - Page 1:
2025-05-26 20:32:32,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 20:32:32,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 20:32:33,063 - INFO - Response - Page 1:
2025-05-26 20:32:33,063 - INFO - 第 1 页获取到 1 条记录
2025-05-26 20:32:33,267 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 20:32:33,267 - INFO - 获取到 1 条表单数据
2025-05-26 20:32:33,267 - INFO - 当前日期 2025-05-26 有 1 条MySQL数据需要处理
2025-05-26 20:32:33,267 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 20:32:33,267 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 20:32:33,267 - INFO - 同步完成
2025-05-26 21:30:35,370 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 21:30:35,370 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 21:30:35,370 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 21:30:35,449 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 217 条记录
2025-05-26 21:30:35,449 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 21:30:35,449 - INFO - 开始处理日期: 2025-05-21
2025-05-26 21:30:35,449 - INFO - Request Parameters - Page 1:
2025-05-26 21:30:35,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:30:35,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:30:43,575 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F0AD6AE0-17DD-741B-B7E9-666572607DF7 Response: {'code': 'ServiceUnavailable', 'requestid': 'F0AD6AE0-17DD-741B-B7E9-666572607DF7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F0AD6AE0-17DD-741B-B7E9-666572607DF7)
2025-05-26 21:30:43,575 - INFO - 开始处理日期: 2025-05-23
2025-05-26 21:30:43,575 - INFO - Request Parameters - Page 1:
2025-05-26 21:30:43,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:30:43,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:30:51,701 - INFO - Response - Page 1:
2025-05-26 21:30:51,701 - INFO - 第 1 页获取到 100 条记录
2025-05-26 21:30:51,904 - INFO - Request Parameters - Page 2:
2025-05-26 21:30:51,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:30:51,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:30:52,764 - INFO - Response - Page 2:
2025-05-26 21:30:52,764 - INFO - 第 2 页获取到 100 条记录
2025-05-26 21:30:52,967 - INFO - Request Parameters - Page 3:
2025-05-26 21:30:52,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:30:52,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:30:53,764 - INFO - Response - Page 3:
2025-05-26 21:30:53,764 - INFO - 第 3 页获取到 100 条记录
2025-05-26 21:30:53,967 - INFO - Request Parameters - Page 4:
2025-05-26 21:30:53,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:30:53,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:30:54,749 - INFO - Response - Page 4:
2025-05-26 21:30:54,749 - INFO - 第 4 页获取到 100 条记录
2025-05-26 21:30:54,952 - INFO - Request Parameters - Page 5:
2025-05-26 21:30:54,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:30:54,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:03,047 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D9C83CBE-8F90-79B0-A4B7-3C54FB842FF7 Response: {'code': 'ServiceUnavailable', 'requestid': 'D9C83CBE-8F90-79B0-A4B7-3C54FB842FF7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D9C83CBE-8F90-79B0-A4B7-3C54FB842FF7)
2025-05-26 21:31:03,047 - INFO - 开始处理日期: 2025-05-24
2025-05-26 21:31:03,047 - INFO - Request Parameters - Page 1:
2025-05-26 21:31:03,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:03,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:03,906 - INFO - Response - Page 1:
2025-05-26 21:31:03,906 - INFO - 第 1 页获取到 100 条记录
2025-05-26 21:31:04,109 - INFO - Request Parameters - Page 2:
2025-05-26 21:31:04,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:04,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:05,594 - INFO - Response - Page 2:
2025-05-26 21:31:05,594 - INFO - 第 2 页获取到 100 条记录
2025-05-26 21:31:05,797 - INFO - Request Parameters - Page 3:
2025-05-26 21:31:05,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:05,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:06,672 - INFO - Response - Page 3:
2025-05-26 21:31:06,672 - INFO - 第 3 页获取到 100 条记录
2025-05-26 21:31:06,876 - INFO - Request Parameters - Page 4:
2025-05-26 21:31:06,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:06,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:07,719 - INFO - Response - Page 4:
2025-05-26 21:31:07,719 - INFO - 第 4 页获取到 100 条记录
2025-05-26 21:31:07,923 - INFO - Request Parameters - Page 5:
2025-05-26 21:31:07,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:07,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:08,766 - INFO - Response - Page 5:
2025-05-26 21:31:08,766 - INFO - 第 5 页获取到 100 条记录
2025-05-26 21:31:08,970 - INFO - Request Parameters - Page 6:
2025-05-26 21:31:08,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:08,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:09,720 - INFO - Response - Page 6:
2025-05-26 21:31:09,720 - INFO - 第 6 页获取到 34 条记录
2025-05-26 21:31:09,923 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 21:31:09,923 - INFO - 获取到 534 条表单数据
2025-05-26 21:31:09,923 - INFO - 当前日期 2025-05-24 有 5 条MySQL数据需要处理
2025-05-26 21:31:09,923 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 21:31:09,923 - INFO - 开始处理日期: 2025-05-25
2025-05-26 21:31:09,923 - INFO - Request Parameters - Page 1:
2025-05-26 21:31:09,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:09,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:10,720 - INFO - Response - Page 1:
2025-05-26 21:31:10,720 - INFO - 第 1 页获取到 100 条记录
2025-05-26 21:31:10,923 - INFO - Request Parameters - Page 2:
2025-05-26 21:31:10,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:10,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:11,751 - INFO - Response - Page 2:
2025-05-26 21:31:11,751 - INFO - 第 2 页获取到 100 条记录
2025-05-26 21:31:11,954 - INFO - Request Parameters - Page 3:
2025-05-26 21:31:11,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:11,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:12,705 - INFO - Response - Page 3:
2025-05-26 21:31:12,705 - INFO - 第 3 页获取到 100 条记录
2025-05-26 21:31:12,908 - INFO - Request Parameters - Page 4:
2025-05-26 21:31:12,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:12,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:13,674 - INFO - Response - Page 4:
2025-05-26 21:31:13,674 - INFO - 第 4 页获取到 100 条记录
2025-05-26 21:31:13,877 - INFO - Request Parameters - Page 5:
2025-05-26 21:31:13,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:13,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:14,705 - INFO - Response - Page 5:
2025-05-26 21:31:14,705 - INFO - 第 5 页获取到 100 条记录
2025-05-26 21:31:14,908 - INFO - Request Parameters - Page 6:
2025-05-26 21:31:14,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:14,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:15,721 - INFO - Response - Page 6:
2025-05-26 21:31:15,721 - INFO - 第 6 页获取到 66 条记录
2025-05-26 21:31:15,924 - INFO - 查询完成，共获取到 566 条记录
2025-05-26 21:31:15,924 - INFO - 获取到 566 条表单数据
2025-05-26 21:31:15,924 - INFO - 当前日期 2025-05-25 有 200 条MySQL数据需要处理
2025-05-26 21:31:15,924 - INFO - 开始批量插入 3 条新记录
2025-05-26 21:31:16,096 - INFO - 批量插入响应状态码: 200
2025-05-26 21:31:16,096 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 13:31:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1F71035A-9E54-7890-A0C6-2544B6998085', 'x-acs-trace-id': 'fa290a89ccabadbd6bea02866c628cbc', 'etag': '1TImHbHWbsYVxJyn7XBNJAw3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 21:31:16,096 - INFO - 批量插入响应体: {'result': ['FINST-DUF66091TERVZUPE7L3ON9PFDKCB3U2NL45BMF', 'FINST-DUF66091TERVZUPE7L3ON9PFDKCB3U2NL45BMG', 'FINST-DUF66091TERVZUPE7L3ON9PFDKCB3U2NL45BMH']}
2025-05-26 21:31:16,096 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-26 21:31:16,096 - INFO - 成功插入的数据ID: ['FINST-DUF66091TERVZUPE7L3ON9PFDKCB3U2NL45BMF', 'FINST-DUF66091TERVZUPE7L3ON9PFDKCB3U2NL45BMG', 'FINST-DUF66091TERVZUPE7L3ON9PFDKCB3U2NL45BMH']
2025-05-26 21:31:21,112 - INFO - 批量插入完成，共 3 条记录
2025-05-26 21:31:21,112 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-26 21:31:21,112 - INFO - 开始处理日期: 2025-05-26
2025-05-26 21:31:21,112 - INFO - Request Parameters - Page 1:
2025-05-26 21:31:21,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:31:21,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:31:21,644 - INFO - Response - Page 1:
2025-05-26 21:31:21,644 - INFO - 第 1 页获取到 1 条记录
2025-05-26 21:31:21,847 - INFO - 查询完成，共获取到 1 条记录
2025-05-26 21:31:21,847 - INFO - 获取到 1 条表单数据
2025-05-26 21:31:21,847 - INFO - 当前日期 2025-05-26 有 7 条MySQL数据需要处理
2025-05-26 21:31:21,847 - INFO - 开始批量插入 6 条新记录
2025-05-26 21:31:22,003 - INFO - 批量插入响应状态码: 200
2025-05-26 21:31:22,003 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 13:31:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '294', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5ADAFB03-B669-7949-AFEE-CF85523F5FB1', 'x-acs-trace-id': '18b5d7a16ea2d1c9d5ae7440ef26137c', 'etag': '2k1K0HTuudeFBFqzC/380OA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 21:31:22,003 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM6', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM7', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM8', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM9', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BMA', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BMB']}
2025-05-26 21:31:22,003 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-05-26 21:31:22,003 - INFO - 成功插入的数据ID: ['FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM6', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM7', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM8', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BM9', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BMA', 'FINST-6PF66691AHRVYDO365HAY5G0W46W2PMRL45BMB']
2025-05-26 21:31:27,019 - INFO - 批量插入完成，共 6 条记录
2025-05-26 21:31:27,019 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-05-26 21:31:27,019 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 2 条
2025-05-26 21:32:27,045 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 21:32:27,045 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 21:32:27,045 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 21:32:27,123 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 589 条记录
2025-05-26 21:32:27,123 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 21:32:27,123 - INFO - 开始处理日期: 2025-05-25
2025-05-26 21:32:27,123 - INFO - Request Parameters - Page 1:
2025-05-26 21:32:27,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:27,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:27,935 - INFO - Response - Page 1:
2025-05-26 21:32:27,935 - INFO - 第 1 页获取到 100 条记录
2025-05-26 21:32:28,139 - INFO - Request Parameters - Page 2:
2025-05-26 21:32:28,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:28,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:28,967 - INFO - Response - Page 2:
2025-05-26 21:32:28,967 - INFO - 第 2 页获取到 100 条记录
2025-05-26 21:32:29,170 - INFO - Request Parameters - Page 3:
2025-05-26 21:32:29,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:29,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:30,014 - INFO - Response - Page 3:
2025-05-26 21:32:30,014 - INFO - 第 3 页获取到 100 条记录
2025-05-26 21:32:30,217 - INFO - Request Parameters - Page 4:
2025-05-26 21:32:30,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:30,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:31,014 - INFO - Response - Page 4:
2025-05-26 21:32:31,014 - INFO - 第 4 页获取到 100 条记录
2025-05-26 21:32:31,217 - INFO - Request Parameters - Page 5:
2025-05-26 21:32:31,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:31,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:31,983 - INFO - Response - Page 5:
2025-05-26 21:32:31,983 - INFO - 第 5 页获取到 100 条记录
2025-05-26 21:32:32,186 - INFO - Request Parameters - Page 6:
2025-05-26 21:32:32,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:32,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:32,983 - INFO - Response - Page 6:
2025-05-26 21:32:32,983 - INFO - 第 6 页获取到 69 条记录
2025-05-26 21:32:33,186 - INFO - 查询完成，共获取到 569 条记录
2025-05-26 21:32:33,186 - INFO - 获取到 569 条表单数据
2025-05-26 21:32:33,186 - INFO - 当前日期 2025-05-25 有 582 条MySQL数据需要处理
2025-05-26 21:32:33,202 - INFO - 开始批量插入 13 条新记录
2025-05-26 21:32:33,358 - INFO - 批量插入响应状态码: 200
2025-05-26 21:32:33,358 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 13:32:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '623', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E7BC35C0-C0B0-7FED-A9D5-4AB64250DA69', 'x-acs-trace-id': 'd3ea7b731200c5a63a20f0e0b5b16ab8', 'etag': '6ktELFrvjcJcGDBMRsX/ENA3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 21:32:33,358 - INFO - 批量插入响应体: {'result': ['FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BM9', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMA', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMB', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMC', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMD', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BME', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMF', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMG', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMH', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMI', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMJ', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMK', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BML']}
2025-05-26 21:32:33,358 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-05-26 21:32:33,358 - INFO - 成功插入的数据ID: ['FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BM9', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMA', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMB', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMC', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMD', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BME', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMF', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMG', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMH', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMI', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMJ', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BMK', 'FINST-68E66TC1YGRVU3W7ATA5X9SGA2ZU2QOAN45BML']
2025-05-26 21:32:38,375 - INFO - 批量插入完成，共 13 条记录
2025-05-26 21:32:38,375 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-05-26 21:32:38,375 - INFO - 开始处理日期: 2025-05-26
2025-05-26 21:32:38,375 - INFO - Request Parameters - Page 1:
2025-05-26 21:32:38,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 21:32:38,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 21:32:38,953 - INFO - Response - Page 1:
2025-05-26 21:32:38,953 - INFO - 第 1 页获取到 7 条记录
2025-05-26 21:32:39,156 - INFO - 查询完成，共获取到 7 条记录
2025-05-26 21:32:39,156 - INFO - 获取到 7 条表单数据
2025-05-26 21:32:39,156 - INFO - 当前日期 2025-05-26 有 7 条MySQL数据需要处理
2025-05-26 21:32:39,156 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 21:32:39,156 - INFO - 数据同步完成！更新: 0 条，插入: 13 条，错误: 0 条
2025-05-26 21:32:39,156 - INFO - 同步完成
2025-05-26 22:30:33,680 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 22:30:33,680 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 22:30:33,680 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 22:30:33,759 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 274 条记录
2025-05-26 22:30:33,759 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 22:30:33,759 - INFO - 开始处理日期: 2025-05-21
2025-05-26 22:30:33,759 - INFO - Request Parameters - Page 1:
2025-05-26 22:30:33,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:33,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:30:41,885 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 96370E33-536C-7156-89A3-AA84EC8235FC Response: {'code': 'ServiceUnavailable', 'requestid': '96370E33-536C-7156-89A3-AA84EC8235FC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 96370E33-536C-7156-89A3-AA84EC8235FC)
2025-05-26 22:30:41,885 - INFO - 开始处理日期: 2025-05-23
2025-05-26 22:30:41,885 - INFO - Request Parameters - Page 1:
2025-05-26 22:30:41,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:41,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:30:44,542 - INFO - Response - Page 1:
2025-05-26 22:30:44,542 - INFO - 第 1 页获取到 100 条记录
2025-05-26 22:30:44,745 - INFO - Request Parameters - Page 2:
2025-05-26 22:30:44,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:44,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:30:45,526 - INFO - Response - Page 2:
2025-05-26 22:30:45,526 - INFO - 第 2 页获取到 100 条记录
2025-05-26 22:30:45,729 - INFO - Request Parameters - Page 3:
2025-05-26 22:30:45,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:45,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:30:53,840 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3EF7E1A9-0C62-76D6-AC13-8883DA4A3783 Response: {'code': 'ServiceUnavailable', 'requestid': '3EF7E1A9-0C62-76D6-AC13-8883DA4A3783', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3EF7E1A9-0C62-76D6-AC13-8883DA4A3783)
2025-05-26 22:30:53,840 - INFO - 开始处理日期: 2025-05-24
2025-05-26 22:30:53,840 - INFO - Request Parameters - Page 1:
2025-05-26 22:30:53,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:53,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:30:54,653 - INFO - Response - Page 1:
2025-05-26 22:30:54,653 - INFO - 第 1 页获取到 100 条记录
2025-05-26 22:30:54,856 - INFO - Request Parameters - Page 2:
2025-05-26 22:30:54,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:54,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:30:59,669 - INFO - Response - Page 2:
2025-05-26 22:30:59,669 - INFO - 第 2 页获取到 100 条记录
2025-05-26 22:30:59,872 - INFO - Request Parameters - Page 3:
2025-05-26 22:30:59,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:30:59,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:00,685 - INFO - Response - Page 3:
2025-05-26 22:31:00,685 - INFO - 第 3 页获取到 100 条记录
2025-05-26 22:31:00,888 - INFO - Request Parameters - Page 4:
2025-05-26 22:31:00,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:00,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:01,654 - INFO - Response - Page 4:
2025-05-26 22:31:01,654 - INFO - 第 4 页获取到 100 条记录
2025-05-26 22:31:01,857 - INFO - Request Parameters - Page 5:
2025-05-26 22:31:01,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:01,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:02,654 - INFO - Response - Page 5:
2025-05-26 22:31:02,654 - INFO - 第 5 页获取到 100 条记录
2025-05-26 22:31:02,857 - INFO - Request Parameters - Page 6:
2025-05-26 22:31:02,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:02,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:03,498 - INFO - Response - Page 6:
2025-05-26 22:31:03,498 - INFO - 第 6 页获取到 34 条记录
2025-05-26 22:31:03,701 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 22:31:03,701 - INFO - 获取到 534 条表单数据
2025-05-26 22:31:03,701 - INFO - 当前日期 2025-05-24 有 5 条MySQL数据需要处理
2025-05-26 22:31:03,701 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 22:31:03,701 - INFO - 开始处理日期: 2025-05-25
2025-05-26 22:31:03,701 - INFO - Request Parameters - Page 1:
2025-05-26 22:31:03,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:03,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:04,545 - INFO - Response - Page 1:
2025-05-26 22:31:04,545 - INFO - 第 1 页获取到 100 条记录
2025-05-26 22:31:04,748 - INFO - Request Parameters - Page 2:
2025-05-26 22:31:04,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:04,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:05,514 - INFO - Response - Page 2:
2025-05-26 22:31:05,514 - INFO - 第 2 页获取到 100 条记录
2025-05-26 22:31:05,717 - INFO - Request Parameters - Page 3:
2025-05-26 22:31:05,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:05,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:06,451 - INFO - Response - Page 3:
2025-05-26 22:31:06,451 - INFO - 第 3 页获取到 100 条记录
2025-05-26 22:31:06,655 - INFO - Request Parameters - Page 4:
2025-05-26 22:31:06,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:06,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:07,577 - INFO - Response - Page 4:
2025-05-26 22:31:07,577 - INFO - 第 4 页获取到 100 条记录
2025-05-26 22:31:07,780 - INFO - Request Parameters - Page 5:
2025-05-26 22:31:07,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:07,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:08,545 - INFO - Response - Page 5:
2025-05-26 22:31:08,545 - INFO - 第 5 页获取到 100 条记录
2025-05-26 22:31:08,749 - INFO - Request Parameters - Page 6:
2025-05-26 22:31:08,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:08,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:09,577 - INFO - Response - Page 6:
2025-05-26 22:31:09,577 - INFO - 第 6 页获取到 82 条记录
2025-05-26 22:31:09,780 - INFO - 查询完成，共获取到 582 条记录
2025-05-26 22:31:09,780 - INFO - 获取到 582 条表单数据
2025-05-26 22:31:09,780 - INFO - 当前日期 2025-05-25 有 200 条MySQL数据需要处理
2025-05-26 22:31:09,780 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 22:31:09,780 - INFO - 开始处理日期: 2025-05-26
2025-05-26 22:31:09,780 - INFO - Request Parameters - Page 1:
2025-05-26 22:31:09,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:31:09,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:31:10,343 - INFO - Response - Page 1:
2025-05-26 22:31:10,343 - INFO - 第 1 页获取到 7 条记录
2025-05-26 22:31:10,546 - INFO - 查询完成，共获取到 7 条记录
2025-05-26 22:31:10,546 - INFO - 获取到 7 条表单数据
2025-05-26 22:31:10,546 - INFO - 当前日期 2025-05-26 有 64 条MySQL数据需要处理
2025-05-26 22:31:10,546 - INFO - 开始批量插入 57 条新记录
2025-05-26 22:31:10,780 - INFO - 批量插入响应状态码: 200
2025-05-26 22:31:10,780 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 14:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2712', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-65B4-7CAF-8D17-DB6B5A0E00C8', 'x-acs-trace-id': '399ab42b9675e846ed0a88f0d48b188d', 'etag': '2sc9WPieM3tBmmEdGU4OpbA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 22:31:10,780 - INFO - 批量插入响应体: {'result': ['FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM0', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM2', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM3', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM4', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM5', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM6', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM7', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM8', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM9', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMA', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMB', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMC', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMD', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BME', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMF', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMG', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMH', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMI', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMJ', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMK', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BML', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMM', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMN', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMO', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMP', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMQ', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMR', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMS', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMT', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMU', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMV', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMW', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMX', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMY', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMZ', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM01', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM11', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM21', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM31', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM41', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM51', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM61', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM71', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM81', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM91', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMA1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMB1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMC1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMD1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BME1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMF1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMG1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMH1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMI1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMJ1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC32BOQ65BMK1']}
2025-05-26 22:31:10,780 - INFO - 批量插入表单数据成功，批次 1，共 57 条记录
2025-05-26 22:31:10,780 - INFO - 成功插入的数据ID: ['FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM0', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM2', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM3', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM4', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM5', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM6', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM7', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM8', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM9', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMA', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMB', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMC', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMD', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BME', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMF', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMG', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMH', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMI', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMJ', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMK', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BML', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMM', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMN', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMO', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMP', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMQ', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMR', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMS', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMT', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMU', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMV', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMW', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMX', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMY', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMZ', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM01', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM11', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM21', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM31', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM41', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM51', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM61', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM71', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM81', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BM91', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMA1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMB1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMC1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMD1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BME1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMF1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMG1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMH1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMI1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC31BOQ65BMJ1', 'FINST-4OD66CC1DPRV2SGD7UGGS84RQPGC32BOQ65BMK1']
2025-05-26 22:31:15,797 - INFO - 批量插入完成，共 57 条记录
2025-05-26 22:31:15,797 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 57 条，错误: 0 条
2025-05-26 22:31:15,797 - INFO - 数据同步完成！更新: 0 条，插入: 57 条，错误: 2 条
2025-05-26 22:32:15,822 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 22:32:15,822 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 22:32:15,822 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 22:32:15,900 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 646 条记录
2025-05-26 22:32:15,900 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 22:32:15,900 - INFO - 开始处理日期: 2025-05-25
2025-05-26 22:32:15,900 - INFO - Request Parameters - Page 1:
2025-05-26 22:32:15,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:15,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:16,822 - INFO - Response - Page 1:
2025-05-26 22:32:16,822 - INFO - 第 1 页获取到 100 条记录
2025-05-26 22:32:17,025 - INFO - Request Parameters - Page 2:
2025-05-26 22:32:17,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:17,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:17,807 - INFO - Response - Page 2:
2025-05-26 22:32:17,807 - INFO - 第 2 页获取到 100 条记录
2025-05-26 22:32:18,010 - INFO - Request Parameters - Page 3:
2025-05-26 22:32:18,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:18,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:18,822 - INFO - Response - Page 3:
2025-05-26 22:32:18,822 - INFO - 第 3 页获取到 100 条记录
2025-05-26 22:32:19,025 - INFO - Request Parameters - Page 4:
2025-05-26 22:32:19,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:19,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:19,822 - INFO - Response - Page 4:
2025-05-26 22:32:19,822 - INFO - 第 4 页获取到 100 条记录
2025-05-26 22:32:20,026 - INFO - Request Parameters - Page 5:
2025-05-26 22:32:20,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:20,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:20,791 - INFO - Response - Page 5:
2025-05-26 22:32:20,791 - INFO - 第 5 页获取到 100 条记录
2025-05-26 22:32:20,995 - INFO - Request Parameters - Page 6:
2025-05-26 22:32:20,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:20,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:21,713 - INFO - Response - Page 6:
2025-05-26 22:32:21,713 - INFO - 第 6 页获取到 82 条记录
2025-05-26 22:32:21,917 - INFO - 查询完成，共获取到 582 条记录
2025-05-26 22:32:21,917 - INFO - 获取到 582 条表单数据
2025-05-26 22:32:21,917 - INFO - 当前日期 2025-05-25 有 582 条MySQL数据需要处理
2025-05-26 22:32:21,932 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 22:32:21,932 - INFO - 开始处理日期: 2025-05-26
2025-05-26 22:32:21,932 - INFO - Request Parameters - Page 1:
2025-05-26 22:32:21,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 22:32:21,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 22:32:22,635 - INFO - Response - Page 1:
2025-05-26 22:32:22,635 - INFO - 第 1 页获取到 64 条记录
2025-05-26 22:32:22,839 - INFO - 查询完成，共获取到 64 条记录
2025-05-26 22:32:22,839 - INFO - 获取到 64 条表单数据
2025-05-26 22:32:22,839 - INFO - 当前日期 2025-05-26 有 64 条MySQL数据需要处理
2025-05-26 22:32:22,839 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 22:32:22,839 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 22:32:22,839 - INFO - 同步完成
2025-05-26 23:30:33,725 - INFO - 使用默认增量同步（当天更新数据）
2025-05-26 23:30:33,725 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 23:30:33,725 - INFO - 查询参数: ('2025-05-26',)
2025-05-26 23:30:33,803 - INFO - MySQL查询成功，增量数据（日期: 2025-05-26），共获取 295 条记录
2025-05-26 23:30:33,803 - INFO - 获取到 5 个日期需要处理: ['2025-05-21', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26']
2025-05-26 23:30:33,803 - INFO - 开始处理日期: 2025-05-21
2025-05-26 23:30:33,803 - INFO - Request Parameters - Page 1:
2025-05-26 23:30:33,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:33,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:30:41,912 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1AD13DA3-17A4-70CF-9ACA-9ACABEC1462D Response: {'code': 'ServiceUnavailable', 'requestid': '1AD13DA3-17A4-70CF-9ACA-9ACABEC1462D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1AD13DA3-17A4-70CF-9ACA-9ACABEC1462D)
2025-05-26 23:30:41,912 - INFO - 开始处理日期: 2025-05-23
2025-05-26 23:30:41,912 - INFO - Request Parameters - Page 1:
2025-05-26 23:30:41,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:41,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:30:50,021 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DBF7D480-CA06-7857-A6C6-3D62E6269A7A Response: {'code': 'ServiceUnavailable', 'requestid': 'DBF7D480-CA06-7857-A6C6-3D62E6269A7A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DBF7D480-CA06-7857-A6C6-3D62E6269A7A)
2025-05-26 23:30:50,021 - INFO - 开始处理日期: 2025-05-24
2025-05-26 23:30:50,021 - INFO - Request Parameters - Page 1:
2025-05-26 23:30:50,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:50,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:30:50,990 - INFO - Response - Page 1:
2025-05-26 23:30:50,990 - INFO - 第 1 页获取到 100 条记录
2025-05-26 23:30:51,193 - INFO - Request Parameters - Page 2:
2025-05-26 23:30:51,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:51,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:30:57,240 - INFO - Response - Page 2:
2025-05-26 23:30:57,256 - INFO - 第 2 页获取到 100 条记录
2025-05-26 23:30:57,459 - INFO - Request Parameters - Page 3:
2025-05-26 23:30:57,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:57,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:30:58,240 - INFO - Response - Page 3:
2025-05-26 23:30:58,240 - INFO - 第 3 页获取到 100 条记录
2025-05-26 23:30:58,443 - INFO - Request Parameters - Page 4:
2025-05-26 23:30:58,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:58,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:30:59,209 - INFO - Response - Page 4:
2025-05-26 23:30:59,209 - INFO - 第 4 页获取到 100 条记录
2025-05-26 23:30:59,412 - INFO - Request Parameters - Page 5:
2025-05-26 23:30:59,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:30:59,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:00,209 - INFO - Response - Page 5:
2025-05-26 23:31:00,209 - INFO - 第 5 页获取到 100 条记录
2025-05-26 23:31:00,412 - INFO - Request Parameters - Page 6:
2025-05-26 23:31:00,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:00,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:01,037 - INFO - Response - Page 6:
2025-05-26 23:31:01,037 - INFO - 第 6 页获取到 34 条记录
2025-05-26 23:31:01,240 - INFO - 查询完成，共获取到 534 条记录
2025-05-26 23:31:01,240 - INFO - 获取到 534 条表单数据
2025-05-26 23:31:01,240 - INFO - 当前日期 2025-05-24 有 5 条MySQL数据需要处理
2025-05-26 23:31:01,240 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 23:31:01,240 - INFO - 开始处理日期: 2025-05-25
2025-05-26 23:31:01,240 - INFO - Request Parameters - Page 1:
2025-05-26 23:31:01,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:01,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:02,006 - INFO - Response - Page 1:
2025-05-26 23:31:02,006 - INFO - 第 1 页获取到 100 条记录
2025-05-26 23:31:02,209 - INFO - Request Parameters - Page 2:
2025-05-26 23:31:02,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:02,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:02,974 - INFO - Response - Page 2:
2025-05-26 23:31:02,974 - INFO - 第 2 页获取到 100 条记录
2025-05-26 23:31:03,177 - INFO - Request Parameters - Page 3:
2025-05-26 23:31:03,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:03,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:03,943 - INFO - Response - Page 3:
2025-05-26 23:31:03,943 - INFO - 第 3 页获取到 100 条记录
2025-05-26 23:31:04,146 - INFO - Request Parameters - Page 4:
2025-05-26 23:31:04,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:04,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:04,912 - INFO - Response - Page 4:
2025-05-26 23:31:04,912 - INFO - 第 4 页获取到 100 条记录
2025-05-26 23:31:05,115 - INFO - Request Parameters - Page 5:
2025-05-26 23:31:05,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:05,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:05,927 - INFO - Response - Page 5:
2025-05-26 23:31:05,927 - INFO - 第 5 页获取到 100 条记录
2025-05-26 23:31:06,131 - INFO - Request Parameters - Page 6:
2025-05-26 23:31:06,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:06,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:06,896 - INFO - Response - Page 6:
2025-05-26 23:31:06,896 - INFO - 第 6 页获取到 82 条记录
2025-05-26 23:31:07,099 - INFO - 查询完成，共获取到 582 条记录
2025-05-26 23:31:07,099 - INFO - 获取到 582 条表单数据
2025-05-26 23:31:07,099 - INFO - 当前日期 2025-05-25 有 200 条MySQL数据需要处理
2025-05-26 23:31:07,099 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 23:31:07,099 - INFO - 开始处理日期: 2025-05-26
2025-05-26 23:31:07,099 - INFO - Request Parameters - Page 1:
2025-05-26 23:31:07,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:31:07,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:31:07,849 - INFO - Response - Page 1:
2025-05-26 23:31:07,849 - INFO - 第 1 页获取到 64 条记录
2025-05-26 23:31:08,052 - INFO - 查询完成，共获取到 64 条记录
2025-05-26 23:31:08,052 - INFO - 获取到 64 条表单数据
2025-05-26 23:31:08,052 - INFO - 当前日期 2025-05-26 有 85 条MySQL数据需要处理
2025-05-26 23:31:08,052 - INFO - 开始批量插入 21 条新记录
2025-05-26 23:31:08,209 - INFO - 批量插入响应状态码: 200
2025-05-26 23:31:08,209 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 26 May 2025 15:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '05959206-1858-7FFA-8967-33D6615B7A8A', 'x-acs-trace-id': 'ab2310a5f59ddc5f85e5f521d4013271', 'etag': '14ESwcst2hFrmenkkDPBxqA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-26 23:31:08,209 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMGD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMHD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMID', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMJD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMKD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMLD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMMD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMND', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMOD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMPD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMQD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMRD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMSD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMTD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMUD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMVD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMWD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMXD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMYD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMZD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BM0E']}
2025-05-26 23:31:08,209 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-05-26 23:31:08,209 - INFO - 成功插入的数据ID: ['FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMGD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMHD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMID', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMJD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMKD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMLD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMMD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2LITV85BMND', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMOD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMPD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMQD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMRD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMSD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMTD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMUD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMVD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMWD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMXD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMYD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BMZD', 'FINST-90E66JD1XFOVAGAYAD1WQ62DOFAV2MITV85BM0E']
2025-05-26 23:31:13,224 - INFO - 批量插入完成，共 21 条记录
2025-05-26 23:31:13,224 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-05-26 23:31:13,224 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 2 条
2025-05-26 23:32:13,240 - INFO - 开始同步昨天与今天的销售数据: 2025-05-25 至 2025-05-26
2025-05-26 23:32:13,240 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-26 23:32:13,240 - INFO - 查询参数: ('2025-05-25', '2025-05-26')
2025-05-26 23:32:13,318 - INFO - MySQL查询成功，时间段: 2025-05-25 至 2025-05-26，共获取 667 条记录
2025-05-26 23:32:13,318 - INFO - 获取到 2 个日期需要处理: ['2025-05-25', '2025-05-26']
2025-05-26 23:32:13,318 - INFO - 开始处理日期: 2025-05-25
2025-05-26 23:32:13,333 - INFO - Request Parameters - Page 1:
2025-05-26 23:32:13,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:13,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:14,224 - INFO - Response - Page 1:
2025-05-26 23:32:14,224 - INFO - 第 1 页获取到 100 条记录
2025-05-26 23:32:14,427 - INFO - Request Parameters - Page 2:
2025-05-26 23:32:14,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:14,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:15,396 - INFO - Response - Page 2:
2025-05-26 23:32:15,396 - INFO - 第 2 页获取到 100 条记录
2025-05-26 23:32:15,599 - INFO - Request Parameters - Page 3:
2025-05-26 23:32:15,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:15,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:16,458 - INFO - Response - Page 3:
2025-05-26 23:32:16,458 - INFO - 第 3 页获取到 100 条记录
2025-05-26 23:32:16,661 - INFO - Request Parameters - Page 4:
2025-05-26 23:32:16,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:16,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:17,443 - INFO - Response - Page 4:
2025-05-26 23:32:17,443 - INFO - 第 4 页获取到 100 条记录
2025-05-26 23:32:17,646 - INFO - Request Parameters - Page 5:
2025-05-26 23:32:17,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:17,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:18,396 - INFO - Response - Page 5:
2025-05-26 23:32:18,396 - INFO - 第 5 页获取到 100 条记录
2025-05-26 23:32:18,599 - INFO - Request Parameters - Page 6:
2025-05-26 23:32:18,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:18,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:19,318 - INFO - Response - Page 6:
2025-05-26 23:32:19,318 - INFO - 第 6 页获取到 82 条记录
2025-05-26 23:32:19,521 - INFO - 查询完成，共获取到 582 条记录
2025-05-26 23:32:19,521 - INFO - 获取到 582 条表单数据
2025-05-26 23:32:19,521 - INFO - 当前日期 2025-05-25 有 582 条MySQL数据需要处理
2025-05-26 23:32:19,536 - INFO - 日期 2025-05-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 23:32:19,536 - INFO - 开始处理日期: 2025-05-26
2025-05-26 23:32:19,536 - INFO - Request Parameters - Page 1:
2025-05-26 23:32:19,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 23:32:19,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 23:32:20,239 - INFO - Response - Page 1:
2025-05-26 23:32:20,239 - INFO - 第 1 页获取到 85 条记录
2025-05-26 23:32:20,443 - INFO - 查询完成，共获取到 85 条记录
2025-05-26 23:32:20,443 - INFO - 获取到 85 条表单数据
2025-05-26 23:32:20,443 - INFO - 当前日期 2025-05-26 有 85 条MySQL数据需要处理
2025-05-26 23:32:20,443 - INFO - 日期 2025-05-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 23:32:20,443 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-26 23:32:20,443 - INFO - 同步完成
