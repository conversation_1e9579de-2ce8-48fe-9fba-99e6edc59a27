2025-07-02 00:00:03,333 - INFO - =================使用默认全量同步=============
2025-07-02 00:00:05,192 - INFO - MySQL查询成功，共获取 3977 条记录
2025-07-02 00:00:05,192 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 00:00:05,239 - INFO - 开始处理日期: 2025-01
2025-07-02 00:00:05,239 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:05,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:05,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:06,395 - INFO - Response - Page 1:
2025-07-02 00:00:06,598 - INFO - 第 1 页获取到 100 条记录
2025-07-02 00:00:06,598 - INFO - Request Parameters - Page 2:
2025-07-02 00:00:06,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:06,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:07,630 - INFO - Response - Page 2:
2025-07-02 00:00:07,833 - INFO - 第 2 页获取到 100 条记录
2025-07-02 00:00:07,833 - INFO - Request Parameters - Page 3:
2025-07-02 00:00:07,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:07,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:08,426 - INFO - Response - Page 3:
2025-07-02 00:00:08,630 - INFO - 第 3 页获取到 100 条记录
2025-07-02 00:00:08,630 - INFO - Request Parameters - Page 4:
2025-07-02 00:00:08,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:08,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:09,130 - INFO - Response - Page 4:
2025-07-02 00:00:09,333 - INFO - 第 4 页获取到 100 条记录
2025-07-02 00:00:09,333 - INFO - Request Parameters - Page 5:
2025-07-02 00:00:09,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:09,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:09,864 - INFO - Response - Page 5:
2025-07-02 00:00:10,067 - INFO - 第 5 页获取到 100 条记录
2025-07-02 00:00:10,067 - INFO - Request Parameters - Page 6:
2025-07-02 00:00:10,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:10,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:10,583 - INFO - Response - Page 6:
2025-07-02 00:00:10,786 - INFO - 第 6 页获取到 100 条记录
2025-07-02 00:00:10,786 - INFO - Request Parameters - Page 7:
2025-07-02 00:00:10,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:10,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:11,286 - INFO - Response - Page 7:
2025-07-02 00:00:11,489 - INFO - 第 7 页获取到 82 条记录
2025-07-02 00:00:11,489 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 00:00:11,489 - INFO - 获取到 682 条表单数据
2025-07-02 00:00:11,489 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 00:00:11,505 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 00:00:11,505 - INFO - 开始处理日期: 2025-02
2025-07-02 00:00:11,505 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:11,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:11,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:12,176 - INFO - Response - Page 1:
2025-07-02 00:00:12,380 - INFO - 第 1 页获取到 100 条记录
2025-07-02 00:00:12,380 - INFO - Request Parameters - Page 2:
2025-07-02 00:00:12,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:12,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:12,942 - INFO - Response - Page 2:
2025-07-02 00:00:13,145 - INFO - 第 2 页获取到 100 条记录
2025-07-02 00:00:13,145 - INFO - Request Parameters - Page 3:
2025-07-02 00:00:13,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:13,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:13,770 - INFO - Response - Page 3:
2025-07-02 00:00:13,973 - INFO - 第 3 页获取到 100 条记录
2025-07-02 00:00:13,973 - INFO - Request Parameters - Page 4:
2025-07-02 00:00:13,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:13,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:14,489 - INFO - Response - Page 4:
2025-07-02 00:00:14,692 - INFO - 第 4 页获取到 100 条记录
2025-07-02 00:00:14,692 - INFO - Request Parameters - Page 5:
2025-07-02 00:00:14,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:14,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:15,208 - INFO - Response - Page 5:
2025-07-02 00:00:15,411 - INFO - 第 5 页获取到 100 条记录
2025-07-02 00:00:15,411 - INFO - Request Parameters - Page 6:
2025-07-02 00:00:15,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:15,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:15,942 - INFO - Response - Page 6:
2025-07-02 00:00:16,145 - INFO - 第 6 页获取到 100 条记录
2025-07-02 00:00:16,145 - INFO - Request Parameters - Page 7:
2025-07-02 00:00:16,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:16,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:16,708 - INFO - Response - Page 7:
2025-07-02 00:00:16,911 - INFO - 第 7 页获取到 70 条记录
2025-07-02 00:00:16,911 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 00:00:16,911 - INFO - 获取到 670 条表单数据
2025-07-02 00:00:16,911 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 00:00:16,926 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 00:00:16,926 - INFO - 开始处理日期: 2025-03
2025-07-02 00:00:16,926 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:16,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:16,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:17,598 - INFO - Response - Page 1:
2025-07-02 00:00:17,801 - INFO - 第 1 页获取到 100 条记录
2025-07-02 00:00:17,801 - INFO - Request Parameters - Page 2:
2025-07-02 00:00:17,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:17,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:18,333 - INFO - Response - Page 2:
2025-07-02 00:00:18,536 - INFO - 第 2 页获取到 100 条记录
2025-07-02 00:00:18,536 - INFO - Request Parameters - Page 3:
2025-07-02 00:00:18,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:18,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:19,020 - INFO - Response - Page 3:
2025-07-02 00:00:19,223 - INFO - 第 3 页获取到 100 条记录
2025-07-02 00:00:19,223 - INFO - Request Parameters - Page 4:
2025-07-02 00:00:19,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:19,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:19,801 - INFO - Response - Page 4:
2025-07-02 00:00:20,005 - INFO - 第 4 页获取到 100 条记录
2025-07-02 00:00:20,005 - INFO - Request Parameters - Page 5:
2025-07-02 00:00:20,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:20,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:20,505 - INFO - Response - Page 5:
2025-07-02 00:00:20,708 - INFO - 第 5 页获取到 100 条记录
2025-07-02 00:00:20,708 - INFO - Request Parameters - Page 6:
2025-07-02 00:00:20,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:20,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:21,161 - INFO - Response - Page 6:
2025-07-02 00:00:21,364 - INFO - 第 6 页获取到 100 条记录
2025-07-02 00:00:21,364 - INFO - Request Parameters - Page 7:
2025-07-02 00:00:21,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:21,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:21,801 - INFO - Response - Page 7:
2025-07-02 00:00:22,005 - INFO - 第 7 页获取到 61 条记录
2025-07-02 00:00:22,005 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 00:00:22,005 - INFO - 获取到 661 条表单数据
2025-07-02 00:00:22,005 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 00:00:22,020 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 00:00:22,020 - INFO - 开始处理日期: 2025-04
2025-07-02 00:00:22,020 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:22,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:22,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:22,505 - INFO - Response - Page 1:
2025-07-02 00:00:22,708 - INFO - 第 1 页获取到 100 条记录
2025-07-02 00:00:22,708 - INFO - Request Parameters - Page 2:
2025-07-02 00:00:22,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:22,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:23,176 - INFO - Response - Page 2:
2025-07-02 00:00:23,380 - INFO - 第 2 页获取到 100 条记录
2025-07-02 00:00:23,380 - INFO - Request Parameters - Page 3:
2025-07-02 00:00:23,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:23,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:23,958 - INFO - Response - Page 3:
2025-07-02 00:00:24,161 - INFO - 第 3 页获取到 100 条记录
2025-07-02 00:00:24,161 - INFO - Request Parameters - Page 4:
2025-07-02 00:00:24,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:24,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:24,614 - INFO - Response - Page 4:
2025-07-02 00:00:24,817 - INFO - 第 4 页获取到 100 条记录
2025-07-02 00:00:24,817 - INFO - Request Parameters - Page 5:
2025-07-02 00:00:24,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:24,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:25,317 - INFO - Response - Page 5:
2025-07-02 00:00:25,520 - INFO - 第 5 页获取到 100 条记录
2025-07-02 00:00:25,520 - INFO - Request Parameters - Page 6:
2025-07-02 00:00:25,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:25,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:26,067 - INFO - Response - Page 6:
2025-07-02 00:00:26,270 - INFO - 第 6 页获取到 100 条记录
2025-07-02 00:00:26,270 - INFO - Request Parameters - Page 7:
2025-07-02 00:00:26,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:26,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:26,692 - INFO - Response - Page 7:
2025-07-02 00:00:26,895 - INFO - 第 7 页获取到 56 条记录
2025-07-02 00:00:26,895 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 00:00:26,895 - INFO - 获取到 656 条表单数据
2025-07-02 00:00:26,895 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 00:00:26,911 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 00:00:26,911 - INFO - 开始处理日期: 2025-05
2025-07-02 00:00:26,911 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:26,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:26,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:27,442 - INFO - Response - Page 1:
2025-07-02 00:00:27,645 - INFO - 第 1 页获取到 100 条记录
2025-07-02 00:00:27,645 - INFO - Request Parameters - Page 2:
2025-07-02 00:00:27,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:27,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:28,176 - INFO - Response - Page 2:
2025-07-02 00:00:28,380 - INFO - 第 2 页获取到 100 条记录
2025-07-02 00:00:28,380 - INFO - Request Parameters - Page 3:
2025-07-02 00:00:28,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:28,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:28,895 - INFO - Response - Page 3:
2025-07-02 00:00:29,098 - INFO - 第 3 页获取到 100 条记录
2025-07-02 00:00:29,098 - INFO - Request Parameters - Page 4:
2025-07-02 00:00:29,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:29,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:29,598 - INFO - Response - Page 4:
2025-07-02 00:00:29,801 - INFO - 第 4 页获取到 100 条记录
2025-07-02 00:00:29,801 - INFO - Request Parameters - Page 5:
2025-07-02 00:00:29,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:29,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:30,411 - INFO - Response - Page 5:
2025-07-02 00:00:30,614 - INFO - 第 5 页获取到 100 条记录
2025-07-02 00:00:30,614 - INFO - Request Parameters - Page 6:
2025-07-02 00:00:30,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:30,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:31,161 - INFO - Response - Page 6:
2025-07-02 00:00:31,364 - INFO - 第 6 页获取到 100 条记录
2025-07-02 00:00:31,364 - INFO - Request Parameters - Page 7:
2025-07-02 00:00:31,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:31,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:31,786 - INFO - Response - Page 7:
2025-07-02 00:00:31,989 - INFO - 第 7 页获取到 65 条记录
2025-07-02 00:00:31,989 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 00:00:31,989 - INFO - 获取到 665 条表单数据
2025-07-02 00:00:31,989 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 00:00:32,004 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 00:00:32,004 - INFO - 开始处理日期: 2025-06
2025-07-02 00:00:32,004 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:32,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:32,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:32,520 - INFO - Response - Page 1:
2025-07-02 00:00:32,723 - INFO - 第 1 页获取到 100 条记录
2025-07-02 00:00:32,723 - INFO - Request Parameters - Page 2:
2025-07-02 00:00:32,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:32,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:33,301 - INFO - Response - Page 2:
2025-07-02 00:00:33,504 - INFO - 第 2 页获取到 100 条记录
2025-07-02 00:00:33,504 - INFO - Request Parameters - Page 3:
2025-07-02 00:00:33,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:33,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:33,973 - INFO - Response - Page 3:
2025-07-02 00:00:34,176 - INFO - 第 3 页获取到 100 条记录
2025-07-02 00:00:34,176 - INFO - Request Parameters - Page 4:
2025-07-02 00:00:34,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:34,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:34,770 - INFO - Response - Page 4:
2025-07-02 00:00:34,973 - INFO - 第 4 页获取到 100 条记录
2025-07-02 00:00:34,973 - INFO - Request Parameters - Page 5:
2025-07-02 00:00:34,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:34,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:35,504 - INFO - Response - Page 5:
2025-07-02 00:00:35,708 - INFO - 第 5 页获取到 100 条记录
2025-07-02 00:00:35,708 - INFO - Request Parameters - Page 6:
2025-07-02 00:00:35,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:35,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:36,254 - INFO - Response - Page 6:
2025-07-02 00:00:36,458 - INFO - 第 6 页获取到 100 条记录
2025-07-02 00:00:36,458 - INFO - Request Parameters - Page 7:
2025-07-02 00:00:36,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:36,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:36,833 - INFO - Response - Page 7:
2025-07-02 00:00:37,036 - INFO - 第 7 页获取到 30 条记录
2025-07-02 00:00:37,036 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 00:00:37,036 - INFO - 获取到 630 条表单数据
2025-07-02 00:00:37,036 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 00:00:37,051 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 00:00:37,051 - INFO - 开始处理日期: 2025-07
2025-07-02 00:00:37,051 - INFO - Request Parameters - Page 1:
2025-07-02 00:00:37,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 00:00:37,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 00:00:37,286 - INFO - Response - Page 1:
2025-07-02 00:00:37,489 - INFO - 第 1 页获取到 3 条记录
2025-07-02 00:00:37,489 - INFO - 查询完成，共获取到 3 条记录
2025-07-02 00:00:37,489 - INFO - 获取到 3 条表单数据
2025-07-02 00:00:37,489 - INFO - 当前日期 2025-07 有 13 条MySQL数据需要处理
2025-07-02 00:00:37,489 - INFO - 开始批量插入 10 条新记录
2025-07-02 00:00:37,629 - INFO - 批量插入响应状态码: 200
2025-07-02 00:00:37,629 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 16:00:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '492', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E039AB82-E749-7366-B251-D2952968708D', 'x-acs-trace-id': '268fc3737cc6e027525e538d637a6703', 'etag': '4ZPqgr1WtxMovFIOsZP3Mlg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 00:00:37,629 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMQ8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMS8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMT8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMU8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMV8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMW8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMX8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMY8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMZ8']}
2025-07-02 00:00:37,629 - INFO - 批量插入表单数据成功，批次 1，共 10 条记录
2025-07-02 00:00:37,629 - INFO - 成功插入的数据ID: ['FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMQ8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMS8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMT8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMU8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMV8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMW8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMX8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMY8', 'FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMZ8']
2025-07-02 00:00:40,645 - INFO - 批量插入完成，共 10 条记录
2025-07-02 00:00:40,645 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 10 条，错误: 0 条
2025-07-02 00:00:40,645 - INFO - 数据同步完成！更新: 0 条，插入: 10 条，错误: 0 条
2025-07-02 00:00:40,645 - INFO - =================同步完成====================
2025-07-02 03:00:03,279 - INFO - =================使用默认全量同步=============
2025-07-02 03:00:05,138 - INFO - MySQL查询成功，共获取 3978 条记录
2025-07-02 03:00:05,138 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 03:00:05,169 - INFO - 开始处理日期: 2025-01
2025-07-02 03:00:05,169 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:05,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:05,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:06,826 - INFO - Response - Page 1:
2025-07-02 03:00:07,029 - INFO - 第 1 页获取到 100 条记录
2025-07-02 03:00:07,029 - INFO - Request Parameters - Page 2:
2025-07-02 03:00:07,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:07,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:07,654 - INFO - Response - Page 2:
2025-07-02 03:00:07,857 - INFO - 第 2 页获取到 100 条记录
2025-07-02 03:00:07,857 - INFO - Request Parameters - Page 3:
2025-07-02 03:00:07,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:07,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:08,388 - INFO - Response - Page 3:
2025-07-02 03:00:08,591 - INFO - 第 3 页获取到 100 条记录
2025-07-02 03:00:08,591 - INFO - Request Parameters - Page 4:
2025-07-02 03:00:08,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:08,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:09,169 - INFO - Response - Page 4:
2025-07-02 03:00:09,373 - INFO - 第 4 页获取到 100 条记录
2025-07-02 03:00:09,373 - INFO - Request Parameters - Page 5:
2025-07-02 03:00:09,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:09,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:09,888 - INFO - Response - Page 5:
2025-07-02 03:00:10,091 - INFO - 第 5 页获取到 100 条记录
2025-07-02 03:00:10,091 - INFO - Request Parameters - Page 6:
2025-07-02 03:00:10,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:10,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:10,701 - INFO - Response - Page 6:
2025-07-02 03:00:10,904 - INFO - 第 6 页获取到 100 条记录
2025-07-02 03:00:10,904 - INFO - Request Parameters - Page 7:
2025-07-02 03:00:10,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:10,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:11,404 - INFO - Response - Page 7:
2025-07-02 03:00:11,607 - INFO - 第 7 页获取到 82 条记录
2025-07-02 03:00:11,607 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 03:00:11,607 - INFO - 获取到 682 条表单数据
2025-07-02 03:00:11,607 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 03:00:11,623 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 03:00:11,623 - INFO - 开始处理日期: 2025-02
2025-07-02 03:00:11,623 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:11,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:11,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:12,138 - INFO - Response - Page 1:
2025-07-02 03:00:12,341 - INFO - 第 1 页获取到 100 条记录
2025-07-02 03:00:12,341 - INFO - Request Parameters - Page 2:
2025-07-02 03:00:12,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:12,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:12,857 - INFO - Response - Page 2:
2025-07-02 03:00:13,060 - INFO - 第 2 页获取到 100 条记录
2025-07-02 03:00:13,060 - INFO - Request Parameters - Page 3:
2025-07-02 03:00:13,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:13,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:13,623 - INFO - Response - Page 3:
2025-07-02 03:00:13,826 - INFO - 第 3 页获取到 100 条记录
2025-07-02 03:00:13,826 - INFO - Request Parameters - Page 4:
2025-07-02 03:00:13,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:13,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:14,310 - INFO - Response - Page 4:
2025-07-02 03:00:14,513 - INFO - 第 4 页获取到 100 条记录
2025-07-02 03:00:14,513 - INFO - Request Parameters - Page 5:
2025-07-02 03:00:14,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:14,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:15,044 - INFO - Response - Page 5:
2025-07-02 03:00:15,247 - INFO - 第 5 页获取到 100 条记录
2025-07-02 03:00:15,247 - INFO - Request Parameters - Page 6:
2025-07-02 03:00:15,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:15,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:15,794 - INFO - Response - Page 6:
2025-07-02 03:00:15,998 - INFO - 第 6 页获取到 100 条记录
2025-07-02 03:00:15,998 - INFO - Request Parameters - Page 7:
2025-07-02 03:00:15,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:15,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:16,451 - INFO - Response - Page 7:
2025-07-02 03:00:16,654 - INFO - 第 7 页获取到 70 条记录
2025-07-02 03:00:16,654 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 03:00:16,654 - INFO - 获取到 670 条表单数据
2025-07-02 03:00:16,654 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 03:00:16,669 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 03:00:16,669 - INFO - 开始处理日期: 2025-03
2025-07-02 03:00:16,669 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:16,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:16,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:17,232 - INFO - Response - Page 1:
2025-07-02 03:00:17,435 - INFO - 第 1 页获取到 100 条记录
2025-07-02 03:00:17,435 - INFO - Request Parameters - Page 2:
2025-07-02 03:00:17,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:17,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:17,904 - INFO - Response - Page 2:
2025-07-02 03:00:18,107 - INFO - 第 2 页获取到 100 条记录
2025-07-02 03:00:18,107 - INFO - Request Parameters - Page 3:
2025-07-02 03:00:18,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:18,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:18,622 - INFO - Response - Page 3:
2025-07-02 03:00:18,826 - INFO - 第 3 页获取到 100 条记录
2025-07-02 03:00:18,826 - INFO - Request Parameters - Page 4:
2025-07-02 03:00:18,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:18,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:19,357 - INFO - Response - Page 4:
2025-07-02 03:00:19,560 - INFO - 第 4 页获取到 100 条记录
2025-07-02 03:00:19,560 - INFO - Request Parameters - Page 5:
2025-07-02 03:00:19,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:19,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:20,091 - INFO - Response - Page 5:
2025-07-02 03:00:20,294 - INFO - 第 5 页获取到 100 条记录
2025-07-02 03:00:20,294 - INFO - Request Parameters - Page 6:
2025-07-02 03:00:20,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:20,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:20,826 - INFO - Response - Page 6:
2025-07-02 03:00:21,029 - INFO - 第 6 页获取到 100 条记录
2025-07-02 03:00:21,029 - INFO - Request Parameters - Page 7:
2025-07-02 03:00:21,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:21,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:21,482 - INFO - Response - Page 7:
2025-07-02 03:00:21,685 - INFO - 第 7 页获取到 61 条记录
2025-07-02 03:00:21,685 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 03:00:21,685 - INFO - 获取到 661 条表单数据
2025-07-02 03:00:21,685 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 03:00:21,701 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 03:00:21,701 - INFO - 开始处理日期: 2025-04
2025-07-02 03:00:21,701 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:21,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:21,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:22,263 - INFO - Response - Page 1:
2025-07-02 03:00:22,466 - INFO - 第 1 页获取到 100 条记录
2025-07-02 03:00:22,466 - INFO - Request Parameters - Page 2:
2025-07-02 03:00:22,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:22,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:22,904 - INFO - Response - Page 2:
2025-07-02 03:00:23,107 - INFO - 第 2 页获取到 100 条记录
2025-07-02 03:00:23,107 - INFO - Request Parameters - Page 3:
2025-07-02 03:00:23,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:23,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:23,607 - INFO - Response - Page 3:
2025-07-02 03:00:23,810 - INFO - 第 3 页获取到 100 条记录
2025-07-02 03:00:23,810 - INFO - Request Parameters - Page 4:
2025-07-02 03:00:23,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:23,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:24,372 - INFO - Response - Page 4:
2025-07-02 03:00:24,576 - INFO - 第 4 页获取到 100 条记录
2025-07-02 03:00:24,576 - INFO - Request Parameters - Page 5:
2025-07-02 03:00:24,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:24,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:25,154 - INFO - Response - Page 5:
2025-07-02 03:00:25,357 - INFO - 第 5 页获取到 100 条记录
2025-07-02 03:00:25,357 - INFO - Request Parameters - Page 6:
2025-07-02 03:00:25,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:25,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:25,841 - INFO - Response - Page 6:
2025-07-02 03:00:26,044 - INFO - 第 6 页获取到 100 条记录
2025-07-02 03:00:26,044 - INFO - Request Parameters - Page 7:
2025-07-02 03:00:26,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:26,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:26,466 - INFO - Response - Page 7:
2025-07-02 03:00:26,669 - INFO - 第 7 页获取到 56 条记录
2025-07-02 03:00:26,669 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 03:00:26,669 - INFO - 获取到 656 条表单数据
2025-07-02 03:00:26,669 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 03:00:26,685 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 03:00:26,685 - INFO - 开始处理日期: 2025-05
2025-07-02 03:00:26,685 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:26,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:26,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:27,263 - INFO - Response - Page 1:
2025-07-02 03:00:27,466 - INFO - 第 1 页获取到 100 条记录
2025-07-02 03:00:27,466 - INFO - Request Parameters - Page 2:
2025-07-02 03:00:27,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:27,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:27,997 - INFO - Response - Page 2:
2025-07-02 03:00:28,201 - INFO - 第 2 页获取到 100 条记录
2025-07-02 03:00:28,201 - INFO - Request Parameters - Page 3:
2025-07-02 03:00:28,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:28,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:28,763 - INFO - Response - Page 3:
2025-07-02 03:00:28,966 - INFO - 第 3 页获取到 100 条记录
2025-07-02 03:00:28,966 - INFO - Request Parameters - Page 4:
2025-07-02 03:00:28,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:28,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:29,529 - INFO - Response - Page 4:
2025-07-02 03:00:29,732 - INFO - 第 4 页获取到 100 条记录
2025-07-02 03:00:29,732 - INFO - Request Parameters - Page 5:
2025-07-02 03:00:29,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:29,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:30,247 - INFO - Response - Page 5:
2025-07-02 03:00:30,451 - INFO - 第 5 页获取到 100 条记录
2025-07-02 03:00:30,451 - INFO - Request Parameters - Page 6:
2025-07-02 03:00:30,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:30,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:30,966 - INFO - Response - Page 6:
2025-07-02 03:00:31,169 - INFO - 第 6 页获取到 100 条记录
2025-07-02 03:00:31,169 - INFO - Request Parameters - Page 7:
2025-07-02 03:00:31,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:31,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:31,591 - INFO - Response - Page 7:
2025-07-02 03:00:31,794 - INFO - 第 7 页获取到 65 条记录
2025-07-02 03:00:31,794 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 03:00:31,794 - INFO - 获取到 665 条表单数据
2025-07-02 03:00:31,794 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 03:00:31,810 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 03:00:31,810 - INFO - 开始处理日期: 2025-06
2025-07-02 03:00:31,810 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:31,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:31,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:32,341 - INFO - Response - Page 1:
2025-07-02 03:00:32,544 - INFO - 第 1 页获取到 100 条记录
2025-07-02 03:00:32,544 - INFO - Request Parameters - Page 2:
2025-07-02 03:00:32,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:32,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:32,997 - INFO - Response - Page 2:
2025-07-02 03:00:33,201 - INFO - 第 2 页获取到 100 条记录
2025-07-02 03:00:33,201 - INFO - Request Parameters - Page 3:
2025-07-02 03:00:33,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:33,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:33,701 - INFO - Response - Page 3:
2025-07-02 03:00:33,904 - INFO - 第 3 页获取到 100 条记录
2025-07-02 03:00:33,904 - INFO - Request Parameters - Page 4:
2025-07-02 03:00:33,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:33,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:34,372 - INFO - Response - Page 4:
2025-07-02 03:00:34,576 - INFO - 第 4 页获取到 100 条记录
2025-07-02 03:00:34,576 - INFO - Request Parameters - Page 5:
2025-07-02 03:00:34,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:34,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:35,075 - INFO - Response - Page 5:
2025-07-02 03:00:35,279 - INFO - 第 5 页获取到 100 条记录
2025-07-02 03:00:35,279 - INFO - Request Parameters - Page 6:
2025-07-02 03:00:35,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:35,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:35,825 - INFO - Response - Page 6:
2025-07-02 03:00:36,029 - INFO - 第 6 页获取到 100 条记录
2025-07-02 03:00:36,029 - INFO - Request Parameters - Page 7:
2025-07-02 03:00:36,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:36,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:36,435 - INFO - Response - Page 7:
2025-07-02 03:00:36,638 - INFO - 第 7 页获取到 30 条记录
2025-07-02 03:00:36,638 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 03:00:36,638 - INFO - 获取到 630 条表单数据
2025-07-02 03:00:36,638 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 03:00:36,654 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 03:00:36,654 - INFO - 开始处理日期: 2025-07
2025-07-02 03:00:36,654 - INFO - Request Parameters - Page 1:
2025-07-02 03:00:36,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 03:00:36,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 03:00:37,013 - INFO - Response - Page 1:
2025-07-02 03:00:37,216 - INFO - 第 1 页获取到 13 条记录
2025-07-02 03:00:37,216 - INFO - 查询完成，共获取到 13 条记录
2025-07-02 03:00:37,216 - INFO - 获取到 13 条表单数据
2025-07-02 03:00:37,216 - INFO - 当前日期 2025-07 有 14 条MySQL数据需要处理
2025-07-02 03:00:37,216 - INFO - 开始批量插入 1 条新记录
2025-07-02 03:00:37,372 - INFO - 批量插入响应状态码: 200
2025-07-02 03:00:37,372 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 19:00:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '23F5AB98-D29E-7B14-AF78-D2FF9A95B68D', 'x-acs-trace-id': '22617f43e9c9a0432dabce964335a6c3', 'etag': '6SkSZh+lgRamEo7ovzQCx9g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 03:00:37,372 - INFO - 批量插入响应体: {'result': ['FINST-K7G66FA1N2SWHIR9APGKP6AW4Q143TOV7WKCMP3']}
2025-07-02 03:00:37,372 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-02 03:00:37,372 - INFO - 成功插入的数据ID: ['FINST-K7G66FA1N2SWHIR9APGKP6AW4Q143TOV7WKCMP3']
2025-07-02 03:00:40,388 - INFO - 批量插入完成，共 1 条记录
2025-07-02 03:00:40,388 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-02 03:00:40,388 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-07-02 03:00:40,388 - INFO - =================同步完成====================
2025-07-02 06:00:03,335 - INFO - =================使用默认全量同步=============
2025-07-02 06:00:05,147 - INFO - MySQL查询成功，共获取 3978 条记录
2025-07-02 06:00:05,147 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 06:00:05,178 - INFO - 开始处理日期: 2025-01
2025-07-02 06:00:05,194 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:05,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:05,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:06,694 - INFO - Response - Page 1:
2025-07-02 06:00:06,897 - INFO - 第 1 页获取到 100 条记录
2025-07-02 06:00:06,897 - INFO - Request Parameters - Page 2:
2025-07-02 06:00:06,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:06,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:07,444 - INFO - Response - Page 2:
2025-07-02 06:00:07,647 - INFO - 第 2 页获取到 100 条记录
2025-07-02 06:00:07,647 - INFO - Request Parameters - Page 3:
2025-07-02 06:00:07,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:07,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:08,178 - INFO - Response - Page 3:
2025-07-02 06:00:08,381 - INFO - 第 3 页获取到 100 条记录
2025-07-02 06:00:08,381 - INFO - Request Parameters - Page 4:
2025-07-02 06:00:08,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:08,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:08,928 - INFO - Response - Page 4:
2025-07-02 06:00:09,131 - INFO - 第 4 页获取到 100 条记录
2025-07-02 06:00:09,131 - INFO - Request Parameters - Page 5:
2025-07-02 06:00:09,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:09,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:09,663 - INFO - Response - Page 5:
2025-07-02 06:00:09,866 - INFO - 第 5 页获取到 100 条记录
2025-07-02 06:00:09,866 - INFO - Request Parameters - Page 6:
2025-07-02 06:00:09,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:09,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:10,397 - INFO - Response - Page 6:
2025-07-02 06:00:10,600 - INFO - 第 6 页获取到 100 条记录
2025-07-02 06:00:10,600 - INFO - Request Parameters - Page 7:
2025-07-02 06:00:10,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:10,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:11,038 - INFO - Response - Page 7:
2025-07-02 06:00:11,241 - INFO - 第 7 页获取到 82 条记录
2025-07-02 06:00:11,241 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 06:00:11,241 - INFO - 获取到 682 条表单数据
2025-07-02 06:00:11,241 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 06:00:11,256 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:11,256 - INFO - 开始处理日期: 2025-02
2025-07-02 06:00:11,256 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:11,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:11,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:11,756 - INFO - Response - Page 1:
2025-07-02 06:00:11,960 - INFO - 第 1 页获取到 100 条记录
2025-07-02 06:00:11,960 - INFO - Request Parameters - Page 2:
2025-07-02 06:00:11,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:11,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:12,506 - INFO - Response - Page 2:
2025-07-02 06:00:12,710 - INFO - 第 2 页获取到 100 条记录
2025-07-02 06:00:12,710 - INFO - Request Parameters - Page 3:
2025-07-02 06:00:12,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:12,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:13,241 - INFO - Response - Page 3:
2025-07-02 06:00:13,444 - INFO - 第 3 页获取到 100 条记录
2025-07-02 06:00:13,444 - INFO - Request Parameters - Page 4:
2025-07-02 06:00:13,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:13,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:13,897 - INFO - Response - Page 4:
2025-07-02 06:00:14,100 - INFO - 第 4 页获取到 100 条记录
2025-07-02 06:00:14,100 - INFO - Request Parameters - Page 5:
2025-07-02 06:00:14,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:14,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:14,663 - INFO - Response - Page 5:
2025-07-02 06:00:14,866 - INFO - 第 5 页获取到 100 条记录
2025-07-02 06:00:14,866 - INFO - Request Parameters - Page 6:
2025-07-02 06:00:14,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:14,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:15,366 - INFO - Response - Page 6:
2025-07-02 06:00:15,569 - INFO - 第 6 页获取到 100 条记录
2025-07-02 06:00:15,569 - INFO - Request Parameters - Page 7:
2025-07-02 06:00:15,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:15,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:16,116 - INFO - Response - Page 7:
2025-07-02 06:00:16,319 - INFO - 第 7 页获取到 70 条记录
2025-07-02 06:00:16,319 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 06:00:16,319 - INFO - 获取到 670 条表单数据
2025-07-02 06:00:16,319 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 06:00:16,335 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:16,335 - INFO - 开始处理日期: 2025-03
2025-07-02 06:00:16,335 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:16,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:16,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:16,866 - INFO - Response - Page 1:
2025-07-02 06:00:17,069 - INFO - 第 1 页获取到 100 条记录
2025-07-02 06:00:17,069 - INFO - Request Parameters - Page 2:
2025-07-02 06:00:17,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:17,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:17,600 - INFO - Response - Page 2:
2025-07-02 06:00:17,803 - INFO - 第 2 页获取到 100 条记录
2025-07-02 06:00:17,803 - INFO - Request Parameters - Page 3:
2025-07-02 06:00:17,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:17,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:18,350 - INFO - Response - Page 3:
2025-07-02 06:00:18,553 - INFO - 第 3 页获取到 100 条记录
2025-07-02 06:00:18,553 - INFO - Request Parameters - Page 4:
2025-07-02 06:00:18,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:18,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:19,022 - INFO - Response - Page 4:
2025-07-02 06:00:19,225 - INFO - 第 4 页获取到 100 条记录
2025-07-02 06:00:19,225 - INFO - Request Parameters - Page 5:
2025-07-02 06:00:19,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:19,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:19,756 - INFO - Response - Page 5:
2025-07-02 06:00:19,960 - INFO - 第 5 页获取到 100 条记录
2025-07-02 06:00:19,960 - INFO - Request Parameters - Page 6:
2025-07-02 06:00:19,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:19,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:20,413 - INFO - Response - Page 6:
2025-07-02 06:00:20,616 - INFO - 第 6 页获取到 100 条记录
2025-07-02 06:00:20,616 - INFO - Request Parameters - Page 7:
2025-07-02 06:00:20,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:20,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:21,053 - INFO - Response - Page 7:
2025-07-02 06:00:21,256 - INFO - 第 7 页获取到 61 条记录
2025-07-02 06:00:21,256 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 06:00:21,256 - INFO - 获取到 661 条表单数据
2025-07-02 06:00:21,256 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 06:00:21,272 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:21,272 - INFO - 开始处理日期: 2025-04
2025-07-02 06:00:21,272 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:21,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:21,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:21,850 - INFO - Response - Page 1:
2025-07-02 06:00:22,053 - INFO - 第 1 页获取到 100 条记录
2025-07-02 06:00:22,053 - INFO - Request Parameters - Page 2:
2025-07-02 06:00:22,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:22,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:22,616 - INFO - Response - Page 2:
2025-07-02 06:00:22,819 - INFO - 第 2 页获取到 100 条记录
2025-07-02 06:00:22,819 - INFO - Request Parameters - Page 3:
2025-07-02 06:00:22,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:22,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:23,303 - INFO - Response - Page 3:
2025-07-02 06:00:23,506 - INFO - 第 3 页获取到 100 条记录
2025-07-02 06:00:23,506 - INFO - Request Parameters - Page 4:
2025-07-02 06:00:23,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:23,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:24,006 - INFO - Response - Page 4:
2025-07-02 06:00:24,210 - INFO - 第 4 页获取到 100 条记录
2025-07-02 06:00:24,210 - INFO - Request Parameters - Page 5:
2025-07-02 06:00:24,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:24,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:24,772 - INFO - Response - Page 5:
2025-07-02 06:00:24,975 - INFO - 第 5 页获取到 100 条记录
2025-07-02 06:00:24,975 - INFO - Request Parameters - Page 6:
2025-07-02 06:00:24,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:24,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:25,491 - INFO - Response - Page 6:
2025-07-02 06:00:25,694 - INFO - 第 6 页获取到 100 条记录
2025-07-02 06:00:25,694 - INFO - Request Parameters - Page 7:
2025-07-02 06:00:25,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:25,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:26,194 - INFO - Response - Page 7:
2025-07-02 06:00:26,397 - INFO - 第 7 页获取到 56 条记录
2025-07-02 06:00:26,397 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 06:00:26,397 - INFO - 获取到 656 条表单数据
2025-07-02 06:00:26,397 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 06:00:26,413 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:26,413 - INFO - 开始处理日期: 2025-05
2025-07-02 06:00:26,413 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:26,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:26,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:26,944 - INFO - Response - Page 1:
2025-07-02 06:00:27,147 - INFO - 第 1 页获取到 100 条记录
2025-07-02 06:00:27,147 - INFO - Request Parameters - Page 2:
2025-07-02 06:00:27,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:27,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:27,647 - INFO - Response - Page 2:
2025-07-02 06:00:27,850 - INFO - 第 2 页获取到 100 条记录
2025-07-02 06:00:27,850 - INFO - Request Parameters - Page 3:
2025-07-02 06:00:27,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:27,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:28,366 - INFO - Response - Page 3:
2025-07-02 06:00:28,569 - INFO - 第 3 页获取到 100 条记录
2025-07-02 06:00:28,569 - INFO - Request Parameters - Page 4:
2025-07-02 06:00:28,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:28,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:29,084 - INFO - Response - Page 4:
2025-07-02 06:00:29,288 - INFO - 第 4 页获取到 100 条记录
2025-07-02 06:00:29,288 - INFO - Request Parameters - Page 5:
2025-07-02 06:00:29,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:29,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:29,803 - INFO - Response - Page 5:
2025-07-02 06:00:30,006 - INFO - 第 5 页获取到 100 条记录
2025-07-02 06:00:30,006 - INFO - Request Parameters - Page 6:
2025-07-02 06:00:30,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:30,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:30,491 - INFO - Response - Page 6:
2025-07-02 06:00:30,694 - INFO - 第 6 页获取到 100 条记录
2025-07-02 06:00:30,694 - INFO - Request Parameters - Page 7:
2025-07-02 06:00:30,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:30,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:31,147 - INFO - Response - Page 7:
2025-07-02 06:00:31,350 - INFO - 第 7 页获取到 65 条记录
2025-07-02 06:00:31,350 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 06:00:31,350 - INFO - 获取到 665 条表单数据
2025-07-02 06:00:31,350 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 06:00:31,366 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:31,366 - INFO - 开始处理日期: 2025-06
2025-07-02 06:00:31,366 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:31,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:31,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:31,834 - INFO - Response - Page 1:
2025-07-02 06:00:32,038 - INFO - 第 1 页获取到 100 条记录
2025-07-02 06:00:32,038 - INFO - Request Parameters - Page 2:
2025-07-02 06:00:32,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:32,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:32,553 - INFO - Response - Page 2:
2025-07-02 06:00:32,756 - INFO - 第 2 页获取到 100 条记录
2025-07-02 06:00:32,756 - INFO - Request Parameters - Page 3:
2025-07-02 06:00:32,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:32,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:33,319 - INFO - Response - Page 3:
2025-07-02 06:00:33,522 - INFO - 第 3 页获取到 100 条记录
2025-07-02 06:00:33,522 - INFO - Request Parameters - Page 4:
2025-07-02 06:00:33,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:33,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:34,178 - INFO - Response - Page 4:
2025-07-02 06:00:34,381 - INFO - 第 4 页获取到 100 条记录
2025-07-02 06:00:34,381 - INFO - Request Parameters - Page 5:
2025-07-02 06:00:34,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:34,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:34,866 - INFO - Response - Page 5:
2025-07-02 06:00:35,069 - INFO - 第 5 页获取到 100 条记录
2025-07-02 06:00:35,069 - INFO - Request Parameters - Page 6:
2025-07-02 06:00:35,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:35,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:35,584 - INFO - Response - Page 6:
2025-07-02 06:00:35,803 - INFO - 第 6 页获取到 100 条记录
2025-07-02 06:00:35,803 - INFO - Request Parameters - Page 7:
2025-07-02 06:00:35,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:35,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:36,147 - INFO - Response - Page 7:
2025-07-02 06:00:36,350 - INFO - 第 7 页获取到 30 条记录
2025-07-02 06:00:36,350 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 06:00:36,350 - INFO - 获取到 630 条表单数据
2025-07-02 06:00:36,350 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 06:00:36,366 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:36,366 - INFO - 开始处理日期: 2025-07
2025-07-02 06:00:36,366 - INFO - Request Parameters - Page 1:
2025-07-02 06:00:36,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 06:00:36,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 06:00:36,694 - INFO - Response - Page 1:
2025-07-02 06:00:36,897 - INFO - 第 1 页获取到 14 条记录
2025-07-02 06:00:36,897 - INFO - 查询完成，共获取到 14 条记录
2025-07-02 06:00:36,897 - INFO - 获取到 14 条表单数据
2025-07-02 06:00:36,897 - INFO - 当前日期 2025-07 有 14 条MySQL数据需要处理
2025-07-02 06:00:36,897 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:36,897 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 06:00:36,897 - INFO - =================同步完成====================
2025-07-02 09:00:03,069 - INFO - =================使用默认全量同步=============
2025-07-02 09:00:04,881 - INFO - MySQL查询成功，共获取 4063 条记录
2025-07-02 09:00:04,897 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 09:00:04,928 - INFO - 开始处理日期: 2025-01
2025-07-02 09:00:04,928 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:04,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:04,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:06,037 - INFO - Response - Page 1:
2025-07-02 09:00:06,240 - INFO - 第 1 页获取到 100 条记录
2025-07-02 09:00:06,240 - INFO - Request Parameters - Page 2:
2025-07-02 09:00:06,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:06,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:06,678 - INFO - Response - Page 2:
2025-07-02 09:00:06,881 - INFO - 第 2 页获取到 100 条记录
2025-07-02 09:00:06,881 - INFO - Request Parameters - Page 3:
2025-07-02 09:00:06,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:06,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:07,428 - INFO - Response - Page 3:
2025-07-02 09:00:07,631 - INFO - 第 3 页获取到 100 条记录
2025-07-02 09:00:07,631 - INFO - Request Parameters - Page 4:
2025-07-02 09:00:07,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:07,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:08,397 - INFO - Response - Page 4:
2025-07-02 09:00:08,600 - INFO - 第 4 页获取到 100 条记录
2025-07-02 09:00:08,600 - INFO - Request Parameters - Page 5:
2025-07-02 09:00:08,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:08,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:09,084 - INFO - Response - Page 5:
2025-07-02 09:00:09,287 - INFO - 第 5 页获取到 100 条记录
2025-07-02 09:00:09,287 - INFO - Request Parameters - Page 6:
2025-07-02 09:00:09,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:09,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:09,787 - INFO - Response - Page 6:
2025-07-02 09:00:09,990 - INFO - 第 6 页获取到 100 条记录
2025-07-02 09:00:09,990 - INFO - Request Parameters - Page 7:
2025-07-02 09:00:09,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:09,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:10,506 - INFO - Response - Page 7:
2025-07-02 09:00:10,709 - INFO - 第 7 页获取到 82 条记录
2025-07-02 09:00:10,709 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 09:00:10,709 - INFO - 获取到 682 条表单数据
2025-07-02 09:00:10,709 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 09:00:10,725 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 09:00:10,725 - INFO - 开始处理日期: 2025-02
2025-07-02 09:00:10,725 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:10,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:10,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:11,225 - INFO - Response - Page 1:
2025-07-02 09:00:11,428 - INFO - 第 1 页获取到 100 条记录
2025-07-02 09:00:11,428 - INFO - Request Parameters - Page 2:
2025-07-02 09:00:11,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:11,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:12,022 - INFO - Response - Page 2:
2025-07-02 09:00:12,225 - INFO - 第 2 页获取到 100 条记录
2025-07-02 09:00:12,225 - INFO - Request Parameters - Page 3:
2025-07-02 09:00:12,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:12,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:12,803 - INFO - Response - Page 3:
2025-07-02 09:00:13,006 - INFO - 第 3 页获取到 100 条记录
2025-07-02 09:00:13,006 - INFO - Request Parameters - Page 4:
2025-07-02 09:00:13,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:13,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:13,537 - INFO - Response - Page 4:
2025-07-02 09:00:13,740 - INFO - 第 4 页获取到 100 条记录
2025-07-02 09:00:13,740 - INFO - Request Parameters - Page 5:
2025-07-02 09:00:13,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:13,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:14,209 - INFO - Response - Page 5:
2025-07-02 09:00:14,412 - INFO - 第 5 页获取到 100 条记录
2025-07-02 09:00:14,412 - INFO - Request Parameters - Page 6:
2025-07-02 09:00:14,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:14,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:14,959 - INFO - Response - Page 6:
2025-07-02 09:00:15,162 - INFO - 第 6 页获取到 100 条记录
2025-07-02 09:00:15,162 - INFO - Request Parameters - Page 7:
2025-07-02 09:00:15,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:15,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:15,615 - INFO - Response - Page 7:
2025-07-02 09:00:15,818 - INFO - 第 7 页获取到 70 条记录
2025-07-02 09:00:15,818 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 09:00:15,818 - INFO - 获取到 670 条表单数据
2025-07-02 09:00:15,818 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 09:00:15,834 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 09:00:15,834 - INFO - 开始处理日期: 2025-03
2025-07-02 09:00:15,834 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:15,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:15,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:16,303 - INFO - Response - Page 1:
2025-07-02 09:00:16,506 - INFO - 第 1 页获取到 100 条记录
2025-07-02 09:00:16,506 - INFO - Request Parameters - Page 2:
2025-07-02 09:00:16,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:16,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:17,084 - INFO - Response - Page 2:
2025-07-02 09:00:17,287 - INFO - 第 2 页获取到 100 条记录
2025-07-02 09:00:17,287 - INFO - Request Parameters - Page 3:
2025-07-02 09:00:17,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:17,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:17,850 - INFO - Response - Page 3:
2025-07-02 09:00:18,053 - INFO - 第 3 页获取到 100 条记录
2025-07-02 09:00:18,053 - INFO - Request Parameters - Page 4:
2025-07-02 09:00:18,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:18,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:18,631 - INFO - Response - Page 4:
2025-07-02 09:00:18,834 - INFO - 第 4 页获取到 100 条记录
2025-07-02 09:00:18,834 - INFO - Request Parameters - Page 5:
2025-07-02 09:00:18,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:18,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:19,303 - INFO - Response - Page 5:
2025-07-02 09:00:19,506 - INFO - 第 5 页获取到 100 条记录
2025-07-02 09:00:19,506 - INFO - Request Parameters - Page 6:
2025-07-02 09:00:19,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:19,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:20,037 - INFO - Response - Page 6:
2025-07-02 09:00:20,240 - INFO - 第 6 页获取到 100 条记录
2025-07-02 09:00:20,240 - INFO - Request Parameters - Page 7:
2025-07-02 09:00:20,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:20,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:20,631 - INFO - Response - Page 7:
2025-07-02 09:00:20,834 - INFO - 第 7 页获取到 61 条记录
2025-07-02 09:00:20,834 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 09:00:20,834 - INFO - 获取到 661 条表单数据
2025-07-02 09:00:20,834 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 09:00:20,850 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 09:00:20,850 - INFO - 开始处理日期: 2025-04
2025-07-02 09:00:20,850 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:20,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:20,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:21,365 - INFO - Response - Page 1:
2025-07-02 09:00:21,568 - INFO - 第 1 页获取到 100 条记录
2025-07-02 09:00:21,568 - INFO - Request Parameters - Page 2:
2025-07-02 09:00:21,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:21,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:22,037 - INFO - Response - Page 2:
2025-07-02 09:00:22,240 - INFO - 第 2 页获取到 100 条记录
2025-07-02 09:00:22,240 - INFO - Request Parameters - Page 3:
2025-07-02 09:00:22,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:22,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:22,725 - INFO - Response - Page 3:
2025-07-02 09:00:22,928 - INFO - 第 3 页获取到 100 条记录
2025-07-02 09:00:22,928 - INFO - Request Parameters - Page 4:
2025-07-02 09:00:22,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:22,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:23,475 - INFO - Response - Page 4:
2025-07-02 09:00:23,678 - INFO - 第 4 页获取到 100 条记录
2025-07-02 09:00:23,678 - INFO - Request Parameters - Page 5:
2025-07-02 09:00:23,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:23,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:24,287 - INFO - Response - Page 5:
2025-07-02 09:00:24,490 - INFO - 第 5 页获取到 100 条记录
2025-07-02 09:00:24,490 - INFO - Request Parameters - Page 6:
2025-07-02 09:00:24,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:24,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:25,006 - INFO - Response - Page 6:
2025-07-02 09:00:25,209 - INFO - 第 6 页获取到 100 条记录
2025-07-02 09:00:25,209 - INFO - Request Parameters - Page 7:
2025-07-02 09:00:25,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:25,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:25,631 - INFO - Response - Page 7:
2025-07-02 09:00:25,834 - INFO - 第 7 页获取到 56 条记录
2025-07-02 09:00:25,834 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 09:00:25,834 - INFO - 获取到 656 条表单数据
2025-07-02 09:00:25,834 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 09:00:25,850 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 09:00:25,850 - INFO - 开始处理日期: 2025-05
2025-07-02 09:00:25,850 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:25,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:25,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:26,350 - INFO - Response - Page 1:
2025-07-02 09:00:26,553 - INFO - 第 1 页获取到 100 条记录
2025-07-02 09:00:26,553 - INFO - Request Parameters - Page 2:
2025-07-02 09:00:26,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:26,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:26,990 - INFO - Response - Page 2:
2025-07-02 09:00:27,193 - INFO - 第 2 页获取到 100 条记录
2025-07-02 09:00:27,193 - INFO - Request Parameters - Page 3:
2025-07-02 09:00:27,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:27,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:27,678 - INFO - Response - Page 3:
2025-07-02 09:00:27,881 - INFO - 第 3 页获取到 100 条记录
2025-07-02 09:00:27,881 - INFO - Request Parameters - Page 4:
2025-07-02 09:00:27,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:27,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:28,490 - INFO - Response - Page 4:
2025-07-02 09:00:28,693 - INFO - 第 4 页获取到 100 条记录
2025-07-02 09:00:28,693 - INFO - Request Parameters - Page 5:
2025-07-02 09:00:28,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:28,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:29,178 - INFO - Response - Page 5:
2025-07-02 09:00:29,381 - INFO - 第 5 页获取到 100 条记录
2025-07-02 09:00:29,381 - INFO - Request Parameters - Page 6:
2025-07-02 09:00:29,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:29,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:29,881 - INFO - Response - Page 6:
2025-07-02 09:00:30,084 - INFO - 第 6 页获取到 100 条记录
2025-07-02 09:00:30,084 - INFO - Request Parameters - Page 7:
2025-07-02 09:00:30,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:30,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:30,553 - INFO - Response - Page 7:
2025-07-02 09:00:30,756 - INFO - 第 7 页获取到 65 条记录
2025-07-02 09:00:30,756 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 09:00:30,756 - INFO - 获取到 665 条表单数据
2025-07-02 09:00:30,756 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 09:00:30,771 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 09:00:30,771 - INFO - 开始处理日期: 2025-06
2025-07-02 09:00:30,771 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:30,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:30,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:31,318 - INFO - Response - Page 1:
2025-07-02 09:00:31,521 - INFO - 第 1 页获取到 100 条记录
2025-07-02 09:00:31,521 - INFO - Request Parameters - Page 2:
2025-07-02 09:00:31,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:31,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:32,037 - INFO - Response - Page 2:
2025-07-02 09:00:32,240 - INFO - 第 2 页获取到 100 条记录
2025-07-02 09:00:32,240 - INFO - Request Parameters - Page 3:
2025-07-02 09:00:32,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:32,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:32,818 - INFO - Response - Page 3:
2025-07-02 09:00:33,021 - INFO - 第 3 页获取到 100 条记录
2025-07-02 09:00:33,021 - INFO - Request Parameters - Page 4:
2025-07-02 09:00:33,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:33,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:33,521 - INFO - Response - Page 4:
2025-07-02 09:00:33,725 - INFO - 第 4 页获取到 100 条记录
2025-07-02 09:00:33,725 - INFO - Request Parameters - Page 5:
2025-07-02 09:00:33,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:33,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:34,162 - INFO - Response - Page 5:
2025-07-02 09:00:34,365 - INFO - 第 5 页获取到 100 条记录
2025-07-02 09:00:34,365 - INFO - Request Parameters - Page 6:
2025-07-02 09:00:34,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:34,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:34,912 - INFO - Response - Page 6:
2025-07-02 09:00:35,115 - INFO - 第 6 页获取到 100 条记录
2025-07-02 09:00:35,115 - INFO - Request Parameters - Page 7:
2025-07-02 09:00:35,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:35,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:35,490 - INFO - Response - Page 7:
2025-07-02 09:00:35,693 - INFO - 第 7 页获取到 30 条记录
2025-07-02 09:00:35,693 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 09:00:35,693 - INFO - 获取到 630 条表单数据
2025-07-02 09:00:35,693 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 09:00:35,709 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 09:00:35,709 - INFO - 开始处理日期: 2025-07
2025-07-02 09:00:35,709 - INFO - Request Parameters - Page 1:
2025-07-02 09:00:35,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 09:00:35,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 09:00:36,084 - INFO - Response - Page 1:
2025-07-02 09:00:36,287 - INFO - 第 1 页获取到 14 条记录
2025-07-02 09:00:36,287 - INFO - 查询完成，共获取到 14 条记录
2025-07-02 09:00:36,287 - INFO - 获取到 14 条表单数据
2025-07-02 09:00:36,287 - INFO - 当前日期 2025-07 有 99 条MySQL数据需要处理
2025-07-02 09:00:36,287 - INFO - 开始批量插入 85 条新记录
2025-07-02 09:00:36,521 - INFO - 批量插入响应状态码: 200
2025-07-02 09:00:36,521 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 01:00:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4092', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9279A8F9-9582-7859-82AE-F6D39319ECCA', 'x-acs-trace-id': 'c4388735512376080da58db6b6f7d60d', 'etag': '4d2HzgIIsqTHBat6FR2H1XA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 09:00:36,521 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM08', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM18', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM28', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM38', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM48', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM58', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM68', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM78', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM88', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM98', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMA8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMB8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMC8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMD8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCME8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMF8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMG8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMH8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMI8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMJ8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMK8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCML8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMM8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMN8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM09', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM19', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM29', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM39', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM49', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM59', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM69', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM79', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM89', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM99', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMA9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMB9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMC9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMD9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCME9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMF9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMG9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMH9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMI9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMJ9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMK9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCML9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMM9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMN9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMO9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMP9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMQ9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMR9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMS9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMT9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMU9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMV9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMW9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMX9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMY9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMZ9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM0A']}
2025-07-02 09:00:36,521 - INFO - 批量插入表单数据成功，批次 1，共 85 条记录
2025-07-02 09:00:36,521 - INFO - 成功插入的数据ID: ['FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ7', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM08', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM18', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM28', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM38', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM48', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM58', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM68', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM78', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM88', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM98', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMA8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMB8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMC8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMD8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCME8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMF8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMG8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMH8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMI8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMJ8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMK8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCML8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMM8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMN8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ8', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM09', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM19', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM29', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM39', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM49', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM59', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM69', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM79', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM89', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM99', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMA9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMB9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMC9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMD9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCME9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMF9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMG9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMH9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMI9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMJ9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMK9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCML9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMM9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMN9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMO9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMP9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMQ9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMR9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMS9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMT9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMU9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMV9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMW9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMX9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMY9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMZ9', 'FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM0A']
2025-07-02 09:00:39,537 - INFO - 批量插入完成，共 85 条记录
2025-07-02 09:00:39,537 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 85 条，错误: 0 条
2025-07-02 09:00:39,537 - INFO - 数据同步完成！更新: 0 条，插入: 85 条，错误: 0 条
2025-07-02 09:00:39,537 - INFO - =================同步完成====================
2025-07-02 12:00:03,236 - INFO - =================使用默认全量同步=============
2025-07-02 12:00:05,048 - INFO - MySQL查询成功，共获取 4484 条记录
2025-07-02 12:00:05,048 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 12:00:05,095 - INFO - 开始处理日期: 2025-01
2025-07-02 12:00:05,095 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:05,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:05,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:06,720 - INFO - Response - Page 1:
2025-07-02 12:00:06,923 - INFO - 第 1 页获取到 100 条记录
2025-07-02 12:00:06,923 - INFO - Request Parameters - Page 2:
2025-07-02 12:00:06,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:06,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:07,548 - INFO - Response - Page 2:
2025-07-02 12:00:07,751 - INFO - 第 2 页获取到 100 条记录
2025-07-02 12:00:07,751 - INFO - Request Parameters - Page 3:
2025-07-02 12:00:07,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:07,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:08,329 - INFO - Response - Page 3:
2025-07-02 12:00:08,533 - INFO - 第 3 页获取到 100 条记录
2025-07-02 12:00:08,533 - INFO - Request Parameters - Page 4:
2025-07-02 12:00:08,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:08,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:09,220 - INFO - Response - Page 4:
2025-07-02 12:00:09,423 - INFO - 第 4 页获取到 100 条记录
2025-07-02 12:00:09,423 - INFO - Request Parameters - Page 5:
2025-07-02 12:00:09,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:09,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:09,939 - INFO - Response - Page 5:
2025-07-02 12:00:10,142 - INFO - 第 5 页获取到 100 条记录
2025-07-02 12:00:10,142 - INFO - Request Parameters - Page 6:
2025-07-02 12:00:10,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:10,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:10,595 - INFO - Response - Page 6:
2025-07-02 12:00:10,798 - INFO - 第 6 页获取到 100 条记录
2025-07-02 12:00:10,798 - INFO - Request Parameters - Page 7:
2025-07-02 12:00:10,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:10,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:11,283 - INFO - Response - Page 7:
2025-07-02 12:00:11,486 - INFO - 第 7 页获取到 82 条记录
2025-07-02 12:00:11,486 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 12:00:11,486 - INFO - 获取到 682 条表单数据
2025-07-02 12:00:11,486 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 12:00:11,501 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 12:00:11,501 - INFO - 开始处理日期: 2025-02
2025-07-02 12:00:11,501 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:11,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:11,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:12,079 - INFO - Response - Page 1:
2025-07-02 12:00:12,283 - INFO - 第 1 页获取到 100 条记录
2025-07-02 12:00:12,283 - INFO - Request Parameters - Page 2:
2025-07-02 12:00:12,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:12,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:12,783 - INFO - Response - Page 2:
2025-07-02 12:00:12,986 - INFO - 第 2 页获取到 100 条记录
2025-07-02 12:00:12,986 - INFO - Request Parameters - Page 3:
2025-07-02 12:00:12,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:12,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:13,564 - INFO - Response - Page 3:
2025-07-02 12:00:13,767 - INFO - 第 3 页获取到 100 条记录
2025-07-02 12:00:13,767 - INFO - Request Parameters - Page 4:
2025-07-02 12:00:13,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:13,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:14,220 - INFO - Response - Page 4:
2025-07-02 12:00:14,423 - INFO - 第 4 页获取到 100 条记录
2025-07-02 12:00:14,423 - INFO - Request Parameters - Page 5:
2025-07-02 12:00:14,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:14,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:15,017 - INFO - Response - Page 5:
2025-07-02 12:00:15,220 - INFO - 第 5 页获取到 100 条记录
2025-07-02 12:00:15,220 - INFO - Request Parameters - Page 6:
2025-07-02 12:00:15,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:15,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:15,751 - INFO - Response - Page 6:
2025-07-02 12:00:15,954 - INFO - 第 6 页获取到 100 条记录
2025-07-02 12:00:15,954 - INFO - Request Parameters - Page 7:
2025-07-02 12:00:15,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:15,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:16,689 - INFO - Response - Page 7:
2025-07-02 12:00:16,892 - INFO - 第 7 页获取到 70 条记录
2025-07-02 12:00:16,892 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 12:00:16,892 - INFO - 获取到 670 条表单数据
2025-07-02 12:00:16,892 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 12:00:16,908 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 12:00:16,908 - INFO - 开始处理日期: 2025-03
2025-07-02 12:00:16,908 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:16,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:16,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:17,470 - INFO - Response - Page 1:
2025-07-02 12:00:17,673 - INFO - 第 1 页获取到 100 条记录
2025-07-02 12:00:17,673 - INFO - Request Parameters - Page 2:
2025-07-02 12:00:17,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:17,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:18,361 - INFO - Response - Page 2:
2025-07-02 12:00:18,564 - INFO - 第 2 页获取到 100 条记录
2025-07-02 12:00:18,564 - INFO - Request Parameters - Page 3:
2025-07-02 12:00:18,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:18,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:19,126 - INFO - Response - Page 3:
2025-07-02 12:00:19,329 - INFO - 第 3 页获取到 100 条记录
2025-07-02 12:00:19,329 - INFO - Request Parameters - Page 4:
2025-07-02 12:00:19,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:19,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:19,861 - INFO - Response - Page 4:
2025-07-02 12:00:20,064 - INFO - 第 4 页获取到 100 条记录
2025-07-02 12:00:20,064 - INFO - Request Parameters - Page 5:
2025-07-02 12:00:20,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:20,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:20,642 - INFO - Response - Page 5:
2025-07-02 12:00:20,845 - INFO - 第 5 页获取到 100 条记录
2025-07-02 12:00:20,845 - INFO - Request Parameters - Page 6:
2025-07-02 12:00:20,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:20,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:21,486 - INFO - Response - Page 6:
2025-07-02 12:00:21,689 - INFO - 第 6 页获取到 100 条记录
2025-07-02 12:00:21,689 - INFO - Request Parameters - Page 7:
2025-07-02 12:00:21,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:21,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:22,157 - INFO - Response - Page 7:
2025-07-02 12:00:22,361 - INFO - 第 7 页获取到 61 条记录
2025-07-02 12:00:22,361 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 12:00:22,361 - INFO - 获取到 661 条表单数据
2025-07-02 12:00:22,361 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 12:00:22,376 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 12:00:22,376 - INFO - 开始处理日期: 2025-04
2025-07-02 12:00:22,376 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:22,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:22,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:22,907 - INFO - Response - Page 1:
2025-07-02 12:00:23,111 - INFO - 第 1 页获取到 100 条记录
2025-07-02 12:00:23,111 - INFO - Request Parameters - Page 2:
2025-07-02 12:00:23,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:23,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:23,876 - INFO - Response - Page 2:
2025-07-02 12:00:24,079 - INFO - 第 2 页获取到 100 条记录
2025-07-02 12:00:24,079 - INFO - Request Parameters - Page 3:
2025-07-02 12:00:24,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:24,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:24,657 - INFO - Response - Page 3:
2025-07-02 12:00:24,861 - INFO - 第 3 页获取到 100 条记录
2025-07-02 12:00:24,861 - INFO - Request Parameters - Page 4:
2025-07-02 12:00:24,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:24,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:25,407 - INFO - Response - Page 4:
2025-07-02 12:00:25,611 - INFO - 第 4 页获取到 100 条记录
2025-07-02 12:00:25,611 - INFO - Request Parameters - Page 5:
2025-07-02 12:00:25,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:25,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:26,126 - INFO - Response - Page 5:
2025-07-02 12:00:26,329 - INFO - 第 5 页获取到 100 条记录
2025-07-02 12:00:26,329 - INFO - Request Parameters - Page 6:
2025-07-02 12:00:26,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:26,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:26,829 - INFO - Response - Page 6:
2025-07-02 12:00:27,032 - INFO - 第 6 页获取到 100 条记录
2025-07-02 12:00:27,032 - INFO - Request Parameters - Page 7:
2025-07-02 12:00:27,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:27,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:27,470 - INFO - Response - Page 7:
2025-07-02 12:00:27,673 - INFO - 第 7 页获取到 56 条记录
2025-07-02 12:00:27,673 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 12:00:27,673 - INFO - 获取到 656 条表单数据
2025-07-02 12:00:27,673 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 12:00:27,689 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 12:00:27,689 - INFO - 开始处理日期: 2025-05
2025-07-02 12:00:27,689 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:27,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:27,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:28,204 - INFO - Response - Page 1:
2025-07-02 12:00:28,407 - INFO - 第 1 页获取到 100 条记录
2025-07-02 12:00:28,407 - INFO - Request Parameters - Page 2:
2025-07-02 12:00:28,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:28,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:28,954 - INFO - Response - Page 2:
2025-07-02 12:00:29,157 - INFO - 第 2 页获取到 100 条记录
2025-07-02 12:00:29,157 - INFO - Request Parameters - Page 3:
2025-07-02 12:00:29,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:29,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:29,611 - INFO - Response - Page 3:
2025-07-02 12:00:29,814 - INFO - 第 3 页获取到 100 条记录
2025-07-02 12:00:29,814 - INFO - Request Parameters - Page 4:
2025-07-02 12:00:29,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:29,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:30,345 - INFO - Response - Page 4:
2025-07-02 12:00:30,548 - INFO - 第 4 页获取到 100 条记录
2025-07-02 12:00:30,548 - INFO - Request Parameters - Page 5:
2025-07-02 12:00:30,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:30,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:31,032 - INFO - Response - Page 5:
2025-07-02 12:00:31,236 - INFO - 第 5 页获取到 100 条记录
2025-07-02 12:00:31,236 - INFO - Request Parameters - Page 6:
2025-07-02 12:00:31,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:31,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:31,751 - INFO - Response - Page 6:
2025-07-02 12:00:31,954 - INFO - 第 6 页获取到 100 条记录
2025-07-02 12:00:31,954 - INFO - Request Parameters - Page 7:
2025-07-02 12:00:31,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:31,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:32,470 - INFO - Response - Page 7:
2025-07-02 12:00:32,673 - INFO - 第 7 页获取到 65 条记录
2025-07-02 12:00:32,673 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 12:00:32,673 - INFO - 获取到 665 条表单数据
2025-07-02 12:00:32,673 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 12:00:32,689 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 12:00:32,689 - INFO - 开始处理日期: 2025-06
2025-07-02 12:00:32,689 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:32,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:32,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:33,236 - INFO - Response - Page 1:
2025-07-02 12:00:33,439 - INFO - 第 1 页获取到 100 条记录
2025-07-02 12:00:33,439 - INFO - Request Parameters - Page 2:
2025-07-02 12:00:33,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:33,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:33,970 - INFO - Response - Page 2:
2025-07-02 12:00:34,173 - INFO - 第 2 页获取到 100 条记录
2025-07-02 12:00:34,173 - INFO - Request Parameters - Page 3:
2025-07-02 12:00:34,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:34,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:34,611 - INFO - Response - Page 3:
2025-07-02 12:00:34,814 - INFO - 第 3 页获取到 100 条记录
2025-07-02 12:00:34,814 - INFO - Request Parameters - Page 4:
2025-07-02 12:00:34,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:34,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:35,798 - INFO - Response - Page 4:
2025-07-02 12:00:36,001 - INFO - 第 4 页获取到 100 条记录
2025-07-02 12:00:36,001 - INFO - Request Parameters - Page 5:
2025-07-02 12:00:36,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:36,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:36,501 - INFO - Response - Page 5:
2025-07-02 12:00:36,704 - INFO - 第 5 页获取到 100 条记录
2025-07-02 12:00:36,704 - INFO - Request Parameters - Page 6:
2025-07-02 12:00:36,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:36,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:37,298 - INFO - Response - Page 6:
2025-07-02 12:00:37,501 - INFO - 第 6 页获取到 100 条记录
2025-07-02 12:00:37,501 - INFO - Request Parameters - Page 7:
2025-07-02 12:00:37,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:37,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:37,892 - INFO - Response - Page 7:
2025-07-02 12:00:38,095 - INFO - 第 7 页获取到 30 条记录
2025-07-02 12:00:38,095 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 12:00:38,095 - INFO - 获取到 630 条表单数据
2025-07-02 12:00:38,095 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 12:00:38,095 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-07-02 12:00:38,595 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-07-02 12:00:38,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 522943.0, 'new_value': 536418.0}, {'field': 'offline_amount', 'old_value': 211991.0, 'new_value': 222493.0}, {'field': 'total_amount', 'old_value': 734934.0, 'new_value': 758911.0}, {'field': 'order_count', 'old_value': 850, 'new_value': 891}]
2025-07-02 12:00:38,595 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-07-02 12:00:39,032 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-07-02 12:00:39,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220401.0, 'new_value': 221562.0}, {'field': 'total_amount', 'old_value': 220401.0, 'new_value': 221562.0}]
2025-07-02 12:00:39,032 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-07-02 12:00:39,595 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-07-02 12:00:39,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229297.6, 'new_value': 235351.73}, {'field': 'total_amount', 'old_value': 229297.6, 'new_value': 235351.73}, {'field': 'order_count', 'old_value': 1471, 'new_value': 1507}]
2025-07-02 12:00:39,595 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-07-02 12:00:40,064 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-07-02 12:00:40,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1271401.9, 'new_value': 1283241.1}, {'field': 'total_amount', 'old_value': 1355758.2, 'new_value': 1367597.4}, {'field': 'order_count', 'old_value': 118, 'new_value': 119}]
2025-07-02 12:00:40,064 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-07-02 12:00:40,470 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-07-02 12:00:40,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 731938.43, 'new_value': 752489.39}, {'field': 'total_amount', 'old_value': 731938.43, 'new_value': 752489.39}, {'field': 'order_count', 'old_value': 5539, 'new_value': 5729}]
2025-07-02 12:00:40,470 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-07-02 12:00:40,876 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-07-02 12:00:40,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 225090.0, 'new_value': 235328.0}, {'field': 'offline_amount', 'old_value': 343923.0, 'new_value': 348776.0}, {'field': 'total_amount', 'old_value': 569013.0, 'new_value': 584104.0}, {'field': 'order_count', 'old_value': 4192, 'new_value': 4296}]
2025-07-02 12:00:40,876 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-07-02 12:00:41,329 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-07-02 12:00:41,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19133.47, 'new_value': 19534.09}, {'field': 'offline_amount', 'old_value': 315943.22, 'new_value': 325708.72}, {'field': 'total_amount', 'old_value': 335076.69, 'new_value': 345242.81}, {'field': 'order_count', 'old_value': 2214, 'new_value': 2281}]
2025-07-02 12:00:41,329 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-07-02 12:00:41,892 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-07-02 12:00:41,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 829210.61, 'new_value': 860704.75}, {'field': 'total_amount', 'old_value': 858186.3, 'new_value': 889680.44}, {'field': 'order_count', 'old_value': 2933, 'new_value': 3041}]
2025-07-02 12:00:41,892 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-07-02 12:00:42,345 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-07-02 12:00:42,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93305.0, 'new_value': 98295.0}, {'field': 'offline_amount', 'old_value': 172424.0, 'new_value': 177119.0}, {'field': 'total_amount', 'old_value': 265729.0, 'new_value': 275414.0}, {'field': 'order_count', 'old_value': 6069, 'new_value': 6285}]
2025-07-02 12:00:42,345 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-07-02 12:00:42,845 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-07-02 12:00:42,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174719.83, 'new_value': 180970.81}, {'field': 'offline_amount', 'old_value': 482092.18, 'new_value': 495418.72}, {'field': 'total_amount', 'old_value': 656812.01, 'new_value': 676389.53}, {'field': 'order_count', 'old_value': 6355, 'new_value': 6553}]
2025-07-02 12:00:42,845 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-07-02 12:00:43,298 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-07-02 12:00:43,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108501.0, 'new_value': 13309.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 96513.0}, {'field': 'total_amount', 'old_value': 108501.0, 'new_value': 109822.0}]
2025-07-02 12:00:43,298 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-07-02 12:00:43,782 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-07-02 12:00:43,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 226218.1, 'new_value': 232503.0}, {'field': 'offline_amount', 'old_value': 366235.12, 'new_value': 375091.6}, {'field': 'total_amount', 'old_value': 592453.22, 'new_value': 607594.6}, {'field': 'order_count', 'old_value': 5166, 'new_value': 5300}]
2025-07-02 12:00:43,782 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-07-02 12:00:44,173 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-07-02 12:00:44,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12728.5, 'new_value': 12718.5}, {'field': 'offline_amount', 'old_value': 8461.5, 'new_value': 9567.5}, {'field': 'total_amount', 'old_value': 21190.0, 'new_value': 22286.0}]
2025-07-02 12:00:44,173 - INFO - 日期 2025-06 处理完成 - 更新: 13 条，插入: 0 条，错误: 0 条
2025-07-02 12:00:44,173 - INFO - 开始处理日期: 2025-07
2025-07-02 12:00:44,173 - INFO - Request Parameters - Page 1:
2025-07-02 12:00:44,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 12:00:44,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 12:00:44,673 - INFO - Response - Page 1:
2025-07-02 12:00:44,876 - INFO - 第 1 页获取到 99 条记录
2025-07-02 12:00:44,876 - INFO - 查询完成，共获取到 99 条记录
2025-07-02 12:00:44,876 - INFO - 获取到 99 条表单数据
2025-07-02 12:00:44,876 - INFO - 当前日期 2025-07 有 520 条MySQL数据需要处理
2025-07-02 12:00:44,876 - INFO - 开始批量插入 421 条新记录
2025-07-02 12:00:45,189 - INFO - 批量插入响应状态码: 200
2025-07-02 12:00:45,189 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 04:00:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DC723770-B767-731C-B6F6-76A719A943D2', 'x-acs-trace-id': '7aea564c4d6dc66e919ab3c383a14ac0', 'etag': '4v6nx4N5+8hRhIhmSyCc9Mg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 12:00:45,189 - INFO - 批量插入响应体: {'result': ['FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMS4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMT4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMU4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMV4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMW4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMX4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMY4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMZ4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM15', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM25', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM35', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM45', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM55', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM65', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM75', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM85', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM95', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMA5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMB5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMC5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMD5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCME5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM06', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM16', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM26', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM36', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM46', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM56', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM66', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM76', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM86', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM96', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM07', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM17', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM27', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM37', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM47', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM57', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM67', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM77', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM87', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM97', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ7']}
2025-07-02 12:00:45,189 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-07-02 12:00:45,189 - INFO - 成功插入的数据ID: ['FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMS4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMT4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMU4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMV4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMW4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMX4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMY4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMZ4', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM15', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM25', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM35', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM45', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM55', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM65', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM75', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM85', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM95', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMA5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMB5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMC5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMD5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCME5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ5', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM06', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM16', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM26', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM36', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM46', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM56', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM66', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM76', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM86', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM96', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ6', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM07', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM17', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM27', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM37', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM47', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM57', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM67', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM77', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM87', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM97', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI7', 'FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ7']
2025-07-02 12:00:48,470 - INFO - 批量插入响应状态码: 200
2025-07-02 12:00:48,470 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 04:00:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4780', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6399F202-0DE8-7636-AD37-E072D53F2AAF', 'x-acs-trace-id': '3346eb9d0485b91b5ab1103a6e89d287', 'etag': '4aGBw61UbyzuaJZ+K+p5ohw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 12:00:48,470 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM4', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM5', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM6', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM7', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM8', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM9', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM01', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM11', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM21', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM31', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM41', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM51', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM61', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM71', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM81', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM91', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM02', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM12', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM22', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM32', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM42', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM52', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM62', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM72', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM82', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM92', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV2']}
2025-07-02 12:00:48,470 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-07-02 12:00:48,470 - INFO - 成功插入的数据ID: ['FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM4', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM5', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM6', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM7', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM8', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM9', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM01', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM11', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM21', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM31', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM41', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM51', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM61', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM71', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM81', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM91', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ1', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM02', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM12', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM22', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM32', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM42', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM52', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM62', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM72', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM82', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM92', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU2', 'FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV2']
2025-07-02 12:00:51,782 - INFO - 批量插入响应状态码: 200
2025-07-02 12:00:51,782 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 04:00:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4799', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '791D091A-C995-7A3B-A433-4B245FA2EC2E', 'x-acs-trace-id': '63a94d11c5dd6dafbf3e463ff3785fcb', 'etag': '4qKPhDdPhse7AZ5rqRRA30A9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 12:00:51,782 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMN', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMO', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMP', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMQ', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMR', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMS', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMT', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMU', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMV', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMW', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMX', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMY', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM01', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM11', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM21', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM31', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM41', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM51', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM61', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM71', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM81', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM91', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMJ1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMP1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMV1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMX1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM02', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM12', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM22', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM32', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM42', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM52', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM62', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM72', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM82', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM92', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMJ2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMP2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMV2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMX2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM03', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM13', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM23', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM33', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM43', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM53', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM63', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM73', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM83', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM93', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME3']}
2025-07-02 12:00:51,782 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-07-02 12:00:51,782 - INFO - 成功插入的数据ID: ['FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMN', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMO', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMP', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMQ', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMR', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMS', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMT', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMU', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMV', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMW', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMX', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMY', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM01', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM11', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM21', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM31', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM41', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM51', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM61', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM71', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM81', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM91', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMJ1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMP1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMV1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMX1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ1', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM02', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM12', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM22', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM32', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM42', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM52', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM62', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM72', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM82', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM92', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMJ2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMP2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMV2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMX2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ2', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM03', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM13', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM23', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM33', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM43', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM53', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM63', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM73', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM83', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM93', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD3', 'FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME3']
2025-07-02 12:00:55,048 - INFO - 批量插入响应状态码: 200
2025-07-02 12:00:55,048 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 04:00:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4799', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7644ECA3-EE96-7745-B3A4-DA641FDBAACE', 'x-acs-trace-id': 'e29e1c7159a119b9dd159631183a6f42', 'etag': '4k4y2poUZUrYyTbmnpeU6Fw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 12:00:55,048 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM01', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM11', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM21', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM31', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM41', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM51', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM61', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM71', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM81', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM91', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCME1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM02', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM12', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM22', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM32', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM42', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM52', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM62', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM72', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM82', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM92', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCME2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM03', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM13', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM23', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM33', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM43', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM53', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM63', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM73', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM83', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM93', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMA3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMB3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMC3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMD3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCME3']}
2025-07-02 12:00:55,048 - INFO - 批量插入表单数据成功，批次 4，共 100 条记录
2025-07-02 12:00:55,048 - INFO - 成功插入的数据ID: ['FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM01', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM11', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM21', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM31', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM41', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM51', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM61', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM71', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM81', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM91', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCME1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ1', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM02', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM12', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM22', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM32', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM42', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM52', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM62', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM72', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM82', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM92', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCME2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ2', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM03', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM13', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM23', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM33', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM43', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM53', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM63', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM73', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM83', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM93', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMA3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMB3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMC3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMD3', 'FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCME3']
2025-07-02 12:00:58,235 - INFO - 批量插入响应状态码: 200
2025-07-02 12:00:58,235 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 04:00:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '98F40171-2261-72DF-9D5E-D323002A24AB', 'x-acs-trace-id': 'a6b0754ad2ce8f6fd48908db6367db3f', 'etag': '1+5uZiaW1rg1yl7fZ2C6/ow0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 12:00:58,235 - INFO - 批量插入响应体: {'result': ['FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCME9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMF9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMG9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMH9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMI9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMJ9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMK9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCML9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMM9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMN9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMO9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMP9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMQ9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMR9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMS9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMW9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMX9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMY9']}
2025-07-02 12:00:58,235 - INFO - 批量插入表单数据成功，批次 5，共 21 条记录
2025-07-02 12:00:58,251 - INFO - 成功插入的数据ID: ['FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCME9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMF9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMG9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMH9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMI9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMJ9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMK9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCML9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMM9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMN9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMO9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMP9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMQ9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMR9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMS9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMW9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMX9', 'FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMY9']
2025-07-02 12:01:01,267 - INFO - 批量插入完成，共 421 条记录
2025-07-02 12:01:01,267 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 421 条，错误: 0 条
2025-07-02 12:01:01,267 - INFO - 数据同步完成！更新: 13 条，插入: 421 条，错误: 0 条
2025-07-02 12:01:01,267 - INFO - =================同步完成====================
2025-07-02 15:00:03,268 - INFO - =================使用默认全量同步=============
2025-07-02 15:00:05,112 - INFO - MySQL查询成功，共获取 4486 条记录
2025-07-02 15:00:05,112 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 15:00:05,158 - INFO - 开始处理日期: 2025-01
2025-07-02 15:00:05,158 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:05,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:05,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:06,440 - INFO - Response - Page 1:
2025-07-02 15:00:06,643 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:06,643 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:06,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:06,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:07,221 - INFO - Response - Page 2:
2025-07-02 15:00:07,424 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:07,424 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:07,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:07,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:07,924 - INFO - Response - Page 3:
2025-07-02 15:00:08,127 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:08,127 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:08,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:08,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:08,940 - INFO - Response - Page 4:
2025-07-02 15:00:09,143 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:09,143 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:09,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:09,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:09,658 - INFO - Response - Page 5:
2025-07-02 15:00:09,861 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:09,861 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:09,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:09,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:10,455 - INFO - Response - Page 6:
2025-07-02 15:00:10,658 - INFO - 第 6 页获取到 100 条记录
2025-07-02 15:00:10,658 - INFO - Request Parameters - Page 7:
2025-07-02 15:00:10,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:10,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:11,096 - INFO - Response - Page 7:
2025-07-02 15:00:11,315 - INFO - 第 7 页获取到 82 条记录
2025-07-02 15:00:11,315 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 15:00:11,315 - INFO - 获取到 682 条表单数据
2025-07-02 15:00:11,315 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 15:00:11,330 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 15:00:11,330 - INFO - 开始处理日期: 2025-02
2025-07-02 15:00:11,330 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:11,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:11,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:11,846 - INFO - Response - Page 1:
2025-07-02 15:00:12,049 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:12,049 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:12,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:12,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:12,565 - INFO - Response - Page 2:
2025-07-02 15:00:12,768 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:12,768 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:12,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:12,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:13,361 - INFO - Response - Page 3:
2025-07-02 15:00:13,565 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:13,565 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:13,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:13,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:14,018 - INFO - Response - Page 4:
2025-07-02 15:00:14,221 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:14,221 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:14,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:14,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:14,721 - INFO - Response - Page 5:
2025-07-02 15:00:14,924 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:14,924 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:14,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:14,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:15,424 - INFO - Response - Page 6:
2025-07-02 15:00:15,627 - INFO - 第 6 页获取到 100 条记录
2025-07-02 15:00:15,627 - INFO - Request Parameters - Page 7:
2025-07-02 15:00:15,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:15,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:16,111 - INFO - Response - Page 7:
2025-07-02 15:00:16,315 - INFO - 第 7 页获取到 70 条记录
2025-07-02 15:00:16,315 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 15:00:16,315 - INFO - 获取到 670 条表单数据
2025-07-02 15:00:16,315 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 15:00:16,330 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 15:00:16,330 - INFO - 开始处理日期: 2025-03
2025-07-02 15:00:16,330 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:16,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:16,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:16,830 - INFO - Response - Page 1:
2025-07-02 15:00:17,033 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:17,033 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:17,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:17,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:17,533 - INFO - Response - Page 2:
2025-07-02 15:00:17,736 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:17,736 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:17,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:17,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:18,236 - INFO - Response - Page 3:
2025-07-02 15:00:18,440 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:18,440 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:18,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:18,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:18,986 - INFO - Response - Page 4:
2025-07-02 15:00:19,190 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:19,190 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:19,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:19,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:19,721 - INFO - Response - Page 5:
2025-07-02 15:00:19,924 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:19,924 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:19,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:19,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:20,408 - INFO - Response - Page 6:
2025-07-02 15:00:20,611 - INFO - 第 6 页获取到 100 条记录
2025-07-02 15:00:20,611 - INFO - Request Parameters - Page 7:
2025-07-02 15:00:20,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:20,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:21,096 - INFO - Response - Page 7:
2025-07-02 15:00:21,299 - INFO - 第 7 页获取到 61 条记录
2025-07-02 15:00:21,299 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 15:00:21,299 - INFO - 获取到 661 条表单数据
2025-07-02 15:00:21,299 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 15:00:21,315 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 15:00:21,315 - INFO - 开始处理日期: 2025-04
2025-07-02 15:00:21,315 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:21,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:21,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:21,861 - INFO - Response - Page 1:
2025-07-02 15:00:22,065 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:22,065 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:22,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:22,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:22,690 - INFO - Response - Page 2:
2025-07-02 15:00:22,893 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:22,893 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:22,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:22,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:23,377 - INFO - Response - Page 3:
2025-07-02 15:00:23,580 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:23,580 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:23,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:23,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:24,190 - INFO - Response - Page 4:
2025-07-02 15:00:24,393 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:24,393 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:24,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:24,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:24,924 - INFO - Response - Page 5:
2025-07-02 15:00:25,127 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:25,127 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:25,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:25,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:25,596 - INFO - Response - Page 6:
2025-07-02 15:00:25,799 - INFO - 第 6 页获取到 100 条记录
2025-07-02 15:00:25,799 - INFO - Request Parameters - Page 7:
2025-07-02 15:00:25,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:25,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:26,268 - INFO - Response - Page 7:
2025-07-02 15:00:26,471 - INFO - 第 7 页获取到 56 条记录
2025-07-02 15:00:26,471 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 15:00:26,471 - INFO - 获取到 656 条表单数据
2025-07-02 15:00:26,471 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 15:00:26,486 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 15:00:26,486 - INFO - 开始处理日期: 2025-05
2025-07-02 15:00:26,486 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:26,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:26,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:27,018 - INFO - Response - Page 1:
2025-07-02 15:00:27,221 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:27,221 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:27,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:27,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:27,768 - INFO - Response - Page 2:
2025-07-02 15:00:27,971 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:27,971 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:27,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:27,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:28,533 - INFO - Response - Page 3:
2025-07-02 15:00:28,736 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:28,736 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:28,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:28,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:29,236 - INFO - Response - Page 4:
2025-07-02 15:00:29,439 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:29,439 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:29,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:29,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:29,971 - INFO - Response - Page 5:
2025-07-02 15:00:30,174 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:30,174 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:30,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:30,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:30,705 - INFO - Response - Page 6:
2025-07-02 15:00:30,908 - INFO - 第 6 页获取到 100 条记录
2025-07-02 15:00:30,908 - INFO - Request Parameters - Page 7:
2025-07-02 15:00:30,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:30,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:31,314 - INFO - Response - Page 7:
2025-07-02 15:00:31,518 - INFO - 第 7 页获取到 65 条记录
2025-07-02 15:00:31,518 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 15:00:31,518 - INFO - 获取到 665 条表单数据
2025-07-02 15:00:31,518 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 15:00:31,533 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 15:00:31,533 - INFO - 开始处理日期: 2025-06
2025-07-02 15:00:31,533 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:31,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:31,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:32,143 - INFO - Response - Page 1:
2025-07-02 15:00:32,346 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:32,346 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:32,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:32,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:32,830 - INFO - Response - Page 2:
2025-07-02 15:00:33,033 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:33,033 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:33,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:33,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:33,471 - INFO - Response - Page 3:
2025-07-02 15:00:33,674 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:33,674 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:33,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:33,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:34,236 - INFO - Response - Page 4:
2025-07-02 15:00:34,439 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:34,439 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:34,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:34,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:34,955 - INFO - Response - Page 5:
2025-07-02 15:00:35,158 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:35,158 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:35,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:35,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:35,689 - INFO - Response - Page 6:
2025-07-02 15:00:35,893 - INFO - 第 6 页获取到 100 条记录
2025-07-02 15:00:35,893 - INFO - Request Parameters - Page 7:
2025-07-02 15:00:35,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:35,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:36,283 - INFO - Response - Page 7:
2025-07-02 15:00:36,486 - INFO - 第 7 页获取到 30 条记录
2025-07-02 15:00:36,486 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 15:00:36,486 - INFO - 获取到 630 条表单数据
2025-07-02 15:00:36,486 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 15:00:36,502 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 15:00:36,502 - INFO - 开始处理日期: 2025-07
2025-07-02 15:00:36,502 - INFO - Request Parameters - Page 1:
2025-07-02 15:00:36,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:36,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:37,064 - INFO - Response - Page 1:
2025-07-02 15:00:37,268 - INFO - 第 1 页获取到 100 条记录
2025-07-02 15:00:37,268 - INFO - Request Parameters - Page 2:
2025-07-02 15:00:37,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:37,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:37,783 - INFO - Response - Page 2:
2025-07-02 15:00:37,986 - INFO - 第 2 页获取到 100 条记录
2025-07-02 15:00:37,986 - INFO - Request Parameters - Page 3:
2025-07-02 15:00:37,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:37,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:38,549 - INFO - Response - Page 3:
2025-07-02 15:00:38,752 - INFO - 第 3 页获取到 100 条记录
2025-07-02 15:00:38,752 - INFO - Request Parameters - Page 4:
2025-07-02 15:00:38,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:38,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:39,283 - INFO - Response - Page 4:
2025-07-02 15:00:39,486 - INFO - 第 4 页获取到 100 条记录
2025-07-02 15:00:39,486 - INFO - Request Parameters - Page 5:
2025-07-02 15:00:39,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:39,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:40,033 - INFO - Response - Page 5:
2025-07-02 15:00:40,236 - INFO - 第 5 页获取到 100 条记录
2025-07-02 15:00:40,236 - INFO - Request Parameters - Page 6:
2025-07-02 15:00:40,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 15:00:40,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 15:00:40,564 - INFO - Response - Page 6:
2025-07-02 15:00:40,768 - INFO - 第 6 页获取到 20 条记录
2025-07-02 15:00:40,768 - INFO - 查询完成，共获取到 520 条记录
2025-07-02 15:00:40,768 - INFO - 获取到 520 条表单数据
2025-07-02 15:00:40,768 - INFO - 当前日期 2025-07 有 522 条MySQL数据需要处理
2025-07-02 15:00:40,783 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9
2025-07-02 15:00:41,314 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9
2025-07-02 15:00:41,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 13046.27}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 13046.27}, {'field': 'order_count', 'old_value': 350, 'new_value': 323}]
2025-07-02 15:00:41,314 - INFO - 开始批量插入 2 条新记录
2025-07-02 15:00:41,455 - INFO - 批量插入响应状态码: 200
2025-07-02 15:00:41,455 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 07:00:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E15B9898-996E-7A1B-B421-3C7078B7A298', 'x-acs-trace-id': '2d6d9c96b5795bfeba43008ce09d7493', 'etag': '19KcKXGokLFCM5ugoTtQG4A8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 15:00:41,455 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH', 'FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMEH']}
2025-07-02 15:00:41,455 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-02 15:00:41,455 - INFO - 成功插入的数据ID: ['FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH', 'FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMEH']
2025-07-02 15:00:44,471 - INFO - 批量插入完成，共 2 条记录
2025-07-02 15:00:44,471 - INFO - 日期 2025-07 处理完成 - 更新: 1 条，插入: 2 条，错误: 0 条
2025-07-02 15:00:44,471 - INFO - 数据同步完成！更新: 1 条，插入: 2 条，错误: 0 条
2025-07-02 15:00:44,471 - INFO - =================同步完成====================
2025-07-02 18:00:02,651 - INFO - =================使用默认全量同步=============
2025-07-02 18:00:04,479 - INFO - MySQL查询成功，共获取 4503 条记录
2025-07-02 18:00:04,494 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 18:00:04,526 - INFO - 开始处理日期: 2025-01
2025-07-02 18:00:04,526 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:04,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:04,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:06,104 - INFO - Response - Page 1:
2025-07-02 18:00:06,307 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:06,307 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:06,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:06,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:06,791 - INFO - Response - Page 2:
2025-07-02 18:00:06,994 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:06,994 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:06,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:06,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:07,494 - INFO - Response - Page 3:
2025-07-02 18:00:07,697 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:07,697 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:07,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:07,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:08,213 - INFO - Response - Page 4:
2025-07-02 18:00:08,416 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:08,416 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:08,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:08,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:08,979 - INFO - Response - Page 5:
2025-07-02 18:00:09,182 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:09,182 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:09,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:09,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:09,744 - INFO - Response - Page 6:
2025-07-02 18:00:09,947 - INFO - 第 6 页获取到 100 条记录
2025-07-02 18:00:09,947 - INFO - Request Parameters - Page 7:
2025-07-02 18:00:09,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:09,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:10,463 - INFO - Response - Page 7:
2025-07-02 18:00:10,666 - INFO - 第 7 页获取到 82 条记录
2025-07-02 18:00:10,666 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 18:00:10,666 - INFO - 获取到 682 条表单数据
2025-07-02 18:00:10,666 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 18:00:10,682 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 18:00:10,682 - INFO - 开始处理日期: 2025-02
2025-07-02 18:00:10,682 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:10,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:10,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:11,244 - INFO - Response - Page 1:
2025-07-02 18:00:11,447 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:11,447 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:11,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:11,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:12,041 - INFO - Response - Page 2:
2025-07-02 18:00:12,244 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:12,244 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:12,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:12,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:12,838 - INFO - Response - Page 3:
2025-07-02 18:00:13,041 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:13,041 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:13,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:13,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:13,572 - INFO - Response - Page 4:
2025-07-02 18:00:13,776 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:13,776 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:13,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:13,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:14,322 - INFO - Response - Page 5:
2025-07-02 18:00:14,525 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:14,525 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:14,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:14,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:15,088 - INFO - Response - Page 6:
2025-07-02 18:00:15,291 - INFO - 第 6 页获取到 100 条记录
2025-07-02 18:00:15,291 - INFO - Request Parameters - Page 7:
2025-07-02 18:00:15,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:15,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:15,760 - INFO - Response - Page 7:
2025-07-02 18:00:15,963 - INFO - 第 7 页获取到 70 条记录
2025-07-02 18:00:15,963 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 18:00:15,963 - INFO - 获取到 670 条表单数据
2025-07-02 18:00:15,963 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 18:00:15,979 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 18:00:15,979 - INFO - 开始处理日期: 2025-03
2025-07-02 18:00:15,979 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:15,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:15,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:16,650 - INFO - Response - Page 1:
2025-07-02 18:00:16,854 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:16,854 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:16,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:16,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:17,400 - INFO - Response - Page 2:
2025-07-02 18:00:17,604 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:17,604 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:17,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:17,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:18,150 - INFO - Response - Page 3:
2025-07-02 18:00:18,354 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:18,354 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:18,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:18,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:18,869 - INFO - Response - Page 4:
2025-07-02 18:00:19,072 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:19,072 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:19,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:19,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:19,588 - INFO - Response - Page 5:
2025-07-02 18:00:19,791 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:19,791 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:20,354 - INFO - Response - Page 6:
2025-07-02 18:00:20,557 - INFO - 第 6 页获取到 100 条记录
2025-07-02 18:00:20,557 - INFO - Request Parameters - Page 7:
2025-07-02 18:00:20,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:20,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:20,963 - INFO - Response - Page 7:
2025-07-02 18:00:21,166 - INFO - 第 7 页获取到 61 条记录
2025-07-02 18:00:21,166 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 18:00:21,166 - INFO - 获取到 661 条表单数据
2025-07-02 18:00:21,166 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 18:00:21,182 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 18:00:21,182 - INFO - 开始处理日期: 2025-04
2025-07-02 18:00:21,182 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:21,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:21,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:21,775 - INFO - Response - Page 1:
2025-07-02 18:00:21,979 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:21,979 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:21,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:21,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:22,447 - INFO - Response - Page 2:
2025-07-02 18:00:22,650 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:22,650 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:22,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:22,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:23,150 - INFO - Response - Page 3:
2025-07-02 18:00:23,354 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:23,354 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:23,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:23,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:23,994 - INFO - Response - Page 4:
2025-07-02 18:00:24,197 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:24,197 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:24,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:24,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:24,729 - INFO - Response - Page 5:
2025-07-02 18:00:24,932 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:24,932 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:24,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:24,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:25,369 - INFO - Response - Page 6:
2025-07-02 18:00:25,572 - INFO - 第 6 页获取到 100 条记录
2025-07-02 18:00:25,572 - INFO - Request Parameters - Page 7:
2025-07-02 18:00:25,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:25,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:26,010 - INFO - Response - Page 7:
2025-07-02 18:00:26,213 - INFO - 第 7 页获取到 56 条记录
2025-07-02 18:00:26,213 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 18:00:26,213 - INFO - 获取到 656 条表单数据
2025-07-02 18:00:26,213 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 18:00:26,229 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 18:00:26,229 - INFO - 开始处理日期: 2025-05
2025-07-02 18:00:26,229 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:26,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:26,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:26,822 - INFO - Response - Page 1:
2025-07-02 18:00:27,025 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:27,025 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:27,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:27,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:27,525 - INFO - Response - Page 2:
2025-07-02 18:00:27,729 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:27,729 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:27,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:27,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:28,229 - INFO - Response - Page 3:
2025-07-02 18:00:28,432 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:28,432 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:28,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:28,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:28,900 - INFO - Response - Page 4:
2025-07-02 18:00:29,104 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:29,104 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:29,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:29,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:29,619 - INFO - Response - Page 5:
2025-07-02 18:00:29,822 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:29,822 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:29,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:29,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:30,338 - INFO - Response - Page 6:
2025-07-02 18:00:30,541 - INFO - 第 6 页获取到 100 条记录
2025-07-02 18:00:30,541 - INFO - Request Parameters - Page 7:
2025-07-02 18:00:30,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:30,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:31,104 - INFO - Response - Page 7:
2025-07-02 18:00:31,307 - INFO - 第 7 页获取到 65 条记录
2025-07-02 18:00:31,307 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 18:00:31,307 - INFO - 获取到 665 条表单数据
2025-07-02 18:00:31,307 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 18:00:31,322 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 18:00:31,322 - INFO - 开始处理日期: 2025-06
2025-07-02 18:00:31,322 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:31,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:31,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:31,916 - INFO - Response - Page 1:
2025-07-02 18:00:32,119 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:32,119 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:32,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:32,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:32,635 - INFO - Response - Page 2:
2025-07-02 18:00:32,838 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:32,838 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:32,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:32,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:33,369 - INFO - Response - Page 3:
2025-07-02 18:00:33,572 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:33,572 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:33,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:33,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:34,150 - INFO - Response - Page 4:
2025-07-02 18:00:34,353 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:34,353 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:34,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:34,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:34,932 - INFO - Response - Page 5:
2025-07-02 18:00:35,135 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:35,135 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:35,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:35,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:35,760 - INFO - Response - Page 6:
2025-07-02 18:00:35,963 - INFO - 第 6 页获取到 100 条记录
2025-07-02 18:00:35,963 - INFO - Request Parameters - Page 7:
2025-07-02 18:00:35,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:35,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:36,338 - INFO - Response - Page 7:
2025-07-02 18:00:36,541 - INFO - 第 7 页获取到 30 条记录
2025-07-02 18:00:36,541 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 18:00:36,541 - INFO - 获取到 630 条表单数据
2025-07-02 18:00:36,557 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 18:00:36,557 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-07-02 18:00:37,072 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-07-02 18:00:37,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97834.42, 'new_value': 97818.8}, {'field': 'total_amount', 'old_value': 97834.42, 'new_value': 97818.8}]
2025-07-02 18:00:37,072 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-07-02 18:00:37,525 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-07-02 18:00:37,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4578.22, 'new_value': 4579.22}, {'field': 'total_amount', 'old_value': 154373.96, 'new_value': 154374.96}]
2025-07-02 18:00:37,541 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-07-02 18:00:38,041 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-07-02 18:00:38,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30870.3, 'new_value': 23749.0}, {'field': 'offline_amount', 'old_value': 250155.5, 'new_value': 257540.0}, {'field': 'total_amount', 'old_value': 281025.8, 'new_value': 281289.0}]
2025-07-02 18:00:38,041 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-07-02 18:00:38,916 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-07-02 18:00:38,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4223.2, 'new_value': 4145.5}, {'field': 'offline_amount', 'old_value': 102314.6, 'new_value': 102391.2}, {'field': 'total_amount', 'old_value': 106537.8, 'new_value': 106536.7}]
2025-07-02 18:00:38,916 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-07-02 18:00:39,416 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-07-02 18:00:39,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51430.86, 'new_value': 48440.76}, {'field': 'total_amount', 'old_value': 51430.86, 'new_value': 48440.76}, {'field': 'order_count', 'old_value': 1866, 'new_value': 1883}]
2025-07-02 18:00:39,416 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-07-02 18:00:39,900 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-07-02 18:00:39,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22050.7, 'new_value': 25486.0}, {'field': 'total_amount', 'old_value': 46314.7, 'new_value': 49750.0}, {'field': 'order_count', 'old_value': 4857, 'new_value': 5152}]
2025-07-02 18:00:39,900 - INFO - 日期 2025-06 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-07-02 18:00:39,900 - INFO - 开始处理日期: 2025-07
2025-07-02 18:00:39,900 - INFO - Request Parameters - Page 1:
2025-07-02 18:00:39,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:39,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:40,478 - INFO - Response - Page 1:
2025-07-02 18:00:40,682 - INFO - 第 1 页获取到 100 条记录
2025-07-02 18:00:40,682 - INFO - Request Parameters - Page 2:
2025-07-02 18:00:40,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:40,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:41,135 - INFO - Response - Page 2:
2025-07-02 18:00:41,338 - INFO - 第 2 页获取到 100 条记录
2025-07-02 18:00:41,338 - INFO - Request Parameters - Page 3:
2025-07-02 18:00:41,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:41,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:41,900 - INFO - Response - Page 3:
2025-07-02 18:00:42,103 - INFO - 第 3 页获取到 100 条记录
2025-07-02 18:00:42,103 - INFO - Request Parameters - Page 4:
2025-07-02 18:00:42,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:42,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:42,650 - INFO - Response - Page 4:
2025-07-02 18:00:42,853 - INFO - 第 4 页获取到 100 条记录
2025-07-02 18:00:42,853 - INFO - Request Parameters - Page 5:
2025-07-02 18:00:42,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:42,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:43,338 - INFO - Response - Page 5:
2025-07-02 18:00:43,541 - INFO - 第 5 页获取到 100 条记录
2025-07-02 18:00:43,541 - INFO - Request Parameters - Page 6:
2025-07-02 18:00:43,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 18:00:43,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 18:00:43,869 - INFO - Response - Page 6:
2025-07-02 18:00:44,072 - INFO - 第 6 页获取到 22 条记录
2025-07-02 18:00:44,072 - INFO - 查询完成，共获取到 522 条记录
2025-07-02 18:00:44,072 - INFO - 获取到 522 条表单数据
2025-07-02 18:00:44,072 - INFO - 当前日期 2025-07 有 539 条MySQL数据需要处理
2025-07-02 18:00:44,088 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ2
2025-07-02 18:00:44,572 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ2
2025-07-02 18:00:44,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9000.0, 'new_value': 9020.62}, {'field': 'total_amount', 'old_value': 9000.0, 'new_value': 9020.62}, {'field': 'order_count', 'old_value': 80, 'new_value': 419}]
2025-07-02 18:00:44,572 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK2
2025-07-02 18:00:45,135 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK2
2025-07-02 18:00:45,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 13105.0}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 13105.0}]
2025-07-02 18:00:45,135 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2
2025-07-02 18:00:45,588 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2
2025-07-02 18:00:45,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 900.0}, {'field': 'total_amount', 'old_value': 3000.0, 'new_value': 900.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 70}]
2025-07-02 18:00:45,588 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN2
2025-07-02 18:00:46,057 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN2
2025-07-02 18:00:46,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 6592.32}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 6592.32}, {'field': 'order_count', 'old_value': 3, 'new_value': 21}]
2025-07-02 18:00:46,057 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO2
2025-07-02 18:00:46,510 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO2
2025-07-02 18:00:46,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 150.0}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 150.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 1}]
2025-07-02 18:00:46,510 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ2
2025-07-02 18:00:46,963 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ2
2025-07-02 18:00:46,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12000.0, 'new_value': 14481.39}, {'field': 'total_amount', 'old_value': 12000.0, 'new_value': 14481.39}, {'field': 'order_count', 'old_value': 100, 'new_value': 129}]
2025-07-02 18:00:46,963 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS2
2025-07-02 18:00:47,400 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS2
2025-07-02 18:00:47,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400.0, 'new_value': 899.0}, {'field': 'total_amount', 'old_value': 400.0, 'new_value': 899.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-07-02 18:00:47,400 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW2
2025-07-02 18:00:47,869 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW2
2025-07-02 18:00:47,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1899.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1899.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-02 18:00:47,869 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY2
2025-07-02 18:00:48,353 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY2
2025-07-02 18:00:48,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-02 18:00:48,353 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM33
2025-07-02 18:00:48,807 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM33
2025-07-02 18:00:48,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 2240.0}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 2240.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 210}]
2025-07-02 18:00:48,807 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM83
2025-07-02 18:00:49,275 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM83
2025-07-02 18:00:49,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 1074.34}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 1074.34}, {'field': 'order_count', 'old_value': 40, 'new_value': 33}]
2025-07-02 18:00:49,275 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMA3
2025-07-02 18:00:49,728 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMA3
2025-07-02 18:00:49,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 344.0}, {'field': 'total_amount', 'old_value': 1000.0, 'new_value': 344.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 10}]
2025-07-02 18:00:49,728 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCME3
2025-07-02 18:00:50,150 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCME3
2025-07-02 18:00:50,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 899.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 899.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-02 18:00:50,150 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMJ9
2025-07-02 18:00:50,619 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMJ9
2025-07-02 18:00:50,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3754.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3754.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 4}]
2025-07-02 18:00:50,619 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMN9
2025-07-02 18:00:51,041 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMN9
2025-07-02 18:00:51,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 506.4}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 506.4}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-02 18:00:51,041 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9
2025-07-02 18:00:51,494 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9
2025-07-02 18:00:51,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 48975.93}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 48975.93}, {'field': 'order_count', 'old_value': 175, 'new_value': 231}]
2025-07-02 18:00:51,494 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9
2025-07-02 18:00:51,994 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9
2025-07-02 18:00:51,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6500.0, 'new_value': 11961.43}, {'field': 'total_amount', 'old_value': 6500.0, 'new_value': 11961.43}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-07-02 18:00:51,994 - INFO - 开始批量插入 17 条新记录
2025-07-02 18:00:52,166 - INFO - 批量插入响应状态码: 200
2025-07-02 18:00:52,166 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 10:00:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E23B9344-496B-76D3-9010-688F443837CD', 'x-acs-trace-id': '7513ffdbc30600c3fe1826a6e3291639', 'etag': '8e+DLX58Ybw7t1bwIRme5FA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-02 18:00:52,166 - INFO - 批量插入响应体: {'result': ['FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM84', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM94', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMA4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMB4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMC4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMD4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCME4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMF4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMG4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMH4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMI4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMJ4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMK4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCML4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMM4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMN4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMO4']}
2025-07-02 18:00:52,166 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-07-02 18:00:52,166 - INFO - 成功插入的数据ID: ['FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM84', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM94', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMA4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMB4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMC4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMD4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCME4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMF4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMG4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMH4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMI4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMJ4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMK4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCML4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMM4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMN4', 'FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMO4']
2025-07-02 18:00:55,181 - INFO - 批量插入完成，共 17 条记录
2025-07-02 18:00:55,181 - INFO - 日期 2025-07 处理完成 - 更新: 17 条，插入: 17 条，错误: 0 条
2025-07-02 18:00:55,181 - INFO - 数据同步完成！更新: 23 条，插入: 17 条，错误: 0 条
2025-07-02 18:00:55,181 - INFO - =================同步完成====================
2025-07-02 21:00:03,633 - INFO - =================使用默认全量同步=============
2025-07-02 21:00:05,477 - INFO - MySQL查询成功，共获取 4503 条记录
2025-07-02 21:00:05,477 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-02 21:00:05,508 - INFO - 开始处理日期: 2025-01
2025-07-02 21:00:05,508 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:05,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:05,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:07,103 - INFO - Response - Page 1:
2025-07-02 21:00:07,306 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:07,306 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:07,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:07,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:07,884 - INFO - Response - Page 2:
2025-07-02 21:00:08,088 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:08,088 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:08,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:08,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:08,572 - INFO - Response - Page 3:
2025-07-02 21:00:08,775 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:08,775 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:08,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:08,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:09,338 - INFO - Response - Page 4:
2025-07-02 21:00:09,541 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:09,541 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:09,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:09,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:10,182 - INFO - Response - Page 5:
2025-07-02 21:00:10,385 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:10,385 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:10,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:10,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:10,901 - INFO - Response - Page 6:
2025-07-02 21:00:11,104 - INFO - 第 6 页获取到 100 条记录
2025-07-02 21:00:11,104 - INFO - Request Parameters - Page 7:
2025-07-02 21:00:11,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:11,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:11,620 - INFO - Response - Page 7:
2025-07-02 21:00:11,823 - INFO - 第 7 页获取到 82 条记录
2025-07-02 21:00:11,823 - INFO - 查询完成，共获取到 682 条记录
2025-07-02 21:00:11,823 - INFO - 获取到 682 条表单数据
2025-07-02 21:00:11,823 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-02 21:00:11,839 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:11,839 - INFO - 开始处理日期: 2025-02
2025-07-02 21:00:11,839 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:11,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:11,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:12,371 - INFO - Response - Page 1:
2025-07-02 21:00:12,574 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:12,574 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:12,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:12,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:13,152 - INFO - Response - Page 2:
2025-07-02 21:00:13,355 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:13,355 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:13,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:13,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:13,855 - INFO - Response - Page 3:
2025-07-02 21:00:14,059 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:14,059 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:14,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:14,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:14,575 - INFO - Response - Page 4:
2025-07-02 21:00:14,778 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:14,778 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:14,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:14,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:15,294 - INFO - Response - Page 5:
2025-07-02 21:00:15,497 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:15,497 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:15,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:15,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:15,934 - INFO - Response - Page 6:
2025-07-02 21:00:16,138 - INFO - 第 6 页获取到 100 条记录
2025-07-02 21:00:16,138 - INFO - Request Parameters - Page 7:
2025-07-02 21:00:16,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:16,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:16,575 - INFO - Response - Page 7:
2025-07-02 21:00:16,779 - INFO - 第 7 页获取到 70 条记录
2025-07-02 21:00:16,779 - INFO - 查询完成，共获取到 670 条记录
2025-07-02 21:00:16,779 - INFO - 获取到 670 条表单数据
2025-07-02 21:00:16,779 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-02 21:00:16,794 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:16,794 - INFO - 开始处理日期: 2025-03
2025-07-02 21:00:16,794 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:16,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:16,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:17,357 - INFO - Response - Page 1:
2025-07-02 21:00:17,560 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:17,560 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:17,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:17,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:18,201 - INFO - Response - Page 2:
2025-07-02 21:00:18,404 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:18,404 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:18,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:18,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:18,920 - INFO - Response - Page 3:
2025-07-02 21:00:19,123 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:19,123 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:19,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:19,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:19,639 - INFO - Response - Page 4:
2025-07-02 21:00:19,842 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:19,842 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:19,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:19,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:20,405 - INFO - Response - Page 5:
2025-07-02 21:00:20,608 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:20,608 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:20,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:20,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:21,124 - INFO - Response - Page 6:
2025-07-02 21:00:21,327 - INFO - 第 6 页获取到 100 条记录
2025-07-02 21:00:21,327 - INFO - Request Parameters - Page 7:
2025-07-02 21:00:21,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:21,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:21,749 - INFO - Response - Page 7:
2025-07-02 21:00:21,952 - INFO - 第 7 页获取到 61 条记录
2025-07-02 21:00:21,952 - INFO - 查询完成，共获取到 661 条记录
2025-07-02 21:00:21,952 - INFO - 获取到 661 条表单数据
2025-07-02 21:00:21,952 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-02 21:00:21,968 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:21,968 - INFO - 开始处理日期: 2025-04
2025-07-02 21:00:21,968 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:21,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:21,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:22,437 - INFO - Response - Page 1:
2025-07-02 21:00:22,640 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:22,640 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:22,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:22,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:23,094 - INFO - Response - Page 2:
2025-07-02 21:00:23,297 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:23,297 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:23,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:23,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:23,813 - INFO - Response - Page 3:
2025-07-02 21:00:24,016 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:24,016 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:24,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:24,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:24,625 - INFO - Response - Page 4:
2025-07-02 21:00:24,829 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:24,829 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:24,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:24,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:25,329 - INFO - Response - Page 5:
2025-07-02 21:00:25,532 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:25,532 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:25,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:25,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:26,032 - INFO - Response - Page 6:
2025-07-02 21:00:26,235 - INFO - 第 6 页获取到 100 条记录
2025-07-02 21:00:26,235 - INFO - Request Parameters - Page 7:
2025-07-02 21:00:26,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:26,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:26,689 - INFO - Response - Page 7:
2025-07-02 21:00:26,892 - INFO - 第 7 页获取到 56 条记录
2025-07-02 21:00:26,892 - INFO - 查询完成，共获取到 656 条记录
2025-07-02 21:00:26,892 - INFO - 获取到 656 条表单数据
2025-07-02 21:00:26,892 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-02 21:00:26,908 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:26,908 - INFO - 开始处理日期: 2025-05
2025-07-02 21:00:26,908 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:26,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:26,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:27,408 - INFO - Response - Page 1:
2025-07-02 21:00:27,611 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:27,611 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:27,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:27,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:28,096 - INFO - Response - Page 2:
2025-07-02 21:00:28,299 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:28,299 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:28,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:28,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:28,861 - INFO - Response - Page 3:
2025-07-02 21:00:29,065 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:29,065 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:29,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:29,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:29,534 - INFO - Response - Page 4:
2025-07-02 21:00:29,737 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:29,737 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:29,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:29,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:30,221 - INFO - Response - Page 5:
2025-07-02 21:00:30,425 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:30,425 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:30,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:30,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:30,972 - INFO - Response - Page 6:
2025-07-02 21:00:31,175 - INFO - 第 6 页获取到 100 条记录
2025-07-02 21:00:31,175 - INFO - Request Parameters - Page 7:
2025-07-02 21:00:31,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:31,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:31,613 - INFO - Response - Page 7:
2025-07-02 21:00:31,816 - INFO - 第 7 页获取到 65 条记录
2025-07-02 21:00:31,816 - INFO - 查询完成，共获取到 665 条记录
2025-07-02 21:00:31,816 - INFO - 获取到 665 条表单数据
2025-07-02 21:00:31,816 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-02 21:00:31,831 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:31,831 - INFO - 开始处理日期: 2025-06
2025-07-02 21:00:31,831 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:31,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:31,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:32,269 - INFO - Response - Page 1:
2025-07-02 21:00:32,472 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:32,472 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:32,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:32,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:33,004 - INFO - Response - Page 2:
2025-07-02 21:00:33,207 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:33,207 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:33,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:33,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:33,770 - INFO - Response - Page 3:
2025-07-02 21:00:33,973 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:33,973 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:33,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:33,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:34,426 - INFO - Response - Page 4:
2025-07-02 21:00:34,629 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:34,629 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:34,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:34,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:35,114 - INFO - Response - Page 5:
2025-07-02 21:00:35,317 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:35,317 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:35,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:35,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:35,786 - INFO - Response - Page 6:
2025-07-02 21:00:35,989 - INFO - 第 6 页获取到 100 条记录
2025-07-02 21:00:35,989 - INFO - Request Parameters - Page 7:
2025-07-02 21:00:35,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:35,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:36,349 - INFO - Response - Page 7:
2025-07-02 21:00:36,552 - INFO - 第 7 页获取到 30 条记录
2025-07-02 21:00:36,552 - INFO - 查询完成，共获取到 630 条记录
2025-07-02 21:00:36,552 - INFO - 获取到 630 条表单数据
2025-07-02 21:00:36,552 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-07-02 21:00:36,552 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Z
2025-07-02 21:00:37,005 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Z
2025-07-02 21:00:37,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13487.0, 'new_value': 33487.0}, {'field': 'total_amount', 'old_value': 13487.0, 'new_value': 33487.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-07-02 21:00:37,005 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:37,005 - INFO - 开始处理日期: 2025-07
2025-07-02 21:00:37,005 - INFO - Request Parameters - Page 1:
2025-07-02 21:00:37,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:37,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:37,521 - INFO - Response - Page 1:
2025-07-02 21:00:37,724 - INFO - 第 1 页获取到 100 条记录
2025-07-02 21:00:37,724 - INFO - Request Parameters - Page 2:
2025-07-02 21:00:37,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:37,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:38,225 - INFO - Response - Page 2:
2025-07-02 21:00:38,428 - INFO - 第 2 页获取到 100 条记录
2025-07-02 21:00:38,428 - INFO - Request Parameters - Page 3:
2025-07-02 21:00:38,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:38,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:38,912 - INFO - Response - Page 3:
2025-07-02 21:00:39,116 - INFO - 第 3 页获取到 100 条记录
2025-07-02 21:00:39,116 - INFO - Request Parameters - Page 4:
2025-07-02 21:00:39,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:39,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:39,631 - INFO - Response - Page 4:
2025-07-02 21:00:39,835 - INFO - 第 4 页获取到 100 条记录
2025-07-02 21:00:39,835 - INFO - Request Parameters - Page 5:
2025-07-02 21:00:39,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:39,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:40,335 - INFO - Response - Page 5:
2025-07-02 21:00:40,538 - INFO - 第 5 页获取到 100 条记录
2025-07-02 21:00:40,538 - INFO - Request Parameters - Page 6:
2025-07-02 21:00:40,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-02 21:00:40,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-02 21:00:40,897 - INFO - Response - Page 6:
2025-07-02 21:00:41,101 - INFO - 第 6 页获取到 39 条记录
2025-07-02 21:00:41,101 - INFO - 查询完成，共获取到 539 条记录
2025-07-02 21:00:41,101 - INFO - 获取到 539 条表单数据
2025-07-02 21:00:41,101 - INFO - 当前日期 2025-07 有 539 条MySQL数据需要处理
2025-07-02 21:00:41,116 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMX8
2025-07-02 21:00:41,710 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMX8
2025-07-02 21:00:41,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3129.0, 'new_value': 5363.0}, {'field': 'offline_amount', 'old_value': 3144.0, 'new_value': 7575.0}, {'field': 'total_amount', 'old_value': 6273.0, 'new_value': 12938.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 275}]
2025-07-02 21:00:41,726 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM92
2025-07-02 21:00:42,164 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM92
2025-07-02 21:00:42,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5999.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5999.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-02 21:00:42,164 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2
2025-07-02 21:00:42,633 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2
2025-07-02 21:00:42,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 900.0, 'new_value': 1874.71}, {'field': 'total_amount', 'old_value': 900.0, 'new_value': 1874.71}, {'field': 'order_count', 'old_value': 70, 'new_value': 138}]
2025-07-02 21:00:42,633 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV2
2025-07-02 21:00:43,133 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV2
2025-07-02 21:00:43,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4000.0, 'new_value': 3457.0}, {'field': 'total_amount', 'old_value': 4000.0, 'new_value': 3457.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 71}]
2025-07-02 21:00:43,133 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM43
2025-07-02 21:00:43,586 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM43
2025-07-02 21:00:43,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 216178.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 216178.0}, {'field': 'order_count', 'old_value': 3000, 'new_value': 4200}]
2025-07-02 21:00:43,586 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMQ9
2025-07-02 21:00:44,071 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMQ9
2025-07-02 21:00:44,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400.0, 'new_value': 849.0}, {'field': 'total_amount', 'old_value': 400.0, 'new_value': 849.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 10}]
2025-07-02 21:00:44,071 - INFO - 日期 2025-07 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:44,071 - INFO - 数据同步完成！更新: 7 条，插入: 0 条，错误: 0 条
2025-07-02 21:00:44,071 - INFO - =================同步完成====================
