# com.gooagoo.exportbill 账单导入接口测试工具

## 概述

基于数衍平台接口文档生成的专用测试工具，支持账单导入接口的完整测试功能。

## 功能特性

- ✅ **完整的签名机制** - 基于现有的MD5签名方法
- 🧪 **测试环境支持** - 内置测试环境和生产环境配置
- 📊 **多种账单类型** - 支持结账单、退款单、日结单
- 🍔 **外卖平台支持** - 美团、饿了么、抖音、京东外卖
- 📝 **详细日志记录** - 完整的请求响应日志
- 🎯 **交互式界面** - 友好的菜单选择界面
- 🚀 **简洁高效** - 仅包含必填参数，快速测试

## 快速开始

### 1. 环境要求
```bash
pip install requests
```

### 2. 运行方式

#### 交互式模式（推荐）
```bash
python simple_api_test.py
```

#### 命令行模式
```bash
# 基础测试
python simple_api_test.py basic

# 多种账单类型测试
python simple_api_test.py multi

# 查看接口信息
python simple_api_test.py info
```

## 接口配置

### 测试环境（默认）
- **URL**: `http://api.test.goago.cn/oapi/rest`
- **AppId**: `d1667ebbaa3e4935a7e09be1a50f0af5`
- **设备编号**: `6A53BB2D7CDE`

### 生产环境
- **URL**: `http://api.gooagoo.com/oapi/rest`
- **AppId**: `a5274b7e5d9a41939346c33c2c3443db`

## 账单类型说明

### 基础账单类型
- `1` - 结账单
- `6` - 退款单
- `3` - 日结单

### 细分账单类型
- `10102` - 美团外卖单
- `10103` - 饿了么外卖单
- `10104` - 抖音外卖单
- `10105` - 京东外卖单
- `10602` - 美团外卖退款单
- `10603` - 饿了么外卖退款单
- `10604` - 抖音外卖退款单
- `10605` - 京东外卖退款单

## 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| exactBillType | string | 细分账单类型 | "10102" |
| billSerialNumber | string | 票据流水号 | "TEST123456" |
| terminalNumber | string | 设备编号(12位) | "6A53BB2D7CDE" |
| saleTime | string | 销售时间 | "2025-01-06 15:30:00" |
| thirdPartyOrderNo | string | 第三方订单号 | "ORDER123456" |
| receivableAmount | double | 实收金额 | 55.22 |
| totalNum | double | 商品数量 | 10.0 |
| totalFee | double | 应收金额 | 60.00 |
| paidAmount | double | 实付金额 | 55.22 |
| billType | string | 账单类型 | "1" |

## 测试特点

- ✅ **仅包含必填参数** - 简化测试，专注核心功能
- 🎯 **快速验证** - 最小参数集合，快速验证接口可用性
- 📋 **标准格式** - 严格按照接口文档的必填参数要求

## 响应格式

### 成功响应
```json
{
    "rescode": "OPEN_SUCCESS",
    "resmsg": "账单导入成功",
    "data": "成功",
    "sign": "A1CEA4C453218B8D962196EAD8070F07"
}
```

### 失败响应
```json
{
    "rescode": "OPEN_FAIL",
    "resmsg": "账单导入失败",
    "data": "失败",
    "sign": "A1CEA4C453218B8D962196EAD8070F07"
}
```

## 自定义测试

修改 `simple_api_test.py` 中的 `business_data` 参数（仅必填参数）：

```python
business_data = {
    "exactBillType": "10102",  # 细分账单类型
    "billSerialNumber": "YOUR_SERIAL_NUMBER",  # 票据流水号
    "terminalNumber": "YOUR_TERMINAL_NUMBER",  # 设备编号
    "saleTime": "2025-01-06 15:30:00",  # 销售时间
    "thirdPartyOrderNo": "YOUR_ORDER_NO",  # 第三方订单号
    "receivableAmount": 55.22,  # 实收金额
    "totalNum": 10.0,  # 商品数量
    "totalFee": 60.00,  # 应收金额
    "paidAmount": 55.22,  # 实付金额
    "billType": "1"  # 账单类型
}
```

## 测试场景

### 1. 基础账单导入测试
- 使用默认参数进行单次账单导入测试
- 验证接口基本功能

### 2. 多种账单类型测试
- 美团外卖结账单
- 饿了么外卖退款单
- 抖音外卖结账单
- 自动生成不同的测试数据

### 3. 自定义参数测试
- 修改代码中的参数进行测试
- 支持所有接口参数的自定义

## 日志文件

- **文件名**: `exportbill_test_YYYYMMDD.log`
- **内容**: 详细的请求参数、响应结果、错误信息
- **位置**: 脚本运行目录

## 输出结果

- **控制台输出**: 实时显示测试过程和结果
- **日志记录**: 详细的请求响应信息
- **JSON格式**: 清晰的参数和响应展示

## 注意事项

1. **设备编号**: 必须在数衍平台存在，否则导入失败
2. **订单号唯一性**: `thirdPartyOrderNo` 要求唯一，重复会覆盖
3. **金额格式**: 保留两位小数
4. **时间格式**: `saleTime` 使用 `yyyy-MM-dd HH:mm:ss` 格式
5. **测试频率**: 避免过于频繁的请求

## 故障排除

### 常见错误

1. **签名错误**
   - 检查 appId、appKey、apiSecret 是否正确
   - 确认参数格式符合要求

2. **设备不存在**
   - 确认 terminalNumber 在平台中已注册
   - 检查设备编号格式（12位）

3. **参数格式错误**
   - 检查必填参数是否完整
   - 确认数据类型正确

4. **网络连接**
   - 检查网络连接
   - 确认API地址正确

## 联系支持

如有问题，请：
1. 查看生成的日志文件
2. 确认参数配置正确
3. 检查网络连接状态
4. 联系技术支持团队
