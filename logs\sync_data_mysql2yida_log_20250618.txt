2025-06-18 01:30:33,613 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 01:30:33,613 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 01:30:33,613 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 01:30:33,691 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 0 条记录
2025-06-18 01:30:33,691 - ERROR - 未获取到MySQL数据
2025-06-18 01:31:33,707 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 01:31:33,707 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 01:31:33,707 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 01:31:33,816 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 69 条记录
2025-06-18 01:31:33,832 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 01:31:33,832 - INFO - 开始处理日期: 2025-06-17
2025-06-18 01:31:33,832 - INFO - Request Parameters - Page 1:
2025-06-18 01:31:33,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 01:31:33,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 01:31:41,941 - ERROR - 处理日期 2025-06-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C67A210D-13A4-757A-8F90-68749389900E Response: {'code': 'ServiceUnavailable', 'requestid': 'C67A210D-13A4-757A-8F90-68749389900E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C67A210D-13A4-757A-8F90-68749389900E)
2025-06-18 01:31:41,941 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-18 01:31:41,941 - INFO - 同步完成
2025-06-18 04:30:33,759 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 04:30:33,759 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 04:30:33,759 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 04:30:33,884 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 1 条记录
2025-06-18 04:30:33,884 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 04:30:33,884 - INFO - 开始处理日期: 2025-06-17
2025-06-18 04:30:33,884 - INFO - Request Parameters - Page 1:
2025-06-18 04:30:33,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 04:30:33,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 04:30:42,009 - ERROR - 处理日期 2025-06-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5A843775-E246-7CAA-BB0D-9F3804794CA2 Response: {'code': 'ServiceUnavailable', 'requestid': '5A843775-E246-7CAA-BB0D-9F3804794CA2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5A843775-E246-7CAA-BB0D-9F3804794CA2)
2025-06-18 04:30:42,009 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-18 04:31:42,024 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 04:31:42,024 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 04:31:42,024 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 04:31:42,149 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 70 条记录
2025-06-18 04:31:42,149 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 04:31:42,149 - INFO - 开始处理日期: 2025-06-17
2025-06-18 04:31:42,149 - INFO - Request Parameters - Page 1:
2025-06-18 04:31:42,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 04:31:42,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 04:31:47,946 - INFO - Response - Page 1:
2025-06-18 04:31:47,946 - INFO - 第 1 页获取到 24 条记录
2025-06-18 04:31:48,446 - INFO - 查询完成，共获取到 24 条记录
2025-06-18 04:31:48,446 - INFO - 获取到 24 条表单数据
2025-06-18 04:31:48,446 - INFO - 当前日期 2025-06-17 有 69 条MySQL数据需要处理
2025-06-18 04:31:48,446 - INFO - 开始批量插入 45 条新记录
2025-06-18 04:31:48,681 - INFO - 批量插入响应状态码: 200
2025-06-18 04:31:48,681 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 17 Jun 2025 20:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2172', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '253544CB-9B69-709A-A8AB-87D81F64F920', 'x-acs-trace-id': '6f04e847a0fee9bd239f336085448404', 'etag': '2uWw/Kl6j4QHM4Cnw7HxhaQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 04:31:48,681 - INFO - 批量插入响应体: {'result': ['FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM44', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM54', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM64', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM74', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM84', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM94', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMA4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMB4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMC4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMD4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CME4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMF4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMG4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMH4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMI4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMJ4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMK4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CML4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMM4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMN4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMO4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMP4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMQ4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMR4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMS4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMT4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMU4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMV4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMW4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMX4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMY4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMZ4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM05', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM15', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM25', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM35', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM45', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM55', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM65', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM75', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM85', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM95', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMA5', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMB5', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMC5']}
2025-06-18 04:31:48,681 - INFO - 批量插入表单数据成功，批次 1，共 45 条记录
2025-06-18 04:31:48,681 - INFO - 成功插入的数据ID: ['FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM44', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM54', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM64', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM74', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM84', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM94', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMA4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMB4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMC4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMD4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CME4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMF4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMG4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMH4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMI4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMJ4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMK4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CML4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMM4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMN4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMO4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMP4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMQ4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMR4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMS4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMT4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMU4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMV4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMW4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMX4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMY4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMZ4', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM05', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM15', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM25', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM35', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM45', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM55', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM65', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM75', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM85', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CM95', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMA5', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMB5', 'FINST-5A966081K3EWW8YR9RE0K78DEW2X20E8BZ0CMC5']
2025-06-18 04:31:53,696 - INFO - 批量插入完成，共 45 条记录
2025-06-18 04:31:53,696 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 45 条，错误: 0 条
2025-06-18 04:31:53,696 - INFO - 数据同步完成！更新: 0 条，插入: 45 条，错误: 0 条
2025-06-18 04:31:53,696 - INFO - 同步完成
2025-06-18 07:30:33,749 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 07:30:33,749 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 07:30:33,749 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 07:30:33,874 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 1 条记录
2025-06-18 07:30:33,874 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 07:30:33,874 - INFO - 开始处理日期: 2025-06-17
2025-06-18 07:30:33,874 - INFO - Request Parameters - Page 1:
2025-06-18 07:30:33,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 07:30:33,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 07:30:41,999 - ERROR - 处理日期 2025-06-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9C259883-4AF0-713B-89A7-52BC48999FB1 Response: {'code': 'ServiceUnavailable', 'requestid': '9C259883-4AF0-713B-89A7-52BC48999FB1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9C259883-4AF0-713B-89A7-52BC48999FB1)
2025-06-18 07:30:41,999 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-18 07:31:42,014 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 07:31:42,014 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 07:31:42,014 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 07:31:42,139 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 70 条记录
2025-06-18 07:31:42,139 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 07:31:42,139 - INFO - 开始处理日期: 2025-06-17
2025-06-18 07:31:42,139 - INFO - Request Parameters - Page 1:
2025-06-18 07:31:42,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 07:31:42,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 07:31:47,998 - INFO - Response - Page 1:
2025-06-18 07:31:47,998 - INFO - 第 1 页获取到 50 条记录
2025-06-18 07:31:48,514 - INFO - Request Parameters - Page 2:
2025-06-18 07:31:48,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 07:31:48,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 07:31:49,108 - INFO - Response - Page 2:
2025-06-18 07:31:49,108 - INFO - 第 2 页获取到 19 条记录
2025-06-18 07:31:49,608 - INFO - 查询完成，共获取到 69 条记录
2025-06-18 07:31:49,608 - INFO - 获取到 69 条表单数据
2025-06-18 07:31:49,608 - INFO - 当前日期 2025-06-17 有 69 条MySQL数据需要处理
2025-06-18 07:31:49,608 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 07:31:49,608 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 07:31:49,608 - INFO - 同步完成
2025-06-18 10:30:33,617 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 10:30:33,617 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 10:30:33,617 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 10:30:33,757 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 125 条记录
2025-06-18 10:30:33,757 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-17']
2025-06-18 10:30:33,757 - INFO - 开始处理日期: 2025-06-15
2025-06-18 10:30:33,757 - INFO - Request Parameters - Page 1:
2025-06-18 10:30:33,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:30:33,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:30:39,742 - INFO - Response - Page 1:
2025-06-18 10:30:39,742 - INFO - 第 1 页获取到 50 条记录
2025-06-18 10:30:40,257 - INFO - Request Parameters - Page 2:
2025-06-18 10:30:40,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:30:40,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:30:48,382 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A2C10CCB-3600-72E0-B5FB-3F707158E5D1 Response: {'code': 'ServiceUnavailable', 'requestid': 'A2C10CCB-3600-72E0-B5FB-3F707158E5D1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A2C10CCB-3600-72E0-B5FB-3F707158E5D1)
2025-06-18 10:30:48,382 - INFO - 开始处理日期: 2025-06-17
2025-06-18 10:30:48,382 - INFO - Request Parameters - Page 1:
2025-06-18 10:30:48,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:30:48,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:30:49,866 - INFO - Response - Page 1:
2025-06-18 10:30:49,866 - INFO - 第 1 页获取到 50 条记录
2025-06-18 10:30:50,366 - INFO - Request Parameters - Page 2:
2025-06-18 10:30:50,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:30:50,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:30:50,929 - INFO - Response - Page 2:
2025-06-18 10:30:50,929 - INFO - 第 2 页获取到 19 条记录
2025-06-18 10:30:51,445 - INFO - 查询完成，共获取到 69 条记录
2025-06-18 10:30:51,445 - INFO - 获取到 69 条表单数据
2025-06-18 10:30:51,445 - INFO - 当前日期 2025-06-17 有 121 条MySQL数据需要处理
2025-06-18 10:30:51,445 - INFO - 开始批量插入 120 条新记录
2025-06-18 10:30:51,695 - INFO - 批量插入响应状态码: 200
2025-06-18 10:30:51,695 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F19B4F27-7B7C-7F66-B52A-03AD26B78BF1', 'x-acs-trace-id': '3b86daa69519e729f2ab5796abac227e', 'etag': '2NzQjG4BFSBMJ8L8JopHTJA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:30:51,695 - INFO - 批量插入响应体: {'result': ['FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMJ8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMK8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CML8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMM8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMN8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMO8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMP8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMQ8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMR8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMS8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMT8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMU8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMV8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMW8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMX8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMY8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMZ8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM09', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM19', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM29', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM39', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM49', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM59', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM69', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM79', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM89', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM99', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMA9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMB9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMC9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMD9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CME9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMF9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMG9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMH9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMI9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMJ9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMK9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CML9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMM9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMN9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMO9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMP9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMQ9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMR9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMS9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMT9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMU9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMV9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMW9']}
2025-06-18 10:30:51,695 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-18 10:30:51,695 - INFO - 成功插入的数据ID: ['FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMJ8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMK8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CML8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMM8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMN8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMO8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMP8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMQ8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMR8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMS8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMT8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMU8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMV8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMW8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMX8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMY8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMZ8', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM09', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM19', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM29', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM39', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM49', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM59', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM69', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM79', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM89', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CM99', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMA9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMB9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMC9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMD9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CME9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMF9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMG9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMH9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMI9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMJ9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMK9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CML9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMM9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMN9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMO9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMP9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMQ9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMR9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMS9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMT9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMU9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMV9', 'FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMW9']
2025-06-18 10:30:56,945 - INFO - 批量插入响应状态码: 200
2025-06-18 10:30:56,945 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2397', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '655FC9C8-82A6-7E51-9361-804FF30398A0', 'x-acs-trace-id': '5ef11fc2af5bce8915b7601d4013d0ff', 'etag': '2oihn9z5MCSZjB7HFOn/QNg7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:30:56,945 - INFO - 批量插入响应体: {'result': ['FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CML', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMM', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMN', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMO', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMP', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMQ', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMR', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMS', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMT', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMU', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMV', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMW', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMX', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMY', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMZ', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM01', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM11', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM21', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM31', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM41', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM51', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM61', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM71', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM81', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM91', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMA1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMB1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMC1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMD1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CME1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMF1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMG1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMH1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMI1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMJ1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMK1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CML1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMM1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMN1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMO1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMP1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMQ1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMR1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMS1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMT1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMU1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMV1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMW1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMX1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMY1']}
2025-06-18 10:30:56,945 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-18 10:30:56,945 - INFO - 成功插入的数据ID: ['FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CML', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMM', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMN', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMO', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMP', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMQ', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMR', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMS', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMT', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMU', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMV', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMW', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMX', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMY', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMZ', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM01', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM11', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM21', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM31', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM41', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM51', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM61', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM71', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM81', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CM91', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMA1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMB1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMC1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMD1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CME1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMF1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMG1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMH1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMI1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMJ1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMK1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CML1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMM1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMN1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMO1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMP1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMQ1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMR1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMS1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMT1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMU1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMV1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMW1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMX1', 'FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMY1']
2025-06-18 10:31:02,148 - INFO - 批量插入响应状态码: 200
2025-06-18 10:31:02,163 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '952', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F3E77D5E-77C1-7CE4-9AE7-56311304C9D1', 'x-acs-trace-id': '23841be1362eeb32870ae7b765b15837', 'etag': '9UDKZtNMDsVbYlE0l40vidA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:31:02,163 - INFO - 批量插入响应体: {'result': ['FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMG', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMH', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMI', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMJ', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMK', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CML', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMM', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMN', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMO', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMP', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMQ', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMR', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMS', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMT', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMU', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMV', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMW', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMX', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMY', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMZ']}
2025-06-18 10:31:02,163 - INFO - 批量插入表单数据成功，批次 3，共 20 条记录
2025-06-18 10:31:02,163 - INFO - 成功插入的数据ID: ['FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMG', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMH', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMI', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMJ', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMK', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CML', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMM', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMN', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMO', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMP', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMQ', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMR', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMS', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMT', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMU', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMV', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMW', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMX', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMY', 'FINST-3PF66V716KEW3TE272I4K9HW7WW42Q975C1CMZ']
2025-06-18 10:31:07,179 - INFO - 批量插入完成，共 120 条记录
2025-06-18 10:31:07,179 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 120 条，错误: 0 条
2025-06-18 10:31:07,179 - INFO - 数据同步完成！更新: 0 条，插入: 120 条，错误: 1 条
2025-06-18 10:32:07,194 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 10:32:07,194 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 10:32:07,194 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 10:32:07,335 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 509 条记录
2025-06-18 10:32:07,335 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 10:32:07,335 - INFO - 开始处理日期: 2025-06-17
2025-06-18 10:32:07,335 - INFO - Request Parameters - Page 1:
2025-06-18 10:32:07,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:32:07,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:32:08,069 - INFO - Response - Page 1:
2025-06-18 10:32:08,069 - INFO - 第 1 页获取到 50 条记录
2025-06-18 10:32:08,569 - INFO - Request Parameters - Page 2:
2025-06-18 10:32:08,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:32:08,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:32:09,225 - INFO - Response - Page 2:
2025-06-18 10:32:09,225 - INFO - 第 2 页获取到 50 条记录
2025-06-18 10:32:09,725 - INFO - Request Parameters - Page 3:
2025-06-18 10:32:09,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:32:09,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:32:10,382 - INFO - Response - Page 3:
2025-06-18 10:32:10,382 - INFO - 第 3 页获取到 50 条记录
2025-06-18 10:32:10,882 - INFO - Request Parameters - Page 4:
2025-06-18 10:32:10,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 10:32:10,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 10:32:11,553 - INFO - Response - Page 4:
2025-06-18 10:32:11,553 - INFO - 第 4 页获取到 39 条记录
2025-06-18 10:32:12,069 - INFO - 查询完成，共获取到 189 条记录
2025-06-18 10:32:12,069 - INFO - 获取到 189 条表单数据
2025-06-18 10:32:12,069 - INFO - 当前日期 2025-06-17 有 496 条MySQL数据需要处理
2025-06-18 10:32:12,069 - INFO - 开始批量插入 307 条新记录
2025-06-18 10:32:12,303 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:12,303 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E3945C5D-A394-74F7-B98D-45522F08AF3B', 'x-acs-trace-id': 'a2eeb9b34ada2c77ad64e092841940f5', 'etag': '23wqWtDnOf9VBNGhdWcvhUw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:12,303 - INFO - 批量插入响应体: {'result': ['FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM41', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM51', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM61', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM71', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM81', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM91', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMA1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMB1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMC1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMD1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CME1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMF1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMG1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMH1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMI1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMJ1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMK1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CML1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMM1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMN1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMO1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMP1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMQ1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMR1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMS1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMT1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMU1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMV1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMW1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMX1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMY1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMZ1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM02', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM12', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM22', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM32', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM42', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM52', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM62', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM72', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM82', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM92', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMA2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMB2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMC2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMD2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CME2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMF2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMG2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMH2']}
2025-06-18 10:32:12,303 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-18 10:32:12,303 - INFO - 成功插入的数据ID: ['FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM41', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM51', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM61', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM71', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM81', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM91', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMA1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMB1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMC1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMD1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CME1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMF1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMG1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMH1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMI1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMJ1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMK1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CML1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMM1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMN1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMO1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMP1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMQ1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMR1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMS1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMT1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMU1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMV1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMW1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMX1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMY1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMZ1', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM02', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM12', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM22', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM32', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM42', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM52', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM62', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM72', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM82', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM92', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMA2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMB2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMC2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMD2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CME2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMF2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMG2', 'FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CMH2']
2025-06-18 10:32:17,522 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:17,522 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '089C4C1D-0635-7A43-AAE1-58349B8D8D74', 'x-acs-trace-id': '047512c718d3627103bb8eea2c4b0722', 'etag': '2FnG1asyJl7I+3oZeWostFg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:17,522 - INFO - 批量插入响应体: {'result': ['FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMK3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CML3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMM3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMN3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMO3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMP3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMQ3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMR3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMS3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMT3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMU3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMV3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMW3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMX3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMY3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMZ3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM04', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM14', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM24', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM34', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM44', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM54', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM64', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM74', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM84', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM94', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMA4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMB4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMC4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMD4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CME4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMF4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMG4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMH4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMI4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMJ4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMK4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CML4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMM4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMN4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMO4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMP4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMQ4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMR4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMS4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMT4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMU4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMV4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMW4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMX4']}
2025-06-18 10:32:17,522 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-18 10:32:17,522 - INFO - 成功插入的数据ID: ['FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMK3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CML3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMM3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMN3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMO3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMP3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMQ3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMR3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMS3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMT3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMU3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMV3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMW3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMX3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMY3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMZ3', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM04', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM14', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM24', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM34', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM44', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM54', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM64', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM74', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM84', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CM94', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMA4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMB4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMC4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMD4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CME4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMF4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMG4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMH4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMI4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMJ4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMK4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CML4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMM4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMN4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMO4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMP4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMQ4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMR4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMS4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMT4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMU4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMV4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMW4', 'FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMX4']
2025-06-18 10:32:22,788 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:22,788 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E18E47E8-68B2-7514-AC66-870DC27AFCD4', 'x-acs-trace-id': '9cef35d4dd9457126fc600503470c841', 'etag': '2xOsBFzu/fw7zwhxGZjBI9Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:22,788 - INFO - 批量插入响应体: {'result': ['FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMPG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMQG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMRG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMSG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMTG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMUG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMVG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMWG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMXG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMYG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMZG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM0H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM1H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM2H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM3H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM4H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM5H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM6H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM7H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM8H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM9H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMAH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMBH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMCH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMDH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMEH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMFH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMGH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMHH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMIH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMJH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMKH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMLH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMMH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMNH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMOH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMPH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMQH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMRH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMSH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMTH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMUH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMVH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMWH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMXH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMYH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMZH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM0I', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM1I', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM2I']}
2025-06-18 10:32:22,788 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-18 10:32:22,788 - INFO - 成功插入的数据ID: ['FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMPG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMQG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMRG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMSG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMTG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMUG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMVG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMWG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMXG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMYG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMZG', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM0H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM1H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM2H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM3H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM4H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM5H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM6H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM7H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM8H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM9H', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMAH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMBH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMCH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMDH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMEH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMFH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMGH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMHH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMIH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMJH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMKH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMLH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMMH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMNH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMOH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMPH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMQH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMRH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMSH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMTH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMUH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMVH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMWH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMXH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMYH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CMZH', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM0I', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM1I', 'FINST-5TD66N91W3EW46RXBC2RABQVDBSE2JHX6C1CM2I']
2025-06-18 10:32:28,022 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:28,022 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '815EB978-ED6C-79D3-BA68-25E54820829F', 'x-acs-trace-id': '93f10ab0d6c86909f6c320c1ba202138', 'etag': '27iEC0y3dLe7hmPFuml1hEA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:28,022 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM57', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM67', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM77', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM87', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM97', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMA7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMB7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMC7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMD7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CME7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMF7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMG7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMH7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMI7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMJ7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMK7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CML7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMM7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMN7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMO7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMP7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMQ7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMR7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMS7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMT7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMU7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMV7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMW7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMX7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMY7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMZ7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM08', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM18', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM28', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM38', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM48', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM58', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM68', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM78', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM88', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM98', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMA8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMB8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMC8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMD8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CME8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMF8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMG8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMH8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMI8']}
2025-06-18 10:32:28,022 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-18 10:32:28,022 - INFO - 成功插入的数据ID: ['FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM57', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM67', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM77', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM87', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM97', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMA7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMB7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMC7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMD7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CME7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMF7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMG7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMH7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMI7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMJ7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMK7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CML7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMM7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMN7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMO7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMP7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMQ7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMR7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMS7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMT7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMU7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMV7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMW7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMX7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMY7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMZ7', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM08', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM18', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM28', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM38', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM48', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM58', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM68', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM78', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM88', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CM98', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMA8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMB8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMC8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMD8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CME8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMF8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMG8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMH8', 'FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMI8']
2025-06-18 10:32:33,256 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:33,256 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:33 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2387', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6E3BAAC6-0655-7CA4-ACBA-A002699E7FF1', 'x-acs-trace-id': '01cec7e7bdff48e31cab5dc50eed2b93', 'etag': '2yWAt/9YaWGvPquHwMfGNsA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:33,256 - INFO - 批量插入响应体: {'result': ['FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMB', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMC', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMD', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CME', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMF', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMG', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMH', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMI', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMJ', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMK', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CML', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMM', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMN', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMO', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMP', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMQ', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMR', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMS', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMT', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMU', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMV', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMW', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMX', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMY', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMZ', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM01', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM11', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM21', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM31', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM41', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM51', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM61', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM71', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM81', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM91', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMA1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMB1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMC1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMD1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CME1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMF1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMG1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMH1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMI1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMJ1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMK1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CML1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMM1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMN1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMO1']}
2025-06-18 10:32:33,256 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-18 10:32:33,256 - INFO - 成功插入的数据ID: ['FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMB', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMC', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMD', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CME', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMF', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMG', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMH', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMI', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMJ', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMK', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CML', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMM', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMN', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMO', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMP', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMQ', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMR', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMS', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMT', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMU', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMV', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMW', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMX', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMY', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMZ', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM01', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM11', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM21', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM31', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM41', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM51', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM61', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM71', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM81', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CM91', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMA1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMB1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMC1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMD1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CME1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMF1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMG1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMH1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMI1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMJ1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMK1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CML1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMM1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMN1', 'FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CMO1']
2025-06-18 10:32:38,491 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:38,491 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2399', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '13E4D65C-6B0F-7776-BC53-D4181B9E3B04', 'x-acs-trace-id': 'cb4040d8bf52947582557f4bed0f0358', 'etag': '2JWhuwdZwUdFF7YBHjnx5lQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:38,491 - INFO - 批量插入响应体: {'result': ['FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMN', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMO', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMP', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMQ', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMR', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMS', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMT', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMU', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMV', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMW', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMX', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMY', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMZ', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM01', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM11', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM21', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM31', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM41', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM51', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM61', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM71', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM81', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM91', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMA1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMB1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMC1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMD1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CME1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMF1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMG1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMH1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMI1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMJ1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMK1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CML1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMM1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMN1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMO1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMP1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMQ1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMR1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMS1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMT1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMU1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMV1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMW1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMX1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMY1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMZ1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM02']}
2025-06-18 10:32:38,491 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-06-18 10:32:38,491 - INFO - 成功插入的数据ID: ['FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMN', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMO', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMP', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMQ', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMR', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMS', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMT', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMU', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMV', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMW', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMX', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMY', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMZ', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM01', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM11', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM21', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM31', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM41', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM51', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM61', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM71', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM81', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM91', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMA1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMB1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMC1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMD1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CME1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMF1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMG1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMH1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMI1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMJ1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMK1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CML1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMM1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMN1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMO1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMP1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMQ1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMR1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMS1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMT1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMU1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMV1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMW1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMX1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMY1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CMZ1', 'FINST-74766M716UDW9KIC6TKYT7MMOSZR3KL97C1CM02']
2025-06-18 10:32:43,647 - INFO - 批量插入响应状态码: 200
2025-06-18 10:32:43,647 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 02:32:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '341', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B709F1E1-EB7A-7289-8C96-B61E13569585', 'x-acs-trace-id': '0f23b40cd5fbd5a40b530ce3e971258f', 'etag': '3Pwlj0zWqjx0XWK773xFL6Q1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 10:32:43,647 - INFO - 批量插入响应体: {'result': ['FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMI', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMJ', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMK', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CML', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMM', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMN', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMO']}
2025-06-18 10:32:43,647 - INFO - 批量插入表单数据成功，批次 7，共 7 条记录
2025-06-18 10:32:43,647 - INFO - 成功插入的数据ID: ['FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMI', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMJ', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMK', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CML', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMM', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMN', 'FINST-X8D66N81DJEW1UO08NNGK7BI0F3T2VKD7C1CMO']
2025-06-18 10:32:48,663 - INFO - 批量插入完成，共 307 条记录
2025-06-18 10:32:48,663 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 307 条，错误: 0 条
2025-06-18 10:32:48,663 - INFO - 数据同步完成！更新: 0 条，插入: 307 条，错误: 0 条
2025-06-18 10:32:48,663 - INFO - 同步完成
2025-06-18 13:30:33,872 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 13:30:33,872 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 13:30:33,872 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 13:30:33,997 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 134 条记录
2025-06-18 13:30:33,997 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-17']
2025-06-18 13:30:34,012 - INFO - 开始处理日期: 2025-06-15
2025-06-18 13:30:34,012 - INFO - Request Parameters - Page 1:
2025-06-18 13:30:34,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:34,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:42,137 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 18415EEA-7623-7A5B-B7BA-48F374132350 Response: {'code': 'ServiceUnavailable', 'requestid': '18415EEA-7623-7A5B-B7BA-48F374132350', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 18415EEA-7623-7A5B-B7BA-48F374132350)
2025-06-18 13:30:42,137 - INFO - 开始处理日期: 2025-06-17
2025-06-18 13:30:42,137 - INFO - Request Parameters - Page 1:
2025-06-18 13:30:42,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:42,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:42,872 - INFO - Response - Page 1:
2025-06-18 13:30:42,872 - INFO - 第 1 页获取到 50 条记录
2025-06-18 13:30:43,387 - INFO - Request Parameters - Page 2:
2025-06-18 13:30:43,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:43,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:44,012 - INFO - Response - Page 2:
2025-06-18 13:30:44,012 - INFO - 第 2 页获取到 50 条记录
2025-06-18 13:30:44,528 - INFO - Request Parameters - Page 3:
2025-06-18 13:30:44,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:44,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:45,200 - INFO - Response - Page 3:
2025-06-18 13:30:45,200 - INFO - 第 3 页获取到 50 条记录
2025-06-18 13:30:45,715 - INFO - Request Parameters - Page 4:
2025-06-18 13:30:45,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:45,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:48,090 - INFO - Response - Page 4:
2025-06-18 13:30:48,090 - INFO - 第 4 页获取到 50 条记录
2025-06-18 13:30:48,590 - INFO - Request Parameters - Page 5:
2025-06-18 13:30:48,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:48,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:49,231 - INFO - Response - Page 5:
2025-06-18 13:30:49,231 - INFO - 第 5 页获取到 50 条记录
2025-06-18 13:30:49,731 - INFO - Request Parameters - Page 6:
2025-06-18 13:30:49,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:49,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:50,418 - INFO - Response - Page 6:
2025-06-18 13:30:50,418 - INFO - 第 6 页获取到 50 条记录
2025-06-18 13:30:50,918 - INFO - Request Parameters - Page 7:
2025-06-18 13:30:50,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:50,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:51,622 - INFO - Response - Page 7:
2025-06-18 13:30:51,622 - INFO - 第 7 页获取到 50 条记录
2025-06-18 13:30:52,137 - INFO - Request Parameters - Page 8:
2025-06-18 13:30:52,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:52,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:52,778 - INFO - Response - Page 8:
2025-06-18 13:30:52,778 - INFO - 第 8 页获取到 50 条记录
2025-06-18 13:30:53,293 - INFO - Request Parameters - Page 9:
2025-06-18 13:30:53,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:53,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:53,918 - INFO - Response - Page 9:
2025-06-18 13:30:53,918 - INFO - 第 9 页获取到 50 条记录
2025-06-18 13:30:54,434 - INFO - Request Parameters - Page 10:
2025-06-18 13:30:54,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:30:54,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:30:55,122 - INFO - Response - Page 10:
2025-06-18 13:30:55,122 - INFO - 第 10 页获取到 46 条记录
2025-06-18 13:30:55,637 - INFO - 查询完成，共获取到 496 条记录
2025-06-18 13:30:55,637 - INFO - 获取到 496 条表单数据
2025-06-18 13:30:55,637 - INFO - 当前日期 2025-06-17 有 130 条MySQL数据需要处理
2025-06-18 13:30:55,637 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMU4
2025-06-18 13:30:56,278 - INFO - 更新表单数据成功: FINST-7PF66MD1T2EWJWQ6FYTLRCITCVIB25FT6C1CMU4
2025-06-18 13:30:56,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9800.0, 'new_value': 39800.0}, {'field': 'total_amount', 'old_value': 9800.0, 'new_value': 39800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-18 13:30:56,278 - INFO - 开始批量插入 8 条新记录
2025-06-18 13:30:56,465 - INFO - 批量插入响应状态码: 200
2025-06-18 13:30:56,465 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 05:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4944E261-BD29-7317-A311-1FD0DACE02B2', 'x-acs-trace-id': 'ea0489c5c5493a34a6f025e14c9ec341', 'etag': '3BmZZnmosQ52h6wKR4/q9vQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 13:30:56,465 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CML8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMM8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMN8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMO8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMP8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMQ8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMR8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMS8']}
2025-06-18 13:30:56,465 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-06-18 13:30:56,465 - INFO - 成功插入的数据ID: ['FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CML8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMM8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMN8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMO8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMP8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMQ8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMR8', 'FINST-LLF66J7133EWQ5CJBW6UX6H0CX902N9KKI1CMS8']
2025-06-18 13:31:01,481 - INFO - 批量插入完成，共 8 条记录
2025-06-18 13:31:01,481 - INFO - 日期 2025-06-17 处理完成 - 更新: 1 条，插入: 8 条，错误: 0 条
2025-06-18 13:31:01,481 - INFO - 数据同步完成！更新: 1 条，插入: 8 条，错误: 1 条
2025-06-18 13:32:01,496 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 13:32:01,496 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 13:32:01,496 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 13:32:01,637 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 518 条记录
2025-06-18 13:32:01,637 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 13:32:01,637 - INFO - 开始处理日期: 2025-06-17
2025-06-18 13:32:01,637 - INFO - Request Parameters - Page 1:
2025-06-18 13:32:01,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:01,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:02,309 - INFO - Response - Page 1:
2025-06-18 13:32:02,309 - INFO - 第 1 页获取到 50 条记录
2025-06-18 13:32:02,809 - INFO - Request Parameters - Page 2:
2025-06-18 13:32:02,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:02,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:03,480 - INFO - Response - Page 2:
2025-06-18 13:32:03,480 - INFO - 第 2 页获取到 50 条记录
2025-06-18 13:32:03,996 - INFO - Request Parameters - Page 3:
2025-06-18 13:32:03,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:03,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:04,684 - INFO - Response - Page 3:
2025-06-18 13:32:04,684 - INFO - 第 3 页获取到 50 条记录
2025-06-18 13:32:05,199 - INFO - Request Parameters - Page 4:
2025-06-18 13:32:05,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:05,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:05,855 - INFO - Response - Page 4:
2025-06-18 13:32:05,855 - INFO - 第 4 页获取到 50 条记录
2025-06-18 13:32:06,371 - INFO - Request Parameters - Page 5:
2025-06-18 13:32:06,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:06,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:07,059 - INFO - Response - Page 5:
2025-06-18 13:32:07,059 - INFO - 第 5 页获取到 50 条记录
2025-06-18 13:32:07,574 - INFO - Request Parameters - Page 6:
2025-06-18 13:32:07,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:07,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:08,324 - INFO - Response - Page 6:
2025-06-18 13:32:08,324 - INFO - 第 6 页获取到 50 条记录
2025-06-18 13:32:08,824 - INFO - Request Parameters - Page 7:
2025-06-18 13:32:08,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:08,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:09,433 - INFO - Response - Page 7:
2025-06-18 13:32:09,433 - INFO - 第 7 页获取到 50 条记录
2025-06-18 13:32:09,933 - INFO - Request Parameters - Page 8:
2025-06-18 13:32:09,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:09,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:10,574 - INFO - Response - Page 8:
2025-06-18 13:32:10,590 - INFO - 第 8 页获取到 50 条记录
2025-06-18 13:32:11,090 - INFO - Request Parameters - Page 9:
2025-06-18 13:32:11,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:11,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:11,730 - INFO - Response - Page 9:
2025-06-18 13:32:11,730 - INFO - 第 9 页获取到 50 条记录
2025-06-18 13:32:12,246 - INFO - Request Parameters - Page 10:
2025-06-18 13:32:12,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:12,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:12,902 - INFO - Response - Page 10:
2025-06-18 13:32:12,902 - INFO - 第 10 页获取到 50 条记录
2025-06-18 13:32:13,402 - INFO - Request Parameters - Page 11:
2025-06-18 13:32:13,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 13:32:13,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 13:32:13,949 - INFO - Response - Page 11:
2025-06-18 13:32:13,949 - INFO - 第 11 页获取到 4 条记录
2025-06-18 13:32:14,465 - INFO - 查询完成，共获取到 504 条记录
2025-06-18 13:32:14,465 - INFO - 获取到 504 条表单数据
2025-06-18 13:32:14,465 - INFO - 当前日期 2025-06-17 有 505 条MySQL数据需要处理
2025-06-18 13:32:14,480 - INFO - 开始批量插入 1 条新记录
2025-06-18 13:32:14,637 - INFO - 批量插入响应状态码: 200
2025-06-18 13:32:14,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 05:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '793FF42C-F943-7D54-8288-46363C8E96C8', 'x-acs-trace-id': '7ce8e755f43236a9b4586d489bcb3f2d', 'etag': '6TMoUaQBhJ7Zvh3W8O0q7Nw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 13:32:14,637 - INFO - 批量插入响应体: {'result': ['FINST-X3766I91MHEWAO4LCTVUDA02VWTX21L8MI1CMN3']}
2025-06-18 13:32:14,637 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-18 13:32:14,637 - INFO - 成功插入的数据ID: ['FINST-X3766I91MHEWAO4LCTVUDA02VWTX21L8MI1CMN3']
2025-06-18 13:32:19,652 - INFO - 批量插入完成，共 1 条记录
2025-06-18 13:32:19,652 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-18 13:32:19,652 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-18 13:32:19,652 - INFO - 同步完成
2025-06-18 16:30:33,659 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 16:30:33,659 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 16:30:33,659 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 16:30:33,784 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 148 条记录
2025-06-18 16:30:33,784 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-17']
2025-06-18 16:30:33,784 - INFO - 开始处理日期: 2025-06-15
2025-06-18 16:30:33,800 - INFO - Request Parameters - Page 1:
2025-06-18 16:30:33,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:33,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:41,925 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2B08C485-270B-743C-9630-A4E60DA6F119 Response: {'code': 'ServiceUnavailable', 'requestid': '2B08C485-270B-743C-9630-A4E60DA6F119', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2B08C485-270B-743C-9630-A4E60DA6F119)
2025-06-18 16:30:41,925 - INFO - 开始处理日期: 2025-06-17
2025-06-18 16:30:41,925 - INFO - Request Parameters - Page 1:
2025-06-18 16:30:41,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:41,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:48,534 - INFO - Response - Page 1:
2025-06-18 16:30:48,534 - INFO - 第 1 页获取到 50 条记录
2025-06-18 16:30:49,050 - INFO - Request Parameters - Page 2:
2025-06-18 16:30:49,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:49,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:49,721 - INFO - Response - Page 2:
2025-06-18 16:30:49,721 - INFO - 第 2 页获取到 50 条记录
2025-06-18 16:30:50,237 - INFO - Request Parameters - Page 3:
2025-06-18 16:30:50,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:50,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:50,925 - INFO - Response - Page 3:
2025-06-18 16:30:50,925 - INFO - 第 3 页获取到 50 条记录
2025-06-18 16:30:51,425 - INFO - Request Parameters - Page 4:
2025-06-18 16:30:51,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:51,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:52,065 - INFO - Response - Page 4:
2025-06-18 16:30:52,065 - INFO - 第 4 页获取到 50 条记录
2025-06-18 16:30:52,581 - INFO - Request Parameters - Page 5:
2025-06-18 16:30:52,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:52,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:53,206 - INFO - Response - Page 5:
2025-06-18 16:30:53,206 - INFO - 第 5 页获取到 50 条记录
2025-06-18 16:30:53,721 - INFO - Request Parameters - Page 6:
2025-06-18 16:30:53,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:53,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:54,315 - INFO - Response - Page 6:
2025-06-18 16:30:54,315 - INFO - 第 6 页获取到 50 条记录
2025-06-18 16:30:54,831 - INFO - Request Parameters - Page 7:
2025-06-18 16:30:54,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:54,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:55,487 - INFO - Response - Page 7:
2025-06-18 16:30:55,487 - INFO - 第 7 页获取到 50 条记录
2025-06-18 16:30:55,987 - INFO - Request Parameters - Page 8:
2025-06-18 16:30:55,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:55,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:56,643 - INFO - Response - Page 8:
2025-06-18 16:30:56,643 - INFO - 第 8 页获取到 50 条记录
2025-06-18 16:30:57,159 - INFO - Request Parameters - Page 9:
2025-06-18 16:30:57,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:57,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:57,846 - INFO - Response - Page 9:
2025-06-18 16:30:57,846 - INFO - 第 9 页获取到 50 条记录
2025-06-18 16:30:58,362 - INFO - Request Parameters - Page 10:
2025-06-18 16:30:58,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:58,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:58,956 - INFO - Response - Page 10:
2025-06-18 16:30:58,956 - INFO - 第 10 页获取到 50 条记录
2025-06-18 16:30:59,471 - INFO - Request Parameters - Page 11:
2025-06-18 16:30:59,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:30:59,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:30:59,940 - INFO - Response - Page 11:
2025-06-18 16:30:59,940 - INFO - 第 11 页获取到 5 条记录
2025-06-18 16:31:00,456 - INFO - 查询完成，共获取到 505 条记录
2025-06-18 16:31:00,456 - INFO - 获取到 505 条表单数据
2025-06-18 16:31:00,456 - INFO - 当前日期 2025-06-17 有 143 条MySQL数据需要处理
2025-06-18 16:31:00,456 - INFO - 开始批量插入 13 条新记录
2025-06-18 16:31:00,628 - INFO - 批量插入响应状态码: 200
2025-06-18 16:31:00,628 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 08:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '623', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0F0A0769-1E86-763F-A660-95C5F50ED73C', 'x-acs-trace-id': '53bcadfcdd973cff86ad7b51570983ee', 'etag': '6h2xoPSiX6kovknkQRUPYyQ3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 16:31:00,628 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMB', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMC', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMD', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CME', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMF', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMG', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMH', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMI', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMJ', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMK', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CML', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMM', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMN']}
2025-06-18 16:31:00,628 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-06-18 16:31:00,628 - INFO - 成功插入的数据ID: ['FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMB', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMC', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMD', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CME', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMF', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMG', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMH', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMI', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMJ', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMK', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CML', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMM', 'FINST-1MD668B1JREWO9U669H3YCDP8A0Q28V40P1CMN']
2025-06-18 16:31:05,643 - INFO - 批量插入完成，共 13 条记录
2025-06-18 16:31:05,643 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-06-18 16:31:05,643 - INFO - 数据同步完成！更新: 0 条，插入: 13 条，错误: 1 条
2025-06-18 16:32:05,658 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 16:32:05,658 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 16:32:05,658 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 16:32:05,799 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 551 条记录
2025-06-18 16:32:05,799 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 16:32:05,799 - INFO - 开始处理日期: 2025-06-17
2025-06-18 16:32:05,799 - INFO - Request Parameters - Page 1:
2025-06-18 16:32:05,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:05,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:06,502 - INFO - Response - Page 1:
2025-06-18 16:32:06,502 - INFO - 第 1 页获取到 50 条记录
2025-06-18 16:32:07,002 - INFO - Request Parameters - Page 2:
2025-06-18 16:32:07,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:07,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:07,658 - INFO - Response - Page 2:
2025-06-18 16:32:07,658 - INFO - 第 2 页获取到 50 条记录
2025-06-18 16:32:08,158 - INFO - Request Parameters - Page 3:
2025-06-18 16:32:08,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:08,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:08,846 - INFO - Response - Page 3:
2025-06-18 16:32:08,846 - INFO - 第 3 页获取到 50 条记录
2025-06-18 16:32:09,362 - INFO - Request Parameters - Page 4:
2025-06-18 16:32:09,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:09,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:10,049 - INFO - Response - Page 4:
2025-06-18 16:32:10,049 - INFO - 第 4 页获取到 50 条记录
2025-06-18 16:32:10,549 - INFO - Request Parameters - Page 5:
2025-06-18 16:32:10,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:10,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:11,174 - INFO - Response - Page 5:
2025-06-18 16:32:11,174 - INFO - 第 5 页获取到 50 条记录
2025-06-18 16:32:11,690 - INFO - Request Parameters - Page 6:
2025-06-18 16:32:11,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:11,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:12,424 - INFO - Response - Page 6:
2025-06-18 16:32:12,424 - INFO - 第 6 页获取到 50 条记录
2025-06-18 16:32:12,924 - INFO - Request Parameters - Page 7:
2025-06-18 16:32:12,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:12,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:13,752 - INFO - Response - Page 7:
2025-06-18 16:32:13,752 - INFO - 第 7 页获取到 50 条记录
2025-06-18 16:32:14,268 - INFO - Request Parameters - Page 8:
2025-06-18 16:32:14,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:14,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:14,893 - INFO - Response - Page 8:
2025-06-18 16:32:14,893 - INFO - 第 8 页获取到 50 条记录
2025-06-18 16:32:15,393 - INFO - Request Parameters - Page 9:
2025-06-18 16:32:15,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:15,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:16,565 - INFO - Response - Page 9:
2025-06-18 16:32:16,565 - INFO - 第 9 页获取到 50 条记录
2025-06-18 16:32:17,065 - INFO - Request Parameters - Page 10:
2025-06-18 16:32:17,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:17,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:17,783 - INFO - Response - Page 10:
2025-06-18 16:32:17,783 - INFO - 第 10 页获取到 50 条记录
2025-06-18 16:32:18,283 - INFO - Request Parameters - Page 11:
2025-06-18 16:32:18,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 16:32:18,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 16:32:18,893 - INFO - Response - Page 11:
2025-06-18 16:32:18,893 - INFO - 第 11 页获取到 18 条记录
2025-06-18 16:32:19,408 - INFO - 查询完成，共获取到 518 条记录
2025-06-18 16:32:19,408 - INFO - 获取到 518 条表单数据
2025-06-18 16:32:19,408 - INFO - 当前日期 2025-06-17 有 537 条MySQL数据需要处理
2025-06-18 16:32:19,424 - INFO - 开始批量插入 19 条新记录
2025-06-18 16:32:19,627 - INFO - 批量插入响应状态码: 200
2025-06-18 16:32:19,627 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 08:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8D20F275-560B-7B8A-9E94-64CF2BCA854B', 'x-acs-trace-id': '99bd00fe96148e0d380bccf6f30c68a9', 'etag': '9Oda5+2UCDG5ge5ql8U+AoQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 16:32:19,627 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA113EWNVHV9J87BCOTT73L3UTT1P1CMPA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMQA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMRA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMSA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMTA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMUA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMVA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMWA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMXA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMYA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMZA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM0B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM1B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM2B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM3B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM4B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM5B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM6B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM7B']}
2025-06-18 16:32:19,627 - INFO - 批量插入表单数据成功，批次 1，共 19 条记录
2025-06-18 16:32:19,627 - INFO - 成功插入的数据ID: ['FINST-OIF66BA113EWNVHV9J87BCOTT73L3UTT1P1CMPA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMQA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMRA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMSA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMTA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMUA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMVA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMWA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMXA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMYA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CMZA', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM0B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM1B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM2B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM3B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM4B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM5B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM6B', 'FINST-OIF66BA113EWNVHV9J87BCOTT73L3VTT1P1CM7B']
2025-06-18 16:32:24,643 - INFO - 批量插入完成，共 19 条记录
2025-06-18 16:32:24,643 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 19 条，错误: 0 条
2025-06-18 16:32:24,643 - INFO - 数据同步完成！更新: 0 条，插入: 19 条，错误: 0 条
2025-06-18 16:32:24,643 - INFO - 同步完成
2025-06-18 19:30:34,524 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 19:30:34,524 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 19:30:34,524 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 19:30:34,665 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 149 条记录
2025-06-18 19:30:34,665 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-17']
2025-06-18 19:30:34,665 - INFO - 开始处理日期: 2025-06-15
2025-06-18 19:30:34,665 - INFO - Request Parameters - Page 1:
2025-06-18 19:30:34,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:34,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:42,775 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 795F7AD8-BF03-7509-A91E-E39E7E0AD222 Response: {'code': 'ServiceUnavailable', 'requestid': '795F7AD8-BF03-7509-A91E-E39E7E0AD222', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 795F7AD8-BF03-7509-A91E-E39E7E0AD222)
2025-06-18 19:30:42,775 - INFO - 开始处理日期: 2025-06-17
2025-06-18 19:30:42,775 - INFO - Request Parameters - Page 1:
2025-06-18 19:30:42,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:42,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:48,057 - INFO - Response - Page 1:
2025-06-18 19:30:48,057 - INFO - 第 1 页获取到 50 条记录
2025-06-18 19:30:48,557 - INFO - Request Parameters - Page 2:
2025-06-18 19:30:48,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:48,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:49,229 - INFO - Response - Page 2:
2025-06-18 19:30:49,229 - INFO - 第 2 页获取到 50 条记录
2025-06-18 19:30:49,745 - INFO - Request Parameters - Page 3:
2025-06-18 19:30:49,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:49,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:50,448 - INFO - Response - Page 3:
2025-06-18 19:30:50,448 - INFO - 第 3 页获取到 50 条记录
2025-06-18 19:30:50,964 - INFO - Request Parameters - Page 4:
2025-06-18 19:30:50,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:50,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:51,605 - INFO - Response - Page 4:
2025-06-18 19:30:51,605 - INFO - 第 4 页获取到 50 条记录
2025-06-18 19:30:52,105 - INFO - Request Parameters - Page 5:
2025-06-18 19:30:52,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:52,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:52,855 - INFO - Response - Page 5:
2025-06-18 19:30:52,855 - INFO - 第 5 页获取到 50 条记录
2025-06-18 19:30:53,370 - INFO - Request Parameters - Page 6:
2025-06-18 19:30:53,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:53,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:54,027 - INFO - Response - Page 6:
2025-06-18 19:30:54,027 - INFO - 第 6 页获取到 50 条记录
2025-06-18 19:30:54,527 - INFO - Request Parameters - Page 7:
2025-06-18 19:30:54,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:54,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:55,261 - INFO - Response - Page 7:
2025-06-18 19:30:55,261 - INFO - 第 7 页获取到 50 条记录
2025-06-18 19:30:55,777 - INFO - Request Parameters - Page 8:
2025-06-18 19:30:55,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:55,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:56,386 - INFO - Response - Page 8:
2025-06-18 19:30:56,386 - INFO - 第 8 页获取到 50 条记录
2025-06-18 19:30:56,887 - INFO - Request Parameters - Page 9:
2025-06-18 19:30:56,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:56,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:57,558 - INFO - Response - Page 9:
2025-06-18 19:30:57,558 - INFO - 第 9 页获取到 50 条记录
2025-06-18 19:30:58,059 - INFO - Request Parameters - Page 10:
2025-06-18 19:30:58,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:58,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:58,715 - INFO - Response - Page 10:
2025-06-18 19:30:58,715 - INFO - 第 10 页获取到 50 条记录
2025-06-18 19:30:59,215 - INFO - Request Parameters - Page 11:
2025-06-18 19:30:59,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:30:59,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:30:59,824 - INFO - Response - Page 11:
2025-06-18 19:30:59,824 - INFO - 第 11 页获取到 37 条记录
2025-06-18 19:31:00,324 - INFO - 查询完成，共获取到 537 条记录
2025-06-18 19:31:00,324 - INFO - 获取到 537 条表单数据
2025-06-18 19:31:00,324 - INFO - 当前日期 2025-06-17 有 144 条MySQL数据需要处理
2025-06-18 19:31:00,324 - INFO - 开始更新记录 - 表单实例ID: FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM71
2025-06-18 19:31:00,793 - INFO - 更新表单数据成功: FINST-SI766181A3EW1B9NBFQN78EDYYDR3HEP6C1CM71
2025-06-18 19:31:00,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93450.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 93450.0, 'new_value': 0.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-18 19:31:00,793 - INFO - 日期 2025-06-17 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-18 19:31:00,793 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 1 条
2025-06-18 19:32:00,817 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 19:32:00,817 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 19:32:00,817 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 19:32:00,957 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 551 条记录
2025-06-18 19:32:00,957 - INFO - 获取到 1 个日期需要处理: ['2025-06-17']
2025-06-18 19:32:00,957 - INFO - 开始处理日期: 2025-06-17
2025-06-18 19:32:00,957 - INFO - Request Parameters - Page 1:
2025-06-18 19:32:00,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:00,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:01,614 - INFO - Response - Page 1:
2025-06-18 19:32:01,614 - INFO - 第 1 页获取到 50 条记录
2025-06-18 19:32:02,130 - INFO - Request Parameters - Page 2:
2025-06-18 19:32:02,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:02,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:02,880 - INFO - Response - Page 2:
2025-06-18 19:32:02,880 - INFO - 第 2 页获取到 50 条记录
2025-06-18 19:32:03,395 - INFO - Request Parameters - Page 3:
2025-06-18 19:32:03,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:03,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:04,020 - INFO - Response - Page 3:
2025-06-18 19:32:04,020 - INFO - 第 3 页获取到 50 条记录
2025-06-18 19:32:04,520 - INFO - Request Parameters - Page 4:
2025-06-18 19:32:04,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:04,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:05,130 - INFO - Response - Page 4:
2025-06-18 19:32:05,130 - INFO - 第 4 页获取到 50 条记录
2025-06-18 19:32:05,646 - INFO - Request Parameters - Page 5:
2025-06-18 19:32:05,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:05,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:06,349 - INFO - Response - Page 5:
2025-06-18 19:32:06,349 - INFO - 第 5 页获取到 50 条记录
2025-06-18 19:32:06,864 - INFO - Request Parameters - Page 6:
2025-06-18 19:32:06,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:06,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:07,552 - INFO - Response - Page 6:
2025-06-18 19:32:07,552 - INFO - 第 6 页获取到 50 条记录
2025-06-18 19:32:08,068 - INFO - Request Parameters - Page 7:
2025-06-18 19:32:08,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:08,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:08,709 - INFO - Response - Page 7:
2025-06-18 19:32:08,709 - INFO - 第 7 页获取到 50 条记录
2025-06-18 19:32:09,224 - INFO - Request Parameters - Page 8:
2025-06-18 19:32:09,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:09,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:09,849 - INFO - Response - Page 8:
2025-06-18 19:32:09,849 - INFO - 第 8 页获取到 50 条记录
2025-06-18 19:32:10,349 - INFO - Request Parameters - Page 9:
2025-06-18 19:32:10,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:10,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:10,943 - INFO - Response - Page 9:
2025-06-18 19:32:10,943 - INFO - 第 9 页获取到 50 条记录
2025-06-18 19:32:11,459 - INFO - Request Parameters - Page 10:
2025-06-18 19:32:11,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:11,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:12,100 - INFO - Response - Page 10:
2025-06-18 19:32:12,100 - INFO - 第 10 页获取到 50 条记录
2025-06-18 19:32:12,600 - INFO - Request Parameters - Page 11:
2025-06-18 19:32:12,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 19:32:12,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 19:32:13,225 - INFO - Response - Page 11:
2025-06-18 19:32:13,225 - INFO - 第 11 页获取到 37 条记录
2025-06-18 19:32:13,725 - INFO - 查询完成，共获取到 537 条记录
2025-06-18 19:32:13,725 - INFO - 获取到 537 条表单数据
2025-06-18 19:32:13,725 - INFO - 当前日期 2025-06-17 有 537 条MySQL数据需要处理
2025-06-18 19:32:13,740 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 19:32:13,740 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 19:32:13,740 - INFO - 同步完成
2025-06-18 22:30:34,403 - INFO - 使用默认增量同步（当天更新数据）
2025-06-18 22:30:34,403 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-18 22:30:34,403 - INFO - 查询参数: ('2025-06-18',)
2025-06-18 22:30:34,543 - INFO - MySQL查询成功，增量数据（日期: 2025-06-18），共获取 197 条记录
2025-06-18 22:30:34,543 - INFO - 获取到 3 个日期需要处理: ['2025-06-15', '2025-06-17', '2025-06-18']
2025-06-18 22:30:34,543 - INFO - 开始处理日期: 2025-06-15
2025-06-18 22:30:34,543 - INFO - Request Parameters - Page 1:
2025-06-18 22:30:34,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:34,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:40,747 - INFO - Response - Page 1:
2025-06-18 22:30:40,747 - INFO - 第 1 页获取到 50 条记录
2025-06-18 22:30:41,263 - INFO - Request Parameters - Page 2:
2025-06-18 22:30:41,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:41,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:49,358 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 233EA2E6-28C9-7334-AEF2-B9D170E7AC7A Response: {'code': 'ServiceUnavailable', 'requestid': '233EA2E6-28C9-7334-AEF2-B9D170E7AC7A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 233EA2E6-28C9-7334-AEF2-B9D170E7AC7A)
2025-06-18 22:30:49,358 - INFO - 开始处理日期: 2025-06-17
2025-06-18 22:30:49,358 - INFO - Request Parameters - Page 1:
2025-06-18 22:30:49,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:49,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:50,014 - INFO - Response - Page 1:
2025-06-18 22:30:50,014 - INFO - 第 1 页获取到 50 条记录
2025-06-18 22:30:50,530 - INFO - Request Parameters - Page 2:
2025-06-18 22:30:50,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:50,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:51,233 - INFO - Response - Page 2:
2025-06-18 22:30:51,233 - INFO - 第 2 页获取到 50 条记录
2025-06-18 22:30:51,733 - INFO - Request Parameters - Page 3:
2025-06-18 22:30:51,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:51,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:52,343 - INFO - Response - Page 3:
2025-06-18 22:30:52,343 - INFO - 第 3 页获取到 50 条记录
2025-06-18 22:30:52,843 - INFO - Request Parameters - Page 4:
2025-06-18 22:30:52,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:52,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:53,452 - INFO - Response - Page 4:
2025-06-18 22:30:53,452 - INFO - 第 4 页获取到 50 条记录
2025-06-18 22:30:53,968 - INFO - Request Parameters - Page 5:
2025-06-18 22:30:53,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:53,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:54,577 - INFO - Response - Page 5:
2025-06-18 22:30:54,577 - INFO - 第 5 页获取到 50 条记录
2025-06-18 22:30:55,077 - INFO - Request Parameters - Page 6:
2025-06-18 22:30:55,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:55,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:55,765 - INFO - Response - Page 6:
2025-06-18 22:30:55,765 - INFO - 第 6 页获取到 50 条记录
2025-06-18 22:30:56,281 - INFO - Request Parameters - Page 7:
2025-06-18 22:30:56,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:56,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:56,968 - INFO - Response - Page 7:
2025-06-18 22:30:56,968 - INFO - 第 7 页获取到 50 条记录
2025-06-18 22:30:57,468 - INFO - Request Parameters - Page 8:
2025-06-18 22:30:57,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:57,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:58,172 - INFO - Response - Page 8:
2025-06-18 22:30:58,172 - INFO - 第 8 页获取到 50 条记录
2025-06-18 22:30:58,687 - INFO - Request Parameters - Page 9:
2025-06-18 22:30:58,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:58,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:30:59,344 - INFO - Response - Page 9:
2025-06-18 22:30:59,344 - INFO - 第 9 页获取到 50 条记录
2025-06-18 22:30:59,844 - INFO - Request Parameters - Page 10:
2025-06-18 22:30:59,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:30:59,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:31:00,500 - INFO - Response - Page 10:
2025-06-18 22:31:00,500 - INFO - 第 10 页获取到 50 条记录
2025-06-18 22:31:01,016 - INFO - Request Parameters - Page 11:
2025-06-18 22:31:01,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:31:01,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:31:01,594 - INFO - Response - Page 11:
2025-06-18 22:31:01,594 - INFO - 第 11 页获取到 37 条记录
2025-06-18 22:31:02,110 - INFO - 查询完成，共获取到 537 条记录
2025-06-18 22:31:02,110 - INFO - 获取到 537 条表单数据
2025-06-18 22:31:02,110 - INFO - 当前日期 2025-06-17 有 144 条MySQL数据需要处理
2025-06-18 22:31:02,110 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 22:31:02,110 - INFO - 开始处理日期: 2025-06-18
2025-06-18 22:31:02,110 - INFO - Request Parameters - Page 1:
2025-06-18 22:31:02,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:31:02,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:31:02,531 - INFO - Response - Page 1:
2025-06-18 22:31:02,531 - INFO - 查询完成，共获取到 0 条记录
2025-06-18 22:31:02,531 - INFO - 获取到 0 条表单数据
2025-06-18 22:31:02,531 - INFO - 当前日期 2025-06-18 有 46 条MySQL数据需要处理
2025-06-18 22:31:02,531 - INFO - 开始批量插入 46 条新记录
2025-06-18 22:31:02,766 - INFO - 批量插入响应状态码: 200
2025-06-18 22:31:02,766 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 18 Jun 2025 14:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2220', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B8ECD177-CA59-7694-BF70-79704CA11165', 'x-acs-trace-id': '01aa92bd40761d7e8e364b17fb4159e5', 'etag': '2sAG4JFTPAX4UyAoWs4Ratg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-18 22:31:02,766 - INFO - 批量插入响应体: {'result': ['FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMG6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMH6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMI6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMJ6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMK6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CML6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMM6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMN6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMO6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMP6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMQ6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMR6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMS6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMT6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMU6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMV6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMW6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMX6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMY6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMZ6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM07', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM17', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM27', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM37', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM47', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM57', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM67', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM77', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM87', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM97', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMA7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMB7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMC7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMD7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CME7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMF7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMG7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMH7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMI7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMJ7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMK7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CML7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMM7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMN7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMO7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMP7']}
2025-06-18 22:31:02,766 - INFO - 批量插入表单数据成功，批次 1，共 46 条记录
2025-06-18 22:31:02,766 - INFO - 成功插入的数据ID: ['FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMG6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMH6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMI6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMJ6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMK6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CML6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMM6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMN6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMO6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMP6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMQ6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMR6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMS6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMT6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMU6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMV6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMW6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMX6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMY6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMZ6', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM07', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM17', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM27', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM37', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM47', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM57', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM67', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM77', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM87', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CM97', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMA7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMB7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMC7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMD7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CME7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMF7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMG7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMH7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMI7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMJ7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMK7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CML7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMM7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMN7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMO7', 'FINST-NWE664C1IHEWBW8QEXU5XD913WBV3CK3V12CMP7']
2025-06-18 22:31:07,782 - INFO - 批量插入完成，共 46 条记录
2025-06-18 22:31:07,782 - INFO - 日期 2025-06-18 处理完成 - 更新: 0 条，插入: 46 条，错误: 0 条
2025-06-18 22:31:07,782 - INFO - 数据同步完成！更新: 0 条，插入: 46 条，错误: 1 条
2025-06-18 22:32:07,805 - INFO - 开始同步昨天与今天的销售数据: 2025-06-17 至 2025-06-18
2025-06-18 22:32:07,805 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-18 22:32:07,805 - INFO - 查询参数: ('2025-06-17', '2025-06-18')
2025-06-18 22:32:07,946 - INFO - MySQL查询成功，时间段: 2025-06-17 至 2025-06-18，共获取 599 条记录
2025-06-18 22:32:07,946 - INFO - 获取到 2 个日期需要处理: ['2025-06-17', '2025-06-18']
2025-06-18 22:32:07,946 - INFO - 开始处理日期: 2025-06-17
2025-06-18 22:32:07,946 - INFO - Request Parameters - Page 1:
2025-06-18 22:32:07,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:07,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:08,821 - INFO - Response - Page 1:
2025-06-18 22:32:08,821 - INFO - 第 1 页获取到 50 条记录
2025-06-18 22:32:09,321 - INFO - Request Parameters - Page 2:
2025-06-18 22:32:09,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:09,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:09,931 - INFO - Response - Page 2:
2025-06-18 22:32:09,931 - INFO - 第 2 页获取到 50 条记录
2025-06-18 22:32:10,446 - INFO - Request Parameters - Page 3:
2025-06-18 22:32:10,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:10,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:11,087 - INFO - Response - Page 3:
2025-06-18 22:32:11,087 - INFO - 第 3 页获取到 50 条记录
2025-06-18 22:32:11,603 - INFO - Request Parameters - Page 4:
2025-06-18 22:32:11,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:11,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:12,290 - INFO - Response - Page 4:
2025-06-18 22:32:12,290 - INFO - 第 4 页获取到 50 条记录
2025-06-18 22:32:12,806 - INFO - Request Parameters - Page 5:
2025-06-18 22:32:12,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:12,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:13,415 - INFO - Response - Page 5:
2025-06-18 22:32:13,415 - INFO - 第 5 页获取到 50 条记录
2025-06-18 22:32:13,916 - INFO - Request Parameters - Page 6:
2025-06-18 22:32:13,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:13,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:14,525 - INFO - Response - Page 6:
2025-06-18 22:32:14,525 - INFO - 第 6 页获取到 50 条记录
2025-06-18 22:32:15,041 - INFO - Request Parameters - Page 7:
2025-06-18 22:32:15,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:15,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:15,744 - INFO - Response - Page 7:
2025-06-18 22:32:15,744 - INFO - 第 7 页获取到 50 条记录
2025-06-18 22:32:16,260 - INFO - Request Parameters - Page 8:
2025-06-18 22:32:16,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:16,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:16,963 - INFO - Response - Page 8:
2025-06-18 22:32:16,963 - INFO - 第 8 页获取到 50 条记录
2025-06-18 22:32:17,478 - INFO - Request Parameters - Page 9:
2025-06-18 22:32:17,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:17,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:18,119 - INFO - Response - Page 9:
2025-06-18 22:32:18,119 - INFO - 第 9 页获取到 50 条记录
2025-06-18 22:32:18,619 - INFO - Request Parameters - Page 10:
2025-06-18 22:32:18,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:18,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:19,276 - INFO - Response - Page 10:
2025-06-18 22:32:19,276 - INFO - 第 10 页获取到 50 条记录
2025-06-18 22:32:19,776 - INFO - Request Parameters - Page 11:
2025-06-18 22:32:19,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:19,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:20,385 - INFO - Response - Page 11:
2025-06-18 22:32:20,385 - INFO - 第 11 页获取到 37 条记录
2025-06-18 22:32:20,901 - INFO - 查询完成，共获取到 537 条记录
2025-06-18 22:32:20,901 - INFO - 获取到 537 条表单数据
2025-06-18 22:32:20,901 - INFO - 当前日期 2025-06-17 有 537 条MySQL数据需要处理
2025-06-18 22:32:20,916 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 22:32:20,916 - INFO - 开始处理日期: 2025-06-18
2025-06-18 22:32:20,916 - INFO - Request Parameters - Page 1:
2025-06-18 22:32:20,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-18 22:32:20,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-18 22:32:21,588 - INFO - Response - Page 1:
2025-06-18 22:32:21,588 - INFO - 第 1 页获取到 46 条记录
2025-06-18 22:32:22,104 - INFO - 查询完成，共获取到 46 条记录
2025-06-18 22:32:22,104 - INFO - 获取到 46 条表单数据
2025-06-18 22:32:22,104 - INFO - 当前日期 2025-06-18 有 46 条MySQL数据需要处理
2025-06-18 22:32:22,104 - INFO - 日期 2025-06-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 22:32:22,104 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-18 22:32:22,104 - INFO - 同步完成
