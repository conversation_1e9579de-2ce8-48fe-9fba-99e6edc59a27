2025-04-28 00:30:34,164 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 00:30:34,164 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 00:30:34,164 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 00:30:34,211 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 0 条记录
2025-04-28 00:30:34,211 - ERROR - 未获取到MySQL数据
2025-04-28 00:31:34,279 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 00:31:34,279 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 00:31:34,279 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 00:31:34,326 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 00:31:34,326 - ERROR - 未获取到MySQL数据
2025-04-28 00:31:34,326 - INFO - 同步完成
2025-04-28 01:30:34,304 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 01:30:34,304 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 01:30:34,304 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 01:30:34,350 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 1 条记录
2025-04-28 01:30:34,350 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 01:30:34,350 - INFO - 开始处理日期: 2025-04-27
2025-04-28 01:30:34,350 - INFO - Request Parameters - Page 1:
2025-04-28 01:30:34,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 01:30:34,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 01:30:42,467 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9D992C9A-0A25-701A-B13C-CC7C30409394 Response: {'code': 'ServiceUnavailable', 'requestid': '9D992C9A-0A25-701A-B13C-CC7C30409394', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9D992C9A-0A25-701A-B13C-CC7C30409394)
2025-04-28 01:30:42,467 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 01:31:42,535 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 01:31:42,535 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 01:31:42,535 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 01:31:42,582 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 01:31:42,582 - ERROR - 未获取到MySQL数据
2025-04-28 01:31:42,582 - INFO - 同步完成
2025-04-28 02:30:34,271 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 02:30:34,271 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 02:30:34,271 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 02:30:34,334 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 1 条记录
2025-04-28 02:30:34,334 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 02:30:34,334 - INFO - 开始处理日期: 2025-04-27
2025-04-28 02:30:34,334 - INFO - Request Parameters - Page 1:
2025-04-28 02:30:34,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 02:30:34,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 02:30:42,450 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6B7EB4EA-DFC0-78D5-81CD-0201D6F58BCB Response: {'code': 'ServiceUnavailable', 'requestid': '6B7EB4EA-DFC0-78D5-81CD-0201D6F58BCB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6B7EB4EA-DFC0-78D5-81CD-0201D6F58BCB)
2025-04-28 02:30:42,450 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 02:31:42,519 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 02:31:42,519 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 02:31:42,519 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 02:31:42,565 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 02:31:42,565 - ERROR - 未获取到MySQL数据
2025-04-28 02:31:42,565 - INFO - 同步完成
2025-04-28 03:30:34,285 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 03:30:34,285 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 03:30:34,285 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 03:30:34,332 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 1 条记录
2025-04-28 03:30:34,332 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 03:30:34,332 - INFO - 开始处理日期: 2025-04-27
2025-04-28 03:30:34,332 - INFO - Request Parameters - Page 1:
2025-04-28 03:30:34,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:30:34,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:30:42,480 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 98922AFC-80A1-7EEB-A9F7-E31F7D275439 Response: {'code': 'ServiceUnavailable', 'requestid': '98922AFC-80A1-7EEB-A9F7-E31F7D275439', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 98922AFC-80A1-7EEB-A9F7-E31F7D275439)
2025-04-28 03:30:42,480 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 03:31:42,548 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 03:31:42,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 03:31:42,548 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 03:31:42,595 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 03:31:42,595 - ERROR - 未获取到MySQL数据
2025-04-28 03:31:42,595 - INFO - 同步完成
2025-04-28 04:30:34,346 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 04:30:34,346 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 04:30:34,346 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 04:30:34,393 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 1 条记录
2025-04-28 04:30:34,409 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 04:30:34,409 - INFO - 开始处理日期: 2025-04-27
2025-04-28 04:30:34,409 - INFO - Request Parameters - Page 1:
2025-04-28 04:30:34,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 04:30:34,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 04:30:42,525 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A4F1D643-C1F6-7537-AFE4-E34C984EE322 Response: {'code': 'ServiceUnavailable', 'requestid': 'A4F1D643-C1F6-7537-AFE4-E34C984EE322', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A4F1D643-C1F6-7537-AFE4-E34C984EE322)
2025-04-28 04:30:42,525 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 04:31:42,593 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 04:31:42,593 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 04:31:42,593 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 04:31:42,640 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 04:31:42,640 - ERROR - 未获取到MySQL数据
2025-04-28 04:31:42,640 - INFO - 同步完成
2025-04-28 05:30:34,309 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 05:30:34,309 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 05:30:34,309 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 05:30:34,356 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 1 条记录
2025-04-28 05:30:34,356 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 05:30:34,356 - INFO - 开始处理日期: 2025-04-27
2025-04-28 05:30:34,372 - INFO - Request Parameters - Page 1:
2025-04-28 05:30:34,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 05:30:34,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 05:30:42,504 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9A74B3ED-E798-764A-985B-B79EC2A5ABBC Response: {'code': 'ServiceUnavailable', 'requestid': '9A74B3ED-E798-764A-985B-B79EC2A5ABBC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9A74B3ED-E798-764A-985B-B79EC2A5ABBC)
2025-04-28 05:30:42,504 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 05:31:42,572 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 05:31:42,572 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 05:31:42,572 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 05:31:42,619 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 05:31:42,619 - ERROR - 未获取到MySQL数据
2025-04-28 05:31:42,619 - INFO - 同步完成
2025-04-28 06:30:33,875 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 06:30:33,875 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 06:30:33,875 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 06:30:33,922 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 1 条记录
2025-04-28 06:30:33,922 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 06:30:33,922 - INFO - 开始处理日期: 2025-04-27
2025-04-28 06:30:33,922 - INFO - Request Parameters - Page 1:
2025-04-28 06:30:33,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:30:33,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:30:42,047 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6D3F424B-A7A1-7726-BA99-58EA9D15E8B4 Response: {'code': 'ServiceUnavailable', 'requestid': '6D3F424B-A7A1-7726-BA99-58EA9D15E8B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6D3F424B-A7A1-7726-BA99-58EA9D15E8B4)
2025-04-28 06:30:42,047 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 06:31:42,062 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 06:31:42,062 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 06:31:42,062 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 06:31:42,109 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 06:31:42,109 - ERROR - 未获取到MySQL数据
2025-04-28 06:31:42,109 - INFO - 同步完成
2025-04-28 07:30:33,743 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 07:30:33,743 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 07:30:33,743 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 07:30:33,790 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 3 条记录
2025-04-28 07:30:33,805 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 07:30:33,805 - INFO - 开始处理日期: 2025-04-27
2025-04-28 07:30:33,805 - INFO - Request Parameters - Page 1:
2025-04-28 07:30:33,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 07:30:33,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 07:30:41,930 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 18CDFB6A-6021-7D51-BD36-7407B868F249 Response: {'code': 'ServiceUnavailable', 'requestid': '18CDFB6A-6021-7D51-BD36-7407B868F249', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 18CDFB6A-6021-7D51-BD36-7407B868F249)
2025-04-28 07:30:41,930 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 07:31:41,945 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 07:31:41,945 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 07:31:41,945 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 07:31:41,992 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 07:31:41,992 - ERROR - 未获取到MySQL数据
2025-04-28 07:31:41,992 - INFO - 同步完成
2025-04-28 08:30:33,532 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 08:30:33,532 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 08:30:33,532 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 08:30:33,595 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 18 条记录
2025-04-28 08:30:33,595 - INFO - 获取到 1 个日期需要处理: ['2025-04-27']
2025-04-28 08:30:33,595 - INFO - 开始处理日期: 2025-04-27
2025-04-28 08:30:33,595 - INFO - Request Parameters - Page 1:
2025-04-28 08:30:33,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 08:30:33,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 08:30:41,720 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 90499C00-906A-70FD-9838-ECF9B453246D Response: {'code': 'ServiceUnavailable', 'requestid': '90499C00-906A-70FD-9838-ECF9B453246D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 90499C00-906A-70FD-9838-ECF9B453246D)
2025-04-28 08:30:41,720 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-28 08:31:41,735 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 08:31:41,735 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 08:31:41,735 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 08:31:41,782 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 08:31:41,782 - ERROR - 未获取到MySQL数据
2025-04-28 08:31:41,782 - INFO - 同步完成
2025-04-28 09:30:33,713 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 09:30:33,713 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 09:30:33,713 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 09:30:33,775 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 93 条记录
2025-04-28 09:30:33,775 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-28 09:30:33,775 - INFO - 开始处理日期: 2025-04-27
2025-04-28 09:30:33,775 - INFO - Request Parameters - Page 1:
2025-04-28 09:30:33,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:30:33,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:30:41,916 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2C967A66-6459-770C-BBC4-AADB1F808579 Response: {'code': 'ServiceUnavailable', 'requestid': '2C967A66-6459-770C-BBC4-AADB1F808579', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2C967A66-6459-770C-BBC4-AADB1F808579)
2025-04-28 09:30:41,916 - INFO - 开始处理日期: 2025-04-28
2025-04-28 09:30:41,916 - INFO - Request Parameters - Page 1:
2025-04-28 09:30:41,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:30:41,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:30:42,775 - INFO - Response - Page 1:
2025-04-28 09:30:42,775 - INFO - 查询完成，共获取到 0 条记录
2025-04-28 09:30:42,775 - INFO - 获取到 0 条表单数据
2025-04-28 09:30:42,775 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 09:30:42,775 - INFO - 开始批量插入 1 条新记录
2025-04-28 09:30:42,931 - INFO - 批量插入响应状态码: 200
2025-04-28 09:30:42,931 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 01:30:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8B8CC74D-5D72-7EFE-8EC2-2E77DCBCC828', 'x-acs-trace-id': '0a49f77a972d3de5cc4c665f1008801e', 'etag': '5wBmfluj4d60vZodL/RyVHg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 09:30:42,931 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B814OYUMMIQ8S43MAJBNT8N3CNRIE0AM0']}
2025-04-28 09:30:42,931 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 09:30:42,931 - INFO - 成功插入的数据ID: ['FINST-WBF66B814OYUMMIQ8S43MAJBNT8N3CNRIE0AM0']
2025-04-28 09:30:47,947 - INFO - 批量插入完成，共 1 条记录
2025-04-28 09:30:47,947 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 09:30:47,947 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-04-28 09:31:47,962 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 09:31:47,962 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 09:31:47,962 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 09:31:48,009 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 09:31:48,009 - ERROR - 未获取到MySQL数据
2025-04-28 09:31:48,009 - INFO - 同步完成
2025-04-28 10:30:33,909 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 10:30:33,909 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 10:30:33,909 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 10:30:33,971 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 196 条记录
2025-04-28 10:30:33,971 - INFO - 获取到 6 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-24', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 10:30:33,971 - INFO - 开始处理日期: 2025-04-11
2025-04-28 10:30:33,971 - INFO - Request Parameters - Page 1:
2025-04-28 10:30:33,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:30:33,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:30:42,096 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8B1A0880-DBA5-7051-BB4B-D30231F1436B Response: {'code': 'ServiceUnavailable', 'requestid': '8B1A0880-DBA5-7051-BB4B-D30231F1436B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8B1A0880-DBA5-7051-BB4B-D30231F1436B)
2025-04-28 10:30:42,112 - INFO - 开始处理日期: 2025-04-22
2025-04-28 10:30:42,112 - INFO - Request Parameters - Page 1:
2025-04-28 10:30:42,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:30:42,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:30:50,221 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 843D0CF9-4E14-7EBF-BDC7-7ABD4BC11F29 Response: {'code': 'ServiceUnavailable', 'requestid': '843D0CF9-4E14-7EBF-BDC7-7ABD4BC11F29', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 843D0CF9-4E14-7EBF-BDC7-7ABD4BC11F29)
2025-04-28 10:30:50,221 - INFO - 开始处理日期: 2025-04-24
2025-04-28 10:30:50,221 - INFO - Request Parameters - Page 1:
2025-04-28 10:30:50,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:30:50,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:30:55,799 - INFO - Response - Page 1:
2025-04-28 10:30:55,799 - INFO - 第 1 页获取到 100 条记录
2025-04-28 10:30:56,002 - INFO - Request Parameters - Page 2:
2025-04-28 10:30:56,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:30:56,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:30:56,752 - INFO - Response - Page 2:
2025-04-28 10:30:56,752 - INFO - 第 2 页获取到 100 条记录
2025-04-28 10:30:56,955 - INFO - Request Parameters - Page 3:
2025-04-28 10:30:56,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:30:56,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:30:57,533 - INFO - Response - Page 3:
2025-04-28 10:30:57,533 - INFO - 第 3 页获取到 34 条记录
2025-04-28 10:30:57,737 - INFO - 查询完成，共获取到 234 条记录
2025-04-28 10:30:57,737 - INFO - 获取到 234 条表单数据
2025-04-28 10:30:57,737 - INFO - 当前日期 2025-04-24 有 1 条MySQL数据需要处理
2025-04-28 10:30:57,737 - INFO - 开始批量插入 1 条新记录
2025-04-28 10:30:57,893 - INFO - 批量插入响应状态码: 200
2025-04-28 10:30:57,893 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 02:30:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-4487-737B-A0EE-8BA368D7EFEC', 'x-acs-trace-id': '4406d1ebb5745c9706b3ba55e4646fb2', 'etag': '68DodidU4RtJuX/Eso0ClGw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 10:30:57,893 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J71P4VUJJ3S9PEBRB3D1PDD2GZ8OG0AMNK']}
2025-04-28 10:30:57,893 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 10:30:57,893 - INFO - 成功插入的数据ID: ['FINST-LLF66J71P4VUJJ3S9PEBRB3D1PDD2GZ8OG0AMNK']
2025-04-28 10:31:02,908 - INFO - 批量插入完成，共 1 条记录
2025-04-28 10:31:02,908 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 10:31:02,908 - INFO - 开始处理日期: 2025-04-26
2025-04-28 10:31:02,908 - INFO - Request Parameters - Page 1:
2025-04-28 10:31:02,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:31:02,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:31:03,596 - INFO - Response - Page 1:
2025-04-28 10:31:03,596 - INFO - 第 1 页获取到 100 条记录
2025-04-28 10:31:03,799 - INFO - Request Parameters - Page 2:
2025-04-28 10:31:03,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:31:03,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:31:04,471 - INFO - Response - Page 2:
2025-04-28 10:31:04,471 - INFO - 第 2 页获取到 100 条记录
2025-04-28 10:31:04,674 - INFO - Request Parameters - Page 3:
2025-04-28 10:31:04,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:31:04,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:31:05,408 - INFO - Response - Page 3:
2025-04-28 10:31:05,408 - INFO - 第 3 页获取到 81 条记录
2025-04-28 10:31:05,612 - INFO - 查询完成，共获取到 281 条记录
2025-04-28 10:31:05,612 - INFO - 获取到 281 条表单数据
2025-04-28 10:31:05,612 - INFO - 当前日期 2025-04-26 有 1 条MySQL数据需要处理
2025-04-28 10:31:05,612 - INFO - 开始批量插入 1 条新记录
2025-04-28 10:31:05,752 - INFO - 批量插入响应状态码: 200
2025-04-28 10:31:05,752 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 02:30:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '74B6840E-297A-7F00-87D3-CFF15941D5A0', 'x-acs-trace-id': '0e6a9156eeacf3b38ab217aff2ca5160', 'etag': '6HBRjowml/nhzspCyfkHkRg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 10:31:05,752 - INFO - 批量插入响应体: {'result': ['FINST-HXD667B167VU1R307YKWWB7H8PJR332FOG0AM49']}
2025-04-28 10:31:05,752 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 10:31:05,752 - INFO - 成功插入的数据ID: ['FINST-HXD667B167VU1R307YKWWB7H8PJR332FOG0AM49']
2025-04-28 10:31:10,752 - INFO - 批量插入完成，共 1 条记录
2025-04-28 10:31:10,752 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 10:31:10,752 - INFO - 开始处理日期: 2025-04-27
2025-04-28 10:31:10,752 - INFO - Request Parameters - Page 1:
2025-04-28 10:31:10,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:31:10,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:31:11,361 - INFO - Response - Page 1:
2025-04-28 10:31:11,361 - INFO - 第 1 页获取到 22 条记录
2025-04-28 10:31:11,565 - INFO - 查询完成，共获取到 22 条记录
2025-04-28 10:31:11,565 - INFO - 获取到 22 条表单数据
2025-04-28 10:31:11,565 - INFO - 当前日期 2025-04-27 有 191 条MySQL数据需要处理
2025-04-28 10:31:11,565 - INFO - 开始批量插入 191 条新记录
2025-04-28 10:31:11,861 - INFO - 批量插入响应状态码: 200
2025-04-28 10:31:11,861 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 02:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '385C8DB7-6652-7B09-96E9-C0A408693480', 'x-acs-trace-id': '32a81e4d5e1d25199ec2f14d9299fac0', 'etag': '4rFjajruIgkbvHM5XYXYJRg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 10:31:11,861 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AM8C', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AM9C', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMAC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMBC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMCC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMDC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMEC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMFC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMGC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMHC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMIC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMJC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMKC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMLC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMMC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMNC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMOC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMPC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMQC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMRC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMSC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMTC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMVC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMWC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMXC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMYC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMZC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM0D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM1D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM2D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM3D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM4D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM5D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM6D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM7D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM8D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM9D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMAD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMBD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMCD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMDD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMED', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMFD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMGD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMHD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMID', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMJD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMKD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMLD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMMD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMND', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMOD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMPD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMQD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMRD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMSD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMTD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMVD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMWD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMXD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMYD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMZD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM0E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM1E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM2E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM3E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM4E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM5E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM6E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM7E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM8E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM9E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMAE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMBE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMCE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMDE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMEE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMFE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMGE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMHE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMIE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMJE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMKE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMLE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMME', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMNE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMOE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMPE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMQE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMRE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMSE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMTE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMVE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMWE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMXE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMYE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMZE']}
2025-04-28 10:31:11,861 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-04-28 10:31:11,861 - INFO - 成功插入的数据ID: ['FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AM8C', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AM9C', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMAC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMBC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMCC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMDC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMEC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMFC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMGC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMHC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMIC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMJC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMKC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMLC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMMC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMNC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2JRJOG0AMOC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMPC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMQC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMRC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMSC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMTC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMVC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMWC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMXC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMYC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMZC', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM0D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM1D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM2D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM3D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM4D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM5D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM6D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM7D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM8D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM9D', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMAD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMBD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMCD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMDD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMED', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMFD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMGD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMHD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMID', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMJD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMKD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMLD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMMD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMND', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMOD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMPD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMQD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMRD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMSD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMTD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMVD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMWD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMXD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMYD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMZD', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM0E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM1E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM2E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM3E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM4E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM5E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM6E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM7E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM8E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AM9E', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMAE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMBE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMCE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMDE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMEE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMFE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMGE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMHE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMIE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMJE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMKE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMLE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMME', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMNE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMOE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMPE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMQE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMRE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMSE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMTE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMVE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMWE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMXE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMYE', 'FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMZE']
2025-04-28 10:31:17,143 - INFO - 批量插入响应状态码: 200
2025-04-28 10:31:17,143 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 02:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4345', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A9135F84-3800-7E4D-9336-C6E50BC248BD', 'x-acs-trace-id': '70e56d00d99def1da8cbceb8112bcd77', 'etag': '4c6Xnmdm/vZJNq3XiLUZfig5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 10:31:17,143 - INFO - 批量插入响应体: {'result': ['FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM3', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM4', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM5', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM6', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM7', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM8', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM9', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMA', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMB', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMC', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AME', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMK', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AML', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMM', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMN', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMO', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMP', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMQ', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMR', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMS', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMT', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMU', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMV', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMW', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMX', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMY', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMZ', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM01', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM11', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM21', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM31', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM41', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM51', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM61', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM71', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM81', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM91', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMA1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMB1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMC1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AME1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMK1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AML1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMM1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMN1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMO1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMP1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMQ1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMR1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMS1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMT1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMU1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMV1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMW1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMX1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMY1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMZ1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM02', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM12', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM22', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM32', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM42', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM52', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM62', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM72', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM82', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM92', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMA2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMB2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMC2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AME2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ2']}
2025-04-28 10:31:17,143 - INFO - 批量插入表单数据成功，批次 2，共 91 条记录
2025-04-28 10:31:17,143 - INFO - 成功插入的数据ID: ['FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM3', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM4', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM5', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM6', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM7', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM8', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM9', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMA', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMB', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMC', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AME', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMK', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AML', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMM', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMN', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMO', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMP', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMQ', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMR', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMS', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMT', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMU', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMV', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMW', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMX', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMY', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMZ', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM01', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM11', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM21', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM31', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM41', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM51', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM61', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM71', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM81', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM91', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMA1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMB1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMC1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AME1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMK1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AML1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMM1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMN1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMO1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMP1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMQ1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMR1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMS1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMT1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMU1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMV1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMW1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMX1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMY1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMZ1', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM02', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM12', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM22', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM32', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM42', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM52', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM62', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM72', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM82', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM92', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMA2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMB2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMC2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AME2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI2', 'FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ2']
2025-04-28 10:31:22,158 - INFO - 批量插入完成，共 191 条记录
2025-04-28 10:31:22,158 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 191 条，错误: 0 条
2025-04-28 10:31:22,158 - INFO - 开始处理日期: 2025-04-28
2025-04-28 10:31:22,158 - INFO - Request Parameters - Page 1:
2025-04-28 10:31:22,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 10:31:22,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 10:31:22,611 - INFO - Response - Page 1:
2025-04-28 10:31:22,611 - INFO - 第 1 页获取到 1 条记录
2025-04-28 10:31:22,815 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 10:31:22,815 - INFO - 获取到 1 条表单数据
2025-04-28 10:31:22,815 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 10:31:22,815 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 10:31:22,815 - INFO - 数据同步完成！更新: 0 条，插入: 193 条，错误: 2 条
2025-04-28 10:32:22,830 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 10:32:22,830 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 10:32:22,830 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 10:32:22,877 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 10:32:22,877 - ERROR - 未获取到MySQL数据
2025-04-28 10:32:22,877 - INFO - 同步完成
2025-04-28 11:30:33,730 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 11:30:33,730 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 11:30:33,730 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 11:30:33,792 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 205 条记录
2025-04-28 11:30:33,792 - INFO - 获取到 6 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-24', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 11:30:33,792 - INFO - 开始处理日期: 2025-04-11
2025-04-28 11:30:33,792 - INFO - Request Parameters - Page 1:
2025-04-28 11:30:33,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:33,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:41,917 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DBA3EE43-A0C6-76C2-BB1A-67EB1B6D7C96 Response: {'code': 'ServiceUnavailable', 'requestid': 'DBA3EE43-A0C6-76C2-BB1A-67EB1B6D7C96', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DBA3EE43-A0C6-76C2-BB1A-67EB1B6D7C96)
2025-04-28 11:30:41,917 - INFO - 开始处理日期: 2025-04-22
2025-04-28 11:30:41,917 - INFO - Request Parameters - Page 1:
2025-04-28 11:30:41,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:41,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:42,620 - INFO - Response - Page 1:
2025-04-28 11:30:42,620 - INFO - 第 1 页获取到 100 条记录
2025-04-28 11:30:42,823 - INFO - Request Parameters - Page 2:
2025-04-28 11:30:42,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:42,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:50,933 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F1AEE894-D3A3-7B72-AE27-17FF2400489D Response: {'code': 'ServiceUnavailable', 'requestid': 'F1AEE894-D3A3-7B72-AE27-17FF2400489D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F1AEE894-D3A3-7B72-AE27-17FF2400489D)
2025-04-28 11:30:50,933 - INFO - 开始处理日期: 2025-04-24
2025-04-28 11:30:50,933 - INFO - Request Parameters - Page 1:
2025-04-28 11:30:50,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:50,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:51,589 - INFO - Response - Page 1:
2025-04-28 11:30:51,589 - INFO - 第 1 页获取到 100 条记录
2025-04-28 11:30:51,792 - INFO - Request Parameters - Page 2:
2025-04-28 11:30:51,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:51,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:52,542 - INFO - Response - Page 2:
2025-04-28 11:30:52,542 - INFO - 第 2 页获取到 100 条记录
2025-04-28 11:30:52,745 - INFO - Request Parameters - Page 3:
2025-04-28 11:30:52,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:52,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:53,323 - INFO - Response - Page 3:
2025-04-28 11:30:53,323 - INFO - 第 3 页获取到 35 条记录
2025-04-28 11:30:53,526 - INFO - 查询完成，共获取到 235 条记录
2025-04-28 11:30:53,526 - INFO - 获取到 235 条表单数据
2025-04-28 11:30:53,526 - INFO - 当前日期 2025-04-24 有 1 条MySQL数据需要处理
2025-04-28 11:30:53,526 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 11:30:53,526 - INFO - 开始处理日期: 2025-04-26
2025-04-28 11:30:53,526 - INFO - Request Parameters - Page 1:
2025-04-28 11:30:53,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:53,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:56,167 - INFO - Response - Page 1:
2025-04-28 11:30:56,167 - INFO - 第 1 页获取到 100 条记录
2025-04-28 11:30:56,370 - INFO - Request Parameters - Page 2:
2025-04-28 11:30:56,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:56,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:57,136 - INFO - Response - Page 2:
2025-04-28 11:30:57,136 - INFO - 第 2 页获取到 100 条记录
2025-04-28 11:30:57,339 - INFO - Request Parameters - Page 3:
2025-04-28 11:30:57,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:57,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:58,073 - INFO - Response - Page 3:
2025-04-28 11:30:58,073 - INFO - 第 3 页获取到 82 条记录
2025-04-28 11:30:58,276 - INFO - 查询完成，共获取到 282 条记录
2025-04-28 11:30:58,276 - INFO - 获取到 282 条表单数据
2025-04-28 11:30:58,276 - INFO - 当前日期 2025-04-26 有 1 条MySQL数据需要处理
2025-04-28 11:30:58,276 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 11:30:58,276 - INFO - 开始处理日期: 2025-04-27
2025-04-28 11:30:58,276 - INFO - Request Parameters - Page 1:
2025-04-28 11:30:58,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:58,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:59,073 - INFO - Response - Page 1:
2025-04-28 11:30:59,073 - INFO - 第 1 页获取到 100 条记录
2025-04-28 11:30:59,276 - INFO - Request Parameters - Page 2:
2025-04-28 11:30:59,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:30:59,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:30:59,980 - INFO - Response - Page 2:
2025-04-28 11:30:59,980 - INFO - 第 2 页获取到 100 条记录
2025-04-28 11:31:00,183 - INFO - Request Parameters - Page 3:
2025-04-28 11:31:00,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:31:00,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:31:00,651 - INFO - Response - Page 3:
2025-04-28 11:31:00,667 - INFO - 第 3 页获取到 13 条记录
2025-04-28 11:31:00,870 - INFO - 查询完成，共获取到 213 条记录
2025-04-28 11:31:00,870 - INFO - 获取到 213 条表单数据
2025-04-28 11:31:00,870 - INFO - 当前日期 2025-04-27 有 200 条MySQL数据需要处理
2025-04-28 11:31:00,870 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUE
2025-04-28 11:31:01,370 - INFO - 更新表单数据成功: FINST-XL866HB1MMVUPXG57TOH4C6AN3TI2KRJOG0AMUE
2025-04-28 11:31:01,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6500.0, 'new_value': 3423.0}, {'field': 'total_amount', 'old_value': 6500.0, 'new_value': 3423.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 25}]
2025-04-28 11:31:01,370 - INFO - 开始批量插入 9 条新记录
2025-04-28 11:31:01,526 - INFO - 批量插入响应状态码: 200
2025-04-28 11:31:01,526 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 03:30:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '37A27A25-0B60-723D-9D66-C59557B2E075', 'x-acs-trace-id': 'c7ddafe49e413b9b5e6769abb61813a1', 'etag': '4ROLQ2ABAEzjUqGvBYe+qtg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 11:31:01,526 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM11', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM21', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM31', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM41', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM51', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM61', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM71', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM81', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM91']}
2025-04-28 11:31:01,526 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-04-28 11:31:01,526 - INFO - 成功插入的数据ID: ['FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM11', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM21', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM31', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM41', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM51', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM61', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM71', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM81', 'FINST-FD966QA1XLVUILWO8ZWB74ZES2R72WKHTI0AM91']
2025-04-28 11:31:06,542 - INFO - 批量插入完成，共 9 条记录
2025-04-28 11:31:06,542 - INFO - 日期 2025-04-27 处理完成 - 更新: 1 条，插入: 9 条，错误: 0 条
2025-04-28 11:31:06,542 - INFO - 开始处理日期: 2025-04-28
2025-04-28 11:31:06,542 - INFO - Request Parameters - Page 1:
2025-04-28 11:31:06,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 11:31:06,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 11:31:06,995 - INFO - Response - Page 1:
2025-04-28 11:31:06,995 - INFO - 第 1 页获取到 1 条记录
2025-04-28 11:31:07,198 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 11:31:07,198 - INFO - 获取到 1 条表单数据
2025-04-28 11:31:07,198 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 11:31:07,198 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 11:31:07,198 - INFO - 数据同步完成！更新: 1 条，插入: 9 条，错误: 2 条
2025-04-28 11:32:07,214 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 11:32:07,214 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 11:32:07,214 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 11:32:07,260 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 11:32:07,260 - ERROR - 未获取到MySQL数据
2025-04-28 11:32:07,260 - INFO - 同步完成
2025-04-28 12:30:33,879 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 12:30:33,879 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 12:30:33,879 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 12:30:33,942 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 205 条记录
2025-04-28 12:30:33,942 - INFO - 获取到 6 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-24', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 12:30:33,942 - INFO - 开始处理日期: 2025-04-11
2025-04-28 12:30:33,942 - INFO - Request Parameters - Page 1:
2025-04-28 12:30:33,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:33,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:42,066 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 18F6B277-E0F4-79FD-A649-177EDB93B8D6 Response: {'code': 'ServiceUnavailable', 'requestid': '18F6B277-E0F4-79FD-A649-177EDB93B8D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 18F6B277-E0F4-79FD-A649-177EDB93B8D6)
2025-04-28 12:30:42,066 - INFO - 开始处理日期: 2025-04-22
2025-04-28 12:30:42,066 - INFO - Request Parameters - Page 1:
2025-04-28 12:30:42,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:42,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:50,207 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9751FDF3-DBC8-72E1-A7F0-B9F6EE2DF4CE Response: {'code': 'ServiceUnavailable', 'requestid': '9751FDF3-DBC8-72E1-A7F0-B9F6EE2DF4CE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9751FDF3-DBC8-72E1-A7F0-B9F6EE2DF4CE)
2025-04-28 12:30:50,207 - INFO - 开始处理日期: 2025-04-24
2025-04-28 12:30:50,207 - INFO - Request Parameters - Page 1:
2025-04-28 12:30:50,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:50,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:50,895 - INFO - Response - Page 1:
2025-04-28 12:30:50,895 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:30:51,098 - INFO - Request Parameters - Page 2:
2025-04-28 12:30:51,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:51,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:51,832 - INFO - Response - Page 2:
2025-04-28 12:30:51,832 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:30:52,035 - INFO - Request Parameters - Page 3:
2025-04-28 12:30:52,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:52,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:52,645 - INFO - Response - Page 3:
2025-04-28 12:30:52,645 - INFO - 第 3 页获取到 35 条记录
2025-04-28 12:30:52,848 - INFO - 查询完成，共获取到 235 条记录
2025-04-28 12:30:52,848 - INFO - 获取到 235 条表单数据
2025-04-28 12:30:52,848 - INFO - 当前日期 2025-04-24 有 1 条MySQL数据需要处理
2025-04-28 12:30:52,848 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:30:52,848 - INFO - 开始处理日期: 2025-04-26
2025-04-28 12:30:52,848 - INFO - Request Parameters - Page 1:
2025-04-28 12:30:52,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:52,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:53,582 - INFO - Response - Page 1:
2025-04-28 12:30:53,582 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:30:53,785 - INFO - Request Parameters - Page 2:
2025-04-28 12:30:53,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:53,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:54,520 - INFO - Response - Page 2:
2025-04-28 12:30:54,520 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:30:54,723 - INFO - Request Parameters - Page 3:
2025-04-28 12:30:54,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:54,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:55,426 - INFO - Response - Page 3:
2025-04-28 12:30:55,426 - INFO - 第 3 页获取到 82 条记录
2025-04-28 12:30:55,629 - INFO - 查询完成，共获取到 282 条记录
2025-04-28 12:30:55,629 - INFO - 获取到 282 条表单数据
2025-04-28 12:30:55,629 - INFO - 当前日期 2025-04-26 有 1 条MySQL数据需要处理
2025-04-28 12:30:55,629 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:30:55,629 - INFO - 开始处理日期: 2025-04-27
2025-04-28 12:30:55,629 - INFO - Request Parameters - Page 1:
2025-04-28 12:30:55,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:55,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:56,348 - INFO - Response - Page 1:
2025-04-28 12:30:56,348 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:30:56,551 - INFO - Request Parameters - Page 2:
2025-04-28 12:30:56,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:56,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:57,301 - INFO - Response - Page 2:
2025-04-28 12:30:57,301 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:30:57,520 - INFO - Request Parameters - Page 3:
2025-04-28 12:30:57,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:57,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:58,082 - INFO - Response - Page 3:
2025-04-28 12:30:58,082 - INFO - 第 3 页获取到 22 条记录
2025-04-28 12:30:58,285 - INFO - 查询完成，共获取到 222 条记录
2025-04-28 12:30:58,285 - INFO - 获取到 222 条表单数据
2025-04-28 12:30:58,285 - INFO - 当前日期 2025-04-27 有 200 条MySQL数据需要处理
2025-04-28 12:30:58,285 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:30:58,285 - INFO - 开始处理日期: 2025-04-28
2025-04-28 12:30:58,285 - INFO - Request Parameters - Page 1:
2025-04-28 12:30:58,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:30:58,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:30:58,769 - INFO - Response - Page 1:
2025-04-28 12:30:58,769 - INFO - 第 1 页获取到 1 条记录
2025-04-28 12:30:58,973 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 12:30:58,973 - INFO - 获取到 1 条表单数据
2025-04-28 12:30:58,973 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 12:30:58,973 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:30:58,973 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-28 12:31:58,988 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 12:31:58,988 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 12:31:58,988 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 12:31:59,035 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 12:31:59,035 - ERROR - 未获取到MySQL数据
2025-04-28 12:31:59,035 - INFO - 同步完成
2025-04-28 13:30:33,825 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 13:30:33,825 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 13:30:33,825 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 13:30:33,888 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 205 条记录
2025-04-28 13:30:33,888 - INFO - 获取到 6 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-24', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 13:30:33,888 - INFO - 开始处理日期: 2025-04-11
2025-04-28 13:30:33,888 - INFO - Request Parameters - Page 1:
2025-04-28 13:30:33,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:33,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:42,013 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 53EC83EF-C11A-79F9-87EC-BAD61C22684D Response: {'code': 'ServiceUnavailable', 'requestid': '53EC83EF-C11A-79F9-87EC-BAD61C22684D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 53EC83EF-C11A-79F9-87EC-BAD61C22684D)
2025-04-28 13:30:42,013 - INFO - 开始处理日期: 2025-04-22
2025-04-28 13:30:42,013 - INFO - Request Parameters - Page 1:
2025-04-28 13:30:42,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:42,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:47,434 - INFO - Response - Page 1:
2025-04-28 13:30:47,434 - INFO - 第 1 页获取到 100 条记录
2025-04-28 13:30:47,638 - INFO - Request Parameters - Page 2:
2025-04-28 13:30:47,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:47,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:55,747 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 799462B0-EE80-7301-84BC-BAA2FC3B7715 Response: {'code': 'ServiceUnavailable', 'requestid': '799462B0-EE80-7301-84BC-BAA2FC3B7715', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 799462B0-EE80-7301-84BC-BAA2FC3B7715)
2025-04-28 13:30:55,747 - INFO - 开始处理日期: 2025-04-24
2025-04-28 13:30:55,747 - INFO - Request Parameters - Page 1:
2025-04-28 13:30:55,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:55,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:56,512 - INFO - Response - Page 1:
2025-04-28 13:30:56,512 - INFO - 第 1 页获取到 100 条记录
2025-04-28 13:30:56,716 - INFO - Request Parameters - Page 2:
2025-04-28 13:30:56,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:56,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:57,481 - INFO - Response - Page 2:
2025-04-28 13:30:57,481 - INFO - 第 2 页获取到 100 条记录
2025-04-28 13:30:57,684 - INFO - Request Parameters - Page 3:
2025-04-28 13:30:57,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:57,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:58,278 - INFO - Response - Page 3:
2025-04-28 13:30:58,278 - INFO - 第 3 页获取到 35 条记录
2025-04-28 13:30:58,481 - INFO - 查询完成，共获取到 235 条记录
2025-04-28 13:30:58,481 - INFO - 获取到 235 条表单数据
2025-04-28 13:30:58,481 - INFO - 当前日期 2025-04-24 有 1 条MySQL数据需要处理
2025-04-28 13:30:58,481 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 13:30:58,481 - INFO - 开始处理日期: 2025-04-26
2025-04-28 13:30:58,481 - INFO - Request Parameters - Page 1:
2025-04-28 13:30:58,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:58,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:30:59,216 - INFO - Response - Page 1:
2025-04-28 13:30:59,216 - INFO - 第 1 页获取到 100 条记录
2025-04-28 13:30:59,419 - INFO - Request Parameters - Page 2:
2025-04-28 13:30:59,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:30:59,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:31:00,137 - INFO - Response - Page 2:
2025-04-28 13:31:00,137 - INFO - 第 2 页获取到 100 条记录
2025-04-28 13:31:00,341 - INFO - Request Parameters - Page 3:
2025-04-28 13:31:00,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:31:00,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:31:01,137 - INFO - Response - Page 3:
2025-04-28 13:31:01,137 - INFO - 第 3 页获取到 82 条记录
2025-04-28 13:31:01,341 - INFO - 查询完成，共获取到 282 条记录
2025-04-28 13:31:01,341 - INFO - 获取到 282 条表单数据
2025-04-28 13:31:01,341 - INFO - 当前日期 2025-04-26 有 1 条MySQL数据需要处理
2025-04-28 13:31:01,341 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 13:31:01,341 - INFO - 开始处理日期: 2025-04-27
2025-04-28 13:31:01,341 - INFO - Request Parameters - Page 1:
2025-04-28 13:31:01,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:31:01,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:31:02,403 - INFO - Response - Page 1:
2025-04-28 13:31:02,403 - INFO - 第 1 页获取到 100 条记录
2025-04-28 13:31:02,606 - INFO - Request Parameters - Page 2:
2025-04-28 13:31:02,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:31:02,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:31:03,341 - INFO - Response - Page 2:
2025-04-28 13:31:03,341 - INFO - 第 2 页获取到 100 条记录
2025-04-28 13:31:03,544 - INFO - Request Parameters - Page 3:
2025-04-28 13:31:03,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:31:03,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:31:04,106 - INFO - Response - Page 3:
2025-04-28 13:31:04,106 - INFO - 第 3 页获取到 22 条记录
2025-04-28 13:31:04,309 - INFO - 查询完成，共获取到 222 条记录
2025-04-28 13:31:04,309 - INFO - 获取到 222 条表单数据
2025-04-28 13:31:04,309 - INFO - 当前日期 2025-04-27 有 200 条MySQL数据需要处理
2025-04-28 13:31:04,309 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 13:31:04,309 - INFO - 开始处理日期: 2025-04-28
2025-04-28 13:31:04,309 - INFO - Request Parameters - Page 1:
2025-04-28 13:31:04,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 13:31:04,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 13:31:04,825 - INFO - Response - Page 1:
2025-04-28 13:31:04,825 - INFO - 第 1 页获取到 1 条记录
2025-04-28 13:31:05,028 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 13:31:05,028 - INFO - 获取到 1 条表单数据
2025-04-28 13:31:05,028 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 13:31:05,028 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 13:31:05,028 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-28 13:32:05,043 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 13:32:05,043 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 13:32:05,043 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 13:32:05,090 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 13:32:05,090 - ERROR - 未获取到MySQL数据
2025-04-28 13:32:05,090 - INFO - 同步完成
2025-04-28 14:30:33,740 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 14:30:33,740 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 14:30:33,740 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 14:30:33,802 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 206 条记录
2025-04-28 14:30:33,802 - INFO - 获取到 6 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-24', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 14:30:33,802 - INFO - 开始处理日期: 2025-04-11
2025-04-28 14:30:33,802 - INFO - Request Parameters - Page 1:
2025-04-28 14:30:33,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:33,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:41,927 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8B3F2DDF-FE9A-744E-8C48-28A27E096549 Response: {'code': 'ServiceUnavailable', 'requestid': '8B3F2DDF-FE9A-744E-8C48-28A27E096549', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8B3F2DDF-FE9A-744E-8C48-28A27E096549)
2025-04-28 14:30:41,927 - INFO - 开始处理日期: 2025-04-22
2025-04-28 14:30:41,927 - INFO - Request Parameters - Page 1:
2025-04-28 14:30:41,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:41,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:50,037 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B5F83C4A-D8DD-743A-876D-4EB90360437A Response: {'code': 'ServiceUnavailable', 'requestid': 'B5F83C4A-D8DD-743A-876D-4EB90360437A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B5F83C4A-D8DD-743A-876D-4EB90360437A)
2025-04-28 14:30:50,037 - INFO - 开始处理日期: 2025-04-24
2025-04-28 14:30:50,037 - INFO - Request Parameters - Page 1:
2025-04-28 14:30:50,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:50,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:50,849 - INFO - Response - Page 1:
2025-04-28 14:30:50,849 - INFO - 第 1 页获取到 100 条记录
2025-04-28 14:30:51,052 - INFO - Request Parameters - Page 2:
2025-04-28 14:30:51,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:51,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:51,771 - INFO - Response - Page 2:
2025-04-28 14:30:51,771 - INFO - 第 2 页获取到 100 条记录
2025-04-28 14:30:51,974 - INFO - Request Parameters - Page 3:
2025-04-28 14:30:51,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:51,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:52,646 - INFO - Response - Page 3:
2025-04-28 14:30:52,646 - INFO - 第 3 页获取到 35 条记录
2025-04-28 14:30:52,849 - INFO - 查询完成，共获取到 235 条记录
2025-04-28 14:30:52,849 - INFO - 获取到 235 条表单数据
2025-04-28 14:30:52,849 - INFO - 当前日期 2025-04-24 有 1 条MySQL数据需要处理
2025-04-28 14:30:52,849 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 14:30:52,849 - INFO - 开始处理日期: 2025-04-26
2025-04-28 14:30:52,849 - INFO - Request Parameters - Page 1:
2025-04-28 14:30:52,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:52,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:53,552 - INFO - Response - Page 1:
2025-04-28 14:30:53,552 - INFO - 第 1 页获取到 100 条记录
2025-04-28 14:30:53,755 - INFO - Request Parameters - Page 2:
2025-04-28 14:30:53,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:53,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:54,443 - INFO - Response - Page 2:
2025-04-28 14:30:54,443 - INFO - 第 2 页获取到 100 条记录
2025-04-28 14:30:54,646 - INFO - Request Parameters - Page 3:
2025-04-28 14:30:54,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:54,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:55,302 - INFO - Response - Page 3:
2025-04-28 14:30:55,302 - INFO - 第 3 页获取到 82 条记录
2025-04-28 14:30:55,505 - INFO - 查询完成，共获取到 282 条记录
2025-04-28 14:30:55,505 - INFO - 获取到 282 条表单数据
2025-04-28 14:30:55,505 - INFO - 当前日期 2025-04-26 有 1 条MySQL数据需要处理
2025-04-28 14:30:55,505 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 14:30:55,505 - INFO - 开始处理日期: 2025-04-27
2025-04-28 14:30:55,505 - INFO - Request Parameters - Page 1:
2025-04-28 14:30:55,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:55,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:56,209 - INFO - Response - Page 1:
2025-04-28 14:30:56,209 - INFO - 第 1 页获取到 100 条记录
2025-04-28 14:30:56,412 - INFO - Request Parameters - Page 2:
2025-04-28 14:30:56,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:56,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:57,115 - INFO - Response - Page 2:
2025-04-28 14:30:57,115 - INFO - 第 2 页获取到 100 条记录
2025-04-28 14:30:57,318 - INFO - Request Parameters - Page 3:
2025-04-28 14:30:57,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:30:57,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:30:57,865 - INFO - Response - Page 3:
2025-04-28 14:30:57,865 - INFO - 第 3 页获取到 22 条记录
2025-04-28 14:30:58,068 - INFO - 查询完成，共获取到 222 条记录
2025-04-28 14:30:58,068 - INFO - 获取到 222 条表单数据
2025-04-28 14:30:58,068 - INFO - 当前日期 2025-04-27 有 201 条MySQL数据需要处理
2025-04-28 14:30:58,068 - INFO - 开始批量插入 1 条新记录
2025-04-28 14:30:58,287 - INFO - 批量插入响应状态码: 200
2025-04-28 14:30:58,287 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 06:30:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BC41BEE4-9DAA-7165-AA6D-AA30ABED6F68', 'x-acs-trace-id': '5e6a40f5fab831f0388f5c8dabaf2c23', 'etag': '5pCVP7B5yaOI/IZykVcVMpg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 14:30:58,287 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1RVYUNNGO717H38NKF2UN3XGW8P0AM0']}
2025-04-28 14:30:58,287 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 14:30:58,287 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1RVYUNNGO717H38NKF2UN3XGW8P0AM0']
2025-04-28 14:31:03,302 - INFO - 批量插入完成，共 1 条记录
2025-04-28 14:31:03,302 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 14:31:03,302 - INFO - 开始处理日期: 2025-04-28
2025-04-28 14:31:03,302 - INFO - Request Parameters - Page 1:
2025-04-28 14:31:03,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 14:31:03,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 14:31:03,755 - INFO - Response - Page 1:
2025-04-28 14:31:03,755 - INFO - 第 1 页获取到 1 条记录
2025-04-28 14:31:03,958 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 14:31:03,958 - INFO - 获取到 1 条表单数据
2025-04-28 14:31:03,958 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 14:31:03,958 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 14:31:03,958 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-04-28 14:32:03,974 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 14:32:03,974 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 14:32:03,974 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 14:32:04,021 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 14:32:04,021 - ERROR - 未获取到MySQL数据
2025-04-28 14:32:04,021 - INFO - 同步完成
2025-04-28 15:30:33,905 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 15:30:33,905 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 15:30:33,905 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 15:30:33,967 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 208 条记录
2025-04-28 15:30:33,967 - INFO - 获取到 6 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-24', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 15:30:33,967 - INFO - 开始处理日期: 2025-04-11
2025-04-28 15:30:33,967 - INFO - Request Parameters - Page 1:
2025-04-28 15:30:33,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:33,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:42,108 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D28E3348-98AD-797F-8712-D22B6E2C57C8 Response: {'code': 'ServiceUnavailable', 'requestid': 'D28E3348-98AD-797F-8712-D22B6E2C57C8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D28E3348-98AD-797F-8712-D22B6E2C57C8)
2025-04-28 15:30:42,108 - INFO - 开始处理日期: 2025-04-22
2025-04-28 15:30:42,108 - INFO - Request Parameters - Page 1:
2025-04-28 15:30:42,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:42,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:50,233 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0C53FA88-81EE-7B09-878F-55A8D69EDF60 Response: {'code': 'ServiceUnavailable', 'requestid': '0C53FA88-81EE-7B09-878F-55A8D69EDF60', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0C53FA88-81EE-7B09-878F-55A8D69EDF60)
2025-04-28 15:30:50,233 - INFO - 开始处理日期: 2025-04-24
2025-04-28 15:30:50,233 - INFO - Request Parameters - Page 1:
2025-04-28 15:30:50,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:50,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:51,014 - INFO - Response - Page 1:
2025-04-28 15:30:51,014 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:30:51,217 - INFO - Request Parameters - Page 2:
2025-04-28 15:30:51,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:51,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:51,998 - INFO - Response - Page 2:
2025-04-28 15:30:51,998 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:30:52,201 - INFO - Request Parameters - Page 3:
2025-04-28 15:30:52,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:52,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:52,795 - INFO - Response - Page 3:
2025-04-28 15:30:52,795 - INFO - 第 3 页获取到 35 条记录
2025-04-28 15:30:52,998 - INFO - 查询完成，共获取到 235 条记录
2025-04-28 15:30:52,998 - INFO - 获取到 235 条表单数据
2025-04-28 15:30:52,998 - INFO - 当前日期 2025-04-24 有 1 条MySQL数据需要处理
2025-04-28 15:30:52,998 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 15:30:52,998 - INFO - 开始处理日期: 2025-04-26
2025-04-28 15:30:52,998 - INFO - Request Parameters - Page 1:
2025-04-28 15:30:52,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:52,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:53,701 - INFO - Response - Page 1:
2025-04-28 15:30:53,701 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:30:53,905 - INFO - Request Parameters - Page 2:
2025-04-28 15:30:53,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:53,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:54,686 - INFO - Response - Page 2:
2025-04-28 15:30:54,686 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:30:54,889 - INFO - Request Parameters - Page 3:
2025-04-28 15:30:54,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:54,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:55,592 - INFO - Response - Page 3:
2025-04-28 15:30:55,592 - INFO - 第 3 页获取到 82 条记录
2025-04-28 15:30:55,795 - INFO - 查询完成，共获取到 282 条记录
2025-04-28 15:30:55,795 - INFO - 获取到 282 条表单数据
2025-04-28 15:30:55,795 - INFO - 当前日期 2025-04-26 有 1 条MySQL数据需要处理
2025-04-28 15:30:55,795 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 15:30:55,795 - INFO - 开始处理日期: 2025-04-27
2025-04-28 15:30:55,795 - INFO - Request Parameters - Page 1:
2025-04-28 15:30:55,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:55,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:56,608 - INFO - Response - Page 1:
2025-04-28 15:30:56,608 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:30:56,811 - INFO - Request Parameters - Page 2:
2025-04-28 15:30:56,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:56,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:57,530 - INFO - Response - Page 2:
2025-04-28 15:30:57,530 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:30:57,733 - INFO - Request Parameters - Page 3:
2025-04-28 15:30:57,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:30:57,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:30:58,295 - INFO - Response - Page 3:
2025-04-28 15:30:58,295 - INFO - 第 3 页获取到 23 条记录
2025-04-28 15:30:58,498 - INFO - 查询完成，共获取到 223 条记录
2025-04-28 15:30:58,498 - INFO - 获取到 223 条表单数据
2025-04-28 15:30:58,498 - INFO - 当前日期 2025-04-27 有 203 条MySQL数据需要处理
2025-04-28 15:30:58,498 - INFO - 开始批量插入 2 条新记录
2025-04-28 15:30:58,670 - INFO - 批量插入响应状态码: 200
2025-04-28 15:30:58,670 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 07:30:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '46A6810D-260B-7C8B-AE97-5519A0495516', 'x-acs-trace-id': '95ce9d03cfebbf55c743781b323c3008', 'etag': '1VdPUI7iG+h9YmJ0IJfRlAg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 15:30:58,670 - INFO - 批量插入响应体: {'result': ['FINST-UW9663715WYUTFV0DULT853BW8TC28K2ER0AM1', 'FINST-UW9663715WYUTFV0DULT853BW8TC28K2ER0AM2']}
2025-04-28 15:30:58,670 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-04-28 15:30:58,670 - INFO - 成功插入的数据ID: ['FINST-UW9663715WYUTFV0DULT853BW8TC28K2ER0AM1', 'FINST-UW9663715WYUTFV0DULT853BW8TC28K2ER0AM2']
2025-04-28 15:31:03,686 - INFO - 批量插入完成，共 2 条记录
2025-04-28 15:31:03,686 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-04-28 15:31:03,686 - INFO - 开始处理日期: 2025-04-28
2025-04-28 15:31:03,686 - INFO - Request Parameters - Page 1:
2025-04-28 15:31:03,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:31:03,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:31:04,092 - INFO - Response - Page 1:
2025-04-28 15:31:04,108 - INFO - 第 1 页获取到 1 条记录
2025-04-28 15:31:04,311 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 15:31:04,311 - INFO - 获取到 1 条表单数据
2025-04-28 15:31:04,311 - INFO - 当前日期 2025-04-28 有 1 条MySQL数据需要处理
2025-04-28 15:31:04,311 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 15:31:04,311 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-04-28 15:32:04,326 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 15:32:04,326 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 15:32:04,326 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 15:32:04,373 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 15:32:04,373 - ERROR - 未获取到MySQL数据
2025-04-28 15:32:04,373 - INFO - 同步完成
2025-04-28 16:30:33,757 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 16:30:33,757 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 16:30:33,757 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 16:30:33,819 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 229 条记录
2025-04-28 16:30:33,819 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 16:30:33,819 - INFO - 开始处理日期: 2025-04-11
2025-04-28 16:30:33,819 - INFO - Request Parameters - Page 1:
2025-04-28 16:30:33,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:30:33,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:30:41,944 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ADDC2A11-6F2E-7A4B-BB3C-F042246CF670 Response: {'code': 'ServiceUnavailable', 'requestid': 'ADDC2A11-6F2E-7A4B-BB3C-F042246CF670', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ADDC2A11-6F2E-7A4B-BB3C-F042246CF670)
2025-04-28 16:30:41,944 - INFO - 开始处理日期: 2025-04-22
2025-04-28 16:30:41,944 - INFO - Request Parameters - Page 1:
2025-04-28 16:30:41,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:30:41,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:30:50,069 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D6E54261-8BBC-7047-A5A3-45146D30C2A6 Response: {'code': 'ServiceUnavailable', 'requestid': 'D6E54261-8BBC-7047-A5A3-45146D30C2A6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D6E54261-8BBC-7047-A5A3-45146D30C2A6)
2025-04-28 16:30:50,069 - INFO - 开始处理日期: 2025-04-23
2025-04-28 16:30:50,069 - INFO - Request Parameters - Page 1:
2025-04-28 16:30:50,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:30:50,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:30:50,804 - INFO - Response - Page 1:
2025-04-28 16:30:50,804 - INFO - 第 1 页获取到 100 条记录
2025-04-28 16:30:51,007 - INFO - Request Parameters - Page 2:
2025-04-28 16:30:51,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:30:51,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:30:56,585 - INFO - Response - Page 2:
2025-04-28 16:30:56,585 - INFO - 第 2 页获取到 100 条记录
2025-04-28 16:30:56,788 - INFO - Request Parameters - Page 3:
2025-04-28 16:30:56,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:30:56,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:30:57,554 - INFO - Response - Page 3:
2025-04-28 16:30:57,554 - INFO - 第 3 页获取到 83 条记录
2025-04-28 16:30:57,757 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 16:30:57,757 - INFO - 获取到 283 条表单数据
2025-04-28 16:30:57,757 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 16:30:57,757 - INFO - 开始批量插入 1 条新记录
2025-04-28 16:30:57,897 - INFO - 批量插入响应状态码: 200
2025-04-28 16:30:57,897 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 08:30:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'ED865DCC-06D5-7543-8D55-8A8443442511', 'x-acs-trace-id': 'f145b8a9ca6d56652346f0e30bd28e01', 'etag': '6kDuqNdEjxxBWQXZ4XTJ5GQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 16:30:57,897 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I71OTXUTOBHDTJ1VD27GW853NR7JT0AMO4']}
2025-04-28 16:30:57,897 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 16:30:57,897 - INFO - 成功插入的数据ID: ['FINST-2FD66I71OTXUTOBHDTJ1VD27GW853NR7JT0AMO4']
2025-04-28 16:31:02,913 - INFO - 批量插入完成，共 1 条记录
2025-04-28 16:31:02,913 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 16:31:02,913 - INFO - 开始处理日期: 2025-04-24
2025-04-28 16:31:02,913 - INFO - Request Parameters - Page 1:
2025-04-28 16:31:02,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:02,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:03,663 - INFO - Response - Page 1:
2025-04-28 16:31:03,663 - INFO - 第 1 页获取到 100 条记录
2025-04-28 16:31:03,866 - INFO - Request Parameters - Page 2:
2025-04-28 16:31:03,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:03,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:04,522 - INFO - Response - Page 2:
2025-04-28 16:31:04,538 - INFO - 第 2 页获取到 100 条记录
2025-04-28 16:31:04,741 - INFO - Request Parameters - Page 3:
2025-04-28 16:31:04,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:04,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:05,351 - INFO - Response - Page 3:
2025-04-28 16:31:05,351 - INFO - 第 3 页获取到 35 条记录
2025-04-28 16:31:05,554 - INFO - 查询完成，共获取到 235 条记录
2025-04-28 16:31:05,554 - INFO - 获取到 235 条表单数据
2025-04-28 16:31:05,554 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 16:31:05,554 - INFO - 开始更新记录 - 表单实例ID: FINST-VRA66VA1GMVU1NYEE3OEPDX3NKPC2FUDMAW9MZ1
2025-04-28 16:31:06,100 - INFO - 更新表单数据成功: FINST-VRA66VA1GMVU1NYEE3OEPDX3NKPC2FUDMAW9MZ1
2025-04-28 16:31:06,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:06,100 - INFO - 开始批量插入 1 条新记录
2025-04-28 16:31:06,241 - INFO - 批量插入响应状态码: 200
2025-04-28 16:31:06,241 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 08:30:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '211B843A-3E2C-7379-9B88-4ED4128E2FD9', 'x-acs-trace-id': '3589acfda364ea2235d86b9bbddd6675', 'etag': '5HHijWwgLGYSeSltP1d9Jtg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 16:31:06,241 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P915RXUN0L89LJ6IBW01W2D2B7EJT0AMN']}
2025-04-28 16:31:06,241 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 16:31:06,241 - INFO - 成功插入的数据ID: ['FINST-SWC66P915RXUN0L89LJ6IBW01W2D2B7EJT0AMN']
2025-04-28 16:31:11,257 - INFO - 批量插入完成，共 1 条记录
2025-04-28 16:31:11,257 - INFO - 日期 2025-04-24 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-04-28 16:31:11,257 - INFO - 开始处理日期: 2025-04-25
2025-04-28 16:31:11,257 - INFO - Request Parameters - Page 1:
2025-04-28 16:31:11,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:11,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:12,116 - INFO - Response - Page 1:
2025-04-28 16:31:12,116 - INFO - 第 1 页获取到 100 条记录
2025-04-28 16:31:12,319 - INFO - Request Parameters - Page 2:
2025-04-28 16:31:12,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:12,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:13,116 - INFO - Response - Page 2:
2025-04-28 16:31:13,116 - INFO - 第 2 页获取到 100 条记录
2025-04-28 16:31:13,319 - INFO - Request Parameters - Page 3:
2025-04-28 16:31:13,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:13,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:13,804 - INFO - Response - Page 3:
2025-04-28 16:31:13,804 - INFO - 第 3 页获取到 13 条记录
2025-04-28 16:31:14,007 - INFO - 查询完成，共获取到 213 条记录
2025-04-28 16:31:14,007 - INFO - 获取到 213 条表单数据
2025-04-28 16:31:14,007 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 16:31:14,007 - INFO - 开始批量插入 1 条新记录
2025-04-28 16:31:14,179 - INFO - 批量插入响应状态码: 200
2025-04-28 16:31:14,179 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 08:30:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C301515F-7E21-7AEF-8844-A6F1DFCD3DED', 'x-acs-trace-id': 'cd80fcd30d1d25c8d5778057994c3477', 'etag': '6c5f8f1kWe6Wh545RW5FVOw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 16:31:14,179 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671QOXU8M1VCUIYH78VT9KR2FBKJT0AM08']}
2025-04-28 16:31:14,179 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 16:31:14,179 - INFO - 成功插入的数据ID: ['FINST-PPA66671QOXU8M1VCUIYH78VT9KR2FBKJT0AM08']
2025-04-28 16:31:19,194 - INFO - 批量插入完成，共 1 条记录
2025-04-28 16:31:19,194 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 16:31:19,194 - INFO - 开始处理日期: 2025-04-26
2025-04-28 16:31:19,194 - INFO - Request Parameters - Page 1:
2025-04-28 16:31:19,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:19,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:19,913 - INFO - Response - Page 1:
2025-04-28 16:31:19,913 - INFO - 第 1 页获取到 100 条记录
2025-04-28 16:31:20,116 - INFO - Request Parameters - Page 2:
2025-04-28 16:31:20,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:20,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:20,804 - INFO - Response - Page 2:
2025-04-28 16:31:20,804 - INFO - 第 2 页获取到 100 条记录
2025-04-28 16:31:21,007 - INFO - Request Parameters - Page 3:
2025-04-28 16:31:21,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:21,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:21,741 - INFO - Response - Page 3:
2025-04-28 16:31:21,741 - INFO - 第 3 页获取到 82 条记录
2025-04-28 16:31:21,944 - INFO - 查询完成，共获取到 282 条记录
2025-04-28 16:31:21,944 - INFO - 获取到 282 条表单数据
2025-04-28 16:31:21,944 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 16:31:21,944 - INFO - 开始批量插入 1 条新记录
2025-04-28 16:31:22,116 - INFO - 批量插入响应状态码: 200
2025-04-28 16:31:22,116 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 08:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BB32F8A6-CE88-7FD2-A851-C5CAD36FB126', 'x-acs-trace-id': '6f2600006b480b4ac19c3e3207e925fa', 'etag': '5gYtG3qIspVTm3vK7dM6HoA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 16:31:22,116 - INFO - 批量插入响应体: {'result': ['FINST-2XF66ID1SXYUJFPGB12OX8BD05C637GQJT0AM2']}
2025-04-28 16:31:22,116 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 16:31:22,116 - INFO - 成功插入的数据ID: ['FINST-2XF66ID1SXYUJFPGB12OX8BD05C637GQJT0AM2']
2025-04-28 16:31:27,132 - INFO - 批量插入完成，共 1 条记录
2025-04-28 16:31:27,132 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 16:31:27,132 - INFO - 开始处理日期: 2025-04-27
2025-04-28 16:31:27,132 - INFO - Request Parameters - Page 1:
2025-04-28 16:31:27,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:27,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:27,788 - INFO - Response - Page 1:
2025-04-28 16:31:27,788 - INFO - 第 1 页获取到 100 条记录
2025-04-28 16:31:27,991 - INFO - Request Parameters - Page 2:
2025-04-28 16:31:27,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:27,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:28,678 - INFO - Response - Page 2:
2025-04-28 16:31:28,678 - INFO - 第 2 页获取到 100 条记录
2025-04-28 16:31:28,882 - INFO - Request Parameters - Page 3:
2025-04-28 16:31:28,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:28,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:29,491 - INFO - Response - Page 3:
2025-04-28 16:31:29,491 - INFO - 第 3 页获取到 25 条记录
2025-04-28 16:31:29,694 - INFO - 查询完成，共获取到 225 条记录
2025-04-28 16:31:29,694 - INFO - 获取到 225 条表单数据
2025-04-28 16:31:29,694 - INFO - 当前日期 2025-04-27 有 217 条MySQL数据需要处理
2025-04-28 16:31:29,694 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD1
2025-04-28 16:31:30,147 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMD1
2025-04-28 16:31:30,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4900.23}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4900.23}, {'field': 'order_count', 'old_value': 0, 'new_value': 289}]
2025-04-28 16:31:30,147 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF1
2025-04-28 16:31:30,897 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMF1
2025-04-28 16:31:30,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1058.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1058.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 102}]
2025-04-28 16:31:30,897 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG1
2025-04-28 16:31:31,350 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMG1
2025-04-28 16:31:31,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9596.88}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9596.88}, {'field': 'order_count', 'old_value': 0, 'new_value': 17}]
2025-04-28 16:31:31,350 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH1
2025-04-28 16:31:31,757 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMH1
2025-04-28 16:31:31,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 100.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 100.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:31,757 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI1
2025-04-28 16:31:32,257 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMI1
2025-04-28 16:31:32,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 66000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 66000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:32,257 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ1
2025-04-28 16:31:32,741 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMJ1
2025-04-28 16:31:32,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13524.3}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13524.3}, {'field': 'order_count', 'old_value': 0, 'new_value': 116}]
2025-04-28 16:31:32,741 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AML1
2025-04-28 16:31:33,210 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AML1
2025-04-28 16:31:33,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 170.7}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 170.7}, {'field': 'order_count', 'old_value': 0, 'new_value': 7}]
2025-04-28 16:31:33,210 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMM1
2025-04-28 16:31:33,663 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMM1
2025-04-28 16:31:33,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:33,663 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMO1
2025-04-28 16:31:34,147 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMO1
2025-04-28 16:31:34,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3311.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3311.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 29}]
2025-04-28 16:31:34,147 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMP1
2025-04-28 16:31:34,600 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMP1
2025-04-28 16:31:34,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 833.2}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 833.2}, {'field': 'order_count', 'old_value': 0, 'new_value': 12}]
2025-04-28 16:31:34,600 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMQ1
2025-04-28 16:31:35,007 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMQ1
2025-04-28 16:31:35,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5158.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5158.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 219}]
2025-04-28 16:31:35,007 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMR1
2025-04-28 16:31:35,475 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMR1
2025-04-28 16:31:35,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4949.19}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4949.19}, {'field': 'order_count', 'old_value': 0, 'new_value': 38}]
2025-04-28 16:31:35,475 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMW1
2025-04-28 16:31:35,928 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMW1
2025-04-28 16:31:35,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:35,928 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMY1
2025-04-28 16:31:36,382 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMY1
2025-04-28 16:31:36,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1250.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1250.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 119}]
2025-04-28 16:31:36,382 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMZ1
2025-04-28 16:31:36,835 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AMZ1
2025-04-28 16:31:36,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 228407.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 228407.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3976}]
2025-04-28 16:31:36,835 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM02
2025-04-28 16:31:37,288 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM02
2025-04-28 16:31:37,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 99.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 99.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:37,288 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM12
2025-04-28 16:31:37,757 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM12
2025-04-28 16:31:37,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 421.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 421.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 14}]
2025-04-28 16:31:37,757 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM22
2025-04-28 16:31:38,225 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM22
2025-04-28 16:31:38,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-04-28 16:31:38,225 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM32
2025-04-28 16:31:38,757 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM32
2025-04-28 16:31:38,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 156500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 156500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:38,757 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM42
2025-04-28 16:31:39,194 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM42
2025-04-28 16:31:39,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6398.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6398.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-04-28 16:31:39,194 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM72
2025-04-28 16:31:39,632 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM72
2025-04-28 16:31:39,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2453.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2453.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-04-28 16:31:39,632 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM92
2025-04-28 16:31:40,116 - INFO - 更新表单数据成功: FINST-90D66XA1NPYUI7039W5QA46USA1F3WTNOG0AM92
2025-04-28 16:31:40,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1197.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1197.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 7}]
2025-04-28 16:31:40,116 - INFO - 开始批量插入 14 条新记录
2025-04-28 16:31:40,288 - INFO - 批量插入响应状态码: 200
2025-04-28 16:31:40,288 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 08:31:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '670', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9DE6A33A-5FED-73DD-B0FE-496ED4BE4C76', 'x-acs-trace-id': '33fa492b9589f3d226030d023c404be9', 'etag': '6PkRhlE35r8pxn9DyNaTqjg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 16:31:40,288 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ21H4KT0AM1', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM2', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM3', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM4', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM5', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM6', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM7', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM8', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM9', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMA', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMB', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMC', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMD', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AME']}
2025-04-28 16:31:40,288 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-04-28 16:31:40,288 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ21H4KT0AM1', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM2', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM3', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM4', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM5', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM6', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM7', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM8', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AM9', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMA', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMB', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMC', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AMD', 'FINST-YPE66RB1ZXYU9KMXCO3D3BX0IXZZ22H4KT0AME']
2025-04-28 16:31:45,303 - INFO - 批量插入完成，共 14 条记录
2025-04-28 16:31:45,303 - INFO - 日期 2025-04-27 处理完成 - 更新: 22 条，插入: 14 条，错误: 0 条
2025-04-28 16:31:45,303 - INFO - 开始处理日期: 2025-04-28
2025-04-28 16:31:45,303 - INFO - Request Parameters - Page 1:
2025-04-28 16:31:45,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 16:31:45,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 16:31:45,741 - INFO - Response - Page 1:
2025-04-28 16:31:45,741 - INFO - 第 1 页获取到 1 条记录
2025-04-28 16:31:45,944 - INFO - 查询完成，共获取到 1 条记录
2025-04-28 16:31:45,944 - INFO - 获取到 1 条表单数据
2025-04-28 16:31:45,944 - INFO - 当前日期 2025-04-28 有 3 条MySQL数据需要处理
2025-04-28 16:31:45,944 - INFO - 开始批量插入 2 条新记录
2025-04-28 16:31:46,085 - INFO - 批量插入响应状态码: 200
2025-04-28 16:31:46,100 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 08:31:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2460BF74-A56D-7900-A735-9E168B27EDD3', 'x-acs-trace-id': 'f505797ef845bed4b8657dd98137b989', 'etag': '1DPXSA63uSiDC8eWL86xntg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 16:31:46,100 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1H2WUCIAY9KDEV92OHB7439Y8KT0AMUI', 'FINST-FQD66YB1H2WUCIAY9KDEV92OHB7439Y8KT0AMVI']}
2025-04-28 16:31:46,100 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-04-28 16:31:46,100 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1H2WUCIAY9KDEV92OHB7439Y8KT0AMUI', 'FINST-FQD66YB1H2WUCIAY9KDEV92OHB7439Y8KT0AMVI']
2025-04-28 16:31:51,116 - INFO - 批量插入完成，共 2 条记录
2025-04-28 16:31:51,116 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-04-28 16:31:51,116 - INFO - 数据同步完成！更新: 23 条，插入: 20 条，错误: 2 条
2025-04-28 16:32:51,131 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 16:32:51,131 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 16:32:51,131 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 16:32:51,178 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 16:32:51,178 - ERROR - 未获取到MySQL数据
2025-04-28 16:32:51,178 - INFO - 同步完成
2025-04-28 17:30:33,734 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 17:30:33,734 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 17:30:33,734 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 17:30:33,797 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 231 条记录
2025-04-28 17:30:33,797 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 17:30:33,797 - INFO - 开始处理日期: 2025-04-11
2025-04-28 17:30:33,797 - INFO - Request Parameters - Page 1:
2025-04-28 17:30:33,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:33,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:41,922 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 32EAA0F7-89F4-7E67-9731-6C0803BEC7A4 Response: {'code': 'ServiceUnavailable', 'requestid': '32EAA0F7-89F4-7E67-9731-6C0803BEC7A4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 32EAA0F7-89F4-7E67-9731-6C0803BEC7A4)
2025-04-28 17:30:41,922 - INFO - 开始处理日期: 2025-04-22
2025-04-28 17:30:41,922 - INFO - Request Parameters - Page 1:
2025-04-28 17:30:41,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:41,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:50,031 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CEDE4004-FB11-7249-972E-512565587F5D Response: {'code': 'ServiceUnavailable', 'requestid': 'CEDE4004-FB11-7249-972E-512565587F5D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CEDE4004-FB11-7249-972E-512565587F5D)
2025-04-28 17:30:50,031 - INFO - 开始处理日期: 2025-04-23
2025-04-28 17:30:50,031 - INFO - Request Parameters - Page 1:
2025-04-28 17:30:50,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:50,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:50,875 - INFO - Response - Page 1:
2025-04-28 17:30:50,875 - INFO - 第 1 页获取到 100 条记录
2025-04-28 17:30:51,078 - INFO - Request Parameters - Page 2:
2025-04-28 17:30:51,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:51,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:51,828 - INFO - Response - Page 2:
2025-04-28 17:30:51,828 - INFO - 第 2 页获取到 100 条记录
2025-04-28 17:30:52,031 - INFO - Request Parameters - Page 3:
2025-04-28 17:30:52,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:52,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:52,765 - INFO - Response - Page 3:
2025-04-28 17:30:52,765 - INFO - 第 3 页获取到 84 条记录
2025-04-28 17:30:52,968 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 17:30:52,968 - INFO - 获取到 284 条表单数据
2025-04-28 17:30:52,968 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 17:30:52,968 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 17:30:52,968 - INFO - 开始处理日期: 2025-04-24
2025-04-28 17:30:52,968 - INFO - Request Parameters - Page 1:
2025-04-28 17:30:52,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:52,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:53,703 - INFO - Response - Page 1:
2025-04-28 17:30:53,703 - INFO - 第 1 页获取到 100 条记录
2025-04-28 17:30:53,906 - INFO - Request Parameters - Page 2:
2025-04-28 17:30:53,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:53,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:54,593 - INFO - Response - Page 2:
2025-04-28 17:30:54,593 - INFO - 第 2 页获取到 100 条记录
2025-04-28 17:30:54,797 - INFO - Request Parameters - Page 3:
2025-04-28 17:30:54,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:54,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:55,406 - INFO - Response - Page 3:
2025-04-28 17:30:55,406 - INFO - 第 3 页获取到 36 条记录
2025-04-28 17:30:55,609 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 17:30:55,609 - INFO - 获取到 236 条表单数据
2025-04-28 17:30:55,609 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 17:30:55,609 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 17:30:55,609 - INFO - 开始处理日期: 2025-04-25
2025-04-28 17:30:55,609 - INFO - Request Parameters - Page 1:
2025-04-28 17:30:55,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:55,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:56,390 - INFO - Response - Page 1:
2025-04-28 17:30:56,390 - INFO - 第 1 页获取到 100 条记录
2025-04-28 17:30:56,593 - INFO - Request Parameters - Page 2:
2025-04-28 17:30:56,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:56,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:57,328 - INFO - Response - Page 2:
2025-04-28 17:30:57,328 - INFO - 第 2 页获取到 100 条记录
2025-04-28 17:30:57,531 - INFO - Request Parameters - Page 3:
2025-04-28 17:30:57,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:57,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:58,125 - INFO - Response - Page 3:
2025-04-28 17:30:58,125 - INFO - 第 3 页获取到 14 条记录
2025-04-28 17:30:58,328 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 17:30:58,328 - INFO - 获取到 214 条表单数据
2025-04-28 17:30:58,328 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 17:30:58,328 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 17:30:58,328 - INFO - 开始处理日期: 2025-04-26
2025-04-28 17:30:58,328 - INFO - Request Parameters - Page 1:
2025-04-28 17:30:58,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:58,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:30:59,093 - INFO - Response - Page 1:
2025-04-28 17:30:59,093 - INFO - 第 1 页获取到 100 条记录
2025-04-28 17:30:59,297 - INFO - Request Parameters - Page 2:
2025-04-28 17:30:59,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:30:59,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:31:00,062 - INFO - Response - Page 2:
2025-04-28 17:31:00,062 - INFO - 第 2 页获取到 100 条记录
2025-04-28 17:31:00,265 - INFO - Request Parameters - Page 3:
2025-04-28 17:31:00,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:31:00,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:31:00,922 - INFO - Response - Page 3:
2025-04-28 17:31:00,922 - INFO - 第 3 页获取到 83 条记录
2025-04-28 17:31:01,125 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 17:31:01,125 - INFO - 获取到 283 条表单数据
2025-04-28 17:31:01,125 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 17:31:01,125 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 17:31:01,125 - INFO - 开始处理日期: 2025-04-27
2025-04-28 17:31:01,125 - INFO - Request Parameters - Page 1:
2025-04-28 17:31:01,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:31:01,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:31:01,859 - INFO - Response - Page 1:
2025-04-28 17:31:01,859 - INFO - 第 1 页获取到 100 条记录
2025-04-28 17:31:02,062 - INFO - Request Parameters - Page 2:
2025-04-28 17:31:02,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:31:02,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:31:02,765 - INFO - Response - Page 2:
2025-04-28 17:31:02,765 - INFO - 第 2 页获取到 100 条记录
2025-04-28 17:31:02,968 - INFO - Request Parameters - Page 3:
2025-04-28 17:31:02,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:31:02,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:31:03,562 - INFO - Response - Page 3:
2025-04-28 17:31:03,562 - INFO - 第 3 页获取到 39 条记录
2025-04-28 17:31:03,765 - INFO - 查询完成，共获取到 239 条记录
2025-04-28 17:31:03,765 - INFO - 获取到 239 条表单数据
2025-04-28 17:31:03,765 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 17:31:03,765 - INFO - 开始批量插入 2 条新记录
2025-04-28 17:31:03,921 - INFO - 批量插入响应状态码: 200
2025-04-28 17:31:03,937 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 09:30:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E286F428-12DF-7032-82CC-E6AFBD238134', 'x-acs-trace-id': 'd53dc306daf218528f3f78b1b7e4f93b', 'etag': '1dh1lVqC6hdBdyVn+FTcH0A6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 17:31:03,937 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671KWYU3L3A6XJF76GJME153P7IOV0AM9', 'FINST-PPA66671KWYU3L3A6XJF76GJME153P7IOV0AMA']}
2025-04-28 17:31:03,937 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-04-28 17:31:03,937 - INFO - 成功插入的数据ID: ['FINST-PPA66671KWYU3L3A6XJF76GJME153P7IOV0AM9', 'FINST-PPA66671KWYU3L3A6XJF76GJME153P7IOV0AMA']
2025-04-28 17:31:08,953 - INFO - 批量插入完成，共 2 条记录
2025-04-28 17:31:08,953 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-04-28 17:31:08,953 - INFO - 开始处理日期: 2025-04-28
2025-04-28 17:31:08,953 - INFO - Request Parameters - Page 1:
2025-04-28 17:31:08,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 17:31:08,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 17:31:09,453 - INFO - Response - Page 1:
2025-04-28 17:31:09,453 - INFO - 第 1 页获取到 3 条记录
2025-04-28 17:31:09,656 - INFO - 查询完成，共获取到 3 条记录
2025-04-28 17:31:09,656 - INFO - 获取到 3 条表单数据
2025-04-28 17:31:09,656 - INFO - 当前日期 2025-04-28 有 3 条MySQL数据需要处理
2025-04-28 17:31:09,656 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 17:31:09,656 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-04-28 17:32:09,671 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 17:32:09,671 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 17:32:09,671 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 17:32:09,718 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 17:32:09,718 - ERROR - 未获取到MySQL数据
2025-04-28 17:32:09,718 - INFO - 同步完成
2025-04-28 18:30:33,930 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 18:30:33,930 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 18:30:33,930 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 18:30:34,008 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 231 条记录
2025-04-28 18:30:34,008 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 18:30:34,008 - INFO - 开始处理日期: 2025-04-11
2025-04-28 18:30:34,008 - INFO - Request Parameters - Page 1:
2025-04-28 18:30:34,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:34,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:42,133 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 124E13FE-4728-7C2C-B4A0-E9D379AF1435 Response: {'code': 'ServiceUnavailable', 'requestid': '124E13FE-4728-7C2C-B4A0-E9D379AF1435', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 124E13FE-4728-7C2C-B4A0-E9D379AF1435)
2025-04-28 18:30:42,133 - INFO - 开始处理日期: 2025-04-22
2025-04-28 18:30:42,133 - INFO - Request Parameters - Page 1:
2025-04-28 18:30:42,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:42,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:50,258 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F230A6DA-F394-7699-9C86-CFB701B5E860 Response: {'code': 'ServiceUnavailable', 'requestid': 'F230A6DA-F394-7699-9C86-CFB701B5E860', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F230A6DA-F394-7699-9C86-CFB701B5E860)
2025-04-28 18:30:50,258 - INFO - 开始处理日期: 2025-04-23
2025-04-28 18:30:50,258 - INFO - Request Parameters - Page 1:
2025-04-28 18:30:50,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:50,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:51,055 - INFO - Response - Page 1:
2025-04-28 18:30:51,055 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:30:51,258 - INFO - Request Parameters - Page 2:
2025-04-28 18:30:51,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:51,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:54,993 - INFO - Response - Page 2:
2025-04-28 18:30:54,993 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:30:55,196 - INFO - Request Parameters - Page 3:
2025-04-28 18:30:55,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:55,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:55,977 - INFO - Response - Page 3:
2025-04-28 18:30:55,977 - INFO - 第 3 页获取到 84 条记录
2025-04-28 18:30:56,180 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 18:30:56,180 - INFO - 获取到 284 条表单数据
2025-04-28 18:30:56,180 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 18:30:56,180 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:30:56,180 - INFO - 开始处理日期: 2025-04-24
2025-04-28 18:30:56,180 - INFO - Request Parameters - Page 1:
2025-04-28 18:30:56,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:56,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:56,899 - INFO - Response - Page 1:
2025-04-28 18:30:56,899 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:30:57,102 - INFO - Request Parameters - Page 2:
2025-04-28 18:30:57,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:57,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:57,852 - INFO - Response - Page 2:
2025-04-28 18:30:57,852 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:30:58,055 - INFO - Request Parameters - Page 3:
2025-04-28 18:30:58,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:58,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:58,664 - INFO - Response - Page 3:
2025-04-28 18:30:58,664 - INFO - 第 3 页获取到 36 条记录
2025-04-28 18:30:58,867 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 18:30:58,867 - INFO - 获取到 236 条表单数据
2025-04-28 18:30:58,867 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 18:30:58,867 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:30:58,867 - INFO - 开始处理日期: 2025-04-25
2025-04-28 18:30:58,867 - INFO - Request Parameters - Page 1:
2025-04-28 18:30:58,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:58,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:30:59,680 - INFO - Response - Page 1:
2025-04-28 18:30:59,680 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:30:59,883 - INFO - Request Parameters - Page 2:
2025-04-28 18:30:59,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:30:59,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:00,586 - INFO - Response - Page 2:
2025-04-28 18:31:00,586 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:31:00,789 - INFO - Request Parameters - Page 3:
2025-04-28 18:31:00,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:00,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:01,336 - INFO - Response - Page 3:
2025-04-28 18:31:01,336 - INFO - 第 3 页获取到 14 条记录
2025-04-28 18:31:01,539 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 18:31:01,539 - INFO - 获取到 214 条表单数据
2025-04-28 18:31:01,539 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 18:31:01,539 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:31:01,539 - INFO - 开始处理日期: 2025-04-26
2025-04-28 18:31:01,539 - INFO - Request Parameters - Page 1:
2025-04-28 18:31:01,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:01,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:02,305 - INFO - Response - Page 1:
2025-04-28 18:31:02,305 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:31:02,508 - INFO - Request Parameters - Page 2:
2025-04-28 18:31:02,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:02,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:03,149 - INFO - Response - Page 2:
2025-04-28 18:31:03,149 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:31:03,352 - INFO - Request Parameters - Page 3:
2025-04-28 18:31:03,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:03,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:04,071 - INFO - Response - Page 3:
2025-04-28 18:31:04,071 - INFO - 第 3 页获取到 83 条记录
2025-04-28 18:31:04,274 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 18:31:04,274 - INFO - 获取到 283 条表单数据
2025-04-28 18:31:04,274 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 18:31:04,274 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:31:04,274 - INFO - 开始处理日期: 2025-04-27
2025-04-28 18:31:04,274 - INFO - Request Parameters - Page 1:
2025-04-28 18:31:04,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:04,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:04,977 - INFO - Response - Page 1:
2025-04-28 18:31:04,977 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:31:05,180 - INFO - Request Parameters - Page 2:
2025-04-28 18:31:05,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:05,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:05,883 - INFO - Response - Page 2:
2025-04-28 18:31:05,883 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:31:06,086 - INFO - Request Parameters - Page 3:
2025-04-28 18:31:06,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:06,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:06,742 - INFO - Response - Page 3:
2025-04-28 18:31:06,742 - INFO - 第 3 页获取到 41 条记录
2025-04-28 18:31:06,946 - INFO - 查询完成，共获取到 241 条记录
2025-04-28 18:31:06,946 - INFO - 获取到 241 条表单数据
2025-04-28 18:31:06,946 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 18:31:06,946 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:31:06,946 - INFO - 开始处理日期: 2025-04-28
2025-04-28 18:31:06,946 - INFO - Request Parameters - Page 1:
2025-04-28 18:31:06,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:31:06,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:31:07,414 - INFO - Response - Page 1:
2025-04-28 18:31:07,414 - INFO - 第 1 页获取到 3 条记录
2025-04-28 18:31:07,617 - INFO - 查询完成，共获取到 3 条记录
2025-04-28 18:31:07,617 - INFO - 获取到 3 条表单数据
2025-04-28 18:31:07,617 - INFO - 当前日期 2025-04-28 有 3 条MySQL数据需要处理
2025-04-28 18:31:07,617 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:31:07,617 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-28 18:32:07,633 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 18:32:07,633 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 18:32:07,633 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 18:32:07,680 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 18:32:07,680 - ERROR - 未获取到MySQL数据
2025-04-28 18:32:07,680 - INFO - 同步完成
2025-04-28 19:30:33,713 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 19:30:33,713 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 19:30:33,713 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 19:30:33,776 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 231 条记录
2025-04-28 19:30:33,776 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 19:30:33,791 - INFO - 开始处理日期: 2025-04-11
2025-04-28 19:30:33,791 - INFO - Request Parameters - Page 1:
2025-04-28 19:30:33,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:30:33,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:30:41,901 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D16FB0FC-0B2D-7647-9847-3BC1671C4425 Response: {'code': 'ServiceUnavailable', 'requestid': 'D16FB0FC-0B2D-7647-9847-3BC1671C4425', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D16FB0FC-0B2D-7647-9847-3BC1671C4425)
2025-04-28 19:30:41,901 - INFO - 开始处理日期: 2025-04-22
2025-04-28 19:30:41,901 - INFO - Request Parameters - Page 1:
2025-04-28 19:30:41,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:30:41,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:30:46,354 - INFO - Response - Page 1:
2025-04-28 19:30:46,354 - INFO - 第 1 页获取到 100 条记录
2025-04-28 19:30:46,557 - INFO - Request Parameters - Page 2:
2025-04-28 19:30:46,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:30:46,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:30:47,307 - INFO - Response - Page 2:
2025-04-28 19:30:47,307 - INFO - 第 2 页获取到 100 条记录
2025-04-28 19:30:47,510 - INFO - Request Parameters - Page 3:
2025-04-28 19:30:47,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:30:47,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:30:53,901 - INFO - Response - Page 3:
2025-04-28 19:30:53,901 - INFO - 第 3 页获取到 6 条记录
2025-04-28 19:30:54,104 - INFO - 查询完成，共获取到 206 条记录
2025-04-28 19:30:54,104 - INFO - 获取到 206 条表单数据
2025-04-28 19:30:54,104 - INFO - 当前日期 2025-04-22 有 1 条MySQL数据需要处理
2025-04-28 19:30:54,104 - INFO - 开始批量插入 1 条新记录
2025-04-28 19:30:54,260 - INFO - 批量插入响应状态码: 200
2025-04-28 19:30:54,260 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 11:30:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5D5F40B2-FBE2-711C-A415-EE6B7A0F7198', 'x-acs-trace-id': 'c310660290ae9f74501948ac5ecdd725', 'etag': '596sBlDEBQay45ly83vToig9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 19:30:54,260 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X81E3ZUOPIJ6G8SD9RMR2ZF3VV0ZZ0AM0']}
2025-04-28 19:30:54,260 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 19:30:54,260 - INFO - 成功插入的数据ID: ['FINST-X3E66X81E3ZUOPIJ6G8SD9RMR2ZF3VV0ZZ0AM0']
2025-04-28 19:30:59,276 - INFO - 批量插入完成，共 1 条记录
2025-04-28 19:30:59,276 - INFO - 日期 2025-04-22 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-04-28 19:30:59,276 - INFO - 开始处理日期: 2025-04-23
2025-04-28 19:30:59,276 - INFO - Request Parameters - Page 1:
2025-04-28 19:30:59,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:30:59,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:30:59,994 - INFO - Response - Page 1:
2025-04-28 19:30:59,994 - INFO - 第 1 页获取到 100 条记录
2025-04-28 19:31:00,197 - INFO - Request Parameters - Page 2:
2025-04-28 19:31:00,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:00,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:00,900 - INFO - Response - Page 2:
2025-04-28 19:31:00,900 - INFO - 第 2 页获取到 100 条记录
2025-04-28 19:31:01,104 - INFO - Request Parameters - Page 3:
2025-04-28 19:31:01,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:01,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:01,807 - INFO - Response - Page 3:
2025-04-28 19:31:01,807 - INFO - 第 3 页获取到 84 条记录
2025-04-28 19:31:02,010 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 19:31:02,010 - INFO - 获取到 284 条表单数据
2025-04-28 19:31:02,010 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 19:31:02,010 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 19:31:02,010 - INFO - 开始处理日期: 2025-04-24
2025-04-28 19:31:02,010 - INFO - Request Parameters - Page 1:
2025-04-28 19:31:02,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:02,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:02,697 - INFO - Response - Page 1:
2025-04-28 19:31:02,697 - INFO - 第 1 页获取到 100 条记录
2025-04-28 19:31:02,900 - INFO - Request Parameters - Page 2:
2025-04-28 19:31:02,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:02,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:03,588 - INFO - Response - Page 2:
2025-04-28 19:31:03,588 - INFO - 第 2 页获取到 100 条记录
2025-04-28 19:31:03,791 - INFO - Request Parameters - Page 3:
2025-04-28 19:31:03,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:03,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:04,463 - INFO - Response - Page 3:
2025-04-28 19:31:04,463 - INFO - 第 3 页获取到 36 条记录
2025-04-28 19:31:04,666 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 19:31:04,666 - INFO - 获取到 236 条表单数据
2025-04-28 19:31:04,666 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 19:31:04,666 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 19:31:04,666 - INFO - 开始处理日期: 2025-04-25
2025-04-28 19:31:04,666 - INFO - Request Parameters - Page 1:
2025-04-28 19:31:04,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:04,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:05,400 - INFO - Response - Page 1:
2025-04-28 19:31:05,400 - INFO - 第 1 页获取到 100 条记录
2025-04-28 19:31:05,604 - INFO - Request Parameters - Page 2:
2025-04-28 19:31:05,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:05,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:06,291 - INFO - Response - Page 2:
2025-04-28 19:31:06,291 - INFO - 第 2 页获取到 100 条记录
2025-04-28 19:31:06,494 - INFO - Request Parameters - Page 3:
2025-04-28 19:31:06,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:06,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:07,088 - INFO - Response - Page 3:
2025-04-28 19:31:07,088 - INFO - 第 3 页获取到 14 条记录
2025-04-28 19:31:07,291 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 19:31:07,291 - INFO - 获取到 214 条表单数据
2025-04-28 19:31:07,291 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 19:31:07,291 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 19:31:07,291 - INFO - 开始处理日期: 2025-04-26
2025-04-28 19:31:07,291 - INFO - Request Parameters - Page 1:
2025-04-28 19:31:07,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:07,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:08,088 - INFO - Response - Page 1:
2025-04-28 19:31:08,088 - INFO - 第 1 页获取到 100 条记录
2025-04-28 19:31:08,307 - INFO - Request Parameters - Page 2:
2025-04-28 19:31:08,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:08,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:09,010 - INFO - Response - Page 2:
2025-04-28 19:31:09,025 - INFO - 第 2 页获取到 100 条记录
2025-04-28 19:31:09,229 - INFO - Request Parameters - Page 3:
2025-04-28 19:31:09,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:09,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:09,885 - INFO - Response - Page 3:
2025-04-28 19:31:09,885 - INFO - 第 3 页获取到 83 条记录
2025-04-28 19:31:10,088 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 19:31:10,088 - INFO - 获取到 283 条表单数据
2025-04-28 19:31:10,088 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 19:31:10,088 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 19:31:10,088 - INFO - 开始处理日期: 2025-04-27
2025-04-28 19:31:10,088 - INFO - Request Parameters - Page 1:
2025-04-28 19:31:10,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:10,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:10,791 - INFO - Response - Page 1:
2025-04-28 19:31:10,791 - INFO - 第 1 页获取到 100 条记录
2025-04-28 19:31:10,994 - INFO - Request Parameters - Page 2:
2025-04-28 19:31:10,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:10,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:11,697 - INFO - Response - Page 2:
2025-04-28 19:31:11,697 - INFO - 第 2 页获取到 100 条记录
2025-04-28 19:31:11,900 - INFO - Request Parameters - Page 3:
2025-04-28 19:31:11,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:11,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:12,510 - INFO - Response - Page 3:
2025-04-28 19:31:12,510 - INFO - 第 3 页获取到 41 条记录
2025-04-28 19:31:12,713 - INFO - 查询完成，共获取到 241 条记录
2025-04-28 19:31:12,713 - INFO - 获取到 241 条表单数据
2025-04-28 19:31:12,713 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 19:31:12,713 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 19:31:12,713 - INFO - 开始处理日期: 2025-04-28
2025-04-28 19:31:12,713 - INFO - Request Parameters - Page 1:
2025-04-28 19:31:12,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 19:31:12,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 19:31:13,182 - INFO - Response - Page 1:
2025-04-28 19:31:13,182 - INFO - 第 1 页获取到 3 条记录
2025-04-28 19:31:13,385 - INFO - 查询完成，共获取到 3 条记录
2025-04-28 19:31:13,385 - INFO - 获取到 3 条表单数据
2025-04-28 19:31:13,385 - INFO - 当前日期 2025-04-28 有 3 条MySQL数据需要处理
2025-04-28 19:31:13,385 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 19:31:13,385 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-04-28 19:32:13,400 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 19:32:13,400 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 19:32:13,400 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 19:32:13,447 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 19:32:13,447 - ERROR - 未获取到MySQL数据
2025-04-28 19:32:13,447 - INFO - 同步完成
2025-04-28 20:30:33,737 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 20:30:33,737 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 20:30:33,737 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 20:30:33,800 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 231 条记录
2025-04-28 20:30:33,800 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 20:30:33,800 - INFO - 开始处理日期: 2025-04-11
2025-04-28 20:30:33,800 - INFO - Request Parameters - Page 1:
2025-04-28 20:30:33,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:33,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:41,941 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 865BCA74-630F-7475-B894-B57248046DDA Response: {'code': 'ServiceUnavailable', 'requestid': '865BCA74-630F-7475-B894-B57248046DDA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 865BCA74-630F-7475-B894-B57248046DDA)
2025-04-28 20:30:41,941 - INFO - 开始处理日期: 2025-04-22
2025-04-28 20:30:41,941 - INFO - Request Parameters - Page 1:
2025-04-28 20:30:41,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:41,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:50,050 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 81B294C3-F13E-73A8-BDC8-9E0C6CD59799 Response: {'code': 'ServiceUnavailable', 'requestid': '81B294C3-F13E-73A8-BDC8-9E0C6CD59799', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 81B294C3-F13E-73A8-BDC8-9E0C6CD59799)
2025-04-28 20:30:50,050 - INFO - 开始处理日期: 2025-04-23
2025-04-28 20:30:50,050 - INFO - Request Parameters - Page 1:
2025-04-28 20:30:50,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:50,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:50,722 - INFO - Response - Page 1:
2025-04-28 20:30:50,722 - INFO - 第 1 页获取到 100 条记录
2025-04-28 20:30:50,925 - INFO - Request Parameters - Page 2:
2025-04-28 20:30:50,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:50,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:51,597 - INFO - Response - Page 2:
2025-04-28 20:30:51,597 - INFO - 第 2 页获取到 100 条记录
2025-04-28 20:30:51,800 - INFO - Request Parameters - Page 3:
2025-04-28 20:30:51,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:51,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:52,550 - INFO - Response - Page 3:
2025-04-28 20:30:52,550 - INFO - 第 3 页获取到 84 条记录
2025-04-28 20:30:52,753 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 20:30:52,753 - INFO - 获取到 284 条表单数据
2025-04-28 20:30:52,753 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 20:30:52,753 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 20:30:52,753 - INFO - 开始处理日期: 2025-04-24
2025-04-28 20:30:52,753 - INFO - Request Parameters - Page 1:
2025-04-28 20:30:52,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:52,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:53,409 - INFO - Response - Page 1:
2025-04-28 20:30:53,409 - INFO - 第 1 页获取到 100 条记录
2025-04-28 20:30:53,612 - INFO - Request Parameters - Page 2:
2025-04-28 20:30:53,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:53,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:54,378 - INFO - Response - Page 2:
2025-04-28 20:30:54,378 - INFO - 第 2 页获取到 100 条记录
2025-04-28 20:30:54,581 - INFO - Request Parameters - Page 3:
2025-04-28 20:30:54,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:54,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:55,190 - INFO - Response - Page 3:
2025-04-28 20:30:55,190 - INFO - 第 3 页获取到 36 条记录
2025-04-28 20:30:55,394 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 20:30:55,394 - INFO - 获取到 236 条表单数据
2025-04-28 20:30:55,394 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 20:30:55,394 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 20:30:55,394 - INFO - 开始处理日期: 2025-04-25
2025-04-28 20:30:55,394 - INFO - Request Parameters - Page 1:
2025-04-28 20:30:55,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:55,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:56,097 - INFO - Response - Page 1:
2025-04-28 20:30:56,097 - INFO - 第 1 页获取到 100 条记录
2025-04-28 20:30:56,300 - INFO - Request Parameters - Page 2:
2025-04-28 20:30:56,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:56,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:57,003 - INFO - Response - Page 2:
2025-04-28 20:30:57,003 - INFO - 第 2 页获取到 100 条记录
2025-04-28 20:30:57,206 - INFO - Request Parameters - Page 3:
2025-04-28 20:30:57,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:57,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:57,784 - INFO - Response - Page 3:
2025-04-28 20:30:57,784 - INFO - 第 3 页获取到 14 条记录
2025-04-28 20:30:57,987 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 20:30:57,987 - INFO - 获取到 214 条表单数据
2025-04-28 20:30:57,987 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 20:30:57,987 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 20:30:57,987 - INFO - 开始处理日期: 2025-04-26
2025-04-28 20:30:57,987 - INFO - Request Parameters - Page 1:
2025-04-28 20:30:57,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:57,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:58,737 - INFO - Response - Page 1:
2025-04-28 20:30:58,737 - INFO - 第 1 页获取到 100 条记录
2025-04-28 20:30:58,940 - INFO - Request Parameters - Page 2:
2025-04-28 20:30:58,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:58,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:30:59,675 - INFO - Response - Page 2:
2025-04-28 20:30:59,675 - INFO - 第 2 页获取到 100 条记录
2025-04-28 20:30:59,878 - INFO - Request Parameters - Page 3:
2025-04-28 20:30:59,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:30:59,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:31:00,659 - INFO - Response - Page 3:
2025-04-28 20:31:00,659 - INFO - 第 3 页获取到 83 条记录
2025-04-28 20:31:00,862 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 20:31:00,862 - INFO - 获取到 283 条表单数据
2025-04-28 20:31:00,862 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 20:31:00,862 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 20:31:00,862 - INFO - 开始处理日期: 2025-04-27
2025-04-28 20:31:00,862 - INFO - Request Parameters - Page 1:
2025-04-28 20:31:00,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:31:00,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:31:01,565 - INFO - Response - Page 1:
2025-04-28 20:31:01,565 - INFO - 第 1 页获取到 100 条记录
2025-04-28 20:31:01,769 - INFO - Request Parameters - Page 2:
2025-04-28 20:31:01,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:31:01,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:31:02,456 - INFO - Response - Page 2:
2025-04-28 20:31:02,456 - INFO - 第 2 页获取到 100 条记录
2025-04-28 20:31:02,659 - INFO - Request Parameters - Page 3:
2025-04-28 20:31:02,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:31:02,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:31:03,300 - INFO - Response - Page 3:
2025-04-28 20:31:03,300 - INFO - 第 3 页获取到 41 条记录
2025-04-28 20:31:03,503 - INFO - 查询完成，共获取到 241 条记录
2025-04-28 20:31:03,503 - INFO - 获取到 241 条表单数据
2025-04-28 20:31:03,503 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 20:31:03,503 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 20:31:03,503 - INFO - 开始处理日期: 2025-04-28
2025-04-28 20:31:03,503 - INFO - Request Parameters - Page 1:
2025-04-28 20:31:03,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 20:31:03,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 20:31:03,956 - INFO - Response - Page 1:
2025-04-28 20:31:03,956 - INFO - 第 1 页获取到 3 条记录
2025-04-28 20:31:04,159 - INFO - 查询完成，共获取到 3 条记录
2025-04-28 20:31:04,159 - INFO - 获取到 3 条表单数据
2025-04-28 20:31:04,159 - INFO - 当前日期 2025-04-28 有 3 条MySQL数据需要处理
2025-04-28 20:31:04,159 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 20:31:04,159 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-28 20:32:04,174 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 20:32:04,174 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 20:32:04,174 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 20:32:04,221 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 20:32:04,221 - ERROR - 未获取到MySQL数据
2025-04-28 20:32:04,221 - INFO - 同步完成
2025-04-28 21:30:33,871 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 21:30:33,871 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 21:30:33,871 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 21:30:33,934 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 233 条记录
2025-04-28 21:30:33,934 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 21:30:33,934 - INFO - 开始处理日期: 2025-04-11
2025-04-28 21:30:33,934 - INFO - Request Parameters - Page 1:
2025-04-28 21:30:33,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:33,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:42,043 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E9D101E5-A119-7150-9E21-AA99E3CEB55E Response: {'code': 'ServiceUnavailable', 'requestid': 'E9D101E5-A119-7150-9E21-AA99E3CEB55E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E9D101E5-A119-7150-9E21-AA99E3CEB55E)
2025-04-28 21:30:42,043 - INFO - 开始处理日期: 2025-04-22
2025-04-28 21:30:42,043 - INFO - Request Parameters - Page 1:
2025-04-28 21:30:42,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:42,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:50,168 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8CC4905F-EA1C-7587-900D-B0CDB4A7E687 Response: {'code': 'ServiceUnavailable', 'requestid': '8CC4905F-EA1C-7587-900D-B0CDB4A7E687', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8CC4905F-EA1C-7587-900D-B0CDB4A7E687)
2025-04-28 21:30:50,168 - INFO - 开始处理日期: 2025-04-23
2025-04-28 21:30:50,168 - INFO - Request Parameters - Page 1:
2025-04-28 21:30:50,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:50,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:50,918 - INFO - Response - Page 1:
2025-04-28 21:30:50,918 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:30:51,121 - INFO - Request Parameters - Page 2:
2025-04-28 21:30:51,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:51,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:51,871 - INFO - Response - Page 2:
2025-04-28 21:30:51,871 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:30:52,074 - INFO - Request Parameters - Page 3:
2025-04-28 21:30:52,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:52,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:52,793 - INFO - Response - Page 3:
2025-04-28 21:30:52,793 - INFO - 第 3 页获取到 84 条记录
2025-04-28 21:30:52,996 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 21:30:52,996 - INFO - 获取到 284 条表单数据
2025-04-28 21:30:52,996 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 21:30:52,996 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:30:52,996 - INFO - 开始处理日期: 2025-04-24
2025-04-28 21:30:52,996 - INFO - Request Parameters - Page 1:
2025-04-28 21:30:52,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:52,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:53,699 - INFO - Response - Page 1:
2025-04-28 21:30:53,699 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:30:53,902 - INFO - Request Parameters - Page 2:
2025-04-28 21:30:53,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:53,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:54,605 - INFO - Response - Page 2:
2025-04-28 21:30:54,605 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:30:54,808 - INFO - Request Parameters - Page 3:
2025-04-28 21:30:54,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:54,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:55,371 - INFO - Response - Page 3:
2025-04-28 21:30:55,371 - INFO - 第 3 页获取到 36 条记录
2025-04-28 21:30:55,574 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 21:30:55,574 - INFO - 获取到 236 条表单数据
2025-04-28 21:30:55,574 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 21:30:55,574 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:30:55,574 - INFO - 开始处理日期: 2025-04-25
2025-04-28 21:30:55,574 - INFO - Request Parameters - Page 1:
2025-04-28 21:30:55,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:55,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:56,355 - INFO - Response - Page 1:
2025-04-28 21:30:56,355 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:30:56,558 - INFO - Request Parameters - Page 2:
2025-04-28 21:30:56,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:56,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:57,246 - INFO - Response - Page 2:
2025-04-28 21:30:57,246 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:30:57,449 - INFO - Request Parameters - Page 3:
2025-04-28 21:30:57,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:57,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:57,996 - INFO - Response - Page 3:
2025-04-28 21:30:57,996 - INFO - 第 3 页获取到 14 条记录
2025-04-28 21:30:58,199 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 21:30:58,199 - INFO - 获取到 214 条表单数据
2025-04-28 21:30:58,199 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 21:30:58,199 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:30:58,199 - INFO - 开始处理日期: 2025-04-26
2025-04-28 21:30:58,199 - INFO - Request Parameters - Page 1:
2025-04-28 21:30:58,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:58,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:58,887 - INFO - Response - Page 1:
2025-04-28 21:30:58,887 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:30:59,090 - INFO - Request Parameters - Page 2:
2025-04-28 21:30:59,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:59,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:30:59,793 - INFO - Response - Page 2:
2025-04-28 21:30:59,793 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:30:59,996 - INFO - Request Parameters - Page 3:
2025-04-28 21:30:59,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:30:59,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:31:00,715 - INFO - Response - Page 3:
2025-04-28 21:31:00,715 - INFO - 第 3 页获取到 83 条记录
2025-04-28 21:31:00,918 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 21:31:00,918 - INFO - 获取到 283 条表单数据
2025-04-28 21:31:00,918 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 21:31:00,918 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:31:00,918 - INFO - 开始处理日期: 2025-04-27
2025-04-28 21:31:00,918 - INFO - Request Parameters - Page 1:
2025-04-28 21:31:00,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:31:00,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:31:01,605 - INFO - Response - Page 1:
2025-04-28 21:31:01,605 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:31:01,808 - INFO - Request Parameters - Page 2:
2025-04-28 21:31:01,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:31:01,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:31:02,543 - INFO - Response - Page 2:
2025-04-28 21:31:02,543 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:31:02,746 - INFO - Request Parameters - Page 3:
2025-04-28 21:31:02,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:31:02,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:31:03,402 - INFO - Response - Page 3:
2025-04-28 21:31:03,402 - INFO - 第 3 页获取到 41 条记录
2025-04-28 21:31:03,605 - INFO - 查询完成，共获取到 241 条记录
2025-04-28 21:31:03,605 - INFO - 获取到 241 条表单数据
2025-04-28 21:31:03,605 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 21:31:03,605 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:31:03,605 - INFO - 开始处理日期: 2025-04-28
2025-04-28 21:31:03,605 - INFO - Request Parameters - Page 1:
2025-04-28 21:31:03,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:31:03,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:31:04,043 - INFO - Response - Page 1:
2025-04-28 21:31:04,043 - INFO - 第 1 页获取到 3 条记录
2025-04-28 21:31:04,246 - INFO - 查询完成，共获取到 3 条记录
2025-04-28 21:31:04,246 - INFO - 获取到 3 条表单数据
2025-04-28 21:31:04,246 - INFO - 当前日期 2025-04-28 有 5 条MySQL数据需要处理
2025-04-28 21:31:04,246 - INFO - 开始批量插入 2 条新记录
2025-04-28 21:31:04,387 - INFO - 批量插入响应状态码: 200
2025-04-28 21:31:04,387 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 13:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4388A8AE-E95E-7441-B6D6-5FE2539A0087', 'x-acs-trace-id': '6853dc5655f916ba43f038e90aefc4c4', 'etag': '1qxBZUUHdEi5aKUYTQheNWg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 21:31:04,387 - INFO - 批量插入响应体: {'result': ['FINST-2PF66CD1S4ZURWJ07TVYPDVLF7K52KAK941AMF1', 'FINST-2PF66CD1S4ZURWJ07TVYPDVLF7K52KAK941AMG1']}
2025-04-28 21:31:04,387 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-04-28 21:31:04,387 - INFO - 成功插入的数据ID: ['FINST-2PF66CD1S4ZURWJ07TVYPDVLF7K52KAK941AMF1', 'FINST-2PF66CD1S4ZURWJ07TVYPDVLF7K52KAK941AMG1']
2025-04-28 21:31:09,402 - INFO - 批量插入完成，共 2 条记录
2025-04-28 21:31:09,402 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-04-28 21:31:09,402 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-04-28 21:32:09,417 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 21:32:09,417 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 21:32:09,417 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 21:32:09,464 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 21:32:09,464 - ERROR - 未获取到MySQL数据
2025-04-28 21:32:09,464 - INFO - 同步完成
2025-04-28 22:30:33,911 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 22:30:33,911 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 22:30:33,911 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 22:30:33,973 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 276 条记录
2025-04-28 22:30:33,973 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 22:30:33,989 - INFO - 开始处理日期: 2025-04-11
2025-04-28 22:30:33,989 - INFO - Request Parameters - Page 1:
2025-04-28 22:30:33,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:33,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:42,098 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 51528E49-2AF6-7B33-A941-E9BE6842574D Response: {'code': 'ServiceUnavailable', 'requestid': '51528E49-2AF6-7B33-A941-E9BE6842574D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 51528E49-2AF6-7B33-A941-E9BE6842574D)
2025-04-28 22:30:42,098 - INFO - 开始处理日期: 2025-04-22
2025-04-28 22:30:42,098 - INFO - Request Parameters - Page 1:
2025-04-28 22:30:42,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:42,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:42,864 - INFO - Response - Page 1:
2025-04-28 22:30:42,864 - INFO - 第 1 页获取到 100 条记录
2025-04-28 22:30:43,067 - INFO - Request Parameters - Page 2:
2025-04-28 22:30:43,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:43,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:51,176 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 10B5458C-B23A-758D-979F-72E249D80CE5 Response: {'code': 'ServiceUnavailable', 'requestid': '10B5458C-B23A-758D-979F-72E249D80CE5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 10B5458C-B23A-758D-979F-72E249D80CE5)
2025-04-28 22:30:51,176 - INFO - 开始处理日期: 2025-04-23
2025-04-28 22:30:51,176 - INFO - Request Parameters - Page 1:
2025-04-28 22:30:51,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:51,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:51,864 - INFO - Response - Page 1:
2025-04-28 22:30:51,864 - INFO - 第 1 页获取到 100 条记录
2025-04-28 22:30:52,067 - INFO - Request Parameters - Page 2:
2025-04-28 22:30:52,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:52,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:54,286 - INFO - Response - Page 2:
2025-04-28 22:30:54,286 - INFO - 第 2 页获取到 100 条记录
2025-04-28 22:30:54,489 - INFO - Request Parameters - Page 3:
2025-04-28 22:30:54,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:54,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:55,208 - INFO - Response - Page 3:
2025-04-28 22:30:55,208 - INFO - 第 3 页获取到 84 条记录
2025-04-28 22:30:55,411 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 22:30:55,411 - INFO - 获取到 284 条表单数据
2025-04-28 22:30:55,411 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 22:30:55,411 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 22:30:55,411 - INFO - 开始处理日期: 2025-04-24
2025-04-28 22:30:55,411 - INFO - Request Parameters - Page 1:
2025-04-28 22:30:55,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:55,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:56,145 - INFO - Response - Page 1:
2025-04-28 22:30:56,145 - INFO - 第 1 页获取到 100 条记录
2025-04-28 22:30:56,348 - INFO - Request Parameters - Page 2:
2025-04-28 22:30:56,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:56,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:57,098 - INFO - Response - Page 2:
2025-04-28 22:30:57,098 - INFO - 第 2 页获取到 100 条记录
2025-04-28 22:30:57,301 - INFO - Request Parameters - Page 3:
2025-04-28 22:30:57,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:57,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:57,926 - INFO - Response - Page 3:
2025-04-28 22:30:57,926 - INFO - 第 3 页获取到 36 条记录
2025-04-28 22:30:58,130 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 22:30:58,130 - INFO - 获取到 236 条表单数据
2025-04-28 22:30:58,130 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 22:30:58,130 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 22:30:58,130 - INFO - 开始处理日期: 2025-04-25
2025-04-28 22:30:58,130 - INFO - Request Parameters - Page 1:
2025-04-28 22:30:58,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:58,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:58,973 - INFO - Response - Page 1:
2025-04-28 22:30:58,973 - INFO - 第 1 页获取到 100 条记录
2025-04-28 22:30:59,176 - INFO - Request Parameters - Page 2:
2025-04-28 22:30:59,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:30:59,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:30:59,942 - INFO - Response - Page 2:
2025-04-28 22:30:59,942 - INFO - 第 2 页获取到 100 条记录
2025-04-28 22:31:00,145 - INFO - Request Parameters - Page 3:
2025-04-28 22:31:00,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:00,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:00,692 - INFO - Response - Page 3:
2025-04-28 22:31:00,692 - INFO - 第 3 页获取到 14 条记录
2025-04-28 22:31:00,895 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 22:31:00,895 - INFO - 获取到 214 条表单数据
2025-04-28 22:31:00,895 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 22:31:00,895 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 22:31:00,895 - INFO - 开始处理日期: 2025-04-26
2025-04-28 22:31:00,895 - INFO - Request Parameters - Page 1:
2025-04-28 22:31:00,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:00,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:01,661 - INFO - Response - Page 1:
2025-04-28 22:31:01,661 - INFO - 第 1 页获取到 100 条记录
2025-04-28 22:31:01,864 - INFO - Request Parameters - Page 2:
2025-04-28 22:31:01,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:01,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:02,614 - INFO - Response - Page 2:
2025-04-28 22:31:02,614 - INFO - 第 2 页获取到 100 条记录
2025-04-28 22:31:02,817 - INFO - Request Parameters - Page 3:
2025-04-28 22:31:02,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:02,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:03,473 - INFO - Response - Page 3:
2025-04-28 22:31:03,473 - INFO - 第 3 页获取到 83 条记录
2025-04-28 22:31:03,676 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 22:31:03,676 - INFO - 获取到 283 条表单数据
2025-04-28 22:31:03,676 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 22:31:03,676 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 22:31:03,676 - INFO - 开始处理日期: 2025-04-27
2025-04-28 22:31:03,676 - INFO - Request Parameters - Page 1:
2025-04-28 22:31:03,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:03,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:04,395 - INFO - Response - Page 1:
2025-04-28 22:31:04,395 - INFO - 第 1 页获取到 100 条记录
2025-04-28 22:31:04,598 - INFO - Request Parameters - Page 2:
2025-04-28 22:31:04,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:04,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:05,286 - INFO - Response - Page 2:
2025-04-28 22:31:05,286 - INFO - 第 2 页获取到 100 条记录
2025-04-28 22:31:05,489 - INFO - Request Parameters - Page 3:
2025-04-28 22:31:05,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:05,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:06,051 - INFO - Response - Page 3:
2025-04-28 22:31:06,051 - INFO - 第 3 页获取到 41 条记录
2025-04-28 22:31:06,254 - INFO - 查询完成，共获取到 241 条记录
2025-04-28 22:31:06,254 - INFO - 获取到 241 条表单数据
2025-04-28 22:31:06,254 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 22:31:06,254 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 22:31:06,254 - INFO - 开始处理日期: 2025-04-28
2025-04-28 22:31:06,254 - INFO - Request Parameters - Page 1:
2025-04-28 22:31:06,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 22:31:06,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 22:31:06,708 - INFO - Response - Page 1:
2025-04-28 22:31:06,723 - INFO - 第 1 页获取到 5 条记录
2025-04-28 22:31:06,926 - INFO - 查询完成，共获取到 5 条记录
2025-04-28 22:31:06,926 - INFO - 获取到 5 条表单数据
2025-04-28 22:31:06,926 - INFO - 当前日期 2025-04-28 有 48 条MySQL数据需要处理
2025-04-28 22:31:06,926 - INFO - 开始批量插入 43 条新记录
2025-04-28 22:31:07,129 - INFO - 批量插入响应状态码: 200
2025-04-28 22:31:07,129 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2043', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-502A-7BCF-803B-FE76FE4FD9E1', 'x-acs-trace-id': '83618d261488f5fdd0bec6a410046d4f', 'etag': '2r/zLm65e/ahcYYE/Z03BvQ3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 22:31:07,129 - INFO - 批量插入响应体: {'result': ['FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM3', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM4', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM5', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM6', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM7', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM8', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM9', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMA', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMB', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMC', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMD', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AME', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMF', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMG', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMH', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMI', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMJ', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMK', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AML', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMM', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMN', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMO', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMP', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMQ', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMR', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMS', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMT', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMU', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMV', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMW', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMX', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMY', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMZ', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM01', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM11', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM21', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM31', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM41', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM51', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM61', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM71', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM81', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM91']}
2025-04-28 22:31:07,129 - INFO - 批量插入表单数据成功，批次 1，共 43 条记录
2025-04-28 22:31:07,129 - INFO - 成功插入的数据ID: ['FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM3', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM4', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM5', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM6', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM7', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM8', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM9', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMA', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMB', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMC', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMD', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AME', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMF', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMG', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMH', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMI', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMJ', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMK', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AML', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMM', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMN', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMO', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMP', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMQ', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMR', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMS', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMT', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMU', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMV', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMW', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMX', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMY', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AMZ', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM01', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM11', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM21', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM31', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM41', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM51', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM61', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM71', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM81', 'FINST-2PF662C1H3ZU0YBCEI4RG84XKL3N2F7SE61AM91']
2025-04-28 22:31:12,145 - INFO - 批量插入完成，共 43 条记录
2025-04-28 22:31:12,145 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 43 条，错误: 0 条
2025-04-28 22:31:12,145 - INFO - 数据同步完成！更新: 0 条，插入: 43 条，错误: 2 条
2025-04-28 22:32:12,160 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 22:32:12,160 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 22:32:12,160 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 22:32:12,207 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 22:32:12,207 - ERROR - 未获取到MySQL数据
2025-04-28 22:32:12,207 - INFO - 同步完成
2025-04-28 23:30:34,336 - INFO - 使用默认增量同步（当天更新数据）
2025-04-28 23:30:34,336 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 23:30:34,336 - INFO - 查询参数: ('2025-04-28',)
2025-04-28 23:30:34,399 - INFO - MySQL查询成功，增量数据（日期: 2025-04-28），共获取 292 条记录
2025-04-28 23:30:34,399 - INFO - 获取到 8 个日期需要处理: ['2025-04-11', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-26', '2025-04-27', '2025-04-28']
2025-04-28 23:30:34,399 - INFO - 开始处理日期: 2025-04-11
2025-04-28 23:30:34,399 - INFO - Request Parameters - Page 1:
2025-04-28 23:30:34,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:34,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1744300800000, 1744387199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:42,546 - ERROR - 处理日期 2025-04-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 29EAE9F8-2879-7741-9FAD-B7EF5D829F2F Response: {'code': 'ServiceUnavailable', 'requestid': '29EAE9F8-2879-7741-9FAD-B7EF5D829F2F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 29EAE9F8-2879-7741-9FAD-B7EF5D829F2F)
2025-04-28 23:30:42,546 - INFO - 开始处理日期: 2025-04-22
2025-04-28 23:30:42,546 - INFO - Request Parameters - Page 1:
2025-04-28 23:30:42,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:42,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:43,280 - INFO - Response - Page 1:
2025-04-28 23:30:43,280 - INFO - 第 1 页获取到 100 条记录
2025-04-28 23:30:43,484 - INFO - Request Parameters - Page 2:
2025-04-28 23:30:43,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:43,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:51,599 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 863CEB58-53EE-7BC3-A73B-E2E61DC73413 Response: {'code': 'ServiceUnavailable', 'requestid': '863CEB58-53EE-7BC3-A73B-E2E61DC73413', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 863CEB58-53EE-7BC3-A73B-E2E61DC73413)
2025-04-28 23:30:51,599 - INFO - 开始处理日期: 2025-04-23
2025-04-28 23:30:51,599 - INFO - Request Parameters - Page 1:
2025-04-28 23:30:51,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:51,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:54,586 - INFO - Response - Page 1:
2025-04-28 23:30:54,586 - INFO - 第 1 页获取到 100 条记录
2025-04-28 23:30:54,789 - INFO - Request Parameters - Page 2:
2025-04-28 23:30:54,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:54,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:55,477 - INFO - Response - Page 2:
2025-04-28 23:30:55,477 - INFO - 第 2 页获取到 100 条记录
2025-04-28 23:30:55,680 - INFO - Request Parameters - Page 3:
2025-04-28 23:30:55,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:55,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745337600000, 1745423999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:56,306 - INFO - Response - Page 3:
2025-04-28 23:30:56,306 - INFO - 第 3 页获取到 84 条记录
2025-04-28 23:30:56,509 - INFO - 查询完成，共获取到 284 条记录
2025-04-28 23:30:56,509 - INFO - 获取到 284 条表单数据
2025-04-28 23:30:56,509 - INFO - 当前日期 2025-04-23 有 1 条MySQL数据需要处理
2025-04-28 23:30:56,509 - INFO - 日期 2025-04-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 23:30:56,509 - INFO - 开始处理日期: 2025-04-24
2025-04-28 23:30:56,509 - INFO - Request Parameters - Page 1:
2025-04-28 23:30:56,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:56,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:57,197 - INFO - Response - Page 1:
2025-04-28 23:30:57,197 - INFO - 第 1 页获取到 100 条记录
2025-04-28 23:30:57,400 - INFO - Request Parameters - Page 2:
2025-04-28 23:30:57,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:57,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:58,104 - INFO - Response - Page 2:
2025-04-28 23:30:58,104 - INFO - 第 2 页获取到 100 条记录
2025-04-28 23:30:58,307 - INFO - Request Parameters - Page 3:
2025-04-28 23:30:58,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:58,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745424000000, 1745510399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:58,917 - INFO - Response - Page 3:
2025-04-28 23:30:58,917 - INFO - 第 3 页获取到 36 条记录
2025-04-28 23:30:59,120 - INFO - 查询完成，共获取到 236 条记录
2025-04-28 23:30:59,120 - INFO - 获取到 236 条表单数据
2025-04-28 23:30:59,120 - INFO - 当前日期 2025-04-24 有 3 条MySQL数据需要处理
2025-04-28 23:30:59,120 - INFO - 日期 2025-04-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 23:30:59,120 - INFO - 开始处理日期: 2025-04-25
2025-04-28 23:30:59,120 - INFO - Request Parameters - Page 1:
2025-04-28 23:30:59,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:30:59,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:30:59,824 - INFO - Response - Page 1:
2025-04-28 23:30:59,824 - INFO - 第 1 页获取到 100 条记录
2025-04-28 23:31:00,027 - INFO - Request Parameters - Page 2:
2025-04-28 23:31:00,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:00,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:00,731 - INFO - Response - Page 2:
2025-04-28 23:31:00,731 - INFO - 第 2 页获取到 100 条记录
2025-04-28 23:31:00,934 - INFO - Request Parameters - Page 3:
2025-04-28 23:31:00,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:00,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745510400000, 1745596799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:01,482 - INFO - Response - Page 3:
2025-04-28 23:31:01,482 - INFO - 第 3 页获取到 14 条记录
2025-04-28 23:31:01,685 - INFO - 查询完成，共获取到 214 条记录
2025-04-28 23:31:01,685 - INFO - 获取到 214 条表单数据
2025-04-28 23:31:01,685 - INFO - 当前日期 2025-04-25 有 1 条MySQL数据需要处理
2025-04-28 23:31:01,685 - INFO - 日期 2025-04-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 23:31:01,685 - INFO - 开始处理日期: 2025-04-26
2025-04-28 23:31:01,685 - INFO - Request Parameters - Page 1:
2025-04-28 23:31:01,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:01,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:02,404 - INFO - Response - Page 1:
2025-04-28 23:31:02,404 - INFO - 第 1 页获取到 100 条记录
2025-04-28 23:31:02,607 - INFO - Request Parameters - Page 2:
2025-04-28 23:31:02,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:02,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:03,295 - INFO - Response - Page 2:
2025-04-28 23:31:03,295 - INFO - 第 2 页获取到 100 条记录
2025-04-28 23:31:03,499 - INFO - Request Parameters - Page 3:
2025-04-28 23:31:03,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:03,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745596800000, 1745683199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:04,171 - INFO - Response - Page 3:
2025-04-28 23:31:04,171 - INFO - 第 3 页获取到 83 条记录
2025-04-28 23:31:04,374 - INFO - 查询完成，共获取到 283 条记录
2025-04-28 23:31:04,374 - INFO - 获取到 283 条表单数据
2025-04-28 23:31:04,374 - INFO - 当前日期 2025-04-26 有 2 条MySQL数据需要处理
2025-04-28 23:31:04,374 - INFO - 日期 2025-04-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 23:31:04,374 - INFO - 开始处理日期: 2025-04-27
2025-04-28 23:31:04,374 - INFO - Request Parameters - Page 1:
2025-04-28 23:31:04,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:04,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:05,078 - INFO - Response - Page 1:
2025-04-28 23:31:05,078 - INFO - 第 1 页获取到 100 条记录
2025-04-28 23:31:05,281 - INFO - Request Parameters - Page 2:
2025-04-28 23:31:05,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:05,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:06,001 - INFO - Response - Page 2:
2025-04-28 23:31:06,001 - INFO - 第 2 页获取到 100 条记录
2025-04-28 23:31:06,204 - INFO - Request Parameters - Page 3:
2025-04-28 23:31:06,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:06,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:06,814 - INFO - Response - Page 3:
2025-04-28 23:31:06,814 - INFO - 第 3 页获取到 41 条记录
2025-04-28 23:31:07,017 - INFO - 查询完成，共获取到 241 条记录
2025-04-28 23:31:07,017 - INFO - 获取到 241 条表单数据
2025-04-28 23:31:07,017 - INFO - 当前日期 2025-04-27 有 219 条MySQL数据需要处理
2025-04-28 23:31:07,017 - INFO - 日期 2025-04-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 23:31:07,017 - INFO - 开始处理日期: 2025-04-28
2025-04-28 23:31:07,017 - INFO - Request Parameters - Page 1:
2025-04-28 23:31:07,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 23:31:07,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 23:31:07,674 - INFO - Response - Page 1:
2025-04-28 23:31:07,674 - INFO - 第 1 页获取到 48 条记录
2025-04-28 23:31:07,877 - INFO - 查询完成，共获取到 48 条记录
2025-04-28 23:31:07,877 - INFO - 获取到 48 条表单数据
2025-04-28 23:31:07,877 - INFO - 当前日期 2025-04-28 有 64 条MySQL数据需要处理
2025-04-28 23:31:07,877 - INFO - 开始批量插入 16 条新记录
2025-04-28 23:31:08,018 - INFO - 批量插入响应状态码: 200
2025-04-28 23:31:08,018 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 15:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '780', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E7C54E77-EB26-7930-BC06-3B565AD56554', 'x-acs-trace-id': '634c08289f06351213a4eed94e56b6aa', 'etag': '7hYSUR/UkldmSvdiy3BcQwA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 23:31:08,018 - INFO - 批量插入响应体: {'result': ['FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMN3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMO3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMP3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMQ3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMR3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMS3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMT3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMU3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMV3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMW3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMX3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMY3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMZ3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AM04', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AM14', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AM24']}
2025-04-28 23:31:08,018 - INFO - 批量插入表单数据成功，批次 1，共 16 条记录
2025-04-28 23:31:08,018 - INFO - 成功插入的数据ID: ['FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMN3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMO3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMP3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMQ3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMR3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMS3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMT3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMU3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMV3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMW3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMX3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMY3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AMZ3', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AM04', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AM14', 'FINST-LLF66F71X4ZUTHL8E9SKQCBGXY8T2MSXJ81AM24']
2025-04-28 23:31:13,037 - INFO - 批量插入完成，共 16 条记录
2025-04-28 23:31:13,037 - INFO - 日期 2025-04-28 处理完成 - 更新: 0 条，插入: 16 条，错误: 0 条
2025-04-28 23:31:13,037 - INFO - 数据同步完成！更新: 0 条，插入: 16 条，错误: 2 条
2025-04-28 23:32:13,098 - INFO - 开始同步昨天与今天的销售数据: 20250427 至 *************-04-28 23:32:13,098 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-28 23:32:13,098 - INFO - 查询参数: ('20250427', '20250428')
2025-04-28 23:32:13,145 - INFO - MySQL查询成功，时间段: 20250427 至 20250428，共获取 0 条记录
2025-04-28 23:32:13,145 - ERROR - 未获取到MySQL数据
2025-04-28 23:32:13,145 - INFO - 同步完成
