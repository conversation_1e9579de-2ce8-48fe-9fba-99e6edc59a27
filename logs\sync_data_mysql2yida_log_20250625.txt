2025-06-25 01:30:34,781 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 01:30:34,781 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 01:30:34,781 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 01:30:34,859 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 0 条记录
2025-06-25 01:30:34,859 - ERROR - 未获取到MySQL数据
2025-06-25 01:31:34,883 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 01:31:34,883 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 01:31:34,883 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 01:31:35,008 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 60 条记录
2025-06-25 01:31:35,008 - INFO - 获取到 1 个日期需要处理: ['2025-06-24']
2025-06-25 01:31:35,008 - INFO - 开始处理日期: 2025-06-24
2025-06-25 01:31:35,008 - INFO - Request Parameters - Page 1:
2025-06-25 01:31:35,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 01:31:35,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 01:31:43,150 - ERROR - 处理日期 2025-06-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C8304AF0-FF4F-7997-BB16-9AEA460DB6A5 Response: {'code': 'ServiceUnavailable', 'requestid': 'C8304AF0-FF4F-7997-BB16-9AEA460DB6A5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C8304AF0-FF4F-7997-BB16-9AEA460DB6A5)
2025-06-25 01:31:43,150 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-25 01:31:43,150 - INFO - 同步完成
2025-06-25 04:30:34,867 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 04:30:34,867 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 04:30:34,867 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 04:30:35,008 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 1 条记录
2025-06-25 04:30:35,008 - INFO - 获取到 1 个日期需要处理: ['2025-06-24']
2025-06-25 04:30:35,008 - INFO - 开始处理日期: 2025-06-24
2025-06-25 04:30:35,008 - INFO - Request Parameters - Page 1:
2025-06-25 04:30:35,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 04:30:35,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 04:30:43,118 - ERROR - 处理日期 2025-06-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0957B5D7-2E7D-703A-945D-2E92C4FD64D4 Response: {'code': 'ServiceUnavailable', 'requestid': '0957B5D7-2E7D-703A-945D-2E92C4FD64D4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0957B5D7-2E7D-703A-945D-2E92C4FD64D4)
2025-06-25 04:30:43,118 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-25 04:31:43,143 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 04:31:43,143 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 04:31:43,143 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 04:31:43,268 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 61 条记录
2025-06-25 04:31:43,268 - INFO - 获取到 1 个日期需要处理: ['2025-06-24']
2025-06-25 04:31:43,268 - INFO - 开始处理日期: 2025-06-24
2025-06-25 04:31:43,268 - INFO - Request Parameters - Page 1:
2025-06-25 04:31:43,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 04:31:43,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 04:31:49,144 - INFO - Response - Page 1:
2025-06-25 04:31:49,144 - INFO - 第 1 页获取到 49 条记录
2025-06-25 04:31:49,659 - INFO - 查询完成，共获取到 49 条记录
2025-06-25 04:31:49,659 - INFO - 获取到 49 条表单数据
2025-06-25 04:31:49,659 - INFO - 当前日期 2025-06-24 有 60 条MySQL数据需要处理
2025-06-25 04:31:49,659 - INFO - 开始批量插入 11 条新记录
2025-06-25 04:31:49,831 - INFO - 批量插入响应状态码: 200
2025-06-25 04:31:49,831 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 20:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C4A9221B-2347-7EBD-9303-54C182AE00B8', 'x-acs-trace-id': '28d4d9c7a0380efc0104908096bc9584', 'etag': '584+CsxgD4z+NVYmliR1O2w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 04:31:49,831 - INFO - 批量插入响应体: {'result': ['FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMT2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMU2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMV2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMW2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMX2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMY2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMZ2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM03', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM13', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM23', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM33']}
2025-06-25 04:31:49,831 - INFO - 批量插入表单数据成功，批次 1，共 11 条记录
2025-06-25 04:31:49,831 - INFO - 成功插入的数据ID: ['FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMT2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMU2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMV2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMW2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMX2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMY2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACMZ2', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM03', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM13', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM23', 'FINST-NS76699124LWP37EEVD6R64ZVNZJ2TK5EZACM33']
2025-06-25 04:31:54,848 - INFO - 批量插入完成，共 11 条记录
2025-06-25 04:31:54,848 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 11 条，错误: 0 条
2025-06-25 04:31:54,848 - INFO - 数据同步完成！更新: 0 条，插入: 11 条，错误: 0 条
2025-06-25 04:31:54,848 - INFO - 同步完成
2025-06-25 07:30:33,585 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 07:30:33,585 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 07:30:33,585 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 07:30:33,710 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 1 条记录
2025-06-25 07:30:33,710 - INFO - 获取到 1 个日期需要处理: ['2025-06-24']
2025-06-25 07:30:33,710 - INFO - 开始处理日期: 2025-06-24
2025-06-25 07:30:33,725 - INFO - Request Parameters - Page 1:
2025-06-25 07:30:33,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 07:30:33,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 07:30:41,850 - ERROR - 处理日期 2025-06-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FD8A5175-6B64-7C55-848C-6A7D4C6623C2 Response: {'code': 'ServiceUnavailable', 'requestid': 'FD8A5175-6B64-7C55-848C-6A7D4C6623C2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FD8A5175-6B64-7C55-848C-6A7D4C6623C2)
2025-06-25 07:30:41,850 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-25 07:31:41,865 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 07:31:41,865 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 07:31:41,865 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 07:31:41,990 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 61 条记录
2025-06-25 07:31:41,990 - INFO - 获取到 1 个日期需要处理: ['2025-06-24']
2025-06-25 07:31:41,990 - INFO - 开始处理日期: 2025-06-24
2025-06-25 07:31:41,990 - INFO - Request Parameters - Page 1:
2025-06-25 07:31:41,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 07:31:41,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 07:31:47,522 - INFO - Response - Page 1:
2025-06-25 07:31:47,522 - INFO - 第 1 页获取到 50 条记录
2025-06-25 07:31:48,022 - INFO - Request Parameters - Page 2:
2025-06-25 07:31:48,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 07:31:48,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 07:31:48,568 - INFO - Response - Page 2:
2025-06-25 07:31:48,568 - INFO - 第 2 页获取到 10 条记录
2025-06-25 07:31:49,068 - INFO - 查询完成，共获取到 60 条记录
2025-06-25 07:31:49,068 - INFO - 获取到 60 条表单数据
2025-06-25 07:31:49,068 - INFO - 当前日期 2025-06-24 有 60 条MySQL数据需要处理
2025-06-25 07:31:49,068 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 07:31:49,068 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 07:31:49,068 - INFO - 同步完成
2025-06-25 10:30:33,693 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 10:30:33,693 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 10:30:33,693 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 10:30:33,819 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 96 条记录
2025-06-25 10:30:33,819 - INFO - 获取到 3 个日期需要处理: ['2025-06-23', '2025-06-24', '2025-06-25']
2025-06-25 10:30:33,819 - INFO - 开始处理日期: 2025-06-23
2025-06-25 10:30:33,834 - INFO - Request Parameters - Page 1:
2025-06-25 10:30:33,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:30:33,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:30:41,990 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4F396292-68F2-7B53-8E52-5C9EA3430033 Response: {'code': 'ServiceUnavailable', 'requestid': '4F396292-68F2-7B53-8E52-5C9EA3430033', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4F396292-68F2-7B53-8E52-5C9EA3430033)
2025-06-25 10:30:41,990 - INFO - 开始处理日期: 2025-06-24
2025-06-25 10:30:41,990 - INFO - Request Parameters - Page 1:
2025-06-25 10:30:41,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:30:41,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:30:48,303 - INFO - Response - Page 1:
2025-06-25 10:30:48,303 - INFO - 第 1 页获取到 50 条记录
2025-06-25 10:30:48,803 - INFO - Request Parameters - Page 2:
2025-06-25 10:30:48,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:30:48,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:30:49,303 - INFO - Response - Page 2:
2025-06-25 10:30:49,303 - INFO - 第 2 页获取到 10 条记录
2025-06-25 10:30:49,818 - INFO - 查询完成，共获取到 60 条记录
2025-06-25 10:30:49,818 - INFO - 获取到 60 条表单数据
2025-06-25 10:30:49,818 - INFO - 当前日期 2025-06-24 有 89 条MySQL数据需要处理
2025-06-25 10:30:49,818 - INFO - 开始批量插入 88 条新记录
2025-06-25 10:30:50,084 - INFO - 批量插入响应状态码: 200
2025-06-25 10:30:50,084 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:30:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '77886033-4A59-74D6-BCA8-4CFAC911F84B', 'x-acs-trace-id': 'e831bc39de6e0cf40e225bbe94cd0c1d', 'etag': '25lHBxXdBS6qW9XInRcJY4w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:30:50,084 - INFO - 批量插入响应体: {'result': ['FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMEH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMFH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMGH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMHH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMIH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMJH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMKH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMLH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMMH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMNH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMOH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMPH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMQH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMRH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMSH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMTH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMUH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMVH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMWH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMXH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMYH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMZH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM0I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM1I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM2I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM3I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM4I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM5I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM6I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM7I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM8I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM9I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMAI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMBI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMCI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMDI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMEI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMFI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMGI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMHI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMII', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMJI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMKI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMLI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMMI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMNI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMOI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMPI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMQI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMRI']}
2025-06-25 10:30:50,084 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-25 10:30:50,084 - INFO - 成功插入的数据ID: ['FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMEH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMFH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMGH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMHH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMIH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMJH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMKH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMLH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMMH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMNH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMOH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMPH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMQH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMRH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMSH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMTH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMUH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMVH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMWH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMXH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMYH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMZH', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM0I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM1I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM2I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM3I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM4I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM5I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM6I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM7I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM8I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM9I', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMAI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMBI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMCI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMDI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMEI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMFI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMGI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMHI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMII', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMJI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMKI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMLI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMMI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMNI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMOI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMPI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMQI', 'FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCMRI']
2025-06-25 10:30:55,303 - INFO - 批量插入响应状态码: 200
2025-06-25 10:30:55,303 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:30:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8AB16866-046B-7D78-BEB6-B3FFA6EE4A5A', 'x-acs-trace-id': 'ff8f3e31f8e88e01369ce303da2792f2', 'etag': '1NOEwP+vDJQkmkIoC+1erAw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:30:55,303 - INFO - 批量插入响应体: {'result': ['FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMOB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMPB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMQB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMRB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMSB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMTB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMUB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMVB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMWB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMXB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMYB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMZB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM0C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM1C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM2C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM3C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM4C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM5C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM6C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM7C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM8C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM9C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMAC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMBC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMCC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMDC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMEC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMFC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMGC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMHC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMIC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMJC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMKC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMLC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMMC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMNC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMOC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMPC']}
2025-06-25 10:30:55,303 - INFO - 批量插入表单数据成功，批次 2，共 38 条记录
2025-06-25 10:30:55,303 - INFO - 成功插入的数据ID: ['FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMOB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMPB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMQB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMRB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMSB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMTB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMUB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMVB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMWB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMXB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMYB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMZB', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM0C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM1C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM2C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM3C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM4C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM5C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM6C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM7C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM8C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCM9C', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMAC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMBC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMCC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMDC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMEC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMFC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMGC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMHC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMIC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMJC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMKC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMLC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMMC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMNC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMOC', 'FINST-K7G66FA1P3LWOZY2B10EH4DDD9J420XS7CBCMPC']
2025-06-25 10:31:00,318 - INFO - 批量插入完成，共 88 条记录
2025-06-25 10:31:00,318 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 88 条，错误: 0 条
2025-06-25 10:31:00,318 - INFO - 开始处理日期: 2025-06-25
2025-06-25 10:31:00,318 - INFO - Request Parameters - Page 1:
2025-06-25 10:31:00,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:31:00,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:31:00,771 - INFO - Response - Page 1:
2025-06-25 10:31:00,771 - INFO - 查询完成，共获取到 0 条记录
2025-06-25 10:31:00,771 - INFO - 获取到 0 条表单数据
2025-06-25 10:31:00,771 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-25 10:31:00,771 - INFO - 开始批量插入 1 条新记录
2025-06-25 10:31:00,943 - INFO - 批量插入响应状态码: 200
2025-06-25 10:31:00,959 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D775D3AB-F53B-765A-BF67-DB31FC1A78C4', 'x-acs-trace-id': '5fd520327220fdf03a143f168b319798', 'etag': '5fiXwSlfI0OTqQJJWh78GdA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:31:00,959 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1XOLWZSSS8B81LC7XCCK13S9X7CBCM5']}
2025-06-25 10:31:00,959 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-25 10:31:00,959 - INFO - 成功插入的数据ID: ['FINST-K7666JC1XOLWZSSS8B81LC7XCCK13S9X7CBCM5']
2025-06-25 10:31:05,975 - INFO - 批量插入完成，共 1 条记录
2025-06-25 10:31:05,975 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-25 10:31:05,975 - INFO - 数据同步完成！更新: 0 条，插入: 89 条，错误: 1 条
2025-06-25 10:32:05,990 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 10:32:05,990 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 10:32:05,990 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 10:32:06,130 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 383 条记录
2025-06-25 10:32:06,130 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-25 10:32:06,130 - INFO - 开始处理日期: 2025-06-24
2025-06-25 10:32:06,130 - INFO - Request Parameters - Page 1:
2025-06-25 10:32:06,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:32:06,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:32:06,927 - INFO - Response - Page 1:
2025-06-25 10:32:06,927 - INFO - 第 1 页获取到 50 条记录
2025-06-25 10:32:07,443 - INFO - Request Parameters - Page 2:
2025-06-25 10:32:07,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:32:07,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:32:08,068 - INFO - Response - Page 2:
2025-06-25 10:32:08,068 - INFO - 第 2 页获取到 50 条记录
2025-06-25 10:32:08,583 - INFO - Request Parameters - Page 3:
2025-06-25 10:32:08,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:32:08,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:32:09,302 - INFO - Response - Page 3:
2025-06-25 10:32:09,302 - INFO - 第 3 页获取到 48 条记录
2025-06-25 10:32:09,802 - INFO - 查询完成，共获取到 148 条记录
2025-06-25 10:32:09,802 - INFO - 获取到 148 条表单数据
2025-06-25 10:32:09,802 - INFO - 当前日期 2025-06-24 有 372 条MySQL数据需要处理
2025-06-25 10:32:09,802 - INFO - 开始批量插入 224 条新记录
2025-06-25 10:32:10,021 - INFO - 批量插入响应状态码: 200
2025-06-25 10:32:10,021 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '582F0784-4B72-7E98-A89F-80C0031391B2', 'x-acs-trace-id': '233c7312eb46a5d967a3e1c48a9957c2', 'etag': '2AxPIdJdWyWZ1J5ukT3EnDA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:32:10,021 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM41', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM51', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM61', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM71', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM81', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM91', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMA1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMB1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMC1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMD1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCME1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMF1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMG1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMH1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMI1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMJ1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMK1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCML1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMM1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMN1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMO1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMP1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMQ1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMR1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMS1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMT1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMU1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMV1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMW1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMX1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMY1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMZ1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM02', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM12', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM22', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM32', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM42', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM52', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM62', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM72', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM82', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM92', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMA2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMB2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMC2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMD2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCME2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMF2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMG2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMH2']}
2025-06-25 10:32:10,021 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-25 10:32:10,021 - INFO - 成功插入的数据ID: ['FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM41', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM51', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM61', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM71', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM81', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM91', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMA1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMB1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMC1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMD1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCME1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMF1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMG1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMH1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMI1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMJ1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMK1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCML1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMM1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMN1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMO1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMP1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMQ1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMR1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMS1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMT1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMU1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMV1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMW1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMX1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMY1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMZ1', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM02', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM12', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM22', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM32', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM42', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM52', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM62', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM72', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM82', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCM92', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMA2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMB2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMC2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMD2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCME2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMF2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMG2', 'FINST-FD966QA1SMLWBTNGBH7XADNZ8ZOF3OKE9CBCMH2']
2025-06-25 10:32:15,287 - INFO - 批量插入响应状态码: 200
2025-06-25 10:32:15,287 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '66C14AFB-DA87-78CB-9EC7-F9FCC3FDE66A', 'x-acs-trace-id': '1e03410f641f2337544b4926a30071b3', 'etag': '2k71waigtMPLGEihHY16EUA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:32:15,287 - INFO - 批量插入响应体: {'result': ['FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMSG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMTG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMUG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMVG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMWG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMXG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMYG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMZG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM0H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM1H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM2H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM3H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM4H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM5H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM6H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM7H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM8H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM9H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMAH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMBH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMCH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMDH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMEH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMFH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMGH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMHH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMIH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMJH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMKH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMLH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMMH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMNH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMOH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMPH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMQH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMRH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMSH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMTH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMUH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMVH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMWH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMXH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMYH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMZH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM0I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM1I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM2I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM3I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM4I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM5I']}
2025-06-25 10:32:15,287 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-25 10:32:15,287 - INFO - 成功插入的数据ID: ['FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMSG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMTG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMUG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMVG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMWG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMXG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMYG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113RMI9CBCMZG', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM0H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM1H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM2H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM3H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM4H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM5H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM6H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM7H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM8H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM9H', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMAH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMBH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMCH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMDH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMEH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMFH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMGH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMHH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMIH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMJH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMKH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMLH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMMH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMNH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMOH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMPH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMQH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMRH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMSH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMTH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMUH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMVH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMWH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMXH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMYH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCMZH', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM0I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM1I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM2I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM3I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM4I', 'FINST-CPC66T91POKWX9J9EV23WDC3Q3113SMI9CBCM5I']
2025-06-25 10:32:20,537 - INFO - 批量插入响应状态码: 200
2025-06-25 10:32:20,537 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '033F720B-C22D-7B91-A7AF-803EFB0970DC', 'x-acs-trace-id': '51d9901f5c089ca33cfef5fc9b4a642d', 'etag': '2EE5O640Y7lHiu2d8GEth+w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:32:20,537 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMX7', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMY7', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMZ7', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM08', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM18', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM28', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM38', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM48', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM58', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM68', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM78', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM88', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM98', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMA8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMB8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMC8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMD8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCME8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMF8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMG8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMH8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMI8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMJ8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMK8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCML8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMM8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMN8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMO8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMP8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMQ8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMR8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMS8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMT8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMU8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMV8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMW8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMX8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMY8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMZ8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM09', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM19', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM29', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM39', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM49', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM59', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM69', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM79', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM89', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM99', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMA9']}
2025-06-25 10:32:20,537 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-25 10:32:20,537 - INFO - 成功插入的数据ID: ['FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMX7', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMY7', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMZ7', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM08', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM18', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM28', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM38', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM48', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM58', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM68', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM78', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM88', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM98', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMA8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMB8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMC8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMD8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCME8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMF8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMG8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMH8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMI8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMJ8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMK8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCML8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMM8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMN8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMO8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMP8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMQ8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMR8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMS8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMT8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMU8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMV8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMW8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMX8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMY8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMZ8', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM09', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM19', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM29', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM39', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM49', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM59', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM69', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM79', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM89', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM99', 'FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCMA9']
2025-06-25 10:32:25,771 - INFO - 批量插入响应状态码: 200
2025-06-25 10:32:25,771 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2F446977-F71C-766E-859F-15BE09787927', 'x-acs-trace-id': 'd7dadd315a326e7d7e03cad52e9e8ff2', 'etag': '2ePLtDw7DBKh0EDdkfabjng2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:32:25,771 - INFO - 批量插入响应体: {'result': ['FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCME1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMF1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMG1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMH1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMI1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMJ1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMK1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCML1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMM1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMN1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMO1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMP1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMQ1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMR1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMS1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMT1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMU1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMV1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMW1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMX1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMY1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMZ1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM02', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM12', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM22', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM32', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM42', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM52', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM62', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM72', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM82', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM92', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMA2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMB2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMC2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMD2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCME2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMF2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMG2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMH2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMI2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMJ2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMK2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCML2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMM2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMN2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMO2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMP2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMQ2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMR2']}
2025-06-25 10:32:25,771 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-25 10:32:25,771 - INFO - 成功插入的数据ID: ['FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCME1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMF1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMG1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMH1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMI1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMJ1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMK1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCML1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMM1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMN1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMO1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMP1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMQ1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMR1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMS1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMT1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMU1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMV1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMW1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMX1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMY1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMZ1', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM02', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM12', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM22', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM32', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM42', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM52', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM62', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM72', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM82', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCM92', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMA2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMB2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMC2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMD2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCME2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMF2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMG2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMH2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMI2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMJ2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMK2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCML2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMM2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMN2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMO2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMP2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMQ2', 'FINST-LLF66O71KYKWLATR8OT1MDZRHB0F31QQ9CBCMR2']
2025-06-25 10:32:30,974 - INFO - 批量插入响应状态码: 200
2025-06-25 10:32:30,974 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 02:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E8F6EE48-1A63-7520-B515-BBB735CD5DC9', 'x-acs-trace-id': '246425da30cb7010c2010519357e7886', 'etag': '16kJFO76NklkyxrvgN0/PsQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 10:32:30,974 - INFO - 批量插入响应体: {'result': ['FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMF1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMG1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMH1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMI1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMJ1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMK1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCML1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMM1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMN1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMO1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMP1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMQ1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMR1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMS1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMT1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMU1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMV1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMW1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMX1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMY1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMZ1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCM02', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCM12', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCM22']}
2025-06-25 10:32:30,974 - INFO - 批量插入表单数据成功，批次 5，共 24 条记录
2025-06-25 10:32:30,974 - INFO - 成功插入的数据ID: ['FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMF1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMG1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMH1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMI1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMJ1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMK1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCML1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMM1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMN1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMO1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMP1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMQ1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMR1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMS1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMT1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMU1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMV1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMW1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMX1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMY1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCMZ1', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCM02', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCM12', 'FINST-RI766091M6LWLKCODIOZSD8HIBFL3LQU9CBCM22']
2025-06-25 10:32:35,990 - INFO - 批量插入完成，共 224 条记录
2025-06-25 10:32:35,990 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 224 条，错误: 0 条
2025-06-25 10:32:35,990 - INFO - 开始处理日期: 2025-06-25
2025-06-25 10:32:35,990 - INFO - Request Parameters - Page 1:
2025-06-25 10:32:35,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 10:32:35,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 10:32:37,130 - INFO - Response - Page 1:
2025-06-25 10:32:37,130 - INFO - 第 1 页获取到 1 条记录
2025-06-25 10:32:37,646 - INFO - 查询完成，共获取到 1 条记录
2025-06-25 10:32:37,646 - INFO - 获取到 1 条表单数据
2025-06-25 10:32:37,646 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-25 10:32:37,646 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 10:32:37,646 - INFO - 数据同步完成！更新: 0 条，插入: 224 条，错误: 0 条
2025-06-25 10:32:37,646 - INFO - 同步完成
2025-06-25 13:30:33,581 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 13:30:33,581 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 13:30:33,581 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 13:30:33,721 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 110 条记录
2025-06-25 13:30:33,721 - INFO - 获取到 3 个日期需要处理: ['2025-06-23', '2025-06-24', '2025-06-25']
2025-06-25 13:30:33,721 - INFO - 开始处理日期: 2025-06-23
2025-06-25 13:30:33,721 - INFO - Request Parameters - Page 1:
2025-06-25 13:30:33,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:33,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:41,831 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0CA27958-4A41-7D46-98D8-7DA609CBA043 Response: {'code': 'ServiceUnavailable', 'requestid': '0CA27958-4A41-7D46-98D8-7DA609CBA043', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0CA27958-4A41-7D46-98D8-7DA609CBA043)
2025-06-25 13:30:41,831 - INFO - 开始处理日期: 2025-06-24
2025-06-25 13:30:41,831 - INFO - Request Parameters - Page 1:
2025-06-25 13:30:41,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:41,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:49,346 - INFO - Response - Page 1:
2025-06-25 13:30:49,346 - INFO - 第 1 页获取到 50 条记录
2025-06-25 13:30:49,846 - INFO - Request Parameters - Page 2:
2025-06-25 13:30:49,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:49,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:50,487 - INFO - Response - Page 2:
2025-06-25 13:30:50,487 - INFO - 第 2 页获取到 50 条记录
2025-06-25 13:30:51,002 - INFO - Request Parameters - Page 3:
2025-06-25 13:30:51,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:51,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:51,643 - INFO - Response - Page 3:
2025-06-25 13:30:51,643 - INFO - 第 3 页获取到 50 条记录
2025-06-25 13:30:52,159 - INFO - Request Parameters - Page 4:
2025-06-25 13:30:52,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:52,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:52,971 - INFO - Response - Page 4:
2025-06-25 13:30:52,971 - INFO - 第 4 页获取到 50 条记录
2025-06-25 13:30:53,487 - INFO - Request Parameters - Page 5:
2025-06-25 13:30:53,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:53,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:54,096 - INFO - Response - Page 5:
2025-06-25 13:30:54,096 - INFO - 第 5 页获取到 50 条记录
2025-06-25 13:30:54,596 - INFO - Request Parameters - Page 6:
2025-06-25 13:30:54,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:54,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:55,237 - INFO - Response - Page 6:
2025-06-25 13:30:55,237 - INFO - 第 6 页获取到 50 条记录
2025-06-25 13:30:55,737 - INFO - Request Parameters - Page 7:
2025-06-25 13:30:55,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:55,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:56,455 - INFO - Response - Page 7:
2025-06-25 13:30:56,455 - INFO - 第 7 页获取到 50 条记录
2025-06-25 13:30:56,971 - INFO - Request Parameters - Page 8:
2025-06-25 13:30:56,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:30:56,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:30:57,659 - INFO - Response - Page 8:
2025-06-25 13:30:57,659 - INFO - 第 8 页获取到 22 条记录
2025-06-25 13:30:58,159 - INFO - 查询完成，共获取到 372 条记录
2025-06-25 13:30:58,159 - INFO - 获取到 372 条表单数据
2025-06-25 13:30:58,159 - INFO - 当前日期 2025-06-24 有 103 条MySQL数据需要处理
2025-06-25 13:30:58,159 - INFO - 开始批量插入 14 条新记录
2025-06-25 13:30:58,330 - INFO - 批量插入响应状态码: 200
2025-06-25 13:30:58,330 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 05:30:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '66527580-A3AB-74C3-96FB-15136810D510', 'x-acs-trace-id': 'c9b65fc04daf13e82c59a7cecb8c8c95', 'etag': '6mDuWf7jSAG7U18sJcPZOFw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 13:30:58,330 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMS4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMT4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMU4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMV4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMW4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMX4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMY4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMZ4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM05', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM15', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM25', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM35', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM45', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM55']}
2025-06-25 13:30:58,330 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-06-25 13:30:58,330 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMS4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMT4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMU4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMV4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMW4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMX4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMY4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCMZ4', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM05', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM15', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM25', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM35', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM45', 'FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM55']
2025-06-25 13:31:03,346 - INFO - 批量插入完成，共 14 条记录
2025-06-25 13:31:03,346 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 14 条，错误: 0 条
2025-06-25 13:31:03,346 - INFO - 开始处理日期: 2025-06-25
2025-06-25 13:31:03,346 - INFO - Request Parameters - Page 1:
2025-06-25 13:31:03,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:31:03,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:31:03,784 - INFO - Response - Page 1:
2025-06-25 13:31:03,784 - INFO - 第 1 页获取到 1 条记录
2025-06-25 13:31:04,299 - INFO - 查询完成，共获取到 1 条记录
2025-06-25 13:31:04,299 - INFO - 获取到 1 条表单数据
2025-06-25 13:31:04,299 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-25 13:31:04,299 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 13:31:04,299 - INFO - 数据同步完成！更新: 0 条，插入: 14 条，错误: 1 条
2025-06-25 13:32:04,314 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 13:32:04,314 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 13:32:04,314 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 13:32:04,455 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 410 条记录
2025-06-25 13:32:04,455 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-25 13:32:04,455 - INFO - 开始处理日期: 2025-06-24
2025-06-25 13:32:04,455 - INFO - Request Parameters - Page 1:
2025-06-25 13:32:04,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:04,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:05,189 - INFO - Response - Page 1:
2025-06-25 13:32:05,189 - INFO - 第 1 页获取到 50 条记录
2025-06-25 13:32:05,689 - INFO - Request Parameters - Page 2:
2025-06-25 13:32:05,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:05,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:06,424 - INFO - Response - Page 2:
2025-06-25 13:32:06,424 - INFO - 第 2 页获取到 50 条记录
2025-06-25 13:32:06,924 - INFO - Request Parameters - Page 3:
2025-06-25 13:32:06,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:06,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:07,580 - INFO - Response - Page 3:
2025-06-25 13:32:07,580 - INFO - 第 3 页获取到 50 条记录
2025-06-25 13:32:08,096 - INFO - Request Parameters - Page 4:
2025-06-25 13:32:08,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:08,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:08,767 - INFO - Response - Page 4:
2025-06-25 13:32:08,767 - INFO - 第 4 页获取到 50 条记录
2025-06-25 13:32:09,283 - INFO - Request Parameters - Page 5:
2025-06-25 13:32:09,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:09,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:09,986 - INFO - Response - Page 5:
2025-06-25 13:32:09,986 - INFO - 第 5 页获取到 50 条记录
2025-06-25 13:32:10,502 - INFO - Request Parameters - Page 6:
2025-06-25 13:32:10,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:10,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:11,127 - INFO - Response - Page 6:
2025-06-25 13:32:11,127 - INFO - 第 6 页获取到 50 条记录
2025-06-25 13:32:11,642 - INFO - Request Parameters - Page 7:
2025-06-25 13:32:11,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:11,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:12,330 - INFO - Response - Page 7:
2025-06-25 13:32:12,330 - INFO - 第 7 页获取到 50 条记录
2025-06-25 13:32:12,830 - INFO - Request Parameters - Page 8:
2025-06-25 13:32:12,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:12,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:13,408 - INFO - Response - Page 8:
2025-06-25 13:32:13,408 - INFO - 第 8 页获取到 36 条记录
2025-06-25 13:32:13,908 - INFO - 查询完成，共获取到 386 条记录
2025-06-25 13:32:13,908 - INFO - 获取到 386 条表单数据
2025-06-25 13:32:13,908 - INFO - 当前日期 2025-06-24 有 399 条MySQL数据需要处理
2025-06-25 13:32:13,924 - INFO - 开始批量插入 13 条新记录
2025-06-25 13:32:14,096 - INFO - 批量插入响应状态码: 200
2025-06-25 13:32:14,096 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 05:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B82F7440-3B51-7076-A386-AFD27D77652F', 'x-acs-trace-id': '41d976bc4c61623e4ed3e1c56d36e23c', 'etag': '6f9UKTGDp91agLpKwHYQ2lQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 13:32:14,096 - INFO - 批量插入响应体: {'result': ['FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMU6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMV6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMW6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMX6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMY6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMZ6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM07', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM17', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM27', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM37', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM47', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM57', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM67']}
2025-06-25 13:32:14,096 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-06-25 13:32:14,096 - INFO - 成功插入的数据ID: ['FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMU6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMV6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMW6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMX6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMY6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCMZ6', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM07', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM17', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM27', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM37', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM47', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM57', 'FINST-K7G66FA1S6LWS46SCV5ICAS5BW893L3ZOIBCM67']
2025-06-25 13:32:19,111 - INFO - 批量插入完成，共 13 条记录
2025-06-25 13:32:19,111 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-06-25 13:32:19,111 - INFO - 开始处理日期: 2025-06-25
2025-06-25 13:32:19,111 - INFO - Request Parameters - Page 1:
2025-06-25 13:32:19,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 13:32:19,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 13:32:19,549 - INFO - Response - Page 1:
2025-06-25 13:32:19,549 - INFO - 第 1 页获取到 1 条记录
2025-06-25 13:32:20,049 - INFO - 查询完成，共获取到 1 条记录
2025-06-25 13:32:20,049 - INFO - 获取到 1 条表单数据
2025-06-25 13:32:20,049 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-25 13:32:20,049 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 13:32:20,049 - INFO - 数据同步完成！更新: 0 条，插入: 13 条，错误: 0 条
2025-06-25 13:32:20,049 - INFO - 同步完成
2025-06-25 16:30:33,606 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 16:30:33,606 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 16:30:33,606 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 16:30:33,746 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 116 条记录
2025-06-25 16:30:33,746 - INFO - 获取到 4 个日期需要处理: ['2025-06-03', '2025-06-23', '2025-06-24', '2025-06-25']
2025-06-25 16:30:33,746 - INFO - 开始处理日期: 2025-06-03
2025-06-25 16:30:33,746 - INFO - Request Parameters - Page 1:
2025-06-25 16:30:33,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:33,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:41,871 - ERROR - 处理日期 2025-06-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 38C7E84B-A1F0-722A-84DB-380C49C7EFF2 Response: {'code': 'ServiceUnavailable', 'requestid': '38C7E84B-A1F0-722A-84DB-380C49C7EFF2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 38C7E84B-A1F0-722A-84DB-380C49C7EFF2)
2025-06-25 16:30:41,871 - INFO - 开始处理日期: 2025-06-23
2025-06-25 16:30:41,871 - INFO - Request Parameters - Page 1:
2025-06-25 16:30:41,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:41,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:49,230 - INFO - Response - Page 1:
2025-06-25 16:30:49,230 - INFO - 第 1 页获取到 50 条记录
2025-06-25 16:30:49,730 - INFO - Request Parameters - Page 2:
2025-06-25 16:30:49,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:49,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:50,434 - INFO - Response - Page 2:
2025-06-25 16:30:50,434 - INFO - 第 2 页获取到 50 条记录
2025-06-25 16:30:50,949 - INFO - Request Parameters - Page 3:
2025-06-25 16:30:50,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:50,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:51,621 - INFO - Response - Page 3:
2025-06-25 16:30:51,621 - INFO - 第 3 页获取到 50 条记录
2025-06-25 16:30:52,137 - INFO - Request Parameters - Page 4:
2025-06-25 16:30:52,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:52,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:52,824 - INFO - Response - Page 4:
2025-06-25 16:30:52,824 - INFO - 第 4 页获取到 50 条记录
2025-06-25 16:30:53,340 - INFO - Request Parameters - Page 5:
2025-06-25 16:30:53,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:53,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:54,027 - INFO - Response - Page 5:
2025-06-25 16:30:54,027 - INFO - 第 5 页获取到 50 条记录
2025-06-25 16:30:54,527 - INFO - Request Parameters - Page 6:
2025-06-25 16:30:54,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:54,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:55,184 - INFO - Response - Page 6:
2025-06-25 16:30:55,184 - INFO - 第 6 页获取到 50 条记录
2025-06-25 16:30:55,684 - INFO - Request Parameters - Page 7:
2025-06-25 16:30:55,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:55,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:56,402 - INFO - Response - Page 7:
2025-06-25 16:30:56,402 - INFO - 第 7 页获取到 50 条记录
2025-06-25 16:30:56,902 - INFO - Request Parameters - Page 8:
2025-06-25 16:30:56,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:56,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:57,543 - INFO - Response - Page 8:
2025-06-25 16:30:57,543 - INFO - 第 8 页获取到 50 条记录
2025-06-25 16:30:58,059 - INFO - Request Parameters - Page 9:
2025-06-25 16:30:58,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:58,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:58,762 - INFO - Response - Page 9:
2025-06-25 16:30:58,762 - INFO - 第 9 页获取到 50 条记录
2025-06-25 16:30:59,277 - INFO - Request Parameters - Page 10:
2025-06-25 16:30:59,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:30:59,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:30:59,887 - INFO - Response - Page 10:
2025-06-25 16:30:59,887 - INFO - 第 10 页获取到 50 条记录
2025-06-25 16:31:00,402 - INFO - Request Parameters - Page 11:
2025-06-25 16:31:00,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:00,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:01,012 - INFO - Response - Page 11:
2025-06-25 16:31:01,012 - INFO - 第 11 页获取到 44 条记录
2025-06-25 16:31:01,512 - INFO - 查询完成，共获取到 544 条记录
2025-06-25 16:31:01,512 - INFO - 获取到 544 条表单数据
2025-06-25 16:31:01,512 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-06-25 16:31:01,512 - INFO - 开始批量插入 1 条新记录
2025-06-25 16:31:01,668 - INFO - 批量插入响应状态码: 200
2025-06-25 16:31:01,668 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 08:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15D78C70-05DD-7B85-A323-9804A46DB379', 'x-acs-trace-id': '2b6dcc8f68be08ff799d3e91f2e14943', 'etag': '60Ga1ueoy5VKZJ8+aH7gypA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 16:31:01,668 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q613MLWF7K6AAE9S9FQQG913ULW2PBCMN4']}
2025-06-25 16:31:01,668 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-25 16:31:01,668 - INFO - 成功插入的数据ID: ['FINST-F3G66Q613MLWF7K6AAE9S9FQQG913ULW2PBCMN4']
2025-06-25 16:31:06,684 - INFO - 批量插入完成，共 1 条记录
2025-06-25 16:31:06,684 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-25 16:31:06,684 - INFO - 开始处理日期: 2025-06-24
2025-06-25 16:31:06,684 - INFO - Request Parameters - Page 1:
2025-06-25 16:31:06,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:06,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:07,340 - INFO - Response - Page 1:
2025-06-25 16:31:07,340 - INFO - 第 1 页获取到 50 条记录
2025-06-25 16:31:07,855 - INFO - Request Parameters - Page 2:
2025-06-25 16:31:07,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:07,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:08,621 - INFO - Response - Page 2:
2025-06-25 16:31:08,621 - INFO - 第 2 页获取到 50 条记录
2025-06-25 16:31:09,137 - INFO - Request Parameters - Page 3:
2025-06-25 16:31:09,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:09,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:09,808 - INFO - Response - Page 3:
2025-06-25 16:31:09,808 - INFO - 第 3 页获取到 50 条记录
2025-06-25 16:31:10,324 - INFO - Request Parameters - Page 4:
2025-06-25 16:31:10,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:10,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:10,949 - INFO - Response - Page 4:
2025-06-25 16:31:10,949 - INFO - 第 4 页获取到 50 条记录
2025-06-25 16:31:11,465 - INFO - Request Parameters - Page 5:
2025-06-25 16:31:11,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:11,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:12,168 - INFO - Response - Page 5:
2025-06-25 16:31:12,168 - INFO - 第 5 页获取到 50 条记录
2025-06-25 16:31:12,683 - INFO - Request Parameters - Page 6:
2025-06-25 16:31:12,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:12,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:13,371 - INFO - Response - Page 6:
2025-06-25 16:31:13,371 - INFO - 第 6 页获取到 50 条记录
2025-06-25 16:31:13,887 - INFO - Request Parameters - Page 7:
2025-06-25 16:31:13,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:13,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:14,558 - INFO - Response - Page 7:
2025-06-25 16:31:14,558 - INFO - 第 7 页获取到 50 条记录
2025-06-25 16:31:15,058 - INFO - Request Parameters - Page 8:
2025-06-25 16:31:15,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:15,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:15,683 - INFO - Response - Page 8:
2025-06-25 16:31:15,683 - INFO - 第 8 页获取到 49 条记录
2025-06-25 16:31:16,199 - INFO - 查询完成，共获取到 399 条记录
2025-06-25 16:31:16,199 - INFO - 获取到 399 条表单数据
2025-06-25 16:31:16,199 - INFO - 当前日期 2025-06-24 有 108 条MySQL数据需要处理
2025-06-25 16:31:16,199 - INFO - 开始批量插入 5 条新记录
2025-06-25 16:31:16,387 - INFO - 批量插入响应状态码: 200
2025-06-25 16:31:16,387 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 08:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '247', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B06D6B70-6067-7BFE-8BB0-408A27D9B123', 'x-acs-trace-id': '594221a47402d7f839cc7c936b9b8814', 'etag': '2PMLw5XsmMVI0Lu6likWVpg7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 16:31:16,387 - INFO - 批量插入响应体: {'result': ['FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMV', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMW', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMX', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMY', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMZ']}
2025-06-25 16:31:16,387 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-06-25 16:31:16,387 - INFO - 成功插入的数据ID: ['FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMV', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMW', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMX', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMY', 'FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMZ']
2025-06-25 16:31:21,402 - INFO - 批量插入完成，共 5 条记录
2025-06-25 16:31:21,402 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-06-25 16:31:21,402 - INFO - 开始处理日期: 2025-06-25
2025-06-25 16:31:21,402 - INFO - Request Parameters - Page 1:
2025-06-25 16:31:21,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:31:21,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:31:21,840 - INFO - Response - Page 1:
2025-06-25 16:31:21,840 - INFO - 第 1 页获取到 1 条记录
2025-06-25 16:31:22,355 - INFO - 查询完成，共获取到 1 条记录
2025-06-25 16:31:22,355 - INFO - 获取到 1 条表单数据
2025-06-25 16:31:22,355 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-25 16:31:22,355 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 16:31:22,355 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 1 条
2025-06-25 16:32:22,370 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 16:32:22,370 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 16:32:22,370 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 16:32:22,511 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 432 条记录
2025-06-25 16:32:22,511 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-25 16:32:22,511 - INFO - 开始处理日期: 2025-06-24
2025-06-25 16:32:22,511 - INFO - Request Parameters - Page 1:
2025-06-25 16:32:22,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:22,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:23,308 - INFO - Response - Page 1:
2025-06-25 16:32:23,308 - INFO - 第 1 页获取到 50 条记录
2025-06-25 16:32:23,808 - INFO - Request Parameters - Page 2:
2025-06-25 16:32:23,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:23,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:24,417 - INFO - Response - Page 2:
2025-06-25 16:32:24,417 - INFO - 第 2 页获取到 50 条记录
2025-06-25 16:32:24,933 - INFO - Request Parameters - Page 3:
2025-06-25 16:32:24,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:24,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:25,714 - INFO - Response - Page 3:
2025-06-25 16:32:25,714 - INFO - 第 3 页获取到 50 条记录
2025-06-25 16:32:26,230 - INFO - Request Parameters - Page 4:
2025-06-25 16:32:26,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:26,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:26,886 - INFO - Response - Page 4:
2025-06-25 16:32:26,886 - INFO - 第 4 页获取到 50 条记录
2025-06-25 16:32:27,386 - INFO - Request Parameters - Page 5:
2025-06-25 16:32:27,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:27,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:28,058 - INFO - Response - Page 5:
2025-06-25 16:32:28,058 - INFO - 第 5 页获取到 50 条记录
2025-06-25 16:32:28,558 - INFO - Request Parameters - Page 6:
2025-06-25 16:32:28,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:28,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:29,214 - INFO - Response - Page 6:
2025-06-25 16:32:29,214 - INFO - 第 6 页获取到 50 条记录
2025-06-25 16:32:29,714 - INFO - Request Parameters - Page 7:
2025-06-25 16:32:29,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:29,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:30,370 - INFO - Response - Page 7:
2025-06-25 16:32:30,370 - INFO - 第 7 页获取到 50 条记录
2025-06-25 16:32:30,870 - INFO - Request Parameters - Page 8:
2025-06-25 16:32:30,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:30,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:31,620 - INFO - Response - Page 8:
2025-06-25 16:32:31,620 - INFO - 第 8 页获取到 50 条记录
2025-06-25 16:32:32,136 - INFO - Request Parameters - Page 9:
2025-06-25 16:32:32,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:32,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:32,620 - INFO - Response - Page 9:
2025-06-25 16:32:32,620 - INFO - 第 9 页获取到 4 条记录
2025-06-25 16:32:33,136 - INFO - 查询完成，共获取到 404 条记录
2025-06-25 16:32:33,136 - INFO - 获取到 404 条表单数据
2025-06-25 16:32:33,136 - INFO - 当前日期 2025-06-24 有 421 条MySQL数据需要处理
2025-06-25 16:32:33,152 - INFO - 开始批量插入 17 条新记录
2025-06-25 16:32:33,308 - INFO - 批量插入响应状态码: 200
2025-06-25 16:32:33,308 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 08:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '811', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E305346-6926-7DDD-B9D5-1CF7A7491398', 'x-acs-trace-id': '26adb0d244ab7d1b970ed8bc94177a02', 'etag': '80z+d8iyaoNyJ1TIFvzjBjg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 16:32:33,308 - INFO - 批量插入响应体: {'result': ['FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM2', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM3', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM4', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM5', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM6', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM7', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM8', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM9', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMA', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMB', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMC', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMD', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCME', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMF', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMG', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMH', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMI']}
2025-06-25 16:32:33,308 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-06-25 16:32:33,308 - INFO - 成功插入的数据ID: ['FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM2', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM3', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM4', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM5', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM6', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM7', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM8', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCM9', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMA', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMB', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMC', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMD', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCME', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMF', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMG', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMH', 'FINST-K7G66FA1MYLWF6O2A5EWIC124VY02KBV4PBCMI']
2025-06-25 16:32:38,323 - INFO - 批量插入完成，共 17 条记录
2025-06-25 16:32:38,323 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 17 条，错误: 0 条
2025-06-25 16:32:38,323 - INFO - 开始处理日期: 2025-06-25
2025-06-25 16:32:38,323 - INFO - Request Parameters - Page 1:
2025-06-25 16:32:38,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 16:32:38,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 16:32:38,761 - INFO - Response - Page 1:
2025-06-25 16:32:38,761 - INFO - 第 1 页获取到 1 条记录
2025-06-25 16:32:39,277 - INFO - 查询完成，共获取到 1 条记录
2025-06-25 16:32:39,277 - INFO - 获取到 1 条表单数据
2025-06-25 16:32:39,277 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-25 16:32:39,277 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 16:32:39,277 - INFO - 数据同步完成！更新: 0 条，插入: 17 条，错误: 0 条
2025-06-25 16:32:39,277 - INFO - 同步完成
2025-06-25 19:30:33,965 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 19:30:33,965 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 19:30:33,965 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 19:30:34,090 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 152 条记录
2025-06-25 19:30:34,090 - INFO - 获取到 4 个日期需要处理: ['2025-06-03', '2025-06-23', '2025-06-24', '2025-06-25']
2025-06-25 19:30:34,106 - INFO - 开始处理日期: 2025-06-03
2025-06-25 19:30:34,106 - INFO - Request Parameters - Page 1:
2025-06-25 19:30:34,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:34,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:42,218 - ERROR - 处理日期 2025-06-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BF3C9931-0B3A-7B0B-B50D-A1E38EED1561 Response: {'code': 'ServiceUnavailable', 'requestid': 'BF3C9931-0B3A-7B0B-B50D-A1E38EED1561', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BF3C9931-0B3A-7B0B-B50D-A1E38EED1561)
2025-06-25 19:30:42,218 - INFO - 开始处理日期: 2025-06-23
2025-06-25 19:30:42,218 - INFO - Request Parameters - Page 1:
2025-06-25 19:30:42,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:42,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:48,220 - INFO - Response - Page 1:
2025-06-25 19:30:48,220 - INFO - 第 1 页获取到 50 条记录
2025-06-25 19:30:48,720 - INFO - Request Parameters - Page 2:
2025-06-25 19:30:48,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:48,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:49,392 - INFO - Response - Page 2:
2025-06-25 19:30:49,392 - INFO - 第 2 页获取到 50 条记录
2025-06-25 19:30:49,892 - INFO - Request Parameters - Page 3:
2025-06-25 19:30:49,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:49,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:50,533 - INFO - Response - Page 3:
2025-06-25 19:30:50,533 - INFO - 第 3 页获取到 50 条记录
2025-06-25 19:30:51,033 - INFO - Request Parameters - Page 4:
2025-06-25 19:30:51,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:51,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:51,705 - INFO - Response - Page 4:
2025-06-25 19:30:51,705 - INFO - 第 4 页获取到 50 条记录
2025-06-25 19:30:52,221 - INFO - Request Parameters - Page 5:
2025-06-25 19:30:52,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:52,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:52,831 - INFO - Response - Page 5:
2025-06-25 19:30:52,831 - INFO - 第 5 页获取到 50 条记录
2025-06-25 19:30:53,346 - INFO - Request Parameters - Page 6:
2025-06-25 19:30:53,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:53,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:54,050 - INFO - Response - Page 6:
2025-06-25 19:30:54,050 - INFO - 第 6 页获取到 50 条记录
2025-06-25 19:30:54,566 - INFO - Request Parameters - Page 7:
2025-06-25 19:30:54,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:54,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:55,269 - INFO - Response - Page 7:
2025-06-25 19:30:55,269 - INFO - 第 7 页获取到 50 条记录
2025-06-25 19:30:55,769 - INFO - Request Parameters - Page 8:
2025-06-25 19:30:55,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:55,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:56,566 - INFO - Response - Page 8:
2025-06-25 19:30:56,566 - INFO - 第 8 页获取到 50 条记录
2025-06-25 19:30:57,082 - INFO - Request Parameters - Page 9:
2025-06-25 19:30:57,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:57,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:57,707 - INFO - Response - Page 9:
2025-06-25 19:30:57,707 - INFO - 第 9 页获取到 50 条记录
2025-06-25 19:30:58,223 - INFO - Request Parameters - Page 10:
2025-06-25 19:30:58,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:58,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:58,848 - INFO - Response - Page 10:
2025-06-25 19:30:58,848 - INFO - 第 10 页获取到 50 条记录
2025-06-25 19:30:59,349 - INFO - Request Parameters - Page 11:
2025-06-25 19:30:59,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:30:59,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:30:59,958 - INFO - Response - Page 11:
2025-06-25 19:30:59,958 - INFO - 第 11 页获取到 45 条记录
2025-06-25 19:31:00,458 - INFO - 查询完成，共获取到 545 条记录
2025-06-25 19:31:00,458 - INFO - 获取到 545 条表单数据
2025-06-25 19:31:00,458 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-06-25 19:31:00,458 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 19:31:00,458 - INFO - 开始处理日期: 2025-06-24
2025-06-25 19:31:00,458 - INFO - Request Parameters - Page 1:
2025-06-25 19:31:00,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:00,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:01,099 - INFO - Response - Page 1:
2025-06-25 19:31:01,099 - INFO - 第 1 页获取到 50 条记录
2025-06-25 19:31:01,599 - INFO - Request Parameters - Page 2:
2025-06-25 19:31:01,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:01,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:02,256 - INFO - Response - Page 2:
2025-06-25 19:31:02,256 - INFO - 第 2 页获取到 50 条记录
2025-06-25 19:31:02,772 - INFO - Request Parameters - Page 3:
2025-06-25 19:31:02,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:02,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:03,475 - INFO - Response - Page 3:
2025-06-25 19:31:03,475 - INFO - 第 3 页获取到 50 条记录
2025-06-25 19:31:03,991 - INFO - Request Parameters - Page 4:
2025-06-25 19:31:03,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:03,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:04,632 - INFO - Response - Page 4:
2025-06-25 19:31:04,632 - INFO - 第 4 页获取到 50 条记录
2025-06-25 19:31:05,132 - INFO - Request Parameters - Page 5:
2025-06-25 19:31:05,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:05,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:05,757 - INFO - Response - Page 5:
2025-06-25 19:31:05,757 - INFO - 第 5 页获取到 50 条记录
2025-06-25 19:31:06,273 - INFO - Request Parameters - Page 6:
2025-06-25 19:31:06,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:06,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:06,914 - INFO - Response - Page 6:
2025-06-25 19:31:06,914 - INFO - 第 6 页获取到 50 条记录
2025-06-25 19:31:07,429 - INFO - Request Parameters - Page 7:
2025-06-25 19:31:07,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:07,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:08,039 - INFO - Response - Page 7:
2025-06-25 19:31:08,039 - INFO - 第 7 页获取到 50 条记录
2025-06-25 19:31:08,539 - INFO - Request Parameters - Page 8:
2025-06-25 19:31:08,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:08,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:09,196 - INFO - Response - Page 8:
2025-06-25 19:31:09,196 - INFO - 第 8 页获取到 50 条记录
2025-06-25 19:31:09,711 - INFO - Request Parameters - Page 9:
2025-06-25 19:31:09,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:09,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:10,274 - INFO - Response - Page 9:
2025-06-25 19:31:10,274 - INFO - 第 9 页获取到 21 条记录
2025-06-25 19:31:10,790 - INFO - 查询完成，共获取到 421 条记录
2025-06-25 19:31:10,790 - INFO - 获取到 421 条表单数据
2025-06-25 19:31:10,790 - INFO - 当前日期 2025-06-24 有 142 条MySQL数据需要处理
2025-06-25 19:31:10,790 - INFO - 开始更新记录 - 表单实例ID: FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMX
2025-06-25 19:31:11,290 - INFO - 更新表单数据成功: FINST-487664C1TMLWJQC69X9M79A4YL8C2PY73PBCMX
2025-06-25 19:31:11,290 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-25 19:31:11,290 - INFO - 开始批量插入 34 条新记录
2025-06-25 19:31:11,509 - INFO - 批量插入响应状态码: 200
2025-06-25 19:31:11,509 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 11:31:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1644', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D42707D2-3E43-772E-B3D1-316A46EC37B1', 'x-acs-trace-id': 'c9e42f82ee6cdf9ddbac91665098f9ed', 'etag': '1PkXZMfGvIFayYSCWsTlzHw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 19:31:11,509 - INFO - 批量插入响应体: {'result': ['FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM86', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM96', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMA6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMB6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMC6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMD6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCME6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMF6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMG6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMH6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMI6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMJ6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMK6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCML6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMM6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMN6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMO6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMP6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMQ6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMR6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMS6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMT6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMU6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMV6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMW6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMX6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMY6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMZ6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM07', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM17', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM27', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM37', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM47', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM57']}
2025-06-25 19:31:11,509 - INFO - 批量插入表单数据成功，批次 1，共 34 条记录
2025-06-25 19:31:11,509 - INFO - 成功插入的数据ID: ['FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM86', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM96', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMA6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMB6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMC6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMD6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCME6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMF6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMG6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMH6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMI6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMJ6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMK6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCML6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMM6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMN6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMO6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMP6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMQ6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMR6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMS6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMT6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMU6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMV6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMW6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMX6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMY6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCMZ6', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM07', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM17', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM27', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM37', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM47', 'FINST-Q7866DD1D6LWCTX9D4QUJ5NBMBZF2DSSIVBCM57']
2025-06-25 19:31:16,526 - INFO - 批量插入完成，共 34 条记录
2025-06-25 19:31:16,526 - INFO - 日期 2025-06-24 处理完成 - 更新: 1 条，插入: 34 条，错误: 0 条
2025-06-25 19:31:16,526 - INFO - 开始处理日期: 2025-06-25
2025-06-25 19:31:16,526 - INFO - Request Parameters - Page 1:
2025-06-25 19:31:16,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:31:16,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:31:16,948 - INFO - Response - Page 1:
2025-06-25 19:31:16,948 - INFO - 第 1 页获取到 1 条记录
2025-06-25 19:31:17,464 - INFO - 查询完成，共获取到 1 条记录
2025-06-25 19:31:17,464 - INFO - 获取到 1 条表单数据
2025-06-25 19:31:17,464 - INFO - 当前日期 2025-06-25 有 3 条MySQL数据需要处理
2025-06-25 19:31:17,464 - INFO - 开始批量插入 2 条新记录
2025-06-25 19:31:17,620 - INFO - 批量插入响应状态码: 200
2025-06-25 19:31:17,620 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 11:31:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3C9B34E1-7005-7E40-A011-09101B9ADFCD', 'x-acs-trace-id': '60306d2340193767d9a0197b3c0d2af6', 'etag': '1GBhk266pVcg0/Ey1FMDbHQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 19:31:17,620 - INFO - 批量插入响应体: {'result': ['FINST-NS866I91G6LW2APDCDNFN8X7JFK42SHXIVBCMLC', 'FINST-NS866I91G6LW2APDCDNFN8X7JFK42SHXIVBCMMC']}
2025-06-25 19:31:17,620 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-25 19:31:17,620 - INFO - 成功插入的数据ID: ['FINST-NS866I91G6LW2APDCDNFN8X7JFK42SHXIVBCMLC', 'FINST-NS866I91G6LW2APDCDNFN8X7JFK42SHXIVBCMMC']
2025-06-25 19:31:22,637 - INFO - 批量插入完成，共 2 条记录
2025-06-25 19:31:22,637 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-25 19:31:22,637 - INFO - 数据同步完成！更新: 1 条，插入: 36 条，错误: 1 条
2025-06-25 19:32:22,673 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 19:32:22,673 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 19:32:22,673 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 19:32:22,814 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 507 条记录
2025-06-25 19:32:22,814 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-25 19:32:22,814 - INFO - 开始处理日期: 2025-06-24
2025-06-25 19:32:22,814 - INFO - Request Parameters - Page 1:
2025-06-25 19:32:22,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:22,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:23,455 - INFO - Response - Page 1:
2025-06-25 19:32:23,455 - INFO - 第 1 页获取到 50 条记录
2025-06-25 19:32:23,971 - INFO - Request Parameters - Page 2:
2025-06-25 19:32:23,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:23,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:24,627 - INFO - Response - Page 2:
2025-06-25 19:32:24,627 - INFO - 第 2 页获取到 50 条记录
2025-06-25 19:32:25,143 - INFO - Request Parameters - Page 3:
2025-06-25 19:32:25,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:25,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:25,784 - INFO - Response - Page 3:
2025-06-25 19:32:25,784 - INFO - 第 3 页获取到 50 条记录
2025-06-25 19:32:26,300 - INFO - Request Parameters - Page 4:
2025-06-25 19:32:26,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:26,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:26,987 - INFO - Response - Page 4:
2025-06-25 19:32:26,987 - INFO - 第 4 页获取到 50 条记录
2025-06-25 19:32:27,487 - INFO - Request Parameters - Page 5:
2025-06-25 19:32:27,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:27,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:28,144 - INFO - Response - Page 5:
2025-06-25 19:32:28,144 - INFO - 第 5 页获取到 50 条记录
2025-06-25 19:32:28,660 - INFO - Request Parameters - Page 6:
2025-06-25 19:32:28,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:28,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:29,269 - INFO - Response - Page 6:
2025-06-25 19:32:29,269 - INFO - 第 6 页获取到 50 条记录
2025-06-25 19:32:29,785 - INFO - Request Parameters - Page 7:
2025-06-25 19:32:29,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:29,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:30,473 - INFO - Response - Page 7:
2025-06-25 19:32:30,473 - INFO - 第 7 页获取到 50 条记录
2025-06-25 19:32:30,989 - INFO - Request Parameters - Page 8:
2025-06-25 19:32:30,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:30,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:31,629 - INFO - Response - Page 8:
2025-06-25 19:32:31,629 - INFO - 第 8 页获取到 50 条记录
2025-06-25 19:32:32,130 - INFO - Request Parameters - Page 9:
2025-06-25 19:32:32,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:32,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:32,786 - INFO - Response - Page 9:
2025-06-25 19:32:32,786 - INFO - 第 9 页获取到 50 条记录
2025-06-25 19:32:33,302 - INFO - Request Parameters - Page 10:
2025-06-25 19:32:33,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:33,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:33,802 - INFO - Response - Page 10:
2025-06-25 19:32:33,802 - INFO - 第 10 页获取到 5 条记录
2025-06-25 19:32:34,302 - INFO - 查询完成，共获取到 455 条记录
2025-06-25 19:32:34,302 - INFO - 获取到 455 条表单数据
2025-06-25 19:32:34,302 - INFO - 当前日期 2025-06-24 有 492 条MySQL数据需要处理
2025-06-25 19:32:34,318 - INFO - 开始批量插入 37 条新记录
2025-06-25 19:32:34,552 - INFO - 批量插入响应状态码: 200
2025-06-25 19:32:34,552 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 11:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1788', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BFB5FFAF-EE58-7BE1-A962-B632DF3519D3', 'x-acs-trace-id': '8b6504dc651b1a60e75a881ae442bbb7', 'etag': '1o0StvCaRfcyh+wpr6ezXOw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 19:32:34,552 - INFO - 批量插入响应体: {'result': ['FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMB2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMC2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMD2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCME2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMF2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMG2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMH2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMI2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMJ2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMK2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCML2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMM2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMN2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMO2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMP2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMQ2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMR2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMS2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMT2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMU2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMV2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMW2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMX2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMY2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMZ2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM03', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM13', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM23', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM33', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM43', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM53', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM63', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM73', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM83', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM93', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMA3', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMB3']}
2025-06-25 19:32:34,552 - INFO - 批量插入表单数据成功，批次 1，共 37 条记录
2025-06-25 19:32:34,552 - INFO - 成功插入的数据ID: ['FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMB2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMC2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMD2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCME2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMF2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMG2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMH2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMI2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMJ2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMK2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCML2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMM2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMN2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMO2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMP2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMQ2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMR2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMS2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMT2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMU2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMV2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMW2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMX2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMY2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMZ2', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM03', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM13', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM23', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM33', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM43', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM53', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM63', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM73', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM83', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCM93', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMA3', 'FINST-AJF66F71CNLW3ILO70EJC566LGOE39UKKVBCMB3']
2025-06-25 19:32:39,570 - INFO - 批量插入完成，共 37 条记录
2025-06-25 19:32:39,570 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 37 条，错误: 0 条
2025-06-25 19:32:39,570 - INFO - 开始处理日期: 2025-06-25
2025-06-25 19:32:39,570 - INFO - Request Parameters - Page 1:
2025-06-25 19:32:39,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 19:32:39,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 19:32:40,023 - INFO - Response - Page 1:
2025-06-25 19:32:40,023 - INFO - 第 1 页获取到 3 条记录
2025-06-25 19:32:40,523 - INFO - 查询完成，共获取到 3 条记录
2025-06-25 19:32:40,523 - INFO - 获取到 3 条表单数据
2025-06-25 19:32:40,523 - INFO - 当前日期 2025-06-25 有 3 条MySQL数据需要处理
2025-06-25 19:32:40,523 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 19:32:40,523 - INFO - 数据同步完成！更新: 0 条，插入: 37 条，错误: 0 条
2025-06-25 19:32:40,523 - INFO - 同步完成
2025-06-25 22:30:34,224 - INFO - 使用默认增量同步（当天更新数据）
2025-06-25 22:30:34,224 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-25 22:30:34,224 - INFO - 查询参数: ('2025-06-25',)
2025-06-25 22:30:34,365 - INFO - MySQL查询成功，增量数据（日期: 2025-06-25），共获取 205 条记录
2025-06-25 22:30:34,365 - INFO - 获取到 4 个日期需要处理: ['2025-06-03', '2025-06-23', '2025-06-24', '2025-06-25']
2025-06-25 22:30:34,365 - INFO - 开始处理日期: 2025-06-03
2025-06-25 22:30:34,365 - INFO - Request Parameters - Page 1:
2025-06-25 22:30:34,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:34,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:40,883 - INFO - Response - Page 1:
2025-06-25 22:30:40,883 - INFO - 第 1 页获取到 50 条记录
2025-06-25 22:30:41,399 - INFO - Request Parameters - Page 2:
2025-06-25 22:30:41,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:41,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:42,071 - INFO - Response - Page 2:
2025-06-25 22:30:42,071 - INFO - 第 2 页获取到 50 条记录
2025-06-25 22:30:42,587 - INFO - Request Parameters - Page 3:
2025-06-25 22:30:42,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:42,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:50,699 - ERROR - 处理日期 2025-06-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 786020D2-58F9-7A72-91BA-8F75B4ACBCE1 Response: {'code': 'ServiceUnavailable', 'requestid': '786020D2-58F9-7A72-91BA-8F75B4ACBCE1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 786020D2-58F9-7A72-91BA-8F75B4ACBCE1)
2025-06-25 22:30:50,699 - INFO - 开始处理日期: 2025-06-23
2025-06-25 22:30:50,699 - INFO - Request Parameters - Page 1:
2025-06-25 22:30:50,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:50,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:51,386 - INFO - Response - Page 1:
2025-06-25 22:30:51,386 - INFO - 第 1 页获取到 50 条记录
2025-06-25 22:30:51,902 - INFO - Request Parameters - Page 2:
2025-06-25 22:30:51,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:51,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:52,496 - INFO - Response - Page 2:
2025-06-25 22:30:52,496 - INFO - 第 2 页获取到 50 条记录
2025-06-25 22:30:53,012 - INFO - Request Parameters - Page 3:
2025-06-25 22:30:53,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:53,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:53,715 - INFO - Response - Page 3:
2025-06-25 22:30:53,715 - INFO - 第 3 页获取到 50 条记录
2025-06-25 22:30:54,231 - INFO - Request Parameters - Page 4:
2025-06-25 22:30:54,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:54,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:54,934 - INFO - Response - Page 4:
2025-06-25 22:30:54,934 - INFO - 第 4 页获取到 50 条记录
2025-06-25 22:30:55,435 - INFO - Request Parameters - Page 5:
2025-06-25 22:30:55,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:55,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:56,091 - INFO - Response - Page 5:
2025-06-25 22:30:56,091 - INFO - 第 5 页获取到 50 条记录
2025-06-25 22:30:56,591 - INFO - Request Parameters - Page 6:
2025-06-25 22:30:56,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:56,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:57,279 - INFO - Response - Page 6:
2025-06-25 22:30:57,279 - INFO - 第 6 页获取到 50 条记录
2025-06-25 22:30:57,795 - INFO - Request Parameters - Page 7:
2025-06-25 22:30:57,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:57,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:58,483 - INFO - Response - Page 7:
2025-06-25 22:30:58,483 - INFO - 第 7 页获取到 50 条记录
2025-06-25 22:30:58,998 - INFO - Request Parameters - Page 8:
2025-06-25 22:30:58,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:30:58,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:30:59,655 - INFO - Response - Page 8:
2025-06-25 22:30:59,655 - INFO - 第 8 页获取到 50 条记录
2025-06-25 22:31:00,171 - INFO - Request Parameters - Page 9:
2025-06-25 22:31:00,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:00,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:00,827 - INFO - Response - Page 9:
2025-06-25 22:31:00,827 - INFO - 第 9 页获取到 50 条记录
2025-06-25 22:31:01,327 - INFO - Request Parameters - Page 10:
2025-06-25 22:31:01,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:01,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:01,968 - INFO - Response - Page 10:
2025-06-25 22:31:01,968 - INFO - 第 10 页获取到 50 条记录
2025-06-25 22:31:02,484 - INFO - Request Parameters - Page 11:
2025-06-25 22:31:02,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:02,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:03,109 - INFO - Response - Page 11:
2025-06-25 22:31:03,109 - INFO - 第 11 页获取到 45 条记录
2025-06-25 22:31:03,625 - INFO - 查询完成，共获取到 545 条记录
2025-06-25 22:31:03,625 - INFO - 获取到 545 条表单数据
2025-06-25 22:31:03,625 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-06-25 22:31:03,625 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 22:31:03,625 - INFO - 开始处理日期: 2025-06-24
2025-06-25 22:31:03,625 - INFO - Request Parameters - Page 1:
2025-06-25 22:31:03,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:03,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:04,344 - INFO - Response - Page 1:
2025-06-25 22:31:04,344 - INFO - 第 1 页获取到 50 条记录
2025-06-25 22:31:04,860 - INFO - Request Parameters - Page 2:
2025-06-25 22:31:04,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:04,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:05,563 - INFO - Response - Page 2:
2025-06-25 22:31:05,563 - INFO - 第 2 页获取到 50 条记录
2025-06-25 22:31:06,063 - INFO - Request Parameters - Page 3:
2025-06-25 22:31:06,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:06,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:06,704 - INFO - Response - Page 3:
2025-06-25 22:31:06,704 - INFO - 第 3 页获取到 50 条记录
2025-06-25 22:31:07,220 - INFO - Request Parameters - Page 4:
2025-06-25 22:31:07,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:07,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:07,876 - INFO - Response - Page 4:
2025-06-25 22:31:07,876 - INFO - 第 4 页获取到 50 条记录
2025-06-25 22:31:08,392 - INFO - Request Parameters - Page 5:
2025-06-25 22:31:08,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:08,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:09,017 - INFO - Response - Page 5:
2025-06-25 22:31:09,017 - INFO - 第 5 页获取到 50 条记录
2025-06-25 22:31:09,518 - INFO - Request Parameters - Page 6:
2025-06-25 22:31:09,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:09,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:10,205 - INFO - Response - Page 6:
2025-06-25 22:31:10,205 - INFO - 第 6 页获取到 50 条记录
2025-06-25 22:31:10,721 - INFO - Request Parameters - Page 7:
2025-06-25 22:31:10,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:10,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:11,424 - INFO - Response - Page 7:
2025-06-25 22:31:11,424 - INFO - 第 7 页获取到 50 条记录
2025-06-25 22:31:11,940 - INFO - Request Parameters - Page 8:
2025-06-25 22:31:11,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:11,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:12,597 - INFO - Response - Page 8:
2025-06-25 22:31:12,597 - INFO - 第 8 页获取到 50 条记录
2025-06-25 22:31:13,113 - INFO - Request Parameters - Page 9:
2025-06-25 22:31:13,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:13,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:13,878 - INFO - Response - Page 9:
2025-06-25 22:31:13,878 - INFO - 第 9 页获取到 50 条记录
2025-06-25 22:31:14,394 - INFO - Request Parameters - Page 10:
2025-06-25 22:31:14,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:14,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:15,051 - INFO - Response - Page 10:
2025-06-25 22:31:15,051 - INFO - 第 10 页获取到 42 条记录
2025-06-25 22:31:15,551 - INFO - 查询完成，共获取到 492 条记录
2025-06-25 22:31:15,551 - INFO - 获取到 492 条表单数据
2025-06-25 22:31:15,551 - INFO - 当前日期 2025-06-24 有 142 条MySQL数据需要处理
2025-06-25 22:31:15,551 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 22:31:15,551 - INFO - 开始处理日期: 2025-06-25
2025-06-25 22:31:15,551 - INFO - Request Parameters - Page 1:
2025-06-25 22:31:15,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:31:15,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:31:16,004 - INFO - Response - Page 1:
2025-06-25 22:31:16,004 - INFO - 第 1 页获取到 3 条记录
2025-06-25 22:31:16,520 - INFO - 查询完成，共获取到 3 条记录
2025-06-25 22:31:16,520 - INFO - 获取到 3 条表单数据
2025-06-25 22:31:16,520 - INFO - 当前日期 2025-06-25 有 55 条MySQL数据需要处理
2025-06-25 22:31:16,520 - INFO - 开始批量插入 52 条新记录
2025-06-25 22:31:16,786 - INFO - 批量插入响应状态码: 200
2025-06-25 22:31:16,786 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 14:31:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E98D5B89-9392-768A-ADF4-84FD48D2CADD', 'x-acs-trace-id': '97c4e0664282fb8f9ea9035a083befe5', 'etag': '2Pqyml4mUOWyMDkybL3Ttjw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 22:31:16,786 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMNG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMOG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMPG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMQG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMRG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMSG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMTG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMUG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMVG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMWG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMXG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMYG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMZG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM0H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM1H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM2H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM3H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM4H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM5H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM6H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM7H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM8H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM9H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMAH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMBH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMCH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMDH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMEH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMFH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMGH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMHH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMIH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMJH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMKH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMLH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMMH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMNH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMOH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMPH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMQH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMRH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMSH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMTH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMUH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMVH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMWH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMXH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMYH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMZH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM0I']}
2025-06-25 22:31:16,786 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-25 22:31:16,786 - INFO - 成功插入的数据ID: ['FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMNG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMOG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMPG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMQG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMRG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMSG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMTG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMUG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMVG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMWG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMXG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMYG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMZG', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM0H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM1H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM2H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM3H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM4H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM5H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM6H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM7H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM8H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM9H', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMAH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMBH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMCH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMDH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMEH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMFH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMGH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMHH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMIH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMJH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMKH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMLH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMMH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMNH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMOH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMPH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMQH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMRH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMSH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMTH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMUH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMVH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMWH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMXH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMYH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCMZH', 'FINST-MUC66Q81TLLWAOM66CLKADZMPWRV3EDBY1CCM0I']
2025-06-25 22:31:22,022 - INFO - 批量插入响应状态码: 200
2025-06-25 22:31:22,022 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 14:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E8B910C6-EE4D-7D0B-93D4-B34F59DB2DBE', 'x-acs-trace-id': 'bfefc06a5c13042f735e4db6ae4a05fc', 'etag': '1SKveIYPHqRNmpKsKAi10VQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 22:31:22,022 - INFO - 批量插入响应体: {'result': ['FINST-V4G66WC1PKLWPIMQA7D81CUMUCF63AEFY1CCM75', 'FINST-V4G66WC1PKLWPIMQA7D81CUMUCF63AEFY1CCM85']}
2025-06-25 22:31:22,022 - INFO - 批量插入表单数据成功，批次 2，共 2 条记录
2025-06-25 22:31:22,022 - INFO - 成功插入的数据ID: ['FINST-V4G66WC1PKLWPIMQA7D81CUMUCF63AEFY1CCM75', 'FINST-V4G66WC1PKLWPIMQA7D81CUMUCF63AEFY1CCM85']
2025-06-25 22:31:27,039 - INFO - 批量插入完成，共 52 条记录
2025-06-25 22:31:27,039 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 52 条，错误: 0 条
2025-06-25 22:31:27,039 - INFO - 数据同步完成！更新: 0 条，插入: 52 条，错误: 1 条
2025-06-25 22:32:27,075 - INFO - 开始同步昨天与今天的销售数据: 2025-06-24 至 2025-06-25
2025-06-25 22:32:27,075 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-25 22:32:27,075 - INFO - 查询参数: ('2025-06-24', '2025-06-25')
2025-06-25 22:32:27,231 - INFO - MySQL查询成功，时间段: 2025-06-24 至 2025-06-25，共获取 560 条记录
2025-06-25 22:32:27,231 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-25 22:32:27,231 - INFO - 开始处理日期: 2025-06-24
2025-06-25 22:32:27,231 - INFO - Request Parameters - Page 1:
2025-06-25 22:32:27,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:27,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:27,919 - INFO - Response - Page 1:
2025-06-25 22:32:27,919 - INFO - 第 1 页获取到 50 条记录
2025-06-25 22:32:28,435 - INFO - Request Parameters - Page 2:
2025-06-25 22:32:28,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:28,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:29,076 - INFO - Response - Page 2:
2025-06-25 22:32:29,076 - INFO - 第 2 页获取到 50 条记录
2025-06-25 22:32:29,592 - INFO - Request Parameters - Page 3:
2025-06-25 22:32:29,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:29,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:30,342 - INFO - Response - Page 3:
2025-06-25 22:32:30,342 - INFO - 第 3 页获取到 50 条记录
2025-06-25 22:32:30,858 - INFO - Request Parameters - Page 4:
2025-06-25 22:32:30,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:30,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:31,498 - INFO - Response - Page 4:
2025-06-25 22:32:31,498 - INFO - 第 4 页获取到 50 条记录
2025-06-25 22:32:32,014 - INFO - Request Parameters - Page 5:
2025-06-25 22:32:32,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:32,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:32,718 - INFO - Response - Page 5:
2025-06-25 22:32:32,718 - INFO - 第 5 页获取到 50 条记录
2025-06-25 22:32:33,233 - INFO - Request Parameters - Page 6:
2025-06-25 22:32:33,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:33,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:33,874 - INFO - Response - Page 6:
2025-06-25 22:32:33,874 - INFO - 第 6 页获取到 50 条记录
2025-06-25 22:32:34,390 - INFO - Request Parameters - Page 7:
2025-06-25 22:32:34,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:34,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:35,047 - INFO - Response - Page 7:
2025-06-25 22:32:35,047 - INFO - 第 7 页获取到 50 条记录
2025-06-25 22:32:35,547 - INFO - Request Parameters - Page 8:
2025-06-25 22:32:35,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:35,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:36,188 - INFO - Response - Page 8:
2025-06-25 22:32:36,188 - INFO - 第 8 页获取到 50 条记录
2025-06-25 22:32:36,703 - INFO - Request Parameters - Page 9:
2025-06-25 22:32:36,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:36,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:37,329 - INFO - Response - Page 9:
2025-06-25 22:32:37,329 - INFO - 第 9 页获取到 50 条记录
2025-06-25 22:32:37,844 - INFO - Request Parameters - Page 10:
2025-06-25 22:32:37,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:37,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:38,485 - INFO - Response - Page 10:
2025-06-25 22:32:38,485 - INFO - 第 10 页获取到 42 条记录
2025-06-25 22:32:38,985 - INFO - 查询完成，共获取到 492 条记录
2025-06-25 22:32:38,985 - INFO - 获取到 492 条表单数据
2025-06-25 22:32:38,985 - INFO - 当前日期 2025-06-24 有 492 条MySQL数据需要处理
2025-06-25 22:32:39,001 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 22:32:39,001 - INFO - 开始处理日期: 2025-06-25
2025-06-25 22:32:39,001 - INFO - Request Parameters - Page 1:
2025-06-25 22:32:39,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:39,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:39,626 - INFO - Response - Page 1:
2025-06-25 22:32:39,626 - INFO - 第 1 页获取到 50 条记录
2025-06-25 22:32:40,126 - INFO - Request Parameters - Page 2:
2025-06-25 22:32:40,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 22:32:40,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 22:32:40,627 - INFO - Response - Page 2:
2025-06-25 22:32:40,627 - INFO - 第 2 页获取到 5 条记录
2025-06-25 22:32:41,142 - INFO - 查询完成，共获取到 55 条记录
2025-06-25 22:32:41,142 - INFO - 获取到 55 条表单数据
2025-06-25 22:32:41,142 - INFO - 当前日期 2025-06-25 有 55 条MySQL数据需要处理
2025-06-25 22:32:41,142 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 22:32:41,142 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 22:32:41,142 - INFO - 同步完成
