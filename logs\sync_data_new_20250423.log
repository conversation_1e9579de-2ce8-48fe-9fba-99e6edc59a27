2025-04-23 20:34:32,660 - INFO - 日志文件已创建: logs\sync_data_new_20250423.log
2025-04-23 20:34:33,038 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-23 20:34:33,039 - INFO - sales_data表已存在，无需创建
2025-04-23 20:34:33,040 - INFO - 开始数据同步流程...
2025-04-23 20:34:33,040 - INFO - 正在获取数衍平台数据...
2025-04-23 20:34:33,041 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-23 20:34:33,041 - INFO - 正在获取********至********的数据
2025-04-23 20:34:33,042 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-23 20:34:33,042 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BB0AF1329FEC6D889C1F92191BD55365'}
2025-04-23 20:34:33,947 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-23 20:34:33,949 - INFO - 过滤后保留 238 条记录
2025-04-23 20:34:35,950 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-23 20:34:35,973 - INFO - SQLite数据保存完成，统计信息：
2025-04-23 20:34:35,974 - INFO - - 总记录数: 238
2025-04-23 20:34:35,974 - INFO - - 成功插入: 0
2025-04-23 20:34:35,975 - INFO - - 成功更新: 0
2025-04-23 20:34:35,975 - INFO - - 无需更新: 238
2025-04-23 20:34:35,975 - INFO - - 处理失败: 0
2025-04-23 20:34:36,155 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250423.xlsx
2025-04-23 20:34:36,156 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-23 20:34:36,156 - INFO - 正在获取宜搭表单数据...
2025-04-23 20:34:36,275 - INFO - 开始从宜搭表单获取数据，时间段: ******** 至 *************-04-23 20:34:36,275 - INFO - Request Parameters - Page 1:
2025-04-23 20:34:36,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:34:36,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:34:36,838 - INFO - Response - Page 1:
2025-04-23 20:34:36,839 - INFO - 第 1 页获取到 100 条记录
2025-04-23 20:34:36,839 - INFO - Request Parameters - Page 2:
2025-04-23 20:34:36,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:34:36,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:34:37,423 - INFO - Response - Page 2:
2025-04-23 20:34:37,423 - INFO - 第 2 页获取到 100 条记录
2025-04-23 20:34:37,424 - INFO - Request Parameters - Page 3:
2025-04-23 20:34:37,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:34:37,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:34:37,796 - INFO - Response - Page 3:
2025-04-23 20:34:37,797 - INFO - 第 3 页获取到 38 条记录
2025-04-23 20:34:37,797 - INFO - 查询完成，共获取到 238 条记录
2025-04-23 20:34:37,797 - INFO - 成功获取宜搭表单数据，共 238 条记录
2025-04-23 20:34:37,805 - INFO - 表单数据处理完成，成功处理 238 条记录
2025-04-23 20:34:37,805 - INFO - 成功获取宜搭表单数据，共 238 条记录
2025-04-23 20:34:37,805 - INFO - 正在处理宜搭数据索引...
2025-04-23 20:34:37,806 - INFO - 数据索引处理完成，共 238 条记录
2025-04-23 20:34:37,806 - INFO - 开始数据对比和同步...
2025-04-23 20:34:37,808 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-04-23 20:36:04,381 - INFO - 日志文件已创建: logs\sync_data_new_20250423.log
2025-04-23 20:36:04,784 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-23 20:36:04,785 - INFO - sales_data表已存在，无需创建
2025-04-23 20:36:04,785 - INFO - 开始数据同步流程...
2025-04-23 20:36:04,785 - INFO - 正在获取数衍平台数据...
2025-04-23 20:36:04,786 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-23 20:36:04,786 - INFO - 正在获取********至********的数据
2025-04-23 20:36:04,787 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-23 20:36:04,787 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B65129E1276CAA9ADEF1FF849ABEE899'}
2025-04-23 20:36:06,108 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-23 20:36:06,111 - INFO - 过滤后保留 238 条记录
2025-04-23 20:36:08,112 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-23 20:36:08,134 - INFO - SQLite数据保存完成，统计信息：
2025-04-23 20:36:08,134 - INFO - - 总记录数: 238
2025-04-23 20:36:08,135 - INFO - - 成功插入: 0
2025-04-23 20:36:08,135 - INFO - - 成功更新: 0
2025-04-23 20:36:08,135 - INFO - - 无需更新: 238
2025-04-23 20:36:08,135 - INFO - - 处理失败: 0
2025-04-23 20:36:08,306 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250423.xlsx
2025-04-23 20:36:08,307 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-23 20:36:08,307 - INFO - 正在获取宜搭表单数据...
2025-04-23 20:36:08,440 - INFO - 开始从宜搭表单获取数据，时间段: ******** 至 *************-04-23 20:36:08,441 - INFO - Request Parameters - Page 1:
2025-04-23 20:36:08,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:36:08,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:36:09,028 - INFO - Response - Page 1:
2025-04-23 20:36:09,029 - INFO - 第 1 页获取到 100 条记录
2025-04-23 20:36:09,029 - INFO - Request Parameters - Page 2:
2025-04-23 20:36:09,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:36:09,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:36:09,517 - INFO - Response - Page 2:
2025-04-23 20:36:09,517 - INFO - 第 2 页获取到 100 条记录
2025-04-23 20:36:09,518 - INFO - Request Parameters - Page 3:
2025-04-23 20:36:09,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:36:09,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:36:09,941 - INFO - Response - Page 3:
2025-04-23 20:36:09,941 - INFO - 第 3 页获取到 38 条记录
2025-04-23 20:36:09,942 - INFO - 查询完成，共获取到 238 条记录
2025-04-23 20:36:09,942 - INFO - 成功获取宜搭表单数据，共 238 条记录
2025-04-23 20:36:09,949 - INFO - 表单数据处理完成，成功处理 238 条记录
2025-04-23 20:36:09,950 - INFO - 成功获取宜搭表单数据，共 238 条记录
2025-04-23 20:36:09,950 - INFO - 正在处理宜搭数据索引...
2025-04-23 20:36:09,951 - INFO - 数据索引处理完成，共 238 条记录
2025-04-23 20:36:09,951 - INFO - 开始数据对比和同步...
2025-04-23 20:36:09,954 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-04-23 20:36:55,753 - INFO - 日志文件已创建: logs\sync_data_new_20250423.log
2025-04-23 20:36:56,123 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-23 20:36:56,124 - INFO - sales_data表已存在，无需创建
2025-04-23 20:36:56,124 - INFO - 开始数据同步流程...
2025-04-23 20:36:56,124 - INFO - 正在获取数衍平台数据...
2025-04-23 20:36:56,125 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-23 20:36:56,125 - INFO - 正在获取********至********的数据
2025-04-23 20:36:56,126 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-23 20:36:56,126 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '49A827E524C18AEB9A5A29C2CD036D50'}
2025-04-23 20:36:57,172 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-23 20:36:57,176 - INFO - 过滤后保留 238 条记录
2025-04-23 20:36:59,177 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-23 20:36:59,199 - INFO - SQLite数据保存完成，统计信息：
2025-04-23 20:36:59,199 - INFO - - 总记录数: 238
2025-04-23 20:36:59,200 - INFO - - 成功插入: 0
2025-04-23 20:36:59,200 - INFO - - 成功更新: 0
2025-04-23 20:36:59,200 - INFO - - 无需更新: 238
2025-04-23 20:36:59,201 - INFO - - 处理失败: 0
2025-04-23 20:36:59,373 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250423.xlsx
2025-04-23 20:36:59,374 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-23 20:36:59,374 - INFO - 正在获取宜搭表单数据...
2025-04-23 20:36:59,504 - INFO - 开始从宜搭表单获取数据，时间段: ******** 至 *************-04-23 20:36:59,504 - INFO - Request Parameters - Page 1:
2025-04-23 20:36:59,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:36:59,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:37:00,152 - INFO - Response - Page 1:
2025-04-23 20:37:00,153 - INFO - 第 1 页获取到 100 条记录
2025-04-23 20:37:00,154 - INFO - Request Parameters - Page 2:
2025-04-23 20:37:00,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:37:00,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:37:00,670 - INFO - Response - Page 2:
2025-04-23 20:37:00,671 - INFO - 第 2 页获取到 100 条记录
2025-04-23 20:37:00,671 - INFO - Request Parameters - Page 3:
2025-04-23 20:37:00,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-23 20:37:00,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-23 20:37:01,103 - INFO - Response - Page 3:
2025-04-23 20:37:01,103 - INFO - 第 3 页获取到 28 条记录
2025-04-23 20:37:01,104 - INFO - 查询完成，共获取到 228 条记录
2025-04-23 20:37:01,104 - INFO - 成功获取宜搭表单数据，共 228 条记录
2025-04-23 20:37:01,111 - INFO - 表单数据处理完成，成功处理 228 条记录
2025-04-23 20:37:01,111 - INFO - 成功获取宜搭表单数据，共 228 条记录
2025-04-23 20:37:01,111 - INFO - 正在处理宜搭数据索引...
2025-04-23 20:37:01,112 - INFO - 数据索引处理完成，共 228 条记录
2025-04-23 20:37:01,112 - INFO - 开始数据对比和同步...
2025-04-23 20:37:01,559 - INFO - 更新表单数据成功: FINST-F7D66UA1Z4TU4VD0C4IIBCYV3RJZ1E8AZWT9MBA
2025-04-23 20:37:01,560 - INFO - 更新记录成功，变更字段: [{'field': 'instoreCount', 'old_value': 65, 'new_value': 6}]
2025-04-23 20:37:02,071 - INFO - 更新表单数据成功: FINST-F7D66UA1Z4TU4VD0C4IIBCYV3RJZ1E8AZWT9MJA
2025-04-23 20:37:02,072 - INFO - 更新记录成功，变更字段: [{'field': 'instoreCount', 'old_value': 5655, 'new_value': 56}]
2025-04-23 20:37:02,223 - INFO - 批量插入表单数据成功，批次 1，共 10 条记录
2025-04-23 20:37:07,224 - INFO - 批量插入完成，共 10 条记录
2025-04-23 20:37:07,224 - INFO - 数据同步完成！更新: 2 条，插入: 10 条，错误: 0 条
