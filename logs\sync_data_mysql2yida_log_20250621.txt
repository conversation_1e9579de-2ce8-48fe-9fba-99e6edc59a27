2025-06-21 01:30:33,780 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 01:30:33,780 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 01:30:33,780 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 01:30:33,858 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 0 条记录
2025-06-21 01:30:33,858 - ERROR - 未获取到MySQL数据
2025-06-21 01:31:33,877 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 01:31:33,877 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 01:31:33,877 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 01:31:34,002 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 99 条记录
2025-06-21 01:31:34,002 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 01:31:34,002 - INFO - 开始处理日期: 2025-06-20
2025-06-21 01:31:34,002 - INFO - Request Parameters - Page 1:
2025-06-21 01:31:34,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 01:31:34,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 01:31:42,112 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 10895A7A-21A0-7E8D-AA2D-2CADE2E52C9C Response: {'code': 'ServiceUnavailable', 'requestid': '10895A7A-21A0-7E8D-AA2D-2CADE2E52C9C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 10895A7A-21A0-7E8D-AA2D-2CADE2E52C9C)
2025-06-21 01:31:42,112 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-21 01:31:42,112 - INFO - 同步完成
2025-06-21 04:30:33,888 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 04:30:33,888 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 04:30:33,888 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 04:30:34,013 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 3 条记录
2025-06-21 04:30:34,013 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 04:30:34,013 - INFO - 开始处理日期: 2025-06-20
2025-06-21 04:30:34,013 - INFO - Request Parameters - Page 1:
2025-06-21 04:30:34,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 04:30:34,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 04:30:42,138 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 86F79A35-2AF6-7723-8F20-1E01EA7E8DB9 Response: {'code': 'ServiceUnavailable', 'requestid': '86F79A35-2AF6-7723-8F20-1E01EA7E8DB9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 86F79A35-2AF6-7723-8F20-1E01EA7E8DB9)
2025-06-21 04:30:42,154 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-21 04:31:42,173 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 04:31:42,173 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 04:31:42,173 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 04:31:42,298 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 106 条记录
2025-06-21 04:31:42,298 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 04:31:42,298 - INFO - 开始处理日期: 2025-06-20
2025-06-21 04:31:42,298 - INFO - Request Parameters - Page 1:
2025-06-21 04:31:42,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 04:31:42,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 04:31:42,970 - INFO - Response - Page 1:
2025-06-21 04:31:42,970 - INFO - 第 1 页获取到 50 条记录
2025-06-21 04:31:43,486 - INFO - Request Parameters - Page 2:
2025-06-21 04:31:43,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 04:31:43,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 04:31:44,126 - INFO - Response - Page 2:
2025-06-21 04:31:44,126 - INFO - 第 2 页获取到 45 条记录
2025-06-21 04:31:44,626 - INFO - 查询完成，共获取到 95 条记录
2025-06-21 04:31:44,626 - INFO - 获取到 95 条表单数据
2025-06-21 04:31:44,626 - INFO - 当前日期 2025-06-20 有 104 条MySQL数据需要处理
2025-06-21 04:31:44,626 - INFO - 开始批量插入 9 条新记录
2025-06-21 04:31:44,798 - INFO - 批量插入响应状态码: 200
2025-06-21 04:31:44,798 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 20:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5F002060-945F-729D-A8D9-25AE33E12A46', 'x-acs-trace-id': '4a75e5aa3c5bf331ce00b80e9363798c', 'etag': '4CytM2I+Cz9zgU38CpL6k6A4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 04:31:44,798 - INFO - 批量插入响应体: {'result': ['FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMQ6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMR6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMS6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMT6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMU6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMV6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMW6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMX6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMY6']}
2025-06-21 04:31:44,798 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-06-21 04:31:44,798 - INFO - 成功插入的数据ID: ['FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMQ6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMR6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMS6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMT6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMU6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMV6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMW6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMX6', 'FINST-RI766091NXGWK9OWBIBIR9FDNY2Q3PAOM95CMY6']
2025-06-21 04:31:49,814 - INFO - 批量插入完成，共 9 条记录
2025-06-21 04:31:49,814 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-06-21 04:31:49,814 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-06-21 04:31:49,814 - INFO - 同步完成
2025-06-21 07:30:33,716 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 07:30:33,716 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 07:30:33,716 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 07:30:33,856 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 3 条记录
2025-06-21 07:30:33,856 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 07:30:33,856 - INFO - 开始处理日期: 2025-06-20
2025-06-21 07:30:33,856 - INFO - Request Parameters - Page 1:
2025-06-21 07:30:33,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 07:30:33,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 07:30:41,966 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 909E3D06-39C5-7F03-B82C-334A18C67414 Response: {'code': 'ServiceUnavailable', 'requestid': '909E3D06-39C5-7F03-B82C-334A18C67414', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 909E3D06-39C5-7F03-B82C-334A18C67414)
2025-06-21 07:30:41,966 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-21 07:31:41,981 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 07:31:41,981 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 07:31:41,981 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 07:31:42,106 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 106 条记录
2025-06-21 07:31:42,106 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 07:31:42,106 - INFO - 开始处理日期: 2025-06-20
2025-06-21 07:31:42,106 - INFO - Request Parameters - Page 1:
2025-06-21 07:31:42,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 07:31:42,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 07:31:42,778 - INFO - Response - Page 1:
2025-06-21 07:31:42,778 - INFO - 第 1 页获取到 50 条记录
2025-06-21 07:31:43,293 - INFO - Request Parameters - Page 2:
2025-06-21 07:31:43,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 07:31:43,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 07:31:43,965 - INFO - Response - Page 2:
2025-06-21 07:31:43,965 - INFO - 第 2 页获取到 50 条记录
2025-06-21 07:31:44,465 - INFO - Request Parameters - Page 3:
2025-06-21 07:31:44,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 07:31:44,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 07:31:44,950 - INFO - Response - Page 3:
2025-06-21 07:31:44,950 - INFO - 第 3 页获取到 4 条记录
2025-06-21 07:31:45,465 - INFO - 查询完成，共获取到 104 条记录
2025-06-21 07:31:45,465 - INFO - 获取到 104 条表单数据
2025-06-21 07:31:45,465 - INFO - 当前日期 2025-06-20 有 104 条MySQL数据需要处理
2025-06-21 07:31:45,465 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 07:31:45,465 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 07:31:45,465 - INFO - 同步完成
2025-06-21 10:30:33,596 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 10:30:33,596 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 10:30:33,596 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 10:30:33,721 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 99 条记录
2025-06-21 10:30:33,721 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-21 10:30:33,736 - INFO - 开始处理日期: 2025-06-19
2025-06-21 10:30:33,736 - INFO - Request Parameters - Page 1:
2025-06-21 10:30:33,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:30:33,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:30:39,533 - INFO - Response - Page 1:
2025-06-21 10:30:39,533 - INFO - 第 1 页获取到 50 条记录
2025-06-21 10:30:40,033 - INFO - Request Parameters - Page 2:
2025-06-21 10:30:40,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:30:40,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:30:40,674 - INFO - Response - Page 2:
2025-06-21 10:30:40,674 - INFO - 第 2 页获取到 50 条记录
2025-06-21 10:30:41,174 - INFO - Request Parameters - Page 3:
2025-06-21 10:30:41,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:30:41,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:30:49,267 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D93211F5-F76D-7CA5-B5F3-D660FA88ED1B Response: {'code': 'ServiceUnavailable', 'requestid': 'D93211F5-F76D-7CA5-B5F3-D660FA88ED1B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D93211F5-F76D-7CA5-B5F3-D660FA88ED1B)
2025-06-21 10:30:49,267 - INFO - 开始处理日期: 2025-06-20
2025-06-21 10:30:49,267 - INFO - Request Parameters - Page 1:
2025-06-21 10:30:49,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:30:49,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:30:51,064 - INFO - Response - Page 1:
2025-06-21 10:30:51,064 - INFO - 第 1 页获取到 50 条记录
2025-06-21 10:30:51,580 - INFO - Request Parameters - Page 2:
2025-06-21 10:30:51,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:30:51,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:30:52,267 - INFO - Response - Page 2:
2025-06-21 10:30:52,267 - INFO - 第 2 页获取到 50 条记录
2025-06-21 10:30:52,783 - INFO - Request Parameters - Page 3:
2025-06-21 10:30:52,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:30:52,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:30:53,267 - INFO - Response - Page 3:
2025-06-21 10:30:53,267 - INFO - 第 3 页获取到 4 条记录
2025-06-21 10:30:53,767 - INFO - 查询完成，共获取到 104 条记录
2025-06-21 10:30:53,767 - INFO - 获取到 104 条表单数据
2025-06-21 10:30:53,767 - INFO - 当前日期 2025-06-20 有 96 条MySQL数据需要处理
2025-06-21 10:30:53,767 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666711XEW95O3DELTB59NTDDE3R3JGD4CMYH
2025-06-21 10:30:54,221 - INFO - 更新表单数据成功: FINST-PPA666711XEW95O3DELTB59NTDDE3R3JGD4CMYH
2025-06-21 10:30:54,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3403.9, 'new_value': 4277.21}, {'field': 'total_amount', 'old_value': 10083.66, 'new_value': 10956.97}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/ae6d8fa65ae94c78b2d76e1eb0d9be54.png?Expires=2060149888&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=d1C%2F1%2BOydrgHSGTGvzqMI683te0%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/161269683d6942549bd1c780c6dc109e.jpg?Expires=2060151182&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=XNqZ0zoEu%2Fj%2BW%2FqPhIkR7YOxqhg%3D'}]
2025-06-21 10:30:54,221 - INFO - 开始批量插入 92 条新记录
2025-06-21 10:30:54,455 - INFO - 批量插入响应状态码: 200
2025-06-21 10:30:54,455 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 02:30:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2F976FA2-6682-7AFB-A00E-9D9188A1816C', 'x-acs-trace-id': '430caea530c1a8b4719b92a52bd3ba61', 'etag': '26GIeBclDOrAvQtKcWkvgjA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 10:30:54,455 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMN5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMO5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMP5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMQ5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMR5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMS5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMT5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMU5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMV5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMW5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMX5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMY5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMZ5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM06', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM16', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM26', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM36', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM46', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM56', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM66', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM76', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM86', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM96', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMA6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMB6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMC6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMD6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CME6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMF6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMG6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMH6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMI6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMJ6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMK6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CML6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMM6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMN6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMO6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMP6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMQ6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMR6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMS6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMT6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMU6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMV6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMW6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMX6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMY6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMZ6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM07']}
2025-06-21 10:30:54,455 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-21 10:30:54,455 - INFO - 成功插入的数据ID: ['FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMN5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMO5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMP5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMQ5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMR5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMS5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMT5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMU5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMV5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMW5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMX5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMY5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMZ5', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM06', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM16', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM26', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM36', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM46', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM56', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM66', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM76', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM86', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM96', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMA6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMB6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMC6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMD6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CME6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMF6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMG6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMH6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMI6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMJ6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMK6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CML6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMM6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMN6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMO6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMP6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMQ6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMR6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMS6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMT6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMU6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMV6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMW6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMX6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMY6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CMZ6', 'FINST-8PF66V71OWGWZUIECDU15A1XNILH2PGHGM5CM07']
2025-06-21 10:30:59,689 - INFO - 批量插入响应状态码: 200
2025-06-21 10:30:59,689 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 02:30:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1998', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '32A34744-72C4-7F87-A961-03596008B942', 'x-acs-trace-id': 'be7edcbdfec8e93c598f43349f31e875', 'etag': '1E8+bbkqtrggVbMRWUGBApA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 10:30:59,689 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM6', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM7', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM8', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM9', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMA', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMB', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMC', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMD', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CME', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMF', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMG', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMH', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMI', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMJ', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMK', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CML', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMM', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMN', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMO', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMP', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMQ', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMR', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMS', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMT', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMU', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMV', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMW', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMX', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMY', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMZ', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM01', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM11', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM21', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM31', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM41', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM51', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM61', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM71', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM81', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM91', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMA1', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMB1']}
2025-06-21 10:30:59,689 - INFO - 批量插入表单数据成功，批次 2，共 42 条记录
2025-06-21 10:30:59,689 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM6', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM7', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM8', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM9', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMA', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMB', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMC', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMD', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CME', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMF', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMG', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMH', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMI', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMJ', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMK', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CML', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMM', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMN', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMO', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMP', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMQ', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMR', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMS', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMT', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMU', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMV', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMW', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMX', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMY', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMZ', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM01', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM11', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM21', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM31', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM41', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM51', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM61', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM71', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM81', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CM91', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMA1', 'FINST-E3G66QA1GXGW2JO7FE1M6AYHB5752CILGM5CMB1']
2025-06-21 10:31:04,705 - INFO - 批量插入完成，共 92 条记录
2025-06-21 10:31:04,705 - INFO - 日期 2025-06-20 处理完成 - 更新: 1 条，插入: 92 条，错误: 0 条
2025-06-21 10:31:04,705 - INFO - 数据同步完成！更新: 1 条，插入: 92 条，错误: 1 条
2025-06-21 10:32:04,720 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 10:32:04,720 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 10:32:04,720 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 10:32:04,861 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 364 条记录
2025-06-21 10:32:04,861 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 10:32:04,861 - INFO - 开始处理日期: 2025-06-20
2025-06-21 10:32:04,861 - INFO - Request Parameters - Page 1:
2025-06-21 10:32:04,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:32:04,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:32:05,533 - INFO - Response - Page 1:
2025-06-21 10:32:05,533 - INFO - 第 1 页获取到 50 条记录
2025-06-21 10:32:06,048 - INFO - Request Parameters - Page 2:
2025-06-21 10:32:06,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:32:06,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:32:06,673 - INFO - Response - Page 2:
2025-06-21 10:32:06,689 - INFO - 第 2 页获取到 50 条记录
2025-06-21 10:32:07,204 - INFO - Request Parameters - Page 3:
2025-06-21 10:32:07,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:32:07,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:32:07,861 - INFO - Response - Page 3:
2025-06-21 10:32:07,861 - INFO - 第 3 页获取到 50 条记录
2025-06-21 10:32:08,376 - INFO - Request Parameters - Page 4:
2025-06-21 10:32:08,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 10:32:08,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 10:32:09,079 - INFO - Response - Page 4:
2025-06-21 10:32:09,079 - INFO - 第 4 页获取到 46 条记录
2025-06-21 10:32:09,595 - INFO - 查询完成，共获取到 196 条记录
2025-06-21 10:32:09,595 - INFO - 获取到 196 条表单数据
2025-06-21 10:32:09,595 - INFO - 当前日期 2025-06-20 有 358 条MySQL数据需要处理
2025-06-21 10:32:09,595 - INFO - 开始批量插入 162 条新记录
2025-06-21 10:32:09,845 - INFO - 批量插入响应状态码: 200
2025-06-21 10:32:09,845 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 02:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2397', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5940E8B4-51BD-723E-B4AC-A193E0B69E9B', 'x-acs-trace-id': '72d255978a051f3f565d85544a1a99f7', 'etag': '2uh5maZkX4u2ANa0pEMclfw7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 10:32:09,845 - INFO - 批量插入响应体: {'result': ['FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CML', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMM', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMN', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMO', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMP', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMQ', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMR', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMS', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMT', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMU', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMV', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMW', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMX', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMY', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMZ', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM01', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM11', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM21', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM31', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM41', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM51', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM61', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM71', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM81', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM91', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMA1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMB1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMC1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMD1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CME1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMF1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMG1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMH1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMI1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMJ1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMK1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CML1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMM1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMN1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMO1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMP1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMQ1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMR1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMS1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMT1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMU1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMV1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMW1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMX1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMY1']}
2025-06-21 10:32:09,845 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-21 10:32:09,845 - INFO - 成功插入的数据ID: ['FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CML', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMM', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMN', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMO', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMP', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMQ', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMR', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMS', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMT', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMU', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMV', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMW', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMX', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMY', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMZ', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM01', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM11', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM21', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM31', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM41', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM51', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM61', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM71', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM81', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CM91', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMA1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMB1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMC1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMD1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CME1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMF1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMG1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMH1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMI1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMJ1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMK1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CML1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMM1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMN1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMO1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMP1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMQ1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMR1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMS1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMT1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMU1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMV1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMW1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMX1', 'FINST-3RE66ZB1PTGWBA2GCYAIVA913M2Q2VM3IM5CMY1']
2025-06-21 10:32:15,095 - INFO - 批量插入响应状态码: 200
2025-06-21 10:32:15,095 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 02:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '16293DA1-78B3-7449-AF39-3DE48DB2D182', 'x-acs-trace-id': '8bf338be7dfea737587df2691e3bae67', 'etag': '2h9ar2D5Q5HOVHoFOLQBE9A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 10:32:15,095 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM3E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM4E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM5E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM6E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM7E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM8E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM9E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMAE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMBE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMCE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMDE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMEE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMFE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMGE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMHE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMIE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMJE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMKE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMLE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMME', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMNE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMOE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMPE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMQE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMRE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMSE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMTE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMUE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMVE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMWE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMXE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMYE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMZE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM0F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM1F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM2F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM3F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM4F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM5F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM6F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM7F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM8F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM9F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMAF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMBF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMCF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMDF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMEF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMFF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMGF']}
2025-06-21 10:32:15,095 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-21 10:32:15,095 - INFO - 成功插入的数据ID: ['FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM3E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM4E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM5E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM6E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM7E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM8E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM9E', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMAE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMBE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMCE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMDE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMEE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMFE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMGE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMHE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMIE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMJE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMKE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMLE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMME', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMNE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMOE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMPE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMQE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMRE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMSE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMTE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMUE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMVE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMWE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMXE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMYE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMZE', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM0F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM1F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM2F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM3F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM4F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM5F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM6F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM7F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM8F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CM9F', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMAF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMBF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMCF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMDF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMEF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMFF', 'FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMGF']
2025-06-21 10:32:20,361 - INFO - 批量插入响应状态码: 200
2025-06-21 10:32:20,361 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 02:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2397', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B8BA2CDF-2DEB-76AE-BDB6-8756DDD57CB8', 'x-acs-trace-id': '803e69adde0b865f5551b255ee4d0ebf', 'etag': '2G+CqzjwAt/FKn5vr9pE/sA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 10:32:20,361 - INFO - 批量插入响应体: {'result': ['FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CML', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMM', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMN', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMO', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMP', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMQ', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMR', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMS', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMT', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMU', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMV', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMW', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMX', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMY', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMZ', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM01', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM11', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM21', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM31', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM41', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM51', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM61', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM71', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM81', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM91', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMA1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMB1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMC1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMD1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CME1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMF1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMG1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMH1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMI1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMJ1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMK1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CML1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMM1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMN1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMO1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMP1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMQ1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMR1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMS1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMT1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMU1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMV1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMW1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMX1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMY1']}
2025-06-21 10:32:20,361 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-21 10:32:20,361 - INFO - 成功插入的数据ID: ['FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CML', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMM', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMN', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMO', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMP', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMQ', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMR', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMS', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMT', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMU', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMV', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMW', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMX', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMY', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMZ', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM01', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM11', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM21', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM31', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM41', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM51', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM61', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM71', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM81', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CM91', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMA1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMB1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMC1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMD1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CME1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMF1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMG1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMH1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMI1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMJ1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMK1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CML1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMM1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMN1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX21RBIM5CMO1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMP1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMQ1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMR1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMS1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMT1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMU1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMV1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMW1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMX1', 'FINST-XL666BD17XGWR7T4C1FOV7RGMVLX22RBIM5CMY1']
2025-06-21 10:32:25,611 - INFO - 批量插入响应状态码: 200
2025-06-21 10:32:25,611 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 02:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '588', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '90D4FC13-E315-79B8-A91D-66F25A050E4B', 'x-acs-trace-id': '9146976fb6bbea405260b869ca00de35', 'etag': '5r0yqgxuOi8QE59BaeD+fTA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 10:32:25,611 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMQM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMRM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMSM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMTM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMUM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMVM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMWM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMXM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMYM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMZM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CM0N', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CM1N']}
2025-06-21 10:32:25,611 - INFO - 批量插入表单数据成功，批次 4，共 12 条记录
2025-06-21 10:32:25,611 - INFO - 成功插入的数据ID: ['FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMQM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMRM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMSM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMTM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMUM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMVM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMWM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMXM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMYM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CMZM', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CM0N', 'FINST-AEF66BC1VXGW27KKBB8J68W30KIT2VSFIM5CM1N']
2025-06-21 10:32:30,626 - INFO - 批量插入完成，共 162 条记录
2025-06-21 10:32:30,626 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 162 条，错误: 0 条
2025-06-21 10:32:30,626 - INFO - 数据同步完成！更新: 0 条，插入: 162 条，错误: 0 条
2025-06-21 10:32:30,626 - INFO - 同步完成
2025-06-21 13:30:33,756 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 13:30:33,756 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 13:30:33,756 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 13:30:33,881 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 147 条记录
2025-06-21 13:30:33,881 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-21 13:30:33,881 - INFO - 开始处理日期: 2025-06-19
2025-06-21 13:30:33,897 - INFO - Request Parameters - Page 1:
2025-06-21 13:30:33,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:33,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:42,084 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6394BA12-55EF-7C13-822D-8F36A870F558 Response: {'code': 'ServiceUnavailable', 'requestid': '6394BA12-55EF-7C13-822D-8F36A870F558', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6394BA12-55EF-7C13-822D-8F36A870F558)
2025-06-21 13:30:42,084 - INFO - 开始处理日期: 2025-06-20
2025-06-21 13:30:42,084 - INFO - Request Parameters - Page 1:
2025-06-21 13:30:42,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:42,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:42,803 - INFO - Response - Page 1:
2025-06-21 13:30:42,803 - INFO - 第 1 页获取到 50 条记录
2025-06-21 13:30:43,303 - INFO - Request Parameters - Page 2:
2025-06-21 13:30:43,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:43,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:49,022 - INFO - Response - Page 2:
2025-06-21 13:30:49,022 - INFO - 第 2 页获取到 50 条记录
2025-06-21 13:30:49,522 - INFO - Request Parameters - Page 3:
2025-06-21 13:30:49,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:49,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:50,193 - INFO - Response - Page 3:
2025-06-21 13:30:50,193 - INFO - 第 3 页获取到 50 条记录
2025-06-21 13:30:50,709 - INFO - Request Parameters - Page 4:
2025-06-21 13:30:50,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:50,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:51,365 - INFO - Response - Page 4:
2025-06-21 13:30:51,365 - INFO - 第 4 页获取到 50 条记录
2025-06-21 13:30:51,881 - INFO - Request Parameters - Page 5:
2025-06-21 13:30:51,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:51,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:52,553 - INFO - Response - Page 5:
2025-06-21 13:30:52,553 - INFO - 第 5 页获取到 50 条记录
2025-06-21 13:30:53,053 - INFO - Request Parameters - Page 6:
2025-06-21 13:30:53,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:53,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:53,709 - INFO - Response - Page 6:
2025-06-21 13:30:53,709 - INFO - 第 6 页获取到 50 条记录
2025-06-21 13:30:54,225 - INFO - Request Parameters - Page 7:
2025-06-21 13:30:54,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:54,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:54,912 - INFO - Response - Page 7:
2025-06-21 13:30:54,912 - INFO - 第 7 页获取到 50 条记录
2025-06-21 13:30:55,428 - INFO - Request Parameters - Page 8:
2025-06-21 13:30:55,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:30:55,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:30:55,928 - INFO - Response - Page 8:
2025-06-21 13:30:55,928 - INFO - 第 8 页获取到 8 条记录
2025-06-21 13:30:56,443 - INFO - 查询完成，共获取到 358 条记录
2025-06-21 13:30:56,443 - INFO - 获取到 358 条表单数据
2025-06-21 13:30:56,443 - INFO - 当前日期 2025-06-20 有 144 条MySQL数据需要处理
2025-06-21 13:30:56,443 - INFO - 开始批量插入 48 条新记录
2025-06-21 13:30:56,709 - INFO - 批量插入响应状态码: 200
2025-06-21 13:30:56,709 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 05:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2316', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CDBD9161-E850-7275-A4C8-66B8A38EFF7F', 'x-acs-trace-id': '20f8520162dbfbd4819ac123665433f1', 'etag': '26X5foAwx1DJV5D8bXZuaZQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 13:30:56,709 - INFO - 批量插入响应体: {'result': ['FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CM81', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CM91', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMA1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMB1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMC1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMD1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CME1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMF1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMG1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMH1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMI1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMJ1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMK1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CML1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMM1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMN1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMO1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMP1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMQ1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMR1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMS1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMT1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMU1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMV1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMW1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMX1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMY1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMZ1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM02', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM12', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM22', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM32', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM42', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM52', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM62', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM72', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM82', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM92', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMA2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMB2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMC2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMD2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CME2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMF2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMG2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMH2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMI2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMJ2']}
2025-06-21 13:30:56,709 - INFO - 批量插入表单数据成功，批次 1，共 48 条记录
2025-06-21 13:30:56,709 - INFO - 成功插入的数据ID: ['FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CM81', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CM91', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMA1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMB1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMC1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMD1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CME1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMF1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMG1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMH1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMI1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMJ1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CMK1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS29L0WS5CML1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMM1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMN1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMO1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMP1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMQ1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMR1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMS1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMT1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMU1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMV1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMW1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMX1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMY1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMZ1', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM02', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM12', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM22', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM32', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM42', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM52', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM62', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM72', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM82', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CM92', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMA2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMB2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMC2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMD2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CME2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMF2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMG2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMH2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMI2', 'FINST-NS766991BXGWNPP767L4WAWN2XKS2AL0WS5CMJ2']
2025-06-21 13:31:01,725 - INFO - 批量插入完成，共 48 条记录
2025-06-21 13:31:01,725 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 48 条，错误: 0 条
2025-06-21 13:31:01,725 - INFO - 数据同步完成！更新: 0 条，插入: 48 条，错误: 1 条
2025-06-21 13:32:01,740 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 13:32:01,740 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 13:32:01,740 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 13:32:01,880 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 463 条记录
2025-06-21 13:32:01,880 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 13:32:01,880 - INFO - 开始处理日期: 2025-06-20
2025-06-21 13:32:01,880 - INFO - Request Parameters - Page 1:
2025-06-21 13:32:01,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:01,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:02,599 - INFO - Response - Page 1:
2025-06-21 13:32:02,599 - INFO - 第 1 页获取到 50 条记录
2025-06-21 13:32:03,115 - INFO - Request Parameters - Page 2:
2025-06-21 13:32:03,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:03,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:03,802 - INFO - Response - Page 2:
2025-06-21 13:32:03,802 - INFO - 第 2 页获取到 50 条记录
2025-06-21 13:32:04,302 - INFO - Request Parameters - Page 3:
2025-06-21 13:32:04,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:04,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:04,974 - INFO - Response - Page 3:
2025-06-21 13:32:04,974 - INFO - 第 3 页获取到 50 条记录
2025-06-21 13:32:05,474 - INFO - Request Parameters - Page 4:
2025-06-21 13:32:05,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:05,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:06,146 - INFO - Response - Page 4:
2025-06-21 13:32:06,146 - INFO - 第 4 页获取到 50 条记录
2025-06-21 13:32:06,646 - INFO - Request Parameters - Page 5:
2025-06-21 13:32:06,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:06,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:07,349 - INFO - Response - Page 5:
2025-06-21 13:32:07,349 - INFO - 第 5 页获取到 50 条记录
2025-06-21 13:32:07,849 - INFO - Request Parameters - Page 6:
2025-06-21 13:32:07,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:07,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:08,537 - INFO - Response - Page 6:
2025-06-21 13:32:08,537 - INFO - 第 6 页获取到 50 条记录
2025-06-21 13:32:09,037 - INFO - Request Parameters - Page 7:
2025-06-21 13:32:09,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:09,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:09,693 - INFO - Response - Page 7:
2025-06-21 13:32:09,693 - INFO - 第 7 页获取到 50 条记录
2025-06-21 13:32:10,193 - INFO - Request Parameters - Page 8:
2025-06-21 13:32:10,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:10,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:10,880 - INFO - Response - Page 8:
2025-06-21 13:32:10,880 - INFO - 第 8 页获取到 50 条记录
2025-06-21 13:32:11,396 - INFO - Request Parameters - Page 9:
2025-06-21 13:32:11,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 13:32:11,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 13:32:11,865 - INFO - Response - Page 9:
2025-06-21 13:32:11,865 - INFO - 第 9 页获取到 6 条记录
2025-06-21 13:32:12,380 - INFO - 查询完成，共获取到 406 条记录
2025-06-21 13:32:12,380 - INFO - 获取到 406 条表单数据
2025-06-21 13:32:12,380 - INFO - 当前日期 2025-06-20 有 457 条MySQL数据需要处理
2025-06-21 13:32:12,396 - INFO - 开始批量插入 51 条新记录
2025-06-21 13:32:12,708 - INFO - 批量插入响应状态码: 200
2025-06-21 13:32:12,708 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 05:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '35896A4D-2F47-796B-9254-E58928F3B4D4', 'x-acs-trace-id': 'ad33919f7d3506c36769e222e793075b', 'etag': '2O9e7IOPVItUTUW4pXB6o9g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 13:32:12,708 - INFO - 批量插入响应体: {'result': ['FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMI3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMJ3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMK3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CML3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMM3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMN3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMO3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMP3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMQ3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMR3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMS3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMT3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMU3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMV3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMW3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMX3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMY3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMZ3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM04', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM14', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM24', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM34', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM44', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM54', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM64', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM74', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM84', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM94', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMA4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMB4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMC4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMD4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CME4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMF4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMG4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMH4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMI4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMJ4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMK4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CML4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMM4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMN4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMO4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMP4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMQ4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMR4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMS4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMT4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMU4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMV4']}
2025-06-21 13:32:12,708 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-21 13:32:12,708 - INFO - 成功插入的数据ID: ['FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMI3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMJ3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMK3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CML3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMM3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMN3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3T8NXS5CMO3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMP3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMQ3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMR3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMS3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMT3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMU3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMV3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMW3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMX3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMY3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMZ3', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM04', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM14', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM24', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM34', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM44', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM54', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM64', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM74', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM84', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CM94', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMA4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMB4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMC4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMD4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CME4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMF4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMG4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMH4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMI4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMJ4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMK4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CML4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMM4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMN4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMO4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMP4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMQ4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMR4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMS4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMT4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMU4', 'FINST-49866E71JXGWR5JCA2CM14K4STRB3U8NXS5CMV4']
2025-06-21 13:32:17,880 - INFO - 批量插入响应状态码: 200
2025-06-21 13:32:17,880 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 05:32:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '56DF1936-4F8C-73CB-BD72-321F5503C877', 'x-acs-trace-id': '8c981d29f417a6edb4962c936832f5bd', 'etag': '6fLL3esx+LF4Z4BPqtywNYA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 13:32:17,880 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71PXGW5OMJFGQBR60SKBBV388RXS5CMO6']}
2025-06-21 13:32:17,880 - INFO - 批量插入表单数据成功，批次 2，共 1 条记录
2025-06-21 13:32:17,880 - INFO - 成功插入的数据ID: ['FINST-00D66K71PXGW5OMJFGQBR60SKBBV388RXS5CMO6']
2025-06-21 13:32:22,896 - INFO - 批量插入完成，共 51 条记录
2025-06-21 13:32:22,896 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 51 条，错误: 0 条
2025-06-21 13:32:22,896 - INFO - 数据同步完成！更新: 0 条，插入: 51 条，错误: 0 条
2025-06-21 13:32:22,896 - INFO - 同步完成
2025-06-21 16:30:33,573 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 16:30:33,573 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 16:30:33,573 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 16:30:33,714 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 147 条记录
2025-06-21 16:30:33,714 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-21 16:30:33,714 - INFO - 开始处理日期: 2025-06-19
2025-06-21 16:30:33,714 - INFO - Request Parameters - Page 1:
2025-06-21 16:30:33,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:33,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:41,839 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 53830D18-4C21-73AD-A15A-20AAF2E5B5A9 Response: {'code': 'ServiceUnavailable', 'requestid': '53830D18-4C21-73AD-A15A-20AAF2E5B5A9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 53830D18-4C21-73AD-A15A-20AAF2E5B5A9)
2025-06-21 16:30:41,839 - INFO - 开始处理日期: 2025-06-20
2025-06-21 16:30:41,839 - INFO - Request Parameters - Page 1:
2025-06-21 16:30:41,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:41,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:48,510 - INFO - Response - Page 1:
2025-06-21 16:30:48,510 - INFO - 第 1 页获取到 50 条记录
2025-06-21 16:30:49,010 - INFO - Request Parameters - Page 2:
2025-06-21 16:30:49,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:49,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:49,698 - INFO - Response - Page 2:
2025-06-21 16:30:49,698 - INFO - 第 2 页获取到 50 条记录
2025-06-21 16:30:50,198 - INFO - Request Parameters - Page 3:
2025-06-21 16:30:50,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:50,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:50,885 - INFO - Response - Page 3:
2025-06-21 16:30:50,885 - INFO - 第 3 页获取到 50 条记录
2025-06-21 16:30:51,401 - INFO - Request Parameters - Page 4:
2025-06-21 16:30:51,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:51,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:52,135 - INFO - Response - Page 4:
2025-06-21 16:30:52,135 - INFO - 第 4 页获取到 50 条记录
2025-06-21 16:30:52,651 - INFO - Request Parameters - Page 5:
2025-06-21 16:30:52,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:52,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:53,307 - INFO - Response - Page 5:
2025-06-21 16:30:53,307 - INFO - 第 5 页获取到 50 条记录
2025-06-21 16:30:53,807 - INFO - Request Parameters - Page 6:
2025-06-21 16:30:53,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:53,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:54,495 - INFO - Response - Page 6:
2025-06-21 16:30:54,495 - INFO - 第 6 页获取到 50 条记录
2025-06-21 16:30:55,010 - INFO - Request Parameters - Page 7:
2025-06-21 16:30:55,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:55,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:55,698 - INFO - Response - Page 7:
2025-06-21 16:30:55,698 - INFO - 第 7 页获取到 50 条记录
2025-06-21 16:30:56,213 - INFO - Request Parameters - Page 8:
2025-06-21 16:30:56,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:56,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:56,963 - INFO - Response - Page 8:
2025-06-21 16:30:56,963 - INFO - 第 8 页获取到 50 条记录
2025-06-21 16:30:57,479 - INFO - Request Parameters - Page 9:
2025-06-21 16:30:57,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:57,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:58,151 - INFO - Response - Page 9:
2025-06-21 16:30:58,151 - INFO - 第 9 页获取到 50 条记录
2025-06-21 16:30:58,666 - INFO - Request Parameters - Page 10:
2025-06-21 16:30:58,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:30:58,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:30:59,151 - INFO - Response - Page 10:
2025-06-21 16:30:59,151 - INFO - 第 10 页获取到 7 条记录
2025-06-21 16:30:59,667 - INFO - 查询完成，共获取到 457 条记录
2025-06-21 16:30:59,667 - INFO - 获取到 457 条表单数据
2025-06-21 16:30:59,667 - INFO - 当前日期 2025-06-20 有 144 条MySQL数据需要处理
2025-06-21 16:30:59,667 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 16:30:59,667 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-21 16:31:59,682 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 16:31:59,682 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 16:31:59,682 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 16:31:59,822 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 463 条记录
2025-06-21 16:31:59,822 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 16:31:59,822 - INFO - 开始处理日期: 2025-06-20
2025-06-21 16:31:59,822 - INFO - Request Parameters - Page 1:
2025-06-21 16:31:59,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:31:59,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:00,494 - INFO - Response - Page 1:
2025-06-21 16:32:00,494 - INFO - 第 1 页获取到 50 条记录
2025-06-21 16:32:01,010 - INFO - Request Parameters - Page 2:
2025-06-21 16:32:01,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:01,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:01,619 - INFO - Response - Page 2:
2025-06-21 16:32:01,619 - INFO - 第 2 页获取到 50 条记录
2025-06-21 16:32:02,119 - INFO - Request Parameters - Page 3:
2025-06-21 16:32:02,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:02,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:02,760 - INFO - Response - Page 3:
2025-06-21 16:32:02,760 - INFO - 第 3 页获取到 50 条记录
2025-06-21 16:32:03,275 - INFO - Request Parameters - Page 4:
2025-06-21 16:32:03,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:03,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:03,979 - INFO - Response - Page 4:
2025-06-21 16:32:03,979 - INFO - 第 4 页获取到 50 条记录
2025-06-21 16:32:04,494 - INFO - Request Parameters - Page 5:
2025-06-21 16:32:04,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:04,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:05,197 - INFO - Response - Page 5:
2025-06-21 16:32:05,197 - INFO - 第 5 页获取到 50 条记录
2025-06-21 16:32:05,697 - INFO - Request Parameters - Page 6:
2025-06-21 16:32:05,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:05,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:06,322 - INFO - Response - Page 6:
2025-06-21 16:32:06,322 - INFO - 第 6 页获取到 50 条记录
2025-06-21 16:32:06,838 - INFO - Request Parameters - Page 7:
2025-06-21 16:32:06,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:06,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:07,525 - INFO - Response - Page 7:
2025-06-21 16:32:07,525 - INFO - 第 7 页获取到 50 条记录
2025-06-21 16:32:08,025 - INFO - Request Parameters - Page 8:
2025-06-21 16:32:08,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:08,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:08,635 - INFO - Response - Page 8:
2025-06-21 16:32:08,635 - INFO - 第 8 页获取到 50 条记录
2025-06-21 16:32:09,150 - INFO - Request Parameters - Page 9:
2025-06-21 16:32:09,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:09,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:09,885 - INFO - Response - Page 9:
2025-06-21 16:32:09,885 - INFO - 第 9 页获取到 50 条记录
2025-06-21 16:32:10,385 - INFO - Request Parameters - Page 10:
2025-06-21 16:32:10,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 16:32:10,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 16:32:10,869 - INFO - Response - Page 10:
2025-06-21 16:32:10,869 - INFO - 第 10 页获取到 7 条记录
2025-06-21 16:32:11,385 - INFO - 查询完成，共获取到 457 条记录
2025-06-21 16:32:11,385 - INFO - 获取到 457 条表单数据
2025-06-21 16:32:11,385 - INFO - 当前日期 2025-06-20 有 457 条MySQL数据需要处理
2025-06-21 16:32:11,400 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 16:32:11,400 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 16:32:11,400 - INFO - 同步完成
2025-06-21 19:30:33,864 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 19:30:33,864 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 19:30:33,864 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 19:30:34,005 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 156 条记录
2025-06-21 19:30:34,005 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-21 19:30:34,005 - INFO - 开始处理日期: 2025-06-19
2025-06-21 19:30:34,005 - INFO - Request Parameters - Page 1:
2025-06-21 19:30:34,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:34,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:42,131 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A624571C-7B3A-75E0-A082-D11249699639 Response: {'code': 'ServiceUnavailable', 'requestid': 'A624571C-7B3A-75E0-A082-D11249699639', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A624571C-7B3A-75E0-A082-D11249699639)
2025-06-21 19:30:42,131 - INFO - 开始处理日期: 2025-06-20
2025-06-21 19:30:42,131 - INFO - Request Parameters - Page 1:
2025-06-21 19:30:42,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:42,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:47,694 - INFO - Response - Page 1:
2025-06-21 19:30:47,694 - INFO - 第 1 页获取到 50 条记录
2025-06-21 19:30:48,210 - INFO - Request Parameters - Page 2:
2025-06-21 19:30:48,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:48,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:48,882 - INFO - Response - Page 2:
2025-06-21 19:30:48,882 - INFO - 第 2 页获取到 50 条记录
2025-06-21 19:30:49,382 - INFO - Request Parameters - Page 3:
2025-06-21 19:30:49,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:49,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:50,039 - INFO - Response - Page 3:
2025-06-21 19:30:50,039 - INFO - 第 3 页获取到 50 条记录
2025-06-21 19:30:50,539 - INFO - Request Parameters - Page 4:
2025-06-21 19:30:50,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:50,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:51,211 - INFO - Response - Page 4:
2025-06-21 19:30:51,211 - INFO - 第 4 页获取到 50 条记录
2025-06-21 19:30:51,711 - INFO - Request Parameters - Page 5:
2025-06-21 19:30:51,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:51,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:52,351 - INFO - Response - Page 5:
2025-06-21 19:30:52,351 - INFO - 第 5 页获取到 50 条记录
2025-06-21 19:30:52,852 - INFO - Request Parameters - Page 6:
2025-06-21 19:30:52,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:52,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:53,539 - INFO - Response - Page 6:
2025-06-21 19:30:53,539 - INFO - 第 6 页获取到 50 条记录
2025-06-21 19:30:54,055 - INFO - Request Parameters - Page 7:
2025-06-21 19:30:54,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:54,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:54,696 - INFO - Response - Page 7:
2025-06-21 19:30:54,696 - INFO - 第 7 页获取到 50 条记录
2025-06-21 19:30:55,211 - INFO - Request Parameters - Page 8:
2025-06-21 19:30:55,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:55,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:55,868 - INFO - Response - Page 8:
2025-06-21 19:30:55,868 - INFO - 第 8 页获取到 50 条记录
2025-06-21 19:30:56,368 - INFO - Request Parameters - Page 9:
2025-06-21 19:30:56,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:56,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:56,993 - INFO - Response - Page 9:
2025-06-21 19:30:56,993 - INFO - 第 9 页获取到 50 条记录
2025-06-21 19:30:57,493 - INFO - Request Parameters - Page 10:
2025-06-21 19:30:57,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:30:57,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:30:57,946 - INFO - Response - Page 10:
2025-06-21 19:30:57,946 - INFO - 第 10 页获取到 7 条记录
2025-06-21 19:30:58,462 - INFO - 查询完成，共获取到 457 条记录
2025-06-21 19:30:58,462 - INFO - 获取到 457 条表单数据
2025-06-21 19:30:58,462 - INFO - 当前日期 2025-06-20 有 153 条MySQL数据需要处理
2025-06-21 19:30:58,462 - INFO - 开始批量插入 9 条新记录
2025-06-21 19:30:58,618 - INFO - 批量插入响应状态码: 200
2025-06-21 19:30:58,618 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 11:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '435', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '12CD4582-53E1-7528-9215-06A9ECCB745B', 'x-acs-trace-id': '2726d8eaa83830087c7651742729d47b', 'etag': '4KutmVOoZYqT5unCYwIQMog5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 19:30:58,618 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM5', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM6', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM7', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM8', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM9', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMA', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMB', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMC', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMD']}
2025-06-21 19:30:58,634 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-06-21 19:30:58,634 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM5', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM6', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM7', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM8', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CM9', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMA', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMB', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMC', 'FINST-OIF66BA1VZHW1Y05A1PUDBFRKPB72194R56CMD']
2025-06-21 19:31:03,650 - INFO - 批量插入完成，共 9 条记录
2025-06-21 19:31:03,650 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-06-21 19:31:03,650 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 1 条
2025-06-21 19:32:03,677 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 19:32:03,677 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 19:32:03,677 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 19:32:03,817 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 472 条记录
2025-06-21 19:32:03,817 - INFO - 获取到 1 个日期需要处理: ['2025-06-20']
2025-06-21 19:32:03,817 - INFO - 开始处理日期: 2025-06-20
2025-06-21 19:32:03,817 - INFO - Request Parameters - Page 1:
2025-06-21 19:32:03,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:03,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:04,630 - INFO - Response - Page 1:
2025-06-21 19:32:04,630 - INFO - 第 1 页获取到 50 条记录
2025-06-21 19:32:05,130 - INFO - Request Parameters - Page 2:
2025-06-21 19:32:05,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:05,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:05,849 - INFO - Response - Page 2:
2025-06-21 19:32:05,849 - INFO - 第 2 页获取到 50 条记录
2025-06-21 19:32:06,365 - INFO - Request Parameters - Page 3:
2025-06-21 19:32:06,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:06,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:07,021 - INFO - Response - Page 3:
2025-06-21 19:32:07,021 - INFO - 第 3 页获取到 50 条记录
2025-06-21 19:32:07,537 - INFO - Request Parameters - Page 4:
2025-06-21 19:32:07,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:07,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:08,146 - INFO - Response - Page 4:
2025-06-21 19:32:08,146 - INFO - 第 4 页获取到 50 条记录
2025-06-21 19:32:08,662 - INFO - Request Parameters - Page 5:
2025-06-21 19:32:08,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:08,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:09,334 - INFO - Response - Page 5:
2025-06-21 19:32:09,334 - INFO - 第 5 页获取到 50 条记录
2025-06-21 19:32:09,834 - INFO - Request Parameters - Page 6:
2025-06-21 19:32:09,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:09,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:10,522 - INFO - Response - Page 6:
2025-06-21 19:32:10,522 - INFO - 第 6 页获取到 50 条记录
2025-06-21 19:32:11,037 - INFO - Request Parameters - Page 7:
2025-06-21 19:32:11,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:11,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:11,725 - INFO - Response - Page 7:
2025-06-21 19:32:11,725 - INFO - 第 7 页获取到 50 条记录
2025-06-21 19:32:12,241 - INFO - Request Parameters - Page 8:
2025-06-21 19:32:12,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:12,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:12,819 - INFO - Response - Page 8:
2025-06-21 19:32:12,819 - INFO - 第 8 页获取到 50 条记录
2025-06-21 19:32:13,319 - INFO - Request Parameters - Page 9:
2025-06-21 19:32:13,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:13,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:13,991 - INFO - Response - Page 9:
2025-06-21 19:32:13,991 - INFO - 第 9 页获取到 50 条记录
2025-06-21 19:32:14,491 - INFO - Request Parameters - Page 10:
2025-06-21 19:32:14,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 19:32:14,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 19:32:15,054 - INFO - Response - Page 10:
2025-06-21 19:32:15,054 - INFO - 第 10 页获取到 16 条记录
2025-06-21 19:32:15,554 - INFO - 查询完成，共获取到 466 条记录
2025-06-21 19:32:15,554 - INFO - 获取到 466 条表单数据
2025-06-21 19:32:15,554 - INFO - 当前日期 2025-06-20 有 466 条MySQL数据需要处理
2025-06-21 19:32:15,569 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 19:32:15,569 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 19:32:15,569 - INFO - 同步完成
2025-06-21 22:30:34,325 - INFO - 使用默认增量同步（当天更新数据）
2025-06-21 22:30:34,325 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-21 22:30:34,325 - INFO - 查询参数: ('2025-06-21',)
2025-06-21 22:30:34,466 - INFO - MySQL查询成功，增量数据（日期: 2025-06-21），共获取 207 条记录
2025-06-21 22:30:34,466 - INFO - 获取到 3 个日期需要处理: ['2025-06-19', '2025-06-20', '2025-06-21']
2025-06-21 22:30:34,482 - INFO - 开始处理日期: 2025-06-19
2025-06-21 22:30:34,482 - INFO - Request Parameters - Page 1:
2025-06-21 22:30:34,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:34,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:42,592 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5651036A-8F55-7271-9087-30E294CD96C6 Response: {'code': 'ServiceUnavailable', 'requestid': '5651036A-8F55-7271-9087-30E294CD96C6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5651036A-8F55-7271-9087-30E294CD96C6)
2025-06-21 22:30:42,592 - INFO - 开始处理日期: 2025-06-20
2025-06-21 22:30:42,592 - INFO - Request Parameters - Page 1:
2025-06-21 22:30:42,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:42,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:48,562 - INFO - Response - Page 1:
2025-06-21 22:30:48,562 - INFO - 第 1 页获取到 50 条记录
2025-06-21 22:30:49,062 - INFO - Request Parameters - Page 2:
2025-06-21 22:30:49,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:49,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:49,703 - INFO - Response - Page 2:
2025-06-21 22:30:49,703 - INFO - 第 2 页获取到 50 条记录
2025-06-21 22:30:50,203 - INFO - Request Parameters - Page 3:
2025-06-21 22:30:50,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:50,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:50,844 - INFO - Response - Page 3:
2025-06-21 22:30:50,844 - INFO - 第 3 页获取到 50 条记录
2025-06-21 22:30:51,344 - INFO - Request Parameters - Page 4:
2025-06-21 22:30:51,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:51,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:51,985 - INFO - Response - Page 4:
2025-06-21 22:30:51,985 - INFO - 第 4 页获取到 50 条记录
2025-06-21 22:30:52,485 - INFO - Request Parameters - Page 5:
2025-06-21 22:30:52,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:52,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:53,125 - INFO - Response - Page 5:
2025-06-21 22:30:53,125 - INFO - 第 5 页获取到 50 条记录
2025-06-21 22:30:53,641 - INFO - Request Parameters - Page 6:
2025-06-21 22:30:53,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:53,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:54,344 - INFO - Response - Page 6:
2025-06-21 22:30:54,344 - INFO - 第 6 页获取到 50 条记录
2025-06-21 22:30:54,845 - INFO - Request Parameters - Page 7:
2025-06-21 22:30:54,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:54,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:55,470 - INFO - Response - Page 7:
2025-06-21 22:30:55,470 - INFO - 第 7 页获取到 50 条记录
2025-06-21 22:30:55,985 - INFO - Request Parameters - Page 8:
2025-06-21 22:30:55,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:55,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:56,595 - INFO - Response - Page 8:
2025-06-21 22:30:56,595 - INFO - 第 8 页获取到 50 条记录
2025-06-21 22:30:57,095 - INFO - Request Parameters - Page 9:
2025-06-21 22:30:57,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:57,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:57,736 - INFO - Response - Page 9:
2025-06-21 22:30:57,736 - INFO - 第 9 页获取到 50 条记录
2025-06-21 22:30:58,236 - INFO - Request Parameters - Page 10:
2025-06-21 22:30:58,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:58,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:58,798 - INFO - Response - Page 10:
2025-06-21 22:30:58,798 - INFO - 第 10 页获取到 16 条记录
2025-06-21 22:30:59,314 - INFO - 查询完成，共获取到 466 条记录
2025-06-21 22:30:59,314 - INFO - 获取到 466 条表单数据
2025-06-21 22:30:59,314 - INFO - 当前日期 2025-06-20 有 153 条MySQL数据需要处理
2025-06-21 22:30:59,314 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 22:30:59,314 - INFO - 开始处理日期: 2025-06-21
2025-06-21 22:30:59,314 - INFO - Request Parameters - Page 1:
2025-06-21 22:30:59,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:30:59,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:30:59,752 - INFO - Response - Page 1:
2025-06-21 22:30:59,752 - INFO - 查询完成，共获取到 0 条记录
2025-06-21 22:30:59,752 - INFO - 获取到 0 条表单数据
2025-06-21 22:30:59,752 - INFO - 当前日期 2025-06-21 有 45 条MySQL数据需要处理
2025-06-21 22:30:59,752 - INFO - 开始批量插入 45 条新记录
2025-06-21 22:31:00,017 - INFO - 批量插入响应状态码: 200
2025-06-21 22:31:00,017 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 14:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2140', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '41A5B1E5-50C5-725F-885B-6FFAE0EA27A6', 'x-acs-trace-id': '87531f7bc7127eca02d8ba4f9c3f4ba1', 'etag': '2ZtLk3O7YwGUYKF9Bi1PxOw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-21 22:31:00,017 - INFO - 批量插入响应体: {'result': ['FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM4', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM5', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM6', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM7', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM8', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM9', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMA', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMB', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMC', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMD', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CME', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMF', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMG', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMH', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMI', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMJ', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMK', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CML', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMM', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMN', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMO', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMP', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMQ', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMR', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMS', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMT', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMU', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMV', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMW', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMX', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMY', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMZ', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM01', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM11', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM21', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM31', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM41', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM51', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM61', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM71', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM81', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM91', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMA1', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMB1', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMC1']}
2025-06-21 22:31:00,017 - INFO - 批量插入表单数据成功，批次 1，共 45 条记录
2025-06-21 22:31:00,017 - INFO - 成功插入的数据ID: ['FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM4', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM5', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM6', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM7', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM8', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM9', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMA', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMB', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMC', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMD', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CME', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMF', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMG', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMH', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMI', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMJ', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMK', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CML', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMM', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMN', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMO', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMP', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMQ', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMR', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMS', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMT', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMU', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMV', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMW', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMX', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMY', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMZ', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM01', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM11', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM21', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM31', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM41', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM51', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM61', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM71', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM81', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CM91', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMA1', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMB1', 'FINST-487664C1OZHWAUMI63LA8AZNYI3Q2R5L6C6CMC1']
2025-06-21 22:31:05,034 - INFO - 批量插入完成，共 45 条记录
2025-06-21 22:31:05,034 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 45 条，错误: 0 条
2025-06-21 22:31:05,034 - INFO - 数据同步完成！更新: 0 条，插入: 45 条，错误: 1 条
2025-06-21 22:32:05,060 - INFO - 开始同步昨天与今天的销售数据: 2025-06-20 至 2025-06-21
2025-06-21 22:32:05,060 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-21 22:32:05,060 - INFO - 查询参数: ('2025-06-20', '2025-06-21')
2025-06-21 22:32:05,201 - INFO - MySQL查询成功，时间段: 2025-06-20 至 2025-06-21，共获取 523 条记录
2025-06-21 22:32:05,201 - INFO - 获取到 2 个日期需要处理: ['2025-06-20', '2025-06-21']
2025-06-21 22:32:05,201 - INFO - 开始处理日期: 2025-06-20
2025-06-21 22:32:05,201 - INFO - Request Parameters - Page 1:
2025-06-21 22:32:05,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:05,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:05,888 - INFO - Response - Page 1:
2025-06-21 22:32:05,888 - INFO - 第 1 页获取到 50 条记录
2025-06-21 22:32:06,404 - INFO - Request Parameters - Page 2:
2025-06-21 22:32:06,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:06,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:07,092 - INFO - Response - Page 2:
2025-06-21 22:32:07,092 - INFO - 第 2 页获取到 50 条记录
2025-06-21 22:32:07,608 - INFO - Request Parameters - Page 3:
2025-06-21 22:32:07,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:07,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:08,280 - INFO - Response - Page 3:
2025-06-21 22:32:08,280 - INFO - 第 3 页获取到 50 条记录
2025-06-21 22:32:08,780 - INFO - Request Parameters - Page 4:
2025-06-21 22:32:08,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:08,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:09,483 - INFO - Response - Page 4:
2025-06-21 22:32:09,483 - INFO - 第 4 页获取到 50 条记录
2025-06-21 22:32:09,983 - INFO - Request Parameters - Page 5:
2025-06-21 22:32:09,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:09,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:10,624 - INFO - Response - Page 5:
2025-06-21 22:32:10,624 - INFO - 第 5 页获取到 50 条记录
2025-06-21 22:32:11,139 - INFO - Request Parameters - Page 6:
2025-06-21 22:32:11,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:11,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:11,827 - INFO - Response - Page 6:
2025-06-21 22:32:11,827 - INFO - 第 6 页获取到 50 条记录
2025-06-21 22:32:12,327 - INFO - Request Parameters - Page 7:
2025-06-21 22:32:12,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:12,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:12,952 - INFO - Response - Page 7:
2025-06-21 22:32:12,952 - INFO - 第 7 页获取到 50 条记录
2025-06-21 22:32:13,452 - INFO - Request Parameters - Page 8:
2025-06-21 22:32:13,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:13,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:14,077 - INFO - Response - Page 8:
2025-06-21 22:32:14,077 - INFO - 第 8 页获取到 50 条记录
2025-06-21 22:32:14,593 - INFO - Request Parameters - Page 9:
2025-06-21 22:32:14,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:14,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:15,234 - INFO - Response - Page 9:
2025-06-21 22:32:15,234 - INFO - 第 9 页获取到 50 条记录
2025-06-21 22:32:15,750 - INFO - Request Parameters - Page 10:
2025-06-21 22:32:15,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:15,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:16,281 - INFO - Response - Page 10:
2025-06-21 22:32:16,281 - INFO - 第 10 页获取到 16 条记录
2025-06-21 22:32:16,781 - INFO - 查询完成，共获取到 466 条记录
2025-06-21 22:32:16,781 - INFO - 获取到 466 条表单数据
2025-06-21 22:32:16,781 - INFO - 当前日期 2025-06-20 有 466 条MySQL数据需要处理
2025-06-21 22:32:16,797 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 22:32:16,797 - INFO - 开始处理日期: 2025-06-21
2025-06-21 22:32:16,797 - INFO - Request Parameters - Page 1:
2025-06-21 22:32:16,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 22:32:16,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 22:32:17,406 - INFO - Response - Page 1:
2025-06-21 22:32:17,406 - INFO - 第 1 页获取到 45 条记录
2025-06-21 22:32:17,922 - INFO - 查询完成，共获取到 45 条记录
2025-06-21 22:32:17,922 - INFO - 获取到 45 条表单数据
2025-06-21 22:32:17,922 - INFO - 当前日期 2025-06-21 有 45 条MySQL数据需要处理
2025-06-21 22:32:17,922 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 22:32:17,922 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 22:32:17,922 - INFO - 同步完成
