2025-06-08 21:30:33,093 - INFO - 使用默认增量同步（当天更新数据）
2025-06-08 21:30:33,093 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-08 21:30:33,093 - INFO - 查询参数: ('2025-06-08',)
2025-06-08 21:30:33,180 - INFO - MySQL查询成功，增量数据（日期: 2025-06-08），共获取 194 条记录
2025-06-08 21:30:33,181 - INFO - 获取到 5 个日期需要处理: ['2025-05-31', '2025-06-02', '2025-06-06', '2025-06-07', '2025-06-08']
2025-06-08 21:30:33,182 - INFO - 开始处理日期: 2025-05-31
2025-06-08 21:30:33,185 - INFO - Request Parameters - Page 1:
2025-06-08 21:30:33,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:33,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:41,282 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0BFC1E99-1730-70EF-B9FB-178289D7537C Response: {'code': 'ServiceUnavailable', 'requestid': '0BFC1E99-1730-70EF-B9FB-178289D7537C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0BFC1E99-1730-70EF-B9FB-178289D7537C)
2025-06-08 21:30:41,282 - INFO - 开始处理日期: 2025-06-02
2025-06-08 21:30:41,282 - INFO - Request Parameters - Page 1:
2025-06-08 21:30:41,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:41,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:49,402 - ERROR - 处理日期 2025-06-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EC94E3BC-A3EF-7426-A194-76B3728BEDFF Response: {'code': 'ServiceUnavailable', 'requestid': 'EC94E3BC-A3EF-7426-A194-76B3728BEDFF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EC94E3BC-A3EF-7426-A194-76B3728BEDFF)
2025-06-08 21:30:49,402 - INFO - 开始处理日期: 2025-06-06
2025-06-08 21:30:49,402 - INFO - Request Parameters - Page 1:
2025-06-08 21:30:49,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:49,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:50,292 - INFO - Response - Page 1:
2025-06-08 21:30:50,292 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:30:50,493 - INFO - Request Parameters - Page 2:
2025-06-08 21:30:50,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:50,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:51,259 - INFO - Response - Page 2:
2025-06-08 21:30:51,259 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:30:51,460 - INFO - Request Parameters - Page 3:
2025-06-08 21:30:51,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:51,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:52,257 - INFO - Response - Page 3:
2025-06-08 21:30:52,257 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:30:52,458 - INFO - Request Parameters - Page 4:
2025-06-08 21:30:52,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:52,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:53,304 - INFO - Response - Page 4:
2025-06-08 21:30:53,304 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:30:53,516 - INFO - Request Parameters - Page 5:
2025-06-08 21:30:53,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:53,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:54,355 - INFO - Response - Page 5:
2025-06-08 21:30:54,355 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:30:54,563 - INFO - Request Parameters - Page 6:
2025-06-08 21:30:54,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:30:54,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:30:55,277 - INFO - Response - Page 6:
2025-06-08 21:30:55,277 - INFO - 第 6 页获取到 21 条记录
2025-06-08 21:30:55,481 - INFO - 查询完成，共获取到 521 条记录
2025-06-08 21:30:55,481 - INFO - 获取到 521 条表单数据
2025-06-08 21:30:55,481 - INFO - 当前日期 2025-06-06 有 1 条MySQL数据需要处理
2025-06-08 21:30:55,481 - INFO - 开始批量插入 1 条新记录
2025-06-08 21:30:55,637 - INFO - 批量插入响应状态码: 200
2025-06-08 21:30:55,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 13:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DE01A228-C773-73F9-A8F5-E2F4F19F5BAE', 'x-acs-trace-id': 'f1ab08a01c77083f50bdcf3d6f5d139b', 'etag': '6TcFy50Olh+7m/T+Q25pm7A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 21:30:55,637 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91DB2W0YCH79RTSDUT4SH525HCBPNBMO9']}
2025-06-08 21:30:55,637 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 21:30:55,637 - INFO - 成功插入的数据ID: ['FINST-07E66I91DB2W0YCH79RTSDUT4SH525HCBPNBMO9']
2025-06-08 21:31:00,640 - INFO - 批量插入完成，共 1 条记录
2025-06-08 21:31:00,640 - INFO - 日期 2025-06-06 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-08 21:31:00,640 - INFO - 开始处理日期: 2025-06-07
2025-06-08 21:31:00,640 - INFO - Request Parameters - Page 1:
2025-06-08 21:31:00,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:31:00,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:31:01,497 - INFO - Response - Page 1:
2025-06-08 21:31:01,497 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:31:01,700 - INFO - Request Parameters - Page 2:
2025-06-08 21:31:01,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:31:01,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:31:02,572 - INFO - Response - Page 2:
2025-06-08 21:31:02,572 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:31:02,775 - INFO - Request Parameters - Page 3:
2025-06-08 21:31:02,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:31:02,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:31:03,649 - INFO - Response - Page 3:
2025-06-08 21:31:03,649 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:31:03,849 - INFO - Request Parameters - Page 4:
2025-06-08 21:31:03,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:31:03,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:31:04,616 - INFO - Response - Page 4:
2025-06-08 21:31:04,616 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:31:04,816 - INFO - Request Parameters - Page 5:
2025-06-08 21:31:04,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:31:04,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:31:05,552 - INFO - Response - Page 5:
2025-06-08 21:31:05,552 - INFO - 第 5 页获取到 64 条记录
2025-06-08 21:31:05,755 - INFO - 查询完成，共获取到 464 条记录
2025-06-08 21:31:05,755 - INFO - 获取到 464 条表单数据
2025-06-08 21:31:05,755 - INFO - 当前日期 2025-06-07 有 182 条MySQL数据需要处理
2025-06-08 21:31:05,755 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:31:05,755 - INFO - 开始处理日期: 2025-06-08
2025-06-08 21:31:05,755 - INFO - Request Parameters - Page 1:
2025-06-08 21:31:05,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:31:05,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749312000000, 1749398399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:31:06,286 - INFO - Response - Page 1:
2025-06-08 21:31:06,286 - INFO - 第 1 页获取到 2 条记录
2025-06-08 21:31:06,487 - INFO - 查询完成，共获取到 2 条记录
2025-06-08 21:31:06,487 - INFO - 获取到 2 条表单数据
2025-06-08 21:31:06,487 - INFO - 当前日期 2025-06-08 有 7 条MySQL数据需要处理
2025-06-08 21:31:06,487 - INFO - 开始批量插入 5 条新记录
2025-06-08 21:31:06,644 - INFO - 批量插入响应状态码: 200
2025-06-08 21:31:06,644 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 13:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5C7C2241-65D5-7141-9733-A0D0511E2384', 'x-acs-trace-id': '90a138de4d6b8d6b4aff03a43fb27420', 'etag': '2JDFPTJe+PqSAHGFMNxjONQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 21:31:06,644 - INFO - 批量插入响应体: {'result': ['FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBML7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMM7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMN7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMO7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMP7']}
2025-06-08 21:31:06,644 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-06-08 21:31:06,644 - INFO - 成功插入的数据ID: ['FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBML7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMM7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMN7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMO7', 'FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR23ZKBPNBMP7']
2025-06-08 21:31:11,648 - INFO - 批量插入完成，共 5 条记录
2025-06-08 21:31:11,648 - INFO - 日期 2025-06-08 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-06-08 21:31:11,648 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 2 条
2025-06-08 21:32:11,633 - INFO - 开始同步昨天与今天的销售数据: 2025-06-07 至 2025-06-08
2025-06-08 21:32:11,633 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-08 21:32:11,633 - INFO - 查询参数: ('2025-06-07', '2025-06-08')
2025-06-08 21:32:11,713 - INFO - MySQL查询成功，时间段: 2025-06-07 至 2025-06-08，共获取 471 条记录
2025-06-08 21:32:11,713 - INFO - 获取到 2 个日期需要处理: ['2025-06-07', '2025-06-08']
2025-06-08 21:32:11,713 - INFO - 开始处理日期: 2025-06-07
2025-06-08 21:32:11,713 - INFO - Request Parameters - Page 1:
2025-06-08 21:32:11,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:32:11,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:32:12,638 - INFO - Response - Page 1:
2025-06-08 21:32:12,638 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:32:12,853 - INFO - Request Parameters - Page 2:
2025-06-08 21:32:12,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:32:12,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:32:13,683 - INFO - Response - Page 2:
2025-06-08 21:32:13,683 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:32:13,897 - INFO - Request Parameters - Page 3:
2025-06-08 21:32:13,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:32:13,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:32:21,989 - ERROR - 处理日期 2025-06-07 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 65860E4A-1C34-76C9-87EE-6AE780227F7C Response: {'code': 'ServiceUnavailable', 'requestid': '65860E4A-1C34-76C9-87EE-6AE780227F7C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 65860E4A-1C34-76C9-87EE-6AE780227F7C)
2025-06-08 21:32:22,004 - INFO - 开始处理日期: 2025-06-08
2025-06-08 21:32:22,004 - INFO - Request Parameters - Page 1:
2025-06-08 21:32:22,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:32:22,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749312000000, 1749398399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:32:25,461 - INFO - Response - Page 1:
2025-06-08 21:32:25,461 - INFO - 第 1 页获取到 7 条记录
2025-06-08 21:32:25,661 - INFO - 查询完成，共获取到 7 条记录
2025-06-08 21:32:25,661 - INFO - 获取到 7 条表单数据
2025-06-08 21:32:25,661 - INFO - 当前日期 2025-06-08 有 7 条MySQL数据需要处理
2025-06-08 21:32:25,662 - INFO - 日期 2025-06-08 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:32:25,662 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-08 21:32:25,662 - INFO - 同步完成
2025-06-08 22:30:32,760 - INFO - 使用默认增量同步（当天更新数据）
2025-06-08 22:30:32,760 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-08 22:30:32,760 - INFO - 查询参数: ('2025-06-08',)
2025-06-08 22:30:32,838 - INFO - MySQL查询成功，增量数据（日期: 2025-06-08），共获取 246 条记录
2025-06-08 22:30:32,838 - INFO - 获取到 10 个日期需要处理: ['2025-05-13', '2025-05-15', '2025-05-27', '2025-05-28', '2025-05-29', '2025-05-31', '2025-06-02', '2025-06-06', '2025-06-07', '2025-06-08']
2025-06-08 22:30:32,853 - INFO - 开始处理日期: 2025-05-13
2025-06-08 22:30:32,853 - INFO - Request Parameters - Page 1:
2025-06-08 22:30:32,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:32,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:30:40,990 - ERROR - 处理日期 2025-05-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 26D99C86-737C-7B13-B810-6D6440354C8F Response: {'code': 'ServiceUnavailable', 'requestid': '26D99C86-737C-7B13-B810-6D6440354C8F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 26D99C86-737C-7B13-B810-6D6440354C8F)
2025-06-08 22:30:40,990 - INFO - 开始处理日期: 2025-05-15
2025-06-08 22:30:40,991 - INFO - Request Parameters - Page 1:
2025-06-08 22:30:40,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:40,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:30:47,743 - INFO - Response - Page 1:
2025-06-08 22:30:47,743 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:30:47,946 - INFO - Request Parameters - Page 2:
2025-06-08 22:30:47,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:47,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:30:48,727 - INFO - Response - Page 2:
2025-06-08 22:30:48,727 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:30:48,930 - INFO - Request Parameters - Page 3:
2025-06-08 22:30:48,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:48,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:30:49,789 - INFO - Response - Page 3:
2025-06-08 22:30:49,789 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:30:49,992 - INFO - Request Parameters - Page 4:
2025-06-08 22:30:49,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:49,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:30:58,080 - ERROR - 处理日期 2025-05-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 216806F7-53F3-7A61-94C9-93DFA6918AA3 Response: {'code': 'ServiceUnavailable', 'requestid': '216806F7-53F3-7A61-94C9-93DFA6918AA3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 216806F7-53F3-7A61-94C9-93DFA6918AA3)
2025-06-08 22:30:58,080 - INFO - 开始处理日期: 2025-05-27
2025-06-08 22:30:58,080 - INFO - Request Parameters - Page 1:
2025-06-08 22:30:58,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:58,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:30:58,924 - INFO - Response - Page 1:
2025-06-08 22:30:58,924 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:30:59,127 - INFO - Request Parameters - Page 2:
2025-06-08 22:30:59,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:30:59,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:00,033 - INFO - Response - Page 2:
2025-06-08 22:31:00,033 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:00,236 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:00,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:00,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:01,140 - INFO - Response - Page 3:
2025-06-08 22:31:01,140 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:01,340 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:01,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:01,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:02,192 - INFO - Response - Page 4:
2025-06-08 22:31:02,193 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:02,393 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:02,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:02,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:03,192 - INFO - Response - Page 5:
2025-06-08 22:31:03,192 - INFO - 第 5 页获取到 100 条记录
2025-06-08 22:31:03,392 - INFO - Request Parameters - Page 6:
2025-06-08 22:31:03,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:03,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:04,157 - INFO - Response - Page 6:
2025-06-08 22:31:04,157 - INFO - 第 6 页获取到 84 条记录
2025-06-08 22:31:04,357 - INFO - 查询完成，共获取到 584 条记录
2025-06-08 22:31:04,357 - INFO - 获取到 584 条表单数据
2025-06-08 22:31:04,366 - INFO - 当前日期 2025-05-27 有 1 条MySQL数据需要处理
2025-06-08 22:31:04,366 - INFO - 开始批量插入 1 条新记录
2025-06-08 22:31:04,521 - INFO - 批量插入响应状态码: 200
2025-06-08 22:31:04,521 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FBA5F5C6-1E16-7D69-97CD-E92A09B38A28', 'x-acs-trace-id': '5396c9344d4365079573525ae8fe47e8', 'etag': '62ZU8HI4D2Q+TtEK8jr5uGQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 22:31:04,521 - INFO - 批量插入响应体: {'result': ['FINST-DO566BD1WO4WUI4C951V2DKLQB2824UPGRNBMF6']}
2025-06-08 22:31:04,521 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 22:31:04,521 - INFO - 成功插入的数据ID: ['FINST-DO566BD1WO4WUI4C951V2DKLQB2824UPGRNBMF6']
2025-06-08 22:31:09,520 - INFO - 批量插入完成，共 1 条记录
2025-06-08 22:31:09,520 - INFO - 日期 2025-05-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-08 22:31:09,520 - INFO - 开始处理日期: 2025-05-28
2025-06-08 22:31:09,520 - INFO - Request Parameters - Page 1:
2025-06-08 22:31:09,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:09,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:10,324 - INFO - Response - Page 1:
2025-06-08 22:31:10,324 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:31:10,524 - INFO - Request Parameters - Page 2:
2025-06-08 22:31:10,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:10,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:11,316 - INFO - Response - Page 2:
2025-06-08 22:31:11,316 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:11,516 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:11,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:11,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:12,619 - INFO - Response - Page 3:
2025-06-08 22:31:12,619 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:12,821 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:12,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:12,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:13,732 - INFO - Response - Page 4:
2025-06-08 22:31:13,732 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:13,932 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:13,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:13,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:14,784 - INFO - Response - Page 5:
2025-06-08 22:31:14,785 - INFO - 第 5 页获取到 100 条记录
2025-06-08 22:31:14,985 - INFO - Request Parameters - Page 6:
2025-06-08 22:31:14,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:14,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:15,765 - INFO - Response - Page 6:
2025-06-08 22:31:15,765 - INFO - 第 6 页获取到 83 条记录
2025-06-08 22:31:15,970 - INFO - 查询完成，共获取到 583 条记录
2025-06-08 22:31:15,970 - INFO - 获取到 583 条表单数据
2025-06-08 22:31:15,970 - INFO - 当前日期 2025-05-28 有 1 条MySQL数据需要处理
2025-06-08 22:31:15,970 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM0E
2025-06-08 22:31:16,567 - INFO - 更新表单数据成功: FINST-EZD66RB1FETVWNEP80XZQ6DFE3G632A8IT8BM0E
2025-06-08 22:31:16,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1858.0, 'new_value': 1910.0}, {'field': 'total_amount', 'old_value': 1858.0, 'new_value': 1910.0}]
2025-06-08 22:31:16,567 - INFO - 日期 2025-05-28 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-08 22:31:16,567 - INFO - 开始处理日期: 2025-05-29
2025-06-08 22:31:16,567 - INFO - Request Parameters - Page 1:
2025-06-08 22:31:16,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:16,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:17,400 - INFO - Response - Page 1:
2025-06-08 22:31:17,400 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:31:17,600 - INFO - Request Parameters - Page 2:
2025-06-08 22:31:17,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:17,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:18,418 - INFO - Response - Page 2:
2025-06-08 22:31:18,418 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:18,622 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:18,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:18,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:19,414 - INFO - Response - Page 3:
2025-06-08 22:31:19,414 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:19,617 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:19,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:19,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:20,470 - INFO - Response - Page 4:
2025-06-08 22:31:20,470 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:20,675 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:20,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:20,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:21,466 - INFO - Response - Page 5:
2025-06-08 22:31:21,466 - INFO - 第 5 页获取到 100 条记录
2025-06-08 22:31:21,671 - INFO - Request Parameters - Page 6:
2025-06-08 22:31:21,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:21,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:22,553 - INFO - Response - Page 6:
2025-06-08 22:31:22,553 - INFO - 第 6 页获取到 100 条记录
2025-06-08 22:31:22,753 - INFO - Request Parameters - Page 7:
2025-06-08 22:31:22,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:22,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:23,299 - INFO - Response - Page 7:
2025-06-08 22:31:23,299 - INFO - 第 7 页获取到 9 条记录
2025-06-08 22:31:23,511 - INFO - 查询完成，共获取到 609 条记录
2025-06-08 22:31:23,511 - INFO - 获取到 609 条表单数据
2025-06-08 22:31:23,511 - INFO - 当前日期 2025-05-29 有 1 条MySQL数据需要处理
2025-06-08 22:31:23,511 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1PEUV0G6P619FXDVCOWOT3F22P4ABM63
2025-06-08 22:31:23,932 - INFO - 更新表单数据成功: FINST-ZNE66RC1PEUV0G6P619FXDVCOWOT3F22P4ABM63
2025-06-08 22:31:23,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10367.44}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10367.44}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-08 22:31:23,932 - INFO - 日期 2025-05-29 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-08 22:31:23,932 - INFO - 开始处理日期: 2025-05-31
2025-06-08 22:31:23,932 - INFO - Request Parameters - Page 1:
2025-06-08 22:31:23,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:23,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:24,729 - INFO - Response - Page 1:
2025-06-08 22:31:24,729 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:31:24,943 - INFO - Request Parameters - Page 2:
2025-06-08 22:31:24,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:24,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:25,707 - INFO - Response - Page 2:
2025-06-08 22:31:25,707 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:25,914 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:25,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:25,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:26,731 - INFO - Response - Page 3:
2025-06-08 22:31:26,731 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:26,931 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:26,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:26,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:27,905 - INFO - Response - Page 4:
2025-06-08 22:31:27,905 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:28,105 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:28,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:28,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:28,945 - INFO - Response - Page 5:
2025-06-08 22:31:28,946 - INFO - 第 5 页获取到 100 条记录
2025-06-08 22:31:29,146 - INFO - Request Parameters - Page 6:
2025-06-08 22:31:29,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:29,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:29,924 - INFO - Response - Page 6:
2025-06-08 22:31:29,924 - INFO - 第 6 页获取到 40 条记录
2025-06-08 22:31:30,125 - INFO - 查询完成，共获取到 540 条记录
2025-06-08 22:31:30,125 - INFO - 获取到 540 条表单数据
2025-06-08 22:31:30,128 - INFO - 当前日期 2025-05-31 有 3 条MySQL数据需要处理
2025-06-08 22:31:30,137 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMCA
2025-06-08 22:31:30,583 - INFO - 更新表单数据成功: FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMCA
2025-06-08 22:31:30,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 548.52, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 14432.66, 'new_value': 15004.28}, {'field': 'total_amount', 'old_value': 14981.18, 'new_value': 15004.28}]
2025-06-08 22:31:30,583 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM88
2025-06-08 22:31:31,067 - INFO - 更新表单数据成功: FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM88
2025-06-08 22:31:31,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 898.23, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11850.62, 'new_value': 13412.48}, {'field': 'total_amount', 'old_value': 12748.85, 'new_value': 13412.48}]
2025-06-08 22:31:31,067 - INFO - 开始批量插入 1 条新记录
2025-06-08 22:31:31,207 - INFO - 批量插入响应状态码: 200
2025-06-08 22:31:31,207 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 14:31:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '10B04E7F-E5A2-7999-9311-A2DBBC96E058', 'x-acs-trace-id': 'a89c16b7157b1972e19b3603f27eb809', 'etag': '6TO56Wz6PUDK2f5JUS2ZB5g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 22:31:31,207 - INFO - 批量插入响应体: {'result': ['FINST-RN7661814B2WNHLJFGOTG4NDPPEK3TFAHRNBMDE']}
2025-06-08 22:31:31,207 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 22:31:31,207 - INFO - 成功插入的数据ID: ['FINST-RN7661814B2WNHLJFGOTG4NDPPEK3TFAHRNBMDE']
2025-06-08 22:31:36,207 - INFO - 批量插入完成，共 1 条记录
2025-06-08 22:31:36,207 - INFO - 日期 2025-05-31 处理完成 - 更新: 2 条，插入: 1 条，错误: 0 条
2025-06-08 22:31:36,207 - INFO - 开始处理日期: 2025-06-02
2025-06-08 22:31:36,207 - INFO - Request Parameters - Page 1:
2025-06-08 22:31:36,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:36,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:37,123 - INFO - Response - Page 1:
2025-06-08 22:31:37,123 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:31:37,324 - INFO - Request Parameters - Page 2:
2025-06-08 22:31:37,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:37,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:38,229 - INFO - Response - Page 2:
2025-06-08 22:31:38,229 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:38,429 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:38,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:38,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:39,270 - INFO - Response - Page 3:
2025-06-08 22:31:39,271 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:39,471 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:39,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:39,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:40,275 - INFO - Response - Page 4:
2025-06-08 22:31:40,276 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:40,476 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:40,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:40,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:41,325 - INFO - Response - Page 5:
2025-06-08 22:31:41,325 - INFO - 第 5 页获取到 100 条记录
2025-06-08 22:31:41,526 - INFO - Request Parameters - Page 6:
2025-06-08 22:31:41,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:41,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:42,373 - INFO - Response - Page 6:
2025-06-08 22:31:42,374 - INFO - 第 6 页获取到 100 条记录
2025-06-08 22:31:42,579 - INFO - Request Parameters - Page 7:
2025-06-08 22:31:42,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:42,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:43,240 - INFO - Response - Page 7:
2025-06-08 22:31:43,241 - INFO - 第 7 页获取到 17 条记录
2025-06-08 22:31:43,441 - INFO - 查询完成，共获取到 617 条记录
2025-06-08 22:31:43,441 - INFO - 获取到 617 条表单数据
2025-06-08 22:31:43,452 - INFO - 当前日期 2025-06-02 有 1 条MySQL数据需要处理
2025-06-08 22:31:43,453 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA119VVQENXDIZI39VH8MNH2JJVKWFBM9N
2025-06-08 22:31:44,970 - INFO - 更新表单数据成功: FINST-MLF66JA119VVQENXDIZI39VH8MNH2JJVKWFBM9N
2025-06-08 22:31:44,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2372.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2372.0}, {'field': 'order_count', 'old_value': 2372, 'new_value': 2}]
2025-06-08 22:31:44,970 - INFO - 日期 2025-06-02 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-08 22:31:44,970 - INFO - 开始处理日期: 2025-06-06
2025-06-08 22:31:44,970 - INFO - Request Parameters - Page 1:
2025-06-08 22:31:44,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:44,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:45,840 - INFO - Response - Page 1:
2025-06-08 22:31:45,840 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:31:46,041 - INFO - Request Parameters - Page 2:
2025-06-08 22:31:46,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:46,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:46,837 - INFO - Response - Page 2:
2025-06-08 22:31:46,837 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:47,040 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:47,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:47,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:48,148 - INFO - Response - Page 3:
2025-06-08 22:31:48,148 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:48,351 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:48,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:48,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:49,226 - INFO - Response - Page 4:
2025-06-08 22:31:49,227 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:49,428 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:49,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:49,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:50,208 - INFO - Response - Page 5:
2025-06-08 22:31:50,208 - INFO - 第 5 页获取到 100 条记录
2025-06-08 22:31:50,411 - INFO - Request Parameters - Page 6:
2025-06-08 22:31:50,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:50,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:51,050 - INFO - Response - Page 6:
2025-06-08 22:31:51,050 - INFO - 第 6 页获取到 22 条记录
2025-06-08 22:31:51,253 - INFO - 查询完成，共获取到 522 条记录
2025-06-08 22:31:51,253 - INFO - 获取到 522 条表单数据
2025-06-08 22:31:51,253 - INFO - 当前日期 2025-06-06 有 1 条MySQL数据需要处理
2025-06-08 22:31:51,253 - INFO - 日期 2025-06-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 22:31:51,253 - INFO - 开始处理日期: 2025-06-07
2025-06-08 22:31:51,253 - INFO - Request Parameters - Page 1:
2025-06-08 22:31:51,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:51,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:52,155 - INFO - Response - Page 1:
2025-06-08 22:31:52,155 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:31:52,356 - INFO - Request Parameters - Page 2:
2025-06-08 22:31:52,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:52,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:53,161 - INFO - Response - Page 2:
2025-06-08 22:31:53,161 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:31:53,376 - INFO - Request Parameters - Page 3:
2025-06-08 22:31:53,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:53,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:54,138 - INFO - Response - Page 3:
2025-06-08 22:31:54,138 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:31:54,344 - INFO - Request Parameters - Page 4:
2025-06-08 22:31:54,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:54,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:55,170 - INFO - Response - Page 4:
2025-06-08 22:31:55,170 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:31:55,373 - INFO - Request Parameters - Page 5:
2025-06-08 22:31:55,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:31:55,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:31:56,126 - INFO - Response - Page 5:
2025-06-08 22:31:56,126 - INFO - 第 5 页获取到 64 条记录
2025-06-08 22:31:56,326 - INFO - 查询完成，共获取到 464 条记录
2025-06-08 22:31:56,326 - INFO - 获取到 464 条表单数据
2025-06-08 22:31:56,335 - INFO - 当前日期 2025-06-07 有 184 条MySQL数据需要处理
2025-06-08 22:31:56,339 - INFO - 开始批量插入 2 条新记录
2025-06-08 22:31:56,492 - INFO - 批量插入响应状态码: 200
2025-06-08 22:31:56,492 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 14:31:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CB6C493C-8E76-78DF-9CE6-3E2B4F078EC7', 'x-acs-trace-id': '349a4d57862ef4f7e5d1867d56b9bbf7', 'etag': '1+FmLpvHdumMDCMWSOfWMkA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 22:31:56,492 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61VB2W64XQA3Z7E63UHD6N3YXTHRNBMB01', 'FINST-3PF66X61VB2W64XQA3Z7E63UHD6N3YXTHRNBMC01']}
2025-06-08 22:31:56,492 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-08 22:31:56,492 - INFO - 成功插入的数据ID: ['FINST-3PF66X61VB2W64XQA3Z7E63UHD6N3YXTHRNBMB01', 'FINST-3PF66X61VB2W64XQA3Z7E63UHD6N3YXTHRNBMC01']
2025-06-08 22:32:01,492 - INFO - 批量插入完成，共 2 条记录
2025-06-08 22:32:01,492 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-08 22:32:01,492 - INFO - 开始处理日期: 2025-06-08
2025-06-08 22:32:01,492 - INFO - Request Parameters - Page 1:
2025-06-08 22:32:01,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:32:01,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749312000000, 1749398399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:32:02,076 - INFO - Response - Page 1:
2025-06-08 22:32:02,076 - INFO - 第 1 页获取到 7 条记录
2025-06-08 22:32:02,276 - INFO - 查询完成，共获取到 7 条记录
2025-06-08 22:32:02,276 - INFO - 获取到 7 条表单数据
2025-06-08 22:32:02,277 - INFO - 当前日期 2025-06-08 有 52 条MySQL数据需要处理
2025-06-08 22:32:02,278 - INFO - 开始批量插入 45 条新记录
2025-06-08 22:32:02,510 - INFO - 批量插入响应状态码: 200
2025-06-08 22:32:02,511 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 14:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2172', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '577BC7E7-543F-7C70-9EBF-CA2E86F4B321', 'x-acs-trace-id': '377a39de244c9c1d184a338052fc95af', 'etag': '26VV4K/JDDNE1uiVYwJiMBQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 22:32:02,511 - INFO - 批量插入响应体: {'result': ['FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM1P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM2P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM3P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM4P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM5P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM6P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM7P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM8P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM9P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMAP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMBP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMCP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMDP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMEP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMFP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMGP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMHP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMIP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMJP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMKP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMLP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMMP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMNP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMOP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMPP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMQP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMRP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMSP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMTP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMUP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMVP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMWP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMXP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMYP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMZP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM0Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM1Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM2Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM3Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM4Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM5Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM6Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM7Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM8Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM9Q']}
2025-06-08 22:32:02,511 - INFO - 批量插入表单数据成功，批次 1，共 45 条记录
2025-06-08 22:32:02,511 - INFO - 成功插入的数据ID: ['FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM1P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM2P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM3P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM4P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM5P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM6P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM7P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM8P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM9P', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMAP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMBP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMCP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMDP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMEP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMFP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMGP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMHP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMIP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMJP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMKP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMLP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMMP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMNP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMOP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMPP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMQP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMRP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMSP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMTP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMUP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMVP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMWP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMXP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMYP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBMZP', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM0Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM1Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM2Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM3Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM4Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM5Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM6Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM7Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM8Q', 'FINST-FIG66R81KS2WNBZI993PMA07T8OG26LYHRNBM9Q']
2025-06-08 22:32:07,511 - INFO - 批量插入完成，共 45 条记录
2025-06-08 22:32:07,511 - INFO - 日期 2025-06-08 处理完成 - 更新: 0 条，插入: 45 条，错误: 0 条
2025-06-08 22:32:07,511 - INFO - 数据同步完成！更新: 5 条，插入: 49 条，错误: 2 条
2025-06-08 22:33:07,496 - INFO - 开始同步昨天与今天的销售数据: 2025-06-07 至 2025-06-08
2025-06-08 22:33:07,497 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-08 22:33:07,497 - INFO - 查询参数: ('2025-06-07', '2025-06-08')
2025-06-08 22:33:07,582 - INFO - MySQL查询成功，时间段: 2025-06-07 至 2025-06-08，共获取 526 条记录
2025-06-08 22:33:07,582 - INFO - 获取到 2 个日期需要处理: ['2025-06-07', '2025-06-08']
2025-06-08 22:33:07,586 - INFO - 开始处理日期: 2025-06-07
2025-06-08 22:33:07,587 - INFO - Request Parameters - Page 1:
2025-06-08 22:33:07,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:33:07,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:33:08,594 - INFO - Response - Page 1:
2025-06-08 22:33:08,594 - INFO - 第 1 页获取到 100 条记录
2025-06-08 22:33:08,794 - INFO - Request Parameters - Page 2:
2025-06-08 22:33:08,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:33:08,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:33:09,603 - INFO - Response - Page 2:
2025-06-08 22:33:09,604 - INFO - 第 2 页获取到 100 条记录
2025-06-08 22:33:09,813 - INFO - Request Parameters - Page 3:
2025-06-08 22:33:09,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:33:09,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:33:10,667 - INFO - Response - Page 3:
2025-06-08 22:33:10,667 - INFO - 第 3 页获取到 100 条记录
2025-06-08 22:33:10,867 - INFO - Request Parameters - Page 4:
2025-06-08 22:33:10,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:33:10,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:33:11,741 - INFO - Response - Page 4:
2025-06-08 22:33:11,741 - INFO - 第 4 页获取到 100 条记录
2025-06-08 22:33:11,941 - INFO - Request Parameters - Page 5:
2025-06-08 22:33:11,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:33:11,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:33:12,661 - INFO - Response - Page 5:
2025-06-08 22:33:12,661 - INFO - 第 5 页获取到 66 条记录
2025-06-08 22:33:12,861 - INFO - 查询完成，共获取到 466 条记录
2025-06-08 22:33:12,861 - INFO - 获取到 466 条表单数据
2025-06-08 22:33:12,869 - INFO - 当前日期 2025-06-07 有 474 条MySQL数据需要处理
2025-06-08 22:33:12,877 - INFO - 开始批量插入 8 条新记录
2025-06-08 22:33:13,050 - INFO - 批量插入响应状态码: 200
2025-06-08 22:33:13,050 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 14:33:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E0B35D18-609C-712C-B4F0-632AE364B490', 'x-acs-trace-id': '432a06b86faaea4a609287fd1b840a9d', 'etag': '38CkLkL6FuyDPhMq9A3TDRQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 22:33:13,050 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMI3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMJ3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMK3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBML3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMM3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMN3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMO3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMP3']}
2025-06-08 22:33:13,050 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-06-08 22:33:13,050 - INFO - 成功插入的数据ID: ['FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMI3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMJ3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMK3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBML3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMM3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMN3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMO3', 'FINST-RNA66D71UK4WZP1SCKVCH6Y6VDC4251HJRNBMP3']
2025-06-08 22:33:18,050 - INFO - 批量插入完成，共 8 条记录
2025-06-08 22:33:18,050 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-06-08 22:33:18,050 - INFO - 开始处理日期: 2025-06-08
2025-06-08 22:33:18,050 - INFO - Request Parameters - Page 1:
2025-06-08 22:33:18,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 22:33:18,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749312000000, 1749398399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 22:33:18,810 - INFO - Response - Page 1:
2025-06-08 22:33:18,811 - INFO - 第 1 页获取到 52 条记录
2025-06-08 22:33:19,011 - INFO - 查询完成，共获取到 52 条记录
2025-06-08 22:33:19,011 - INFO - 获取到 52 条表单数据
2025-06-08 22:33:19,013 - INFO - 当前日期 2025-06-08 有 52 条MySQL数据需要处理
2025-06-08 22:33:19,014 - INFO - 日期 2025-06-08 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 22:33:19,014 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 0 条
2025-06-08 22:33:19,015 - INFO - 同步完成
2025-06-08 23:30:33,592 - INFO - 使用默认增量同步（当天更新数据）
2025-06-08 23:30:33,592 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-08 23:30:33,592 - INFO - 查询参数: ('2025-06-08',)
2025-06-08 23:30:33,701 - INFO - MySQL查询成功，增量数据（日期: 2025-06-08），共获取 798 条记录
2025-06-08 23:30:33,701 - INFO - 获取到 35 个日期需要处理: ['2025-05-01', '2025-05-02', '2025-05-03', '2025-05-04', '2025-05-05', '2025-05-06', '2025-05-07', '2025-05-08', '2025-05-09', '2025-05-10', '2025-05-11', '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15', '2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20', '2025-05-21', '2025-05-22', '2025-05-23', '2025-05-24', '2025-05-25', '2025-05-26', '2025-05-27', '2025-05-28', '2025-05-29', '2025-05-30', '2025-05-31', '2025-06-02', '2025-06-06', '2025-06-07', '2025-06-08']
2025-06-08 23:30:33,701 - INFO - 开始处理日期: 2025-05-01
2025-06-08 23:30:33,717 - INFO - Request Parameters - Page 1:
2025-06-08 23:30:33,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:30:33,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:30:41,840 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3FCFC8C6-86BB-7698-9F12-ADC62B59DBD1 Response: {'code': 'ServiceUnavailable', 'requestid': '3FCFC8C6-86BB-7698-9F12-ADC62B59DBD1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3FCFC8C6-86BB-7698-9F12-ADC62B59DBD1)
2025-06-08 23:30:41,840 - INFO - 开始处理日期: 2025-05-02
2025-06-08 23:30:41,840 - INFO - Request Parameters - Page 1:
2025-06-08 23:30:41,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:30:41,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:30:49,961 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BEB15521-F848-7B4D-B054-9FEF3E667066 Response: {'code': 'ServiceUnavailable', 'requestid': 'BEB15521-F848-7B4D-B054-9FEF3E667066', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BEB15521-F848-7B4D-B054-9FEF3E667066)
2025-06-08 23:30:49,962 - INFO - 开始处理日期: 2025-05-03
2025-06-08 23:30:49,962 - INFO - Request Parameters - Page 1:
2025-06-08 23:30:49,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:30:49,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:30:50,880 - INFO - Response - Page 1:
2025-06-08 23:30:50,880 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:30:51,083 - INFO - Request Parameters - Page 2:
2025-06-08 23:30:51,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:30:51,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:30:51,880 - INFO - Response - Page 2:
2025-06-08 23:30:51,880 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:30:52,083 - INFO - Request Parameters - Page 3:
2025-06-08 23:30:52,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:30:52,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:00,209 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 221590C2-3F5A-7FAB-BD6D-7920AC0E4653 Response: {'code': 'ServiceUnavailable', 'requestid': '221590C2-3F5A-7FAB-BD6D-7920AC0E4653', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 221590C2-3F5A-7FAB-BD6D-7920AC0E4653)
2025-06-08 23:31:00,209 - INFO - 开始处理日期: 2025-05-04
2025-06-08 23:31:00,209 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:00,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:00,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:00,986 - INFO - Response - Page 1:
2025-06-08 23:31:01,002 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:01,205 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:01,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:01,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:02,077 - INFO - Response - Page 2:
2025-06-08 23:31:02,077 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:31:02,280 - INFO - Request Parameters - Page 3:
2025-06-08 23:31:02,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:02,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:03,326 - INFO - Response - Page 3:
2025-06-08 23:31:03,326 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:31:03,526 - INFO - Request Parameters - Page 4:
2025-06-08 23:31:03,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:03,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:04,325 - INFO - Response - Page 4:
2025-06-08 23:31:04,325 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:31:04,527 - INFO - Request Parameters - Page 5:
2025-06-08 23:31:04,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:04,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:05,335 - INFO - Response - Page 5:
2025-06-08 23:31:05,335 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:31:05,539 - INFO - Request Parameters - Page 6:
2025-06-08 23:31:05,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:05,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:06,476 - INFO - Response - Page 6:
2025-06-08 23:31:06,476 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:31:06,679 - INFO - Request Parameters - Page 7:
2025-06-08 23:31:06,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:06,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:07,304 - INFO - Response - Page 7:
2025-06-08 23:31:07,304 - INFO - 第 7 页获取到 23 条记录
2025-06-08 23:31:07,507 - INFO - 查询完成，共获取到 623 条记录
2025-06-08 23:31:07,507 - INFO - 获取到 623 条表单数据
2025-06-08 23:31:07,507 - INFO - 当前日期 2025-05-04 有 17 条MySQL数据需要处理
2025-06-08 23:31:07,507 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHT1
2025-06-08 23:31:07,914 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHT1
2025-06-08 23:31:07,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4473.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2.0, 'new_value': 4473.0}, {'field': 'total_amount', 'old_value': 4475.0, 'new_value': 4473.0}]
2025-06-08 23:31:07,914 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMQT1
2025-06-08 23:31:08,289 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMQT1
2025-06-08 23:31:08,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7078.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11107.0, 'new_value': 18185.0}]
2025-06-08 23:31:08,289 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6U1
2025-06-08 23:31:08,804 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6U1
2025-06-08 23:31:08,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6727.0, 'new_value': 6727.9}, {'field': 'total_amount', 'old_value': 6727.0, 'new_value': 6727.9}]
2025-06-08 23:31:08,804 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMVU1
2025-06-08 23:31:09,216 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMVU1
2025-06-08 23:31:09,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8860.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 8860.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:31:09,217 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV21
2025-06-08 23:31:09,634 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV21
2025-06-08 23:31:09,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39457.2, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 23480.0, 'new_value': 63098.2}, {'field': 'total_amount', 'old_value': 62937.2, 'new_value': 63098.2}]
2025-06-08 23:31:09,634 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66O713Y9VDVT3BOLSP6D0AR2P3HDW9WGAM02
2025-06-08 23:31:10,079 - INFO - 更新表单数据成功: FINST-3PF66O713Y9VDVT3BOLSP6D0AR2P3HDW9WGAM02
2025-06-08 23:31:10,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 1591.0, 'new_value': 1513.0}]
2025-06-08 23:31:10,079 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66O713Y9VDVT3BOLSP6D0AR2P3HDW9WGAMM2
2025-06-08 23:31:10,474 - INFO - 更新表单数据成功: FINST-3PF66O713Y9VDVT3BOLSP6D0AR2P3HDW9WGAMM2
2025-06-08 23:31:10,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120840.0, 'new_value': 119563.0}, {'field': 'total_amount', 'old_value': 120840.0, 'new_value': 119563.0}]
2025-06-08 23:31:10,475 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMEZ
2025-06-08 23:31:10,871 - INFO - 更新表单数据成功: FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMEZ
2025-06-08 23:31:10,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6877.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5193.0, 'new_value': 12070.8}]
2025-06-08 23:31:10,871 - INFO - 日期 2025-05-04 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-06-08 23:31:10,871 - INFO - 开始处理日期: 2025-05-05
2025-06-08 23:31:10,872 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:10,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:10,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:11,763 - INFO - Response - Page 1:
2025-06-08 23:31:11,763 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:11,963 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:11,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:11,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:12,685 - INFO - Response - Page 2:
2025-06-08 23:31:12,685 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:31:12,885 - INFO - Request Parameters - Page 3:
2025-06-08 23:31:12,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:12,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:13,623 - INFO - Response - Page 3:
2025-06-08 23:31:13,623 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:31:13,827 - INFO - Request Parameters - Page 4:
2025-06-08 23:31:13,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:13,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:14,605 - INFO - Response - Page 4:
2025-06-08 23:31:14,605 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:31:14,808 - INFO - Request Parameters - Page 5:
2025-06-08 23:31:14,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:14,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:15,622 - INFO - Response - Page 5:
2025-06-08 23:31:15,622 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:31:15,823 - INFO - Request Parameters - Page 6:
2025-06-08 23:31:15,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:15,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:16,696 - INFO - Response - Page 6:
2025-06-08 23:31:16,697 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:31:16,902 - INFO - Request Parameters - Page 7:
2025-06-08 23:31:16,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:16,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:17,587 - INFO - Response - Page 7:
2025-06-08 23:31:17,587 - INFO - 第 7 页获取到 24 条记录
2025-06-08 23:31:17,789 - INFO - 查询完成，共获取到 624 条记录
2025-06-08 23:31:17,789 - INFO - 获取到 624 条表单数据
2025-06-08 23:31:17,789 - INFO - 当前日期 2025-05-05 有 17 条MySQL数据需要处理
2025-06-08 23:31:17,789 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1M4AV13PRCC4SK83A362C33I3AWGAM37
2025-06-08 23:31:18,182 - INFO - 更新表单数据成功: FINST-DO566BD1M4AV13PRCC4SK83A362C33I3AWGAM37
2025-06-08 23:31:18,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2247.7, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2.0, 'new_value': 2249.7}]
2025-06-08 23:31:18,182 - INFO - 开始更新记录 - 表单实例ID: FINST-FPB66VB1F48VHYMPC7NHODUIBWAC2WK7AWGAM2J
2025-06-08 23:31:18,571 - INFO - 更新表单数据成功: FINST-FPB66VB1F48VHYMPC7NHODUIBWAC2WK7AWGAM2J
2025-06-08 23:31:18,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 311.0, 'new_value': 272.0}]
2025-06-08 23:31:18,571 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM2E
2025-06-08 23:31:19,024 - INFO - 更新表单数据成功: FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM2E
2025-06-08 23:31:19,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5027.8, 'new_value': 5027.86}, {'field': 'total_amount', 'old_value': 5027.8, 'new_value': 5027.86}]
2025-06-08 23:31:19,024 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM9F
2025-06-08 23:31:19,401 - INFO - 更新表单数据成功: FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM9F
2025-06-08 23:31:19,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105793.0, 'new_value': 104993.6}, {'field': 'total_amount', 'old_value': 105793.0, 'new_value': 104993.6}]
2025-06-08 23:31:19,401 - INFO - 开始更新记录 - 表单实例ID: FINST-68E66TC118AVV8TWDYFUICYZCP1H2BOBAWGAMB4
2025-06-08 23:31:19,770 - INFO - 更新表单数据成功: FINST-68E66TC118AVV8TWDYFUICYZCP1H2BOBAWGAMB4
2025-06-08 23:31:19,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1999.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1999.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:31:19,770 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMJF
2025-06-08 23:31:20,255 - INFO - 更新表单数据成功: FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMJF
2025-06-08 23:31:20,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2510.1, 'new_value': 2570.1}, {'field': 'total_amount', 'old_value': 2510.1, 'new_value': 2570.1}]
2025-06-08 23:31:20,255 - INFO - 开始更新记录 - 表单实例ID: FINST-68E66TC118AVV8TWDYFUICYZCP1H2BOBAWGAMI4
2025-06-08 23:31:20,615 - INFO - 更新表单数据成功: FINST-68E66TC118AVV8TWDYFUICYZCP1H2BOBAWGAMI4
2025-06-08 23:31:20,615 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32037.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 28048.0, 'new_value': 60085.9}]
2025-06-08 23:31:20,615 - INFO - 开始更新记录 - 表单实例ID: FINST-68E66TC118AVV8TWDYFUICYZCP1H2BOBAWGAM35
2025-06-08 23:31:21,061 - INFO - 更新表单数据成功: FINST-68E66TC118AVV8TWDYFUICYZCP1H2BOBAWGAM35
2025-06-08 23:31:21,061 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6345.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4903.9, 'new_value': 11249.5}]
2025-06-08 23:31:21,062 - INFO - 日期 2025-05-05 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-06-08 23:31:21,062 - INFO - 开始处理日期: 2025-05-06
2025-06-08 23:31:21,062 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:21,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:21,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:21,911 - INFO - Response - Page 1:
2025-06-08 23:31:21,911 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:22,114 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:22,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:22,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:22,931 - INFO - Response - Page 2:
2025-06-08 23:31:22,931 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:31:23,131 - INFO - Request Parameters - Page 3:
2025-06-08 23:31:23,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:23,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:23,894 - INFO - Response - Page 3:
2025-06-08 23:31:23,894 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:31:24,094 - INFO - Request Parameters - Page 4:
2025-06-08 23:31:24,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:24,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:24,880 - INFO - Response - Page 4:
2025-06-08 23:31:24,880 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:31:25,080 - INFO - Request Parameters - Page 5:
2025-06-08 23:31:25,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:25,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:25,857 - INFO - Response - Page 5:
2025-06-08 23:31:25,857 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:31:26,058 - INFO - Request Parameters - Page 6:
2025-06-08 23:31:26,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:26,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:26,822 - INFO - Response - Page 6:
2025-06-08 23:31:26,822 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:31:27,022 - INFO - Request Parameters - Page 7:
2025-06-08 23:31:27,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:27,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746460800000, 1746547199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:27,607 - INFO - Response - Page 7:
2025-06-08 23:31:27,607 - INFO - 第 7 页获取到 22 条记录
2025-06-08 23:31:27,807 - INFO - 查询完成，共获取到 622 条记录
2025-06-08 23:31:27,807 - INFO - 获取到 622 条表单数据
2025-06-08 23:31:27,819 - INFO - 当前日期 2025-05-06 有 17 条MySQL数据需要处理
2025-06-08 23:31:27,819 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1W9AVDJ0G9JTUO4PCGA523PAMAWGAMV2
2025-06-08 23:31:28,218 - INFO - 更新表单数据成功: FINST-BCC66FB1W9AVDJ0G9JTUO4PCGA523PAMAWGAMV2
2025-06-08 23:31:28,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 69.0}]
2025-06-08 23:31:28,219 - INFO - 开始更新记录 - 表单实例ID: FINST-U89668713T7VT52QDEZ2U7CNTYQ23FHUAWGAMLJ
2025-06-08 23:31:28,607 - INFO - 更新表单数据成功: FINST-U89668713T7VT52QDEZ2U7CNTYQ23FHUAWGAMLJ
2025-06-08 23:31:28,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 799.0, 'new_value': 8875.0}, {'field': 'total_amount', 'old_value': 799.0, 'new_value': 8875.0}]
2025-06-08 23:31:28,607 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66881EB8V1ROBAO6RFDX1Q2NQ30JYAWGAMQK
2025-06-08 23:31:29,054 - INFO - 更新表单数据成功: FINST-LLF66881EB8V1ROBAO6RFDX1Q2NQ30JYAWGAMQK
2025-06-08 23:31:29,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4037.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3138.9, 'new_value': 7176.7}]
2025-06-08 23:31:29,054 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66881EB8V1ROBAO6RFDX1Q2NQ30JYAWGAM0L
2025-06-08 23:31:29,480 - INFO - 更新表单数据成功: FINST-LLF66881EB8V1ROBAO6RFDX1Q2NQ30JYAWGAM0L
2025-06-08 23:31:29,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61593.0, 'new_value': 61080.0}, {'field': 'total_amount', 'old_value': 61593.0, 'new_value': 61080.0}]
2025-06-08 23:31:29,480 - INFO - 日期 2025-05-06 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-08 23:31:29,480 - INFO - 开始处理日期: 2025-05-07
2025-06-08 23:31:29,480 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:29,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:29,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:30,277 - INFO - Response - Page 1:
2025-06-08 23:31:30,277 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:30,480 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:30,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:30,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:31,284 - INFO - Response - Page 2:
2025-06-08 23:31:31,284 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:31:31,484 - INFO - Request Parameters - Page 3:
2025-06-08 23:31:31,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:31,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:32,283 - INFO - Response - Page 3:
2025-06-08 23:31:32,283 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:31:32,486 - INFO - Request Parameters - Page 4:
2025-06-08 23:31:32,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:32,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:33,251 - INFO - Response - Page 4:
2025-06-08 23:31:33,251 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:31:33,451 - INFO - Request Parameters - Page 5:
2025-06-08 23:31:33,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:33,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:34,206 - INFO - Response - Page 5:
2025-06-08 23:31:34,206 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:31:34,416 - INFO - Request Parameters - Page 6:
2025-06-08 23:31:34,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:34,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:35,171 - INFO - Response - Page 6:
2025-06-08 23:31:35,172 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:31:35,379 - INFO - Request Parameters - Page 7:
2025-06-08 23:31:35,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:35,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746547200000, 1746633599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:35,998 - INFO - Response - Page 7:
2025-06-08 23:31:35,998 - INFO - 第 7 页获取到 21 条记录
2025-06-08 23:31:36,199 - INFO - 查询完成，共获取到 621 条记录
2025-06-08 23:31:36,199 - INFO - 获取到 621 条表单数据
2025-06-08 23:31:36,209 - INFO - 当前日期 2025-05-07 有 17 条MySQL数据需要处理
2025-06-08 23:31:36,210 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66KD1379VUL788B6K67MOW6JC2XV8BWGAMGN
2025-06-08 23:31:36,664 - INFO - 更新表单数据成功: FINST-2PF66KD1379VUL788B6K67MOW6JC2XV8BWGAMGN
2025-06-08 23:31:36,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2850.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4242.0, 'new_value': 7092.0}]
2025-06-08 23:31:36,664 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF662B1B3AV7ZED6Q1X25PJWFH92CYCBWGAMT7
2025-06-08 23:31:37,160 - INFO - 更新表单数据成功: FINST-MLF662B1B3AV7ZED6Q1X25PJWFH92CYCBWGAMT7
2025-06-08 23:31:37,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6328.0, 'new_value': 6328.8}, {'field': 'total_amount', 'old_value': 6328.0, 'new_value': 6328.8}]
2025-06-08 23:31:37,161 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3ZXWK1EAM3I1
2025-06-08 23:31:37,651 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3ZXWK1EAM3I1
2025-06-08 23:31:37,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2928.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3292.0, 'new_value': 6220.0}]
2025-06-08 23:31:37,651 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF662B1B3AV7ZED6Q1X25PJWFH92CYCBWGAM39
2025-06-08 23:31:38,119 - INFO - 更新表单数据成功: FINST-MLF662B1B3AV7ZED6Q1X25PJWFH92CYCBWGAM39
2025-06-08 23:31:38,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4273.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3607.1, 'new_value': 7880.7}]
2025-06-08 23:31:38,119 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF662B1B3AV7ZED6Q1X25PJWFH92DYCBWGAMS9
2025-06-08 23:31:38,557 - INFO - 更新表单数据成功: FINST-MLF662B1B3AV7ZED6Q1X25PJWFH92DYCBWGAMS9
2025-06-08 23:31:38,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49789.0, 'new_value': 49006.67}, {'field': 'total_amount', 'old_value': 49789.0, 'new_value': 49006.67}]
2025-06-08 23:31:38,557 - INFO - 开始更新记录 - 表单实例ID: FINST-U8966871TS4VPJJBAUD35847H4L42S741PEAMZR
2025-06-08 23:31:38,979 - INFO - 更新表单数据成功: FINST-U8966871TS4VPJJBAUD35847H4L42S741PEAMZR
2025-06-08 23:31:38,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2100.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2100.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:31:38,979 - INFO - 日期 2025-05-07 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-06-08 23:31:38,979 - INFO - 开始处理日期: 2025-05-08
2025-06-08 23:31:38,979 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:38,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:38,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:39,854 - INFO - Response - Page 1:
2025-06-08 23:31:39,854 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:40,057 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:40,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:40,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:40,760 - INFO - Response - Page 2:
2025-06-08 23:31:40,760 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:31:40,963 - INFO - Request Parameters - Page 3:
2025-06-08 23:31:40,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:40,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:41,760 - INFO - Response - Page 3:
2025-06-08 23:31:41,760 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:31:41,963 - INFO - Request Parameters - Page 4:
2025-06-08 23:31:41,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:41,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:42,713 - INFO - Response - Page 4:
2025-06-08 23:31:42,713 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:31:42,916 - INFO - Request Parameters - Page 5:
2025-06-08 23:31:42,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:42,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:43,666 - INFO - Response - Page 5:
2025-06-08 23:31:43,666 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:31:43,869 - INFO - Request Parameters - Page 6:
2025-06-08 23:31:43,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:43,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:44,666 - INFO - Response - Page 6:
2025-06-08 23:31:44,666 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:31:44,869 - INFO - Request Parameters - Page 7:
2025-06-08 23:31:44,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:44,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746633600000, 1746719999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:45,526 - INFO - Response - Page 7:
2025-06-08 23:31:45,526 - INFO - 第 7 页获取到 21 条记录
2025-06-08 23:31:45,729 - INFO - 查询完成，共获取到 621 条记录
2025-06-08 23:31:45,729 - INFO - 获取到 621 条表单数据
2025-06-08 23:31:45,729 - INFO - 当前日期 2025-05-08 有 17 条MySQL数据需要处理
2025-06-08 23:31:45,729 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66BA1K09V2DNQBVE0K6CX8PLB3BEGWEFAM23
2025-06-08 23:31:46,182 - INFO - 更新表单数据成功: FINST-OIF66BA1K09V2DNQBVE0K6CX8PLB3BEGWEFAM23
2025-06-08 23:31:46,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 849.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 849.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:31:46,182 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1AZ9VEKJL7R63K8BWFT8R3BBNBWGAMDA
2025-06-08 23:31:46,604 - INFO - 更新表单数据成功: FINST-OIF66RB1AZ9VEKJL7R63K8BWFT8R3BBNBWGAMDA
2025-06-08 23:31:46,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 915.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3.0, 'new_value': 918.0}]
2025-06-08 23:31:46,604 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ31ERBWGAMR2
2025-06-08 23:31:46,994 - INFO - 更新表单数据成功: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ31ERBWGAMR2
2025-06-08 23:31:46,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2128.0, 'new_value': 2128.1}, {'field': 'total_amount', 'old_value': 2128.0, 'new_value': 2128.1}]
2025-06-08 23:31:46,994 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ31ERBWGAMZ2
2025-06-08 23:31:47,573 - INFO - 更新表单数据成功: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ31ERBWGAMZ2
2025-06-08 23:31:47,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 499.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3129.0, 'new_value': 3628.0}]
2025-06-08 23:31:47,573 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ31ERBWGAMP3
2025-06-08 23:31:47,991 - INFO - 更新表单数据成功: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ31ERBWGAMP3
2025-06-08 23:31:47,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 918.4, 'new_value': 918.7}, {'field': 'total_amount', 'old_value': 918.4, 'new_value': 918.7}]
2025-06-08 23:31:47,991 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ32ERBWGAMD4
2025-06-08 23:31:48,398 - INFO - 更新表单数据成功: FINST-VOC66Y91P3AVE1A2CSMPR7K4SPQQ32ERBWGAMD4
2025-06-08 23:31:48,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3197.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3197.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:31:48,398 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691I38VUZX07E7WDBDI9KSY24GVBWGAM9Q
2025-06-08 23:31:48,826 - INFO - 更新表单数据成功: FINST-6PF66691I38VUZX07E7WDBDI9KSY24GVBWGAM9Q
2025-06-08 23:31:48,826 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3564.4, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3452.0, 'new_value': 7016.4}]
2025-06-08 23:31:48,826 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691I38VUZX07E7WDBDI9KSY24GVBWGAM1S
2025-06-08 23:31:49,355 - INFO - 更新表单数据成功: FINST-6PF66691I38VUZX07E7WDBDI9KSY24GVBWGAM1S
2025-06-08 23:31:49,356 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4611.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4363.0, 'new_value': 8974.0}]
2025-06-08 23:31:49,356 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691I38VUZX07E7WDBDI9KSY24GVBWGAM8S
2025-06-08 23:31:49,786 - INFO - 更新表单数据成功: FINST-6PF66691I38VUZX07E7WDBDI9KSY24GVBWGAM8S
2025-06-08 23:31:49,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61849.0, 'new_value': 61421.4}, {'field': 'total_amount', 'old_value': 61849.0, 'new_value': 61421.4}]
2025-06-08 23:31:49,786 - INFO - 日期 2025-05-08 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-06-08 23:31:49,786 - INFO - 开始处理日期: 2025-05-09
2025-06-08 23:31:49,787 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:49,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:49,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:50,620 - INFO - Response - Page 1:
2025-06-08 23:31:50,620 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:50,821 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:50,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:50,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:51,626 - INFO - Response - Page 2:
2025-06-08 23:31:51,626 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:31:51,835 - INFO - Request Parameters - Page 3:
2025-06-08 23:31:51,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:51,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:52,695 - INFO - Response - Page 3:
2025-06-08 23:31:52,695 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:31:52,898 - INFO - Request Parameters - Page 4:
2025-06-08 23:31:52,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:52,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:53,710 - INFO - Response - Page 4:
2025-06-08 23:31:53,710 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:31:53,914 - INFO - Request Parameters - Page 5:
2025-06-08 23:31:53,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:53,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:54,661 - INFO - Response - Page 5:
2025-06-08 23:31:54,661 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:31:54,864 - INFO - Request Parameters - Page 6:
2025-06-08 23:31:54,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:54,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746720000000, 1746806399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:55,614 - INFO - Response - Page 6:
2025-06-08 23:31:55,614 - INFO - 第 6 页获取到 51 条记录
2025-06-08 23:31:55,815 - INFO - 查询完成，共获取到 551 条记录
2025-06-08 23:31:55,815 - INFO - 获取到 551 条表单数据
2025-06-08 23:31:55,825 - INFO - 当前日期 2025-05-09 有 17 条MySQL数据需要处理
2025-06-08 23:31:55,825 - INFO - 开始更新记录 - 表单实例ID: FINST-S8866981659V2J2Z6KQSY9Y46AOF3CAUCWGAMM4
2025-06-08 23:31:56,226 - INFO - 更新表单数据成功: FINST-S8866981659V2J2Z6KQSY9Y46AOF3CAUCWGAMM4
2025-06-08 23:31:56,226 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 446.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3.0, 'new_value': 449.0}]
2025-06-08 23:31:56,226 - INFO - 开始更新记录 - 表单实例ID: FINST-S8866981659V2J2Z6KQSY9Y46AOF3CAUCWGAMD5
2025-06-08 23:31:56,601 - INFO - 更新表单数据成功: FINST-S8866981659V2J2Z6KQSY9Y46AOF3CAUCWGAMD5
2025-06-08 23:31:56,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 950.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 300.0, 'new_value': 1250.0}]
2025-06-08 23:31:56,601 - INFO - 开始更新记录 - 表单实例ID: FINST-S8866981659V2J2Z6KQSY9Y46AOF3CAUCWGAM76
2025-06-08 23:31:57,006 - INFO - 更新表单数据成功: FINST-S8866981659V2J2Z6KQSY9Y46AOF3CAUCWGAM76
2025-06-08 23:31:57,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2400.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2400.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:31:57,006 - INFO - 开始更新记录 - 表单实例ID: FINST-2HF66O61IDZUMIRPA2LMHAPD3EYX2T9YCWGAMK23
2025-06-08 23:31:57,423 - INFO - 更新表单数据成功: FINST-2HF66O61IDZUMIRPA2LMHAPD3EYX2T9YCWGAMK23
2025-06-08 23:31:57,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4373.4, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2848.0, 'new_value': 7221.4}]
2025-06-08 23:31:57,424 - INFO - 开始更新记录 - 表单实例ID: FINST-6IF66PC1X4AVRM9N6XEJ7D6WGL1621T7KEJAMXE
2025-06-08 23:31:57,948 - INFO - 更新表单数据成功: FINST-6IF66PC1X4AVRM9N6XEJ7D6WGL1621T7KEJAMXE
2025-06-08 23:31:57,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54484.0, 'new_value': 54225.0}, {'field': 'total_amount', 'old_value': 54484.0, 'new_value': 54225.0}]
2025-06-08 23:31:57,949 - INFO - 开始更新记录 - 表单实例ID: FINST-OLC66Z61QS8VYTDUBQYD46GCJEA42DT03MHAM06
2025-06-08 23:31:58,333 - INFO - 更新表单数据成功: FINST-OLC66Z61QS8VYTDUBQYD46GCJEA42DT03MHAM06
2025-06-08 23:31:58,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5220.4, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3954.4, 'new_value': 9174.8}]
2025-06-08 23:31:58,333 - INFO - 日期 2025-05-09 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-06-08 23:31:58,333 - INFO - 开始处理日期: 2025-05-10
2025-06-08 23:31:58,333 - INFO - Request Parameters - Page 1:
2025-06-08 23:31:58,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:58,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:31:59,114 - INFO - Response - Page 1:
2025-06-08 23:31:59,114 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:31:59,318 - INFO - Request Parameters - Page 2:
2025-06-08 23:31:59,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:31:59,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:00,028 - INFO - Response - Page 2:
2025-06-08 23:32:00,028 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:00,230 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:00,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:00,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:00,990 - INFO - Response - Page 3:
2025-06-08 23:32:00,990 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:01,191 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:01,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:01,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:02,186 - INFO - Response - Page 4:
2025-06-08 23:32:02,186 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:02,389 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:02,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:02,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:03,199 - INFO - Response - Page 5:
2025-06-08 23:32:03,199 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:03,402 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:03,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:03,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746806400000, 1746892799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:04,089 - INFO - Response - Page 6:
2025-06-08 23:32:04,089 - INFO - 第 6 页获取到 40 条记录
2025-06-08 23:32:04,292 - INFO - 查询完成，共获取到 540 条记录
2025-06-08 23:32:04,292 - INFO - 获取到 540 条表单数据
2025-06-08 23:32:04,292 - INFO - 当前日期 2025-05-10 有 17 条MySQL数据需要处理
2025-06-08 23:32:04,292 - INFO - 开始更新记录 - 表单实例ID: FINST-XMC66R913QZU9UNO7WRPNB0YEGRK2EUOKEJAMVU
2025-06-08 23:32:04,683 - INFO - 更新表单数据成功: FINST-XMC66R913QZU9UNO7WRPNB0YEGRK2EUOKEJAMVU
2025-06-08 23:32:04,683 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1836.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5.0, 'new_value': 1841.0}]
2025-06-08 23:32:04,683 - INFO - 开始更新记录 - 表单实例ID: FINST-5A966081A9ZUQ9RZBX1PK94EAC1920XSKEJAM5R1
2025-06-08 23:32:05,105 - INFO - 更新表单数据成功: FINST-5A966081A9ZUQ9RZBX1PK94EAC1920XSKEJAM5R1
2025-06-08 23:32:05,105 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1968.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2422.0, 'new_value': 4390.3}]
2025-06-08 23:32:05,105 - INFO - 开始更新记录 - 表单实例ID: FINST-5A966081A9ZUQ9RZBX1PK94EAC1920XSKEJAMKS1
2025-06-08 23:32:05,558 - INFO - 更新表单数据成功: FINST-5A966081A9ZUQ9RZBX1PK94EAC1920XSKEJAMKS1
2025-06-08 23:32:05,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3769.1, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3769.1}]
2025-06-08 23:32:05,558 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66KA1H6AVI1MMFGCN37565QH021U0YDIAMHI
2025-06-08 23:32:06,011 - INFO - 更新表单数据成功: FINST-1PF66KA1H6AVI1MMFGCN37565QH021U0YDIAMHI
2025-06-08 23:32:06,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2895.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2895.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:32:06,011 - INFO - 开始更新记录 - 表单实例ID: FINST-5A966081A9ZUQ9RZBX1PK94EAC1920XSKEJAM4T1
2025-06-08 23:32:06,495 - INFO - 更新表单数据成功: FINST-5A966081A9ZUQ9RZBX1PK94EAC1920XSKEJAM4T1
2025-06-08 23:32:06,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3168.0, 'new_value': 3168.31}, {'field': 'total_amount', 'old_value': 3168.0, 'new_value': 3168.31}]
2025-06-08 23:32:06,495 - INFO - 开始更新记录 - 表单实例ID: FINST-2S666NA1O29VTOSZA2TX1853WSPS34ZWKEJAMPK
2025-06-08 23:32:06,980 - INFO - 更新表单数据成功: FINST-2S666NA1O29VTOSZA2TX1853WSPS34ZWKEJAMPK
2025-06-08 23:32:06,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102111.0, 'new_value': 101642.0}, {'field': 'total_amount', 'old_value': 102111.0, 'new_value': 101642.0}]
2025-06-08 23:32:06,980 - INFO - 开始更新记录 - 表单实例ID: FINST-2S666NA1O29VTOSZA2TX1853WSPS34ZWKEJAMDL
2025-06-08 23:32:07,433 - INFO - 更新表单数据成功: FINST-2S666NA1O29VTOSZA2TX1853WSPS34ZWKEJAMDL
2025-06-08 23:32:07,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6167.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5213.5, 'new_value': 11381.3}]
2025-06-08 23:32:07,433 - INFO - 日期 2025-05-10 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-08 23:32:07,433 - INFO - 开始处理日期: 2025-05-11
2025-06-08 23:32:07,433 - INFO - Request Parameters - Page 1:
2025-06-08 23:32:07,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:07,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:08,198 - INFO - Response - Page 1:
2025-06-08 23:32:08,198 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:32:08,401 - INFO - Request Parameters - Page 2:
2025-06-08 23:32:08,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:08,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:09,136 - INFO - Response - Page 2:
2025-06-08 23:32:09,136 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:09,339 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:09,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:09,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:10,151 - INFO - Response - Page 3:
2025-06-08 23:32:10,151 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:10,355 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:10,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:10,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:11,214 - INFO - Response - Page 4:
2025-06-08 23:32:11,214 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:11,414 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:11,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:11,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:12,199 - INFO - Response - Page 5:
2025-06-08 23:32:12,199 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:12,402 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:12,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:12,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746892800000, 1746979199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:13,184 - INFO - Response - Page 6:
2025-06-08 23:32:13,184 - INFO - 第 6 页获取到 92 条记录
2025-06-08 23:32:13,387 - INFO - 查询完成，共获取到 592 条记录
2025-06-08 23:32:13,387 - INFO - 获取到 592 条表单数据
2025-06-08 23:32:13,387 - INFO - 当前日期 2025-05-11 有 17 条MySQL数据需要处理
2025-06-08 23:32:13,387 - INFO - 开始更新记录 - 表单实例ID: FINST-FD966QA1S4AVOB9K91ZAN70D31513QVX0HKAMJ3
2025-06-08 23:32:13,887 - INFO - 更新表单数据成功: FINST-FD966QA1S4AVOB9K91ZAN70D31513QVX0HKAMJ3
2025-06-08 23:32:13,887 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3233.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 9.0, 'new_value': 3242.0}]
2025-06-08 23:32:13,887 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3XNR9TJAMK11
2025-06-08 23:32:14,277 - INFO - 更新表单数据成功: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3XNR9TJAMK11
2025-06-08 23:32:14,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9132.0, 'new_value': 9132.93}, {'field': 'total_amount', 'old_value': 9132.0, 'new_value': 9132.93}]
2025-06-08 23:32:14,277 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAMQ11
2025-06-08 23:32:14,777 - INFO - 更新表单数据成功: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAMQ11
2025-06-08 23:32:14,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 548.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 14775.0, 'new_value': 15323.0}]
2025-06-08 23:32:14,777 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAM221
2025-06-08 23:32:15,253 - INFO - 更新表单数据成功: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAM221
2025-06-08 23:32:15,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2500.0, 'new_value': 2255.0}, {'field': 'total_amount', 'old_value': 2500.0, 'new_value': 2255.0}]
2025-06-08 23:32:15,254 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAMI31
2025-06-08 23:32:15,746 - INFO - 更新表单数据成功: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAMI31
2025-06-08 23:32:15,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125167.0, 'new_value': 124276.07}, {'field': 'total_amount', 'old_value': 125167.0, 'new_value': 124276.07}]
2025-06-08 23:32:15,746 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAMK31
2025-06-08 23:32:16,236 - INFO - 更新表单数据成功: FINST-90E66JD1R98VH2EI7VBEOAH4KMYR3YNR9TJAMK31
2025-06-08 23:32:16,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6351.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 7094.6, 'new_value': 13446.4}]
2025-06-08 23:32:16,237 - INFO - 开始更新记录 - 表单实例ID: FINST-RDC668B1HMZUI2CB830VJCVD553F223LUEKAMSM1
2025-06-08 23:32:16,640 - INFO - 更新表单数据成功: FINST-RDC668B1HMZUI2CB830VJCVD553F223LUEKAMSM1
2025-06-08 23:32:16,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3588.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3588.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:32:16,640 - INFO - 日期 2025-05-11 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-08 23:32:16,640 - INFO - 开始处理日期: 2025-05-12
2025-06-08 23:32:16,640 - INFO - Request Parameters - Page 1:
2025-06-08 23:32:16,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:16,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:17,459 - INFO - Response - Page 1:
2025-06-08 23:32:17,459 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:32:17,672 - INFO - Request Parameters - Page 2:
2025-06-08 23:32:17,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:17,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:18,468 - INFO - Response - Page 2:
2025-06-08 23:32:18,468 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:18,671 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:18,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:18,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:19,405 - INFO - Response - Page 3:
2025-06-08 23:32:19,405 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:19,608 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:19,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:19,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:20,405 - INFO - Response - Page 4:
2025-06-08 23:32:20,405 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:20,608 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:20,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:20,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:21,327 - INFO - Response - Page 5:
2025-06-08 23:32:21,327 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:21,530 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:21,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:21,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:22,296 - INFO - Response - Page 6:
2025-06-08 23:32:22,296 - INFO - 第 6 页获取到 99 条记录
2025-06-08 23:32:22,499 - INFO - 查询完成，共获取到 599 条记录
2025-06-08 23:32:22,499 - INFO - 获取到 599 条表单数据
2025-06-08 23:32:22,499 - INFO - 当前日期 2025-05-12 有 17 条MySQL数据需要处理
2025-06-08 23:32:22,499 - INFO - 开始更新记录 - 表单实例ID: FINST-MKF66PA114DVDD43CGNKJDWH2DXA2K6XDWLAMDA
2025-06-08 23:32:22,952 - INFO - 更新表单数据成功: FINST-MKF66PA114DVDD43CGNKJDWH2DXA2K6XDWLAMDA
2025-06-08 23:32:22,952 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3778.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3608.3, 'new_value': 7387.1}]
2025-06-08 23:32:22,952 - INFO - 开始更新记录 - 表单实例ID: FINST-MKF66PA114DVDD43CGNKJDWH2DXA2K6XDWLAMJA
2025-06-08 23:32:23,405 - INFO - 更新表单数据成功: FINST-MKF66PA114DVDD43CGNKJDWH2DXA2K6XDWLAMJA
2025-06-08 23:32:23,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40903.0, 'new_value': 40490.5}, {'field': 'total_amount', 'old_value': 40903.0, 'new_value': 40490.5}]
2025-06-08 23:32:23,405 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81Y4DVRPG6AXOY97OJ0LD0366VBWLAMG8
2025-06-08 23:32:23,780 - INFO - 更新表单数据成功: FINST-NU966I81Y4DVRPG6AXOY97OJ0LD0366VBWLAMG8
2025-06-08 23:32:23,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1109.0, 'new_value': 1109.9}, {'field': 'total_amount', 'old_value': 1109.0, 'new_value': 1109.9}]
2025-06-08 23:32:23,780 - INFO - 开始更新记录 - 表单实例ID: FINST-RDC668B1K8BVWCKRFS1AJ8J9XPPD3C8ZBWLAMYA
2025-06-08 23:32:24,249 - INFO - 更新表单数据成功: FINST-RDC668B1K8BVWCKRFS1AJ8J9XPPD3C8ZBWLAMYA
2025-06-08 23:32:24,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 806.8, 'new_value': 4.9}, {'field': 'total_amount', 'old_value': 806.8, 'new_value': 4.9}]
2025-06-08 23:32:24,249 - INFO - 开始更新记录 - 表单实例ID: FINST-RDC668B1K8BVWCKRFS1AJ8J9XPPD3D8ZBWLAMXB
2025-06-08 23:32:24,628 - INFO - 更新表单数据成功: FINST-RDC668B1K8BVWCKRFS1AJ8J9XPPD3D8ZBWLAMXB
2025-06-08 23:32:24,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1680.0, 'new_value': 5157.0}, {'field': 'total_amount', 'old_value': 1680.0, 'new_value': 5157.0}]
2025-06-08 23:32:24,629 - INFO - 日期 2025-05-12 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-08 23:32:24,629 - INFO - 开始处理日期: 2025-05-13
2025-06-08 23:32:24,629 - INFO - Request Parameters - Page 1:
2025-06-08 23:32:24,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:24,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:25,385 - INFO - Response - Page 1:
2025-06-08 23:32:25,386 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:32:25,586 - INFO - Request Parameters - Page 2:
2025-06-08 23:32:25,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:25,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:26,342 - INFO - Response - Page 2:
2025-06-08 23:32:26,342 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:26,544 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:26,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:26,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:27,309 - INFO - Response - Page 3:
2025-06-08 23:32:27,309 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:27,510 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:27,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:27,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:28,298 - INFO - Response - Page 4:
2025-06-08 23:32:28,298 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:28,498 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:28,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:28,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:29,365 - INFO - Response - Page 5:
2025-06-08 23:32:29,366 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:29,566 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:29,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:29,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:30,324 - INFO - Response - Page 6:
2025-06-08 23:32:30,324 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:32:30,524 - INFO - Request Parameters - Page 7:
2025-06-08 23:32:30,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:30,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747065600000, 1747151999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:31,060 - INFO - Response - Page 7:
2025-06-08 23:32:31,060 - INFO - 第 7 页获取到 5 条记录
2025-06-08 23:32:31,264 - INFO - 查询完成，共获取到 605 条记录
2025-06-08 23:32:31,264 - INFO - 获取到 605 条表单数据
2025-06-08 23:32:31,264 - INFO - 当前日期 2025-05-13 有 18 条MySQL数据需要处理
2025-06-08 23:32:31,264 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q71ZWEVKV70BK6BV9QJH22R26ZSP9NAMW1
2025-06-08 23:32:31,670 - INFO - 更新表单数据成功: FINST-OLF66Q71ZWEVKV70BK6BV9QJH22R26ZSP9NAMW1
2025-06-08 23:32:31,670 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 8, 'new_value': 0}]
2025-06-08 23:32:31,685 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q71ZWEVKV70BK6BV9QJH22R26ZSP9NAMH2
2025-06-08 23:32:32,139 - INFO - 更新表单数据成功: FINST-OLF66Q71ZWEVKV70BK6BV9QJH22R26ZSP9NAMH2
2025-06-08 23:32:32,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10265.0, 'new_value': 27035.0}, {'field': 'total_amount', 'old_value': 10265.0, 'new_value': 27035.0}]
2025-06-08 23:32:32,139 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q71ZWEVKV70BK6BV9QJH22R27ZSP9NAMG3
2025-06-08 23:32:32,545 - INFO - 更新表单数据成功: FINST-OLF66Q71ZWEVKV70BK6BV9QJH22R27ZSP9NAMG3
2025-06-08 23:32:32,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 949.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1489.0, 'new_value': 2438.0}]
2025-06-08 23:32:32,545 - INFO - 开始更新记录 - 表单实例ID: FINST-6AG66W814XDV9VMQA9MP199JPHJ4248X5MMAM62
2025-06-08 23:32:32,935 - INFO - 更新表单数据成功: FINST-6AG66W814XDV9VMQA9MP199JPHJ4248X5MMAM62
2025-06-08 23:32:32,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5775.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5775.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:32:32,935 - INFO - 开始更新记录 - 表单实例ID: FINST-4OD66CC1F39VETX398OYI7SN7QSC3Q51Q9NAM91
2025-06-08 23:32:33,389 - INFO - 更新表单数据成功: FINST-4OD66CC1F39VETX398OYI7SN7QSC3Q51Q9NAM91
2025-06-08 23:32:33,389 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4916.2, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3013.5, 'new_value': 7929.7}]
2025-06-08 23:32:33,389 - INFO - 开始更新记录 - 表单实例ID: FINST-487664C1IS7VH52ZB05JND5IEQZP212DO9NAM8H
2025-06-08 23:32:33,826 - INFO - 更新表单数据成功: FINST-487664C1IS7VH52ZB05JND5IEQZP212DO9NAM8H
2025-06-08 23:32:33,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47017.0, 'new_value': 46456.69}, {'field': 'total_amount', 'old_value': 47017.0, 'new_value': 46456.69}]
2025-06-08 23:32:33,826 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA19ZEVTY6FB2B9R76A5PYO2WBAUBNAMM
2025-06-08 23:32:34,322 - INFO - 更新表单数据成功: FINST-VFF66XA19ZEVTY6FB2B9R76A5PYO2WBAUBNAMM
2025-06-08 23:32:34,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2112.0, 'new_value': 2112.5}, {'field': 'total_amount', 'old_value': 2112.0, 'new_value': 2112.5}]
2025-06-08 23:32:34,322 - INFO - 日期 2025-05-13 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-08 23:32:34,322 - INFO - 开始处理日期: 2025-05-14
2025-06-08 23:32:34,323 - INFO - Request Parameters - Page 1:
2025-06-08 23:32:34,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:34,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:35,080 - INFO - Response - Page 1:
2025-06-08 23:32:35,080 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:32:35,280 - INFO - Request Parameters - Page 2:
2025-06-08 23:32:35,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:35,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:36,166 - INFO - Response - Page 2:
2025-06-08 23:32:36,166 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:36,371 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:36,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:36,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:37,147 - INFO - Response - Page 3:
2025-06-08 23:32:37,147 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:37,351 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:37,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:37,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:38,236 - INFO - Response - Page 4:
2025-06-08 23:32:38,236 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:38,439 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:38,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:38,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:39,205 - INFO - Response - Page 5:
2025-06-08 23:32:39,205 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:39,408 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:39,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:39,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:40,151 - INFO - Response - Page 6:
2025-06-08 23:32:40,151 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:32:40,354 - INFO - Request Parameters - Page 7:
2025-06-08 23:32:40,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:40,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:40,900 - INFO - Response - Page 7:
2025-06-08 23:32:40,900 - INFO - 第 7 页获取到 5 条记录
2025-06-08 23:32:41,103 - INFO - 查询完成，共获取到 605 条记录
2025-06-08 23:32:41,103 - INFO - 获取到 605 条表单数据
2025-06-08 23:32:41,103 - INFO - 当前日期 2025-05-14 有 17 条MySQL数据需要处理
2025-06-08 23:32:41,103 - INFO - 开始更新记录 - 表单实例ID: FINST-R1A66H91SFFV4VEV720YB8H2TPSD38AI7ROAMHC
2025-06-08 23:32:41,557 - INFO - 更新表单数据成功: FINST-R1A66H91SFFV4VEV720YB8H2TPSD38AI7ROAMHC
2025-06-08 23:32:41,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47927.0, 'new_value': 47431.06}, {'field': 'total_amount', 'old_value': 47927.0, 'new_value': 47431.06}]
2025-06-08 23:32:41,557 - INFO - 开始更新记录 - 表单实例ID: FINST-R1A66H91SFFV4VEV720YB8H2TPSD38AI7ROAMTC
2025-06-08 23:32:41,932 - INFO - 更新表单数据成功: FINST-R1A66H91SFFV4VEV720YB8H2TPSD38AI7ROAMTC
2025-06-08 23:32:41,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4385.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4270.2, 'new_value': 8656.0}]
2025-06-08 23:32:41,932 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1GDFVVTIOCVVRJBF8Y2FY2CAM7ROAMY7
2025-06-08 23:32:42,388 - INFO - 更新表单数据成功: FINST-7PF66BA1GDFVVTIOCVVRJBF8Y2FY2CAM7ROAMY7
2025-06-08 23:32:42,388 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1954.2, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3568.0, 'new_value': 5522.2}]
2025-06-08 23:32:42,388 - INFO - 开始更新记录 - 表单实例ID: FINST-SI766181SFFVAHDCCJIBK8HHM1KZ128J5ROAMPD
2025-06-08 23:32:42,837 - INFO - 更新表单数据成功: FINST-SI766181SFFVAHDCCJIBK8HHM1KZ128J5ROAMPD
2025-06-08 23:32:42,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2679.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2679.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:32:42,837 - INFO - 日期 2025-05-14 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-06-08 23:32:42,837 - INFO - 开始处理日期: 2025-05-15
2025-06-08 23:32:42,838 - INFO - Request Parameters - Page 1:
2025-06-08 23:32:42,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:42,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:43,664 - INFO - Response - Page 1:
2025-06-08 23:32:43,664 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:32:43,864 - INFO - Request Parameters - Page 2:
2025-06-08 23:32:43,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:43,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:44,581 - INFO - Response - Page 2:
2025-06-08 23:32:44,581 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:44,792 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:44,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:44,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:45,556 - INFO - Response - Page 3:
2025-06-08 23:32:45,556 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:45,759 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:45,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:45,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:46,629 - INFO - Response - Page 4:
2025-06-08 23:32:46,629 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:46,829 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:46,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:46,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:47,555 - INFO - Response - Page 5:
2025-06-08 23:32:47,555 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:47,756 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:47,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:47,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:48,538 - INFO - Response - Page 6:
2025-06-08 23:32:48,538 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:32:48,740 - INFO - Request Parameters - Page 7:
2025-06-08 23:32:48,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:48,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747238400000, 1747324799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:49,312 - INFO - Response - Page 7:
2025-06-08 23:32:49,313 - INFO - 第 7 页获取到 6 条记录
2025-06-08 23:32:49,513 - INFO - 查询完成，共获取到 606 条记录
2025-06-08 23:32:49,513 - INFO - 获取到 606 条表单数据
2025-06-08 23:32:49,523 - INFO - 当前日期 2025-05-15 有 18 条MySQL数据需要处理
2025-06-08 23:32:49,523 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V719XGVL564CSVU57SXRH8V2SXSH4QAMO2
2025-06-08 23:32:49,928 - INFO - 更新表单数据成功: FINST-8PF66V719XGVL564CSVU57SXRH8V2SXSH4QAMO2
2025-06-08 23:32:49,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 289.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 289.0}]
2025-06-08 23:32:49,928 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V719XGVL564CSVU57SXRH8V2SXSH4QAMP2
2025-06-08 23:32:50,340 - INFO - 更新表单数据成功: FINST-8PF66V719XGVL564CSVU57SXRH8V2SXSH4QAMP2
2025-06-08 23:32:50,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11686.0, 'new_value': 11786.0}, {'field': 'total_amount', 'old_value': 11686.0, 'new_value': 11786.0}]
2025-06-08 23:32:50,340 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66E9189GV4UPLDKZTW8S30XYR2L51I4QAMVC
2025-06-08 23:32:50,721 - INFO - 更新表单数据成功: FINST-3PF66E9189GV4UPLDKZTW8S30XYR2L51I4QAMVC
2025-06-08 23:32:50,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2529.0, 'new_value': 2489.2}, {'field': 'total_amount', 'old_value': 2529.0, 'new_value': 2489.2}]
2025-06-08 23:32:50,722 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66E9189GV4UPLDKZTW8S30XYR2L51I4QAMWC
2025-06-08 23:32:51,129 - INFO - 更新表单数据成功: FINST-3PF66E9189GV4UPLDKZTW8S30XYR2L51I4QAMWC
2025-06-08 23:32:51,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1432.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4268.0, 'new_value': 5700.6}]
2025-06-08 23:32:51,129 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66E9189GV4UPLDKZTW8S30XYR2L51I4QAM6F
2025-06-08 23:32:51,549 - INFO - 更新表单数据成功: FINST-3PF66E9189GV4UPLDKZTW8S30XYR2L51I4QAM6F
2025-06-08 23:32:51,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41976.0, 'new_value': 41721.6}, {'field': 'total_amount', 'old_value': 41976.0, 'new_value': 41721.6}]
2025-06-08 23:32:51,550 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF669B1PAGVNS6H6KKOMAPTGFMJ2L85I4QAMOI
2025-06-08 23:32:51,933 - INFO - 更新表单数据成功: FINST-MLF669B1PAGVNS6H6KKOMAPTGFMJ2L85I4QAMOI
2025-06-08 23:32:51,933 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5299.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3988.7, 'new_value': 9288.3}]
2025-06-08 23:32:51,933 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF669B1PAGVNS6H6KKOMAPTGFMJ2M85I4QAMHJ
2025-06-08 23:32:52,326 - INFO - 更新表单数据成功: FINST-MLF669B1PAGVNS6H6KKOMAPTGFMJ2M85I4QAMHJ
2025-06-08 23:32:52,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 998.0, 'new_value': 1853.0}, {'field': 'total_amount', 'old_value': 998.0, 'new_value': 1853.0}]
2025-06-08 23:32:52,326 - INFO - 开始更新记录 - 表单实例ID: FINST-SI766181KWFVOE0W9A1X26CLKJ0638A9I4QAMB3
2025-06-08 23:32:52,728 - INFO - 更新表单数据成功: FINST-SI766181KWFVOE0W9A1X26CLKJ0638A9I4QAMB3
2025-06-08 23:32:52,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 15000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 15000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:32:52,728 - INFO - 日期 2025-05-15 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-06-08 23:32:52,728 - INFO - 开始处理日期: 2025-05-16
2025-06-08 23:32:52,728 - INFO - Request Parameters - Page 1:
2025-06-08 23:32:52,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:52,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:53,662 - INFO - Response - Page 1:
2025-06-08 23:32:53,662 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:32:53,865 - INFO - Request Parameters - Page 2:
2025-06-08 23:32:53,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:53,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:54,644 - INFO - Response - Page 2:
2025-06-08 23:32:54,644 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:32:54,844 - INFO - Request Parameters - Page 3:
2025-06-08 23:32:54,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:54,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:55,597 - INFO - Response - Page 3:
2025-06-08 23:32:55,597 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:32:55,801 - INFO - Request Parameters - Page 4:
2025-06-08 23:32:55,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:55,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:56,642 - INFO - Response - Page 4:
2025-06-08 23:32:56,642 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:32:56,843 - INFO - Request Parameters - Page 5:
2025-06-08 23:32:56,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:56,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:57,645 - INFO - Response - Page 5:
2025-06-08 23:32:57,645 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:32:57,848 - INFO - Request Parameters - Page 6:
2025-06-08 23:32:57,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:32:57,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:32:58,525 - INFO - Response - Page 6:
2025-06-08 23:32:58,526 - INFO - 第 6 页获取到 30 条记录
2025-06-08 23:32:58,726 - INFO - 查询完成，共获取到 530 条记录
2025-06-08 23:32:58,726 - INFO - 获取到 530 条表单数据
2025-06-08 23:32:58,737 - INFO - 当前日期 2025-05-16 有 17 条MySQL数据需要处理
2025-06-08 23:32:58,737 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM37
2025-06-08 23:32:59,186 - INFO - 更新表单数据成功: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM37
2025-06-08 23:32:59,186 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 960.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3.0, 'new_value': 963.0}]
2025-06-08 23:32:59,186 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM67
2025-06-08 23:32:59,604 - INFO - 更新表单数据成功: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM67
2025-06-08 23:32:59,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29.9, 'new_value': 353.63}, {'field': 'total_amount', 'old_value': 29.9, 'new_value': 353.63}]
2025-06-08 23:32:59,604 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG7
2025-06-08 23:33:00,025 - INFO - 更新表单数据成功: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG7
2025-06-08 23:33:00,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1637.0, 'new_value': 137.0}, {'field': 'total_amount', 'old_value': 1637.0, 'new_value': 137.0}]
2025-06-08 23:33:00,025 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM98
2025-06-08 23:33:00,528 - INFO - 更新表单数据成功: FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM98
2025-06-08 23:33:00,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4012.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4832.0, 'new_value': 8844.6}]
2025-06-08 23:33:00,529 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM67
2025-06-08 23:33:00,961 - INFO - 更新表单数据成功: FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM67
2025-06-08 23:33:00,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42174.0, 'new_value': 41994.0}, {'field': 'total_amount', 'old_value': 42174.0, 'new_value': 41994.0}]
2025-06-08 23:33:00,962 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMQ7
2025-06-08 23:33:01,530 - INFO - 更新表单数据成功: FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMQ7
2025-06-08 23:33:01,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4953.5, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4334.8, 'new_value': 9288.3}]
2025-06-08 23:33:01,530 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMJP
2025-06-08 23:33:01,981 - INFO - 更新表单数据成功: FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMJP
2025-06-08 23:33:01,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3578.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3578.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:01,981 - INFO - 日期 2025-05-16 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-08 23:33:01,981 - INFO - 开始处理日期: 2025-05-17
2025-06-08 23:33:01,981 - INFO - Request Parameters - Page 1:
2025-06-08 23:33:01,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:01,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:02,849 - INFO - Response - Page 1:
2025-06-08 23:33:02,849 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:33:03,049 - INFO - Request Parameters - Page 2:
2025-06-08 23:33:03,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:03,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:03,905 - INFO - Response - Page 2:
2025-06-08 23:33:03,905 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:33:04,105 - INFO - Request Parameters - Page 3:
2025-06-08 23:33:04,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:04,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:04,915 - INFO - Response - Page 3:
2025-06-08 23:33:04,915 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:33:05,115 - INFO - Request Parameters - Page 4:
2025-06-08 23:33:05,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:05,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:05,992 - INFO - Response - Page 4:
2025-06-08 23:33:05,992 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:33:06,196 - INFO - Request Parameters - Page 5:
2025-06-08 23:33:06,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:06,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:07,026 - INFO - Response - Page 5:
2025-06-08 23:33:07,026 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:33:07,228 - INFO - Request Parameters - Page 6:
2025-06-08 23:33:07,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:07,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:08,057 - INFO - Response - Page 6:
2025-06-08 23:33:08,057 - INFO - 第 6 页获取到 68 条记录
2025-06-08 23:33:08,261 - INFO - 查询完成，共获取到 568 条记录
2025-06-08 23:33:08,261 - INFO - 获取到 568 条表单数据
2025-06-08 23:33:08,261 - INFO - 当前日期 2025-05-17 有 17 条MySQL数据需要处理
2025-06-08 23:33:08,261 - INFO - 开始更新记录 - 表单实例ID: FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMYE
2025-06-08 23:33:08,692 - INFO - 更新表单数据成功: FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMYE
2025-06-08 23:33:08,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4186.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 6.0, 'new_value': 4192.0}]
2025-06-08 23:33:08,693 - INFO - 开始更新记录 - 表单实例ID: FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM9G
2025-06-08 23:33:09,127 - INFO - 更新表单数据成功: FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM9G
2025-06-08 23:33:09,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1230.0, 'new_value': 9755.0}, {'field': 'total_amount', 'old_value': 1230.0, 'new_value': 9755.0}]
2025-06-08 23:33:09,127 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1ESHVTE2RB2CG6DJWRBCL351UDZSAMIL
2025-06-08 23:33:09,550 - INFO - 更新表单数据成功: FINST-BD766BC1ESHVTE2RB2CG6DJWRBCL351UDZSAMIL
2025-06-08 23:33:09,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6449.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5246.5, 'new_value': 11696.1}]
2025-06-08 23:33:09,551 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1ESHVTE2RB2CG6DJWRBCL351UDZSAMJL
2025-06-08 23:33:10,009 - INFO - 更新表单数据成功: FINST-BD766BC1ESHVTE2RB2CG6DJWRBCL351UDZSAMJL
2025-06-08 23:33:10,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72692.0, 'new_value': 71433.29}, {'field': 'total_amount', 'old_value': 72692.0, 'new_value': 71433.29}]
2025-06-08 23:33:10,010 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66O71GZHVN465D6NO37MF6GVB3YDHO3TAMZE
2025-06-08 23:33:10,410 - INFO - 更新表单数据成功: FINST-3PF66O71GZHVN465D6NO37MF6GVB3YDHO3TAMZE
2025-06-08 23:33:10,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38686.2, 'new_value': 38785.2}, {'field': 'total_amount', 'old_value': 38686.2, 'new_value': 38785.2}]
2025-06-08 23:33:10,411 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66O71GZHVN465D6NO37MF6GVB3YDHO3TAMOF
2025-06-08 23:33:10,775 - INFO - 更新表单数据成功: FINST-3PF66O71GZHVN465D6NO37MF6GVB3YDHO3TAMOF
2025-06-08 23:33:10,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 31888.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 31888.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:10,776 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66O71GZHVN465D6NO37MF6GVB3YDHO3TAMSF
2025-06-08 23:33:11,188 - INFO - 更新表单数据成功: FINST-3PF66O71GZHVN465D6NO37MF6GVB3YDHO3TAMSF
2025-06-08 23:33:11,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3471.0, 'new_value': 3471.5}, {'field': 'total_amount', 'old_value': 3471.0, 'new_value': 3471.5}]
2025-06-08 23:33:11,188 - INFO - 日期 2025-05-17 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-08 23:33:11,188 - INFO - 开始处理日期: 2025-05-18
2025-06-08 23:33:11,188 - INFO - Request Parameters - Page 1:
2025-06-08 23:33:11,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:11,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:12,022 - INFO - Response - Page 1:
2025-06-08 23:33:12,022 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:33:12,225 - INFO - Request Parameters - Page 2:
2025-06-08 23:33:12,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:12,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:13,053 - INFO - Response - Page 2:
2025-06-08 23:33:13,053 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:33:13,257 - INFO - Request Parameters - Page 3:
2025-06-08 23:33:13,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:13,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:14,100 - INFO - Response - Page 3:
2025-06-08 23:33:14,100 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:33:14,303 - INFO - Request Parameters - Page 4:
2025-06-08 23:33:14,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:14,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:15,163 - INFO - Response - Page 4:
2025-06-08 23:33:15,163 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:33:15,366 - INFO - Request Parameters - Page 5:
2025-06-08 23:33:15,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:15,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:16,210 - INFO - Response - Page 5:
2025-06-08 23:33:16,210 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:33:16,413 - INFO - Request Parameters - Page 6:
2025-06-08 23:33:16,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:16,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:17,319 - INFO - Response - Page 6:
2025-06-08 23:33:17,319 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:33:17,522 - INFO - Request Parameters - Page 7:
2025-06-08 23:33:17,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:17,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:18,163 - INFO - Response - Page 7:
2025-06-08 23:33:18,163 - INFO - 第 7 页获取到 19 条记录
2025-06-08 23:33:18,366 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 23:33:18,366 - INFO - 获取到 619 条表单数据
2025-06-08 23:33:18,366 - INFO - 当前日期 2025-05-18 有 17 条MySQL数据需要处理
2025-06-08 23:33:18,366 - INFO - 开始更新记录 - 表单实例ID: FINST-Z7B66WA1PAJV99QUBN17MCGPFW6N3SQLZGUAMBL
2025-06-08 23:33:18,803 - INFO - 更新表单数据成功: FINST-Z7B66WA1PAJV99QUBN17MCGPFW6N3SQLZGUAMBL
2025-06-08 23:33:18,803 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4252.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2.0, 'new_value': 4254.0}]
2025-06-08 23:33:18,803 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V714ZGVKX8E79LL27TAQXD23WTPZGUAM7W
2025-06-08 23:33:19,225 - INFO - 更新表单数据成功: FINST-3PF66V714ZGVKX8E79LL27TAQXD23WTPZGUAM7W
2025-06-08 23:33:19,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23059.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 23800.0, 'new_value': 46859.8}]
2025-06-08 23:33:19,225 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V714ZGVKX8E79LL27TAQXD23WTPZGUAMQW
2025-06-08 23:33:19,725 - INFO - 更新表单数据成功: FINST-3PF66V714ZGVKX8E79LL27TAQXD23WTPZGUAMQW
2025-06-08 23:33:19,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5133.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5133.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:19,725 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V714ZGVKX8E79LL27TAQXD23XTPZGUAMOX
2025-06-08 23:33:20,163 - INFO - 更新表单数据成功: FINST-3PF66V714ZGVKX8E79LL27TAQXD23XTPZGUAMOX
2025-06-08 23:33:20,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6713.7, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5166.9, 'new_value': 11880.6}]
2025-06-08 23:33:20,163 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V714ZGVKX8E79LL27TAQXD23XTPZGUAMVX
2025-06-08 23:33:20,600 - INFO - 更新表单数据成功: FINST-3PF66V714ZGVKX8E79LL27TAQXD23XTPZGUAMVX
2025-06-08 23:33:20,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94170.0, 'new_value': 91474.5}, {'field': 'total_amount', 'old_value': 94170.0, 'new_value': 91474.5}]
2025-06-08 23:33:20,600 - INFO - 开始更新记录 - 表单实例ID: FINST-HXD667B1QIJVRN4T8POJZ9TVTS7L21E0YGUAMRD
2025-06-08 23:33:21,069 - INFO - 更新表单数据成功: FINST-HXD667B1QIJVRN4T8POJZ9TVTS7L21E0YGUAMRD
2025-06-08 23:33:21,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7864.0, 'new_value': 7864.98}, {'field': 'total_amount', 'old_value': 7864.0, 'new_value': 7864.98}]
2025-06-08 23:33:21,069 - INFO - 日期 2025-05-18 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-06-08 23:33:21,069 - INFO - 开始处理日期: 2025-05-19
2025-06-08 23:33:21,069 - INFO - Request Parameters - Page 1:
2025-06-08 23:33:21,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:21,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:21,975 - INFO - Response - Page 1:
2025-06-08 23:33:21,975 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:33:22,178 - INFO - Request Parameters - Page 2:
2025-06-08 23:33:22,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:22,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:22,975 - INFO - Response - Page 2:
2025-06-08 23:33:22,975 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:33:23,178 - INFO - Request Parameters - Page 3:
2025-06-08 23:33:23,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:23,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:24,006 - INFO - Response - Page 3:
2025-06-08 23:33:24,006 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:33:24,207 - INFO - Request Parameters - Page 4:
2025-06-08 23:33:24,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:24,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:24,958 - INFO - Response - Page 4:
2025-06-08 23:33:24,958 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:33:25,163 - INFO - Request Parameters - Page 5:
2025-06-08 23:33:25,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:25,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:25,952 - INFO - Response - Page 5:
2025-06-08 23:33:25,952 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:33:26,152 - INFO - Request Parameters - Page 6:
2025-06-08 23:33:26,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:26,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:26,946 - INFO - Response - Page 6:
2025-06-08 23:33:26,947 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:33:27,147 - INFO - Request Parameters - Page 7:
2025-06-08 23:33:27,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:27,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:27,731 - INFO - Response - Page 7:
2025-06-08 23:33:27,731 - INFO - 第 7 页获取到 10 条记录
2025-06-08 23:33:27,932 - INFO - 查询完成，共获取到 610 条记录
2025-06-08 23:33:27,932 - INFO - 获取到 610 条表单数据
2025-06-08 23:33:27,944 - INFO - 当前日期 2025-05-19 有 17 条MySQL数据需要处理
2025-06-08 23:33:27,944 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMY1
2025-06-08 23:33:28,449 - INFO - 更新表单数据成功: FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMY1
2025-06-08 23:33:28,449 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 487.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 487.0}]
2025-06-08 23:33:28,449 - INFO - 开始更新记录 - 表单实例ID: FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMLD
2025-06-08 23:33:28,891 - INFO - 更新表单数据成功: FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMLD
2025-06-08 23:33:28,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1798.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1798.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:28,892 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM89
2025-06-08 23:33:29,326 - INFO - 更新表单数据成功: FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM89
2025-06-08 23:33:29,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2357.0, 'new_value': 2357.5}, {'field': 'total_amount', 'old_value': 2357.0, 'new_value': 2357.5}]
2025-06-08 23:33:29,327 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAML9
2025-06-08 23:33:29,723 - INFO - 更新表单数据成功: FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAML9
2025-06-08 23:33:29,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5721.7, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3415.0, 'new_value': 9136.7}]
2025-06-08 23:33:29,723 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMEA
2025-06-08 23:33:30,287 - INFO - 更新表单数据成功: FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMEA
2025-06-08 23:33:30,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44598.0, 'new_value': 42922.95}, {'field': 'total_amount', 'old_value': 44598.0, 'new_value': 42922.95}]
2025-06-08 23:33:30,287 - INFO - 开始更新记录 - 表单实例ID: FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPB
2025-06-08 23:33:30,740 - INFO - 更新表单数据成功: FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPB
2025-06-08 23:33:30,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 64959.2}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 64959.2}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:30,740 - INFO - 日期 2025-05-19 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-06-08 23:33:30,740 - INFO - 开始处理日期: 2025-05-20
2025-06-08 23:33:30,740 - INFO - Request Parameters - Page 1:
2025-06-08 23:33:30,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:30,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:31,489 - INFO - Response - Page 1:
2025-06-08 23:33:31,489 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:33:31,689 - INFO - Request Parameters - Page 2:
2025-06-08 23:33:31,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:31,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:32,690 - INFO - Response - Page 2:
2025-06-08 23:33:32,690 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:33:32,901 - INFO - Request Parameters - Page 3:
2025-06-08 23:33:32,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:32,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:33,684 - INFO - Response - Page 3:
2025-06-08 23:33:33,684 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:33:33,887 - INFO - Request Parameters - Page 4:
2025-06-08 23:33:33,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:33,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:34,874 - INFO - Response - Page 4:
2025-06-08 23:33:34,874 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:33:35,074 - INFO - Request Parameters - Page 5:
2025-06-08 23:33:35,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:35,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:35,907 - INFO - Response - Page 5:
2025-06-08 23:33:35,907 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:33:36,107 - INFO - Request Parameters - Page 6:
2025-06-08 23:33:36,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:36,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:36,981 - INFO - Response - Page 6:
2025-06-08 23:33:36,981 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:33:37,181 - INFO - Request Parameters - Page 7:
2025-06-08 23:33:37,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:37,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:37,688 - INFO - Response - Page 7:
2025-06-08 23:33:37,688 - INFO - 第 7 页获取到 3 条记录
2025-06-08 23:33:37,891 - INFO - 查询完成，共获取到 603 条记录
2025-06-08 23:33:37,891 - INFO - 获取到 603 条表单数据
2025-06-08 23:33:37,891 - INFO - 当前日期 2025-05-20 有 17 条MySQL数据需要处理
2025-06-08 23:33:37,891 - INFO - 开始更新记录 - 表单实例ID: FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK1
2025-06-08 23:33:38,396 - INFO - 更新表单数据成功: FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK1
2025-06-08 23:33:38,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 69.0}]
2025-06-08 23:33:38,396 - INFO - 开始更新记录 - 表单实例ID: FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU1
2025-06-08 23:33:38,876 - INFO - 更新表单数据成功: FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU1
2025-06-08 23:33:38,877 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1538.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 8795.0, 'new_value': 10333.6}]
2025-06-08 23:33:38,877 - INFO - 开始更新记录 - 表单实例ID: FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMJ4
2025-06-08 23:33:39,390 - INFO - 更新表单数据成功: FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMJ4
2025-06-08 23:33:39,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6756.1, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3037.0, 'new_value': 9793.1}]
2025-06-08 23:33:39,390 - INFO - 开始更新记录 - 表单实例ID: FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM95
2025-06-08 23:33:39,808 - INFO - 更新表单数据成功: FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM95
2025-06-08 23:33:39,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38783.0, 'new_value': 37878.39}, {'field': 'total_amount', 'old_value': 38783.0, 'new_value': 37878.39}]
2025-06-08 23:33:39,808 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMSB
2025-06-08 23:33:40,230 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMSB
2025-06-08 23:33:40,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4524.0, 'new_value': 4559.46}, {'field': 'total_amount', 'old_value': 4524.0, 'new_value': 4559.46}]
2025-06-08 23:33:40,230 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMTB
2025-06-08 23:33:40,667 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMTB
2025-06-08 23:33:40,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6815.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6815.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:40,667 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:33:40,824 - INFO - 批量插入响应状态码: 200
2025-06-08 23:33:40,824 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:33:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F5612BC6-D32D-7FF0-9B1B-37302537A85F', 'x-acs-trace-id': '56004f317f04c7b0be3d7006cd4ef424', 'etag': '6B3zfaoXIkZDXSFQjNXQlAQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:33:40,824 - INFO - 批量插入响应体: {'result': ['FINST-U89668716C3WEII8EFHINACCMPN52KX5PTNBM1Q']}
2025-06-08 23:33:40,824 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:33:40,824 - INFO - 成功插入的数据ID: ['FINST-U89668716C3WEII8EFHINACCMPN52KX5PTNBM1Q']
2025-06-08 23:33:45,830 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:33:45,830 - INFO - 日期 2025-05-20 处理完成 - 更新: 6 条，插入: 1 条，错误: 0 条
2025-06-08 23:33:45,830 - INFO - 开始处理日期: 2025-05-21
2025-06-08 23:33:45,830 - INFO - Request Parameters - Page 1:
2025-06-08 23:33:45,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:45,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:46,678 - INFO - Response - Page 1:
2025-06-08 23:33:46,678 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:33:46,878 - INFO - Request Parameters - Page 2:
2025-06-08 23:33:46,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:46,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:47,705 - INFO - Response - Page 2:
2025-06-08 23:33:47,705 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:33:47,905 - INFO - Request Parameters - Page 3:
2025-06-08 23:33:47,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:47,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:48,719 - INFO - Response - Page 3:
2025-06-08 23:33:48,719 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:33:48,931 - INFO - Request Parameters - Page 4:
2025-06-08 23:33:48,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:48,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:49,728 - INFO - Response - Page 4:
2025-06-08 23:33:49,728 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:33:49,928 - INFO - Request Parameters - Page 5:
2025-06-08 23:33:49,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:49,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:50,745 - INFO - Response - Page 5:
2025-06-08 23:33:50,745 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:33:50,958 - INFO - Request Parameters - Page 6:
2025-06-08 23:33:50,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:50,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:51,795 - INFO - Response - Page 6:
2025-06-08 23:33:51,795 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:33:52,002 - INFO - Request Parameters - Page 7:
2025-06-08 23:33:52,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:33:52,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:33:52,580 - INFO - Response - Page 7:
2025-06-08 23:33:52,580 - INFO - 第 7 页获取到 4 条记录
2025-06-08 23:33:52,784 - INFO - 查询完成，共获取到 604 条记录
2025-06-08 23:33:52,784 - INFO - 获取到 604 条表单数据
2025-06-08 23:33:52,784 - INFO - 当前日期 2025-05-21 有 17 条MySQL数据需要处理
2025-06-08 23:33:52,784 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC1ZAMVTX896BCB4DODQ2782LHX4PYAMN3
2025-06-08 23:33:53,174 - INFO - 更新表单数据成功: FINST-KLF66WC1ZAMVTX896BCB4DODQ2782LHX4PYAMN3
2025-06-08 23:33:53,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4544.0, 'new_value': 4563.8}]
2025-06-08 23:33:53,174 - INFO - 开始更新记录 - 表单实例ID: FINST-NS866I91ZBMVYG6K8YT3Y7JO378128N55PYAMH9
2025-06-08 23:33:53,627 - INFO - 更新表单数据成功: FINST-NS866I91ZBMVYG6K8YT3Y7JO378128N55PYAMH9
2025-06-08 23:33:53,627 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1855.2, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3188.0, 'new_value': 5043.2}]
2025-06-08 23:33:53,627 - INFO - 开始更新记录 - 表单实例ID: FINST-NS866I91ZBMVYG6K8YT3Y7JO378128N55PYAMZ9
2025-06-08 23:33:54,049 - INFO - 更新表单数据成功: FINST-NS866I91ZBMVYG6K8YT3Y7JO378128N55PYAMZ9
2025-06-08 23:33:54,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4497.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3545.0, 'new_value': 8042.3}]
2025-06-08 23:33:54,049 - INFO - 开始更新记录 - 表单实例ID: FINST-NS866I91ZBMVYG6K8YT3Y7JO378128N55PYAMGA
2025-06-08 23:33:54,508 - INFO - 更新表单数据成功: FINST-NS866I91ZBMVYG6K8YT3Y7JO378128N55PYAMGA
2025-06-08 23:33:54,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33860.0, 'new_value': 33484.65}, {'field': 'total_amount', 'old_value': 33860.0, 'new_value': 33484.65}]
2025-06-08 23:33:54,508 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1E3MV03SEFRVFZCU9AU6Q27P95PYAM3G
2025-06-08 23:33:54,987 - INFO - 更新表单数据成功: FINST-ZNE66RC1E3MV03SEFRVFZCU9AU6Q27P95PYAM3G
2025-06-08 23:33:54,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 28000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 28000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:54,987 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1E3MV03SEFRVFZCU9AU6Q27P95PYAMBG
2025-06-08 23:33:55,393 - INFO - 更新表单数据成功: FINST-ZNE66RC1E3MV03SEFRVFZCU9AU6Q27P95PYAMBG
2025-06-08 23:33:55,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4675.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4675.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:33:55,393 - INFO - 开始更新记录 - 表单实例ID: FINST-CPC66T91Z7MVGZTB7I460DBXFX283AE9ARYAM83
2025-06-08 23:33:55,745 - INFO - 更新表单数据成功: FINST-CPC66T91Z7MVGZTB7I460DBXFX283AE9ARYAM83
2025-06-08 23:33:55,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1304.0, 'new_value': 1301.8}, {'field': 'total_amount', 'old_value': 1304.0, 'new_value': 1301.8}]
2025-06-08 23:33:55,746 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:33:55,892 - INFO - 批量插入响应状态码: 200
2025-06-08 23:33:55,893 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:33:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1BA82286-D007-75E7-9460-1250B2DBDDCD', 'x-acs-trace-id': '66824edbdcdc6c369a7425ab56d91748', 'etag': '6fCO3vx7h+p11fQvOL8e8Dg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:33:55,893 - INFO - 批量插入响应体: {'result': ['FINST-BD766BC1MN4WI6T286V0M6676P6K36KHPTNBMH3']}
2025-06-08 23:33:55,893 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:33:55,893 - INFO - 成功插入的数据ID: ['FINST-BD766BC1MN4WI6T286V0M6676P6K36KHPTNBMH3']
2025-06-08 23:34:00,894 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:34:00,894 - INFO - 日期 2025-05-21 处理完成 - 更新: 7 条，插入: 1 条，错误: 0 条
2025-06-08 23:34:00,894 - INFO - 开始处理日期: 2025-05-22
2025-06-08 23:34:00,894 - INFO - Request Parameters - Page 1:
2025-06-08 23:34:00,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:00,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:01,684 - INFO - Response - Page 1:
2025-06-08 23:34:01,684 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:34:01,887 - INFO - Request Parameters - Page 2:
2025-06-08 23:34:01,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:01,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:02,713 - INFO - Response - Page 2:
2025-06-08 23:34:02,713 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:34:02,916 - INFO - Request Parameters - Page 3:
2025-06-08 23:34:02,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:02,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:03,760 - INFO - Response - Page 3:
2025-06-08 23:34:03,760 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:34:03,963 - INFO - Request Parameters - Page 4:
2025-06-08 23:34:03,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:03,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:04,776 - INFO - Response - Page 4:
2025-06-08 23:34:04,776 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:34:04,979 - INFO - Request Parameters - Page 5:
2025-06-08 23:34:04,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:04,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:05,776 - INFO - Response - Page 5:
2025-06-08 23:34:05,776 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:34:05,979 - INFO - Request Parameters - Page 6:
2025-06-08 23:34:05,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:05,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:06,737 - INFO - Response - Page 6:
2025-06-08 23:34:06,737 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:34:06,937 - INFO - Request Parameters - Page 7:
2025-06-08 23:34:06,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:06,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:07,547 - INFO - Response - Page 7:
2025-06-08 23:34:07,548 - INFO - 第 7 页获取到 22 条记录
2025-06-08 23:34:07,748 - INFO - 查询完成，共获取到 622 条记录
2025-06-08 23:34:07,748 - INFO - 获取到 622 条表单数据
2025-06-08 23:34:07,760 - INFO - 当前日期 2025-05-22 有 17 条MySQL数据需要处理
2025-06-08 23:34:07,761 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMPF
2025-06-08 23:34:08,169 - INFO - 更新表单数据成功: FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMPF
2025-06-08 23:34:08,169 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 380.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 381.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:34:08,169 - INFO - 开始更新记录 - 表单实例ID: FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM1
2025-06-08 23:34:08,629 - INFO - 更新表单数据成功: FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM1
2025-06-08 23:34:08,629 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4150.2, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2800.0, 'new_value': 6950.2}]
2025-06-08 23:34:08,630 - INFO - 开始更新记录 - 表单实例ID: FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM32
2025-06-08 23:34:09,087 - INFO - 更新表单数据成功: FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM32
2025-06-08 23:34:09,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30.0, 'new_value': 50026.0}, {'field': 'total_amount', 'old_value': 30.0, 'new_value': 50026.0}, {'field': 'order_count', 'old_value': 50026, 'new_value': 30}]
2025-06-08 23:34:09,087 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJM
2025-06-08 23:34:09,593 - INFO - 更新表单数据成功: FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJM
2025-06-08 23:34:09,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33807.0, 'new_value': 33434.92}, {'field': 'total_amount', 'old_value': 33807.0, 'new_value': 33434.92}]
2025-06-08 23:34:09,594 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM5N
2025-06-08 23:34:10,004 - INFO - 更新表单数据成功: FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM5N
2025-06-08 23:34:10,005 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3942.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4384.3, 'new_value': 8327.1}]
2025-06-08 23:34:10,005 - INFO - 开始更新记录 - 表单实例ID: FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6G
2025-06-08 23:34:10,437 - INFO - 更新表单数据成功: FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6G
2025-06-08 23:34:10,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5534.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5534.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:34:10,437 - INFO - 开始更新记录 - 表单实例ID: FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBH
2025-06-08 23:34:10,845 - INFO - 更新表单数据成功: FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBH
2025-06-08 23:34:10,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2638.0, 'new_value': 2638.5}, {'field': 'total_amount', 'old_value': 2638.0, 'new_value': 2638.5}]
2025-06-08 23:34:10,845 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:34:11,011 - INFO - 批量插入响应状态码: 200
2025-06-08 23:34:11,011 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:34:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B5E94B41-7330-7C26-899C-A75779BEC5DE', 'x-acs-trace-id': 'e89e31a986f9ada3d647255dc27b20dd', 'etag': '6sctpSMMGxRel7h3NBKT+2g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:34:11,011 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271WJ4WDK7CAS6FH6FB7DAL258TPTNBM4A']}
2025-06-08 23:34:11,011 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:34:11,011 - INFO - 成功插入的数据ID: ['FINST-3PF66271WJ4WDK7CAS6FH6FB7DAL258TPTNBM4A']
2025-06-08 23:34:16,023 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:34:16,023 - INFO - 日期 2025-05-22 处理完成 - 更新: 7 条，插入: 1 条，错误: 0 条
2025-06-08 23:34:16,023 - INFO - 开始处理日期: 2025-05-23
2025-06-08 23:34:16,023 - INFO - Request Parameters - Page 1:
2025-06-08 23:34:16,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:16,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:16,757 - INFO - Response - Page 1:
2025-06-08 23:34:16,757 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:34:16,960 - INFO - Request Parameters - Page 2:
2025-06-08 23:34:16,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:16,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:17,663 - INFO - Response - Page 2:
2025-06-08 23:34:17,663 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:34:17,866 - INFO - Request Parameters - Page 3:
2025-06-08 23:34:17,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:17,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:18,655 - INFO - Response - Page 3:
2025-06-08 23:34:18,655 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:34:18,856 - INFO - Request Parameters - Page 4:
2025-06-08 23:34:18,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:18,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:19,648 - INFO - Response - Page 4:
2025-06-08 23:34:19,648 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:34:19,851 - INFO - Request Parameters - Page 5:
2025-06-08 23:34:19,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:19,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:21,003 - INFO - Response - Page 5:
2025-06-08 23:34:21,003 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:34:21,206 - INFO - Request Parameters - Page 6:
2025-06-08 23:34:21,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:21,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:21,908 - INFO - Response - Page 6:
2025-06-08 23:34:21,908 - INFO - 第 6 页获取到 50 条记录
2025-06-08 23:34:22,111 - INFO - 查询完成，共获取到 550 条记录
2025-06-08 23:34:22,111 - INFO - 获取到 550 条表单数据
2025-06-08 23:34:22,111 - INFO - 当前日期 2025-05-23 有 17 条MySQL数据需要处理
2025-06-08 23:34:22,111 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMI5
2025-06-08 23:34:22,547 - INFO - 更新表单数据成功: FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMI5
2025-06-08 23:34:22,547 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 102.0}]
2025-06-08 23:34:22,547 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2P
2025-06-08 23:34:23,047 - INFO - 更新表单数据成功: FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2P
2025-06-08 23:34:23,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29.9, 'new_value': 3878.29}, {'field': 'total_amount', 'old_value': 29.9, 'new_value': 3878.29}]
2025-06-08 23:34:23,047 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLP
2025-06-08 23:34:23,531 - INFO - 更新表单数据成功: FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLP
2025-06-08 23:34:23,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 514.0, 'new_value': 713.0}]
2025-06-08 23:34:23,531 - INFO - 开始更新记录 - 表单实例ID: FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMWL
2025-06-08 23:34:24,031 - INFO - 更新表单数据成功: FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMWL
2025-06-08 23:34:24,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4428.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5284.0, 'new_value': 9712.0}]
2025-06-08 23:34:24,031 - INFO - 开始更新记录 - 表单实例ID: FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUN
2025-06-08 23:34:24,500 - INFO - 更新表单数据成功: FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUN
2025-06-08 23:34:24,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7658.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7658.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:34:24,500 - INFO - 开始更新记录 - 表单实例ID: FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAP
2025-06-08 23:34:24,934 - INFO - 更新表单数据成功: FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAP
2025-06-08 23:34:24,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2615.0, 'new_value': 2615.3}, {'field': 'total_amount', 'old_value': 2615.0, 'new_value': 2615.3}]
2025-06-08 23:34:24,934 - INFO - 开始更新记录 - 表单实例ID: FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4Q
2025-06-08 23:34:25,388 - INFO - 更新表单数据成功: FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4Q
2025-06-08 23:34:25,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45309.0, 'new_value': 44574.82}, {'field': 'total_amount', 'old_value': 45309.0, 'new_value': 44574.82}]
2025-06-08 23:34:25,388 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:34:25,528 - INFO - 批量插入响应状态码: 200
2025-06-08 23:34:25,528 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:34:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BC58134B-59B5-71C9-B4E6-E16FFCFF64FE', 'x-acs-trace-id': 'a7a20a45821738098f26bf2bee435d2c', 'etag': '6NE1TrPi15zaQ1TPI6jSegA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:34:25,528 - INFO - 批量插入响应体: {'result': ['FINST-W3B66L71J82WJ33IFHR687NMYMXR3KF4QTNBMQ11']}
2025-06-08 23:34:25,528 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:34:25,528 - INFO - 成功插入的数据ID: ['FINST-W3B66L71J82WJ33IFHR687NMYMXR3KF4QTNBMQ11']
2025-06-08 23:34:30,542 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:34:30,542 - INFO - 日期 2025-05-23 处理完成 - 更新: 7 条，插入: 1 条，错误: 0 条
2025-06-08 23:34:30,542 - INFO - 开始处理日期: 2025-05-24
2025-06-08 23:34:30,542 - INFO - Request Parameters - Page 1:
2025-06-08 23:34:30,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:30,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:31,355 - INFO - Response - Page 1:
2025-06-08 23:34:31,355 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:34:31,558 - INFO - Request Parameters - Page 2:
2025-06-08 23:34:31,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:31,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:32,370 - INFO - Response - Page 2:
2025-06-08 23:34:32,370 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:34:32,573 - INFO - Request Parameters - Page 3:
2025-06-08 23:34:32,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:32,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:33,542 - INFO - Response - Page 3:
2025-06-08 23:34:33,542 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:34:33,745 - INFO - Request Parameters - Page 4:
2025-06-08 23:34:33,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:33,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:34,620 - INFO - Response - Page 4:
2025-06-08 23:34:34,620 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:34:34,823 - INFO - Request Parameters - Page 5:
2025-06-08 23:34:34,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:34,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:35,698 - INFO - Response - Page 5:
2025-06-08 23:34:35,698 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:34:35,901 - INFO - Request Parameters - Page 6:
2025-06-08 23:34:35,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:35,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:36,605 - INFO - Response - Page 6:
2025-06-08 23:34:36,605 - INFO - 第 6 页获取到 48 条记录
2025-06-08 23:34:36,808 - INFO - 查询完成，共获取到 548 条记录
2025-06-08 23:34:36,808 - INFO - 获取到 548 条表单数据
2025-06-08 23:34:36,808 - INFO - 当前日期 2025-05-24 有 17 条MySQL数据需要处理
2025-06-08 23:34:36,808 - INFO - 开始更新记录 - 表单实例ID: FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC6
2025-06-08 23:34:37,355 - INFO - 更新表单数据成功: FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMC6
2025-06-08 23:34:37,355 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3143.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 8.0, 'new_value': 3151.0}]
2025-06-08 23:34:37,355 - INFO - 开始更新记录 - 表单实例ID: FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM8
2025-06-08 23:34:37,761 - INFO - 更新表单数据成功: FINST-RN766181SDOVF1HT6DPGE5B4N9Y32KFSGZ2BMM8
2025-06-08 23:34:37,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70816.0, 'new_value': 70367.99}, {'field': 'total_amount', 'old_value': 70816.0, 'new_value': 70367.99}]
2025-06-08 23:34:37,761 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMYE
2025-06-08 23:34:38,230 - INFO - 更新表单数据成功: FINST-5XA66LC124NVSE9EE45ZV692BF343PGWGZ2BMYE
2025-06-08 23:34:38,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6766.4, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4273.5, 'new_value': 11039.9}]
2025-06-08 23:34:38,230 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMRF
2025-06-08 23:34:38,730 - INFO - 更新表单数据成功: FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BMRF
2025-06-08 23:34:38,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 22000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 22000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:34:38,730 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM3G
2025-06-08 23:34:39,167 - INFO - 更新表单数据成功: FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM3G
2025-06-08 23:34:39,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5935.0, 'new_value': 5935.59}, {'field': 'total_amount', 'old_value': 5935.0, 'new_value': 5935.59}]
2025-06-08 23:34:39,167 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM6G
2025-06-08 23:34:39,573 - INFO - 更新表单数据成功: FINST-5XA66LC124NVSE9EE45ZV692BF343QGWGZ2BM6G
2025-06-08 23:34:39,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7659.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7659.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:34:39,573 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:34:39,730 - INFO - 批量插入响应状态码: 200
2025-06-08 23:34:39,730 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:34:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F154097-757B-7D44-A787-45E912B11419', 'x-acs-trace-id': 'ea1ed99bea0940a4a527b534ea5a7836', 'etag': '6wJrB4mvxwPpzJsoaxDNLDQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:34:39,730 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B1SU1WWGGOCKA1S7ELK5B12ZDFQTNBM77']}
2025-06-08 23:34:39,730 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:34:39,730 - INFO - 成功插入的数据ID: ['FINST-1MD668B1SU1WWGGOCKA1S7ELK5B12ZDFQTNBM77']
2025-06-08 23:34:44,740 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:34:44,740 - INFO - 日期 2025-05-24 处理完成 - 更新: 6 条，插入: 1 条，错误: 0 条
2025-06-08 23:34:44,740 - INFO - 开始处理日期: 2025-05-25
2025-06-08 23:34:44,740 - INFO - Request Parameters - Page 1:
2025-06-08 23:34:44,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:44,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:45,607 - INFO - Response - Page 1:
2025-06-08 23:34:45,607 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:34:45,808 - INFO - Request Parameters - Page 2:
2025-06-08 23:34:45,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:45,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:46,604 - INFO - Response - Page 2:
2025-06-08 23:34:46,604 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:34:46,805 - INFO - Request Parameters - Page 3:
2025-06-08 23:34:46,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:46,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:47,657 - INFO - Response - Page 3:
2025-06-08 23:34:47,657 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:34:47,857 - INFO - Request Parameters - Page 4:
2025-06-08 23:34:47,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:47,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:48,641 - INFO - Response - Page 4:
2025-06-08 23:34:48,642 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:34:48,842 - INFO - Request Parameters - Page 5:
2025-06-08 23:34:48,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:48,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:49,638 - INFO - Response - Page 5:
2025-06-08 23:34:49,638 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:34:49,840 - INFO - Request Parameters - Page 6:
2025-06-08 23:34:49,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:49,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748102400000, 1748188799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:50,632 - INFO - Response - Page 6:
2025-06-08 23:34:50,632 - INFO - 第 6 页获取到 92 条记录
2025-06-08 23:34:50,832 - INFO - 查询完成，共获取到 592 条记录
2025-06-08 23:34:50,832 - INFO - 获取到 592 条表单数据
2025-06-08 23:34:50,835 - INFO - 当前日期 2025-05-25 有 17 条MySQL数据需要处理
2025-06-08 23:34:50,835 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2O
2025-06-08 23:34:51,241 - INFO - 更新表单数据成功: FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BM2O
2025-06-08 23:34:51,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2580.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 20.0, 'new_value': 2600.0}]
2025-06-08 23:34:51,241 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNO
2025-06-08 23:34:51,710 - INFO - 更新表单数据成功: FINST-8PF66V712POV6YTA7YINLAHA9CLN2MA22H4BMNO
2025-06-08 23:34:51,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3327.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2617.0, 'new_value': 5944.0}]
2025-06-08 23:34:51,710 - INFO - 开始更新记录 - 表单实例ID: FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF1
2025-06-08 23:34:52,163 - INFO - 更新表单数据成功: FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMF1
2025-06-08 23:34:52,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19144.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 17328.0, 'new_value': 36640.8}, {'field': 'total_amount', 'old_value': 36472.8, 'new_value': 36640.8}]
2025-06-08 23:34:52,163 - INFO - 开始更新记录 - 表单实例ID: FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM1
2025-06-08 23:34:52,569 - INFO - 更新表单数据成功: FINST-I6E66WA1E6RVHY62AMJHN8A9WEA926D62H4BMM1
2025-06-08 23:34:52,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5108.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5108.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:34:52,569 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMR7
2025-06-08 23:34:52,960 - INFO - 更新表单数据成功: FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMR7
2025-06-08 23:34:52,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90153.0, 'new_value': 89019.1}, {'field': 'total_amount', 'old_value': 90153.0, 'new_value': 89019.1}]
2025-06-08 23:34:52,960 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMX7
2025-06-08 23:34:53,366 - INFO - 更新表单数据成功: FINST-YPE66RB1TMQVBPOVDKUG05MSKHT82E1MFT3BMX7
2025-06-08 23:34:53,366 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5524.8, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4214.3, 'new_value': 9739.1}]
2025-06-08 23:34:53,366 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH2
2025-06-08 23:34:53,851 - INFO - 更新表单数据成功: FINST-WBF66B81X4RVL3QX8ODIJD91BKOI3VAH0H4BMH2
2025-06-08 23:34:53,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6515.0, 'new_value': 6515.8}, {'field': 'total_amount', 'old_value': 6515.0, 'new_value': 6515.8}]
2025-06-08 23:34:53,851 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:34:54,004 - INFO - 批量插入响应状态码: 200
2025-06-08 23:34:54,004 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:34:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '93F65802-AC60-7751-97DE-5C01DC3E808C', 'x-acs-trace-id': '77c6771ecc6e6fcec1f5388cd0ff8ce0', 'etag': '6024Se3Dap0dAQX+Ljf3MJg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:34:54,004 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X81LP2W61XE6Q5E5AGNS8OJ3FEQQTNBMIN']}
2025-06-08 23:34:54,004 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:34:54,004 - INFO - 成功插入的数据ID: ['FINST-X3E66X81LP2W61XE6Q5E5AGNS8OJ3FEQQTNBMIN']
2025-06-08 23:34:59,018 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:34:59,018 - INFO - 日期 2025-05-25 处理完成 - 更新: 7 条，插入: 1 条，错误: 0 条
2025-06-08 23:34:59,018 - INFO - 开始处理日期: 2025-05-26
2025-06-08 23:34:59,018 - INFO - Request Parameters - Page 1:
2025-06-08 23:34:59,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:59,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:34:59,783 - INFO - Response - Page 1:
2025-06-08 23:34:59,783 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:34:59,986 - INFO - Request Parameters - Page 2:
2025-06-08 23:34:59,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:34:59,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:00,791 - INFO - Response - Page 2:
2025-06-08 23:35:00,791 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:35:00,991 - INFO - Request Parameters - Page 3:
2025-06-08 23:35:00,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:00,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:01,754 - INFO - Response - Page 3:
2025-06-08 23:35:01,754 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:35:01,954 - INFO - Request Parameters - Page 4:
2025-06-08 23:35:01,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:01,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:02,717 - INFO - Response - Page 4:
2025-06-08 23:35:02,717 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:35:02,917 - INFO - Request Parameters - Page 5:
2025-06-08 23:35:02,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:02,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:03,702 - INFO - Response - Page 5:
2025-06-08 23:35:03,702 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:35:03,913 - INFO - Request Parameters - Page 6:
2025-06-08 23:35:03,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:03,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748188800000, 1748275199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:04,773 - INFO - Response - Page 6:
2025-06-08 23:35:04,773 - INFO - 第 6 页获取到 94 条记录
2025-06-08 23:35:04,974 - INFO - 查询完成，共获取到 594 条记录
2025-06-08 23:35:04,975 - INFO - 获取到 594 条表单数据
2025-06-08 23:35:04,987 - INFO - 当前日期 2025-05-26 有 17 条MySQL数据需要处理
2025-06-08 23:35:04,988 - INFO - 开始更新记录 - 表单实例ID: FINST-HXD667B1F5SVCR7NFSRKR7UF1A332ZQDCU5BM13
2025-06-08 23:35:05,437 - INFO - 更新表单数据成功: FINST-HXD667B1F5SVCR7NFSRKR7UF1A332ZQDCU5BM13
2025-06-08 23:35:05,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1309.0, 'new_value': 1358.0}, {'field': 'total_amount', 'old_value': 1309.0, 'new_value': 1358.0}]
2025-06-08 23:35:05,437 - INFO - 开始更新记录 - 表单实例ID: FINST-HXD667B1F5SVCR7NFSRKR7UF1A332ZQDCU5BM75
2025-06-08 23:35:05,910 - INFO - 更新表单数据成功: FINST-HXD667B1F5SVCR7NFSRKR7UF1A332ZQDCU5BM75
2025-06-08 23:35:05,910 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 385.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 386.0}]
2025-06-08 23:35:05,910 - INFO - 开始更新记录 - 表单实例ID: FINST-UW96637177RV5156990Q99Q2QF3F3YSHCU5BMN2
2025-06-08 23:35:06,375 - INFO - 更新表单数据成功: FINST-UW96637177RV5156990Q99Q2QF3F3YSHCU5BMN2
2025-06-08 23:35:06,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29.9, 'new_value': 11172.46}, {'field': 'total_amount', 'old_value': 29.9, 'new_value': 11172.46}]
2025-06-08 23:35:06,376 - INFO - 开始更新记录 - 表单实例ID: FINST-UW96637177RV5156990Q99Q2QF3F3ZSHCU5BMZ3
2025-06-08 23:35:06,831 - INFO - 更新表单数据成功: FINST-UW96637177RV5156990Q99Q2QF3F3ZSHCU5BMZ3
2025-06-08 23:35:06,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1479.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1479.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:35:06,831 - INFO - 开始更新记录 - 表单实例ID: FINST-UW96637177RV5156990Q99Q2QF3F3ZSHCU5BMM4
2025-06-08 23:35:07,284 - INFO - 更新表单数据成功: FINST-UW96637177RV5156990Q99Q2QF3F3ZSHCU5BMM4
2025-06-08 23:35:07,284 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4287.2, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 5287.2}]
2025-06-08 23:35:07,284 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1SGRVIPGDEGZ8R5RLF23J3CG67S5BMB
2025-06-08 23:35:07,815 - INFO - 更新表单数据成功: FINST-YPE66RB1SGRVIPGDEGZ8R5RLF23J3CG67S5BMB
2025-06-08 23:35:07,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35955.0, 'new_value': 35672.37}, {'field': 'total_amount', 'old_value': 35955.0, 'new_value': 35672.37}]
2025-06-08 23:35:07,815 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1SGRVIPGDEGZ8R5RLF23J3CG67S5BMP
2025-06-08 23:35:08,253 - INFO - 更新表单数据成功: FINST-YPE66RB1SGRVIPGDEGZ8R5RLF23J3CG67S5BMP
2025-06-08 23:35:08,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2973.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4672.5, 'new_value': 7646.4}]
2025-06-08 23:35:08,253 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:35:08,409 - INFO - 批量插入响应状态码: 200
2025-06-08 23:35:08,409 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:35:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2C937AE0-8D86-7985-9D92-61D002ACAC3C', 'x-acs-trace-id': 'ec36d18a0e1a6f3221dc1eb765ea6563', 'etag': '6UfJ+0RbUcjkY8q7A8q6ZeQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:35:08,409 - INFO - 批量插入响应体: {'result': ['FINST-UW966371GQ2WRJAW61C20COAR1O43OI1RTNBM8I']}
2025-06-08 23:35:08,409 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:35:08,409 - INFO - 成功插入的数据ID: ['FINST-UW966371GQ2WRJAW61C20COAR1O43OI1RTNBM8I']
2025-06-08 23:35:13,425 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:35:13,425 - INFO - 日期 2025-05-26 处理完成 - 更新: 7 条，插入: 1 条，错误: 0 条
2025-06-08 23:35:13,425 - INFO - 开始处理日期: 2025-05-27
2025-06-08 23:35:13,425 - INFO - Request Parameters - Page 1:
2025-06-08 23:35:13,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:13,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:14,190 - INFO - Response - Page 1:
2025-06-08 23:35:14,190 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:35:14,393 - INFO - Request Parameters - Page 2:
2025-06-08 23:35:14,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:14,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:15,222 - INFO - Response - Page 2:
2025-06-08 23:35:15,222 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:35:15,425 - INFO - Request Parameters - Page 3:
2025-06-08 23:35:15,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:15,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:16,206 - INFO - Response - Page 3:
2025-06-08 23:35:16,206 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:35:16,409 - INFO - Request Parameters - Page 4:
2025-06-08 23:35:16,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:16,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:17,425 - INFO - Response - Page 4:
2025-06-08 23:35:17,425 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:35:17,628 - INFO - Request Parameters - Page 5:
2025-06-08 23:35:17,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:17,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:18,440 - INFO - Response - Page 5:
2025-06-08 23:35:18,440 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:35:18,643 - INFO - Request Parameters - Page 6:
2025-06-08 23:35:18,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:18,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748275200000, 1748361599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:19,378 - INFO - Response - Page 6:
2025-06-08 23:35:19,378 - INFO - 第 6 页获取到 85 条记录
2025-06-08 23:35:19,581 - INFO - 查询完成，共获取到 585 条记录
2025-06-08 23:35:19,581 - INFO - 获取到 585 条表单数据
2025-06-08 23:35:19,581 - INFO - 当前日期 2025-05-27 有 18 条MySQL数据需要处理
2025-06-08 23:35:19,581 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291CHRVYUSL7ZUU24KXFBOG3O32S97BMW3
2025-06-08 23:35:20,050 - INFO - 更新表单数据成功: FINST-80B66291CHRVYUSL7ZUU24KXFBOG3O32S97BMW3
2025-06-08 23:35:20,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3.0, 'new_value': 198.0}]
2025-06-08 23:35:20,050 - INFO - 开始更新记录 - 表单实例ID: FINST-AJF66F71LESVL7QMEHGD96GCX7OJ2L66S97BM42
2025-06-08 23:35:20,675 - INFO - 更新表单数据成功: FINST-AJF66F71LESVL7QMEHGD96GCX7OJ2L66S97BM42
2025-06-08 23:35:20,675 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2706.7, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 500.0, 'new_value': 3206.7}]
2025-06-08 23:35:20,675 - INFO - 开始更新记录 - 表单实例ID: FINST-5TD66N91GHSVCHBH8MTZ0AGYEPKW34TKHQ6BMW3
2025-06-08 23:35:21,221 - INFO - 更新表单数据成功: FINST-5TD66N91GHSVCHBH8MTZ0AGYEPKW34TKHQ6BMW3
2025-06-08 23:35:21,221 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3927.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4251.4, 'new_value': 8179.0}]
2025-06-08 23:35:21,221 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381QIRVPDCL9V74T43UWAX0228AS97BM1N
2025-06-08 23:35:21,706 - INFO - 更新表单数据成功: FINST-OJ966381QIRVPDCL9V74T43UWAX0228AS97BM1N
2025-06-08 23:35:21,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1196.0, 'new_value': 1196.4}, {'field': 'total_amount', 'old_value': 1196.0, 'new_value': 1196.4}]
2025-06-08 23:35:21,706 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381QIRVPDCL9V74T43UWAX0228AS97BMON
2025-06-08 23:35:22,159 - INFO - 更新表单数据成功: FINST-OJ966381QIRVPDCL9V74T43UWAX0228AS97BMON
2025-06-08 23:35:22,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1099.0, 'new_value': 6047.0}, {'field': 'total_amount', 'old_value': 1099.0, 'new_value': 6047.0}]
2025-06-08 23:35:22,159 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC1T5SVBJ5RDQE255536WNQ2B8FXB7BMND
2025-06-08 23:35:22,565 - INFO - 更新表单数据成功: FINST-AEF66BC1T5SVBJ5RDQE255536WNQ2B8FXB7BMND
2025-06-08 23:35:22,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30208.0, 'new_value': 29642.63}, {'field': 'total_amount', 'old_value': 30208.0, 'new_value': 29642.63}]
2025-06-08 23:35:22,565 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:35:22,706 - INFO - 批量插入响应状态码: 200
2025-06-08 23:35:22,706 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:35:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '78C2D078-E9E8-7AA0-8D50-041DB4D7C50A', 'x-acs-trace-id': 'b057d31269606da15cef4a434906d787', 'etag': '6kppn4N2Q68ALwijfhjRDsw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:35:22,706 - INFO - 批量插入响应体: {'result': ['FINST-3Z966E91FO2W7Y9U63JZYC0ZC6OX2TJCRTNBMKB']}
2025-06-08 23:35:22,706 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:35:22,706 - INFO - 成功插入的数据ID: ['FINST-3Z966E91FO2W7Y9U63JZYC0ZC6OX2TJCRTNBMKB']
2025-06-08 23:35:27,721 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:35:27,721 - INFO - 日期 2025-05-27 处理完成 - 更新: 6 条，插入: 1 条，错误: 0 条
2025-06-08 23:35:27,721 - INFO - 开始处理日期: 2025-05-28
2025-06-08 23:35:27,721 - INFO - Request Parameters - Page 1:
2025-06-08 23:35:27,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:27,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:28,487 - INFO - Response - Page 1:
2025-06-08 23:35:28,487 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:35:28,690 - INFO - Request Parameters - Page 2:
2025-06-08 23:35:28,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:28,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:29,503 - INFO - Response - Page 2:
2025-06-08 23:35:29,503 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:35:29,706 - INFO - Request Parameters - Page 3:
2025-06-08 23:35:29,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:29,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:30,487 - INFO - Response - Page 3:
2025-06-08 23:35:30,487 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:35:30,690 - INFO - Request Parameters - Page 4:
2025-06-08 23:35:30,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:30,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:31,534 - INFO - Response - Page 4:
2025-06-08 23:35:31,534 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:35:31,737 - INFO - Request Parameters - Page 5:
2025-06-08 23:35:31,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:31,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:32,550 - INFO - Response - Page 5:
2025-06-08 23:35:32,550 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:35:32,753 - INFO - Request Parameters - Page 6:
2025-06-08 23:35:32,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:32,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748361600000, 1748447999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:33,643 - INFO - Response - Page 6:
2025-06-08 23:35:33,643 - INFO - 第 6 页获取到 83 条记录
2025-06-08 23:35:33,846 - INFO - 查询完成，共获取到 583 条记录
2025-06-08 23:35:33,846 - INFO - 获取到 583 条表单数据
2025-06-08 23:35:33,846 - INFO - 当前日期 2025-05-28 有 18 条MySQL数据需要处理
2025-06-08 23:35:33,846 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1QFSVCFCPA52KI4UZORQM2D39S38BMYN
2025-06-08 23:35:34,362 - INFO - 更新表单数据成功: FINST-K7666JC1QFSVCFCPA52KI4UZORQM2D39S38BMYN
2025-06-08 23:35:34,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29109.0, 'new_value': 28184.62}, {'field': 'total_amount', 'old_value': 29109.0, 'new_value': 28184.62}]
2025-06-08 23:35:34,362 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMAD
2025-06-08 23:35:34,831 - INFO - 更新表单数据成功: FINST-EZD66RB1FETVWNEP80XZQ6DFE3G631A8IT8BMAD
2025-06-08 23:35:34,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 428.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 489.0}, {'field': 'total_amount', 'old_value': 429.9, 'new_value': 489.0}]
2025-06-08 23:35:34,831 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMO5
2025-06-08 23:35:35,253 - INFO - 更新表单数据成功: FINST-LLF66J71O5SVZ7W3FM6TGAKJ2JV721DCIT8BMO5
2025-06-08 23:35:35,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7269.1, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 8269.1}]
2025-06-08 23:35:35,253 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1QFSVCFCPA52KI4UZORQM2E39S38BMAP
2025-06-08 23:35:35,831 - INFO - 更新表单数据成功: FINST-K7666JC1QFSVCFCPA52KI4UZORQM2E39S38BMAP
2025-06-08 23:35:35,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3840.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2518.9, 'new_value': 6359.2}]
2025-06-08 23:35:35,831 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM6A
2025-06-08 23:35:36,362 - INFO - 更新表单数据成功: FINST-1PF66VA1QESVBLQX7GTIMAJIQUDN2NJKIT8BM6A
2025-06-08 23:35:36,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5888.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5888.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:35:36,362 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRO
2025-06-08 23:35:36,799 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BMRO
2025-06-08 23:35:36,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5017.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5017.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:35:36,799 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4P
2025-06-08 23:35:37,268 - INFO - 更新表单数据成功: FINST-VFF66XA1DJPVIFMRBV4NA90QCHQU3FMOIT8BM4P
2025-06-08 23:35:37,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2576.0, 'new_value': 2576.9}, {'field': 'total_amount', 'old_value': 2576.0, 'new_value': 2576.9}]
2025-06-08 23:35:37,268 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:35:37,409 - INFO - 批量插入响应状态码: 200
2025-06-08 23:35:37,409 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:35:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EEB29130-63D6-7575-8C06-3F3FB62C7BA0', 'x-acs-trace-id': 'ef5b9fd516a94af8d440defe3aa68fd8', 'etag': '6LjKF98FB+2GVWC+g6ow/0Q1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:35:37,409 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA19E2W5AC7DOXF046KWQRB3HWNRTNBMT51']}
2025-06-08 23:35:37,409 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:35:37,409 - INFO - 成功插入的数据ID: ['FINST-I6E66WA19E2W5AC7DOXF046KWQRB3HWNRTNBMT51']
2025-06-08 23:35:42,424 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:35:42,424 - INFO - 日期 2025-05-28 处理完成 - 更新: 7 条，插入: 1 条，错误: 0 条
2025-06-08 23:35:42,424 - INFO - 开始处理日期: 2025-05-29
2025-06-08 23:35:42,424 - INFO - Request Parameters - Page 1:
2025-06-08 23:35:42,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:42,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:43,230 - INFO - Response - Page 1:
2025-06-08 23:35:43,230 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:35:43,431 - INFO - Request Parameters - Page 2:
2025-06-08 23:35:43,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:43,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:44,261 - INFO - Response - Page 2:
2025-06-08 23:35:44,262 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:35:44,462 - INFO - Request Parameters - Page 3:
2025-06-08 23:35:44,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:44,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:45,250 - INFO - Response - Page 3:
2025-06-08 23:35:45,250 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:35:45,450 - INFO - Request Parameters - Page 4:
2025-06-08 23:35:45,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:45,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:46,251 - INFO - Response - Page 4:
2025-06-08 23:35:46,251 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:35:46,452 - INFO - Request Parameters - Page 5:
2025-06-08 23:35:46,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:46,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:47,285 - INFO - Response - Page 5:
2025-06-08 23:35:47,285 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:35:47,485 - INFO - Request Parameters - Page 6:
2025-06-08 23:35:47,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:47,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:48,293 - INFO - Response - Page 6:
2025-06-08 23:35:48,293 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:35:48,503 - INFO - Request Parameters - Page 7:
2025-06-08 23:35:48,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:48,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:49,090 - INFO - Response - Page 7:
2025-06-08 23:35:49,090 - INFO - 第 7 页获取到 9 条记录
2025-06-08 23:35:49,291 - INFO - 查询完成，共获取到 609 条记录
2025-06-08 23:35:49,291 - INFO - 获取到 609 条表单数据
2025-06-08 23:35:49,302 - INFO - 当前日期 2025-05-29 有 18 条MySQL数据需要处理
2025-06-08 23:35:49,302 - INFO - 开始更新记录 - 表单实例ID: FINST-6AG66W81WDUVGVK085Y1IC428LQ02C0YO4ABMO3
2025-06-08 23:35:49,702 - INFO - 更新表单数据成功: FINST-6AG66W81WDUVGVK085Y1IC428LQ02C0YO4ABMO3
2025-06-08 23:35:49,702 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 519.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 520.0}]
2025-06-08 23:35:49,702 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1PEUV0G6P619FXDVCOWOT3E22P4ABMA1
2025-06-08 23:35:50,080 - INFO - 更新表单数据成功: FINST-ZNE66RC1PEUV0G6P619FXDVCOWOT3E22P4ABMA1
2025-06-08 23:35:50,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5526.1, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1500.0, 'new_value': 7026.1}]
2025-06-08 23:35:50,080 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66F71LJUVHEJLA2HCK44ZC37F2PG98Y9BMK9
2025-06-08 23:35:50,543 - INFO - 更新表单数据成功: FINST-LLF66F71LJUVHEJLA2HCK44ZC37F2PG98Y9BMK9
2025-06-08 23:35:50,543 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4010.7, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3302.8, 'new_value': 7313.5}]
2025-06-08 23:35:50,543 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66F71LJUVHEJLA2HCK44ZC37F2PG98Y9BMN9
2025-06-08 23:35:50,995 - INFO - 更新表单数据成功: FINST-LLF66F71LJUVHEJLA2HCK44ZC37F2PG98Y9BMN9
2025-06-08 23:35:50,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34953.0, 'new_value': 33949.21}, {'field': 'total_amount', 'old_value': 34953.0, 'new_value': 33949.21}]
2025-06-08 23:35:50,995 - INFO - 开始更新记录 - 表单实例ID: FINST-F3G66Q61A9UVG8ABD1TE25IDSE6X3F2IN4ABMF5
2025-06-08 23:35:51,362 - INFO - 更新表单数据成功: FINST-F3G66Q61A9UVG8ABD1TE25IDSE6X3F2IN4ABMF5
2025-06-08 23:35:51,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2022.0, 'new_value': 2022.3}, {'field': 'total_amount', 'old_value': 2022.0, 'new_value': 2022.3}]
2025-06-08 23:35:51,362 - INFO - 开始更新记录 - 表单实例ID: FINST-F3G66Q61A9UVG8ABD1TE25IDSE6X3F2IN4ABMN5
2025-06-08 23:35:51,834 - INFO - 更新表单数据成功: FINST-F3G66Q61A9UVG8ABD1TE25IDSE6X3F2IN4ABMN5
2025-06-08 23:35:51,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1817.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1817.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:35:51,834 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:35:51,990 - INFO - 批量插入响应状态码: 200
2025-06-08 23:35:51,990 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:35:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '45139340-7204-745A-B229-9FB0E59A1456', 'x-acs-trace-id': '590b302f0c65cf9254197c260a7e560a', 'etag': '64BCuEEPo7T32tq8N6RviVA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:35:51,990 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q81VK4WM10QEQ0A04JFXJ3U235ZRTNBMK8']}
2025-06-08 23:35:51,990 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:35:51,990 - INFO - 成功插入的数据ID: ['FINST-MUC66Q81VK4WM10QEQ0A04JFXJ3U235ZRTNBMK8']
2025-06-08 23:35:57,005 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:35:57,005 - INFO - 日期 2025-05-29 处理完成 - 更新: 6 条，插入: 1 条，错误: 0 条
2025-06-08 23:35:57,005 - INFO - 开始处理日期: 2025-05-30
2025-06-08 23:35:57,005 - INFO - Request Parameters - Page 1:
2025-06-08 23:35:57,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:57,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:57,865 - INFO - Response - Page 1:
2025-06-08 23:35:57,865 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:35:58,068 - INFO - Request Parameters - Page 2:
2025-06-08 23:35:58,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:58,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:35:59,099 - INFO - Response - Page 2:
2025-06-08 23:35:59,099 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:35:59,302 - INFO - Request Parameters - Page 3:
2025-06-08 23:35:59,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:35:59,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:00,052 - INFO - Response - Page 3:
2025-06-08 23:36:00,052 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:36:00,255 - INFO - Request Parameters - Page 4:
2025-06-08 23:36:00,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:00,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:01,052 - INFO - Response - Page 4:
2025-06-08 23:36:01,052 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:36:01,255 - INFO - Request Parameters - Page 5:
2025-06-08 23:36:01,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:01,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:02,037 - INFO - Response - Page 5:
2025-06-08 23:36:02,037 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:36:02,240 - INFO - Request Parameters - Page 6:
2025-06-08 23:36:02,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:02,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:02,912 - INFO - Response - Page 6:
2025-06-08 23:36:02,912 - INFO - 第 6 页获取到 46 条记录
2025-06-08 23:36:03,115 - INFO - 查询完成，共获取到 546 条记录
2025-06-08 23:36:03,115 - INFO - 获取到 546 条表单数据
2025-06-08 23:36:03,115 - INFO - 当前日期 2025-05-30 有 17 条MySQL数据需要处理
2025-06-08 23:36:03,115 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMF1
2025-06-08 23:36:03,521 - INFO - 更新表单数据成功: FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMF1
2025-06-08 23:36:03,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 585.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 586.0}]
2025-06-08 23:36:03,521 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMF7
2025-06-08 23:36:03,927 - INFO - 更新表单数据成功: FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMF7
2025-06-08 23:36:03,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3656.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3656.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 23:36:03,927 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S818ETVMC8HCF6XU73C3RA13N5JNYABM9B
2025-06-08 23:36:04,334 - INFO - 更新表单数据成功: FINST-N3G66S818ETVMC8HCF6XU73C3RA13N5JNYABM9B
2025-06-08 23:36:04,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44912.0, 'new_value': 44019.62}, {'field': 'total_amount', 'old_value': 44912.0, 'new_value': 44019.62}]
2025-06-08 23:36:04,334 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S818ETVMC8HCF6XU73C3RA13N5JNYABMDB
2025-06-08 23:36:04,771 - INFO - 更新表单数据成功: FINST-N3G66S818ETVMC8HCF6XU73C3RA13N5JNYABMDB
2025-06-08 23:36:04,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4720.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 3675.7, 'new_value': 8396.3}]
2025-06-08 23:36:04,771 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMRC
2025-06-08 23:36:05,209 - INFO - 更新表单数据成功: FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMRC
2025-06-08 23:36:05,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4183.0, 'new_value': 4183.1}, {'field': 'total_amount', 'old_value': 4183.0, 'new_value': 4183.1}]
2025-06-08 23:36:05,209 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:36:05,365 - INFO - 批量插入响应状态码: 200
2025-06-08 23:36:05,365 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:36:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CA14A1B4-5CF8-7425-B325-C81780CF84E6', 'x-acs-trace-id': '345c84f9edee576029e71b700aa3b445', 'etag': '67Br5epqqCb5E8LblDgI1ag0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:36:05,365 - INFO - 批量插入响应体: {'result': ['FINST-1OC66A91QB2WRXEW8ITVH8I0RQ2O2VG9STNBM6D']}
2025-06-08 23:36:05,365 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:36:05,365 - INFO - 成功插入的数据ID: ['FINST-1OC66A91QB2WRXEW8ITVH8I0RQ2O2VG9STNBM6D']
2025-06-08 23:36:10,380 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:36:10,380 - INFO - 日期 2025-05-30 处理完成 - 更新: 5 条，插入: 1 条，错误: 0 条
2025-06-08 23:36:10,380 - INFO - 开始处理日期: 2025-05-31
2025-06-08 23:36:10,380 - INFO - Request Parameters - Page 1:
2025-06-08 23:36:10,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:10,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:11,162 - INFO - Response - Page 1:
2025-06-08 23:36:11,162 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:36:11,365 - INFO - Request Parameters - Page 2:
2025-06-08 23:36:11,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:11,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:12,115 - INFO - Response - Page 2:
2025-06-08 23:36:12,115 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:36:12,318 - INFO - Request Parameters - Page 3:
2025-06-08 23:36:12,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:12,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:13,068 - INFO - Response - Page 3:
2025-06-08 23:36:13,068 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:36:13,271 - INFO - Request Parameters - Page 4:
2025-06-08 23:36:13,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:13,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:14,052 - INFO - Response - Page 4:
2025-06-08 23:36:14,052 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:36:14,255 - INFO - Request Parameters - Page 5:
2025-06-08 23:36:14,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:14,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:15,099 - INFO - Response - Page 5:
2025-06-08 23:36:15,099 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:36:15,302 - INFO - Request Parameters - Page 6:
2025-06-08 23:36:15,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:15,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:15,943 - INFO - Response - Page 6:
2025-06-08 23:36:15,958 - INFO - 第 6 页获取到 41 条记录
2025-06-08 23:36:16,162 - INFO - 查询完成，共获取到 541 条记录
2025-06-08 23:36:16,162 - INFO - 获取到 541 条表单数据
2025-06-08 23:36:16,177 - INFO - 当前日期 2025-05-31 有 20 条MySQL数据需要处理
2025-06-08 23:36:16,177 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMM7
2025-06-08 23:36:16,552 - INFO - 更新表单数据成功: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMM7
2025-06-08 23:36:16,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14500.0, 'new_value': 15614.5}, {'field': 'total_amount', 'old_value': 14500.0, 'new_value': 15614.5}]
2025-06-08 23:36:16,552 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMS7
2025-06-08 23:36:16,958 - INFO - 更新表单数据成功: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMS7
2025-06-08 23:36:16,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3246.86, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5.0, 'new_value': 3251.86}]
2025-06-08 23:36:16,958 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMZ8
2025-06-08 23:36:17,365 - INFO - 更新表单数据成功: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMZ8
2025-06-08 23:36:17,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 959.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1098.0, 'new_value': 2057.9}]
2025-06-08 23:36:17,365 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMR9
2025-06-08 23:36:17,818 - INFO - 更新表单数据成功: FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMR9
2025-06-08 23:36:17,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98479.0, 'new_value': 95941.35}, {'field': 'total_amount', 'old_value': 98479.0, 'new_value': 95941.35}]
2025-06-08 23:36:17,818 - INFO - 开始更新记录 - 表单实例ID: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMGL
2025-06-08 23:36:18,224 - INFO - 更新表单数据成功: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMGL
2025-06-08 23:36:18,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6242.7, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 4869.7, 'new_value': 17500.19}, {'field': 'total_amount', 'old_value': 11112.4, 'new_value': 17500.19}]
2025-06-08 23:36:18,224 - INFO - 开始更新记录 - 表单实例ID: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM8M
2025-06-08 23:36:18,599 - INFO - 更新表单数据成功: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM8M
2025-06-08 23:36:18,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5050.9, 'new_value': 5050.98}, {'field': 'total_amount', 'old_value': 5050.9, 'new_value': 5050.98}]
2025-06-08 23:36:18,599 - INFO - 开始更新记录 - 表单实例ID: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMQM
2025-06-08 23:36:19,037 - INFO - 更新表单数据成功: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMQM
2025-06-08 23:36:19,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30000.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4558.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 4558.0}]
2025-06-08 23:36:19,037 - INFO - 开始更新记录 - 表单实例ID: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMUM
2025-06-08 23:36:19,490 - INFO - 更新表单数据成功: FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMUM
2025-06-08 23:36:19,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25000.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 25000.0}]
2025-06-08 23:36:19,490 - INFO - 开始批量插入 1 条新记录
2025-06-08 23:36:19,662 - INFO - 批量插入响应状态码: 200
2025-06-08 23:36:19,662 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:36:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6F6EAB22-79A5-706E-BB26-F08AAA536A41', 'x-acs-trace-id': '3680cefee8341e7a40bd44e81f4f7e8c', 'etag': '6f2EKmsfjMmBKKnJE2h37/Q1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:36:19,662 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691BO2WFE17EU3VA95E6ST62MHKSTNBMLF1']}
2025-06-08 23:36:19,662 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-08 23:36:19,662 - INFO - 成功插入的数据ID: ['FINST-6PF66691BO2WFE17EU3VA95E6ST62MHKSTNBMLF1']
2025-06-08 23:36:24,677 - INFO - 批量插入完成，共 1 条记录
2025-06-08 23:36:24,677 - INFO - 日期 2025-05-31 处理完成 - 更新: 8 条，插入: 1 条，错误: 0 条
2025-06-08 23:36:24,677 - INFO - 开始处理日期: 2025-06-02
2025-06-08 23:36:24,677 - INFO - Request Parameters - Page 1:
2025-06-08 23:36:24,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:24,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:25,490 - INFO - Response - Page 1:
2025-06-08 23:36:25,490 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:36:25,693 - INFO - Request Parameters - Page 2:
2025-06-08 23:36:25,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:25,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:26,536 - INFO - Response - Page 2:
2025-06-08 23:36:26,552 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:36:26,755 - INFO - Request Parameters - Page 3:
2025-06-08 23:36:26,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:26,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:27,536 - INFO - Response - Page 3:
2025-06-08 23:36:27,536 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:36:27,740 - INFO - Request Parameters - Page 4:
2025-06-08 23:36:27,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:27,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:28,505 - INFO - Response - Page 4:
2025-06-08 23:36:28,505 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:36:28,705 - INFO - Request Parameters - Page 5:
2025-06-08 23:36:28,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:28,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:29,549 - INFO - Response - Page 5:
2025-06-08 23:36:29,550 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:36:29,750 - INFO - Request Parameters - Page 6:
2025-06-08 23:36:29,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:29,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:30,498 - INFO - Response - Page 6:
2025-06-08 23:36:30,498 - INFO - 第 6 页获取到 100 条记录
2025-06-08 23:36:30,699 - INFO - Request Parameters - Page 7:
2025-06-08 23:36:30,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:30,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:31,295 - INFO - Response - Page 7:
2025-06-08 23:36:31,295 - INFO - 第 7 页获取到 17 条记录
2025-06-08 23:36:31,495 - INFO - 查询完成，共获取到 617 条记录
2025-06-08 23:36:31,495 - INFO - 获取到 617 条表单数据
2025-06-08 23:36:31,506 - INFO - 当前日期 2025-06-02 有 1 条MySQL数据需要处理
2025-06-08 23:36:31,506 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 23:36:31,506 - INFO - 开始处理日期: 2025-06-06
2025-06-08 23:36:31,507 - INFO - Request Parameters - Page 1:
2025-06-08 23:36:31,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:31,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:32,279 - INFO - Response - Page 1:
2025-06-08 23:36:32,279 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:36:32,482 - INFO - Request Parameters - Page 2:
2025-06-08 23:36:32,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:32,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:33,279 - INFO - Response - Page 2:
2025-06-08 23:36:33,279 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:36:33,482 - INFO - Request Parameters - Page 3:
2025-06-08 23:36:33,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:33,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:34,310 - INFO - Response - Page 3:
2025-06-08 23:36:34,310 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:36:34,513 - INFO - Request Parameters - Page 4:
2025-06-08 23:36:34,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:34,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:35,341 - INFO - Response - Page 4:
2025-06-08 23:36:35,341 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:36:35,544 - INFO - Request Parameters - Page 5:
2025-06-08 23:36:35,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:35,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:36,341 - INFO - Response - Page 5:
2025-06-08 23:36:36,341 - INFO - 第 5 页获取到 100 条记录
2025-06-08 23:36:36,544 - INFO - Request Parameters - Page 6:
2025-06-08 23:36:36,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:36,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:37,232 - INFO - Response - Page 6:
2025-06-08 23:36:37,232 - INFO - 第 6 页获取到 22 条记录
2025-06-08 23:36:37,435 - INFO - 查询完成，共获取到 522 条记录
2025-06-08 23:36:37,435 - INFO - 获取到 522 条表单数据
2025-06-08 23:36:37,435 - INFO - 当前日期 2025-06-06 有 1 条MySQL数据需要处理
2025-06-08 23:36:37,435 - INFO - 日期 2025-06-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 23:36:37,435 - INFO - 开始处理日期: 2025-06-07
2025-06-08 23:36:37,435 - INFO - Request Parameters - Page 1:
2025-06-08 23:36:37,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:37,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:38,232 - INFO - Response - Page 1:
2025-06-08 23:36:38,232 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:36:38,435 - INFO - Request Parameters - Page 2:
2025-06-08 23:36:38,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:38,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:39,232 - INFO - Response - Page 2:
2025-06-08 23:36:39,232 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:36:39,435 - INFO - Request Parameters - Page 3:
2025-06-08 23:36:39,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:39,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:40,294 - INFO - Response - Page 3:
2025-06-08 23:36:40,294 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:36:40,497 - INFO - Request Parameters - Page 4:
2025-06-08 23:36:40,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:40,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:41,404 - INFO - Response - Page 4:
2025-06-08 23:36:41,404 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:36:41,607 - INFO - Request Parameters - Page 5:
2025-06-08 23:36:41,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:41,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:42,419 - INFO - Response - Page 5:
2025-06-08 23:36:42,419 - INFO - 第 5 页获取到 74 条记录
2025-06-08 23:36:42,622 - INFO - 查询完成，共获取到 474 条记录
2025-06-08 23:36:42,622 - INFO - 获取到 474 条表单数据
2025-06-08 23:36:42,622 - INFO - 当前日期 2025-06-07 有 186 条MySQL数据需要处理
2025-06-08 23:36:42,622 - INFO - 开始批量插入 2 条新记录
2025-06-08 23:36:42,794 - INFO - 批量插入响应状态码: 200
2025-06-08 23:36:42,794 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:36:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7929DBFF-4F28-7EDA-9AA2-17D5693482D5', 'x-acs-trace-id': 'b5ac16ee9158d698fa415a3771c4cf08', 'etag': '1xCc75sDKL3leEWIBrJcd3g8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:36:42,794 - INFO - 批量插入响应体: {'result': ['FINST-X8D66N81IP4W6H2IAOARBDRE5R7D28C2TTNBMJ6', 'FINST-X8D66N81IP4W6H2IAOARBDRE5R7D28C2TTNBMK6']}
2025-06-08 23:36:42,794 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-08 23:36:42,794 - INFO - 成功插入的数据ID: ['FINST-X8D66N81IP4W6H2IAOARBDRE5R7D28C2TTNBMJ6', 'FINST-X8D66N81IP4W6H2IAOARBDRE5R7D28C2TTNBMK6']
2025-06-08 23:36:47,810 - INFO - 批量插入完成，共 2 条记录
2025-06-08 23:36:47,810 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-08 23:36:47,810 - INFO - 开始处理日期: 2025-06-08
2025-06-08 23:36:47,810 - INFO - Request Parameters - Page 1:
2025-06-08 23:36:47,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:36:47,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749312000000, 1749398399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:36:48,513 - INFO - Response - Page 1:
2025-06-08 23:36:48,513 - INFO - 第 1 页获取到 52 条记录
2025-06-08 23:36:48,715 - INFO - 查询完成，共获取到 52 条记录
2025-06-08 23:36:48,715 - INFO - 获取到 52 条表单数据
2025-06-08 23:36:48,717 - INFO - 当前日期 2025-06-08 有 75 条MySQL数据需要处理
2025-06-08 23:36:48,718 - INFO - 开始批量插入 23 条新记录
2025-06-08 23:36:48,873 - INFO - 批量插入响应状态码: 200
2025-06-08 23:36:48,873 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:36:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1116', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D67C9993-A187-7226-9FB7-FB15FD75434A', 'x-acs-trace-id': 'a9070e4ac31bc5c81758ae0c5c391ea0', 'etag': '16rPS+lEWxhfxe6rcf8GKPg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:36:48,873 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMH9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMI9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMJ9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMK9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBML9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMM9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMN9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMO9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMP9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMQ9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMR9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMS9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMT9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMU9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMV9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMW9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMX9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMY9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMZ9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM0A', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM1A', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM2A', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM3A']}
2025-06-08 23:36:48,873 - INFO - 批量插入表单数据成功，批次 1，共 23 条记录
2025-06-08 23:36:48,873 - INFO - 成功插入的数据ID: ['FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMH9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMI9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMJ9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMK9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBML9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMM9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMN9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMO9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMP9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMQ9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMR9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMS9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMT9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMU9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMV9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMW9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMX9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMY9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBMZ9', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM0A', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM1A', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM2A', 'FINST-LLF66FD1HK4W4Z2B9BK3XDIPI5ZJ2A17TTNBM3A']
2025-06-08 23:36:53,880 - INFO - 批量插入完成，共 23 条记录
2025-06-08 23:36:53,880 - INFO - 日期 2025-06-08 处理完成 - 更新: 0 条，插入: 23 条，错误: 0 条
2025-06-08 23:36:53,880 - INFO - 数据同步完成！更新: 184 条，插入: 37 条，错误: 3 条
2025-06-08 23:37:53,895 - INFO - 开始同步昨天与今天的销售数据: 2025-06-07 至 2025-06-08
2025-06-08 23:37:53,895 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-08 23:37:53,895 - INFO - 查询参数: ('2025-06-07', '2025-06-08')
2025-06-08 23:37:53,973 - INFO - MySQL查询成功，时间段: 2025-06-07 至 2025-06-08，共获取 573 条记录
2025-06-08 23:37:53,973 - INFO - 获取到 2 个日期需要处理: ['2025-06-07', '2025-06-08']
2025-06-08 23:37:53,973 - INFO - 开始处理日期: 2025-06-07
2025-06-08 23:37:53,973 - INFO - Request Parameters - Page 1:
2025-06-08 23:37:53,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:37:53,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:37:54,895 - INFO - Response - Page 1:
2025-06-08 23:37:54,895 - INFO - 第 1 页获取到 100 条记录
2025-06-08 23:37:55,098 - INFO - Request Parameters - Page 2:
2025-06-08 23:37:55,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:37:55,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:37:55,880 - INFO - Response - Page 2:
2025-06-08 23:37:55,880 - INFO - 第 2 页获取到 100 条记录
2025-06-08 23:37:56,083 - INFO - Request Parameters - Page 3:
2025-06-08 23:37:56,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:37:56,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:37:56,833 - INFO - Response - Page 3:
2025-06-08 23:37:56,833 - INFO - 第 3 页获取到 100 条记录
2025-06-08 23:37:57,036 - INFO - Request Parameters - Page 4:
2025-06-08 23:37:57,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:37:57,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:37:57,880 - INFO - Response - Page 4:
2025-06-08 23:37:57,880 - INFO - 第 4 页获取到 100 条记录
2025-06-08 23:37:58,083 - INFO - Request Parameters - Page 5:
2025-06-08 23:37:58,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:37:58,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:37:58,880 - INFO - Response - Page 5:
2025-06-08 23:37:58,880 - INFO - 第 5 页获取到 76 条记录
2025-06-08 23:37:59,083 - INFO - 查询完成，共获取到 476 条记录
2025-06-08 23:37:59,083 - INFO - 获取到 476 条表单数据
2025-06-08 23:37:59,083 - INFO - 当前日期 2025-06-07 有 498 条MySQL数据需要处理
2025-06-08 23:37:59,098 - INFO - 开始批量插入 22 条新记录
2025-06-08 23:37:59,270 - INFO - 批量插入响应状态码: 200
2025-06-08 23:37:59,270 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 08 Jun 2025 15:37:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1068', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D040EDA-B357-7D5F-BE92-D8DEEDFA62C5', 'x-acs-trace-id': '1f8d4392868566ee0ddf7951874d13f9', 'etag': '1+A1QOVLI7e01kKbSc/5nYA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-08 23:37:59,270 - INFO - 批量插入响应体: {'result': ['FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMLG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMMG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMNG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMOG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMPG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMQG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMRG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMSG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMTG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMUG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMVG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMWG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMXG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMYG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMZG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM0H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM1H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM2H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM3H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM4H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM5H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM6H']}
2025-06-08 23:37:59,270 - INFO - 批量插入表单数据成功，批次 1，共 22 条记录
2025-06-08 23:37:59,270 - INFO - 成功插入的数据ID: ['FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMLG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMMG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMNG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMOG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMPG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMQG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMRG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMSG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMTG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMUG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMVG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMWG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMXG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMYG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBMZG', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM0H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM1H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM2H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM3H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM4H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM5H', 'FINST-L5766E714V1W3CMVCX7WS8HHA8U32SCPUTNBM6H']
2025-06-08 23:38:04,286 - INFO - 批量插入完成，共 22 条记录
2025-06-08 23:38:04,286 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 22 条，错误: 0 条
2025-06-08 23:38:04,286 - INFO - 开始处理日期: 2025-06-08
2025-06-08 23:38:04,286 - INFO - Request Parameters - Page 1:
2025-06-08 23:38:04,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 23:38:04,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749312000000, 1749398399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 23:38:05,130 - INFO - Response - Page 1:
2025-06-08 23:38:05,130 - INFO - 第 1 页获取到 75 条记录
2025-06-08 23:38:05,333 - INFO - 查询完成，共获取到 75 条记录
2025-06-08 23:38:05,333 - INFO - 获取到 75 条表单数据
2025-06-08 23:38:05,333 - INFO - 当前日期 2025-06-08 有 75 条MySQL数据需要处理
2025-06-08 23:38:05,333 - INFO - 日期 2025-06-08 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 23:38:05,333 - INFO - 数据同步完成！更新: 0 条，插入: 22 条，错误: 0 条
2025-06-08 23:38:05,333 - INFO - 同步完成
