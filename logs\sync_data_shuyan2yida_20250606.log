2025-06-06 08:00:03,208 - INFO - ==================================================
2025-06-06 08:00:03,208 - INFO - 程序启动 - 版本 v1.0.0
2025-06-06 08:00:03,208 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250606.log
2025-06-06 08:00:03,208 - INFO - ==================================================
2025-06-06 08:00:03,208 - INFO - 程序入口点: __main__
2025-06-06 08:00:03,223 - INFO - ==================================================
2025-06-06 08:00:03,223 - INFO - 程序启动 - 版本 v1.0.1
2025-06-06 08:00:03,223 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250606.log
2025-06-06 08:00:03,223 - INFO - ==================================================
2025-06-06 08:00:03,504 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-06 08:00:03,504 - INFO - sales_data表已存在，无需创建
2025-06-06 08:00:03,504 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-06 08:00:03,504 - INFO - DataSyncManager初始化完成
2025-06-06 08:00:03,504 - INFO - 未提供日期参数，使用默认值
2025-06-06 08:00:03,504 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-06 08:00:03,504 - INFO - 开始综合数据同步流程...
2025-06-06 08:00:03,504 - INFO - 正在获取数衍平台日销售数据...
2025-06-06 08:00:03,504 - INFO - 查询数衍平台数据，时间段为: 2025-04-06, 2025-06-05
2025-06-06 08:00:03,504 - INFO - 正在获取********至********的数据
2025-06-06 08:00:03,504 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:03,504 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DA35F85443B9D6E6AE0F64D38C971129'}
2025-06-06 08:00:05,770 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:05,770 - INFO - 过滤后保留 427 条记录
2025-06-06 08:00:07,785 - INFO - 正在获取********至********的数据
2025-06-06 08:00:07,785 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:07,785 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8EF707904CA203CFECBF74F47608EF1D'}
2025-06-06 08:00:09,690 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:09,690 - INFO - 过滤后保留 426 条记录
2025-06-06 08:00:11,706 - INFO - 正在获取********至********的数据
2025-06-06 08:00:11,706 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:11,706 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6E533BDCA0231C6845B01C317603EF19'}
2025-06-06 08:00:13,377 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:13,377 - INFO - 过滤后保留 422 条记录
2025-06-06 08:00:15,392 - INFO - 正在获取********至********的数据
2025-06-06 08:00:15,392 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:15,392 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6647E1C9D49F5D94B908E904C2B59183'}
2025-06-06 08:00:17,048 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:17,064 - INFO - 过滤后保留 440 条记录
2025-06-06 08:00:19,079 - INFO - 正在获取********至********的数据
2025-06-06 08:00:19,079 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:19,079 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7CBCA06A79C4579B1DFC9A9E970A9EC0'}
2025-06-06 08:00:20,735 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:20,735 - INFO - 过滤后保留 429 条记录
2025-06-06 08:00:22,765 - INFO - 正在获取********至********的数据
2025-06-06 08:00:22,765 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:22,765 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C34D81AAC6844BA2EABB57F5D7A289E0'}
2025-06-06 08:00:24,265 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:24,265 - INFO - 过滤后保留 429 条记录
2025-06-06 08:00:26,280 - INFO - 正在获取********至********的数据
2025-06-06 08:00:26,280 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:26,280 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '937907AFFE38579A628018481F5EB97F'}
2025-06-06 08:00:27,905 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:27,920 - INFO - 过滤后保留 432 条记录
2025-06-06 08:00:29,936 - INFO - 正在获取********至********的数据
2025-06-06 08:00:29,936 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:29,936 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F7EB73857B94E291330BD448F227823F'}
2025-06-06 08:00:31,435 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:31,435 - INFO - 过滤后保留 429 条记录
2025-06-06 08:00:33,450 - INFO - 正在获取********至********的数据
2025-06-06 08:00:33,450 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:33,450 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '103420946647EA34541165E856C556D2'}
2025-06-06 08:00:35,012 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:35,012 - INFO - 过滤后保留 419 条记录
2025-06-06 08:00:37,028 - INFO - 正在获取********至********的数据
2025-06-06 08:00:37,028 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:37,028 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '12237F95690FC04EC16C03446F3FA039'}
2025-06-06 08:00:38,574 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:38,590 - INFO - 过滤后保留 416 条记录
2025-06-06 08:00:40,605 - INFO - 正在获取********至********的数据
2025-06-06 08:00:40,605 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:40,605 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1824EC0CC7D2460A25E7390145F8B5F0'}
2025-06-06 08:00:42,136 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:42,136 - INFO - 过滤后保留 434 条记录
2025-06-06 08:00:44,151 - INFO - 正在获取********至********的数据
2025-06-06 08:00:44,151 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:44,151 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6E122096F64C900DDAB1A71969180F8B'}
2025-06-06 08:00:45,807 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:45,807 - INFO - 过滤后保留 424 条记录
2025-06-06 08:00:47,806 - INFO - 正在获取********至********的数据
2025-06-06 08:00:47,806 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:47,806 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3A819039D855EC986697E9828D2F0BB9'}
2025-06-06 08:00:49,243 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:49,259 - INFO - 过滤后保留 432 条记录
2025-06-06 08:00:51,258 - INFO - 正在获取********至********的数据
2025-06-06 08:00:51,258 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:51,258 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6C376C84912C99173775D6AB68459424'}
2025-06-06 08:00:52,649 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:52,664 - INFO - 过滤后保留 422 条记录
2025-06-06 08:00:54,680 - INFO - 正在获取********至********的数据
2025-06-06 08:00:54,680 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:54,680 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '49C9775124E550D2E172167408376720'}
2025-06-06 08:00:56,273 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:56,273 - INFO - 过滤后保留 426 条记录
2025-06-06 08:00:58,288 - INFO - 正在获取********至********的数据
2025-06-06 08:00:58,288 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:00:58,288 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D2F5D411EBF38B6AA17103D39E7CFB90'}
2025-06-06 08:00:59,647 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:00:59,647 - INFO - 过滤后保留 410 条记录
2025-06-06 08:01:01,662 - INFO - 正在获取********至********的数据
2025-06-06 08:01:01,662 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:01,662 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1009D51FE8CFD4F4BE57BB380CACBFF7'}
2025-06-06 08:01:03,271 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:03,271 - INFO - 过滤后保留 423 条记录
2025-06-06 08:01:05,286 - INFO - 正在获取********至********的数据
2025-06-06 08:01:05,286 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:05,286 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '00064DBF1D112232C56CF3757AADDB29'}
2025-06-06 08:01:06,739 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:06,739 - INFO - 过滤后保留 436 条记录
2025-06-06 08:01:08,754 - INFO - 正在获取********至********的数据
2025-06-06 08:01:08,754 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:08,754 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6E60E8B44936C205F986CD4BEE9E7744'}
2025-06-06 08:01:10,113 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:10,129 - INFO - 过滤后保留 425 条记录
2025-06-06 08:01:12,144 - INFO - 正在获取********至********的数据
2025-06-06 08:01:12,144 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:12,144 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9AAEAB0944C3E4DF1A66461E4BD0E9AA'}
2025-06-06 08:01:13,706 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:13,706 - INFO - 过滤后保留 417 条记录
2025-06-06 08:01:15,721 - INFO - 正在获取********至********的数据
2025-06-06 08:01:15,721 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:15,721 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8480DE4DE7E258AC3720873AAAE65168'}
2025-06-06 08:01:17,096 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:17,096 - INFO - 过滤后保留 425 条记录
2025-06-06 08:01:19,111 - INFO - 正在获取********至********的数据
2025-06-06 08:01:19,111 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:19,111 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '063E28B3D6FE6AB1032E24637E766FD4'}
2025-06-06 08:01:20,548 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:20,548 - INFO - 过滤后保留 426 条记录
2025-06-06 08:01:22,563 - INFO - 正在获取********至********的数据
2025-06-06 08:01:22,563 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:22,563 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F45189EE4E7434614F506D65AE54A08B'}
2025-06-06 08:01:23,860 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:23,860 - INFO - 过滤后保留 421 条记录
2025-06-06 08:01:25,875 - INFO - 正在获取********至********的数据
2025-06-06 08:01:25,875 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:25,875 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4E8E1FF9B97D0CA2FD0DFC7BDDEC0263'}
2025-06-06 08:01:27,406 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:27,406 - INFO - 过滤后保留 416 条记录
2025-06-06 08:01:29,421 - INFO - 正在获取********至********的数据
2025-06-06 08:01:29,421 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:29,421 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9BC76FD583B55BF277512704734C5360'}
2025-06-06 08:01:30,718 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:30,733 - INFO - 过滤后保留 424 条记录
2025-06-06 08:01:32,748 - INFO - 正在获取********至********的数据
2025-06-06 08:01:32,748 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:32,748 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2C21AA2FCEF210E51ADDC22C175DA350'}
2025-06-06 08:01:34,186 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:34,186 - INFO - 过滤后保留 410 条记录
2025-06-06 08:01:36,201 - INFO - 正在获取********至********的数据
2025-06-06 08:01:36,201 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:36,201 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4AD559BCD8FD33AF71677BAEBA1042DE'}
2025-06-06 08:01:37,700 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:37,700 - INFO - 过滤后保留 414 条记录
2025-06-06 08:01:39,715 - INFO - 正在获取********至********的数据
2025-06-06 08:01:39,715 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:39,715 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'ACED0D3FA348E78B17ABF1A1C3C9FF5D'}
2025-06-06 08:01:41,121 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:41,121 - INFO - 过滤后保留 417 条记录
2025-06-06 08:01:43,136 - INFO - 正在获取********至********的数据
2025-06-06 08:01:43,136 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:43,136 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5D3844BB158140187E96CC598E4A5D62'}
2025-06-06 08:01:44,433 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:44,433 - INFO - 过滤后保留 407 条记录
2025-06-06 08:01:46,448 - INFO - 正在获取********至********的数据
2025-06-06 08:01:46,448 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:46,448 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6B078560B356F209FEBBA816AE8571AD'}
2025-06-06 08:01:47,635 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:47,635 - INFO - 过滤后保留 396 条记录
2025-06-06 08:01:49,651 - INFO - 正在获取********至********的数据
2025-06-06 08:01:49,651 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-06 08:01:49,651 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '883BB75BBAC70BE7F339B4DE70D73DB1'}
2025-06-06 08:01:50,728 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-06 08:01:50,728 - INFO - 过滤后保留 198 条记录
2025-06-06 08:01:52,744 - INFO - 开始保存数据到SQLite数据库，共 12872 条记录待处理
2025-06-06 08:01:53,150 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-01
2025-06-06 08:01:53,150 - INFO - 变更字段: recommend_amount: 0.0 -> 5610.0, daily_bill_amount: 0.0 -> 5610.0
2025-06-06 08:01:53,150 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-01
2025-06-06 08:01:53,150 - INFO - 变更字段: recommend_amount: 0.0 -> 23196.5, daily_bill_amount: 0.0 -> 23196.5
2025-06-06 08:01:53,165 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-03
2025-06-06 08:01:53,165 - INFO - 变更字段: recommend_amount: 0.0 -> 15763.1, daily_bill_amount: 0.0 -> 15763.1
2025-06-06 08:01:53,165 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-02
2025-06-06 08:01:53,165 - INFO - 变更字段: recommend_amount: 0.0 -> 20739.9, daily_bill_amount: 0.0 -> 20739.9
2025-06-06 08:01:53,181 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-05-02
2025-06-06 08:01:53,181 - INFO - 变更字段: recommend_amount: 0.0 -> 3672.91, daily_bill_amount: 0.0 -> 3672.91
2025-06-06 08:01:53,181 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-04
2025-06-06 08:01:53,181 - INFO - 变更字段: recommend_amount: 0.0 -> 1540.0, daily_bill_amount: 0.0 -> 1540.0
2025-06-06 08:01:53,197 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-04
2025-06-06 08:01:53,197 - INFO - 变更字段: recommend_amount: 0.0 -> 16842.0, daily_bill_amount: 0.0 -> 16842.0
2025-06-06 08:01:53,228 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-09
2025-06-06 08:01:53,228 - INFO - 变更字段: recommend_amount: 0.0 -> 8178.0, daily_bill_amount: 0.0 -> 8178.0
2025-06-06 08:01:53,228 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-05-08
2025-06-06 08:01:53,228 - INFO - 变更字段: recommend_amount: 0.0 -> 2770.96, daily_bill_amount: 0.0 -> 2770.96
2025-06-06 08:01:53,243 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRD2F2MCTBD6AJB6QM8HA650011P3, sale_time=2025-05-08
2025-06-06 08:01:53,243 - INFO - 变更字段: recommend_amount: 43386.95 -> 43896.95, amount: 43386 -> 43896, count: 128 -> 129, instore_amount: 43386.95 -> 43896.95, instore_count: 128 -> 129
2025-06-06 08:01:53,243 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-11
2025-06-06 08:01:53,243 - INFO - 变更字段: recommend_amount: 0.0 -> 594.0, daily_bill_amount: 0.0 -> 594.0
2025-06-06 08:01:53,259 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-11
2025-06-06 08:01:53,259 - INFO - 变更字段: recommend_amount: 0.0 -> 20139.53, daily_bill_amount: 0.0 -> 20139.53
2025-06-06 08:01:53,259 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-10
2025-06-06 08:01:53,259 - INFO - 变更字段: recommend_amount: 0.0 -> 15502.6, daily_bill_amount: 0.0 -> 15502.6
2025-06-06 08:01:53,275 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-13
2025-06-06 08:01:53,275 - INFO - 变更字段: recommend_amount: 0.0 -> 3806.0, daily_bill_amount: 0.0 -> 3806.0
2025-06-06 08:01:53,275 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-12
2025-06-06 08:01:53,275 - INFO - 变更字段: recommend_amount: 0.0 -> 4090.06, daily_bill_amount: 0.0 -> 4090.06
2025-06-06 08:01:53,290 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDULBESLOI0I86N3H2U1PE001F2K, sale_time=2025-05-14
2025-06-06 08:01:53,290 - INFO - 变更字段: recommend_amount: 0.0 -> 3236.76, daily_bill_amount: 0.0 -> 3236.76
2025-06-06 08:01:53,290 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-05-14
2025-06-06 08:01:53,290 - INFO - 变更字段: recommend_amount: 0.0 -> 2822.77, daily_bill_amount: 0.0 -> 2822.77
2025-06-06 08:01:53,306 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-17
2025-06-06 08:01:53,306 - INFO - 变更字段: recommend_amount: 0.0 -> 4130.0, daily_bill_amount: 0.0 -> 4130.0
2025-06-06 08:01:53,306 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-16
2025-06-06 08:01:53,306 - INFO - 变更字段: recommend_amount: 0.0 -> 4230.0, daily_bill_amount: 0.0 -> 4230.0
2025-06-06 08:01:53,353 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-20
2025-06-06 08:01:53,353 - INFO - 变更字段: recommend_amount: 0.0 -> 3542.0, daily_bill_amount: 0.0 -> 3542.0
2025-06-06 08:01:53,368 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-23
2025-06-06 08:01:53,368 - INFO - 变更字段: recommend_amount: 0.0 -> 279.0, daily_bill_amount: 0.0 -> 279.0
2025-06-06 08:01:53,431 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-29
2025-06-06 08:01:53,431 - INFO - 变更字段: recommend_amount: 0.0 -> 2027.0, daily_bill_amount: 0.0 -> 2027.0
2025-06-06 08:01:53,446 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V, sale_time=2025-05-31
2025-06-06 08:01:53,446 - INFO - 变更字段: recommend_amount: 6190.0 -> 358231.99, daily_bill_amount: 6190.0 -> 358231.99
2025-06-06 08:01:53,462 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-02
2025-06-06 08:01:53,462 - INFO - 变更字段: amount: 3147 -> 3133
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-01
2025-06-06 08:01:53,478 - INFO - 变更字段: amount: 87306 -> 94039, count: 1457 -> 1549, instore_amount: 83521.52 -> 90254.3, instore_count: 1391 -> 1483
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: recommend_amount: 0.0 -> 2528.8, daily_bill_amount: 0.0 -> 2528.8
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: daily_bill_amount: 0.0 -> 11800.55
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: amount: 2701 -> 2713, count: 162 -> 163, online_amount: 1423.38 -> 1434.88, online_count: 81 -> 82
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: amount: 2766 -> 2748
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: amount: 633 -> 652, count: 26 -> 27, online_amount: 532.11 -> 550.91, online_count: 23 -> 24
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: recommend_amount: 0.0 -> 5038.6, daily_bill_amount: 0.0 -> 5038.6
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: recommend_amount: 0.0 -> 8432.0, daily_bill_amount: 0.0 -> 8432.0
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: amount: 3417 -> 5185, count: 3 -> 7, instore_amount: 3417.0 -> 5185.0, instore_count: 3 -> 7
2025-06-06 08:01:53,478 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-06-04
2025-06-06 08:01:53,478 - INFO - 变更字段: recommend_amount: 0.0 -> 1482.0, daily_bill_amount: 0.0 -> 1482.0
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 1644.01 -> 1644.1, daily_bill_amount: 1644.01 -> 1644.1
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 1911.43 -> 1925.43, amount: 1911 -> 1925, count: 113 -> 114, online_amount: 1384.02 -> 1398.02, online_count: 82 -> 83
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 2293.31 -> 2298.81, amount: 2293 -> 2298, instore_amount: 2300.47 -> 2305.97
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 0.0 -> 354.0, daily_bill_amount: 0.0 -> 354.0
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 6843.76 -> 6868.78, amount: 6843 -> 6868, count: 136 -> 138, instore_amount: 5673.44 -> 5698.46, instore_count: 109 -> 111
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-03
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 8702.24 -> 8750.15, amount: 8702 -> 8750, count: 158 -> 160, instore_amount: 8121.6 -> 8169.51, instore_count: 140 -> 142
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: amount: 2421 -> 2480, count: 162 -> 167, online_amount: 2190.7 -> 2250.2, online_count: 116 -> 121
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 4789.59 -> 4791.59, amount: 4789 -> 4791, count: 228 -> 229, online_amount: 3645.8 -> 3647.8, online_count: 185 -> 186
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: amount: 3591 -> 3622, count: 51 -> 52, instore_amount: 1384.12 -> 1415.02, instore_count: 11 -> 12
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 0.0 -> 12538.79, daily_bill_amount: 0.0 -> 12538.79
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-06-03
2025-06-06 08:01:53,493 - INFO - 变更字段: amount: -3821 -> -3768, count: 12 -> 13, online_amount: 208.1 -> 262.0, online_count: 11 -> 12
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: amount: 21927 -> 21906
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: amount: 8257 -> 8285, count: 90 -> 91, online_amount: 2029.9 -> 2058.0, online_count: 41 -> 42
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: amount: 4693 -> 4718, count: 223 -> 224, online_amount: 3363.53 -> 3388.73, online_count: 153 -> 154
2025-06-06 08:01:53,493 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-06-04
2025-06-06 08:01:53,493 - INFO - 变更字段: recommend_amount: 0.0 -> 5090.8, daily_bill_amount: 0.0 -> 5090.8, amount: 99 -> 5209, count: 1 -> 12, instore_amount: 99.0 -> 5209.8, instore_count: 1 -> 12
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 3747 -> 3792, count: 241 -> 251, online_amount: 3778.82 -> 3823.52, online_count: 234 -> 244
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 3245 -> 3312, count: 135 -> 137, online_amount: 2782.1 -> 2849.0, online_count: 101 -> 103
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: instore_amount: 4268.45 -> 4296.48, instore_count: 238 -> 244, online_amount: 1355.33 -> 1327.3, online_count: 95 -> 89
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 3790 -> 3802, count: 71 -> 72, online_amount: 1716.76 -> 1728.85, online_count: 28 -> 29
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 799 -> 771
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=3F059827C9E04DEAA6B50797867EC52B, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 2900 -> 3055, count: 23 -> 24, instore_amount: 2900.0 -> 3055.0, instore_count: 23 -> 24
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GPFAB4PUHT7OL7Q2OV4FVC7US001R67, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: recommend_amount: 6270.1 -> 14934.1, amount: 6270 -> 14934, count: 9 -> 10, instore_amount: 6270.1 -> 14934.1, instore_count: 9 -> 10
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: recommend_amount: 0.0 -> 22037.89, daily_bill_amount: 0.0 -> 22037.89
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: recommend_amount: 5129.27 -> 5125.27, amount: 5129 -> 5125, count: 282 -> 283, instore_amount: 2372.4 -> 2386.2, instore_count: 131 -> 132
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: recommend_amount: 0.0 -> 3599.23, daily_bill_amount: 0.0 -> 3599.23
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: count: 47 -> 48, instore_count: 37 -> 38
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-03
2025-06-06 08:01:53,509 - INFO - 变更字段: recommend_amount: 0.0 -> 37841.55, daily_bill_amount: 0.0 -> 37841.55
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 21557 -> 23181, count: 123 -> 125, instore_amount: 18928.51 -> 20552.51, instore_count: 92 -> 94
2025-06-06 08:01:53,509 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-04
2025-06-06 08:01:53,509 - INFO - 变更字段: amount: 1255 -> 1246
2025-06-06 08:01:53,743 - INFO - SQLite数据保存完成，统计信息：
2025-06-06 08:01:53,759 - INFO - - 总记录数: 12872
2025-06-06 08:01:53,759 - INFO - - 成功插入: 205
2025-06-06 08:01:53,759 - INFO - - 成功更新: 63
2025-06-06 08:01:53,759 - INFO - - 无需更新: 12604
2025-06-06 08:01:53,759 - INFO - - 处理失败: 0
2025-06-06 08:01:59,164 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250606.xlsx
2025-06-06 08:01:59,179 - INFO - 成功获取数衍平台数据，共 12872 条记录
2025-06-06 08:01:59,179 - INFO - 正在更新SQLite月度汇总数据...
2025-06-06 08:01:59,179 - INFO - 月度数据sqllite清空完成
2025-06-06 08:01:59,476 - INFO - 月度汇总数据更新完成，处理了 1402 条汇总记录
2025-06-06 08:01:59,476 - INFO - 成功更新月度汇总数据，共 1402 条记录
2025-06-06 08:01:59,476 - INFO - 正在获取宜搭日销售表单数据...
2025-06-06 08:01:59,476 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-06 00:00:00 至 2025-06-05 23:59:59
2025-06-06 08:01:59,476 - INFO - 查询分段 1: 2025-04-06 至 2025-04-07
2025-06-06 08:01:59,476 - INFO - 查询日期范围: 2025-04-06 至 2025-04-07，使用分页查询，每页 100 条记录
2025-06-06 08:01:59,476 - INFO - Request Parameters - Page 1:
2025-06-06 08:01:59,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:01:59,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:07,693 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 723C83BE-70B0-7330-8A78-06E6FCC272D9 Response: {'code': 'ServiceUnavailable', 'requestid': '723C83BE-70B0-7330-8A78-06E6FCC272D9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-06 08:02:07,693 - ERROR - 服务不可用，将等待后重试
2025-06-06 08:02:07,693 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 723C83BE-70B0-7330-8A78-06E6FCC272D9 Response: {'code': 'ServiceUnavailable', 'requestid': '723C83BE-70B0-7330-8A78-06E6FCC272D9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-06 08:02:07,693 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-06-06 08:02:13,707 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-06-06 08:02:13,707 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 723C83BE-70B0-7330-8A78-06E6FCC272D9 Response: {'code': 'ServiceUnavailable', 'requestid': '723C83BE-70B0-7330-8A78-06E6FCC272D9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-06-06 08:02:23,720 - INFO - Request Parameters - Page 1:
2025-06-06 08:02:23,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:23,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:24,548 - INFO - API请求耗时: 828ms
2025-06-06 08:02:24,548 - INFO - Response - Page 1
2025-06-06 08:02:24,548 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:02:25,064 - INFO - Request Parameters - Page 2:
2025-06-06 08:02:25,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:25,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:25,798 - INFO - API请求耗时: 734ms
2025-06-06 08:02:25,798 - INFO - Response - Page 2
2025-06-06 08:02:25,798 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:02:26,298 - INFO - Request Parameters - Page 3:
2025-06-06 08:02:26,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:26,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:27,001 - INFO - API请求耗时: 703ms
2025-06-06 08:02:27,001 - INFO - Response - Page 3
2025-06-06 08:02:27,001 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:02:27,516 - INFO - Request Parameters - Page 4:
2025-06-06 08:02:27,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:27,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:28,251 - INFO - API请求耗时: 734ms
2025-06-06 08:02:28,251 - INFO - Response - Page 4
2025-06-06 08:02:28,251 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:02:28,766 - INFO - Request Parameters - Page 5:
2025-06-06 08:02:28,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:28,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:29,500 - INFO - API请求耗时: 734ms
2025-06-06 08:02:29,500 - INFO - Response - Page 5
2025-06-06 08:02:29,500 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:02:30,016 - INFO - Request Parameters - Page 6:
2025-06-06 08:02:30,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:30,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:30,719 - INFO - API请求耗时: 703ms
2025-06-06 08:02:30,719 - INFO - Response - Page 6
2025-06-06 08:02:30,719 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:02:31,234 - INFO - Request Parameters - Page 7:
2025-06-06 08:02:31,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:31,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800476, 1743955200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:31,781 - INFO - API请求耗时: 547ms
2025-06-06 08:02:31,781 - INFO - Response - Page 7
2025-06-06 08:02:31,781 - INFO - 第 7 页获取到 36 条记录
2025-06-06 08:02:31,781 - INFO - 查询完成，共获取到 636 条记录
2025-06-06 08:02:31,781 - INFO - 分段 1 查询成功，获取到 636 条记录
2025-06-06 08:02:32,796 - INFO - 查询分段 2: 2025-04-08 至 2025-04-09
2025-06-06 08:02:32,796 - INFO - 查询日期范围: 2025-04-08 至 2025-04-09，使用分页查询，每页 100 条记录
2025-06-06 08:02:32,796 - INFO - Request Parameters - Page 1:
2025-06-06 08:02:32,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:32,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:33,640 - INFO - API请求耗时: 844ms
2025-06-06 08:02:33,640 - INFO - Response - Page 1
2025-06-06 08:02:33,640 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:02:34,155 - INFO - Request Parameters - Page 2:
2025-06-06 08:02:34,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:34,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:34,968 - INFO - API请求耗时: 812ms
2025-06-06 08:02:34,968 - INFO - Response - Page 2
2025-06-06 08:02:34,968 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:02:35,468 - INFO - Request Parameters - Page 3:
2025-06-06 08:02:35,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:35,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:36,186 - INFO - API请求耗时: 719ms
2025-06-06 08:02:36,186 - INFO - Response - Page 3
2025-06-06 08:02:36,186 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:02:36,702 - INFO - Request Parameters - Page 4:
2025-06-06 08:02:36,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:36,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:37,451 - INFO - API请求耗时: 750ms
2025-06-06 08:02:37,451 - INFO - Response - Page 4
2025-06-06 08:02:37,451 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:02:37,967 - INFO - Request Parameters - Page 5:
2025-06-06 08:02:37,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:37,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:38,685 - INFO - API请求耗时: 719ms
2025-06-06 08:02:38,685 - INFO - Response - Page 5
2025-06-06 08:02:38,685 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:02:39,185 - INFO - Request Parameters - Page 6:
2025-06-06 08:02:39,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:39,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:39,951 - INFO - API请求耗时: 765ms
2025-06-06 08:02:39,951 - INFO - Response - Page 6
2025-06-06 08:02:39,951 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:02:40,466 - INFO - Request Parameters - Page 7:
2025-06-06 08:02:40,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:40,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600476, 1744128000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:41,060 - INFO - API请求耗时: 594ms
2025-06-06 08:02:41,060 - INFO - Response - Page 7
2025-06-06 08:02:41,060 - INFO - 第 7 页获取到 36 条记录
2025-06-06 08:02:41,060 - INFO - 查询完成，共获取到 636 条记录
2025-06-06 08:02:41,060 - INFO - 分段 2 查询成功，获取到 636 条记录
2025-06-06 08:02:42,075 - INFO - 查询分段 3: 2025-04-10 至 2025-04-11
2025-06-06 08:02:42,075 - INFO - 查询日期范围: 2025-04-10 至 2025-04-11，使用分页查询，每页 100 条记录
2025-06-06 08:02:42,075 - INFO - Request Parameters - Page 1:
2025-06-06 08:02:42,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:42,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:42,872 - INFO - API请求耗时: 797ms
2025-06-06 08:02:42,872 - INFO - Response - Page 1
2025-06-06 08:02:42,872 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:02:43,387 - INFO - Request Parameters - Page 2:
2025-06-06 08:02:43,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:43,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:44,090 - INFO - API请求耗时: 703ms
2025-06-06 08:02:44,090 - INFO - Response - Page 2
2025-06-06 08:02:44,090 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:02:44,606 - INFO - Request Parameters - Page 3:
2025-06-06 08:02:44,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:44,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:45,356 - INFO - API请求耗时: 750ms
2025-06-06 08:02:45,356 - INFO - Response - Page 3
2025-06-06 08:02:45,356 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:02:45,871 - INFO - Request Parameters - Page 4:
2025-06-06 08:02:45,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:45,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:46,574 - INFO - API请求耗时: 703ms
2025-06-06 08:02:46,574 - INFO - Response - Page 4
2025-06-06 08:02:46,590 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:02:47,105 - INFO - Request Parameters - Page 5:
2025-06-06 08:02:47,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:47,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:47,808 - INFO - API请求耗时: 703ms
2025-06-06 08:02:47,808 - INFO - Response - Page 5
2025-06-06 08:02:47,808 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:02:48,308 - INFO - Request Parameters - Page 6:
2025-06-06 08:02:48,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:48,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:48,980 - INFO - API请求耗时: 672ms
2025-06-06 08:02:48,980 - INFO - Response - Page 6
2025-06-06 08:02:48,980 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:02:49,495 - INFO - Request Parameters - Page 7:
2025-06-06 08:02:49,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:49,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400476, 1744300800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:50,073 - INFO - API请求耗时: 578ms
2025-06-06 08:02:50,073 - INFO - Response - Page 7
2025-06-06 08:02:50,073 - INFO - 第 7 页获取到 33 条记录
2025-06-06 08:02:50,073 - INFO - 查询完成，共获取到 633 条记录
2025-06-06 08:02:50,073 - INFO - 分段 3 查询成功，获取到 633 条记录
2025-06-06 08:02:51,073 - INFO - 查询分段 4: 2025-04-12 至 2025-04-13
2025-06-06 08:02:51,073 - INFO - 查询日期范围: 2025-04-12 至 2025-04-13，使用分页查询，每页 100 条记录
2025-06-06 08:02:51,073 - INFO - Request Parameters - Page 1:
2025-06-06 08:02:51,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:51,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:51,714 - INFO - API请求耗时: 640ms
2025-06-06 08:02:51,714 - INFO - Response - Page 1
2025-06-06 08:02:51,714 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:02:52,229 - INFO - Request Parameters - Page 2:
2025-06-06 08:02:52,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:52,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:52,994 - INFO - API请求耗时: 765ms
2025-06-06 08:02:52,994 - INFO - Response - Page 2
2025-06-06 08:02:52,994 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:02:53,494 - INFO - Request Parameters - Page 3:
2025-06-06 08:02:53,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:53,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:54,307 - INFO - API请求耗时: 812ms
2025-06-06 08:02:54,307 - INFO - Response - Page 3
2025-06-06 08:02:54,307 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:02:54,807 - INFO - Request Parameters - Page 4:
2025-06-06 08:02:54,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:54,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:55,541 - INFO - API请求耗时: 734ms
2025-06-06 08:02:55,541 - INFO - Response - Page 4
2025-06-06 08:02:55,541 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:02:56,056 - INFO - Request Parameters - Page 5:
2025-06-06 08:02:56,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:56,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:56,806 - INFO - API请求耗时: 750ms
2025-06-06 08:02:56,806 - INFO - Response - Page 5
2025-06-06 08:02:56,806 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:02:57,306 - INFO - Request Parameters - Page 6:
2025-06-06 08:02:57,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:57,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:58,040 - INFO - API请求耗时: 734ms
2025-06-06 08:02:58,040 - INFO - Response - Page 6
2025-06-06 08:02:58,040 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:02:58,556 - INFO - Request Parameters - Page 7:
2025-06-06 08:02:58,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:02:58,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200476, 1744473600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:02:59,165 - INFO - API请求耗时: 609ms
2025-06-06 08:02:59,165 - INFO - Response - Page 7
2025-06-06 08:02:59,165 - INFO - 第 7 页获取到 57 条记录
2025-06-06 08:02:59,165 - INFO - 查询完成，共获取到 657 条记录
2025-06-06 08:02:59,165 - INFO - 分段 4 查询成功，获取到 657 条记录
2025-06-06 08:03:00,180 - INFO - 查询分段 5: 2025-04-14 至 2025-04-15
2025-06-06 08:03:00,180 - INFO - 查询日期范围: 2025-04-14 至 2025-04-15，使用分页查询，每页 100 条记录
2025-06-06 08:03:00,180 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:00,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:00,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:00,914 - INFO - API请求耗时: 734ms
2025-06-06 08:03:00,914 - INFO - Response - Page 1
2025-06-06 08:03:00,914 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:01,430 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:01,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:01,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:02,164 - INFO - API请求耗时: 734ms
2025-06-06 08:03:02,164 - INFO - Response - Page 2
2025-06-06 08:03:02,164 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:02,680 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:02,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:02,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:03,414 - INFO - API请求耗时: 734ms
2025-06-06 08:03:03,414 - INFO - Response - Page 3
2025-06-06 08:03:03,414 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:03,929 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:03,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:03,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:04,648 - INFO - API请求耗时: 719ms
2025-06-06 08:03:04,648 - INFO - Response - Page 4
2025-06-06 08:03:04,648 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:05,163 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:05,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:05,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:06,007 - INFO - API请求耗时: 844ms
2025-06-06 08:03:06,007 - INFO - Response - Page 5
2025-06-06 08:03:06,007 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:03:06,507 - INFO - Request Parameters - Page 6:
2025-06-06 08:03:06,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:06,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:07,257 - INFO - API请求耗时: 750ms
2025-06-06 08:03:07,257 - INFO - Response - Page 6
2025-06-06 08:03:07,257 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:03:07,757 - INFO - Request Parameters - Page 7:
2025-06-06 08:03:07,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:07,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000476, 1744646400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:08,397 - INFO - API请求耗时: 640ms
2025-06-06 08:03:08,397 - INFO - Response - Page 7
2025-06-06 08:03:08,397 - INFO - 第 7 页获取到 42 条记录
2025-06-06 08:03:08,397 - INFO - 查询完成，共获取到 642 条记录
2025-06-06 08:03:08,397 - INFO - 分段 5 查询成功，获取到 642 条记录
2025-06-06 08:03:09,397 - INFO - 查询分段 6: 2025-04-16 至 2025-04-17
2025-06-06 08:03:09,397 - INFO - 查询日期范围: 2025-04-16 至 2025-04-17，使用分页查询，每页 100 条记录
2025-06-06 08:03:09,397 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:09,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:09,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:10,100 - INFO - API请求耗时: 703ms
2025-06-06 08:03:10,100 - INFO - Response - Page 1
2025-06-06 08:03:10,100 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:10,600 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:10,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:10,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:11,287 - INFO - API请求耗时: 687ms
2025-06-06 08:03:11,287 - INFO - Response - Page 2
2025-06-06 08:03:11,287 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:11,802 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:11,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:11,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:12,552 - INFO - API请求耗时: 750ms
2025-06-06 08:03:12,552 - INFO - Response - Page 3
2025-06-06 08:03:12,552 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:13,068 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:13,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:13,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:13,927 - INFO - API请求耗时: 859ms
2025-06-06 08:03:13,927 - INFO - Response - Page 4
2025-06-06 08:03:13,927 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:14,427 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:14,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:14,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:15,145 - INFO - API请求耗时: 719ms
2025-06-06 08:03:15,145 - INFO - Response - Page 5
2025-06-06 08:03:15,145 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:03:15,645 - INFO - Request Parameters - Page 6:
2025-06-06 08:03:15,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:15,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:16,395 - INFO - API请求耗时: 750ms
2025-06-06 08:03:16,395 - INFO - Response - Page 6
2025-06-06 08:03:16,395 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:03:16,895 - INFO - Request Parameters - Page 7:
2025-06-06 08:03:16,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:16,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800476, 1744819200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:17,598 - INFO - API请求耗时: 703ms
2025-06-06 08:03:17,598 - INFO - Response - Page 7
2025-06-06 08:03:17,598 - INFO - 第 7 页获取到 57 条记录
2025-06-06 08:03:17,598 - INFO - 查询完成，共获取到 657 条记录
2025-06-06 08:03:17,598 - INFO - 分段 6 查询成功，获取到 657 条记录
2025-06-06 08:03:18,613 - INFO - 查询分段 7: 2025-04-18 至 2025-04-19
2025-06-06 08:03:18,613 - INFO - 查询日期范围: 2025-04-18 至 2025-04-19，使用分页查询，每页 100 条记录
2025-06-06 08:03:18,613 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:18,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:18,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:19,394 - INFO - API请求耗时: 781ms
2025-06-06 08:03:19,394 - INFO - Response - Page 1
2025-06-06 08:03:19,394 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:19,910 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:19,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:19,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:20,628 - INFO - API请求耗时: 719ms
2025-06-06 08:03:20,628 - INFO - Response - Page 2
2025-06-06 08:03:20,628 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:21,144 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:21,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:21,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:21,816 - INFO - API请求耗时: 672ms
2025-06-06 08:03:21,816 - INFO - Response - Page 3
2025-06-06 08:03:21,816 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:22,331 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:22,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:22,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:23,065 - INFO - API请求耗时: 734ms
2025-06-06 08:03:23,065 - INFO - Response - Page 4
2025-06-06 08:03:23,065 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:23,581 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:23,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:23,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:24,346 - INFO - API请求耗时: 765ms
2025-06-06 08:03:24,346 - INFO - Response - Page 5
2025-06-06 08:03:24,346 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:03:24,846 - INFO - Request Parameters - Page 6:
2025-06-06 08:03:24,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:24,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:25,580 - INFO - API请求耗时: 734ms
2025-06-06 08:03:25,580 - INFO - Response - Page 6
2025-06-06 08:03:25,580 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:03:26,096 - INFO - Request Parameters - Page 7:
2025-06-06 08:03:26,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:26,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600476, 1744992000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:26,627 - INFO - API请求耗时: 531ms
2025-06-06 08:03:26,627 - INFO - Response - Page 7
2025-06-06 08:03:26,627 - INFO - 第 7 页获取到 45 条记录
2025-06-06 08:03:26,627 - INFO - 查询完成，共获取到 645 条记录
2025-06-06 08:03:26,627 - INFO - 分段 7 查询成功，获取到 645 条记录
2025-06-06 08:03:27,642 - INFO - 查询分段 8: 2025-04-20 至 2025-04-21
2025-06-06 08:03:27,642 - INFO - 查询日期范围: 2025-04-20 至 2025-04-21，使用分页查询，每页 100 条记录
2025-06-06 08:03:27,642 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:27,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:27,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:28,361 - INFO - API请求耗时: 719ms
2025-06-06 08:03:28,361 - INFO - Response - Page 1
2025-06-06 08:03:28,361 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:28,876 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:28,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:28,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:29,626 - INFO - API请求耗时: 750ms
2025-06-06 08:03:29,626 - INFO - Response - Page 2
2025-06-06 08:03:29,626 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:30,142 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:30,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:30,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:30,907 - INFO - API请求耗时: 765ms
2025-06-06 08:03:30,907 - INFO - Response - Page 3
2025-06-06 08:03:30,907 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:31,423 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:31,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:31,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:32,157 - INFO - API请求耗时: 734ms
2025-06-06 08:03:32,157 - INFO - Response - Page 4
2025-06-06 08:03:32,157 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:32,688 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:32,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:32,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:33,422 - INFO - API请求耗时: 734ms
2025-06-06 08:03:33,422 - INFO - Response - Page 5
2025-06-06 08:03:33,422 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:03:33,938 - INFO - Request Parameters - Page 6:
2025-06-06 08:03:33,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:33,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:34,656 - INFO - API请求耗时: 719ms
2025-06-06 08:03:34,656 - INFO - Response - Page 6
2025-06-06 08:03:34,656 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:03:35,172 - INFO - Request Parameters - Page 7:
2025-06-06 08:03:35,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:35,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400476, 1745164800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:35,765 - INFO - API请求耗时: 594ms
2025-06-06 08:03:35,765 - INFO - Response - Page 7
2025-06-06 08:03:35,765 - INFO - 第 7 页获取到 39 条记录
2025-06-06 08:03:35,765 - INFO - 查询完成，共获取到 639 条记录
2025-06-06 08:03:35,765 - INFO - 分段 8 查询成功，获取到 639 条记录
2025-06-06 08:03:36,765 - INFO - 查询分段 9: 2025-04-22 至 2025-04-23
2025-06-06 08:03:36,765 - INFO - 查询日期范围: 2025-04-22 至 2025-04-23，使用分页查询，每页 100 条记录
2025-06-06 08:03:36,765 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:36,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:36,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:37,437 - INFO - API请求耗时: 672ms
2025-06-06 08:03:37,437 - INFO - Response - Page 1
2025-06-06 08:03:37,437 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:37,937 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:37,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:37,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:38,624 - INFO - API请求耗时: 687ms
2025-06-06 08:03:38,624 - INFO - Response - Page 2
2025-06-06 08:03:38,624 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:39,140 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:39,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:39,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:40,014 - INFO - API请求耗时: 875ms
2025-06-06 08:03:40,014 - INFO - Response - Page 3
2025-06-06 08:03:40,014 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:40,514 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:40,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:40,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:41,217 - INFO - API请求耗时: 703ms
2025-06-06 08:03:41,217 - INFO - Response - Page 4
2025-06-06 08:03:41,233 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:41,733 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:41,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:41,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:42,607 - INFO - API请求耗时: 875ms
2025-06-06 08:03:42,607 - INFO - Response - Page 5
2025-06-06 08:03:42,607 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:03:43,123 - INFO - Request Parameters - Page 6:
2025-06-06 08:03:43,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:43,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:43,795 - INFO - API请求耗时: 672ms
2025-06-06 08:03:43,795 - INFO - Response - Page 6
2025-06-06 08:03:43,795 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:03:44,310 - INFO - Request Parameters - Page 7:
2025-06-06 08:03:44,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:44,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200476, 1745337600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:44,779 - INFO - API请求耗时: 469ms
2025-06-06 08:03:44,779 - INFO - Response - Page 7
2025-06-06 08:03:44,779 - INFO - 第 7 页获取到 21 条记录
2025-06-06 08:03:44,794 - INFO - 查询完成，共获取到 621 条记录
2025-06-06 08:03:44,794 - INFO - 分段 9 查询成功，获取到 621 条记录
2025-06-06 08:03:45,794 - INFO - 查询分段 10: 2025-04-24 至 2025-04-25
2025-06-06 08:03:45,794 - INFO - 查询日期范围: 2025-04-24 至 2025-04-25，使用分页查询，每页 100 条记录
2025-06-06 08:03:45,794 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:45,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:45,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:46,747 - INFO - API请求耗时: 953ms
2025-06-06 08:03:46,747 - INFO - Response - Page 1
2025-06-06 08:03:46,747 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:47,247 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:47,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:47,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:47,981 - INFO - API请求耗时: 734ms
2025-06-06 08:03:47,981 - INFO - Response - Page 2
2025-06-06 08:03:47,981 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:48,497 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:48,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:48,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:49,168 - INFO - API请求耗时: 672ms
2025-06-06 08:03:49,168 - INFO - Response - Page 3
2025-06-06 08:03:49,168 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:49,684 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:49,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:49,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:50,371 - INFO - API请求耗时: 687ms
2025-06-06 08:03:50,371 - INFO - Response - Page 4
2025-06-06 08:03:50,371 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:50,871 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:50,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:50,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:51,558 - INFO - API请求耗时: 687ms
2025-06-06 08:03:51,558 - INFO - Response - Page 5
2025-06-06 08:03:51,558 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:03:52,074 - INFO - Request Parameters - Page 6:
2025-06-06 08:03:52,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:52,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:52,808 - INFO - API请求耗时: 734ms
2025-06-06 08:03:52,808 - INFO - Response - Page 6
2025-06-06 08:03:52,808 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:03:53,308 - INFO - Request Parameters - Page 7:
2025-06-06 08:03:53,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:53,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000476, 1745510400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:53,823 - INFO - API请求耗时: 515ms
2025-06-06 08:03:53,823 - INFO - Response - Page 7
2025-06-06 08:03:53,823 - INFO - 第 7 页获取到 27 条记录
2025-06-06 08:03:53,823 - INFO - 查询完成，共获取到 627 条记录
2025-06-06 08:03:53,823 - INFO - 分段 10 查询成功，获取到 627 条记录
2025-06-06 08:03:54,839 - INFO - 查询分段 11: 2025-04-26 至 2025-04-27
2025-06-06 08:03:54,839 - INFO - 查询日期范围: 2025-04-26 至 2025-04-27，使用分页查询，每页 100 条记录
2025-06-06 08:03:54,839 - INFO - Request Parameters - Page 1:
2025-06-06 08:03:54,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:54,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:55,542 - INFO - API请求耗时: 703ms
2025-06-06 08:03:55,557 - INFO - Response - Page 1
2025-06-06 08:03:55,557 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:03:56,073 - INFO - Request Parameters - Page 2:
2025-06-06 08:03:56,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:56,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:56,776 - INFO - API请求耗时: 703ms
2025-06-06 08:03:56,776 - INFO - Response - Page 2
2025-06-06 08:03:56,776 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:03:57,276 - INFO - Request Parameters - Page 3:
2025-06-06 08:03:57,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:57,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:58,010 - INFO - API请求耗时: 734ms
2025-06-06 08:03:58,026 - INFO - Response - Page 3
2025-06-06 08:03:58,026 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:03:58,541 - INFO - Request Parameters - Page 4:
2025-06-06 08:03:58,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:58,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:03:59,291 - INFO - API请求耗时: 750ms
2025-06-06 08:03:59,291 - INFO - Response - Page 4
2025-06-06 08:03:59,291 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:03:59,806 - INFO - Request Parameters - Page 5:
2025-06-06 08:03:59,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:03:59,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:00,541 - INFO - API请求耗时: 734ms
2025-06-06 08:04:00,541 - INFO - Response - Page 5
2025-06-06 08:04:00,541 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:01,056 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:01,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:01,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:01,837 - INFO - API请求耗时: 781ms
2025-06-06 08:04:01,837 - INFO - Response - Page 6
2025-06-06 08:04:01,837 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:02,353 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:02,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:02,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800476, 1745683200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:02,977 - INFO - API请求耗时: 625ms
2025-06-06 08:04:02,977 - INFO - Response - Page 7
2025-06-06 08:04:02,977 - INFO - 第 7 页获取到 48 条记录
2025-06-06 08:04:02,977 - INFO - 查询完成，共获取到 648 条记录
2025-06-06 08:04:02,977 - INFO - 分段 11 查询成功，获取到 648 条记录
2025-06-06 08:04:03,993 - INFO - 查询分段 12: 2025-04-28 至 2025-04-29
2025-06-06 08:04:03,993 - INFO - 查询日期范围: 2025-04-28 至 2025-04-29，使用分页查询，每页 100 条记录
2025-06-06 08:04:03,993 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:03,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:03,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:04,790 - INFO - API请求耗时: 797ms
2025-06-06 08:04:04,790 - INFO - Response - Page 1
2025-06-06 08:04:04,790 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:05,305 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:05,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:05,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:06,039 - INFO - API请求耗时: 734ms
2025-06-06 08:04:06,039 - INFO - Response - Page 2
2025-06-06 08:04:06,039 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:04:06,539 - INFO - Request Parameters - Page 3:
2025-06-06 08:04:06,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:06,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:07,305 - INFO - API请求耗时: 765ms
2025-06-06 08:04:07,305 - INFO - Response - Page 3
2025-06-06 08:04:07,305 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:04:07,804 - INFO - Request Parameters - Page 4:
2025-06-06 08:04:07,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:07,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:08,523 - INFO - API请求耗时: 719ms
2025-06-06 08:04:08,539 - INFO - Response - Page 4
2025-06-06 08:04:08,539 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:04:09,054 - INFO - Request Parameters - Page 5:
2025-06-06 08:04:09,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:09,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:09,804 - INFO - API请求耗时: 750ms
2025-06-06 08:04:09,804 - INFO - Response - Page 5
2025-06-06 08:04:09,804 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:10,319 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:10,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:10,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:11,038 - INFO - API请求耗时: 719ms
2025-06-06 08:04:11,038 - INFO - Response - Page 6
2025-06-06 08:04:11,038 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:11,554 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:11,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:11,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600476, 1745856000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:12,147 - INFO - API请求耗时: 594ms
2025-06-06 08:04:12,147 - INFO - Response - Page 7
2025-06-06 08:04:12,163 - INFO - 第 7 页获取到 36 条记录
2025-06-06 08:04:12,163 - INFO - 查询完成，共获取到 636 条记录
2025-06-06 08:04:12,163 - INFO - 分段 12 查询成功，获取到 636 条记录
2025-06-06 08:04:13,178 - INFO - 查询分段 13: 2025-04-30 至 2025-05-01
2025-06-06 08:04:13,178 - INFO - 查询日期范围: 2025-04-30 至 2025-05-01，使用分页查询，每页 100 条记录
2025-06-06 08:04:13,178 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:13,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:13,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:13,928 - INFO - API请求耗时: 750ms
2025-06-06 08:04:13,928 - INFO - Response - Page 1
2025-06-06 08:04:13,928 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:14,443 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:14,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:14,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:15,068 - INFO - API请求耗时: 625ms
2025-06-06 08:04:15,068 - INFO - Response - Page 2
2025-06-06 08:04:15,068 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:04:15,568 - INFO - Request Parameters - Page 3:
2025-06-06 08:04:15,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:15,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:16,365 - INFO - API请求耗时: 797ms
2025-06-06 08:04:16,365 - INFO - Response - Page 3
2025-06-06 08:04:16,380 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:04:16,896 - INFO - Request Parameters - Page 4:
2025-06-06 08:04:16,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:16,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:17,630 - INFO - API请求耗时: 734ms
2025-06-06 08:04:17,630 - INFO - Response - Page 4
2025-06-06 08:04:17,630 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:04:18,146 - INFO - Request Parameters - Page 5:
2025-06-06 08:04:18,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:18,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:18,958 - INFO - API请求耗时: 812ms
2025-06-06 08:04:18,958 - INFO - Response - Page 5
2025-06-06 08:04:18,958 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:19,473 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:19,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:19,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:20,239 - INFO - API请求耗时: 765ms
2025-06-06 08:04:20,239 - INFO - Response - Page 6
2025-06-06 08:04:20,239 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:20,739 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:20,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:20,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400476, 1746028800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:21,364 - INFO - API请求耗时: 625ms
2025-06-06 08:04:21,364 - INFO - Response - Page 7
2025-06-06 08:04:21,364 - INFO - 第 7 页获取到 43 条记录
2025-06-06 08:04:21,364 - INFO - 查询完成，共获取到 643 条记录
2025-06-06 08:04:21,364 - INFO - 分段 13 查询成功，获取到 643 条记录
2025-06-06 08:04:22,379 - INFO - 查询分段 14: 2025-05-02 至 2025-05-03
2025-06-06 08:04:22,379 - INFO - 查询日期范围: 2025-05-02 至 2025-05-03，使用分页查询，每页 100 条记录
2025-06-06 08:04:22,379 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:22,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:22,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:23,238 - INFO - API请求耗时: 859ms
2025-06-06 08:04:23,238 - INFO - Response - Page 1
2025-06-06 08:04:23,238 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:23,738 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:23,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:23,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:24,457 - INFO - API请求耗时: 719ms
2025-06-06 08:04:24,457 - INFO - Response - Page 2
2025-06-06 08:04:24,457 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:04:24,972 - INFO - Request Parameters - Page 3:
2025-06-06 08:04:24,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:24,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:25,659 - INFO - API请求耗时: 687ms
2025-06-06 08:04:25,659 - INFO - Response - Page 3
2025-06-06 08:04:25,659 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:04:26,175 - INFO - Request Parameters - Page 4:
2025-06-06 08:04:26,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:26,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:26,925 - INFO - API请求耗时: 750ms
2025-06-06 08:04:26,925 - INFO - Response - Page 4
2025-06-06 08:04:26,925 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:04:27,440 - INFO - Request Parameters - Page 5:
2025-06-06 08:04:27,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:27,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:28,284 - INFO - API请求耗时: 844ms
2025-06-06 08:04:28,284 - INFO - Response - Page 5
2025-06-06 08:04:28,284 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:28,799 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:28,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:28,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:29,533 - INFO - API请求耗时: 734ms
2025-06-06 08:04:29,533 - INFO - Response - Page 6
2025-06-06 08:04:29,533 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:30,049 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:30,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:30,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200476, 1746201600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:30,611 - INFO - API请求耗时: 562ms
2025-06-06 08:04:30,611 - INFO - Response - Page 7
2025-06-06 08:04:30,611 - INFO - 第 7 页获取到 36 条记录
2025-06-06 08:04:30,611 - INFO - 查询完成，共获取到 636 条记录
2025-06-06 08:04:30,611 - INFO - 分段 14 查询成功，获取到 636 条记录
2025-06-06 08:04:31,627 - INFO - 查询分段 15: 2025-05-04 至 2025-05-05
2025-06-06 08:04:31,627 - INFO - 查询日期范围: 2025-05-04 至 2025-05-05，使用分页查询，每页 100 条记录
2025-06-06 08:04:31,627 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:31,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:31,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:32,361 - INFO - API请求耗时: 734ms
2025-06-06 08:04:32,361 - INFO - Response - Page 1
2025-06-06 08:04:32,361 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:32,876 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:32,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:32,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:33,579 - INFO - API请求耗时: 703ms
2025-06-06 08:04:33,579 - INFO - Response - Page 2
2025-06-06 08:04:33,579 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:04:34,095 - INFO - Request Parameters - Page 3:
2025-06-06 08:04:34,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:34,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:34,798 - INFO - API请求耗时: 703ms
2025-06-06 08:04:34,798 - INFO - Response - Page 3
2025-06-06 08:04:34,798 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:04:35,298 - INFO - Request Parameters - Page 4:
2025-06-06 08:04:35,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:35,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:36,048 - INFO - API请求耗时: 750ms
2025-06-06 08:04:36,048 - INFO - Response - Page 4
2025-06-06 08:04:36,048 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:04:36,563 - INFO - Request Parameters - Page 5:
2025-06-06 08:04:36,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:36,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:37,250 - INFO - API请求耗时: 687ms
2025-06-06 08:04:37,250 - INFO - Response - Page 5
2025-06-06 08:04:37,250 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:37,766 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:37,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:37,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:38,516 - INFO - API请求耗时: 750ms
2025-06-06 08:04:38,516 - INFO - Response - Page 6
2025-06-06 08:04:38,516 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:39,031 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:39,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:39,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000476, 1746374400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:39,609 - INFO - API请求耗时: 578ms
2025-06-06 08:04:39,609 - INFO - Response - Page 7
2025-06-06 08:04:39,609 - INFO - 第 7 页获取到 31 条记录
2025-06-06 08:04:39,609 - INFO - 查询完成，共获取到 631 条记录
2025-06-06 08:04:39,609 - INFO - 分段 15 查询成功，获取到 631 条记录
2025-06-06 08:04:40,625 - INFO - 查询分段 16: 2025-05-06 至 2025-05-07
2025-06-06 08:04:40,625 - INFO - 查询日期范围: 2025-05-06 至 2025-05-07，使用分页查询，每页 100 条记录
2025-06-06 08:04:40,625 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:40,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:40,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:41,359 - INFO - API请求耗时: 734ms
2025-06-06 08:04:41,359 - INFO - Response - Page 1
2025-06-06 08:04:41,359 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:41,874 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:41,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:41,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:42,608 - INFO - API请求耗时: 734ms
2025-06-06 08:04:42,608 - INFO - Response - Page 2
2025-06-06 08:04:42,608 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:04:43,124 - INFO - Request Parameters - Page 3:
2025-06-06 08:04:43,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:43,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:43,780 - INFO - API请求耗时: 656ms
2025-06-06 08:04:43,780 - INFO - Response - Page 3
2025-06-06 08:04:43,780 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:04:44,280 - INFO - Request Parameters - Page 4:
2025-06-06 08:04:44,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:44,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:44,967 - INFO - API请求耗时: 687ms
2025-06-06 08:04:44,967 - INFO - Response - Page 4
2025-06-06 08:04:44,967 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:04:45,483 - INFO - Request Parameters - Page 5:
2025-06-06 08:04:45,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:45,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:46,201 - INFO - API请求耗时: 719ms
2025-06-06 08:04:46,201 - INFO - Response - Page 5
2025-06-06 08:04:46,201 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:46,717 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:46,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:46,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:47,435 - INFO - API请求耗时: 719ms
2025-06-06 08:04:47,435 - INFO - Response - Page 6
2025-06-06 08:04:47,451 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:47,967 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:47,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:47,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800476, 1746547200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:48,544 - INFO - API请求耗时: 578ms
2025-06-06 08:04:48,544 - INFO - Response - Page 7
2025-06-06 08:04:48,544 - INFO - 第 7 页获取到 18 条记录
2025-06-06 08:04:48,544 - INFO - 查询完成，共获取到 618 条记录
2025-06-06 08:04:48,544 - INFO - 分段 16 查询成功，获取到 618 条记录
2025-06-06 08:04:49,560 - INFO - 查询分段 17: 2025-05-08 至 2025-05-09
2025-06-06 08:04:49,560 - INFO - 查询日期范围: 2025-05-08 至 2025-05-09，使用分页查询，每页 100 条记录
2025-06-06 08:04:49,560 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:49,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:49,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:50,294 - INFO - API请求耗时: 734ms
2025-06-06 08:04:50,294 - INFO - Response - Page 1
2025-06-06 08:04:50,294 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:50,810 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:50,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:50,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:51,513 - INFO - API请求耗时: 703ms
2025-06-06 08:04:51,513 - INFO - Response - Page 2
2025-06-06 08:04:51,513 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:04:52,012 - INFO - Request Parameters - Page 3:
2025-06-06 08:04:52,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:52,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:52,637 - INFO - API请求耗时: 625ms
2025-06-06 08:04:52,637 - INFO - Response - Page 3
2025-06-06 08:04:52,637 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:04:53,137 - INFO - Request Parameters - Page 4:
2025-06-06 08:04:53,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:53,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:54,137 - INFO - API请求耗时: 1000ms
2025-06-06 08:04:54,137 - INFO - Response - Page 4
2025-06-06 08:04:54,137 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:04:54,652 - INFO - Request Parameters - Page 5:
2025-06-06 08:04:54,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:54,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:55,340 - INFO - API请求耗时: 687ms
2025-06-06 08:04:55,340 - INFO - Response - Page 5
2025-06-06 08:04:55,340 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:04:55,855 - INFO - Request Parameters - Page 6:
2025-06-06 08:04:55,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:55,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:56,527 - INFO - API请求耗时: 672ms
2025-06-06 08:04:56,527 - INFO - Response - Page 6
2025-06-06 08:04:56,527 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:04:57,027 - INFO - Request Parameters - Page 7:
2025-06-06 08:04:57,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:57,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600476, 1746720000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:57,574 - INFO - API请求耗时: 547ms
2025-06-06 08:04:57,574 - INFO - Response - Page 7
2025-06-06 08:04:57,574 - INFO - 第 7 页获取到 42 条记录
2025-06-06 08:04:57,574 - INFO - 查询完成，共获取到 642 条记录
2025-06-06 08:04:57,574 - INFO - 分段 17 查询成功，获取到 642 条记录
2025-06-06 08:04:58,589 - INFO - 查询分段 18: 2025-05-10 至 2025-05-11
2025-06-06 08:04:58,589 - INFO - 查询日期范围: 2025-05-10 至 2025-05-11，使用分页查询，每页 100 条记录
2025-06-06 08:04:58,589 - INFO - Request Parameters - Page 1:
2025-06-06 08:04:58,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:58,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:04:59,339 - INFO - API请求耗时: 750ms
2025-06-06 08:04:59,339 - INFO - Response - Page 1
2025-06-06 08:04:59,339 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:04:59,854 - INFO - Request Parameters - Page 2:
2025-06-06 08:04:59,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:04:59,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:00,557 - INFO - API请求耗时: 703ms
2025-06-06 08:05:00,557 - INFO - Response - Page 2
2025-06-06 08:05:00,557 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:01,088 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:01,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:01,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:01,791 - INFO - API请求耗时: 703ms
2025-06-06 08:05:01,791 - INFO - Response - Page 3
2025-06-06 08:05:01,791 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:02,307 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:02,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:02,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:03,041 - INFO - API请求耗时: 734ms
2025-06-06 08:05:03,041 - INFO - Response - Page 4
2025-06-06 08:05:03,041 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:03,541 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:03,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:03,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:04,244 - INFO - API请求耗时: 703ms
2025-06-06 08:05:04,244 - INFO - Response - Page 5
2025-06-06 08:05:04,244 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:04,759 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:04,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:04,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:05,603 - INFO - API请求耗时: 844ms
2025-06-06 08:05:05,603 - INFO - Response - Page 6
2025-06-06 08:05:05,603 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:05:06,118 - INFO - Request Parameters - Page 7:
2025-06-06 08:05:06,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:06,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400476, 1746892800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:06,774 - INFO - API请求耗时: 656ms
2025-06-06 08:05:06,774 - INFO - Response - Page 7
2025-06-06 08:05:06,774 - INFO - 第 7 页获取到 52 条记录
2025-06-06 08:05:06,774 - INFO - 查询完成，共获取到 652 条记录
2025-06-06 08:05:06,774 - INFO - 分段 18 查询成功，获取到 652 条记录
2025-06-06 08:05:07,790 - INFO - 查询分段 19: 2025-05-12 至 2025-05-13
2025-06-06 08:05:07,790 - INFO - 查询日期范围: 2025-05-12 至 2025-05-13，使用分页查询，每页 100 条记录
2025-06-06 08:05:07,790 - INFO - Request Parameters - Page 1:
2025-06-06 08:05:07,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:07,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:08,586 - INFO - API请求耗时: 797ms
2025-06-06 08:05:08,586 - INFO - Response - Page 1
2025-06-06 08:05:08,586 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:05:09,102 - INFO - Request Parameters - Page 2:
2025-06-06 08:05:09,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:09,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:09,758 - INFO - API请求耗时: 656ms
2025-06-06 08:05:09,758 - INFO - Response - Page 2
2025-06-06 08:05:09,758 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:10,258 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:10,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:10,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:10,898 - INFO - API请求耗时: 640ms
2025-06-06 08:05:10,898 - INFO - Response - Page 3
2025-06-06 08:05:10,898 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:11,414 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:11,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:11,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:12,148 - INFO - API请求耗时: 734ms
2025-06-06 08:05:12,148 - INFO - Response - Page 4
2025-06-06 08:05:12,148 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:12,664 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:12,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:12,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:13,382 - INFO - API请求耗时: 719ms
2025-06-06 08:05:13,382 - INFO - Response - Page 5
2025-06-06 08:05:13,382 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:13,898 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:13,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:13,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:14,632 - INFO - API请求耗时: 734ms
2025-06-06 08:05:14,632 - INFO - Response - Page 6
2025-06-06 08:05:14,632 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:05:15,147 - INFO - Request Parameters - Page 7:
2025-06-06 08:05:15,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:15,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200476, 1747065600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:15,710 - INFO - API请求耗时: 562ms
2025-06-06 08:05:15,710 - INFO - Response - Page 7
2025-06-06 08:05:15,710 - INFO - 第 7 页获取到 30 条记录
2025-06-06 08:05:15,710 - INFO - 查询完成，共获取到 630 条记录
2025-06-06 08:05:15,710 - INFO - 分段 19 查询成功，获取到 630 条记录
2025-06-06 08:05:16,710 - INFO - 查询分段 20: 2025-05-14 至 2025-05-15
2025-06-06 08:05:16,710 - INFO - 查询日期范围: 2025-05-14 至 2025-05-15，使用分页查询，每页 100 条记录
2025-06-06 08:05:16,710 - INFO - Request Parameters - Page 1:
2025-06-06 08:05:16,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:16,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:17,459 - INFO - API请求耗时: 750ms
2025-06-06 08:05:17,459 - INFO - Response - Page 1
2025-06-06 08:05:17,459 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:05:17,975 - INFO - Request Parameters - Page 2:
2025-06-06 08:05:17,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:17,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:18,740 - INFO - API请求耗时: 765ms
2025-06-06 08:05:18,740 - INFO - Response - Page 2
2025-06-06 08:05:18,740 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:19,256 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:19,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:19,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:19,943 - INFO - API请求耗时: 687ms
2025-06-06 08:05:19,943 - INFO - Response - Page 3
2025-06-06 08:05:19,943 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:20,459 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:20,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:20,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:21,193 - INFO - API请求耗时: 734ms
2025-06-06 08:05:21,193 - INFO - Response - Page 4
2025-06-06 08:05:21,193 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:21,693 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:21,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:21,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:22,349 - INFO - API请求耗时: 656ms
2025-06-06 08:05:22,349 - INFO - Response - Page 5
2025-06-06 08:05:22,349 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:22,849 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:22,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:22,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:23,489 - INFO - API请求耗时: 640ms
2025-06-06 08:05:23,489 - INFO - Response - Page 6
2025-06-06 08:05:23,489 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:05:23,989 - INFO - Request Parameters - Page 7:
2025-06-06 08:05:23,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:23,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000476, 1747238400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:24,614 - INFO - API请求耗时: 625ms
2025-06-06 08:05:24,614 - INFO - Response - Page 7
2025-06-06 08:05:24,614 - INFO - 第 7 页获取到 30 条记录
2025-06-06 08:05:24,614 - INFO - 查询完成，共获取到 630 条记录
2025-06-06 08:05:24,614 - INFO - 分段 20 查询成功，获取到 630 条记录
2025-06-06 08:05:25,614 - INFO - 查询分段 21: 2025-05-16 至 2025-05-17
2025-06-06 08:05:25,614 - INFO - 查询日期范围: 2025-05-16 至 2025-05-17，使用分页查询，每页 100 条记录
2025-06-06 08:05:25,614 - INFO - Request Parameters - Page 1:
2025-06-06 08:05:25,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:25,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:26,426 - INFO - API请求耗时: 812ms
2025-06-06 08:05:26,426 - INFO - Response - Page 1
2025-06-06 08:05:26,426 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:05:26,941 - INFO - Request Parameters - Page 2:
2025-06-06 08:05:26,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:26,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:27,660 - INFO - API请求耗时: 719ms
2025-06-06 08:05:27,660 - INFO - Response - Page 2
2025-06-06 08:05:27,660 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:28,175 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:28,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:28,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:29,081 - INFO - API请求耗时: 906ms
2025-06-06 08:05:29,081 - INFO - Response - Page 3
2025-06-06 08:05:29,081 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:29,597 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:29,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:29,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:30,331 - INFO - API请求耗时: 734ms
2025-06-06 08:05:30,331 - INFO - Response - Page 4
2025-06-06 08:05:30,331 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:30,831 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:30,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:30,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:31,612 - INFO - API请求耗时: 781ms
2025-06-06 08:05:31,612 - INFO - Response - Page 5
2025-06-06 08:05:31,612 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:32,112 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:32,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:32,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:32,846 - INFO - API请求耗时: 734ms
2025-06-06 08:05:32,846 - INFO - Response - Page 6
2025-06-06 08:05:32,846 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:05:33,362 - INFO - Request Parameters - Page 7:
2025-06-06 08:05:33,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:33,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800476, 1747411200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:33,893 - INFO - API请求耗时: 531ms
2025-06-06 08:05:33,893 - INFO - Response - Page 7
2025-06-06 08:05:33,893 - INFO - 第 7 页获取到 45 条记录
2025-06-06 08:05:33,893 - INFO - 查询完成，共获取到 645 条记录
2025-06-06 08:05:33,893 - INFO - 分段 21 查询成功，获取到 645 条记录
2025-06-06 08:05:34,908 - INFO - 查询分段 22: 2025-05-18 至 2025-05-19
2025-06-06 08:05:34,908 - INFO - 查询日期范围: 2025-05-18 至 2025-05-19，使用分页查询，每页 100 条记录
2025-06-06 08:05:34,908 - INFO - Request Parameters - Page 1:
2025-06-06 08:05:34,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:34,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:35,642 - INFO - API请求耗时: 734ms
2025-06-06 08:05:35,642 - INFO - Response - Page 1
2025-06-06 08:05:35,642 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:05:36,142 - INFO - Request Parameters - Page 2:
2025-06-06 08:05:36,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:36,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:36,876 - INFO - API请求耗时: 734ms
2025-06-06 08:05:36,876 - INFO - Response - Page 2
2025-06-06 08:05:36,876 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:37,392 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:37,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:37,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:38,111 - INFO - API请求耗时: 719ms
2025-06-06 08:05:38,126 - INFO - Response - Page 3
2025-06-06 08:05:38,126 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:38,642 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:38,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:38,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:39,376 - INFO - API请求耗时: 734ms
2025-06-06 08:05:39,376 - INFO - Response - Page 4
2025-06-06 08:05:39,376 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:39,891 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:39,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:39,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:40,641 - INFO - API请求耗时: 750ms
2025-06-06 08:05:40,641 - INFO - Response - Page 5
2025-06-06 08:05:40,641 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:41,141 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:41,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:41,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:41,828 - INFO - API请求耗时: 687ms
2025-06-06 08:05:41,828 - INFO - Response - Page 6
2025-06-06 08:05:41,828 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:05:42,344 - INFO - Request Parameters - Page 7:
2025-06-06 08:05:42,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:42,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600476, 1747584000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:42,875 - INFO - API请求耗时: 531ms
2025-06-06 08:05:42,875 - INFO - Response - Page 7
2025-06-06 08:05:42,875 - INFO - 第 7 页获取到 30 条记录
2025-06-06 08:05:42,875 - INFO - 查询完成，共获取到 630 条记录
2025-06-06 08:05:42,875 - INFO - 分段 22 查询成功，获取到 630 条记录
2025-06-06 08:05:43,890 - INFO - 查询分段 23: 2025-05-20 至 2025-05-21
2025-06-06 08:05:43,890 - INFO - 查询日期范围: 2025-05-20 至 2025-05-21，使用分页查询，每页 100 条记录
2025-06-06 08:05:43,890 - INFO - Request Parameters - Page 1:
2025-06-06 08:05:43,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:43,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:44,656 - INFO - API请求耗时: 765ms
2025-06-06 08:05:44,656 - INFO - Response - Page 1
2025-06-06 08:05:44,656 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:05:45,156 - INFO - Request Parameters - Page 2:
2025-06-06 08:05:45,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:45,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:45,859 - INFO - API请求耗时: 703ms
2025-06-06 08:05:45,859 - INFO - Response - Page 2
2025-06-06 08:05:45,859 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:46,359 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:46,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:46,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:47,077 - INFO - API请求耗时: 719ms
2025-06-06 08:05:47,077 - INFO - Response - Page 3
2025-06-06 08:05:47,077 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:47,593 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:47,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:47,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:48,436 - INFO - API请求耗时: 844ms
2025-06-06 08:05:48,436 - INFO - Response - Page 4
2025-06-06 08:05:48,436 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:48,936 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:48,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:48,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:49,608 - INFO - API请求耗时: 672ms
2025-06-06 08:05:49,608 - INFO - Response - Page 5
2025-06-06 08:05:49,608 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:50,123 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:50,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:50,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:50,826 - INFO - API请求耗时: 703ms
2025-06-06 08:05:50,826 - INFO - Response - Page 6
2025-06-06 08:05:50,826 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:05:51,326 - INFO - Request Parameters - Page 7:
2025-06-06 08:05:51,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:51,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400476, 1747756800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:51,888 - INFO - API请求耗时: 562ms
2025-06-06 08:05:51,888 - INFO - Response - Page 7
2025-06-06 08:05:51,888 - INFO - 第 7 页获取到 24 条记录
2025-06-06 08:05:51,888 - INFO - 查询完成，共获取到 624 条记录
2025-06-06 08:05:51,888 - INFO - 分段 23 查询成功，获取到 624 条记录
2025-06-06 08:05:52,904 - INFO - 查询分段 24: 2025-05-22 至 2025-05-23
2025-06-06 08:05:52,904 - INFO - 查询日期范围: 2025-05-22 至 2025-05-23，使用分页查询，每页 100 条记录
2025-06-06 08:05:52,904 - INFO - Request Parameters - Page 1:
2025-06-06 08:05:52,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:52,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:53,669 - INFO - API请求耗时: 765ms
2025-06-06 08:05:53,669 - INFO - Response - Page 1
2025-06-06 08:05:53,669 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:05:54,185 - INFO - Request Parameters - Page 2:
2025-06-06 08:05:54,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:54,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:54,856 - INFO - API请求耗时: 672ms
2025-06-06 08:05:54,856 - INFO - Response - Page 2
2025-06-06 08:05:54,856 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:05:55,356 - INFO - Request Parameters - Page 3:
2025-06-06 08:05:55,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:55,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:55,997 - INFO - API请求耗时: 640ms
2025-06-06 08:05:56,012 - INFO - Response - Page 3
2025-06-06 08:05:56,012 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:05:56,528 - INFO - Request Parameters - Page 4:
2025-06-06 08:05:56,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:56,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:57,231 - INFO - API请求耗时: 703ms
2025-06-06 08:05:57,231 - INFO - Response - Page 4
2025-06-06 08:05:57,231 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:05:57,746 - INFO - Request Parameters - Page 5:
2025-06-06 08:05:57,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:57,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:58,621 - INFO - API请求耗时: 875ms
2025-06-06 08:05:58,621 - INFO - Response - Page 5
2025-06-06 08:05:58,621 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:05:59,121 - INFO - Request Parameters - Page 6:
2025-06-06 08:05:59,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:05:59,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:05:59,855 - INFO - API请求耗时: 734ms
2025-06-06 08:05:59,855 - INFO - Response - Page 6
2025-06-06 08:05:59,855 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:06:00,371 - INFO - Request Parameters - Page 7:
2025-06-06 08:06:00,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:00,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200476, 1747929600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:00,886 - INFO - API请求耗时: 516ms
2025-06-06 08:06:00,886 - INFO - Response - Page 7
2025-06-06 08:06:00,886 - INFO - 第 7 页获取到 24 条记录
2025-06-06 08:06:00,886 - INFO - 查询完成，共获取到 624 条记录
2025-06-06 08:06:00,886 - INFO - 分段 24 查询成功，获取到 624 条记录
2025-06-06 08:06:01,902 - INFO - 查询分段 25: 2025-05-24 至 2025-05-25
2025-06-06 08:06:01,902 - INFO - 查询日期范围: 2025-05-24 至 2025-05-25，使用分页查询，每页 100 条记录
2025-06-06 08:06:01,902 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:01,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:01,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:02,667 - INFO - API请求耗时: 765ms
2025-06-06 08:06:02,667 - INFO - Response - Page 1
2025-06-06 08:06:02,667 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:06:03,167 - INFO - Request Parameters - Page 2:
2025-06-06 08:06:03,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:03,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:04,026 - INFO - API请求耗时: 859ms
2025-06-06 08:06:04,026 - INFO - Response - Page 2
2025-06-06 08:06:04,026 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:06:04,542 - INFO - Request Parameters - Page 3:
2025-06-06 08:06:04,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:04,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:05,229 - INFO - API请求耗时: 687ms
2025-06-06 08:06:05,245 - INFO - Response - Page 3
2025-06-06 08:06:05,245 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:06:05,760 - INFO - Request Parameters - Page 4:
2025-06-06 08:06:05,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:05,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:06,447 - INFO - API请求耗时: 687ms
2025-06-06 08:06:06,447 - INFO - Response - Page 4
2025-06-06 08:06:06,447 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:06:06,963 - INFO - Request Parameters - Page 5:
2025-06-06 08:06:06,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:06,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:07,713 - INFO - API请求耗时: 750ms
2025-06-06 08:06:07,713 - INFO - Response - Page 5
2025-06-06 08:06:07,713 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:06:08,228 - INFO - Request Parameters - Page 6:
2025-06-06 08:06:08,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:08,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:08,962 - INFO - API请求耗时: 734ms
2025-06-06 08:06:08,962 - INFO - Response - Page 6
2025-06-06 08:06:08,962 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:06:09,478 - INFO - Request Parameters - Page 7:
2025-06-06 08:06:09,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:09,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000476, 1748102400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:10,056 - INFO - API请求耗时: 578ms
2025-06-06 08:06:10,056 - INFO - Response - Page 7
2025-06-06 08:06:10,056 - INFO - 第 7 页获取到 27 条记录
2025-06-06 08:06:10,056 - INFO - 查询完成，共获取到 627 条记录
2025-06-06 08:06:10,056 - INFO - 分段 25 查询成功，获取到 627 条记录
2025-06-06 08:06:11,071 - INFO - 查询分段 26: 2025-05-26 至 2025-05-27
2025-06-06 08:06:11,071 - INFO - 查询日期范围: 2025-05-26 至 2025-05-27，使用分页查询，每页 100 条记录
2025-06-06 08:06:11,071 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:11,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:11,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:11,790 - INFO - API请求耗时: 719ms
2025-06-06 08:06:11,790 - INFO - Response - Page 1
2025-06-06 08:06:11,790 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:06:12,305 - INFO - Request Parameters - Page 2:
2025-06-06 08:06:12,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:12,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:13,008 - INFO - API请求耗时: 703ms
2025-06-06 08:06:13,008 - INFO - Response - Page 2
2025-06-06 08:06:13,008 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:06:13,508 - INFO - Request Parameters - Page 3:
2025-06-06 08:06:13,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:13,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:14,211 - INFO - API请求耗时: 703ms
2025-06-06 08:06:14,211 - INFO - Response - Page 3
2025-06-06 08:06:14,227 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:06:14,742 - INFO - Request Parameters - Page 4:
2025-06-06 08:06:14,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:14,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:15,445 - INFO - API请求耗时: 703ms
2025-06-06 08:06:15,445 - INFO - Response - Page 4
2025-06-06 08:06:15,445 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:06:15,945 - INFO - Request Parameters - Page 5:
2025-06-06 08:06:15,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:15,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:16,632 - INFO - API请求耗时: 687ms
2025-06-06 08:06:16,632 - INFO - Response - Page 5
2025-06-06 08:06:16,632 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:06:17,132 - INFO - Request Parameters - Page 6:
2025-06-06 08:06:17,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:17,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:17,898 - INFO - API请求耗时: 765ms
2025-06-06 08:06:17,898 - INFO - Response - Page 6
2025-06-06 08:06:17,898 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:06:18,413 - INFO - Request Parameters - Page 7:
2025-06-06 08:06:18,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:18,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800476, 1748275200476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:18,944 - INFO - API请求耗时: 531ms
2025-06-06 08:06:18,944 - INFO - Response - Page 7
2025-06-06 08:06:18,944 - INFO - 第 7 页获取到 15 条记录
2025-06-06 08:06:18,944 - INFO - 查询完成，共获取到 615 条记录
2025-06-06 08:06:18,944 - INFO - 分段 26 查询成功，获取到 615 条记录
2025-06-06 08:06:19,960 - INFO - 查询分段 27: 2025-05-28 至 2025-05-29
2025-06-06 08:06:19,960 - INFO - 查询日期范围: 2025-05-28 至 2025-05-29，使用分页查询，每页 100 条记录
2025-06-06 08:06:19,960 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:19,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:19,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:20,694 - INFO - API请求耗时: 734ms
2025-06-06 08:06:20,694 - INFO - Response - Page 1
2025-06-06 08:06:20,694 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:06:21,209 - INFO - Request Parameters - Page 2:
2025-06-06 08:06:21,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:21,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:21,897 - INFO - API请求耗时: 687ms
2025-06-06 08:06:21,897 - INFO - Response - Page 2
2025-06-06 08:06:21,897 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:06:22,412 - INFO - Request Parameters - Page 3:
2025-06-06 08:06:22,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:22,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:23,115 - INFO - API请求耗时: 703ms
2025-06-06 08:06:23,115 - INFO - Response - Page 3
2025-06-06 08:06:23,115 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:06:23,631 - INFO - Request Parameters - Page 4:
2025-06-06 08:06:23,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:23,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:24,349 - INFO - API请求耗时: 719ms
2025-06-06 08:06:24,349 - INFO - Response - Page 4
2025-06-06 08:06:24,349 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:06:24,865 - INFO - Request Parameters - Page 5:
2025-06-06 08:06:24,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:24,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:25,521 - INFO - API请求耗时: 656ms
2025-06-06 08:06:25,521 - INFO - Response - Page 5
2025-06-06 08:06:25,521 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:06:26,036 - INFO - Request Parameters - Page 6:
2025-06-06 08:06:26,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:26,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:26,802 - INFO - API请求耗时: 765ms
2025-06-06 08:06:26,802 - INFO - Response - Page 6
2025-06-06 08:06:26,802 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:06:27,317 - INFO - Request Parameters - Page 7:
2025-06-06 08:06:27,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:27,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600476, 1748448000476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:27,802 - INFO - API请求耗时: 484ms
2025-06-06 08:06:27,802 - INFO - Response - Page 7
2025-06-06 08:06:27,802 - INFO - 第 7 页获取到 18 条记录
2025-06-06 08:06:27,802 - INFO - 查询完成，共获取到 618 条记录
2025-06-06 08:06:27,802 - INFO - 分段 27 查询成功，获取到 618 条记录
2025-06-06 08:06:28,817 - INFO - 查询分段 28: 2025-05-30 至 2025-05-31
2025-06-06 08:06:28,817 - INFO - 查询日期范围: 2025-05-30 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-06 08:06:28,817 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:28,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:28,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:29,442 - INFO - API请求耗时: 625ms
2025-06-06 08:06:29,442 - INFO - Response - Page 1
2025-06-06 08:06:29,442 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:06:29,957 - INFO - Request Parameters - Page 2:
2025-06-06 08:06:29,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:29,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:30,692 - INFO - API请求耗时: 734ms
2025-06-06 08:06:30,692 - INFO - Response - Page 2
2025-06-06 08:06:30,692 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:06:31,207 - INFO - Request Parameters - Page 3:
2025-06-06 08:06:31,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:31,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:31,910 - INFO - API请求耗时: 703ms
2025-06-06 08:06:31,910 - INFO - Response - Page 3
2025-06-06 08:06:31,910 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:06:32,425 - INFO - Request Parameters - Page 4:
2025-06-06 08:06:32,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:32,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:33,175 - INFO - API请求耗时: 750ms
2025-06-06 08:06:33,175 - INFO - Response - Page 4
2025-06-06 08:06:33,175 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:06:33,691 - INFO - Request Parameters - Page 5:
2025-06-06 08:06:33,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:33,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:34,456 - INFO - API请求耗时: 765ms
2025-06-06 08:06:34,456 - INFO - Response - Page 5
2025-06-06 08:06:34,456 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:06:34,956 - INFO - Request Parameters - Page 6:
2025-06-06 08:06:34,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:34,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:35,690 - INFO - API请求耗时: 734ms
2025-06-06 08:06:35,690 - INFO - Response - Page 6
2025-06-06 08:06:35,690 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:06:36,206 - INFO - Request Parameters - Page 7:
2025-06-06 08:06:36,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:36,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400476, 1748620800476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:36,706 - INFO - API请求耗时: 500ms
2025-06-06 08:06:36,706 - INFO - Response - Page 7
2025-06-06 08:06:36,721 - INFO - 第 7 页获取到 25 条记录
2025-06-06 08:06:36,721 - INFO - 查询完成，共获取到 625 条记录
2025-06-06 08:06:36,721 - INFO - 分段 28 查询成功，获取到 625 条记录
2025-06-06 08:06:37,721 - INFO - 查询分段 29: 2025-06-01 至 2025-06-02
2025-06-06 08:06:37,721 - INFO - 查询日期范围: 2025-06-01 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-06 08:06:37,721 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:37,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:37,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200476, 1748793600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:38,440 - INFO - API请求耗时: 719ms
2025-06-06 08:06:38,440 - INFO - Response - Page 1
2025-06-06 08:06:38,455 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:06:38,971 - INFO - Request Parameters - Page 2:
2025-06-06 08:06:38,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:38,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200476, 1748793600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:39,658 - INFO - API请求耗时: 687ms
2025-06-06 08:06:39,658 - INFO - Response - Page 2
2025-06-06 08:06:39,658 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:06:40,174 - INFO - Request Parameters - Page 3:
2025-06-06 08:06:40,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:40,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200476, 1748793600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:40,845 - INFO - API请求耗时: 672ms
2025-06-06 08:06:40,845 - INFO - Response - Page 3
2025-06-06 08:06:40,845 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:06:41,345 - INFO - Request Parameters - Page 4:
2025-06-06 08:06:41,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:41,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200476, 1748793600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:42,220 - INFO - API请求耗时: 875ms
2025-06-06 08:06:42,220 - INFO - Response - Page 4
2025-06-06 08:06:42,220 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:06:42,735 - INFO - Request Parameters - Page 5:
2025-06-06 08:06:42,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:42,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200476, 1748793600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:43,438 - INFO - API请求耗时: 703ms
2025-06-06 08:06:43,438 - INFO - Response - Page 5
2025-06-06 08:06:43,438 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:06:43,954 - INFO - Request Parameters - Page 6:
2025-06-06 08:06:43,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:43,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200476, 1748793600476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:44,563 - INFO - API请求耗时: 609ms
2025-06-06 08:06:44,563 - INFO - Response - Page 6
2025-06-06 08:06:44,563 - INFO - 第 6 页获取到 48 条记录
2025-06-06 08:06:44,563 - INFO - 查询完成，共获取到 548 条记录
2025-06-06 08:06:44,563 - INFO - 分段 29 查询成功，获取到 548 条记录
2025-06-06 08:06:45,579 - INFO - 查询分段 30: 2025-06-03 至 2025-06-04
2025-06-06 08:06:45,579 - INFO - 查询日期范围: 2025-06-03 至 2025-06-04，使用分页查询，每页 100 条记录
2025-06-06 08:06:45,579 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:45,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:45,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000476, 1748966400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:46,250 - INFO - API请求耗时: 672ms
2025-06-06 08:06:46,250 - INFO - Response - Page 1
2025-06-06 08:06:46,250 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:06:46,766 - INFO - Request Parameters - Page 2:
2025-06-06 08:06:46,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:46,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000476, 1748966400476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:47,500 - INFO - API请求耗时: 734ms
2025-06-06 08:06:47,500 - INFO - Response - Page 2
2025-06-06 08:06:47,500 - INFO - 第 2 页获取到 96 条记录
2025-06-06 08:06:47,500 - INFO - 查询完成，共获取到 196 条记录
2025-06-06 08:06:47,500 - INFO - 分段 30 查询成功，获取到 196 条记录
2025-06-06 08:06:48,515 - INFO - 查询分段 31: 2025-06-05 至 2025-06-05
2025-06-06 08:06:48,515 - INFO - 查询日期范围: 2025-06-05 至 2025-06-05，使用分页查询，每页 100 条记录
2025-06-06 08:06:48,515 - INFO - Request Parameters - Page 1:
2025-06-06 08:06:48,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:06:48,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800476, 1749139199476], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:06:48,953 - INFO - API请求耗时: 437ms
2025-06-06 08:06:48,953 - INFO - Response - Page 1
2025-06-06 08:06:48,953 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:06:48,953 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:06:48,953 - WARNING - 分段 31 查询返回空数据
2025-06-06 08:06:49,968 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 18511 条记录，失败 0 次
2025-06-06 08:06:49,968 - INFO - 成功获取宜搭日销售表单数据，共 18511 条记录
2025-06-06 08:06:49,968 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-06 08:06:49,968 - INFO - 开始对比和同步日销售数据...
2025-06-06 08:06:50,484 - INFO - 成功创建宜搭日销售数据索引，共 6322 条记录
2025-06-06 08:06:50,484 - INFO - 开始处理数衍数据，共 12872 条记录
2025-06-06 08:06:51,202 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMQY
2025-06-06 08:06:51,202 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 554.41}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 554.41}]
2025-06-06 08:06:51,655 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMSY
2025-06-06 08:06:51,655 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6969.0, 'new_value': 4105.0}, {'field': 'amount', 'old_value': 6969.0, 'new_value': 4105.0}]
2025-06-06 08:06:52,202 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBM0Z
2025-06-06 08:06:52,218 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3797.83, 'new_value': 3997.0}, {'field': 'amount', 'old_value': 3797.83, 'new_value': 3997.0}, {'field': 'count', 'old_value': 164, 'new_value': 165}, {'field': 'instoreAmount', 'old_value': 747.62, 'new_value': 946.79}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 49}]
2025-06-06 08:06:52,686 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMEZ
2025-06-06 08:06:52,686 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10000.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10000.0}]
2025-06-06 08:06:53,280 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMMZ
2025-06-06 08:06:53,280 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 12478.25, 'new_value': 10900.0}, {'field': 'amount', 'old_value': 12478.25, 'new_value': 10900.0}]
2025-06-06 08:06:53,827 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMOZ
2025-06-06 08:06:53,827 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9242.69, 'new_value': 8416.39}, {'field': 'dailyBillAmount', 'old_value': 9242.69, 'new_value': 8416.39}]
2025-06-06 08:06:54,420 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBM601
2025-06-06 08:06:54,420 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5610.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5610.0}]
2025-06-06 08:06:54,842 - INFO - 更新表单数据成功: FINST-T9D66B810AVVP9XIFPYE34WEQSBO31REYKEBM34
2025-06-06 08:06:54,842 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 23196.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 23196.5}]
2025-06-06 08:06:55,311 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMFJ
2025-06-06 08:06:55,311 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6064.0, 'new_value': 3092.0}, {'field': 'amount', 'old_value': 6064.0, 'new_value': 3092.0}]
2025-06-06 08:06:55,873 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMNJ
2025-06-06 08:06:55,873 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3098.54, 'new_value': 4038.8}, {'field': 'amount', 'old_value': 3098.54, 'new_value': 4038.8}, {'field': 'count', 'old_value': 215, 'new_value': 216}, {'field': 'instoreAmount', 'old_value': 941.07, 'new_value': 1881.33}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 51}]
2025-06-06 08:06:56,295 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBM1K
2025-06-06 08:06:56,295 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6102.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6102.5}]
2025-06-06 08:06:56,748 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBM9K
2025-06-06 08:06:56,748 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10603.52, 'new_value': 9561.0}, {'field': 'amount', 'old_value': 10603.52, 'new_value': 9561.0}]
2025-06-06 08:06:57,154 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMBK
2025-06-06 08:06:57,154 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7366.52, 'new_value': 6622.05}, {'field': 'dailyBillAmount', 'old_value': 7366.52, 'new_value': 6622.05}]
2025-06-06 08:06:57,607 - INFO - 更新表单数据成功: FINST-8LC66GC1EUVVDVBV6H44M8Q4ARX62FHPYKEBM37
2025-06-06 08:06:57,607 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15763.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15763.1}]
2025-06-06 08:06:58,122 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3DGXYKEBMA61
2025-06-06 08:06:58,122 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5908.0, 'new_value': 3031.0}, {'field': 'amount', 'old_value': 5908.0, 'new_value': 3031.0}]
2025-06-06 08:06:58,653 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3DGXYKEBMI61
2025-06-06 08:06:58,653 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3604.8, 'new_value': 4642.0}, {'field': 'amount', 'old_value': 3604.8, 'new_value': 4642.0}, {'field': 'count', 'old_value': 257, 'new_value': 258}, {'field': 'instoreAmount', 'old_value': 809.49, 'new_value': 1846.69}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 40}]
2025-06-06 08:06:59,122 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3EGXYKEBMV61
2025-06-06 08:06:59,122 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10123.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10123.5}]
2025-06-06 08:06:59,606 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3EGXYKEBM371
2025-06-06 08:06:59,606 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9557.66, 'new_value': 8637.0}, {'field': 'amount', 'old_value': 9557.66, 'new_value': 8637.0}]
2025-06-06 08:07:00,091 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3EGXYKEBM571
2025-06-06 08:07:00,091 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7240.92, 'new_value': 6580.86}, {'field': 'dailyBillAmount', 'old_value': 7240.92, 'new_value': 6580.86}]
2025-06-06 08:07:00,497 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMYO
2025-06-06 08:07:00,497 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1964.28, 'new_value': 2697.65}, {'field': 'dailyBillAmount', 'old_value': 1964.28, 'new_value': 2697.65}]
2025-06-06 08:07:00,919 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBM0P
2025-06-06 08:07:00,919 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6643.0, 'new_value': 2438.0}, {'field': 'amount', 'old_value': 6643.0, 'new_value': 2438.0}]
2025-06-06 08:07:01,418 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBM8P
2025-06-06 08:07:01,418 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2610.52, 'new_value': 3618.0}, {'field': 'amount', 'old_value': 2610.52, 'new_value': 3618.0}, {'field': 'count', 'old_value': 191, 'new_value': 192}, {'field': 'instoreAmount', 'old_value': 612.36, 'new_value': 1619.84}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 47}]
2025-06-06 08:07:01,856 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMMP
2025-06-06 08:07:01,856 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6455.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6455.3}]
2025-06-06 08:07:02,293 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMUP
2025-06-06 08:07:02,293 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7353.32, 'new_value': 167.0}, {'field': 'amount', 'old_value': 7353.32, 'new_value': 167.0}]
2025-06-06 08:07:02,715 - INFO - 更新表单数据成功: FINST-HJ966H81PCTV5Q0WDWTK272PKZK72EZAZKEBM21
2025-06-06 08:07:02,715 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6394.21}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6394.21}]
2025-06-06 08:07:03,137 - INFO - 更新表单数据成功: FINST-SI766181NAVVPLZDDLWKSD4CCKXF2F9GZKEBM3C
2025-06-06 08:07:03,137 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6786.92}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6786.92}]
2025-06-06 08:07:03,574 - INFO - 更新表单数据成功: FINST-LFA66G91UIUV3ITZDDLSWCJZCJ6Z191JZKEBMQ7
2025-06-06 08:07:03,574 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1800.26, 'new_value': 2222.19}, {'field': 'dailyBillAmount', 'old_value': 1800.26, 'new_value': 2222.19}]
2025-06-06 08:07:04,043 - INFO - 更新表单数据成功: FINST-LFA66G91UIUV3ITZDDLSWCJZCJ6Z191JZKEBMS7
2025-06-06 08:07:04,043 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4711.0, 'new_value': 2211.0}, {'field': 'amount', 'old_value': 4711.0, 'new_value': 2211.0}]
2025-06-06 08:07:04,527 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25PLZKEBMHT
2025-06-06 08:07:04,527 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3383.06, 'new_value': 3327.0}, {'field': 'amount', 'old_value': 3383.06, 'new_value': 3327.0}]
2025-06-06 08:07:05,011 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26PLZKEBM3U
2025-06-06 08:07:05,011 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7347.87, 'new_value': 6525.0}, {'field': 'amount', 'old_value': 7347.87, 'new_value': 6525.0}]
2025-06-06 08:07:05,464 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26PLZKEBM5U
2025-06-06 08:07:05,464 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6959.27}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6959.27}]
2025-06-06 08:07:05,933 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26PLZKEBMNU
2025-06-06 08:07:05,933 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8178.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8178.0}]
2025-06-06 08:07:06,464 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMAD
2025-06-06 08:07:06,464 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4718.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4718.0}]
2025-06-06 08:07:06,917 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMID
2025-06-06 08:07:06,917 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2487.0, 'new_value': 2047.0}, {'field': 'amount', 'old_value': 2487.0, 'new_value': 2047.0}]
2025-06-06 08:07:07,370 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMQD
2025-06-06 08:07:07,370 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3542.11, 'new_value': 3676.0}, {'field': 'amount', 'old_value': 3542.11, 'new_value': 3676.0}, {'field': 'count', 'old_value': 158, 'new_value': 159}, {'field': 'instoreAmount', 'old_value': 598.58, 'new_value': 732.47}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-06-06 08:07:07,792 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMAE
2025-06-06 08:07:07,792 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8660.33, 'new_value': 9565.0}, {'field': 'amount', 'old_value': 8660.33, 'new_value': 9565.0}, {'field': 'count', 'old_value': 159, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 7748.82, 'new_value': 8653.49}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 142}]
2025-06-06 08:07:08,385 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMCE
2025-06-06 08:07:08,385 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7556.91}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7556.91}]
2025-06-06 08:07:08,901 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42C8WZKEBMUE
2025-06-06 08:07:08,901 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 594.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 594.0}]
2025-06-06 08:07:09,323 - INFO - 更新表单数据成功: FINST-COC668A1REVVQ2Y7FIDUY6XN89OF27WYZKEBMHO
2025-06-06 08:07:09,323 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 20139.53}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 20139.53}]
2025-06-06 08:07:09,745 - INFO - 更新表单数据成功: FINST-S0E660A1SISV563KCERY4CPODIZZ1XI10LEBM231
2025-06-06 08:07:09,745 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2334.21}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2334.21}]
2025-06-06 08:07:10,198 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN31W60LEBMBG
2025-06-06 08:07:10,198 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1918.02, 'new_value': 2630.08}, {'field': 'dailyBillAmount', 'old_value': 1918.02, 'new_value': 2630.08}]
2025-06-06 08:07:10,744 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN31W60LEBMDG
2025-06-06 08:07:10,744 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4254.0, 'new_value': 2044.0}, {'field': 'amount', 'old_value': 4254.0, 'new_value': 2044.0}]
2025-06-06 08:07:11,150 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN31W60LEBMLG
2025-06-06 08:07:11,150 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2967.21, 'new_value': 2813.0}, {'field': 'amount', 'old_value': 2967.21, 'new_value': 2813.0}]
2025-06-06 08:07:11,760 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN32W60LEBM7H
2025-06-06 08:07:11,760 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9659.11, 'new_value': 9469.0}, {'field': 'amount', 'old_value': 9659.11, 'new_value': 9469.0}]
2025-06-06 08:07:12,197 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN32W60LEBM9H
2025-06-06 08:07:12,197 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6716.79}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6716.79}]
2025-06-06 08:07:12,681 - INFO - 更新表单数据成功: FINST-68E66TC1PNOVFN8G8BQWK6JFZQH525H90LEBMSW1
2025-06-06 08:07:12,681 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3806.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3806.0}]
2025-06-06 08:07:13,197 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBMUL
2025-06-06 08:07:13,197 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1896.54, 'new_value': 2581.52}, {'field': 'dailyBillAmount', 'old_value': 1896.54, 'new_value': 2581.52}]
2025-06-06 08:07:13,712 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBMWL
2025-06-06 08:07:13,712 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3649.0, 'new_value': 1246.0}, {'field': 'amount', 'old_value': 3649.0, 'new_value': 1246.0}]
2025-06-06 08:07:14,212 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBM4M
2025-06-06 08:07:14,212 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2034.09, 'new_value': 2207.0}, {'field': 'amount', 'old_value': 2034.09, 'new_value': 2207.0}, {'field': 'count', 'old_value': 116, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 717.37, 'new_value': 890.28}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 35}]
2025-06-06 08:07:14,712 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62CHH0LEBMPM
2025-06-06 08:07:14,712 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7817.8, 'new_value': 6425.0}, {'field': 'amount', 'old_value': 7817.8, 'new_value': 6425.0}]
2025-06-06 08:07:15,149 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62CHH0LEBMRM
2025-06-06 08:07:15,149 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5740.89}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5740.89}]
2025-06-06 08:07:15,556 - INFO - 更新表单数据成功: FINST-UW966371CESVP1N8ACK6C8AQV8E3391S0LEBMBL
2025-06-06 08:07:15,556 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 508.0, 'new_value': 1239.7}, {'field': 'dailyBillAmount', 'old_value': 508.0, 'new_value': 1239.7}]
2025-06-06 08:07:15,962 - INFO - 更新表单数据成功: FINST-UW966371CESVP1N8ACK6C8AQV8E3391S0LEBMDL
2025-06-06 08:07:15,962 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5114.0, 'new_value': 2142.0}, {'field': 'amount', 'old_value': 5114.0, 'new_value': 2142.0}]
2025-06-06 08:07:16,337 - INFO - 更新表单数据成功: FINST-UW966371CESVP1N8ACK6C8AQV8E3391S0LEBMLL
2025-06-06 08:07:16,337 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3059.29, 'new_value': 3518.0}, {'field': 'amount', 'old_value': 3059.29, 'new_value': 3518.0}, {'field': 'count', 'old_value': 159, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 1131.68, 'new_value': 1590.39}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 58}]
2025-06-06 08:07:16,758 - INFO - 更新表单数据成功: FINST-AEF66BC1DHVVYX8P8M6CI7Z5UVCK2FQU0LEBMVB
2025-06-06 08:07:16,758 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11553.37, 'new_value': 9933.0}, {'field': 'amount', 'old_value': 11553.37, 'new_value': 9933.0}]
2025-06-06 08:07:17,165 - INFO - 更新表单数据成功: FINST-AEF66BC1DHVVYX8P8M6CI7Z5UVCK2FQU0LEBMXB
2025-06-06 08:07:17,165 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8295.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8295.74}]
2025-06-06 08:07:17,633 - INFO - 更新表单数据成功: FINST-AEF66BC1DHVVYX8P8M6CI7Z5UVCK2FQU0LEBMFC
2025-06-06 08:07:17,633 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4130.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4130.0}]
2025-06-06 08:07:18,086 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2OE51LEBMSL
2025-06-06 08:07:18,086 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2419.54, 'new_value': 2491.9}, {'field': 'dailyBillAmount', 'old_value': 2419.54, 'new_value': 2491.9}]
2025-06-06 08:07:18,539 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2OE51LEBMUL
2025-06-06 08:07:18,539 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5462.0, 'new_value': 2782.0}, {'field': 'amount', 'old_value': 5462.0, 'new_value': 2782.0}]
2025-06-06 08:07:18,961 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2OE51LEBM2M
2025-06-06 08:07:18,961 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2025.87, 'new_value': 2230.0}, {'field': 'amount', 'old_value': 2025.8700000000001, 'new_value': 2230.0}, {'field': 'count', 'old_value': 134, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 562.34, 'new_value': 766.47}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 39}]
2025-06-06 08:07:19,414 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2PE51LEBMOM
2025-06-06 08:07:19,414 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7557.54, 'new_value': 6913.0}, {'field': 'amount', 'old_value': 7557.54, 'new_value': 6913.0}]
2025-06-06 08:07:19,883 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2PE51LEBMQM
2025-06-06 08:07:19,883 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5429.16}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5429.16}]
2025-06-06 08:07:20,304 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM6E
2025-06-06 08:07:20,304 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2199.04, 'new_value': 3361.68}, {'field': 'dailyBillAmount', 'old_value': 2199.04, 'new_value': 3361.68}]
2025-06-06 08:07:20,742 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM8E
2025-06-06 08:07:20,742 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4852.0, 'new_value': 1665.0}, {'field': 'amount', 'old_value': 4852.0, 'new_value': 1665.0}]
2025-06-06 08:07:21,195 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBMGE
2025-06-06 08:07:21,195 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2513.58, 'new_value': 2787.0}, {'field': 'amount', 'old_value': 2513.58, 'new_value': 2787.0}, {'field': 'count', 'old_value': 132, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 657.16, 'new_value': 930.58}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 29}]
2025-06-06 08:07:21,695 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM2F
2025-06-06 08:07:21,695 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8235.53, 'new_value': 7526.0}, {'field': 'amount', 'old_value': 8235.53, 'new_value': 7526.000000000001}]
2025-06-06 08:07:22,257 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM4F
2025-06-06 08:07:22,257 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6633.79}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6633.79}]
2025-06-06 08:07:22,741 - INFO - 更新表单数据成功: FINST-6IF66PC1O8UVZGJ3AEIDX9JX15K539HL1LEBMKS
2025-06-06 08:07:22,741 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 5936.5, 'new_value': 6056.1}, {'field': 'count', 'old_value': 106, 'new_value': 108}, {'field': 'instoreAmount', 'old_value': 5110.64, 'new_value': 5230.24}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 87}]
2025-06-06 08:07:23,163 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1CQQ1LEBMZS
2025-06-06 08:07:23,163 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1362.14, 'new_value': 639.84}, {'field': 'dailyBillAmount', 'old_value': 1362.14, 'new_value': 639.84}]
2025-06-06 08:07:23,554 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1CQQ1LEBM1T
2025-06-06 08:07:23,554 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5108.0, 'new_value': 2676.0}, {'field': 'amount', 'old_value': 5108.0, 'new_value': 2676.0}]
2025-06-06 08:07:24,022 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1DQQ1LEBM9T
2025-06-06 08:07:24,022 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2044.92, 'new_value': 2407.0}, {'field': 'amount', 'old_value': 2044.92, 'new_value': 2407.0}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 499.49, 'new_value': 861.57}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-06-06 08:07:24,475 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1DQQ1LEBMUT
2025-06-06 08:07:24,475 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7082.02, 'new_value': 5805.0}, {'field': 'amount', 'old_value': 7082.02, 'new_value': 5805.0}]
2025-06-06 08:07:24,913 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1DQQ1LEBMWT
2025-06-06 08:07:24,913 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6273.41}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6273.41}]
2025-06-06 08:07:25,334 - INFO - 更新表单数据成功: FINST-NWE664C1UCTVIBSCEHTRWAM5ELCC3RDT1LEBMWM
2025-06-06 08:07:25,334 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 279.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 279.0}]
2025-06-06 08:07:25,834 - INFO - 更新表单数据成功: FINST-3PF66O71R9VV8NWIATMOQAUPDNL82SD12LEBMZN
2025-06-06 08:07:25,834 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3833.0, 'new_value': 3007.0}, {'field': 'amount', 'old_value': 3833.0, 'new_value': 3007.0}]
2025-06-06 08:07:26,256 - INFO - 更新表单数据成功: FINST-3PF66O71R9VV8NWIATMOQAUPDNL82SD12LEBM7O
2025-06-06 08:07:26,256 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2774.9, 'new_value': 3164.0}, {'field': 'amount', 'old_value': 2774.9, 'new_value': 3164.0}, {'field': 'count', 'old_value': 140, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 561.2, 'new_value': 950.3}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 25}]
2025-06-06 08:07:26,756 - INFO - 更新表单数据成功: FINST-****************************************
2025-06-06 08:07:26,756 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9712.59, 'new_value': 8814.0}, {'field': 'amount', 'old_value': 9712.59, 'new_value': 8814.0}]
2025-06-06 08:07:27,162 - INFO - 更新表单数据成功: FINST-3PF66X61ZHSVJXG1ETEMXCGNDZ992J242LEBM4A1
2025-06-06 08:07:27,162 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6010.51}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6010.51}]
2025-06-06 08:07:27,615 - INFO - 更新表单数据成功: FINST-6PF66691GDSVWTNYCTVNHBULRWKO28C92LEBM0T
2025-06-06 08:07:27,615 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2387.29}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2387.29}]
2025-06-06 08:07:28,084 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMH2
2025-06-06 08:07:28,084 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1725.86, 'new_value': 2149.49}, {'field': 'dailyBillAmount', 'old_value': 1725.86, 'new_value': 2149.49}]
2025-06-06 08:07:28,599 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMJ2
2025-06-06 08:07:28,599 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4012.0, 'new_value': 1771.0}, {'field': 'amount', 'old_value': 4012.0, 'new_value': 1771.0}]
2025-06-06 08:07:29,005 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMR2
2025-06-06 08:07:29,005 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1932.23, 'new_value': 2397.0}, {'field': 'amount', 'old_value': 1932.23, 'new_value': 2397.0}, {'field': 'count', 'old_value': 112, 'new_value': 113}, {'field': 'instoreAmount', 'old_value': 438.93, 'new_value': 903.7}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 21}]
2025-06-06 08:07:29,427 - INFO - 更新表单数据成功: FINST-YWD66FA1QLVVCVS06LIS8432MWW230OE2LEBMJR
2025-06-06 08:07:29,443 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7792.33, 'new_value': 7020.0}, {'field': 'amount', 'old_value': 7792.33, 'new_value': 7020.0}]
2025-06-06 08:07:29,880 - INFO - 更新表单数据成功: FINST-YWD66FA1QLVVCVS06LIS8432MWW230OE2LEBMLR
2025-06-06 08:07:29,880 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5571.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5571.9}]
2025-06-06 08:07:30,427 - INFO - 更新表单数据成功: FINST-F7D66UA197SVSZMPF3TX6BZD7NRJ2TYJ2LEBMQL
2025-06-06 08:07:30,427 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5328.21}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5328.21}]
2025-06-06 08:07:30,927 - INFO - 更新表单数据成功: FINST-XO8662C1AEUV3DNCC02EF5TKUH5T2TKM2LEBMH51
2025-06-06 08:07:30,927 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1503.57, 'new_value': 969.64}, {'field': 'dailyBillAmount', 'old_value': 1503.57, 'new_value': 969.64}]
2025-06-06 08:07:31,380 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3TAP2LEBMZ9
2025-06-06 08:07:31,380 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4497.0, 'new_value': 534.7}, {'field': 'amount', 'old_value': 4497.0, 'new_value': 534.6999999999998}]
2025-06-06 08:07:31,833 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3TAP2LEBM7A
2025-06-06 08:07:31,833 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2775.06, 'new_value': 3027.0}, {'field': 'amount', 'old_value': 2775.0600000000004, 'new_value': 3027.0}, {'field': 'count', 'old_value': 137, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 570.24, 'new_value': 822.18}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 29}]
2025-06-06 08:07:32,286 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMTA
2025-06-06 08:07:32,286 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10526.62, 'new_value': 8377.0}, {'field': 'amount', 'old_value': 10526.62, 'new_value': 8377.0}]
2025-06-06 08:07:32,708 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMVA
2025-06-06 08:07:32,708 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6235.27}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6235.27}]
2025-06-06 08:07:33,145 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMCB
2025-06-06 08:07:33,145 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2027.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2027.0}]
2025-06-06 08:07:33,551 - INFO - 更新表单数据成功: FINST-PAB66N710QOV8VW1C4GQBC58Z2GM33OU2LEBMUN1
2025-06-06 08:07:33,551 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6950.29}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6950.29}]
2025-06-06 08:07:33,910 - INFO - 更新表单数据成功: FINST-PAB66N710QOV8VW1C4GQBC58Z2GM33OU2LEBM2O1
2025-06-06 08:07:33,910 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_********, 变更字段: [{'field': 'amount', 'old_value': 2228.7999999999997, 'new_value': 2433.7999999999997}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 2497.7, 'new_value': 2702.7}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-06-06 08:07:34,270 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBMA5
2025-06-06 08:07:34,270 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6177.0, 'new_value': 3844.0}, {'field': 'amount', 'old_value': 6177.0, 'new_value': 3844.0}]
2025-06-06 08:07:34,629 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBMI5
2025-06-06 08:07:34,629 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3897.1, 'new_value': 3958.0}, {'field': 'amount', 'old_value': 3897.1, 'new_value': 3957.9999999999995}, {'field': 'count', 'old_value': 212, 'new_value': 213}, {'field': 'instoreAmount', 'old_value': 924.18, 'new_value': 985.08}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 45}]
2025-06-06 08:07:35,160 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBM36
2025-06-06 08:07:35,160 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10380.6, 'new_value': 9973.0}, {'field': 'amount', 'old_value': 10380.6, 'new_value': 9973.0}]
2025-06-06 08:07:35,629 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBM56
2025-06-06 08:07:35,629 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5695.93}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5695.93}]
2025-06-06 08:07:36,176 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMAB
2025-06-06 08:07:36,176 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 22752.3, 'new_value': 22832.2}, {'field': 'amount', 'old_value': 22752.3, 'new_value': 22832.2}, {'field': 'count', 'old_value': 72, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 23071.3, 'new_value': 23151.2}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 73}]
2025-06-06 08:07:36,629 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2GB53LEBM2C
2025-06-06 08:07:36,629 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6190.0, 'new_value': 358231.99}, {'field': 'dailyBillAmount', 'old_value': 6190.0, 'new_value': 358231.99}]
2025-06-06 08:07:37,035 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMWD
2025-06-06 08:07:37,035 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2024.05}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2024.05}]
2025-06-06 08:07:37,613 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMDX
2025-06-06 08:07:37,613 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7176.74, 'new_value': 7281.44}, {'field': 'amount', 'old_value': 7176.74, 'new_value': 7281.44}, {'field': 'count', 'old_value': 130, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 6325.45, 'new_value': 6430.15}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 111}]
2025-06-06 08:07:38,003 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMEX
2025-06-06 08:07:38,003 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 3147.76, 'new_value': 3133.86}]
2025-06-06 08:07:38,378 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMJX
2025-06-06 08:07:38,378 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5774.61, 'new_value': 5782.41}, {'field': 'amount', 'old_value': 5774.610000000001, 'new_value': 5782.410000000001}, {'field': 'count', 'old_value': 294, 'new_value': 295}, {'field': 'onlineAmount', 'old_value': 4339.62, 'new_value': 4347.42}, {'field': 'onlineCount', 'old_value': 229, 'new_value': 230}]
2025-06-06 08:07:38,847 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMOY
2025-06-06 08:07:38,847 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 4974.09, 'new_value': 4977.09}, {'field': 'instoreCount', 'old_value': 322, 'new_value': 323}, {'field': 'onlineAmount', 'old_value': 1802.1, 'new_value': 1799.1}, {'field': 'onlineCount', 'old_value': 134, 'new_value': 133}]
2025-06-06 08:07:39,315 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMTY
2025-06-06 08:07:39,315 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'amount', 'old_value': 7102.660000000001, 'new_value': 7182.46}, {'field': 'count', 'old_value': 169, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 7135.26, 'new_value': 7215.06}, {'field': 'instoreCount', 'old_value': 165, 'new_value': 166}]
2025-06-06 08:07:39,815 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMIK
2025-06-06 08:07:39,815 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1432.11}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1432.11}]
2025-06-06 08:07:40,237 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3GQ2EMIBM76
2025-06-06 08:07:40,237 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2528.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2528.8}]
2025-06-06 08:07:40,721 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3HQ2EMIBMF6
2025-06-06 08:07:40,721 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11800.55}]
2025-06-06 08:07:41,190 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3HQ2EMIBM07
2025-06-06 08:07:41,190 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_********, 变更字段: [{'field': 'amount', 'old_value': 2701.55, 'new_value': 2713.05}, {'field': 'count', 'old_value': 162, 'new_value': 163}, {'field': 'onlineAmount', 'old_value': 1423.38, 'new_value': 1434.88}, {'field': 'onlineCount', 'old_value': 81, 'new_value': 82}]
2025-06-06 08:07:41,627 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3HQ2EMIBM47
2025-06-06 08:07:41,627 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_********, 变更字段: [{'field': 'amount', 'old_value': 2766.8799999999997, 'new_value': 2748.98}]
2025-06-06 08:07:42,065 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3HQ2EMIBM97
2025-06-06 08:07:42,065 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_********, 变更字段: [{'field': 'amount', 'old_value': 633.31, 'new_value': 652.11}, {'field': 'count', 'old_value': 26, 'new_value': 27}, {'field': 'onlineAmount', 'old_value': 532.11, 'new_value': 550.91}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 24}]
2025-06-06 08:07:42,580 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3HQ2EMIBMF7
2025-06-06 08:07:42,580 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5038.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5038.6}]
2025-06-06 08:07:43,143 - INFO - 更新表单数据成功: FINST-UW966371LD0W8WFO6OR4B8XG7BSW3HQ2EMIBMM7
2025-06-06 08:07:43,143 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8432.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8432.0}]
2025-06-06 08:07:43,502 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMZ
2025-06-06 08:07:43,502 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_********, 变更字段: [{'field': 'amount', 'old_value': 3417.0, 'new_value': 5185.0}, {'field': 'count', 'old_value': 3, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 3417.0, 'new_value': 5185.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 7}]
2025-06-06 08:07:43,955 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBM31
2025-06-06 08:07:43,955 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1482.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1482.0}]
2025-06-06 08:07:44,392 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBM41
2025-06-06 08:07:44,392 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1644.01, 'new_value': 1644.1}, {'field': 'dailyBillAmount', 'old_value': 1644.01, 'new_value': 1644.1}]
2025-06-06 08:07:44,783 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBM91
2025-06-06 08:07:44,783 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1911.43, 'new_value': 1925.43}, {'field': 'amount', 'old_value': 1911.43, 'new_value': 1925.43}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'onlineAmount', 'old_value': 1384.02, 'new_value': 1398.02}, {'field': 'onlineCount', 'old_value': 82, 'new_value': 83}]
2025-06-06 08:07:45,205 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMD1
2025-06-06 08:07:45,205 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2293.31, 'new_value': 2298.81}, {'field': 'amount', 'old_value': 2293.31, 'new_value': 2298.81}, {'field': 'instoreAmount', 'old_value': 2300.47, 'new_value': 2305.97}]
2025-06-06 08:07:45,626 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMI1
2025-06-06 08:07:45,626 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 354.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 354.0}]
2025-06-06 08:07:46,095 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMK1
2025-06-06 08:07:46,095 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6843.76, 'new_value': 6868.78}, {'field': 'amount', 'old_value': 6843.76, 'new_value': 6868.78}, {'field': 'count', 'old_value': 136, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 5673.44, 'new_value': 5698.46}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 111}]
2025-06-06 08:07:46,517 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBML1
2025-06-06 08:07:46,517 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 2421.0499999999997, 'new_value': 2480.5499999999997}, {'field': 'count', 'old_value': 162, 'new_value': 167}, {'field': 'onlineAmount', 'old_value': 2190.7, 'new_value': 2250.2}, {'field': 'onlineCount', 'old_value': 116, 'new_value': 121}]
2025-06-06 08:07:46,985 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMS1
2025-06-06 08:07:46,985 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4789.59, 'new_value': 4791.59}, {'field': 'amount', 'old_value': 4789.589999999999, 'new_value': 4791.589999999999}, {'field': 'count', 'old_value': 228, 'new_value': 229}, {'field': 'onlineAmount', 'old_value': 3645.8, 'new_value': 3647.8}, {'field': 'onlineCount', 'old_value': 185, 'new_value': 186}]
2025-06-06 08:07:47,376 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMU1
2025-06-06 08:07:47,376 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_********, 变更字段: [{'field': 'amount', 'old_value': 3591.24, 'new_value': 3622.14}, {'field': 'count', 'old_value': 51, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 1384.12, 'new_value': 1415.02}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-06-06 08:07:47,876 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3FE5EMIBMX1
2025-06-06 08:07:47,876 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 12538.79}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12538.79}]
2025-06-06 08:07:48,282 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBM62
2025-06-06 08:07:48,282 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_********, 变更字段: [{'field': 'amount', 'old_value': 21927.22, 'new_value': 21906.120000000003}]
2025-06-06 08:07:48,876 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBM82
2025-06-06 08:07:48,876 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_********, 变更字段: [{'field': 'amount', 'old_value': 8257.8, 'new_value': 8285.9}, {'field': 'count', 'old_value': 90, 'new_value': 91}, {'field': 'onlineAmount', 'old_value': 2029.9, 'new_value': 2058.0}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 42}]
2025-06-06 08:07:49,329 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBMC2
2025-06-06 08:07:49,329 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 4693.27, 'new_value': 4718.47}, {'field': 'count', 'old_value': 223, 'new_value': 224}, {'field': 'onlineAmount', 'old_value': 3363.53, 'new_value': 3388.73}, {'field': 'onlineCount', 'old_value': 153, 'new_value': 154}]
2025-06-06 08:07:49,782 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBMZ2
2025-06-06 08:07:49,782 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5090.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5090.8}, {'field': 'amount', 'old_value': 99.0, 'new_value': 5209.8}, {'field': 'count', 'old_value': 1, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 99.0, 'new_value': 5209.8}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 12}]
2025-06-06 08:07:50,266 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBM43
2025-06-06 08:07:50,266 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 3747.52, 'new_value': 3792.22}, {'field': 'count', 'old_value': 241, 'new_value': 251}, {'field': 'onlineAmount', 'old_value': 3778.82, 'new_value': 3823.52}, {'field': 'onlineCount', 'old_value': 234, 'new_value': 244}]
2025-06-06 08:07:50,735 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBM73
2025-06-06 08:07:50,735 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_********, 变更字段: [{'field': 'amount', 'old_value': 3245.5600000000004, 'new_value': 3312.46}, {'field': 'count', 'old_value': 135, 'new_value': 137}, {'field': 'onlineAmount', 'old_value': 2782.1, 'new_value': 2849.0}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 103}]
2025-06-06 08:07:51,156 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBMB3
2025-06-06 08:07:51,156 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 4268.45, 'new_value': 4296.48}, {'field': 'instoreCount', 'old_value': 238, 'new_value': 244}, {'field': 'onlineAmount', 'old_value': 1355.33, 'new_value': 1327.3}, {'field': 'onlineCount', 'old_value': 95, 'new_value': 89}]
2025-06-06 08:07:51,609 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBME3
2025-06-06 08:07:51,609 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 3790.16, 'new_value': 3802.25}, {'field': 'count', 'old_value': 71, 'new_value': 72}, {'field': 'onlineAmount', 'old_value': 1716.76, 'new_value': 1728.85}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 29}]
2025-06-06 08:07:52,078 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBMH3
2025-06-06 08:07:52,078 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_********, 变更字段: [{'field': 'amount', 'old_value': 799.48, 'new_value': 771.5799999999999}]
2025-06-06 08:07:52,547 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBMJ3
2025-06-06 08:07:52,547 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_********, 变更字段: [{'field': 'amount', 'old_value': 2900.0, 'new_value': 3055.0}, {'field': 'count', 'old_value': 23, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 2900.0, 'new_value': 3055.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-06-06 08:07:53,093 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBML3
2025-06-06 08:07:53,093 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6270.1, 'new_value': 14934.1}, {'field': 'amount', 'old_value': 6270.1, 'new_value': 14934.1}, {'field': 'count', 'old_value': 9, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 6270.1, 'new_value': 14934.1}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-06-06 08:07:53,593 - INFO - 更新表单数据成功: FINST-OIF66BA1YL0WKZE98PVZU8KJY92C3GE5EMIBMO3
2025-06-06 08:07:53,593 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 22037.89}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 22037.89}]
2025-06-06 08:07:54,062 - INFO - 更新表单数据成功: FINST-3Z966E91XLVV0RXEB3I9N8DJAPCA39X7EMIBMZ31
2025-06-06 08:07:54,062 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5129.27, 'new_value': 5125.27}, {'field': 'amount', 'old_value': 5129.2699999999995, 'new_value': 5125.2699999999995}, {'field': 'count', 'old_value': 282, 'new_value': 283}, {'field': 'instoreAmount', 'old_value': 2372.4, 'new_value': 2386.2}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 132}]
2025-06-06 08:07:54,484 - INFO - 更新表单数据成功: FINST-3Z966E91XLVV0RXEB3I9N8DJAPCA39X7EMIBM041
2025-06-06 08:07:54,484 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3599.23}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3599.23}]
2025-06-06 08:07:54,859 - INFO - 更新表单数据成功: FINST-3Z966E91XLVV0RXEB3I9N8DJAPCA39X7EMIBM741
2025-06-06 08:07:54,859 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'count', 'old_value': 47, 'new_value': 48}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}]
2025-06-06 08:07:55,312 - INFO - 更新表单数据成功: FINST-3Z966E91XLVV0RXEB3I9N8DJAPCA39X7EMIBMH41
2025-06-06 08:07:55,312 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 21557.58, 'new_value': 23181.58}, {'field': 'count', 'old_value': 123, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 18928.51, 'new_value': 20552.51}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 94}]
2025-06-06 08:07:55,749 - INFO - 更新表单数据成功: FINST-3Z966E91XLVV0RXEB3I9N8DJAPCA39X7EMIBMZ41
2025-06-06 08:07:55,749 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_********, 变更字段: [{'field': 'amount', 'old_value': 1255.0500000000002, 'new_value': 1246.15}]
2025-06-06 08:07:55,890 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-06 08:07:56,311 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-06 08:07:59,326 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-06 08:07:59,764 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-06 08:08:02,778 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-06 08:08:03,341 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-06 08:08:06,356 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-06 08:08:06,902 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-06 08:08:09,917 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-06 08:08:10,339 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-06 08:08:13,354 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-06 08:08:13,823 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-06 08:08:16,838 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-06 08:08:17,259 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-06 08:08:20,274 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-06 08:08:20,805 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-06 08:08:23,820 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-06 08:08:24,304 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-06 08:08:27,319 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-06 08:08:27,726 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-06 08:08:30,740 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-06 08:08:31,115 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-06 08:08:34,130 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-06 08:08:34,583 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-06 08:08:37,598 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-06 08:08:38,051 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-06 08:08:41,066 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-06 08:08:41,472 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-06 08:08:44,487 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-06 08:08:44,909 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-06 08:08:47,924 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-06 08:08:48,392 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-06 08:08:51,407 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-06 08:08:51,798 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-06 08:08:54,813 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-06 08:08:55,328 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-06 08:08:58,343 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-06 08:08:58,812 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-06 08:09:01,842 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-06 08:09:02,233 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-06 08:09:05,248 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-06 08:09:05,654 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-06 08:09:08,669 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-06 08:09:09,028 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-06 08:09:12,043 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-06 08:09:12,496 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-06 08:09:15,511 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-06 08:09:15,933 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-06 08:09:18,947 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-06 08:09:19,354 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-06 08:09:22,368 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-06 08:09:22,868 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-06 08:09:25,883 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-06 08:09:26,258 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-06 08:09:29,273 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-06 08:09:29,789 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-06 08:09:32,803 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-06 08:09:33,225 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-06 08:09:36,240 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-06 08:09:36,584 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-06 08:09:39,599 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-06 08:09:39,958 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-06 08:09:42,973 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-06 08:09:43,457 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-06 08:09:46,488 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-06 08:09:47,050 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-06 08:09:50,065 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-06 08:09:50,534 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-06 08:09:53,533 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-06 08:09:54,033 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-06 08:09:57,048 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-06 08:09:57,438 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-06 08:10:00,453 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-06 08:10:00,953 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-06 08:10:03,968 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-06 08:10:04,421 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-06 08:10:07,436 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-06 08:10:07,826 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-06 08:10:10,841 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-06 08:10:11,294 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-06 08:10:14,309 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-06 08:10:14,778 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-06 08:10:17,793 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-06 08:10:18,230 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-06 08:10:21,245 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-06 08:10:21,745 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-06 08:10:24,760 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-06 08:10:25,244 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-06 08:10:28,259 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-06 08:10:28,680 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-06 08:10:31,695 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-06 08:10:32,164 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-06 08:10:35,179 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-06 08:10:35,585 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-06 08:10:38,600 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-06 08:10:39,037 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-06 08:10:42,052 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-06 08:10:42,552 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-06 08:10:45,567 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-06 08:10:46,114 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-06 08:10:49,129 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-06 08:10:49,582 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-06 08:10:52,597 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-06 08:10:52,971 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-06 08:10:55,986 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-06 08:10:56,439 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-06 08:10:59,454 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-06 08:10:59,954 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-06 08:11:02,969 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-06 08:11:03,297 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-06 08:11:06,312 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-06 08:11:06,702 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-06 08:11:09,717 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-06 08:11:10,170 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-06 08:11:13,185 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-06 08:11:13,701 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-06 08:11:16,716 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-06 08:11:17,122 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-06 08:11:20,137 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-06 08:11:20,558 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-06 08:11:23,573 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-06 08:11:23,995 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-06 08:11:27,010 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-06 08:11:27,432 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-06 08:11:30,447 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-06 08:11:30,853 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-06 08:11:33,868 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-06 08:11:34,305 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-06 08:11:37,320 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-06 08:11:37,804 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-06 08:11:40,819 - INFO - 正在批量插入每日数据，批次 66/66，共 50 条记录
2025-06-06 08:11:41,116 - INFO - 批量插入每日数据成功，批次 66，50 条记录
2025-06-06 08:11:44,131 - INFO - 批量插入每日数据完成: 总计 6550 条，成功 6550 条，失败 0 条
2025-06-06 08:11:44,131 - INFO - 批量插入日销售数据完成，共 6550 条记录
2025-06-06 08:11:44,131 - INFO - 日销售数据同步完成！更新: 141 条，插入: 6550 条，错误: 0 条，跳过: 6181 条
2025-06-06 08:11:44,131 - INFO - 正在获取宜搭月销售表单数据...
2025-06-06 08:11:44,131 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-06 08:11:44,131 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-06 08:11:44,131 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-06 08:11:44,131 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:44,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:44,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:44,974 - INFO - API请求耗时: 844ms
2025-06-06 08:11:44,974 - INFO - Response - Page 1
2025-06-06 08:11:44,990 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:44,990 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:44,990 - WARNING - 月度分段 1 查询返回空数据
2025-06-06 08:11:44,990 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-06 08:11:44,990 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-06 08:11:44,990 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:44,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:44,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:45,224 - INFO - API请求耗时: 234ms
2025-06-06 08:11:45,224 - INFO - Response - Page 1
2025-06-06 08:11:45,224 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:45,224 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:45,224 - WARNING - 单月查询返回空数据: 2024-06
2025-06-06 08:11:45,724 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-06 08:11:45,724 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:45,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:45,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:45,927 - INFO - API请求耗时: 203ms
2025-06-06 08:11:45,927 - INFO - Response - Page 1
2025-06-06 08:11:45,927 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:45,927 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:45,927 - WARNING - 单月查询返回空数据: 2024-07
2025-06-06 08:11:46,443 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-06 08:11:46,443 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:46,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:46,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:46,646 - INFO - API请求耗时: 203ms
2025-06-06 08:11:46,646 - INFO - Response - Page 1
2025-06-06 08:11:46,646 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:46,646 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:46,646 - WARNING - 单月查询返回空数据: 2024-08
2025-06-06 08:11:48,177 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-06 08:11:48,177 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-06 08:11:48,177 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:48,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:48,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:48,380 - INFO - API请求耗时: 203ms
2025-06-06 08:11:48,380 - INFO - Response - Page 1
2025-06-06 08:11:48,380 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:48,380 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:48,380 - WARNING - 月度分段 2 查询返回空数据
2025-06-06 08:11:48,380 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-06 08:11:48,380 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-06 08:11:48,380 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:48,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:48,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:48,614 - INFO - API请求耗时: 234ms
2025-06-06 08:11:48,614 - INFO - Response - Page 1
2025-06-06 08:11:48,614 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:48,614 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:48,614 - WARNING - 单月查询返回空数据: 2024-09
2025-06-06 08:11:49,130 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-06 08:11:49,130 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:49,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:49,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:49,333 - INFO - API请求耗时: 203ms
2025-06-06 08:11:49,333 - INFO - Response - Page 1
2025-06-06 08:11:49,333 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:49,333 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:49,333 - WARNING - 单月查询返回空数据: 2024-10
2025-06-06 08:11:49,848 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-06 08:11:49,848 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:49,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:49,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:50,051 - INFO - API请求耗时: 203ms
2025-06-06 08:11:50,051 - INFO - Response - Page 1
2025-06-06 08:11:50,067 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-06 08:11:50,067 - INFO - 查询完成，共获取到 0 条记录
2025-06-06 08:11:50,067 - WARNING - 单月查询返回空数据: 2024-11
2025-06-06 08:11:51,582 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-06 08:11:51,582 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-06 08:11:51,582 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:51,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:51,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:52,223 - INFO - API请求耗时: 640ms
2025-06-06 08:11:52,223 - INFO - Response - Page 1
2025-06-06 08:11:52,223 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:11:52,738 - INFO - Request Parameters - Page 2:
2025-06-06 08:11:52,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:52,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:53,363 - INFO - API请求耗时: 625ms
2025-06-06 08:11:53,363 - INFO - Response - Page 2
2025-06-06 08:11:53,363 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:11:53,878 - INFO - Request Parameters - Page 3:
2025-06-06 08:11:53,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:53,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:54,441 - INFO - API请求耗时: 562ms
2025-06-06 08:11:54,441 - INFO - Response - Page 3
2025-06-06 08:11:54,441 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:11:54,941 - INFO - Request Parameters - Page 4:
2025-06-06 08:11:54,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:54,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:55,487 - INFO - API请求耗时: 547ms
2025-06-06 08:11:55,487 - INFO - Response - Page 4
2025-06-06 08:11:55,487 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:11:56,003 - INFO - Request Parameters - Page 5:
2025-06-06 08:11:56,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:56,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:56,534 - INFO - API请求耗时: 531ms
2025-06-06 08:11:56,534 - INFO - Response - Page 5
2025-06-06 08:11:56,534 - INFO - 第 5 页获取到 94 条记录
2025-06-06 08:11:56,534 - INFO - 查询完成，共获取到 494 条记录
2025-06-06 08:11:56,534 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-06 08:11:57,534 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-06 08:11:57,534 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-06 08:11:57,534 - INFO - Request Parameters - Page 1:
2025-06-06 08:11:57,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:57,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:58,065 - INFO - API请求耗时: 531ms
2025-06-06 08:11:58,065 - INFO - Response - Page 1
2025-06-06 08:11:58,081 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:11:58,580 - INFO - Request Parameters - Page 2:
2025-06-06 08:11:58,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:58,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:11:59,112 - INFO - API请求耗时: 531ms
2025-06-06 08:11:59,112 - INFO - Response - Page 2
2025-06-06 08:11:59,112 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:11:59,627 - INFO - Request Parameters - Page 3:
2025-06-06 08:11:59,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:11:59,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:00,143 - INFO - API请求耗时: 515ms
2025-06-06 08:12:00,143 - INFO - Response - Page 3
2025-06-06 08:12:00,143 - INFO - 第 3 页获取到 100 条记录
2025-06-06 08:12:00,658 - INFO - Request Parameters - Page 4:
2025-06-06 08:12:00,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:00,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:01,236 - INFO - API请求耗时: 578ms
2025-06-06 08:12:01,236 - INFO - Response - Page 4
2025-06-06 08:12:01,236 - INFO - 第 4 页获取到 100 条记录
2025-06-06 08:12:01,736 - INFO - Request Parameters - Page 5:
2025-06-06 08:12:01,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:01,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:02,330 - INFO - API请求耗时: 594ms
2025-06-06 08:12:02,330 - INFO - Response - Page 5
2025-06-06 08:12:02,330 - INFO - 第 5 页获取到 100 条记录
2025-06-06 08:12:02,845 - INFO - Request Parameters - Page 6:
2025-06-06 08:12:02,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:02,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:03,361 - INFO - API请求耗时: 515ms
2025-06-06 08:12:03,361 - INFO - Response - Page 6
2025-06-06 08:12:03,361 - INFO - 第 6 页获取到 100 条记录
2025-06-06 08:12:03,860 - INFO - Request Parameters - Page 7:
2025-06-06 08:12:03,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:03,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:04,407 - INFO - API请求耗时: 547ms
2025-06-06 08:12:04,407 - INFO - Response - Page 7
2025-06-06 08:12:04,407 - INFO - 第 7 页获取到 98 条记录
2025-06-06 08:12:04,407 - INFO - 查询完成，共获取到 698 条记录
2025-06-06 08:12:04,407 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-06 08:12:05,423 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-06 08:12:05,423 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-06 08:12:05,423 - INFO - Request Parameters - Page 1:
2025-06-06 08:12:05,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:05,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:05,985 - INFO - API请求耗时: 562ms
2025-06-06 08:12:05,985 - INFO - Response - Page 1
2025-06-06 08:12:05,985 - INFO - 第 1 页获取到 100 条记录
2025-06-06 08:12:06,500 - INFO - Request Parameters - Page 2:
2025-06-06 08:12:06,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:06,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:07,032 - INFO - API请求耗时: 531ms
2025-06-06 08:12:07,047 - INFO - Response - Page 2
2025-06-06 08:12:07,047 - INFO - 第 2 页获取到 100 条记录
2025-06-06 08:12:07,563 - INFO - Request Parameters - Page 3:
2025-06-06 08:12:07,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-06 08:12:07,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-06 08:12:07,875 - INFO - API请求耗时: 312ms
2025-06-06 08:12:07,875 - INFO - Response - Page 3
2025-06-06 08:12:07,875 - INFO - 第 3 页获取到 10 条记录
2025-06-06 08:12:07,875 - INFO - 查询完成，共获取到 210 条记录
2025-06-06 08:12:07,875 - INFO - 月度分段 5 查询成功，获取到 210 条记录
2025-06-06 08:12:08,890 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1402 条记录，失败 0 次
2025-06-06 08:12:08,890 - INFO - 成功获取宜搭月销售表单数据，共 1402 条记录
2025-06-06 08:12:08,890 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-06 08:12:08,890 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-06 08:12:08,890 - INFO - 成功获取SQLite月度汇总数据，共 1402 条记录
2025-06-06 08:12:08,969 - INFO - 成功创建宜搭月销售数据索引，共 1402 条记录
2025-06-06 08:12:08,969 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:09,437 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-06 08:12:09,437 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33849.64, 'new_value': 42210.13}, {'field': 'dailyBillAmount', 'old_value': 33849.64, 'new_value': 42210.13}, {'field': 'amount', 'old_value': 1103.2, 'new_value': 1339.8}, {'field': 'count', 'old_value': 13, 'new_value': 17}, {'field': 'onlineAmount', 'old_value': 1103.2, 'new_value': 1339.8}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 17}]
2025-06-06 08:12:09,437 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:09,921 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-06 08:12:09,921 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 80489.39, 'new_value': 92871.33}, {'field': 'dailyBillAmount', 'old_value': 80489.39, 'new_value': 92871.33}, {'field': 'amount', 'old_value': 47126.9, 'new_value': 55213.0}, {'field': 'count', 'old_value': 437, 'new_value': 512}, {'field': 'instoreAmount', 'old_value': 19001.5, 'new_value': 23776.600000000002}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 178}, {'field': 'onlineAmount', 'old_value': 28126.2, 'new_value': 31437.2}, {'field': 'onlineCount', 'old_value': 297, 'new_value': 334}]
2025-06-06 08:12:09,921 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:10,406 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-06 08:12:10,406 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48640.25, 'new_value': 57785.38}, {'field': 'dailyBillAmount', 'old_value': 48640.25, 'new_value': 57785.38}, {'field': 'amount', 'old_value': 49187.36, 'new_value': 58475.159999999996}, {'field': 'count', 'old_value': 313, 'new_value': 380}, {'field': 'instoreAmount', 'old_value': 45251.73, 'new_value': 54201.33}, {'field': 'instoreCount', 'old_value': 259, 'new_value': 318}, {'field': 'onlineAmount', 'old_value': 3994.61, 'new_value': 4332.81}, {'field': 'onlineCount', 'old_value': 54, 'new_value': 62}]
2025-06-06 08:12:10,406 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:10,937 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-06 08:12:10,937 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 87655.6, 'new_value': 131098.66}, {'field': 'dailyBillAmount', 'old_value': 87655.6, 'new_value': 131098.66}, {'field': 'amount', 'old_value': 84808.7, 'new_value': 100044.4}, {'field': 'count', 'old_value': 406, 'new_value': 477}, {'field': 'instoreAmount', 'old_value': 84808.8, 'new_value': 100044.5}, {'field': 'instoreCount', 'old_value': 406, 'new_value': 477}]
2025-06-06 08:12:10,937 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:11,390 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-06 08:12:11,390 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 83520.51000000001, 'new_value': 95331.66}, {'field': 'dailyBillAmount', 'old_value': 83520.51000000001, 'new_value': 95331.66}, {'field': 'amount', 'old_value': 119924.1, 'new_value': 145487.8}, {'field': 'count', 'old_value': 410, 'new_value': 509}, {'field': 'instoreAmount', 'old_value': 118997.0, 'new_value': 144454.0}, {'field': 'instoreCount', 'old_value': 406, 'new_value': 504}, {'field': 'onlineAmount', 'old_value': 927.3, 'new_value': 1034.0}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-06-06 08:12:11,390 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:11,858 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-06 08:12:11,858 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7356.5, 'new_value': 9107.6}, {'field': 'dailyBillAmount', 'old_value': 7356.5, 'new_value': 9107.6}, {'field': 'amount', 'old_value': 8873.9, 'new_value': 11000.6}, {'field': 'count', 'old_value': 49, 'new_value': 64}, {'field': 'onlineAmount', 'old_value': 5436.5, 'new_value': 7563.2}, {'field': 'onlineCount', 'old_value': 43, 'new_value': 58}]
2025-06-06 08:12:11,858 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:12,233 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-06 08:12:12,233 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15946.1, 'new_value': 27570.1}, {'field': 'amount', 'old_value': 15946.1, 'new_value': 27570.0}, {'field': 'count', 'old_value': 17, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 15946.1, 'new_value': 27570.1}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 23}]
2025-06-06 08:12:12,233 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:12,686 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-06 08:12:12,686 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 107380.0, 'new_value': 134813.6}, {'field': 'dailyBillAmount', 'old_value': 107380.0, 'new_value': 134813.6}, {'field': 'amount', 'old_value': 67040.79000000001, 'new_value': 84231.39}, {'field': 'count', 'old_value': 470, 'new_value': 601}, {'field': 'instoreAmount', 'old_value': 61534.19, 'new_value': 76892.19}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 330}, {'field': 'onlineAmount', 'old_value': 7013.1, 'new_value': 9177.7}, {'field': 'onlineCount', 'old_value': 203, 'new_value': 271}]
2025-06-06 08:12:12,686 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:13,202 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-06 08:12:13,202 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96823.04, 'new_value': 119292.15}, {'field': 'dailyBillAmount', 'old_value': 96823.04, 'new_value': 119292.15}, {'field': 'amount', 'old_value': 18249.0, 'new_value': 22004.82}, {'field': 'count', 'old_value': 93, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 18249.0, 'new_value': 22004.82}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 115}]
2025-06-06 08:12:13,202 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:13,671 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAI
2025-06-06 08:12:13,671 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10659.3, 'new_value': 29684.86}, {'field': 'dailyBillAmount', 'old_value': 10659.3, 'new_value': 29684.86}]
2025-06-06 08:12:13,671 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:14,124 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-06 08:12:14,124 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11435.0, 'new_value': 14644.0}, {'field': 'count', 'old_value': 16, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 11435.0, 'new_value': 14644.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 23}]
2025-06-06 08:12:14,124 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:14,686 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-06 08:12:14,686 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 191736.47, 'new_value': 225231.87}, {'field': 'dailyBillAmount', 'old_value': 191736.47, 'new_value': 225231.87}, {'field': 'amount', 'old_value': -68902.43000000001, 'new_value': -94577.15000000001}, {'field': 'count', 'old_value': 190, 'new_value': 210}, {'field': 'instoreAmount', 'old_value': 121104.49, 'new_value': 129060.67}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 210}]
2025-06-06 08:12:14,686 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:15,076 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-06 08:12:15,076 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50662.0, 'new_value': 56293.0}, {'field': 'amount', 'old_value': 50662.0, 'new_value': 56293.0}, {'field': 'count', 'old_value': 201, 'new_value': 227}, {'field': 'instoreAmount', 'old_value': 50662.0, 'new_value': 56293.0}, {'field': 'instoreCount', 'old_value': 201, 'new_value': 227}]
2025-06-06 08:12:15,076 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:15,498 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-06 08:12:15,498 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66365.76, 'new_value': 75630.45999999999}, {'field': 'dailyBillAmount', 'old_value': 58479.16, 'new_value': 67089.86}, {'field': 'amount', 'old_value': 66365.76, 'new_value': 75630.45999999999}, {'field': 'count', 'old_value': 219, 'new_value': 248}, {'field': 'instoreAmount', 'old_value': 66365.76, 'new_value': 75630.45999999999}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 248}]
2025-06-06 08:12:15,498 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:15,889 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-06 08:12:15,889 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14762.2, 'new_value': 23194.2}, {'field': 'dailyBillAmount', 'old_value': 14762.2, 'new_value': 23194.2}, {'field': 'amount', 'old_value': 1711.2, 'new_value': 3240.7}, {'field': 'count', 'old_value': 7, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 1770.3, 'new_value': 3376.3}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 12}]
2025-06-06 08:12:15,889 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:16,326 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFI
2025-06-06 08:12:16,326 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15596.6, 'new_value': 18849.68}, {'field': 'dailyBillAmount', 'old_value': 15596.6, 'new_value': 18849.68}, {'field': 'amount', 'old_value': 8578.57, 'new_value': 10438.63}, {'field': 'count', 'old_value': 150, 'new_value': 185}, {'field': 'instoreAmount', 'old_value': 8850.82, 'new_value': 10739.88}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 185}]
2025-06-06 08:12:16,326 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:16,904 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-06 08:12:16,904 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23894.12, 'new_value': 29796.55}, {'field': 'dailyBillAmount', 'old_value': 5186.04, 'new_value': 10902.77}, {'field': 'amount', 'old_value': 23893.76, 'new_value': 29796.19}, {'field': 'count', 'old_value': 785, 'new_value': 983}, {'field': 'instoreAmount', 'old_value': 21743.440000000002, 'new_value': 27035.440000000002}, {'field': 'instoreCount', 'old_value': 725, 'new_value': 905}, {'field': 'onlineAmount', 'old_value': 2150.68, 'new_value': 2761.11}, {'field': 'onlineCount', 'old_value': 60, 'new_value': 78}]
2025-06-06 08:12:16,904 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:17,357 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-06 08:12:17,357 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28912.83, 'new_value': 63887.83}, {'field': 'dailyBillAmount', 'old_value': 28871.0, 'new_value': 63846.0}, {'field': 'amount', 'old_value': 22905.83, 'new_value': 32246.83}, {'field': 'count', 'old_value': 37, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 22728.0, 'new_value': 32069.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 41}]
2025-06-06 08:12:17,373 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:17,795 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-06 08:12:17,795 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52130.75, 'new_value': 72857.38}, {'field': 'dailyBillAmount', 'old_value': 52130.75, 'new_value': 72857.38}, {'field': 'amount', 'old_value': 52130.75, 'new_value': 72857.38}, {'field': 'count', 'old_value': 61, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 52130.75, 'new_value': 72857.38}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 79}]
2025-06-06 08:12:17,795 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:18,201 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJI
2025-06-06 08:12:18,201 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-06, 变更字段: [{'field': 'amount', 'old_value': 5256.0, 'new_value': 5988.0}, {'field': 'count', 'old_value': 7, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 5256.0, 'new_value': 5988.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 10}]
2025-06-06 08:12:18,201 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:18,716 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-06 08:12:18,716 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14788.8, 'new_value': 16946.5}, {'field': 'dailyBillAmount', 'old_value': 14788.8, 'new_value': 16946.5}, {'field': 'amount', 'old_value': 16237.8, 'new_value': 18395.5}, {'field': 'count', 'old_value': 48, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 16237.8, 'new_value': 18395.5}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 56}]
2025-06-06 08:12:18,716 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:19,154 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-06 08:12:19,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12035.0, 'new_value': 13159.0}, {'field': 'amount', 'old_value': 12035.0, 'new_value': 13159.0}, {'field': 'count', 'old_value': 21, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 12035.0, 'new_value': 13159.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 25}]
2025-06-06 08:12:19,154 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:19,607 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-06 08:12:19,607 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34701.619999999995, 'new_value': 39740.22}, {'field': 'dailyBillAmount', 'old_value': 34701.619999999995, 'new_value': 39740.22}, {'field': 'amount', 'old_value': 53909.0, 'new_value': 59345.9}, {'field': 'count', 'old_value': 266, 'new_value': 302}, {'field': 'instoreAmount', 'old_value': 53909.3, 'new_value': 59346.2}, {'field': 'instoreCount', 'old_value': 266, 'new_value': 302}]
2025-06-06 08:12:19,607 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:20,060 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-06 08:12:20,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26241.34, 'new_value': 33396.88}, {'field': 'dailyBillAmount', 'old_value': 26241.34, 'new_value': 33396.88}, {'field': 'amount', 'old_value': 2267.14, 'new_value': 3164.55}, {'field': 'count', 'old_value': 198, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 2966.53, 'new_value': 4020.34}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 284}]
2025-06-06 08:12:20,060 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:20,481 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-06 08:12:20,481 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44657.4, 'new_value': 51228.3}, {'field': 'dailyBillAmount', 'old_value': 44657.4, 'new_value': 51228.3}, {'field': 'amount', 'old_value': 44713.4, 'new_value': 51284.3}, {'field': 'count', 'old_value': 114, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 44713.4, 'new_value': 51284.3}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 129}]
2025-06-06 08:12:20,481 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:20,981 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-06 08:12:20,981 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40979.04, 'new_value': 50198.09}, {'field': 'dailyBillAmount', 'old_value': 40979.04, 'new_value': 50198.09}, {'field': 'amount', 'old_value': 12746.4, 'new_value': 14718.3}, {'field': 'count', 'old_value': 31, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 12746.4, 'new_value': 14718.3}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 38}]
2025-06-06 08:12:20,981 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:21,434 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-06 08:12:21,450 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55820.67, 'new_value': 69851.64}, {'field': 'dailyBillAmount', 'old_value': 55820.67, 'new_value': 69851.64}, {'field': 'amount', 'old_value': 22160.2, 'new_value': 27467.9}, {'field': 'count', 'old_value': 88, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 22160.2, 'new_value': 27467.9}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 114}]
2025-06-06 08:12:21,450 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:21,872 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-06 08:12:21,872 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14333.0, 'new_value': 18385.57}, {'field': 'dailyBillAmount', 'old_value': 14333.0, 'new_value': 18385.57}, {'field': 'amount', 'old_value': 2875.65, 'new_value': 3842.65}, {'field': 'count', 'old_value': 114, 'new_value': 148}, {'field': 'instoreAmount', 'old_value': 752.5, 'new_value': 946.9}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 24}, {'field': 'onlineAmount', 'old_value': 2123.84, 'new_value': 2896.55}, {'field': 'onlineCount', 'old_value': 96, 'new_value': 124}]
2025-06-06 08:12:21,872 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:22,309 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-06 08:12:22,309 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26071.47, 'new_value': 31739.989999999998}, {'field': 'dailyBillAmount', 'old_value': 26071.47, 'new_value': 31739.989999999998}, {'field': 'amount', 'old_value': 4815.44, 'new_value': 5879.87}, {'field': 'count', 'old_value': 110, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 4068.96, 'new_value': 5022.65}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 122}, {'field': 'onlineAmount', 'old_value': 757.55, 'new_value': 868.29}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 23}]
2025-06-06 08:12:22,309 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:22,668 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-06 08:12:22,668 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7242.6, 'new_value': 7750.6}, {'field': 'dailyBillAmount', 'old_value': 7242.6, 'new_value': 7750.6}, {'field': 'amount', 'old_value': 6835.6, 'new_value': 7543.6}, {'field': 'count', 'old_value': 164, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 6835.6, 'new_value': 7643.6}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 188}]
2025-06-06 08:12:22,668 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:23,059 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-06 08:12:23,059 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9031.16, 'new_value': 11011.74}, {'field': 'dailyBillAmount', 'old_value': 9031.16, 'new_value': 11011.74}, {'field': 'amount', 'old_value': 4520.84, 'new_value': 5548.24}, {'field': 'count', 'old_value': 256, 'new_value': 302}, {'field': 'instoreAmount', 'old_value': 1816.9199999999998, 'new_value': 2038.12}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 84}, {'field': 'onlineAmount', 'old_value': 2929.34, 'new_value': 3735.54}, {'field': 'onlineCount', 'old_value': 182, 'new_value': 218}]
2025-06-06 08:12:23,075 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:23,543 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-06 08:12:23,543 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51326.44, 'new_value': 65461.29}, {'field': 'dailyBillAmount', 'old_value': 51326.44, 'new_value': 65461.29}, {'field': 'amount', 'old_value': 29130.97, 'new_value': 37178.97}, {'field': 'count', 'old_value': 126, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 29963.52, 'new_value': 38058.520000000004}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 158}]
2025-06-06 08:12:23,543 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:23,996 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-06 08:12:23,996 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29719.67, 'new_value': 37271.61}, {'field': 'dailyBillAmount', 'old_value': 29719.67, 'new_value': 37271.61}, {'field': 'amount', 'old_value': 13549.51, 'new_value': 17776.52}, {'field': 'count', 'old_value': 671, 'new_value': 842}, {'field': 'instoreAmount', 'old_value': 13989.35, 'new_value': 18315.14}, {'field': 'instoreCount', 'old_value': 671, 'new_value': 842}]
2025-06-06 08:12:23,996 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:24,402 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-06 08:12:24,402 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91916.3, 'new_value': 105084.5}, {'field': 'dailyBillAmount', 'old_value': 91916.3, 'new_value': 105084.5}, {'field': 'amount', 'old_value': 91916.3, 'new_value': 105084.5}, {'field': 'count', 'old_value': 114, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 91916.3, 'new_value': 105084.5}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 131}]
2025-06-06 08:12:24,402 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:24,824 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-06 08:12:24,824 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56035.1, 'new_value': 62890.3}, {'field': 'dailyBillAmount', 'old_value': 56035.1, 'new_value': 62890.3}, {'field': 'amount', 'old_value': 40076.5, 'new_value': 43535.4}, {'field': 'count', 'old_value': 97, 'new_value': 108}, {'field': 'instoreAmount', 'old_value': 40076.5, 'new_value': 43535.4}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 108}]
2025-06-06 08:12:24,824 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:25,261 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-06 08:12:25,261 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8327.0, 'new_value': 11006.0}, {'field': 'dailyBillAmount', 'old_value': 8327.0, 'new_value': 11006.0}, {'field': 'amount', 'old_value': 8327.0, 'new_value': 10971.0}, {'field': 'count', 'old_value': 166, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 8327.0, 'new_value': 10971.0}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 212}]
2025-06-06 08:12:25,261 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:25,714 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-06 08:12:25,714 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14095.76, 'new_value': 17287.5}, {'field': 'dailyBillAmount', 'old_value': 14095.76, 'new_value': 17287.5}, {'field': 'amount', 'old_value': 14257.94, 'new_value': 17878.66}, {'field': 'count', 'old_value': 766, 'new_value': 963}, {'field': 'instoreAmount', 'old_value': 7557.6900000000005, 'new_value': 9598.79}, {'field': 'instoreCount', 'old_value': 402, 'new_value': 502}, {'field': 'onlineAmount', 'old_value': 6965.83, 'new_value': 8663.6}, {'field': 'onlineCount', 'old_value': 364, 'new_value': 461}]
2025-06-06 08:12:25,714 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:26,136 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-06 08:12:26,136 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8742.46, 'new_value': 9501.26}, {'field': 'count', 'old_value': 336, 'new_value': 370}, {'field': 'onlineAmount', 'old_value': 3202.86, 'new_value': 3961.66}, {'field': 'onlineCount', 'old_value': 153, 'new_value': 187}]
2025-06-06 08:12:26,136 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:26,605 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-06 08:12:26,605 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6860.9, 'new_value': 11483.4}, {'field': 'count', 'old_value': 104, 'new_value': 140}, {'field': 'instoreAmount', 'old_value': 6861.79, 'new_value': 11484.289999999999}, {'field': 'instoreCount', 'old_value': 104, 'new_value': 140}]
2025-06-06 08:12:26,605 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:27,042 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-06 08:12:27,058 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14866.9, 'new_value': 16892.6}, {'field': 'amount', 'old_value': 14866.1, 'new_value': 16891.8}, {'field': 'count', 'old_value': 322, 'new_value': 388}, {'field': 'instoreAmount', 'old_value': 15103.2, 'new_value': 17169.7}, {'field': 'instoreCount', 'old_value': 322, 'new_value': 388}]
2025-06-06 08:12:27,058 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:27,527 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-06 08:12:27,527 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59398.5, 'new_value': 66293.5}, {'field': 'dailyBillAmount', 'old_value': 59398.5, 'new_value': 66293.5}, {'field': 'amount', 'old_value': 24656.5, 'new_value': 26751.5}, {'field': 'count', 'old_value': 72, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 24656.5, 'new_value': 26751.5}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 78}]
2025-06-06 08:12:27,542 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:27,964 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-06 08:12:27,964 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13853.04, 'new_value': 16460.04}, {'field': 'dailyBillAmount', 'old_value': 13853.04, 'new_value': 16460.04}, {'field': 'amount', 'old_value': 13400.880000000001, 'new_value': 16007.880000000001}, {'field': 'count', 'old_value': 55, 'new_value': 68}, {'field': 'instoreAmount', 'old_value': 13640.04, 'new_value': 16247.04}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 68}]
2025-06-06 08:12:27,964 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:28,589 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-06 08:12:28,589 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11674.0, 'new_value': 14672.0}, {'field': 'count', 'old_value': 16, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 11674.0, 'new_value': 14672.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 21}]
2025-06-06 08:12:28,589 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:29,042 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-06 08:12:29,042 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15242.900000000001, 'new_value': 17451.2}, {'field': 'dailyBillAmount', 'old_value': 15242.900000000001, 'new_value': 17451.2}, {'field': 'amount', 'old_value': 15242.8, 'new_value': 17451.1}, {'field': 'count', 'old_value': 38, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 16053.5, 'new_value': 18261.8}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 43}]
2025-06-06 08:12:29,042 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:29,573 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-06 08:12:29,573 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26770.4, 'new_value': 30137.0}, {'field': 'dailyBillAmount', 'old_value': 26770.4, 'new_value': 30137.0}, {'field': 'amount', 'old_value': 15502.22, 'new_value': 17926.48}, {'field': 'count', 'old_value': 372, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 14638.75, 'new_value': 16525.56}, {'field': 'instoreCount', 'old_value': 327, 'new_value': 368}, {'field': 'onlineAmount', 'old_value': 1723.29, 'new_value': 2260.74}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 59}]
2025-06-06 08:12:29,573 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:29,963 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-06 08:12:29,963 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24283.59, 'new_value': 31679.09}, {'field': 'dailyBillAmount', 'old_value': 24197.22, 'new_value': 30960.25}, {'field': 'amount', 'old_value': 24282.6, 'new_value': 31678.1}, {'field': 'count', 'old_value': 306, 'new_value': 411}, {'field': 'instoreAmount', 'old_value': 23897.62, 'new_value': 30838.62}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 401}, {'field': 'onlineAmount', 'old_value': 385.96999999999997, 'new_value': 840.47}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 10}]
2025-06-06 08:12:29,963 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:30,401 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-06 08:12:30,401 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14913.08, 'new_value': 19226.55}, {'field': 'dailyBillAmount', 'old_value': 14913.08, 'new_value': 19226.55}, {'field': 'amount', 'old_value': 15540.44, 'new_value': 19955.92}, {'field': 'count', 'old_value': 85, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 14088.82, 'new_value': 18368.32}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 89}, {'field': 'onlineAmount', 'old_value': 1452.9499999999998, 'new_value': 1588.9299999999998}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 15}]
2025-06-06 08:12:30,401 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:30,885 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-06 08:12:30,885 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26036.9, 'new_value': 38352.8}, {'field': 'dailyBillAmount', 'old_value': 26036.9, 'new_value': 38352.8}, {'field': 'amount', 'old_value': 31373.9, 'new_value': 42396.8}, {'field': 'count', 'old_value': 119, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 32185.9, 'new_value': 43208.8}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 155}]
2025-06-06 08:12:30,885 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:31,338 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-06 08:12:31,338 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5500.0, 'new_value': 6245.0}, {'field': 'dailyBillAmount', 'old_value': 5500.0, 'new_value': 6245.0}, {'field': 'amount', 'old_value': 5500.0, 'new_value': 6245.0}, {'field': 'count', 'old_value': 14, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 5579.0, 'new_value': 6324.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 17}]
2025-06-06 08:12:31,338 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:31,760 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-06 08:12:31,760 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10136.08, 'new_value': 13201.36}, {'field': 'amount', 'old_value': 10135.87, 'new_value': 13201.150000000001}, {'field': 'count', 'old_value': 455, 'new_value': 598}, {'field': 'instoreAmount', 'old_value': 10749.34, 'new_value': 13868.43}, {'field': 'instoreCount', 'old_value': 455, 'new_value': 598}]
2025-06-06 08:12:31,760 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:32,166 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-06 08:12:32,166 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19694.559999999998, 'new_value': 22687.26}, {'field': 'dailyBillAmount', 'old_value': 19694.559999999998, 'new_value': 22687.26}, {'field': 'amount', 'old_value': 15856.48, 'new_value': 18532.38}, {'field': 'count', 'old_value': 64, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 15702.7, 'new_value': 18378.6}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 73}]
2025-06-06 08:12:32,166 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:32,588 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-06 08:12:32,588 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59961.19, 'new_value': 73880.35}, {'field': 'dailyBillAmount', 'old_value': 59961.19, 'new_value': 73880.35}, {'field': 'amount', 'old_value': 23264.23, 'new_value': 30071.58}, {'field': 'count', 'old_value': 234, 'new_value': 301}, {'field': 'instoreAmount', 'old_value': 12782.4, 'new_value': 17202.34}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 124}, {'field': 'onlineAmount', 'old_value': 10482.5, 'new_value': 12869.91}, {'field': 'onlineCount', 'old_value': 141, 'new_value': 177}]
2025-06-06 08:12:32,588 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:33,166 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-06 08:12:33,166 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35890.479999999996, 'new_value': 41070.7}, {'field': 'dailyBillAmount', 'old_value': 35890.479999999996, 'new_value': 41070.7}, {'field': 'amount', 'old_value': 45913.0, 'new_value': 51868.0}, {'field': 'count', 'old_value': 241, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 45913.8, 'new_value': 51868.8}, {'field': 'instoreCount', 'old_value': 241, 'new_value': 273}]
2025-06-06 08:12:33,166 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:33,619 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-06 08:12:33,619 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26014.5, 'new_value': 31297.45}, {'field': 'dailyBillAmount', 'old_value': 26014.5, 'new_value': 31297.45}, {'field': 'amount', 'old_value': 11803.92, 'new_value': 14097.6}, {'field': 'count', 'old_value': 137, 'new_value': 165}, {'field': 'instoreAmount', 'old_value': 11491.22, 'new_value': 13656.2}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 141}, {'field': 'onlineAmount', 'old_value': 651.7, 'new_value': 780.4}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 24}]
2025-06-06 08:12:33,619 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:34,072 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-06 08:12:34,072 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 672.85, 'new_value': 709.6}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 673.55, 'new_value': 710.3}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-06-06 08:12:34,072 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:34,494 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-06 08:12:34,494 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32969.32, 'new_value': 40060.79}, {'field': 'dailyBillAmount', 'old_value': 32969.32, 'new_value': 40060.79}, {'field': 'amount', 'old_value': 32969.32, 'new_value': 40060.79}, {'field': 'count', 'old_value': 128, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 32969.32, 'new_value': 40060.79}, {'field': 'instoreCount', 'old_value': 128, 'new_value': 163}]
2025-06-06 08:12:34,494 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:34,884 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-06 08:12:34,884 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6048.82, 'new_value': 7870.97}, {'field': 'dailyBillAmount', 'old_value': 6048.82, 'new_value': 7870.97}, {'field': 'amount', 'old_value': 6568.75, 'new_value': 8677.39}, {'field': 'count', 'old_value': 183, 'new_value': 236}, {'field': 'instoreAmount', 'old_value': 6569.72, 'new_value': 8678.36}, {'field': 'instoreCount', 'old_value': 183, 'new_value': 236}]
2025-06-06 08:12:34,884 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:35,259 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-06 08:12:35,259 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49527.0, 'new_value': 57144.0}, {'field': 'dailyBillAmount', 'old_value': 23932.0, 'new_value': 35732.55}, {'field': 'amount', 'old_value': 49527.0, 'new_value': 57144.0}, {'field': 'count', 'old_value': 85, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 49527.0, 'new_value': 57144.0}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 98}]
2025-06-06 08:12:35,259 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:35,665 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-06 08:12:35,665 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6867.95, 'new_value': 7878.24}, {'field': 'amount', 'old_value': 6867.01, 'new_value': 7877.3}, {'field': 'count', 'old_value': 59, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 6867.95, 'new_value': 7878.24}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 70}]
2025-06-06 08:12:35,665 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:36,071 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-06 08:12:36,071 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65771.0, 'new_value': 66570.0}, {'field': 'amount', 'old_value': 65771.0, 'new_value': 66570.0}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 65771.0, 'new_value': 66570.0}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-06-06 08:12:36,071 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:36,524 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-06 08:12:36,524 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34957.0, 'new_value': 40388.0}, {'field': 'dailyBillAmount', 'old_value': 31645.0, 'new_value': 34194.0}, {'field': 'amount', 'old_value': 34957.0, 'new_value': 40388.0}, {'field': 'count', 'old_value': 168, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 34957.0, 'new_value': 40388.0}, {'field': 'instoreCount', 'old_value': 168, 'new_value': 206}]
2025-06-06 08:12:36,524 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:36,962 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMI1
2025-06-06 08:12:36,962 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8900.0, 'new_value': 16200.0}, {'field': 'amount', 'old_value': 8900.0, 'new_value': 16200.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 8900.0, 'new_value': 16200.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-06 08:12:36,962 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:37,305 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-06 08:12:37,305 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 9531.1, 'new_value': 11401.1}, {'field': 'count', 'old_value': 87, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 9531.9, 'new_value': 11401.9}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 110}]
2025-06-06 08:12:37,305 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:37,727 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-06 08:12:37,743 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45924.72, 'new_value': 60370.64}, {'field': 'dailyBillAmount', 'old_value': 45924.72, 'new_value': 60370.64}, {'field': 'amount', 'old_value': 57900.0, 'new_value': 72345.92}, {'field': 'count', 'old_value': 182, 'new_value': 227}, {'field': 'instoreAmount', 'old_value': 57900.72, 'new_value': 72346.64}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 227}]
2025-06-06 08:12:37,743 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:38,149 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-06 08:12:38,149 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72098.0, 'new_value': 111269.69}, {'field': 'dailyBillAmount', 'old_value': 72098.0, 'new_value': 111269.69}, {'field': 'amount', 'old_value': 138841.93, 'new_value': 180036.62}, {'field': 'count', 'old_value': 192, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 138841.93, 'new_value': 180036.62}, {'field': 'instoreCount', 'old_value': 192, 'new_value': 240}]
2025-06-06 08:12:38,149 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:38,586 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMJ1
2025-06-06 08:12:38,586 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-06, 变更字段: [{'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-06 08:12:38,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:39,039 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-06 08:12:39,039 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15515.599999999999, 'new_value': 18044.4}, {'field': 'dailyBillAmount', 'old_value': 15515.599999999999, 'new_value': 18044.4}]
2025-06-06 08:12:39,039 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-06 08:12:39,524 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-06-06 08:12:39,524 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70985.77, 'new_value': 80252.41}, {'field': 'dailyBillAmount', 'old_value': 70985.77, 'new_value': 80252.41}]
2025-06-06 08:12:39,524 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-06 08:12:39,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-06-06 08:12:39,977 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146577.79, 'new_value': 300105.74}, {'field': 'dailyBillAmount', 'old_value': 146577.79, 'new_value': 300105.74}]
2025-06-06 08:12:39,977 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-06 08:12:40,398 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-06-06 08:12:40,398 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28696.0, 'new_value': 58826.0}, {'field': 'dailyBillAmount', 'old_value': 28696.0, 'new_value': 58826.0}]
2025-06-06 08:12:40,398 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:40,805 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-06 08:12:40,805 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41407.51, 'new_value': 44942.34}, {'field': 'amount', 'old_value': 41406.58, 'new_value': 44941.409999999996}, {'field': 'count', 'old_value': 434, 'new_value': 489}, {'field': 'instoreAmount', 'old_value': 30384.47, 'new_value': 31425.89}, {'field': 'instoreCount', 'old_value': 282, 'new_value': 295}, {'field': 'onlineAmount', 'old_value': 11950.02, 'new_value': 14607.93}, {'field': 'onlineCount', 'old_value': 152, 'new_value': 194}]
2025-06-06 08:12:40,805 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:41,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-06 08:12:41,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55964.36, 'new_value': 66781.54000000001}, {'field': 'dailyBillAmount', 'old_value': 55964.36, 'new_value': 66781.54000000001}, {'field': 'amount', 'old_value': 4591.84, 'new_value': 5227.74}, {'field': 'count', 'old_value': 136, 'new_value': 161}, {'field': 'instoreAmount', 'old_value': 5055.94, 'new_value': 5798.54}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 161}]
2025-06-06 08:12:41,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:41,586 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-06 08:12:41,586 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67145.23999999999, 'new_value': 72538.04}, {'field': 'dailyBillAmount', 'old_value': 67145.23999999999, 'new_value': 72538.04}, {'field': 'amount', 'old_value': 29044.84, 'new_value': 32054.71}, {'field': 'count', 'old_value': 605, 'new_value': 676}, {'field': 'instoreAmount', 'old_value': 27346.809999999998, 'new_value': 29782.41}, {'field': 'instoreCount', 'old_value': 564, 'new_value': 617}, {'field': 'onlineAmount', 'old_value': 2307.6200000000003, 'new_value': 2881.8900000000003}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 59}]
2025-06-06 08:12:41,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:41,992 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-06 08:12:41,992 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44284.799999999996, 'new_value': 51982.2}, {'field': 'dailyBillAmount', 'old_value': 72194.1, 'new_value': 82466.40000000001}, {'field': 'amount', 'old_value': 44283.7, 'new_value': 51981.1}, {'field': 'count', 'old_value': 162, 'new_value': 196}, {'field': 'instoreAmount', 'old_value': 45406.7, 'new_value': 53660.9}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 196}]
2025-06-06 08:12:41,992 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:42,445 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-06 08:12:42,445 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'amount', 'old_value': 119896.16, 'new_value': 134567.72}, {'field': 'count', 'old_value': 2052, 'new_value': 2274}, {'field': 'instoreAmount', 'old_value': 112052.93000000001, 'new_value': 126209.13}, {'field': 'instoreCount', 'old_value': 1902, 'new_value': 2115}, {'field': 'onlineAmount', 'old_value': 8272.06, 'new_value': 8787.45}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 159}]
2025-06-06 08:12:42,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:42,867 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-06 08:12:42,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 82238.06, 'new_value': 101369.21}, {'field': 'amount', 'old_value': 82237.2, 'new_value': 101368.35}, {'field': 'count', 'old_value': 1857, 'new_value': 2304}, {'field': 'instoreAmount', 'old_value': 60124.76, 'new_value': 74402.86}, {'field': 'instoreCount', 'old_value': 1242, 'new_value': 1551}, {'field': 'onlineAmount', 'old_value': 22113.3, 'new_value': 26966.35}, {'field': 'onlineCount', 'old_value': 615, 'new_value': 753}]
2025-06-06 08:12:42,867 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:43,273 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-06 08:12:43,273 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -2050.19, 'new_value': -2222.98}, {'field': 'count', 'old_value': 17, 'new_value': 23}, {'field': 'onlineAmount', 'old_value': 270.0, 'new_value': 443.0}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 18}]
2025-06-06 08:12:43,273 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:43,632 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-06 08:12:43,632 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'amount', 'old_value': 24511.25, 'new_value': 29161.63}, {'field': 'count', 'old_value': 1578, 'new_value': 1916}, {'field': 'instoreAmount', 'old_value': 18502.04, 'new_value': 21828.8}, {'field': 'instoreCount', 'old_value': 1083, 'new_value': 1320}, {'field': 'onlineAmount', 'old_value': 6544.48, 'new_value': 7938.85}, {'field': 'onlineCount', 'old_value': 495, 'new_value': 596}]
2025-06-06 08:12:43,632 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:44,038 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-06 08:12:44,038 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49785.06, 'new_value': 55513.369999999995}, {'field': 'dailyBillAmount', 'old_value': 49785.06, 'new_value': 55513.369999999995}, {'field': 'amount', 'old_value': 48487.86, 'new_value': 53062.509999999995}, {'field': 'count', 'old_value': 1423, 'new_value': 1590}, {'field': 'instoreAmount', 'old_value': 48634.36, 'new_value': 53282.71}, {'field': 'instoreCount', 'old_value': 1423, 'new_value': 1590}]
2025-06-06 08:12:44,038 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:44,429 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-06 08:12:44,429 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19639.57, 'new_value': 22486.06}, {'field': 'amount', 'old_value': 19639.530000000002, 'new_value': 22486.02}, {'field': 'count', 'old_value': 1113, 'new_value': 1266}, {'field': 'instoreAmount', 'old_value': 10321.93, 'new_value': 11544.42}, {'field': 'instoreCount', 'old_value': 686, 'new_value': 765}, {'field': 'onlineAmount', 'old_value': 9317.64, 'new_value': 10941.64}, {'field': 'onlineCount', 'old_value': 427, 'new_value': 501}]
2025-06-06 08:12:44,429 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:44,913 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-06 08:12:44,913 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26530.81, 'new_value': 29706.32}, {'field': 'dailyBillAmount', 'old_value': 26530.81, 'new_value': 29706.32}, {'field': 'amount', 'old_value': 5312.64, 'new_value': 6035.54}, {'field': 'count', 'old_value': 173, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 5412.78, 'new_value': 6135.68}, {'field': 'instoreCount', 'old_value': 173, 'new_value': 198}]
2025-06-06 08:12:44,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:45,382 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-06 08:12:45,382 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23321.26, 'new_value': 27163.45}, {'field': 'dailyBillAmount', 'old_value': 23321.26, 'new_value': 27163.45}, {'field': 'amount', 'old_value': 15935.62, 'new_value': 19133.440000000002}, {'field': 'count', 'old_value': 760, 'new_value': 895}, {'field': 'instoreAmount', 'old_value': 3131.4500000000003, 'new_value': 3969.13}, {'field': 'instoreCount', 'old_value': 210, 'new_value': 240}, {'field': 'onlineAmount', 'old_value': 13156.9, 'new_value': 15545.7}, {'field': 'onlineCount', 'old_value': 550, 'new_value': 655}]
2025-06-06 08:12:45,382 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:45,819 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-06 08:12:45,835 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24302.940000000002, 'new_value': 25870.13}, {'field': 'amount', 'old_value': 24302.39, 'new_value': 25869.58}, {'field': 'count', 'old_value': 618, 'new_value': 677}, {'field': 'instoreAmount', 'old_value': 23375.670000000002, 'new_value': 24794.96}, {'field': 'instoreCount', 'old_value': 604, 'new_value': 662}, {'field': 'onlineAmount', 'old_value': 934.57, 'new_value': 1082.47}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 15}]
2025-06-06 08:12:45,835 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:46,319 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-06 08:12:46,319 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 25892.45, 'new_value': 33336.61}, {'field': 'count', 'old_value': 1416, 'new_value': 1764}, {'field': 'instoreAmount', 'old_value': 27102.370000000003, 'new_value': 35081.23}, {'field': 'instoreCount', 'old_value': 1403, 'new_value': 1751}]
2025-06-06 08:12:46,319 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:46,787 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-06 08:12:46,787 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25031.87, 'new_value': 34875.479999999996}, {'field': 'dailyBillAmount', 'old_value': 25031.87, 'new_value': 34875.479999999996}, {'field': 'amount', 'old_value': 17864.52, 'new_value': 21313.22}, {'field': 'count', 'old_value': 1265, 'new_value': 1515}, {'field': 'instoreAmount', 'old_value': 1072.0, 'new_value': 1284.5}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 89}, {'field': 'onlineAmount', 'old_value': 17556.21, 'new_value': 20850.43}, {'field': 'onlineCount', 'old_value': 1189, 'new_value': 1426}]
2025-06-06 08:12:46,787 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:47,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-06 08:12:47,194 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36613.44, 'new_value': 40630.7}, {'field': 'dailyBillAmount', 'old_value': 36613.44, 'new_value': 40630.7}, {'field': 'amount', 'old_value': 29509.22, 'new_value': 33545.11}, {'field': 'count', 'old_value': 896, 'new_value': 992}, {'field': 'instoreAmount', 'old_value': 17321.55, 'new_value': 19394.74}, {'field': 'instoreCount', 'old_value': 695, 'new_value': 756}, {'field': 'onlineAmount', 'old_value': 13339.4, 'new_value': 15772.599999999999}, {'field': 'onlineCount', 'old_value': 201, 'new_value': 236}]
2025-06-06 08:12:47,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:47,584 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-06 08:12:47,584 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11201.49, 'new_value': 13313.77}, {'field': 'dailyBillAmount', 'old_value': 11201.49, 'new_value': 13313.77}, {'field': 'amount', 'old_value': 12689.23, 'new_value': 15750.79}, {'field': 'count', 'old_value': 445, 'new_value': 535}, {'field': 'instoreAmount', 'old_value': 4620.96, 'new_value': 5958.2}, {'field': 'instoreCount', 'old_value': 174, 'new_value': 195}, {'field': 'onlineAmount', 'old_value': 8183.04, 'new_value': 9950.66}, {'field': 'onlineCount', 'old_value': 271, 'new_value': 340}]
2025-06-06 08:12:47,584 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:48,037 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-06 08:12:48,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21085.65, 'new_value': 22933.77}, {'field': 'dailyBillAmount', 'old_value': 21085.65, 'new_value': 22933.77}, {'field': 'amount', 'old_value': 21887.13, 'new_value': 23948.58}, {'field': 'count', 'old_value': 711, 'new_value': 803}, {'field': 'instoreAmount', 'old_value': 21867.37, 'new_value': 23928.82}, {'field': 'instoreCount', 'old_value': 709, 'new_value': 801}]
2025-06-06 08:12:48,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:48,475 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-06 08:12:48,475 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52215.0, 'new_value': 53629.0}, {'field': 'dailyBillAmount', 'old_value': 52215.0, 'new_value': 53629.0}, {'field': 'amount', 'old_value': 60758.0, 'new_value': 62172.0}, {'field': 'count', 'old_value': 49, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 63854.0, 'new_value': 65268.0}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 51}]
2025-06-06 08:12:48,475 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:49,021 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-06 08:12:49,021 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28122.4, 'new_value': 36096.0}, {'field': 'dailyBillAmount', 'old_value': 28122.4, 'new_value': 36096.0}, {'field': 'amount', 'old_value': 28854.6, 'new_value': 36348.4}, {'field': 'count', 'old_value': 60, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 29289.01, 'new_value': 37332.61}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 78}]
2025-06-06 08:12:49,021 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:49,396 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMHK
2025-06-06 08:12:49,396 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4837.0, 'new_value': 6164.0}, {'field': 'amount', 'old_value': 4837.0, 'new_value': 6164.0}, {'field': 'count', 'old_value': 16, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 4837.0, 'new_value': 6164.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 20}]
2025-06-06 08:12:49,396 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:49,865 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-06 08:12:49,865 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10386.0, 'new_value': 12519.0}, {'field': 'dailyBillAmount', 'old_value': 10386.0, 'new_value': 12519.0}, {'field': 'amount', 'old_value': 3486.0, 'new_value': 4503.0}, {'field': 'count', 'old_value': 11, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 3486.0, 'new_value': 4503.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 13}]
2025-06-06 08:12:49,865 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:50,334 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-06 08:12:50,334 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9569.0, 'new_value': 10507.0}, {'field': 'dailyBillAmount', 'old_value': 4285.0, 'new_value': 5223.0}]
2025-06-06 08:12:50,334 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:50,787 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-06 08:12:50,787 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13258.5, 'new_value': 14353.5}, {'field': 'amount', 'old_value': 13258.5, 'new_value': 14353.5}, {'field': 'count', 'old_value': 32, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 13258.5, 'new_value': 14353.5}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 35}]
2025-06-06 08:12:50,787 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:51,302 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-06 08:12:51,302 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72312.0, 'new_value': 84809.0}, {'field': 'dailyBillAmount', 'old_value': 72312.0, 'new_value': 84809.0}, {'field': 'amount', 'old_value': 72312.0, 'new_value': 101556.0}, {'field': 'count', 'old_value': 9, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 72312.0, 'new_value': 101556.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 15}]
2025-06-06 08:12:51,302 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:51,739 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMMK
2025-06-06 08:12:51,739 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23150.0, 'new_value': 26185.0}, {'field': 'amount', 'old_value': 23150.0, 'new_value': 26185.0}, {'field': 'count', 'old_value': 7, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 23150.0, 'new_value': 26185.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 10}]
2025-06-06 08:12:51,739 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:52,146 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-06 08:12:52,146 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5013.0, 'new_value': 6059.0}, {'field': 'amount', 'old_value': 5013.0, 'new_value': 6059.0}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 5013.0, 'new_value': 6059.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-06-06 08:12:52,146 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:52,583 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMOK
2025-06-06 08:12:52,583 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7114.0, 'new_value': 7474.0}, {'field': 'amount', 'old_value': 7114.0, 'new_value': 7474.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 7114.0, 'new_value': 7474.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-06 08:12:52,583 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:53,020 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-06 08:12:53,020 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45162.5, 'new_value': 50076.1}, {'field': 'dailyBillAmount', 'old_value': 45162.5, 'new_value': 50076.1}, {'field': 'amount', 'old_value': 44328.9, 'new_value': 49242.5}, {'field': 'count', 'old_value': 61, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 45057.3, 'new_value': 50219.9}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 69}]
2025-06-06 08:12:53,020 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:53,505 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-06 08:12:53,505 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4544.1, 'new_value': 6953.299999999999}, {'field': 'dailyBillAmount', 'old_value': 4544.1, 'new_value': 6953.299999999999}, {'field': 'amount', 'old_value': 3938.7999999999997, 'new_value': 6704.3}, {'field': 'count', 'old_value': 33, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 3138.4, 'new_value': 5347.5}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 42}, {'field': 'onlineAmount', 'old_value': 802.3, 'new_value': 1358.7}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 15}]
2025-06-06 08:12:53,505 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:53,973 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMRK
2025-06-06 08:12:53,973 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1208.0, 'new_value': 2592.0}, {'field': 'amount', 'old_value': 1208.0, 'new_value': 2592.0}, {'field': 'count', 'old_value': 3, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 1208.0, 'new_value': 2592.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 6}]
2025-06-06 08:12:53,973 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:54,364 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-06 08:12:54,364 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3738.0, 'new_value': 3977.0}, {'field': 'dailyBillAmount', 'old_value': 3738.0, 'new_value': 3977.0}, {'field': 'amount', 'old_value': 3733.0, 'new_value': 3972.0}, {'field': 'count', 'old_value': 17, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 4263.0, 'new_value': 4502.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 18}]
2025-06-06 08:12:54,364 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:54,801 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-06 08:12:54,801 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7250.700000000001, 'new_value': 7726.700000000001}, {'field': 'amount', 'old_value': 7249.9, 'new_value': 7725.9}, {'field': 'count', 'old_value': 42, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 7358.700000000001, 'new_value': 7834.700000000001}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 44}]
2025-06-06 08:12:54,817 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:55,223 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-06 08:12:55,223 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1503.0, 'new_value': 1799.0}, {'field': 'dailyBillAmount', 'old_value': 1503.0, 'new_value': 1799.0}, {'field': 'amount', 'old_value': 10872.0, 'new_value': 11761.0}, {'field': 'count', 'old_value': 24, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 10872.0, 'new_value': 11761.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 27}]
2025-06-06 08:12:55,223 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:55,723 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-06 08:12:55,723 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 231348.68999999997, 'new_value': 248812.86}, {'field': 'dailyBillAmount', 'old_value': 231348.68999999997, 'new_value': 248812.86}, {'field': 'amount', 'old_value': 14251.58, 'new_value': 15233.3}, {'field': 'count', 'old_value': 147, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 11232.67, 'new_value': 11998.039999999999}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 116}, {'field': 'onlineAmount', 'old_value': 3137.19, 'new_value': 3353.54}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 44}]
2025-06-06 08:12:55,723 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:56,207 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-06 08:12:56,207 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6859.0, 'new_value': 10788.0}, {'field': 'amount', 'old_value': 6859.0, 'new_value': 10788.0}, {'field': 'count', 'old_value': 12, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 7258.0, 'new_value': 11586.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 17}]
2025-06-06 08:12:56,207 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:56,582 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVR
2025-06-06 08:12:56,582 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1435.0, 'new_value': 3238.0}, {'field': 'amount', 'old_value': 1435.0, 'new_value': 3238.0}, {'field': 'count', 'old_value': 4, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 1435.0, 'new_value': 3238.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 6}]
2025-06-06 08:12:56,582 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:57,066 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-06 08:12:57,066 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7825.0, 'new_value': 7942.599999999999}, {'field': 'amount', 'old_value': 7825.0, 'new_value': 7942.599999999999}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 7825.0, 'new_value': 7942.599999999999}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-06-06 08:12:57,066 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:57,550 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-06 08:12:57,550 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3526.0, 'new_value': 5019.0}, {'field': 'dailyBillAmount', 'old_value': 3526.0, 'new_value': 5019.0}, {'field': 'amount', 'old_value': 3579.0, 'new_value': 5072.0}, {'field': 'count', 'old_value': 14, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 5214.0, 'new_value': 6707.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 16}]
2025-06-06 08:12:57,550 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:58,035 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-06 08:12:58,035 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46155.119999999995, 'new_value': 52278.04}, {'field': 'dailyBillAmount', 'old_value': 43639.71, 'new_value': 44994.009999999995}, {'field': 'amount', 'old_value': 44020.07, 'new_value': 50142.99}, {'field': 'count', 'old_value': 255, 'new_value': 279}, {'field': 'instoreAmount', 'old_value': 44020.71, 'new_value': 50143.63}, {'field': 'instoreCount', 'old_value': 255, 'new_value': 279}]
2025-06-06 08:12:58,035 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:58,425 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-06 08:12:58,425 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29773.0, 'new_value': 29889.0}, {'field': 'amount', 'old_value': 29773.0, 'new_value': 29889.0}, {'field': 'count', 'old_value': 153, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 30817.0, 'new_value': 30933.0}, {'field': 'instoreCount', 'old_value': 153, 'new_value': 154}]
2025-06-06 08:12:58,441 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:58,863 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-06 08:12:58,863 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44287.0, 'new_value': 50069.5}, {'field': 'dailyBillAmount', 'old_value': 44287.0, 'new_value': 50069.5}, {'field': 'amount', 'old_value': 43991.0, 'new_value': 49773.5}, {'field': 'count', 'old_value': 246, 'new_value': 282}, {'field': 'instoreAmount', 'old_value': 43991.0, 'new_value': 49773.5}, {'field': 'instoreCount', 'old_value': 246, 'new_value': 282}]
2025-06-06 08:12:58,863 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:59,300 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-06 08:12:59,300 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13886.86, 'new_value': 16126.23}, {'field': 'dailyBillAmount', 'old_value': 13886.86, 'new_value': 16126.23}, {'field': 'amount', 'old_value': 8421.72, 'new_value': 9188.73}, {'field': 'count', 'old_value': 721, 'new_value': 815}, {'field': 'instoreAmount', 'old_value': 8669.63, 'new_value': 9465.83}, {'field': 'instoreCount', 'old_value': 721, 'new_value': 815}]
2025-06-06 08:12:59,300 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:12:59,737 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-06 08:12:59,737 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 104994.06, 'new_value': 118357.17}, {'field': 'dailyBillAmount', 'old_value': 104994.06, 'new_value': 118357.17}, {'field': 'amount', 'old_value': 103680.18, 'new_value': 116428.31}, {'field': 'count', 'old_value': 1078, 'new_value': 1274}, {'field': 'instoreAmount', 'old_value': 88315.41, 'new_value': 97434.5}, {'field': 'instoreCount', 'old_value': 441, 'new_value': 497}, {'field': 'onlineAmount', 'old_value': 15783.16, 'new_value': 19605.2}, {'field': 'onlineCount', 'old_value': 637, 'new_value': 777}]
2025-06-06 08:12:59,737 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:00,175 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-06 08:13:00,175 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39003.3, 'new_value': 41322.2}, {'field': 'amount', 'old_value': 39003.3, 'new_value': 41322.2}, {'field': 'count', 'old_value': 232, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 39003.3, 'new_value': 41322.2}, {'field': 'instoreCount', 'old_value': 232, 'new_value': 249}]
2025-06-06 08:13:00,175 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:00,628 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-06 08:13:00,628 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 21256.11, 'new_value': 25946.68}, {'field': 'count', 'old_value': 845, 'new_value': 1067}, {'field': 'instoreAmount', 'old_value': 5936.3, 'new_value': 7216.22}, {'field': 'instoreCount', 'old_value': 232, 'new_value': 301}, {'field': 'onlineAmount', 'old_value': 15793.119999999999, 'new_value': 19271.71}, {'field': 'onlineCount', 'old_value': 613, 'new_value': 766}]
2025-06-06 08:13:00,628 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:01,034 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-06 08:13:01,034 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13613.29, 'new_value': 15386.35}, {'field': 'dailyBillAmount', 'old_value': 13613.29, 'new_value': 15386.35}, {'field': 'amount', 'old_value': 19668.04, 'new_value': 22437.35}, {'field': 'count', 'old_value': 956, 'new_value': 1119}, {'field': 'instoreAmount', 'old_value': 11173.130000000001, 'new_value': 12406.14}, {'field': 'instoreCount', 'old_value': 594, 'new_value': 677}, {'field': 'onlineAmount', 'old_value': 8786.72, 'new_value': 10386.82}, {'field': 'onlineCount', 'old_value': 362, 'new_value': 442}]
2025-06-06 08:13:01,034 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:01,425 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-06 08:13:01,425 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9619.0, 'new_value': 10227.0}, {'field': 'amount', 'old_value': 9619.0, 'new_value': 10227.0}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 9619.0, 'new_value': 10227.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-06-06 08:13:01,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:01,909 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-06 08:13:01,909 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20260.32, 'new_value': 22754.3}, {'field': 'dailyBillAmount', 'old_value': 20260.32, 'new_value': 22754.3}, {'field': 'amount', 'old_value': 9166.93, 'new_value': 10730.03}, {'field': 'count', 'old_value': 560, 'new_value': 706}, {'field': 'instoreAmount', 'old_value': 1868.5, 'new_value': 1943.1}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 87}, {'field': 'onlineAmount', 'old_value': 7299.03, 'new_value': 8787.53}, {'field': 'onlineCount', 'old_value': 477, 'new_value': 619}]
2025-06-06 08:13:01,909 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:02,362 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-06 08:13:02,362 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64509.8, 'new_value': 71771.08}, {'field': 'dailyBillAmount', 'old_value': 64509.8, 'new_value': 71771.08}, {'field': 'amount', 'old_value': 61055.71, 'new_value': 67679.81}, {'field': 'count', 'old_value': 493, 'new_value': 578}, {'field': 'instoreAmount', 'old_value': 49922.72, 'new_value': 55013.22}, {'field': 'instoreCount', 'old_value': 292, 'new_value': 340}, {'field': 'onlineAmount', 'old_value': 11134.1, 'new_value': 12668.6}, {'field': 'onlineCount', 'old_value': 201, 'new_value': 238}]
2025-06-06 08:13:02,362 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:02,799 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-06 08:13:02,799 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73852.42, 'new_value': 81699.43000000001}, {'field': 'dailyBillAmount', 'old_value': 73852.42, 'new_value': 81699.43000000001}, {'field': 'amount', 'old_value': 68926.7, 'new_value': 74774.7}, {'field': 'count', 'old_value': 376, 'new_value': 412}, {'field': 'instoreAmount', 'old_value': 69432.2, 'new_value': 75280.2}, {'field': 'instoreCount', 'old_value': 376, 'new_value': 412}]
2025-06-06 08:13:02,799 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:03,299 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-06 08:13:03,299 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 167430.84, 'new_value': 190784.85}, {'field': 'dailyBillAmount', 'old_value': 167430.84, 'new_value': 190784.85}, {'field': 'amount', 'old_value': 184129.18, 'new_value': 209344.74}, {'field': 'count', 'old_value': 1041, 'new_value': 1216}, {'field': 'instoreAmount', 'old_value': 145106.19999999998, 'new_value': 162654.06}, {'field': 'instoreCount', 'old_value': 578, 'new_value': 655}, {'field': 'onlineAmount', 'old_value': 39339.14, 'new_value': 47262.86}, {'field': 'onlineCount', 'old_value': 463, 'new_value': 561}]
2025-06-06 08:13:03,299 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:03,690 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-06 08:13:03,690 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58451.950000000004, 'new_value': 63401.670000000006}, {'field': 'dailyBillAmount', 'old_value': 58451.950000000004, 'new_value': 63401.670000000006}, {'field': 'amount', 'old_value': 79096.1, 'new_value': 87231.7}, {'field': 'count', 'old_value': 372, 'new_value': 417}, {'field': 'instoreAmount', 'old_value': 73957.8, 'new_value': 81065.0}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 319}, {'field': 'onlineAmount', 'old_value': 5383.61, 'new_value': 6412.01}, {'field': 'onlineCount', 'old_value': 78, 'new_value': 98}]
2025-06-06 08:13:03,690 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:04,002 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-06 08:13:04,002 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 68060.77, 'new_value': 75816.42}, {'field': 'dailyBillAmount', 'old_value': 68060.77, 'new_value': 75816.42}, {'field': 'amount', 'old_value': 64891.7, 'new_value': 72284.5}, {'field': 'count', 'old_value': 264, 'new_value': 300}, {'field': 'instoreAmount', 'old_value': 65908.6, 'new_value': 73301.4}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 300}]
2025-06-06 08:13:04,002 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:04,439 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-06 08:13:04,439 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 144416.53, 'new_value': 162315.93}, {'field': 'amount', 'old_value': 144416.33000000002, 'new_value': 162315.73}, {'field': 'count', 'old_value': 1034, 'new_value': 1214}, {'field': 'instoreAmount', 'old_value': 144416.53, 'new_value': 162315.93}, {'field': 'instoreCount', 'old_value': 1034, 'new_value': 1214}]
2025-06-06 08:13:04,439 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:04,877 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-06 08:13:04,877 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 133658.91999999998, 'new_value': 152827.07}, {'field': 'dailyBillAmount', 'old_value': 133658.91999999998, 'new_value': 152827.07}, {'field': 'amount', 'old_value': 153970.26, 'new_value': 175858.91}, {'field': 'count', 'old_value': 1046, 'new_value': 1224}, {'field': 'instoreAmount', 'old_value': 90091.5, 'new_value': 97882.0}, {'field': 'instoreCount', 'old_value': 455, 'new_value': 503}, {'field': 'onlineAmount', 'old_value': 65978.1, 'new_value': 80141.8}, {'field': 'onlineCount', 'old_value': 591, 'new_value': 721}]
2025-06-06 08:13:04,892 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:05,330 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-06 08:13:05,330 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'amount', 'old_value': 87818.61, 'new_value': 96963.19}, {'field': 'count', 'old_value': 809, 'new_value': 947}, {'field': 'instoreAmount', 'old_value': 68857.86, 'new_value': 72858.76}, {'field': 'instoreCount', 'old_value': 431, 'new_value': 466}, {'field': 'onlineAmount', 'old_value': 19271.68, 'new_value': 24445.73}, {'field': 'onlineCount', 'old_value': 378, 'new_value': 481}]
2025-06-06 08:13:05,330 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:05,830 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-06 08:13:05,830 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 95830.12999999999, 'new_value': 110407.58}, {'field': 'dailyBillAmount', 'old_value': 95830.12999999999, 'new_value': 110407.58}, {'field': 'amount', 'old_value': 96246.78, 'new_value': 110617.45999999999}, {'field': 'count', 'old_value': 772, 'new_value': 926}, {'field': 'instoreAmount', 'old_value': 86704.89, 'new_value': 98980.78}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 528}, {'field': 'onlineAmount', 'old_value': 9568.35, 'new_value': 11685.61}, {'field': 'onlineCount', 'old_value': 325, 'new_value': 398}]
2025-06-06 08:13:05,830 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:06,314 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-06 08:13:06,314 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20196.4, 'new_value': 22022.4}, {'field': 'amount', 'old_value': 20196.4, 'new_value': 22022.4}, {'field': 'count', 'old_value': 103, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 20196.4, 'new_value': 22022.4}, {'field': 'instoreCount', 'old_value': 103, 'new_value': 115}]
2025-06-06 08:13:06,314 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:06,845 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-06 08:13:06,845 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 83510.64, 'new_value': 87781.14}, {'field': 'dailyBillAmount', 'old_value': 83510.64, 'new_value': 87781.14}, {'field': 'amount', 'old_value': -55308.08, 'new_value': -58967.28}, {'field': 'count', 'old_value': 151, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 1592.5, 'new_value': 1670.5}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 67}, {'field': 'onlineAmount', 'old_value': 2375.54, 'new_value': 2768.34}, {'field': 'onlineCount', 'old_value': 88, 'new_value': 105}]
2025-06-06 08:13:06,845 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:07,361 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-06 08:13:07,361 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51468.310000000005, 'new_value': 64007.100000000006}, {'field': 'dailyBillAmount', 'old_value': 51468.310000000005, 'new_value': 64007.100000000006}, {'field': 'amount', 'old_value': 73055.92, 'new_value': 79298.78}, {'field': 'count', 'old_value': 322, 'new_value': 355}, {'field': 'instoreAmount', 'old_value': 73056.68, 'new_value': 79299.54}, {'field': 'instoreCount', 'old_value': 322, 'new_value': 355}]
2025-06-06 08:13:07,361 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:07,939 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-06 08:13:07,939 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 95396.48000000001, 'new_value': 113057.34}, {'field': 'dailyBillAmount', 'old_value': 95396.48000000001, 'new_value': 113057.34}, {'field': 'amount', 'old_value': 29144.1, 'new_value': 36411.9}, {'field': 'count', 'old_value': 126, 'new_value': 149}, {'field': 'instoreAmount', 'old_value': 29919.0, 'new_value': 36925.0}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 143}, {'field': 'onlineAmount', 'old_value': 265.1, 'new_value': 526.9}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 6}]
2025-06-06 08:13:07,939 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:08,438 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-06 08:13:08,438 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60421.780000000006, 'new_value': 68634.98000000001}, {'field': 'dailyBillAmount', 'old_value': 60421.780000000006, 'new_value': 68634.98000000001}, {'field': 'amount', 'old_value': 59486.399999999994, 'new_value': 67617.34999999999}, {'field': 'count', 'old_value': 328, 'new_value': 388}, {'field': 'instoreAmount', 'old_value': 58044.27, 'new_value': 65281.77}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 323}, {'field': 'onlineAmount', 'old_value': 1442.4099999999999, 'new_value': 2335.86}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 65}]
2025-06-06 08:13:08,438 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:08,876 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-06 08:13:08,876 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 63329.159999999996, 'new_value': 70328.19}, {'field': 'dailyBillAmount', 'old_value': 63329.159999999996, 'new_value': 70328.19}, {'field': 'amount', 'old_value': 28673.0, 'new_value': 30855.47}, {'field': 'count', 'old_value': 369, 'new_value': 436}, {'field': 'instoreAmount', 'old_value': 21206.79, 'new_value': 21758.690000000002}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 143}, {'field': 'onlineAmount', 'old_value': 7468.0599999999995, 'new_value': 9098.77}, {'field': 'onlineCount', 'old_value': 234, 'new_value': 293}]
2025-06-06 08:13:08,876 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:09,282 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-06 08:13:09,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22896.84, 'new_value': 28885.65}, {'field': 'amount', 'old_value': 22896.43, 'new_value': 28884.65}, {'field': 'count', 'old_value': 1124, 'new_value': 1373}, {'field': 'instoreAmount', 'old_value': 5697.01, 'new_value': 6903.0}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 296}, {'field': 'onlineAmount', 'old_value': 17618.03, 'new_value': 22479.14}, {'field': 'onlineCount', 'old_value': 884, 'new_value': 1077}]
2025-06-06 08:13:09,298 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:09,735 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-06 08:13:09,735 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9110.0, 'new_value': 10487.0}, {'field': 'amount', 'old_value': 9110.0, 'new_value': 10487.0}, {'field': 'count', 'old_value': 41, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 9110.0, 'new_value': 10487.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 45}]
2025-06-06 08:13:09,735 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:10,157 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-06 08:13:10,157 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72739.13, 'new_value': 81044.73}, {'field': 'dailyBillAmount', 'old_value': 72739.13, 'new_value': 81044.73}, {'field': 'amount', 'old_value': 31324.9, 'new_value': 34272.0}, {'field': 'count', 'old_value': 554, 'new_value': 618}, {'field': 'instoreAmount', 'old_value': 31465.7, 'new_value': 34508.3}, {'field': 'instoreCount', 'old_value': 554, 'new_value': 618}]
2025-06-06 08:13:10,157 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:10,719 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-06 08:13:10,719 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7144.85, 'new_value': 8770.86}, {'field': 'amount', 'old_value': 7142.97, 'new_value': 8768.98}, {'field': 'count', 'old_value': 412, 'new_value': 482}, {'field': 'instoreAmount', 'old_value': 3436.2, 'new_value': 3688.1}, {'field': 'instoreCount', 'old_value': 170, 'new_value': 184}, {'field': 'onlineAmount', 'old_value': 3907.41, 'new_value': 5296.27}, {'field': 'onlineCount', 'old_value': 242, 'new_value': 298}]
2025-06-06 08:13:10,719 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:11,141 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-06 08:13:11,141 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13457.7, 'new_value': 14477.1}, {'field': 'amount', 'old_value': 13457.7, 'new_value': 14477.1}, {'field': 'count', 'old_value': 34, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 13457.7, 'new_value': 14477.1}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 38}]
2025-06-06 08:13:11,141 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-06 08:13:11,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-06-06 08:13:11,688 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22742.0, 'new_value': 374783.99}, {'field': 'dailyBillAmount', 'old_value': 22742.0, 'new_value': 374783.99}]
2025-06-06 08:13:11,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:12,125 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-06 08:13:12,125 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31900.86, 'new_value': 38332.12}, {'field': 'dailyBillAmount', 'old_value': 26310.4, 'new_value': 31080.0}, {'field': 'amount', 'old_value': 31900.49, 'new_value': 38331.75}, {'field': 'count', 'old_value': 431, 'new_value': 516}, {'field': 'instoreAmount', 'old_value': 30676.1, 'new_value': 36630.5}, {'field': 'instoreCount', 'old_value': 372, 'new_value': 439}, {'field': 'onlineAmount', 'old_value': 1253.76, 'new_value': 1730.62}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 77}]
2025-06-06 08:13:12,125 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:12,562 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-06 08:13:12,562 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5733.7, 'new_value': 6601.2}, {'field': 'amount', 'old_value': 5733.36, 'new_value': 6600.86}, {'field': 'count', 'old_value': 249, 'new_value': 292}, {'field': 'instoreAmount', 'old_value': 4949.5, 'new_value': 5718.0}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 264}, {'field': 'onlineAmount', 'old_value': 818.6, 'new_value': 917.6}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 28}]
2025-06-06 08:13:12,562 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:12,984 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-06 08:13:12,984 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78441.62000000001, 'new_value': 86352.83}, {'field': 'dailyBillAmount', 'old_value': 78441.62000000001, 'new_value': 86352.83}, {'field': 'amount', 'old_value': 100396.2, 'new_value': 110730.49}, {'field': 'count', 'old_value': 794, 'new_value': 931}, {'field': 'instoreAmount', 'old_value': 96534.05, 'new_value': 106117.24}, {'field': 'instoreCount', 'old_value': 554, 'new_value': 656}, {'field': 'onlineAmount', 'old_value': 5887.09, 'new_value': 6638.19}, {'field': 'onlineCount', 'old_value': 240, 'new_value': 275}]
2025-06-06 08:13:12,984 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:13,406 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-06 08:13:13,406 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19475.03, 'new_value': 23074.260000000002}, {'field': 'dailyBillAmount', 'old_value': 19475.03, 'new_value': 23074.260000000002}, {'field': 'amount', 'old_value': 7467.7300000000005, 'new_value': 8842.19}, {'field': 'count', 'old_value': 90, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 5408.94, 'new_value': 6313.76}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 80}, {'field': 'onlineAmount', 'old_value': 2082.88, 'new_value': 2560.88}, {'field': 'onlineCount', 'old_value': 36, 'new_value': 43}]
2025-06-06 08:13:13,406 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:13,797 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-06 08:13:13,797 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19269.41, 'new_value': 24511.870000000003}, {'field': 'dailyBillAmount', 'old_value': 19354.67, 'new_value': 24637.27}, {'field': 'amount', 'old_value': 19269.41, 'new_value': 24511.6}, {'field': 'count', 'old_value': 1036, 'new_value': 1345}, {'field': 'instoreAmount', 'old_value': 9201.25, 'new_value': 11778.98}, {'field': 'instoreCount', 'old_value': 439, 'new_value': 593}, {'field': 'onlineAmount', 'old_value': 10305.710000000001, 'new_value': 12988.240000000002}, {'field': 'onlineCount', 'old_value': 597, 'new_value': 752}]
2025-06-06 08:13:13,812 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:14,171 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-06 08:13:14,171 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12075.1, 'new_value': 14352.73}, {'field': 'amount', 'old_value': 12074.89, 'new_value': 14352.52}, {'field': 'count', 'old_value': 760, 'new_value': 907}, {'field': 'instoreAmount', 'old_value': 6597.16, 'new_value': 7771.3099999999995}, {'field': 'instoreCount', 'old_value': 357, 'new_value': 423}, {'field': 'onlineAmount', 'old_value': 6307.53, 'new_value': 7544.6900000000005}, {'field': 'onlineCount', 'old_value': 403, 'new_value': 484}]
2025-06-06 08:13:14,171 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:14,593 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-06 08:13:14,593 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 36206.32, 'new_value': 38202.479999999996}, {'field': 'count', 'old_value': 343, 'new_value': 385}, {'field': 'instoreAmount', 'old_value': 36219.799999999996, 'new_value': 38215.96}, {'field': 'instoreCount', 'old_value': 343, 'new_value': 385}]
2025-06-06 08:13:14,593 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:14,999 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-06 08:13:14,999 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16550.27, 'new_value': 20016.25}, {'field': 'dailyBillAmount', 'old_value': 17223.66, 'new_value': 20867.0}, {'field': 'amount', 'old_value': 16549.72, 'new_value': 20015.7}, {'field': 'count', 'old_value': 450, 'new_value': 543}, {'field': 'instoreAmount', 'old_value': 14887.35, 'new_value': 18113.64}, {'field': 'instoreCount', 'old_value': 307, 'new_value': 381}, {'field': 'onlineAmount', 'old_value': 1674.52, 'new_value': 1914.21}, {'field': 'onlineCount', 'old_value': 143, 'new_value': 162}]
2025-06-06 08:13:14,999 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:15,421 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-06 08:13:15,421 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30004.39, 'new_value': 40318.22}, {'field': 'dailyBillAmount', 'old_value': 30004.39, 'new_value': 40318.22}, {'field': 'amount', 'old_value': 3930.05, 'new_value': 5488.59}, {'field': 'count', 'old_value': 139, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 4451.97, 'new_value': 6110.860000000001}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 201}]
2025-06-06 08:13:15,421 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:15,859 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-06 08:13:15,859 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 90356.14, 'new_value': 106751.03}, {'field': 'dailyBillAmount', 'old_value': 90356.14, 'new_value': 106751.03}, {'field': 'amount', 'old_value': 8323.0, 'new_value': 10310.8}, {'field': 'count', 'old_value': 45, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 8323.0, 'new_value': 10310.8}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 57}]
2025-06-06 08:13:15,859 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:16,280 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-06 08:13:16,280 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2862.83, 'new_value': 3667.66}, {'field': 'count', 'old_value': 141, 'new_value': 185}, {'field': 'onlineAmount', 'old_value': 2886.51, 'new_value': 3691.34}, {'field': 'onlineCount', 'old_value': 141, 'new_value': 185}]
2025-06-06 08:13:16,280 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:16,733 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-06 08:13:16,733 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55883.89, 'new_value': 70015.61}, {'field': 'amount', 'old_value': 55882.98, 'new_value': 70014.7}, {'field': 'count', 'old_value': 555, 'new_value': 730}, {'field': 'instoreAmount', 'old_value': 51832.600000000006, 'new_value': 64795.3}, {'field': 'instoreCount', 'old_value': 443, 'new_value': 579}, {'field': 'onlineAmount', 'old_value': 4337.13, 'new_value': 5523.55}, {'field': 'onlineCount', 'old_value': 112, 'new_value': 151}]
2025-06-06 08:13:16,733 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:17,171 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-06 08:13:17,171 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25957.53, 'new_value': 33633.55}, {'field': 'dailyBillAmount', 'old_value': 22340.33, 'new_value': 30016.350000000002}, {'field': 'amount', 'old_value': 19015.04, 'new_value': 24048.760000000002}, {'field': 'count', 'old_value': 571, 'new_value': 723}, {'field': 'instoreAmount', 'old_value': 3834.88, 'new_value': 5063.68}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 109}, {'field': 'onlineAmount', 'old_value': 15267.32, 'new_value': 19105.239999999998}, {'field': 'onlineCount', 'old_value': 493, 'new_value': 614}]
2025-06-06 08:13:17,171 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:17,577 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-06 08:13:17,577 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15921.960000000001, 'new_value': 22647.91}, {'field': 'dailyBillAmount', 'old_value': 15921.960000000001, 'new_value': 22647.91}, {'field': 'amount', 'old_value': 1021.23, 'new_value': 1484.2}, {'field': 'count', 'old_value': 40, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 1021.53, 'new_value': 1484.5}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 54}]
2025-06-06 08:13:17,577 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:18,108 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-06 08:13:18,108 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1202.2, 'new_value': 1434.3200000000002}, {'field': 'count', 'old_value': 59, 'new_value': 69}, {'field': 'onlineAmount', 'old_value': 1256.27, 'new_value': 1488.39}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 69}]
2025-06-06 08:13:18,108 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:18,514 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-06 08:13:18,514 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15654.210000000001, 'new_value': 19081.11}, {'field': 'dailyBillAmount', 'old_value': 8730.720000000001, 'new_value': 10212.32}, {'field': 'amount', 'old_value': 15653.32, 'new_value': 19080.22}, {'field': 'count', 'old_value': 404, 'new_value': 501}, {'field': 'instoreAmount', 'old_value': 9695.39, 'new_value': 11075.69}, {'field': 'instoreCount', 'old_value': 239, 'new_value': 280}, {'field': 'onlineAmount', 'old_value': 6370.26, 'new_value': 8416.86}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 221}]
2025-06-06 08:13:18,514 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:18,967 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-06 08:13:18,967 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20601.51, 'new_value': 26476.37}, {'field': 'amount', 'old_value': 20601.239999999998, 'new_value': 26476.1}, {'field': 'count', 'old_value': 935, 'new_value': 1225}, {'field': 'instoreAmount', 'old_value': 20863.67, 'new_value': 26831.81}, {'field': 'instoreCount', 'old_value': 935, 'new_value': 1225}]
2025-06-06 08:13:18,967 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:19,373 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-06 08:13:19,373 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8340.2, 'new_value': 10971.58}, {'field': 'dailyBillAmount', 'old_value': 8340.2, 'new_value': 10971.58}, {'field': 'amount', 'old_value': 4847.83, 'new_value': 6608.53}, {'field': 'count', 'old_value': 240, 'new_value': 323}, {'field': 'instoreAmount', 'old_value': 1830.45, 'new_value': 2401.93}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 88}, {'field': 'onlineAmount', 'old_value': 3018.33, 'new_value': 4207.55}, {'field': 'onlineCount', 'old_value': 171, 'new_value': 235}]
2025-06-06 08:13:19,373 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:19,811 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-06 08:13:19,811 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10511.189999999999, 'new_value': 12432.97}, {'field': 'amount', 'old_value': 10511.17, 'new_value': 12432.949999999999}, {'field': 'count', 'old_value': 341, 'new_value': 420}, {'field': 'instoreAmount', 'old_value': 5316.8, 'new_value': 6208.32}, {'field': 'instoreCount', 'old_value': 217, 'new_value': 272}, {'field': 'onlineAmount', 'old_value': 5194.39, 'new_value': 6224.65}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 148}]
2025-06-06 08:13:19,811 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:20,201 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-06 08:13:20,201 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6829.49, 'new_value': 8497.49}, {'field': 'amount', 'old_value': 6828.65, 'new_value': 8496.65}, {'field': 'count', 'old_value': 170, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 5809.0, 'new_value': 7252.7}, {'field': 'instoreCount', 'old_value': 148, 'new_value': 184}, {'field': 'onlineAmount', 'old_value': 1216.49, 'new_value': 1504.79}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 28}]
2025-06-06 08:13:20,201 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:20,670 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-06 08:13:20,670 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35136.08, 'new_value': 45156.0}, {'field': 'dailyBillAmount', 'old_value': 35136.08, 'new_value': 45156.0}, {'field': 'amount', 'old_value': 22091.68, 'new_value': 29816.629999999997}, {'field': 'count', 'old_value': 566, 'new_value': 747}, {'field': 'instoreAmount', 'old_value': 13022.900000000001, 'new_value': 19018.120000000003}, {'field': 'instoreCount', 'old_value': 282, 'new_value': 394}, {'field': 'onlineAmount', 'old_value': 11028.15, 'new_value': 13483.78}, {'field': 'onlineCount', 'old_value': 284, 'new_value': 353}]
2025-06-06 08:13:20,685 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:21,170 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-06 08:13:21,170 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132487.25, 'new_value': 156281.35}, {'field': 'dailyBillAmount', 'old_value': 132487.25, 'new_value': 156281.35}, {'field': 'amount', 'old_value': 102692.8, 'new_value': 118529.9}, {'field': 'count', 'old_value': 677, 'new_value': 782}, {'field': 'instoreAmount', 'old_value': 78531.40000000001, 'new_value': 89385.8}, {'field': 'instoreCount', 'old_value': 563, 'new_value': 645}, {'field': 'onlineAmount', 'old_value': 24162.199999999997, 'new_value': 29144.899999999998}, {'field': 'onlineCount', 'old_value': 114, 'new_value': 137}]
2025-06-06 08:13:21,170 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:21,607 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-06 08:13:21,607 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 163851.56, 'new_value': 202808.36000000002}, {'field': 'amount', 'old_value': 163851.26, 'new_value': 202808.06}, {'field': 'count', 'old_value': 563, 'new_value': 702}, {'field': 'instoreAmount', 'old_value': 163708.56, 'new_value': 202665.36000000002}, {'field': 'instoreCount', 'old_value': 562, 'new_value': 701}]
2025-06-06 08:13:21,607 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:22,029 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-06 08:13:22,029 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81135.05, 'new_value': 100908.63}, {'field': 'dailyBillAmount', 'old_value': 71745.48, 'new_value': 89180.22}, {'field': 'amount', 'old_value': 81134.31, 'new_value': 100907.89}, {'field': 'count', 'old_value': 525, 'new_value': 680}, {'field': 'instoreAmount', 'old_value': 73371.85, 'new_value': 90801.0}, {'field': 'instoreCount', 'old_value': 320, 'new_value': 395}, {'field': 'onlineAmount', 'old_value': 7806.76, 'new_value': 10201.52}, {'field': 'onlineCount', 'old_value': 205, 'new_value': 285}]
2025-06-06 08:13:22,029 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:22,623 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-06 08:13:22,623 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59663.77, 'new_value': 69420.43}, {'field': 'dailyBillAmount', 'old_value': 45877.68, 'new_value': 55603.34}, {'field': 'amount', 'old_value': 59663.77, 'new_value': 69420.43}, {'field': 'count', 'old_value': 180, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 54145.4, 'new_value': 62764.4}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 159}, {'field': 'onlineAmount', 'old_value': 5685.2, 'new_value': 6822.860000000001}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 53}]
2025-06-06 08:13:22,623 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:23,044 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-06 08:13:23,044 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 114956.47, 'new_value': 137207.53}, {'field': 'amount', 'old_value': 114955.51999999999, 'new_value': 137206.58}, {'field': 'count', 'old_value': 702, 'new_value': 863}, {'field': 'instoreAmount', 'old_value': 104558.1, 'new_value': 124471.19}, {'field': 'instoreCount', 'old_value': 378, 'new_value': 453}, {'field': 'onlineAmount', 'old_value': 10398.37, 'new_value': 12736.55}, {'field': 'onlineCount', 'old_value': 324, 'new_value': 410}]
2025-06-06 08:13:23,044 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:23,450 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-06 08:13:23,450 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 115632.76999999999, 'new_value': 138030.02}, {'field': 'dailyBillAmount', 'old_value': 115632.76999999999, 'new_value': 138030.02}, {'field': 'amount', 'old_value': 99900.58, 'new_value': 126966.1}, {'field': 'count', 'old_value': 531, 'new_value': 662}, {'field': 'instoreAmount', 'old_value': 90599.44, 'new_value': 114597.04}, {'field': 'instoreCount', 'old_value': 431, 'new_value': 535}, {'field': 'onlineAmount', 'old_value': 9560.720000000001, 'new_value': 12629.220000000001}, {'field': 'onlineCount', 'old_value': 100, 'new_value': 127}]
2025-06-06 08:13:23,450 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:23,794 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-06 08:13:23,794 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30110.8, 'new_value': 36973.8}, {'field': 'dailyBillAmount', 'old_value': 30110.8, 'new_value': 36973.8}, {'field': 'amount', 'old_value': 29388.0, 'new_value': 36126.0}, {'field': 'count', 'old_value': 49, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 29388.0, 'new_value': 36126.0}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 60}]
2025-06-06 08:13:23,794 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:24,216 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-06 08:13:24,216 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21641.78, 'new_value': 24146.48}, {'field': 'dailyBillAmount', 'old_value': 21641.78, 'new_value': 24146.48}, {'field': 'amount', 'old_value': 19701.66, 'new_value': 22681.36}, {'field': 'count', 'old_value': 25, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 22764.55, 'new_value': 25744.25}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 26}]
2025-06-06 08:13:24,216 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:24,763 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-06 08:13:24,763 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3083.39, 'new_value': 3795.79}, {'field': 'amount', 'old_value': 3083.15, 'new_value': 3795.55}, {'field': 'count', 'old_value': 60, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 3083.39, 'new_value': 3795.79}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 73}]
2025-06-06 08:13:24,763 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:25,231 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-06 08:13:25,231 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9421.539999999999, 'new_value': 12429.94}, {'field': 'amount', 'old_value': 9420.55, 'new_value': 12428.95}, {'field': 'count', 'old_value': 94, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 9421.539999999999, 'new_value': 12429.94}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 119}]
2025-06-06 08:13:25,231 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:25,637 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-06 08:13:25,637 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55494.28, 'new_value': 68419.4}, {'field': 'dailyBillAmount', 'old_value': 55494.28, 'new_value': 68419.4}, {'field': 'amount', 'old_value': 58724.7, 'new_value': 71195.22}, {'field': 'count', 'old_value': 1585, 'new_value': 1969}, {'field': 'instoreAmount', 'old_value': 55128.85, 'new_value': 66770.85}, {'field': 'instoreCount', 'old_value': 1381, 'new_value': 1714}, {'field': 'onlineAmount', 'old_value': 4404.77, 'new_value': 5494.79}, {'field': 'onlineCount', 'old_value': 204, 'new_value': 255}]
2025-06-06 08:13:25,637 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-06 08:13:26,044 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-06-06 08:13:26,044 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1538764.13, 'new_value': 1539274.13}, {'field': 'amount', 'old_value': 1538763.73, 'new_value': 1539272.78}, {'field': 'count', 'old_value': 4947, 'new_value': 4948}, {'field': 'instoreAmount', 'old_value': 1539975.13, 'new_value': 1540485.13}, {'field': 'instoreCount', 'old_value': 4947, 'new_value': 4948}]
2025-06-06 08:13:26,044 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:26,418 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-06 08:13:26,434 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 90969.4, 'new_value': 125605.4}, {'field': 'dailyBillAmount', 'old_value': 90969.4, 'new_value': 125605.4}, {'field': 'amount', 'old_value': 89344.15, 'new_value': 116201.62}, {'field': 'count', 'old_value': 277, 'new_value': 350}, {'field': 'instoreAmount', 'old_value': 89321.18000000001, 'new_value': 119902.84}, {'field': 'instoreCount', 'old_value': 228, 'new_value': 287}, {'field': 'onlineAmount', 'old_value': 1494.68, 'new_value': 1824.83}, {'field': 'onlineCount', 'old_value': 49, 'new_value': 63}]
2025-06-06 08:13:26,434 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:26,934 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-06 08:13:26,934 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 168159.97, 'new_value': 211570.69}, {'field': 'amount', 'old_value': 168159.91999999998, 'new_value': 211570.64}, {'field': 'count', 'old_value': 572, 'new_value': 698}, {'field': 'instoreAmount', 'old_value': 170104.97, 'new_value': 213515.69}, {'field': 'instoreCount', 'old_value': 572, 'new_value': 698}]
2025-06-06 08:13:26,934 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:27,356 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-06 08:13:27,356 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 101784.06, 'new_value': 181766.95}, {'field': 'dailyBillAmount', 'old_value': 101784.06, 'new_value': 181766.95}, {'field': 'amount', 'old_value': 108890.39, 'new_value': 134844.12}, {'field': 'count', 'old_value': 410, 'new_value': 527}, {'field': 'instoreAmount', 'old_value': 107885.25, 'new_value': 133716.41}, {'field': 'instoreCount', 'old_value': 245, 'new_value': 312}, {'field': 'onlineAmount', 'old_value': 5274.2, 'new_value': 6863.3}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 215}]
2025-06-06 08:13:27,356 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:27,809 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-06 08:13:27,809 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 266023.2, 'new_value': 339190.76}, {'field': 'dailyBillAmount', 'old_value': 266023.2, 'new_value': 339190.76}, {'field': 'amount', 'old_value': 208137.0, 'new_value': 277493.0}, {'field': 'count', 'old_value': 489, 'new_value': 643}, {'field': 'instoreAmount', 'old_value': 221602.0, 'new_value': 293490.0}, {'field': 'instoreCount', 'old_value': 489, 'new_value': 643}]
2025-06-06 08:13:27,809 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:28,199 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-06 08:13:28,199 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45188.37, 'new_value': 55814.54}, {'field': 'dailyBillAmount', 'old_value': 45188.37, 'new_value': 55814.54}, {'field': 'amount', 'old_value': 44938.58, 'new_value': 55341.2}, {'field': 'count', 'old_value': 230, 'new_value': 290}, {'field': 'instoreAmount', 'old_value': 42502.3, 'new_value': 52442.1}, {'field': 'instoreCount', 'old_value': 192, 'new_value': 240}, {'field': 'onlineAmount', 'old_value': 2809.69, 'new_value': 3523.17}, {'field': 'onlineCount', 'old_value': 38, 'new_value': 50}]
2025-06-06 08:13:28,199 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:28,590 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-06 08:13:28,590 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 118707.81, 'new_value': 152365.43}, {'field': 'dailyBillAmount', 'old_value': 118707.81, 'new_value': 152365.43}, {'field': 'amount', 'old_value': 154969.68, 'new_value': 188627.3}, {'field': 'count', 'old_value': 683, 'new_value': 831}, {'field': 'instoreAmount', 'old_value': 154969.73, 'new_value': 188627.35}, {'field': 'instoreCount', 'old_value': 683, 'new_value': 831}]
2025-06-06 08:13:28,590 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:29,090 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-06 08:13:29,090 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50323.27, 'new_value': 61831.3}, {'field': 'dailyBillAmount', 'old_value': 50323.27, 'new_value': 61831.3}, {'field': 'amount', 'old_value': 89436.1, 'new_value': 107559.4}, {'field': 'count', 'old_value': 143, 'new_value': 185}, {'field': 'instoreAmount', 'old_value': 88277.8, 'new_value': 106334.8}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 177}, {'field': 'onlineAmount', 'old_value': 1159.6, 'new_value': 1225.8999999999999}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 8}]
2025-06-06 08:13:29,090 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:29,574 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-06 08:13:29,574 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47769.520000000004, 'new_value': 52862.59}, {'field': 'dailyBillAmount', 'old_value': 47769.520000000004, 'new_value': 52862.59}, {'field': 'amount', 'old_value': 51871.3, 'new_value': 57130.3}, {'field': 'count', 'old_value': 339, 'new_value': 380}, {'field': 'instoreAmount', 'old_value': 51871.3, 'new_value': 57296.3}, {'field': 'instoreCount', 'old_value': 339, 'new_value': 380}]
2025-06-06 08:13:29,574 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:30,027 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-06 08:13:30,027 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43502.73, 'new_value': 50805.76}, {'field': 'dailyBillAmount', 'old_value': 43502.73, 'new_value': 50805.76}, {'field': 'amount', 'old_value': 33763.2, 'new_value': 39971.2}, {'field': 'count', 'old_value': 198, 'new_value': 246}, {'field': 'instoreAmount', 'old_value': 34762.0, 'new_value': 41406.0}, {'field': 'instoreCount', 'old_value': 192, 'new_value': 240}]
2025-06-06 08:13:30,027 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:30,433 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-06 08:13:30,433 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3284.4, 'new_value': 4612.2}, {'field': 'dailyBillAmount', 'old_value': 3284.4, 'new_value': 4612.2}]
2025-06-06 08:13:30,433 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:30,871 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-06 08:13:30,871 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 13485.24, 'new_value': 17492.59}, {'field': 'count', 'old_value': 788, 'new_value': 973}, {'field': 'instoreAmount', 'old_value': 1945.99, 'new_value': 2400.82}, {'field': 'instoreCount', 'old_value': 178, 'new_value': 219}, {'field': 'onlineAmount', 'old_value': 12400.5, 'new_value': 16009.0}, {'field': 'onlineCount', 'old_value': 610, 'new_value': 754}]
2025-06-06 08:13:30,871 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:31,308 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-06 08:13:31,308 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28657.15, 'new_value': 37846.32}, {'field': 'amount', 'old_value': 28655.760000000002, 'new_value': 37844.24}, {'field': 'count', 'old_value': 545, 'new_value': 727}, {'field': 'instoreAmount', 'old_value': 24471.989999999998, 'new_value': 32964.13}, {'field': 'instoreCount', 'old_value': 446, 'new_value': 614}, {'field': 'onlineAmount', 'old_value': 4185.16, 'new_value': 4882.19}, {'field': 'onlineCount', 'old_value': 99, 'new_value': 113}]
2025-06-06 08:13:31,308 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:31,745 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-06 08:13:31,745 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6139.2, 'new_value': 7350.0}, {'field': 'amount', 'old_value': 6138.8, 'new_value': 7349.6}, {'field': 'count', 'old_value': 36, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 6139.2, 'new_value': 7350.0}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 43}]
2025-06-06 08:13:31,745 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:32,214 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-06 08:13:32,214 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2856.0, 'new_value': 3210.0}, {'field': 'dailyBillAmount', 'old_value': 2856.0, 'new_value': 3210.0}, {'field': 'amount', 'old_value': 4714.3, 'new_value': 6105.6}, {'field': 'count', 'old_value': 46, 'new_value': 58}, {'field': 'instoreAmount', 'old_value': 4882.6, 'new_value': 6273.9}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 58}]
2025-06-06 08:13:32,214 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:32,542 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-06 08:13:32,542 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8346.0, 'new_value': 8888.0}, {'field': 'dailyBillAmount', 'old_value': 8346.0, 'new_value': 8888.0}]
2025-06-06 08:13:32,542 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:32,979 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-06 08:13:32,979 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38446.0, 'new_value': 45807.5}, {'field': 'dailyBillAmount', 'old_value': 38446.0, 'new_value': 45807.5}, {'field': 'amount', 'old_value': 27913.16, 'new_value': 33056.31}, {'field': 'count', 'old_value': 825, 'new_value': 1029}, {'field': 'instoreAmount', 'old_value': 26134.97, 'new_value': 31059.82}, {'field': 'instoreCount', 'old_value': 778, 'new_value': 971}, {'field': 'onlineAmount', 'old_value': 1964.1799999999998, 'new_value': 2218.88}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 58}]
2025-06-06 08:13:32,979 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:33,432 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-06 08:13:33,432 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7940.299999999999, 'new_value': 9486.8}, {'field': 'dailyBillAmount', 'old_value': 7940.299999999999, 'new_value': 9486.8}, {'field': 'amount', 'old_value': 7889.4, 'new_value': 8985.9}, {'field': 'count', 'old_value': 53, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 8417.8, 'new_value': 9514.3}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 57}]
2025-06-06 08:13:33,432 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:33,807 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-06 08:13:33,807 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9487.49, 'new_value': 12630.92}, {'field': 'dailyBillAmount', 'old_value': 9487.49, 'new_value': 12630.92}]
2025-06-06 08:13:33,807 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:34,198 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-06 08:13:34,198 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8376.14, 'new_value': 10415.949999999999}, {'field': 'amount', 'old_value': 8375.54, 'new_value': 10414.54}, {'field': 'count', 'old_value': 499, 'new_value': 628}, {'field': 'instoreAmount', 'old_value': 8436.11, 'new_value': 10493.16}, {'field': 'instoreCount', 'old_value': 499, 'new_value': 628}]
2025-06-06 08:13:34,198 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:34,604 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-06 08:13:34,604 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11521.58, 'new_value': 14438.2}, {'field': 'dailyBillAmount', 'old_value': 11521.58, 'new_value': 14438.2}, {'field': 'amount', 'old_value': 11517.89, 'new_value': 14630.369999999999}, {'field': 'count', 'old_value': 572, 'new_value': 735}, {'field': 'instoreAmount', 'old_value': 10641.4, 'new_value': 13427.5}, {'field': 'instoreCount', 'old_value': 522, 'new_value': 667}, {'field': 'onlineAmount', 'old_value': 919.26, 'new_value': 1280.67}, {'field': 'onlineCount', 'old_value': 50, 'new_value': 68}]
2025-06-06 08:13:34,604 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:35,041 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-06 08:13:35,041 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9218.89, 'new_value': 10982.27}, {'field': 'amount', 'old_value': 9218.74, 'new_value': 10982.12}, {'field': 'count', 'old_value': 418, 'new_value': 514}, {'field': 'instoreAmount', 'old_value': 5243.64, 'new_value': 6460.01}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 320}, {'field': 'onlineAmount', 'old_value': 4013.07, 'new_value': 4560.08}, {'field': 'onlineCount', 'old_value': 169, 'new_value': 194}]
2025-06-06 08:13:35,041 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:35,463 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-06 08:13:35,463 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6696.18, 'new_value': 8480.34}, {'field': 'dailyBillAmount', 'old_value': 6696.18, 'new_value': 8480.34}, {'field': 'amount', 'old_value': 4500.01, 'new_value': 5849.71}, {'field': 'count', 'old_value': 170, 'new_value': 226}, {'field': 'instoreAmount', 'old_value': 4614.15, 'new_value': 5963.85}, {'field': 'instoreCount', 'old_value': 170, 'new_value': 226}]
2025-06-06 08:13:35,463 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:35,869 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-06 08:13:35,869 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9564.52, 'new_value': 11831.92}, {'field': 'amount', 'old_value': 9564.43, 'new_value': 11831.4}, {'field': 'count', 'old_value': 597, 'new_value': 730}, {'field': 'instoreAmount', 'old_value': 2277.15, 'new_value': 2881.95}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 144}, {'field': 'onlineAmount', 'old_value': 7440.139999999999, 'new_value': 9229.84}, {'field': 'onlineCount', 'old_value': 480, 'new_value': 586}]
2025-06-06 08:13:35,869 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:36,244 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-06 08:13:36,244 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18939.54, 'new_value': 22517.73}, {'field': 'dailyBillAmount', 'old_value': 18939.54, 'new_value': 22517.73}, {'field': 'amount', 'old_value': 14357.8, 'new_value': 17508.7}, {'field': 'count', 'old_value': 125, 'new_value': 149}, {'field': 'instoreAmount', 'old_value': 14358.3, 'new_value': 17509.2}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 149}]
2025-06-06 08:13:36,244 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:36,666 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-06 08:13:36,666 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22222.8, 'new_value': 25353.8}, {'field': 'dailyBillAmount', 'old_value': 22222.8, 'new_value': 25353.8}, {'field': 'amount', 'old_value': 26353.0, 'new_value': 30025.0}, {'field': 'count', 'old_value': 105, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 26353.0, 'new_value': 30025.0}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 118}]
2025-06-06 08:13:36,666 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:37,166 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-06 08:13:37,166 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1848.0, 'new_value': 3230.0}, {'field': 'dailyBillAmount', 'old_value': 1848.0, 'new_value': 3230.0}, {'field': 'amount', 'old_value': 9612.0, 'new_value': 10994.0}, {'field': 'count', 'old_value': 50, 'new_value': 58}, {'field': 'instoreAmount', 'old_value': 9612.0, 'new_value': 10994.0}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 58}]
2025-06-06 08:13:37,166 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:37,619 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-06 08:13:37,619 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15895.0, 'new_value': 21088.0}, {'field': 'amount', 'old_value': 15895.0, 'new_value': 21088.0}, {'field': 'count', 'old_value': 191, 'new_value': 243}, {'field': 'instoreAmount', 'old_value': 15895.0, 'new_value': 21088.0}, {'field': 'instoreCount', 'old_value': 191, 'new_value': 243}]
2025-06-06 08:13:37,619 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:37,978 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-06 08:13:37,978 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3275.05, 'new_value': 4772.98}, {'field': 'dailyBillAmount', 'old_value': 3275.05, 'new_value': 4772.98}, {'field': 'amount', 'old_value': 527.3, 'new_value': 466.09999999999997}]
2025-06-06 08:13:37,978 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:38,447 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-06 08:13:38,462 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4874.0, 'new_value': 6356.0}, {'field': 'dailyBillAmount', 'old_value': 4874.0, 'new_value': 6356.0}, {'field': 'amount', 'old_value': 7247.0, 'new_value': 8591.0}, {'field': 'count', 'old_value': 37, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 7247.0, 'new_value': 8591.0}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 43}]
2025-06-06 08:13:38,462 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:38,915 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-06 08:13:38,915 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10845.53, 'new_value': 14594.98}, {'field': 'dailyBillAmount', 'old_value': 10845.53, 'new_value': 14594.98}, {'field': 'amount', 'old_value': 8169.54, 'new_value': 11307.34}, {'field': 'count', 'old_value': 263, 'new_value': 362}, {'field': 'instoreAmount', 'old_value': 7960.97, 'new_value': 11050.87}, {'field': 'instoreCount', 'old_value': 253, 'new_value': 350}, {'field': 'onlineAmount', 'old_value': 209.0, 'new_value': 256.9}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 12}]
2025-06-06 08:13:38,915 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:39,290 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-06 08:13:39,290 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12526.41, 'new_value': 16268.84}, {'field': 'dailyBillAmount', 'old_value': 12526.41, 'new_value': 16268.84}, {'field': 'amount', 'old_value': 12192.2, 'new_value': 15930.900000000001}, {'field': 'count', 'old_value': 68, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 12132.4, 'new_value': 15871.1}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 82}]
2025-06-06 08:13:39,290 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-06 08:13:39,681 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-06 08:13:39,681 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31854.2, 'new_value': 37901.85}, {'field': 'dailyBillAmount', 'old_value': 31854.2, 'new_value': 37901.85}, {'field': 'amount', 'old_value': 33790.3, 'new_value': 40169.3}, {'field': 'count', 'old_value': 210, 'new_value': 270}, {'field': 'instoreAmount', 'old_value': 32268.0, 'new_value': 38383.0}, {'field': 'instoreCount', 'old_value': 179, 'new_value': 234}, {'field': 'onlineAmount', 'old_value': 1554.3, 'new_value': 1818.3}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 36}]
2025-06-06 08:13:39,681 - INFO - 月销售数据同步完成！更新: 205 条，插入: 0 条，错误: 0 条，跳过: 1197 条
2025-06-06 08:13:39,681 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-7 至 2025-6
2025-06-06 08:13:40,321 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250606.xlsx
2025-06-06 08:13:40,337 - INFO - 综合数据同步流程完成！
2025-06-06 08:13:40,399 - INFO - 综合数据同步完成
2025-06-06 08:13:40,399 - INFO - ==================================================
2025-06-06 08:13:40,399 - INFO - 程序退出
2025-06-06 08:13:40,399 - INFO - ==================================================
