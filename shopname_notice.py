import pymysql
import logging
import datetime
import os
from typing import List, Dict, Any, Optional, Set
from load_config import load_config
from sendemail import send_email

# 配置日志
def setup_logging():
    """配置日志设置"""
    # 确保logs目录存在
    os.makedirs('logs', exist_ok=True)
    
    log_date = datetime.datetime.now().strftime('%Y%m%d')
    log_file = f'logs/shopname_notice_{log_date}.log'
    
    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        encoding='utf-8'
    )
    
    # 添加控制台处理器，便于调试
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logging.getLogger().addHandler(console_handler)
    
    return log_file

def connect_database1():
    """连接到数据库1"""
    db_config = load_config('config.ini', 'DATABASE1')
    return pymysql.connect(
        host=db_config['host'],
        port=int(db_config['port']),
        user=db_config['user'],
        password=db_config['password'],
        database=db_config['database'],
        charset=db_config['charset'],
        cursorclass=pymysql.cursors.DictCursor
    )

def get_max_sale_date(conn):
    """获取销售数据表的最大日期"""
    with conn.cursor() as cursor:
        cursor.execute("SELECT MAX(sale_time) as max_date FROM sales_data")
        result = cursor.fetchone()
        return result['max_date'] if result and result['max_date'] else None

def get_shops_from_db1(conn, max_date):
    """根据最大日期获取数据库1中的店铺名称"""
    shops = set()
    with conn.cursor() as cursor:
        cursor.execute("SELECT shop_entity_name FROM sales_data WHERE sale_time=%s", (max_date,))
        for row in cursor.fetchall():
            if row['shop_entity_name']:
                shops.add(row['shop_entity_name'])
    return shops

def connect_database2():
    """连接到数据库2"""
    db_config = load_config('config.ini', 'DATABASE2')
    return pymysql.connect(
        host=db_config['host'],
        port=int(db_config['port']),
        user=db_config['user'],
        password=db_config['password'],
        database=db_config['database'],
        charset=db_config['charset'],
        cursorclass=pymysql.cursors.DictCursor
    )

def get_shops_from_db2(conn):
    """获取数据库2中的所有店铺名称"""
    shops = set()
    with conn.cursor() as cursor:
        # cursor.execute("SELECT name FROM yx_b_tenants WHERE status=1 AND deleted=0")
        #不判断店铺启用或禁用状态
        cursor.execute("SELECT name FROM yx_b_tenants WHERE  deleted=0")
        for row in cursor.fetchall():
            if row['name']:
                shops.add(row['name'])
    return shops

def compare_shops(db1_shops, db2_shops):
    """比较两个数据库中的店铺名称，找出在db1中但不在db2中的店铺"""
    return db1_shops - db2_shops

def send_notification(missing_shops, max_date, log_file=None):
    """发送邮件通知"""
    # 构造邮件主题
    subject = f"店铺名称不匹配警告 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    # 构造邮件内容
    html_body = f"""
    <html>
        <body>
            <h2>店铺名称不匹配警告</h2>
            <p>在最新的销售数据中（日期：{max_date}），发现以下店铺名称在yx_b_tenants表中不存在：</p>
            <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
                <tr style="background-color: #f2f2f2;">
                    <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">序号</th>
                    <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">店铺名称</th>
                </tr>
    """
    
    for idx, shop in enumerate(sorted(missing_shops), 1):
        html_body += f"""
                <tr>
                    <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">{idx}</td>
                    <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">{shop}</td>
                </tr>
        """
    
    html_body += """
            </table>
            <p>请及时处理以上不匹配的店铺名称。</p>
        </body>
    </html>
    """
    
    # 生成店铺名称清单附件
    shop_list_file = f'logs/shop_list_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    with open(shop_list_file, 'w', encoding='utf-8') as f:
        f.write(f"不匹配店铺名称清单 (生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})\n")
        f.write(f"销售数据日期: {max_date}\n")
        f.write("-" * 50 + "\n\n")
        for idx, shop in enumerate(sorted(missing_shops), 1):
            f.write(f"{idx}. {shop}\n")
    
    # 发送邮件，附加店铺名称清单文件
    return send_email(subject, html_body, attachment_path=shop_list_file)

def main():
    """主函数"""
    # 设置日志
    log_file = setup_logging()
    
    try:
        logging.info("开始执行店铺名称检查...")
        
        # 连接数据库1
        db1_conn = connect_database1()
        logging.info("已连接到数据库1")
        
        # 获取最大销售日期
        max_date = get_max_sale_date(db1_conn)
        if not max_date:
            logging.error("未能获取到销售数据的最大日期")
            return
        
        logging.info(f"获取到最大销售日期: {max_date}")
        
        # 获取数据库1中的店铺名称
        db1_shops = get_shops_from_db1(db1_conn, max_date)
        logging.info(f"从数据库1获取到 {len(db1_shops)} 家店铺")
        
        # 关闭数据库1连接
        db1_conn.close()
        
        # 连接数据库2
        db2_conn = connect_database2()
        logging.info("已连接到数据库2")
        
        # 获取数据库2中的店铺名称
        db2_shops = get_shops_from_db2(db2_conn)
        logging.info(f"从数据库2获取到 {len(db2_shops)} 家店铺")
        
        # 关闭数据库2连接
        db2_conn.close()
        
        # 比较店铺名称
        missing_shops = compare_shops(db1_shops, db2_shops)
        
        if missing_shops:
            logging.warning(f"发现 {len(missing_shops)} 家店铺在数据库2中不存在: {', '.join(missing_shops)}")
            
            # 发送邮件通知
            if send_notification(missing_shops, max_date, log_file):
                logging.info("邮件通知已发送")
            else:
                logging.error("邮件通知发送失败")
        else:
            logging.info("所有店铺名称都匹配，无需发送通知")
        
        logging.info("店铺名称检查完成")
        
    except Exception as e:
        logging.exception(f"执行过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
