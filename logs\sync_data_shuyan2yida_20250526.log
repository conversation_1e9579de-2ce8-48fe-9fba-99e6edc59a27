2025-05-26 08:00:03,509 - INFO - ==================================================
2025-05-26 08:00:03,509 - INFO - 程序启动 - 版本 v1.0.0
2025-05-26 08:00:03,509 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250526.log
2025-05-26 08:00:03,509 - INFO - ==================================================
2025-05-26 08:00:03,509 - INFO - 程序入口点: __main__
2025-05-26 08:00:03,509 - INFO - ==================================================
2025-05-26 08:00:03,509 - INFO - 程序启动 - 版本 v1.0.1
2025-05-26 08:00:03,509 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250526.log
2025-05-26 08:00:03,509 - INFO - ==================================================
2025-05-26 08:00:03,821 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-26 08:00:03,837 - INFO - sales_data表已存在，无需创建
2025-05-26 08:00:03,837 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-26 08:00:03,837 - INFO - DataSyncManager初始化完成
2025-05-26 08:00:03,837 - INFO - 未提供日期参数，使用默认值
2025-05-26 08:00:03,837 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-26 08:00:03,837 - INFO - 开始综合数据同步流程...
2025-05-26 08:00:03,837 - INFO - 正在获取数衍平台日销售数据...
2025-05-26 08:00:03,837 - INFO - 查询数衍平台数据，时间段为: 2025-03-26, 2025-05-25
2025-05-26 08:00:03,837 - INFO - 正在获取********至********的数据
2025-05-26 08:00:03,837 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:03,837 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7F87702E39366C6201D2C31C566683A7'}
2025-05-26 08:00:08,509 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:08,524 - INFO - 过滤后保留 1555 条记录
2025-05-26 08:00:10,540 - INFO - 正在获取********至********的数据
2025-05-26 08:00:10,540 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:10,540 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '65C2A517D141C74FF16F5A005FF8D164'}
2025-05-26 08:00:13,930 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:13,946 - INFO - 过滤后保留 1503 条记录
2025-05-26 08:00:15,946 - INFO - 正在获取********至********的数据
2025-05-26 08:00:15,946 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:15,946 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8A1876C052206E14DA50455B4E25EEFE'}
2025-05-26 08:00:18,852 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:18,868 - INFO - 过滤后保留 1503 条记录
2025-05-26 08:00:20,868 - INFO - 正在获取********至********的数据
2025-05-26 08:00:20,868 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:20,868 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C6B8C9C95E722190123D80AD9382360E'}
2025-05-26 08:00:24,634 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:24,649 - INFO - 过滤后保留 1502 条记录
2025-05-26 08:00:26,649 - INFO - 正在获取********至********的数据
2025-05-26 08:00:26,649 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:26,649 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FB781353E2BB30B642DD46EBB624656A'}
2025-05-26 08:00:30,149 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:30,165 - INFO - 过滤后保留 1481 条记录
2025-05-26 08:00:32,180 - INFO - 正在获取********至********的数据
2025-05-26 08:00:32,180 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:32,180 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-05-26 08:00:34,852 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:34,868 - INFO - 过滤后保留 1477 条记录
2025-05-26 08:00:36,883 - INFO - 正在获取********至********的数据
2025-05-26 08:00:36,883 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:36,883 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '84B711EC591B42AD7DBCDE526FE38A48'}
2025-05-26 08:00:39,383 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:39,383 - INFO - 过滤后保留 1486 条记录
2025-05-26 08:00:41,399 - INFO - 正在获取********至********的数据
2025-05-26 08:00:41,399 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:41,399 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '558E2B52814849C9CA9E9E7AF9B8D835'}
2025-05-26 08:00:43,493 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:43,493 - INFO - 过滤后保留 1475 条记录
2025-05-26 08:00:45,524 - INFO - 正在获取********至********的数据
2025-05-26 08:00:45,524 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-26 08:00:45,524 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '08435731E2F07D688CF71CED60F17C33'}
2025-05-26 08:00:47,149 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-26 08:00:47,149 - INFO - 过滤后保留 1040 条记录
2025-05-26 08:00:49,180 - INFO - 开始保存数据到SQLite数据库，共 13022 条记录待处理
2025-05-26 08:00:49,993 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-24
2025-05-26 08:00:49,993 - INFO - 变更字段: recommend_amount: 0.0 -> 8206.5, daily_bill_amount: 0.0 -> 8206.5
2025-05-26 08:00:50,008 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=D384CB5088914FB296DE32297895B8D6, sale_time=2025-05-24
2025-05-26 08:00:50,008 - INFO - 变更字段: amount: 0 -> 604, count: 2 -> 3, instore_amount: 0.0 -> 604.0, instore_count: 2 -> 3
2025-05-26 08:00:50,008 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-05-24
2025-05-26 08:00:50,008 - INFO - 变更字段: amount: 6903 -> 6993, count: 73 -> 74, instore_amount: 3688.18 -> 3778.04, instore_count: 30 -> 31
2025-05-26 08:00:50,008 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-05-24
2025-05-26 08:00:50,008 - INFO - 变更字段: amount: 3910 -> 3939, count: 95 -> 96, online_amount: 280.7 -> 309.75, online_count: 11 -> 12
2025-05-26 08:00:50,008 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-05-24
2025-05-26 08:00:50,008 - INFO - 变更字段: amount: 4117 -> 4066, online_amount: 2076.84 -> 2025.84
2025-05-26 08:00:50,008 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-05-24
2025-05-26 08:00:50,008 - INFO - 变更字段: count: 73 -> 74, online_count: 43 -> 44
2025-05-26 08:00:50,008 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-05-24
2025-05-26 08:00:50,008 - INFO - 变更字段: amount: 768 -> 786, count: 29 -> 30, online_amount: 693.64 -> 711.64, online_count: 26 -> 27
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: recommend_amount: 0.0 -> 9102.7, daily_bill_amount: 0.0 -> 9102.7
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: recommend_amount: 2802.81 -> 2814.81, amount: 2802 -> 2814, count: 151 -> 153, online_amount: 2134.02 -> 2146.02, online_count: 123 -> 125
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: amount: 6767 -> 6811, count: 325 -> 329, online_amount: 4135.13 -> 4179.43, online_count: 183 -> 187
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: recommend_amount: 2053.4 -> 2057.19, amount: 2053 -> 2057, count: 148 -> 149, online_amount: 943.87 -> 947.66, online_count: 94 -> 95
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1IFRR231GUB4QN7QBECDAL3H28001J69, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: recommend_amount: 27228.89 -> 25183.15, daily_bill_amount: 27228.89 -> 25183.15
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: recommend_amount: 6642.66 -> 6653.26, amount: 6642 -> 6653, count: 307 -> 308, online_amount: 4677.45 -> 4688.05, online_count: 209 -> 210
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: recommend_amount: 0.0 -> 359.0, daily_bill_amount: 0.0 -> 359.0
2025-05-26 08:00:50,024 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-05-24
2025-05-26 08:00:50,024 - INFO - 变更字段: amount: 6906 -> 6914, count: 111 -> 112, instore_amount: 4451.34 -> 4459.4, instore_count: 33 -> 34
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: recommend_amount: 0.0 -> 28518.15, daily_bill_amount: 0.0 -> 28518.15
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 32121 -> 32544, count: 294 -> 296, instore_amount: 24225.7 -> 24648.6, instore_count: 162 -> 164
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 41681 -> 43138, count: 292 -> 298, instore_amount: 24834.8 -> 26291.8, instore_count: 129 -> 135
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 56039 -> 56918, count: 315 -> 318, instore_amount: 46609.7 -> 47488.23, instore_count: 188 -> 191
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 24632 -> 25034, count: 146 -> 147, instore_amount: 23380.9 -> 23782.9, instore_count: 136 -> 137
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 5098 -> 5113, count: 292 -> 293, online_amount: 2232.45 -> 2246.85, online_count: 132 -> 133
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 31490 -> 31500, count: 290 -> 291, instore_amount: 26813.17 -> 26823.05, instore_count: 135 -> 136
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: recommend_amount: 0.0 -> 4089.35, daily_bill_amount: 0.0 -> 4089.35, amount: 255 -> 2002, count: 28 -> 173, instore_amount: 286.5 -> 2084.05, instore_count: 28 -> 173
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: recommend_amount: 0.0 -> 55496.37, daily_bill_amount: 0.0 -> 55496.37, amount: 1217 -> 3112, count: 12 -> 37, instore_amount: 175.02 -> 2070.08, instore_count: 3 -> 28
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-23
2025-05-26 08:00:50,040 - INFO - 变更字段: recommend_amount: 0.0 -> 18858.54, daily_bill_amount: 0.0 -> 18858.54, amount: 451 -> 1209, count: 7 -> 12, instore_amount: 143.52 -> 901.82, instore_count: 2 -> 7
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-22
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 1199 -> 1229, count: 11 -> 12, instore_amount: 656.04 -> 685.94, instore_count: 6 -> 7
2025-05-26 08:00:50,040 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-24
2025-05-26 08:00:50,040 - INFO - 变更字段: amount: 10103 -> 13559, count: 15 -> 17, instore_amount: 10103.9 -> 13559.3, instore_count: 15 -> 17
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVBEGSM760I86N3H2U12H001EBN, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: recommend_amount: 1680.0 -> 2658.0, amount: 1680 -> 2658, count: 1 -> 3, instore_amount: 1680.0 -> 2658.0, instore_count: 1 -> 3
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 7109 -> 7113, count: 243 -> 244, online_amount: 3187.3 -> 3415.5, online_count: 62 -> 63
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 5460 -> 5625, count: 397 -> 420, online_amount: 5031.68 -> 5202.38, online_count: 366 -> 389
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 6965 -> 7773, count: 354 -> 355, instore_amount: 7115.43 -> 7923.88, instore_count: 352 -> 353
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 4021 -> 4015, count: 203 -> 204, online_amount: 3462.41 -> 3469.41, online_count: 157 -> 158
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 7481 -> 7500, count: 552 -> 554, instore_amount: 4963.46 -> 5000.08, instore_count: 352 -> 358, online_amount: 2673.62 -> 2656.0, online_count: 200 -> 196
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 25479 -> 25577, count: 511 -> 513, online_amount: 1150.7 -> 1248.6, online_count: 20 -> 22
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: amount: 25656 -> 25634
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: recommend_amount: 4352.72 -> 4345.63, amount: 4352 -> 4345
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-05-24
2025-05-26 08:00:50,055 - INFO - 变更字段: recommend_amount: 8687.91 -> 8658.61, daily_bill_amount: 8687.91 -> 8658.61
2025-05-26 08:00:50,055 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-24
2025-05-26 08:00:50,071 - INFO - 变更字段: amount: 26536 -> 28101, count: 210 -> 215, instore_amount: 25351.7 -> 26916.1, instore_count: 150 -> 155
2025-05-26 08:00:50,071 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-24
2025-05-26 08:00:50,071 - INFO - 变更字段: amount: 24940 -> 27403, count: 133 -> 138, instore_amount: 22475.01 -> 24937.46, instore_count: 109 -> 114
2025-05-26 08:00:50,071 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRBVE2CQT7AV8LHQQGID9001EJI, sale_time=2025-05-24
2025-05-26 08:00:50,071 - INFO - 变更字段: amount: 57757 -> 58752, count: 326 -> 330, instore_amount: 47718.2 -> 48713.7, instore_count: 276 -> 280
2025-05-26 08:00:50,071 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-24
2025-05-26 08:00:50,071 - INFO - 变更字段: amount: 148 -> 180, count: 6 -> 7, instore_amount: 148.2 -> 180.2, instore_count: 6 -> 7
2025-05-26 08:00:50,290 - INFO - SQLite数据保存完成，统计信息：
2025-05-26 08:00:50,290 - INFO - - 总记录数: 13022
2025-05-26 08:00:50,290 - INFO - - 成功插入: 206
2025-05-26 08:00:50,290 - INFO - - 成功更新: 41
2025-05-26 08:00:50,290 - INFO - - 无需更新: 12775
2025-05-26 08:00:50,290 - INFO - - 处理失败: 0
2025-05-26 08:00:55,665 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250526.xlsx
2025-05-26 08:00:55,680 - INFO - 成功获取数衍平台数据，共 13022 条记录
2025-05-26 08:00:55,680 - INFO - 正在更新SQLite月度汇总数据...
2025-05-26 08:00:55,680 - INFO - 月度数据sqllite清空完成
2025-05-26 08:00:55,946 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-26 08:00:55,946 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-26 08:00:55,946 - INFO - 正在获取宜搭日销售表单数据...
2025-05-26 08:00:55,946 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-26 00:00:00 至 2025-05-25 23:59:59
2025-05-26 08:00:55,946 - INFO - 查询分段 1: 2025-03-26 至 2025-04-01
2025-05-26 08:00:55,946 - INFO - 查询日期范围: 2025-03-26 至 2025-04-01，使用分页查询，每页 100 条记录
2025-05-26 08:00:55,946 - INFO - Request Parameters - Page 1:
2025-05-26 08:00:55,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:00:55,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:01,977 - INFO - API请求耗时: 6031ms
2025-05-26 08:01:01,977 - INFO - Response - Page 1
2025-05-26 08:01:01,977 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:01:02,493 - INFO - Request Parameters - Page 2:
2025-05-26 08:01:02,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:02,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:06,540 - INFO - API请求耗时: 4047ms
2025-05-26 08:01:06,540 - INFO - Response - Page 2
2025-05-26 08:01:06,540 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:01:07,040 - INFO - Request Parameters - Page 3:
2025-05-26 08:01:07,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:07,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:07,821 - INFO - API请求耗时: 781ms
2025-05-26 08:01:07,821 - INFO - Response - Page 3
2025-05-26 08:01:07,821 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:01:08,336 - INFO - Request Parameters - Page 4:
2025-05-26 08:01:08,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:08,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:09,024 - INFO - API请求耗时: 687ms
2025-05-26 08:01:09,024 - INFO - Response - Page 4
2025-05-26 08:01:09,024 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:01:09,540 - INFO - Request Parameters - Page 5:
2025-05-26 08:01:09,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:09,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:10,305 - INFO - API请求耗时: 766ms
2025-05-26 08:01:10,305 - INFO - Response - Page 5
2025-05-26 08:01:10,305 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:01:10,821 - INFO - Request Parameters - Page 6:
2025-05-26 08:01:10,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:10,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:11,524 - INFO - API请求耗时: 687ms
2025-05-26 08:01:11,524 - INFO - Response - Page 6
2025-05-26 08:01:11,524 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:01:12,039 - INFO - Request Parameters - Page 7:
2025-05-26 08:01:12,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:12,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:12,711 - INFO - API请求耗时: 672ms
2025-05-26 08:01:12,711 - INFO - Response - Page 7
2025-05-26 08:01:12,711 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:01:13,211 - INFO - Request Parameters - Page 8:
2025-05-26 08:01:13,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:13,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:13,868 - INFO - API请求耗时: 656ms
2025-05-26 08:01:13,868 - INFO - Response - Page 8
2025-05-26 08:01:13,868 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:01:14,368 - INFO - Request Parameters - Page 9:
2025-05-26 08:01:14,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:14,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:15,024 - INFO - API请求耗时: 656ms
2025-05-26 08:01:15,039 - INFO - Response - Page 9
2025-05-26 08:01:15,039 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:01:15,539 - INFO - Request Parameters - Page 10:
2025-05-26 08:01:15,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:15,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:16,274 - INFO - API请求耗时: 734ms
2025-05-26 08:01:16,274 - INFO - Response - Page 10
2025-05-26 08:01:16,274 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:01:16,789 - INFO - Request Parameters - Page 11:
2025-05-26 08:01:16,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:16,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:17,508 - INFO - API请求耗时: 719ms
2025-05-26 08:01:17,508 - INFO - Response - Page 11
2025-05-26 08:01:17,508 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:01:18,024 - INFO - Request Parameters - Page 12:
2025-05-26 08:01:18,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:18,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:18,758 - INFO - API请求耗时: 734ms
2025-05-26 08:01:18,758 - INFO - Response - Page 12
2025-05-26 08:01:18,758 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:01:19,258 - INFO - Request Parameters - Page 13:
2025-05-26 08:01:19,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:19,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:19,899 - INFO - API请求耗时: 641ms
2025-05-26 08:01:19,914 - INFO - Response - Page 13
2025-05-26 08:01:19,914 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:01:20,414 - INFO - Request Parameters - Page 14:
2025-05-26 08:01:20,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:20,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:21,133 - INFO - API请求耗时: 719ms
2025-05-26 08:01:21,133 - INFO - Response - Page 14
2025-05-26 08:01:21,133 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:01:21,649 - INFO - Request Parameters - Page 15:
2025-05-26 08:01:21,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:21,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:22,274 - INFO - API请求耗时: 625ms
2025-05-26 08:01:22,274 - INFO - Response - Page 15
2025-05-26 08:01:22,274 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:01:22,789 - INFO - Request Parameters - Page 16:
2025-05-26 08:01:22,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:22,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:23,477 - INFO - API请求耗时: 688ms
2025-05-26 08:01:23,477 - INFO - Response - Page 16
2025-05-26 08:01:23,477 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:01:23,993 - INFO - Request Parameters - Page 17:
2025-05-26 08:01:23,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:23,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:24,727 - INFO - API请求耗时: 734ms
2025-05-26 08:01:24,727 - INFO - Response - Page 17
2025-05-26 08:01:24,727 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:01:25,227 - INFO - Request Parameters - Page 18:
2025-05-26 08:01:25,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:25,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:25,946 - INFO - API请求耗时: 719ms
2025-05-26 08:01:25,946 - INFO - Response - Page 18
2025-05-26 08:01:25,946 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:01:26,461 - INFO - Request Parameters - Page 19:
2025-05-26 08:01:26,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:26,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:27,211 - INFO - API请求耗时: 750ms
2025-05-26 08:01:27,227 - INFO - Response - Page 19
2025-05-26 08:01:27,227 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:01:27,743 - INFO - Request Parameters - Page 20:
2025-05-26 08:01:27,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:27,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:28,461 - INFO - API请求耗时: 719ms
2025-05-26 08:01:28,461 - INFO - Response - Page 20
2025-05-26 08:01:28,461 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:01:28,961 - INFO - Request Parameters - Page 21:
2025-05-26 08:01:28,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:28,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:29,618 - INFO - API请求耗时: 656ms
2025-05-26 08:01:29,618 - INFO - Response - Page 21
2025-05-26 08:01:29,618 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:01:30,133 - INFO - Request Parameters - Page 22:
2025-05-26 08:01:30,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:30,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:30,946 - INFO - API请求耗时: 813ms
2025-05-26 08:01:30,946 - INFO - Response - Page 22
2025-05-26 08:01:30,946 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:01:31,461 - INFO - Request Parameters - Page 23:
2025-05-26 08:01:31,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:31,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:32,164 - INFO - API请求耗时: 703ms
2025-05-26 08:01:32,164 - INFO - Response - Page 23
2025-05-26 08:01:32,164 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:01:32,664 - INFO - Request Parameters - Page 24:
2025-05-26 08:01:32,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:32,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:33,321 - INFO - API请求耗时: 656ms
2025-05-26 08:01:33,321 - INFO - Response - Page 24
2025-05-26 08:01:33,321 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:01:33,821 - INFO - Request Parameters - Page 25:
2025-05-26 08:01:33,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:33,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:34,555 - INFO - API请求耗时: 734ms
2025-05-26 08:01:34,555 - INFO - Response - Page 25
2025-05-26 08:01:34,555 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:01:35,071 - INFO - Request Parameters - Page 26:
2025-05-26 08:01:35,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:35,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:35,789 - INFO - API请求耗时: 719ms
2025-05-26 08:01:35,789 - INFO - Response - Page 26
2025-05-26 08:01:35,789 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:01:36,305 - INFO - Request Parameters - Page 27:
2025-05-26 08:01:36,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:36,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:37,055 - INFO - API请求耗时: 750ms
2025-05-26 08:01:37,055 - INFO - Response - Page 27
2025-05-26 08:01:37,055 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:01:37,571 - INFO - Request Parameters - Page 28:
2025-05-26 08:01:37,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:37,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:38,227 - INFO - API请求耗时: 656ms
2025-05-26 08:01:38,227 - INFO - Response - Page 28
2025-05-26 08:01:38,227 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:01:38,727 - INFO - Request Parameters - Page 29:
2025-05-26 08:01:38,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:38,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:39,430 - INFO - API请求耗时: 703ms
2025-05-26 08:01:39,430 - INFO - Response - Page 29
2025-05-26 08:01:39,446 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:01:39,946 - INFO - Request Parameters - Page 30:
2025-05-26 08:01:39,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:39,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:40,649 - INFO - API请求耗时: 703ms
2025-05-26 08:01:40,649 - INFO - Response - Page 30
2025-05-26 08:01:40,649 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:01:41,149 - INFO - Request Parameters - Page 31:
2025-05-26 08:01:41,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:41,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:41,805 - INFO - API请求耗时: 656ms
2025-05-26 08:01:41,805 - INFO - Response - Page 31
2025-05-26 08:01:41,805 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:01:42,321 - INFO - Request Parameters - Page 32:
2025-05-26 08:01:42,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:42,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:43,024 - INFO - API请求耗时: 703ms
2025-05-26 08:01:43,024 - INFO - Response - Page 32
2025-05-26 08:01:43,024 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:01:43,539 - INFO - Request Parameters - Page 33:
2025-05-26 08:01:43,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:43,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:44,367 - INFO - API请求耗时: 828ms
2025-05-26 08:01:44,367 - INFO - Response - Page 33
2025-05-26 08:01:44,367 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:01:44,883 - INFO - Request Parameters - Page 34:
2025-05-26 08:01:44,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:44,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400946, 1743436800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:45,414 - INFO - API请求耗时: 531ms
2025-05-26 08:01:45,414 - INFO - Response - Page 34
2025-05-26 08:01:45,414 - INFO - 第 34 页获取到 23 条记录
2025-05-26 08:01:45,414 - INFO - 查询完成，共获取到 3323 条记录
2025-05-26 08:01:45,414 - INFO - 分段 1 查询成功，获取到 3323 条记录
2025-05-26 08:01:46,430 - INFO - 查询分段 2: 2025-04-02 至 2025-04-08
2025-05-26 08:01:46,430 - INFO - 查询日期范围: 2025-04-02 至 2025-04-08，使用分页查询，每页 100 条记录
2025-05-26 08:01:46,430 - INFO - Request Parameters - Page 1:
2025-05-26 08:01:46,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:46,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:47,071 - INFO - API请求耗时: 641ms
2025-05-26 08:01:47,071 - INFO - Response - Page 1
2025-05-26 08:01:47,071 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:01:47,586 - INFO - Request Parameters - Page 2:
2025-05-26 08:01:47,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:47,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:48,336 - INFO - API请求耗时: 750ms
2025-05-26 08:01:48,336 - INFO - Response - Page 2
2025-05-26 08:01:48,336 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:01:48,852 - INFO - Request Parameters - Page 3:
2025-05-26 08:01:48,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:48,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:49,461 - INFO - API请求耗时: 609ms
2025-05-26 08:01:49,461 - INFO - Response - Page 3
2025-05-26 08:01:49,461 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:01:49,992 - INFO - Request Parameters - Page 4:
2025-05-26 08:01:49,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:49,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:50,774 - INFO - API请求耗时: 781ms
2025-05-26 08:01:50,774 - INFO - Response - Page 4
2025-05-26 08:01:50,774 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:01:51,289 - INFO - Request Parameters - Page 5:
2025-05-26 08:01:51,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:51,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:52,055 - INFO - API请求耗时: 766ms
2025-05-26 08:01:52,055 - INFO - Response - Page 5
2025-05-26 08:01:52,055 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:01:52,570 - INFO - Request Parameters - Page 6:
2025-05-26 08:01:52,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:52,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:53,430 - INFO - API请求耗时: 859ms
2025-05-26 08:01:53,430 - INFO - Response - Page 6
2025-05-26 08:01:53,430 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:01:53,930 - INFO - Request Parameters - Page 7:
2025-05-26 08:01:53,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:53,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:54,664 - INFO - API请求耗时: 734ms
2025-05-26 08:01:54,664 - INFO - Response - Page 7
2025-05-26 08:01:54,664 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:01:55,180 - INFO - Request Parameters - Page 8:
2025-05-26 08:01:55,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:55,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:55,805 - INFO - API请求耗时: 625ms
2025-05-26 08:01:55,805 - INFO - Response - Page 8
2025-05-26 08:01:55,805 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:01:56,320 - INFO - Request Parameters - Page 9:
2025-05-26 08:01:56,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:56,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:57,102 - INFO - API请求耗时: 781ms
2025-05-26 08:01:57,102 - INFO - Response - Page 9
2025-05-26 08:01:57,102 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:01:57,602 - INFO - Request Parameters - Page 10:
2025-05-26 08:01:57,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:57,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:58,274 - INFO - API请求耗时: 672ms
2025-05-26 08:01:58,274 - INFO - Response - Page 10
2025-05-26 08:01:58,274 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:01:58,789 - INFO - Request Parameters - Page 11:
2025-05-26 08:01:58,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:58,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:01:59,445 - INFO - API请求耗时: 656ms
2025-05-26 08:01:59,445 - INFO - Response - Page 11
2025-05-26 08:01:59,445 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:01:59,961 - INFO - Request Parameters - Page 12:
2025-05-26 08:01:59,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:01:59,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:00,664 - INFO - API请求耗时: 703ms
2025-05-26 08:02:00,664 - INFO - Response - Page 12
2025-05-26 08:02:00,664 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:02:01,180 - INFO - Request Parameters - Page 13:
2025-05-26 08:02:01,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:01,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:01,977 - INFO - API请求耗时: 797ms
2025-05-26 08:02:01,977 - INFO - Response - Page 13
2025-05-26 08:02:01,977 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:02:02,492 - INFO - Request Parameters - Page 14:
2025-05-26 08:02:02,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:02,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:03,211 - INFO - API请求耗时: 719ms
2025-05-26 08:02:03,211 - INFO - Response - Page 14
2025-05-26 08:02:03,211 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:02:03,727 - INFO - Request Parameters - Page 15:
2025-05-26 08:02:03,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:03,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:04,383 - INFO - API请求耗时: 656ms
2025-05-26 08:02:04,383 - INFO - Response - Page 15
2025-05-26 08:02:04,383 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:02:04,883 - INFO - Request Parameters - Page 16:
2025-05-26 08:02:04,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:04,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:05,633 - INFO - API请求耗时: 750ms
2025-05-26 08:02:05,633 - INFO - Response - Page 16
2025-05-26 08:02:05,633 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:02:06,133 - INFO - Request Parameters - Page 17:
2025-05-26 08:02:06,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:06,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:06,820 - INFO - API请求耗时: 688ms
2025-05-26 08:02:06,820 - INFO - Response - Page 17
2025-05-26 08:02:06,820 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:02:07,336 - INFO - Request Parameters - Page 18:
2025-05-26 08:02:07,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:07,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:08,117 - INFO - API请求耗时: 781ms
2025-05-26 08:02:08,133 - INFO - Response - Page 18
2025-05-26 08:02:08,133 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:02:08,633 - INFO - Request Parameters - Page 19:
2025-05-26 08:02:08,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:08,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:09,336 - INFO - API请求耗时: 703ms
2025-05-26 08:02:09,336 - INFO - Response - Page 19
2025-05-26 08:02:09,352 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:02:09,867 - INFO - Request Parameters - Page 20:
2025-05-26 08:02:09,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:09,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:10,602 - INFO - API请求耗时: 734ms
2025-05-26 08:02:10,602 - INFO - Response - Page 20
2025-05-26 08:02:10,602 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:02:11,102 - INFO - Request Parameters - Page 21:
2025-05-26 08:02:11,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:11,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:11,867 - INFO - API请求耗时: 766ms
2025-05-26 08:02:11,867 - INFO - Response - Page 21
2025-05-26 08:02:11,867 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:02:12,383 - INFO - Request Parameters - Page 22:
2025-05-26 08:02:12,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:12,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:13,148 - INFO - API请求耗时: 766ms
2025-05-26 08:02:13,148 - INFO - Response - Page 22
2025-05-26 08:02:13,148 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:02:13,648 - INFO - Request Parameters - Page 23:
2025-05-26 08:02:13,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:13,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:14,258 - INFO - API请求耗时: 609ms
2025-05-26 08:02:14,258 - INFO - Response - Page 23
2025-05-26 08:02:14,258 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:02:14,758 - INFO - Request Parameters - Page 24:
2025-05-26 08:02:14,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:14,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:15,414 - INFO - API请求耗时: 656ms
2025-05-26 08:02:15,414 - INFO - Response - Page 24
2025-05-26 08:02:15,414 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:02:15,930 - INFO - Request Parameters - Page 25:
2025-05-26 08:02:15,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:15,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:16,617 - INFO - API请求耗时: 688ms
2025-05-26 08:02:16,617 - INFO - Response - Page 25
2025-05-26 08:02:16,617 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:02:17,117 - INFO - Request Parameters - Page 26:
2025-05-26 08:02:17,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:17,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:17,836 - INFO - API请求耗时: 719ms
2025-05-26 08:02:17,836 - INFO - Response - Page 26
2025-05-26 08:02:17,836 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:02:18,336 - INFO - Request Parameters - Page 27:
2025-05-26 08:02:18,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:18,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:19,070 - INFO - API请求耗时: 734ms
2025-05-26 08:02:19,070 - INFO - Response - Page 27
2025-05-26 08:02:19,086 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:02:19,586 - INFO - Request Parameters - Page 28:
2025-05-26 08:02:19,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:19,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:20,273 - INFO - API请求耗时: 687ms
2025-05-26 08:02:20,273 - INFO - Response - Page 28
2025-05-26 08:02:20,273 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:02:20,789 - INFO - Request Parameters - Page 29:
2025-05-26 08:02:20,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:20,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:21,539 - INFO - API请求耗时: 750ms
2025-05-26 08:02:21,539 - INFO - Response - Page 29
2025-05-26 08:02:21,539 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:02:22,055 - INFO - Request Parameters - Page 30:
2025-05-26 08:02:22,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:22,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:22,711 - INFO - API请求耗时: 656ms
2025-05-26 08:02:22,711 - INFO - Response - Page 30
2025-05-26 08:02:22,711 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:02:23,227 - INFO - Request Parameters - Page 31:
2025-05-26 08:02:23,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:23,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:23,914 - INFO - API请求耗时: 688ms
2025-05-26 08:02:23,914 - INFO - Response - Page 31
2025-05-26 08:02:23,914 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:02:24,430 - INFO - Request Parameters - Page 32:
2025-05-26 08:02:24,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:24,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:25,086 - INFO - API请求耗时: 656ms
2025-05-26 08:02:25,086 - INFO - Response - Page 32
2025-05-26 08:02:25,086 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:02:25,602 - INFO - Request Parameters - Page 33:
2025-05-26 08:02:25,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:25,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:26,336 - INFO - API请求耗时: 734ms
2025-05-26 08:02:26,336 - INFO - Response - Page 33
2025-05-26 08:02:26,336 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:02:26,851 - INFO - Request Parameters - Page 34:
2025-05-26 08:02:26,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:26,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:27,539 - INFO - API请求耗时: 687ms
2025-05-26 08:02:27,539 - INFO - Response - Page 34
2025-05-26 08:02:27,539 - INFO - 第 34 页获取到 100 条记录
2025-05-26 08:02:28,055 - INFO - Request Parameters - Page 35:
2025-05-26 08:02:28,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:28,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200946, 1744041600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:28,555 - INFO - API请求耗时: 500ms
2025-05-26 08:02:28,555 - INFO - Response - Page 35
2025-05-26 08:02:28,555 - INFO - 第 35 页获取到 34 条记录
2025-05-26 08:02:28,555 - INFO - 查询完成，共获取到 3434 条记录
2025-05-26 08:02:28,555 - INFO - 分段 2 查询成功，获取到 3434 条记录
2025-05-26 08:02:29,570 - INFO - 查询分段 3: 2025-04-09 至 2025-04-15
2025-05-26 08:02:29,570 - INFO - 查询日期范围: 2025-04-09 至 2025-04-15，使用分页查询，每页 100 条记录
2025-05-26 08:02:29,570 - INFO - Request Parameters - Page 1:
2025-05-26 08:02:29,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:29,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:30,367 - INFO - API请求耗时: 797ms
2025-05-26 08:02:30,367 - INFO - Response - Page 1
2025-05-26 08:02:30,367 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:02:30,883 - INFO - Request Parameters - Page 2:
2025-05-26 08:02:30,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:30,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:31,570 - INFO - API请求耗时: 688ms
2025-05-26 08:02:31,570 - INFO - Response - Page 2
2025-05-26 08:02:31,570 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:02:32,070 - INFO - Request Parameters - Page 3:
2025-05-26 08:02:32,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:32,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:32,680 - INFO - API请求耗时: 609ms
2025-05-26 08:02:32,680 - INFO - Response - Page 3
2025-05-26 08:02:32,680 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:02:33,195 - INFO - Request Parameters - Page 4:
2025-05-26 08:02:33,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:33,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:33,867 - INFO - API请求耗时: 672ms
2025-05-26 08:02:33,867 - INFO - Response - Page 4
2025-05-26 08:02:33,867 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:02:34,383 - INFO - Request Parameters - Page 5:
2025-05-26 08:02:34,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:34,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:35,070 - INFO - API请求耗时: 687ms
2025-05-26 08:02:35,070 - INFO - Response - Page 5
2025-05-26 08:02:35,070 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:02:35,586 - INFO - Request Parameters - Page 6:
2025-05-26 08:02:35,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:35,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:36,305 - INFO - API请求耗时: 719ms
2025-05-26 08:02:36,305 - INFO - Response - Page 6
2025-05-26 08:02:36,305 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:02:36,805 - INFO - Request Parameters - Page 7:
2025-05-26 08:02:36,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:36,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:37,414 - INFO - API请求耗时: 609ms
2025-05-26 08:02:37,414 - INFO - Response - Page 7
2025-05-26 08:02:37,414 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:02:37,930 - INFO - Request Parameters - Page 8:
2025-05-26 08:02:37,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:37,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:38,711 - INFO - API请求耗时: 781ms
2025-05-26 08:02:38,711 - INFO - Response - Page 8
2025-05-26 08:02:38,711 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:02:39,211 - INFO - Request Parameters - Page 9:
2025-05-26 08:02:39,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:39,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:39,914 - INFO - API请求耗时: 703ms
2025-05-26 08:02:39,914 - INFO - Response - Page 9
2025-05-26 08:02:39,914 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:02:40,430 - INFO - Request Parameters - Page 10:
2025-05-26 08:02:40,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:40,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:41,055 - INFO - API请求耗时: 625ms
2025-05-26 08:02:41,055 - INFO - Response - Page 10
2025-05-26 08:02:41,055 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:02:41,555 - INFO - Request Parameters - Page 11:
2025-05-26 08:02:41,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:41,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:42,367 - INFO - API请求耗时: 812ms
2025-05-26 08:02:42,367 - INFO - Response - Page 11
2025-05-26 08:02:42,367 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:02:42,867 - INFO - Request Parameters - Page 12:
2025-05-26 08:02:42,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:42,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:43,570 - INFO - API请求耗时: 703ms
2025-05-26 08:02:43,570 - INFO - Response - Page 12
2025-05-26 08:02:43,570 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:02:44,086 - INFO - Request Parameters - Page 13:
2025-05-26 08:02:44,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:44,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:44,742 - INFO - API请求耗时: 656ms
2025-05-26 08:02:44,742 - INFO - Response - Page 13
2025-05-26 08:02:44,742 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:02:45,258 - INFO - Request Parameters - Page 14:
2025-05-26 08:02:45,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:45,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:46,023 - INFO - API请求耗时: 766ms
2025-05-26 08:02:46,023 - INFO - Response - Page 14
2025-05-26 08:02:46,023 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:02:46,523 - INFO - Request Parameters - Page 15:
2025-05-26 08:02:46,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:46,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:47,179 - INFO - API请求耗时: 656ms
2025-05-26 08:02:47,179 - INFO - Response - Page 15
2025-05-26 08:02:47,179 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:02:47,695 - INFO - Request Parameters - Page 16:
2025-05-26 08:02:47,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:47,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:48,398 - INFO - API请求耗时: 703ms
2025-05-26 08:02:48,398 - INFO - Response - Page 16
2025-05-26 08:02:48,398 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:02:48,914 - INFO - Request Parameters - Page 17:
2025-05-26 08:02:48,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:48,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:49,554 - INFO - API请求耗时: 641ms
2025-05-26 08:02:49,554 - INFO - Response - Page 17
2025-05-26 08:02:49,554 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:02:50,070 - INFO - Request Parameters - Page 18:
2025-05-26 08:02:50,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:50,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:50,883 - INFO - API请求耗时: 813ms
2025-05-26 08:02:50,883 - INFO - Response - Page 18
2025-05-26 08:02:50,883 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:02:51,398 - INFO - Request Parameters - Page 19:
2025-05-26 08:02:51,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:51,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:52,133 - INFO - API请求耗时: 734ms
2025-05-26 08:02:52,133 - INFO - Response - Page 19
2025-05-26 08:02:52,133 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:02:52,648 - INFO - Request Parameters - Page 20:
2025-05-26 08:02:52,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:52,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:53,383 - INFO - API请求耗时: 734ms
2025-05-26 08:02:53,383 - INFO - Response - Page 20
2025-05-26 08:02:53,383 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:02:53,898 - INFO - Request Parameters - Page 21:
2025-05-26 08:02:53,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:53,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:54,633 - INFO - API请求耗时: 734ms
2025-05-26 08:02:54,633 - INFO - Response - Page 21
2025-05-26 08:02:54,633 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:02:55,148 - INFO - Request Parameters - Page 22:
2025-05-26 08:02:55,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:55,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:55,851 - INFO - API请求耗时: 703ms
2025-05-26 08:02:55,851 - INFO - Response - Page 22
2025-05-26 08:02:55,851 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:02:56,367 - INFO - Request Parameters - Page 23:
2025-05-26 08:02:56,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:56,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:57,117 - INFO - API请求耗时: 750ms
2025-05-26 08:02:57,117 - INFO - Response - Page 23
2025-05-26 08:02:57,117 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:02:57,617 - INFO - Request Parameters - Page 24:
2025-05-26 08:02:57,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:57,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:58,461 - INFO - API请求耗时: 844ms
2025-05-26 08:02:58,461 - INFO - Response - Page 24
2025-05-26 08:02:58,461 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:02:58,976 - INFO - Request Parameters - Page 25:
2025-05-26 08:02:58,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:02:58,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:02:59,648 - INFO - API请求耗时: 672ms
2025-05-26 08:02:59,648 - INFO - Response - Page 25
2025-05-26 08:02:59,648 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:03:00,164 - INFO - Request Parameters - Page 26:
2025-05-26 08:03:00,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:00,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:00,851 - INFO - API请求耗时: 688ms
2025-05-26 08:03:00,851 - INFO - Response - Page 26
2025-05-26 08:03:00,851 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:03:01,367 - INFO - Request Parameters - Page 27:
2025-05-26 08:03:01,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:01,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:02,086 - INFO - API请求耗时: 719ms
2025-05-26 08:03:02,086 - INFO - Response - Page 27
2025-05-26 08:03:02,086 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:03:02,601 - INFO - Request Parameters - Page 28:
2025-05-26 08:03:02,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:02,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:03,351 - INFO - API请求耗时: 750ms
2025-05-26 08:03:03,351 - INFO - Response - Page 28
2025-05-26 08:03:03,351 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:03:03,867 - INFO - Request Parameters - Page 29:
2025-05-26 08:03:03,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:03,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:04,633 - INFO - API请求耗时: 766ms
2025-05-26 08:03:04,633 - INFO - Response - Page 29
2025-05-26 08:03:04,633 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:03:05,148 - INFO - Request Parameters - Page 30:
2025-05-26 08:03:05,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:05,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:05,992 - INFO - API请求耗时: 844ms
2025-05-26 08:03:05,992 - INFO - Response - Page 30
2025-05-26 08:03:06,007 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:03:06,507 - INFO - Request Parameters - Page 31:
2025-05-26 08:03:06,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:06,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:07,211 - INFO - API请求耗时: 703ms
2025-05-26 08:03:07,211 - INFO - Response - Page 31
2025-05-26 08:03:07,211 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:03:07,726 - INFO - Request Parameters - Page 32:
2025-05-26 08:03:07,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:07,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:08,367 - INFO - API请求耗时: 641ms
2025-05-26 08:03:08,367 - INFO - Response - Page 32
2025-05-26 08:03:08,367 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:03:08,882 - INFO - Request Parameters - Page 33:
2025-05-26 08:03:08,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:08,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:09,617 - INFO - API请求耗时: 734ms
2025-05-26 08:03:09,617 - INFO - Response - Page 33
2025-05-26 08:03:09,617 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:03:10,117 - INFO - Request Parameters - Page 34:
2025-05-26 08:03:10,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:10,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:10,867 - INFO - API请求耗时: 750ms
2025-05-26 08:03:10,867 - INFO - Response - Page 34
2025-05-26 08:03:10,867 - INFO - 第 34 页获取到 100 条记录
2025-05-26 08:03:11,367 - INFO - Request Parameters - Page 35:
2025-05-26 08:03:11,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:11,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000946, 1744646400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:11,992 - INFO - API请求耗时: 625ms
2025-05-26 08:03:11,992 - INFO - Response - Page 35
2025-05-26 08:03:11,992 - INFO - 第 35 页获取到 45 条记录
2025-05-26 08:03:11,992 - INFO - 查询完成，共获取到 3445 条记录
2025-05-26 08:03:11,992 - INFO - 分段 3 查询成功，获取到 3445 条记录
2025-05-26 08:03:13,007 - INFO - 查询分段 4: 2025-04-16 至 2025-04-22
2025-05-26 08:03:13,007 - INFO - 查询日期范围: 2025-04-16 至 2025-04-22，使用分页查询，每页 100 条记录
2025-05-26 08:03:13,007 - INFO - Request Parameters - Page 1:
2025-05-26 08:03:13,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:13,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:13,695 - INFO - API请求耗时: 688ms
2025-05-26 08:03:13,695 - INFO - Response - Page 1
2025-05-26 08:03:13,695 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:03:14,211 - INFO - Request Parameters - Page 2:
2025-05-26 08:03:14,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:14,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:14,898 - INFO - API请求耗时: 687ms
2025-05-26 08:03:14,898 - INFO - Response - Page 2
2025-05-26 08:03:14,898 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:03:15,398 - INFO - Request Parameters - Page 3:
2025-05-26 08:03:15,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:15,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:16,179 - INFO - API请求耗时: 781ms
2025-05-26 08:03:16,179 - INFO - Response - Page 3
2025-05-26 08:03:16,179 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:03:16,695 - INFO - Request Parameters - Page 4:
2025-05-26 08:03:16,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:16,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:17,429 - INFO - API请求耗时: 734ms
2025-05-26 08:03:17,429 - INFO - Response - Page 4
2025-05-26 08:03:17,429 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:03:17,945 - INFO - Request Parameters - Page 5:
2025-05-26 08:03:17,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:17,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:18,601 - INFO - API请求耗时: 656ms
2025-05-26 08:03:18,601 - INFO - Response - Page 5
2025-05-26 08:03:18,601 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:03:19,117 - INFO - Request Parameters - Page 6:
2025-05-26 08:03:19,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:19,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:19,757 - INFO - API请求耗时: 641ms
2025-05-26 08:03:19,757 - INFO - Response - Page 6
2025-05-26 08:03:19,757 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:03:20,257 - INFO - Request Parameters - Page 7:
2025-05-26 08:03:20,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:20,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:20,992 - INFO - API请求耗时: 734ms
2025-05-26 08:03:20,992 - INFO - Response - Page 7
2025-05-26 08:03:20,992 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:03:21,507 - INFO - Request Parameters - Page 8:
2025-05-26 08:03:21,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:21,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:22,242 - INFO - API请求耗时: 734ms
2025-05-26 08:03:22,242 - INFO - Response - Page 8
2025-05-26 08:03:22,242 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:03:22,757 - INFO - Request Parameters - Page 9:
2025-05-26 08:03:22,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:22,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:23,461 - INFO - API请求耗时: 703ms
2025-05-26 08:03:23,461 - INFO - Response - Page 9
2025-05-26 08:03:23,461 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:03:23,976 - INFO - Request Parameters - Page 10:
2025-05-26 08:03:23,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:23,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:24,648 - INFO - API请求耗时: 672ms
2025-05-26 08:03:24,648 - INFO - Response - Page 10
2025-05-26 08:03:24,648 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:03:25,148 - INFO - Request Parameters - Page 11:
2025-05-26 08:03:25,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:25,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:25,757 - INFO - API请求耗时: 609ms
2025-05-26 08:03:25,757 - INFO - Response - Page 11
2025-05-26 08:03:25,757 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:03:26,273 - INFO - Request Parameters - Page 12:
2025-05-26 08:03:26,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:26,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:26,914 - INFO - API请求耗时: 641ms
2025-05-26 08:03:26,914 - INFO - Response - Page 12
2025-05-26 08:03:26,914 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:03:27,429 - INFO - Request Parameters - Page 13:
2025-05-26 08:03:27,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:27,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:28,085 - INFO - API请求耗时: 656ms
2025-05-26 08:03:28,085 - INFO - Response - Page 13
2025-05-26 08:03:28,085 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:03:28,585 - INFO - Request Parameters - Page 14:
2025-05-26 08:03:28,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:28,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:29,273 - INFO - API请求耗时: 687ms
2025-05-26 08:03:29,273 - INFO - Response - Page 14
2025-05-26 08:03:29,273 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:03:29,773 - INFO - Request Parameters - Page 15:
2025-05-26 08:03:29,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:29,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:30,492 - INFO - API请求耗时: 719ms
2025-05-26 08:03:30,507 - INFO - Response - Page 15
2025-05-26 08:03:30,507 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:03:31,007 - INFO - Request Parameters - Page 16:
2025-05-26 08:03:31,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:31,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:31,820 - INFO - API请求耗时: 812ms
2025-05-26 08:03:31,820 - INFO - Response - Page 16
2025-05-26 08:03:31,820 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:03:32,335 - INFO - Request Parameters - Page 17:
2025-05-26 08:03:32,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:32,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:33,070 - INFO - API请求耗时: 734ms
2025-05-26 08:03:33,070 - INFO - Response - Page 17
2025-05-26 08:03:33,085 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:03:33,585 - INFO - Request Parameters - Page 18:
2025-05-26 08:03:33,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:33,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:34,226 - INFO - API请求耗时: 641ms
2025-05-26 08:03:34,226 - INFO - Response - Page 18
2025-05-26 08:03:34,226 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:03:34,726 - INFO - Request Parameters - Page 19:
2025-05-26 08:03:34,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:34,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:35,507 - INFO - API请求耗时: 781ms
2025-05-26 08:03:35,507 - INFO - Response - Page 19
2025-05-26 08:03:35,507 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:03:36,023 - INFO - Request Parameters - Page 20:
2025-05-26 08:03:36,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:36,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:36,679 - INFO - API请求耗时: 656ms
2025-05-26 08:03:36,695 - INFO - Response - Page 20
2025-05-26 08:03:36,695 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:03:37,210 - INFO - Request Parameters - Page 21:
2025-05-26 08:03:37,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:37,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:37,867 - INFO - API请求耗时: 656ms
2025-05-26 08:03:37,867 - INFO - Response - Page 21
2025-05-26 08:03:37,867 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:03:38,382 - INFO - Request Parameters - Page 22:
2025-05-26 08:03:38,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:38,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:39,039 - INFO - API请求耗时: 656ms
2025-05-26 08:03:39,039 - INFO - Response - Page 22
2025-05-26 08:03:39,039 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:03:39,539 - INFO - Request Parameters - Page 23:
2025-05-26 08:03:39,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:39,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:40,414 - INFO - API请求耗时: 875ms
2025-05-26 08:03:40,414 - INFO - Response - Page 23
2025-05-26 08:03:40,414 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:03:40,929 - INFO - Request Parameters - Page 24:
2025-05-26 08:03:40,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:40,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:41,710 - INFO - API请求耗时: 781ms
2025-05-26 08:03:41,710 - INFO - Response - Page 24
2025-05-26 08:03:41,710 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:03:42,226 - INFO - Request Parameters - Page 25:
2025-05-26 08:03:42,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:42,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:42,898 - INFO - API请求耗时: 672ms
2025-05-26 08:03:42,898 - INFO - Response - Page 25
2025-05-26 08:03:42,898 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:03:43,413 - INFO - Request Parameters - Page 26:
2025-05-26 08:03:43,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:43,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:44,085 - INFO - API请求耗时: 672ms
2025-05-26 08:03:44,085 - INFO - Response - Page 26
2025-05-26 08:03:44,085 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:03:44,585 - INFO - Request Parameters - Page 27:
2025-05-26 08:03:44,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:44,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:45,242 - INFO - API请求耗时: 656ms
2025-05-26 08:03:45,242 - INFO - Response - Page 27
2025-05-26 08:03:45,242 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:03:45,757 - INFO - Request Parameters - Page 28:
2025-05-26 08:03:45,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:45,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:46,445 - INFO - API请求耗时: 688ms
2025-05-26 08:03:46,445 - INFO - Response - Page 28
2025-05-26 08:03:46,445 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:03:46,960 - INFO - Request Parameters - Page 29:
2025-05-26 08:03:46,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:46,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:47,648 - INFO - API请求耗时: 688ms
2025-05-26 08:03:47,648 - INFO - Response - Page 29
2025-05-26 08:03:47,648 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:03:48,163 - INFO - Request Parameters - Page 30:
2025-05-26 08:03:48,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:48,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:48,788 - INFO - API请求耗时: 625ms
2025-05-26 08:03:48,788 - INFO - Response - Page 30
2025-05-26 08:03:48,788 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:03:49,288 - INFO - Request Parameters - Page 31:
2025-05-26 08:03:49,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:49,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:49,960 - INFO - API请求耗时: 672ms
2025-05-26 08:03:49,960 - INFO - Response - Page 31
2025-05-26 08:03:49,960 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:03:50,460 - INFO - Request Parameters - Page 32:
2025-05-26 08:03:50,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:50,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:51,132 - INFO - API请求耗时: 672ms
2025-05-26 08:03:51,132 - INFO - Response - Page 32
2025-05-26 08:03:51,132 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:03:51,648 - INFO - Request Parameters - Page 33:
2025-05-26 08:03:51,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:51,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:52,382 - INFO - API请求耗时: 734ms
2025-05-26 08:03:52,382 - INFO - Response - Page 33
2025-05-26 08:03:52,382 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:03:52,898 - INFO - Request Parameters - Page 34:
2025-05-26 08:03:52,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:52,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:53,601 - INFO - API请求耗时: 703ms
2025-05-26 08:03:53,601 - INFO - Response - Page 34
2025-05-26 08:03:53,601 - INFO - 第 34 页获取到 100 条记录
2025-05-26 08:03:54,101 - INFO - Request Parameters - Page 35:
2025-05-26 08:03:54,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:54,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800946, 1745251200946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:54,632 - INFO - API请求耗时: 531ms
2025-05-26 08:03:54,632 - INFO - Response - Page 35
2025-05-26 08:03:54,632 - INFO - 第 35 页获取到 32 条记录
2025-05-26 08:03:54,632 - INFO - 查询完成，共获取到 3432 条记录
2025-05-26 08:03:54,632 - INFO - 分段 4 查询成功，获取到 3432 条记录
2025-05-26 08:03:55,632 - INFO - 查询分段 5: 2025-04-23 至 2025-04-29
2025-05-26 08:03:55,632 - INFO - 查询日期范围: 2025-04-23 至 2025-04-29，使用分页查询，每页 100 条记录
2025-05-26 08:03:55,632 - INFO - Request Parameters - Page 1:
2025-05-26 08:03:55,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:55,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:56,367 - INFO - API请求耗时: 734ms
2025-05-26 08:03:56,367 - INFO - Response - Page 1
2025-05-26 08:03:56,367 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:03:56,882 - INFO - Request Parameters - Page 2:
2025-05-26 08:03:56,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:56,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:57,804 - INFO - API请求耗时: 922ms
2025-05-26 08:03:57,804 - INFO - Response - Page 2
2025-05-26 08:03:57,804 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:03:58,320 - INFO - Request Parameters - Page 3:
2025-05-26 08:03:58,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:58,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:03:59,070 - INFO - API请求耗时: 750ms
2025-05-26 08:03:59,070 - INFO - Response - Page 3
2025-05-26 08:03:59,070 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:03:59,570 - INFO - Request Parameters - Page 4:
2025-05-26 08:03:59,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:03:59,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:00,335 - INFO - API请求耗时: 766ms
2025-05-26 08:04:00,335 - INFO - Response - Page 4
2025-05-26 08:04:00,335 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:04:00,851 - INFO - Request Parameters - Page 5:
2025-05-26 08:04:00,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:00,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:01,523 - INFO - API请求耗时: 672ms
2025-05-26 08:04:01,523 - INFO - Response - Page 5
2025-05-26 08:04:01,523 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:04:02,038 - INFO - Request Parameters - Page 6:
2025-05-26 08:04:02,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:02,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:02,757 - INFO - API请求耗时: 719ms
2025-05-26 08:04:02,757 - INFO - Response - Page 6
2025-05-26 08:04:02,757 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:04:03,273 - INFO - Request Parameters - Page 7:
2025-05-26 08:04:03,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:03,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:03,913 - INFO - API请求耗时: 641ms
2025-05-26 08:04:03,913 - INFO - Response - Page 7
2025-05-26 08:04:03,913 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:04:04,413 - INFO - Request Parameters - Page 8:
2025-05-26 08:04:04,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:04,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:05,116 - INFO - API请求耗时: 703ms
2025-05-26 08:04:05,116 - INFO - Response - Page 8
2025-05-26 08:04:05,116 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:04:05,632 - INFO - Request Parameters - Page 9:
2025-05-26 08:04:05,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:05,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:06,413 - INFO - API请求耗时: 781ms
2025-05-26 08:04:06,413 - INFO - Response - Page 9
2025-05-26 08:04:06,413 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:04:06,913 - INFO - Request Parameters - Page 10:
2025-05-26 08:04:06,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:06,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:07,570 - INFO - API请求耗时: 656ms
2025-05-26 08:04:07,570 - INFO - Response - Page 10
2025-05-26 08:04:07,570 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:04:08,070 - INFO - Request Parameters - Page 11:
2025-05-26 08:04:08,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:08,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:08,757 - INFO - API请求耗时: 687ms
2025-05-26 08:04:08,757 - INFO - Response - Page 11
2025-05-26 08:04:08,757 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:04:09,273 - INFO - Request Parameters - Page 12:
2025-05-26 08:04:09,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:09,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:10,085 - INFO - API请求耗时: 812ms
2025-05-26 08:04:10,085 - INFO - Response - Page 12
2025-05-26 08:04:10,085 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:04:10,601 - INFO - Request Parameters - Page 13:
2025-05-26 08:04:10,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:10,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:11,320 - INFO - API请求耗时: 719ms
2025-05-26 08:04:11,320 - INFO - Response - Page 13
2025-05-26 08:04:11,320 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:04:11,835 - INFO - Request Parameters - Page 14:
2025-05-26 08:04:11,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:11,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:12,601 - INFO - API请求耗时: 766ms
2025-05-26 08:04:12,601 - INFO - Response - Page 14
2025-05-26 08:04:12,601 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:04:13,116 - INFO - Request Parameters - Page 15:
2025-05-26 08:04:13,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:13,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:13,866 - INFO - API请求耗时: 750ms
2025-05-26 08:04:13,866 - INFO - Response - Page 15
2025-05-26 08:04:13,866 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:04:14,366 - INFO - Request Parameters - Page 16:
2025-05-26 08:04:14,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:14,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:15,085 - INFO - API请求耗时: 719ms
2025-05-26 08:04:15,085 - INFO - Response - Page 16
2025-05-26 08:04:15,085 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:04:15,601 - INFO - Request Parameters - Page 17:
2025-05-26 08:04:15,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:15,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:16,351 - INFO - API请求耗时: 750ms
2025-05-26 08:04:16,351 - INFO - Response - Page 17
2025-05-26 08:04:16,351 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:04:16,851 - INFO - Request Parameters - Page 18:
2025-05-26 08:04:16,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:16,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:17,570 - INFO - API请求耗时: 719ms
2025-05-26 08:04:17,570 - INFO - Response - Page 18
2025-05-26 08:04:17,570 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:04:18,085 - INFO - Request Parameters - Page 19:
2025-05-26 08:04:18,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:18,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:18,851 - INFO - API请求耗时: 766ms
2025-05-26 08:04:18,851 - INFO - Response - Page 19
2025-05-26 08:04:18,851 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:04:19,366 - INFO - Request Parameters - Page 20:
2025-05-26 08:04:19,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:19,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:20,210 - INFO - API请求耗时: 844ms
2025-05-26 08:04:20,210 - INFO - Response - Page 20
2025-05-26 08:04:20,210 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:04:20,726 - INFO - Request Parameters - Page 21:
2025-05-26 08:04:20,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:20,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:21,398 - INFO - API请求耗时: 672ms
2025-05-26 08:04:21,398 - INFO - Response - Page 21
2025-05-26 08:04:21,398 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:04:21,913 - INFO - Request Parameters - Page 22:
2025-05-26 08:04:21,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:21,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:22,757 - INFO - API请求耗时: 844ms
2025-05-26 08:04:22,757 - INFO - Response - Page 22
2025-05-26 08:04:22,757 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:04:23,273 - INFO - Request Parameters - Page 23:
2025-05-26 08:04:23,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:23,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:23,976 - INFO - API请求耗时: 703ms
2025-05-26 08:04:23,976 - INFO - Response - Page 23
2025-05-26 08:04:23,976 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:04:24,491 - INFO - Request Parameters - Page 24:
2025-05-26 08:04:24,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:24,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:25,132 - INFO - API请求耗时: 641ms
2025-05-26 08:04:25,132 - INFO - Response - Page 24
2025-05-26 08:04:25,132 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:04:25,648 - INFO - Request Parameters - Page 25:
2025-05-26 08:04:25,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:25,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:26,257 - INFO - API请求耗时: 609ms
2025-05-26 08:04:26,273 - INFO - Response - Page 25
2025-05-26 08:04:26,273 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:04:26,788 - INFO - Request Parameters - Page 26:
2025-05-26 08:04:26,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:26,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:27,491 - INFO - API请求耗时: 703ms
2025-05-26 08:04:27,491 - INFO - Response - Page 26
2025-05-26 08:04:27,491 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:04:28,007 - INFO - Request Parameters - Page 27:
2025-05-26 08:04:28,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:28,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:28,726 - INFO - API请求耗时: 719ms
2025-05-26 08:04:28,726 - INFO - Response - Page 27
2025-05-26 08:04:28,726 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:04:29,226 - INFO - Request Parameters - Page 28:
2025-05-26 08:04:29,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:29,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:29,913 - INFO - API请求耗时: 687ms
2025-05-26 08:04:29,913 - INFO - Response - Page 28
2025-05-26 08:04:29,913 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:04:30,413 - INFO - Request Parameters - Page 29:
2025-05-26 08:04:30,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:30,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:31,085 - INFO - API请求耗时: 672ms
2025-05-26 08:04:31,085 - INFO - Response - Page 29
2025-05-26 08:04:31,085 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:04:31,601 - INFO - Request Parameters - Page 30:
2025-05-26 08:04:31,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:31,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:32,241 - INFO - API请求耗时: 641ms
2025-05-26 08:04:32,241 - INFO - Response - Page 30
2025-05-26 08:04:32,257 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:04:32,757 - INFO - Request Parameters - Page 31:
2025-05-26 08:04:32,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:32,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:33,429 - INFO - API请求耗时: 672ms
2025-05-26 08:04:33,429 - INFO - Response - Page 31
2025-05-26 08:04:33,429 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:04:33,944 - INFO - Request Parameters - Page 32:
2025-05-26 08:04:33,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:33,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:34,632 - INFO - API请求耗时: 687ms
2025-05-26 08:04:34,632 - INFO - Response - Page 32
2025-05-26 08:04:34,632 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:04:35,132 - INFO - Request Parameters - Page 33:
2025-05-26 08:04:35,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:35,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:35,866 - INFO - API请求耗时: 734ms
2025-05-26 08:04:35,882 - INFO - Response - Page 33
2025-05-26 08:04:35,882 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:04:36,398 - INFO - Request Parameters - Page 34:
2025-05-26 08:04:36,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:36,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600946, 1745856000946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:37,116 - INFO - API请求耗时: 719ms
2025-05-26 08:04:37,116 - INFO - Response - Page 34
2025-05-26 08:04:37,116 - INFO - 第 34 页获取到 92 条记录
2025-05-26 08:04:37,116 - INFO - 查询完成，共获取到 3392 条记录
2025-05-26 08:04:37,116 - INFO - 分段 5 查询成功，获取到 3392 条记录
2025-05-26 08:04:38,116 - INFO - 查询分段 6: 2025-04-30 至 2025-05-06
2025-05-26 08:04:38,116 - INFO - 查询日期范围: 2025-04-30 至 2025-05-06，使用分页查询，每页 100 条记录
2025-05-26 08:04:38,116 - INFO - Request Parameters - Page 1:
2025-05-26 08:04:38,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:38,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:38,804 - INFO - API请求耗时: 687ms
2025-05-26 08:04:38,804 - INFO - Response - Page 1
2025-05-26 08:04:38,804 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:04:39,304 - INFO - Request Parameters - Page 2:
2025-05-26 08:04:39,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:39,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:39,991 - INFO - API请求耗时: 687ms
2025-05-26 08:04:39,991 - INFO - Response - Page 2
2025-05-26 08:04:39,991 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:04:40,491 - INFO - Request Parameters - Page 3:
2025-05-26 08:04:40,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:40,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:41,116 - INFO - API请求耗时: 625ms
2025-05-26 08:04:41,116 - INFO - Response - Page 3
2025-05-26 08:04:41,116 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:04:41,616 - INFO - Request Parameters - Page 4:
2025-05-26 08:04:41,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:41,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:42,335 - INFO - API请求耗时: 719ms
2025-05-26 08:04:42,335 - INFO - Response - Page 4
2025-05-26 08:04:42,335 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:04:42,851 - INFO - Request Parameters - Page 5:
2025-05-26 08:04:42,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:42,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:43,569 - INFO - API请求耗时: 719ms
2025-05-26 08:04:43,569 - INFO - Response - Page 5
2025-05-26 08:04:43,569 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:04:44,069 - INFO - Request Parameters - Page 6:
2025-05-26 08:04:44,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:44,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:44,788 - INFO - API请求耗时: 719ms
2025-05-26 08:04:44,788 - INFO - Response - Page 6
2025-05-26 08:04:44,788 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:04:45,304 - INFO - Request Parameters - Page 7:
2025-05-26 08:04:45,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:45,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:45,960 - INFO - API请求耗时: 656ms
2025-05-26 08:04:45,960 - INFO - Response - Page 7
2025-05-26 08:04:45,960 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:04:46,460 - INFO - Request Parameters - Page 8:
2025-05-26 08:04:46,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:46,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:47,147 - INFO - API请求耗时: 687ms
2025-05-26 08:04:47,147 - INFO - Response - Page 8
2025-05-26 08:04:47,147 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:04:47,647 - INFO - Request Parameters - Page 9:
2025-05-26 08:04:47,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:47,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:48,351 - INFO - API请求耗时: 703ms
2025-05-26 08:04:48,351 - INFO - Response - Page 9
2025-05-26 08:04:48,351 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:04:48,851 - INFO - Request Parameters - Page 10:
2025-05-26 08:04:48,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:48,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:49,522 - INFO - API请求耗时: 672ms
2025-05-26 08:04:49,522 - INFO - Response - Page 10
2025-05-26 08:04:49,522 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:04:50,038 - INFO - Request Parameters - Page 11:
2025-05-26 08:04:50,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:50,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:50,726 - INFO - API请求耗时: 688ms
2025-05-26 08:04:50,726 - INFO - Response - Page 11
2025-05-26 08:04:50,726 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:04:51,241 - INFO - Request Parameters - Page 12:
2025-05-26 08:04:51,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:51,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:52,038 - INFO - API请求耗时: 797ms
2025-05-26 08:04:52,038 - INFO - Response - Page 12
2025-05-26 08:04:52,038 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:04:52,538 - INFO - Request Parameters - Page 13:
2025-05-26 08:04:52,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:52,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:53,304 - INFO - API请求耗时: 766ms
2025-05-26 08:04:53,304 - INFO - Response - Page 13
2025-05-26 08:04:53,304 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:04:53,804 - INFO - Request Parameters - Page 14:
2025-05-26 08:04:53,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:53,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:54,601 - INFO - API请求耗时: 797ms
2025-05-26 08:04:54,601 - INFO - Response - Page 14
2025-05-26 08:04:54,601 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:04:55,116 - INFO - Request Parameters - Page 15:
2025-05-26 08:04:55,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:55,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:55,944 - INFO - API请求耗时: 828ms
2025-05-26 08:04:55,944 - INFO - Response - Page 15
2025-05-26 08:04:55,944 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:04:56,460 - INFO - Request Parameters - Page 16:
2025-05-26 08:04:56,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:56,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:57,241 - INFO - API请求耗时: 781ms
2025-05-26 08:04:57,241 - INFO - Response - Page 16
2025-05-26 08:04:57,241 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:04:57,757 - INFO - Request Parameters - Page 17:
2025-05-26 08:04:57,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:57,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:58,491 - INFO - API请求耗时: 734ms
2025-05-26 08:04:58,491 - INFO - Response - Page 17
2025-05-26 08:04:58,491 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:04:58,991 - INFO - Request Parameters - Page 18:
2025-05-26 08:04:58,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:04:58,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:04:59,679 - INFO - API请求耗时: 688ms
2025-05-26 08:04:59,679 - INFO - Response - Page 18
2025-05-26 08:04:59,679 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:05:00,194 - INFO - Request Parameters - Page 19:
2025-05-26 08:05:00,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:00,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:00,991 - INFO - API请求耗时: 797ms
2025-05-26 08:05:00,991 - INFO - Response - Page 19
2025-05-26 08:05:00,991 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:05:01,507 - INFO - Request Parameters - Page 20:
2025-05-26 08:05:01,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:01,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:02,225 - INFO - API请求耗时: 719ms
2025-05-26 08:05:02,225 - INFO - Response - Page 20
2025-05-26 08:05:02,225 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:05:02,741 - INFO - Request Parameters - Page 21:
2025-05-26 08:05:02,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:02,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:03,507 - INFO - API请求耗时: 766ms
2025-05-26 08:05:03,507 - INFO - Response - Page 21
2025-05-26 08:05:03,507 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:05:04,007 - INFO - Request Parameters - Page 22:
2025-05-26 08:05:04,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:04,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:04,710 - INFO - API请求耗时: 703ms
2025-05-26 08:05:04,710 - INFO - Response - Page 22
2025-05-26 08:05:04,710 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:05:05,210 - INFO - Request Parameters - Page 23:
2025-05-26 08:05:05,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:05,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:05,835 - INFO - API请求耗时: 625ms
2025-05-26 08:05:05,835 - INFO - Response - Page 23
2025-05-26 08:05:05,835 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:05:06,350 - INFO - Request Parameters - Page 24:
2025-05-26 08:05:06,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:06,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:07,054 - INFO - API请求耗时: 703ms
2025-05-26 08:05:07,054 - INFO - Response - Page 24
2025-05-26 08:05:07,054 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:05:07,569 - INFO - Request Parameters - Page 25:
2025-05-26 08:05:07,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:07,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:08,319 - INFO - API请求耗时: 750ms
2025-05-26 08:05:08,319 - INFO - Response - Page 25
2025-05-26 08:05:08,335 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:05:08,850 - INFO - Request Parameters - Page 26:
2025-05-26 08:05:08,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:08,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:09,757 - INFO - API请求耗时: 906ms
2025-05-26 08:05:09,757 - INFO - Response - Page 26
2025-05-26 08:05:09,757 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:05:10,272 - INFO - Request Parameters - Page 27:
2025-05-26 08:05:10,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:10,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:10,991 - INFO - API请求耗时: 719ms
2025-05-26 08:05:10,991 - INFO - Response - Page 27
2025-05-26 08:05:10,991 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:05:11,491 - INFO - Request Parameters - Page 28:
2025-05-26 08:05:11,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:11,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:12,210 - INFO - API请求耗时: 719ms
2025-05-26 08:05:12,210 - INFO - Response - Page 28
2025-05-26 08:05:12,210 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:05:12,725 - INFO - Request Parameters - Page 29:
2025-05-26 08:05:12,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:12,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:13,366 - INFO - API请求耗时: 641ms
2025-05-26 08:05:13,366 - INFO - Response - Page 29
2025-05-26 08:05:13,366 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:05:13,866 - INFO - Request Parameters - Page 30:
2025-05-26 08:05:13,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:13,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:14,741 - INFO - API请求耗时: 875ms
2025-05-26 08:05:14,741 - INFO - Response - Page 30
2025-05-26 08:05:14,741 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:05:15,241 - INFO - Request Parameters - Page 31:
2025-05-26 08:05:15,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:15,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:15,960 - INFO - API请求耗时: 719ms
2025-05-26 08:05:15,960 - INFO - Response - Page 31
2025-05-26 08:05:15,960 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:05:16,475 - INFO - Request Parameters - Page 32:
2025-05-26 08:05:16,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:16,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:17,163 - INFO - API请求耗时: 687ms
2025-05-26 08:05:17,163 - INFO - Response - Page 32
2025-05-26 08:05:17,163 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:05:17,663 - INFO - Request Parameters - Page 33:
2025-05-26 08:05:17,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:17,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:18,335 - INFO - API请求耗时: 672ms
2025-05-26 08:05:18,335 - INFO - Response - Page 33
2025-05-26 08:05:18,335 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:05:18,850 - INFO - Request Parameters - Page 34:
2025-05-26 08:05:18,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:18,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:19,507 - INFO - API请求耗时: 656ms
2025-05-26 08:05:19,507 - INFO - Response - Page 34
2025-05-26 08:05:19,507 - INFO - 第 34 页获取到 100 条记录
2025-05-26 08:05:20,022 - INFO - Request Parameters - Page 35:
2025-05-26 08:05:20,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:20,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:20,694 - INFO - API请求耗时: 672ms
2025-05-26 08:05:20,694 - INFO - Response - Page 35
2025-05-26 08:05:20,694 - INFO - 第 35 页获取到 100 条记录
2025-05-26 08:05:21,210 - INFO - Request Parameters - Page 36:
2025-05-26 08:05:21,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:21,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:21,897 - INFO - API请求耗时: 687ms
2025-05-26 08:05:21,897 - INFO - Response - Page 36
2025-05-26 08:05:21,897 - INFO - 第 36 页获取到 100 条记录
2025-05-26 08:05:22,413 - INFO - Request Parameters - Page 37:
2025-05-26 08:05:22,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:22,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:23,116 - INFO - API请求耗时: 703ms
2025-05-26 08:05:23,132 - INFO - Response - Page 37
2025-05-26 08:05:23,132 - INFO - 第 37 页获取到 100 条记录
2025-05-26 08:05:23,632 - INFO - Request Parameters - Page 38:
2025-05-26 08:05:23,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:23,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:24,350 - INFO - API请求耗时: 719ms
2025-05-26 08:05:24,350 - INFO - Response - Page 38
2025-05-26 08:05:24,350 - INFO - 第 38 页获取到 100 条记录
2025-05-26 08:05:24,866 - INFO - Request Parameters - Page 39:
2025-05-26 08:05:24,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:24,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:25,585 - INFO - API请求耗时: 719ms
2025-05-26 08:05:25,585 - INFO - Response - Page 39
2025-05-26 08:05:25,585 - INFO - 第 39 页获取到 100 条记录
2025-05-26 08:05:26,085 - INFO - Request Parameters - Page 40:
2025-05-26 08:05:26,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:26,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400946, 1746460800946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:26,553 - INFO - API请求耗时: 469ms
2025-05-26 08:05:26,553 - INFO - Response - Page 40
2025-05-26 08:05:26,553 - INFO - 第 40 页获取到 23 条记录
2025-05-26 08:05:26,553 - INFO - 查询完成，共获取到 3923 条记录
2025-05-26 08:05:26,553 - INFO - 分段 6 查询成功，获取到 3923 条记录
2025-05-26 08:05:27,569 - INFO - 查询分段 7: 2025-05-07 至 2025-05-13
2025-05-26 08:05:27,569 - INFO - 查询日期范围: 2025-05-07 至 2025-05-13，使用分页查询，每页 100 条记录
2025-05-26 08:05:27,569 - INFO - Request Parameters - Page 1:
2025-05-26 08:05:27,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:27,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:28,288 - INFO - API请求耗时: 719ms
2025-05-26 08:05:28,303 - INFO - Response - Page 1
2025-05-26 08:05:28,303 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:05:28,803 - INFO - Request Parameters - Page 2:
2025-05-26 08:05:28,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:28,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:29,444 - INFO - API请求耗时: 641ms
2025-05-26 08:05:29,444 - INFO - Response - Page 2
2025-05-26 08:05:29,444 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:05:29,960 - INFO - Request Parameters - Page 3:
2025-05-26 08:05:29,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:29,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:30,694 - INFO - API请求耗时: 734ms
2025-05-26 08:05:30,694 - INFO - Response - Page 3
2025-05-26 08:05:30,694 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:05:31,194 - INFO - Request Parameters - Page 4:
2025-05-26 08:05:31,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:31,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:31,944 - INFO - API请求耗时: 750ms
2025-05-26 08:05:31,944 - INFO - Response - Page 4
2025-05-26 08:05:31,944 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:05:32,460 - INFO - Request Parameters - Page 5:
2025-05-26 08:05:32,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:32,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:33,116 - INFO - API请求耗时: 656ms
2025-05-26 08:05:33,116 - INFO - Response - Page 5
2025-05-26 08:05:33,116 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:05:33,616 - INFO - Request Parameters - Page 6:
2025-05-26 08:05:33,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:33,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:34,397 - INFO - API请求耗时: 781ms
2025-05-26 08:05:34,397 - INFO - Response - Page 6
2025-05-26 08:05:34,397 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:05:34,897 - INFO - Request Parameters - Page 7:
2025-05-26 08:05:34,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:34,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:35,585 - INFO - API请求耗时: 687ms
2025-05-26 08:05:35,585 - INFO - Response - Page 7
2025-05-26 08:05:35,585 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:05:36,100 - INFO - Request Parameters - Page 8:
2025-05-26 08:05:36,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:36,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:36,819 - INFO - API请求耗时: 719ms
2025-05-26 08:05:36,819 - INFO - Response - Page 8
2025-05-26 08:05:36,819 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:05:37,335 - INFO - Request Parameters - Page 9:
2025-05-26 08:05:37,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:37,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:38,022 - INFO - API请求耗时: 687ms
2025-05-26 08:05:38,022 - INFO - Response - Page 9
2025-05-26 08:05:38,022 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:05:38,522 - INFO - Request Parameters - Page 10:
2025-05-26 08:05:38,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:38,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:39,272 - INFO - API请求耗时: 750ms
2025-05-26 08:05:39,272 - INFO - Response - Page 10
2025-05-26 08:05:39,272 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:05:39,788 - INFO - Request Parameters - Page 11:
2025-05-26 08:05:39,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:39,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:40,506 - INFO - API请求耗时: 719ms
2025-05-26 08:05:40,506 - INFO - Response - Page 11
2025-05-26 08:05:40,506 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:05:41,022 - INFO - Request Parameters - Page 12:
2025-05-26 08:05:41,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:41,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:41,725 - INFO - API请求耗时: 703ms
2025-05-26 08:05:41,725 - INFO - Response - Page 12
2025-05-26 08:05:41,725 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:05:42,241 - INFO - Request Parameters - Page 13:
2025-05-26 08:05:42,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:42,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:42,928 - INFO - API请求耗时: 687ms
2025-05-26 08:05:42,928 - INFO - Response - Page 13
2025-05-26 08:05:42,928 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:05:43,444 - INFO - Request Parameters - Page 14:
2025-05-26 08:05:43,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:43,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:44,241 - INFO - API请求耗时: 797ms
2025-05-26 08:05:44,241 - INFO - Response - Page 14
2025-05-26 08:05:44,241 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:05:44,756 - INFO - Request Parameters - Page 15:
2025-05-26 08:05:44,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:44,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:45,397 - INFO - API请求耗时: 641ms
2025-05-26 08:05:45,397 - INFO - Response - Page 15
2025-05-26 08:05:45,397 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:05:45,897 - INFO - Request Parameters - Page 16:
2025-05-26 08:05:45,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:45,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:46,710 - INFO - API请求耗时: 812ms
2025-05-26 08:05:46,710 - INFO - Response - Page 16
2025-05-26 08:05:46,710 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:05:47,210 - INFO - Request Parameters - Page 17:
2025-05-26 08:05:47,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:47,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:47,835 - INFO - API请求耗时: 625ms
2025-05-26 08:05:47,835 - INFO - Response - Page 17
2025-05-26 08:05:47,835 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:05:48,335 - INFO - Request Parameters - Page 18:
2025-05-26 08:05:48,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:48,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:49,256 - INFO - API请求耗时: 922ms
2025-05-26 08:05:49,256 - INFO - Response - Page 18
2025-05-26 08:05:49,256 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:05:49,772 - INFO - Request Parameters - Page 19:
2025-05-26 08:05:49,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:49,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:50,506 - INFO - API请求耗时: 734ms
2025-05-26 08:05:50,506 - INFO - Response - Page 19
2025-05-26 08:05:50,506 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:05:51,006 - INFO - Request Parameters - Page 20:
2025-05-26 08:05:51,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:51,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:51,663 - INFO - API请求耗时: 656ms
2025-05-26 08:05:51,663 - INFO - Response - Page 20
2025-05-26 08:05:51,663 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:05:52,163 - INFO - Request Parameters - Page 21:
2025-05-26 08:05:52,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:52,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:52,803 - INFO - API请求耗时: 641ms
2025-05-26 08:05:52,803 - INFO - Response - Page 21
2025-05-26 08:05:52,803 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:05:53,319 - INFO - Request Parameters - Page 22:
2025-05-26 08:05:53,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:53,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:54,116 - INFO - API请求耗时: 797ms
2025-05-26 08:05:54,116 - INFO - Response - Page 22
2025-05-26 08:05:54,116 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:05:54,631 - INFO - Request Parameters - Page 23:
2025-05-26 08:05:54,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:54,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:55,381 - INFO - API请求耗时: 750ms
2025-05-26 08:05:55,381 - INFO - Response - Page 23
2025-05-26 08:05:55,381 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:05:55,897 - INFO - Request Parameters - Page 24:
2025-05-26 08:05:55,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:55,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:56,959 - INFO - API请求耗时: 1062ms
2025-05-26 08:05:56,959 - INFO - Response - Page 24
2025-05-26 08:05:56,959 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:05:57,460 - INFO - Request Parameters - Page 25:
2025-05-26 08:05:57,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:57,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:58,163 - INFO - API请求耗时: 703ms
2025-05-26 08:05:58,163 - INFO - Response - Page 25
2025-05-26 08:05:58,163 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:05:58,678 - INFO - Request Parameters - Page 26:
2025-05-26 08:05:58,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:58,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:05:59,319 - INFO - API请求耗时: 641ms
2025-05-26 08:05:59,319 - INFO - Response - Page 26
2025-05-26 08:05:59,319 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:05:59,834 - INFO - Request Parameters - Page 27:
2025-05-26 08:05:59,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:05:59,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:00,475 - INFO - API请求耗时: 641ms
2025-05-26 08:06:00,475 - INFO - Response - Page 27
2025-05-26 08:06:00,475 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:06:00,975 - INFO - Request Parameters - Page 28:
2025-05-26 08:06:00,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:00,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:01,694 - INFO - API请求耗时: 719ms
2025-05-26 08:06:01,694 - INFO - Response - Page 28
2025-05-26 08:06:01,694 - INFO - 第 28 页获取到 100 条记录
2025-05-26 08:06:02,209 - INFO - Request Parameters - Page 29:
2025-05-26 08:06:02,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:02,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:02,897 - INFO - API请求耗时: 688ms
2025-05-26 08:06:02,897 - INFO - Response - Page 29
2025-05-26 08:06:02,913 - INFO - 第 29 页获取到 100 条记录
2025-05-26 08:06:03,413 - INFO - Request Parameters - Page 30:
2025-05-26 08:06:03,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:03,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:04,069 - INFO - API请求耗时: 656ms
2025-05-26 08:06:04,069 - INFO - Response - Page 30
2025-05-26 08:06:04,084 - INFO - 第 30 页获取到 100 条记录
2025-05-26 08:06:04,600 - INFO - Request Parameters - Page 31:
2025-05-26 08:06:04,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:04,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:05,350 - INFO - API请求耗时: 750ms
2025-05-26 08:06:05,350 - INFO - Response - Page 31
2025-05-26 08:06:05,350 - INFO - 第 31 页获取到 100 条记录
2025-05-26 08:06:05,866 - INFO - Request Parameters - Page 32:
2025-05-26 08:06:05,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:05,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:06,475 - INFO - API请求耗时: 609ms
2025-05-26 08:06:06,475 - INFO - Response - Page 32
2025-05-26 08:06:06,475 - INFO - 第 32 页获取到 100 条记录
2025-05-26 08:06:06,975 - INFO - Request Parameters - Page 33:
2025-05-26 08:06:06,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:06,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:07,631 - INFO - API请求耗时: 656ms
2025-05-26 08:06:07,631 - INFO - Response - Page 33
2025-05-26 08:06:07,631 - INFO - 第 33 页获取到 100 条记录
2025-05-26 08:06:08,147 - INFO - Request Parameters - Page 34:
2025-05-26 08:06:08,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:08,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200946, 1747065600946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:08,866 - INFO - API请求耗时: 719ms
2025-05-26 08:06:08,866 - INFO - Response - Page 34
2025-05-26 08:06:08,866 - INFO - 第 34 页获取到 88 条记录
2025-05-26 08:06:08,866 - INFO - 查询完成，共获取到 3388 条记录
2025-05-26 08:06:08,866 - INFO - 分段 7 查询成功，获取到 3388 条记录
2025-05-26 08:06:09,866 - INFO - 查询分段 8: 2025-05-14 至 2025-05-20
2025-05-26 08:06:09,866 - INFO - 查询日期范围: 2025-05-14 至 2025-05-20，使用分页查询，每页 100 条记录
2025-05-26 08:06:09,866 - INFO - Request Parameters - Page 1:
2025-05-26 08:06:09,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:09,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:10,569 - INFO - API请求耗时: 703ms
2025-05-26 08:06:10,569 - INFO - Response - Page 1
2025-05-26 08:06:10,569 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:06:11,069 - INFO - Request Parameters - Page 2:
2025-05-26 08:06:11,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:11,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:11,788 - INFO - API请求耗时: 719ms
2025-05-26 08:06:11,788 - INFO - Response - Page 2
2025-05-26 08:06:11,788 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:06:12,303 - INFO - Request Parameters - Page 3:
2025-05-26 08:06:12,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:12,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:13,069 - INFO - API请求耗时: 766ms
2025-05-26 08:06:13,069 - INFO - Response - Page 3
2025-05-26 08:06:13,069 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:06:13,569 - INFO - Request Parameters - Page 4:
2025-05-26 08:06:13,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:13,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:14,225 - INFO - API请求耗时: 656ms
2025-05-26 08:06:14,225 - INFO - Response - Page 4
2025-05-26 08:06:14,225 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:06:14,741 - INFO - Request Parameters - Page 5:
2025-05-26 08:06:14,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:14,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:15,397 - INFO - API请求耗时: 656ms
2025-05-26 08:06:15,397 - INFO - Response - Page 5
2025-05-26 08:06:15,397 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:06:15,913 - INFO - Request Parameters - Page 6:
2025-05-26 08:06:15,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:15,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:16,631 - INFO - API请求耗时: 719ms
2025-05-26 08:06:16,631 - INFO - Response - Page 6
2025-05-26 08:06:16,631 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:06:17,147 - INFO - Request Parameters - Page 7:
2025-05-26 08:06:17,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:17,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:17,850 - INFO - API请求耗时: 703ms
2025-05-26 08:06:17,850 - INFO - Response - Page 7
2025-05-26 08:06:17,850 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:06:18,366 - INFO - Request Parameters - Page 8:
2025-05-26 08:06:18,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:18,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:19,162 - INFO - API请求耗时: 797ms
2025-05-26 08:06:19,162 - INFO - Response - Page 8
2025-05-26 08:06:19,162 - INFO - 第 8 页获取到 100 条记录
2025-05-26 08:06:19,662 - INFO - Request Parameters - Page 9:
2025-05-26 08:06:19,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:19,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:20,444 - INFO - API请求耗时: 781ms
2025-05-26 08:06:20,444 - INFO - Response - Page 9
2025-05-26 08:06:20,444 - INFO - 第 9 页获取到 100 条记录
2025-05-26 08:06:20,959 - INFO - Request Parameters - Page 10:
2025-05-26 08:06:20,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:20,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:21,600 - INFO - API请求耗时: 641ms
2025-05-26 08:06:21,616 - INFO - Response - Page 10
2025-05-26 08:06:21,616 - INFO - 第 10 页获取到 100 条记录
2025-05-26 08:06:22,131 - INFO - Request Parameters - Page 11:
2025-05-26 08:06:22,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:22,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:22,897 - INFO - API请求耗时: 766ms
2025-05-26 08:06:22,897 - INFO - Response - Page 11
2025-05-26 08:06:22,897 - INFO - 第 11 页获取到 100 条记录
2025-05-26 08:06:23,397 - INFO - Request Parameters - Page 12:
2025-05-26 08:06:23,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:23,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:24,397 - INFO - API请求耗时: 1000ms
2025-05-26 08:06:24,397 - INFO - Response - Page 12
2025-05-26 08:06:24,397 - INFO - 第 12 页获取到 100 条记录
2025-05-26 08:06:24,912 - INFO - Request Parameters - Page 13:
2025-05-26 08:06:24,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:24,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:25,600 - INFO - API请求耗时: 687ms
2025-05-26 08:06:25,600 - INFO - Response - Page 13
2025-05-26 08:06:25,600 - INFO - 第 13 页获取到 100 条记录
2025-05-26 08:06:26,100 - INFO - Request Parameters - Page 14:
2025-05-26 08:06:26,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:26,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:26,741 - INFO - API请求耗时: 641ms
2025-05-26 08:06:26,741 - INFO - Response - Page 14
2025-05-26 08:06:26,741 - INFO - 第 14 页获取到 100 条记录
2025-05-26 08:06:27,241 - INFO - Request Parameters - Page 15:
2025-05-26 08:06:27,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:27,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:27,912 - INFO - API请求耗时: 672ms
2025-05-26 08:06:27,912 - INFO - Response - Page 15
2025-05-26 08:06:27,912 - INFO - 第 15 页获取到 100 条记录
2025-05-26 08:06:28,428 - INFO - Request Parameters - Page 16:
2025-05-26 08:06:28,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:28,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:29,178 - INFO - API请求耗时: 750ms
2025-05-26 08:06:29,178 - INFO - Response - Page 16
2025-05-26 08:06:29,194 - INFO - 第 16 页获取到 100 条记录
2025-05-26 08:06:29,694 - INFO - Request Parameters - Page 17:
2025-05-26 08:06:29,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:29,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:30,412 - INFO - API请求耗时: 719ms
2025-05-26 08:06:30,412 - INFO - Response - Page 17
2025-05-26 08:06:30,412 - INFO - 第 17 页获取到 100 条记录
2025-05-26 08:06:30,928 - INFO - Request Parameters - Page 18:
2025-05-26 08:06:30,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:30,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:31,537 - INFO - API请求耗时: 609ms
2025-05-26 08:06:31,537 - INFO - Response - Page 18
2025-05-26 08:06:31,537 - INFO - 第 18 页获取到 100 条记录
2025-05-26 08:06:32,053 - INFO - Request Parameters - Page 19:
2025-05-26 08:06:32,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:32,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:32,741 - INFO - API请求耗时: 688ms
2025-05-26 08:06:32,741 - INFO - Response - Page 19
2025-05-26 08:06:32,741 - INFO - 第 19 页获取到 100 条记录
2025-05-26 08:06:33,256 - INFO - Request Parameters - Page 20:
2025-05-26 08:06:33,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:33,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:33,975 - INFO - API请求耗时: 719ms
2025-05-26 08:06:33,975 - INFO - Response - Page 20
2025-05-26 08:06:33,975 - INFO - 第 20 页获取到 100 条记录
2025-05-26 08:06:34,491 - INFO - Request Parameters - Page 21:
2025-05-26 08:06:34,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:34,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:35,131 - INFO - API请求耗时: 641ms
2025-05-26 08:06:35,131 - INFO - Response - Page 21
2025-05-26 08:06:35,131 - INFO - 第 21 页获取到 100 条记录
2025-05-26 08:06:35,647 - INFO - Request Parameters - Page 22:
2025-05-26 08:06:35,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:35,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:36,303 - INFO - API请求耗时: 656ms
2025-05-26 08:06:36,303 - INFO - Response - Page 22
2025-05-26 08:06:36,303 - INFO - 第 22 页获取到 100 条记录
2025-05-26 08:06:36,819 - INFO - Request Parameters - Page 23:
2025-05-26 08:06:36,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:36,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:37,506 - INFO - API请求耗时: 687ms
2025-05-26 08:06:37,506 - INFO - Response - Page 23
2025-05-26 08:06:37,506 - INFO - 第 23 页获取到 100 条记录
2025-05-26 08:06:38,022 - INFO - Request Parameters - Page 24:
2025-05-26 08:06:38,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:38,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:38,709 - INFO - API请求耗时: 687ms
2025-05-26 08:06:38,709 - INFO - Response - Page 24
2025-05-26 08:06:38,709 - INFO - 第 24 页获取到 100 条记录
2025-05-26 08:06:39,225 - INFO - Request Parameters - Page 25:
2025-05-26 08:06:39,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:39,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:39,850 - INFO - API请求耗时: 625ms
2025-05-26 08:06:39,850 - INFO - Response - Page 25
2025-05-26 08:06:39,850 - INFO - 第 25 页获取到 100 条记录
2025-05-26 08:06:40,365 - INFO - Request Parameters - Page 26:
2025-05-26 08:06:40,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:40,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:41,115 - INFO - API请求耗时: 750ms
2025-05-26 08:06:41,115 - INFO - Response - Page 26
2025-05-26 08:06:41,115 - INFO - 第 26 页获取到 100 条记录
2025-05-26 08:06:41,615 - INFO - Request Parameters - Page 27:
2025-05-26 08:06:41,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:41,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:42,240 - INFO - API请求耗时: 625ms
2025-05-26 08:06:42,240 - INFO - Response - Page 27
2025-05-26 08:06:42,240 - INFO - 第 27 页获取到 100 条记录
2025-05-26 08:06:42,756 - INFO - Request Parameters - Page 28:
2025-05-26 08:06:42,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:42,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000946, 1747670400946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:43,084 - INFO - API请求耗时: 328ms
2025-05-26 08:06:43,084 - INFO - Response - Page 28
2025-05-26 08:06:43,084 - INFO - 第 28 页获取到 3 条记录
2025-05-26 08:06:43,084 - INFO - 查询完成，共获取到 2703 条记录
2025-05-26 08:06:43,084 - INFO - 分段 8 查询成功，获取到 2703 条记录
2025-05-26 08:06:44,100 - INFO - 查询分段 9: 2025-05-21 至 2025-05-25
2025-05-26 08:06:44,100 - INFO - 查询日期范围: 2025-05-21 至 2025-05-25，使用分页查询，每页 100 条记录
2025-05-26 08:06:44,100 - INFO - Request Parameters - Page 1:
2025-05-26 08:06:44,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:44,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800946, 1748188799946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:44,772 - INFO - API请求耗时: 672ms
2025-05-26 08:06:44,772 - INFO - Response - Page 1
2025-05-26 08:06:44,772 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:06:45,287 - INFO - Request Parameters - Page 2:
2025-05-26 08:06:45,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:45,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800946, 1748188799946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:46,147 - INFO - API请求耗时: 859ms
2025-05-26 08:06:46,147 - INFO - Response - Page 2
2025-05-26 08:06:46,147 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:06:46,662 - INFO - Request Parameters - Page 3:
2025-05-26 08:06:46,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:46,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800946, 1748188799946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:47,319 - INFO - API请求耗时: 656ms
2025-05-26 08:06:47,319 - INFO - Response - Page 3
2025-05-26 08:06:47,319 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:06:47,834 - INFO - Request Parameters - Page 4:
2025-05-26 08:06:47,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:47,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800946, 1748188799946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:48,522 - INFO - API请求耗时: 688ms
2025-05-26 08:06:48,522 - INFO - Response - Page 4
2025-05-26 08:06:48,522 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:06:49,037 - INFO - Request Parameters - Page 5:
2025-05-26 08:06:49,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:49,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800946, 1748188799946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:49,694 - INFO - API请求耗时: 656ms
2025-05-26 08:06:49,694 - INFO - Response - Page 5
2025-05-26 08:06:49,694 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:06:50,209 - INFO - Request Parameters - Page 6:
2025-05-26 08:06:50,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:06:50,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800946, 1748188799946], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:06:50,865 - INFO - API请求耗时: 656ms
2025-05-26 08:06:50,881 - INFO - Response - Page 6
2025-05-26 08:06:50,881 - INFO - 第 6 页获取到 91 条记录
2025-05-26 08:06:50,881 - INFO - 查询完成，共获取到 591 条记录
2025-05-26 08:06:50,881 - INFO - 分段 9 查询成功，获取到 591 条记录
2025-05-26 08:06:51,897 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 27631 条记录，失败 0 次
2025-05-26 08:06:51,897 - INFO - 成功获取宜搭日销售表单数据，共 27631 条记录
2025-05-26 08:06:51,897 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-26 08:06:51,897 - INFO - 开始对比和同步日销售数据...
2025-05-26 08:06:52,709 - INFO - 成功创建宜搭日销售数据索引，共 10863 条记录
2025-05-26 08:06:52,709 - INFO - 开始处理数衍数据，共 13022 条记录
2025-05-26 08:06:53,303 - INFO - 更新表单数据成功: FINST-COC668A14NZUZ303B5AU6C994R4N3XB95W8AM3A1
2025-05-26 08:06:53,303 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250501, 变更字段: [{'field': 'amount', 'old_value': 13369.29, 'new_value': 13575.49}, {'field': 'count', 'old_value': 132, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 3849.77, 'new_value': 4055.97}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 26}]
2025-05-26 08:06:53,787 - INFO - 更新表单数据成功: FINST-K7666JC18T4VXKAH7HU0P8MGF7E73A5KNBAAMVI
2025-05-26 08:06:53,787 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 4351.6, 'new_value': 4429.6}, {'field': 'dailyBillAmount', 'old_value': 4351.6, 'new_value': 4429.6}]
2025-05-26 08:06:54,303 - INFO - 更新表单数据成功: FINST-F7D66UA1RBKVVEEZBXEN49AT3NL23ZDLQUYAM26
2025-05-26 08:06:54,303 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_20250503, 变更字段: [{'field': 'amount', 'old_value': 10595.7, 'new_value': 10728.8}, {'field': 'count', 'old_value': 41, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 10848.8, 'new_value': 10981.9}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 41}]
2025-05-26 08:06:54,772 - INFO - 更新表单数据成功: FINST-ME9666C1P3MVZW1FAF2UPD7ICQD63WU9K10BMF4
2025-05-26 08:06:54,772 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'amount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'count', 'old_value': 60, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 61}]
2025-05-26 08:06:55,256 - INFO - 更新表单数据成功: FINST-LLF66J71Q4NVCL9C6OFDU6A1JF0Z2KQYQUYAM72
2025-05-26 08:06:55,256 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_20250517, 变更字段: [{'field': 'amount', 'old_value': 46011.0, 'new_value': 46548.0}, {'field': 'count', 'old_value': 161, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 46011.0, 'new_value': 46548.0}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 162}]
2025-05-26 08:06:55,678 - INFO - 更新表单数据成功: FINST-LLF66J71Q4NVCL9C6OFDU6A1JF0Z2KQYQUYAMQ2
2025-05-26 08:06:55,678 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 15257.9, 'new_value': 15335.9}, {'field': 'dailyBillAmount', 'old_value': 15257.9, 'new_value': 15335.9}]
2025-05-26 08:06:56,178 - INFO - 更新表单数据成功: FINST-90D66XA1QCLV7WREDI4T58VSP5OA2881RUYAMH4
2025-05-26 08:06:56,178 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 40861.75, 'new_value': 35906.27}, {'field': 'dailyBillAmount', 'old_value': 40861.75, 'new_value': 35906.27}]
2025-05-26 08:06:56,647 - INFO - 更新表单数据成功: FINST-QVA66B81BROVRTGKB6CDRA5GHCZM3XTTGW2BM3H
2025-05-26 08:06:56,647 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8206.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8206.5}]
2025-05-26 08:06:57,053 - INFO - 更新表单数据成功: FINST-QVA66B81BROVRTGKB6CDRA5GHCZM3XTTGW2BM6H
2025-05-26 08:06:57,053 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_20250524, 变更字段: [{'field': 'amount', 'old_value': 0.0, 'new_value': 604.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 604.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-05-26 08:06:57,506 - INFO - 更新表单数据成功: FINST-QVA66B81BROVRTGKB6CDRA5GHCZM3XTTGW2BMYH
2025-05-26 08:06:57,522 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250524, 变更字段: [{'field': 'amount', 'old_value': 6903.54, 'new_value': 6993.4}, {'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 3688.18, 'new_value': 3778.04}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 31}]
2025-05-26 08:06:57,959 - INFO - 更新表单数据成功: FINST-QVA66B81BROVRTGKB6CDRA5GHCZM3XTTGW2BMHI
2025-05-26 08:06:57,959 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_20250524, 变更字段: [{'field': 'amount', 'old_value': 3910.67, 'new_value': 3939.72}, {'field': 'count', 'old_value': 95, 'new_value': 96}, {'field': 'onlineAmount', 'old_value': 280.7, 'new_value': 309.75}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 12}]
2025-05-26 08:06:58,350 - INFO - 更新表单数据成功: FINST-QVA66B81BROVRTGKB6CDRA5GHCZM3XTTGW2BM0J
2025-05-26 08:06:58,350 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_20250524, 变更字段: [{'field': 'amount', 'old_value': 4117.15, 'new_value': 4066.1499999999996}, {'field': 'onlineAmount', 'old_value': 2076.84, 'new_value': 2025.84}]
2025-05-26 08:06:58,756 - INFO - 更新表单数据成功: FINST-QVA66B81BROVRTGKB6CDRA5GHCZM3XTTGW2BMGJ
2025-05-26 08:06:58,756 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_20250524, 变更字段: [{'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'onlineCount', 'old_value': 43, 'new_value': 44}]
2025-05-26 08:06:59,240 - INFO - 更新表单数据成功: FINST-YPE66RB1WEOVLBAO8KI89ATQQE0326HWGW2BMBC
2025-05-26 08:06:59,240 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_20250524, 变更字段: [{'field': 'amount', 'old_value': 768.64, 'new_value': 786.64}, {'field': 'count', 'old_value': 29, 'new_value': 30}, {'field': 'onlineAmount', 'old_value': 693.64, 'new_value': 711.64}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-05-26 08:06:59,693 - INFO - 更新表单数据成功: FINST-YPE66RB1WEOVLBAO8KI89ATQQE0326HWGW2BM5D
2025-05-26 08:06:59,693 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9102.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9102.7}]
2025-05-26 08:07:00,209 - INFO - 更新表单数据成功: FINST-YPE66RB1WEOVLBAO8KI89ATQQE0326HWGW2BM8E
2025-05-26 08:07:00,209 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 2802.81, 'new_value': 2814.81}, {'field': 'amount', 'old_value': 2802.81, 'new_value': 2814.81}, {'field': 'count', 'old_value': 151, 'new_value': 153}, {'field': 'onlineAmount', 'old_value': 2134.02, 'new_value': 2146.02}, {'field': 'onlineCount', 'old_value': 123, 'new_value': 125}]
2025-05-26 08:07:00,631 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3G4ZGW2BM4
2025-05-26 08:07:00,631 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250524, 变更字段: [{'field': 'amount', 'old_value': 6767.57, 'new_value': 6811.87}, {'field': 'count', 'old_value': 325, 'new_value': 329}, {'field': 'onlineAmount', 'old_value': 4135.13, 'new_value': 4179.43}, {'field': 'onlineCount', 'old_value': 183, 'new_value': 187}]
2025-05-26 08:07:01,084 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMD
2025-05-26 08:07:01,084 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 2053.4, 'new_value': 2057.19}, {'field': 'amount', 'old_value': 2053.3999999999996, 'new_value': 2057.19}, {'field': 'count', 'old_value': 148, 'new_value': 149}, {'field': 'onlineAmount', 'old_value': 943.87, 'new_value': 947.66}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 95}]
2025-05-26 08:07:01,600 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMI
2025-05-26 08:07:01,600 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 27228.89, 'new_value': 25183.15}, {'field': 'dailyBillAmount', 'old_value': 27228.89, 'new_value': 25183.15}]
2025-05-26 08:07:02,022 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMM
2025-05-26 08:07:02,022 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 6642.66, 'new_value': 6653.26}, {'field': 'amount', 'old_value': 6642.66, 'new_value': 6653.26}, {'field': 'count', 'old_value': 307, 'new_value': 308}, {'field': 'onlineAmount', 'old_value': 4677.45, 'new_value': 4688.05}, {'field': 'onlineCount', 'old_value': 209, 'new_value': 210}]
2025-05-26 08:07:02,490 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMP
2025-05-26 08:07:02,490 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 359.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 359.0}]
2025-05-26 08:07:02,990 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMS
2025-05-26 08:07:02,990 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_20250524, 变更字段: [{'field': 'amount', 'old_value': 6906.0, 'new_value': 6914.06}, {'field': 'count', 'old_value': 111, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 4451.34, 'new_value': 4459.4}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 34}]
2025-05-26 08:07:03,428 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMY
2025-05-26 08:07:03,428 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 28518.15}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 28518.15}]
2025-05-26 08:07:03,881 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BM81
2025-05-26 08:07:03,881 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_20250524, 变更字段: [{'field': 'amount', 'old_value': 32121.23, 'new_value': 32544.129999999997}, {'field': 'count', 'old_value': 294, 'new_value': 296}, {'field': 'instoreAmount', 'old_value': 24225.7, 'new_value': 24648.6}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 164}]
2025-05-26 08:07:04,381 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMA1
2025-05-26 08:07:04,381 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250524, 变更字段: [{'field': 'amount', 'old_value': 41681.3, 'new_value': 43138.3}, {'field': 'count', 'old_value': 292, 'new_value': 298}, {'field': 'instoreAmount', 'old_value': 24834.8, 'new_value': 26291.8}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 135}]
2025-05-26 08:07:04,834 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMJ1
2025-05-26 08:07:04,834 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250524, 变更字段: [{'field': 'amount', 'old_value': 56039.73, 'new_value': 56918.26}, {'field': 'count', 'old_value': 315, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 46609.7, 'new_value': 47488.23}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 191}]
2025-05-26 08:07:05,287 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMM1
2025-05-26 08:07:05,287 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_20250524, 变更字段: [{'field': 'amount', 'old_value': 24632.1, 'new_value': 25034.1}, {'field': 'count', 'old_value': 146, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 23380.9, 'new_value': 23782.9}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 137}]
2025-05-26 08:07:05,787 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMX1
2025-05-26 08:07:05,787 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250524, 变更字段: [{'field': 'amount', 'old_value': 5098.870000000001, 'new_value': 5113.27}, {'field': 'count', 'old_value': 292, 'new_value': 293}, {'field': 'onlineAmount', 'old_value': 2232.45, 'new_value': 2246.85}, {'field': 'onlineCount', 'old_value': 132, 'new_value': 133}]
2025-05-26 08:07:06,350 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BM22
2025-05-26 08:07:06,350 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250524, 变更字段: [{'field': 'amount', 'old_value': 31490.24, 'new_value': 31500.12}, {'field': 'count', 'old_value': 290, 'new_value': 291}, {'field': 'instoreAmount', 'old_value': 26813.17, 'new_value': 26823.05}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 136}]
2025-05-26 08:07:06,772 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BM52
2025-05-26 08:07:06,772 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4089.35}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4089.35}, {'field': 'amount', 'old_value': 255.11, 'new_value': 2002.4}, {'field': 'count', 'old_value': 28, 'new_value': 173}, {'field': 'instoreAmount', 'old_value': 286.5, 'new_value': 2084.05}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 173}]
2025-05-26 08:07:07,318 - INFO - 更新表单数据成功: FINST-VRA66VA1TFPV58TNE4EXJ993C92O3H4ZGW2BMM2
2025-05-26 08:07:07,318 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 55496.37}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 55496.37}, {'field': 'amount', 'old_value': 1217.87, 'new_value': 3112.93}, {'field': 'count', 'old_value': 12, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 175.02, 'new_value': 2070.08}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 28}]
2025-05-26 08:07:07,772 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BMTA
2025-05-26 08:07:07,772 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 18858.54}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 18858.54}, {'field': 'amount', 'old_value': 451.35, 'new_value': 1209.65}, {'field': 'count', 'old_value': 7, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 143.52, 'new_value': 901.82}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 7}]
2025-05-26 08:07:08,256 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863AGKK10BM9J
2025-05-26 08:07:08,256 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250522, 变更字段: [{'field': 'amount', 'old_value': 1199.6, 'new_value': 1229.5}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 656.04, 'new_value': 685.94}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-05-26 08:07:08,740 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BMC
2025-05-26 08:07:08,740 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_20250524, 变更字段: [{'field': 'amount', 'old_value': 10103.9, 'new_value': 13559.3}, {'field': 'count', 'old_value': 15, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 10103.9, 'new_value': 13559.3}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 17}]
2025-05-26 08:07:09,209 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BM31
2025-05-26 08:07:09,209 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 1680.0, 'new_value': 2658.0}, {'field': 'amount', 'old_value': 1680.0, 'new_value': 2658.0}, {'field': 'count', 'old_value': 1, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 1680.0, 'new_value': 2658.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 3}]
2025-05-26 08:07:09,709 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BM51
2025-05-26 08:07:09,709 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_20250524, 变更字段: [{'field': 'amount', 'old_value': 7109.860000000001, 'new_value': 7113.06}, {'field': 'count', 'old_value': 243, 'new_value': 244}, {'field': 'onlineAmount', 'old_value': 3187.3, 'new_value': 3415.5}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 63}]
2025-05-26 08:07:10,162 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BM71
2025-05-26 08:07:10,162 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250524, 变更字段: [{'field': 'amount', 'old_value': 5460.43, 'new_value': 5625.43}, {'field': 'count', 'old_value': 397, 'new_value': 420}, {'field': 'onlineAmount', 'old_value': 5031.68, 'new_value': 5202.38}, {'field': 'onlineCount', 'old_value': 366, 'new_value': 389}]
2025-05-26 08:07:10,662 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BMA1
2025-05-26 08:07:10,662 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_20250524, 变更字段: [{'field': 'amount', 'old_value': 6965.23, 'new_value': 7773.679999999999}, {'field': 'count', 'old_value': 354, 'new_value': 355}, {'field': 'instoreAmount', 'old_value': 7115.43, 'new_value': 7923.88}, {'field': 'instoreCount', 'old_value': 352, 'new_value': 353}]
2025-05-26 08:07:11,115 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BMF1
2025-05-26 08:07:11,115 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_20250524, 变更字段: [{'field': 'amount', 'old_value': 4021.41, 'new_value': 4015.44}, {'field': 'count', 'old_value': 203, 'new_value': 204}, {'field': 'onlineAmount', 'old_value': 3462.41, 'new_value': 3469.41}, {'field': 'onlineCount', 'old_value': 157, 'new_value': 158}]
2025-05-26 08:07:11,553 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3SS1HW2BMN1
2025-05-26 08:07:11,553 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250524, 变更字段: [{'field': 'amount', 'old_value': 7481.42, 'new_value': 7500.42}, {'field': 'count', 'old_value': 552, 'new_value': 554}, {'field': 'instoreAmount', 'old_value': 4963.46, 'new_value': 5000.08}, {'field': 'instoreCount', 'old_value': 352, 'new_value': 358}, {'field': 'onlineAmount', 'old_value': 2673.62, 'new_value': 2656.0}, {'field': 'onlineCount', 'old_value': 200, 'new_value': 196}]
2025-05-26 08:07:12,006 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3TS1HW2BMV1
2025-05-26 08:07:12,006 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250524, 变更字段: [{'field': 'amount', 'old_value': 25479.87, 'new_value': 25577.77}, {'field': 'count', 'old_value': 511, 'new_value': 513}, {'field': 'onlineAmount', 'old_value': 1150.7, 'new_value': 1248.6}, {'field': 'onlineCount', 'old_value': 20, 'new_value': 22}]
2025-05-26 08:07:12,459 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3TS1HW2BMC2
2025-05-26 08:07:12,459 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_20250524, 变更字段: [{'field': 'amount', 'old_value': 25656.399999999998, 'new_value': 25634.1}]
2025-05-26 08:07:12,959 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3TS1HW2BMZ2
2025-05-26 08:07:12,959 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 4352.72, 'new_value': 4345.63}, {'field': 'amount', 'old_value': 4352.72, 'new_value': 4345.63}]
2025-05-26 08:07:13,443 - INFO - 更新表单数据成功: FINST-XMC66R91B3OVP1PVC8ERUAC9N0ZF28E4HW2BMQ6
2025-05-26 08:07:13,443 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 8687.91, 'new_value': 8658.61}, {'field': 'dailyBillAmount', 'old_value': 8687.91, 'new_value': 8658.61}]
2025-05-26 08:07:13,912 - INFO - 更新表单数据成功: FINST-XMC66R91B3OVP1PVC8ERUAC9N0ZF28E4HW2BMT6
2025-05-26 08:07:13,912 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250524, 变更字段: [{'field': 'amount', 'old_value': 26536.65, 'new_value': 28101.05}, {'field': 'count', 'old_value': 210, 'new_value': 215}, {'field': 'instoreAmount', 'old_value': 25351.7, 'new_value': 26916.1}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 155}]
2025-05-26 08:07:14,334 - INFO - 更新表单数据成功: FINST-XMC66R91B3OVP1PVC8ERUAC9N0ZF29E4HW2BMU7
2025-05-26 08:07:14,334 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250524, 变更字段: [{'field': 'amount', 'old_value': 24940.56, 'new_value': 27403.010000000002}, {'field': 'count', 'old_value': 133, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 22475.01, 'new_value': 24937.46}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 114}]
2025-05-26 08:07:14,928 - INFO - 更新表单数据成功: FINST-XMC66R91B3OVP1PVC8ERUAC9N0ZF29E4HW2BM58
2025-05-26 08:07:14,928 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_20250524, 变更字段: [{'field': 'amount', 'old_value': 57757.0, 'new_value': 58752.5}, {'field': 'count', 'old_value': 326, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 47718.2, 'new_value': 48713.7}, {'field': 'instoreCount', 'old_value': 276, 'new_value': 280}]
2025-05-26 08:07:15,412 - INFO - 更新表单数据成功: FINST-XMC66R91B3OVP1PVC8ERUAC9N0ZF29E4HW2BML8
2025-05-26 08:07:15,412 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_20250524, 变更字段: [{'field': 'amount', 'old_value': 148.2, 'new_value': 180.2}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 148.2, 'new_value': 180.2}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-05-26 08:07:15,475 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-26 08:07:15,990 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-26 08:07:19,006 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-26 08:07:19,459 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-26 08:07:22,475 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-26 08:07:22,896 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-26 08:07:25,912 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-26 08:07:26,381 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-26 08:07:29,396 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-26 08:07:29,771 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-26 08:07:32,787 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-26 08:07:33,193 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-26 08:07:36,209 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-26 08:07:36,568 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-26 08:07:39,584 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-26 08:07:39,959 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-26 08:07:42,974 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-26 08:07:43,365 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-26 08:07:46,381 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-26 08:07:46,818 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-26 08:07:49,834 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-26 08:07:50,334 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-26 08:07:53,349 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-26 08:07:53,756 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-26 08:07:56,771 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-26 08:07:57,177 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-26 08:08:00,193 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-26 08:08:00,818 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-26 08:08:03,834 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-26 08:08:04,334 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-26 08:08:07,349 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-26 08:08:07,787 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-26 08:08:10,802 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-26 08:08:11,287 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-26 08:08:14,302 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-26 08:08:14,693 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-26 08:08:17,709 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-26 08:08:18,162 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-26 08:08:21,162 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-26 08:08:21,537 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-26 08:08:24,552 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-26 08:08:24,959 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-26 08:08:27,974 - INFO - 正在批量插入每日数据，批次 22/22，共 59 条记录
2025-05-26 08:08:28,334 - INFO - 批量插入每日数据成功，批次 22，59 条记录
2025-05-26 08:08:31,349 - INFO - 批量插入每日数据完成: 总计 2159 条，成功 2159 条，失败 0 条
2025-05-26 08:08:31,349 - INFO - 批量插入日销售数据完成，共 2159 条记录
2025-05-26 08:08:31,349 - INFO - 日销售数据同步完成！更新: 48 条，插入: 2159 条，错误: 0 条，跳过: 10815 条
2025-05-26 08:08:31,349 - INFO - 正在获取宜搭月销售表单数据...
2025-05-26 08:08:31,349 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-26 08:08:31,349 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-26 08:08:31,349 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:31,349 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:31,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:31,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:31,865 - INFO - API请求耗时: 516ms
2025-05-26 08:08:31,865 - INFO - Response - Page 1
2025-05-26 08:08:31,865 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:31,865 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:31,865 - WARNING - 月度分段 1 查询返回空数据
2025-05-26 08:08:31,865 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-26 08:08:31,865 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:31,865 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:31,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:31,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:32,224 - INFO - API请求耗时: 359ms
2025-05-26 08:08:32,224 - INFO - Response - Page 1
2025-05-26 08:08:32,224 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:32,224 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:32,224 - WARNING - 单月查询返回空数据: 2024-05
2025-05-26 08:08:32,724 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-26 08:08:32,724 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:32,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:32,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:32,912 - INFO - API请求耗时: 187ms
2025-05-26 08:08:32,912 - INFO - Response - Page 1
2025-05-26 08:08:32,912 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:32,912 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:32,912 - WARNING - 单月查询返回空数据: 2024-06
2025-05-26 08:08:33,427 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:33,427 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:33,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:33,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:33,630 - INFO - API请求耗时: 203ms
2025-05-26 08:08:33,630 - INFO - Response - Page 1
2025-05-26 08:08:33,630 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:33,630 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:33,630 - WARNING - 单月查询返回空数据: 2024-07
2025-05-26 08:08:35,146 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-26 08:08:35,146 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:35,146 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:35,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:35,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:35,365 - INFO - API请求耗时: 219ms
2025-05-26 08:08:35,365 - INFO - Response - Page 1
2025-05-26 08:08:35,365 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:35,365 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:35,365 - WARNING - 月度分段 2 查询返回空数据
2025-05-26 08:08:35,365 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-26 08:08:35,365 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:35,365 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:35,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:35,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:35,583 - INFO - API请求耗时: 203ms
2025-05-26 08:08:35,583 - INFO - Response - Page 1
2025-05-26 08:08:35,583 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:35,583 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:35,583 - WARNING - 单月查询返回空数据: 2024-08
2025-05-26 08:08:36,099 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-26 08:08:36,099 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:36,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:36,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:36,333 - INFO - API请求耗时: 234ms
2025-05-26 08:08:36,333 - INFO - Response - Page 1
2025-05-26 08:08:36,333 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:36,333 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:36,333 - WARNING - 单月查询返回空数据: 2024-09
2025-05-26 08:08:36,849 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:36,849 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:36,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:36,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:37,052 - INFO - API请求耗时: 203ms
2025-05-26 08:08:37,052 - INFO - Response - Page 1
2025-05-26 08:08:37,052 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-26 08:08:37,052 - INFO - 查询完成，共获取到 0 条记录
2025-05-26 08:08:37,052 - WARNING - 单月查询返回空数据: 2024-10
2025-05-26 08:08:38,568 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-26 08:08:38,568 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:38,568 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:38,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:38,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:39,052 - INFO - API请求耗时: 484ms
2025-05-26 08:08:39,052 - INFO - Response - Page 1
2025-05-26 08:08:39,052 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:08:39,552 - INFO - Request Parameters - Page 2:
2025-05-26 08:08:39,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:39,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:40,349 - INFO - API请求耗时: 797ms
2025-05-26 08:08:40,349 - INFO - Response - Page 2
2025-05-26 08:08:40,349 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:08:40,849 - INFO - Request Parameters - Page 3:
2025-05-26 08:08:40,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:40,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:41,302 - INFO - API请求耗时: 453ms
2025-05-26 08:08:41,302 - INFO - Response - Page 3
2025-05-26 08:08:41,302 - INFO - 第 3 页获取到 48 条记录
2025-05-26 08:08:41,302 - INFO - 查询完成，共获取到 248 条记录
2025-05-26 08:08:41,302 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-26 08:08:42,318 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-26 08:08:42,318 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-26 08:08:42,318 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:42,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:42,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:42,880 - INFO - API请求耗时: 562ms
2025-05-26 08:08:42,880 - INFO - Response - Page 1
2025-05-26 08:08:42,880 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:08:43,396 - INFO - Request Parameters - Page 2:
2025-05-26 08:08:43,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:43,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:43,880 - INFO - API请求耗时: 484ms
2025-05-26 08:08:43,896 - INFO - Response - Page 2
2025-05-26 08:08:43,896 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:08:44,412 - INFO - Request Parameters - Page 3:
2025-05-26 08:08:44,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:44,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:45,005 - INFO - API请求耗时: 594ms
2025-05-26 08:08:45,005 - INFO - Response - Page 3
2025-05-26 08:08:45,005 - INFO - 第 3 页获取到 100 条记录
2025-05-26 08:08:45,521 - INFO - Request Parameters - Page 4:
2025-05-26 08:08:45,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:45,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:46,083 - INFO - API请求耗时: 562ms
2025-05-26 08:08:46,083 - INFO - Response - Page 4
2025-05-26 08:08:46,083 - INFO - 第 4 页获取到 100 条记录
2025-05-26 08:08:46,583 - INFO - Request Parameters - Page 5:
2025-05-26 08:08:46,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:46,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:47,271 - INFO - API请求耗时: 687ms
2025-05-26 08:08:47,271 - INFO - Response - Page 5
2025-05-26 08:08:47,271 - INFO - 第 5 页获取到 100 条记录
2025-05-26 08:08:47,771 - INFO - Request Parameters - Page 6:
2025-05-26 08:08:47,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:47,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:48,287 - INFO - API请求耗时: 516ms
2025-05-26 08:08:48,287 - INFO - Response - Page 6
2025-05-26 08:08:48,287 - INFO - 第 6 页获取到 100 条记录
2025-05-26 08:08:48,802 - INFO - Request Parameters - Page 7:
2025-05-26 08:08:48,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:48,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:49,318 - INFO - API请求耗时: 516ms
2025-05-26 08:08:49,318 - INFO - Response - Page 7
2025-05-26 08:08:49,318 - INFO - 第 7 页获取到 100 条记录
2025-05-26 08:08:49,818 - INFO - Request Parameters - Page 8:
2025-05-26 08:08:49,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:49,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:50,146 - INFO - API请求耗时: 328ms
2025-05-26 08:08:50,146 - INFO - Response - Page 8
2025-05-26 08:08:50,146 - INFO - 第 8 页获取到 16 条记录
2025-05-26 08:08:50,146 - INFO - 查询完成，共获取到 716 条记录
2025-05-26 08:08:50,161 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-26 08:08:51,177 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-26 08:08:51,177 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-26 08:08:51,177 - INFO - Request Parameters - Page 1:
2025-05-26 08:08:51,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:51,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:51,740 - INFO - API请求耗时: 562ms
2025-05-26 08:08:51,740 - INFO - Response - Page 1
2025-05-26 08:08:51,740 - INFO - 第 1 页获取到 100 条记录
2025-05-26 08:08:52,255 - INFO - Request Parameters - Page 2:
2025-05-26 08:08:52,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:52,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:52,833 - INFO - API请求耗时: 578ms
2025-05-26 08:08:52,833 - INFO - Response - Page 2
2025-05-26 08:08:52,833 - INFO - 第 2 页获取到 100 条记录
2025-05-26 08:08:53,333 - INFO - Request Parameters - Page 3:
2025-05-26 08:08:53,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-26 08:08:53,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-26 08:08:53,708 - INFO - API请求耗时: 375ms
2025-05-26 08:08:53,708 - INFO - Response - Page 3
2025-05-26 08:08:53,708 - INFO - 第 3 页获取到 24 条记录
2025-05-26 08:08:53,708 - INFO - 查询完成，共获取到 224 条记录
2025-05-26 08:08:53,708 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-26 08:08:54,724 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-26 08:08:54,724 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-26 08:08:54,724 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-26 08:08:54,724 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-26 08:08:54,724 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-26 08:08:54,786 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-26 08:08:55,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-26 08:08:55,240 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 170864.48, 'new_value': 180873.02}, {'field': 'dailyBillAmount', 'old_value': 170864.48, 'new_value': 180873.02}, {'field': 'amount', 'old_value': 4752.9, 'new_value': 4921.8}, {'field': 'count', 'old_value': 67, 'new_value': 70}, {'field': 'onlineAmount', 'old_value': 4828.9, 'new_value': 4997.8}, {'field': 'onlineCount', 'old_value': 67, 'new_value': 70}]
2025-05-26 08:08:55,724 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-26 08:08:55,724 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 441071.63, 'new_value': 462405.05}, {'field': 'dailyBillAmount', 'old_value': 441071.63, 'new_value': 462405.05}, {'field': 'amount', 'old_value': 236937.3, 'new_value': 245507.8}, {'field': 'count', 'old_value': 2224, 'new_value': 2300}, {'field': 'instoreAmount', 'old_value': 97158.4, 'new_value': 100476.3}, {'field': 'instoreCount', 'old_value': 748, 'new_value': 773}, {'field': 'onlineAmount', 'old_value': 140131.3, 'new_value': 145383.9}, {'field': 'onlineCount', 'old_value': 1476, 'new_value': 1527}]
2025-05-26 08:08:56,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-26 08:08:56,240 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 302496.88, 'new_value': 318173.63}, {'field': 'dailyBillAmount', 'old_value': 302496.88, 'new_value': 318173.63}, {'field': 'amount', 'old_value': 305220.51, 'new_value': 320959.82}, {'field': 'count', 'old_value': 2023, 'new_value': 2119}, {'field': 'instoreAmount', 'old_value': 289107.08, 'new_value': 304186.68}, {'field': 'instoreCount', 'old_value': 1790, 'new_value': 1876}, {'field': 'onlineAmount', 'old_value': 16355.63, 'new_value': 17015.34}, {'field': 'onlineCount', 'old_value': 233, 'new_value': 243}]
2025-05-26 08:08:56,693 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-26 08:08:56,693 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 651165.1, 'new_value': 677240.67}, {'field': 'dailyBillAmount', 'old_value': 651165.1, 'new_value': 677240.67}, {'field': 'amount', 'old_value': 471200.98, 'new_value': 490326.98}, {'field': 'count', 'old_value': 2272, 'new_value': 2371}, {'field': 'instoreAmount', 'old_value': 471200.98, 'new_value': 490326.98}, {'field': 'instoreCount', 'old_value': 2272, 'new_value': 2371}]
2025-05-26 08:08:57,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-26 08:08:57,115 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 511692.0, 'new_value': 538215.1}, {'field': 'dailyBillAmount', 'old_value': 511692.0, 'new_value': 538215.1}, {'field': 'amount', 'old_value': 844680.0, 'new_value': 883535.0}, {'field': 'count', 'old_value': 2915, 'new_value': 3058}, {'field': 'instoreAmount', 'old_value': 845930.0, 'new_value': 884785.0}, {'field': 'instoreCount', 'old_value': 2915, 'new_value': 3058}]
2025-05-26 08:08:57,583 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-26 08:08:57,583 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60909.8, 'new_value': 61223.7}, {'field': 'dailyBillAmount', 'old_value': 60909.8, 'new_value': 61223.7}, {'field': 'amount', 'old_value': 81204.01, 'new_value': 81698.81}, {'field': 'count', 'old_value': 303, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 43776.6, 'new_value': 43914.6}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 41524.32, 'new_value': 41881.12}, {'field': 'onlineCount', 'old_value': 259, 'new_value': 269}]
2025-05-26 08:08:58,068 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-26 08:08:58,068 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 213751.9, 'new_value': 217811.9}, {'field': 'amount', 'old_value': 213751.9, 'new_value': 217811.9}, {'field': 'count', 'old_value': 123, 'new_value': 126}, {'field': 'instoreAmount', 'old_value': 213751.9, 'new_value': 217811.9}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 126}]
2025-05-26 08:08:58,536 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-26 08:08:58,536 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 707570.42, 'new_value': 739744.52}, {'field': 'dailyBillAmount', 'old_value': 707570.42, 'new_value': 739744.52}, {'field': 'amount', 'old_value': 635882.35, 'new_value': 658132.85}, {'field': 'count', 'old_value': 4508, 'new_value': 4646}, {'field': 'instoreAmount', 'old_value': 522587.11, 'new_value': 542592.41}, {'field': 'instoreCount', 'old_value': 2242, 'new_value': 2331}, {'field': 'onlineAmount', 'old_value': 117280.07, 'new_value': 119547.67}, {'field': 'onlineCount', 'old_value': 2266, 'new_value': 2315}]
2025-05-26 08:08:58,943 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-26 08:08:58,943 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 707050.4299999999, 'new_value': 737272.69}, {'field': 'dailyBillAmount', 'old_value': 707050.4299999999, 'new_value': 737272.69}, {'field': 'amount', 'old_value': 200133.5, 'new_value': 205080.04}, {'field': 'count', 'old_value': 1136, 'new_value': 1165}, {'field': 'instoreAmount', 'old_value': 200133.5, 'new_value': 205080.04}, {'field': 'instoreCount', 'old_value': 1136, 'new_value': 1165}]
2025-05-26 08:08:59,427 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-26 08:08:59,427 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 48698.0, 'new_value': 50604.0}, {'field': 'count', 'old_value': 70, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 48698.0, 'new_value': 50604.0}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 73}]
2025-05-26 08:08:59,880 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-26 08:08:59,880 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 103593.2, 'new_value': 107022.2}, {'field': 'count', 'old_value': 289, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 103594.9, 'new_value': 107023.9}, {'field': 'instoreCount', 'old_value': 289, 'new_value': 297}]
2025-05-26 08:09:00,333 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-26 08:09:00,333 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32567.9, 'new_value': 34866.9}, {'field': 'amount', 'old_value': 32567.9, 'new_value': 34866.9}, {'field': 'count', 'old_value': 29, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 33963.9, 'new_value': 36262.9}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 31}]
2025-05-26 08:09:00,802 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-26 08:09:00,802 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 971382.79, 'new_value': 1017795.5599999999}, {'field': 'dailyBillAmount', 'old_value': 971382.79, 'new_value': 1017795.5599999999}, {'field': 'amount', 'old_value': -383117.34, 'new_value': -368715.41000000003}, {'field': 'count', 'old_value': 1019, 'new_value': 1113}, {'field': 'instoreAmount', 'old_value': 607015.87, 'new_value': 655923.81}, {'field': 'instoreCount', 'old_value': 1019, 'new_value': 1113}]
2025-05-26 08:09:01,271 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-26 08:09:01,271 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 413260.0, 'new_value': 435506.0}, {'field': 'amount', 'old_value': 413260.0, 'new_value': 435506.0}, {'field': 'count', 'old_value': 1359, 'new_value': 1432}, {'field': 'instoreAmount', 'old_value': 413260.0, 'new_value': 435506.0}, {'field': 'instoreCount', 'old_value': 1359, 'new_value': 1432}]
2025-05-26 08:09:01,755 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-26 08:09:01,755 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 385560.95, 'new_value': 401310.04}, {'field': 'dailyBillAmount', 'old_value': 294362.05, 'new_value': 309085.54}, {'field': 'amount', 'old_value': 385560.95, 'new_value': 401310.04}, {'field': 'count', 'old_value': 1313, 'new_value': 1359}, {'field': 'instoreAmount', 'old_value': 385560.95, 'new_value': 401310.04}, {'field': 'instoreCount', 'old_value': 1313, 'new_value': 1359}]
2025-05-26 08:09:02,177 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-26 08:09:02,177 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 192952.53, 'new_value': 202055.23}, {'field': 'dailyBillAmount', 'old_value': 192952.53, 'new_value': 202055.23}, {'field': 'amount', 'old_value': 13432.4, 'new_value': 13677.4}, {'field': 'count', 'old_value': 98, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 15845.9, 'new_value': 16090.9}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 101}]
2025-05-26 08:09:02,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-26 08:09:02,646 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99646.4, 'new_value': 104895.18}, {'field': 'dailyBillAmount', 'old_value': 99646.4, 'new_value': 104895.18}, {'field': 'amount', 'old_value': 61130.95, 'new_value': 64375.87}, {'field': 'count', 'old_value': 904, 'new_value': 957}, {'field': 'instoreAmount', 'old_value': 63081.85, 'new_value': 66341.77}, {'field': 'instoreCount', 'old_value': 904, 'new_value': 957}]
2025-05-26 08:09:03,099 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-26 08:09:03,099 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 143720.52, 'new_value': 149718.91999999998}, {'field': 'amount', 'old_value': 143719.66, 'new_value': 149718.06}, {'field': 'count', 'old_value': 4930, 'new_value': 5115}, {'field': 'instoreAmount', 'old_value': 125159.6, 'new_value': 130468.16}, {'field': 'instoreCount', 'old_value': 4467, 'new_value': 4634}, {'field': 'onlineAmount', 'old_value': 18560.92, 'new_value': 19250.76}, {'field': 'onlineCount', 'old_value': 463, 'new_value': 481}]
2025-05-26 08:09:03,708 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-26 08:09:03,708 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 299149.22, 'new_value': 308248.22}, {'field': 'dailyBillAmount', 'old_value': 293759.0, 'new_value': 302858.0}, {'field': 'amount', 'old_value': 248091.01, 'new_value': 254582.01}, {'field': 'count', 'old_value': 231, 'new_value': 239}, {'field': 'instoreAmount', 'old_value': 247858.0, 'new_value': 254349.0}, {'field': 'instoreCount', 'old_value': 228, 'new_value': 236}]
2025-05-26 08:09:04,271 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-26 08:09:04,271 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 560882.72, 'new_value': 591980.22}, {'field': 'dailyBillAmount', 'old_value': 560378.17, 'new_value': 591475.67}, {'field': 'amount', 'old_value': 560882.72, 'new_value': 591980.22}, {'field': 'count', 'old_value': 501, 'new_value': 522}, {'field': 'instoreAmount', 'old_value': 560883.72, 'new_value': 591981.22}, {'field': 'instoreCount', 'old_value': 501, 'new_value': 522}]
2025-05-26 08:09:04,724 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-26 08:09:04,724 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 37295.0, 'new_value': 40575.0}, {'field': 'count', 'old_value': 65, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 37295.0, 'new_value': 40575.0}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 69}]
2025-05-26 08:09:05,177 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-26 08:09:05,177 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 101506.0, 'new_value': 106342.5}, {'field': 'dailyBillAmount', 'old_value': 101506.0, 'new_value': 106342.5}, {'field': 'amount', 'old_value': 111126.2, 'new_value': 115553.9}, {'field': 'count', 'old_value': 298, 'new_value': 308}, {'field': 'instoreAmount', 'old_value': 111132.09999999999, 'new_value': 115559.8}, {'field': 'instoreCount', 'old_value': 298, 'new_value': 308}]
2025-05-26 08:09:05,583 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-26 08:09:05,583 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 153783.7, 'new_value': 159289.7}, {'field': 'amount', 'old_value': 153783.7, 'new_value': 159289.7}, {'field': 'count', 'old_value': 184, 'new_value': 191}, {'field': 'instoreAmount', 'old_value': 153910.7, 'new_value': 159416.7}, {'field': 'instoreCount', 'old_value': 184, 'new_value': 191}]
2025-05-26 08:09:06,068 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-26 08:09:06,068 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221711.16, 'new_value': 236236.38}, {'field': 'dailyBillAmount', 'old_value': 221711.16, 'new_value': 236236.38}, {'field': 'amount', 'old_value': 233334.55, 'new_value': 248714.55}, {'field': 'count', 'old_value': 1558, 'new_value': 1646}, {'field': 'instoreAmount', 'old_value': 234543.55, 'new_value': 249923.55}, {'field': 'instoreCount', 'old_value': 1558, 'new_value': 1646}]
2025-05-26 08:09:06,536 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-26 08:09:06,536 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151996.95, 'new_value': 152122.52}, {'field': 'dailyBillAmount', 'old_value': 151996.95, 'new_value': 152122.52}, {'field': 'amount', 'old_value': 15102.83, 'new_value': 15080.92}, {'field': 'count', 'old_value': 1367, 'new_value': 1381}, {'field': 'instoreAmount', 'old_value': 20016.739999999998, 'new_value': 20160.93}, {'field': 'instoreCount', 'old_value': 1367, 'new_value': 1381}]
2025-05-26 08:09:07,083 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-26 08:09:07,083 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 271244.11, 'new_value': 284204.31}, {'field': 'amount', 'old_value': 271239.96, 'new_value': 284200.16000000003}, {'field': 'count', 'old_value': 6297, 'new_value': 6576}, {'field': 'instoreAmount', 'old_value': 264674.56, 'new_value': 277470.66}, {'field': 'instoreCount', 'old_value': 6073, 'new_value': 6345}, {'field': 'onlineAmount', 'old_value': 10629.43, 'new_value': 10894.53}, {'field': 'onlineCount', 'old_value': 224, 'new_value': 231}]
2025-05-26 08:09:07,458 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-26 08:09:07,458 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231137.7, 'new_value': 241128.2}, {'field': 'dailyBillAmount', 'old_value': 231137.7, 'new_value': 241128.2}, {'field': 'amount', 'old_value': 231137.7, 'new_value': 241128.2}, {'field': 'count', 'old_value': 691, 'new_value': 722}, {'field': 'instoreAmount', 'old_value': 231137.7, 'new_value': 241128.2}, {'field': 'instoreCount', 'old_value': 691, 'new_value': 722}]
2025-05-26 08:09:07,911 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-26 08:09:07,911 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 202160.65, 'new_value': 214732.2}, {'field': 'dailyBillAmount', 'old_value': 202160.65, 'new_value': 214732.2}, {'field': 'amount', 'old_value': 69531.2, 'new_value': 71430.2}, {'field': 'count', 'old_value': 162, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 69531.2, 'new_value': 71430.2}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 166}]
2025-05-26 08:09:08,349 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-26 08:09:08,349 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 406534.57, 'new_value': 424052.34}, {'field': 'dailyBillAmount', 'old_value': 406534.57, 'new_value': 424052.34}, {'field': 'amount', 'old_value': 167727.3, 'new_value': 175849.6}, {'field': 'count', 'old_value': 622, 'new_value': 655}, {'field': 'instoreAmount', 'old_value': 167727.56, 'new_value': 175849.86}, {'field': 'instoreCount', 'old_value': 622, 'new_value': 655}]
2025-05-26 08:09:08,755 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-26 08:09:08,755 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87965.66, 'new_value': 91422.53}, {'field': 'dailyBillAmount', 'old_value': 87965.66, 'new_value': 91422.53}, {'field': 'amount', 'old_value': 26317.87, 'new_value': 27668.46}, {'field': 'count', 'old_value': 968, 'new_value': 1007}, {'field': 'instoreAmount', 'old_value': 6126.83, 'new_value': 6407.03}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 169}, {'field': 'onlineAmount', 'old_value': 20461.94, 'new_value': 21561.77}, {'field': 'onlineCount', 'old_value': 804, 'new_value': 838}]
2025-05-26 08:09:09,239 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-26 08:09:09,239 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 140393.06, 'new_value': 146427.94}, {'field': 'dailyBillAmount', 'old_value': 140393.06, 'new_value': 146427.94}, {'field': 'amount', 'old_value': 23465.76, 'new_value': 24598.36}, {'field': 'count', 'old_value': 562, 'new_value': 590}, {'field': 'instoreAmount', 'old_value': 20406.95, 'new_value': 21396.25}, {'field': 'instoreCount', 'old_value': 501, 'new_value': 525}, {'field': 'onlineAmount', 'old_value': 3059.5, 'new_value': 3202.8}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 65}]
2025-05-26 08:09:09,661 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-26 08:09:09,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19550.43, 'new_value': 21021.43}, {'field': 'dailyBillAmount', 'old_value': 19550.43, 'new_value': 21021.43}, {'field': 'amount', 'old_value': 15186.58, 'new_value': 16457.58}, {'field': 'count', 'old_value': 558, 'new_value': 601}, {'field': 'instoreAmount', 'old_value': 15554.18, 'new_value': 16825.18}, {'field': 'instoreCount', 'old_value': 558, 'new_value': 601}]
2025-05-26 08:09:10,161 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-26 08:09:10,161 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44683.26, 'new_value': 46910.26}, {'field': 'dailyBillAmount', 'old_value': 44683.26, 'new_value': 46910.26}, {'field': 'amount', 'old_value': 28741.67, 'new_value': 29911.47}, {'field': 'count', 'old_value': 1537, 'new_value': 1600}, {'field': 'instoreAmount', 'old_value': 14696.72, 'new_value': 15348.72}, {'field': 'instoreCount', 'old_value': 619, 'new_value': 650}, {'field': 'onlineAmount', 'old_value': 14903.07, 'new_value': 15475.87}, {'field': 'onlineCount', 'old_value': 918, 'new_value': 950}]
2025-05-26 08:09:10,568 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-26 08:09:10,568 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 313795.26, 'new_value': 326567.19}, {'field': 'dailyBillAmount', 'old_value': 313795.26, 'new_value': 326567.19}, {'field': 'amount', 'old_value': 142866.46, 'new_value': 149645.06}, {'field': 'count', 'old_value': 596, 'new_value': 624}, {'field': 'instoreAmount', 'old_value': 147449.92, 'new_value': 154232.9}, {'field': 'instoreCount', 'old_value': 596, 'new_value': 624}]
2025-05-26 08:09:11,052 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-26 08:09:11,052 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 17814.38, 'new_value': 18382.55}, {'field': 'count', 'old_value': 152, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 17888.62, 'new_value': 18456.79}, {'field': 'instoreCount', 'old_value': 152, 'new_value': 158}]
2025-05-26 08:09:11,521 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-26 08:09:11,521 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194391.52, 'new_value': 202278.18}, {'field': 'dailyBillAmount', 'old_value': 194391.52, 'new_value': 202278.18}, {'field': 'amount', 'old_value': 94583.14, 'new_value': 98325.5}, {'field': 'count', 'old_value': 4023, 'new_value': 4191}, {'field': 'instoreAmount', 'old_value': 96516.18, 'new_value': 100365.84}, {'field': 'instoreCount', 'old_value': 4023, 'new_value': 4191}]
2025-05-26 08:09:11,989 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-26 08:09:11,989 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 453968.6, 'new_value': 477938.8}, {'field': 'dailyBillAmount', 'old_value': 453968.6, 'new_value': 477938.8}, {'field': 'amount', 'old_value': 453968.6, 'new_value': 477938.8}, {'field': 'count', 'old_value': 579, 'new_value': 609}, {'field': 'instoreAmount', 'old_value': 453968.6, 'new_value': 477938.8}, {'field': 'instoreCount', 'old_value': 579, 'new_value': 609}]
2025-05-26 08:09:12,427 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-26 08:09:12,427 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 202449.59, 'new_value': 212526.39}, {'field': 'dailyBillAmount', 'old_value': 202449.59, 'new_value': 212526.39}, {'field': 'amount', 'old_value': 115128.65, 'new_value': 122530.35}, {'field': 'count', 'old_value': 301, 'new_value': 321}, {'field': 'instoreAmount', 'old_value': 116545.25, 'new_value': 123946.95}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 321}]
2025-05-26 08:09:12,864 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-26 08:09:12,864 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50451.0, 'new_value': 52145.0}, {'field': 'dailyBillAmount', 'old_value': 50451.0, 'new_value': 52145.0}, {'field': 'amount', 'old_value': 50451.0, 'new_value': 52145.0}, {'field': 'count', 'old_value': 995, 'new_value': 1022}, {'field': 'instoreAmount', 'old_value': 50490.0, 'new_value': 52184.0}, {'field': 'instoreCount', 'old_value': 995, 'new_value': 1022}]
2025-05-26 08:09:13,302 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-26 08:09:13,302 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87366.79, 'new_value': 91219.85}, {'field': 'dailyBillAmount', 'old_value': 87366.79, 'new_value': 91219.85}, {'field': 'amount', 'old_value': 90316.21, 'new_value': 94342.78}, {'field': 'count', 'old_value': 4775, 'new_value': 4974}, {'field': 'instoreAmount', 'old_value': 43499.05, 'new_value': 45591.31}, {'field': 'instoreCount', 'old_value': 2177, 'new_value': 2280}, {'field': 'onlineAmount', 'old_value': 48075.1, 'new_value': 50039.56}, {'field': 'onlineCount', 'old_value': 2598, 'new_value': 2694}]
2025-05-26 08:09:13,724 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-26 08:09:13,724 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30931.19, 'new_value': 32283.69}, {'field': 'dailyBillAmount', 'old_value': 30931.19, 'new_value': 32283.69}, {'field': 'amount', 'old_value': 42381.54, 'new_value': 44057.64}, {'field': 'count', 'old_value': 1239, 'new_value': 1281}, {'field': 'instoreAmount', 'old_value': 38621.89, 'new_value': 40297.99}, {'field': 'instoreCount', 'old_value': 1088, 'new_value': 1130}]
2025-05-26 08:09:14,255 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-26 08:09:14,255 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63511.63, 'new_value': 66139.13}, {'field': 'dailyBillAmount', 'old_value': 63511.63, 'new_value': 66139.13}, {'field': 'amount', 'old_value': 63384.14, 'new_value': 66037.83}, {'field': 'count', 'old_value': 2465, 'new_value': 2561}, {'field': 'instoreAmount', 'old_value': 41368.92, 'new_value': 42986.44}, {'field': 'instoreCount', 'old_value': 1470, 'new_value': 1525}, {'field': 'onlineAmount', 'old_value': 22315.85, 'new_value': 23352.02}, {'field': 'onlineCount', 'old_value': 995, 'new_value': 1036}]
2025-05-26 08:09:14,661 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-26 08:09:14,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 61951.79, 'new_value': 64303.48}, {'field': 'count', 'old_value': 764, 'new_value': 799}, {'field': 'instoreAmount', 'old_value': 62416.69, 'new_value': 64768.38}, {'field': 'instoreCount', 'old_value': 764, 'new_value': 799}]
2025-05-26 08:09:15,083 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-26 08:09:15,083 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68160.6, 'new_value': 71151.9}, {'field': 'amount', 'old_value': 68160.1, 'new_value': 71151.4}, {'field': 'count', 'old_value': 1733, 'new_value': 1826}, {'field': 'instoreAmount', 'old_value': 69209.6, 'new_value': 72221.7}, {'field': 'instoreCount', 'old_value': 1733, 'new_value': 1826}]
2025-05-26 08:09:15,521 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-26 08:09:15,521 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 316607.42, 'new_value': 332987.42}, {'field': 'dailyBillAmount', 'old_value': 316607.42, 'new_value': 332987.42}, {'field': 'amount', 'old_value': 98428.82, 'new_value': 103269.82}, {'field': 'count', 'old_value': 351, 'new_value': 364}, {'field': 'instoreAmount', 'old_value': 98428.82, 'new_value': 103269.82}, {'field': 'instoreCount', 'old_value': 351, 'new_value': 364}]
2025-05-26 08:09:16,036 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-26 08:09:16,036 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85162.66, 'new_value': 88927.76}, {'field': 'dailyBillAmount', 'old_value': 85162.66, 'new_value': 88927.76}, {'field': 'amount', 'old_value': 86464.46, 'new_value': 90229.56}, {'field': 'count', 'old_value': 316, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 88599.09, 'new_value': 92364.19}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 330}]
2025-05-26 08:09:16,474 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-26 08:09:16,489 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49120.0, 'new_value': 51219.0}, {'field': 'dailyBillAmount', 'old_value': 49120.0, 'new_value': 51219.0}, {'field': 'amount', 'old_value': 60815.0, 'new_value': 63388.0}, {'field': 'count', 'old_value': 117, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 65770.0, 'new_value': 68343.0}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 121}]
2025-05-26 08:09:16,911 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-26 08:09:16,927 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85736.15, 'new_value': 89042.15}, {'field': 'dailyBillAmount', 'old_value': 83242.95, 'new_value': 86548.95}, {'field': 'amount', 'old_value': 85733.55, 'new_value': 89039.55}, {'field': 'count', 'old_value': 264, 'new_value': 274}, {'field': 'instoreAmount', 'old_value': 96626.25, 'new_value': 99932.25}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 274}]
2025-05-26 08:09:17,380 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-26 08:09:17,380 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114629.13, 'new_value': 121123.78}, {'field': 'dailyBillAmount', 'old_value': 114629.13, 'new_value': 121123.78}, {'field': 'amount', 'old_value': 64166.0, 'new_value': 67587.8}, {'field': 'count', 'old_value': 1756, 'new_value': 1844}, {'field': 'instoreAmount', 'old_value': 55808.04, 'new_value': 59073.74}, {'field': 'instoreCount', 'old_value': 1480, 'new_value': 1563}, {'field': 'onlineAmount', 'old_value': 9461.92, 'new_value': 9642.57}, {'field': 'onlineCount', 'old_value': 276, 'new_value': 281}]
2025-05-26 08:09:17,943 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-26 08:09:17,943 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 164787.19, 'new_value': 168828.42}, {'field': 'dailyBillAmount', 'old_value': 159652.47, 'new_value': 163662.5}, {'field': 'amount', 'old_value': 164787.19, 'new_value': 168828.42}, {'field': 'count', 'old_value': 2022, 'new_value': 2075}, {'field': 'instoreAmount', 'old_value': 156653.85, 'new_value': 160654.85}, {'field': 'instoreCount', 'old_value': 1928, 'new_value': 1980}, {'field': 'onlineAmount', 'old_value': 8192.58, 'new_value': 8232.81}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 95}]
2025-05-26 08:09:18,427 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-26 08:09:18,427 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73600.02, 'new_value': 77856.41}, {'field': 'dailyBillAmount', 'old_value': 73600.02, 'new_value': 77856.41}, {'field': 'amount', 'old_value': 99948.34, 'new_value': 104136.43}, {'field': 'count', 'old_value': 440, 'new_value': 462}, {'field': 'instoreAmount', 'old_value': 96430.9, 'new_value': 100539.45999999999}, {'field': 'instoreCount', 'old_value': 393, 'new_value': 414}, {'field': 'onlineAmount', 'old_value': 3655.44, 'new_value': 3734.97}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 48}]
2025-05-26 08:09:18,864 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-26 08:09:18,864 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 192239.3, 'new_value': 198482.3}, {'field': 'dailyBillAmount', 'old_value': 192239.3, 'new_value': 198482.3}, {'field': 'amount', 'old_value': 195991.8, 'new_value': 202304.8}, {'field': 'count', 'old_value': 721, 'new_value': 745}, {'field': 'instoreAmount', 'old_value': 198876.7, 'new_value': 205189.7}, {'field': 'instoreCount', 'old_value': 721, 'new_value': 745}]
2025-05-26 08:09:19,302 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-26 08:09:19,302 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44028.0, 'new_value': 45989.0}, {'field': 'dailyBillAmount', 'old_value': 44028.0, 'new_value': 45989.0}, {'field': 'amount', 'old_value': 41226.0, 'new_value': 43187.0}, {'field': 'count', 'old_value': 105, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 41819.0, 'new_value': 43780.0}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 109}]
2025-05-26 08:09:19,771 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-26 08:09:19,771 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24329.9, 'new_value': 25787.2}, {'field': 'dailyBillAmount', 'old_value': 24329.9, 'new_value': 25787.2}, {'field': 'amount', 'old_value': 18552.31, 'new_value': 19511.51}, {'field': 'count', 'old_value': 831, 'new_value': 866}, {'field': 'instoreAmount', 'old_value': 18745.36, 'new_value': 19734.76}, {'field': 'instoreCount', 'old_value': 831, 'new_value': 866}]
2025-05-26 08:09:20,224 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-26 08:09:20,224 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46370.6, 'new_value': 47384.49}, {'field': 'amount', 'old_value': 46368.96, 'new_value': 47382.85}, {'field': 'count', 'old_value': 2310, 'new_value': 2375}, {'field': 'instoreAmount', 'old_value': 53360.8, 'new_value': 54709.99}, {'field': 'instoreCount', 'old_value': 2310, 'new_value': 2375}]
2025-05-26 08:09:20,661 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-26 08:09:20,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127716.17, 'new_value': 133374.97}, {'field': 'dailyBillAmount', 'old_value': 127716.17, 'new_value': 133374.97}, {'field': 'amount', 'old_value': 102584.2, 'new_value': 106770.2}, {'field': 'count', 'old_value': 416, 'new_value': 437}, {'field': 'instoreAmount', 'old_value': 102584.2, 'new_value': 106770.2}, {'field': 'instoreCount', 'old_value': 415, 'new_value': 436}]
2025-05-26 08:09:21,099 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-26 08:09:21,099 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 364436.88, 'new_value': 377995.77999999997}, {'field': 'dailyBillAmount', 'old_value': 364436.88, 'new_value': 377995.77999999997}, {'field': 'amount', 'old_value': 210639.26, 'new_value': 216651.88999999998}, {'field': 'count', 'old_value': 2427, 'new_value': 2499}, {'field': 'instoreAmount', 'old_value': 92747.27, 'new_value': 95197.3}, {'field': 'instoreCount', 'old_value': 1044, 'new_value': 1074}, {'field': 'onlineAmount', 'old_value': 117894.06, 'new_value': 121457.06}, {'field': 'onlineCount', 'old_value': 1383, 'new_value': 1425}]
2025-05-26 08:09:21,568 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-26 08:09:21,568 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 211451.33, 'new_value': 223298.75}, {'field': 'dailyBillAmount', 'old_value': 211451.33, 'new_value': 223298.75}, {'field': 'amount', 'old_value': 224533.19999999998, 'new_value': 235618.19999999998}, {'field': 'count', 'old_value': 1367, 'new_value': 1437}, {'field': 'instoreAmount', 'old_value': 225313.1, 'new_value': 236398.1}, {'field': 'instoreCount', 'old_value': 1367, 'new_value': 1437}]
2025-05-26 08:09:22,083 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-26 08:09:22,083 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74547.89, 'new_value': 74636.89}, {'field': 'amount', 'old_value': 74547.89, 'new_value': 74636.89}, {'field': 'count', 'old_value': 32, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 74547.89, 'new_value': 74636.89}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 33}]
2025-05-26 08:09:22,505 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-26 08:09:22,505 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2151.4, 'new_value': 2607.05}, {'field': 'count', 'old_value': 28, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 2151.4, 'new_value': 2607.05}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 33}]
2025-05-26 08:09:22,927 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-26 08:09:22,927 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 190686.45, 'new_value': 199484.04}, {'field': 'dailyBillAmount', 'old_value': 185880.7, 'new_value': 194678.29}, {'field': 'amount', 'old_value': 190686.45, 'new_value': 199484.04}, {'field': 'count', 'old_value': 803, 'new_value': 839}, {'field': 'instoreAmount', 'old_value': 190686.45, 'new_value': 199484.04}, {'field': 'instoreCount', 'old_value': 803, 'new_value': 839}]
2025-05-26 08:09:23,302 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-26 08:09:23,302 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23033.98, 'new_value': 24299.26}, {'field': 'dailyBillAmount', 'old_value': 23033.98, 'new_value': 24299.26}, {'field': 'amount', 'old_value': 27550.88, 'new_value': 28951.760000000002}, {'field': 'count', 'old_value': 803, 'new_value': 853}, {'field': 'instoreAmount', 'old_value': 27570.68, 'new_value': 28971.56}, {'field': 'instoreCount', 'old_value': 803, 'new_value': 853}]
2025-05-26 08:09:23,802 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-26 08:09:23,802 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 291437.8, 'new_value': 308123.8}, {'field': 'amount', 'old_value': 291437.8, 'new_value': 308123.8}, {'field': 'count', 'old_value': 453, 'new_value': 475}, {'field': 'instoreAmount', 'old_value': 291437.8, 'new_value': 308123.8}, {'field': 'instoreCount', 'old_value': 453, 'new_value': 475}]
2025-05-26 08:09:24,239 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-26 08:09:24,239 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44419.32, 'new_value': 45804.33}, {'field': 'amount', 'old_value': 44419.32, 'new_value': 45804.33}, {'field': 'count', 'old_value': 364, 'new_value': 385}, {'field': 'instoreAmount', 'old_value': 44419.32, 'new_value': 45804.33}, {'field': 'instoreCount', 'old_value': 364, 'new_value': 385}]
2025-05-26 08:09:24,661 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-26 08:09:24,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 297226.0, 'new_value': 311410.0}, {'field': 'amount', 'old_value': 297226.0, 'new_value': 311410.0}, {'field': 'count', 'old_value': 66, 'new_value': 71}, {'field': 'instoreAmount', 'old_value': 297226.0, 'new_value': 311410.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 71}]
2025-05-26 08:09:25,068 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-26 08:09:25,068 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 878.2, 'new_value': 9034.5}, {'field': 'dailyBillAmount', 'old_value': 878.2, 'new_value': 9034.5}, {'field': 'amount', 'old_value': 42565.05, 'new_value': 50944.99}, {'field': 'count', 'old_value': 506, 'new_value': 530}, {'field': 'instoreAmount', 'old_value': 42565.05, 'new_value': 50944.99}, {'field': 'instoreCount', 'old_value': 506, 'new_value': 530}]
2025-05-26 08:09:25,536 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-26 08:09:25,536 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40359.9, 'new_value': 41516.9}, {'field': 'dailyBillAmount', 'old_value': 40359.9, 'new_value': 41516.9}, {'field': 'amount', 'old_value': 41823.2, 'new_value': 42980.2}, {'field': 'count', 'old_value': 52, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 42721.2, 'new_value': 43878.2}, {'field': 'instoreCount', 'old_value': 52, 'new_value': 53}]
2025-05-26 08:09:26,005 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-26 08:09:26,005 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 416260.08, 'new_value': 442132.08}, {'field': 'dailyBillAmount', 'old_value': 416260.08, 'new_value': 442132.08}, {'field': 'amount', 'old_value': 423431.08, 'new_value': 449303.08}, {'field': 'count', 'old_value': 1348, 'new_value': 1433}, {'field': 'instoreAmount', 'old_value': 423431.08, 'new_value': 449303.08}, {'field': 'instoreCount', 'old_value': 1348, 'new_value': 1433}]
2025-05-26 08:09:26,458 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-26 08:09:26,458 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 978149.24, 'new_value': 1023057.84}, {'field': 'count', 'old_value': 1229, 'new_value': 1286}, {'field': 'instoreAmount', 'old_value': 978149.41, 'new_value': 1023058.01}, {'field': 'instoreCount', 'old_value': 1229, 'new_value': 1286}]
2025-05-26 08:09:26,943 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMH01
2025-05-26 08:09:26,943 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 188.0, 'new_value': 792.0}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 188.0, 'new_value': 792.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-05-26 08:09:27,443 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-26 08:09:27,443 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144731.8, 'new_value': 152938.3}, {'field': 'dailyBillAmount', 'old_value': 144731.8, 'new_value': 152938.3}, {'field': 'amount', 'old_value': 30351.8, 'new_value': 32264.7}, {'field': 'count', 'old_value': 117, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 30353.3, 'new_value': 32266.2}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 125}]
2025-05-26 08:09:27,974 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-26 08:09:27,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 189799.99, 'new_value': 198549.48}, {'field': 'amount', 'old_value': 189797.47, 'new_value': 198546.96}, {'field': 'count', 'old_value': 1990, 'new_value': 2086}, {'field': 'instoreAmount', 'old_value': 122837.87, 'new_value': 127617.22}, {'field': 'instoreCount', 'old_value': 1131, 'new_value': 1179}, {'field': 'onlineAmount', 'old_value': 72007.85, 'new_value': 76286.46}, {'field': 'onlineCount', 'old_value': 859, 'new_value': 907}]
2025-05-26 08:09:28,505 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-26 08:09:28,505 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 318141.66, 'new_value': 334742.17}, {'field': 'dailyBillAmount', 'old_value': 318141.66, 'new_value': 334742.17}, {'field': 'amount', 'old_value': 28519.93, 'new_value': 29894.36}, {'field': 'count', 'old_value': 877, 'new_value': 918}, {'field': 'instoreAmount', 'old_value': 33304.4, 'new_value': 35049.43}, {'field': 'instoreCount', 'old_value': 877, 'new_value': 918}]
2025-05-26 08:09:28,958 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-26 08:09:28,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 294480.33, 'new_value': 311721.18}, {'field': 'dailyBillAmount', 'old_value': 294480.33, 'new_value': 311721.18}, {'field': 'amount', 'old_value': 150806.52, 'new_value': 158450.03}, {'field': 'count', 'old_value': 3414, 'new_value': 3585}, {'field': 'instoreAmount', 'old_value': 125861.40000000001, 'new_value': 132327.54}, {'field': 'instoreCount', 'old_value': 2848, 'new_value': 2993}, {'field': 'onlineAmount', 'old_value': 27165.68, 'new_value': 28343.05}, {'field': 'onlineCount', 'old_value': 566, 'new_value': 592}]
2025-05-26 08:09:29,458 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-26 08:09:29,458 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 263523.4, 'new_value': 277438.4}, {'field': 'amount', 'old_value': 263521.8, 'new_value': 277436.8}, {'field': 'count', 'old_value': 1057, 'new_value': 1104}, {'field': 'instoreAmount', 'old_value': 266613.3, 'new_value': 280647.3}, {'field': 'instoreCount', 'old_value': 1057, 'new_value': 1104}]
2025-05-26 08:09:29,958 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-26 08:09:29,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 435269.41, 'new_value': 454542.25}, {'field': 'count', 'old_value': 8207, 'new_value': 8580}, {'field': 'instoreAmount', 'old_value': 406350.69, 'new_value': 424765.57}, {'field': 'instoreCount', 'old_value': 7645, 'new_value': 8002}, {'field': 'onlineAmount', 'old_value': 30690.56, 'new_value': 31549.29}, {'field': 'onlineCount', 'old_value': 562, 'new_value': 578}]
2025-05-26 08:09:30,380 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-26 08:09:30,380 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 340345.32, 'new_value': 372468.42}, {'field': 'amount', 'old_value': 308060.05, 'new_value': 340183.15}, {'field': 'count', 'old_value': 7380, 'new_value': 8018}, {'field': 'instoreAmount', 'old_value': 243212.5, 'new_value': 265525.1}, {'field': 'instoreCount', 'old_value': 5328, 'new_value': 5752}, {'field': 'onlineAmount', 'old_value': 65007.35, 'new_value': 74817.85}, {'field': 'onlineCount', 'old_value': 2052, 'new_value': 2266}]
2025-05-26 08:09:30,849 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-26 08:09:30,849 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 31196.579999999998, 'new_value': 30723.579999999998}, {'field': 'count', 'old_value': 834, 'new_value': 838}, {'field': 'instoreAmount', 'old_value': 2471.0, 'new_value': 2665.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 43}, {'field': 'onlineAmount', 'old_value': 44979.729999999996, 'new_value': 45018.729999999996}, {'field': 'onlineCount', 'old_value': 793, 'new_value': 795}]
2025-05-26 08:09:31,255 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-26 08:09:31,255 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92187.0, 'new_value': 101738.69}, {'field': 'dailyBillAmount', 'old_value': 92187.0, 'new_value': 101738.69}, {'field': 'amount', 'old_value': 159263.79, 'new_value': 166569.73}, {'field': 'count', 'old_value': 10840, 'new_value': 11367}, {'field': 'instoreAmount', 'old_value': 129135.04000000001, 'new_value': 134228.1}, {'field': 'instoreCount', 'old_value': 8540, 'new_value': 8901}, {'field': 'onlineAmount', 'old_value': 33897.58, 'new_value': 36259.4}, {'field': 'onlineCount', 'old_value': 2300, 'new_value': 2466}]
2025-05-26 08:09:31,771 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-26 08:09:31,771 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 259910.03, 'new_value': 275531.91}, {'field': 'dailyBillAmount', 'old_value': 259910.03, 'new_value': 275531.91}, {'field': 'amount', 'old_value': 251007.86000000002, 'new_value': 266943.66}, {'field': 'count', 'old_value': 7339, 'new_value': 7821}, {'field': 'instoreAmount', 'old_value': 252687.97, 'new_value': 268623.77}, {'field': 'instoreCount', 'old_value': 7339, 'new_value': 7821}]
2025-05-26 08:09:32,208 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-26 08:09:32,208 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76568.49, 'new_value': 80967.68000000001}, {'field': 'amount', 'old_value': 76565.24, 'new_value': 80964.43000000001}, {'field': 'count', 'old_value': 4126, 'new_value': 4378}, {'field': 'instoreAmount', 'old_value': 43395.159999999996, 'new_value': 45510.75}, {'field': 'instoreCount', 'old_value': 2523, 'new_value': 2658}, {'field': 'onlineAmount', 'old_value': 33173.33, 'new_value': 35456.93}, {'field': 'onlineCount', 'old_value': 1603, 'new_value': 1720}]
2025-05-26 08:09:32,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-26 08:09:32,646 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 133910.93, 'new_value': 142279.55}, {'field': 'dailyBillAmount', 'old_value': 133910.93, 'new_value': 142279.55}, {'field': 'amount', 'old_value': 27750.64, 'new_value': 29115.04}, {'field': 'count', 'old_value': 989, 'new_value': 1044}, {'field': 'instoreAmount', 'old_value': 28715.68, 'new_value': 30130.88}, {'field': 'instoreCount', 'old_value': 989, 'new_value': 1044}]
2025-05-26 08:09:33,052 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-26 08:09:33,052 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 91372.5, 'new_value': 94812.58}, {'field': 'count', 'old_value': 4544, 'new_value': 4731}, {'field': 'instoreAmount', 'old_value': 19615.510000000002, 'new_value': 20305.49}, {'field': 'instoreCount', 'old_value': 1411, 'new_value': 1470}, {'field': 'onlineAmount', 'old_value': 73275.82, 'new_value': 76056.33}, {'field': 'onlineCount', 'old_value': 3133, 'new_value': 3261}]
2025-05-26 08:09:33,692 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-26 08:09:33,692 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102052.96, 'new_value': 109924.41}, {'field': 'amount', 'old_value': 102051.55, 'new_value': 109923.0}, {'field': 'count', 'old_value': 2649, 'new_value': 2828}, {'field': 'instoreAmount', 'old_value': 98147.07, 'new_value': 104829.52}, {'field': 'instoreCount', 'old_value': 2578, 'new_value': 2752}, {'field': 'onlineAmount', 'old_value': 4964.41, 'new_value': 6153.41}, {'field': 'onlineCount', 'old_value': 71, 'new_value': 76}]
2025-05-26 08:09:34,114 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-26 08:09:34,114 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66948.05, 'new_value': 81238.54000000001}, {'field': 'dailyBillAmount', 'old_value': 66948.05, 'new_value': 81238.54000000001}, {'field': 'amount', 'old_value': 157752.05, 'new_value': 164133.77}, {'field': 'count', 'old_value': 6440, 'new_value': 6773}, {'field': 'instoreAmount', 'old_value': 160929.89, 'new_value': 167464.75}, {'field': 'instoreCount', 'old_value': 6383, 'new_value': 6714}, {'field': 'onlineAmount', 'old_value': 2094.41, 'new_value': 2119.41}, {'field': 'onlineCount', 'old_value': 57, 'new_value': 59}]
2025-05-26 08:09:34,567 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-26 08:09:34,567 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 115032.27, 'new_value': 119466.45}, {'field': 'count', 'old_value': 9392, 'new_value': 9719}, {'field': 'instoreAmount', 'old_value': 8292.11, 'new_value': 8726.82}, {'field': 'instoreCount', 'old_value': 456, 'new_value': 492}, {'field': 'onlineAmount', 'old_value': 111890.24, 'new_value': 115986.94}, {'field': 'onlineCount', 'old_value': 8936, 'new_value': 9227}]
2025-05-26 08:09:35,005 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-26 08:09:35,005 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156378.1, 'new_value': 164508.25}, {'field': 'dailyBillAmount', 'old_value': 156378.1, 'new_value': 164508.25}, {'field': 'amount', 'old_value': 133575.01, 'new_value': 139685.54}, {'field': 'count', 'old_value': 4380, 'new_value': 4624}, {'field': 'instoreAmount', 'old_value': 72922.51, 'new_value': 76387.1}, {'field': 'instoreCount', 'old_value': 3146, 'new_value': 3336}, {'field': 'onlineAmount', 'old_value': 69346.82, 'new_value': 72293.72}, {'field': 'onlineCount', 'old_value': 1234, 'new_value': 1288}]
2025-05-26 08:09:35,505 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-26 08:09:35,505 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118927.45, 'new_value': 119905.45}, {'field': 'amount', 'old_value': 118926.92, 'new_value': 119904.92}, {'field': 'count', 'old_value': 78, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 118927.45, 'new_value': 119905.45}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 80}]
2025-05-26 08:09:36,052 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-26 08:09:36,052 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56056.33, 'new_value': 58407.7}, {'field': 'dailyBillAmount', 'old_value': 56056.33, 'new_value': 58407.7}, {'field': 'amount', 'old_value': 74629.14, 'new_value': 77215.85}, {'field': 'count', 'old_value': 2913, 'new_value': 3018}, {'field': 'instoreAmount', 'old_value': 23948.27, 'new_value': 25008.78}, {'field': 'instoreCount', 'old_value': 1032, 'new_value': 1072}, {'field': 'onlineAmount', 'old_value': 51755.23, 'new_value': 53315.33}, {'field': 'onlineCount', 'old_value': 1881, 'new_value': 1946}]
2025-05-26 08:09:36,474 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-26 08:09:36,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92667.36, 'new_value': 98950.48}, {'field': 'dailyBillAmount', 'old_value': 92667.36, 'new_value': 98950.48}, {'field': 'amount', 'old_value': 95407.69, 'new_value': 101901.69}, {'field': 'count', 'old_value': 3374, 'new_value': 3579}, {'field': 'instoreAmount', 'old_value': 95407.69, 'new_value': 101901.69}, {'field': 'instoreCount', 'old_value': 3374, 'new_value': 3579}]
2025-05-26 08:09:37,036 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-26 08:09:37,036 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 330709.0, 'new_value': 345255.0}, {'field': 'count', 'old_value': 267, 'new_value': 280}, {'field': 'instoreAmount', 'old_value': 360888.0, 'new_value': 375434.0}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 280}]
2025-05-26 08:09:37,474 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-26 08:09:37,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228910.88999999998, 'new_value': 235864.95}, {'field': 'dailyBillAmount', 'old_value': 228910.88999999998, 'new_value': 235864.95}, {'field': 'amount', 'old_value': 233094.96, 'new_value': 240199.31}, {'field': 'count', 'old_value': 452, 'new_value': 470}, {'field': 'instoreAmount', 'old_value': 235768.46, 'new_value': 243668.81}, {'field': 'instoreCount', 'old_value': 452, 'new_value': 470}]
2025-05-26 08:09:37,942 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-26 08:09:37,942 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42192.0, 'new_value': 43328.0}, {'field': 'amount', 'old_value': 42192.0, 'new_value': 43328.0}, {'field': 'count', 'old_value': 91, 'new_value': 94}, {'field': 'instoreAmount', 'old_value': 42192.0, 'new_value': 43328.0}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 94}]
2025-05-26 08:09:38,396 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-26 08:09:38,396 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72839.0, 'new_value': 76108.0}, {'field': 'dailyBillAmount', 'old_value': 72839.0, 'new_value': 76108.0}, {'field': 'amount', 'old_value': 37340.0, 'new_value': 39413.0}, {'field': 'count', 'old_value': 102, 'new_value': 108}, {'field': 'instoreAmount', 'old_value': 38802.0, 'new_value': 40875.0}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 108}]
2025-05-26 08:09:38,802 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-26 08:09:38,802 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63458.0, 'new_value': 66471.0}, {'field': 'amount', 'old_value': 59216.0, 'new_value': 62229.0}, {'field': 'count', 'old_value': 79, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 59216.0, 'new_value': 62229.0}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 85}]
2025-05-26 08:09:39,286 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-26 08:09:39,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66948.1, 'new_value': 68262.1}, {'field': 'amount', 'old_value': 66945.9, 'new_value': 68259.9}, {'field': 'count', 'old_value': 180, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 67436.8, 'new_value': 68750.8}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 183}]
2025-05-26 08:09:39,692 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-26 08:09:39,692 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 572096.0, 'new_value': 606288.0}, {'field': 'dailyBillAmount', 'old_value': 572096.0, 'new_value': 606288.0}, {'field': 'amount', 'old_value': 621124.0, 'new_value': 655316.0}, {'field': 'count', 'old_value': 76, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 621124.0, 'new_value': 655316.0}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 81}]
2025-05-26 08:09:40,146 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-26 08:09:40,146 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88371.0, 'new_value': 90855.0}, {'field': 'amount', 'old_value': 88371.0, 'new_value': 90855.0}, {'field': 'count', 'old_value': 23, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 88371.0, 'new_value': 90855.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 25}]
2025-05-26 08:09:40,599 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-26 08:09:40,599 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25955.0, 'new_value': 27805.0}, {'field': 'amount', 'old_value': 25955.0, 'new_value': 27805.0}, {'field': 'count', 'old_value': 40, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 25955.0, 'new_value': 27805.0}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 41}]
2025-05-26 08:09:41,083 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-26 08:09:41,083 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66770.0, 'new_value': 68368.0}, {'field': 'amount', 'old_value': 66770.0, 'new_value': 68368.0}, {'field': 'count', 'old_value': 74, 'new_value': 77}, {'field': 'instoreAmount', 'old_value': 66770.0, 'new_value': 68368.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 77}]
2025-05-26 08:09:41,536 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-26 08:09:41,536 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 237728.1, 'new_value': 250901.9}, {'field': 'dailyBillAmount', 'old_value': 237728.1, 'new_value': 250901.9}, {'field': 'amount', 'old_value': 322942.1, 'new_value': 338971.0}, {'field': 'count', 'old_value': 405, 'new_value': 425}, {'field': 'instoreAmount', 'old_value': 335939.16000000003, 'new_value': 352317.36}, {'field': 'instoreCount', 'old_value': 405, 'new_value': 425}]
2025-05-26 08:09:42,067 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-26 08:09:42,067 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105597.04, 'new_value': 112112.84}, {'field': 'dailyBillAmount', 'old_value': 105597.04, 'new_value': 112112.84}, {'field': 'amount', 'old_value': 44344.5, 'new_value': 51464.6}, {'field': 'count', 'old_value': 434, 'new_value': 506}, {'field': 'instoreAmount', 'old_value': 43388.58, 'new_value': 50410.68}, {'field': 'instoreCount', 'old_value': 377, 'new_value': 447}, {'field': 'onlineAmount', 'old_value': 3518.02, 'new_value': 3616.02}, {'field': 'onlineCount', 'old_value': 57, 'new_value': 59}]
2025-05-26 08:09:42,474 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-26 08:09:42,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35741.0, 'new_value': 35932.0}, {'field': 'dailyBillAmount', 'old_value': 35741.0, 'new_value': 35932.0}, {'field': 'amount', 'old_value': 41028.0, 'new_value': 41219.0}, {'field': 'count', 'old_value': 131, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 41028.0, 'new_value': 41219.0}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 132}]
2025-05-26 08:09:42,880 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-26 08:09:42,880 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33333.7, 'new_value': 33938.7}, {'field': 'amount', 'old_value': 33333.7, 'new_value': 33938.7}, {'field': 'count', 'old_value': 202, 'new_value': 204}, {'field': 'instoreAmount', 'old_value': 33671.7, 'new_value': 34276.7}, {'field': 'instoreCount', 'old_value': 202, 'new_value': 204}]
2025-05-26 08:09:43,286 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-26 08:09:43,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 8048.0, 'new_value': 8290.0}, {'field': 'dailyBillAmount', 'old_value': 8048.0, 'new_value': 8290.0}, {'field': 'amount', 'old_value': 39448.0, 'new_value': 41185.0}, {'field': 'count', 'old_value': 121, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 40223.0, 'new_value': 41960.0}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 129}]
2025-05-26 08:09:43,755 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-26 08:09:43,755 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 697848.42, 'new_value': 820962.93}, {'field': 'dailyBillAmount', 'old_value': 697848.42, 'new_value': 820962.93}, {'field': 'amount', 'old_value': 46552.11, 'new_value': 51607.94}, {'field': 'count', 'old_value': 449, 'new_value': 510}, {'field': 'instoreAmount', 'old_value': 36981.659999999996, 'new_value': 41242.25}, {'field': 'instoreCount', 'old_value': 310, 'new_value': 361}, {'field': 'onlineAmount', 'old_value': 10593.48, 'new_value': 11390.8}, {'field': 'onlineCount', 'old_value': 139, 'new_value': 149}]
2025-05-26 08:09:44,177 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-26 08:09:44,177 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78998.5, 'new_value': 81071.5}, {'field': 'amount', 'old_value': 78798.5, 'new_value': 80871.5}, {'field': 'count', 'old_value': 97, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 81345.0, 'new_value': 83418.0}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 103}]
2025-05-26 08:09:44,630 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-26 08:09:44,630 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16378.0, 'new_value': 17044.0}, {'field': 'amount', 'old_value': 16378.0, 'new_value': 17044.0}, {'field': 'count', 'old_value': 28, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 16378.0, 'new_value': 17044.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 30}]
2025-05-26 08:09:45,083 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-26 08:09:45,083 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22954.27, 'new_value': 24130.47}, {'field': 'amount', 'old_value': 22953.57, 'new_value': 24129.77}, {'field': 'count', 'old_value': 92, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 22954.27, 'new_value': 24130.47}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 97}]
2025-05-26 08:09:45,489 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-26 08:09:45,489 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41824.0, 'new_value': 43583.0}, {'field': 'dailyBillAmount', 'old_value': 41824.0, 'new_value': 43583.0}, {'field': 'amount', 'old_value': 42023.0, 'new_value': 43782.0}, {'field': 'count', 'old_value': 103, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 43269.0, 'new_value': 45028.0}, {'field': 'instoreCount', 'old_value': 103, 'new_value': 107}]
2025-05-26 08:09:45,958 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-26 08:09:45,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335451.07, 'new_value': 354226.25}, {'field': 'dailyBillAmount', 'old_value': 312775.6, 'new_value': 327812.1}, {'field': 'amount', 'old_value': 333471.04, 'new_value': 352246.22}, {'field': 'count', 'old_value': 814, 'new_value': 917}, {'field': 'instoreAmount', 'old_value': 336959.1, 'new_value': 355734.27999999997}, {'field': 'instoreCount', 'old_value': 814, 'new_value': 917}]
2025-05-26 08:09:46,411 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-26 08:09:46,411 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64329.0, 'new_value': 73771.0}, {'field': 'amount', 'old_value': 64329.0, 'new_value': 73771.0}, {'field': 'count', 'old_value': 286, 'new_value': 317}, {'field': 'instoreAmount', 'old_value': 65257.0, 'new_value': 74757.0}, {'field': 'instoreCount', 'old_value': 286, 'new_value': 317}]
2025-05-26 08:09:46,895 - INFO - 更新表单数据成功: FINST-VRA66VA1RMZU72KJ6T3JJ8YBFM4G3QAM6RBAM032
2025-05-26 08:09:46,895 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36603.0, 'new_value': 38283.0}, {'field': 'amount', 'old_value': 36603.0, 'new_value': 38283.0}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 36603.0, 'new_value': 38283.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-26 08:09:47,333 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-26 08:09:47,333 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88760.79, 'new_value': 101344.79}, {'field': 'dailyBillAmount', 'old_value': 88760.79, 'new_value': 101344.79}, {'field': 'amount', 'old_value': 92023.31, 'new_value': 104291.31}, {'field': 'count', 'old_value': 560, 'new_value': 630}, {'field': 'instoreAmount', 'old_value': 92023.31, 'new_value': 104291.31}, {'field': 'instoreCount', 'old_value': 560, 'new_value': 630}]
2025-05-26 08:09:47,755 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-26 08:09:47,755 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95263.31, 'new_value': 99352.66}, {'field': 'dailyBillAmount', 'old_value': 95263.31, 'new_value': 99352.66}, {'field': 'amount', 'old_value': 33570.88, 'new_value': 36037.69}, {'field': 'count', 'old_value': 3311, 'new_value': 3536}, {'field': 'instoreAmount', 'old_value': 35769.0, 'new_value': 38333.65}, {'field': 'instoreCount', 'old_value': 3311, 'new_value': 3536}]
2025-05-26 08:09:48,364 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-26 08:09:48,364 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 590780.69, 'new_value': 623921.34}, {'field': 'dailyBillAmount', 'old_value': 590780.69, 'new_value': 623921.34}, {'field': 'amount', 'old_value': 605750.95, 'new_value': 639631.27}, {'field': 'count', 'old_value': 5962, 'new_value': 6278}, {'field': 'instoreAmount', 'old_value': 460739.26, 'new_value': 487895.5}, {'field': 'instoreCount', 'old_value': 2306, 'new_value': 2440}, {'field': 'onlineAmount', 'old_value': 150081.37, 'new_value': 157055.49}, {'field': 'onlineCount', 'old_value': 3656, 'new_value': 3838}]
2025-05-26 08:09:48,802 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-26 08:09:48,802 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 184540.88, 'new_value': 199645.08}, {'field': 'amount', 'old_value': 184540.88, 'new_value': 199645.08}, {'field': 'count', 'old_value': 1241, 'new_value': 1337}, {'field': 'instoreAmount', 'old_value': 184975.88, 'new_value': 200080.08}, {'field': 'instoreCount', 'old_value': 1241, 'new_value': 1337}]
2025-05-26 08:09:49,239 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-26 08:09:49,239 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90846.29, 'new_value': 93969.6}, {'field': 'dailyBillAmount', 'old_value': 90846.29, 'new_value': 93969.6}, {'field': 'amount', 'old_value': 110901.69, 'new_value': 115170.4}, {'field': 'count', 'old_value': 5165, 'new_value': 5393}, {'field': 'instoreAmount', 'old_value': 56645.57, 'new_value': 58918.12}, {'field': 'instoreCount', 'old_value': 2974, 'new_value': 3095}, {'field': 'onlineAmount', 'old_value': 55496.95, 'new_value': 57522.08}, {'field': 'onlineCount', 'old_value': 2191, 'new_value': 2298}]
2025-05-26 08:09:49,692 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-26 08:09:49,708 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 2520.0, 'new_value': 2649.0}, {'field': 'amount', 'old_value': 2520.0, 'new_value': 2649.0}, {'field': 'count', 'old_value': 114, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 2520.0, 'new_value': 2649.0}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 120}]
2025-05-26 08:09:50,161 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-26 08:09:50,161 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116701.31, 'new_value': 122124.23}, {'field': 'dailyBillAmount', 'old_value': 116701.31, 'new_value': 122124.23}, {'field': 'amount', 'old_value': 56255.42, 'new_value': 59102.08}, {'field': 'count', 'old_value': 3918, 'new_value': 4214}, {'field': 'instoreAmount', 'old_value': 7872.98, 'new_value': 8179.56}, {'field': 'instoreCount', 'old_value': 335, 'new_value': 345}, {'field': 'onlineAmount', 'old_value': 48382.44, 'new_value': 50922.52}, {'field': 'onlineCount', 'old_value': 3583, 'new_value': 3869}]
2025-05-26 08:09:50,552 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-26 08:09:50,552 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335386.9, 'new_value': 354780.63}, {'field': 'dailyBillAmount', 'old_value': 335386.9, 'new_value': 354780.63}, {'field': 'amount', 'old_value': 314652.18, 'new_value': 332324.79}, {'field': 'count', 'old_value': 2775, 'new_value': 2930}, {'field': 'instoreAmount', 'old_value': 228978.49, 'new_value': 242136.49}, {'field': 'instoreCount', 'old_value': 1153, 'new_value': 1244}, {'field': 'onlineAmount', 'old_value': 85674.91, 'new_value': 90189.52}, {'field': 'onlineCount', 'old_value': 1622, 'new_value': 1686}]
2025-05-26 08:09:51,005 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-26 08:09:51,020 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 374022.33999999997, 'new_value': 395883.89}, {'field': 'dailyBillAmount', 'old_value': 374022.33999999997, 'new_value': 395883.89}, {'field': 'amount', 'old_value': 383789.76, 'new_value': 406171.46}, {'field': 'count', 'old_value': 2333, 'new_value': 2461}, {'field': 'instoreAmount', 'old_value': 348346.96, 'new_value': 369882.76}, {'field': 'instoreCount', 'old_value': 1958, 'new_value': 2077}, {'field': 'onlineAmount', 'old_value': 41584.1, 'new_value': 42430.1}, {'field': 'onlineCount', 'old_value': 375, 'new_value': 384}]
2025-05-26 08:09:51,474 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-26 08:09:51,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 951521.51, 'new_value': 997924.29}, {'field': 'dailyBillAmount', 'old_value': 951521.51, 'new_value': 997924.29}, {'field': 'amount', 'old_value': 1056472.81, 'new_value': 1106588.6}, {'field': 'count', 'old_value': 5882, 'new_value': 6160}, {'field': 'instoreAmount', 'old_value': 797341.08, 'new_value': 836917.74}, {'field': 'instoreCount', 'old_value': 3214, 'new_value': 3378}, {'field': 'onlineAmount', 'old_value': 267525.73, 'new_value': 278484.8}, {'field': 'onlineCount', 'old_value': 2668, 'new_value': 2782}]
2025-05-26 08:09:51,849 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-26 08:09:51,849 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 314009.85, 'new_value': 329803.15}, {'field': 'dailyBillAmount', 'old_value': 314009.85, 'new_value': 329803.15}, {'field': 'amount', 'old_value': 445265.01, 'new_value': 468669.51}, {'field': 'count', 'old_value': 2092, 'new_value': 2206}, {'field': 'instoreAmount', 'old_value': 418592.26, 'new_value': 440792.16}, {'field': 'instoreCount', 'old_value': 1678, 'new_value': 1773}, {'field': 'onlineAmount', 'old_value': 27432.95, 'new_value': 28637.55}, {'field': 'onlineCount', 'old_value': 414, 'new_value': 433}]
2025-05-26 08:09:52,333 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-26 08:09:52,333 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 367988.38, 'new_value': 388032.58}, {'field': 'dailyBillAmount', 'old_value': 367988.38, 'new_value': 388032.58}, {'field': 'amount', 'old_value': 346069.6, 'new_value': 364853.6}, {'field': 'count', 'old_value': 1552, 'new_value': 1644}, {'field': 'instoreAmount', 'old_value': 352530.2, 'new_value': 371525.2}, {'field': 'instoreCount', 'old_value': 1552, 'new_value': 1644}]
2025-05-26 08:09:52,755 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-26 08:09:52,755 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 758741.65, 'new_value': 799400.25}, {'field': 'amount', 'old_value': 758740.95, 'new_value': 799399.55}, {'field': 'count', 'old_value': 6062, 'new_value': 6363}, {'field': 'instoreAmount', 'old_value': 758741.65, 'new_value': 799400.25}, {'field': 'instoreCount', 'old_value': 6062, 'new_value': 6363}]
2025-05-26 08:09:53,192 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-26 08:09:53,192 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 617852.57, 'new_value': 653875.99}, {'field': 'dailyBillAmount', 'old_value': 617852.57, 'new_value': 653875.99}, {'field': 'amount', 'old_value': 765075.51, 'new_value': 806539.31}, {'field': 'count', 'old_value': 5321, 'new_value': 5611}, {'field': 'instoreAmount', 'old_value': 424555.2, 'new_value': 447966.6}, {'field': 'instoreCount', 'old_value': 2238, 'new_value': 2359}, {'field': 'onlineAmount', 'old_value': 350417.5, 'new_value': 368732.9}, {'field': 'onlineCount', 'old_value': 3083, 'new_value': 3252}]
2025-05-26 08:09:53,630 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-26 08:09:53,630 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 395431.71, 'new_value': 420964.83}, {'field': 'dailyBillAmount', 'old_value': 395431.71, 'new_value': 420964.83}, {'field': 'amount', 'old_value': 464728.23, 'new_value': 490168.48}, {'field': 'count', 'old_value': 5096, 'new_value': 5357}, {'field': 'instoreAmount', 'old_value': 319329.92, 'new_value': 338331.82}, {'field': 'instoreCount', 'old_value': 2191, 'new_value': 2319}, {'field': 'onlineAmount', 'old_value': 147354.53, 'new_value': 153827.51}, {'field': 'onlineCount', 'old_value': 2905, 'new_value': 3038}]
2025-05-26 08:09:54,099 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-26 08:09:54,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 492810.35, 'new_value': 520101.22}, {'field': 'dailyBillAmount', 'old_value': 492810.35, 'new_value': 520101.22}, {'field': 'amount', 'old_value': 499560.85, 'new_value': 526689.83}, {'field': 'count', 'old_value': 4746, 'new_value': 4961}, {'field': 'instoreAmount', 'old_value': 435226.44, 'new_value': 459892.81}, {'field': 'instoreCount', 'old_value': 2511, 'new_value': 2648}, {'field': 'onlineAmount', 'old_value': 65379.6, 'new_value': 67871.11}, {'field': 'onlineCount', 'old_value': 2235, 'new_value': 2313}]
2025-05-26 08:09:54,583 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-26 08:09:54,583 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 120325.8, 'new_value': 123150.8}, {'field': 'amount', 'old_value': 120325.3, 'new_value': 123150.3}, {'field': 'count', 'old_value': 558, 'new_value': 581}, {'field': 'instoreAmount', 'old_value': 120325.8, 'new_value': 123150.8}, {'field': 'instoreCount', 'old_value': 558, 'new_value': 581}]
2025-05-26 08:09:55,036 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-26 08:09:55,036 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 344795.55, 'new_value': 361665.25}, {'field': 'dailyBillAmount', 'old_value': 344795.55, 'new_value': 361665.25}, {'field': 'amount', 'old_value': -266299.18, 'new_value': -283165.88}, {'field': 'count', 'old_value': 933, 'new_value': 973}, {'field': 'instoreAmount', 'old_value': 6512.1, 'new_value': 6971.1}, {'field': 'instoreCount', 'old_value': 308, 'new_value': 323}, {'field': 'onlineAmount', 'old_value': 19393.37, 'new_value': 20131.67}, {'field': 'onlineCount', 'old_value': 625, 'new_value': 650}]
2025-05-26 08:09:55,520 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-26 08:09:55,520 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 583852.16, 'new_value': 612370.31}, {'field': 'dailyBillAmount', 'old_value': 583852.16, 'new_value': 612370.31}, {'field': 'amount', 'old_value': 439855.15, 'new_value': 461689.2}, {'field': 'count', 'old_value': 1845, 'new_value': 1947}, {'field': 'instoreAmount', 'old_value': 439855.15, 'new_value': 461689.2}, {'field': 'instoreCount', 'old_value': 1845, 'new_value': 1947}]
2025-05-26 08:09:55,974 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-26 08:09:55,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 378022.27999999997, 'new_value': 399396.37}, {'field': 'dailyBillAmount', 'old_value': 378022.27999999997, 'new_value': 399396.37}, {'field': 'amount', 'old_value': 158899.9, 'new_value': 165875.2}, {'field': 'count', 'old_value': 652, 'new_value': 687}, {'field': 'instoreAmount', 'old_value': 165043.2, 'new_value': 171985.3}, {'field': 'instoreCount', 'old_value': 631, 'new_value': 665}, {'field': 'onlineAmount', 'old_value': 1471.8999999999999, 'new_value': 1505.1}, {'field': 'onlineCount', 'old_value': 21, 'new_value': 22}]
2025-05-26 08:09:56,380 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-26 08:09:56,380 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 298684.21, 'new_value': 317380.03}, {'field': 'dailyBillAmount', 'old_value': 298684.21, 'new_value': 317380.03}, {'field': 'amount', 'old_value': 289294.12, 'new_value': 307647.95}, {'field': 'count', 'old_value': 1908, 'new_value': 2035}, {'field': 'instoreAmount', 'old_value': 272720.60000000003, 'new_value': 289542.71}, {'field': 'instoreCount', 'old_value': 1462, 'new_value': 1552}, {'field': 'onlineAmount', 'old_value': 16737.67, 'new_value': 18269.39}, {'field': 'onlineCount', 'old_value': 446, 'new_value': 483}]
2025-05-26 08:09:56,833 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-26 08:09:56,833 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 321226.3, 'new_value': 335824.52999999997}, {'field': 'dailyBillAmount', 'old_value': 321226.3, 'new_value': 335824.52999999997}, {'field': 'amount', 'old_value': 135750.61, 'new_value': 141711.83}, {'field': 'count', 'old_value': 2299, 'new_value': 2392}, {'field': 'instoreAmount', 'old_value': 78654.58, 'new_value': 81931.03}, {'field': 'instoreCount', 'old_value': 598, 'new_value': 628}, {'field': 'onlineAmount', 'old_value': 57099.28, 'new_value': 59784.11}, {'field': 'onlineCount', 'old_value': 1701, 'new_value': 1764}]
2025-05-26 08:09:57,224 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-26 08:09:57,224 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25903.0, 'new_value': 26262.0}, {'field': 'dailyBillAmount', 'old_value': 25903.0, 'new_value': 26262.0}, {'field': 'amount', 'old_value': 56804.0, 'new_value': 58364.0}, {'field': 'count', 'old_value': 32, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 56804.0, 'new_value': 58364.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 33}]
2025-05-26 08:09:57,692 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-26 08:09:57,692 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 134323.35, 'new_value': 139482.29}, {'field': 'amount', 'old_value': 134312.63, 'new_value': 139471.31}, {'field': 'count', 'old_value': 6143, 'new_value': 6418}, {'field': 'instoreAmount', 'old_value': 48556.56, 'new_value': 50303.95}, {'field': 'instoreCount', 'old_value': 1916, 'new_value': 1990}, {'field': 'onlineAmount', 'old_value': 92950.84, 'new_value': 96629.99}, {'field': 'onlineCount', 'old_value': 4227, 'new_value': 4428}]
2025-05-26 08:09:58,145 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-26 08:09:58,145 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42471.9, 'new_value': 43930.9}, {'field': 'amount', 'old_value': 42471.9, 'new_value': 43930.9}, {'field': 'count', 'old_value': 190, 'new_value': 199}, {'field': 'instoreAmount', 'old_value': 42471.9, 'new_value': 43930.9}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 199}]
2025-05-26 08:09:58,645 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-26 08:09:58,645 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 379453.1, 'new_value': 402590.51}, {'field': 'dailyBillAmount', 'old_value': 379453.1, 'new_value': 402590.51}, {'field': 'amount', 'old_value': 152515.2, 'new_value': 163174.5}, {'field': 'count', 'old_value': 2846, 'new_value': 3058}, {'field': 'instoreAmount', 'old_value': 153724.5, 'new_value': 164457.8}, {'field': 'instoreCount', 'old_value': 2846, 'new_value': 3058}]
2025-05-26 08:09:59,067 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-26 08:09:59,067 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29777.65, 'new_value': 31229.29}, {'field': 'amount', 'old_value': 29773.059999999998, 'new_value': 31224.51}, {'field': 'count', 'old_value': 1800, 'new_value': 1927}, {'field': 'instoreAmount', 'old_value': 15498.94, 'new_value': 16113.94}, {'field': 'instoreCount', 'old_value': 771, 'new_value': 805}, {'field': 'onlineAmount', 'old_value': 14797.210000000001, 'new_value': 15656.19}, {'field': 'onlineCount', 'old_value': 1029, 'new_value': 1122}]
2025-05-26 08:09:59,677 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-26 08:09:59,677 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49222.4, 'new_value': 53152.6}, {'field': 'amount', 'old_value': 49222.4, 'new_value': 53152.6}, {'field': 'count', 'old_value': 123, 'new_value': 136}, {'field': 'instoreAmount', 'old_value': 49222.4, 'new_value': 53152.6}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 136}]
2025-05-26 08:10:00,145 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-26 08:10:00,161 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 184100.62, 'new_value': 192419.03}, {'field': 'dailyBillAmount', 'old_value': 152237.8, 'new_value': 159639.5}, {'field': 'amount', 'old_value': 184099.94, 'new_value': 192418.35}, {'field': 'count', 'old_value': 2588, 'new_value': 2707}, {'field': 'instoreAmount', 'old_value': 175885.0, 'new_value': 183865.9}, {'field': 'instoreCount', 'old_value': 2248, 'new_value': 2356}, {'field': 'onlineAmount', 'old_value': 8442.74, 'new_value': 8805.53}, {'field': 'onlineCount', 'old_value': 340, 'new_value': 351}]
2025-05-26 08:10:00,614 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-26 08:10:00,614 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27790.52, 'new_value': 29990.34}, {'field': 'amount', 'old_value': 27789.72, 'new_value': 29989.54}, {'field': 'count', 'old_value': 1187, 'new_value': 1287}, {'field': 'instoreAmount', 'old_value': 23171.420000000002, 'new_value': 25060.14}, {'field': 'instoreCount', 'old_value': 1057, 'new_value': 1147}, {'field': 'onlineAmount', 'old_value': 4704.8, 'new_value': 5015.9}, {'field': 'onlineCount', 'old_value': 130, 'new_value': 140}]
2025-05-26 08:10:00,958 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-26 08:10:00,958 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 377773.77, 'new_value': 398615.60000000003}, {'field': 'dailyBillAmount', 'old_value': 377773.77, 'new_value': 398615.60000000003}, {'field': 'amount', 'old_value': 486872.31, 'new_value': 513286.12}, {'field': 'count', 'old_value': 5045, 'new_value': 5237}, {'field': 'instoreAmount', 'old_value': 458641.33, 'new_value': 483451.57}, {'field': 'instoreCount', 'old_value': 3487, 'new_value': 3619}, {'field': 'onlineAmount', 'old_value': 38243.56, 'new_value': 39847.18}, {'field': 'onlineCount', 'old_value': 1558, 'new_value': 1618}]
2025-05-26 08:10:01,411 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-26 08:10:01,411 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146538.64, 'new_value': 154712.42}, {'field': 'dailyBillAmount', 'old_value': 146538.64, 'new_value': 154712.42}, {'field': 'amount', 'old_value': 36814.29, 'new_value': 38468.96}, {'field': 'count', 'old_value': 588, 'new_value': 621}, {'field': 'instoreAmount', 'old_value': 24021.73, 'new_value': 25115.25}, {'field': 'instoreCount', 'old_value': 310, 'new_value': 332}, {'field': 'onlineAmount', 'old_value': 13679.65, 'new_value': 14243.85}, {'field': 'onlineCount', 'old_value': 278, 'new_value': 289}]
2025-05-26 08:10:01,974 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-26 08:10:01,974 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146173.03, 'new_value': 151861.19}, {'field': 'dailyBillAmount', 'old_value': 131215.3, 'new_value': 136939.28}, {'field': 'amount', 'old_value': 146170.46, 'new_value': 151858.62}, {'field': 'count', 'old_value': 8230, 'new_value': 8564}, {'field': 'instoreAmount', 'old_value': 90109.59, 'new_value': 93548.24}, {'field': 'instoreCount', 'old_value': 4962, 'new_value': 5155}, {'field': 'onlineAmount', 'old_value': 57888.79, 'new_value': 60163.9}, {'field': 'onlineCount', 'old_value': 3268, 'new_value': 3409}]
2025-05-26 08:10:02,364 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-26 08:10:02,364 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77790.4, 'new_value': 81211.22}, {'field': 'amount', 'old_value': 77780.16, 'new_value': 81200.35}, {'field': 'count', 'old_value': 5001, 'new_value': 5202}, {'field': 'instoreAmount', 'old_value': 34678.26, 'new_value': 36223.82}, {'field': 'instoreCount', 'old_value': 2021, 'new_value': 2088}, {'field': 'onlineAmount', 'old_value': 45504.46, 'new_value': 47450.35}, {'field': 'onlineCount', 'old_value': 2980, 'new_value': 3114}]
2025-05-26 08:10:02,895 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-26 08:10:02,895 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 153842.22, 'new_value': 160398.13}, {'field': 'count', 'old_value': 1534, 'new_value': 1600}, {'field': 'instoreAmount', 'old_value': 154067.13, 'new_value': 160663.41}, {'field': 'instoreCount', 'old_value': 1534, 'new_value': 1600}]
2025-05-26 08:10:03,317 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-26 08:10:03,317 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124619.78, 'new_value': 129801.94}, {'field': 'dailyBillAmount', 'old_value': 128880.59, 'new_value': 134254.21}, {'field': 'amount', 'old_value': 124613.97, 'new_value': 129796.13}, {'field': 'count', 'old_value': 2562, 'new_value': 2683}, {'field': 'instoreAmount', 'old_value': 119258.78, 'new_value': 124111.87}, {'field': 'instoreCount', 'old_value': 2145, 'new_value': 2243}, {'field': 'onlineAmount', 'old_value': 5469.32, 'new_value': 5798.39}, {'field': 'onlineCount', 'old_value': 417, 'new_value': 440}]
2025-05-26 08:10:03,770 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-26 08:10:03,770 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 195691.91999999998, 'new_value': 201179.12}, {'field': 'dailyBillAmount', 'old_value': 195691.91999999998, 'new_value': 201179.12}, {'field': 'amount', 'old_value': 25293.11, 'new_value': 25973.61}, {'field': 'count', 'old_value': 996, 'new_value': 1017}, {'field': 'instoreAmount', 'old_value': 29011.2, 'new_value': 29718.6}, {'field': 'instoreCount', 'old_value': 996, 'new_value': 1017}]
2025-05-26 08:10:04,192 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-26 08:10:04,192 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 516105.86, 'new_value': 545237.16}, {'field': 'dailyBillAmount', 'old_value': 516105.86, 'new_value': 545237.16}, {'field': 'amount', 'old_value': 51489.659999999996, 'new_value': 54356.36}, {'field': 'count', 'old_value': 249, 'new_value': 263}, {'field': 'instoreAmount', 'old_value': 51715.659999999996, 'new_value': 54582.36}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 263}]
2025-05-26 08:10:04,661 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-26 08:10:04,661 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 16927.67, 'new_value': 17240.06}, {'field': 'count', 'old_value': 865, 'new_value': 884}, {'field': 'onlineAmount', 'old_value': 17093.57, 'new_value': 17461.72}, {'field': 'onlineCount', 'old_value': 865, 'new_value': 884}]
2025-05-26 08:10:05,114 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-26 08:10:05,114 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 306760.25, 'new_value': 316481.15}, {'field': 'amount', 'old_value': 306606.47, 'new_value': 316327.37}, {'field': 'count', 'old_value': 3131, 'new_value': 3227}, {'field': 'instoreAmount', 'old_value': 291835.5, 'new_value': 301482.9}, {'field': 'instoreCount', 'old_value': 2637, 'new_value': 2724}, {'field': 'onlineAmount', 'old_value': 20183.54, 'new_value': 20627.74}, {'field': 'onlineCount', 'old_value': 494, 'new_value': 503}]
2025-05-26 08:10:05,536 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-26 08:10:05,536 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 173311.26, 'new_value': 177444.1}, {'field': 'dailyBillAmount', 'old_value': 169510.79, 'new_value': 173643.63}, {'field': 'amount', 'old_value': 128862.75, 'new_value': 131336.37}, {'field': 'count', 'old_value': 4619, 'new_value': 4752}, {'field': 'instoreAmount', 'old_value': 55079.83, 'new_value': 55514.67}, {'field': 'instoreCount', 'old_value': 1883, 'new_value': 1900}, {'field': 'onlineAmount', 'old_value': 75604.04000000001, 'new_value': 77642.82}, {'field': 'onlineCount', 'old_value': 2736, 'new_value': 2852}]
2025-05-26 08:10:05,973 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-26 08:10:05,973 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 4888.53, 'new_value': 5108.72}, {'field': 'count', 'old_value': 225, 'new_value': 233}, {'field': 'instoreAmount', 'old_value': 4888.53, 'new_value': 5108.92}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 233}]
2025-05-26 08:10:07,020 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-26 08:10:07,020 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 6006.75, 'new_value': 6183.5199999999995}, {'field': 'count', 'old_value': 260, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 6006.75, 'new_value': 6183.5199999999995}, {'field': 'onlineCount', 'old_value': 260, 'new_value': 267}]
2025-05-26 08:10:07,489 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-26 08:10:07,489 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102895.62, 'new_value': 106889.47}, {'field': 'dailyBillAmount', 'old_value': 51335.14, 'new_value': 53284.14}, {'field': 'amount', 'old_value': 102895.03, 'new_value': 106888.88}, {'field': 'count', 'old_value': 2544, 'new_value': 2650}, {'field': 'instoreAmount', 'old_value': 55660.26, 'new_value': 57746.55}, {'field': 'instoreCount', 'old_value': 1355, 'new_value': 1407}, {'field': 'onlineAmount', 'old_value': 50172.49, 'new_value': 52080.05}, {'field': 'onlineCount', 'old_value': 1189, 'new_value': 1243}]
2025-05-26 08:10:07,895 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-26 08:10:07,895 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51509.62, 'new_value': 53400.91}, {'field': 'amount', 'old_value': 51509.62, 'new_value': 53400.91}, {'field': 'count', 'old_value': 1944, 'new_value': 2012}, {'field': 'instoreAmount', 'old_value': 52112.68, 'new_value': 54071.57}, {'field': 'instoreCount', 'old_value': 1944, 'new_value': 2012}]
2025-05-26 08:10:08,395 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-26 08:10:08,395 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49685.53, 'new_value': 51034.02}, {'field': 'dailyBillAmount', 'old_value': 49685.53, 'new_value': 51034.02}, {'field': 'amount', 'old_value': 38268.04, 'new_value': 39147.56}, {'field': 'count', 'old_value': 1769, 'new_value': 1827}, {'field': 'instoreAmount', 'old_value': 20791.11, 'new_value': 21035.11}, {'field': 'instoreCount', 'old_value': 711, 'new_value': 721}, {'field': 'onlineAmount', 'old_value': 17559.15, 'new_value': 18194.67}, {'field': 'onlineCount', 'old_value': 1058, 'new_value': 1106}]
2025-05-26 08:10:08,864 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-26 08:10:08,864 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80515.55, 'new_value': 83019.52}, {'field': 'amount', 'old_value': 80515.55, 'new_value': 83019.52}, {'field': 'count', 'old_value': 2468, 'new_value': 2533}, {'field': 'instoreAmount', 'old_value': 32489.84, 'new_value': 33667.020000000004}, {'field': 'instoreCount', 'old_value': 1232, 'new_value': 1266}, {'field': 'onlineAmount', 'old_value': 48127.2, 'new_value': 49453.99}, {'field': 'onlineCount', 'old_value': 1236, 'new_value': 1267}]
2025-05-26 08:10:09,333 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-26 08:10:09,333 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48172.45, 'new_value': 49817.62}, {'field': 'amount', 'old_value': 48171.55, 'new_value': 49816.72}, {'field': 'count', 'old_value': 1152, 'new_value': 1201}, {'field': 'instoreAmount', 'old_value': 37233.8, 'new_value': 38307.7}, {'field': 'instoreCount', 'old_value': 922, 'new_value': 958}, {'field': 'onlineAmount', 'old_value': 11414.66, 'new_value': 11985.93}, {'field': 'onlineCount', 'old_value': 230, 'new_value': 243}]
2025-05-26 08:10:09,864 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-26 08:10:09,864 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 224422.93, 'new_value': 231916.53}, {'field': 'dailyBillAmount', 'old_value': 224422.93, 'new_value': 231916.53}, {'field': 'amount', 'old_value': 151692.52, 'new_value': 156326.32}, {'field': 'count', 'old_value': 3841, 'new_value': 3958}, {'field': 'instoreAmount', 'old_value': 96288.42, 'new_value': 99444.62}, {'field': 'instoreCount', 'old_value': 1911, 'new_value': 1968}, {'field': 'onlineAmount', 'old_value': 68264.44, 'new_value': 70237.04}, {'field': 'onlineCount', 'old_value': 1930, 'new_value': 1990}]
2025-05-26 08:10:10,255 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-26 08:10:10,255 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 660314.48, 'new_value': 699544.89}, {'field': 'dailyBillAmount', 'old_value': 660314.48, 'new_value': 699544.89}, {'field': 'amount', 'old_value': 656545.0, 'new_value': 691058.4}, {'field': 'count', 'old_value': 3902, 'new_value': 4120}, {'field': 'instoreAmount', 'old_value': 469733.2, 'new_value': 492876.6}, {'field': 'instoreCount', 'old_value': 3029, 'new_value': 3191}, {'field': 'onlineAmount', 'old_value': 186814.2, 'new_value': 198184.7}, {'field': 'onlineCount', 'old_value': 873, 'new_value': 929}]
2025-05-26 08:10:10,708 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-26 08:10:10,708 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1019949.32, 'new_value': 1061945.02}, {'field': 'amount', 'old_value': 1019948.82, 'new_value': 1061944.52}, {'field': 'count', 'old_value': 3585, 'new_value': 3745}, {'field': 'instoreAmount', 'old_value': 1019949.32, 'new_value': 1061945.02}, {'field': 'instoreCount', 'old_value': 3585, 'new_value': 3745}]
2025-05-26 08:10:11,145 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-26 08:10:11,145 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 587886.49, 'new_value': 603564.03}, {'field': 'dailyBillAmount', 'old_value': 521977.8, 'new_value': 536113.76}, {'field': 'amount', 'old_value': 587886.49, 'new_value': 603564.03}, {'field': 'count', 'old_value': 3623, 'new_value': 3716}, {'field': 'instoreAmount', 'old_value': 537169.41, 'new_value': 551575.01}, {'field': 'instoreCount', 'old_value': 2299, 'new_value': 2363}, {'field': 'onlineAmount', 'old_value': 51089.06, 'new_value': 52361.0}, {'field': 'onlineCount', 'old_value': 1324, 'new_value': 1353}]
2025-05-26 08:10:11,614 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-26 08:10:11,614 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 573645.66, 'new_value': 602427.75}, {'field': 'dailyBillAmount', 'old_value': 556824.17, 'new_value': 585581.26}, {'field': 'amount', 'old_value': 573639.07, 'new_value': 602421.16}, {'field': 'count', 'old_value': 1401, 'new_value': 1476}, {'field': 'instoreAmount', 'old_value': 533078.4, 'new_value': 560128.9}, {'field': 'instoreCount', 'old_value': 1083, 'new_value': 1145}, {'field': 'onlineAmount', 'old_value': 40694.54, 'new_value': 42426.13}, {'field': 'onlineCount', 'old_value': 318, 'new_value': 331}]
2025-05-26 08:10:12,020 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-26 08:10:12,020 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 709567.19, 'new_value': 738268.16}, {'field': 'amount', 'old_value': 709566.51, 'new_value': 738267.48}, {'field': 'count', 'old_value': 3722, 'new_value': 3895}, {'field': 'instoreAmount', 'old_value': 667656.46, 'new_value': 693955.46}, {'field': 'instoreCount', 'old_value': 2472, 'new_value': 2570}, {'field': 'onlineAmount', 'old_value': 42055.53, 'new_value': 44457.5}, {'field': 'onlineCount', 'old_value': 1250, 'new_value': 1325}]
2025-05-26 08:10:12,520 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-26 08:10:12,536 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 789624.1, 'new_value': 807984.42}, {'field': 'dailyBillAmount', 'old_value': 789624.1, 'new_value': 807984.42}, {'field': 'amount', 'old_value': 714019.63, 'new_value': 734295.85}, {'field': 'count', 'old_value': 3565, 'new_value': 3670}, {'field': 'instoreAmount', 'old_value': 655009.98, 'new_value': 672907.05}, {'field': 'instoreCount', 'old_value': 2952, 'new_value': 3037}, {'field': 'onlineAmount', 'old_value': 59802.06, 'new_value': 62181.22}, {'field': 'onlineCount', 'old_value': 613, 'new_value': 633}]
2025-05-26 08:10:12,989 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-26 08:10:12,989 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 197077.84, 'new_value': 203077.84}, {'field': 'dailyBillAmount', 'old_value': 195671.29, 'new_value': 201671.29}, {'field': 'amount', 'old_value': 193266.66, 'new_value': 199266.66}, {'field': 'count', 'old_value': 285, 'new_value': 291}, {'field': 'instoreAmount', 'old_value': 193266.66, 'new_value': 199266.66}, {'field': 'instoreCount', 'old_value': 285, 'new_value': 291}]
2025-05-26 08:10:13,473 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-26 08:10:13,473 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 160083.73, 'new_value': 171015.03}, {'field': 'dailyBillAmount', 'old_value': 160083.73, 'new_value': 171015.03}, {'field': 'amount', 'old_value': 141886.85, 'new_value': 149464.15}, {'field': 'count', 'old_value': 238, 'new_value': 247}, {'field': 'instoreAmount', 'old_value': 138944.8, 'new_value': 146522.1}, {'field': 'instoreCount', 'old_value': 218, 'new_value': 227}]
2025-05-26 08:10:13,927 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-26 08:10:13,927 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21793.11, 'new_value': 22641.09}, {'field': 'amount', 'old_value': 21793.11, 'new_value': 22641.09}, {'field': 'count', 'old_value': 452, 'new_value': 471}, {'field': 'instoreAmount', 'old_value': 21793.11, 'new_value': 22641.09}, {'field': 'instoreCount', 'old_value': 452, 'new_value': 471}]
2025-05-26 08:10:14,520 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-26 08:10:14,520 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 91642.64, 'new_value': 94896.8}, {'field': 'amount', 'old_value': 91642.64, 'new_value': 94896.8}, {'field': 'count', 'old_value': 771, 'new_value': 802}, {'field': 'instoreAmount', 'old_value': 92193.48, 'new_value': 95447.64}, {'field': 'instoreCount', 'old_value': 771, 'new_value': 802}]
2025-05-26 08:10:15,067 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-26 08:10:15,067 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 303452.38, 'new_value': 319491.79}, {'field': 'dailyBillAmount', 'old_value': 303452.38, 'new_value': 319491.79}, {'field': 'amount', 'old_value': 321364.93, 'new_value': 336732.11}, {'field': 'count', 'old_value': 8779, 'new_value': 9235}, {'field': 'instoreAmount', 'old_value': 303890.24, 'new_value': 318702.34}, {'field': 'instoreCount', 'old_value': 7906, 'new_value': 8315}, {'field': 'onlineAmount', 'old_value': 22272.579999999998, 'new_value': 23208.04}, {'field': 'onlineCount', 'old_value': 873, 'new_value': 920}]
2025-05-26 08:10:15,520 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-26 08:10:15,520 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 97393.94, 'new_value': 108976.18}, {'field': 'dailyBillAmount', 'old_value': 97393.94, 'new_value': 108976.18}, {'field': 'amount', 'old_value': 99697.94, 'new_value': 111280.18}, {'field': 'count', 'old_value': 81, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 99697.94, 'new_value': 111280.18}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 84}]
2025-05-26 08:10:15,973 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-26 08:10:15,973 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 806865.13, 'new_value': 826844.23}, {'field': 'dailyBillAmount', 'old_value': 806865.13, 'new_value': 826844.23}, {'field': 'amount', 'old_value': 728960.2, 'new_value': 741871.17}, {'field': 'count', 'old_value': 1854, 'new_value': 1912}, {'field': 'instoreAmount', 'old_value': 761856.45, 'new_value': 778163.95}, {'field': 'instoreCount', 'old_value': 1540, 'new_value': 1590}, {'field': 'onlineAmount', 'old_value': 7214.14, 'new_value': 7489.21}, {'field': 'onlineCount', 'old_value': 314, 'new_value': 322}]
2025-05-26 08:10:16,411 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-26 08:10:16,411 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1200086.04, 'new_value': 1254174.6}, {'field': 'amount', 'old_value': 1200086.04, 'new_value': 1254174.6}, {'field': 'count', 'old_value': 3865, 'new_value': 4038}, {'field': 'instoreAmount', 'old_value': 1201297.04, 'new_value': 1255385.6}, {'field': 'instoreCount', 'old_value': 3865, 'new_value': 4038}]
2025-05-26 08:10:16,864 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-26 08:10:16,864 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 805158.71, 'new_value': 835195.69}, {'field': 'dailyBillAmount', 'old_value': 805158.71, 'new_value': 835195.69}, {'field': 'amount', 'old_value': 747853.4, 'new_value': 771741.02}, {'field': 'count', 'old_value': 2671, 'new_value': 2759}, {'field': 'instoreAmount', 'old_value': 728851.19, 'new_value': 752092.98}, {'field': 'instoreCount', 'old_value': 1634, 'new_value': 1696}, {'field': 'onlineAmount', 'old_value': 31983.52, 'new_value': 32778.92}, {'field': 'onlineCount', 'old_value': 1037, 'new_value': 1063}]
2025-05-26 08:10:17,239 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-26 08:10:17,239 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1757001.39, 'new_value': 1821067.29}, {'field': 'dailyBillAmount', 'old_value': 1757001.39, 'new_value': 1821067.29}, {'field': 'amount', 'old_value': 1807529.0, 'new_value': 1873291.0}, {'field': 'count', 'old_value': 4773, 'new_value': 4910}, {'field': 'instoreAmount', 'old_value': 1807529.0, 'new_value': 1873291.0}, {'field': 'instoreCount', 'old_value': 4773, 'new_value': 4910}]
2025-05-26 08:10:17,661 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-26 08:10:17,661 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 280244.95, 'new_value': 297095.13}, {'field': 'dailyBillAmount', 'old_value': 280244.95, 'new_value': 297095.13}, {'field': 'amount', 'old_value': 285699.73, 'new_value': 301413.91000000003}, {'field': 'count', 'old_value': 1536, 'new_value': 1612}, {'field': 'instoreAmount', 'old_value': 276846.7, 'new_value': 292860.4}, {'field': 'instoreCount', 'old_value': 1300, 'new_value': 1367}, {'field': 'onlineAmount', 'old_value': 14494.91, 'new_value': 15333.39}, {'field': 'onlineCount', 'old_value': 236, 'new_value': 245}]
2025-05-26 08:10:18,114 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-26 08:10:18,114 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 955056.97, 'new_value': 985202.1900000001}, {'field': 'dailyBillAmount', 'old_value': 955056.97, 'new_value': 985202.1900000001}, {'field': 'amount', 'old_value': 1012114.76, 'new_value': 1042269.26}, {'field': 'count', 'old_value': 4203, 'new_value': 4356}, {'field': 'instoreAmount', 'old_value': 1012115.21, 'new_value': 1042269.71}, {'field': 'instoreCount', 'old_value': 4203, 'new_value': 4356}]
2025-05-26 08:10:18,552 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-26 08:10:18,552 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 422979.76, 'new_value': 440437.16000000003}, {'field': 'dailyBillAmount', 'old_value': 422979.76, 'new_value': 440437.16000000003}, {'field': 'amount', 'old_value': 682402.5, 'new_value': 707505.2999999999}, {'field': 'count', 'old_value': 1147, 'new_value': 1192}, {'field': 'instoreAmount', 'old_value': 677479.68, 'new_value': 702262.68}, {'field': 'instoreCount', 'old_value': 1112, 'new_value': 1155}, {'field': 'onlineAmount', 'old_value': 5255.6, 'new_value': 5575.4}, {'field': 'onlineCount', 'old_value': 35, 'new_value': 37}]
2025-05-26 08:10:18,942 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-26 08:10:18,942 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 245769.34, 'new_value': 260605.29}, {'field': 'dailyBillAmount', 'old_value': 245769.34, 'new_value': 260605.29}, {'field': 'amount', 'old_value': 283519.3, 'new_value': 298492.3}, {'field': 'count', 'old_value': 1968, 'new_value': 2081}, {'field': 'instoreAmount', 'old_value': 287419.3, 'new_value': 302527.3}, {'field': 'instoreCount', 'old_value': 1968, 'new_value': 2081}]
2025-05-26 08:10:19,427 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-26 08:10:19,427 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162287.21, 'new_value': 171233.77}, {'field': 'dailyBillAmount', 'old_value': 162287.21, 'new_value': 171233.77}, {'field': 'amount', 'old_value': 131526.87, 'new_value': 139870.87}, {'field': 'count', 'old_value': 884, 'new_value': 944}, {'field': 'instoreAmount', 'old_value': 131298.0, 'new_value': 140012.0}, {'field': 'instoreCount', 'old_value': 832, 'new_value': 892}]
2025-05-26 08:10:19,864 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-26 08:10:19,864 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 147240.71, 'new_value': 152655.49}, {'field': 'count', 'old_value': 7128, 'new_value': 7396}, {'field': 'instoreAmount', 'old_value': 77822.25, 'new_value': 80630.65}, {'field': 'instoreCount', 'old_value': 3973, 'new_value': 4116}, {'field': 'onlineAmount', 'old_value': 73589.51, 'new_value': 76228.56}, {'field': 'onlineCount', 'old_value': 3155, 'new_value': 3280}]
2025-05-26 08:10:20,317 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-26 08:10:20,317 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 209304.93, 'new_value': 218902.92}, {'field': 'amount', 'old_value': 209295.4, 'new_value': 218893.39}, {'field': 'count', 'old_value': 3945, 'new_value': 4141}, {'field': 'instoreAmount', 'old_value': 191763.81, 'new_value': 199422.47}, {'field': 'instoreCount', 'old_value': 3592, 'new_value': 3752}, {'field': 'onlineAmount', 'old_value': 17541.12, 'new_value': 19480.45}, {'field': 'onlineCount', 'old_value': 353, 'new_value': 389}]
2025-05-26 08:10:20,739 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-26 08:10:20,739 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31157.1, 'new_value': 32960.5}, {'field': 'amount', 'old_value': 31157.1, 'new_value': 32960.5}, {'field': 'count', 'old_value': 205, 'new_value': 222}, {'field': 'instoreAmount', 'old_value': 31157.1, 'new_value': 32960.5}, {'field': 'instoreCount', 'old_value': 205, 'new_value': 222}]
2025-05-26 08:10:21,161 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-26 08:10:21,161 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60590.7, 'new_value': 65809.7}, {'field': 'dailyBillAmount', 'old_value': 60590.7, 'new_value': 65809.7}, {'field': 'amount', 'old_value': 48086.9, 'new_value': 49943.6}, {'field': 'count', 'old_value': 436, 'new_value': 449}, {'field': 'instoreAmount', 'old_value': 48307.3, 'new_value': 50164.0}, {'field': 'instoreCount', 'old_value': 436, 'new_value': 449}]
2025-05-26 08:10:21,598 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-26 08:10:21,598 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53374.0, 'new_value': 54431.0}, {'field': 'dailyBillAmount', 'old_value': 53374.0, 'new_value': 54431.0}]
2025-05-26 08:10:22,005 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-26 08:10:22,005 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 222839.3, 'new_value': 241086.9}, {'field': 'dailyBillAmount', 'old_value': 222839.3, 'new_value': 241086.9}, {'field': 'amount', 'old_value': 176621.5, 'new_value': 186911.53}, {'field': 'count', 'old_value': 4922, 'new_value': 5202}, {'field': 'instoreAmount', 'old_value': 172076.13, 'new_value': 182674.76}, {'field': 'instoreCount', 'old_value': 4738, 'new_value': 5017}, {'field': 'onlineAmount', 'old_value': 7283.28, 'new_value': 7311.4800000000005}, {'field': 'onlineCount', 'old_value': 184, 'new_value': 185}]
2025-05-26 08:10:22,473 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-26 08:10:22,473 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50940.2, 'new_value': 53421.7}, {'field': 'dailyBillAmount', 'old_value': 50940.2, 'new_value': 53421.7}, {'field': 'amount', 'old_value': 50985.7, 'new_value': 53467.2}, {'field': 'count', 'old_value': 296, 'new_value': 311}, {'field': 'instoreAmount', 'old_value': 53501.9, 'new_value': 55983.4}, {'field': 'instoreCount', 'old_value': 293, 'new_value': 308}]
2025-05-26 08:10:22,927 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-26 08:10:22,927 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70015.65, 'new_value': 72971.49}, {'field': 'dailyBillAmount', 'old_value': 70015.65, 'new_value': 72971.49}]
2025-05-26 08:10:23,395 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-26 08:10:23,395 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46618.92, 'new_value': 49349.950000000004}, {'field': 'amount', 'old_value': 46618.68, 'new_value': 49349.71}, {'field': 'count', 'old_value': 2736, 'new_value': 2858}, {'field': 'instoreAmount', 'old_value': 47410.8, 'new_value': 50179.340000000004}, {'field': 'instoreCount', 'old_value': 2736, 'new_value': 2858}]
2025-05-26 08:10:23,895 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-26 08:10:23,895 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74246.1, 'new_value': 77261.87}, {'field': 'dailyBillAmount', 'old_value': 74246.1, 'new_value': 77261.87}, {'field': 'amount', 'old_value': 76477.54, 'new_value': 79544.06}, {'field': 'count', 'old_value': 3762, 'new_value': 3912}, {'field': 'instoreAmount', 'old_value': 71079.2, 'new_value': 73849.7}, {'field': 'instoreCount', 'old_value': 3526, 'new_value': 3662}, {'field': 'onlineAmount', 'old_value': 5480.39, 'new_value': 5776.41}, {'field': 'onlineCount', 'old_value': 236, 'new_value': 250}]
2025-05-26 08:10:24,364 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-26 08:10:24,364 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51265.67, 'new_value': 54243.909999999996}, {'field': 'amount', 'old_value': 51265.67, 'new_value': 54243.909999999996}, {'field': 'count', 'old_value': 2505, 'new_value': 2642}, {'field': 'instoreAmount', 'old_value': 31768.170000000002, 'new_value': 33486.19}, {'field': 'instoreCount', 'old_value': 1646, 'new_value': 1730}, {'field': 'onlineAmount', 'old_value': 19597.91, 'new_value': 20858.13}, {'field': 'onlineCount', 'old_value': 859, 'new_value': 912}]
2025-05-26 08:10:24,848 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-26 08:10:24,848 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 25659.95, 'new_value': 26531.45}, {'field': 'count', 'old_value': 1035, 'new_value': 1066}, {'field': 'instoreAmount', 'old_value': 25932.47, 'new_value': 26803.97}, {'field': 'instoreCount', 'old_value': 1035, 'new_value': 1066}]
2025-05-26 08:10:25,317 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-26 08:10:25,317 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67724.94, 'new_value': 70511.84}, {'field': 'amount', 'old_value': 67717.32, 'new_value': 70503.41}, {'field': 'count', 'old_value': 4038, 'new_value': 4180}, {'field': 'instoreAmount', 'old_value': 17838.31, 'new_value': 18399.51}, {'field': 'instoreCount', 'old_value': 1033, 'new_value': 1057}, {'field': 'onlineAmount', 'old_value': 51837.96, 'new_value': 54105.06}, {'field': 'onlineCount', 'old_value': 3005, 'new_value': 3123}]
2025-05-26 08:10:25,926 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-26 08:10:25,926 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 128901.32, 'new_value': 132467.71}, {'field': 'dailyBillAmount', 'old_value': 128901.32, 'new_value': 132467.71}, {'field': 'amount', 'old_value': 107461.56, 'new_value': 110136.06}, {'field': 'count', 'old_value': 1030, 'new_value': 1066}, {'field': 'instoreAmount', 'old_value': 107461.56, 'new_value': 110136.06}, {'field': 'instoreCount', 'old_value': 1030, 'new_value': 1066}]
2025-05-26 08:10:26,380 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-26 08:10:26,380 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 101391.15, 'new_value': 108022.15}, {'field': 'dailyBillAmount', 'old_value': 101391.15, 'new_value': 108022.15}, {'field': 'amount', 'old_value': 115787.8, 'new_value': 124120.8}, {'field': 'count', 'old_value': 496, 'new_value': 529}, {'field': 'instoreAmount', 'old_value': 115787.8, 'new_value': 124120.8}, {'field': 'instoreCount', 'old_value': 496, 'new_value': 529}]
2025-05-26 08:10:26,833 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-26 08:10:26,833 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65229.6, 'new_value': 69712.6}, {'field': 'dailyBillAmount', 'old_value': 65229.6, 'new_value': 69712.6}, {'field': 'amount', 'old_value': 53264.55, 'new_value': 56625.55}, {'field': 'count', 'old_value': 288, 'new_value': 303}, {'field': 'instoreAmount', 'old_value': 54701.55, 'new_value': 58062.55}, {'field': 'instoreCount', 'old_value': 288, 'new_value': 303}]
2025-05-26 08:10:27,223 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-26 08:10:27,223 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126332.0, 'new_value': 130165.0}, {'field': 'amount', 'old_value': 126332.0, 'new_value': 130165.0}, {'field': 'count', 'old_value': 1299, 'new_value': 1338}, {'field': 'instoreAmount', 'old_value': 126332.0, 'new_value': 130165.0}, {'field': 'instoreCount', 'old_value': 1299, 'new_value': 1338}]
2025-05-26 08:10:27,676 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-26 08:10:27,676 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31412.42, 'new_value': 31872.47}, {'field': 'dailyBillAmount', 'old_value': 31412.42, 'new_value': 31872.47}]
2025-05-26 08:10:28,176 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-26 08:10:28,176 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22320.63, 'new_value': 22936.9}, {'field': 'dailyBillAmount', 'old_value': 22320.63, 'new_value': 22936.9}, {'field': 'amount', 'old_value': 23043.06, 'new_value': 23690.7}, {'field': 'count', 'old_value': 626, 'new_value': 642}, {'field': 'instoreAmount', 'old_value': 22998.76, 'new_value': 23615.02}, {'field': 'instoreCount', 'old_value': 622, 'new_value': 637}, {'field': 'onlineAmount', 'old_value': 112.82, 'new_value': 144.2}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-05-26 08:10:28,661 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-26 08:10:28,661 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43778.1, 'new_value': 46294.8}, {'field': 'dailyBillAmount', 'old_value': 43778.1, 'new_value': 46294.8}, {'field': 'amount', 'old_value': 65842.6, 'new_value': 69380.5}, {'field': 'count', 'old_value': 265, 'new_value': 280}, {'field': 'instoreAmount', 'old_value': 66031.6, 'new_value': 69569.5}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 279}]
2025-05-26 08:10:29,192 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-26 08:10:29,192 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40597.0, 'new_value': 44127.0}, {'field': 'dailyBillAmount', 'old_value': 40597.0, 'new_value': 44127.0}, {'field': 'amount', 'old_value': 44154.0, 'new_value': 47439.0}, {'field': 'count', 'old_value': 242, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 44168.0, 'new_value': 47453.0}, {'field': 'instoreCount', 'old_value': 242, 'new_value': 262}]
2025-05-26 08:10:29,692 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-26 08:10:29,692 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73498.95, 'new_value': 75318.25}, {'field': 'dailyBillAmount', 'old_value': 73498.95, 'new_value': 75318.25}, {'field': 'amount', 'old_value': 65360.08, 'new_value': 66970.68}, {'field': 'count', 'old_value': 2197, 'new_value': 2255}, {'field': 'instoreAmount', 'old_value': 59634.62, 'new_value': 61051.92}, {'field': 'instoreCount', 'old_value': 1927, 'new_value': 1973}, {'field': 'onlineAmount', 'old_value': 5761.9, 'new_value': 5955.2}, {'field': 'onlineCount', 'old_value': 270, 'new_value': 282}]
2025-05-26 08:10:30,176 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-26 08:10:30,176 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42315.21, 'new_value': 45104.43}, {'field': 'dailyBillAmount', 'old_value': 42315.21, 'new_value': 45104.43}, {'field': 'amount', 'old_value': 46816.28, 'new_value': 50751.33}, {'field': 'count', 'old_value': 308, 'new_value': 333}, {'field': 'instoreAmount', 'old_value': 45558.21, 'new_value': 49345.61}, {'field': 'instoreCount', 'old_value': 256, 'new_value': 272}, {'field': 'onlineAmount', 'old_value': 1408.67, 'new_value': 1556.32}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 61}]
2025-05-26 08:10:30,567 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-26 08:10:30,567 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204127.94, 'new_value': 211154.24}, {'field': 'dailyBillAmount', 'old_value': 204127.94, 'new_value': 211154.24}, {'field': 'amount', 'old_value': 211763.8, 'new_value': 219105.8}, {'field': 'count', 'old_value': 1387, 'new_value': 1444}, {'field': 'instoreAmount', 'old_value': 204642.7, 'new_value': 211478.7}, {'field': 'instoreCount', 'old_value': 1240, 'new_value': 1294}, {'field': 'onlineAmount', 'old_value': 9988.1, 'new_value': 10568.1}, {'field': 'onlineCount', 'old_value': 147, 'new_value': 150}]
2025-05-26 08:10:30,567 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-26 08:10:30,567 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-26 08:10:30,567 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-26 08:10:30,567 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-26 08:10:30,567 - INFO - 正在批量插入月度数据，批次 1/1，共 4 条记录
2025-05-26 08:10:30,723 - INFO - 批量插入月度数据成功，批次 1，4 条记录
2025-05-26 08:10:33,739 - INFO - 批量插入月度数据完成: 总计 4 条，成功 4 条，失败 0 条
2025-05-26 08:10:33,739 - INFO - 批量插入月销售数据完成，共 4 条记录
2025-05-26 08:10:33,739 - INFO - 月销售数据同步完成！更新: 207 条，插入: 4 条，错误: 0 条，跳过: 981 条
2025-05-26 08:10:33,739 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-26 08:10:34,208 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250526.xlsx
2025-05-26 08:10:34,208 - INFO - 综合数据同步流程完成！
2025-05-26 08:10:34,301 - INFO - 综合数据同步完成
2025-05-26 08:10:34,301 - INFO - ==================================================
2025-05-26 08:10:34,301 - INFO - 程序退出
2025-05-26 08:10:34,301 - INFO - ==================================================
