2025-05-19 08:00:03,515 - INFO - ==================================================
2025-05-19 08:00:03,516 - INFO - 程序启动 - 版本 v1.0.0
2025-05-19 08:00:03,516 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250519.log
2025-05-19 08:00:03,516 - INFO - ==================================================
2025-05-19 08:00:03,516 - INFO - 程序入口点: __main__
2025-05-19 08:00:03,516 - INFO - ==================================================
2025-05-19 08:00:03,517 - INFO - 程序启动 - 版本 v1.0.1
2025-05-19 08:00:03,517 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250519.log
2025-05-19 08:00:03,517 - INFO - ==================================================
2025-05-19 08:00:03,816 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-19 08:00:03,817 - INFO - sales_data表已存在，无需创建
2025-05-19 08:00:03,818 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-19 08:00:03,818 - INFO - DataSyncManager初始化完成
2025-05-19 08:00:03,818 - INFO - 未提供日期参数，使用默认值
2025-05-19 08:00:03,819 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-19 08:00:03,819 - INFO - 开始综合数据同步流程...
2025-05-19 08:00:03,819 - INFO - 正在获取数衍平台日销售数据...
2025-05-19 08:00:03,819 - INFO - 查询数衍平台数据，时间段为: 2025-03-19, 2025-05-18
2025-05-19 08:00:03,819 - INFO - 正在获取********至********的数据
2025-05-19 08:00:03,820 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:03,820 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DE42288C3E9A1EBD851C3D538B86AECD'}
2025-05-19 08:00:06,906 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:06,920 - INFO - 过滤后保留 1562 条记录
2025-05-19 08:00:08,920 - INFO - 正在获取********至********的数据
2025-05-19 08:00:08,920 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:08,921 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '880E0072A5762B4D50A9493D32148089'}
2025-05-19 08:00:11,898 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:11,912 - INFO - 过滤后保留 1555 条记录
2025-05-19 08:00:13,913 - INFO - 正在获取********至********的数据
2025-05-19 08:00:13,913 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:13,914 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '531D156D1DD48F15ED594D55008DEE3A'}
2025-05-19 08:00:16,252 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:16,264 - INFO - 过滤后保留 1503 条记录
2025-05-19 08:00:18,266 - INFO - 正在获取********至********的数据
2025-05-19 08:00:18,266 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:18,267 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '883871EA935493131EE446CA98DB882D'}
2025-05-19 08:00:20,237 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:20,250 - INFO - 过滤后保留 1503 条记录
2025-05-19 08:00:22,251 - INFO - 正在获取********至********的数据
2025-05-19 08:00:22,251 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:22,252 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EBA8D8E91F9DB2D89FA3835FF7E3B22A'}
2025-05-19 08:00:24,536 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:24,549 - INFO - 过滤后保留 1502 条记录
2025-05-19 08:00:26,551 - INFO - 正在获取********至********的数据
2025-05-19 08:00:26,551 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:26,552 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FD63CC0830ECE0BAF6E22BE02EF60B9F'}
2025-05-19 08:00:28,340 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:28,351 - INFO - 过滤后保留 1481 条记录
2025-05-19 08:00:30,353 - INFO - 正在获取********至********的数据
2025-05-19 08:00:30,353 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:30,354 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D3FBA521873C23544DCCBAFD256FE920'}
2025-05-19 08:00:32,130 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:32,142 - INFO - 过滤后保留 1477 条记录
2025-05-19 08:00:34,144 - INFO - 正在获取********至********的数据
2025-05-19 08:00:34,144 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:34,145 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7583A47A8B74FFEE5419A2B293AA3671'}
2025-05-19 08:00:36,023 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:36,036 - INFO - 过滤后保留 1486 条记录
2025-05-19 08:00:38,037 - INFO - 正在获取********至********的数据
2025-05-19 08:00:38,037 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-19 08:00:38,038 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2B346486D485EE0E114F83D396DDE5E0'}
2025-05-19 08:00:39,321 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-19 08:00:39,330 - INFO - 过滤后保留 1052 条记录
2025-05-19 08:00:41,331 - INFO - 开始保存数据到SQLite数据库，共 13121 条记录待处理
2025-05-19 08:00:41,953 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-04-29
2025-05-19 08:00:41,953 - INFO - 变更字段: recommend_amount: 4879.23 -> 4849.33, amount: 4879 -> 4849, instore_amount: 4542.54 -> 4512.64
2025-05-19 08:00:41,954 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-04-27
2025-05-19 08:00:41,955 - INFO - 变更字段: recommend_amount: 6807.81 -> 5607.33, amount: 6807 -> 5607, instore_amount: 6560.96 -> 5360.48
2025-05-19 08:00:41,955 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-04-29
2025-05-19 08:00:41,956 - INFO - 变更字段: recommend_amount: 2465.12 -> 2303.4, amount: 2465 -> 2303, count: 163 -> 154, instore_amount: 1293.62 -> 1131.9, instore_count: 84 -> 75
2025-05-19 08:00:41,956 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-04-28
2025-05-19 08:00:41,956 - INFO - 变更字段: recommend_amount: 2249.21 -> 2182.12, amount: 2249 -> 2182, count: 153 -> 150, instore_amount: 1232.19 -> 1165.1, instore_count: 74 -> 71
2025-05-19 08:00:41,956 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-04-27
2025-05-19 08:00:41,957 - INFO - 变更字段: recommend_amount: 2188.66 -> 2032.53, amount: 2188 -> 2032, count: 173 -> 166, instore_amount: 817.33 -> 661.2, instore_count: 87 -> 80
2025-05-19 08:00:41,957 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-04-25
2025-05-19 08:00:41,957 - INFO - 变更字段: recommend_amount: 2587.27 -> 2510.41, amount: 2587 -> 2510, count: 190 -> 187, instore_amount: 1442.96 -> 1366.1, instore_count: 108 -> 105
2025-05-19 08:00:41,957 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-04-24
2025-05-19 08:00:41,958 - INFO - 变更字段: recommend_amount: 2703.17 -> 2674.18, amount: 2703 -> 2674, count: 180 -> 179, instore_amount: 1350.19 -> 1321.2, instore_count: 86 -> 85
2025-05-19 08:00:42,019 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS, sale_time=2025-04-30
2025-05-19 08:00:42,019 - INFO - 变更字段: amount: 5776 -> 5058
2025-05-19 08:00:42,022 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-05
2025-05-19 08:00:42,022 - INFO - 变更字段: recommend_amount: 6194.99 -> 6133.49, amount: 6194 -> 6133, instore_amount: 5907.81 -> 5846.31
2025-05-19 08:00:42,022 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-04
2025-05-19 08:00:42,022 - INFO - 变更字段: recommend_amount: 5079.06 -> 5059.16, amount: 5079 -> 5059, instore_amount: 4978.15 -> 4958.25
2025-05-19 08:00:42,023 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-03
2025-05-19 08:00:42,023 - INFO - 变更字段: recommend_amount: 7466.35 -> 7458.75, amount: 7466 -> 7458, instore_amount: 6919.87 -> 6912.27
2025-05-19 08:00:42,023 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-01
2025-05-19 08:00:42,023 - INFO - 变更字段: recommend_amount: 8619.82 -> 8612.02, amount: 8619 -> 8612, instore_amount: 8264.74 -> 8256.94
2025-05-19 08:00:42,024 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-04-30
2025-05-19 08:00:42,024 - INFO - 变更字段: recommend_amount: 4768.87 -> 4758.97, amount: 4768 -> 4758, instore_amount: 4432.86 -> 4422.96
2025-05-19 08:00:42,025 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-06
2025-05-19 08:00:42,025 - INFO - 变更字段: recommend_amount: 2706.92 -> 2674.54, amount: 2706 -> 2674, count: 199 -> 197, instore_amount: 1010.18 -> 977.8, instore_count: 85 -> 83
2025-05-19 08:00:42,025 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-05
2025-05-19 08:00:42,025 - INFO - 变更字段: recommend_amount: 3699.32 -> 3604.15, amount: 3699 -> 3604, count: 233 -> 229, instore_amount: 1854.07 -> 1758.9, instore_count: 124 -> 120
2025-05-19 08:00:42,025 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-03
2025-05-19 08:00:42,026 - INFO - 变更字段: recommend_amount: 4161.02 -> 4138.33, amount: 4161 -> 4138, count: 254 -> 253, instore_amount: 1962.19 -> 1939.5, instore_count: 118 -> 117
2025-05-19 08:00:42,026 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-02
2025-05-19 08:00:42,026 - INFO - 变更字段: recommend_amount: 4051.13 -> 4029.44, amount: 4051 -> 4029, count: 179 -> 178, instore_amount: 2426.34 -> 2404.65, instore_count: 74 -> 73
2025-05-19 08:00:42,026 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-01
2025-05-19 08:00:42,027 - INFO - 变更字段: recommend_amount: 4718.99 -> 4638.02, amount: 4718 -> 4638, count: 205 -> 202, instore_amount: 3183.31 -> 3102.34, instore_count: 98 -> 95
2025-05-19 08:00:42,027 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-04-30
2025-05-19 08:00:42,027 - INFO - 变更字段: recommend_amount: 2807.74 -> 2705.41, amount: 2807 -> 2705, count: 170 -> 165, instore_amount: 1329.73 -> 1227.4, instore_count: 67 -> 62
2025-05-19 08:00:42,090 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-13
2025-05-19 08:00:42,091 - INFO - 变更字段: recommend_amount: 4595.21 -> 4591.41, amount: 4595 -> 4591, instore_amount: 4436.27 -> 4432.47
2025-05-19 08:00:42,091 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-12
2025-05-19 08:00:42,092 - INFO - 变更字段: recommend_amount: 3485.07 -> 3481.27, amount: 3485 -> 3481, instore_amount: 3232.17 -> 3228.37
2025-05-19 08:00:42,092 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-11
2025-05-19 08:00:42,092 - INFO - 变更字段: recommend_amount: 6249.31 -> 6245.51, amount: 6249 -> 6245, instore_amount: 6088.99 -> 6085.19
2025-05-19 08:00:42,092 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-09
2025-05-19 08:00:42,093 - INFO - 变更字段: recommend_amount: 4940.68 -> 4936.88, amount: 4940 -> 4936, instore_amount: 4759.24 -> 4755.44
2025-05-19 08:00:42,093 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-07
2025-05-19 08:00:42,093 - INFO - 变更字段: recommend_amount: 4550.69 -> 4523.79, amount: 4550 -> 4523, instore_amount: 4384.84 -> 4357.94
2025-05-19 08:00:42,094 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-13
2025-05-19 08:00:42,094 - INFO - 变更字段: recommend_amount: 2815.96 -> 2668.19, amount: 2815 -> 2668, count: 185 -> 176, instore_amount: 1250.07 -> 1102.3, instore_count: 76 -> 67
2025-05-19 08:00:42,094 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-12
2025-05-19 08:00:42,094 - INFO - 变更字段: recommend_amount: 1823.66 -> 1775.79, amount: 1823 -> 1775, count: 115 -> 112, instore_amount: 723.78 -> 675.91, instore_count: 50 -> 47
2025-05-19 08:00:42,095 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-11
2025-05-19 08:00:42,095 - INFO - 变更字段: recommend_amount: 3130.87 -> 3010.62, amount: 3130 -> 3010, count: 186 -> 181, instore_amount: 1626.79 -> 1506.54, instore_count: 97 -> 92
2025-05-19 08:00:42,095 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-10
2025-05-19 08:00:42,095 - INFO - 变更字段: recommend_amount: 3824.34 -> 3585.68, amount: 3824 -> 3585, count: 232 -> 220, instore_amount: 2133.36 -> 1894.7, instore_count: 132 -> 120
2025-05-19 08:00:42,096 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-09
2025-05-19 08:00:42,096 - INFO - 变更字段: recommend_amount: 3351.24 -> 3210.11, amount: 3351 -> 3210, count: 216 -> 209, instore_amount: 1309.38 -> 1168.25, instore_count: 81 -> 74
2025-05-19 08:00:42,096 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-08
2025-05-19 08:00:42,096 - INFO - 变更字段: recommend_amount: 2757.24 -> 2728.55, amount: 2757 -> 2728, count: 198 -> 197, instore_amount: 779.39 -> 750.7, instore_count: 57 -> 56
2025-05-19 08:00:42,097 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-07
2025-05-19 08:00:42,097 - INFO - 变更字段: recommend_amount: 2398.47 -> 2356.09, amount: 2398 -> 2356, count: 178 -> 176, instore_amount: 872.78 -> 830.4, instore_count: 63 -> 61
2025-05-19 08:00:42,109 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-17
2025-05-19 08:00:42,109 - INFO - 变更字段: recommend_amount: 0.0 -> 9208.3, daily_bill_amount: 0.0 -> 9208.3
2025-05-19 08:00:42,116 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE29HIJ7QK7Q2OV4FVC7F40014BL, sale_time=2025-05-17
2025-05-19 08:00:42,116 - INFO - 变更字段: recommend_amount: 3588.5 -> 2889.5, amount: 3588 -> 2889
2025-05-19 08:00:42,119 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-05-17
2025-05-19 08:00:42,119 - INFO - 变更字段: amount: 3930 -> 3946, count: 217 -> 218, online_amount: 2148.18 -> 2164.18, online_count: 120 -> 121
2025-05-19 08:00:42,123 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-05-17
2025-05-19 08:00:42,124 - INFO - 变更字段: recommend_amount: 11593.6 -> 11393.6, amount: 11593 -> 11393
2025-05-19 08:00:42,125 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-05-17
2025-05-19 08:00:42,125 - INFO - 变更字段: amount: 5054 -> 5340, count: 13 -> 14, instore_amount: 5054.6 -> 5340.6, instore_count: 13 -> 14
2025-05-19 08:00:42,127 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-17
2025-05-19 08:00:42,127 - INFO - 变更字段: recommend_amount: 0.0 -> 9878.0, daily_bill_amount: 0.0 -> 9878.0
2025-05-19 08:00:42,128 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-05-17
2025-05-19 08:00:42,129 - INFO - 变更字段: recommend_amount: 17674.0 -> 42171.0, amount: 17674 -> 42171, count: 56 -> 95, instore_amount: 17674.0 -> 42171.0, instore_count: 56 -> 95
2025-05-19 08:00:42,129 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-05-17
2025-05-19 08:00:42,129 - INFO - 变更字段: amount: -35477 -> -35685
2025-05-19 08:00:42,130 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-17
2025-05-19 08:00:42,130 - INFO - 变更字段: amount: 4173 -> 5505, count: 5 -> 6, instore_amount: 4173.0 -> 5505.0, instore_count: 5 -> 6
2025-05-19 08:00:42,130 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-16
2025-05-19 08:00:42,130 - INFO - 变更字段: amount: 7512 -> 7957, count: 13 -> 17, instore_amount: 7512.0 -> 7957.0, instore_count: 13 -> 17
2025-05-19 08:00:42,132 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-05-17
2025-05-19 08:00:42,132 - INFO - 变更字段: recommend_amount: 0.0 -> 1191.0, daily_bill_amount: 0.0 -> 1191.0
2025-05-19 08:00:42,135 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-17
2025-05-19 08:00:42,135 - INFO - 变更字段: recommend_amount: 3059.08 -> 3059.29, count: 157 -> 159, online_amount: 1968.4 -> 1968.61, online_count: 100 -> 102
2025-05-19 08:00:42,137 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-05-17
2025-05-19 08:00:42,137 - INFO - 变更字段: recommend_amount: 1730.03 -> 1727.24, amount: 1730 -> 1727, instore_amount: 1780.34 -> 1777.55
2025-05-19 08:00:42,139 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-17
2025-05-19 08:00:42,139 - INFO - 变更字段: recommend_amount: 11343.68 -> 11553.37, amount: 11343 -> 11553, count: 216 -> 218, instore_amount: 10876.03 -> 10965.73, instore_count: 204 -> 205, online_amount: 467.65 -> 587.64, online_count: 12 -> 13
2025-05-19 08:00:42,141 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-17
2025-05-19 08:00:42,141 - INFO - 变更字段: recommend_amount: 1556.21 -> 1554.18, amount: 1556 -> 1554, count: 118 -> 119, online_amount: 820.08 -> 818.05, online_count: 66 -> 67
2025-05-19 08:00:42,143 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-15
2025-05-19 08:00:42,143 - INFO - 变更字段: recommend_amount: 5766.82 -> 5769.82, amount: 5766 -> 5769, count: 264 -> 265, online_amount: 4424.34 -> 4427.34, online_count: 193 -> 194
2025-05-19 08:00:42,144 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-05-17
2025-05-19 08:00:42,144 - INFO - 变更字段: amount: 6493 -> 6501, count: 141 -> 144, instore_amount: 3911.73 -> 3919.32, instore_count: 36 -> 39
2025-05-19 08:00:42,145 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-17
2025-05-19 08:00:42,145 - INFO - 变更字段: recommend_amount: 0.0 -> 32275.89, daily_bill_amount: 0.0 -> 32275.89
2025-05-19 08:00:42,146 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAB2RFI0N0I86N3H2U1VD001F8J, sale_time=2025-05-17
2025-05-19 08:00:42,146 - INFO - 变更字段: recommend_amount: 5683.0 -> 8635.0, amount: 5683 -> 8635, count: 30 -> 31, instore_amount: 5683.0 -> 8635.0, instore_count: 30 -> 31
2025-05-19 08:00:42,147 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-17
2025-05-19 08:00:42,148 - INFO - 变更字段: amount: 27969 -> 29589, count: 289 -> 293, instore_amount: 21493.8 -> 23114.1, instore_count: 152 -> 156
2025-05-19 08:00:42,149 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-17
2025-05-19 08:00:42,149 - INFO - 变更字段: amount: 37957 -> 39410, count: 270 -> 275, instore_amount: 22087.4 -> 23540.4, instore_count: 125 -> 130
2025-05-19 08:00:42,150 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-17
2025-05-19 08:00:42,150 - INFO - 变更字段: amount: 60494 -> 60793, count: 348 -> 349, instore_amount: 47119.91 -> 47418.91, instore_count: 192 -> 193
2025-05-19 08:00:42,153 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-17
2025-05-19 08:00:42,153 - INFO - 变更字段: amount: 31716 -> 31730, count: 323 -> 324, instore_amount: 24974.61 -> 24988.41, instore_count: 137 -> 138
2025-05-19 08:00:42,153 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-17
2025-05-19 08:00:42,154 - INFO - 变更字段: amount: 94 -> 485, count: 13 -> 45, instore_amount: 103.8 -> 522.9, instore_count: 13 -> 45
2025-05-19 08:00:42,154 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDQR0OPQVI0I86N3H2U1NE001F0K, sale_time=2025-05-17
2025-05-19 08:00:42,154 - INFO - 变更字段: recommend_amount: 6039.0 -> 13248.0, amount: 6039 -> 13248, count: 2 -> 3, instore_amount: 6039.0 -> 13248.0, instore_count: 2 -> 3
2025-05-19 08:00:42,155 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-05-16
2025-05-19 08:00:42,155 - INFO - 变更字段: recommend_amount: 8618.78 -> 8989.98, amount: 8618 -> 8989, count: 15 -> 16, instore_amount: 8618.78 -> 8989.98, instore_count: 15 -> 16
2025-05-19 08:00:42,157 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-17
2025-05-19 08:00:42,158 - INFO - 变更字段: amount: 2114 -> 2071, count: 27 -> 28, instore_amount: 1068.26 -> 1154.28, instore_count: 16 -> 17
2025-05-19 08:00:42,161 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-05-17
2025-05-19 08:00:42,161 - INFO - 变更字段: recommend_amount: 3318.0 -> 4226.0, daily_bill_amount: 0.0 -> 4226.0
2025-05-19 08:00:42,163 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-05-17
2025-05-19 08:00:42,163 - INFO - 变更字段: amount: 3182 -> 3215, count: 120 -> 121, online_amount: 1808.88 -> 1841.48, online_count: 67 -> 68
2025-05-19 08:00:42,165 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-17
2025-05-19 08:00:42,165 - INFO - 变更字段: amount: 5175 -> 5213, count: 342 -> 345, online_amount: 4688.62 -> 4726.12, online_count: 299 -> 302
2025-05-19 08:00:42,165 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-05-17
2025-05-19 08:00:42,166 - INFO - 变更字段: recommend_amount: 0.0 -> 17273.47, daily_bill_amount: 0.0 -> 17273.47
2025-05-19 08:00:42,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSK489TE20I86N3H2U114001EAA, sale_time=2025-05-17
2025-05-19 08:00:42,166 - INFO - 变更字段: recommend_amount: 8186.57 -> 8236.07, amount: 8186 -> 8236, count: 154 -> 155, instore_amount: 7746.95 -> 7796.45, instore_count: 149 -> 150
2025-05-19 08:00:42,169 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-17
2025-05-19 08:00:42,169 - INFO - 变更字段: amount: 7075 -> 7169, count: 508 -> 513, instore_amount: 5579.39 -> 5594.64, instore_count: 384 -> 390, online_amount: 1665.7 -> 1744.25, online_count: 124 -> 123
2025-05-19 08:00:42,170 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-16
2025-05-19 08:00:42,170 - INFO - 变更字段: instore_amount: 7392.0 -> 7405.0, instore_count: 663 -> 664, online_amount: 161.89 -> 148.89, online_count: 13 -> 12
2025-05-19 08:00:42,173 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-16
2025-05-19 08:00:42,173 - INFO - 变更字段: recommend_amount: 16842.75 -> 18772.36, amount: 16842 -> 18772, count: 780 -> 863, online_amount: 17278.24 -> 19207.85, online_count: 780 -> 863
2025-05-19 08:00:42,175 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MRVUM0P77G7Q2OV78BKOG4001PUK, sale_time=2025-05-17
2025-05-19 08:00:42,175 - INFO - 变更字段: count: 119 -> 120, instore_count: 109 -> 110
2025-05-19 08:00:42,176 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-05-16
2025-05-19 08:00:42,176 - INFO - 变更字段: recommend_amount: 5372.65 -> 5283.65, amount: 5372 -> 5283, instore_amount: 5233.18 -> 5144.18
2025-05-19 08:00:42,177 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-17
2025-05-19 08:00:42,178 - INFO - 变更字段: recommend_amount: 4580.07 -> 4514.79, amount: 4580 -> 4514, count: 334 -> 329, instore_amount: 2150.62 -> 2085.34, instore_count: 155 -> 150
2025-05-19 08:00:42,178 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-16
2025-05-19 08:00:42,178 - INFO - 变更字段: recommend_amount: 3240.22 -> 2998.86, amount: 3240 -> 2998, count: 185 -> 171, instore_amount: 1655.46 -> 1414.1, instore_count: 96 -> 82
2025-05-19 08:00:42,179 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-15
2025-05-19 08:00:42,179 - INFO - 变更字段: recommend_amount: 3050.79 -> 2925.95, amount: 3050 -> 2925, count: 180 -> 172, instore_amount: 1209.59 -> 1084.75, instore_count: 73 -> 65
2025-05-19 08:00:42,179 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-14
2025-05-19 08:00:42,179 - INFO - 变更字段: recommend_amount: 2418.4 -> 2347.27, amount: 2418 -> 2347, count: 155 -> 151, instore_amount: 1166.53 -> 1095.4, instore_count: 70 -> 66
2025-05-19 08:00:42,179 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-05-17
2025-05-19 08:00:42,180 - INFO - 变更字段: recommend_amount: 6278.42 -> 6313.42, amount: 6278 -> 6313, count: 337 -> 339, instore_amount: 4438.04 -> 4473.04, instore_count: 223 -> 225
2025-05-19 08:00:42,180 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-17
2025-05-19 08:00:42,181 - INFO - 变更字段: amount: 29880 -> 30643, count: 268 -> 271, instore_amount: 28046.94 -> 28809.64, instore_count: 199 -> 202
2025-05-19 08:00:42,182 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-17
2025-05-19 08:00:42,182 - INFO - 变更字段: amount: 24108 -> 26319, count: 90 -> 91, instore_amount: 24342.6 -> 26553.6, instore_count: 63 -> 64
2025-05-19 08:00:42,185 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS34G4B697AV8LHQQGIDL001EJU, sale_time=2025-05-17
2025-05-19 08:00:42,187 - INFO - 变更字段: recommend_amount: 39971.42 -> 39991.68, amount: 39971 -> 39991, count: 228 -> 229, online_amount: 1772.42 -> 1792.68, online_count: 57 -> 58
2025-05-19 08:00:42,190 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQP8527T06E7AERKQ83JQ001UNS, sale_time=2025-05-17
2025-05-19 08:00:42,190 - INFO - 变更字段: amount: 962 -> 943
2025-05-19 08:00:42,453 - INFO - SQLite数据保存完成，统计信息：
2025-05-19 08:00:42,453 - INFO - - 总记录数: 13121
2025-05-19 08:00:42,454 - INFO - - 成功插入: 215
2025-05-19 08:00:42,454 - INFO - - 成功更新: 77
2025-05-19 08:00:42,454 - INFO - - 无需更新: 12829
2025-05-19 08:00:42,454 - INFO - - 处理失败: 0
2025-05-19 08:00:47,916 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250519.xlsx
2025-05-19 08:00:47,924 - INFO - 成功获取数衍平台数据，共 13121 条记录
2025-05-19 08:00:47,924 - INFO - 正在更新SQLite月度汇总数据...
2025-05-19 08:00:47,932 - INFO - 月度数据sqllite清空完成
2025-05-19 08:00:48,180 - INFO - 月度汇总数据更新完成，处理了 1190 条汇总记录
2025-05-19 08:00:48,180 - INFO - 成功更新月度汇总数据，共 1190 条记录
2025-05-19 08:00:48,181 - INFO - 正在获取宜搭日销售表单数据...
2025-05-19 08:00:48,181 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-19 00:00:00 至 2025-05-18 23:59:59
2025-05-19 08:00:48,181 - INFO - 查询分段 1: 2025-03-19 至 2025-03-25
2025-05-19 08:00:48,182 - INFO - 查询日期范围: 2025-03-19 至 2025-03-25，使用分页查询，每页 100 条记录
2025-05-19 08:00:48,182 - INFO - Request Parameters - Page 1:
2025-05-19 08:00:48,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:00:48,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:00:51,394 - INFO - API请求耗时: 3212ms
2025-05-19 08:00:51,394 - INFO - Response - Page 1
2025-05-19 08:00:51,395 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:00:51,896 - INFO - Request Parameters - Page 2:
2025-05-19 08:00:51,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:00:51,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:00:52,574 - INFO - API请求耗时: 677ms
2025-05-19 08:00:52,575 - INFO - Response - Page 2
2025-05-19 08:00:52,575 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:00:53,076 - INFO - Request Parameters - Page 3:
2025-05-19 08:00:53,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:00:53,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:00:57,593 - INFO - API请求耗时: 4515ms
2025-05-19 08:00:57,593 - INFO - Response - Page 3
2025-05-19 08:00:57,593 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:00:58,094 - INFO - Request Parameters - Page 4:
2025-05-19 08:00:58,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:00:58,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:00:58,797 - INFO - API请求耗时: 702ms
2025-05-19 08:00:58,798 - INFO - Response - Page 4
2025-05-19 08:00:58,798 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:00:59,299 - INFO - Request Parameters - Page 5:
2025-05-19 08:00:59,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:00:59,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:00:59,928 - INFO - API请求耗时: 629ms
2025-05-19 08:00:59,928 - INFO - Response - Page 5
2025-05-19 08:00:59,929 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:01:00,430 - INFO - Request Parameters - Page 6:
2025-05-19 08:01:00,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:00,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:01,190 - INFO - API请求耗时: 759ms
2025-05-19 08:01:01,191 - INFO - Response - Page 6
2025-05-19 08:01:01,191 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:01:01,693 - INFO - Request Parameters - Page 7:
2025-05-19 08:01:01,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:01,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:02,427 - INFO - API请求耗时: 733ms
2025-05-19 08:01:02,428 - INFO - Response - Page 7
2025-05-19 08:01:02,428 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:01:02,928 - INFO - Request Parameters - Page 8:
2025-05-19 08:01:02,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:02,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:03,599 - INFO - API请求耗时: 670ms
2025-05-19 08:01:03,600 - INFO - Response - Page 8
2025-05-19 08:01:03,600 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:01:04,102 - INFO - Request Parameters - Page 9:
2025-05-19 08:01:04,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:04,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:04,821 - INFO - API请求耗时: 718ms
2025-05-19 08:01:04,821 - INFO - Response - Page 9
2025-05-19 08:01:04,822 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:01:05,323 - INFO - Request Parameters - Page 10:
2025-05-19 08:01:05,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:05,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:05,983 - INFO - API请求耗时: 659ms
2025-05-19 08:01:05,983 - INFO - Response - Page 10
2025-05-19 08:01:05,984 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:01:06,485 - INFO - Request Parameters - Page 11:
2025-05-19 08:01:06,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:06,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:07,329 - INFO - API请求耗时: 843ms
2025-05-19 08:01:07,330 - INFO - Response - Page 11
2025-05-19 08:01:07,330 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:01:07,832 - INFO - Request Parameters - Page 12:
2025-05-19 08:01:07,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:07,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:08,513 - INFO - API请求耗时: 681ms
2025-05-19 08:01:08,514 - INFO - Response - Page 12
2025-05-19 08:01:08,514 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:01:09,016 - INFO - Request Parameters - Page 13:
2025-05-19 08:01:09,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:09,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:09,865 - INFO - API请求耗时: 848ms
2025-05-19 08:01:09,866 - INFO - Response - Page 13
2025-05-19 08:01:09,866 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:01:10,367 - INFO - Request Parameters - Page 14:
2025-05-19 08:01:10,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:10,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:11,138 - INFO - API请求耗时: 770ms
2025-05-19 08:01:11,138 - INFO - Response - Page 14
2025-05-19 08:01:11,138 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:01:11,640 - INFO - Request Parameters - Page 15:
2025-05-19 08:01:11,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:11,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:12,303 - INFO - API请求耗时: 662ms
2025-05-19 08:01:12,303 - INFO - Response - Page 15
2025-05-19 08:01:12,304 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:01:12,805 - INFO - Request Parameters - Page 16:
2025-05-19 08:01:12,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:12,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:13,468 - INFO - API请求耗时: 662ms
2025-05-19 08:01:13,468 - INFO - Response - Page 16
2025-05-19 08:01:13,469 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:01:13,970 - INFO - Request Parameters - Page 17:
2025-05-19 08:01:13,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:13,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:14,660 - INFO - API请求耗时: 688ms
2025-05-19 08:01:14,660 - INFO - Response - Page 17
2025-05-19 08:01:14,661 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:01:15,161 - INFO - Request Parameters - Page 18:
2025-05-19 08:01:15,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:15,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:15,897 - INFO - API请求耗时: 735ms
2025-05-19 08:01:15,897 - INFO - Response - Page 18
2025-05-19 08:01:15,898 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:01:16,401 - INFO - Request Parameters - Page 19:
2025-05-19 08:01:16,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:16,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:17,066 - INFO - API请求耗时: 663ms
2025-05-19 08:01:17,067 - INFO - Response - Page 19
2025-05-19 08:01:17,068 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:01:17,568 - INFO - Request Parameters - Page 20:
2025-05-19 08:01:17,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:17,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:18,310 - INFO - API请求耗时: 741ms
2025-05-19 08:01:18,311 - INFO - Response - Page 20
2025-05-19 08:01:18,311 - INFO - 第 20 页获取到 100 条记录
2025-05-19 08:01:18,811 - INFO - Request Parameters - Page 21:
2025-05-19 08:01:18,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:18,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742313600181, 1742832000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:19,225 - INFO - API请求耗时: 413ms
2025-05-19 08:01:19,225 - INFO - Response - Page 21
2025-05-19 08:01:19,226 - INFO - 第 21 页获取到 12 条记录
2025-05-19 08:01:19,226 - INFO - 查询完成，共获取到 2012 条记录
2025-05-19 08:01:19,226 - INFO - 分段 1 查询成功，获取到 2012 条记录
2025-05-19 08:01:20,226 - INFO - 查询分段 2: 2025-03-26 至 2025-04-01
2025-05-19 08:01:20,226 - INFO - 查询日期范围: 2025-03-26 至 2025-04-01，使用分页查询，每页 100 条记录
2025-05-19 08:01:20,227 - INFO - Request Parameters - Page 1:
2025-05-19 08:01:20,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:20,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:21,050 - INFO - API请求耗时: 823ms
2025-05-19 08:01:21,051 - INFO - Response - Page 1
2025-05-19 08:01:21,052 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:01:21,553 - INFO - Request Parameters - Page 2:
2025-05-19 08:01:21,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:21,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:22,230 - INFO - API请求耗时: 676ms
2025-05-19 08:01:22,231 - INFO - Response - Page 2
2025-05-19 08:01:22,231 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:01:22,732 - INFO - Request Parameters - Page 3:
2025-05-19 08:01:22,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:22,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:23,379 - INFO - API请求耗时: 646ms
2025-05-19 08:01:23,379 - INFO - Response - Page 3
2025-05-19 08:01:23,380 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:01:23,880 - INFO - Request Parameters - Page 4:
2025-05-19 08:01:23,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:23,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:24,493 - INFO - API请求耗时: 611ms
2025-05-19 08:01:24,494 - INFO - Response - Page 4
2025-05-19 08:01:24,494 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:01:24,995 - INFO - Request Parameters - Page 5:
2025-05-19 08:01:24,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:24,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:25,625 - INFO - API请求耗时: 629ms
2025-05-19 08:01:25,625 - INFO - Response - Page 5
2025-05-19 08:01:25,626 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:01:26,126 - INFO - Request Parameters - Page 6:
2025-05-19 08:01:26,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:26,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:26,763 - INFO - API请求耗时: 636ms
2025-05-19 08:01:26,763 - INFO - Response - Page 6
2025-05-19 08:01:26,764 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:01:27,264 - INFO - Request Parameters - Page 7:
2025-05-19 08:01:27,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:27,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:27,885 - INFO - API请求耗时: 620ms
2025-05-19 08:01:27,885 - INFO - Response - Page 7
2025-05-19 08:01:27,886 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:01:28,387 - INFO - Request Parameters - Page 8:
2025-05-19 08:01:28,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:28,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:29,109 - INFO - API请求耗时: 721ms
2025-05-19 08:01:29,109 - INFO - Response - Page 8
2025-05-19 08:01:29,110 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:01:29,610 - INFO - Request Parameters - Page 9:
2025-05-19 08:01:29,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:29,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:30,229 - INFO - API请求耗时: 618ms
2025-05-19 08:01:30,229 - INFO - Response - Page 9
2025-05-19 08:01:30,230 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:01:30,730 - INFO - Request Parameters - Page 10:
2025-05-19 08:01:30,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:30,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:31,470 - INFO - API请求耗时: 739ms
2025-05-19 08:01:31,470 - INFO - Response - Page 10
2025-05-19 08:01:31,471 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:01:31,971 - INFO - Request Parameters - Page 11:
2025-05-19 08:01:31,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:31,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:32,654 - INFO - API请求耗时: 682ms
2025-05-19 08:01:32,655 - INFO - Response - Page 11
2025-05-19 08:01:32,655 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:01:33,157 - INFO - Request Parameters - Page 12:
2025-05-19 08:01:33,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:33,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:33,887 - INFO - API请求耗时: 730ms
2025-05-19 08:01:33,888 - INFO - Response - Page 12
2025-05-19 08:01:33,889 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:01:34,390 - INFO - Request Parameters - Page 13:
2025-05-19 08:01:34,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:34,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:35,077 - INFO - API请求耗时: 685ms
2025-05-19 08:01:35,078 - INFO - Response - Page 13
2025-05-19 08:01:35,079 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:01:35,580 - INFO - Request Parameters - Page 14:
2025-05-19 08:01:35,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:35,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:36,241 - INFO - API请求耗时: 660ms
2025-05-19 08:01:36,242 - INFO - Response - Page 14
2025-05-19 08:01:36,242 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:01:36,743 - INFO - Request Parameters - Page 15:
2025-05-19 08:01:36,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:36,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:37,675 - INFO - API请求耗时: 932ms
2025-05-19 08:01:37,675 - INFO - Response - Page 15
2025-05-19 08:01:37,676 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:01:38,177 - INFO - Request Parameters - Page 16:
2025-05-19 08:01:38,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:38,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:38,798 - INFO - API请求耗时: 620ms
2025-05-19 08:01:38,799 - INFO - Response - Page 16
2025-05-19 08:01:38,799 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:01:39,300 - INFO - Request Parameters - Page 17:
2025-05-19 08:01:39,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:39,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:39,885 - INFO - API请求耗时: 584ms
2025-05-19 08:01:39,885 - INFO - Response - Page 17
2025-05-19 08:01:39,886 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:01:40,387 - INFO - Request Parameters - Page 18:
2025-05-19 08:01:40,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:40,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400181, 1743436800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:41,002 - INFO - API请求耗时: 614ms
2025-05-19 08:01:41,003 - INFO - Response - Page 18
2025-05-19 08:01:41,003 - INFO - 第 18 页获取到 68 条记录
2025-05-19 08:01:41,003 - INFO - 查询完成，共获取到 1768 条记录
2025-05-19 08:01:41,003 - INFO - 分段 2 查询成功，获取到 1768 条记录
2025-05-19 08:01:42,004 - INFO - 查询分段 3: 2025-04-02 至 2025-04-08
2025-05-19 08:01:42,004 - INFO - 查询日期范围: 2025-04-02 至 2025-04-08，使用分页查询，每页 100 条记录
2025-05-19 08:01:42,004 - INFO - Request Parameters - Page 1:
2025-05-19 08:01:42,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:42,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:42,621 - INFO - API请求耗时: 615ms
2025-05-19 08:01:42,622 - INFO - Response - Page 1
2025-05-19 08:01:42,622 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:01:43,123 - INFO - Request Parameters - Page 2:
2025-05-19 08:01:43,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:43,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:44,056 - INFO - API请求耗时: 932ms
2025-05-19 08:01:44,057 - INFO - Response - Page 2
2025-05-19 08:01:44,057 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:01:44,559 - INFO - Request Parameters - Page 3:
2025-05-19 08:01:44,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:44,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:45,219 - INFO - API请求耗时: 659ms
2025-05-19 08:01:45,219 - INFO - Response - Page 3
2025-05-19 08:01:45,220 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:01:45,721 - INFO - Request Parameters - Page 4:
2025-05-19 08:01:45,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:45,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:46,341 - INFO - API请求耗时: 619ms
2025-05-19 08:01:46,342 - INFO - Response - Page 4
2025-05-19 08:01:46,342 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:01:46,843 - INFO - Request Parameters - Page 5:
2025-05-19 08:01:46,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:46,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:47,486 - INFO - API请求耗时: 642ms
2025-05-19 08:01:47,487 - INFO - Response - Page 5
2025-05-19 08:01:47,487 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:01:47,988 - INFO - Request Parameters - Page 6:
2025-05-19 08:01:47,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:47,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:48,622 - INFO - API请求耗时: 633ms
2025-05-19 08:01:48,622 - INFO - Response - Page 6
2025-05-19 08:01:48,622 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:01:49,124 - INFO - Request Parameters - Page 7:
2025-05-19 08:01:49,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:49,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:49,890 - INFO - API请求耗时: 765ms
2025-05-19 08:01:49,891 - INFO - Response - Page 7
2025-05-19 08:01:49,891 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:01:50,393 - INFO - Request Parameters - Page 8:
2025-05-19 08:01:50,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:50,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:51,078 - INFO - API请求耗时: 685ms
2025-05-19 08:01:51,078 - INFO - Response - Page 8
2025-05-19 08:01:51,079 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:01:51,580 - INFO - Request Parameters - Page 9:
2025-05-19 08:01:51,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:51,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:52,353 - INFO - API请求耗时: 772ms
2025-05-19 08:01:52,353 - INFO - Response - Page 9
2025-05-19 08:01:52,354 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:01:52,854 - INFO - Request Parameters - Page 10:
2025-05-19 08:01:52,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:52,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:53,558 - INFO - API请求耗时: 703ms
2025-05-19 08:01:53,559 - INFO - Response - Page 10
2025-05-19 08:01:53,559 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:01:54,060 - INFO - Request Parameters - Page 11:
2025-05-19 08:01:54,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:54,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:54,655 - INFO - API请求耗时: 594ms
2025-05-19 08:01:54,655 - INFO - Response - Page 11
2025-05-19 08:01:54,656 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:01:55,156 - INFO - Request Parameters - Page 12:
2025-05-19 08:01:55,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:55,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:55,803 - INFO - API请求耗时: 646ms
2025-05-19 08:01:55,803 - INFO - Response - Page 12
2025-05-19 08:01:55,804 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:01:56,305 - INFO - Request Parameters - Page 13:
2025-05-19 08:01:56,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:56,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:56,978 - INFO - API请求耗时: 672ms
2025-05-19 08:01:56,979 - INFO - Response - Page 13
2025-05-19 08:01:56,979 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:01:57,481 - INFO - Request Parameters - Page 14:
2025-05-19 08:01:57,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:57,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:58,139 - INFO - API请求耗时: 657ms
2025-05-19 08:01:58,140 - INFO - Response - Page 14
2025-05-19 08:01:58,141 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:01:58,642 - INFO - Request Parameters - Page 15:
2025-05-19 08:01:58,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:58,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:01:59,340 - INFO - API请求耗时: 697ms
2025-05-19 08:01:59,341 - INFO - Response - Page 15
2025-05-19 08:01:59,341 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:01:59,842 - INFO - Request Parameters - Page 16:
2025-05-19 08:01:59,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:01:59,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:00,547 - INFO - API请求耗时: 704ms
2025-05-19 08:02:00,547 - INFO - Response - Page 16
2025-05-19 08:02:00,548 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:02:01,049 - INFO - Request Parameters - Page 17:
2025-05-19 08:02:01,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:01,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:01,661 - INFO - API请求耗时: 611ms
2025-05-19 08:02:01,661 - INFO - Response - Page 17
2025-05-19 08:02:01,662 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:02:02,163 - INFO - Request Parameters - Page 18:
2025-05-19 08:02:02,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:02,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:02,865 - INFO - API请求耗时: 700ms
2025-05-19 08:02:02,865 - INFO - Response - Page 18
2025-05-19 08:02:02,866 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:02:03,366 - INFO - Request Parameters - Page 19:
2025-05-19 08:02:03,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:03,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:04,078 - INFO - API请求耗时: 711ms
2025-05-19 08:02:04,079 - INFO - Response - Page 19
2025-05-19 08:02:04,079 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:02:04,579 - INFO - Request Parameters - Page 20:
2025-05-19 08:02:04,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:04,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743523200181, 1744041600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:05,124 - INFO - API请求耗时: 544ms
2025-05-19 08:02:05,125 - INFO - Response - Page 20
2025-05-19 08:02:05,125 - INFO - 第 20 页获取到 32 条记录
2025-05-19 08:02:05,126 - INFO - 查询完成，共获取到 1932 条记录
2025-05-19 08:02:05,126 - INFO - 分段 3 查询成功，获取到 1932 条记录
2025-05-19 08:02:06,127 - INFO - 查询分段 4: 2025-04-09 至 2025-04-15
2025-05-19 08:02:06,127 - INFO - 查询日期范围: 2025-04-09 至 2025-04-15，使用分页查询，每页 100 条记录
2025-05-19 08:02:06,128 - INFO - Request Parameters - Page 1:
2025-05-19 08:02:06,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:06,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:06,881 - INFO - API请求耗时: 752ms
2025-05-19 08:02:06,881 - INFO - Response - Page 1
2025-05-19 08:02:06,882 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:02:07,383 - INFO - Request Parameters - Page 2:
2025-05-19 08:02:07,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:07,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:08,046 - INFO - API请求耗时: 662ms
2025-05-19 08:02:08,046 - INFO - Response - Page 2
2025-05-19 08:02:08,047 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:02:08,548 - INFO - Request Parameters - Page 3:
2025-05-19 08:02:08,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:08,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:09,345 - INFO - API请求耗时: 796ms
2025-05-19 08:02:09,345 - INFO - Response - Page 3
2025-05-19 08:02:09,346 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:02:09,846 - INFO - Request Parameters - Page 4:
2025-05-19 08:02:09,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:09,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:10,646 - INFO - API请求耗时: 799ms
2025-05-19 08:02:10,647 - INFO - Response - Page 4
2025-05-19 08:02:10,647 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:02:11,147 - INFO - Request Parameters - Page 5:
2025-05-19 08:02:11,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:11,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:11,850 - INFO - API请求耗时: 701ms
2025-05-19 08:02:11,850 - INFO - Response - Page 5
2025-05-19 08:02:11,851 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:02:12,352 - INFO - Request Parameters - Page 6:
2025-05-19 08:02:12,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:12,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:13,132 - INFO - API请求耗时: 779ms
2025-05-19 08:02:13,132 - INFO - Response - Page 6
2025-05-19 08:02:13,133 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:02:13,634 - INFO - Request Parameters - Page 7:
2025-05-19 08:02:13,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:13,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:14,263 - INFO - API请求耗时: 628ms
2025-05-19 08:02:14,263 - INFO - Response - Page 7
2025-05-19 08:02:14,264 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:02:14,765 - INFO - Request Parameters - Page 8:
2025-05-19 08:02:14,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:14,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:15,488 - INFO - API请求耗时: 723ms
2025-05-19 08:02:15,488 - INFO - Response - Page 8
2025-05-19 08:02:15,489 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:02:15,989 - INFO - Request Parameters - Page 9:
2025-05-19 08:02:15,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:15,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:16,628 - INFO - API请求耗时: 638ms
2025-05-19 08:02:16,628 - INFO - Response - Page 9
2025-05-19 08:02:16,629 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:02:17,129 - INFO - Request Parameters - Page 10:
2025-05-19 08:02:17,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:17,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:17,842 - INFO - API请求耗时: 712ms
2025-05-19 08:02:17,842 - INFO - Response - Page 10
2025-05-19 08:02:17,843 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:02:18,344 - INFO - Request Parameters - Page 11:
2025-05-19 08:02:18,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:18,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:18,986 - INFO - API请求耗时: 641ms
2025-05-19 08:02:18,986 - INFO - Response - Page 11
2025-05-19 08:02:18,987 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:02:19,487 - INFO - Request Parameters - Page 12:
2025-05-19 08:02:19,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:19,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:20,153 - INFO - API请求耗时: 665ms
2025-05-19 08:02:20,154 - INFO - Response - Page 12
2025-05-19 08:02:20,154 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:02:20,654 - INFO - Request Parameters - Page 13:
2025-05-19 08:02:20,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:20,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:21,350 - INFO - API请求耗时: 694ms
2025-05-19 08:02:21,351 - INFO - Response - Page 13
2025-05-19 08:02:21,351 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:02:21,851 - INFO - Request Parameters - Page 14:
2025-05-19 08:02:21,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:21,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:22,515 - INFO - API请求耗时: 663ms
2025-05-19 08:02:22,515 - INFO - Response - Page 14
2025-05-19 08:02:22,516 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:02:23,017 - INFO - Request Parameters - Page 15:
2025-05-19 08:02:23,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:23,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:23,737 - INFO - API请求耗时: 720ms
2025-05-19 08:02:23,738 - INFO - Response - Page 15
2025-05-19 08:02:23,739 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:02:24,240 - INFO - Request Parameters - Page 16:
2025-05-19 08:02:24,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:24,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:24,886 - INFO - API请求耗时: 645ms
2025-05-19 08:02:24,886 - INFO - Response - Page 16
2025-05-19 08:02:24,887 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:02:25,388 - INFO - Request Parameters - Page 17:
2025-05-19 08:02:25,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:25,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:26,069 - INFO - API请求耗时: 681ms
2025-05-19 08:02:26,070 - INFO - Response - Page 17
2025-05-19 08:02:26,070 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:02:26,570 - INFO - Request Parameters - Page 18:
2025-05-19 08:02:26,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:26,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:27,247 - INFO - API请求耗时: 676ms
2025-05-19 08:02:27,248 - INFO - Response - Page 18
2025-05-19 08:02:27,248 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:02:27,749 - INFO - Request Parameters - Page 19:
2025-05-19 08:02:27,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:27,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:28,390 - INFO - API请求耗时: 640ms
2025-05-19 08:02:28,390 - INFO - Response - Page 19
2025-05-19 08:02:28,391 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:02:28,892 - INFO - Request Parameters - Page 20:
2025-05-19 08:02:28,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:28,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000181, 1744646400181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:29,380 - INFO - API请求耗时: 487ms
2025-05-19 08:02:29,381 - INFO - Response - Page 20
2025-05-19 08:02:29,381 - INFO - 第 20 页获取到 38 条记录
2025-05-19 08:02:29,382 - INFO - 查询完成，共获取到 1938 条记录
2025-05-19 08:02:29,382 - INFO - 分段 4 查询成功，获取到 1938 条记录
2025-05-19 08:02:30,383 - INFO - 查询分段 5: 2025-04-16 至 2025-04-22
2025-05-19 08:02:30,383 - INFO - 查询日期范围: 2025-04-16 至 2025-04-22，使用分页查询，每页 100 条记录
2025-05-19 08:02:30,384 - INFO - Request Parameters - Page 1:
2025-05-19 08:02:30,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:30,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:31,078 - INFO - API请求耗时: 694ms
2025-05-19 08:02:31,078 - INFO - Response - Page 1
2025-05-19 08:02:31,079 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:02:31,580 - INFO - Request Parameters - Page 2:
2025-05-19 08:02:31,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:31,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:32,229 - INFO - API请求耗时: 647ms
2025-05-19 08:02:32,230 - INFO - Response - Page 2
2025-05-19 08:02:32,230 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:02:32,732 - INFO - Request Parameters - Page 3:
2025-05-19 08:02:32,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:32,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:33,353 - INFO - API请求耗时: 620ms
2025-05-19 08:02:33,353 - INFO - Response - Page 3
2025-05-19 08:02:33,354 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:02:33,855 - INFO - Request Parameters - Page 4:
2025-05-19 08:02:33,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:33,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:34,575 - INFO - API请求耗时: 720ms
2025-05-19 08:02:34,575 - INFO - Response - Page 4
2025-05-19 08:02:34,576 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:02:35,077 - INFO - Request Parameters - Page 5:
2025-05-19 08:02:35,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:35,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:35,801 - INFO - API请求耗时: 723ms
2025-05-19 08:02:35,802 - INFO - Response - Page 5
2025-05-19 08:02:35,803 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:02:36,303 - INFO - Request Parameters - Page 6:
2025-05-19 08:02:36,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:36,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:36,914 - INFO - API请求耗时: 610ms
2025-05-19 08:02:36,914 - INFO - Response - Page 6
2025-05-19 08:02:36,915 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:02:37,416 - INFO - Request Parameters - Page 7:
2025-05-19 08:02:37,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:37,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:38,080 - INFO - API请求耗时: 663ms
2025-05-19 08:02:38,080 - INFO - Response - Page 7
2025-05-19 08:02:38,081 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:02:38,582 - INFO - Request Parameters - Page 8:
2025-05-19 08:02:38,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:38,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:39,210 - INFO - API请求耗时: 627ms
2025-05-19 08:02:39,211 - INFO - Response - Page 8
2025-05-19 08:02:39,211 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:02:39,713 - INFO - Request Parameters - Page 9:
2025-05-19 08:02:39,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:39,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:40,429 - INFO - API请求耗时: 715ms
2025-05-19 08:02:40,430 - INFO - Response - Page 9
2025-05-19 08:02:40,430 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:02:40,931 - INFO - Request Parameters - Page 10:
2025-05-19 08:02:40,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:40,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:41,655 - INFO - API请求耗时: 724ms
2025-05-19 08:02:41,656 - INFO - Response - Page 10
2025-05-19 08:02:41,656 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:02:42,156 - INFO - Request Parameters - Page 11:
2025-05-19 08:02:42,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:42,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:42,888 - INFO - API请求耗时: 730ms
2025-05-19 08:02:42,889 - INFO - Response - Page 11
2025-05-19 08:02:42,889 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:02:43,390 - INFO - Request Parameters - Page 12:
2025-05-19 08:02:43,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:43,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:43,992 - INFO - API请求耗时: 602ms
2025-05-19 08:02:43,993 - INFO - Response - Page 12
2025-05-19 08:02:43,993 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:02:44,494 - INFO - Request Parameters - Page 13:
2025-05-19 08:02:44,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:44,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:45,147 - INFO - API请求耗时: 652ms
2025-05-19 08:02:45,147 - INFO - Response - Page 13
2025-05-19 08:02:45,148 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:02:45,649 - INFO - Request Parameters - Page 14:
2025-05-19 08:02:45,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:45,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:46,330 - INFO - API请求耗时: 681ms
2025-05-19 08:02:46,330 - INFO - Response - Page 14
2025-05-19 08:02:46,331 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:02:46,832 - INFO - Request Parameters - Page 15:
2025-05-19 08:02:46,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:46,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:47,427 - INFO - API请求耗时: 594ms
2025-05-19 08:02:47,427 - INFO - Response - Page 15
2025-05-19 08:02:47,428 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:02:47,929 - INFO - Request Parameters - Page 16:
2025-05-19 08:02:47,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:47,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:48,819 - INFO - API请求耗时: 890ms
2025-05-19 08:02:48,819 - INFO - Response - Page 16
2025-05-19 08:02:48,820 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:02:49,321 - INFO - Request Parameters - Page 17:
2025-05-19 08:02:49,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:49,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:49,986 - INFO - API请求耗时: 664ms
2025-05-19 08:02:49,986 - INFO - Response - Page 17
2025-05-19 08:02:49,987 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:02:50,488 - INFO - Request Parameters - Page 18:
2025-05-19 08:02:50,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:50,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:51,197 - INFO - API请求耗时: 708ms
2025-05-19 08:02:51,197 - INFO - Response - Page 18
2025-05-19 08:02:51,198 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:02:51,699 - INFO - Request Parameters - Page 19:
2025-05-19 08:02:51,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:51,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:52,370 - INFO - API请求耗时: 670ms
2025-05-19 08:02:52,370 - INFO - Response - Page 19
2025-05-19 08:02:52,371 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:02:52,872 - INFO - Request Parameters - Page 20:
2025-05-19 08:02:52,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:52,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800181, 1745251200181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:53,391 - INFO - API请求耗时: 518ms
2025-05-19 08:02:53,392 - INFO - Response - Page 20
2025-05-19 08:02:53,392 - INFO - 第 20 页获取到 32 条记录
2025-05-19 08:02:53,393 - INFO - 查询完成，共获取到 1932 条记录
2025-05-19 08:02:53,393 - INFO - 分段 5 查询成功，获取到 1932 条记录
2025-05-19 08:02:54,394 - INFO - 查询分段 6: 2025-04-23 至 2025-04-29
2025-05-19 08:02:54,394 - INFO - 查询日期范围: 2025-04-23 至 2025-04-29，使用分页查询，每页 100 条记录
2025-05-19 08:02:54,394 - INFO - Request Parameters - Page 1:
2025-05-19 08:02:54,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:54,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:55,072 - INFO - API请求耗时: 678ms
2025-05-19 08:02:55,073 - INFO - Response - Page 1
2025-05-19 08:02:55,074 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:02:55,574 - INFO - Request Parameters - Page 2:
2025-05-19 08:02:55,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:55,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:56,284 - INFO - API请求耗时: 709ms
2025-05-19 08:02:56,285 - INFO - Response - Page 2
2025-05-19 08:02:56,285 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:02:56,787 - INFO - Request Parameters - Page 3:
2025-05-19 08:02:56,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:56,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:57,380 - INFO - API请求耗时: 592ms
2025-05-19 08:02:57,380 - INFO - Response - Page 3
2025-05-19 08:02:57,381 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:02:57,882 - INFO - Request Parameters - Page 4:
2025-05-19 08:02:57,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:57,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:58,629 - INFO - API请求耗时: 746ms
2025-05-19 08:02:58,629 - INFO - Response - Page 4
2025-05-19 08:02:58,630 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:02:59,131 - INFO - Request Parameters - Page 5:
2025-05-19 08:02:59,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:02:59,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:02:59,737 - INFO - API请求耗时: 605ms
2025-05-19 08:02:59,737 - INFO - Response - Page 5
2025-05-19 08:02:59,738 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:03:00,239 - INFO - Request Parameters - Page 6:
2025-05-19 08:03:00,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:00,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:00,848 - INFO - API请求耗时: 607ms
2025-05-19 08:03:00,849 - INFO - Response - Page 6
2025-05-19 08:03:00,849 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:03:01,349 - INFO - Request Parameters - Page 7:
2025-05-19 08:03:01,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:01,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:01,993 - INFO - API请求耗时: 644ms
2025-05-19 08:03:01,993 - INFO - Response - Page 7
2025-05-19 08:03:01,994 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:03:02,494 - INFO - Request Parameters - Page 8:
2025-05-19 08:03:02,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:02,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:03,139 - INFO - API请求耗时: 644ms
2025-05-19 08:03:03,140 - INFO - Response - Page 8
2025-05-19 08:03:03,140 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:03:03,641 - INFO - Request Parameters - Page 9:
2025-05-19 08:03:03,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:03,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:04,330 - INFO - API请求耗时: 688ms
2025-05-19 08:03:04,330 - INFO - Response - Page 9
2025-05-19 08:03:04,331 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:03:04,832 - INFO - Request Parameters - Page 10:
2025-05-19 08:03:04,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:04,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:05,463 - INFO - API请求耗时: 630ms
2025-05-19 08:03:05,464 - INFO - Response - Page 10
2025-05-19 08:03:05,464 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:03:05,966 - INFO - Request Parameters - Page 11:
2025-05-19 08:03:05,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:05,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:06,665 - INFO - API请求耗时: 698ms
2025-05-19 08:03:06,665 - INFO - Response - Page 11
2025-05-19 08:03:06,666 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:03:07,166 - INFO - Request Parameters - Page 12:
2025-05-19 08:03:07,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:07,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:07,844 - INFO - API请求耗时: 677ms
2025-05-19 08:03:07,845 - INFO - Response - Page 12
2025-05-19 08:03:07,845 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:03:08,347 - INFO - Request Parameters - Page 13:
2025-05-19 08:03:08,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:08,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:08,969 - INFO - API请求耗时: 621ms
2025-05-19 08:03:08,969 - INFO - Response - Page 13
2025-05-19 08:03:08,970 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:03:09,471 - INFO - Request Parameters - Page 14:
2025-05-19 08:03:09,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:09,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:10,227 - INFO - API请求耗时: 756ms
2025-05-19 08:03:10,227 - INFO - Response - Page 14
2025-05-19 08:03:10,228 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:03:10,729 - INFO - Request Parameters - Page 15:
2025-05-19 08:03:10,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:10,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:11,347 - INFO - API请求耗时: 616ms
2025-05-19 08:03:11,347 - INFO - Response - Page 15
2025-05-19 08:03:11,348 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:03:11,848 - INFO - Request Parameters - Page 16:
2025-05-19 08:03:11,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:11,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:12,638 - INFO - API请求耗时: 788ms
2025-05-19 08:03:12,638 - INFO - Response - Page 16
2025-05-19 08:03:12,639 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:03:13,140 - INFO - Request Parameters - Page 17:
2025-05-19 08:03:13,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:13,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:14,089 - INFO - API请求耗时: 948ms
2025-05-19 08:03:14,090 - INFO - Response - Page 17
2025-05-19 08:03:14,091 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:03:14,592 - INFO - Request Parameters - Page 18:
2025-05-19 08:03:14,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:14,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:15,244 - INFO - API请求耗时: 650ms
2025-05-19 08:03:15,244 - INFO - Response - Page 18
2025-05-19 08:03:15,245 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:03:15,746 - INFO - Request Parameters - Page 19:
2025-05-19 08:03:15,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:15,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:16,395 - INFO - API请求耗时: 648ms
2025-05-19 08:03:16,396 - INFO - Response - Page 19
2025-05-19 08:03:16,396 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:03:16,898 - INFO - Request Parameters - Page 20:
2025-05-19 08:03:16,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:16,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600181, 1745856000181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:17,297 - INFO - API请求耗时: 397ms
2025-05-19 08:03:17,297 - INFO - Response - Page 20
2025-05-19 08:03:17,298 - INFO - 第 20 页获取到 14 条记录
2025-05-19 08:03:17,298 - INFO - 查询完成，共获取到 1914 条记录
2025-05-19 08:03:17,298 - INFO - 分段 6 查询成功，获取到 1914 条记录
2025-05-19 08:03:18,299 - INFO - 查询分段 7: 2025-04-30 至 2025-05-06
2025-05-19 08:03:18,299 - INFO - 查询日期范围: 2025-04-30 至 2025-05-06，使用分页查询，每页 100 条记录
2025-05-19 08:03:18,300 - INFO - Request Parameters - Page 1:
2025-05-19 08:03:18,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:18,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:18,927 - INFO - API请求耗时: 627ms
2025-05-19 08:03:18,928 - INFO - Response - Page 1
2025-05-19 08:03:18,928 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:03:19,428 - INFO - Request Parameters - Page 2:
2025-05-19 08:03:19,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:19,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:20,123 - INFO - API请求耗时: 694ms
2025-05-19 08:03:20,123 - INFO - Response - Page 2
2025-05-19 08:03:20,124 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:03:20,624 - INFO - Request Parameters - Page 3:
2025-05-19 08:03:20,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:20,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:21,292 - INFO - API请求耗时: 666ms
2025-05-19 08:03:21,292 - INFO - Response - Page 3
2025-05-19 08:03:21,293 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:03:21,794 - INFO - Request Parameters - Page 4:
2025-05-19 08:03:21,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:21,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:22,438 - INFO - API请求耗时: 644ms
2025-05-19 08:03:22,438 - INFO - Response - Page 4
2025-05-19 08:03:22,439 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:03:22,939 - INFO - Request Parameters - Page 5:
2025-05-19 08:03:22,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:22,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:23,596 - INFO - API请求耗时: 656ms
2025-05-19 08:03:23,596 - INFO - Response - Page 5
2025-05-19 08:03:23,597 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:03:24,099 - INFO - Request Parameters - Page 6:
2025-05-19 08:03:24,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:24,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:24,795 - INFO - API请求耗时: 695ms
2025-05-19 08:03:24,795 - INFO - Response - Page 6
2025-05-19 08:03:24,796 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:03:25,297 - INFO - Request Parameters - Page 7:
2025-05-19 08:03:25,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:25,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:25,896 - INFO - API请求耗时: 599ms
2025-05-19 08:03:25,896 - INFO - Response - Page 7
2025-05-19 08:03:25,897 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:03:26,398 - INFO - Request Parameters - Page 8:
2025-05-19 08:03:26,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:26,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:27,235 - INFO - API请求耗时: 836ms
2025-05-19 08:03:27,235 - INFO - Response - Page 8
2025-05-19 08:03:27,236 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:03:27,737 - INFO - Request Parameters - Page 9:
2025-05-19 08:03:27,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:27,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:28,370 - INFO - API请求耗时: 632ms
2025-05-19 08:03:28,370 - INFO - Response - Page 9
2025-05-19 08:03:28,371 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:03:28,872 - INFO - Request Parameters - Page 10:
2025-05-19 08:03:28,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:28,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:29,692 - INFO - API请求耗时: 819ms
2025-05-19 08:03:29,692 - INFO - Response - Page 10
2025-05-19 08:03:29,693 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:03:30,194 - INFO - Request Parameters - Page 11:
2025-05-19 08:03:30,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:30,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:30,847 - INFO - API请求耗时: 652ms
2025-05-19 08:03:30,848 - INFO - Response - Page 11
2025-05-19 08:03:30,848 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:03:31,349 - INFO - Request Parameters - Page 12:
2025-05-19 08:03:31,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:31,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:31,990 - INFO - API请求耗时: 640ms
2025-05-19 08:03:31,991 - INFO - Response - Page 12
2025-05-19 08:03:31,991 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:03:32,492 - INFO - Request Parameters - Page 13:
2025-05-19 08:03:32,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:32,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:33,130 - INFO - API请求耗时: 636ms
2025-05-19 08:03:33,130 - INFO - Response - Page 13
2025-05-19 08:03:33,131 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:03:33,632 - INFO - Request Parameters - Page 14:
2025-05-19 08:03:33,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:33,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:34,328 - INFO - API请求耗时: 695ms
2025-05-19 08:03:34,329 - INFO - Response - Page 14
2025-05-19 08:03:34,329 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:03:34,830 - INFO - Request Parameters - Page 15:
2025-05-19 08:03:34,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:34,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:35,483 - INFO - API请求耗时: 652ms
2025-05-19 08:03:35,484 - INFO - Response - Page 15
2025-05-19 08:03:35,485 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:03:35,986 - INFO - Request Parameters - Page 16:
2025-05-19 08:03:35,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:35,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:36,637 - INFO - API请求耗时: 650ms
2025-05-19 08:03:36,637 - INFO - Response - Page 16
2025-05-19 08:03:36,638 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:03:37,139 - INFO - Request Parameters - Page 17:
2025-05-19 08:03:37,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:37,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:37,800 - INFO - API请求耗时: 660ms
2025-05-19 08:03:37,800 - INFO - Response - Page 17
2025-05-19 08:03:37,801 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:03:38,301 - INFO - Request Parameters - Page 18:
2025-05-19 08:03:38,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:38,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:38,903 - INFO - API请求耗时: 601ms
2025-05-19 08:03:38,904 - INFO - Response - Page 18
2025-05-19 08:03:38,905 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:03:39,405 - INFO - Request Parameters - Page 19:
2025-05-19 08:03:39,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:39,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:40,095 - INFO - API请求耗时: 689ms
2025-05-19 08:03:40,095 - INFO - Response - Page 19
2025-05-19 08:03:40,096 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:03:40,597 - INFO - Request Parameters - Page 20:
2025-05-19 08:03:40,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:40,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:41,261 - INFO - API请求耗时: 663ms
2025-05-19 08:03:41,261 - INFO - Response - Page 20
2025-05-19 08:03:41,262 - INFO - 第 20 页获取到 100 条记录
2025-05-19 08:03:41,763 - INFO - Request Parameters - Page 21:
2025-05-19 08:03:41,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:41,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:42,390 - INFO - API请求耗时: 626ms
2025-05-19 08:03:42,391 - INFO - Response - Page 21
2025-05-19 08:03:42,391 - INFO - 第 21 页获取到 100 条记录
2025-05-19 08:03:42,892 - INFO - Request Parameters - Page 22:
2025-05-19 08:03:42,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:42,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:43,688 - INFO - API请求耗时: 795ms
2025-05-19 08:03:43,689 - INFO - Response - Page 22
2025-05-19 08:03:43,689 - INFO - 第 22 页获取到 100 条记录
2025-05-19 08:03:44,190 - INFO - Request Parameters - Page 23:
2025-05-19 08:03:44,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:44,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:44,876 - INFO - API请求耗时: 685ms
2025-05-19 08:03:44,876 - INFO - Response - Page 23
2025-05-19 08:03:44,877 - INFO - 第 23 页获取到 100 条记录
2025-05-19 08:03:45,378 - INFO - Request Parameters - Page 24:
2025-05-19 08:03:45,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:45,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:46,138 - INFO - API请求耗时: 759ms
2025-05-19 08:03:46,138 - INFO - Response - Page 24
2025-05-19 08:03:46,138 - INFO - 第 24 页获取到 100 条记录
2025-05-19 08:03:46,639 - INFO - Request Parameters - Page 25:
2025-05-19 08:03:46,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:46,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400181, 1746460800181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:47,239 - INFO - API请求耗时: 599ms
2025-05-19 08:03:47,239 - INFO - Response - Page 25
2025-05-19 08:03:47,240 - INFO - 第 25 页获取到 82 条记录
2025-05-19 08:03:47,240 - INFO - 查询完成，共获取到 2482 条记录
2025-05-19 08:03:47,240 - INFO - 分段 7 查询成功，获取到 2482 条记录
2025-05-19 08:03:48,241 - INFO - 查询分段 8: 2025-05-07 至 2025-05-13
2025-05-19 08:03:48,241 - INFO - 查询日期范围: 2025-05-07 至 2025-05-13，使用分页查询，每页 100 条记录
2025-05-19 08:03:48,242 - INFO - Request Parameters - Page 1:
2025-05-19 08:03:48,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:48,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:48,858 - INFO - API请求耗时: 616ms
2025-05-19 08:03:48,858 - INFO - Response - Page 1
2025-05-19 08:03:48,859 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:03:49,360 - INFO - Request Parameters - Page 2:
2025-05-19 08:03:49,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:49,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:50,173 - INFO - API请求耗时: 812ms
2025-05-19 08:03:50,174 - INFO - Response - Page 2
2025-05-19 08:03:50,174 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:03:50,674 - INFO - Request Parameters - Page 3:
2025-05-19 08:03:50,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:50,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:51,307 - INFO - API请求耗时: 632ms
2025-05-19 08:03:51,307 - INFO - Response - Page 3
2025-05-19 08:03:51,308 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:03:51,809 - INFO - Request Parameters - Page 4:
2025-05-19 08:03:51,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:51,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:52,416 - INFO - API请求耗时: 605ms
2025-05-19 08:03:52,416 - INFO - Response - Page 4
2025-05-19 08:03:52,417 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:03:52,918 - INFO - Request Parameters - Page 5:
2025-05-19 08:03:52,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:52,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:53,571 - INFO - API请求耗时: 652ms
2025-05-19 08:03:53,571 - INFO - Response - Page 5
2025-05-19 08:03:53,572 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:03:54,072 - INFO - Request Parameters - Page 6:
2025-05-19 08:03:54,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:54,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:54,790 - INFO - API请求耗时: 717ms
2025-05-19 08:03:54,790 - INFO - Response - Page 6
2025-05-19 08:03:54,791 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:03:55,292 - INFO - Request Parameters - Page 7:
2025-05-19 08:03:55,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:55,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:55,906 - INFO - API请求耗时: 612ms
2025-05-19 08:03:55,906 - INFO - Response - Page 7
2025-05-19 08:03:55,907 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:03:56,407 - INFO - Request Parameters - Page 8:
2025-05-19 08:03:56,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:56,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:57,199 - INFO - API请求耗时: 791ms
2025-05-19 08:03:57,199 - INFO - Response - Page 8
2025-05-19 08:03:57,200 - INFO - 第 8 页获取到 100 条记录
2025-05-19 08:03:57,700 - INFO - Request Parameters - Page 9:
2025-05-19 08:03:57,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:57,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:58,365 - INFO - API请求耗时: 664ms
2025-05-19 08:03:58,366 - INFO - Response - Page 9
2025-05-19 08:03:58,366 - INFO - 第 9 页获取到 100 条记录
2025-05-19 08:03:58,867 - INFO - Request Parameters - Page 10:
2025-05-19 08:03:58,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:03:58,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:03:59,592 - INFO - API请求耗时: 725ms
2025-05-19 08:03:59,593 - INFO - Response - Page 10
2025-05-19 08:03:59,593 - INFO - 第 10 页获取到 100 条记录
2025-05-19 08:04:00,094 - INFO - Request Parameters - Page 11:
2025-05-19 08:04:00,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:00,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:00,745 - INFO - API请求耗时: 649ms
2025-05-19 08:04:00,746 - INFO - Response - Page 11
2025-05-19 08:04:00,747 - INFO - 第 11 页获取到 100 条记录
2025-05-19 08:04:01,248 - INFO - Request Parameters - Page 12:
2025-05-19 08:04:01,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:01,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:01,915 - INFO - API请求耗时: 666ms
2025-05-19 08:04:01,915 - INFO - Response - Page 12
2025-05-19 08:04:01,916 - INFO - 第 12 页获取到 100 条记录
2025-05-19 08:04:02,417 - INFO - Request Parameters - Page 13:
2025-05-19 08:04:02,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:02,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:03,066 - INFO - API请求耗时: 649ms
2025-05-19 08:04:03,066 - INFO - Response - Page 13
2025-05-19 08:04:03,067 - INFO - 第 13 页获取到 100 条记录
2025-05-19 08:04:03,568 - INFO - Request Parameters - Page 14:
2025-05-19 08:04:03,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:03,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:04,219 - INFO - API请求耗时: 650ms
2025-05-19 08:04:04,219 - INFO - Response - Page 14
2025-05-19 08:04:04,220 - INFO - 第 14 页获取到 100 条记录
2025-05-19 08:04:04,721 - INFO - Request Parameters - Page 15:
2025-05-19 08:04:04,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:04,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:05,340 - INFO - API请求耗时: 618ms
2025-05-19 08:04:05,340 - INFO - Response - Page 15
2025-05-19 08:04:05,341 - INFO - 第 15 页获取到 100 条记录
2025-05-19 08:04:05,842 - INFO - Request Parameters - Page 16:
2025-05-19 08:04:05,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:05,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:06,607 - INFO - API请求耗时: 764ms
2025-05-19 08:04:06,608 - INFO - Response - Page 16
2025-05-19 08:04:06,608 - INFO - 第 16 页获取到 100 条记录
2025-05-19 08:04:07,108 - INFO - Request Parameters - Page 17:
2025-05-19 08:04:07,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:07,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:07,751 - INFO - API请求耗时: 643ms
2025-05-19 08:04:07,752 - INFO - Response - Page 17
2025-05-19 08:04:07,753 - INFO - 第 17 页获取到 100 条记录
2025-05-19 08:04:08,254 - INFO - Request Parameters - Page 18:
2025-05-19 08:04:08,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:08,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:08,929 - INFO - API请求耗时: 674ms
2025-05-19 08:04:08,929 - INFO - Response - Page 18
2025-05-19 08:04:08,930 - INFO - 第 18 页获取到 100 条记录
2025-05-19 08:04:09,430 - INFO - Request Parameters - Page 19:
2025-05-19 08:04:09,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:09,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:10,098 - INFO - API请求耗时: 667ms
2025-05-19 08:04:10,099 - INFO - Response - Page 19
2025-05-19 08:04:10,099 - INFO - 第 19 页获取到 100 条记录
2025-05-19 08:04:10,599 - INFO - Request Parameters - Page 20:
2025-05-19 08:04:10,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:10,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200181, 1747065600181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:11,113 - INFO - API请求耗时: 513ms
2025-05-19 08:04:11,114 - INFO - Response - Page 20
2025-05-19 08:04:11,114 - INFO - 第 20 页获取到 16 条记录
2025-05-19 08:04:11,115 - INFO - 查询完成，共获取到 1916 条记录
2025-05-19 08:04:11,115 - INFO - 分段 8 查询成功，获取到 1916 条记录
2025-05-19 08:04:12,116 - INFO - 查询分段 9: 2025-05-14 至 2025-05-18
2025-05-19 08:04:12,116 - INFO - 查询日期范围: 2025-05-14 至 2025-05-18，使用分页查询，每页 100 条记录
2025-05-19 08:04:12,117 - INFO - Request Parameters - Page 1:
2025-05-19 08:04:12,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:12,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000181, 1747583999181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:12,749 - INFO - API请求耗时: 632ms
2025-05-19 08:04:12,749 - INFO - Response - Page 1
2025-05-19 08:04:12,750 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:04:13,251 - INFO - Request Parameters - Page 2:
2025-05-19 08:04:13,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:13,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000181, 1747583999181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:13,927 - INFO - API请求耗时: 675ms
2025-05-19 08:04:13,927 - INFO - Response - Page 2
2025-05-19 08:04:13,928 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:04:14,429 - INFO - Request Parameters - Page 3:
2025-05-19 08:04:14,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:14,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000181, 1747583999181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:15,337 - INFO - API请求耗时: 907ms
2025-05-19 08:04:15,337 - INFO - Response - Page 3
2025-05-19 08:04:15,338 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:04:15,839 - INFO - Request Parameters - Page 4:
2025-05-19 08:04:15,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:15,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000181, 1747583999181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:16,582 - INFO - API请求耗时: 742ms
2025-05-19 08:04:16,583 - INFO - Response - Page 4
2025-05-19 08:04:16,583 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:04:17,083 - INFO - Request Parameters - Page 5:
2025-05-19 08:04:17,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:17,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000181, 1747583999181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:17,752 - INFO - API请求耗时: 668ms
2025-05-19 08:04:17,753 - INFO - Response - Page 5
2025-05-19 08:04:17,753 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:04:18,255 - INFO - Request Parameters - Page 6:
2025-05-19 08:04:18,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:04:18,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000181, 1747583999181], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:04:18,920 - INFO - API请求耗时: 664ms
2025-05-19 08:04:18,920 - INFO - Response - Page 6
2025-05-19 08:04:18,921 - INFO - 第 6 页获取到 85 条记录
2025-05-19 08:04:18,921 - INFO - 查询完成，共获取到 585 条记录
2025-05-19 08:04:18,921 - INFO - 分段 9 查询成功，获取到 585 条记录
2025-05-19 08:04:19,923 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 16479 条记录，失败 0 次
2025-05-19 08:04:19,923 - INFO - 成功获取宜搭日销售表单数据，共 16479 条记录
2025-05-19 08:04:19,924 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-19 08:04:19,924 - INFO - 开始对比和同步日销售数据...
2025-05-19 08:04:20,430 - INFO - 成功创建宜搭日销售数据索引，共 10936 条记录
2025-05-19 08:04:20,430 - INFO - 开始处理数衍数据，共 13121 条记录
2025-05-19 08:04:21,066 - INFO - 更新表单数据成功: FINST-SWC66P91F9FVTV4N910BM7P27IWT2IDG79PAM04
2025-05-19 08:04:21,066 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4879.23, 'new_value': 4849.33}, {'field': 'amount', 'old_value': 4879.23, 'new_value': 4849.33}, {'field': 'instoreAmount', 'old_value': 4542.54, 'new_value': 4512.64}]
2025-05-19 08:04:21,511 - INFO - 更新表单数据成功: FINST-SWC66P91F9FVTV4N910BM7P27IWT2IDG79PAM24
2025-05-19 08:04:21,511 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250427, 变更字段: [{'field': 'recommendAmount', 'old_value': 6807.81, 'new_value': 5607.33}, {'field': 'amount', 'old_value': 6807.81, 'new_value': 5607.33}, {'field': 'instoreAmount', 'old_value': 6560.96, 'new_value': 5360.48}]
2025-05-19 08:04:21,960 - INFO - 更新表单数据成功: FINST-1MD668B12VFVXKDR9KMIMCRMJBIS3M0J79PAMZ8
2025-05-19 08:04:21,960 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2465.12, 'new_value': 2303.4}, {'field': 'amount', 'old_value': 2465.12, 'new_value': 2303.3999999999996}, {'field': 'count', 'old_value': 163, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 1293.62, 'new_value': 1131.9}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 75}]
2025-05-19 08:04:22,400 - INFO - 更新表单数据成功: FINST-1MD668B12VFVXKDR9KMIMCRMJBIS3M0J79PAM09
2025-05-19 08:04:22,401 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250428, 变更字段: [{'field': 'recommendAmount', 'old_value': 2249.21, 'new_value': 2182.12}, {'field': 'amount', 'old_value': 2249.21, 'new_value': 2182.12}, {'field': 'count', 'old_value': 153, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 1232.19, 'new_value': 1165.1}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 71}]
2025-05-19 08:04:22,846 - INFO - 更新表单数据成功: FINST-1MD668B12VFVXKDR9KMIMCRMJBIS3M0J79PAM19
2025-05-19 08:04:22,846 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250427, 变更字段: [{'field': 'recommendAmount', 'old_value': 2188.66, 'new_value': 2032.53}, {'field': 'amount', 'old_value': 2188.6600000000003, 'new_value': 2032.5300000000002}, {'field': 'count', 'old_value': 173, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 817.33, 'new_value': 661.2}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 80}]
2025-05-19 08:04:23,389 - INFO - 更新表单数据成功: FINST-U8966871YFFVF5T6FYDRZ5WEZAAL2KZH69PAMIF
2025-05-19 08:04:23,390 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250425, 变更字段: [{'field': 'recommendAmount', 'old_value': 2587.27, 'new_value': 2510.41}, {'field': 'amount', 'old_value': 2587.27, 'new_value': 2510.41}, {'field': 'count', 'old_value': 190, 'new_value': 187}, {'field': 'instoreAmount', 'old_value': 1442.96, 'new_value': 1366.1}, {'field': 'instoreCount', 'old_value': 108, 'new_value': 105}]
2025-05-19 08:04:23,894 - INFO - 更新表单数据成功: FINST-U8966871YFFVF5T6FYDRZ5WEZAAL2KZH69PAMJF
2025-05-19 08:04:23,894 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250424, 变更字段: [{'field': 'recommendAmount', 'old_value': 2703.17, 'new_value': 2674.18}, {'field': 'amount', 'old_value': 2703.17, 'new_value': 2674.1800000000003}, {'field': 'count', 'old_value': 180, 'new_value': 179}, {'field': 'instoreAmount', 'old_value': 1350.19, 'new_value': 1321.2}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 85}]
2025-05-19 08:04:24,352 - INFO - 更新表单数据成功: FINST-2K666OB1JN0V35B9D6C269H0ZZGH207N3RBAMT21
2025-05-19 08:04:24,353 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9626.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9626.0}]
2025-05-19 08:04:24,787 - INFO - 更新表单数据成功: FINST-2K666OB1JN0V35B9D6C269H0ZZGH207N3RBAMI31
2025-05-19 08:04:24,787 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250501, 变更字段: [{'field': 'amount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-19 08:04:25,247 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMX
2025-05-19 08:04:25,248 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250505, 变更字段: [{'field': 'recommendAmount', 'old_value': 6194.99, 'new_value': 6133.49}, {'field': 'amount', 'old_value': 6194.99, 'new_value': 6133.49}, {'field': 'instoreAmount', 'old_value': 5907.81, 'new_value': 5846.31}]
2025-05-19 08:04:25,692 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMY
2025-05-19 08:04:25,692 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250504, 变更字段: [{'field': 'recommendAmount', 'old_value': 5079.06, 'new_value': 5059.16}, {'field': 'amount', 'old_value': 5079.06, 'new_value': 5059.160000000001}, {'field': 'instoreAmount', 'old_value': 4978.15, 'new_value': 4958.25}]
2025-05-19 08:04:26,236 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMZ
2025-05-19 08:04:26,236 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250503, 变更字段: [{'field': 'recommendAmount', 'old_value': 7466.35, 'new_value': 7458.75}, {'field': 'amount', 'old_value': 7466.35, 'new_value': 7458.75}, {'field': 'instoreAmount', 'old_value': 6919.87, 'new_value': 6912.27}]
2025-05-19 08:04:26,675 - INFO - 更新表单数据成功: FINST-X3E66X81LMZU2TVFEF5NSARPOTFR399GC16AMEE
2025-05-19 08:04:26,675 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 8619.82, 'new_value': 8612.02}, {'field': 'amount', 'old_value': 8619.82, 'new_value': 8612.02}, {'field': 'instoreAmount', 'old_value': 8264.74, 'new_value': 8256.94}]
2025-05-19 08:04:27,030 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMA1
2025-05-19 08:04:27,030 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2706.92, 'new_value': 2674.54}, {'field': 'amount', 'old_value': 2706.92, 'new_value': 2674.54}, {'field': 'count', 'old_value': 199, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 1010.18, 'new_value': 977.8}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 83}]
2025-05-19 08:04:27,535 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMB1
2025-05-19 08:04:27,535 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250505, 变更字段: [{'field': 'recommendAmount', 'old_value': 3699.32, 'new_value': 3604.15}, {'field': 'amount', 'old_value': 3699.3199999999997, 'new_value': 3604.1499999999996}, {'field': 'count', 'old_value': 233, 'new_value': 229}, {'field': 'instoreAmount', 'old_value': 1854.07, 'new_value': 1758.9}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 120}]
2025-05-19 08:04:27,963 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMD1
2025-05-19 08:04:27,964 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250503, 变更字段: [{'field': 'recommendAmount', 'old_value': 4161.02, 'new_value': 4138.33}, {'field': 'amount', 'old_value': 4161.02, 'new_value': 4138.33}, {'field': 'count', 'old_value': 254, 'new_value': 253}, {'field': 'instoreAmount', 'old_value': 1962.19, 'new_value': 1939.5}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 117}]
2025-05-19 08:04:28,429 - INFO - 更新表单数据成功: FINST-1MD668B12VFVXKDR9KMIMCRMJBIS3M0J79PAMX8
2025-05-19 08:04:28,429 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250502, 变更字段: [{'field': 'recommendAmount', 'old_value': 4051.13, 'new_value': 4029.44}, {'field': 'amount', 'old_value': 4051.13, 'new_value': 4029.44}, {'field': 'count', 'old_value': 179, 'new_value': 178}, {'field': 'instoreAmount', 'old_value': 2426.34, 'new_value': 2404.65}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 73}]
2025-05-19 08:04:28,863 - INFO - 更新表单数据成功: FINST-X3E66X81LMZU2TVFEF5NSARPOTFR399GC16AMOE
2025-05-19 08:04:28,864 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 4718.99, 'new_value': 4638.02}, {'field': 'amount', 'old_value': 4718.99, 'new_value': 4638.02}, {'field': 'count', 'old_value': 205, 'new_value': 202}, {'field': 'instoreAmount', 'old_value': 3183.31, 'new_value': 3102.34}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 95}]
2025-05-19 08:04:29,482 - INFO - 更新表单数据成功: FINST-RI766091IBGVQL11CTY5S7UWTO6S2ZQ299PAM61
2025-05-19 08:04:29,482 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'amount', 'old_value': 1075.9, 'new_value': 1078.9}, {'field': 'instoreAmount', 'old_value': 1075.9, 'new_value': 1078.9}]
2025-05-19 08:04:29,965 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAMJ2
2025-05-19 08:04:29,966 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4595.21, 'new_value': 4591.41}, {'field': 'amount', 'old_value': 4595.21, 'new_value': 4591.41}, {'field': 'instoreAmount', 'old_value': 4436.27, 'new_value': 4432.47}]
2025-05-19 08:04:30,386 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAMK2
2025-05-19 08:04:30,386 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 3485.07, 'new_value': 3481.27}, {'field': 'amount', 'old_value': 3485.07, 'new_value': 3481.27}, {'field': 'instoreAmount', 'old_value': 3232.17, 'new_value': 3228.37}]
2025-05-19 08:04:30,889 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAML2
2025-05-19 08:04:30,889 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 6249.31, 'new_value': 6245.51}, {'field': 'amount', 'old_value': 6249.31, 'new_value': 6245.51}, {'field': 'instoreAmount', 'old_value': 6088.99, 'new_value': 6085.19}]
2025-05-19 08:04:31,327 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMT
2025-05-19 08:04:31,327 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250509, 变更字段: [{'field': 'recommendAmount', 'old_value': 4940.68, 'new_value': 4936.88}, {'field': 'amount', 'old_value': 4940.68, 'new_value': 4936.88}, {'field': 'instoreAmount', 'old_value': 4759.24, 'new_value': 4755.44}]
2025-05-19 08:04:31,779 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAMT2
2025-05-19 08:04:31,779 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2815.96, 'new_value': 2668.19}, {'field': 'amount', 'old_value': 2815.96, 'new_value': 2668.19}, {'field': 'count', 'old_value': 185, 'new_value': 176}, {'field': 'instoreAmount', 'old_value': 1250.07, 'new_value': 1102.3}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 67}]
2025-05-19 08:04:32,212 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13T9D99PAMU2
2025-05-19 08:04:32,213 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 1823.66, 'new_value': 1775.79}, {'field': 'amount', 'old_value': 1823.66, 'new_value': 1775.79}, {'field': 'count', 'old_value': 115, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 723.78, 'new_value': 675.91}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 47}]
2025-05-19 08:04:32,677 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13T9D99PAMV2
2025-05-19 08:04:32,678 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 3130.87, 'new_value': 3010.62}, {'field': 'amount', 'old_value': 3130.87, 'new_value': 3010.62}, {'field': 'count', 'old_value': 186, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 1626.79, 'new_value': 1506.54}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 92}]
2025-05-19 08:04:33,117 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13T9D99PAMW2
2025-05-19 08:04:33,117 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250510, 变更字段: [{'field': 'recommendAmount', 'old_value': 3824.34, 'new_value': 3585.68}, {'field': 'amount', 'old_value': 3824.34, 'new_value': 3585.68}, {'field': 'count', 'old_value': 232, 'new_value': 220}, {'field': 'instoreAmount', 'old_value': 2133.36, 'new_value': 1894.7}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 120}]
2025-05-19 08:04:33,562 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAM71
2025-05-19 08:04:33,562 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250509, 变更字段: [{'field': 'recommendAmount', 'old_value': 3351.24, 'new_value': 3210.11}, {'field': 'amount', 'old_value': 3351.2400000000002, 'new_value': 3210.11}, {'field': 'count', 'old_value': 216, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 1309.38, 'new_value': 1168.25}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 74}]
2025-05-19 08:04:34,043 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAM81
2025-05-19 08:04:34,044 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250508, 变更字段: [{'field': 'recommendAmount', 'old_value': 2757.24, 'new_value': 2728.55}, {'field': 'amount', 'old_value': 2757.2400000000002, 'new_value': 2728.55}, {'field': 'count', 'old_value': 198, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 779.39, 'new_value': 750.7}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 56}]
2025-05-19 08:04:34,467 - INFO - 更新表单数据成功: FINST-RDC668B1YEHVFVNOENU126H1S5832CBKAWSAMRC
2025-05-19 08:04:34,467 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9208.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9208.3}]
2025-05-19 08:04:34,948 - INFO - 更新表单数据成功: FINST-RDC668B1YEHVFVNOENU126H1S5832DBKAWSAM4E
2025-05-19 08:04:34,948 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 3588.5, 'new_value': 2889.5}, {'field': 'amount', 'old_value': 3588.5, 'new_value': 2889.5}]
2025-05-19 08:04:35,374 - INFO - 更新表单数据成功: FINST-RDC668B1YEHVFVNOENU126H1S5832DBKAWSAMKE
2025-05-19 08:04:35,374 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_20250517, 变更字段: [{'field': 'amount', 'old_value': 3930.9, 'new_value': 3946.9}, {'field': 'count', 'old_value': 217, 'new_value': 218}, {'field': 'onlineAmount', 'old_value': 2148.18, 'new_value': 2164.18}, {'field': 'onlineCount', 'old_value': 120, 'new_value': 121}]
2025-05-19 08:04:35,825 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3KZMAWSAMQK
2025-05-19 08:04:35,825 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 11593.6, 'new_value': 11393.6}, {'field': 'amount', 'old_value': 11593.6, 'new_value': 11393.6}]
2025-05-19 08:04:36,215 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMZK
2025-05-19 08:04:36,215 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_20250517, 变更字段: [{'field': 'amount', 'old_value': 5054.6, 'new_value': 5340.6}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 5054.6, 'new_value': 5340.6}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 14}]
2025-05-19 08:04:36,705 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMBL
2025-05-19 08:04:36,706 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9878.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9878.0}]
2025-05-19 08:04:37,191 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMGL
2025-05-19 08:04:37,191 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 17674.0, 'new_value': 42171.0}, {'field': 'amount', 'old_value': 17674.0, 'new_value': 42171.0}, {'field': 'count', 'old_value': 56, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 17674.0, 'new_value': 42171.0}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 95}]
2025-05-19 08:04:37,595 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMJL
2025-05-19 08:04:37,595 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_20250517, 变更字段: [{'field': 'amount', 'old_value': -35477.03, 'new_value': -35685.53}]
2025-05-19 08:04:38,012 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMML
2025-05-19 08:04:38,013 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250517, 变更字段: [{'field': 'amount', 'old_value': 4173.0, 'new_value': 5505.0}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 4173.0, 'new_value': 5505.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-05-19 08:04:38,568 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMT7
2025-05-19 08:04:38,568 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250516, 变更字段: [{'field': 'amount', 'old_value': 7512.0, 'new_value': 7957.0}, {'field': 'count', 'old_value': 13, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 7512.0, 'new_value': 7957.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 17}]
2025-05-19 08:04:39,035 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMWL
2025-05-19 08:04:39,035 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1191.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1191.0}]
2025-05-19 08:04:39,553 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMCM
2025-05-19 08:04:39,554 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 3059.08, 'new_value': 3059.29}, {'field': 'amount', 'old_value': 3059.08, 'new_value': 3059.29}, {'field': 'count', 'old_value': 157, 'new_value': 159}, {'field': 'onlineAmount', 'old_value': 1968.4, 'new_value': 1968.61}, {'field': 'onlineCount', 'old_value': 100, 'new_value': 102}]
2025-05-19 08:04:39,985 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMLM
2025-05-19 08:04:39,985 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 1730.03, 'new_value': 1727.24}, {'field': 'amount', 'old_value': 1730.03, 'new_value': 1727.24}, {'field': 'instoreAmount', 'old_value': 1780.34, 'new_value': 1777.55}]
2025-05-19 08:04:40,581 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAMZM
2025-05-19 08:04:40,582 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 11343.68, 'new_value': 11553.37}, {'field': 'amount', 'old_value': 11343.68, 'new_value': 11553.37}, {'field': 'count', 'old_value': 216, 'new_value': 218}, {'field': 'instoreAmount', 'old_value': 10876.03, 'new_value': 10965.73}, {'field': 'instoreCount', 'old_value': 204, 'new_value': 205}, {'field': 'onlineAmount', 'old_value': 467.65, 'new_value': 587.64}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 13}]
2025-05-19 08:04:41,062 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAM2R
2025-05-19 08:04:41,063 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 1556.21, 'new_value': 1554.18}, {'field': 'amount', 'old_value': 1556.21, 'new_value': 1554.18}, {'field': 'count', 'old_value': 118, 'new_value': 119}, {'field': 'onlineAmount', 'old_value': 820.08, 'new_value': 818.05}, {'field': 'onlineCount', 'old_value': 66, 'new_value': 67}]
2025-05-19 08:04:41,512 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMBR
2025-05-19 08:04:41,513 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 5766.82, 'new_value': 5769.82}, {'field': 'amount', 'old_value': 5766.820000000001, 'new_value': 5769.820000000001}, {'field': 'count', 'old_value': 264, 'new_value': 265}, {'field': 'onlineAmount', 'old_value': 4424.34, 'new_value': 4427.34}, {'field': 'onlineCount', 'old_value': 193, 'new_value': 194}]
2025-05-19 08:04:41,947 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMER
2025-05-19 08:04:41,947 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_20250517, 变更字段: [{'field': 'amount', 'old_value': 6493.61, 'new_value': 6501.2}, {'field': 'count', 'old_value': 141, 'new_value': 144}, {'field': 'instoreAmount', 'old_value': 3911.73, 'new_value': 3919.32}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 39}]
2025-05-19 08:04:42,411 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMLR
2025-05-19 08:04:42,411 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 32275.89}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 32275.89}]
2025-05-19 08:04:42,864 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMQR
2025-05-19 08:04:42,865 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 5683.0, 'new_value': 8635.0}, {'field': 'amount', 'old_value': 5683.0, 'new_value': 8635.0}, {'field': 'count', 'old_value': 30, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 5683.0, 'new_value': 8635.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 31}]
2025-05-19 08:04:43,358 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMUR
2025-05-19 08:04:43,359 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_20250517, 变更字段: [{'field': 'amount', 'old_value': 27969.05, 'new_value': 29589.35}, {'field': 'count', 'old_value': 289, 'new_value': 293}, {'field': 'instoreAmount', 'old_value': 21493.8, 'new_value': 23114.1}, {'field': 'instoreCount', 'old_value': 152, 'new_value': 156}]
2025-05-19 08:04:43,744 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMWR
2025-05-19 08:04:43,745 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250517, 变更字段: [{'field': 'amount', 'old_value': 37957.55, 'new_value': 39410.55}, {'field': 'count', 'old_value': 270, 'new_value': 275}, {'field': 'instoreAmount', 'old_value': 22087.4, 'new_value': 23540.4}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 130}]
2025-05-19 08:04:44,215 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAM4S
2025-05-19 08:04:44,216 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250517, 变更字段: [{'field': 'amount', 'old_value': 60494.51, 'new_value': 60793.51}, {'field': 'count', 'old_value': 348, 'new_value': 349}, {'field': 'instoreAmount', 'old_value': 47119.91, 'new_value': 47418.91}, {'field': 'instoreCount', 'old_value': 192, 'new_value': 193}]
2025-05-19 08:04:44,655 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMOS
2025-05-19 08:04:44,655 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250517, 变更字段: [{'field': 'amount', 'old_value': 31716.3, 'new_value': 31730.100000000002}, {'field': 'count', 'old_value': 323, 'new_value': 324}, {'field': 'instoreAmount', 'old_value': 24974.61, 'new_value': 24988.41}, {'field': 'instoreCount', 'old_value': 137, 'new_value': 138}]
2025-05-19 08:04:45,066 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMRS
2025-05-19 08:04:45,066 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250517, 变更字段: [{'field': 'amount', 'old_value': 94.78999999999999, 'new_value': 485.89}, {'field': 'count', 'old_value': 13, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 103.8, 'new_value': 522.9}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 45}]
2025-05-19 08:04:45,547 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMWS
2025-05-19 08:04:45,547 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 6039.0, 'new_value': 13248.0}, {'field': 'amount', 'old_value': 6039.0, 'new_value': 13248.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 6039.0, 'new_value': 13248.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-05-19 08:04:46,015 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMAH
2025-05-19 08:04:46,015 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 8618.78, 'new_value': 8989.98}, {'field': 'amount', 'old_value': 8618.78, 'new_value': 8989.98}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 8618.78, 'new_value': 8989.98}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-05-19 08:04:46,461 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2FNPAWSAM8T
2025-05-19 08:04:46,461 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250517, 变更字段: [{'field': 'amount', 'old_value': 2114.94, 'new_value': 2071.36}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 1068.26, 'new_value': 1154.28}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 17}]
2025-05-19 08:04:46,940 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP34ASAWSAMY
2025-05-19 08:04:46,941 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 3318.0, 'new_value': 4226.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4226.0}]
2025-05-19 08:04:47,370 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP34ASAWSAMC1
2025-05-19 08:04:47,371 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_20250517, 变更字段: [{'field': 'amount', 'old_value': 3182.82, 'new_value': 3215.42}, {'field': 'count', 'old_value': 120, 'new_value': 121}, {'field': 'onlineAmount', 'old_value': 1808.88, 'new_value': 1841.48}, {'field': 'onlineCount', 'old_value': 67, 'new_value': 68}]
2025-05-19 08:04:47,718 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP34ASAWSAMI1
2025-05-19 08:04:47,718 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250517, 变更字段: [{'field': 'amount', 'old_value': 5175.51, 'new_value': 5213.01}, {'field': 'count', 'old_value': 342, 'new_value': 345}, {'field': 'onlineAmount', 'old_value': 4688.62, 'new_value': 4726.12}, {'field': 'onlineCount', 'old_value': 299, 'new_value': 302}]
2025-05-19 08:04:48,209 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP34ASAWSAMK1
2025-05-19 08:04:48,210 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 17273.47}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 17273.47}]
2025-05-19 08:04:48,659 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP34ASAWSAMM1
2025-05-19 08:04:48,659 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 8186.57, 'new_value': 8236.07}, {'field': 'amount', 'old_value': 8186.57, 'new_value': 8236.07}, {'field': 'count', 'old_value': 154, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 7746.95, 'new_value': 7796.45}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 150}]
2025-05-19 08:04:49,112 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP34ASAWSAMW1
2025-05-19 08:04:49,112 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250517, 变更字段: [{'field': 'amount', 'old_value': 7075.72, 'new_value': 7169.52}, {'field': 'count', 'old_value': 508, 'new_value': 513}, {'field': 'instoreAmount', 'old_value': 5579.39, 'new_value': 5594.64}, {'field': 'instoreCount', 'old_value': 384, 'new_value': 390}, {'field': 'onlineAmount', 'old_value': 1665.7, 'new_value': 1744.25}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 123}]
2025-05-19 08:04:49,593 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAMS2
2025-05-19 08:04:49,594 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_20250517, 变更字段: [{'field': 'count', 'old_value': 119, 'new_value': 120}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 110}]
2025-05-19 08:04:49,972 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMCH
2025-05-19 08:04:49,972 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 5372.65, 'new_value': 5283.65}, {'field': 'amount', 'old_value': 5372.65, 'new_value': 5283.65}, {'field': 'instoreAmount', 'old_value': 5233.18, 'new_value': 5144.18}]
2025-05-19 08:04:50,412 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAM23
2025-05-19 08:04:50,412 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 4580.07, 'new_value': 4514.79}, {'field': 'amount', 'old_value': 4580.07, 'new_value': 4514.79}, {'field': 'count', 'old_value': 334, 'new_value': 329}, {'field': 'instoreAmount', 'old_value': 2150.62, 'new_value': 2085.34}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 150}]
2025-05-19 08:04:50,849 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3UYFD1QAMK7
2025-05-19 08:04:50,850 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 3050.79, 'new_value': 2925.95}, {'field': 'amount', 'old_value': 3050.79, 'new_value': 2925.95}, {'field': 'count', 'old_value': 180, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 1209.59, 'new_value': 1084.75}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 65}]
2025-05-19 08:04:51,328 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAM43
2025-05-19 08:04:51,329 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 6278.42, 'new_value': 6313.42}, {'field': 'amount', 'old_value': 6278.42, 'new_value': 6313.42}, {'field': 'count', 'old_value': 337, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 4438.04, 'new_value': 4473.04}, {'field': 'instoreCount', 'old_value': 223, 'new_value': 225}]
2025-05-19 08:04:51,751 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAMA3
2025-05-19 08:04:51,751 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250517, 变更字段: [{'field': 'amount', 'old_value': 29880.579999999998, 'new_value': 30643.28}, {'field': 'count', 'old_value': 268, 'new_value': 271}, {'field': 'instoreAmount', 'old_value': 28046.94, 'new_value': 28809.64}, {'field': 'instoreCount', 'old_value': 199, 'new_value': 202}]
2025-05-19 08:04:52,294 - INFO - 更新表单数据成功: FINST-RNA66D71GYHVG1PY7014I7W5X12W3ZWUAWSAME9
2025-05-19 08:04:52,295 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250517, 变更字段: [{'field': 'amount', 'old_value': 24108.29, 'new_value': 26319.29}, {'field': 'count', 'old_value': 90, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 24342.6, 'new_value': 26553.6}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 64}]
2025-05-19 08:04:52,698 - INFO - 更新表单数据成功: FINST-RNA66D71GYHVG1PY7014I7W5X12W3ZWUAWSAMZ9
2025-05-19 08:04:52,698 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 39971.42, 'new_value': 39991.68}, {'field': 'amount', 'old_value': 39971.42, 'new_value': 39991.68}, {'field': 'count', 'old_value': 228, 'new_value': 229}, {'field': 'onlineAmount', 'old_value': 1772.42, 'new_value': 1792.68}, {'field': 'onlineCount', 'old_value': 57, 'new_value': 58}]
2025-05-19 08:04:53,098 - INFO - 更新表单数据成功: FINST-RNA66D71GYHVG1PY7014I7W5X12W3ZWUAWSAMFA
2025-05-19 08:04:53,099 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_20250517, 变更字段: [{'field': 'amount', 'old_value': 962.37, 'new_value': 943.85}]
2025-05-19 08:04:53,171 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-19 08:04:53,641 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-19 08:04:56,645 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-19 08:04:57,152 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-19 08:05:00,154 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-19 08:05:00,676 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-19 08:05:03,679 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-19 08:05:04,073 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-19 08:05:07,076 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-19 08:05:07,457 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-19 08:05:10,460 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-19 08:05:10,854 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-19 08:05:13,856 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-19 08:05:14,331 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-19 08:05:17,334 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-19 08:05:17,737 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-19 08:05:20,740 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-19 08:05:21,209 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-19 08:05:24,212 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-19 08:05:24,727 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-19 08:05:27,729 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-19 08:05:28,156 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-19 08:05:31,160 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-19 08:05:31,600 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-19 08:05:34,603 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-19 08:05:35,020 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-19 08:05:38,023 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-19 08:05:38,485 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-19 08:05:41,488 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-19 08:05:41,991 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-19 08:05:44,994 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-19 08:05:45,395 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-19 08:05:48,399 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-19 08:05:48,789 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-19 08:05:51,793 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-19 08:05:52,296 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-19 08:05:55,300 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-19 08:05:55,722 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-19 08:05:58,725 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-19 08:05:59,239 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-19 08:06:02,242 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-19 08:06:02,759 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-19 08:06:05,763 - INFO - 正在批量插入每日数据，批次 22/22，共 85 条记录
2025-05-19 08:06:06,140 - INFO - 批量插入每日数据成功，批次 22，85 条记录
2025-05-19 08:06:09,141 - INFO - 批量插入每日数据完成: 总计 2185 条，成功 2185 条，失败 0 条
2025-05-19 08:06:09,145 - INFO - 批量插入日销售数据完成，共 2185 条记录
2025-05-19 08:06:09,145 - INFO - 日销售数据同步完成！更新: 71 条，插入: 2185 条，错误: 0 条，跳过: 10865 条
2025-05-19 08:06:09,145 - INFO - 正在获取宜搭月销售表单数据...
2025-05-19 08:06:09,145 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-19 08:06:09,146 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-19 08:06:09,146 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:09,146 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:09,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:09,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:09,958 - INFO - API请求耗时: 812ms
2025-05-19 08:06:09,958 - INFO - Response - Page 1
2025-05-19 08:06:09,958 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:09,959 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:09,959 - WARNING - 月度分段 1 查询返回空数据
2025-05-19 08:06:09,959 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-19 08:06:09,960 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:09,960 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:09,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:09,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:10,179 - INFO - API请求耗时: 220ms
2025-05-19 08:06:10,179 - INFO - Response - Page 1
2025-05-19 08:06:10,180 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:10,180 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:10,180 - WARNING - 单月查询返回空数据: 2024-05
2025-05-19 08:06:10,680 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-19 08:06:10,680 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:10,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:10,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:10,885 - INFO - API请求耗时: 204ms
2025-05-19 08:06:10,885 - INFO - Response - Page 1
2025-05-19 08:06:10,885 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:10,885 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:10,886 - WARNING - 单月查询返回空数据: 2024-06
2025-05-19 08:06:11,386 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:11,386 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:11,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:11,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:11,579 - INFO - API请求耗时: 191ms
2025-05-19 08:06:11,580 - INFO - Response - Page 1
2025-05-19 08:06:11,580 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:11,581 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:11,581 - WARNING - 单月查询返回空数据: 2024-07
2025-05-19 08:06:13,083 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-19 08:06:13,083 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:13,084 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:13,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:13,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:13,302 - INFO - API请求耗时: 219ms
2025-05-19 08:06:13,303 - INFO - Response - Page 1
2025-05-19 08:06:13,303 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:13,304 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:13,304 - WARNING - 月度分段 2 查询返回空数据
2025-05-19 08:06:13,305 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-19 08:06:13,305 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:13,305 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:13,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:13,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:13,527 - INFO - API请求耗时: 222ms
2025-05-19 08:06:13,527 - INFO - Response - Page 1
2025-05-19 08:06:13,528 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:13,528 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:13,528 - WARNING - 单月查询返回空数据: 2024-08
2025-05-19 08:06:14,029 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-19 08:06:14,029 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:14,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:14,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:14,214 - INFO - API请求耗时: 184ms
2025-05-19 08:06:14,214 - INFO - Response - Page 1
2025-05-19 08:06:14,214 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:14,214 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:14,214 - WARNING - 单月查询返回空数据: 2024-09
2025-05-19 08:06:14,715 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:14,715 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:14,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:14,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:14,945 - INFO - API请求耗时: 229ms
2025-05-19 08:06:14,945 - INFO - Response - Page 1
2025-05-19 08:06:14,946 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-19 08:06:14,946 - INFO - 查询完成，共获取到 0 条记录
2025-05-19 08:06:14,946 - WARNING - 单月查询返回空数据: 2024-10
2025-05-19 08:06:16,447 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-19 08:06:16,447 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:16,448 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:16,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:16,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:17,083 - INFO - API请求耗时: 634ms
2025-05-19 08:06:17,083 - INFO - Response - Page 1
2025-05-19 08:06:17,084 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:06:17,585 - INFO - Request Parameters - Page 2:
2025-05-19 08:06:17,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:17,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:18,182 - INFO - API请求耗时: 597ms
2025-05-19 08:06:18,182 - INFO - Response - Page 2
2025-05-19 08:06:18,183 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:06:18,683 - INFO - Request Parameters - Page 3:
2025-05-19 08:06:18,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:18,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:19,173 - INFO - API请求耗时: 489ms
2025-05-19 08:06:19,174 - INFO - Response - Page 3
2025-05-19 08:06:19,175 - INFO - 第 3 页获取到 48 条记录
2025-05-19 08:06:19,175 - INFO - 查询完成，共获取到 248 条记录
2025-05-19 08:06:19,175 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-19 08:06:20,176 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-19 08:06:20,176 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-19 08:06:20,177 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:20,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:20,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:20,779 - INFO - API请求耗时: 601ms
2025-05-19 08:06:20,780 - INFO - Response - Page 1
2025-05-19 08:06:20,780 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:06:21,282 - INFO - Request Parameters - Page 2:
2025-05-19 08:06:21,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:21,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:21,886 - INFO - API请求耗时: 604ms
2025-05-19 08:06:21,887 - INFO - Response - Page 2
2025-05-19 08:06:21,887 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:06:22,387 - INFO - Request Parameters - Page 3:
2025-05-19 08:06:22,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:22,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:22,893 - INFO - API请求耗时: 505ms
2025-05-19 08:06:22,893 - INFO - Response - Page 3
2025-05-19 08:06:22,894 - INFO - 第 3 页获取到 100 条记录
2025-05-19 08:06:23,394 - INFO - Request Parameters - Page 4:
2025-05-19 08:06:23,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:23,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:23,948 - INFO - API请求耗时: 553ms
2025-05-19 08:06:23,948 - INFO - Response - Page 4
2025-05-19 08:06:23,949 - INFO - 第 4 页获取到 100 条记录
2025-05-19 08:06:24,449 - INFO - Request Parameters - Page 5:
2025-05-19 08:06:24,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:24,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:25,052 - INFO - API请求耗时: 603ms
2025-05-19 08:06:25,052 - INFO - Response - Page 5
2025-05-19 08:06:25,053 - INFO - 第 5 页获取到 100 条记录
2025-05-19 08:06:25,554 - INFO - Request Parameters - Page 6:
2025-05-19 08:06:25,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:25,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:26,118 - INFO - API请求耗时: 562ms
2025-05-19 08:06:26,118 - INFO - Response - Page 6
2025-05-19 08:06:26,119 - INFO - 第 6 页获取到 100 条记录
2025-05-19 08:06:26,619 - INFO - Request Parameters - Page 7:
2025-05-19 08:06:26,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:26,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:27,225 - INFO - API请求耗时: 605ms
2025-05-19 08:06:27,225 - INFO - Response - Page 7
2025-05-19 08:06:27,226 - INFO - 第 7 页获取到 100 条记录
2025-05-19 08:06:27,726 - INFO - Request Parameters - Page 8:
2025-05-19 08:06:27,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:27,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:28,036 - INFO - API请求耗时: 309ms
2025-05-19 08:06:28,036 - INFO - Response - Page 8
2025-05-19 08:06:28,037 - INFO - 第 8 页获取到 16 条记录
2025-05-19 08:06:28,037 - INFO - 查询完成，共获取到 716 条记录
2025-05-19 08:06:28,037 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-19 08:06:29,038 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-19 08:06:29,038 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-19 08:06:29,039 - INFO - Request Parameters - Page 1:
2025-05-19 08:06:29,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:29,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:29,622 - INFO - API请求耗时: 582ms
2025-05-19 08:06:29,623 - INFO - Response - Page 1
2025-05-19 08:06:29,623 - INFO - 第 1 页获取到 100 条记录
2025-05-19 08:06:30,124 - INFO - Request Parameters - Page 2:
2025-05-19 08:06:30,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:30,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:30,721 - INFO - API请求耗时: 597ms
2025-05-19 08:06:30,721 - INFO - Response - Page 2
2025-05-19 08:06:30,722 - INFO - 第 2 页获取到 100 条记录
2025-05-19 08:06:31,223 - INFO - Request Parameters - Page 3:
2025-05-19 08:06:31,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-19 08:06:31,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-19 08:06:31,635 - INFO - API请求耗时: 412ms
2025-05-19 08:06:31,636 - INFO - Response - Page 3
2025-05-19 08:06:31,636 - INFO - 第 3 页获取到 24 条记录
2025-05-19 08:06:31,636 - INFO - 查询完成，共获取到 224 条记录
2025-05-19 08:06:31,637 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-19 08:06:32,638 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-19 08:06:32,638 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-19 08:06:32,639 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-19 08:06:32,639 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-19 08:06:32,649 - INFO - 成功获取SQLite月度汇总数据，共 1190 条记录
2025-05-19 08:06:32,709 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-19 08:06:33,183 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82621IVMXX9MZD
2025-05-19 08:06:33,184 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-04, 变更字段: [{'field': 'amount', 'old_value': 121454.08, 'new_value': 120735.78}]
2025-05-19 08:06:33,635 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-19 08:06:33,635 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118213.28, 'new_value': 126426.29}, {'field': 'dailyBillAmount', 'old_value': 118213.28, 'new_value': 126426.29}, {'field': 'amount', 'old_value': 3506.7, 'new_value': 3834.7}, {'field': 'count', 'old_value': 49, 'new_value': 51}, {'field': 'onlineAmount', 'old_value': 3582.7, 'new_value': 3910.7}, {'field': 'onlineCount', 'old_value': 49, 'new_value': 51}]
2025-05-19 08:06:34,055 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-19 08:06:34,056 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 313411.63, 'new_value': 336616.61}, {'field': 'dailyBillAmount', 'old_value': 313411.63, 'new_value': 336616.61}, {'field': 'amount', 'old_value': 169079.5, 'new_value': 180628.0}, {'field': 'count', 'old_value': 1546, 'new_value': 1635}, {'field': 'instoreAmount', 'old_value': 66842.9, 'new_value': 72067.7}, {'field': 'instoreCount', 'old_value': 482, 'new_value': 515}, {'field': 'onlineAmount', 'old_value': 102528.0, 'new_value': 108851.7}, {'field': 'onlineCount', 'old_value': 1064, 'new_value': 1120}]
2025-05-19 08:06:34,475 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-19 08:06:34,476 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204699.74, 'new_value': 219419.18}, {'field': 'dailyBillAmount', 'old_value': 204699.74, 'new_value': 219419.18}, {'field': 'amount', 'old_value': 206608.11000000002, 'new_value': 221407.51}, {'field': 'count', 'old_value': 1370, 'new_value': 1464}, {'field': 'instoreAmount', 'old_value': 194756.1, 'new_value': 209068.3}, {'field': 'instoreCount', 'old_value': 1196, 'new_value': 1283}, {'field': 'onlineAmount', 'old_value': 12070.21, 'new_value': 12557.41}, {'field': 'onlineCount', 'old_value': 174, 'new_value': 181}]
2025-05-19 08:06:35,000 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-19 08:06:35,001 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 460566.97, 'new_value': 487720.62}, {'field': 'dailyBillAmount', 'old_value': 460566.97, 'new_value': 487720.62}, {'field': 'amount', 'old_value': 344045.9, 'new_value': 363813.9}, {'field': 'count', 'old_value': 1656, 'new_value': 1760}, {'field': 'instoreAmount', 'old_value': 344045.9, 'new_value': 363813.9}, {'field': 'instoreCount', 'old_value': 1656, 'new_value': 1760}]
2025-05-19 08:06:35,544 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-19 08:06:35,544 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 368705.74, 'new_value': 394501.24}, {'field': 'dailyBillAmount', 'old_value': 368705.74, 'new_value': 394501.24}, {'field': 'amount', 'old_value': 581957.0, 'new_value': 625509.0}, {'field': 'count', 'old_value': 1992, 'new_value': 2147}, {'field': 'instoreAmount', 'old_value': 583207.0, 'new_value': 626759.0}, {'field': 'instoreCount', 'old_value': 1992, 'new_value': 2147}]
2025-05-19 08:06:36,015 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-19 08:06:36,015 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47967.3, 'new_value': 49834.2}, {'field': 'dailyBillAmount', 'old_value': 47967.3, 'new_value': 49834.2}, {'field': 'amount', 'old_value': 63056.21, 'new_value': 65932.71}, {'field': 'count', 'old_value': 196, 'new_value': 210}, {'field': 'instoreAmount', 'old_value': 37950.7, 'new_value': 39080.5}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 36}, {'field': 'onlineAmount', 'old_value': 28744.02, 'new_value': 30948.72}, {'field': 'onlineCount', 'old_value': 163, 'new_value': 174}]
2025-05-19 08:06:36,461 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-19 08:06:36,461 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 155285.9, 'new_value': 191425.9}, {'field': 'amount', 'old_value': 155285.9, 'new_value': 191425.9}, {'field': 'count', 'old_value': 91, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 155285.9, 'new_value': 191425.9}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 97}]
2025-05-19 08:06:36,934 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-19 08:06:36,935 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 507217.5, 'new_value': 540814.56}, {'field': 'dailyBillAmount', 'old_value': 507217.5, 'new_value': 540814.56}, {'field': 'amount', 'old_value': 493632.76, 'new_value': 515692.76}, {'field': 'count', 'old_value': 3572, 'new_value': 3703}, {'field': 'instoreAmount', 'old_value': 394091.56, 'new_value': 414437.26}, {'field': 'instoreCount', 'old_value': 1669, 'new_value': 1761}, {'field': 'onlineAmount', 'old_value': 102815.87, 'new_value': 104530.17}, {'field': 'onlineCount', 'old_value': 1903, 'new_value': 1942}]
2025-05-19 08:06:37,345 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-05-19 08:06:37,345 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 177386.19, 'new_value': 190622.88999999998}, {'field': 'amount', 'old_value': 177379.6, 'new_value': 190616.69}, {'field': 'count', 'old_value': 7431, 'new_value': 7992}, {'field': 'onlineAmount', 'old_value': 181402.03, 'new_value': 194900.17}, {'field': 'onlineCount', 'old_value': 7431, 'new_value': 7992}]
2025-05-19 08:06:37,832 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-19 08:06:37,832 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 518233.42, 'new_value': 549995.07}, {'field': 'dailyBillAmount', 'old_value': 518233.42, 'new_value': 549995.07}, {'field': 'amount', 'old_value': 115069.89, 'new_value': 120642.89}, {'field': 'count', 'old_value': 589, 'new_value': 620}, {'field': 'instoreAmount', 'old_value': 115069.89, 'new_value': 120642.89}, {'field': 'instoreCount', 'old_value': 589, 'new_value': 620}]
2025-05-19 08:06:38,193 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-19 08:06:38,194 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 16440.0, 'new_value': 17137.0}, {'field': 'count', 'old_value': 22, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 16440.0, 'new_value': 17137.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 24}]
2025-05-19 08:06:38,674 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-19 08:06:38,674 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 83999.3, 'new_value': 85776.3}, {'field': 'count', 'old_value': 235, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 84000.1, 'new_value': 85777.1}, {'field': 'instoreCount', 'old_value': 235, 'new_value': 240}]
2025-05-19 08:06:39,162 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-19 08:06:39,162 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22670.9, 'new_value': 24670.9}, {'field': 'amount', 'old_value': 22670.9, 'new_value': 24670.9}, {'field': 'count', 'old_value': 20, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 24066.9, 'new_value': 26066.9}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 21}]
2025-05-19 08:06:39,623 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-19 08:06:39,623 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 695790.5499999999, 'new_value': 764399.5}, {'field': 'dailyBillAmount', 'old_value': 695790.5499999999, 'new_value': 764399.5}, {'field': 'amount', 'old_value': -310320.01, 'new_value': -285788.57}, {'field': 'count', 'old_value': 746, 'new_value': 806}, {'field': 'instoreAmount', 'old_value': 437945.49, 'new_value': 509776.22}, {'field': 'instoreCount', 'old_value': 746, 'new_value': 806}]
2025-05-19 08:06:40,089 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-19 08:06:40,089 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 226305.0, 'new_value': 269672.0}, {'field': 'amount', 'old_value': 226305.0, 'new_value': 269672.0}, {'field': 'count', 'old_value': 902, 'new_value': 1013}, {'field': 'instoreAmount', 'old_value': 226305.0, 'new_value': 269672.0}, {'field': 'instoreCount', 'old_value': 902, 'new_value': 1013}]
2025-05-19 08:06:40,466 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-19 08:06:40,466 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 288127.27, 'new_value': 305361.24}, {'field': 'amount', 'old_value': 288127.27, 'new_value': 305361.24}, {'field': 'count', 'old_value': 1005, 'new_value': 1064}, {'field': 'instoreAmount', 'old_value': 288127.27, 'new_value': 305361.24}, {'field': 'instoreCount', 'old_value': 1005, 'new_value': 1064}]
2025-05-19 08:06:40,917 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-19 08:06:40,918 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 134866.25, 'new_value': 144744.25}, {'field': 'dailyBillAmount', 'old_value': 134866.25, 'new_value': 144744.25}, {'field': 'amount', 'old_value': 9700.3, 'new_value': 10209.3}, {'field': 'count', 'old_value': 76, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 11594.2, 'new_value': 12103.2}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 78}]
2025-05-19 08:06:41,345 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-19 08:06:41,346 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73684.9, 'new_value': 78682.02}, {'field': 'dailyBillAmount', 'old_value': 73684.9, 'new_value': 78682.02}, {'field': 'amount', 'old_value': 47610.16, 'new_value': 50113.12}, {'field': 'count', 'old_value': 698, 'new_value': 735}, {'field': 'instoreAmount', 'old_value': 49230.26, 'new_value': 51951.22}, {'field': 'instoreCount', 'old_value': 698, 'new_value': 735}]
2025-05-19 08:06:41,855 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-19 08:06:41,855 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99571.95, 'new_value': 105619.56999999999}, {'field': 'dailyBillAmount', 'old_value': 55012.57, 'new_value': 60614.67}, {'field': 'amount', 'old_value': 99571.09, 'new_value': 105618.70999999999}, {'field': 'count', 'old_value': 3455, 'new_value': 3669}, {'field': 'instoreAmount', 'old_value': 87866.52, 'new_value': 93120.18}, {'field': 'instoreCount', 'old_value': 3135, 'new_value': 3323}, {'field': 'onlineAmount', 'old_value': 11705.43, 'new_value': 12499.39}, {'field': 'onlineCount', 'old_value': 320, 'new_value': 346}]
2025-05-19 08:06:42,355 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-19 08:06:42,356 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 171982.22, 'new_value': 176313.22}, {'field': 'dailyBillAmount', 'old_value': 168524.0, 'new_value': 172855.0}, {'field': 'amount', 'old_value': 138685.22, 'new_value': 139740.22}, {'field': 'count', 'old_value': 144, 'new_value': 152}, {'field': 'instoreAmount', 'old_value': 138556.0, 'new_value': 139611.0}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 151}]
2025-05-19 08:06:42,814 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-19 08:06:42,814 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 351735.21, 'new_value': 406236.34}, {'field': 'dailyBillAmount', 'old_value': 351230.66, 'new_value': 405731.79}, {'field': 'amount', 'old_value': 351735.21, 'new_value': 406236.34}, {'field': 'count', 'old_value': 330, 'new_value': 358}, {'field': 'instoreAmount', 'old_value': 351736.21, 'new_value': 406237.34}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 358}]
2025-05-19 08:06:43,316 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-19 08:06:43,317 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76251.8, 'new_value': 81269.5}, {'field': 'dailyBillAmount', 'old_value': 76251.8, 'new_value': 81269.5}, {'field': 'amount', 'old_value': 85775.8, 'new_value': 90615.0}, {'field': 'count', 'old_value': 219, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 85780.4, 'new_value': 90620.2}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 234}]
2025-05-19 08:06:43,806 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-19 08:06:43,807 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115452.87, 'new_value': 122435.87}, {'field': 'amount', 'old_value': 115452.87, 'new_value': 122435.87}, {'field': 'count', 'old_value': 139, 'new_value': 144}, {'field': 'instoreAmount', 'old_value': 115579.87, 'new_value': 122562.87}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 144}]
2025-05-19 08:06:44,301 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-19 08:06:44,302 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'amount', 'old_value': 165746.05, 'new_value': 183474.95}, {'field': 'count', 'old_value': 1106, 'new_value': 1213}, {'field': 'instoreAmount', 'old_value': 166955.05, 'new_value': 184683.95}, {'field': 'instoreCount', 'old_value': 1106, 'new_value': 1213}]
2025-05-19 08:06:44,660 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-19 08:06:44,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102036.81, 'new_value': 107079.86}, {'field': 'dailyBillAmount', 'old_value': 102036.81, 'new_value': 107079.86}, {'field': 'amount', 'old_value': 9437.64, 'new_value': 9734.44}, {'field': 'count', 'old_value': 877, 'new_value': 910}, {'field': 'instoreAmount', 'old_value': 12978.0, 'new_value': 13444.1}, {'field': 'instoreCount', 'old_value': 877, 'new_value': 910}]
2025-05-19 08:06:45,164 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-19 08:06:45,164 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 177237.86, 'new_value': 189883.86}, {'field': 'amount', 'old_value': 177235.07, 'new_value': 189880.47}, {'field': 'count', 'old_value': 4463, 'new_value': 4797}, {'field': 'instoreAmount', 'old_value': 172259.31, 'new_value': 184506.01}, {'field': 'instoreCount', 'old_value': 4308, 'new_value': 4624}, {'field': 'onlineAmount', 'old_value': 7578.2300000000005, 'new_value': 8310.73}, {'field': 'onlineCount', 'old_value': 155, 'new_value': 173}]
2025-05-19 08:06:45,573 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-19 08:06:45,574 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156279.47, 'new_value': 174825.29}, {'field': 'dailyBillAmount', 'old_value': 156279.47, 'new_value': 174825.29}, {'field': 'amount', 'old_value': 156279.47, 'new_value': 174825.29}, {'field': 'count', 'old_value': 491, 'new_value': 535}, {'field': 'instoreAmount', 'old_value': 156279.47, 'new_value': 174825.29}, {'field': 'instoreCount', 'old_value': 491, 'new_value': 535}]
2025-05-19 08:06:46,128 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-19 08:06:46,129 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 139906.06, 'new_value': 152216.62}, {'field': 'dailyBillAmount', 'old_value': 139906.06, 'new_value': 152216.62}, {'field': 'amount', 'old_value': 44403.2, 'new_value': 50124.2}, {'field': 'count', 'old_value': 110, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 44403.2, 'new_value': 50124.2}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 119}]
2025-05-19 08:06:46,611 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-19 08:06:46,611 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 286243.63, 'new_value': 303869.9}, {'field': 'dailyBillAmount', 'old_value': 286243.63, 'new_value': 303869.9}, {'field': 'amount', 'old_value': 117479.1, 'new_value': 124803.9}, {'field': 'count', 'old_value': 448, 'new_value': 473}, {'field': 'instoreAmount', 'old_value': 117479.36, 'new_value': 124804.16}, {'field': 'instoreCount', 'old_value': 448, 'new_value': 473}]
2025-05-19 08:06:47,078 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-19 08:06:47,078 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61629.76, 'new_value': 64359.67}, {'field': 'dailyBillAmount', 'old_value': 61629.76, 'new_value': 64359.67}, {'field': 'amount', 'old_value': 18719.05, 'new_value': 19599.08}, {'field': 'count', 'old_value': 683, 'new_value': 715}, {'field': 'instoreAmount', 'old_value': 4586.91, 'new_value': 4727.71}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 120}, {'field': 'onlineAmount', 'old_value': 14371.16, 'new_value': 15125.89}, {'field': 'onlineCount', 'old_value': 569, 'new_value': 595}]
2025-05-19 08:06:47,544 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-19 08:06:47,544 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98243.26, 'new_value': 104561.35}, {'field': 'dailyBillAmount', 'old_value': 98243.26, 'new_value': 104561.35}, {'field': 'amount', 'old_value': 15422.15, 'new_value': 16402.82}, {'field': 'count', 'old_value': 382, 'new_value': 408}, {'field': 'instoreAmount', 'old_value': 13025.11, 'new_value': 13863.31}, {'field': 'instoreCount', 'old_value': 336, 'new_value': 359}, {'field': 'onlineAmount', 'old_value': 2397.73, 'new_value': 2540.2}, {'field': 'onlineCount', 'old_value': 46, 'new_value': 49}]
2025-05-19 08:06:48,026 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-19 08:06:48,026 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14096.03, 'new_value': 17032.03}, {'field': 'dailyBillAmount', 'old_value': 14096.03, 'new_value': 17032.03}, {'field': 'amount', 'old_value': 11685.18, 'new_value': 14460.18}, {'field': 'count', 'old_value': 420, 'new_value': 469}, {'field': 'instoreAmount', 'old_value': 12052.78, 'new_value': 14827.78}, {'field': 'instoreCount', 'old_value': 420, 'new_value': 469}]
2025-05-19 08:06:48,372 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-19 08:06:48,372 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32294.56, 'new_value': 34123.57}, {'field': 'dailyBillAmount', 'old_value': 32294.56, 'new_value': 34123.57}, {'field': 'amount', 'old_value': 20141.97, 'new_value': 21583.09}, {'field': 'count', 'old_value': 1119, 'new_value': 1187}, {'field': 'instoreAmount', 'old_value': 10125.13, 'new_value': 10865.55}, {'field': 'instoreCount', 'old_value': 440, 'new_value': 476}, {'field': 'onlineAmount', 'old_value': 10492.82, 'new_value': 11193.52}, {'field': 'onlineCount', 'old_value': 679, 'new_value': 711}]
2025-05-19 08:06:48,853 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-19 08:06:48,853 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 220108.19, 'new_value': 234153.5}, {'field': 'dailyBillAmount', 'old_value': 220108.19, 'new_value': 234153.5}, {'field': 'amount', 'old_value': 102400.27, 'new_value': 108827.56}, {'field': 'count', 'old_value': 410, 'new_value': 440}, {'field': 'instoreAmount', 'old_value': 106156.52, 'new_value': 112632.52}, {'field': 'instoreCount', 'old_value': 410, 'new_value': 440}]
2025-05-19 08:06:49,310 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-19 08:06:49,310 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 12005.09, 'new_value': 12147.73}, {'field': 'count', 'old_value': 95, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 12079.33, 'new_value': 12221.97}, {'field': 'instoreCount', 'old_value': 95, 'new_value': 99}]
2025-05-19 08:06:49,755 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-19 08:06:49,755 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 141803.51, 'new_value': 149668.63}, {'field': 'dailyBillAmount', 'old_value': 141803.51, 'new_value': 149668.63}, {'field': 'amount', 'old_value': 67551.95999999999, 'new_value': 71977.04}, {'field': 'count', 'old_value': 2865, 'new_value': 3032}, {'field': 'instoreAmount', 'old_value': 68963.18, 'new_value': 73409.26}, {'field': 'instoreCount', 'old_value': 2865, 'new_value': 3032}]
2025-05-19 08:06:50,189 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-19 08:06:50,189 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 304364.4, 'new_value': 329732.8}, {'field': 'dailyBillAmount', 'old_value': 304364.4, 'new_value': 329732.8}, {'field': 'amount', 'old_value': 304364.4, 'new_value': 329732.8}, {'field': 'count', 'old_value': 382, 'new_value': 412}, {'field': 'instoreAmount', 'old_value': 304364.4, 'new_value': 329732.8}, {'field': 'instoreCount', 'old_value': 382, 'new_value': 412}]
2025-05-19 08:06:50,648 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-19 08:06:50,649 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149322.74, 'new_value': 161723.91}, {'field': 'dailyBillAmount', 'old_value': 149322.74, 'new_value': 161723.91}, {'field': 'amount', 'old_value': 86605.81, 'new_value': 93820.70999999999}, {'field': 'count', 'old_value': 227, 'new_value': 243}, {'field': 'instoreAmount', 'old_value': 88022.41, 'new_value': 95237.31}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 243}]
2025-05-19 08:06:51,091 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-19 08:06:51,091 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34662.0, 'new_value': 36301.0}, {'field': 'dailyBillAmount', 'old_value': 34662.0, 'new_value': 36301.0}, {'field': 'amount', 'old_value': 34662.0, 'new_value': 36301.0}, {'field': 'count', 'old_value': 679, 'new_value': 710}, {'field': 'instoreAmount', 'old_value': 34701.0, 'new_value': 36340.0}, {'field': 'instoreCount', 'old_value': 679, 'new_value': 710}]
2025-05-19 08:06:51,627 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-19 08:06:51,627 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60884.04, 'new_value': 64012.45}, {'field': 'dailyBillAmount', 'old_value': 60884.04, 'new_value': 64012.45}, {'field': 'amount', 'old_value': 63056.31, 'new_value': 66215.88}, {'field': 'count', 'old_value': 3323, 'new_value': 3500}, {'field': 'instoreAmount', 'old_value': 29876.57, 'new_value': 31385.62}, {'field': 'instoreCount', 'old_value': 1493, 'new_value': 1581}, {'field': 'onlineAmount', 'old_value': 34075.86, 'new_value': 35727.28}, {'field': 'onlineCount', 'old_value': 1830, 'new_value': 1919}]
2025-05-19 08:06:52,091 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-19 08:06:52,092 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21252.55, 'new_value': 22536.78}, {'field': 'dailyBillAmount', 'old_value': 21252.55, 'new_value': 22536.78}, {'field': 'amount', 'old_value': 29397.4, 'new_value': 31236.49}, {'field': 'count', 'old_value': 865, 'new_value': 909}, {'field': 'instoreAmount', 'old_value': 26463.85, 'new_value': 28112.68}, {'field': 'instoreCount', 'old_value': 746, 'new_value': 786}, {'field': 'onlineAmount', 'old_value': 2956.35, 'new_value': 3146.61}, {'field': 'onlineCount', 'old_value': 119, 'new_value': 123}]
2025-05-19 08:06:52,574 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-19 08:06:52,575 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44440.83, 'new_value': 46468.65}, {'field': 'dailyBillAmount', 'old_value': 44440.83, 'new_value': 46468.65}, {'field': 'amount', 'old_value': 44404.94, 'new_value': 46374.8}, {'field': 'count', 'old_value': 1716, 'new_value': 1797}, {'field': 'instoreAmount', 'old_value': 28755.02, 'new_value': 29785.02}, {'field': 'instoreCount', 'old_value': 1019, 'new_value': 1056}, {'field': 'onlineAmount', 'old_value': 15774.98, 'new_value': 16714.84}, {'field': 'onlineCount', 'old_value': 697, 'new_value': 741}]
2025-05-19 08:06:52,979 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-19 08:06:52,979 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 47284.43, 'new_value': 50486.73}, {'field': 'count', 'old_value': 561, 'new_value': 602}, {'field': 'instoreAmount', 'old_value': 47711.33, 'new_value': 50913.63}, {'field': 'instoreCount', 'old_value': 561, 'new_value': 602}]
2025-05-19 08:06:53,504 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-19 08:06:53,504 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51293.3, 'new_value': 54647.7}, {'field': 'amount', 'old_value': 51292.8, 'new_value': 54647.2}, {'field': 'count', 'old_value': 1262, 'new_value': 1350}, {'field': 'instoreAmount', 'old_value': 51941.5, 'new_value': 55325.6}, {'field': 'instoreCount', 'old_value': 1262, 'new_value': 1350}]
2025-05-19 08:06:53,981 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-19 08:06:53,981 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 232141.02, 'new_value': 249519.42}, {'field': 'dailyBillAmount', 'old_value': 232141.02, 'new_value': 249519.42}, {'field': 'amount', 'old_value': 67626.02, 'new_value': 73731.82}, {'field': 'count', 'old_value': 248, 'new_value': 264}, {'field': 'instoreAmount', 'old_value': 67626.02, 'new_value': 73731.82}, {'field': 'instoreCount', 'old_value': 248, 'new_value': 264}]
2025-05-19 08:06:54,572 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-19 08:06:54,572 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73817.92, 'new_value': 77506.52}, {'field': 'dailyBillAmount', 'old_value': 73817.92, 'new_value': 77506.52}, {'field': 'amount', 'old_value': 71396.12, 'new_value': 74808.72}, {'field': 'count', 'old_value': 233, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 73530.75, 'new_value': 76943.35}, {'field': 'instoreCount', 'old_value': 233, 'new_value': 251}]
2025-05-19 08:06:55,061 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-19 08:06:55,062 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39602.0, 'new_value': 40829.0}, {'field': 'dailyBillAmount', 'old_value': 39602.0, 'new_value': 40829.0}, {'field': 'amount', 'old_value': 49035.0, 'new_value': 50262.0}, {'field': 'count', 'old_value': 90, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 53133.0, 'new_value': 54360.0}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 93}]
2025-05-19 08:06:55,569 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-19 08:06:55,570 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60559.15, 'new_value': 61681.75}, {'field': 'dailyBillAmount', 'old_value': 57366.95, 'new_value': 59188.549999999996}, {'field': 'amount', 'old_value': 60557.95, 'new_value': 61454.85}, {'field': 'count', 'old_value': 177, 'new_value': 187}, {'field': 'instoreAmount', 'old_value': 67796.95, 'new_value': 69393.35}, {'field': 'instoreCount', 'old_value': 177, 'new_value': 187}]
2025-05-19 08:06:56,029 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-19 08:06:56,029 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84673.34, 'new_value': 90833.92}, {'field': 'dailyBillAmount', 'old_value': 84673.34, 'new_value': 90833.92}, {'field': 'amount', 'old_value': 46627.85, 'new_value': 50216.21}, {'field': 'count', 'old_value': 1289, 'new_value': 1382}, {'field': 'instoreAmount', 'old_value': 39937.909999999996, 'new_value': 43365.909999999996}, {'field': 'instoreCount', 'old_value': 1091, 'new_value': 1176}, {'field': 'onlineAmount', 'old_value': 6876.82, 'new_value': 7171.38}, {'field': 'onlineCount', 'old_value': 198, 'new_value': 206}]
2025-05-19 08:06:56,455 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-19 08:06:56,456 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117205.62, 'new_value': 124854.36}, {'field': 'dailyBillAmount', 'old_value': 112887.72, 'new_value': 120394.51}, {'field': 'amount', 'old_value': 117205.62, 'new_value': 124854.36}, {'field': 'count', 'old_value': 1436, 'new_value': 1524}, {'field': 'instoreAmount', 'old_value': 112165.45, 'new_value': 119118.45}, {'field': 'instoreCount', 'old_value': 1380, 'new_value': 1462}, {'field': 'onlineAmount', 'old_value': 5040.17, 'new_value': 5735.91}, {'field': 'onlineCount', 'old_value': 56, 'new_value': 62}]
2025-05-19 08:06:56,903 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-19 08:06:56,904 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53200.18, 'new_value': 57811.65}, {'field': 'dailyBillAmount', 'old_value': 53200.18, 'new_value': 57811.65}, {'field': 'amount', 'old_value': 73396.84, 'new_value': 78117.21}, {'field': 'count', 'old_value': 302, 'new_value': 329}, {'field': 'instoreAmount', 'old_value': 70816.01, 'new_value': 75248.63}, {'field': 'instoreCount', 'old_value': 271, 'new_value': 295}, {'field': 'onlineAmount', 'old_value': 2580.83, 'new_value': 2868.58}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 34}]
2025-05-19 08:06:57,280 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-19 08:06:57,280 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149554.4, 'new_value': 157757.4}, {'field': 'dailyBillAmount', 'old_value': 149554.4, 'new_value': 157757.4}, {'field': 'amount', 'old_value': 150671.7, 'new_value': 160842.7}, {'field': 'count', 'old_value': 550, 'new_value': 585}, {'field': 'instoreAmount', 'old_value': 152852.6, 'new_value': 163023.6}, {'field': 'instoreCount', 'old_value': 550, 'new_value': 585}]
2025-05-19 08:06:57,716 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-19 08:06:57,716 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35059.0, 'new_value': 38170.0}, {'field': 'dailyBillAmount', 'old_value': 35059.0, 'new_value': 38170.0}, {'field': 'amount', 'old_value': 33964.0, 'new_value': 35368.0}, {'field': 'count', 'old_value': 78, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 34320.0, 'new_value': 35724.0}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 84}]
2025-05-19 08:06:58,173 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-19 08:06:58,174 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 14943.08, 'new_value': 15294.2}, {'field': 'count', 'old_value': 30, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 15224.48, 'new_value': 15575.6}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 31}]
2025-05-19 08:06:58,662 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-19 08:06:58,662 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 33459.87, 'new_value': 36468.590000000004}, {'field': 'count', 'old_value': 177, 'new_value': 193}, {'field': 'instoreAmount', 'old_value': 32187.8, 'new_value': 35156.8}, {'field': 'instoreCount', 'old_value': 138, 'new_value': 151}, {'field': 'onlineAmount', 'old_value': 2087.2200000000003, 'new_value': 2152.54}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 42}]
2025-05-19 08:06:59,212 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-19 08:06:59,212 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17092.1, 'new_value': 18921.6}, {'field': 'dailyBillAmount', 'old_value': 17092.1, 'new_value': 18921.6}, {'field': 'amount', 'old_value': 13460.91, 'new_value': 14604.71}, {'field': 'count', 'old_value': 606, 'new_value': 654}, {'field': 'instoreAmount', 'old_value': 13629.36, 'new_value': 14773.16}, {'field': 'instoreCount', 'old_value': 606, 'new_value': 654}]
2025-05-19 08:06:59,626 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-19 08:06:59,627 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29038.07, 'new_value': 30607.41}, {'field': 'amount', 'old_value': 29036.600000000002, 'new_value': 30605.940000000002}, {'field': 'count', 'old_value': 1557, 'new_value': 1631}, {'field': 'instoreAmount', 'old_value': 34534.3, 'new_value': 36246.33}, {'field': 'instoreCount', 'old_value': 1557, 'new_value': 1631}]
2025-05-19 08:07:00,101 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-19 08:07:00,101 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92884.24, 'new_value': 98769.7}, {'field': 'dailyBillAmount', 'old_value': 92884.24, 'new_value': 98769.7}, {'field': 'amount', 'old_value': 73023.6, 'new_value': 77463.3}, {'field': 'count', 'old_value': 294, 'new_value': 313}, {'field': 'instoreAmount', 'old_value': 73023.6, 'new_value': 77463.3}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 313}]
2025-05-19 08:07:00,534 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-19 08:07:00,534 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 263894.32, 'new_value': 280131.72}, {'field': 'dailyBillAmount', 'old_value': 263894.32, 'new_value': 280131.72}, {'field': 'amount', 'old_value': 163271.05, 'new_value': 168044.41}, {'field': 'count', 'old_value': 1898, 'new_value': 1964}, {'field': 'instoreAmount', 'old_value': 66150.21, 'new_value': 68138.17}, {'field': 'instoreCount', 'old_value': 768, 'new_value': 803}, {'field': 'onlineAmount', 'old_value': 97120.84, 'new_value': 99906.24}, {'field': 'onlineCount', 'old_value': 1130, 'new_value': 1161}]
2025-05-19 08:07:00,950 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-19 08:07:00,951 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 167707.82, 'new_value': 175987.82}, {'field': 'dailyBillAmount', 'old_value': 167707.82, 'new_value': 175987.82}, {'field': 'amount', 'old_value': 173847.6, 'new_value': 182276.6}, {'field': 'count', 'old_value': 1028, 'new_value': 1088}, {'field': 'instoreAmount', 'old_value': 174627.5, 'new_value': 183056.5}, {'field': 'instoreCount', 'old_value': 1028, 'new_value': 1088}]
2025-05-19 08:07:01,565 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-19 08:07:01,565 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48459.89, 'new_value': 61795.89}, {'field': 'amount', 'old_value': 48459.89, 'new_value': 61795.89}, {'field': 'count', 'old_value': 22, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 48459.89, 'new_value': 61795.89}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 26}]
2025-05-19 08:07:02,076 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-19 08:07:02,077 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116959.52, 'new_value': 122031.76}, {'field': 'dailyBillAmount', 'old_value': 116959.52, 'new_value': 122031.76}, {'field': 'amount', 'old_value': 76041.67, 'new_value': 78809.37}, {'field': 'count', 'old_value': 867, 'new_value': 904}, {'field': 'instoreAmount', 'old_value': 67668.62, 'new_value': 69882.14}, {'field': 'instoreCount', 'old_value': 594, 'new_value': 613}, {'field': 'onlineAmount', 'old_value': 9164.22, 'new_value': 9718.4}, {'field': 'onlineCount', 'old_value': 273, 'new_value': 291}]
2025-05-19 08:07:02,509 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-19 08:07:02,509 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1694.3, 'new_value': 1839.6}, {'field': 'count', 'old_value': 17, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 1694.3, 'new_value': 1839.6}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 21}]
2025-05-19 08:07:02,929 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-19 08:07:02,930 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 130140.45, 'new_value': 137526.5}, {'field': 'dailyBillAmount', 'old_value': 125334.7, 'new_value': 132720.75}, {'field': 'amount', 'old_value': 130140.45, 'new_value': 137526.5}, {'field': 'count', 'old_value': 543, 'new_value': 577}, {'field': 'instoreAmount', 'old_value': 130140.45, 'new_value': 137526.5}, {'field': 'instoreCount', 'old_value': 543, 'new_value': 577}]
2025-05-19 08:07:03,355 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-19 08:07:03,356 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15711.07, 'new_value': 16651.27}, {'field': 'dailyBillAmount', 'old_value': 15711.07, 'new_value': 16651.27}, {'field': 'amount', 'old_value': 18518.57, 'new_value': 19506.27}, {'field': 'count', 'old_value': 556, 'new_value': 588}, {'field': 'instoreAmount', 'old_value': 18518.57, 'new_value': 19506.27}, {'field': 'instoreCount', 'old_value': 556, 'new_value': 588}]
2025-05-19 08:07:03,874 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-19 08:07:03,875 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 200700.2, 'new_value': 210735.2}, {'field': 'amount', 'old_value': 200700.2, 'new_value': 210735.2}, {'field': 'count', 'old_value': 299, 'new_value': 316}, {'field': 'instoreAmount', 'old_value': 200700.2, 'new_value': 210735.2}, {'field': 'instoreCount', 'old_value': 299, 'new_value': 316}]
2025-05-19 08:07:04,475 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-19 08:07:04,475 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34698.1, 'new_value': 37203.53}, {'field': 'amount', 'old_value': 34698.1, 'new_value': 37203.53}, {'field': 'count', 'old_value': 277, 'new_value': 301}, {'field': 'instoreAmount', 'old_value': 34698.1, 'new_value': 37203.53}, {'field': 'instoreCount', 'old_value': 277, 'new_value': 301}]
2025-05-19 08:07:04,948 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-19 08:07:04,948 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 239866.0, 'new_value': 251464.0}, {'field': 'amount', 'old_value': 239866.0, 'new_value': 251464.0}, {'field': 'count', 'old_value': 48, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 239866.0, 'new_value': 251464.0}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 50}]
2025-05-19 08:07:05,404 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMG01
2025-05-19 08:07:05,404 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38052.0, 'new_value': 44628.0}, {'field': 'amount', 'old_value': 38052.0, 'new_value': 44628.0}, {'field': 'count', 'old_value': 7, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 38052.0, 'new_value': 44628.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 9}]
2025-05-19 08:07:05,955 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-19 08:07:05,955 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 27654.4, 'new_value': 30654.100000000002}, {'field': 'count', 'old_value': 359, 'new_value': 400}, {'field': 'instoreAmount', 'old_value': 27654.4, 'new_value': 30654.100000000002}, {'field': 'instoreCount', 'old_value': 359, 'new_value': 400}]
2025-05-19 08:07:06,423 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-19 08:07:06,424 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33144.4, 'new_value': 34846.0}, {'field': 'dailyBillAmount', 'old_value': 33144.4, 'new_value': 34846.0}, {'field': 'amount', 'old_value': 33144.4, 'new_value': 34846.0}, {'field': 'count', 'old_value': 39, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 33144.4, 'new_value': 34846.0}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 41}]
2025-05-19 08:07:07,130 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-19 08:07:07,130 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 308039.2, 'new_value': 330307.36}, {'field': 'dailyBillAmount', 'old_value': 308039.2, 'new_value': 330307.36}, {'field': 'amount', 'old_value': 320670.2, 'new_value': 342938.36}, {'field': 'count', 'old_value': 1013, 'new_value': 1083}, {'field': 'instoreAmount', 'old_value': 320670.2, 'new_value': 342938.36}, {'field': 'instoreCount', 'old_value': 1013, 'new_value': 1083}]
2025-05-19 08:07:07,560 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-19 08:07:07,560 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 262406.0, 'new_value': 291286.0}, {'field': 'dailyBillAmount', 'old_value': 262406.0, 'new_value': 291286.0}, {'field': 'amount', 'old_value': 709556.3, 'new_value': 734793.32}, {'field': 'count', 'old_value': 916, 'new_value': 950}, {'field': 'instoreAmount', 'old_value': 709556.47, 'new_value': 734793.49}, {'field': 'instoreCount', 'old_value': 916, 'new_value': 950}]
2025-05-19 08:07:08,025 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-19 08:07:08,026 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102014.1, 'new_value': 111222.4}, {'field': 'dailyBillAmount', 'old_value': 102014.1, 'new_value': 111222.4}, {'field': 'amount', 'old_value': 23032.2, 'new_value': 23418.2}, {'field': 'count', 'old_value': 89, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 23033.7, 'new_value': 23419.7}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 90}]
2025-05-19 08:07:08,536 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-19 08:07:08,537 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149538.88999999998, 'new_value': 162223.06}, {'field': 'amount', 'old_value': 149538.24, 'new_value': 162222.41}, {'field': 'count', 'old_value': 1480, 'new_value': 1636}, {'field': 'instoreAmount', 'old_value': 99184.3, 'new_value': 107257.52}, {'field': 'instoreCount', 'old_value': 865, 'new_value': 959}, {'field': 'onlineAmount', 'old_value': 53784.52, 'new_value': 59046.26}, {'field': 'onlineCount', 'old_value': 615, 'new_value': 677}]
2025-05-19 08:07:08,938 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-19 08:07:08,938 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 224365.3, 'new_value': 241963.02}, {'field': 'dailyBillAmount', 'old_value': 224365.3, 'new_value': 241963.02}, {'field': 'amount', 'old_value': 21539.28, 'new_value': 23224.3}, {'field': 'count', 'old_value': 677, 'new_value': 725}, {'field': 'instoreAmount', 'old_value': 24771.88, 'new_value': 26673.41}, {'field': 'instoreCount', 'old_value': 677, 'new_value': 725}]
2025-05-19 08:07:09,472 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-19 08:07:09,472 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231987.03, 'new_value': 242779.29}, {'field': 'dailyBillAmount', 'old_value': 231987.03, 'new_value': 242779.29}, {'field': 'amount', 'old_value': 113955.46, 'new_value': 122835.02}, {'field': 'count', 'old_value': 2537, 'new_value': 2758}, {'field': 'instoreAmount', 'old_value': 97100.21, 'new_value': 104634.33}, {'field': 'instoreCount', 'old_value': 2162, 'new_value': 2351}, {'field': 'onlineAmount', 'old_value': 18697.07, 'new_value': 20185.21}, {'field': 'onlineCount', 'old_value': 375, 'new_value': 407}]
2025-05-19 08:07:09,924 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-19 08:07:09,925 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 216379.6, 'new_value': 228229.5}, {'field': 'amount', 'old_value': 216378.0, 'new_value': 228227.9}, {'field': 'count', 'old_value': 853, 'new_value': 903}, {'field': 'instoreAmount', 'old_value': 219106.9, 'new_value': 231040.1}, {'field': 'instoreCount', 'old_value': 853, 'new_value': 903}]
2025-05-19 08:07:10,440 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-19 08:07:10,440 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 358363.72, 'new_value': 400191.23}, {'field': 'dailyBillAmount', 'old_value': 358363.72, 'new_value': 400191.23}, {'field': 'amount', 'old_value': 354338.54, 'new_value': 378280.02}, {'field': 'count', 'old_value': 6723, 'new_value': 7189}, {'field': 'instoreAmount', 'old_value': 334185.28, 'new_value': 358126.76}, {'field': 'instoreCount', 'old_value': 6330, 'new_value': 6796}]
2025-05-19 08:07:10,867 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-19 08:07:10,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 192854.07, 'new_value': 212822.77}, {'field': 'amount', 'old_value': 160568.8, 'new_value': 180537.5}, {'field': 'count', 'old_value': 3873, 'new_value': 4345}, {'field': 'instoreAmount', 'old_value': 144947.8, 'new_value': 163970.1}, {'field': 'instoreCount', 'old_value': 3154, 'new_value': 3582}, {'field': 'onlineAmount', 'old_value': 15780.8, 'new_value': 16727.2}, {'field': 'onlineCount', 'old_value': 719, 'new_value': 763}]
2025-05-19 08:07:11,334 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-19 08:07:11,335 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 34062.04, 'new_value': 32233.309999999998}, {'field': 'count', 'old_value': 818, 'new_value': 820}, {'field': 'onlineAmount', 'old_value': 44812.729999999996, 'new_value': 44842.729999999996}, {'field': 'onlineCount', 'old_value': 783, 'new_value': 785}]
2025-05-19 08:07:11,793 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-19 08:07:11,793 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53109.82, 'new_value': 63441.99}, {'field': 'dailyBillAmount', 'old_value': 53109.82, 'new_value': 63441.99}, {'field': 'amount', 'old_value': 111263.19, 'new_value': 118514.24}, {'field': 'count', 'old_value': 7644, 'new_value': 8165}, {'field': 'instoreAmount', 'old_value': 92743.18000000001, 'new_value': 98702.65}, {'field': 'instoreCount', 'old_value': 6218, 'new_value': 6623}, {'field': 'onlineAmount', 'old_value': 21103.81, 'new_value': 22830.94}, {'field': 'onlineCount', 'old_value': 1426, 'new_value': 1542}]
2025-05-19 08:07:12,208 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-19 08:07:12,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194002.89, 'new_value': 210610.01}, {'field': 'dailyBillAmount', 'old_value': 194002.89, 'new_value': 210610.01}, {'field': 'amount', 'old_value': 187571.04, 'new_value': 203464.54}, {'field': 'count', 'old_value': 5474, 'new_value': 5942}, {'field': 'instoreAmount', 'old_value': 188822.35, 'new_value': 204771.45}, {'field': 'instoreCount', 'old_value': 5474, 'new_value': 5942}]
2025-05-19 08:07:12,778 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-19 08:07:12,779 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54488.97, 'new_value': 60222.770000000004}, {'field': 'amount', 'old_value': 54488.090000000004, 'new_value': 60221.89}, {'field': 'count', 'old_value': 2971, 'new_value': 3234}, {'field': 'instoreAmount', 'old_value': 34044.81, 'new_value': 37068.51}, {'field': 'instoreCount', 'old_value': 1925, 'new_value': 2090}, {'field': 'onlineAmount', 'old_value': 20444.16, 'new_value': 23154.26}, {'field': 'onlineCount', 'old_value': 1046, 'new_value': 1144}]
2025-05-19 08:07:13,395 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-19 08:07:13,395 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96589.19, 'new_value': 105648.54}, {'field': 'dailyBillAmount', 'old_value': 96589.19, 'new_value': 105648.54}, {'field': 'amount', 'old_value': 20490.43, 'new_value': 21779.670000000002}, {'field': 'count', 'old_value': 714, 'new_value': 758}, {'field': 'instoreAmount', 'old_value': 21149.52, 'new_value': 22455.46}, {'field': 'instoreCount', 'old_value': 714, 'new_value': 758}]
2025-05-19 08:07:13,850 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-19 08:07:13,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77263.08, 'new_value': 84515.75}, {'field': 'dailyBillAmount', 'old_value': 77263.08, 'new_value': 84515.75}, {'field': 'amount', 'old_value': 66728.55, 'new_value': 70214.01}, {'field': 'count', 'old_value': 3355, 'new_value': 3543}, {'field': 'instoreAmount', 'old_value': 15545.74, 'new_value': 16284.69}, {'field': 'instoreCount', 'old_value': 1105, 'new_value': 1163}, {'field': 'onlineAmount', 'old_value': 52161.55, 'new_value': 54943.06}, {'field': 'onlineCount', 'old_value': 2250, 'new_value': 2380}]
2025-05-19 08:07:14,286 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-19 08:07:14,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73056.87, 'new_value': 79400.6}, {'field': 'amount', 'old_value': 73056.06, 'new_value': 79399.72}, {'field': 'count', 'old_value': 1943, 'new_value': 2103}, {'field': 'instoreAmount', 'old_value': 70401.6, 'new_value': 76615.11}, {'field': 'instoreCount', 'old_value': 1893, 'new_value': 2051}, {'field': 'onlineAmount', 'old_value': 3502.29, 'new_value': 3644.41}, {'field': 'onlineCount', 'old_value': 50, 'new_value': 52}]
2025-05-19 08:07:14,747 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-19 08:07:14,747 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49674.58, 'new_value': 66948.05}, {'field': 'dailyBillAmount', 'old_value': 49674.58, 'new_value': 66948.05}, {'field': 'amount', 'old_value': 104014.98, 'new_value': 111312.53}, {'field': 'count', 'old_value': 3778, 'new_value': 4716}, {'field': 'instoreAmount', 'old_value': 105275.94, 'new_value': 113995.63}, {'field': 'instoreCount', 'old_value': 3746, 'new_value': 4674}, {'field': 'onlineAmount', 'old_value': 1522.31, 'new_value': 1585.51}, {'field': 'onlineCount', 'old_value': 32, 'new_value': 42}]
2025-05-19 08:07:15,177 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-19 08:07:15,177 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 84541.35, 'new_value': 89527.7}, {'field': 'count', 'old_value': 7289, 'new_value': 7614}, {'field': 'instoreAmount', 'old_value': 5936.91, 'new_value': 6381.91}, {'field': 'instoreCount', 'old_value': 310, 'new_value': 344}, {'field': 'onlineAmount', 'old_value': 82985.96, 'new_value': 87733.62}, {'field': 'onlineCount', 'old_value': 6979, 'new_value': 7270}]
2025-05-19 08:07:15,621 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-19 08:07:15,622 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115050.83, 'new_value': 122533.37}, {'field': 'dailyBillAmount', 'old_value': 115050.83, 'new_value': 122533.37}, {'field': 'amount', 'old_value': 97388.19, 'new_value': 103243.85}, {'field': 'count', 'old_value': 3337, 'new_value': 3555}, {'field': 'instoreAmount', 'old_value': 55914.84, 'new_value': 59502.0}, {'field': 'instoreCount', 'old_value': 2451, 'new_value': 2626}, {'field': 'onlineAmount', 'old_value': 48348.71, 'new_value': 50685.21}, {'field': 'onlineCount', 'old_value': 886, 'new_value': 929}]
2025-05-19 08:07:16,060 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-19 08:07:16,060 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'count', 'old_value': 36, 'new_value': 38}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 38}]
2025-05-19 08:07:16,487 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-19 08:07:16,487 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39249.92, 'new_value': 42257.64}, {'field': 'dailyBillAmount', 'old_value': 39249.92, 'new_value': 42257.64}, {'field': 'amount', 'old_value': 54605.94, 'new_value': 57816.14}, {'field': 'count', 'old_value': 2130, 'new_value': 2262}, {'field': 'instoreAmount', 'old_value': 18037.57, 'new_value': 19841.02}, {'field': 'instoreCount', 'old_value': 780, 'new_value': 846}, {'field': 'onlineAmount', 'old_value': 37266.98, 'new_value': 38820.75}, {'field': 'onlineCount', 'old_value': 1350, 'new_value': 1416}]
2025-05-19 08:07:16,931 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-19 08:07:16,931 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70204.1, 'new_value': 76509.35}, {'field': 'dailyBillAmount', 'old_value': 70204.1, 'new_value': 76509.35}, {'field': 'amount', 'old_value': 72230.38, 'new_value': 78725.78}, {'field': 'count', 'old_value': 2575, 'new_value': 2791}, {'field': 'instoreAmount', 'old_value': 72230.38, 'new_value': 78725.78}, {'field': 'instoreCount', 'old_value': 2575, 'new_value': 2791}]
2025-05-19 08:07:17,463 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-19 08:07:17,463 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 240694.0, 'new_value': 272889.0}, {'field': 'dailyBillAmount', 'old_value': 240694.0, 'new_value': 272889.0}, {'field': 'amount', 'old_value': 249000.0, 'new_value': 280409.0}, {'field': 'count', 'old_value': 208, 'new_value': 230}, {'field': 'instoreAmount', 'old_value': 269500.0, 'new_value': 306708.0}, {'field': 'instoreCount', 'old_value': 208, 'new_value': 230}]
2025-05-19 08:07:17,958 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-19 08:07:17,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 191621.96, 'new_value': 199807.06}, {'field': 'count', 'old_value': 356, 'new_value': 374}, {'field': 'instoreAmount', 'old_value': 193537.66, 'new_value': 201722.76}, {'field': 'instoreCount', 'old_value': 356, 'new_value': 374}]
2025-05-19 08:07:18,369 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-19 08:07:18,370 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29584.0, 'new_value': 32852.0}, {'field': 'amount', 'old_value': 29584.0, 'new_value': 32852.0}, {'field': 'count', 'old_value': 59, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 29584.0, 'new_value': 32852.0}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 66}]
2025-05-19 08:07:18,795 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-19 08:07:18,795 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 28814.0, 'new_value': 30604.0}, {'field': 'count', 'old_value': 77, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 29977.0, 'new_value': 31767.0}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 81}]
2025-05-19 08:07:19,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-19 08:07:19,240 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50311.0, 'new_value': 54808.0}, {'field': 'dailyBillAmount', 'old_value': 31642.0, 'new_value': 39457.0}, {'field': 'amount', 'old_value': 48056.0, 'new_value': 51176.0}, {'field': 'count', 'old_value': 61, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 48056.0, 'new_value': 51176.0}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 66}]
2025-05-19 08:07:19,718 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-19 08:07:19,718 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51589.5, 'new_value': 54546.5}, {'field': 'amount', 'old_value': 51587.3, 'new_value': 54544.3}, {'field': 'count', 'old_value': 139, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 51589.5, 'new_value': 54546.5}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 147}]
2025-05-19 08:07:20,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-19 08:07:20,185 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 385124.0, 'new_value': 413721.0}, {'field': 'dailyBillAmount', 'old_value': 385124.0, 'new_value': 413721.0}, {'field': 'amount', 'old_value': 447752.0, 'new_value': 476349.0}, {'field': 'count', 'old_value': 56, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 447752.0, 'new_value': 476349.0}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 59}]
2025-05-19 08:07:20,652 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-19 08:07:20,652 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67208.0, 'new_value': 74816.0}, {'field': 'amount', 'old_value': 67208.0, 'new_value': 74816.0}, {'field': 'count', 'old_value': 17, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 67208.0, 'new_value': 74816.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 19}]
2025-05-19 08:07:21,094 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-19 08:07:21,094 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18958.0, 'new_value': 19716.0}, {'field': 'amount', 'old_value': 18958.0, 'new_value': 19716.0}, {'field': 'count', 'old_value': 27, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 18958.0, 'new_value': 19716.0}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 29}]
2025-05-19 08:07:21,528 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-19 08:07:21,528 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51474.0, 'new_value': 56409.0}, {'field': 'amount', 'old_value': 51474.0, 'new_value': 56409.0}, {'field': 'count', 'old_value': 59, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 51474.0, 'new_value': 56409.0}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 63}]
2025-05-19 08:07:21,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-19 08:07:21,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 158658.7, 'new_value': 171747.8}, {'field': 'dailyBillAmount', 'old_value': 158658.7, 'new_value': 171747.8}, {'field': 'amount', 'old_value': 250714.69999999998, 'new_value': 263703.8}, {'field': 'count', 'old_value': 314, 'new_value': 328}, {'field': 'instoreAmount', 'old_value': 258056.56, 'new_value': 271852.26}, {'field': 'instoreCount', 'old_value': 314, 'new_value': 328}]
2025-05-19 08:07:22,439 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-19 08:07:22,440 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78358.61, 'new_value': 86223.59}, {'field': 'dailyBillAmount', 'old_value': 78358.61, 'new_value': 86223.59}, {'field': 'amount', 'old_value': 13770.640000000001, 'new_value': 21795.24}, {'field': 'count', 'old_value': 141, 'new_value': 211}, {'field': 'instoreAmount', 'old_value': 13415.44, 'new_value': 21533.44}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 169}, {'field': 'onlineAmount', 'old_value': 2289.5, 'new_value': 2405.1}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 42}]
2025-05-19 08:07:22,914 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-19 08:07:22,914 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25471.0, 'new_value': 27515.0}, {'field': 'dailyBillAmount', 'old_value': 25471.0, 'new_value': 27515.0}, {'field': 'amount', 'old_value': 29303.0, 'new_value': 31333.0}, {'field': 'count', 'old_value': 95, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 29303.0, 'new_value': 31333.0}, {'field': 'instoreCount', 'old_value': 95, 'new_value': 101}]
2025-05-19 08:07:23,386 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-19 08:07:23,387 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24984.7, 'new_value': 26889.7}, {'field': 'amount', 'old_value': 24984.7, 'new_value': 26889.7}, {'field': 'count', 'old_value': 142, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 25322.7, 'new_value': 27227.7}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 155}]
2025-05-19 08:07:23,781 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-19 08:07:23,781 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 5779.0, 'new_value': 6429.0}, {'field': 'dailyBillAmount', 'old_value': 5779.0, 'new_value': 6429.0}, {'field': 'amount', 'old_value': 31109.0, 'new_value': 32560.0}, {'field': 'count', 'old_value': 95, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 31884.0, 'new_value': 33335.0}, {'field': 'instoreCount', 'old_value': 95, 'new_value': 98}]
2025-05-19 08:07:24,231 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-19 08:07:24,231 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 588266.76, 'new_value': 634834.59}, {'field': 'dailyBillAmount', 'old_value': 588266.76, 'new_value': 634834.59}, {'field': 'amount', 'old_value': 37464.78, 'new_value': 40061.79}, {'field': 'count', 'old_value': 351, 'new_value': 380}, {'field': 'instoreAmount', 'old_value': 30415.79, 'new_value': 32983.8}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 270}, {'field': 'onlineAmount', 'old_value': 7941.41, 'new_value': 8100.37}, {'field': 'onlineCount', 'old_value': 108, 'new_value': 110}]
2025-05-19 08:07:24,686 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-19 08:07:24,686 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60083.0, 'new_value': 62660.0}, {'field': 'amount', 'old_value': 59883.0, 'new_value': 62460.0}, {'field': 'count', 'old_value': 71, 'new_value': 75}, {'field': 'instoreAmount', 'old_value': 60382.0, 'new_value': 62959.0}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 75}]
2025-05-19 08:07:25,168 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-19 08:07:25,169 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13585.0, 'new_value': 14751.0}, {'field': 'amount', 'old_value': 13585.0, 'new_value': 14751.0}, {'field': 'count', 'old_value': 22, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 13585.0, 'new_value': 14751.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 23}]
2025-05-19 08:07:25,611 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-19 08:07:25,611 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15992.53, 'new_value': 18671.77}, {'field': 'amount', 'old_value': 15991.83, 'new_value': 18671.07}, {'field': 'count', 'old_value': 65, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 15992.53, 'new_value': 18671.77}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 72}]
2025-05-19 08:07:26,080 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-19 08:07:26,080 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34063.0, 'new_value': 37162.0}, {'field': 'dailyBillAmount', 'old_value': 34063.0, 'new_value': 37162.0}, {'field': 'amount', 'old_value': 34089.0, 'new_value': 37361.0}, {'field': 'count', 'old_value': 79, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 34539.0, 'new_value': 38607.0}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 89}]
2025-05-19 08:07:26,489 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-19 08:07:26,489 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 261244.53, 'new_value': 288942.02999999997}, {'field': 'dailyBillAmount', 'old_value': 244791.82, 'new_value': 270020.52}, {'field': 'amount', 'old_value': 259468.0, 'new_value': 287164.52}, {'field': 'count', 'old_value': 507, 'new_value': 561}, {'field': 'instoreAmount', 'old_value': 261500.12, 'new_value': 289197.62}, {'field': 'instoreCount', 'old_value': 507, 'new_value': 561}]
2025-05-19 08:07:26,892 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-19 08:07:26,892 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52137.0, 'new_value': 55627.0}, {'field': 'amount', 'old_value': 52137.0, 'new_value': 55627.0}, {'field': 'count', 'old_value': 234, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 52891.0, 'new_value': 56381.0}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 251}]
2025-05-19 08:07:27,337 - INFO - 更新表单数据成功: FINST-VRA66VA1RMZU72KJ6T3JJ8YBFM4G3QAM6RBAM032
2025-05-19 08:07:27,337 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19458.0, 'new_value': 29979.0}, {'field': 'amount', 'old_value': 19458.0, 'new_value': 29979.0}, {'field': 'count', 'old_value': 5, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 19458.0, 'new_value': 29979.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 8}]
2025-05-19 08:07:27,810 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-19 08:07:27,810 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33062.6, 'new_value': 46100.979999999996}, {'field': 'dailyBillAmount', 'old_value': 33062.6, 'new_value': 46100.979999999996}, {'field': 'amount', 'old_value': 36325.119999999995, 'new_value': 49363.5}, {'field': 'count', 'old_value': 214, 'new_value': 287}, {'field': 'instoreAmount', 'old_value': 36325.119999999995, 'new_value': 49363.5}, {'field': 'instoreCount', 'old_value': 214, 'new_value': 287}]
2025-05-19 08:07:28,314 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-19 08:07:28,315 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 25947.46, 'new_value': 26577.57}, {'field': 'count', 'old_value': 2511, 'new_value': 2566}, {'field': 'instoreAmount', 'old_value': 27719.42, 'new_value': 28379.02}, {'field': 'instoreCount', 'old_value': 2511, 'new_value': 2566}]
2025-05-19 08:07:28,749 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-19 08:07:28,749 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 436567.91, 'new_value': 470492.19}, {'field': 'dailyBillAmount', 'old_value': 436567.91, 'new_value': 470492.19}, {'field': 'amount', 'old_value': 450928.35, 'new_value': 486106.04}, {'field': 'count', 'old_value': 4132, 'new_value': 4443}, {'field': 'instoreAmount', 'old_value': 347102.60000000003, 'new_value': 373560.21}, {'field': 'instoreCount', 'old_value': 1660, 'new_value': 1795}, {'field': 'onlineAmount', 'old_value': 107615.78, 'new_value': 116486.45999999999}, {'field': 'onlineCount', 'old_value': 2472, 'new_value': 2648}]
2025-05-19 08:07:29,277 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-19 08:07:29,277 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 135345.96, 'new_value': 148207.56}, {'field': 'amount', 'old_value': 135345.96, 'new_value': 148207.56}, {'field': 'count', 'old_value': 899, 'new_value': 986}, {'field': 'instoreAmount', 'old_value': 135454.96, 'new_value': 148316.56}, {'field': 'instoreCount', 'old_value': 899, 'new_value': 986}]
2025-05-19 08:07:29,615 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-19 08:07:29,615 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65703.72, 'new_value': 70682.06999999999}, {'field': 'dailyBillAmount', 'old_value': 65703.72, 'new_value': 70682.06999999999}, {'field': 'amount', 'old_value': 76913.83, 'new_value': 82353.45}, {'field': 'count', 'old_value': 3430, 'new_value': 3749}, {'field': 'instoreAmount', 'old_value': 40046.19, 'new_value': 43342.51}, {'field': 'instoreCount', 'old_value': 2030, 'new_value': 2232}, {'field': 'onlineAmount', 'old_value': 37526.36, 'new_value': 39790.66}, {'field': 'onlineCount', 'old_value': 1400, 'new_value': 1517}]
2025-05-19 08:07:30,173 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-19 08:07:30,174 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1911.0, 'new_value': 2040.0}, {'field': 'amount', 'old_value': 1911.0, 'new_value': 2040.0}, {'field': 'count', 'old_value': 100, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 1911.0, 'new_value': 2040.0}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 109}]
2025-05-19 08:07:30,609 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-19 08:07:30,610 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37914.0, 'new_value': 37974.0}, {'field': 'amount', 'old_value': 37914.0, 'new_value': 37974.0}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 37914.0, 'new_value': 37974.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-05-19 08:07:30,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-19 08:07:30,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90446.95999999999, 'new_value': 96496.56}, {'field': 'dailyBillAmount', 'old_value': 90446.95999999999, 'new_value': 96496.56}, {'field': 'amount', 'old_value': 42609.41, 'new_value': 45723.16}, {'field': 'count', 'old_value': 2702, 'new_value': 2909}, {'field': 'instoreAmount', 'old_value': 6576.4, 'new_value': 7045.3}, {'field': 'instoreCount', 'old_value': 268, 'new_value': 287}, {'field': 'onlineAmount', 'old_value': 36033.01, 'new_value': 38677.86}, {'field': 'onlineCount', 'old_value': 2434, 'new_value': 2622}]
2025-05-19 08:07:31,514 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-19 08:07:31,514 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 238788.32, 'new_value': 258700.3}, {'field': 'dailyBillAmount', 'old_value': 238788.32, 'new_value': 258700.3}, {'field': 'amount', 'old_value': 223826.46, 'new_value': 242218.78}, {'field': 'count', 'old_value': 1892, 'new_value': 2058}, {'field': 'instoreAmount', 'old_value': 165583.95, 'new_value': 178843.05}, {'field': 'instoreCount', 'old_value': 799, 'new_value': 860}, {'field': 'onlineAmount', 'old_value': 58243.51, 'new_value': 63376.729999999996}, {'field': 'onlineCount', 'old_value': 1093, 'new_value': 1198}]
2025-05-19 08:07:31,986 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-19 08:07:31,987 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 276930.98, 'new_value': 300941.0}, {'field': 'dailyBillAmount', 'old_value': 276930.98, 'new_value': 300941.0}, {'field': 'amount', 'old_value': 283919.21, 'new_value': 308237.51}, {'field': 'count', 'old_value': 1698, 'new_value': 1838}, {'field': 'instoreAmount', 'old_value': 258801.11000000002, 'new_value': 282427.71}, {'field': 'instoreCount', 'old_value': 1432, 'new_value': 1561}, {'field': 'onlineAmount', 'old_value': 29942.3, 'new_value': 30790.0}, {'field': 'onlineCount', 'old_value': 266, 'new_value': 277}]
2025-05-19 08:07:32,396 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-19 08:07:32,397 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 709107.96, 'new_value': 765690.55}, {'field': 'dailyBillAmount', 'old_value': 709107.96, 'new_value': 765690.55}, {'field': 'amount', 'old_value': 780191.37, 'new_value': 843190.43}, {'field': 'count', 'old_value': 4093, 'new_value': 4450}, {'field': 'instoreAmount', 'old_value': 591540.49, 'new_value': 639878.44}, {'field': 'instoreCount', 'old_value': 2324, 'new_value': 2526}, {'field': 'onlineAmount', 'old_value': 194300.84, 'new_value': 209381.24}, {'field': 'onlineCount', 'old_value': 1769, 'new_value': 1924}]
2025-05-19 08:07:32,806 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-19 08:07:32,806 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 214219.52, 'new_value': 237369.49}, {'field': 'dailyBillAmount', 'old_value': 214219.52, 'new_value': 237369.49}, {'field': 'amount', 'old_value': 302070.91, 'new_value': 336119.91}, {'field': 'count', 'old_value': 1447, 'new_value': 1588}, {'field': 'instoreAmount', 'old_value': 283759.12, 'new_value': 316094.62}, {'field': 'instoreCount', 'old_value': 1156, 'new_value': 1277}, {'field': 'onlineAmount', 'old_value': 18516.59, 'new_value': 20281.79}, {'field': 'onlineCount', 'old_value': 291, 'new_value': 311}]
2025-05-19 08:07:33,267 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-19 08:07:33,267 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 277380.53, 'new_value': 297985.24}, {'field': 'dailyBillAmount', 'old_value': 277380.53, 'new_value': 297985.24}, {'field': 'amount', 'old_value': 262311.2, 'new_value': 281960.6}, {'field': 'count', 'old_value': 1147, 'new_value': 1230}, {'field': 'instoreAmount', 'old_value': 266542.9, 'new_value': 286475.3}, {'field': 'instoreCount', 'old_value': 1147, 'new_value': 1230}]
2025-05-19 08:07:33,685 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-19 08:07:33,685 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 565455.44, 'new_value': 611076.72}, {'field': 'amount', 'old_value': 565455.44, 'new_value': 611076.72}, {'field': 'count', 'old_value': 4398, 'new_value': 4725}, {'field': 'instoreAmount', 'old_value': 565455.44, 'new_value': 611076.72}, {'field': 'instoreCount', 'old_value': 4398, 'new_value': 4725}]
2025-05-19 08:07:34,126 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-19 08:07:34,126 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 429109.37, 'new_value': 468019.19}, {'field': 'dailyBillAmount', 'old_value': 429109.37, 'new_value': 468019.19}, {'field': 'amount', 'old_value': 547067.29, 'new_value': 590801.83}, {'field': 'count', 'old_value': 3751, 'new_value': 4033}, {'field': 'instoreAmount', 'old_value': 302759.0, 'new_value': 332027.7}, {'field': 'instoreCount', 'old_value': 1567, 'new_value': 1718}, {'field': 'onlineAmount', 'old_value': 251708.3, 'new_value': 266785.8}, {'field': 'onlineCount', 'old_value': 2184, 'new_value': 2315}]
2025-05-19 08:07:34,598 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-19 08:07:34,599 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 278757.77, 'new_value': 307305.05}, {'field': 'dailyBillAmount', 'old_value': 278757.77, 'new_value': 307305.05}, {'field': 'amount', 'old_value': 349186.39, 'new_value': 379730.35}, {'field': 'count', 'old_value': 3695, 'new_value': 3994}, {'field': 'instoreAmount', 'old_value': 246187.62, 'new_value': 270281.12}, {'field': 'instoreCount', 'old_value': 1650, 'new_value': 1801}, {'field': 'onlineAmount', 'old_value': 104188.24, 'new_value': 110745.09}, {'field': 'onlineCount', 'old_value': 2045, 'new_value': 2193}]
2025-05-19 08:07:35,024 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-19 08:07:35,024 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 357277.44, 'new_value': 387868.08}, {'field': 'dailyBillAmount', 'old_value': 357277.44, 'new_value': 387868.08}, {'field': 'amount', 'old_value': 362341.68, 'new_value': 393128.66}, {'field': 'count', 'old_value': 3398, 'new_value': 3644}, {'field': 'instoreAmount', 'old_value': 316296.46, 'new_value': 344473.76}, {'field': 'instoreCount', 'old_value': 1739, 'new_value': 1906}, {'field': 'onlineAmount', 'old_value': 46931.68, 'new_value': 49571.56}, {'field': 'onlineCount', 'old_value': 1659, 'new_value': 1738}]
2025-05-19 08:07:35,503 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-19 08:07:35,503 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96473.8, 'new_value': 102910.8}, {'field': 'amount', 'old_value': 96473.3, 'new_value': 102910.3}, {'field': 'count', 'old_value': 427, 'new_value': 455}, {'field': 'instoreAmount', 'old_value': 96473.8, 'new_value': 102910.8}, {'field': 'instoreCount', 'old_value': 427, 'new_value': 455}]
2025-05-19 08:07:35,873 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-19 08:07:35,874 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 266858.09, 'new_value': 280524.29}, {'field': 'dailyBillAmount', 'old_value': 266858.09, 'new_value': 280524.29}, {'field': 'amount', 'old_value': -196654.08, 'new_value': -209428.18}, {'field': 'count', 'old_value': 707, 'new_value': 754}, {'field': 'instoreAmount', 'old_value': 5149.5, 'new_value': 5548.3}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 250}, {'field': 'onlineAmount', 'old_value': 14917.42, 'new_value': 15724.52}, {'field': 'onlineCount', 'old_value': 482, 'new_value': 504}]
2025-05-19 08:07:36,275 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-19 08:07:36,275 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 433990.39, 'new_value': 466266.27999999997}, {'field': 'dailyBillAmount', 'old_value': 433990.39, 'new_value': 466266.27999999997}, {'field': 'amount', 'old_value': 343219.54, 'new_value': 368510.49}, {'field': 'count', 'old_value': 1406, 'new_value': 1521}, {'field': 'instoreAmount', 'old_value': 343219.54, 'new_value': 368510.49}, {'field': 'instoreCount', 'old_value': 1406, 'new_value': 1521}]
2025-05-19 08:07:36,731 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-19 08:07:36,731 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'amount', 'old_value': 119199.0, 'new_value': 125856.2}, {'field': 'count', 'old_value': 500, 'new_value': 525}, {'field': 'instoreAmount', 'old_value': 124287.7, 'new_value': 130944.9}, {'field': 'instoreCount', 'old_value': 484, 'new_value': 509}]
2025-05-19 08:07:37,191 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-19 08:07:37,191 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 223936.54, 'new_value': 246498.52}, {'field': 'dailyBillAmount', 'old_value': 223936.54, 'new_value': 246498.52}, {'field': 'amount', 'old_value': 217829.82, 'new_value': 239269.84}, {'field': 'count', 'old_value': 1414, 'new_value': 1522}, {'field': 'instoreAmount', 'old_value': 205797.73, 'new_value': 226823.07}, {'field': 'instoreCount', 'old_value': 1103, 'new_value': 1202}, {'field': 'onlineAmount', 'old_value': 12174.85, 'new_value': 12589.53}, {'field': 'onlineCount', 'old_value': 311, 'new_value': 320}]
2025-05-19 08:07:37,596 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-19 08:07:37,596 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 237328.91999999998, 'new_value': 255975.03999999998}, {'field': 'dailyBillAmount', 'old_value': 237328.91999999998, 'new_value': 255975.03999999998}, {'field': 'amount', 'old_value': 104382.18, 'new_value': 112331.97}, {'field': 'count', 'old_value': 1612, 'new_value': 1749}, {'field': 'instoreAmount', 'old_value': 61672.15, 'new_value': 66065.69}, {'field': 'instoreCount', 'old_value': 448, 'new_value': 477}, {'field': 'onlineAmount', 'old_value': 42711.95, 'new_value': 46268.4}, {'field': 'onlineCount', 'old_value': 1164, 'new_value': 1272}]
2025-05-19 08:07:38,084 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-19 08:07:38,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 47154.0, 'new_value': 47850.0}, {'field': 'count', 'old_value': 26, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 47154.0, 'new_value': 47850.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-05-19 08:07:38,467 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-19 08:07:38,468 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95307.12, 'new_value': 100431.19}, {'field': 'amount', 'old_value': 95300.62, 'new_value': 100424.69}, {'field': 'count', 'old_value': 4263, 'new_value': 4532}, {'field': 'instoreAmount', 'old_value': 36218.39, 'new_value': 37928.28}, {'field': 'instoreCount', 'old_value': 1423, 'new_value': 1513}, {'field': 'onlineAmount', 'old_value': 63349.590000000004, 'new_value': 66911.77}, {'field': 'onlineCount', 'old_value': 2840, 'new_value': 3019}]
2025-05-19 08:07:38,879 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-19 08:07:38,879 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32657.9, 'new_value': 34261.9}, {'field': 'amount', 'old_value': 32657.9, 'new_value': 34261.9}, {'field': 'count', 'old_value': 149, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 32657.9, 'new_value': 34261.9}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 158}]
2025-05-19 08:07:39,267 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-19 08:07:39,268 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 284150.24, 'new_value': 305331.52999999997}, {'field': 'dailyBillAmount', 'old_value': 284150.24, 'new_value': 305331.52999999997}, {'field': 'amount', 'old_value': 111537.0, 'new_value': 119732.1}, {'field': 'count', 'old_value': 2076, 'new_value': 2231}, {'field': 'instoreAmount', 'old_value': 112538.8, 'new_value': 120855.9}, {'field': 'instoreCount', 'old_value': 2076, 'new_value': 2231}]
2025-05-19 08:07:39,748 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-19 08:07:39,749 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116725.28, 'new_value': 127759.76}, {'field': 'amount', 'old_value': 116725.28, 'new_value': 127759.76}, {'field': 'count', 'old_value': 2762, 'new_value': 3000}, {'field': 'instoreAmount', 'old_value': 116725.28, 'new_value': 127759.76}, {'field': 'instoreCount', 'old_value': 2762, 'new_value': 3000}]
2025-05-19 08:07:40,261 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-19 08:07:40,261 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19620.95, 'new_value': 21415.19}, {'field': 'amount', 'old_value': 19618.64, 'new_value': 21412.7}, {'field': 'count', 'old_value': 1145, 'new_value': 1262}, {'field': 'instoreAmount', 'old_value': 11136.59, 'new_value': 12168.59}, {'field': 'instoreCount', 'old_value': 563, 'new_value': 618}, {'field': 'onlineAmount', 'old_value': 8918.11, 'new_value': 9698.1}, {'field': 'onlineCount', 'old_value': 582, 'new_value': 644}]
2025-05-19 08:07:40,756 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-19 08:07:40,756 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31882.7, 'new_value': 37190.2}, {'field': 'amount', 'old_value': 31882.7, 'new_value': 37190.2}, {'field': 'count', 'old_value': 81, 'new_value': 96}, {'field': 'instoreAmount', 'old_value': 31882.7, 'new_value': 37190.2}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 96}]
2025-05-19 08:07:41,198 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M62
2025-05-19 08:07:41,198 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 73395.77, 'new_value': 72802.65}, {'field': 'amount', 'old_value': 73395.73, 'new_value': 72800.56}, {'field': 'count', 'old_value': 4432, 'new_value': 4404}, {'field': 'instoreAmount', 'old_value': 39418.96, 'new_value': 38825.84}, {'field': 'instoreCount', 'old_value': 2369, 'new_value': 2341}]
2025-05-19 08:07:41,627 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M82
2025-05-19 08:07:41,627 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 116601.08, 'new_value': 115360.8}, {'field': 'amount', 'old_value': 116601.08, 'new_value': 115359.17}, {'field': 'instoreAmount', 'old_value': 104293.62, 'new_value': 103053.34}]
2025-05-19 08:07:42,025 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG81
2025-05-19 08:07:42,025 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53897.26, 'new_value': 58717.76}, {'field': 'dailyBillAmount', 'old_value': 53897.26, 'new_value': 58717.76}, {'field': 'amount', 'old_value': 61064.23, 'new_value': 65667.33}, {'field': 'count', 'old_value': 2360, 'new_value': 2530}, {'field': 'instoreAmount', 'old_value': 61393.13, 'new_value': 65996.23}, {'field': 'instoreCount', 'old_value': 2360, 'new_value': 2530}]
2025-05-19 08:07:42,467 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-19 08:07:42,467 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 128782.67, 'new_value': 139817.71}, {'field': 'dailyBillAmount', 'old_value': 107919.1, 'new_value': 117925.1}, {'field': 'amount', 'old_value': 128781.99, 'new_value': 139817.03}, {'field': 'count', 'old_value': 1812, 'new_value': 1935}, {'field': 'instoreAmount', 'old_value': 124119.9, 'new_value': 134911.8}, {'field': 'instoreCount', 'old_value': 1592, 'new_value': 1702}, {'field': 'onlineAmount', 'old_value': 4871.89, 'new_value': 5115.03}, {'field': 'onlineCount', 'old_value': 220, 'new_value': 233}]
2025-05-19 08:07:42,899 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-19 08:07:42,899 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19847.32, 'new_value': 21653.78}, {'field': 'amount', 'old_value': 19846.52, 'new_value': 21652.98}, {'field': 'count', 'old_value': 843, 'new_value': 919}, {'field': 'instoreAmount', 'old_value': 16668.12, 'new_value': 18202.78}, {'field': 'instoreCount', 'old_value': 756, 'new_value': 823}, {'field': 'onlineAmount', 'old_value': 3219.4, 'new_value': 3491.2}, {'field': 'onlineCount', 'old_value': 87, 'new_value': 96}]
2025-05-19 08:07:43,334 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-19 08:07:43,334 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 272399.36, 'new_value': 293545.65}, {'field': 'dailyBillAmount', 'old_value': 272399.36, 'new_value': 293545.65}, {'field': 'amount', 'old_value': 348266.7, 'new_value': 373657.97}, {'field': 'count', 'old_value': 3550, 'new_value': 3807}, {'field': 'instoreAmount', 'old_value': 328953.64, 'new_value': 352350.98}, {'field': 'instoreCount', 'old_value': 2467, 'new_value': 2636}, {'field': 'onlineAmount', 'old_value': 27201.08, 'new_value': 29195.29}, {'field': 'onlineCount', 'old_value': 1083, 'new_value': 1171}]
2025-05-19 08:07:43,771 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-19 08:07:43,771 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109933.11, 'new_value': 117293.58}, {'field': 'dailyBillAmount', 'old_value': 109933.11, 'new_value': 117293.58}, {'field': 'amount', 'old_value': 26335.96, 'new_value': 28238.31}, {'field': 'count', 'old_value': 428, 'new_value': 456}, {'field': 'instoreAmount', 'old_value': 16363.73, 'new_value': 17741.69}, {'field': 'instoreCount', 'old_value': 221, 'new_value': 232}, {'field': 'onlineAmount', 'old_value': 10835.56, 'new_value': 11359.95}, {'field': 'onlineCount', 'old_value': 207, 'new_value': 224}]
2025-05-19 08:07:44,218 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-19 08:07:44,218 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 106095.1, 'new_value': 112607.64}, {'field': 'dailyBillAmount', 'old_value': 93504.01, 'new_value': 100130.38}, {'field': 'amount', 'old_value': 106093.64, 'new_value': 112605.76}, {'field': 'count', 'old_value': 6035, 'new_value': 6379}, {'field': 'instoreAmount', 'old_value': 66342.70999999999, 'new_value': 70791.59}, {'field': 'instoreCount', 'old_value': 3729, 'new_value': 3948}, {'field': 'onlineAmount', 'old_value': 41217.659999999996, 'new_value': 43358.01}, {'field': 'onlineCount', 'old_value': 2306, 'new_value': 2431}]
2025-05-19 08:07:44,691 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-19 08:07:44,691 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56522.409999999996, 'new_value': 59393.46}, {'field': 'amount', 'old_value': 56520.93, 'new_value': 59386.08}, {'field': 'count', 'old_value': 3459, 'new_value': 3665}, {'field': 'instoreAmount', 'old_value': 27261.54, 'new_value': 27850.94}, {'field': 'instoreCount', 'old_value': 1549, 'new_value': 1579}, {'field': 'onlineAmount', 'old_value': 30553.85, 'new_value': 32920.77}, {'field': 'onlineCount', 'old_value': 1910, 'new_value': 2086}]
2025-05-19 08:07:45,147 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-19 08:07:45,147 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13958.0, 'new_value': 16552.0}, {'field': 'dailyBillAmount', 'old_value': 13958.0, 'new_value': 16552.0}, {'field': 'amount', 'old_value': 107556.58, 'new_value': 117228.6}, {'field': 'count', 'old_value': 1059, 'new_value': 1145}, {'field': 'instoreAmount', 'old_value': 107671.08, 'new_value': 117343.48}, {'field': 'instoreCount', 'old_value': 1059, 'new_value': 1145}]
2025-05-19 08:07:45,596 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-19 08:07:45,597 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92051.56, 'new_value': 98574.5}, {'field': 'dailyBillAmount', 'old_value': 94911.01, 'new_value': 101826.79}, {'field': 'amount', 'old_value': 92050.68, 'new_value': 98568.69}, {'field': 'count', 'old_value': 1779, 'new_value': 1907}, {'field': 'instoreAmount', 'old_value': 88386.63, 'new_value': 94779.77}, {'field': 'instoreCount', 'old_value': 1507, 'new_value': 1622}, {'field': 'onlineAmount', 'old_value': 3737.29, 'new_value': 3880.17}, {'field': 'onlineCount', 'old_value': 272, 'new_value': 285}]
2025-05-19 08:07:46,102 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-19 08:07:46,102 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131252.46, 'new_value': 137489.57}, {'field': 'dailyBillAmount', 'old_value': 131252.46, 'new_value': 137489.57}, {'field': 'amount', 'old_value': 16875.65, 'new_value': 17934.59}, {'field': 'count', 'old_value': 662, 'new_value': 691}, {'field': 'instoreAmount', 'old_value': 19684.83, 'new_value': 20779.57}, {'field': 'instoreCount', 'old_value': 662, 'new_value': 691}]
2025-05-19 08:07:46,593 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-19 08:07:46,593 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 344291.45, 'new_value': 371679.53}, {'field': 'dailyBillAmount', 'old_value': 344291.45, 'new_value': 371679.53}, {'field': 'amount', 'old_value': 34456.56, 'new_value': 36398.16}, {'field': 'count', 'old_value': 168, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 34682.36, 'new_value': 36623.96}, {'field': 'instoreCount', 'old_value': 168, 'new_value': 180}]
2025-05-19 08:07:47,074 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-19 08:07:47,074 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 12047.88, 'new_value': 12515.63}, {'field': 'count', 'old_value': 620, 'new_value': 640}, {'field': 'onlineAmount', 'old_value': 12134.14, 'new_value': 12601.89}, {'field': 'onlineCount', 'old_value': 620, 'new_value': 640}]
2025-05-19 08:07:47,484 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-19 08:07:47,485 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 187514.56, 'new_value': 203200.81}, {'field': 'amount', 'old_value': 187360.78, 'new_value': 203047.03}, {'field': 'count', 'old_value': 1989, 'new_value': 2107}, {'field': 'instoreAmount', 'old_value': 177613.9, 'new_value': 192578.7}, {'field': 'instoreCount', 'old_value': 1681, 'new_value': 1783}, {'field': 'onlineAmount', 'old_value': 12216.1, 'new_value': 13145.550000000001}, {'field': 'onlineCount', 'old_value': 308, 'new_value': 324}]
2025-05-19 08:07:47,961 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-19 08:07:47,962 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121853.68, 'new_value': 126963.94}, {'field': 'dailyBillAmount', 'old_value': 118053.20999999999, 'new_value': 123163.47}, {'field': 'amount', 'old_value': 97430.29, 'new_value': 100597.82}, {'field': 'count', 'old_value': 3549, 'new_value': 3666}, {'field': 'instoreAmount', 'old_value': 45576.78, 'new_value': 46678.14}, {'field': 'instoreCount', 'old_value': 1615, 'new_value': 1643}, {'field': 'onlineAmount', 'old_value': 52964.55, 'new_value': 55048.82}, {'field': 'onlineCount', 'old_value': 1934, 'new_value': 2023}]
2025-05-19 08:07:48,457 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-19 08:07:48,457 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2057.25, 'new_value': 2412.94}, {'field': 'count', 'old_value': 94, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 2057.25, 'new_value': 2412.94}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 109}]
2025-05-19 08:07:48,861 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-19 08:07:48,862 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 4257.09, 'new_value': 4471.08}, {'field': 'count', 'old_value': 184, 'new_value': 190}, {'field': 'onlineAmount', 'old_value': 4257.09, 'new_value': 4471.08}, {'field': 'onlineCount', 'old_value': 184, 'new_value': 190}]
2025-05-19 08:07:49,185 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-19 08:07:49,185 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74051.56, 'new_value': 78563.47}, {'field': 'dailyBillAmount', 'old_value': 38614.72, 'new_value': 40768.82}, {'field': 'amount', 'old_value': 74050.97, 'new_value': 78562.88}, {'field': 'count', 'old_value': 1815, 'new_value': 1931}, {'field': 'instoreAmount', 'old_value': 40891.75, 'new_value': 43167.85}, {'field': 'instoreCount', 'old_value': 986, 'new_value': 1041}, {'field': 'onlineAmount', 'old_value': 35001.45, 'new_value': 37361.46}, {'field': 'onlineCount', 'old_value': 829, 'new_value': 890}]
2025-05-19 08:07:49,540 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-19 08:07:49,540 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26282.53, 'new_value': 28424.93}, {'field': 'amount', 'old_value': 26282.53, 'new_value': 28424.93}, {'field': 'count', 'old_value': 977, 'new_value': 1054}, {'field': 'instoreAmount', 'old_value': 26647.98, 'new_value': 28790.38}, {'field': 'instoreCount', 'old_value': 977, 'new_value': 1054}]
2025-05-19 08:07:49,986 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-19 08:07:49,986 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35296.3, 'new_value': 37065.28}, {'field': 'dailyBillAmount', 'old_value': 35296.3, 'new_value': 37065.28}, {'field': 'amount', 'old_value': 29667.68, 'new_value': 30656.41}, {'field': 'count', 'old_value': 1291, 'new_value': 1352}, {'field': 'instoreAmount', 'old_value': 17981.81, 'new_value': 18349.61}, {'field': 'instoreCount', 'old_value': 610, 'new_value': 627}, {'field': 'onlineAmount', 'old_value': 11710.98, 'new_value': 12351.28}, {'field': 'onlineCount', 'old_value': 681, 'new_value': 725}]
2025-05-19 08:07:50,418 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-19 08:07:50,418 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60367.05, 'new_value': 63301.15}, {'field': 'amount', 'old_value': 60367.05, 'new_value': 63301.15}, {'field': 'count', 'old_value': 1775, 'new_value': 1859}, {'field': 'instoreAmount', 'old_value': 23969.89, 'new_value': 24958.0}, {'field': 'instoreCount', 'old_value': 849, 'new_value': 887}, {'field': 'onlineAmount', 'old_value': 36430.16, 'new_value': 38376.15}, {'field': 'onlineCount', 'old_value': 926, 'new_value': 972}]
2025-05-19 08:07:50,874 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-19 08:07:50,874 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35780.26, 'new_value': 37658.75}, {'field': 'amount', 'old_value': 35779.36, 'new_value': 37657.85}, {'field': 'count', 'old_value': 831, 'new_value': 875}, {'field': 'instoreAmount', 'old_value': 28728.2, 'new_value': 30037.9}, {'field': 'instoreCount', 'old_value': 673, 'new_value': 708}, {'field': 'onlineAmount', 'old_value': 7384.59, 'new_value': 7953.38}, {'field': 'onlineCount', 'old_value': 158, 'new_value': 167}]
2025-05-19 08:07:51,345 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-19 08:07:51,345 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 166698.13, 'new_value': 174534.0}, {'field': 'dailyBillAmount', 'old_value': 166698.13, 'new_value': 174534.0}, {'field': 'amount', 'old_value': 109594.88, 'new_value': 114983.37}, {'field': 'count', 'old_value': 2722, 'new_value': 2855}, {'field': 'instoreAmount', 'old_value': 70167.42, 'new_value': 73175.12}, {'field': 'instoreCount', 'old_value': 1375, 'new_value': 1436}, {'field': 'onlineAmount', 'old_value': 48091.24, 'new_value': 50786.93}, {'field': 'onlineCount', 'old_value': 1347, 'new_value': 1419}]
2025-05-19 08:07:51,853 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-19 08:07:51,853 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 506987.32, 'new_value': 542822.07}, {'field': 'dailyBillAmount', 'old_value': 506987.32, 'new_value': 542822.07}, {'field': 'amount', 'old_value': 472309.7, 'new_value': 502402.0}, {'field': 'count', 'old_value': 2780, 'new_value': 2954}, {'field': 'instoreAmount', 'old_value': 344951.0, 'new_value': 362599.0}, {'field': 'instoreCount', 'old_value': 2189, 'new_value': 2305}, {'field': 'onlineAmount', 'old_value': 127360.8, 'new_value': 139805.1}, {'field': 'onlineCount', 'old_value': 591, 'new_value': 649}]
2025-05-19 08:07:52,249 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-19 08:07:52,249 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 740187.73, 'new_value': 781493.03}, {'field': 'amount', 'old_value': 740187.23, 'new_value': 781492.53}, {'field': 'count', 'old_value': 2565, 'new_value': 2737}, {'field': 'instoreAmount', 'old_value': 740187.73, 'new_value': 781493.03}, {'field': 'instoreCount', 'old_value': 2565, 'new_value': 2737}]
2025-05-19 08:07:52,674 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-19 08:07:52,675 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 430353.46, 'new_value': 451049.72}, {'field': 'dailyBillAmount', 'old_value': 383906.55, 'new_value': 402167.37}, {'field': 'amount', 'old_value': 430353.46, 'new_value': 451049.72}, {'field': 'count', 'old_value': 2597, 'new_value': 2720}, {'field': 'instoreAmount', 'old_value': 392691.39, 'new_value': 412285.73}, {'field': 'instoreCount', 'old_value': 1670, 'new_value': 1762}, {'field': 'onlineAmount', 'old_value': 37878.72, 'new_value': 38980.64}, {'field': 'onlineCount', 'old_value': 927, 'new_value': 958}]
2025-05-19 08:07:53,074 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-19 08:07:53,074 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 440372.85000000003, 'new_value': 467415.19}, {'field': 'dailyBillAmount', 'old_value': 419455.03, 'new_value': 444261.37}, {'field': 'amount', 'old_value': 440372.85000000003, 'new_value': 467415.19}, {'field': 'count', 'old_value': 1007, 'new_value': 1081}, {'field': 'instoreAmount', 'old_value': 413946.6, 'new_value': 438737.2}, {'field': 'instoreCount', 'old_value': 784, 'new_value': 838}, {'field': 'onlineAmount', 'old_value': 26553.53, 'new_value': 28805.27}, {'field': 'onlineCount', 'old_value': 223, 'new_value': 243}]
2025-05-19 08:07:53,587 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-19 08:07:53,587 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 500290.26, 'new_value': 525969.82}, {'field': 'amount', 'old_value': 500290.26, 'new_value': 525969.14}, {'field': 'count', 'old_value': 2480, 'new_value': 2645}, {'field': 'instoreAmount', 'old_value': 474571.79, 'new_value': 497887.79}, {'field': 'instoreCount', 'old_value': 1746, 'new_value': 1845}, {'field': 'onlineAmount', 'old_value': 25764.04, 'new_value': 28127.600000000002}, {'field': 'onlineCount', 'old_value': 734, 'new_value': 800}]
2025-05-19 08:07:53,928 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-19 08:07:53,929 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 588964.16, 'new_value': 624870.43}, {'field': 'dailyBillAmount', 'old_value': 588964.16, 'new_value': 624870.43}, {'field': 'amount', 'old_value': 532699.07, 'new_value': 561750.37}, {'field': 'count', 'old_value': 2612, 'new_value': 2776}, {'field': 'instoreAmount', 'old_value': 489218.45999999996, 'new_value': 514842.86}, {'field': 'instoreCount', 'old_value': 2179, 'new_value': 2312}, {'field': 'onlineAmount', 'old_value': 43797.89, 'new_value': 47238.52}, {'field': 'onlineCount', 'old_value': 433, 'new_value': 464}]
2025-05-19 08:07:54,333 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-19 08:07:54,334 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124672.24, 'new_value': 136451.24}, {'field': 'dailyBillAmount', 'old_value': 123265.69, 'new_value': 135044.69}, {'field': 'amount', 'old_value': 122395.66, 'new_value': 134092.66}, {'field': 'count', 'old_value': 200, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 122395.66, 'new_value': 134092.66}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 209}]
2025-05-19 08:07:54,737 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-19 08:07:54,737 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 112156.46, 'new_value': 118505.21}, {'field': 'dailyBillAmount', 'old_value': 112156.46, 'new_value': 118505.21}, {'field': 'amount', 'old_value': 96047.12, 'new_value': 102102.13}, {'field': 'count', 'old_value': 167, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 93792.0, 'new_value': 99702.0}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 166}, {'field': 'onlineAmount', 'old_value': 2255.83, 'new_value': 2400.84}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 14}]
2025-05-19 08:07:55,203 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-19 08:07:55,204 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16496.14, 'new_value': 17863.05}, {'field': 'amount', 'old_value': 16496.14, 'new_value': 17863.05}, {'field': 'count', 'old_value': 328, 'new_value': 358}, {'field': 'instoreAmount', 'old_value': 16496.14, 'new_value': 17863.05}, {'field': 'instoreCount', 'old_value': 328, 'new_value': 358}]
2025-05-19 08:07:55,602 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-19 08:07:55,603 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66787.63, 'new_value': 71358.59}, {'field': 'amount', 'old_value': 66787.63, 'new_value': 71358.59}, {'field': 'count', 'old_value': 556, 'new_value': 595}, {'field': 'instoreAmount', 'old_value': 66972.19, 'new_value': 71543.15}, {'field': 'instoreCount', 'old_value': 556, 'new_value': 595}]
2025-05-19 08:07:56,045 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-19 08:07:56,046 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 207356.06, 'new_value': 223370.12}, {'field': 'dailyBillAmount', 'old_value': 207356.06, 'new_value': 223370.12}, {'field': 'amount', 'old_value': 226494.97, 'new_value': 241657.11000000002}, {'field': 'count', 'old_value': 5969, 'new_value': 6397}, {'field': 'instoreAmount', 'old_value': 215043.95, 'new_value': 230087.25}, {'field': 'instoreCount', 'old_value': 5405, 'new_value': 5816}, {'field': 'onlineAmount', 'old_value': 15059.31, 'new_value': 15516.05}, {'field': 'onlineCount', 'old_value': 564, 'new_value': 581}]
2025-05-19 08:07:56,535 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-19 08:07:56,535 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59089.38, 'new_value': 59762.42}, {'field': 'dailyBillAmount', 'old_value': 59089.38, 'new_value': 59762.42}, {'field': 'amount', 'old_value': 59688.38, 'new_value': 60390.42}, {'field': 'count', 'old_value': 55, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 59688.38, 'new_value': 60390.42}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 57}]
2025-05-19 08:07:56,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-19 08:07:56,978 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 569009.49, 'new_value': 587046.91}, {'field': 'dailyBillAmount', 'old_value': 569009.49, 'new_value': 587046.91}, {'field': 'amount', 'old_value': 516679.35, 'new_value': 534838.18}, {'field': 'count', 'old_value': 1321, 'new_value': 1382}, {'field': 'instoreAmount', 'old_value': 538538.21, 'new_value': 556575.63}, {'field': 'instoreCount', 'old_value': 1100, 'new_value': 1156}, {'field': 'onlineAmount', 'old_value': 4942.69, 'new_value': 5064.1}, {'field': 'onlineCount', 'old_value': 221, 'new_value': 226}]
2025-05-19 08:07:57,479 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-19 08:07:57,479 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 855007.7, 'new_value': 909995.96}, {'field': 'amount', 'old_value': 855007.7, 'new_value': 909995.96}, {'field': 'count', 'old_value': 2747, 'new_value': 2942}, {'field': 'instoreAmount', 'old_value': 856218.7, 'new_value': 911206.96}, {'field': 'instoreCount', 'old_value': 2747, 'new_value': 2942}]
2025-05-19 08:07:58,052 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-19 08:07:58,052 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 631847.61, 'new_value': 664833.51}, {'field': 'dailyBillAmount', 'old_value': 631847.61, 'new_value': 664833.51}, {'field': 'amount', 'old_value': 503132.76, 'new_value': 529009.18}, {'field': 'count', 'old_value': 1869, 'new_value': 1958}, {'field': 'instoreAmount', 'old_value': 490958.02, 'new_value': 515663.83}, {'field': 'instoreCount', 'old_value': 1114, 'new_value': 1173}, {'field': 'onlineAmount', 'old_value': 22779.72, 'new_value': 24022.52}, {'field': 'onlineCount', 'old_value': 755, 'new_value': 785}]
2025-05-19 08:07:58,503 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-19 08:07:58,503 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1278330.29, 'new_value': 1336786.29}, {'field': 'dailyBillAmount', 'old_value': 1278330.29, 'new_value': 1336786.29}, {'field': 'amount', 'old_value': 1322476.0, 'new_value': 1380991.0}, {'field': 'count', 'old_value': 3734, 'new_value': 3857}, {'field': 'instoreAmount', 'old_value': 1322476.0, 'new_value': 1380991.0}, {'field': 'instoreCount', 'old_value': 3734, 'new_value': 3857}]
2025-05-19 08:07:58,943 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-19 08:07:58,943 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 200861.26, 'new_value': 214753.96}, {'field': 'dailyBillAmount', 'old_value': 200861.26, 'new_value': 214753.96}, {'field': 'amount', 'old_value': 198864.13, 'new_value': 212206.83000000002}, {'field': 'count', 'old_value': 1077, 'new_value': 1145}, {'field': 'instoreAmount', 'old_value': 191702.8, 'new_value': 204952.8}, {'field': 'instoreCount', 'old_value': 901, 'new_value': 961}, {'field': 'onlineAmount', 'old_value': 10736.21, 'new_value': 11436.91}, {'field': 'onlineCount', 'old_value': 176, 'new_value': 184}]
2025-05-19 08:07:59,370 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-19 08:07:59,371 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 667285.03, 'new_value': 702627.11}, {'field': 'dailyBillAmount', 'old_value': 667285.03, 'new_value': 702627.11}, {'field': 'amount', 'old_value': 714215.56, 'new_value': 749693.73}, {'field': 'count', 'old_value': 2934, 'new_value': 3090}, {'field': 'instoreAmount', 'old_value': 714216.01, 'new_value': 749694.18}, {'field': 'instoreCount', 'old_value': 2934, 'new_value': 3090}]
2025-05-19 08:07:59,836 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-19 08:07:59,837 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 279810.04, 'new_value': 288540.04}, {'field': 'dailyBillAmount', 'old_value': 279810.04, 'new_value': 288540.04}, {'field': 'amount', 'old_value': 463334.3, 'new_value': 490993.4}, {'field': 'count', 'old_value': 778, 'new_value': 822}, {'field': 'instoreAmount', 'old_value': 460280.78, 'new_value': 487488.28}, {'field': 'instoreCount', 'old_value': 753, 'new_value': 792}, {'field': 'onlineAmount', 'old_value': 3319.0, 'new_value': 3770.6}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 30}]
2025-05-19 08:08:00,407 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-19 08:08:00,408 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172550.84, 'new_value': 189863.99}, {'field': 'dailyBillAmount', 'old_value': 172550.84, 'new_value': 189863.99}, {'field': 'amount', 'old_value': 203485.3, 'new_value': 221500.3}, {'field': 'count', 'old_value': 1416, 'new_value': 1552}, {'field': 'instoreAmount', 'old_value': 206691.3, 'new_value': 224845.3}, {'field': 'instoreCount', 'old_value': 1416, 'new_value': 1552}]
2025-05-19 08:08:00,871 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-19 08:08:00,871 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 100805.79, 'new_value': 111135.82}, {'field': 'dailyBillAmount', 'old_value': 100805.79, 'new_value': 111135.82}, {'field': 'amount', 'old_value': 76414.45999999999, 'new_value': 86490.95999999999}, {'field': 'count', 'old_value': 492, 'new_value': 561}, {'field': 'instoreAmount', 'old_value': 75561.0, 'new_value': 85674.0}, {'field': 'instoreCount', 'old_value': 448, 'new_value': 516}, {'field': 'onlineAmount', 'old_value': 2036.46, 'new_value': 2136.96}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 45}]
2025-05-19 08:08:01,286 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-19 08:08:01,287 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28202.8, 'new_value': 31652.8}, {'field': 'dailyBillAmount', 'old_value': 28202.8, 'new_value': 31652.8}]
2025-05-19 08:08:01,731 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-19 08:08:01,732 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 107333.76, 'new_value': 114558.38}, {'field': 'count', 'old_value': 5099, 'new_value': 5428}, {'field': 'instoreAmount', 'old_value': 57653.31, 'new_value': 61657.33}, {'field': 'instoreCount', 'old_value': 2947, 'new_value': 3133}, {'field': 'onlineAmount', 'old_value': 52825.13, 'new_value': 56130.63}, {'field': 'onlineCount', 'old_value': 2152, 'new_value': 2295}]
2025-05-19 08:08:02,222 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-19 08:08:02,222 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146413.15, 'new_value': 158033.02}, {'field': 'amount', 'old_value': 146407.84, 'new_value': 158027.34}, {'field': 'count', 'old_value': 2702, 'new_value': 2896}, {'field': 'instoreAmount', 'old_value': 139374.61, 'new_value': 149856.95}, {'field': 'instoreCount', 'old_value': 2563, 'new_value': 2740}, {'field': 'onlineAmount', 'old_value': 7038.54, 'new_value': 8176.07}, {'field': 'onlineCount', 'old_value': 139, 'new_value': 156}]
2025-05-19 08:08:02,689 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-19 08:08:02,690 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20602.2, 'new_value': 22013.6}, {'field': 'amount', 'old_value': 20602.2, 'new_value': 22013.6}, {'field': 'count', 'old_value': 146, 'new_value': 159}, {'field': 'instoreAmount', 'old_value': 20602.2, 'new_value': 22013.6}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 159}]
2025-05-19 08:08:03,170 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-19 08:08:03,171 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19304.2, 'new_value': 28682.2}, {'field': 'dailyBillAmount', 'old_value': 19304.2, 'new_value': 28682.2}, {'field': 'amount', 'old_value': 37154.6, 'new_value': 39537.6}, {'field': 'count', 'old_value': 331, 'new_value': 355}, {'field': 'instoreAmount', 'old_value': 37374.8, 'new_value': 39757.8}, {'field': 'instoreCount', 'old_value': 331, 'new_value': 355}]
2025-05-19 08:08:03,647 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-19 08:08:03,647 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38606.0, 'new_value': 40826.0}, {'field': 'dailyBillAmount', 'old_value': 38606.0, 'new_value': 40826.0}]
2025-05-19 08:08:04,029 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-19 08:08:04,029 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 107105.3, 'new_value': 129132.3}, {'field': 'dailyBillAmount', 'old_value': 107105.3, 'new_value': 129132.3}, {'field': 'amount', 'old_value': 115069.93, 'new_value': 125669.92}, {'field': 'count', 'old_value': 3475, 'new_value': 3709}, {'field': 'instoreAmount', 'old_value': 111747.81, 'new_value': 121944.3}, {'field': 'instoreCount', 'old_value': 3337, 'new_value': 3562}, {'field': 'onlineAmount', 'old_value': 5166.22, 'new_value': 5616.12}, {'field': 'onlineCount', 'old_value': 138, 'new_value': 147}]
2025-05-19 08:08:04,461 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-19 08:08:04,462 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33312.5, 'new_value': 38422.1}, {'field': 'dailyBillAmount', 'old_value': 33312.5, 'new_value': 38422.1}, {'field': 'amount', 'old_value': 33070.1, 'new_value': 38227.6}, {'field': 'count', 'old_value': 189, 'new_value': 215}, {'field': 'instoreAmount', 'old_value': 34855.3, 'new_value': 40604.3}, {'field': 'instoreCount', 'old_value': 189, 'new_value': 214}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 97.9}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-05-19 08:08:04,898 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-19 08:08:04,898 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46802.58, 'new_value': 48786.13}, {'field': 'dailyBillAmount', 'old_value': 46802.58, 'new_value': 48786.13}]
2025-05-19 08:08:05,396 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-19 08:08:05,397 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33527.57, 'new_value': 34951.36}, {'field': 'amount', 'old_value': 33527.57, 'new_value': 34951.12}, {'field': 'count', 'old_value': 1921, 'new_value': 2009}, {'field': 'instoreAmount', 'old_value': 34112.93, 'new_value': 35568.62}, {'field': 'instoreCount', 'old_value': 1921, 'new_value': 2009}]
2025-05-19 08:08:05,829 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-19 08:08:05,830 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53571.78, 'new_value': 56262.46}, {'field': 'dailyBillAmount', 'old_value': 53571.78, 'new_value': 56262.46}, {'field': 'amount', 'old_value': 55196.98, 'new_value': 58035.81}, {'field': 'count', 'old_value': 2678, 'new_value': 2814}, {'field': 'instoreAmount', 'old_value': 51392.6, 'new_value': 53961.5}, {'field': 'instoreCount', 'old_value': 2517, 'new_value': 2643}, {'field': 'onlineAmount', 'old_value': 3869.63, 'new_value': 4139.56}, {'field': 'onlineCount', 'old_value': 161, 'new_value': 171}]
2025-05-19 08:08:06,305 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-19 08:08:06,306 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37591.89, 'new_value': 40016.01}, {'field': 'amount', 'old_value': 37591.89, 'new_value': 40016.01}, {'field': 'count', 'old_value': 1811, 'new_value': 1930}, {'field': 'instoreAmount', 'old_value': 23737.89, 'new_value': 25268.13}, {'field': 'instoreCount', 'old_value': 1212, 'new_value': 1289}, {'field': 'onlineAmount', 'old_value': 13915.0, 'new_value': 14808.88}, {'field': 'onlineCount', 'old_value': 599, 'new_value': 641}]
2025-05-19 08:08:06,772 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-19 08:08:06,773 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26520.54, 'new_value': 28231.62}, {'field': 'dailyBillAmount', 'old_value': 26520.54, 'new_value': 28231.62}, {'field': 'amount', 'old_value': 18915.79, 'new_value': 20050.33}, {'field': 'count', 'old_value': 763, 'new_value': 811}, {'field': 'instoreAmount', 'old_value': 19105.19, 'new_value': 20239.73}, {'field': 'instoreCount', 'old_value': 763, 'new_value': 811}]
2025-05-19 08:08:07,247 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-19 08:08:07,248 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49557.43, 'new_value': 52676.46}, {'field': 'amount', 'old_value': 49552.42, 'new_value': 52671.24}, {'field': 'count', 'old_value': 3032, 'new_value': 3210}, {'field': 'instoreAmount', 'old_value': 12695.95, 'new_value': 13638.51}, {'field': 'instoreCount', 'old_value': 788, 'new_value': 861}, {'field': 'onlineAmount', 'old_value': 37903.479999999996, 'new_value': 40192.75}, {'field': 'onlineCount', 'old_value': 2244, 'new_value': 2349}]
2025-05-19 08:08:07,698 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-19 08:08:07,699 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88112.51, 'new_value': 92885.47}, {'field': 'dailyBillAmount', 'old_value': 88112.51, 'new_value': 92885.47}, {'field': 'amount', 'old_value': 73579.54, 'new_value': 77356.04}, {'field': 'count', 'old_value': 730, 'new_value': 768}, {'field': 'instoreAmount', 'old_value': 73579.54, 'new_value': 77356.04}, {'field': 'instoreCount', 'old_value': 730, 'new_value': 768}]
2025-05-19 08:08:08,140 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-19 08:08:08,141 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72634.49, 'new_value': 79222.49}, {'field': 'dailyBillAmount', 'old_value': 72634.49, 'new_value': 79222.49}, {'field': 'amount', 'old_value': 80624.8, 'new_value': 88033.8}, {'field': 'count', 'old_value': 342, 'new_value': 378}, {'field': 'instoreAmount', 'old_value': 80624.8, 'new_value': 88033.8}, {'field': 'instoreCount', 'old_value': 342, 'new_value': 378}]
2025-05-19 08:08:08,578 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-19 08:08:08,578 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49463.7, 'new_value': 51929.7}, {'field': 'dailyBillAmount', 'old_value': 49463.7, 'new_value': 51929.7}, {'field': 'amount', 'old_value': 42941.65, 'new_value': 44327.65}, {'field': 'count', 'old_value': 226, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 44378.65, 'new_value': 45764.65}, {'field': 'instoreCount', 'old_value': 226, 'new_value': 234}]
2025-05-19 08:08:09,008 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-19 08:08:09,008 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92117.0, 'new_value': 96631.0}, {'field': 'amount', 'old_value': 92117.0, 'new_value': 96631.0}, {'field': 'count', 'old_value': 961, 'new_value': 1005}, {'field': 'instoreAmount', 'old_value': 92117.0, 'new_value': 96631.0}, {'field': 'instoreCount', 'old_value': 961, 'new_value': 1005}]
2025-05-19 08:08:09,552 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-19 08:08:09,552 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19497.33, 'new_value': 20131.98}, {'field': 'dailyBillAmount', 'old_value': 19497.33, 'new_value': 20131.98}, {'field': 'amount', 'old_value': 2166.13, 'new_value': 2388.58}, {'field': 'count', 'old_value': 120, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 2578.81, 'new_value': 2801.2599999999998}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 130}]
2025-05-19 08:08:10,041 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-19 08:08:10,042 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16085.59, 'new_value': 16878.97}, {'field': 'dailyBillAmount', 'old_value': 16085.59, 'new_value': 16878.97}, {'field': 'amount', 'old_value': 16721.59, 'new_value': 17514.97}, {'field': 'count', 'old_value': 449, 'new_value': 467}, {'field': 'instoreAmount', 'old_value': 16763.73, 'new_value': 17557.11}, {'field': 'instoreCount', 'old_value': 448, 'new_value': 466}]
2025-05-19 08:08:10,459 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-19 08:08:10,460 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35410.1, 'new_value': 37997.4}, {'field': 'dailyBillAmount', 'old_value': 35410.1, 'new_value': 37997.4}, {'field': 'amount', 'old_value': 48096.6, 'new_value': 51564.6}, {'field': 'count', 'old_value': 190, 'new_value': 203}, {'field': 'instoreAmount', 'old_value': 48348.6, 'new_value': 51816.6}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 203}]
2025-05-19 08:08:10,918 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-19 08:08:10,919 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27445.0, 'new_value': 29688.0}, {'field': 'dailyBillAmount', 'old_value': 27445.0, 'new_value': 29688.0}, {'field': 'amount', 'old_value': 32134.0, 'new_value': 33163.0}, {'field': 'count', 'old_value': 164, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 32148.0, 'new_value': 33177.0}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 172}]
2025-05-19 08:08:11,429 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-19 08:08:11,429 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53504.57, 'new_value': 56826.38}, {'field': 'dailyBillAmount', 'old_value': 53504.57, 'new_value': 56826.38}, {'field': 'amount', 'old_value': 47199.4, 'new_value': 50294.74}, {'field': 'count', 'old_value': 1595, 'new_value': 1685}, {'field': 'instoreAmount', 'old_value': 43293.54, 'new_value': 46020.78}, {'field': 'instoreCount', 'old_value': 1416, 'new_value': 1490}, {'field': 'onlineAmount', 'old_value': 3942.2999999999997, 'new_value': 4310.4}, {'field': 'onlineCount', 'old_value': 179, 'new_value': 195}]
2025-05-19 08:08:11,970 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-19 08:08:11,970 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20740.5, 'new_value': 23036.74}, {'field': 'dailyBillAmount', 'old_value': 20740.5, 'new_value': 23036.74}, {'field': 'amount', 'old_value': 24406.01, 'new_value': 26725.31}, {'field': 'count', 'old_value': 145, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 24471.21, 'new_value': 26697.11}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 157}, {'field': 'onlineAmount', 'old_value': 85.4, 'new_value': 178.8}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 6}]
2025-05-19 08:08:12,414 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-19 08:08:12,415 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 141422.93, 'new_value': 145965.08000000002}, {'field': 'dailyBillAmount', 'old_value': 141422.93, 'new_value': 145965.08000000002}, {'field': 'amount', 'old_value': 148278.6, 'new_value': 152884.6}, {'field': 'count', 'old_value': 1026, 'new_value': 1069}, {'field': 'instoreAmount', 'old_value': 142066.7, 'new_value': 146653.7}, {'field': 'instoreCount', 'old_value': 914, 'new_value': 951}, {'field': 'onlineAmount', 'old_value': 7202.9, 'new_value': 7603.9}, {'field': 'onlineCount', 'old_value': 112, 'new_value': 118}]
2025-05-19 08:08:12,415 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-19 08:08:12,416 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-19 08:08:12,416 - INFO - 正在批量插入月度数据，批次 1/1，共 2 条记录
2025-05-19 08:08:12,571 - INFO - 批量插入月度数据成功，批次 1，2 条记录
2025-05-19 08:08:15,572 - INFO - 批量插入月度数据完成: 总计 2 条，成功 2 条，失败 0 条
2025-05-19 08:08:15,572 - INFO - 批量插入月销售数据完成，共 2 条记录
2025-05-19 08:08:15,573 - INFO - 月销售数据同步完成！更新: 217 条，插入: 2 条，错误: 0 条，跳过: 971 条
2025-05-19 08:08:15,573 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-19 08:08:16,059 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250519.xlsx
2025-05-19 08:08:16,060 - INFO - 综合数据同步流程完成！
2025-05-19 08:08:16,125 - INFO - 综合数据同步完成
2025-05-19 08:08:16,125 - INFO - ==================================================
2025-05-19 08:08:16,125 - INFO - 程序退出
2025-05-19 08:08:16,126 - INFO - ==================================================
