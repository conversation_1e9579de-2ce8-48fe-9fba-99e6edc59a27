2025-05-23 00:00:02,050 - INFO - =================使用默认全量同步=============
2025-05-23 00:00:03,550 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 00:00:03,550 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 00:00:03,581 - INFO - 开始处理日期: 2025-01
2025-05-23 00:00:03,597 - INFO - Request Parameters - Page 1:
2025-05-23 00:00:03,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:03,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:04,425 - INFO - Response - Page 1:
2025-05-23 00:00:04,628 - INFO - 第 1 页获取到 100 条记录
2025-05-23 00:00:04,628 - INFO - Request Parameters - Page 2:
2025-05-23 00:00:04,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:04,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:05,331 - INFO - Response - Page 2:
2025-05-23 00:00:05,534 - INFO - 第 2 页获取到 100 条记录
2025-05-23 00:00:05,534 - INFO - Request Parameters - Page 3:
2025-05-23 00:00:05,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:05,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:06,378 - INFO - Response - Page 3:
2025-05-23 00:00:06,581 - INFO - 第 3 页获取到 100 条记录
2025-05-23 00:00:06,581 - INFO - Request Parameters - Page 4:
2025-05-23 00:00:06,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:06,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:07,112 - INFO - Response - Page 4:
2025-05-23 00:00:07,315 - INFO - 第 4 页获取到 100 条记录
2025-05-23 00:00:07,315 - INFO - Request Parameters - Page 5:
2025-05-23 00:00:07,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:07,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:07,956 - INFO - Response - Page 5:
2025-05-23 00:00:08,159 - INFO - 第 5 页获取到 100 条记录
2025-05-23 00:00:08,159 - INFO - Request Parameters - Page 6:
2025-05-23 00:00:08,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:08,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:08,722 - INFO - Response - Page 6:
2025-05-23 00:00:08,925 - INFO - 第 6 页获取到 100 条记录
2025-05-23 00:00:08,925 - INFO - Request Parameters - Page 7:
2025-05-23 00:00:08,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:08,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:09,440 - INFO - Response - Page 7:
2025-05-23 00:00:09,643 - INFO - 第 7 页获取到 82 条记录
2025-05-23 00:00:09,643 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 00:00:09,643 - INFO - 获取到 682 条表单数据
2025-05-23 00:00:09,643 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 00:00:09,659 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 00:00:09,659 - INFO - 开始处理日期: 2025-02
2025-05-23 00:00:09,659 - INFO - Request Parameters - Page 1:
2025-05-23 00:00:09,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:09,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:10,159 - INFO - Response - Page 1:
2025-05-23 00:00:10,362 - INFO - 第 1 页获取到 100 条记录
2025-05-23 00:00:10,362 - INFO - Request Parameters - Page 2:
2025-05-23 00:00:10,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:10,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:10,878 - INFO - Response - Page 2:
2025-05-23 00:00:11,081 - INFO - 第 2 页获取到 100 条记录
2025-05-23 00:00:11,081 - INFO - Request Parameters - Page 3:
2025-05-23 00:00:11,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:11,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:11,550 - INFO - Response - Page 3:
2025-05-23 00:00:11,753 - INFO - 第 3 页获取到 100 条记录
2025-05-23 00:00:11,753 - INFO - Request Parameters - Page 4:
2025-05-23 00:00:11,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:11,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:12,222 - INFO - Response - Page 4:
2025-05-23 00:00:12,425 - INFO - 第 4 页获取到 100 条记录
2025-05-23 00:00:12,425 - INFO - Request Parameters - Page 5:
2025-05-23 00:00:12,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:12,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:13,003 - INFO - Response - Page 5:
2025-05-23 00:00:13,206 - INFO - 第 5 页获取到 100 条记录
2025-05-23 00:00:13,206 - INFO - Request Parameters - Page 6:
2025-05-23 00:00:13,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:13,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:13,675 - INFO - Response - Page 6:
2025-05-23 00:00:13,878 - INFO - 第 6 页获取到 100 条记录
2025-05-23 00:00:13,878 - INFO - Request Parameters - Page 7:
2025-05-23 00:00:13,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:13,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:14,425 - INFO - Response - Page 7:
2025-05-23 00:00:14,628 - INFO - 第 7 页获取到 70 条记录
2025-05-23 00:00:14,628 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 00:00:14,628 - INFO - 获取到 670 条表单数据
2025-05-23 00:00:14,628 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 00:00:14,643 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 00:00:14,643 - INFO - 开始处理日期: 2025-03
2025-05-23 00:00:14,643 - INFO - Request Parameters - Page 1:
2025-05-23 00:00:14,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:14,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:15,300 - INFO - Response - Page 1:
2025-05-23 00:00:15,503 - INFO - 第 1 页获取到 100 条记录
2025-05-23 00:00:15,503 - INFO - Request Parameters - Page 2:
2025-05-23 00:00:15,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:15,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:15,940 - INFO - Response - Page 2:
2025-05-23 00:00:16,143 - INFO - 第 2 页获取到 100 条记录
2025-05-23 00:00:16,143 - INFO - Request Parameters - Page 3:
2025-05-23 00:00:16,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:16,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:16,612 - INFO - Response - Page 3:
2025-05-23 00:00:16,815 - INFO - 第 3 页获取到 100 条记录
2025-05-23 00:00:16,815 - INFO - Request Parameters - Page 4:
2025-05-23 00:00:16,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:16,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:17,300 - INFO - Response - Page 4:
2025-05-23 00:00:17,503 - INFO - 第 4 页获取到 100 条记录
2025-05-23 00:00:17,503 - INFO - Request Parameters - Page 5:
2025-05-23 00:00:17,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:17,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:17,971 - INFO - Response - Page 5:
2025-05-23 00:00:18,175 - INFO - 第 5 页获取到 100 条记录
2025-05-23 00:00:18,175 - INFO - Request Parameters - Page 6:
2025-05-23 00:00:18,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:18,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:18,737 - INFO - Response - Page 6:
2025-05-23 00:00:18,940 - INFO - 第 6 页获取到 100 条记录
2025-05-23 00:00:18,940 - INFO - Request Parameters - Page 7:
2025-05-23 00:00:18,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:18,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:19,409 - INFO - Response - Page 7:
2025-05-23 00:00:19,612 - INFO - 第 7 页获取到 61 条记录
2025-05-23 00:00:19,612 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 00:00:19,612 - INFO - 获取到 661 条表单数据
2025-05-23 00:00:19,612 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 00:00:19,628 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 00:00:19,628 - INFO - 开始处理日期: 2025-04
2025-05-23 00:00:19,628 - INFO - Request Parameters - Page 1:
2025-05-23 00:00:19,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:19,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:20,112 - INFO - Response - Page 1:
2025-05-23 00:00:20,315 - INFO - 第 1 页获取到 100 条记录
2025-05-23 00:00:20,315 - INFO - Request Parameters - Page 2:
2025-05-23 00:00:20,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:20,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:20,831 - INFO - Response - Page 2:
2025-05-23 00:00:21,034 - INFO - 第 2 页获取到 100 条记录
2025-05-23 00:00:21,034 - INFO - Request Parameters - Page 3:
2025-05-23 00:00:21,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:21,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:21,503 - INFO - Response - Page 3:
2025-05-23 00:00:21,706 - INFO - 第 3 页获取到 100 条记录
2025-05-23 00:00:21,706 - INFO - Request Parameters - Page 4:
2025-05-23 00:00:21,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:21,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:22,175 - INFO - Response - Page 4:
2025-05-23 00:00:22,378 - INFO - 第 4 页获取到 100 条记录
2025-05-23 00:00:22,378 - INFO - Request Parameters - Page 5:
2025-05-23 00:00:22,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:22,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:22,956 - INFO - Response - Page 5:
2025-05-23 00:00:23,159 - INFO - 第 5 页获取到 100 条记录
2025-05-23 00:00:23,159 - INFO - Request Parameters - Page 6:
2025-05-23 00:00:23,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:23,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:23,628 - INFO - Response - Page 6:
2025-05-23 00:00:23,831 - INFO - 第 6 页获取到 100 条记录
2025-05-23 00:00:23,831 - INFO - Request Parameters - Page 7:
2025-05-23 00:00:23,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:23,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:24,253 - INFO - Response - Page 7:
2025-05-23 00:00:24,456 - INFO - 第 7 页获取到 56 条记录
2025-05-23 00:00:24,456 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 00:00:24,456 - INFO - 获取到 656 条表单数据
2025-05-23 00:00:24,456 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 00:00:24,471 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 00:00:24,471 - INFO - 开始处理日期: 2025-05
2025-05-23 00:00:24,471 - INFO - Request Parameters - Page 1:
2025-05-23 00:00:24,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:24,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:25,003 - INFO - Response - Page 1:
2025-05-23 00:00:25,206 - INFO - 第 1 页获取到 100 条记录
2025-05-23 00:00:25,206 - INFO - Request Parameters - Page 2:
2025-05-23 00:00:25,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:25,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:25,956 - INFO - Response - Page 2:
2025-05-23 00:00:26,159 - INFO - 第 2 页获取到 100 条记录
2025-05-23 00:00:26,159 - INFO - Request Parameters - Page 3:
2025-05-23 00:00:26,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:26,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:26,643 - INFO - Response - Page 3:
2025-05-23 00:00:26,846 - INFO - 第 3 页获取到 100 条记录
2025-05-23 00:00:26,846 - INFO - Request Parameters - Page 4:
2025-05-23 00:00:26,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:26,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:27,268 - INFO - Response - Page 4:
2025-05-23 00:00:27,471 - INFO - 第 4 页获取到 100 条记录
2025-05-23 00:00:27,471 - INFO - Request Parameters - Page 5:
2025-05-23 00:00:27,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:27,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:27,909 - INFO - Response - Page 5:
2025-05-23 00:00:28,112 - INFO - 第 5 页获取到 100 条记录
2025-05-23 00:00:28,112 - INFO - Request Parameters - Page 6:
2025-05-23 00:00:28,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:28,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:28,581 - INFO - Response - Page 6:
2025-05-23 00:00:28,784 - INFO - 第 6 页获取到 100 条记录
2025-05-23 00:00:28,784 - INFO - Request Parameters - Page 7:
2025-05-23 00:00:28,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:00:28,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:00:29,175 - INFO - Response - Page 7:
2025-05-23 00:00:29,378 - INFO - 第 7 页获取到 28 条记录
2025-05-23 00:00:29,378 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 00:00:29,378 - INFO - 获取到 628 条表单数据
2025-05-23 00:00:29,378 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 00:00:29,378 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-23 00:00:29,846 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-23 00:00:29,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100763.0, 'new_value': 105208.0}, {'field': 'offline_amount', 'old_value': 111682.28, 'new_value': 118729.28}, {'field': 'total_amount', 'old_value': 212445.28, 'new_value': 223937.28}, {'field': 'order_count', 'old_value': 4550, 'new_value': 4803}]
2025-05-23 00:00:29,846 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-23 00:00:30,331 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-23 00:00:30,331 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 945219.0, 'new_value': 991785.0}, {'field': 'offline_amount', 'old_value': 283997.0, 'new_value': 294219.0}, {'field': 'total_amount', 'old_value': 1229216.0, 'new_value': 1286004.0}, {'field': 'order_count', 'old_value': 1419, 'new_value': 1494}]
2025-05-23 00:00:30,331 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-23 00:00:30,706 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-23 00:00:30,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234280.0, 'new_value': 238825.0}, {'field': 'total_amount', 'old_value': 264280.0, 'new_value': 268825.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-23 00:00:30,706 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-23 00:00:31,206 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-23 00:00:31,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303420.89, 'new_value': 308167.89}, {'field': 'total_amount', 'old_value': 303420.89, 'new_value': 308167.89}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-23 00:00:31,206 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-23 00:00:31,721 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-23 00:00:31,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262080.13, 'new_value': 274580.13}, {'field': 'total_amount', 'old_value': 301440.13, 'new_value': 313940.13}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-23 00:00:31,721 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-23 00:00:32,128 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-23 00:00:32,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23331.2, 'new_value': 24743.2}, {'field': 'offline_amount', 'old_value': 133148.44, 'new_value': 145493.38}, {'field': 'total_amount', 'old_value': 156479.64, 'new_value': 170236.58}, {'field': 'order_count', 'old_value': 219, 'new_value': 231}]
2025-05-23 00:00:32,128 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-23 00:00:32,565 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-23 00:00:32,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37790.54, 'new_value': 40190.33}, {'field': 'total_amount', 'old_value': 37790.54, 'new_value': 40190.33}, {'field': 'order_count', 'old_value': 2740, 'new_value': 2922}]
2025-05-23 00:00:32,565 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-23 00:00:33,034 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-23 00:00:33,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37303.0, 'new_value': 38418.0}, {'field': 'total_amount', 'old_value': 42679.0, 'new_value': 43794.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 196}]
2025-05-23 00:00:33,049 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-23 00:00:33,456 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-23 00:00:33,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55555.12, 'new_value': 68140.42}, {'field': 'total_amount', 'old_value': 55555.12, 'new_value': 68140.42}, {'field': 'order_count', 'old_value': 98, 'new_value': 117}]
2025-05-23 00:00:33,456 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-23 00:00:33,846 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-23 00:00:33,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8893.0, 'new_value': 9573.0}, {'field': 'total_amount', 'old_value': 8893.0, 'new_value': 9573.0}, {'field': 'order_count', 'old_value': 303, 'new_value': 304}]
2025-05-23 00:00:33,846 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-23 00:00:34,253 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-23 00:00:34,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46954.9, 'new_value': 48096.1}, {'field': 'total_amount', 'old_value': 46966.8, 'new_value': 48108.0}, {'field': 'order_count', 'old_value': 268, 'new_value': 273}]
2025-05-23 00:00:34,253 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-23 00:00:34,690 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-23 00:00:34,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44296.01, 'new_value': 46112.96}, {'field': 'offline_amount', 'old_value': 588100.21, 'new_value': 622486.53}, {'field': 'total_amount', 'old_value': 632396.22, 'new_value': 668599.49}, {'field': 'order_count', 'old_value': 2658, 'new_value': 2801}]
2025-05-23 00:00:34,690 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-23 00:00:35,081 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-23 00:00:35,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71651.54, 'new_value': 75291.36}, {'field': 'offline_amount', 'old_value': 36071.46, 'new_value': 37271.21}, {'field': 'total_amount', 'old_value': 107723.0, 'new_value': 112562.57}, {'field': 'order_count', 'old_value': 3705, 'new_value': 3888}]
2025-05-23 00:00:35,081 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-23 00:00:35,534 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-23 00:00:35,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47000.0, 'new_value': 51000.0}, {'field': 'total_amount', 'old_value': 47000.0, 'new_value': 51000.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 61}]
2025-05-23 00:00:35,534 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-23 00:00:35,987 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-23 00:00:35,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3616.05, 'new_value': 3934.69}, {'field': 'offline_amount', 'old_value': 118685.98, 'new_value': 121880.2}, {'field': 'total_amount', 'old_value': 122302.03, 'new_value': 125814.89}, {'field': 'order_count', 'old_value': 566, 'new_value': 586}]
2025-05-23 00:00:35,987 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-23 00:00:36,393 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-23 00:00:36,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1509288.58, 'new_value': 1572635.56}, {'field': 'total_amount', 'old_value': 1509288.58, 'new_value': 1572635.56}, {'field': 'order_count', 'old_value': 12290, 'new_value': 12983}]
2025-05-23 00:00:36,393 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-23 00:00:36,753 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-23 00:00:36,753 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84583.96, 'new_value': 87664.57}, {'field': 'offline_amount', 'old_value': 62135.34, 'new_value': 64116.84}, {'field': 'total_amount', 'old_value': 146719.3, 'new_value': 151781.41}, {'field': 'order_count', 'old_value': 6189, 'new_value': 6421}]
2025-05-23 00:00:36,753 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-23 00:00:37,174 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-23 00:00:37,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96460.0, 'new_value': 100660.0}, {'field': 'total_amount', 'old_value': 96460.0, 'new_value': 100660.0}, {'field': 'order_count', 'old_value': 4644, 'new_value': 4956}]
2025-05-23 00:00:37,174 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-23 00:00:37,659 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-23 00:00:37,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19569.08, 'new_value': 20115.6}, {'field': 'total_amount', 'old_value': 21219.08, 'new_value': 21765.6}, {'field': 'order_count', 'old_value': 407, 'new_value': 422}]
2025-05-23 00:00:37,674 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-23 00:00:38,081 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-23 00:00:38,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 621050.95, 'new_value': 639401.96}, {'field': 'total_amount', 'old_value': 621050.95, 'new_value': 639401.96}, {'field': 'order_count', 'old_value': 4312, 'new_value': 4451}]
2025-05-23 00:00:38,081 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-23 00:00:38,503 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-23 00:00:38,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267286.0, 'new_value': 274795.0}, {'field': 'total_amount', 'old_value': 271386.0, 'new_value': 278895.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 190}]
2025-05-23 00:00:38,503 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-23 00:00:38,924 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-23 00:00:38,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18700.0, 'new_value': 19220.0}, {'field': 'total_amount', 'old_value': 18700.0, 'new_value': 19220.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-23 00:00:38,924 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-23 00:00:39,315 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-23 00:00:39,315 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174584.87, 'new_value': 180866.5}, {'field': 'offline_amount', 'old_value': 326946.92, 'new_value': 334946.92}, {'field': 'total_amount', 'old_value': 501531.79, 'new_value': 515813.42}, {'field': 'order_count', 'old_value': 1226, 'new_value': 1272}]
2025-05-23 00:00:39,315 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-23 00:00:39,768 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-23 00:00:39,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20968.4, 'new_value': 21365.5}, {'field': 'offline_amount', 'old_value': 160715.2, 'new_value': 167002.7}, {'field': 'total_amount', 'old_value': 181683.6, 'new_value': 188368.2}, {'field': 'order_count', 'old_value': 5573, 'new_value': 5815}]
2025-05-23 00:00:39,768 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-23 00:00:40,206 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-23 00:00:40,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269530.0, 'new_value': 273112.0}, {'field': 'total_amount', 'old_value': 274254.0, 'new_value': 277836.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 81}]
2025-05-23 00:00:40,221 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-23 00:00:40,628 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-23 00:00:40,628 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4202.23, 'new_value': 4326.83}, {'field': 'offline_amount', 'old_value': 36233.83, 'new_value': 41152.64}, {'field': 'total_amount', 'old_value': 40436.06, 'new_value': 45479.47}, {'field': 'order_count', 'old_value': 1629, 'new_value': 1793}]
2025-05-23 00:00:40,628 - INFO - 日期 2025-05 处理完成 - 更新: 26 条，插入: 0 条，错误: 0 条
2025-05-23 00:00:40,628 - INFO - 数据同步完成！更新: 26 条，插入: 0 条，错误: 0 条
2025-05-23 00:00:40,628 - INFO - =================同步完成====================
2025-05-23 03:00:01,981 - INFO - =================使用默认全量同步=============
2025-05-23 03:00:03,418 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 03:00:03,418 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 03:00:03,450 - INFO - 开始处理日期: 2025-01
2025-05-23 03:00:03,465 - INFO - Request Parameters - Page 1:
2025-05-23 03:00:03,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:03,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:04,559 - INFO - Response - Page 1:
2025-05-23 03:00:04,762 - INFO - 第 1 页获取到 100 条记录
2025-05-23 03:00:04,762 - INFO - Request Parameters - Page 2:
2025-05-23 03:00:04,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:04,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:05,590 - INFO - Response - Page 2:
2025-05-23 03:00:05,793 - INFO - 第 2 页获取到 100 条记录
2025-05-23 03:00:05,793 - INFO - Request Parameters - Page 3:
2025-05-23 03:00:05,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:05,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:06,371 - INFO - Response - Page 3:
2025-05-23 03:00:06,575 - INFO - 第 3 页获取到 100 条记录
2025-05-23 03:00:06,575 - INFO - Request Parameters - Page 4:
2025-05-23 03:00:06,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:06,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:07,090 - INFO - Response - Page 4:
2025-05-23 03:00:07,293 - INFO - 第 4 页获取到 100 条记录
2025-05-23 03:00:07,293 - INFO - Request Parameters - Page 5:
2025-05-23 03:00:07,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:07,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:07,856 - INFO - Response - Page 5:
2025-05-23 03:00:08,059 - INFO - 第 5 页获取到 100 条记录
2025-05-23 03:00:08,059 - INFO - Request Parameters - Page 6:
2025-05-23 03:00:08,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:08,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:08,621 - INFO - Response - Page 6:
2025-05-23 03:00:08,825 - INFO - 第 6 页获取到 100 条记录
2025-05-23 03:00:08,825 - INFO - Request Parameters - Page 7:
2025-05-23 03:00:08,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:08,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:09,309 - INFO - Response - Page 7:
2025-05-23 03:00:09,512 - INFO - 第 7 页获取到 82 条记录
2025-05-23 03:00:09,512 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 03:00:09,512 - INFO - 获取到 682 条表单数据
2025-05-23 03:00:09,512 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 03:00:09,528 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 03:00:09,528 - INFO - 开始处理日期: 2025-02
2025-05-23 03:00:09,528 - INFO - Request Parameters - Page 1:
2025-05-23 03:00:09,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:09,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:10,043 - INFO - Response - Page 1:
2025-05-23 03:00:10,246 - INFO - 第 1 页获取到 100 条记录
2025-05-23 03:00:10,246 - INFO - Request Parameters - Page 2:
2025-05-23 03:00:10,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:10,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:10,793 - INFO - Response - Page 2:
2025-05-23 03:00:10,996 - INFO - 第 2 页获取到 100 条记录
2025-05-23 03:00:10,996 - INFO - Request Parameters - Page 3:
2025-05-23 03:00:10,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:10,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:11,496 - INFO - Response - Page 3:
2025-05-23 03:00:11,700 - INFO - 第 3 页获取到 100 条记录
2025-05-23 03:00:11,700 - INFO - Request Parameters - Page 4:
2025-05-23 03:00:11,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:11,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:12,184 - INFO - Response - Page 4:
2025-05-23 03:00:12,387 - INFO - 第 4 页获取到 100 条记录
2025-05-23 03:00:12,387 - INFO - Request Parameters - Page 5:
2025-05-23 03:00:12,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:12,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:12,903 - INFO - Response - Page 5:
2025-05-23 03:00:13,106 - INFO - 第 5 页获取到 100 条记录
2025-05-23 03:00:13,106 - INFO - Request Parameters - Page 6:
2025-05-23 03:00:13,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:13,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:13,606 - INFO - Response - Page 6:
2025-05-23 03:00:13,809 - INFO - 第 6 页获取到 100 条记录
2025-05-23 03:00:13,809 - INFO - Request Parameters - Page 7:
2025-05-23 03:00:13,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:13,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:14,278 - INFO - Response - Page 7:
2025-05-23 03:00:14,481 - INFO - 第 7 页获取到 70 条记录
2025-05-23 03:00:14,481 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 03:00:14,481 - INFO - 获取到 670 条表单数据
2025-05-23 03:00:14,481 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 03:00:14,496 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 03:00:14,496 - INFO - 开始处理日期: 2025-03
2025-05-23 03:00:14,496 - INFO - Request Parameters - Page 1:
2025-05-23 03:00:14,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:14,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:15,012 - INFO - Response - Page 1:
2025-05-23 03:00:15,215 - INFO - 第 1 页获取到 100 条记录
2025-05-23 03:00:15,215 - INFO - Request Parameters - Page 2:
2025-05-23 03:00:15,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:15,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:15,731 - INFO - Response - Page 2:
2025-05-23 03:00:15,934 - INFO - 第 2 页获取到 100 条记录
2025-05-23 03:00:15,934 - INFO - Request Parameters - Page 3:
2025-05-23 03:00:15,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:15,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:16,434 - INFO - Response - Page 3:
2025-05-23 03:00:16,637 - INFO - 第 3 页获取到 100 条记录
2025-05-23 03:00:16,637 - INFO - Request Parameters - Page 4:
2025-05-23 03:00:16,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:16,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:17,199 - INFO - Response - Page 4:
2025-05-23 03:00:17,403 - INFO - 第 4 页获取到 100 条记录
2025-05-23 03:00:17,403 - INFO - Request Parameters - Page 5:
2025-05-23 03:00:17,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:17,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:17,887 - INFO - Response - Page 5:
2025-05-23 03:00:18,090 - INFO - 第 5 页获取到 100 条记录
2025-05-23 03:00:18,090 - INFO - Request Parameters - Page 6:
2025-05-23 03:00:18,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:18,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:18,699 - INFO - Response - Page 6:
2025-05-23 03:00:18,903 - INFO - 第 6 页获取到 100 条记录
2025-05-23 03:00:18,903 - INFO - Request Parameters - Page 7:
2025-05-23 03:00:18,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:18,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:19,387 - INFO - Response - Page 7:
2025-05-23 03:00:19,590 - INFO - 第 7 页获取到 61 条记录
2025-05-23 03:00:19,590 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 03:00:19,590 - INFO - 获取到 661 条表单数据
2025-05-23 03:00:19,590 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 03:00:19,606 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 03:00:19,606 - INFO - 开始处理日期: 2025-04
2025-05-23 03:00:19,606 - INFO - Request Parameters - Page 1:
2025-05-23 03:00:19,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:19,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:20,168 - INFO - Response - Page 1:
2025-05-23 03:00:20,371 - INFO - 第 1 页获取到 100 条记录
2025-05-23 03:00:20,371 - INFO - Request Parameters - Page 2:
2025-05-23 03:00:20,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:20,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:20,871 - INFO - Response - Page 2:
2025-05-23 03:00:21,074 - INFO - 第 2 页获取到 100 条记录
2025-05-23 03:00:21,074 - INFO - Request Parameters - Page 3:
2025-05-23 03:00:21,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:21,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:21,543 - INFO - Response - Page 3:
2025-05-23 03:00:21,746 - INFO - 第 3 页获取到 100 条记录
2025-05-23 03:00:21,746 - INFO - Request Parameters - Page 4:
2025-05-23 03:00:21,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:21,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:22,199 - INFO - Response - Page 4:
2025-05-23 03:00:22,403 - INFO - 第 4 页获取到 100 条记录
2025-05-23 03:00:22,403 - INFO - Request Parameters - Page 5:
2025-05-23 03:00:22,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:22,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:22,949 - INFO - Response - Page 5:
2025-05-23 03:00:23,153 - INFO - 第 5 页获取到 100 条记录
2025-05-23 03:00:23,153 - INFO - Request Parameters - Page 6:
2025-05-23 03:00:23,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:23,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:23,746 - INFO - Response - Page 6:
2025-05-23 03:00:23,949 - INFO - 第 6 页获取到 100 条记录
2025-05-23 03:00:23,949 - INFO - Request Parameters - Page 7:
2025-05-23 03:00:23,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:23,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:24,418 - INFO - Response - Page 7:
2025-05-23 03:00:24,621 - INFO - 第 7 页获取到 56 条记录
2025-05-23 03:00:24,621 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 03:00:24,621 - INFO - 获取到 656 条表单数据
2025-05-23 03:00:24,621 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 03:00:24,637 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 03:00:24,637 - INFO - 开始处理日期: 2025-05
2025-05-23 03:00:24,637 - INFO - Request Parameters - Page 1:
2025-05-23 03:00:24,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:24,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:25,121 - INFO - Response - Page 1:
2025-05-23 03:00:25,324 - INFO - 第 1 页获取到 100 条记录
2025-05-23 03:00:25,324 - INFO - Request Parameters - Page 2:
2025-05-23 03:00:25,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:25,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:25,871 - INFO - Response - Page 2:
2025-05-23 03:00:26,074 - INFO - 第 2 页获取到 100 条记录
2025-05-23 03:00:26,074 - INFO - Request Parameters - Page 3:
2025-05-23 03:00:26,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:26,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:26,606 - INFO - Response - Page 3:
2025-05-23 03:00:26,809 - INFO - 第 3 页获取到 100 条记录
2025-05-23 03:00:26,809 - INFO - Request Parameters - Page 4:
2025-05-23 03:00:26,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:26,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:27,309 - INFO - Response - Page 4:
2025-05-23 03:00:27,512 - INFO - 第 4 页获取到 100 条记录
2025-05-23 03:00:27,512 - INFO - Request Parameters - Page 5:
2025-05-23 03:00:27,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:27,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:27,996 - INFO - Response - Page 5:
2025-05-23 03:00:28,199 - INFO - 第 5 页获取到 100 条记录
2025-05-23 03:00:28,199 - INFO - Request Parameters - Page 6:
2025-05-23 03:00:28,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:28,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:28,668 - INFO - Response - Page 6:
2025-05-23 03:00:28,871 - INFO - 第 6 页获取到 100 条记录
2025-05-23 03:00:28,871 - INFO - Request Parameters - Page 7:
2025-05-23 03:00:28,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:00:28,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:00:29,231 - INFO - Response - Page 7:
2025-05-23 03:00:29,434 - INFO - 第 7 页获取到 28 条记录
2025-05-23 03:00:29,434 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 03:00:29,434 - INFO - 获取到 628 条表单数据
2025-05-23 03:00:29,434 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 03:00:29,449 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 03:00:29,449 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 03:00:29,449 - INFO - =================同步完成====================
2025-05-23 06:00:02,051 - INFO - =================使用默认全量同步=============
2025-05-23 06:00:03,489 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 06:00:03,489 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 06:00:03,520 - INFO - 开始处理日期: 2025-01
2025-05-23 06:00:03,520 - INFO - Request Parameters - Page 1:
2025-05-23 06:00:03,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:03,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:04,911 - INFO - Response - Page 1:
2025-05-23 06:00:05,114 - INFO - 第 1 页获取到 100 条记录
2025-05-23 06:00:05,114 - INFO - Request Parameters - Page 2:
2025-05-23 06:00:05,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:05,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:05,676 - INFO - Response - Page 2:
2025-05-23 06:00:05,880 - INFO - 第 2 页获取到 100 条记录
2025-05-23 06:00:05,880 - INFO - Request Parameters - Page 3:
2025-05-23 06:00:05,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:05,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:06,426 - INFO - Response - Page 3:
2025-05-23 06:00:06,630 - INFO - 第 3 页获取到 100 条记录
2025-05-23 06:00:06,630 - INFO - Request Parameters - Page 4:
2025-05-23 06:00:06,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:06,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:07,130 - INFO - Response - Page 4:
2025-05-23 06:00:07,333 - INFO - 第 4 页获取到 100 条记录
2025-05-23 06:00:07,333 - INFO - Request Parameters - Page 5:
2025-05-23 06:00:07,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:07,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:07,848 - INFO - Response - Page 5:
2025-05-23 06:00:08,051 - INFO - 第 5 页获取到 100 条记录
2025-05-23 06:00:08,051 - INFO - Request Parameters - Page 6:
2025-05-23 06:00:08,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:08,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:08,567 - INFO - Response - Page 6:
2025-05-23 06:00:08,770 - INFO - 第 6 页获取到 100 条记录
2025-05-23 06:00:08,770 - INFO - Request Parameters - Page 7:
2025-05-23 06:00:08,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:08,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:09,270 - INFO - Response - Page 7:
2025-05-23 06:00:09,473 - INFO - 第 7 页获取到 82 条记录
2025-05-23 06:00:09,473 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 06:00:09,473 - INFO - 获取到 682 条表单数据
2025-05-23 06:00:09,473 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 06:00:09,489 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 06:00:09,489 - INFO - 开始处理日期: 2025-02
2025-05-23 06:00:09,489 - INFO - Request Parameters - Page 1:
2025-05-23 06:00:09,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:09,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:09,989 - INFO - Response - Page 1:
2025-05-23 06:00:10,192 - INFO - 第 1 页获取到 100 条记录
2025-05-23 06:00:10,192 - INFO - Request Parameters - Page 2:
2025-05-23 06:00:10,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:10,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:10,754 - INFO - Response - Page 2:
2025-05-23 06:00:10,958 - INFO - 第 2 页获取到 100 条记录
2025-05-23 06:00:10,958 - INFO - Request Parameters - Page 3:
2025-05-23 06:00:10,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:10,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:11,583 - INFO - Response - Page 3:
2025-05-23 06:00:11,786 - INFO - 第 3 页获取到 100 条记录
2025-05-23 06:00:11,786 - INFO - Request Parameters - Page 4:
2025-05-23 06:00:11,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:11,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:12,317 - INFO - Response - Page 4:
2025-05-23 06:00:12,520 - INFO - 第 4 页获取到 100 条记录
2025-05-23 06:00:12,520 - INFO - Request Parameters - Page 5:
2025-05-23 06:00:12,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:12,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:12,989 - INFO - Response - Page 5:
2025-05-23 06:00:13,192 - INFO - 第 5 页获取到 100 条记录
2025-05-23 06:00:13,192 - INFO - Request Parameters - Page 6:
2025-05-23 06:00:13,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:13,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:13,708 - INFO - Response - Page 6:
2025-05-23 06:00:13,911 - INFO - 第 6 页获取到 100 条记录
2025-05-23 06:00:13,911 - INFO - Request Parameters - Page 7:
2025-05-23 06:00:13,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:13,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:14,379 - INFO - Response - Page 7:
2025-05-23 06:00:14,583 - INFO - 第 7 页获取到 70 条记录
2025-05-23 06:00:14,583 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 06:00:14,583 - INFO - 获取到 670 条表单数据
2025-05-23 06:00:14,583 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 06:00:14,598 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 06:00:14,598 - INFO - 开始处理日期: 2025-03
2025-05-23 06:00:14,598 - INFO - Request Parameters - Page 1:
2025-05-23 06:00:14,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:14,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:15,083 - INFO - Response - Page 1:
2025-05-23 06:00:15,286 - INFO - 第 1 页获取到 100 条记录
2025-05-23 06:00:15,286 - INFO - Request Parameters - Page 2:
2025-05-23 06:00:15,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:15,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:15,770 - INFO - Response - Page 2:
2025-05-23 06:00:15,973 - INFO - 第 2 页获取到 100 条记录
2025-05-23 06:00:15,973 - INFO - Request Parameters - Page 3:
2025-05-23 06:00:15,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:15,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:16,473 - INFO - Response - Page 3:
2025-05-23 06:00:16,676 - INFO - 第 3 页获取到 100 条记录
2025-05-23 06:00:16,676 - INFO - Request Parameters - Page 4:
2025-05-23 06:00:16,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:16,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:17,208 - INFO - Response - Page 4:
2025-05-23 06:00:17,411 - INFO - 第 4 页获取到 100 条记录
2025-05-23 06:00:17,411 - INFO - Request Parameters - Page 5:
2025-05-23 06:00:17,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:17,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:17,942 - INFO - Response - Page 5:
2025-05-23 06:00:18,145 - INFO - 第 5 页获取到 100 条记录
2025-05-23 06:00:18,145 - INFO - Request Parameters - Page 6:
2025-05-23 06:00:18,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:18,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:18,676 - INFO - Response - Page 6:
2025-05-23 06:00:18,879 - INFO - 第 6 页获取到 100 条记录
2025-05-23 06:00:18,879 - INFO - Request Parameters - Page 7:
2025-05-23 06:00:18,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:18,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:19,333 - INFO - Response - Page 7:
2025-05-23 06:00:19,536 - INFO - 第 7 页获取到 61 条记录
2025-05-23 06:00:19,536 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 06:00:19,536 - INFO - 获取到 661 条表单数据
2025-05-23 06:00:19,536 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 06:00:19,551 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 06:00:19,551 - INFO - 开始处理日期: 2025-04
2025-05-23 06:00:19,551 - INFO - Request Parameters - Page 1:
2025-05-23 06:00:19,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:19,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:20,223 - INFO - Response - Page 1:
2025-05-23 06:00:20,426 - INFO - 第 1 页获取到 100 条记录
2025-05-23 06:00:20,426 - INFO - Request Parameters - Page 2:
2025-05-23 06:00:20,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:20,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:20,895 - INFO - Response - Page 2:
2025-05-23 06:00:21,098 - INFO - 第 2 页获取到 100 条记录
2025-05-23 06:00:21,098 - INFO - Request Parameters - Page 3:
2025-05-23 06:00:21,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:21,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:21,676 - INFO - Response - Page 3:
2025-05-23 06:00:21,879 - INFO - 第 3 页获取到 100 条记录
2025-05-23 06:00:21,879 - INFO - Request Parameters - Page 4:
2025-05-23 06:00:21,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:21,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:22,473 - INFO - Response - Page 4:
2025-05-23 06:00:22,676 - INFO - 第 4 页获取到 100 条记录
2025-05-23 06:00:22,676 - INFO - Request Parameters - Page 5:
2025-05-23 06:00:22,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:22,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:23,192 - INFO - Response - Page 5:
2025-05-23 06:00:23,395 - INFO - 第 5 页获取到 100 条记录
2025-05-23 06:00:23,395 - INFO - Request Parameters - Page 6:
2025-05-23 06:00:23,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:23,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:23,911 - INFO - Response - Page 6:
2025-05-23 06:00:24,114 - INFO - 第 6 页获取到 100 条记录
2025-05-23 06:00:24,114 - INFO - Request Parameters - Page 7:
2025-05-23 06:00:24,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:24,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:24,598 - INFO - Response - Page 7:
2025-05-23 06:00:24,801 - INFO - 第 7 页获取到 56 条记录
2025-05-23 06:00:24,801 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 06:00:24,801 - INFO - 获取到 656 条表单数据
2025-05-23 06:00:24,801 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 06:00:24,817 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 06:00:24,817 - INFO - 开始处理日期: 2025-05
2025-05-23 06:00:24,817 - INFO - Request Parameters - Page 1:
2025-05-23 06:00:24,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:24,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:25,411 - INFO - Response - Page 1:
2025-05-23 06:00:25,614 - INFO - 第 1 页获取到 100 条记录
2025-05-23 06:00:25,614 - INFO - Request Parameters - Page 2:
2025-05-23 06:00:25,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:25,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:26,098 - INFO - Response - Page 2:
2025-05-23 06:00:26,301 - INFO - 第 2 页获取到 100 条记录
2025-05-23 06:00:26,301 - INFO - Request Parameters - Page 3:
2025-05-23 06:00:26,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:26,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:26,770 - INFO - Response - Page 3:
2025-05-23 06:00:26,973 - INFO - 第 3 页获取到 100 条记录
2025-05-23 06:00:26,973 - INFO - Request Parameters - Page 4:
2025-05-23 06:00:26,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:26,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:27,458 - INFO - Response - Page 4:
2025-05-23 06:00:27,661 - INFO - 第 4 页获取到 100 条记录
2025-05-23 06:00:27,661 - INFO - Request Parameters - Page 5:
2025-05-23 06:00:27,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:27,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:28,161 - INFO - Response - Page 5:
2025-05-23 06:00:28,364 - INFO - 第 5 页获取到 100 条记录
2025-05-23 06:00:28,364 - INFO - Request Parameters - Page 6:
2025-05-23 06:00:28,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:28,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:28,832 - INFO - Response - Page 6:
2025-05-23 06:00:29,036 - INFO - 第 6 页获取到 100 条记录
2025-05-23 06:00:29,036 - INFO - Request Parameters - Page 7:
2025-05-23 06:00:29,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:00:29,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:00:29,395 - INFO - Response - Page 7:
2025-05-23 06:00:29,598 - INFO - 第 7 页获取到 28 条记录
2025-05-23 06:00:29,598 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 06:00:29,598 - INFO - 获取到 628 条表单数据
2025-05-23 06:00:29,598 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 06:00:29,598 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-23 06:00:30,098 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-23 06:00:30,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66046.0, 'new_value': 68019.0}, {'field': 'total_amount', 'old_value': 66046.0, 'new_value': 68019.0}, {'field': 'order_count', 'old_value': 575, 'new_value': 595}]
2025-05-23 06:00:30,098 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-23 06:00:30,567 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-23 06:00:30,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25980.0, 'new_value': 27457.0}, {'field': 'total_amount', 'old_value': 27530.0, 'new_value': 29007.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 106}]
2025-05-23 06:00:30,567 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-23 06:00:31,036 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-23 06:00:31,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6940.47, 'new_value': 7045.3}, {'field': 'offline_amount', 'old_value': 92317.76, 'new_value': 94261.09}, {'field': 'total_amount', 'old_value': 99258.23, 'new_value': 101306.39}, {'field': 'order_count', 'old_value': 2353, 'new_value': 2412}]
2025-05-23 06:00:31,036 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-23 06:00:31,536 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-23 06:00:31,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5527.2, 'new_value': 5849.32}, {'field': 'offline_amount', 'old_value': 168780.41, 'new_value': 180762.42}, {'field': 'total_amount', 'old_value': 174307.61, 'new_value': 186611.74}, {'field': 'order_count', 'old_value': 1128, 'new_value': 1170}]
2025-05-23 06:00:31,536 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-23 06:00:32,036 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-23 06:00:32,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74053.06, 'new_value': 78038.06}, {'field': 'offline_amount', 'old_value': 964005.6, 'new_value': 1007608.76}, {'field': 'total_amount', 'old_value': 1038058.66, 'new_value': 1085646.82}, {'field': 'order_count', 'old_value': 8428, 'new_value': 8838}]
2025-05-23 06:00:32,036 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-23 06:00:32,473 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-23 06:00:32,473 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69591.75, 'new_value': 70219.75}, {'field': 'total_amount', 'old_value': 69591.75, 'new_value': 70219.75}, {'field': 'order_count', 'old_value': 394, 'new_value': 400}]
2025-05-23 06:00:32,473 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-23 06:00:32,895 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-23 06:00:32,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23537.4, 'new_value': 24707.1}, {'field': 'offline_amount', 'old_value': 18195.6, 'new_value': 19378.6}, {'field': 'total_amount', 'old_value': 41733.0, 'new_value': 44085.7}, {'field': 'order_count', 'old_value': 224, 'new_value': 235}]
2025-05-23 06:00:32,895 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-23 06:00:33,395 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-23 06:00:33,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14227.21, 'new_value': 15166.89}, {'field': 'offline_amount', 'old_value': 226788.84, 'new_value': 236090.84}, {'field': 'total_amount', 'old_value': 241016.05, 'new_value': 251257.73}, {'field': 'order_count', 'old_value': 13305, 'new_value': 13914}]
2025-05-23 06:00:33,395 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-23 06:00:33,739 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-23 06:00:33,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44906.99, 'new_value': 46170.32}, {'field': 'offline_amount', 'old_value': 28851.0, 'new_value': 29658.0}, {'field': 'total_amount', 'old_value': 73757.99, 'new_value': 75828.32}, {'field': 'order_count', 'old_value': 911, 'new_value': 941}]
2025-05-23 06:00:33,739 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-23 06:00:34,239 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-23 06:00:34,239 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41849.23, 'new_value': 44507.13}, {'field': 'offline_amount', 'old_value': 294588.03, 'new_value': 301696.36}, {'field': 'total_amount', 'old_value': 336437.26, 'new_value': 346203.49}, {'field': 'order_count', 'old_value': 2126, 'new_value': 2189}]
2025-05-23 06:00:34,239 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-23 06:00:34,692 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-23 06:00:34,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67125.0, 'new_value': 67614.0}, {'field': 'total_amount', 'old_value': 67125.0, 'new_value': 67614.0}, {'field': 'order_count', 'old_value': 1982, 'new_value': 1995}]
2025-05-23 06:00:34,692 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-23 06:00:35,192 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-23 06:00:35,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23645.0, 'new_value': 24115.0}, {'field': 'total_amount', 'old_value': 23645.0, 'new_value': 24115.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 134}]
2025-05-23 06:00:35,192 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-23 06:00:35,645 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-23 06:00:35,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159997.0, 'new_value': 164439.0}, {'field': 'total_amount', 'old_value': 159997.0, 'new_value': 164439.0}, {'field': 'order_count', 'old_value': 2065, 'new_value': 2122}]
2025-05-23 06:00:35,645 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF
2025-05-23 06:00:36,082 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF
2025-05-23 06:00:36,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17500.0, 'new_value': 26000.0}, {'field': 'total_amount', 'old_value': 17500.0, 'new_value': 26000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-23 06:00:36,082 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-23 06:00:36,442 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-23 06:00:36,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34392.14, 'new_value': 36001.94}, {'field': 'offline_amount', 'old_value': 937005.3, 'new_value': 966122.88}, {'field': 'total_amount', 'old_value': 971397.44, 'new_value': 1002124.82}, {'field': 'order_count', 'old_value': 4725, 'new_value': 4908}]
2025-05-23 06:00:36,442 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-23 06:00:37,004 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-23 06:00:37,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45113.7, 'new_value': 47924.35}, {'field': 'total_amount', 'old_value': 45113.7, 'new_value': 47924.35}, {'field': 'order_count', 'old_value': 171, 'new_value': 180}]
2025-05-23 06:00:37,004 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-23 06:00:37,489 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-23 06:00:37,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3551.0, 'new_value': 3704.0}, {'field': 'offline_amount', 'old_value': 47401.0, 'new_value': 49443.0}, {'field': 'total_amount', 'old_value': 50952.0, 'new_value': 53147.0}, {'field': 'order_count', 'old_value': 408, 'new_value': 418}]
2025-05-23 06:00:37,489 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-23 06:00:37,926 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-23 06:00:37,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52864.18, 'new_value': 56143.18}, {'field': 'total_amount', 'old_value': 58224.15, 'new_value': 61503.15}, {'field': 'order_count', 'old_value': 885, 'new_value': 965}]
2025-05-23 06:00:37,926 - INFO - 日期 2025-05 处理完成 - 更新: 18 条，插入: 0 条，错误: 0 条
2025-05-23 06:00:37,926 - INFO - 数据同步完成！更新: 18 条，插入: 0 条，错误: 0 条
2025-05-23 06:00:37,926 - INFO - =================同步完成====================
2025-05-23 09:00:02,138 - INFO - =================使用默认全量同步=============
2025-05-23 09:00:03,576 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 09:00:03,576 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 09:00:03,607 - INFO - 开始处理日期: 2025-01
2025-05-23 09:00:03,607 - INFO - Request Parameters - Page 1:
2025-05-23 09:00:03,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:03,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:04,998 - INFO - Response - Page 1:
2025-05-23 09:00:05,201 - INFO - 第 1 页获取到 100 条记录
2025-05-23 09:00:05,201 - INFO - Request Parameters - Page 2:
2025-05-23 09:00:05,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:05,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:06,013 - INFO - Response - Page 2:
2025-05-23 09:00:06,216 - INFO - 第 2 页获取到 100 条记录
2025-05-23 09:00:06,216 - INFO - Request Parameters - Page 3:
2025-05-23 09:00:06,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:06,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:06,873 - INFO - Response - Page 3:
2025-05-23 09:00:07,076 - INFO - 第 3 页获取到 100 条记录
2025-05-23 09:00:07,076 - INFO - Request Parameters - Page 4:
2025-05-23 09:00:07,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:07,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:07,623 - INFO - Response - Page 4:
2025-05-23 09:00:07,826 - INFO - 第 4 页获取到 100 条记录
2025-05-23 09:00:07,826 - INFO - Request Parameters - Page 5:
2025-05-23 09:00:07,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:07,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:08,310 - INFO - Response - Page 5:
2025-05-23 09:00:08,513 - INFO - 第 5 页获取到 100 条记录
2025-05-23 09:00:08,513 - INFO - Request Parameters - Page 6:
2025-05-23 09:00:08,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:08,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:09,044 - INFO - Response - Page 6:
2025-05-23 09:00:09,248 - INFO - 第 6 页获取到 100 条记录
2025-05-23 09:00:09,248 - INFO - Request Parameters - Page 7:
2025-05-23 09:00:09,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:09,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:09,701 - INFO - Response - Page 7:
2025-05-23 09:00:09,919 - INFO - 第 7 页获取到 82 条记录
2025-05-23 09:00:09,919 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 09:00:09,919 - INFO - 获取到 682 条表单数据
2025-05-23 09:00:09,919 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 09:00:09,935 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 09:00:09,935 - INFO - 开始处理日期: 2025-02
2025-05-23 09:00:09,935 - INFO - Request Parameters - Page 1:
2025-05-23 09:00:09,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:09,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:10,498 - INFO - Response - Page 1:
2025-05-23 09:00:10,701 - INFO - 第 1 页获取到 100 条记录
2025-05-23 09:00:10,701 - INFO - Request Parameters - Page 2:
2025-05-23 09:00:10,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:10,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:11,294 - INFO - Response - Page 2:
2025-05-23 09:00:11,498 - INFO - 第 2 页获取到 100 条记录
2025-05-23 09:00:11,498 - INFO - Request Parameters - Page 3:
2025-05-23 09:00:11,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:11,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:11,935 - INFO - Response - Page 3:
2025-05-23 09:00:12,138 - INFO - 第 3 页获取到 100 条记录
2025-05-23 09:00:12,138 - INFO - Request Parameters - Page 4:
2025-05-23 09:00:12,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:12,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:12,623 - INFO - Response - Page 4:
2025-05-23 09:00:12,826 - INFO - 第 4 页获取到 100 条记录
2025-05-23 09:00:12,826 - INFO - Request Parameters - Page 5:
2025-05-23 09:00:12,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:12,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:13,326 - INFO - Response - Page 5:
2025-05-23 09:00:13,529 - INFO - 第 5 页获取到 100 条记录
2025-05-23 09:00:13,529 - INFO - Request Parameters - Page 6:
2025-05-23 09:00:13,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:13,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:14,091 - INFO - Response - Page 6:
2025-05-23 09:00:14,294 - INFO - 第 6 页获取到 100 条记录
2025-05-23 09:00:14,294 - INFO - Request Parameters - Page 7:
2025-05-23 09:00:14,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:14,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:14,685 - INFO - Response - Page 7:
2025-05-23 09:00:14,888 - INFO - 第 7 页获取到 70 条记录
2025-05-23 09:00:14,888 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 09:00:14,888 - INFO - 获取到 670 条表单数据
2025-05-23 09:00:14,888 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 09:00:14,904 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 09:00:14,904 - INFO - 开始处理日期: 2025-03
2025-05-23 09:00:14,904 - INFO - Request Parameters - Page 1:
2025-05-23 09:00:14,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:14,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:15,419 - INFO - Response - Page 1:
2025-05-23 09:00:15,623 - INFO - 第 1 页获取到 100 条记录
2025-05-23 09:00:15,623 - INFO - Request Parameters - Page 2:
2025-05-23 09:00:15,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:15,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:16,123 - INFO - Response - Page 2:
2025-05-23 09:00:16,326 - INFO - 第 2 页获取到 100 条记录
2025-05-23 09:00:16,326 - INFO - Request Parameters - Page 3:
2025-05-23 09:00:16,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:16,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:16,872 - INFO - Response - Page 3:
2025-05-23 09:00:17,076 - INFO - 第 3 页获取到 100 条记录
2025-05-23 09:00:17,076 - INFO - Request Parameters - Page 4:
2025-05-23 09:00:17,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:17,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:17,591 - INFO - Response - Page 4:
2025-05-23 09:00:17,794 - INFO - 第 4 页获取到 100 条记录
2025-05-23 09:00:17,794 - INFO - Request Parameters - Page 5:
2025-05-23 09:00:17,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:17,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:18,326 - INFO - Response - Page 5:
2025-05-23 09:00:18,529 - INFO - 第 5 页获取到 100 条记录
2025-05-23 09:00:18,529 - INFO - Request Parameters - Page 6:
2025-05-23 09:00:18,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:18,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:18,997 - INFO - Response - Page 6:
2025-05-23 09:00:19,201 - INFO - 第 6 页获取到 100 条记录
2025-05-23 09:00:19,201 - INFO - Request Parameters - Page 7:
2025-05-23 09:00:19,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:19,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:19,607 - INFO - Response - Page 7:
2025-05-23 09:00:19,810 - INFO - 第 7 页获取到 61 条记录
2025-05-23 09:00:19,810 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 09:00:19,810 - INFO - 获取到 661 条表单数据
2025-05-23 09:00:19,810 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 09:00:19,826 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 09:00:19,826 - INFO - 开始处理日期: 2025-04
2025-05-23 09:00:19,826 - INFO - Request Parameters - Page 1:
2025-05-23 09:00:19,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:19,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:20,388 - INFO - Response - Page 1:
2025-05-23 09:00:20,591 - INFO - 第 1 页获取到 100 条记录
2025-05-23 09:00:20,591 - INFO - Request Parameters - Page 2:
2025-05-23 09:00:20,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:20,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:21,060 - INFO - Response - Page 2:
2025-05-23 09:00:21,263 - INFO - 第 2 页获取到 100 条记录
2025-05-23 09:00:21,263 - INFO - Request Parameters - Page 3:
2025-05-23 09:00:21,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:21,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:21,779 - INFO - Response - Page 3:
2025-05-23 09:00:21,982 - INFO - 第 3 页获取到 100 条记录
2025-05-23 09:00:21,982 - INFO - Request Parameters - Page 4:
2025-05-23 09:00:21,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:21,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:22,622 - INFO - Response - Page 4:
2025-05-23 09:00:22,826 - INFO - 第 4 页获取到 100 条记录
2025-05-23 09:00:22,826 - INFO - Request Parameters - Page 5:
2025-05-23 09:00:22,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:22,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:23,326 - INFO - Response - Page 5:
2025-05-23 09:00:23,529 - INFO - 第 5 页获取到 100 条记录
2025-05-23 09:00:23,529 - INFO - Request Parameters - Page 6:
2025-05-23 09:00:23,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:23,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:23,997 - INFO - Response - Page 6:
2025-05-23 09:00:24,201 - INFO - 第 6 页获取到 100 条记录
2025-05-23 09:00:24,201 - INFO - Request Parameters - Page 7:
2025-05-23 09:00:24,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:24,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:24,701 - INFO - Response - Page 7:
2025-05-23 09:00:24,904 - INFO - 第 7 页获取到 56 条记录
2025-05-23 09:00:24,904 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 09:00:24,904 - INFO - 获取到 656 条表单数据
2025-05-23 09:00:24,919 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 09:00:24,919 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 09:00:24,919 - INFO - 开始处理日期: 2025-05
2025-05-23 09:00:24,919 - INFO - Request Parameters - Page 1:
2025-05-23 09:00:24,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:24,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:25,482 - INFO - Response - Page 1:
2025-05-23 09:00:25,685 - INFO - 第 1 页获取到 100 条记录
2025-05-23 09:00:25,685 - INFO - Request Parameters - Page 2:
2025-05-23 09:00:25,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:25,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:26,232 - INFO - Response - Page 2:
2025-05-23 09:00:26,435 - INFO - 第 2 页获取到 100 条记录
2025-05-23 09:00:26,435 - INFO - Request Parameters - Page 3:
2025-05-23 09:00:26,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:26,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:26,951 - INFO - Response - Page 3:
2025-05-23 09:00:27,154 - INFO - 第 3 页获取到 100 条记录
2025-05-23 09:00:27,154 - INFO - Request Parameters - Page 4:
2025-05-23 09:00:27,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:27,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:27,732 - INFO - Response - Page 4:
2025-05-23 09:00:27,935 - INFO - 第 4 页获取到 100 条记录
2025-05-23 09:00:27,935 - INFO - Request Parameters - Page 5:
2025-05-23 09:00:27,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:27,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:28,466 - INFO - Response - Page 5:
2025-05-23 09:00:28,669 - INFO - 第 5 页获取到 100 条记录
2025-05-23 09:00:28,669 - INFO - Request Parameters - Page 6:
2025-05-23 09:00:28,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:28,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:29,169 - INFO - Response - Page 6:
2025-05-23 09:00:29,372 - INFO - 第 6 页获取到 100 条记录
2025-05-23 09:00:29,372 - INFO - Request Parameters - Page 7:
2025-05-23 09:00:29,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:00:29,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:00:29,685 - INFO - Response - Page 7:
2025-05-23 09:00:29,888 - INFO - 第 7 页获取到 28 条记录
2025-05-23 09:00:29,888 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 09:00:29,888 - INFO - 获取到 628 条表单数据
2025-05-23 09:00:29,888 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 09:00:29,888 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-23 09:00:30,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-23 09:00:30,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 239200.0, 'new_value': 242000.0}, {'field': 'total_amount', 'old_value': 239200.0, 'new_value': 242000.0}]
2025-05-23 09:00:30,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-23 09:00:30,857 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-23 09:00:30,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34073.0, 'new_value': 35178.0}, {'field': 'total_amount', 'old_value': 35940.0, 'new_value': 37045.0}, {'field': 'order_count', 'old_value': 486, 'new_value': 503}]
2025-05-23 09:00:30,857 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-23 09:00:31,357 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-23 09:00:31,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 330041.0, 'new_value': 342884.0}, {'field': 'total_amount', 'old_value': 330041.0, 'new_value': 342884.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 256}]
2025-05-23 09:00:31,357 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-23 09:00:31,810 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-23 09:00:31,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40545.0, 'new_value': 43990.0}, {'field': 'total_amount', 'old_value': 42135.0, 'new_value': 45580.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 172}]
2025-05-23 09:00:31,810 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-23 09:00:32,247 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-23 09:00:32,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300620.0, 'new_value': 317120.0}, {'field': 'total_amount', 'old_value': 300620.0, 'new_value': 317120.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 187}]
2025-05-23 09:00:32,247 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-23 09:00:32,763 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-23 09:00:32,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79694.0, 'new_value': 90154.0}, {'field': 'total_amount', 'old_value': 79694.0, 'new_value': 90154.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 105}]
2025-05-23 09:00:32,763 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-23 09:00:33,294 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-23 09:00:33,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32122.0, 'new_value': 34102.0}, {'field': 'total_amount', 'old_value': 32122.0, 'new_value': 34102.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-23 09:00:33,294 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-23 09:00:33,888 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-23 09:00:33,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220923.0, 'new_value': 253612.0}, {'field': 'total_amount', 'old_value': 220923.0, 'new_value': 253612.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-05-23 09:00:33,888 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-23 09:00:34,310 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-23 09:00:34,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49477.73, 'new_value': 51800.34}, {'field': 'offline_amount', 'old_value': 96419.25, 'new_value': 100030.25}, {'field': 'total_amount', 'old_value': 145896.98, 'new_value': 151830.59}, {'field': 'order_count', 'old_value': 1690, 'new_value': 1757}]
2025-05-23 09:00:34,310 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-23 09:00:34,716 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-23 09:00:34,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18674.88, 'new_value': 19091.17}, {'field': 'offline_amount', 'old_value': 22775.66, 'new_value': 23503.21}, {'field': 'total_amount', 'old_value': 41450.54, 'new_value': 42594.38}, {'field': 'order_count', 'old_value': 2017, 'new_value': 2066}]
2025-05-23 09:00:34,716 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-23 09:00:35,122 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-23 09:00:35,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41440.0, 'new_value': 44920.0}, {'field': 'total_amount', 'old_value': 41440.0, 'new_value': 44920.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-23 09:00:35,122 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-23 09:00:35,638 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-23 09:00:35,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267937.6, 'new_value': 272476.5}, {'field': 'total_amount', 'old_value': 382957.3, 'new_value': 387496.2}, {'field': 'order_count', 'old_value': 2957, 'new_value': 3039}]
2025-05-23 09:00:35,638 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-23 09:00:35,982 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-23 09:00:35,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87523.0, 'new_value': 92366.0}, {'field': 'total_amount', 'old_value': 87523.0, 'new_value': 92366.0}, {'field': 'order_count', 'old_value': 4763, 'new_value': 5031}]
2025-05-23 09:00:35,982 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-23 09:00:36,404 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-23 09:00:36,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123612.0, 'new_value': 127929.65}, {'field': 'total_amount', 'old_value': 123612.0, 'new_value': 127929.65}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1492}]
2025-05-23 09:00:36,404 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-23 09:00:36,888 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-23 09:00:36,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156443.0, 'new_value': 161942.0}, {'field': 'total_amount', 'old_value': 156443.0, 'new_value': 161942.0}, {'field': 'order_count', 'old_value': 304, 'new_value': 317}]
2025-05-23 09:00:36,888 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-23 09:00:37,419 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-23 09:00:37,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154925.89, 'new_value': 158329.31}, {'field': 'offline_amount', 'old_value': 26999.24, 'new_value': 27760.64}, {'field': 'total_amount', 'old_value': 181925.13, 'new_value': 186089.95}, {'field': 'order_count', 'old_value': 665, 'new_value': 678}]
2025-05-23 09:00:37,419 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-23 09:00:37,841 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-23 09:00:37,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144202.0, 'new_value': 146250.0}, {'field': 'offline_amount', 'old_value': 54077.3, 'new_value': 54653.3}, {'field': 'total_amount', 'old_value': 198279.3, 'new_value': 200903.3}, {'field': 'order_count', 'old_value': 1264, 'new_value': 1287}]
2025-05-23 09:00:37,841 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-23 09:00:38,419 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-23 09:00:38,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7854.0, 'new_value': 8213.0}, {'field': 'total_amount', 'old_value': 9347.0, 'new_value': 9706.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 184}]
2025-05-23 09:00:38,419 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-23 09:00:38,825 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-23 09:00:38,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76069.21, 'new_value': 80356.48}, {'field': 'total_amount', 'old_value': 76069.21, 'new_value': 80356.48}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2158}]
2025-05-23 09:00:38,825 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-23 09:00:39,247 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-23 09:00:39,247 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134508.0, 'new_value': 137533.0}, {'field': 'offline_amount', 'old_value': 59720.72, 'new_value': 60165.72}, {'field': 'total_amount', 'old_value': 194228.72, 'new_value': 197698.72}, {'field': 'order_count', 'old_value': 1373, 'new_value': 1403}]
2025-05-23 09:00:39,247 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-23 09:00:39,685 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-23 09:00:39,685 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9190.2, 'new_value': 9570.81}, {'field': 'offline_amount', 'old_value': 150710.32, 'new_value': 156777.79}, {'field': 'total_amount', 'old_value': 159900.52, 'new_value': 166348.6}, {'field': 'order_count', 'old_value': 1740, 'new_value': 1814}]
2025-05-23 09:00:39,685 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-23 09:00:40,107 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-23 09:00:40,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122254.0, 'new_value': 128102.0}, {'field': 'total_amount', 'old_value': 122254.0, 'new_value': 128102.0}, {'field': 'order_count', 'old_value': 3060, 'new_value': 3211}]
2025-05-23 09:00:40,107 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-23 09:00:40,575 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-23 09:00:40,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19479.81, 'new_value': 20688.94}, {'field': 'total_amount', 'old_value': 19479.81, 'new_value': 20688.94}, {'field': 'order_count', 'old_value': 118, 'new_value': 125}]
2025-05-23 09:00:40,575 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-23 09:00:40,966 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-23 09:00:40,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142327.0, 'new_value': 149172.0}, {'field': 'total_amount', 'old_value': 142327.0, 'new_value': 149172.0}, {'field': 'order_count', 'old_value': 5317, 'new_value': 5595}]
2025-05-23 09:00:40,966 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-23 09:00:41,388 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-23 09:00:41,388 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21441.0, 'new_value': 21736.0}, {'field': 'offline_amount', 'old_value': 434362.0, 'new_value': 437027.0}, {'field': 'total_amount', 'old_value': 455803.0, 'new_value': 458763.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 80}]
2025-05-23 09:00:41,388 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-23 09:00:41,919 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-23 09:00:41,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118070.09, 'new_value': 122412.94}, {'field': 'total_amount', 'old_value': 118070.09, 'new_value': 122412.94}, {'field': 'order_count', 'old_value': 4303, 'new_value': 4455}]
2025-05-23 09:00:41,919 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-23 09:00:42,341 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-23 09:00:42,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17531.14, 'new_value': 18329.14}, {'field': 'total_amount', 'old_value': 17531.14, 'new_value': 18329.14}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-23 09:00:42,341 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-23 09:00:42,794 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-23 09:00:42,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50682.0, 'new_value': 51028.0}, {'field': 'total_amount', 'old_value': 50682.0, 'new_value': 51028.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 100}]
2025-05-23 09:00:42,794 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-23 09:00:43,247 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-23 09:00:43,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447045.3, 'new_value': 464588.3}, {'field': 'total_amount', 'old_value': 513871.19, 'new_value': 531414.19}, {'field': 'order_count', 'old_value': 720, 'new_value': 739}]
2025-05-23 09:00:43,247 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-23 09:00:43,638 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-23 09:00:43,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135126.0, 'new_value': 141325.0}, {'field': 'total_amount', 'old_value': 135126.0, 'new_value': 141325.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-23 09:00:43,638 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-23 09:00:44,107 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-23 09:00:44,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1480239.88, 'new_value': 1511705.88}, {'field': 'total_amount', 'old_value': 1533684.98, 'new_value': 1565150.98}, {'field': 'order_count', 'old_value': 2744, 'new_value': 2814}]
2025-05-23 09:00:44,107 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-23 09:00:44,575 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-23 09:00:44,575 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6312.77, 'new_value': 6574.91}, {'field': 'offline_amount', 'old_value': 13797.59, 'new_value': 14568.25}, {'field': 'total_amount', 'old_value': 20110.36, 'new_value': 21143.16}, {'field': 'order_count', 'old_value': 678, 'new_value': 713}]
2025-05-23 09:00:44,591 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-23 09:00:45,044 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-23 09:00:45,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154669.99, 'new_value': 159679.99}, {'field': 'offline_amount', 'old_value': 125550.14, 'new_value': 131386.14}, {'field': 'total_amount', 'old_value': 280220.13, 'new_value': 291066.13}, {'field': 'order_count', 'old_value': 2467, 'new_value': 2588}]
2025-05-23 09:00:45,044 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-23 09:00:45,513 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-23 09:00:45,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38845.0, 'new_value': 46961.0}, {'field': 'total_amount', 'old_value': 38845.0, 'new_value': 46961.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-23 09:00:45,513 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-23 09:00:45,966 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-23 09:00:45,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68567.73, 'new_value': 79045.01}, {'field': 'total_amount', 'old_value': 68571.03, 'new_value': 79048.31}, {'field': 'order_count', 'old_value': 38, 'new_value': 41}]
2025-05-23 09:00:45,966 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-23 09:00:46,419 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-23 09:00:46,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 314603.6, 'new_value': 325802.9}, {'field': 'offline_amount', 'old_value': 82489.2, 'new_value': 87197.2}, {'field': 'total_amount', 'old_value': 397092.8, 'new_value': 413000.1}, {'field': 'order_count', 'old_value': 501, 'new_value': 522}]
2025-05-23 09:00:46,419 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-23 09:00:46,919 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-23 09:00:46,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23470.0, 'new_value': 24236.0}, {'field': 'total_amount', 'old_value': 23470.0, 'new_value': 24236.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-05-23 09:00:46,919 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-23 09:00:47,325 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-23 09:00:47,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65560.5, 'new_value': 70197.5}, {'field': 'offline_amount', 'old_value': 4844.95, 'new_value': 5684.85}, {'field': 'total_amount', 'old_value': 70405.45, 'new_value': 75882.35}, {'field': 'order_count', 'old_value': 213, 'new_value': 227}]
2025-05-23 09:00:47,325 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-23 09:00:47,779 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-23 09:00:47,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60581.24, 'new_value': 62641.86}, {'field': 'total_amount', 'old_value': 60581.24, 'new_value': 62641.86}, {'field': 'order_count', 'old_value': 1715, 'new_value': 1776}]
2025-05-23 09:00:47,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-23 09:00:48,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-23 09:00:48,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372572.22, 'new_value': 376046.57}, {'field': 'total_amount', 'old_value': 372572.22, 'new_value': 376046.57}, {'field': 'order_count', 'old_value': 482, 'new_value': 495}]
2025-05-23 09:00:48,185 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-23 09:00:48,654 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-23 09:00:48,654 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13430.44, 'new_value': 13959.0}, {'field': 'offline_amount', 'old_value': 353578.76, 'new_value': 362596.77}, {'field': 'total_amount', 'old_value': 367009.2, 'new_value': 376555.77}, {'field': 'order_count', 'old_value': 1535, 'new_value': 1584}]
2025-05-23 09:00:48,654 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-23 09:00:49,107 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-23 09:00:49,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73369.0, 'new_value': 76849.0}, {'field': 'offline_amount', 'old_value': 66281.46, 'new_value': 71483.29}, {'field': 'total_amount', 'old_value': 139650.46, 'new_value': 148332.29}, {'field': 'order_count', 'old_value': 172, 'new_value': 178}]
2025-05-23 09:00:49,107 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-23 09:00:49,529 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-23 09:00:49,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9632.0, 'new_value': 9731.0}, {'field': 'total_amount', 'old_value': 9632.0, 'new_value': 9731.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-23 09:00:49,529 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-23 09:00:50,013 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-23 09:00:50,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7025.82, 'new_value': 7235.85}, {'field': 'offline_amount', 'old_value': 25576.0, 'new_value': 25774.0}, {'field': 'total_amount', 'old_value': 32601.82, 'new_value': 33009.85}, {'field': 'order_count', 'old_value': 182, 'new_value': 187}]
2025-05-23 09:00:50,013 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-23 09:00:50,560 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-23 09:00:50,560 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 196.8, 'new_value': 403.2}, {'field': 'offline_amount', 'old_value': 40029.47, 'new_value': 41095.41}, {'field': 'total_amount', 'old_value': 40226.27, 'new_value': 41498.61}, {'field': 'order_count', 'old_value': 342, 'new_value': 350}]
2025-05-23 09:00:50,560 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-23 09:00:51,107 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-23 09:00:51,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4714.0, 'new_value': 4918.0}, {'field': 'offline_amount', 'old_value': 20938.0, 'new_value': 21585.4}, {'field': 'total_amount', 'old_value': 25652.0, 'new_value': 26503.4}, {'field': 'order_count', 'old_value': 1027, 'new_value': 1060}]
2025-05-23 09:00:51,107 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-23 09:00:51,622 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-23 09:00:51,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77849.46, 'new_value': 79176.26}, {'field': 'total_amount', 'old_value': 77849.46, 'new_value': 79176.26}, {'field': 'order_count', 'old_value': 286, 'new_value': 291}]
2025-05-23 09:00:51,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-23 09:00:52,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-23 09:00:52,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173840.5, 'new_value': 175849.5}, {'field': 'total_amount', 'old_value': 173840.5, 'new_value': 175849.5}, {'field': 'order_count', 'old_value': 644, 'new_value': 653}]
2025-05-23 09:00:52,060 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-23 09:00:52,466 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-23 09:00:52,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126975.4, 'new_value': 131377.8}, {'field': 'total_amount', 'old_value': 126975.4, 'new_value': 131377.8}, {'field': 'order_count', 'old_value': 627, 'new_value': 651}]
2025-05-23 09:00:52,466 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-23 09:00:52,904 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-23 09:00:52,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19386.73, 'new_value': 20547.05}, {'field': 'offline_amount', 'old_value': 35271.58, 'new_value': 37626.26}, {'field': 'total_amount', 'old_value': 54658.31, 'new_value': 58173.31}, {'field': 'order_count', 'old_value': 1964, 'new_value': 2089}]
2025-05-23 09:00:52,904 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-23 09:00:53,388 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-23 09:00:53,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61559.0, 'new_value': 63876.0}, {'field': 'total_amount', 'old_value': 63967.0, 'new_value': 66284.0}, {'field': 'order_count', 'old_value': 260, 'new_value': 270}]
2025-05-23 09:00:53,388 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-23 09:00:53,857 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-23 09:00:53,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49729.96, 'new_value': 49994.97}, {'field': 'total_amount', 'old_value': 68720.66, 'new_value': 68985.67}, {'field': 'order_count', 'old_value': 785, 'new_value': 792}]
2025-05-23 09:00:53,857 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-23 09:00:54,294 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-23 09:00:54,294 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84288.0, 'new_value': 87645.6}, {'field': 'offline_amount', 'old_value': 118736.57, 'new_value': 125940.0}, {'field': 'total_amount', 'old_value': 203024.57, 'new_value': 213585.6}, {'field': 'order_count', 'old_value': 1359, 'new_value': 1406}]
2025-05-23 09:00:54,294 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-23 09:00:54,747 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-23 09:00:54,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44123.0, 'new_value': 44492.0}, {'field': 'total_amount', 'old_value': 44472.0, 'new_value': 44841.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 82}]
2025-05-23 09:00:54,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-23 09:00:55,122 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-23 09:00:55,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100813.08, 'new_value': 104604.53}, {'field': 'total_amount', 'old_value': 100813.08, 'new_value': 104604.53}, {'field': 'order_count', 'old_value': 2959, 'new_value': 3081}]
2025-05-23 09:00:55,138 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-23 09:00:55,528 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-23 09:00:55,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105457.07, 'new_value': 116383.38}, {'field': 'offline_amount', 'old_value': 229020.51, 'new_value': 235994.51}, {'field': 'total_amount', 'old_value': 334477.58, 'new_value': 352377.89}, {'field': 'order_count', 'old_value': 3976, 'new_value': 4141}]
2025-05-23 09:00:55,528 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-23 09:00:56,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-23 09:00:56,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 189696.0, 'new_value': 198552.0}, {'field': 'total_amount', 'old_value': 189696.0, 'new_value': 198552.0}, {'field': 'order_count', 'old_value': 15808, 'new_value': 16546}]
2025-05-23 09:00:56,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-23 09:00:56,513 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-23 09:00:56,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9932.0, 'new_value': 11816.2}, {'field': 'total_amount', 'old_value': 9932.0, 'new_value': 11816.2}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-23 09:00:56,513 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-23 09:00:56,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-23 09:00:56,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38117.0, 'new_value': 39580.3}, {'field': 'total_amount', 'old_value': 38117.0, 'new_value': 39580.3}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-23 09:00:56,950 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-23 09:00:57,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-23 09:00:57,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38219.6, 'new_value': 39833.25}, {'field': 'total_amount', 'old_value': 38219.6, 'new_value': 39833.25}, {'field': 'order_count', 'old_value': 1688, 'new_value': 1766}]
2025-05-23 09:00:57,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-23 09:00:57,778 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-23 09:00:57,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114965.25, 'new_value': 131464.35}, {'field': 'total_amount', 'old_value': 208123.05, 'new_value': 224622.15}, {'field': 'order_count', 'old_value': 5468, 'new_value': 5869}]
2025-05-23 09:00:57,778 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-23 09:00:58,294 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-23 09:00:58,294 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62989.73, 'new_value': 65765.28}, {'field': 'offline_amount', 'old_value': 216853.22, 'new_value': 222089.77}, {'field': 'total_amount', 'old_value': 279842.95, 'new_value': 287855.05}, {'field': 'order_count', 'old_value': 3449, 'new_value': 3507}]
2025-05-23 09:00:58,294 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-23 09:00:58,716 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-23 09:00:58,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48083.92, 'new_value': 49928.59}, {'field': 'offline_amount', 'old_value': 28988.41, 'new_value': 30147.59}, {'field': 'total_amount', 'old_value': 77072.33, 'new_value': 80076.18}, {'field': 'order_count', 'old_value': 4191, 'new_value': 4355}]
2025-05-23 09:00:58,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-23 09:00:59,232 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-23 09:00:59,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18579.18, 'new_value': 19097.18}, {'field': 'total_amount', 'old_value': 18579.18, 'new_value': 19097.18}, {'field': 'order_count', 'old_value': 155, 'new_value': 159}]
2025-05-23 09:00:59,232 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-23 09:00:59,700 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-23 09:00:59,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 599111.15, 'new_value': 614128.15}, {'field': 'total_amount', 'old_value': 599111.15, 'new_value': 614128.15}, {'field': 'order_count', 'old_value': 1580, 'new_value': 1639}]
2025-05-23 09:00:59,700 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-23 09:01:00,185 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-23 09:01:00,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365790.1, 'new_value': 371996.84}, {'field': 'total_amount', 'old_value': 365790.1, 'new_value': 371996.84}, {'field': 'order_count', 'old_value': 1310, 'new_value': 1332}]
2025-05-23 09:01:00,185 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-23 09:01:00,638 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-23 09:01:00,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261970.5, 'new_value': 270672.4}, {'field': 'total_amount', 'old_value': 261970.5, 'new_value': 270672.4}, {'field': 'order_count', 'old_value': 6642, 'new_value': 6872}]
2025-05-23 09:01:00,638 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-23 09:01:01,075 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-23 09:01:01,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32631.04, 'new_value': 34273.32}, {'field': 'total_amount', 'old_value': 32631.04, 'new_value': 34273.32}, {'field': 'order_count', 'old_value': 4195, 'new_value': 4429}]
2025-05-23 09:01:01,075 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-23 09:01:01,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-23 09:01:01,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21561.07, 'new_value': 22280.23}, {'field': 'offline_amount', 'old_value': 27469.46, 'new_value': 27980.06}, {'field': 'total_amount', 'old_value': 49030.53, 'new_value': 50260.29}, {'field': 'order_count', 'old_value': 2199, 'new_value': 2268}]
2025-05-23 09:01:01,466 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-23 09:01:01,919 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-23 09:01:01,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70472.0, 'new_value': 74494.0}, {'field': 'total_amount', 'old_value': 75673.0, 'new_value': 79695.0}, {'field': 'order_count', 'old_value': 216, 'new_value': 226}]
2025-05-23 09:01:01,919 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-23 09:01:02,372 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-23 09:01:02,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244304.8, 'new_value': 248364.8}, {'field': 'total_amount', 'old_value': 244304.8, 'new_value': 248364.8}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-05-23 09:01:02,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-23 09:01:02,872 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-23 09:01:02,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379569.88, 'new_value': 392314.88}, {'field': 'total_amount', 'old_value': 379569.88, 'new_value': 392314.88}, {'field': 'order_count', 'old_value': 1898, 'new_value': 1965}]
2025-05-23 09:01:02,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-23 09:01:03,450 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-23 09:01:03,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40013.4, 'new_value': 41689.46}, {'field': 'offline_amount', 'old_value': 47092.57, 'new_value': 47105.15}, {'field': 'total_amount', 'old_value': 87105.97, 'new_value': 88794.61}, {'field': 'order_count', 'old_value': 295, 'new_value': 303}]
2025-05-23 09:01:03,450 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-23 09:01:03,919 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-23 09:01:03,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 277378.92, 'new_value': 283038.92}, {'field': 'offline_amount', 'old_value': 6665.5, 'new_value': 9193.5}, {'field': 'total_amount', 'old_value': 284044.42, 'new_value': 292232.42}, {'field': 'order_count', 'old_value': 2441, 'new_value': 2511}]
2025-05-23 09:01:03,919 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-23 09:01:04,357 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-23 09:01:04,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17798.0, 'new_value': 18538.0}, {'field': 'total_amount', 'old_value': 17798.0, 'new_value': 18538.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 98}]
2025-05-23 09:01:04,357 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-23 09:01:04,841 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-23 09:01:04,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23304.8, 'new_value': 24117.2}, {'field': 'total_amount', 'old_value': 23304.8, 'new_value': 24117.2}, {'field': 'order_count', 'old_value': 640, 'new_value': 657}]
2025-05-23 09:01:04,841 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-23 09:01:05,263 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-23 09:01:05,263 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6298.1, 'new_value': 6507.1}, {'field': 'offline_amount', 'old_value': 32556.65, 'new_value': 33375.75}, {'field': 'total_amount', 'old_value': 38854.75, 'new_value': 39882.85}, {'field': 'order_count', 'old_value': 449, 'new_value': 459}]
2025-05-23 09:01:05,263 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-23 09:01:05,747 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-23 09:01:05,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27063.93, 'new_value': 28915.37}, {'field': 'total_amount', 'old_value': 27063.93, 'new_value': 28915.37}, {'field': 'order_count', 'old_value': 1003, 'new_value': 1068}]
2025-05-23 09:01:05,763 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-23 09:01:06,247 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-23 09:01:06,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 727764.0, 'new_value': 745874.0}, {'field': 'total_amount', 'old_value': 727764.0, 'new_value': 745874.0}, {'field': 'order_count', 'old_value': 3282, 'new_value': 3360}]
2025-05-23 09:01:06,247 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-23 09:01:06,778 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-23 09:01:06,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29062.0, 'new_value': 30061.0}, {'field': 'total_amount', 'old_value': 29062.0, 'new_value': 30061.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-23 09:01:06,778 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-23 09:01:07,247 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-23 09:01:07,247 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8853.8, 'new_value': 8956.61}, {'field': 'total_amount', 'old_value': 24153.8, 'new_value': 24256.61}, {'field': 'order_count', 'old_value': 138, 'new_value': 140}]
2025-05-23 09:01:07,247 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-23 09:01:07,825 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-23 09:01:07,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80207.06, 'new_value': 83077.1}, {'field': 'offline_amount', 'old_value': 94318.03, 'new_value': 96474.43}, {'field': 'total_amount', 'old_value': 174525.09, 'new_value': 179551.53}, {'field': 'order_count', 'old_value': 7029, 'new_value': 7260}]
2025-05-23 09:01:07,825 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-23 09:01:08,263 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-23 09:01:08,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48087.0, 'new_value': 49227.0}, {'field': 'total_amount', 'old_value': 48087.0, 'new_value': 49227.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 106}]
2025-05-23 09:01:08,263 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-23 09:01:08,700 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-23 09:01:08,700 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 283517.58, 'new_value': 289274.12}, {'field': 'offline_amount', 'old_value': 1078327.22, 'new_value': 1107617.98}, {'field': 'total_amount', 'old_value': 1361844.8, 'new_value': 1396892.1}, {'field': 'order_count', 'old_value': 6830, 'new_value': 7021}]
2025-05-23 09:01:08,700 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-23 09:01:09,169 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-23 09:01:09,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33514.0, 'new_value': 34094.0}, {'field': 'total_amount', 'old_value': 33514.0, 'new_value': 34094.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-05-23 09:01:09,169 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-23 09:01:09,607 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-23 09:01:09,607 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49110.45, 'new_value': 50632.77}, {'field': 'offline_amount', 'old_value': 38686.91, 'new_value': 39183.5}, {'field': 'total_amount', 'old_value': 87797.36, 'new_value': 89816.27}, {'field': 'order_count', 'old_value': 1739, 'new_value': 1788}]
2025-05-23 09:01:09,607 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-23 09:01:10,044 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-23 09:01:10,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115880.3, 'new_value': 119823.1}, {'field': 'offline_amount', 'old_value': 92709.8, 'new_value': 97094.1}, {'field': 'total_amount', 'old_value': 208590.1, 'new_value': 216917.2}, {'field': 'order_count', 'old_value': 4915, 'new_value': 5121}]
2025-05-23 09:01:10,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-23 09:01:10,513 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-23 09:01:10,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1930000.0, 'new_value': 1980000.0}, {'field': 'total_amount', 'old_value': 1930000.0, 'new_value': 1980000.0}, {'field': 'order_count', 'old_value': 278, 'new_value': 279}]
2025-05-23 09:01:10,513 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-23 09:01:10,872 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-23 09:01:10,872 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 192970.24, 'new_value': 211506.21}, {'field': 'offline_amount', 'old_value': 645817.26, 'new_value': 653712.43}, {'field': 'total_amount', 'old_value': 838787.5, 'new_value': 865218.64}, {'field': 'order_count', 'old_value': 4962, 'new_value': 5158}]
2025-05-23 09:01:10,872 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-23 09:01:11,310 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-23 09:01:11,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27431.33, 'new_value': 29375.43}, {'field': 'offline_amount', 'old_value': 299033.95, 'new_value': 306256.35}, {'field': 'total_amount', 'old_value': 326465.28, 'new_value': 335631.78}, {'field': 'order_count', 'old_value': 9499, 'new_value': 9542}]
2025-05-23 09:01:11,310 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-23 09:01:11,763 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-23 09:01:11,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 479882.0, 'new_value': 497976.0}, {'field': 'total_amount', 'old_value': 479882.0, 'new_value': 497976.0}, {'field': 'order_count', 'old_value': 428, 'new_value': 446}]
2025-05-23 09:01:11,763 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-23 09:01:12,200 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-23 09:01:12,200 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 224918.52, 'new_value': 230300.02}, {'field': 'offline_amount', 'old_value': 127595.29, 'new_value': 132150.09}, {'field': 'total_amount', 'old_value': 352513.81, 'new_value': 362450.11}, {'field': 'order_count', 'old_value': 2930, 'new_value': 3046}]
2025-05-23 09:01:12,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-23 09:01:12,685 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-23 09:01:12,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 490000.0, 'new_value': 495000.0}, {'field': 'total_amount', 'old_value': 490000.0, 'new_value': 495000.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 146}]
2025-05-23 09:01:12,685 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-23 09:01:13,122 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-23 09:01:13,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 470000.0, 'new_value': 475000.0}, {'field': 'total_amount', 'old_value': 470000.0, 'new_value': 475000.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 145}]
2025-05-23 09:01:13,138 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-23 09:01:13,528 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-23 09:01:13,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3048674.0, 'new_value': 3098674.0}, {'field': 'total_amount', 'old_value': 3048674.0, 'new_value': 3098674.0}, {'field': 'order_count', 'old_value': 298, 'new_value': 299}]
2025-05-23 09:01:13,528 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-23 09:01:14,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-23 09:01:14,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79006.0, 'new_value': 82629.0}, {'field': 'offline_amount', 'old_value': 918591.0, 'new_value': 959768.0}, {'field': 'total_amount', 'old_value': 997597.0, 'new_value': 1042397.0}, {'field': 'order_count', 'old_value': 24999, 'new_value': 26400}]
2025-05-23 09:01:14,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-23 09:01:14,450 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-23 09:01:14,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332984.65, 'new_value': 340150.88}, {'field': 'total_amount', 'old_value': 346495.13, 'new_value': 353661.36}, {'field': 'order_count', 'old_value': 1127, 'new_value': 1154}]
2025-05-23 09:01:14,450 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-23 09:01:14,794 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-23 09:01:14,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32271.0, 'new_value': 46995.0}, {'field': 'offline_amount', 'old_value': 234834.0, 'new_value': 238772.0}, {'field': 'total_amount', 'old_value': 267105.0, 'new_value': 285767.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 251}]
2025-05-23 09:01:14,794 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-23 09:01:15,200 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-23 09:01:15,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55122.1, 'new_value': 62301.1}, {'field': 'total_amount', 'old_value': 55927.1, 'new_value': 63106.1}, {'field': 'order_count', 'old_value': 16292, 'new_value': 16294}]
2025-05-23 09:01:15,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-23 09:01:16,606 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-23 09:01:16,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147559.42, 'new_value': 150944.99}, {'field': 'total_amount', 'old_value': 147559.42, 'new_value': 150944.99}, {'field': 'order_count', 'old_value': 7630, 'new_value': 7730}]
2025-05-23 09:01:16,606 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-23 09:01:17,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-23 09:01:17,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139283.3, 'new_value': 142892.0}, {'field': 'total_amount', 'old_value': 139283.3, 'new_value': 142892.0}, {'field': 'order_count', 'old_value': 616, 'new_value': 634}]
2025-05-23 09:01:17,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-23 09:01:17,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-23 09:01:17,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119149.4, 'new_value': 120505.0}, {'field': 'total_amount', 'old_value': 119149.4, 'new_value': 120505.0}, {'field': 'order_count', 'old_value': 3284, 'new_value': 3322}]
2025-05-23 09:01:17,466 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-23 09:01:17,903 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-23 09:01:17,903 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8433.0, 'new_value': 8631.0}, {'field': 'total_amount', 'old_value': 19218.0, 'new_value': 19416.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 105}]
2025-05-23 09:01:17,903 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-23 09:01:18,341 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-23 09:01:18,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1673074.0, 'new_value': 1706881.0}, {'field': 'total_amount', 'old_value': 1673074.0, 'new_value': 1706881.0}, {'field': 'order_count', 'old_value': 6549, 'new_value': 6719}]
2025-05-23 09:01:18,341 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-23 09:01:18,794 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-23 09:01:18,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88749.5, 'new_value': 91093.5}, {'field': 'total_amount', 'old_value': 88749.5, 'new_value': 91093.5}, {'field': 'order_count', 'old_value': 426, 'new_value': 437}]
2025-05-23 09:01:18,794 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-23 09:01:19,200 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-23 09:01:19,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253017.76, 'new_value': 254581.36}, {'field': 'total_amount', 'old_value': 253017.76, 'new_value': 254581.36}, {'field': 'order_count', 'old_value': 1414, 'new_value': 1426}]
2025-05-23 09:01:19,200 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-23 09:01:19,638 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-23 09:01:19,638 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51852.47, 'new_value': 52669.92}, {'field': 'offline_amount', 'old_value': 38719.28, 'new_value': 39957.55}, {'field': 'total_amount', 'old_value': 90571.75, 'new_value': 92627.47}, {'field': 'order_count', 'old_value': 7623, 'new_value': 7784}]
2025-05-23 09:01:19,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-23 09:01:20,106 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-23 09:01:20,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83103.56, 'new_value': 85413.56}, {'field': 'total_amount', 'old_value': 83103.56, 'new_value': 85413.56}, {'field': 'order_count', 'old_value': 4281, 'new_value': 4398}]
2025-05-23 09:01:20,122 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-23 09:01:20,513 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-23 09:01:20,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1874.54, 'new_value': 2281.11}, {'field': 'offline_amount', 'old_value': 74351.15, 'new_value': 75798.15}, {'field': 'total_amount', 'old_value': 76225.69, 'new_value': 78079.26}, {'field': 'order_count', 'old_value': 359, 'new_value': 371}]
2025-05-23 09:01:20,513 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-23 09:01:20,919 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-23 09:01:20,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4355.0, 'new_value': 4475.0}, {'field': 'total_amount', 'old_value': 10783.0, 'new_value': 10903.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 105}]
2025-05-23 09:01:20,919 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-23 09:01:21,372 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-23 09:01:21,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45660.78, 'new_value': 48176.53}, {'field': 'offline_amount', 'old_value': 44123.35, 'new_value': 47544.18}, {'field': 'total_amount', 'old_value': 89784.13, 'new_value': 95720.71}, {'field': 'order_count', 'old_value': 4571, 'new_value': 4845}]
2025-05-23 09:01:21,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-23 09:01:21,903 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-23 09:01:21,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 824258.0, 'new_value': 850821.0}, {'field': 'total_amount', 'old_value': 824258.0, 'new_value': 850821.0}, {'field': 'order_count', 'old_value': 967, 'new_value': 998}]
2025-05-23 09:01:21,903 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-23 09:01:22,325 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-23 09:01:22,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170954.3, 'new_value': 180097.3}, {'field': 'total_amount', 'old_value': 176904.6, 'new_value': 186047.6}, {'field': 'order_count', 'old_value': 330, 'new_value': 344}]
2025-05-23 09:01:22,325 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-23 09:01:22,825 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-23 09:01:22,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36700.85, 'new_value': 38209.85}, {'field': 'offline_amount', 'old_value': 99337.0, 'new_value': 104026.0}, {'field': 'total_amount', 'old_value': 136037.85, 'new_value': 142235.85}, {'field': 'order_count', 'old_value': 1496, 'new_value': 1579}]
2025-05-23 09:01:22,825 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-23 09:01:23,310 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-23 09:01:23,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115700.0, 'new_value': 121226.0}, {'field': 'offline_amount', 'old_value': 80852.0, 'new_value': 82724.0}, {'field': 'total_amount', 'old_value': 196552.0, 'new_value': 203950.0}, {'field': 'order_count', 'old_value': 2542, 'new_value': 2661}]
2025-05-23 09:01:23,310 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-23 09:01:23,778 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-23 09:01:23,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20664.67, 'new_value': 20702.67}, {'field': 'total_amount', 'old_value': 28231.07, 'new_value': 28269.07}, {'field': 'order_count', 'old_value': 285, 'new_value': 286}]
2025-05-23 09:01:23,778 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-23 09:01:24,169 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-23 09:01:24,169 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9010.48, 'new_value': 9268.5}, {'field': 'total_amount', 'old_value': 145446.48, 'new_value': 145704.5}, {'field': 'order_count', 'old_value': 65, 'new_value': 66}]
2025-05-23 09:01:24,169 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-23 09:01:24,606 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-23 09:01:24,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186187.5, 'new_value': 191466.5}, {'field': 'total_amount', 'old_value': 186187.5, 'new_value': 191466.5}, {'field': 'order_count', 'old_value': 922, 'new_value': 947}]
2025-05-23 09:01:24,606 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-23 09:01:25,231 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-23 09:01:25,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44941.14, 'new_value': 46596.14}, {'field': 'total_amount', 'old_value': 49105.14, 'new_value': 50760.14}, {'field': 'order_count', 'old_value': 432, 'new_value': 447}]
2025-05-23 09:01:25,231 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-23 09:01:25,653 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-23 09:01:25,653 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166463.3, 'new_value': 171084.3}, {'field': 'total_amount', 'old_value': 166463.3, 'new_value': 171084.3}, {'field': 'order_count', 'old_value': 623, 'new_value': 641}]
2025-05-23 09:01:25,653 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-23 09:01:26,106 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-23 09:01:26,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187066.0, 'new_value': 190291.0}, {'field': 'total_amount', 'old_value': 187066.0, 'new_value': 190291.0}, {'field': 'order_count', 'old_value': 416, 'new_value': 429}]
2025-05-23 09:01:26,106 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-23 09:01:26,606 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-23 09:01:26,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246508.0, 'new_value': 256367.0}, {'field': 'total_amount', 'old_value': 246508.0, 'new_value': 256367.0}, {'field': 'order_count', 'old_value': 305, 'new_value': 317}]
2025-05-23 09:01:26,606 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-23 09:01:27,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-23 09:01:27,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136669.7, 'new_value': 140563.7}, {'field': 'total_amount', 'old_value': 280324.48, 'new_value': 284218.48}]
2025-05-23 09:01:27,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-23 09:01:27,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-23 09:01:27,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166647.0, 'new_value': 171003.0}, {'field': 'offline_amount', 'old_value': 150557.0, 'new_value': 159055.0}, {'field': 'total_amount', 'old_value': 317204.0, 'new_value': 330058.0}, {'field': 'order_count', 'old_value': 860, 'new_value': 887}]
2025-05-23 09:01:27,466 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-23 09:01:27,888 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-23 09:01:27,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 632456.42, 'new_value': 644327.26}, {'field': 'total_amount', 'old_value': 632456.42, 'new_value': 644327.26}, {'field': 'order_count', 'old_value': 3549, 'new_value': 3630}]
2025-05-23 09:01:27,888 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-23 09:01:28,450 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-23 09:01:28,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120490.54, 'new_value': 123937.08}, {'field': 'total_amount', 'old_value': 120490.54, 'new_value': 123937.08}, {'field': 'order_count', 'old_value': 8310, 'new_value': 8548}]
2025-05-23 09:01:28,450 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-23 09:01:28,966 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-23 09:01:28,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 389876.0, 'new_value': 394178.0}, {'field': 'total_amount', 'old_value': 389876.0, 'new_value': 394178.0}, {'field': 'order_count', 'old_value': 8827, 'new_value': 8923}]
2025-05-23 09:01:28,966 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-23 09:01:29,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-23 09:01:29,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85657.0, 'new_value': 87795.0}, {'field': 'total_amount', 'old_value': 85657.0, 'new_value': 87795.0}, {'field': 'order_count', 'old_value': 5773, 'new_value': 5946}]
2025-05-23 09:01:29,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-23 09:01:29,872 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-23 09:01:29,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122382.0, 'new_value': 128895.0}, {'field': 'total_amount', 'old_value': 122382.0, 'new_value': 128895.0}, {'field': 'order_count', 'old_value': 9037, 'new_value': 9540}]
2025-05-23 09:01:29,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-23 09:01:30,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-23 09:01:30,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30727.0, 'new_value': 30745.0}, {'field': 'total_amount', 'old_value': 30727.0, 'new_value': 30745.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-23 09:01:30,294 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-23 09:01:30,763 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-23 09:01:30,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21252.93, 'new_value': 22988.21}, {'field': 'total_amount', 'old_value': 21252.93, 'new_value': 22988.21}, {'field': 'order_count', 'old_value': 97, 'new_value': 101}]
2025-05-23 09:01:30,763 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-23 09:01:31,153 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-23 09:01:31,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279876.0, 'new_value': 289125.0}, {'field': 'total_amount', 'old_value': 279876.0, 'new_value': 289125.0}, {'field': 'order_count', 'old_value': 6077, 'new_value': 6295}]
2025-05-23 09:01:31,153 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-23 09:01:31,778 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-23 09:01:31,778 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85352.16, 'new_value': 89906.25}, {'field': 'offline_amount', 'old_value': 354453.3, 'new_value': 362446.5}, {'field': 'total_amount', 'old_value': 439805.46, 'new_value': 452352.75}, {'field': 'order_count', 'old_value': 3111, 'new_value': 3242}]
2025-05-23 09:01:31,778 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-23 09:01:32,247 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-23 09:01:32,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151695.8, 'new_value': 161508.8}, {'field': 'total_amount', 'old_value': 151695.8, 'new_value': 161508.8}, {'field': 'order_count', 'old_value': 5299, 'new_value': 5671}]
2025-05-23 09:01:32,247 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-23 09:01:32,716 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-23 09:01:32,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264897.2, 'new_value': 272998.99}, {'field': 'total_amount', 'old_value': 264897.2, 'new_value': 272998.99}, {'field': 'order_count', 'old_value': 735, 'new_value': 754}]
2025-05-23 09:01:32,716 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-23 09:01:33,294 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-23 09:01:33,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115821.0, 'new_value': 116559.0}, {'field': 'total_amount', 'old_value': 115821.0, 'new_value': 116559.0}, {'field': 'order_count', 'old_value': 471, 'new_value': 474}]
2025-05-23 09:01:33,294 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-23 09:01:33,763 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-23 09:01:33,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13152.0, 'new_value': 16440.0}, {'field': 'total_amount', 'old_value': 28740.0, 'new_value': 32028.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-23 09:01:33,763 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-23 09:01:34,153 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-23 09:01:34,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42849.0, 'new_value': 45749.0}, {'field': 'total_amount', 'old_value': 42849.0, 'new_value': 45749.0}, {'field': 'order_count', 'old_value': 826, 'new_value': 892}]
2025-05-23 09:01:34,153 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-23 09:01:34,560 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-23 09:01:34,560 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23188.14, 'new_value': 26035.99}, {'field': 'offline_amount', 'old_value': 47810.29, 'new_value': 49019.33}, {'field': 'total_amount', 'old_value': 70998.43, 'new_value': 75055.32}, {'field': 'order_count', 'old_value': 321, 'new_value': 339}]
2025-05-23 09:01:34,560 - INFO - 日期 2025-05 处理完成 - 更新: 139 条，插入: 0 条，错误: 0 条
2025-05-23 09:01:34,560 - INFO - 数据同步完成！更新: 139 条，插入: 0 条，错误: 0 条
2025-05-23 09:01:34,560 - INFO - =================同步完成====================
2025-05-23 12:00:02,163 - INFO - =================使用默认全量同步=============
2025-05-23 12:00:03,600 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 12:00:03,600 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 12:00:03,631 - INFO - 开始处理日期: 2025-01
2025-05-23 12:00:03,631 - INFO - Request Parameters - Page 1:
2025-05-23 12:00:03,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:03,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:04,959 - INFO - Response - Page 1:
2025-05-23 12:00:05,163 - INFO - 第 1 页获取到 100 条记录
2025-05-23 12:00:05,163 - INFO - Request Parameters - Page 2:
2025-05-23 12:00:05,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:05,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:05,725 - INFO - Response - Page 2:
2025-05-23 12:00:05,928 - INFO - 第 2 页获取到 100 条记录
2025-05-23 12:00:05,928 - INFO - Request Parameters - Page 3:
2025-05-23 12:00:05,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:05,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:06,444 - INFO - Response - Page 3:
2025-05-23 12:00:06,647 - INFO - 第 3 页获取到 100 条记录
2025-05-23 12:00:06,647 - INFO - Request Parameters - Page 4:
2025-05-23 12:00:06,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:06,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:07,178 - INFO - Response - Page 4:
2025-05-23 12:00:07,381 - INFO - 第 4 页获取到 100 条记录
2025-05-23 12:00:07,381 - INFO - Request Parameters - Page 5:
2025-05-23 12:00:07,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:07,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:07,897 - INFO - Response - Page 5:
2025-05-23 12:00:08,100 - INFO - 第 5 页获取到 100 条记录
2025-05-23 12:00:08,100 - INFO - Request Parameters - Page 6:
2025-05-23 12:00:08,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:08,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:08,553 - INFO - Response - Page 6:
2025-05-23 12:00:08,756 - INFO - 第 6 页获取到 100 条记录
2025-05-23 12:00:08,756 - INFO - Request Parameters - Page 7:
2025-05-23 12:00:08,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:08,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:09,241 - INFO - Response - Page 7:
2025-05-23 12:00:09,444 - INFO - 第 7 页获取到 82 条记录
2025-05-23 12:00:09,444 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 12:00:09,444 - INFO - 获取到 682 条表单数据
2025-05-23 12:00:09,444 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 12:00:09,459 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 12:00:09,459 - INFO - 开始处理日期: 2025-02
2025-05-23 12:00:09,459 - INFO - Request Parameters - Page 1:
2025-05-23 12:00:09,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:09,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:09,991 - INFO - Response - Page 1:
2025-05-23 12:00:10,194 - INFO - 第 1 页获取到 100 条记录
2025-05-23 12:00:10,194 - INFO - Request Parameters - Page 2:
2025-05-23 12:00:10,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:10,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:10,631 - INFO - Response - Page 2:
2025-05-23 12:00:10,834 - INFO - 第 2 页获取到 100 条记录
2025-05-23 12:00:10,834 - INFO - Request Parameters - Page 3:
2025-05-23 12:00:10,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:10,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:11,381 - INFO - Response - Page 3:
2025-05-23 12:00:11,584 - INFO - 第 3 页获取到 100 条记录
2025-05-23 12:00:11,584 - INFO - Request Parameters - Page 4:
2025-05-23 12:00:11,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:11,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:12,069 - INFO - Response - Page 4:
2025-05-23 12:00:12,272 - INFO - 第 4 页获取到 100 条记录
2025-05-23 12:00:12,272 - INFO - Request Parameters - Page 5:
2025-05-23 12:00:12,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:12,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:12,944 - INFO - Response - Page 5:
2025-05-23 12:00:13,147 - INFO - 第 5 页获取到 100 条记录
2025-05-23 12:00:13,147 - INFO - Request Parameters - Page 6:
2025-05-23 12:00:13,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:13,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:13,662 - INFO - Response - Page 6:
2025-05-23 12:00:13,866 - INFO - 第 6 页获取到 100 条记录
2025-05-23 12:00:13,866 - INFO - Request Parameters - Page 7:
2025-05-23 12:00:13,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:13,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:14,334 - INFO - Response - Page 7:
2025-05-23 12:00:14,538 - INFO - 第 7 页获取到 70 条记录
2025-05-23 12:00:14,538 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 12:00:14,538 - INFO - 获取到 670 条表单数据
2025-05-23 12:00:14,538 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 12:00:14,553 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 12:00:14,553 - INFO - 开始处理日期: 2025-03
2025-05-23 12:00:14,553 - INFO - Request Parameters - Page 1:
2025-05-23 12:00:14,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:14,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:15,069 - INFO - Response - Page 1:
2025-05-23 12:00:15,272 - INFO - 第 1 页获取到 100 条记录
2025-05-23 12:00:15,272 - INFO - Request Parameters - Page 2:
2025-05-23 12:00:15,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:15,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:15,741 - INFO - Response - Page 2:
2025-05-23 12:00:15,944 - INFO - 第 2 页获取到 100 条记录
2025-05-23 12:00:15,944 - INFO - Request Parameters - Page 3:
2025-05-23 12:00:15,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:15,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:16,569 - INFO - Response - Page 3:
2025-05-23 12:00:16,772 - INFO - 第 3 页获取到 100 条记录
2025-05-23 12:00:16,772 - INFO - Request Parameters - Page 4:
2025-05-23 12:00:16,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:16,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:17,241 - INFO - Response - Page 4:
2025-05-23 12:00:17,444 - INFO - 第 4 页获取到 100 条记录
2025-05-23 12:00:17,444 - INFO - Request Parameters - Page 5:
2025-05-23 12:00:17,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:17,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:17,897 - INFO - Response - Page 5:
2025-05-23 12:00:18,100 - INFO - 第 5 页获取到 100 条记录
2025-05-23 12:00:18,100 - INFO - Request Parameters - Page 6:
2025-05-23 12:00:18,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:18,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:18,569 - INFO - Response - Page 6:
2025-05-23 12:00:18,772 - INFO - 第 6 页获取到 100 条记录
2025-05-23 12:00:18,772 - INFO - Request Parameters - Page 7:
2025-05-23 12:00:18,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:18,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:19,241 - INFO - Response - Page 7:
2025-05-23 12:00:19,444 - INFO - 第 7 页获取到 61 条记录
2025-05-23 12:00:19,444 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 12:00:19,444 - INFO - 获取到 661 条表单数据
2025-05-23 12:00:19,444 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 12:00:19,459 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 12:00:19,459 - INFO - 开始处理日期: 2025-04
2025-05-23 12:00:19,459 - INFO - Request Parameters - Page 1:
2025-05-23 12:00:19,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:19,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:20,022 - INFO - Response - Page 1:
2025-05-23 12:00:20,225 - INFO - 第 1 页获取到 100 条记录
2025-05-23 12:00:20,225 - INFO - Request Parameters - Page 2:
2025-05-23 12:00:20,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:20,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:20,850 - INFO - Response - Page 2:
2025-05-23 12:00:21,053 - INFO - 第 2 页获取到 100 条记录
2025-05-23 12:00:21,053 - INFO - Request Parameters - Page 3:
2025-05-23 12:00:21,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:21,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:21,537 - INFO - Response - Page 3:
2025-05-23 12:00:21,741 - INFO - 第 3 页获取到 100 条记录
2025-05-23 12:00:21,741 - INFO - Request Parameters - Page 4:
2025-05-23 12:00:21,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:21,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:22,209 - INFO - Response - Page 4:
2025-05-23 12:00:22,412 - INFO - 第 4 页获取到 100 条记录
2025-05-23 12:00:22,412 - INFO - Request Parameters - Page 5:
2025-05-23 12:00:22,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:22,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:22,897 - INFO - Response - Page 5:
2025-05-23 12:00:23,100 - INFO - 第 5 页获取到 100 条记录
2025-05-23 12:00:23,100 - INFO - Request Parameters - Page 6:
2025-05-23 12:00:23,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:23,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:23,631 - INFO - Response - Page 6:
2025-05-23 12:00:23,834 - INFO - 第 6 页获取到 100 条记录
2025-05-23 12:00:23,834 - INFO - Request Parameters - Page 7:
2025-05-23 12:00:23,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:23,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:24,350 - INFO - Response - Page 7:
2025-05-23 12:00:24,553 - INFO - 第 7 页获取到 56 条记录
2025-05-23 12:00:24,553 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 12:00:24,553 - INFO - 获取到 656 条表单数据
2025-05-23 12:00:24,553 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 12:00:24,569 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 12:00:24,569 - INFO - 开始处理日期: 2025-05
2025-05-23 12:00:24,569 - INFO - Request Parameters - Page 1:
2025-05-23 12:00:24,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:24,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:25,162 - INFO - Response - Page 1:
2025-05-23 12:00:25,366 - INFO - 第 1 页获取到 100 条记录
2025-05-23 12:00:25,366 - INFO - Request Parameters - Page 2:
2025-05-23 12:00:25,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:25,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:25,881 - INFO - Response - Page 2:
2025-05-23 12:00:26,084 - INFO - 第 2 页获取到 100 条记录
2025-05-23 12:00:26,084 - INFO - Request Parameters - Page 3:
2025-05-23 12:00:26,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:26,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:26,616 - INFO - Response - Page 3:
2025-05-23 12:00:26,819 - INFO - 第 3 页获取到 100 条记录
2025-05-23 12:00:26,819 - INFO - Request Parameters - Page 4:
2025-05-23 12:00:26,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:26,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:27,366 - INFO - Response - Page 4:
2025-05-23 12:00:27,569 - INFO - 第 4 页获取到 100 条记录
2025-05-23 12:00:27,569 - INFO - Request Parameters - Page 5:
2025-05-23 12:00:27,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:27,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:28,116 - INFO - Response - Page 5:
2025-05-23 12:00:28,319 - INFO - 第 5 页获取到 100 条记录
2025-05-23 12:00:28,319 - INFO - Request Parameters - Page 6:
2025-05-23 12:00:28,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:28,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:28,866 - INFO - Response - Page 6:
2025-05-23 12:00:29,069 - INFO - 第 6 页获取到 100 条记录
2025-05-23 12:00:29,069 - INFO - Request Parameters - Page 7:
2025-05-23 12:00:29,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:00:29,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:00:29,412 - INFO - Response - Page 7:
2025-05-23 12:00:29,616 - INFO - 第 7 页获取到 28 条记录
2025-05-23 12:00:29,616 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 12:00:29,616 - INFO - 获取到 628 条表单数据
2025-05-23 12:00:29,616 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 12:00:29,631 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-23 12:00:30,069 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-23 12:00:30,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8400000.0, 'new_value': 8700000.0}, {'field': 'total_amount', 'old_value': 8500000.0, 'new_value': 8800000.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-05-23 12:00:30,069 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-23 12:00:30,553 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-23 12:00:30,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154655.94, 'new_value': 165047.3}, {'field': 'total_amount', 'old_value': 154655.94, 'new_value': 165047.3}, {'field': 'order_count', 'old_value': 5967, 'new_value': 6282}]
2025-05-23 12:00:30,553 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-23 12:00:31,037 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-23 12:00:31,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 615746.98, 'new_value': 640303.98}, {'field': 'total_amount', 'old_value': 615746.98, 'new_value': 640303.98}, {'field': 'order_count', 'old_value': 1864, 'new_value': 1956}]
2025-05-23 12:00:31,037 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-23 12:00:31,491 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-23 12:00:31,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22614.9, 'new_value': 22643.9}, {'field': 'total_amount', 'old_value': 22614.9, 'new_value': 22643.9}, {'field': 'order_count', 'old_value': 158, 'new_value': 159}]
2025-05-23 12:00:31,491 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-23 12:00:31,928 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-23 12:00:31,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33917.0, 'new_value': 38735.87}, {'field': 'total_amount', 'old_value': 33917.0, 'new_value': 38735.87}, {'field': 'order_count', 'old_value': 6610, 'new_value': 7600}]
2025-05-23 12:00:31,928 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-23 12:00:32,366 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-23 12:00:32,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53459.0, 'new_value': 54784.0}, {'field': 'total_amount', 'old_value': 56356.0, 'new_value': 57681.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 31}]
2025-05-23 12:00:32,366 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-23 12:00:32,803 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-23 12:00:32,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57765.62, 'new_value': 61698.71}, {'field': 'total_amount', 'old_value': 62354.34, 'new_value': 66287.43}, {'field': 'order_count', 'old_value': 2228, 'new_value': 2373}]
2025-05-23 12:00:32,803 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-23 12:00:33,241 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-23 12:00:33,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27418.58, 'new_value': 29244.61}, {'field': 'offline_amount', 'old_value': 13595.93, 'new_value': 14274.17}, {'field': 'total_amount', 'old_value': 41014.51, 'new_value': 43518.78}, {'field': 'order_count', 'old_value': 2115, 'new_value': 2237}]
2025-05-23 12:00:33,241 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-23 12:00:33,678 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-23 12:00:33,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49089.78, 'new_value': 49129.78}, {'field': 'total_amount', 'old_value': 49089.78, 'new_value': 49129.78}, {'field': 'order_count', 'old_value': 106, 'new_value': 107}]
2025-05-23 12:00:33,678 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-23 12:00:34,147 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-23 12:00:34,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 351507.0, 'new_value': 366612.0}, {'field': 'offline_amount', 'old_value': 288207.0, 'new_value': 296720.0}, {'field': 'total_amount', 'old_value': 639714.0, 'new_value': 663332.0}, {'field': 'order_count', 'old_value': 663, 'new_value': 698}]
2025-05-23 12:00:34,147 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-23 12:00:34,600 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-23 12:00:34,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26339.93, 'new_value': 28972.95}, {'field': 'offline_amount', 'old_value': 78356.33, 'new_value': 79972.76}, {'field': 'total_amount', 'old_value': 104696.26, 'new_value': 108945.71}, {'field': 'order_count', 'old_value': 2402, 'new_value': 2514}]
2025-05-23 12:00:34,600 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-23 12:00:35,084 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-23 12:00:35,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41560.0, 'new_value': 45500.0}, {'field': 'total_amount', 'old_value': 45680.0, 'new_value': 49620.0}, {'field': 'order_count', 'old_value': 451, 'new_value': 474}]
2025-05-23 12:00:35,084 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-23 12:00:35,537 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-23 12:00:35,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18821.8, 'new_value': 20186.1}, {'field': 'total_amount', 'old_value': 19813.8, 'new_value': 21178.1}, {'field': 'order_count', 'old_value': 1460, 'new_value': 2460}]
2025-05-23 12:00:35,537 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-23 12:00:36,022 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-23 12:00:36,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44532.0, 'new_value': 60332.0}, {'field': 'total_amount', 'old_value': 44532.0, 'new_value': 60332.0}]
2025-05-23 12:00:36,022 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-23 12:00:36,569 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-23 12:00:36,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42943.1, 'new_value': 46724.3}, {'field': 'total_amount', 'old_value': 46903.1, 'new_value': 50684.3}, {'field': 'order_count', 'old_value': 336, 'new_value': 350}]
2025-05-23 12:00:36,569 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-23 12:00:37,131 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-23 12:00:37,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58212.0, 'new_value': 58710.0}, {'field': 'total_amount', 'old_value': 58212.0, 'new_value': 58710.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 98}]
2025-05-23 12:00:37,131 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-23 12:00:37,553 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-23 12:00:37,553 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3588.73, 'new_value': 3738.63}, {'field': 'offline_amount', 'old_value': 70264.43, 'new_value': 72859.77}, {'field': 'total_amount', 'old_value': 73853.16, 'new_value': 76598.4}, {'field': 'order_count', 'old_value': 2883, 'new_value': 2995}]
2025-05-23 12:00:37,553 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-23 12:00:37,975 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-23 12:00:37,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41580.0, 'new_value': 43957.0}, {'field': 'offline_amount', 'old_value': 156840.98, 'new_value': 162408.98}, {'field': 'total_amount', 'old_value': 198420.98, 'new_value': 206365.98}, {'field': 'order_count', 'old_value': 1336, 'new_value': 1393}]
2025-05-23 12:00:37,975 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-23 12:00:38,412 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-23 12:00:38,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11447.37, 'new_value': 11923.23}, {'field': 'offline_amount', 'old_value': 31726.64, 'new_value': 33077.69}, {'field': 'total_amount', 'old_value': 43174.01, 'new_value': 45000.92}, {'field': 'order_count', 'old_value': 771, 'new_value': 816}]
2025-05-23 12:00:38,412 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-23 12:00:38,912 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-23 12:00:38,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202378.88, 'new_value': 210081.07}, {'field': 'offline_amount', 'old_value': 15460.15, 'new_value': 16122.15}, {'field': 'total_amount', 'old_value': 217839.03, 'new_value': 226203.22}, {'field': 'order_count', 'old_value': 4715, 'new_value': 4994}]
2025-05-23 12:00:38,912 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-23 12:00:39,350 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-23 12:00:39,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 671486.9, 'new_value': 699990.5}, {'field': 'total_amount', 'old_value': 714123.1, 'new_value': 742626.7}, {'field': 'order_count', 'old_value': 78, 'new_value': 83}]
2025-05-23 12:00:39,350 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-23 12:00:39,740 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-23 12:00:39,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45197.6, 'new_value': 45255.6}, {'field': 'total_amount', 'old_value': 60592.3, 'new_value': 60650.3}, {'field': 'order_count', 'old_value': 643, 'new_value': 644}]
2025-05-23 12:00:39,740 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-23 12:00:40,209 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-23 12:00:40,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21341.07, 'new_value': 23140.12}, {'field': 'offline_amount', 'old_value': 90747.25, 'new_value': 95613.86}, {'field': 'total_amount', 'old_value': 112088.32, 'new_value': 118753.98}, {'field': 'order_count', 'old_value': 1522, 'new_value': 1616}]
2025-05-23 12:00:40,209 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-23 12:00:40,615 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-23 12:00:40,615 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54587.0, 'new_value': 59045.0}, {'field': 'offline_amount', 'old_value': 70277.0, 'new_value': 76807.0}, {'field': 'total_amount', 'old_value': 124864.0, 'new_value': 135852.0}, {'field': 'order_count', 'old_value': 1647, 'new_value': 1825}]
2025-05-23 12:00:40,615 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-23 12:00:41,037 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-23 12:00:41,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32273.5, 'new_value': 43903.5}, {'field': 'total_amount', 'old_value': 32273.5, 'new_value': 43903.5}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-23 12:00:41,037 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-23 12:00:41,475 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-23 12:00:41,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146085.18, 'new_value': 149545.79}, {'field': 'total_amount', 'old_value': 146085.18, 'new_value': 149545.79}, {'field': 'order_count', 'old_value': 210, 'new_value': 223}]
2025-05-23 12:00:41,475 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-23 12:00:42,006 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-23 12:00:42,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80373.85, 'new_value': 82954.29}, {'field': 'total_amount', 'old_value': 80373.85, 'new_value': 82954.29}, {'field': 'order_count', 'old_value': 3064, 'new_value': 3181}]
2025-05-23 12:00:42,006 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-23 12:00:42,506 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-23 12:00:42,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11323.0, 'new_value': 12019.0}, {'field': 'total_amount', 'old_value': 11323.0, 'new_value': 12019.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-23 12:00:42,506 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-23 12:00:42,897 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-23 12:00:42,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80680.18, 'new_value': 85992.7}, {'field': 'offline_amount', 'old_value': 48357.16, 'new_value': 49229.58}, {'field': 'total_amount', 'old_value': 129037.34, 'new_value': 135222.28}, {'field': 'order_count', 'old_value': 7258, 'new_value': 7599}]
2025-05-23 12:00:42,897 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-23 12:00:43,647 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-23 12:00:43,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40240.27, 'new_value': 42365.44}, {'field': 'offline_amount', 'old_value': 43413.88, 'new_value': 44945.51}, {'field': 'total_amount', 'old_value': 83654.15, 'new_value': 87310.95}, {'field': 'order_count', 'old_value': 4373, 'new_value': 4581}]
2025-05-23 12:00:43,647 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-23 12:00:44,022 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-23 12:00:44,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185169.1, 'new_value': 195812.1}, {'field': 'total_amount', 'old_value': 185169.1, 'new_value': 195812.1}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-05-23 12:00:44,022 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-23 12:00:44,428 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-23 12:00:44,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45575.6, 'new_value': 47608.42}, {'field': 'offline_amount', 'old_value': 456927.23, 'new_value': 474231.46}, {'field': 'total_amount', 'old_value': 502502.83, 'new_value': 521839.88}, {'field': 'order_count', 'old_value': 1606, 'new_value': 1675}]
2025-05-23 12:00:44,428 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-23 12:00:44,975 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-23 12:00:44,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8284.85, 'new_value': 8614.43}, {'field': 'offline_amount', 'old_value': 105491.57, 'new_value': 108881.02}, {'field': 'total_amount', 'old_value': 113776.42, 'new_value': 117495.45}, {'field': 'order_count', 'old_value': 1835, 'new_value': 1913}]
2025-05-23 12:00:44,975 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-23 12:00:45,397 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-23 12:00:45,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22279.77, 'new_value': 23783.47}, {'field': 'offline_amount', 'old_value': 18436.94, 'new_value': 19188.5}, {'field': 'total_amount', 'old_value': 40716.71, 'new_value': 42971.97}, {'field': 'order_count', 'old_value': 2324, 'new_value': 2462}]
2025-05-23 12:00:45,397 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-23 12:00:45,850 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-23 12:00:45,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4447.39, 'new_value': 4694.26}, {'field': 'offline_amount', 'old_value': 99159.78, 'new_value': 103146.06}, {'field': 'total_amount', 'old_value': 103607.17, 'new_value': 107840.32}, {'field': 'order_count', 'old_value': 1699, 'new_value': 1764}]
2025-05-23 12:00:45,850 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-23 12:00:46,303 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-23 12:00:46,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175402.0, 'new_value': 185583.0}, {'field': 'total_amount', 'old_value': 175402.0, 'new_value': 185583.0}, {'field': 'order_count', 'old_value': 901, 'new_value': 965}]
2025-05-23 12:00:46,303 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-23 12:00:46,772 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-23 12:00:46,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65499.6, 'new_value': 68720.24}, {'field': 'total_amount', 'old_value': 65499.6, 'new_value': 68720.24}, {'field': 'order_count', 'old_value': 3387, 'new_value': 3566}]
2025-05-23 12:00:46,772 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-23 12:00:47,225 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-23 12:00:47,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53768.0, 'new_value': 54887.0}, {'field': 'total_amount', 'old_value': 59185.32, 'new_value': 60304.32}, {'field': 'order_count', 'old_value': 449, 'new_value': 450}]
2025-05-23 12:00:47,225 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-23 12:00:47,694 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-23 12:00:47,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176098.53, 'new_value': 180620.51}, {'field': 'total_amount', 'old_value': 176098.53, 'new_value': 180620.51}, {'field': 'order_count', 'old_value': 590, 'new_value': 607}]
2025-05-23 12:00:47,709 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-23 12:00:48,256 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-23 12:00:48,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65793.54, 'new_value': 70945.68}, {'field': 'total_amount', 'old_value': 70602.49, 'new_value': 75754.63}, {'field': 'order_count', 'old_value': 4062, 'new_value': 4378}]
2025-05-23 12:00:48,256 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-23 12:00:48,787 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-23 12:00:48,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72201.3, 'new_value': 76653.02}, {'field': 'offline_amount', 'old_value': 752017.38, 'new_value': 786382.98}, {'field': 'total_amount', 'old_value': 824218.68, 'new_value': 863036.0}, {'field': 'order_count', 'old_value': 2664, 'new_value': 2772}]
2025-05-23 12:00:48,787 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-23 12:00:49,287 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-23 12:00:49,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26205.0, 'new_value': 26273.0}, {'field': 'total_amount', 'old_value': 26205.0, 'new_value': 26273.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-23 12:00:49,287 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-23 12:00:49,678 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-23 12:00:49,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 594636.09, 'new_value': 618745.41}, {'field': 'total_amount', 'old_value': 594636.09, 'new_value': 618745.41}]
2025-05-23 12:00:49,678 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-23 12:00:50,131 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-23 12:00:50,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48574.15, 'new_value': 53905.15}, {'field': 'offline_amount', 'old_value': 85991.01, 'new_value': 86006.01}, {'field': 'total_amount', 'old_value': 134565.16, 'new_value': 139911.16}, {'field': 'order_count', 'old_value': 4664, 'new_value': 4864}]
2025-05-23 12:00:50,131 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-23 12:00:50,647 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-23 12:00:50,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59258.5, 'new_value': 26365.2}, {'field': 'total_amount', 'old_value': 92826.72, 'new_value': 59933.42}, {'field': 'order_count', 'old_value': 66, 'new_value': 56}]
2025-05-23 12:00:50,647 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-23 12:00:51,131 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-23 12:00:51,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4360.0, 'new_value': 4583.0}, {'field': 'offline_amount', 'old_value': 15134.0, 'new_value': 17002.0}, {'field': 'total_amount', 'old_value': 19494.0, 'new_value': 21585.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 132}]
2025-05-23 12:00:51,131 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-23 12:00:51,537 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-23 12:00:51,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 260004.33, 'new_value': 276640.7}, {'field': 'total_amount', 'old_value': 260813.33, 'new_value': 277449.7}, {'field': 'order_count', 'old_value': 2970, 'new_value': 3155}]
2025-05-23 12:00:51,537 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-23 12:00:51,912 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-23 12:00:51,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279223.0, 'new_value': 286462.0}, {'field': 'total_amount', 'old_value': 279223.0, 'new_value': 286462.0}, {'field': 'order_count', 'old_value': 164, 'new_value': 168}]
2025-05-23 12:00:51,912 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-23 12:00:52,412 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-23 12:00:52,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152389.59, 'new_value': 156906.98}, {'field': 'total_amount', 'old_value': 152389.59, 'new_value': 156906.98}, {'field': 'order_count', 'old_value': 868, 'new_value': 897}]
2025-05-23 12:00:52,412 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-23 12:00:52,881 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-23 12:00:52,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98905.05, 'new_value': 105269.62}, {'field': 'offline_amount', 'old_value': 80607.47, 'new_value': 85512.44}, {'field': 'total_amount', 'old_value': 179512.52, 'new_value': 190782.06}, {'field': 'order_count', 'old_value': 6526, 'new_value': 6980}]
2025-05-23 12:00:52,881 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-23 12:00:53,287 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-23 12:00:53,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162027.38, 'new_value': 169252.65}, {'field': 'offline_amount', 'old_value': 376272.67, 'new_value': 382584.93}, {'field': 'total_amount', 'old_value': 538300.05, 'new_value': 551837.58}, {'field': 'order_count', 'old_value': 3944, 'new_value': 4078}]
2025-05-23 12:00:53,287 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-23 12:00:53,756 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-23 12:00:53,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25035.57, 'new_value': 25975.57}, {'field': 'total_amount', 'old_value': 25035.57, 'new_value': 25975.57}, {'field': 'order_count', 'old_value': 142, 'new_value': 148}]
2025-05-23 12:00:53,756 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-23 12:00:54,256 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-23 12:00:54,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22545.4, 'new_value': 22613.4}, {'field': 'total_amount', 'old_value': 22610.95, 'new_value': 22678.95}, {'field': 'order_count', 'old_value': 199, 'new_value': 200}]
2025-05-23 12:00:54,256 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-23 12:00:54,725 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-23 12:00:54,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174622.65, 'new_value': 184950.65}, {'field': 'offline_amount', 'old_value': 98775.0, 'new_value': 105947.0}, {'field': 'total_amount', 'old_value': 273397.65, 'new_value': 290897.65}, {'field': 'order_count', 'old_value': 1557, 'new_value': 1638}]
2025-05-23 12:00:54,725 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-23 12:00:55,256 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-23 12:00:55,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32591.9, 'new_value': 34375.9}, {'field': 'total_amount', 'old_value': 32591.9, 'new_value': 34375.9}, {'field': 'order_count', 'old_value': 147, 'new_value': 151}]
2025-05-23 12:00:55,256 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-23 12:00:55,740 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-23 12:00:55,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45689.6, 'new_value': 47053.6}, {'field': 'total_amount', 'old_value': 45689.6, 'new_value': 47053.6}, {'field': 'order_count', 'old_value': 198, 'new_value': 203}]
2025-05-23 12:00:55,740 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-23 12:00:56,318 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-23 12:00:56,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156759.0, 'new_value': 169889.0}, {'field': 'total_amount', 'old_value': 156759.0, 'new_value': 169889.0}, {'field': 'order_count', 'old_value': 10579, 'new_value': 10898}]
2025-05-23 12:00:56,318 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-23 12:00:56,850 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-23 12:00:56,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2.0, 'new_value': 3.0}, {'field': 'offline_amount', 'old_value': 106918.0, 'new_value': 112018.0}, {'field': 'total_amount', 'old_value': 106920.0, 'new_value': 112021.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-23 12:00:56,850 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-23 12:00:57,303 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-23 12:00:57,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141231.0, 'new_value': 151107.4}, {'field': 'total_amount', 'old_value': 148806.8, 'new_value': 158683.2}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-23 12:00:57,303 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-23 12:00:57,787 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-23 12:00:57,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189962.18, 'new_value': 200683.83}, {'field': 'total_amount', 'old_value': 196877.84, 'new_value': 207599.49}, {'field': 'order_count', 'old_value': 3898, 'new_value': 4159}]
2025-05-23 12:00:57,787 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-23 12:00:58,209 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-23 12:00:58,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38237.21, 'new_value': 39829.43}, {'field': 'offline_amount', 'old_value': 79724.97, 'new_value': 81359.49}, {'field': 'total_amount', 'old_value': 117962.18, 'new_value': 121188.92}, {'field': 'order_count', 'old_value': 4295, 'new_value': 4434}]
2025-05-23 12:00:58,209 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-23 12:00:58,678 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-23 12:00:58,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 437474.79, 'new_value': 458394.49}, {'field': 'total_amount', 'old_value': 437474.79, 'new_value': 458394.49}, {'field': 'order_count', 'old_value': 4421, 'new_value': 4629}]
2025-05-23 12:00:58,678 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-23 12:00:59,084 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-23 12:00:59,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72152.19, 'new_value': 72252.19}, {'field': 'total_amount', 'old_value': 75921.29, 'new_value': 76021.29}, {'field': 'order_count', 'old_value': 386, 'new_value': 388}]
2025-05-23 12:00:59,084 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-23 12:00:59,506 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-23 12:00:59,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48454.0, 'new_value': 52008.0}, {'field': 'offline_amount', 'old_value': 147813.0, 'new_value': 152072.0}, {'field': 'total_amount', 'old_value': 196267.0, 'new_value': 204080.0}, {'field': 'order_count', 'old_value': 4294, 'new_value': 4482}]
2025-05-23 12:00:59,506 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-23 12:00:59,897 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-23 12:00:59,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11719.46, 'new_value': 12086.77}, {'field': 'offline_amount', 'old_value': 240999.34, 'new_value': 248239.04}, {'field': 'total_amount', 'old_value': 252718.8, 'new_value': 260325.81}, {'field': 'order_count', 'old_value': 1739, 'new_value': 1798}]
2025-05-23 12:00:59,897 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-23 12:01:00,365 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-23 12:01:00,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89159.64, 'new_value': 91098.64}, {'field': 'total_amount', 'old_value': 94499.64, 'new_value': 96438.64}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-23 12:01:00,365 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-23 12:01:01,084 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-23 12:01:01,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10452.8, 'new_value': 10668.8}, {'field': 'total_amount', 'old_value': 11162.8, 'new_value': 11378.8}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-23 12:01:01,084 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-23 12:01:01,568 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-23 12:01:01,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253280.0, 'new_value': 253379.0}, {'field': 'total_amount', 'old_value': 279664.0, 'new_value': 279763.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-23 12:01:01,568 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-23 12:01:02,053 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-23 12:01:02,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125489.89, 'new_value': 131381.98}, {'field': 'offline_amount', 'old_value': 99683.45, 'new_value': 101216.45}, {'field': 'total_amount', 'old_value': 225173.34, 'new_value': 232598.43}, {'field': 'order_count', 'old_value': 2263, 'new_value': 2340}]
2025-05-23 12:01:02,053 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-23 12:01:02,600 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-23 12:01:02,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 487312.04, 'new_value': 487786.04}, {'field': 'offline_amount', 'old_value': 208938.1, 'new_value': 208963.1}, {'field': 'total_amount', 'old_value': 696250.14, 'new_value': 696749.14}, {'field': 'order_count', 'old_value': 6244, 'new_value': 6252}]
2025-05-23 12:01:02,600 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-23 12:01:03,084 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-23 12:01:03,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35825.6, 'new_value': 36205.6}, {'field': 'offline_amount', 'old_value': 543.0, 'new_value': 544.0}, {'field': 'total_amount', 'old_value': 36368.6, 'new_value': 36749.6}]
2025-05-23 12:01:03,084 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-23 12:01:03,490 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-23 12:01:03,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137738.0, 'new_value': 151095.0}, {'field': 'total_amount', 'old_value': 171484.15, 'new_value': 184841.15}, {'field': 'order_count', 'old_value': 30, 'new_value': 33}]
2025-05-23 12:01:03,490 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-23 12:01:03,897 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-23 12:01:03,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7658.4, 'new_value': 7856.4}, {'field': 'offline_amount', 'old_value': 41013.0, 'new_value': 41711.0}, {'field': 'total_amount', 'old_value': 48671.4, 'new_value': 49567.4}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-05-23 12:01:03,897 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-23 12:01:04,334 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-23 12:01:04,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 514081.0, 'new_value': 534481.0}, {'field': 'total_amount', 'old_value': 514081.0, 'new_value': 534481.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 57}]
2025-05-23 12:01:04,334 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-23 12:01:04,787 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-23 12:01:04,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107921.54, 'new_value': 113864.08}, {'field': 'offline_amount', 'old_value': 367847.76, 'new_value': 384587.75}, {'field': 'total_amount', 'old_value': 475769.3, 'new_value': 498451.83}, {'field': 'order_count', 'old_value': 2597, 'new_value': 2662}]
2025-05-23 12:01:04,787 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-23 12:01:05,443 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-23 12:01:05,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45748.0, 'new_value': 46745.0}, {'field': 'total_amount', 'old_value': 46096.0, 'new_value': 47093.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 99}]
2025-05-23 12:01:05,443 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-23 12:01:05,881 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-23 12:01:05,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 721490.0, 'new_value': 749653.0}, {'field': 'total_amount', 'old_value': 721490.0, 'new_value': 749653.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 132}]
2025-05-23 12:01:05,881 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-23 12:01:06,350 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-23 12:01:06,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136296.4, 'new_value': 142114.5}, {'field': 'total_amount', 'old_value': 136296.4, 'new_value': 142114.5}, {'field': 'order_count', 'old_value': 317, 'new_value': 325}]
2025-05-23 12:01:06,350 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-23 12:01:06,756 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-23 12:01:06,756 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 196738.57, 'new_value': 204537.78}, {'field': 'offline_amount', 'old_value': 98125.45, 'new_value': 99735.85}, {'field': 'total_amount', 'old_value': 294864.02, 'new_value': 304273.63}, {'field': 'order_count', 'old_value': 1097, 'new_value': 1143}]
2025-05-23 12:01:06,756 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-23 12:01:07,522 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-23 12:01:07,522 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 196609.93, 'new_value': 197619.36}, {'field': 'offline_amount', 'old_value': 72844.09, 'new_value': 76265.29}, {'field': 'total_amount', 'old_value': 269454.02, 'new_value': 273884.65}, {'field': 'order_count', 'old_value': 2883, 'new_value': 2944}]
2025-05-23 12:01:07,522 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-23 12:01:07,975 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-23 12:01:07,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81973.44, 'new_value': 83485.57}, {'field': 'total_amount', 'old_value': 81973.44, 'new_value': 83485.57}, {'field': 'order_count', 'old_value': 3156, 'new_value': 3211}]
2025-05-23 12:01:07,975 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-23 12:01:08,490 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-23 12:01:08,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19174.29, 'new_value': 19947.69}, {'field': 'offline_amount', 'old_value': 261713.64, 'new_value': 268147.36}, {'field': 'total_amount', 'old_value': 280887.93, 'new_value': 288095.05}, {'field': 'order_count', 'old_value': 1311, 'new_value': 1349}]
2025-05-23 12:01:08,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-23 12:01:08,928 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-23 12:01:08,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30530.29, 'new_value': 32157.48}, {'field': 'offline_amount', 'old_value': 469286.61, 'new_value': 481631.24}, {'field': 'total_amount', 'old_value': 499816.9, 'new_value': 513788.72}, {'field': 'order_count', 'old_value': 2714, 'new_value': 2769}]
2025-05-23 12:01:08,928 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-23 12:01:09,381 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-23 12:01:09,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81191.02, 'new_value': 89860.54}, {'field': 'total_amount', 'old_value': 81191.02, 'new_value': 89860.54}, {'field': 'order_count', 'old_value': 71, 'new_value': 76}]
2025-05-23 12:01:09,381 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-23 12:01:09,897 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-23 12:01:09,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11520.98, 'new_value': 12596.88}, {'field': 'total_amount', 'old_value': 20760.11, 'new_value': 21836.01}, {'field': 'order_count', 'old_value': 89, 'new_value': 93}]
2025-05-23 12:01:09,897 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-23 12:01:10,397 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-23 12:01:10,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37548.0, 'new_value': 37576.0}, {'field': 'total_amount', 'old_value': 37548.0, 'new_value': 37576.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-05-23 12:01:10,397 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-23 12:01:10,850 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-23 12:01:10,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96574.79, 'new_value': 98966.05}, {'field': 'total_amount', 'old_value': 96574.79, 'new_value': 98966.05}, {'field': 'order_count', 'old_value': 655, 'new_value': 678}]
2025-05-23 12:01:10,850 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-23 12:01:11,318 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-23 12:01:11,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2247.4, 'new_value': 2467.07}, {'field': 'offline_amount', 'old_value': 20153.72, 'new_value': 20649.72}, {'field': 'total_amount', 'old_value': 22401.12, 'new_value': 23116.79}, {'field': 'order_count', 'old_value': 1023, 'new_value': 1054}]
2025-05-23 12:01:11,318 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-23 12:01:11,834 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-23 12:01:11,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48851.0, 'new_value': 53625.0}, {'field': 'total_amount', 'old_value': 48851.0, 'new_value': 53625.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-23 12:01:11,834 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-23 12:01:12,303 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-23 12:01:12,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6249.0, 'new_value': 6489.0}, {'field': 'total_amount', 'old_value': 6249.0, 'new_value': 6489.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-23 12:01:12,303 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-23 12:01:12,756 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-23 12:01:12,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87018.2, 'new_value': 89689.5}, {'field': 'total_amount', 'old_value': 87018.2, 'new_value': 89689.5}, {'field': 'order_count', 'old_value': 267, 'new_value': 276}]
2025-05-23 12:01:12,756 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-23 12:01:13,240 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-23 12:01:13,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10228.39, 'new_value': 11534.65}, {'field': 'offline_amount', 'old_value': 102383.13, 'new_value': 109983.39}, {'field': 'total_amount', 'old_value': 112611.52, 'new_value': 121518.04}, {'field': 'order_count', 'old_value': 2921, 'new_value': 3153}]
2025-05-23 12:01:13,240 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-23 12:01:13,709 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-23 12:01:13,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159132.14, 'new_value': 168780.29}, {'field': 'offline_amount', 'old_value': 298185.09, 'new_value': 304782.84}, {'field': 'total_amount', 'old_value': 457317.23, 'new_value': 473563.13}, {'field': 'order_count', 'old_value': 12753, 'new_value': 13351}]
2025-05-23 12:01:13,709 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-23 12:01:14,303 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-23 12:01:14,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38573.0, 'new_value': 40084.0}, {'field': 'total_amount', 'old_value': 38573.0, 'new_value': 40084.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 105}]
2025-05-23 12:01:14,303 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-23 12:01:14,943 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-23 12:01:14,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31307.0, 'new_value': 31987.0}, {'field': 'total_amount', 'old_value': 31307.0, 'new_value': 31987.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 100}]
2025-05-23 12:01:14,943 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-23 12:01:15,396 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-23 12:01:15,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1058000.0, 'new_value': 1095000.0}, {'field': 'total_amount', 'old_value': 1058000.0, 'new_value': 1095000.0}, {'field': 'order_count', 'old_value': 340, 'new_value': 341}]
2025-05-23 12:01:15,396 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-23 12:01:15,850 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-23 12:01:15,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15870.44, 'new_value': 17168.63}, {'field': 'offline_amount', 'old_value': 27077.73, 'new_value': 28510.91}, {'field': 'total_amount', 'old_value': 42948.17, 'new_value': 45679.54}, {'field': 'order_count', 'old_value': 1927, 'new_value': 2049}]
2025-05-23 12:01:15,850 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-23 12:01:16,334 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-23 12:01:16,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 739328.16, 'new_value': 777153.16}, {'field': 'total_amount', 'old_value': 739328.16, 'new_value': 777153.16}, {'field': 'order_count', 'old_value': 2942, 'new_value': 3085}]
2025-05-23 12:01:16,334 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-23 12:01:16,771 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-23 12:01:16,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41861.0, 'new_value': 43310.0}, {'field': 'total_amount', 'old_value': 43905.0, 'new_value': 45354.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 183}]
2025-05-23 12:01:16,771 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-23 12:01:17,256 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-23 12:01:17,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280017.0, 'new_value': 282545.0}, {'field': 'total_amount', 'old_value': 280017.0, 'new_value': 282545.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 233}]
2025-05-23 12:01:17,256 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-23 12:01:17,850 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-23 12:01:17,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25449.7, 'new_value': 30009.0}, {'field': 'offline_amount', 'old_value': 210311.6, 'new_value': 211005.9}, {'field': 'total_amount', 'old_value': 235761.3, 'new_value': 241014.9}, {'field': 'order_count', 'old_value': 1893, 'new_value': 1953}]
2025-05-23 12:01:17,850 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-23 12:01:18,287 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-23 12:01:18,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249355.25, 'new_value': 260250.85}, {'field': 'total_amount', 'old_value': 249355.25, 'new_value': 260250.85}, {'field': 'order_count', 'old_value': 6785, 'new_value': 7127}]
2025-05-23 12:01:18,287 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-23 12:01:18,787 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-23 12:01:18,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260628.3, 'new_value': 265579.0}, {'field': 'total_amount', 'old_value': 260628.3, 'new_value': 265579.0}, {'field': 'order_count', 'old_value': 2875, 'new_value': 2904}]
2025-05-23 12:01:18,787 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-23 12:01:19,287 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-23 12:01:19,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118424.08, 'new_value': 123919.08}, {'field': 'total_amount', 'old_value': 118424.08, 'new_value': 123919.08}, {'field': 'order_count', 'old_value': 10696, 'new_value': 11212}]
2025-05-23 12:01:19,287 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-23 12:01:19,803 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-23 12:01:19,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51240.0, 'new_value': 63436.0}, {'field': 'total_amount', 'old_value': 51240.0, 'new_value': 63436.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-23 12:01:19,803 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-23 12:01:20,256 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-23 12:01:20,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204553.5, 'new_value': 211251.42}, {'field': 'total_amount', 'old_value': 204553.5, 'new_value': 211251.42}, {'field': 'order_count', 'old_value': 361, 'new_value': 375}]
2025-05-23 12:01:20,256 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-23 12:01:20,693 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-23 12:01:20,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95822.79, 'new_value': 108744.68}, {'field': 'total_amount', 'old_value': 525129.04, 'new_value': 538050.93}, {'field': 'order_count', 'old_value': 2204, 'new_value': 2237}]
2025-05-23 12:01:20,693 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-23 12:01:21,162 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-23 12:01:21,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91229.46, 'new_value': 93644.38}, {'field': 'total_amount', 'old_value': 96840.98, 'new_value': 99255.9}, {'field': 'order_count', 'old_value': 8676, 'new_value': 8934}]
2025-05-23 12:01:21,162 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-23 12:01:21,600 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-23 12:01:21,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7878.81, 'new_value': 8424.31}, {'field': 'offline_amount', 'old_value': 74867.34, 'new_value': 76935.22}, {'field': 'total_amount', 'old_value': 82746.15, 'new_value': 85359.53}, {'field': 'order_count', 'old_value': 2311, 'new_value': 2376}]
2025-05-23 12:01:21,600 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-23 12:01:22,115 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-23 12:01:22,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18067.51, 'new_value': 19575.5}, {'field': 'offline_amount', 'old_value': 34656.64, 'new_value': 36466.19}, {'field': 'total_amount', 'old_value': 52724.15, 'new_value': 56041.69}, {'field': 'order_count', 'old_value': 2780, 'new_value': 2963}]
2025-05-23 12:01:22,115 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-23 12:01:22,568 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-23 12:01:22,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48414.29, 'new_value': 49759.03}, {'field': 'offline_amount', 'old_value': 211350.73, 'new_value': 222471.17}, {'field': 'total_amount', 'old_value': 259765.02, 'new_value': 272230.2}, {'field': 'order_count', 'old_value': 8089, 'new_value': 8504}]
2025-05-23 12:01:22,568 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-23 12:01:22,990 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-23 12:01:22,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27311.77, 'new_value': 27538.77}, {'field': 'total_amount', 'old_value': 29098.67, 'new_value': 29325.67}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 12:01:22,990 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-23 12:01:23,490 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-23 12:01:23,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8682.9, 'new_value': 9248.85}, {'field': 'offline_amount', 'old_value': 10618.73, 'new_value': 10894.48}, {'field': 'total_amount', 'old_value': 19301.63, 'new_value': 20143.33}, {'field': 'order_count', 'old_value': 1487, 'new_value': 1558}]
2025-05-23 12:01:23,490 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-23 12:01:23,975 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-23 12:01:23,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5320167.0, 'new_value': 5533450.0}, {'field': 'total_amount', 'old_value': 5320167.0, 'new_value': 5533450.0}, {'field': 'order_count', 'old_value': 90157, 'new_value': 94162}]
2025-05-23 12:01:23,975 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-23 12:01:24,334 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-23 12:01:24,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64935.0, 'new_value': 65843.0}, {'field': 'total_amount', 'old_value': 64935.0, 'new_value': 65843.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 88}]
2025-05-23 12:01:24,334 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-23 12:01:24,740 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-23 12:01:24,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43772.0, 'new_value': 45960.0}, {'field': 'total_amount', 'old_value': 43772.0, 'new_value': 45960.0}, {'field': 'order_count', 'old_value': 313, 'new_value': 328}]
2025-05-23 12:01:24,740 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-23 12:01:25,131 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-23 12:01:25,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43817.39, 'new_value': 45531.44}, {'field': 'offline_amount', 'old_value': 338040.07, 'new_value': 344479.16}, {'field': 'total_amount', 'old_value': 381857.46, 'new_value': 390010.6}, {'field': 'order_count', 'old_value': 3199, 'new_value': 3278}]
2025-05-23 12:01:25,131 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-23 12:01:25,600 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-23 12:01:25,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401017.5, 'new_value': 413466.5}, {'field': 'total_amount', 'old_value': 446887.48, 'new_value': 459336.48}, {'field': 'order_count', 'old_value': 3458, 'new_value': 3565}]
2025-05-23 12:01:25,600 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-23 12:01:26,100 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-23 12:01:26,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50002.0, 'new_value': 51692.0}, {'field': 'total_amount', 'old_value': 50002.0, 'new_value': 51692.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-05-23 12:01:26,100 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-23 12:01:26,521 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-23 12:01:26,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98319.0, 'new_value': 101819.0}, {'field': 'total_amount', 'old_value': 98319.0, 'new_value': 101819.0}, {'field': 'order_count', 'old_value': 3723, 'new_value': 3724}]
2025-05-23 12:01:26,521 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-23 12:01:26,990 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-23 12:01:26,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74416.02, 'new_value': 78158.25}, {'field': 'offline_amount', 'old_value': 35061.19, 'new_value': 35527.79}, {'field': 'total_amount', 'old_value': 109477.21, 'new_value': 113686.04}, {'field': 'order_count', 'old_value': 6814, 'new_value': 7070}]
2025-05-23 12:01:26,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-23 12:01:27,412 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-23 12:01:27,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326257.71, 'new_value': 333874.46}, {'field': 'total_amount', 'old_value': 326257.71, 'new_value': 333874.46}, {'field': 'order_count', 'old_value': 1599, 'new_value': 1649}]
2025-05-23 12:01:27,412 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-23 12:01:27,896 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-23 12:01:27,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25752.07, 'new_value': 26852.07}, {'field': 'total_amount', 'old_value': 25752.07, 'new_value': 26852.07}, {'field': 'order_count', 'old_value': 2506, 'new_value': 2507}]
2025-05-23 12:01:27,896 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-23 12:01:28,350 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-23 12:01:28,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144057.26, 'new_value': 147994.43}, {'field': 'total_amount', 'old_value': 144057.26, 'new_value': 147994.43}, {'field': 'order_count', 'old_value': 243, 'new_value': 250}]
2025-05-23 12:01:28,350 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-23 12:01:28,771 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-23 12:01:28,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37754.3, 'new_value': 42915.7}, {'field': 'offline_amount', 'old_value': 25763.64, 'new_value': 27110.14}, {'field': 'total_amount', 'old_value': 63517.94, 'new_value': 70025.84}, {'field': 'order_count', 'old_value': 8482, 'new_value': 9017}]
2025-05-23 12:01:28,771 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-23 12:01:29,256 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-23 12:01:29,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94441.98, 'new_value': 97079.98}, {'field': 'total_amount', 'old_value': 94441.98, 'new_value': 97079.98}, {'field': 'order_count', 'old_value': 825, 'new_value': 850}]
2025-05-23 12:01:29,256 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-23 12:01:29,631 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-23 12:01:29,631 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80725.01, 'new_value': 84927.9}, {'field': 'offline_amount', 'old_value': 212825.04, 'new_value': 219763.35}, {'field': 'total_amount', 'old_value': 293550.05, 'new_value': 304691.25}, {'field': 'order_count', 'old_value': 9722, 'new_value': 10157}]
2025-05-23 12:01:29,631 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-23 12:01:30,068 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-23 12:01:30,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91144.53, 'new_value': 94074.3}, {'field': 'offline_amount', 'old_value': 270147.64, 'new_value': 277851.71}, {'field': 'total_amount', 'old_value': 361292.17, 'new_value': 371926.01}, {'field': 'order_count', 'old_value': 4472, 'new_value': 4674}]
2025-05-23 12:01:30,068 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-23 12:01:30,475 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-23 12:01:30,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5150300.0, 'new_value': 5190300.0}, {'field': 'total_amount', 'old_value': 5150300.0, 'new_value': 5190300.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-05-23 12:01:30,475 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-23 12:01:30,928 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-23 12:01:30,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69042.8, 'new_value': 69840.7}, {'field': 'total_amount', 'old_value': 75966.8, 'new_value': 76764.7}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-23 12:01:30,928 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-23 12:01:31,412 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-23 12:01:31,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65959.0, 'new_value': 68369.0}, {'field': 'total_amount', 'old_value': 65959.0, 'new_value': 68369.0}, {'field': 'order_count', 'old_value': 3772, 'new_value': 3890}]
2025-05-23 12:01:31,412 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-23 12:01:31,896 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-23 12:01:31,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40621.5, 'new_value': 42243.5}, {'field': 'total_amount', 'old_value': 40621.5, 'new_value': 42243.5}, {'field': 'order_count', 'old_value': 2055, 'new_value': 2142}]
2025-05-23 12:01:31,896 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-23 12:01:32,334 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-23 12:01:32,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1746.5, 'new_value': 2300.3}, {'field': 'offline_amount', 'old_value': 39362.0, 'new_value': 41391.6}, {'field': 'total_amount', 'old_value': 41108.5, 'new_value': 43691.9}, {'field': 'order_count', 'old_value': 129, 'new_value': 137}]
2025-05-23 12:01:32,334 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-23 12:01:32,771 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-23 12:01:32,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 263170.45, 'new_value': 272021.45}, {'field': 'offline_amount', 'old_value': 16966.62, 'new_value': 17379.42}, {'field': 'total_amount', 'old_value': 280137.07, 'new_value': 289400.87}, {'field': 'order_count', 'old_value': 11235, 'new_value': 11591}]
2025-05-23 12:01:32,771 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-23 12:01:33,256 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-23 12:01:33,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101362.4, 'new_value': 101721.4}, {'field': 'total_amount', 'old_value': 119490.3, 'new_value': 119849.3}, {'field': 'order_count', 'old_value': 164, 'new_value': 167}]
2025-05-23 12:01:33,256 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-23 12:01:33,662 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-23 12:01:33,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31802.17, 'new_value': 35189.18}, {'field': 'offline_amount', 'old_value': 211027.03, 'new_value': 215248.05}, {'field': 'total_amount', 'old_value': 242829.2, 'new_value': 250437.23}, {'field': 'order_count', 'old_value': 4049, 'new_value': 4336}]
2025-05-23 12:01:33,662 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-23 12:01:34,084 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-23 12:01:34,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 377.0}, {'field': 'total_amount', 'old_value': 37712.0, 'new_value': 38089.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 199}]
2025-05-23 12:01:34,084 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-23 12:01:34,553 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-23 12:01:34,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 388019.0, 'new_value': 400019.0}, {'field': 'total_amount', 'old_value': 396837.99, 'new_value': 408837.99}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-05-23 12:01:34,553 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-23 12:01:35,068 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-23 12:01:35,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108229.38, 'new_value': 112263.38}, {'field': 'offline_amount', 'old_value': 226849.2, 'new_value': 233266.82}, {'field': 'total_amount', 'old_value': 335078.58, 'new_value': 345530.2}, {'field': 'order_count', 'old_value': 4262, 'new_value': 4371}]
2025-05-23 12:01:35,068 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-23 12:01:35,521 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-23 12:01:35,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32835.0, 'new_value': 33035.0}, {'field': 'offline_amount', 'old_value': 247587.0, 'new_value': 258937.0}, {'field': 'total_amount', 'old_value': 280422.0, 'new_value': 291972.0}, {'field': 'order_count', 'old_value': 254, 'new_value': 264}]
2025-05-23 12:01:35,521 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-23 12:01:36,068 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-23 12:01:36,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227918.0, 'new_value': 230517.0}, {'field': 'total_amount', 'old_value': 227918.0, 'new_value': 230517.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 108}]
2025-05-23 12:01:36,068 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-23 12:01:36,474 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-23 12:01:36,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69674.5, 'new_value': 71048.3}, {'field': 'total_amount', 'old_value': 69674.5, 'new_value': 71048.3}, {'field': 'order_count', 'old_value': 509, 'new_value': 517}]
2025-05-23 12:01:36,474 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-23 12:01:36,896 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-23 12:01:36,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261918.26, 'new_value': 273363.07}, {'field': 'total_amount', 'old_value': 294998.47, 'new_value': 306443.28}, {'field': 'order_count', 'old_value': 12434, 'new_value': 12892}]
2025-05-23 12:01:36,896 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-23 12:01:37,334 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-23 12:01:37,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 333777.0, 'new_value': 369548.0}, {'field': 'total_amount', 'old_value': 333777.0, 'new_value': 369548.0}, {'field': 'order_count', 'old_value': 9076, 'new_value': 10115}]
2025-05-23 12:01:37,334 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-23 12:01:37,818 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-23 12:01:37,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354045.01, 'new_value': 363045.01}, {'field': 'total_amount', 'old_value': 354045.01, 'new_value': 363045.01}, {'field': 'order_count', 'old_value': 677, 'new_value': 678}]
2025-05-23 12:01:37,818 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-23 12:01:38,334 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-23 12:01:38,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58700.5, 'new_value': 60459.5}, {'field': 'total_amount', 'old_value': 58700.5, 'new_value': 60459.5}, {'field': 'order_count', 'old_value': 1611, 'new_value': 1615}]
2025-05-23 12:01:38,334 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-23 12:01:38,756 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-23 12:01:38,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92190.99, 'new_value': 95636.09}, {'field': 'total_amount', 'old_value': 92190.99, 'new_value': 95636.09}, {'field': 'order_count', 'old_value': 2698, 'new_value': 2803}]
2025-05-23 12:01:38,756 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-23 12:01:39,193 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-23 12:01:39,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15379.0, 'new_value': 15541.0}, {'field': 'total_amount', 'old_value': 15379.0, 'new_value': 15541.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-23 12:01:39,193 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-23 12:01:39,678 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-23 12:01:39,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26463.0, 'new_value': 27151.0}, {'field': 'total_amount', 'old_value': 26463.0, 'new_value': 27151.0}, {'field': 'order_count', 'old_value': 258, 'new_value': 263}]
2025-05-23 12:01:39,678 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-23 12:01:40,068 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-23 12:01:40,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38112.1, 'new_value': 39505.69}, {'field': 'total_amount', 'old_value': 38112.1, 'new_value': 39505.69}, {'field': 'order_count', 'old_value': 154, 'new_value': 161}]
2025-05-23 12:01:40,068 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-23 12:01:40,490 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-23 12:01:40,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170596.59, 'new_value': 180361.41}, {'field': 'total_amount', 'old_value': 170596.59, 'new_value': 180361.41}, {'field': 'order_count', 'old_value': 7239, 'new_value': 7695}]
2025-05-23 12:01:40,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-23 12:01:40,943 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-23 12:01:40,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4496.0, 'new_value': 5210.0}, {'field': 'total_amount', 'old_value': 72208.0, 'new_value': 72922.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-23 12:01:40,943 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-23 12:01:41,428 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-23 12:01:41,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21257.41, 'new_value': 24417.02}, {'field': 'total_amount', 'old_value': 107580.41, 'new_value': 110740.02}, {'field': 'order_count', 'old_value': 90, 'new_value': 91}]
2025-05-23 12:01:41,428 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-23 12:01:42,037 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-23 12:01:42,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65486.0, 'new_value': 65686.0}, {'field': 'total_amount', 'old_value': 65486.0, 'new_value': 65686.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-23 12:01:42,037 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-23 12:01:42,584 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-23 12:01:42,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86810.05, 'new_value': 90814.15}, {'field': 'total_amount', 'old_value': 86810.05, 'new_value': 90814.15}, {'field': 'order_count', 'old_value': 427, 'new_value': 449}]
2025-05-23 12:01:42,584 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-23 12:01:43,193 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-23 12:01:43,193 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39474.44, 'new_value': 43550.65}, {'field': 'offline_amount', 'old_value': 111640.65, 'new_value': 114023.61}, {'field': 'total_amount', 'old_value': 151115.09, 'new_value': 157574.26}, {'field': 'order_count', 'old_value': 8414, 'new_value': 8751}]
2025-05-23 12:01:43,193 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-23 12:01:43,615 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-23 12:01:43,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26093.0, 'new_value': 26251.0}, {'field': 'total_amount', 'old_value': 26093.0, 'new_value': 26251.0}, {'field': 'order_count', 'old_value': 252, 'new_value': 253}]
2025-05-23 12:01:43,615 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-23 12:01:44,021 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-23 12:01:44,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27770.68, 'new_value': 28371.23}, {'field': 'offline_amount', 'old_value': 54512.66, 'new_value': 55767.94}, {'field': 'total_amount', 'old_value': 82283.34, 'new_value': 84139.17}, {'field': 'order_count', 'old_value': 702, 'new_value': 723}]
2025-05-23 12:01:44,021 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-23 12:01:44,521 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-23 12:01:44,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69589.78, 'new_value': 71156.54}, {'field': 'offline_amount', 'old_value': 106343.61, 'new_value': 107133.84}, {'field': 'total_amount', 'old_value': 175933.39, 'new_value': 178290.38}, {'field': 'order_count', 'old_value': 1789, 'new_value': 1827}]
2025-05-23 12:01:44,521 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-23 12:01:44,974 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-23 12:01:44,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1439212.52, 'new_value': 1514134.59}, {'field': 'offline_amount', 'old_value': 153959.3, 'new_value': 154170.3}, {'field': 'total_amount', 'old_value': 1593171.82, 'new_value': 1668304.89}, {'field': 'order_count', 'old_value': 5442, 'new_value': 5699}]
2025-05-23 12:01:44,974 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-23 12:01:45,396 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-23 12:01:45,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27180.0, 'new_value': 28480.0}, {'field': 'total_amount', 'old_value': 28556.0, 'new_value': 29856.0}, {'field': 'order_count', 'old_value': 2964, 'new_value': 2965}]
2025-05-23 12:01:45,396 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-23 12:01:45,834 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-23 12:01:45,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87646.0, 'new_value': 99004.0}, {'field': 'total_amount', 'old_value': 87646.0, 'new_value': 99004.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-23 12:01:45,834 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-23 12:01:46,224 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-23 12:01:46,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14421.7, 'new_value': 15410.5}, {'field': 'offline_amount', 'old_value': 40370.9, 'new_value': 41974.9}, {'field': 'total_amount', 'old_value': 54792.6, 'new_value': 57385.4}, {'field': 'order_count', 'old_value': 157, 'new_value': 165}]
2025-05-23 12:01:46,224 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-23 12:01:46,693 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-23 12:01:46,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9333.0, 'new_value': 9597.0}, {'field': 'total_amount', 'old_value': 11339.0, 'new_value': 11603.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 112}]
2025-05-23 12:01:46,693 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-23 12:01:47,209 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-23 12:01:47,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 559986.0, 'new_value': 563771.0}, {'field': 'total_amount', 'old_value': 561582.0, 'new_value': 565367.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 244}]
2025-05-23 12:01:47,209 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-23 12:01:47,568 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-23 12:01:47,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24989.0, 'new_value': 25553.0}, {'field': 'total_amount', 'old_value': 24989.0, 'new_value': 25553.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 114}]
2025-05-23 12:01:47,568 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-23 12:01:48,037 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-23 12:01:48,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68200.0, 'new_value': 69940.0}, {'field': 'total_amount', 'old_value': 68200.0, 'new_value': 69940.0}, {'field': 'order_count', 'old_value': 477, 'new_value': 491}]
2025-05-23 12:01:48,037 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-23 12:01:48,506 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-23 12:01:48,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81434.4, 'new_value': 82091.7}, {'field': 'offline_amount', 'old_value': 119594.9, 'new_value': 121152.1}, {'field': 'total_amount', 'old_value': 201029.3, 'new_value': 203243.8}, {'field': 'order_count', 'old_value': 4069, 'new_value': 4113}]
2025-05-23 12:01:48,506 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-23 12:01:48,959 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-23 12:01:48,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 403373.88, 'new_value': 414221.35}, {'field': 'total_amount', 'old_value': 403373.88, 'new_value': 414221.35}, {'field': 'order_count', 'old_value': 5521, 'new_value': 5699}]
2025-05-23 12:01:48,959 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-23 12:01:49,568 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-23 12:01:49,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9210.3, 'new_value': 9567.3}, {'field': 'offline_amount', 'old_value': 25409.9, 'new_value': 25488.9}, {'field': 'total_amount', 'old_value': 34620.2, 'new_value': 35056.2}, {'field': 'order_count', 'old_value': 86, 'new_value': 89}]
2025-05-23 12:01:49,568 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-23 12:01:50,006 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-23 12:01:50,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166265.0, 'new_value': 173563.0}, {'field': 'total_amount', 'old_value': 166265.0, 'new_value': 173563.0}, {'field': 'order_count', 'old_value': 252, 'new_value': 260}]
2025-05-23 12:01:50,006 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-23 12:01:50,428 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-23 12:01:50,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148370.0, 'new_value': 155050.0}, {'field': 'total_amount', 'old_value': 148371.0, 'new_value': 155051.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 12:01:50,428 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-23 12:01:50,849 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-23 12:01:50,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75706.15, 'new_value': 77771.07}, {'field': 'total_amount', 'old_value': 75706.15, 'new_value': 77771.07}, {'field': 'order_count', 'old_value': 2332, 'new_value': 2404}]
2025-05-23 12:01:50,849 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-23 12:01:51,318 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-23 12:01:51,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8062.76, 'new_value': 8374.16}, {'field': 'offline_amount', 'old_value': 25741.89, 'new_value': 26522.09}, {'field': 'total_amount', 'old_value': 33804.65, 'new_value': 34896.25}, {'field': 'order_count', 'old_value': 1176, 'new_value': 1212}]
2025-05-23 12:01:51,318 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-23 12:01:51,740 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-23 12:01:51,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262365.0, 'new_value': 266841.0}, {'field': 'total_amount', 'old_value': 262365.0, 'new_value': 266841.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-05-23 12:01:51,740 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-23 12:01:52,209 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-23 12:01:52,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126123.61, 'new_value': 129770.0}, {'field': 'total_amount', 'old_value': 126123.61, 'new_value': 129770.0}, {'field': 'order_count', 'old_value': 3235, 'new_value': 3346}]
2025-05-23 12:01:52,209 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-23 12:01:52,631 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-23 12:01:52,631 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29270.0, 'new_value': 30265.58}, {'field': 'offline_amount', 'old_value': 269669.07, 'new_value': 274092.54}, {'field': 'total_amount', 'old_value': 298939.07, 'new_value': 304358.12}, {'field': 'order_count', 'old_value': 6943, 'new_value': 7076}]
2025-05-23 12:01:52,631 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-23 12:01:53,146 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-23 12:01:53,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285284.69, 'new_value': 293778.56}, {'field': 'total_amount', 'old_value': 285284.69, 'new_value': 293778.56}, {'field': 'order_count', 'old_value': 2659, 'new_value': 2761}]
2025-05-23 12:01:53,146 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-23 12:01:53,568 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-23 12:01:53,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60840.0, 'new_value': 61884.0}, {'field': 'total_amount', 'old_value': 60840.0, 'new_value': 61884.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 303}]
2025-05-23 12:01:53,568 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-23 12:01:54,037 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-23 12:01:54,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6964.82, 'new_value': 7427.26}, {'field': 'offline_amount', 'old_value': 105943.1, 'new_value': 110659.9}, {'field': 'total_amount', 'old_value': 112907.92, 'new_value': 118087.16}, {'field': 'order_count', 'old_value': 6012, 'new_value': 6234}]
2025-05-23 12:01:54,037 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-23 12:01:54,599 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-23 12:01:54,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 366703.9, 'new_value': 372930.2}, {'field': 'total_amount', 'old_value': 366703.9, 'new_value': 372930.2}, {'field': 'order_count', 'old_value': 1824, 'new_value': 1857}]
2025-05-23 12:01:54,599 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-23 12:01:55,052 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-23 12:01:55,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117925.97, 'new_value': 121051.71}, {'field': 'offline_amount', 'old_value': 46437.38, 'new_value': 47575.46}, {'field': 'total_amount', 'old_value': 164363.35, 'new_value': 168627.17}, {'field': 'order_count', 'old_value': 10079, 'new_value': 10340}]
2025-05-23 12:01:55,052 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-23 12:01:55,506 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-23 12:01:55,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16435.38, 'new_value': 17157.66}, {'field': 'offline_amount', 'old_value': 111682.61, 'new_value': 114248.46}, {'field': 'total_amount', 'old_value': 128117.99, 'new_value': 131406.12}, {'field': 'order_count', 'old_value': 3804, 'new_value': 3909}]
2025-05-23 12:01:55,506 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-23 12:01:55,927 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-23 12:01:55,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11099.4, 'new_value': 11137.4}, {'field': 'total_amount', 'old_value': 11528.4, 'new_value': 11566.4}, {'field': 'order_count', 'old_value': 150, 'new_value': 151}]
2025-05-23 12:01:55,927 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-23 12:01:56,396 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-23 12:01:56,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86695.1, 'new_value': 89855.0}, {'field': 'total_amount', 'old_value': 86695.1, 'new_value': 89855.0}, {'field': 'order_count', 'old_value': 4288, 'new_value': 4445}]
2025-05-23 12:01:56,396 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-23 12:01:56,865 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-23 12:01:56,865 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6096.0, 'new_value': 6261.0}, {'field': 'offline_amount', 'old_value': 29001.0, 'new_value': 29192.0}, {'field': 'total_amount', 'old_value': 35097.0, 'new_value': 35453.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 276}]
2025-05-23 12:01:56,865 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-23 12:01:57,256 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-23 12:01:57,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109100.23, 'new_value': 117049.47}, {'field': 'total_amount', 'old_value': 185172.67, 'new_value': 193121.91}, {'field': 'order_count', 'old_value': 7984, 'new_value': 8339}]
2025-05-23 12:01:57,256 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-23 12:01:57,756 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-23 12:01:57,756 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13589.0, 'new_value': 13830.0}, {'field': 'total_amount', 'old_value': 40421.8, 'new_value': 40662.8}, {'field': 'order_count', 'old_value': 80, 'new_value': 82}]
2025-05-23 12:01:57,756 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-23 12:01:58,240 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-23 12:01:58,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174940.0, 'new_value': 182124.0}, {'field': 'total_amount', 'old_value': 174940.0, 'new_value': 182124.0}, {'field': 'order_count', 'old_value': 202, 'new_value': 213}]
2025-05-23 12:01:58,240 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-23 12:01:58,787 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-23 12:01:58,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 606775.07, 'new_value': 618408.26}, {'field': 'total_amount', 'old_value': 606775.07, 'new_value': 618408.26}, {'field': 'order_count', 'old_value': 11522, 'new_value': 11717}]
2025-05-23 12:01:58,787 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-23 12:01:59,271 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-23 12:01:59,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227649.39, 'new_value': 236117.9}, {'field': 'total_amount', 'old_value': 239700.36, 'new_value': 248168.87}, {'field': 'order_count', 'old_value': 10156, 'new_value': 10559}]
2025-05-23 12:01:59,271 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-23 12:01:59,662 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-23 12:01:59,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 780852.0, 'new_value': 828373.0}, {'field': 'total_amount', 'old_value': 780852.0, 'new_value': 828373.0}, {'field': 'order_count', 'old_value': 1669, 'new_value': 1767}]
2025-05-23 12:01:59,662 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-23 12:02:00,099 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-23 12:02:00,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10909.0, 'new_value': 11289.0}, {'field': 'total_amount', 'old_value': 10909.0, 'new_value': 11289.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-23 12:02:00,099 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-23 12:02:00,615 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-23 12:02:00,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 621996.53, 'new_value': 647689.51}, {'field': 'total_amount', 'old_value': 621996.53, 'new_value': 647689.51}, {'field': 'order_count', 'old_value': 4639, 'new_value': 4837}]
2025-05-23 12:02:00,615 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-23 12:02:00,990 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-23 12:02:00,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181137.0, 'new_value': 187864.0}, {'field': 'total_amount', 'old_value': 181137.0, 'new_value': 187864.0}, {'field': 'order_count', 'old_value': 552, 'new_value': 580}]
2025-05-23 12:02:00,990 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-23 12:02:01,412 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-23 12:02:01,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51650.38, 'new_value': 53692.83}, {'field': 'offline_amount', 'old_value': 382638.05, 'new_value': 394848.49}, {'field': 'total_amount', 'old_value': 434288.43, 'new_value': 448541.32}, {'field': 'order_count', 'old_value': 2060, 'new_value': 2142}]
2025-05-23 12:02:01,412 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-23 12:02:01,943 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-23 12:02:01,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 654709.0, 'new_value': 665260.0}, {'field': 'total_amount', 'old_value': 654709.0, 'new_value': 665260.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 158}]
2025-05-23 12:02:01,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-23 12:02:02,537 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-23 12:02:02,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1531.94, 'new_value': 1588.66}, {'field': 'offline_amount', 'old_value': 20081.45, 'new_value': 20213.94}, {'field': 'total_amount', 'old_value': 21613.39, 'new_value': 21802.6}, {'field': 'order_count', 'old_value': 768, 'new_value': 776}]
2025-05-23 12:02:02,537 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-23 12:02:02,990 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-23 12:02:02,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5586.52, 'new_value': 6046.42}, {'field': 'offline_amount', 'old_value': 308013.04, 'new_value': 316624.14}, {'field': 'total_amount', 'old_value': 313599.56, 'new_value': 322670.56}, {'field': 'order_count', 'old_value': 15229, 'new_value': 15667}]
2025-05-23 12:02:02,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-23 12:02:03,521 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-23 12:02:03,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66867.0, 'new_value': 69106.0}, {'field': 'total_amount', 'old_value': 66867.0, 'new_value': 69106.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 174}]
2025-05-23 12:02:03,521 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-23 12:02:03,943 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-23 12:02:03,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176413.9, 'new_value': 185376.9}, {'field': 'total_amount', 'old_value': 176413.9, 'new_value': 185376.9}, {'field': 'order_count', 'old_value': 971, 'new_value': 1022}]
2025-05-23 12:02:03,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-23 12:02:04,396 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-23 12:02:04,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53707.08, 'new_value': 56498.21}, {'field': 'offline_amount', 'old_value': 75693.72, 'new_value': 77284.58}, {'field': 'total_amount', 'old_value': 129400.8, 'new_value': 133782.79}, {'field': 'order_count', 'old_value': 5950, 'new_value': 6152}]
2025-05-23 12:02:04,396 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-23 12:02:04,756 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-23 12:02:04,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310548.15, 'new_value': 320445.17}, {'field': 'total_amount', 'old_value': 332711.27, 'new_value': 342608.29}, {'field': 'order_count', 'old_value': 14098, 'new_value': 14542}]
2025-05-23 12:02:04,756 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-23 12:02:05,177 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-23 12:02:05,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25033.39, 'new_value': 26113.24}, {'field': 'offline_amount', 'old_value': 198536.74, 'new_value': 203683.54}, {'field': 'total_amount', 'old_value': 223570.13, 'new_value': 229796.78}, {'field': 'order_count', 'old_value': 7022, 'new_value': 7235}]
2025-05-23 12:02:05,177 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-23 12:02:05,646 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-23 12:02:05,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332304.74, 'new_value': 341990.38}, {'field': 'total_amount', 'old_value': 332304.74, 'new_value': 341990.38}, {'field': 'order_count', 'old_value': 2396, 'new_value': 2484}]
2025-05-23 12:02:05,662 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-23 12:02:06,099 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-23 12:02:06,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110973.1, 'new_value': 112831.5}, {'field': 'total_amount', 'old_value': 110973.1, 'new_value': 112831.5}, {'field': 'order_count', 'old_value': 212, 'new_value': 219}]
2025-05-23 12:02:06,099 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-23 12:02:06,584 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-23 12:02:06,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81218.0, 'new_value': 82350.0}, {'field': 'total_amount', 'old_value': 96422.0, 'new_value': 97554.0}, {'field': 'order_count', 'old_value': 2193, 'new_value': 2220}]
2025-05-23 12:02:06,584 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-23 12:02:07,146 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-23 12:02:07,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83974.0, 'new_value': 86772.0}, {'field': 'total_amount', 'old_value': 83974.0, 'new_value': 86772.0}, {'field': 'order_count', 'old_value': 573, 'new_value': 596}]
2025-05-23 12:02:07,146 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-23 12:02:07,646 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-23 12:02:07,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161564.16, 'new_value': 166564.16}, {'field': 'total_amount', 'old_value': 161564.16, 'new_value': 166564.16}, {'field': 'order_count', 'old_value': 2065, 'new_value': 2066}]
2025-05-23 12:02:07,646 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-23 12:02:08,224 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-23 12:02:08,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21142.0, 'new_value': 21846.0}, {'field': 'total_amount', 'old_value': 21142.0, 'new_value': 21846.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-23 12:02:08,224 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-23 12:02:08,693 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-23 12:02:08,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120294.0, 'new_value': 121947.0}, {'field': 'total_amount', 'old_value': 120294.0, 'new_value': 121947.0}, {'field': 'order_count', 'old_value': 3843, 'new_value': 3895}]
2025-05-23 12:02:08,693 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-23 12:02:09,115 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-23 12:02:09,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10416.0, 'new_value': 20416.0}, {'field': 'total_amount', 'old_value': 10416.0, 'new_value': 20416.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-23 12:02:09,115 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-23 12:02:09,568 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-23 12:02:09,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108953.71, 'new_value': 114791.29}, {'field': 'offline_amount', 'old_value': 304657.43, 'new_value': 307586.35}, {'field': 'total_amount', 'old_value': 413611.14, 'new_value': 422377.64}, {'field': 'order_count', 'old_value': 3619, 'new_value': 3765}]
2025-05-23 12:02:09,568 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-23 12:02:10,006 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-23 12:02:10,006 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 536.0, 'new_value': 835.0}, {'field': 'total_amount', 'old_value': 237404.46, 'new_value': 237703.46}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-23 12:02:10,006 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-23 12:02:10,490 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-23 12:02:10,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208271.42, 'new_value': 212068.92}, {'field': 'offline_amount', 'old_value': 85937.78, 'new_value': 86167.68}, {'field': 'total_amount', 'old_value': 294209.2, 'new_value': 298236.6}, {'field': 'order_count', 'old_value': 528, 'new_value': 537}]
2025-05-23 12:02:10,490 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-23 12:02:10,959 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-23 12:02:10,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200314.19, 'new_value': 204111.95}, {'field': 'total_amount', 'old_value': 219487.62, 'new_value': 223285.38}, {'field': 'order_count', 'old_value': 4524, 'new_value': 4611}]
2025-05-23 12:02:10,974 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-23 12:02:11,427 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-23 12:02:11,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98659.8, 'new_value': 99514.7}, {'field': 'offline_amount', 'old_value': 66883.88, 'new_value': 67372.68}, {'field': 'total_amount', 'old_value': 165543.68, 'new_value': 166887.38}, {'field': 'order_count', 'old_value': 1110, 'new_value': 1125}]
2025-05-23 12:02:11,427 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-23 12:02:11,881 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-23 12:02:11,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303723.2, 'new_value': 315197.5}, {'field': 'total_amount', 'old_value': 303723.2, 'new_value': 315197.5}, {'field': 'order_count', 'old_value': 376, 'new_value': 388}]
2025-05-23 12:02:11,881 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-23 12:02:12,349 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-23 12:02:12,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90012.0, 'new_value': 95917.0}, {'field': 'total_amount', 'old_value': 90012.0, 'new_value': 95917.0}, {'field': 'order_count', 'old_value': 2203, 'new_value': 2362}]
2025-05-23 12:02:12,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-23 12:02:12,724 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-23 12:02:12,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 797051.0, 'new_value': 797081.0}, {'field': 'total_amount', 'old_value': 797051.0, 'new_value': 797081.0}, {'field': 'order_count', 'old_value': 888, 'new_value': 50914}]
2025-05-23 12:02:12,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-23 12:02:13,224 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-23 12:02:13,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 587612.72, 'new_value': 605520.72}, {'field': 'total_amount', 'old_value': 587612.72, 'new_value': 605520.72}, {'field': 'order_count', 'old_value': 4496, 'new_value': 4649}]
2025-05-23 12:02:13,224 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-23 12:02:13,646 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-23 12:02:13,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 555299.0, 'new_value': 575896.0}, {'field': 'total_amount', 'old_value': 555299.0, 'new_value': 575896.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 65}]
2025-05-23 12:02:13,646 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-23 12:02:14,130 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-23 12:02:14,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67407.72, 'new_value': 69533.82}, {'field': 'total_amount', 'old_value': 67407.72, 'new_value': 69533.82}, {'field': 'order_count', 'old_value': 3836, 'new_value': 3959}]
2025-05-23 12:02:14,130 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-23 12:02:14,584 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-23 12:02:14,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39823.0, 'new_value': 45554.0}, {'field': 'total_amount', 'old_value': 39823.0, 'new_value': 45554.0}, {'field': 'order_count', 'old_value': 390, 'new_value': 392}]
2025-05-23 12:02:14,584 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-23 12:02:15,052 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-23 12:02:15,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54000.0, 'new_value': 64000.0}, {'field': 'total_amount', 'old_value': 54000.0, 'new_value': 64000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-23 12:02:15,052 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-23 12:02:15,880 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-23 12:02:15,880 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12396.45, 'new_value': 13019.45}, {'field': 'offline_amount', 'old_value': 235033.0, 'new_value': 243706.0}, {'field': 'total_amount', 'old_value': 247429.45, 'new_value': 256725.45}, {'field': 'order_count', 'old_value': 1318, 'new_value': 1373}]
2025-05-23 12:02:15,880 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-23 12:02:16,396 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-23 12:02:16,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172400.0, 'new_value': 184200.0}, {'field': 'total_amount', 'old_value': 172400.0, 'new_value': 184200.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-23 12:02:16,396 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-23 12:02:16,927 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-23 12:02:16,927 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 12:02:16,927 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-23 12:02:17,427 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-23 12:02:17,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30561.7, 'new_value': 30827.7}, {'field': 'total_amount', 'old_value': 30561.7, 'new_value': 30827.7}, {'field': 'order_count', 'old_value': 178, 'new_value': 180}]
2025-05-23 12:02:17,427 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-23 12:02:17,849 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-23 12:02:17,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270986.5, 'new_value': 279671.5}, {'field': 'total_amount', 'old_value': 270986.5, 'new_value': 279671.5}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-05-23 12:02:17,865 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-23 12:02:18,287 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-23 12:02:18,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218912.53, 'new_value': 222391.03}, {'field': 'total_amount', 'old_value': 218912.53, 'new_value': 222391.03}, {'field': 'order_count', 'old_value': 1339, 'new_value': 1365}]
2025-05-23 12:02:18,287 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-23 12:02:18,646 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-23 12:02:18,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10714.4, 'new_value': 11843.4}, {'field': 'offline_amount', 'old_value': 24607.0, 'new_value': 29295.0}, {'field': 'total_amount', 'old_value': 35321.4, 'new_value': 41138.4}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-05-23 12:02:18,646 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-23 12:02:19,130 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-23 12:02:19,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87799.83, 'new_value': 89746.83}, {'field': 'total_amount', 'old_value': 87799.83, 'new_value': 89746.83}, {'field': 'order_count', 'old_value': 111, 'new_value': 115}]
2025-05-23 12:02:19,130 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-23 12:02:19,599 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-23 12:02:19,599 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11972.0, 'new_value': 12010.0}, {'field': 'offline_amount', 'old_value': 7908.0, 'new_value': 7946.0}, {'field': 'total_amount', 'old_value': 19880.0, 'new_value': 19956.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-23 12:02:19,599 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-23 12:02:20,052 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-23 12:02:20,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277096.0, 'new_value': 289516.0}, {'field': 'total_amount', 'old_value': 290871.0, 'new_value': 303291.0}, {'field': 'order_count', 'old_value': 6238, 'new_value': 6558}]
2025-05-23 12:02:20,052 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-23 12:02:20,474 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-23 12:02:20,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71314.74, 'new_value': 73248.83}, {'field': 'offline_amount', 'old_value': 161982.4, 'new_value': 166187.07}, {'field': 'total_amount', 'old_value': 233297.14, 'new_value': 239435.9}, {'field': 'order_count', 'old_value': 4366, 'new_value': 4541}]
2025-05-23 12:02:20,474 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-23 12:02:20,912 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-23 12:02:20,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 608246.17, 'new_value': 628168.32}, {'field': 'total_amount', 'old_value': 608246.17, 'new_value': 628168.32}, {'field': 'order_count', 'old_value': 7130, 'new_value': 7394}]
2025-05-23 12:02:20,927 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-23 12:02:21,349 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-23 12:02:21,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176047.8, 'new_value': 180198.0}, {'field': 'offline_amount', 'old_value': 408113.2, 'new_value': 410913.2}, {'field': 'total_amount', 'old_value': 584161.0, 'new_value': 591111.2}, {'field': 'order_count', 'old_value': 3912, 'new_value': 3978}]
2025-05-23 12:02:21,349 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-23 12:02:21,787 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-23 12:02:21,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156991.0, 'new_value': 159180.0}, {'field': 'total_amount', 'old_value': 156991.0, 'new_value': 159180.0}, {'field': 'order_count', 'old_value': 2626, 'new_value': 2669}]
2025-05-23 12:02:21,787 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-23 12:02:22,271 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-23 12:02:22,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156021.29, 'new_value': 160119.64}, {'field': 'total_amount', 'old_value': 156021.29, 'new_value': 160119.64}, {'field': 'order_count', 'old_value': 6578, 'new_value': 6768}]
2025-05-23 12:02:22,271 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-23 12:02:22,693 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-23 12:02:22,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198442.92, 'new_value': 203846.82}, {'field': 'total_amount', 'old_value': 198442.92, 'new_value': 203846.82}, {'field': 'order_count', 'old_value': 1517, 'new_value': 1568}]
2025-05-23 12:02:22,693 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-23 12:02:23,115 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-23 12:02:23,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1728.8, 'new_value': 1966.8}, {'field': 'offline_amount', 'old_value': 74127.5, 'new_value': 76651.2}, {'field': 'total_amount', 'old_value': 75856.3, 'new_value': 78618.0}, {'field': 'order_count', 'old_value': 471, 'new_value': 488}]
2025-05-23 12:02:23,115 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-23 12:02:23,599 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-23 12:02:23,615 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54501.6, 'new_value': 57501.6}, {'field': 'offline_amount', 'old_value': 83885.17, 'new_value': 84234.79}, {'field': 'total_amount', 'old_value': 138386.77, 'new_value': 141736.39}, {'field': 'order_count', 'old_value': 3849, 'new_value': 3929}]
2025-05-23 12:02:23,615 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-23 12:02:24,099 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-23 12:02:24,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176487.16, 'new_value': 184736.71}, {'field': 'offline_amount', 'old_value': 21937.7, 'new_value': 22340.5}, {'field': 'total_amount', 'old_value': 198424.86, 'new_value': 207077.21}, {'field': 'order_count', 'old_value': 9202, 'new_value': 9475}]
2025-05-23 12:02:24,099 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-23 12:02:24,490 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-23 12:02:24,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31256.67, 'new_value': 32256.67}, {'field': 'offline_amount', 'old_value': 41033.54, 'new_value': 42134.82}, {'field': 'total_amount', 'old_value': 72290.21, 'new_value': 74391.49}, {'field': 'order_count', 'old_value': 3501, 'new_value': 3628}]
2025-05-23 12:02:24,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-23 12:02:24,974 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-23 12:02:24,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19041.45, 'new_value': 20009.48}, {'field': 'offline_amount', 'old_value': 13711.16, 'new_value': 14240.96}, {'field': 'total_amount', 'old_value': 32752.61, 'new_value': 34250.44}, {'field': 'order_count', 'old_value': 1396, 'new_value': 1464}]
2025-05-23 12:02:24,974 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-23 12:02:25,412 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-23 12:02:25,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259426.0, 'new_value': 272378.0}, {'field': 'total_amount', 'old_value': 259426.0, 'new_value': 272378.0}, {'field': 'order_count', 'old_value': 386, 'new_value': 403}]
2025-05-23 12:02:25,412 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-23 12:02:25,865 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-23 12:02:25,865 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9652.59, 'new_value': 10450.69}, {'field': 'offline_amount', 'old_value': 54310.03, 'new_value': 56712.87}, {'field': 'total_amount', 'old_value': 63962.62, 'new_value': 67163.56}, {'field': 'order_count', 'old_value': 1461, 'new_value': 1526}]
2025-05-23 12:02:25,865 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-23 12:02:26,380 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-23 12:02:26,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36610.6, 'new_value': 37110.6}, {'field': 'total_amount', 'old_value': 51586.5, 'new_value': 52086.5}, {'field': 'order_count', 'old_value': 536, 'new_value': 537}]
2025-05-23 12:02:26,380 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-23 12:02:26,849 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-23 12:02:26,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97546.29, 'new_value': 103670.1}, {'field': 'offline_amount', 'old_value': 178230.45, 'new_value': 183927.85}, {'field': 'total_amount', 'old_value': 275776.74, 'new_value': 287597.95}, {'field': 'order_count', 'old_value': 8431, 'new_value': 8863}]
2025-05-23 12:02:26,849 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-23 12:02:27,568 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-23 12:02:27,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88804.0, 'new_value': 92328.0}, {'field': 'total_amount', 'old_value': 88804.0, 'new_value': 92328.0}, {'field': 'order_count', 'old_value': 383, 'new_value': 398}]
2025-05-23 12:02:27,568 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-23 12:02:27,959 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-23 12:02:27,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115004.52, 'new_value': 122078.82}, {'field': 'total_amount', 'old_value': 115012.52, 'new_value': 122086.82}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 12:02:27,959 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-23 12:02:28,474 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-23 12:02:28,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149234.57, 'new_value': 154944.91}, {'field': 'offline_amount', 'old_value': 40317.02, 'new_value': 41227.33}, {'field': 'total_amount', 'old_value': 189551.59, 'new_value': 196172.24}, {'field': 'order_count', 'old_value': 10696, 'new_value': 11073}]
2025-05-23 12:02:28,474 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-23 12:02:28,912 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-23 12:02:28,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97106.5, 'new_value': 107949.4}, {'field': 'offline_amount', 'old_value': 27327.4, 'new_value': 29021.5}, {'field': 'total_amount', 'old_value': 124433.9, 'new_value': 136970.9}, {'field': 'order_count', 'old_value': 10108, 'new_value': 11214}]
2025-05-23 12:02:28,912 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-23 12:02:29,443 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-23 12:02:29,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229824.62, 'new_value': 236465.12}, {'field': 'total_amount', 'old_value': 252312.02, 'new_value': 258952.52}, {'field': 'order_count', 'old_value': 1383, 'new_value': 1420}]
2025-05-23 12:02:29,443 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-23 12:02:29,896 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-23 12:02:29,896 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136794.55, 'new_value': 140659.65}, {'field': 'offline_amount', 'old_value': 248356.33, 'new_value': 257371.45}, {'field': 'total_amount', 'old_value': 385150.88, 'new_value': 398031.1}, {'field': 'order_count', 'old_value': 3211, 'new_value': 3327}]
2025-05-23 12:02:29,896 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-23 12:02:30,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-23 12:02:30,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36437.52, 'new_value': 37992.05}, {'field': 'offline_amount', 'old_value': 21935.72, 'new_value': 22196.32}, {'field': 'total_amount', 'old_value': 58373.24, 'new_value': 60188.37}, {'field': 'order_count', 'old_value': 2524, 'new_value': 2614}]
2025-05-23 12:02:30,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-23 12:02:30,849 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-23 12:02:30,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13283.29, 'new_value': 13574.49}, {'field': 'offline_amount', 'old_value': 29197.4, 'new_value': 29742.2}, {'field': 'total_amount', 'old_value': 42480.69, 'new_value': 43316.69}, {'field': 'order_count', 'old_value': 1688, 'new_value': 1723}]
2025-05-23 12:02:30,849 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-23 12:02:31,302 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-23 12:02:31,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316486.9, 'new_value': 321252.9}, {'field': 'total_amount', 'old_value': 316486.9, 'new_value': 321252.9}]
2025-05-23 12:02:31,302 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-23 12:02:31,880 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-23 12:02:31,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74668.0, 'new_value': 75542.0}, {'field': 'total_amount', 'old_value': 74668.0, 'new_value': 75542.0}, {'field': 'order_count', 'old_value': 1691, 'new_value': 1692}]
2025-05-23 12:02:31,880 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-23 12:02:32,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-23 12:02:32,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 533179.0, 'new_value': 538179.0}, {'field': 'total_amount', 'old_value': 533179.0, 'new_value': 538179.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-05-23 12:02:32,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-23 12:02:32,755 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-23 12:02:32,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 569024.0, 'new_value': 583291.0}, {'field': 'total_amount', 'old_value': 569024.0, 'new_value': 583291.0}, {'field': 'order_count', 'old_value': 414, 'new_value': 422}]
2025-05-23 12:02:32,755 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-23 12:02:33,224 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-23 12:02:33,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 661377.17, 'new_value': 678842.17}, {'field': 'total_amount', 'old_value': 661377.17, 'new_value': 678842.17}, {'field': 'order_count', 'old_value': 5271, 'new_value': 5445}]
2025-05-23 12:02:33,224 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-23 12:02:33,630 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-23 12:02:33,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 463102.0, 'new_value': 472201.0}, {'field': 'total_amount', 'old_value': 463102.0, 'new_value': 472201.0}, {'field': 'order_count', 'old_value': 3062, 'new_value': 3166}]
2025-05-23 12:02:33,630 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-23 12:02:34,052 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-23 12:02:34,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20530.86, 'new_value': 22132.52}, {'field': 'offline_amount', 'old_value': 23027.08, 'new_value': 24644.28}, {'field': 'total_amount', 'old_value': 43557.94, 'new_value': 46776.8}, {'field': 'order_count', 'old_value': 2089, 'new_value': 2267}]
2025-05-23 12:02:34,052 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-23 12:02:34,474 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-23 12:02:34,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2962.0, 'new_value': 3066.0}, {'field': 'offline_amount', 'old_value': 25121.2, 'new_value': 25409.0}, {'field': 'total_amount', 'old_value': 28083.2, 'new_value': 28475.0}, {'field': 'order_count', 'old_value': 1026, 'new_value': 1043}]
2025-05-23 12:02:34,490 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-23 12:02:34,990 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-23 12:02:34,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88063.27, 'new_value': 92102.98}, {'field': 'offline_amount', 'old_value': 98348.71, 'new_value': 102357.19}, {'field': 'total_amount', 'old_value': 186411.98, 'new_value': 194460.17}, {'field': 'order_count', 'old_value': 4680, 'new_value': 4912}]
2025-05-23 12:02:34,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-23 12:02:35,427 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-23 12:02:35,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94057.15, 'new_value': 101634.11}, {'field': 'total_amount', 'old_value': 101286.22, 'new_value': 108863.18}, {'field': 'order_count', 'old_value': 546, 'new_value': 574}]
2025-05-23 12:02:35,427 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-23 12:02:35,833 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-23 12:02:35,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27600.62, 'new_value': 28166.62}, {'field': 'offline_amount', 'old_value': 26876.82, 'new_value': 30339.82}, {'field': 'total_amount', 'old_value': 54477.44, 'new_value': 58506.44}, {'field': 'order_count', 'old_value': 242, 'new_value': 255}]
2025-05-23 12:02:35,833 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-23 12:02:36,287 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-23 12:02:36,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57912.0, 'new_value': 58962.0}, {'field': 'offline_amount', 'old_value': 263144.0, 'new_value': 272166.0}, {'field': 'total_amount', 'old_value': 321056.0, 'new_value': 331128.0}, {'field': 'order_count', 'old_value': 1266, 'new_value': 1316}]
2025-05-23 12:02:36,287 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-23 12:02:36,834 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-23 12:02:36,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 890746.0, 'new_value': 915416.0}, {'field': 'total_amount', 'old_value': 890746.0, 'new_value': 915416.0}, {'field': 'order_count', 'old_value': 3900, 'new_value': 4009}]
2025-05-23 12:02:36,834 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-23 12:02:37,271 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-23 12:02:37,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11186132.0, 'new_value': 11498800.0}, {'field': 'total_amount', 'old_value': 11186132.0, 'new_value': 11498800.0}, {'field': 'order_count', 'old_value': 34505, 'new_value': 35572}]
2025-05-23 12:02:37,271 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-23 12:02:37,755 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-23 12:02:37,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3186706.79, 'new_value': 3321938.45}, {'field': 'total_amount', 'old_value': 3186706.79, 'new_value': 3321938.45}, {'field': 'order_count', 'old_value': 5466, 'new_value': 5677}]
2025-05-23 12:02:37,755 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-23 12:02:38,193 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-23 12:02:38,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135377.18, 'new_value': 142897.42}, {'field': 'total_amount', 'old_value': 142816.82, 'new_value': 150337.06}, {'field': 'order_count', 'old_value': 9959, 'new_value': 10505}]
2025-05-23 12:02:38,193 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-23 12:02:38,646 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-23 12:02:38,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280998.0, 'new_value': 296572.0}, {'field': 'total_amount', 'old_value': 280998.0, 'new_value': 296572.0}, {'field': 'order_count', 'old_value': 5922, 'new_value': 6272}]
2025-05-23 12:02:38,646 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-23 12:02:39,115 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-23 12:02:39,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 254789.37, 'new_value': 263090.84}, {'field': 'offline_amount', 'old_value': 177590.68, 'new_value': 182633.86}, {'field': 'total_amount', 'old_value': 432380.05, 'new_value': 445724.7}, {'field': 'order_count', 'old_value': 17289, 'new_value': 17869}]
2025-05-23 12:02:39,115 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-23 12:02:39,521 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-23 12:02:39,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38515.88, 'new_value': 40709.89}, {'field': 'offline_amount', 'old_value': 60534.31, 'new_value': 61815.41}, {'field': 'total_amount', 'old_value': 99050.19, 'new_value': 102525.3}, {'field': 'order_count', 'old_value': 2073, 'new_value': 2150}]
2025-05-23 12:02:39,521 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-23 12:02:39,912 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-23 12:02:39,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 278258.4, 'new_value': 281911.5}, {'field': 'total_amount', 'old_value': 278258.4, 'new_value': 281911.5}, {'field': 'order_count', 'old_value': 6059, 'new_value': 6134}]
2025-05-23 12:02:39,912 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-23 12:02:40,349 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-23 12:02:40,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45197.0, 'new_value': 48093.4}, {'field': 'total_amount', 'old_value': 45197.0, 'new_value': 48093.4}, {'field': 'order_count', 'old_value': 237, 'new_value': 253}]
2025-05-23 12:02:40,349 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-23 12:02:40,787 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-23 12:02:40,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49078.0, 'new_value': 52085.0}, {'field': 'total_amount', 'old_value': 49078.0, 'new_value': 52085.0}, {'field': 'order_count', 'old_value': 10546, 'new_value': 11163}]
2025-05-23 12:02:40,787 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-23 12:02:41,177 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-23 12:02:41,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72263.0, 'new_value': 76773.0}, {'field': 'total_amount', 'old_value': 72263.0, 'new_value': 76773.0}, {'field': 'order_count', 'old_value': 10546, 'new_value': 11163}]
2025-05-23 12:02:41,177 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-23 12:02:41,630 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-23 12:02:41,630 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 253.0, 'new_value': 279.8}, {'field': 'offline_amount', 'old_value': 66681.2, 'new_value': 68160.2}, {'field': 'total_amount', 'old_value': 66934.2, 'new_value': 68440.0}, {'field': 'order_count', 'old_value': 991, 'new_value': 1006}]
2025-05-23 12:02:41,630 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-23 12:02:42,130 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-23 12:02:42,130 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3734.6, 'new_value': 4177.6}, {'field': 'offline_amount', 'old_value': 18676.0, 'new_value': 20460.0}, {'field': 'total_amount', 'old_value': 22410.6, 'new_value': 24637.6}, {'field': 'order_count', 'old_value': 542, 'new_value': 606}]
2025-05-23 12:02:42,130 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-23 12:02:42,646 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-23 12:02:42,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154767.28, 'new_value': 169176.43}, {'field': 'total_amount', 'old_value': 154767.28, 'new_value': 169176.43}, {'field': 'order_count', 'old_value': 11049, 'new_value': 12175}]
2025-05-23 12:02:42,646 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-23 12:02:43,177 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-23 12:02:43,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300000.0, 'new_value': 1254000.0}, {'field': 'total_amount', 'old_value': 1865250.0, 'new_value': 2819250.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-23 12:02:43,177 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-23 12:02:43,646 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-23 12:02:43,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23314.0, 'new_value': 24404.9}, {'field': 'offline_amount', 'old_value': 52468.9, 'new_value': 53466.0}, {'field': 'total_amount', 'old_value': 75782.9, 'new_value': 77870.9}, {'field': 'order_count', 'old_value': 2838, 'new_value': 2909}]
2025-05-23 12:02:43,646 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-23 12:02:44,130 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-23 12:02:44,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29928.8, 'new_value': 32800.11}, {'field': 'total_amount', 'old_value': 29928.8, 'new_value': 32800.11}, {'field': 'order_count', 'old_value': 1380, 'new_value': 1515}]
2025-05-23 12:02:44,130 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-23 12:02:44,552 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-23 12:02:44,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4236564.51, 'new_value': 4411277.51}, {'field': 'total_amount', 'old_value': 4236564.51, 'new_value': 4411277.51}, {'field': 'order_count', 'old_value': 87399, 'new_value': 91220}]
2025-05-23 12:02:44,552 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-23 12:02:45,005 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-23 12:02:45,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113449.9, 'new_value': 149248.9}, {'field': 'total_amount', 'old_value': 113449.9, 'new_value': 149248.9}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-23 12:02:45,005 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-23 12:02:45,599 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-23 12:02:45,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 467116.39, 'new_value': 480623.39}, {'field': 'total_amount', 'old_value': 472662.75, 'new_value': 486169.75}, {'field': 'order_count', 'old_value': 5114, 'new_value': 5239}]
2025-05-23 12:02:45,599 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-23 12:02:46,052 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-23 12:02:46,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168512.15, 'new_value': 178298.5}, {'field': 'total_amount', 'old_value': 168512.15, 'new_value': 178298.5}, {'field': 'order_count', 'old_value': 3149, 'new_value': 3298}]
2025-05-23 12:02:46,052 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-23 12:02:46,583 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-23 12:02:46,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63514.27, 'new_value': 70450.54}, {'field': 'total_amount', 'old_value': 63514.27, 'new_value': 70450.54}, {'field': 'order_count', 'old_value': 6434, 'new_value': 7248}]
2025-05-23 12:02:46,583 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-23 12:02:47,037 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-23 12:02:47,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304306.0, 'new_value': 436386.0}, {'field': 'total_amount', 'old_value': 304306.0, 'new_value': 436386.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 73}]
2025-05-23 12:02:47,037 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-23 12:02:47,537 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-23 12:02:47,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111578.41, 'new_value': 116176.13}, {'field': 'total_amount', 'old_value': 111578.41, 'new_value': 116176.13}, {'field': 'order_count', 'old_value': 2836, 'new_value': 2959}]
2025-05-23 12:02:47,537 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-23 12:02:48,130 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-23 12:02:48,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89691.0, 'new_value': 94012.0}, {'field': 'total_amount', 'old_value': 89691.0, 'new_value': 94012.0}, {'field': 'order_count', 'old_value': 228, 'new_value': 236}]
2025-05-23 12:02:48,130 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-23 12:02:48,552 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-23 12:02:48,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27416.0, 'new_value': 28608.0}, {'field': 'total_amount', 'old_value': 27416.0, 'new_value': 28608.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 90}]
2025-05-23 12:02:48,552 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-23 12:02:49,005 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-23 12:02:49,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68373.0, 'new_value': 70168.0}, {'field': 'total_amount', 'old_value': 70223.0, 'new_value': 72018.0}, {'field': 'order_count', 'old_value': 406, 'new_value': 418}]
2025-05-23 12:02:49,005 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-23 12:02:49,458 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-23 12:02:49,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 615.5, 'new_value': 840.5}, {'field': 'offline_amount', 'old_value': 32612.6, 'new_value': 37162.6}, {'field': 'total_amount', 'old_value': 33228.1, 'new_value': 38003.1}, {'field': 'order_count', 'old_value': 212, 'new_value': 242}]
2025-05-23 12:02:49,458 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-23 12:02:49,943 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-23 12:02:49,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31217.95, 'new_value': 34352.5}, {'field': 'total_amount', 'old_value': 77639.52, 'new_value': 80774.07}, {'field': 'order_count', 'old_value': 5080, 'new_value': 5283}]
2025-05-23 12:02:49,943 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-23 12:02:50,521 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-23 12:02:50,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53866.09, 'new_value': 59854.39}, {'field': 'total_amount', 'old_value': 135248.09, 'new_value': 141236.39}, {'field': 'order_count', 'old_value': 8866, 'new_value': 9281}]
2025-05-23 12:02:50,521 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-23 12:02:50,958 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-23 12:02:50,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000434.96, 'new_value': 1044062.06}, {'field': 'total_amount', 'old_value': 1000434.96, 'new_value': 1044062.06}, {'field': 'order_count', 'old_value': 2927, 'new_value': 3048}]
2025-05-23 12:02:50,958 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-23 12:02:51,505 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-23 12:02:51,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 611844.92, 'new_value': 635088.67}, {'field': 'total_amount', 'old_value': 611844.92, 'new_value': 635088.67}, {'field': 'order_count', 'old_value': 3139, 'new_value': 3303}]
2025-05-23 12:02:51,505 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-23 12:02:51,927 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-23 12:02:51,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 907884.92, 'new_value': 950549.52}, {'field': 'total_amount', 'old_value': 907884.92, 'new_value': 950549.52}, {'field': 'order_count', 'old_value': 3212, 'new_value': 3352}]
2025-05-23 12:02:51,927 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-23 12:02:52,365 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-23 12:02:52,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63000.0, 'new_value': 70000.0}, {'field': 'total_amount', 'old_value': 63000.0, 'new_value': 70000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-23 12:02:52,365 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-23 12:02:52,880 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-23 12:02:52,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 691370.08, 'new_value': 725852.18}, {'field': 'total_amount', 'old_value': 691370.08, 'new_value': 725852.18}, {'field': 'order_count', 'old_value': 1999, 'new_value': 2080}]
2025-05-23 12:02:52,880 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-23 12:02:53,365 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-23 12:02:53,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37346.0, 'new_value': 41185.0}, {'field': 'total_amount', 'old_value': 37346.0, 'new_value': 41185.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-05-23 12:02:53,365 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-23 12:02:53,786 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-23 12:02:53,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18158.0, 'new_value': 18821.0}, {'field': 'offline_amount', 'old_value': 20558.0, 'new_value': 21221.0}, {'field': 'total_amount', 'old_value': 38716.0, 'new_value': 40042.0}, {'field': 'order_count', 'old_value': 18196, 'new_value': 18859}]
2025-05-23 12:02:53,786 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-23 12:02:54,224 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-23 12:02:54,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41116.22, 'new_value': 42798.22}, {'field': 'total_amount', 'old_value': 43960.22, 'new_value': 45642.22}, {'field': 'order_count', 'old_value': 320, 'new_value': 333}]
2025-05-23 12:02:54,224 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-23 12:02:54,661 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-23 12:02:54,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109784.0, 'new_value': 116915.0}, {'field': 'total_amount', 'old_value': 109864.0, 'new_value': 116995.0}, {'field': 'order_count', 'old_value': 10801, 'new_value': 11600}]
2025-05-23 12:02:54,661 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-23 12:02:55,099 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-23 12:02:55,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49554.0, 'new_value': 59158.0}, {'field': 'total_amount', 'old_value': 65059.0, 'new_value': 74663.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 117}]
2025-05-23 12:02:55,099 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-23 12:02:55,599 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-23 12:02:55,599 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115983.0, 'new_value': 122636.0}, {'field': 'offline_amount', 'old_value': 77949.0, 'new_value': 83136.0}, {'field': 'total_amount', 'old_value': 193932.0, 'new_value': 205772.0}, {'field': 'order_count', 'old_value': 7987, 'new_value': 8436}]
2025-05-23 12:02:55,599 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-23 12:02:56,115 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-23 12:02:56,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99487.0, 'new_value': 104861.0}, {'field': 'total_amount', 'old_value': 99487.0, 'new_value': 104861.0}, {'field': 'order_count', 'old_value': 516, 'new_value': 543}]
2025-05-23 12:02:56,115 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-23 12:02:56,536 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-23 12:02:56,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214285.0, 'new_value': 230885.0}, {'field': 'total_amount', 'old_value': 214285.0, 'new_value': 230885.0}, {'field': 'order_count', 'old_value': 508, 'new_value': 541}]
2025-05-23 12:02:56,536 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-23 12:02:56,927 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-23 12:02:56,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164250.0, 'new_value': 176169.0}, {'field': 'total_amount', 'old_value': 164250.0, 'new_value': 176169.0}, {'field': 'order_count', 'old_value': 17313, 'new_value': 18566}]
2025-05-23 12:02:56,927 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-23 12:02:57,333 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-23 12:02:57,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102653.0, 'new_value': 106464.0}, {'field': 'total_amount', 'old_value': 102653.0, 'new_value': 106464.0}, {'field': 'order_count', 'old_value': 963, 'new_value': 1005}]
2025-05-23 12:02:57,333 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-23 12:02:57,911 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-23 12:02:57,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134703.68, 'new_value': 137203.68}, {'field': 'total_amount', 'old_value': 134703.68, 'new_value': 137203.68}, {'field': 'order_count', 'old_value': 1141, 'new_value': 1142}]
2025-05-23 12:02:57,911 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-23 12:02:58,380 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-23 12:02:58,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33989.0, 'new_value': 34187.0}, {'field': 'offline_amount', 'old_value': 39228.4, 'new_value': 39365.4}, {'field': 'total_amount', 'old_value': 73217.4, 'new_value': 73552.4}, {'field': 'order_count', 'old_value': 101, 'new_value': 104}]
2025-05-23 12:02:58,380 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-23 12:02:58,849 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-23 12:02:58,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41614.76, 'new_value': 43841.36}, {'field': 'total_amount', 'old_value': 41614.76, 'new_value': 43841.36}, {'field': 'order_count', 'old_value': 705, 'new_value': 744}]
2025-05-23 12:02:58,849 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-23 12:02:59,318 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-23 12:02:59,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110011.29, 'new_value': 114139.09}, {'field': 'offline_amount', 'old_value': 585259.35, 'new_value': 630673.71}, {'field': 'total_amount', 'old_value': 695270.64, 'new_value': 744812.8}, {'field': 'order_count', 'old_value': 1593, 'new_value': 1671}]
2025-05-23 12:02:59,318 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-23 12:02:59,865 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-23 12:02:59,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67300.6, 'new_value': 69102.6}, {'field': 'total_amount', 'old_value': 67300.6, 'new_value': 69102.6}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-05-23 12:02:59,865 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-23 12:03:00,302 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-23 12:03:00,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74529.33, 'new_value': 78214.29}, {'field': 'offline_amount', 'old_value': 736320.03, 'new_value': 773806.71}, {'field': 'total_amount', 'old_value': 808975.03, 'new_value': 850146.67}, {'field': 'order_count', 'old_value': 3835, 'new_value': 4040}]
2025-05-23 12:03:00,302 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-23 12:03:00,724 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-23 12:03:00,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93699.0, 'new_value': 99769.0}, {'field': 'total_amount', 'old_value': 93699.0, 'new_value': 99769.0}, {'field': 'order_count', 'old_value': 313, 'new_value': 331}]
2025-05-23 12:03:00,724 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-23 12:03:01,240 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-23 12:03:01,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67330.0, 'new_value': 74351.0}, {'field': 'total_amount', 'old_value': 72648.0, 'new_value': 79669.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-23 12:03:01,240 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-23 12:03:01,708 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-23 12:03:01,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14758366.62, 'new_value': 15423514.48}, {'field': 'total_amount', 'old_value': 14758366.62, 'new_value': 15423514.48}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 12:03:01,708 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-23 12:03:02,161 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-23 12:03:02,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36377.36, 'new_value': 38194.36}, {'field': 'total_amount', 'old_value': 40376.36, 'new_value': 42193.36}, {'field': 'order_count', 'old_value': 2519, 'new_value': 2644}]
2025-05-23 12:03:02,161 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-23 12:03:02,615 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-23 12:03:02,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153259.24, 'new_value': 160339.79}, {'field': 'total_amount', 'old_value': 153259.24, 'new_value': 160339.79}, {'field': 'order_count', 'old_value': 16011, 'new_value': 16792}]
2025-05-23 12:03:02,615 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-23 12:03:03,083 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-23 12:03:03,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16914.0, 'new_value': 17013.0}, {'field': 'total_amount', 'old_value': 16914.0, 'new_value': 17013.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 109}]
2025-05-23 12:03:03,083 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-23 12:03:03,615 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-23 12:03:03,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39535.6, 'new_value': 42922.04}, {'field': 'total_amount', 'old_value': 39535.6, 'new_value': 42922.04}, {'field': 'order_count', 'old_value': 2016, 'new_value': 2336}]
2025-05-23 12:03:03,615 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-23 12:03:04,208 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-23 12:03:04,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34650.0, 'new_value': 63159.0}, {'field': 'total_amount', 'old_value': 34650.0, 'new_value': 63159.0}, {'field': 'order_count', 'old_value': 1702, 'new_value': 3016}]
2025-05-23 12:03:04,208 - INFO - 日期 2025-05 处理完成 - 更新: 329 条，插入: 0 条，错误: 0 条
2025-05-23 12:03:04,208 - INFO - 数据同步完成！更新: 329 条，插入: 0 条，错误: 0 条
2025-05-23 12:03:04,208 - INFO - =================同步完成====================
2025-05-23 15:00:02,031 - INFO - =================使用默认全量同步=============
2025-05-23 15:00:03,468 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 15:00:03,468 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 15:00:03,499 - INFO - 开始处理日期: 2025-01
2025-05-23 15:00:03,499 - INFO - Request Parameters - Page 1:
2025-05-23 15:00:03,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:03,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:04,687 - INFO - Response - Page 1:
2025-05-23 15:00:04,890 - INFO - 第 1 页获取到 100 条记录
2025-05-23 15:00:04,890 - INFO - Request Parameters - Page 2:
2025-05-23 15:00:04,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:04,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:05,578 - INFO - Response - Page 2:
2025-05-23 15:00:05,781 - INFO - 第 2 页获取到 100 条记录
2025-05-23 15:00:05,781 - INFO - Request Parameters - Page 3:
2025-05-23 15:00:05,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:05,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:06,296 - INFO - Response - Page 3:
2025-05-23 15:00:06,499 - INFO - 第 3 页获取到 100 条记录
2025-05-23 15:00:06,499 - INFO - Request Parameters - Page 4:
2025-05-23 15:00:06,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:06,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:07,218 - INFO - Response - Page 4:
2025-05-23 15:00:07,421 - INFO - 第 4 页获取到 100 条记录
2025-05-23 15:00:07,421 - INFO - Request Parameters - Page 5:
2025-05-23 15:00:07,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:07,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:07,968 - INFO - Response - Page 5:
2025-05-23 15:00:08,171 - INFO - 第 5 页获取到 100 条记录
2025-05-23 15:00:08,171 - INFO - Request Parameters - Page 6:
2025-05-23 15:00:08,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:08,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:08,687 - INFO - Response - Page 6:
2025-05-23 15:00:08,890 - INFO - 第 6 页获取到 100 条记录
2025-05-23 15:00:08,890 - INFO - Request Parameters - Page 7:
2025-05-23 15:00:08,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:08,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:09,406 - INFO - Response - Page 7:
2025-05-23 15:00:09,609 - INFO - 第 7 页获取到 82 条记录
2025-05-23 15:00:09,609 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 15:00:09,609 - INFO - 获取到 682 条表单数据
2025-05-23 15:00:09,609 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 15:00:09,624 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 15:00:09,624 - INFO - 开始处理日期: 2025-02
2025-05-23 15:00:09,624 - INFO - Request Parameters - Page 1:
2025-05-23 15:00:09,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:09,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:10,171 - INFO - Response - Page 1:
2025-05-23 15:00:10,374 - INFO - 第 1 页获取到 100 条记录
2025-05-23 15:00:10,374 - INFO - Request Parameters - Page 2:
2025-05-23 15:00:10,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:10,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:10,921 - INFO - Response - Page 2:
2025-05-23 15:00:11,124 - INFO - 第 2 页获取到 100 条记录
2025-05-23 15:00:11,124 - INFO - Request Parameters - Page 3:
2025-05-23 15:00:11,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:11,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:11,609 - INFO - Response - Page 3:
2025-05-23 15:00:11,812 - INFO - 第 3 页获取到 100 条记录
2025-05-23 15:00:11,812 - INFO - Request Parameters - Page 4:
2025-05-23 15:00:11,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:11,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:12,328 - INFO - Response - Page 4:
2025-05-23 15:00:12,531 - INFO - 第 4 页获取到 100 条记录
2025-05-23 15:00:12,531 - INFO - Request Parameters - Page 5:
2025-05-23 15:00:12,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:12,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:13,171 - INFO - Response - Page 5:
2025-05-23 15:00:13,374 - INFO - 第 5 页获取到 100 条记录
2025-05-23 15:00:13,374 - INFO - Request Parameters - Page 6:
2025-05-23 15:00:13,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:13,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:13,874 - INFO - Response - Page 6:
2025-05-23 15:00:14,078 - INFO - 第 6 页获取到 100 条记录
2025-05-23 15:00:14,078 - INFO - Request Parameters - Page 7:
2025-05-23 15:00:14,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:14,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:14,562 - INFO - Response - Page 7:
2025-05-23 15:00:14,765 - INFO - 第 7 页获取到 70 条记录
2025-05-23 15:00:14,765 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 15:00:14,765 - INFO - 获取到 670 条表单数据
2025-05-23 15:00:14,765 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 15:00:14,781 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 15:00:14,781 - INFO - 开始处理日期: 2025-03
2025-05-23 15:00:14,781 - INFO - Request Parameters - Page 1:
2025-05-23 15:00:14,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:14,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:15,265 - INFO - Response - Page 1:
2025-05-23 15:00:15,468 - INFO - 第 1 页获取到 100 条记录
2025-05-23 15:00:15,468 - INFO - Request Parameters - Page 2:
2025-05-23 15:00:15,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:15,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:15,968 - INFO - Response - Page 2:
2025-05-23 15:00:16,171 - INFO - 第 2 页获取到 100 条记录
2025-05-23 15:00:16,171 - INFO - Request Parameters - Page 3:
2025-05-23 15:00:16,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:16,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:16,812 - INFO - Response - Page 3:
2025-05-23 15:00:17,015 - INFO - 第 3 页获取到 100 条记录
2025-05-23 15:00:17,015 - INFO - Request Parameters - Page 4:
2025-05-23 15:00:17,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:17,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:17,515 - INFO - Response - Page 4:
2025-05-23 15:00:17,718 - INFO - 第 4 页获取到 100 条记录
2025-05-23 15:00:17,718 - INFO - Request Parameters - Page 5:
2025-05-23 15:00:17,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:17,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:18,218 - INFO - Response - Page 5:
2025-05-23 15:00:18,421 - INFO - 第 5 页获取到 100 条记录
2025-05-23 15:00:18,421 - INFO - Request Parameters - Page 6:
2025-05-23 15:00:18,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:18,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:18,906 - INFO - Response - Page 6:
2025-05-23 15:00:19,109 - INFO - 第 6 页获取到 100 条记录
2025-05-23 15:00:19,109 - INFO - Request Parameters - Page 7:
2025-05-23 15:00:19,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:19,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:19,593 - INFO - Response - Page 7:
2025-05-23 15:00:19,796 - INFO - 第 7 页获取到 61 条记录
2025-05-23 15:00:19,796 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 15:00:19,796 - INFO - 获取到 661 条表单数据
2025-05-23 15:00:19,796 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 15:00:19,812 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 15:00:19,812 - INFO - 开始处理日期: 2025-04
2025-05-23 15:00:19,812 - INFO - Request Parameters - Page 1:
2025-05-23 15:00:19,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:19,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:20,359 - INFO - Response - Page 1:
2025-05-23 15:00:20,562 - INFO - 第 1 页获取到 100 条记录
2025-05-23 15:00:20,562 - INFO - Request Parameters - Page 2:
2025-05-23 15:00:20,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:20,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:21,109 - INFO - Response - Page 2:
2025-05-23 15:00:21,312 - INFO - 第 2 页获取到 100 条记录
2025-05-23 15:00:21,312 - INFO - Request Parameters - Page 3:
2025-05-23 15:00:21,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:21,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:21,953 - INFO - Response - Page 3:
2025-05-23 15:00:22,156 - INFO - 第 3 页获取到 100 条记录
2025-05-23 15:00:22,156 - INFO - Request Parameters - Page 4:
2025-05-23 15:00:22,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:22,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:22,890 - INFO - Response - Page 4:
2025-05-23 15:00:23,093 - INFO - 第 4 页获取到 100 条记录
2025-05-23 15:00:23,093 - INFO - Request Parameters - Page 5:
2025-05-23 15:00:23,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:23,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:23,609 - INFO - Response - Page 5:
2025-05-23 15:00:23,812 - INFO - 第 5 页获取到 100 条记录
2025-05-23 15:00:23,812 - INFO - Request Parameters - Page 6:
2025-05-23 15:00:23,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:23,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:24,374 - INFO - Response - Page 6:
2025-05-23 15:00:24,577 - INFO - 第 6 页获取到 100 条记录
2025-05-23 15:00:24,577 - INFO - Request Parameters - Page 7:
2025-05-23 15:00:24,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:24,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:25,046 - INFO - Response - Page 7:
2025-05-23 15:00:25,249 - INFO - 第 7 页获取到 56 条记录
2025-05-23 15:00:25,249 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 15:00:25,249 - INFO - 获取到 656 条表单数据
2025-05-23 15:00:25,249 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 15:00:25,265 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 15:00:25,265 - INFO - 开始处理日期: 2025-05
2025-05-23 15:00:25,265 - INFO - Request Parameters - Page 1:
2025-05-23 15:00:25,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:25,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:25,765 - INFO - Response - Page 1:
2025-05-23 15:00:25,968 - INFO - 第 1 页获取到 100 条记录
2025-05-23 15:00:25,968 - INFO - Request Parameters - Page 2:
2025-05-23 15:00:25,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:25,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:26,968 - INFO - Response - Page 2:
2025-05-23 15:00:27,171 - INFO - 第 2 页获取到 100 条记录
2025-05-23 15:00:27,171 - INFO - Request Parameters - Page 3:
2025-05-23 15:00:27,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:27,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:27,640 - INFO - Response - Page 3:
2025-05-23 15:00:27,843 - INFO - 第 3 页获取到 100 条记录
2025-05-23 15:00:27,843 - INFO - Request Parameters - Page 4:
2025-05-23 15:00:27,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:27,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:28,312 - INFO - Response - Page 4:
2025-05-23 15:00:28,515 - INFO - 第 4 页获取到 100 条记录
2025-05-23 15:00:28,515 - INFO - Request Parameters - Page 5:
2025-05-23 15:00:28,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:28,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:29,046 - INFO - Response - Page 5:
2025-05-23 15:00:29,249 - INFO - 第 5 页获取到 100 条记录
2025-05-23 15:00:29,249 - INFO - Request Parameters - Page 6:
2025-05-23 15:00:29,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:29,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:29,734 - INFO - Response - Page 6:
2025-05-23 15:00:29,937 - INFO - 第 6 页获取到 100 条记录
2025-05-23 15:00:29,937 - INFO - Request Parameters - Page 7:
2025-05-23 15:00:29,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:00:29,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:00:30,312 - INFO - Response - Page 7:
2025-05-23 15:00:30,515 - INFO - 第 7 页获取到 28 条记录
2025-05-23 15:00:30,515 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 15:00:30,515 - INFO - 获取到 628 条表单数据
2025-05-23 15:00:30,515 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 15:00:30,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-23 15:00:31,093 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-23 15:00:31,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54784.0, 'new_value': 55122.0}, {'field': 'total_amount', 'old_value': 57681.0, 'new_value': 58019.0}]
2025-05-23 15:00:31,093 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-23 15:00:31,531 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-23 15:00:31,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395188.71, 'new_value': 397745.71}, {'field': 'total_amount', 'old_value': 395188.71, 'new_value': 397745.71}, {'field': 'order_count', 'old_value': 386, 'new_value': 389}]
2025-05-23 15:00:31,531 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-23 15:00:32,031 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-23 15:00:32,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 517554.06, 'new_value': 536228.26}, {'field': 'total_amount', 'old_value': 519467.11, 'new_value': 538141.31}, {'field': 'order_count', 'old_value': 1211, 'new_value': 1277}]
2025-05-23 15:00:32,031 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-23 15:00:32,484 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-23 15:00:32,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341990.38, 'new_value': 341145.9}, {'field': 'total_amount', 'old_value': 341990.38, 'new_value': 341145.9}, {'field': 'order_count', 'old_value': 2484, 'new_value': 2476}]
2025-05-23 15:00:32,484 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-23 15:00:33,031 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-23 15:00:33,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104861.0, 'new_value': 103933.0}, {'field': 'total_amount', 'old_value': 104861.0, 'new_value': 103933.0}, {'field': 'order_count', 'old_value': 543, 'new_value': 544}]
2025-05-23 15:00:33,031 - INFO - 日期 2025-05 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-23 15:00:33,031 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-05-23 15:00:33,031 - INFO - =================同步完成====================
2025-05-23 18:00:02,056 - INFO - =================使用默认全量同步=============
2025-05-23 18:00:03,478 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 18:00:03,478 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 18:00:03,509 - INFO - 开始处理日期: 2025-01
2025-05-23 18:00:03,509 - INFO - Request Parameters - Page 1:
2025-05-23 18:00:03,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:03,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:05,040 - INFO - Response - Page 1:
2025-05-23 18:00:05,243 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:00:05,243 - INFO - Request Parameters - Page 2:
2025-05-23 18:00:05,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:05,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:05,774 - INFO - Response - Page 2:
2025-05-23 18:00:05,978 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:00:05,978 - INFO - Request Parameters - Page 3:
2025-05-23 18:00:05,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:05,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:06,509 - INFO - Response - Page 3:
2025-05-23 18:00:06,712 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:00:06,712 - INFO - Request Parameters - Page 4:
2025-05-23 18:00:06,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:06,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:07,462 - INFO - Response - Page 4:
2025-05-23 18:00:07,665 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:00:07,665 - INFO - Request Parameters - Page 5:
2025-05-23 18:00:07,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:07,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:08,134 - INFO - Response - Page 5:
2025-05-23 18:00:08,337 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:00:08,337 - INFO - Request Parameters - Page 6:
2025-05-23 18:00:08,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:08,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:08,915 - INFO - Response - Page 6:
2025-05-23 18:00:09,118 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:00:09,118 - INFO - Request Parameters - Page 7:
2025-05-23 18:00:09,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:09,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:09,618 - INFO - Response - Page 7:
2025-05-23 18:00:09,821 - INFO - 第 7 页获取到 82 条记录
2025-05-23 18:00:09,821 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 18:00:09,821 - INFO - 获取到 682 条表单数据
2025-05-23 18:00:09,821 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 18:00:09,837 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:00:09,837 - INFO - 开始处理日期: 2025-02
2025-05-23 18:00:09,837 - INFO - Request Parameters - Page 1:
2025-05-23 18:00:09,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:09,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:10,353 - INFO - Response - Page 1:
2025-05-23 18:00:10,556 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:00:10,556 - INFO - Request Parameters - Page 2:
2025-05-23 18:00:10,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:10,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:11,649 - INFO - Response - Page 2:
2025-05-23 18:00:11,853 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:00:11,853 - INFO - Request Parameters - Page 3:
2025-05-23 18:00:11,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:11,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:12,384 - INFO - Response - Page 3:
2025-05-23 18:00:12,587 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:00:12,587 - INFO - Request Parameters - Page 4:
2025-05-23 18:00:12,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:12,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:13,118 - INFO - Response - Page 4:
2025-05-23 18:00:13,321 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:00:13,321 - INFO - Request Parameters - Page 5:
2025-05-23 18:00:13,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:13,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:13,868 - INFO - Response - Page 5:
2025-05-23 18:00:14,071 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:00:14,071 - INFO - Request Parameters - Page 6:
2025-05-23 18:00:14,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:14,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:14,665 - INFO - Response - Page 6:
2025-05-23 18:00:14,868 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:00:14,868 - INFO - Request Parameters - Page 7:
2025-05-23 18:00:14,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:14,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:15,431 - INFO - Response - Page 7:
2025-05-23 18:00:15,634 - INFO - 第 7 页获取到 70 条记录
2025-05-23 18:00:15,634 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 18:00:15,634 - INFO - 获取到 670 条表单数据
2025-05-23 18:00:15,634 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 18:00:15,649 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:00:15,649 - INFO - 开始处理日期: 2025-03
2025-05-23 18:00:15,649 - INFO - Request Parameters - Page 1:
2025-05-23 18:00:15,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:15,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:16,165 - INFO - Response - Page 1:
2025-05-23 18:00:16,368 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:00:16,368 - INFO - Request Parameters - Page 2:
2025-05-23 18:00:16,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:16,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:16,962 - INFO - Response - Page 2:
2025-05-23 18:00:17,165 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:00:17,165 - INFO - Request Parameters - Page 3:
2025-05-23 18:00:17,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:17,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:17,727 - INFO - Response - Page 3:
2025-05-23 18:00:17,931 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:00:17,931 - INFO - Request Parameters - Page 4:
2025-05-23 18:00:17,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:17,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:18,478 - INFO - Response - Page 4:
2025-05-23 18:00:18,681 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:00:18,681 - INFO - Request Parameters - Page 5:
2025-05-23 18:00:18,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:18,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:19,118 - INFO - Response - Page 5:
2025-05-23 18:00:19,321 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:00:19,321 - INFO - Request Parameters - Page 6:
2025-05-23 18:00:19,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:19,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:19,790 - INFO - Response - Page 6:
2025-05-23 18:00:19,993 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:00:19,993 - INFO - Request Parameters - Page 7:
2025-05-23 18:00:19,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:19,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:20,493 - INFO - Response - Page 7:
2025-05-23 18:00:20,696 - INFO - 第 7 页获取到 61 条记录
2025-05-23 18:00:20,696 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 18:00:20,696 - INFO - 获取到 661 条表单数据
2025-05-23 18:00:20,712 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 18:00:20,727 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:00:20,727 - INFO - 开始处理日期: 2025-04
2025-05-23 18:00:20,727 - INFO - Request Parameters - Page 1:
2025-05-23 18:00:20,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:20,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:21,259 - INFO - Response - Page 1:
2025-05-23 18:00:21,462 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:00:21,462 - INFO - Request Parameters - Page 2:
2025-05-23 18:00:21,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:21,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:21,977 - INFO - Response - Page 2:
2025-05-23 18:00:22,181 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:00:22,181 - INFO - Request Parameters - Page 3:
2025-05-23 18:00:22,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:22,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:22,681 - INFO - Response - Page 3:
2025-05-23 18:00:22,884 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:00:22,884 - INFO - Request Parameters - Page 4:
2025-05-23 18:00:22,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:22,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:23,415 - INFO - Response - Page 4:
2025-05-23 18:00:23,618 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:00:23,618 - INFO - Request Parameters - Page 5:
2025-05-23 18:00:23,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:23,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:24,165 - INFO - Response - Page 5:
2025-05-23 18:00:24,368 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:00:24,368 - INFO - Request Parameters - Page 6:
2025-05-23 18:00:24,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:24,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:25,071 - INFO - Response - Page 6:
2025-05-23 18:00:25,274 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:00:25,274 - INFO - Request Parameters - Page 7:
2025-05-23 18:00:25,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:25,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:25,696 - INFO - Response - Page 7:
2025-05-23 18:00:25,899 - INFO - 第 7 页获取到 56 条记录
2025-05-23 18:00:25,899 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 18:00:25,899 - INFO - 获取到 656 条表单数据
2025-05-23 18:00:25,899 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 18:00:25,915 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:00:25,915 - INFO - 开始处理日期: 2025-05
2025-05-23 18:00:25,915 - INFO - Request Parameters - Page 1:
2025-05-23 18:00:25,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:25,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:26,509 - INFO - Response - Page 1:
2025-05-23 18:00:26,712 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:00:26,712 - INFO - Request Parameters - Page 2:
2025-05-23 18:00:26,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:26,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:27,290 - INFO - Response - Page 2:
2025-05-23 18:00:27,493 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:00:27,493 - INFO - Request Parameters - Page 3:
2025-05-23 18:00:27,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:27,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:28,040 - INFO - Response - Page 3:
2025-05-23 18:00:28,243 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:00:28,243 - INFO - Request Parameters - Page 4:
2025-05-23 18:00:28,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:28,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:28,712 - INFO - Response - Page 4:
2025-05-23 18:00:28,915 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:00:28,915 - INFO - Request Parameters - Page 5:
2025-05-23 18:00:28,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:28,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:29,415 - INFO - Response - Page 5:
2025-05-23 18:00:29,618 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:00:29,618 - INFO - Request Parameters - Page 6:
2025-05-23 18:00:29,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:29,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:30,321 - INFO - Response - Page 6:
2025-05-23 18:00:30,524 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:00:30,524 - INFO - Request Parameters - Page 7:
2025-05-23 18:00:30,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:00:30,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:00:30,915 - INFO - Response - Page 7:
2025-05-23 18:00:31,118 - INFO - 第 7 页获取到 28 条记录
2025-05-23 18:00:31,118 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 18:00:31,118 - INFO - 获取到 628 条表单数据
2025-05-23 18:00:31,118 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 18:00:31,118 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-23 18:00:31,649 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-23 18:00:31,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22643.9, 'new_value': 25785.9}, {'field': 'total_amount', 'old_value': 22643.9, 'new_value': 25785.9}, {'field': 'order_count', 'old_value': 159, 'new_value': 167}]
2025-05-23 18:00:31,649 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-23 18:00:32,071 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-23 18:00:32,071 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 210081.07, 'new_value': 134246.62}, {'field': 'total_amount', 'old_value': 226203.22, 'new_value': 150368.77}]
2025-05-23 18:00:32,071 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-23 18:00:32,649 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-23 18:00:32,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60691.79, 'new_value': 63259.99}, {'field': 'total_amount', 'old_value': 60691.79, 'new_value': 63259.99}, {'field': 'order_count', 'old_value': 2770, 'new_value': 2883}]
2025-05-23 18:00:32,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-23 18:00:33,087 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-23 18:00:33,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4195.9, 'new_value': 4349.9}, {'field': 'total_amount', 'old_value': 4195.9, 'new_value': 4349.9}, {'field': 'order_count', 'old_value': 271, 'new_value': 287}]
2025-05-23 18:00:33,087 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-23 18:00:33,556 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-23 18:00:33,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1095000.0, 'new_value': 1106000.0}, {'field': 'total_amount', 'old_value': 1095000.0, 'new_value': 1106000.0}]
2025-05-23 18:00:33,556 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-23 18:00:33,993 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-23 18:00:33,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30705.8, 'new_value': 34585.8}, {'field': 'total_amount', 'old_value': 30705.8, 'new_value': 34585.8}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-23 18:00:33,993 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-23 18:00:34,384 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-23 18:00:34,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40430.0, 'new_value': 42110.0}, {'field': 'total_amount', 'old_value': 40430.0, 'new_value': 42110.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 18:00:34,384 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-23 18:00:34,868 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-23 18:00:34,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100766.1, 'new_value': 102937.1}, {'field': 'total_amount', 'old_value': 100766.1, 'new_value': 102937.1}, {'field': 'order_count', 'old_value': 1019, 'new_value': 1030}]
2025-05-23 18:00:34,884 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-23 18:00:35,352 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-23 18:00:35,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51692.0, 'new_value': 54556.0}, {'field': 'total_amount', 'old_value': 51692.0, 'new_value': 54556.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 54}]
2025-05-23 18:00:35,352 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-23 18:00:35,774 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-23 18:00:35,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101819.0, 'new_value': 102115.0}, {'field': 'total_amount', 'old_value': 101819.0, 'new_value': 102115.0}, {'field': 'order_count', 'old_value': 3724, 'new_value': 3831}]
2025-05-23 18:00:35,774 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-23 18:00:36,196 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-23 18:00:36,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26852.07, 'new_value': 26891.07}, {'field': 'total_amount', 'old_value': 26852.07, 'new_value': 26891.07}, {'field': 'order_count', 'old_value': 2507, 'new_value': 2619}]
2025-05-23 18:00:36,196 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-23 18:00:36,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-23 18:00:36,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5190300.0, 'new_value': 5213400.0}, {'field': 'total_amount', 'old_value': 5190300.0, 'new_value': 5213400.0}]
2025-05-23 18:00:36,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-23 18:00:37,196 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-23 18:00:37,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400019.0, 'new_value': 402698.0}, {'field': 'total_amount', 'old_value': 408837.99, 'new_value': 411516.99}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-05-23 18:00:37,196 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-23 18:00:37,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-23 18:00:37,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 363045.01, 'new_value': 368793.75}, {'field': 'total_amount', 'old_value': 363045.01, 'new_value': 368793.75}, {'field': 'order_count', 'old_value': 678, 'new_value': 695}]
2025-05-23 18:00:37,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-23 18:00:38,087 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-23 18:00:38,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65686.0, 'new_value': 65586.0}, {'field': 'total_amount', 'old_value': 65686.0, 'new_value': 65586.0}]
2025-05-23 18:00:38,087 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-23 18:00:38,634 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-23 18:00:38,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28480.0, 'new_value': 28408.0}, {'field': 'total_amount', 'old_value': 29856.0, 'new_value': 29784.0}, {'field': 'order_count', 'old_value': 2965, 'new_value': 3073}]
2025-05-23 18:00:38,634 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-23 18:00:39,102 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-23 18:00:39,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74654.0, 'new_value': 77534.0}, {'field': 'total_amount', 'old_value': 74654.0, 'new_value': 77534.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-23 18:00:39,102 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-23 18:00:39,509 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-23 18:00:39,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166564.16, 'new_value': 166373.66}, {'field': 'total_amount', 'old_value': 166564.16, 'new_value': 166373.66}, {'field': 'order_count', 'old_value': 2066, 'new_value': 2141}]
2025-05-23 18:00:39,509 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-23 18:00:39,962 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-23 18:00:39,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 580300.0, 'new_value': 740300.0}, {'field': 'total_amount', 'old_value': 580300.0, 'new_value': 740300.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-05-23 18:00:39,962 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-23 18:00:40,384 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-23 18:00:40,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9501.67, 'new_value': 9965.67}, {'field': 'total_amount', 'old_value': 9501.67, 'new_value': 9965.67}, {'field': 'order_count', 'old_value': 280, 'new_value': 288}]
2025-05-23 18:00:40,384 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-23 18:00:40,805 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-23 18:00:40,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137203.68, 'new_value': 137782.83}, {'field': 'total_amount', 'old_value': 137203.68, 'new_value': 137782.83}, {'field': 'order_count', 'old_value': 1142, 'new_value': 1174}]
2025-05-23 18:00:40,805 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-23 18:00:41,227 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-23 18:00:41,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6800.0, 'new_value': 9600.0}, {'field': 'total_amount', 'old_value': 8738.95, 'new_value': 11538.95}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-23 18:00:41,227 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-05-23 18:00:41,227 - INFO - 数据同步完成！更新: 22 条，插入: 0 条，错误: 0 条
2025-05-23 18:00:41,227 - INFO - =================同步完成====================
2025-05-23 21:00:02,116 - INFO - =================使用默认全量同步=============
2025-05-23 21:00:03,554 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-23 21:00:03,554 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-23 21:00:03,585 - INFO - 开始处理日期: 2025-01
2025-05-23 21:00:03,585 - INFO - Request Parameters - Page 1:
2025-05-23 21:00:03,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:03,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:04,898 - INFO - Response - Page 1:
2025-05-23 21:00:05,101 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:00:05,101 - INFO - Request Parameters - Page 2:
2025-05-23 21:00:05,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:05,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:05,710 - INFO - Response - Page 2:
2025-05-23 21:00:05,913 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:00:05,913 - INFO - Request Parameters - Page 3:
2025-05-23 21:00:05,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:05,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:06,429 - INFO - Response - Page 3:
2025-05-23 21:00:06,632 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:00:06,632 - INFO - Request Parameters - Page 4:
2025-05-23 21:00:06,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:06,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:07,164 - INFO - Response - Page 4:
2025-05-23 21:00:07,367 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:00:07,367 - INFO - Request Parameters - Page 5:
2025-05-23 21:00:07,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:07,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:07,820 - INFO - Response - Page 5:
2025-05-23 21:00:08,023 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:00:08,023 - INFO - Request Parameters - Page 6:
2025-05-23 21:00:08,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:08,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:08,758 - INFO - Response - Page 6:
2025-05-23 21:00:08,961 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:00:08,961 - INFO - Request Parameters - Page 7:
2025-05-23 21:00:08,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:08,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:09,414 - INFO - Response - Page 7:
2025-05-23 21:00:09,617 - INFO - 第 7 页获取到 82 条记录
2025-05-23 21:00:09,617 - INFO - 查询完成，共获取到 682 条记录
2025-05-23 21:00:09,617 - INFO - 获取到 682 条表单数据
2025-05-23 21:00:09,617 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-23 21:00:09,633 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:00:09,633 - INFO - 开始处理日期: 2025-02
2025-05-23 21:00:09,633 - INFO - Request Parameters - Page 1:
2025-05-23 21:00:09,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:09,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:10,164 - INFO - Response - Page 1:
2025-05-23 21:00:10,367 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:00:10,367 - INFO - Request Parameters - Page 2:
2025-05-23 21:00:10,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:10,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:10,898 - INFO - Response - Page 2:
2025-05-23 21:00:11,101 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:00:11,101 - INFO - Request Parameters - Page 3:
2025-05-23 21:00:11,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:11,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:11,602 - INFO - Response - Page 3:
2025-05-23 21:00:11,805 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:00:11,805 - INFO - Request Parameters - Page 4:
2025-05-23 21:00:11,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:11,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:12,398 - INFO - Response - Page 4:
2025-05-23 21:00:12,602 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:00:12,602 - INFO - Request Parameters - Page 5:
2025-05-23 21:00:12,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:12,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:13,070 - INFO - Response - Page 5:
2025-05-23 21:00:13,274 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:00:13,274 - INFO - Request Parameters - Page 6:
2025-05-23 21:00:13,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:13,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:13,711 - INFO - Response - Page 6:
2025-05-23 21:00:13,914 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:00:13,914 - INFO - Request Parameters - Page 7:
2025-05-23 21:00:13,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:13,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:14,336 - INFO - Response - Page 7:
2025-05-23 21:00:14,539 - INFO - 第 7 页获取到 70 条记录
2025-05-23 21:00:14,539 - INFO - 查询完成，共获取到 670 条记录
2025-05-23 21:00:14,539 - INFO - 获取到 670 条表单数据
2025-05-23 21:00:14,539 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-23 21:00:14,555 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:00:14,555 - INFO - 开始处理日期: 2025-03
2025-05-23 21:00:14,555 - INFO - Request Parameters - Page 1:
2025-05-23 21:00:14,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:14,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:15,039 - INFO - Response - Page 1:
2025-05-23 21:00:15,243 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:00:15,243 - INFO - Request Parameters - Page 2:
2025-05-23 21:00:15,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:15,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:15,852 - INFO - Response - Page 2:
2025-05-23 21:00:16,055 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:00:16,055 - INFO - Request Parameters - Page 3:
2025-05-23 21:00:16,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:16,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:16,586 - INFO - Response - Page 3:
2025-05-23 21:00:16,790 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:00:16,790 - INFO - Request Parameters - Page 4:
2025-05-23 21:00:16,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:16,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:17,258 - INFO - Response - Page 4:
2025-05-23 21:00:17,461 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:00:17,461 - INFO - Request Parameters - Page 5:
2025-05-23 21:00:17,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:17,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:18,040 - INFO - Response - Page 5:
2025-05-23 21:00:18,243 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:00:18,243 - INFO - Request Parameters - Page 6:
2025-05-23 21:00:18,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:18,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:18,696 - INFO - Response - Page 6:
2025-05-23 21:00:18,899 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:00:18,899 - INFO - Request Parameters - Page 7:
2025-05-23 21:00:18,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:18,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:19,446 - INFO - Response - Page 7:
2025-05-23 21:00:19,649 - INFO - 第 7 页获取到 61 条记录
2025-05-23 21:00:19,649 - INFO - 查询完成，共获取到 661 条记录
2025-05-23 21:00:19,649 - INFO - 获取到 661 条表单数据
2025-05-23 21:00:19,649 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-23 21:00:19,665 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:00:19,665 - INFO - 开始处理日期: 2025-04
2025-05-23 21:00:19,665 - INFO - Request Parameters - Page 1:
2025-05-23 21:00:19,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:19,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:20,352 - INFO - Response - Page 1:
2025-05-23 21:00:20,556 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:00:20,556 - INFO - Request Parameters - Page 2:
2025-05-23 21:00:20,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:20,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:21,149 - INFO - Response - Page 2:
2025-05-23 21:00:21,352 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:00:21,352 - INFO - Request Parameters - Page 3:
2025-05-23 21:00:21,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:21,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:21,806 - INFO - Response - Page 3:
2025-05-23 21:00:22,009 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:00:22,009 - INFO - Request Parameters - Page 4:
2025-05-23 21:00:22,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:22,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:22,462 - INFO - Response - Page 4:
2025-05-23 21:00:22,665 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:00:22,665 - INFO - Request Parameters - Page 5:
2025-05-23 21:00:22,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:22,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:23,275 - INFO - Response - Page 5:
2025-05-23 21:00:23,478 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:00:23,478 - INFO - Request Parameters - Page 6:
2025-05-23 21:00:23,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:23,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:23,993 - INFO - Response - Page 6:
2025-05-23 21:00:24,196 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:00:24,196 - INFO - Request Parameters - Page 7:
2025-05-23 21:00:24,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:24,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:24,650 - INFO - Response - Page 7:
2025-05-23 21:00:24,853 - INFO - 第 7 页获取到 56 条记录
2025-05-23 21:00:24,853 - INFO - 查询完成，共获取到 656 条记录
2025-05-23 21:00:24,853 - INFO - 获取到 656 条表单数据
2025-05-23 21:00:24,853 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-23 21:00:24,868 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:00:24,868 - INFO - 开始处理日期: 2025-05
2025-05-23 21:00:24,868 - INFO - Request Parameters - Page 1:
2025-05-23 21:00:24,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:24,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:25,353 - INFO - Response - Page 1:
2025-05-23 21:00:25,556 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:00:25,556 - INFO - Request Parameters - Page 2:
2025-05-23 21:00:25,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:25,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:26,103 - INFO - Response - Page 2:
2025-05-23 21:00:26,306 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:00:26,306 - INFO - Request Parameters - Page 3:
2025-05-23 21:00:26,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:26,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:26,806 - INFO - Response - Page 3:
2025-05-23 21:00:27,009 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:00:27,009 - INFO - Request Parameters - Page 4:
2025-05-23 21:00:27,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:27,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:27,556 - INFO - Response - Page 4:
2025-05-23 21:00:27,759 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:00:27,759 - INFO - Request Parameters - Page 5:
2025-05-23 21:00:27,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:27,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:28,244 - INFO - Response - Page 5:
2025-05-23 21:00:28,447 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:00:28,447 - INFO - Request Parameters - Page 6:
2025-05-23 21:00:28,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:28,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:29,025 - INFO - Response - Page 6:
2025-05-23 21:00:29,228 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:00:29,228 - INFO - Request Parameters - Page 7:
2025-05-23 21:00:29,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:00:29,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:00:29,619 - INFO - Response - Page 7:
2025-05-23 21:00:29,822 - INFO - 第 7 页获取到 28 条记录
2025-05-23 21:00:29,822 - INFO - 查询完成，共获取到 628 条记录
2025-05-23 21:00:29,822 - INFO - 获取到 628 条表单数据
2025-05-23 21:00:29,822 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-23 21:00:29,838 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:00:29,838 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:00:29,838 - INFO - =================同步完成====================
