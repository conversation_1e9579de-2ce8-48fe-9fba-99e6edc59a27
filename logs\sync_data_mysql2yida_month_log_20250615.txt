2025-06-15 00:00:02,775 - INFO - =================使用默认全量同步=============
2025-06-15 00:00:04,478 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 00:00:04,478 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 00:00:04,509 - INFO - 开始处理日期: 2025-01
2025-06-15 00:00:04,509 - INFO - Request Parameters - Page 1:
2025-06-15 00:00:04,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:04,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:05,790 - INFO - Response - Page 1:
2025-06-15 00:00:05,993 - INFO - 第 1 页获取到 100 条记录
2025-06-15 00:00:05,993 - INFO - Request Parameters - Page 2:
2025-06-15 00:00:05,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:05,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:06,884 - INFO - Response - Page 2:
2025-06-15 00:00:07,087 - INFO - 第 2 页获取到 100 条记录
2025-06-15 00:00:07,087 - INFO - Request Parameters - Page 3:
2025-06-15 00:00:07,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:07,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:07,634 - INFO - Response - Page 3:
2025-06-15 00:00:07,837 - INFO - 第 3 页获取到 100 条记录
2025-06-15 00:00:07,837 - INFO - Request Parameters - Page 4:
2025-06-15 00:00:07,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:07,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:08,337 - INFO - Response - Page 4:
2025-06-15 00:00:08,540 - INFO - 第 4 页获取到 100 条记录
2025-06-15 00:00:08,540 - INFO - Request Parameters - Page 5:
2025-06-15 00:00:08,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:08,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:09,071 - INFO - Response - Page 5:
2025-06-15 00:00:09,274 - INFO - 第 5 页获取到 100 条记录
2025-06-15 00:00:09,274 - INFO - Request Parameters - Page 6:
2025-06-15 00:00:09,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:09,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:09,759 - INFO - Response - Page 6:
2025-06-15 00:00:09,962 - INFO - 第 6 页获取到 100 条记录
2025-06-15 00:00:09,962 - INFO - Request Parameters - Page 7:
2025-06-15 00:00:09,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:09,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:10,446 - INFO - Response - Page 7:
2025-06-15 00:00:10,649 - INFO - 第 7 页获取到 82 条记录
2025-06-15 00:00:10,649 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 00:00:10,649 - INFO - 获取到 682 条表单数据
2025-06-15 00:00:10,649 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 00:00:10,665 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 00:00:10,665 - INFO - 开始处理日期: 2025-02
2025-06-15 00:00:10,665 - INFO - Request Parameters - Page 1:
2025-06-15 00:00:10,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:10,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:11,134 - INFO - Response - Page 1:
2025-06-15 00:00:11,337 - INFO - 第 1 页获取到 100 条记录
2025-06-15 00:00:11,337 - INFO - Request Parameters - Page 2:
2025-06-15 00:00:11,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:11,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:11,884 - INFO - Response - Page 2:
2025-06-15 00:00:12,087 - INFO - 第 2 页获取到 100 条记录
2025-06-15 00:00:12,087 - INFO - Request Parameters - Page 3:
2025-06-15 00:00:12,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:12,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:12,602 - INFO - Response - Page 3:
2025-06-15 00:00:12,805 - INFO - 第 3 页获取到 100 条记录
2025-06-15 00:00:12,805 - INFO - Request Parameters - Page 4:
2025-06-15 00:00:12,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:12,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:13,305 - INFO - Response - Page 4:
2025-06-15 00:00:13,509 - INFO - 第 4 页获取到 100 条记录
2025-06-15 00:00:13,509 - INFO - Request Parameters - Page 5:
2025-06-15 00:00:13,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:13,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:13,993 - INFO - Response - Page 5:
2025-06-15 00:00:14,196 - INFO - 第 5 页获取到 100 条记录
2025-06-15 00:00:14,196 - INFO - Request Parameters - Page 6:
2025-06-15 00:00:14,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:14,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:14,618 - INFO - Response - Page 6:
2025-06-15 00:00:14,821 - INFO - 第 6 页获取到 100 条记录
2025-06-15 00:00:14,821 - INFO - Request Parameters - Page 7:
2025-06-15 00:00:14,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:14,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:15,243 - INFO - Response - Page 7:
2025-06-15 00:00:15,446 - INFO - 第 7 页获取到 70 条记录
2025-06-15 00:00:15,446 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 00:00:15,446 - INFO - 获取到 670 条表单数据
2025-06-15 00:00:15,446 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 00:00:15,462 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 00:00:15,462 - INFO - 开始处理日期: 2025-03
2025-06-15 00:00:15,462 - INFO - Request Parameters - Page 1:
2025-06-15 00:00:15,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:15,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:16,024 - INFO - Response - Page 1:
2025-06-15 00:00:16,227 - INFO - 第 1 页获取到 100 条记录
2025-06-15 00:00:16,227 - INFO - Request Parameters - Page 2:
2025-06-15 00:00:16,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:16,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:16,727 - INFO - Response - Page 2:
2025-06-15 00:00:16,930 - INFO - 第 2 页获取到 100 条记录
2025-06-15 00:00:16,930 - INFO - Request Parameters - Page 3:
2025-06-15 00:00:16,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:16,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:17,430 - INFO - Response - Page 3:
2025-06-15 00:00:17,633 - INFO - 第 3 页获取到 100 条记录
2025-06-15 00:00:17,633 - INFO - Request Parameters - Page 4:
2025-06-15 00:00:17,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:17,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:18,086 - INFO - Response - Page 4:
2025-06-15 00:00:18,290 - INFO - 第 4 页获取到 100 条记录
2025-06-15 00:00:18,290 - INFO - Request Parameters - Page 5:
2025-06-15 00:00:18,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:18,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:18,836 - INFO - Response - Page 5:
2025-06-15 00:00:19,039 - INFO - 第 5 页获取到 100 条记录
2025-06-15 00:00:19,039 - INFO - Request Parameters - Page 6:
2025-06-15 00:00:19,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:19,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:19,508 - INFO - Response - Page 6:
2025-06-15 00:00:19,711 - INFO - 第 6 页获取到 100 条记录
2025-06-15 00:00:19,711 - INFO - Request Parameters - Page 7:
2025-06-15 00:00:19,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:19,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:20,118 - INFO - Response - Page 7:
2025-06-15 00:00:20,321 - INFO - 第 7 页获取到 61 条记录
2025-06-15 00:00:20,321 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 00:00:20,321 - INFO - 获取到 661 条表单数据
2025-06-15 00:00:20,321 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 00:00:20,336 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 00:00:20,336 - INFO - 开始处理日期: 2025-04
2025-06-15 00:00:20,336 - INFO - Request Parameters - Page 1:
2025-06-15 00:00:20,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:20,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:20,774 - INFO - Response - Page 1:
2025-06-15 00:00:20,977 - INFO - 第 1 页获取到 100 条记录
2025-06-15 00:00:20,977 - INFO - Request Parameters - Page 2:
2025-06-15 00:00:20,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:20,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:21,508 - INFO - Response - Page 2:
2025-06-15 00:00:21,711 - INFO - 第 2 页获取到 100 条记录
2025-06-15 00:00:21,711 - INFO - Request Parameters - Page 3:
2025-06-15 00:00:21,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:21,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:22,227 - INFO - Response - Page 3:
2025-06-15 00:00:22,430 - INFO - 第 3 页获取到 100 条记录
2025-06-15 00:00:22,430 - INFO - Request Parameters - Page 4:
2025-06-15 00:00:22,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:22,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:22,945 - INFO - Response - Page 4:
2025-06-15 00:00:23,149 - INFO - 第 4 页获取到 100 条记录
2025-06-15 00:00:23,149 - INFO - Request Parameters - Page 5:
2025-06-15 00:00:23,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:23,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:23,742 - INFO - Response - Page 5:
2025-06-15 00:00:23,945 - INFO - 第 5 页获取到 100 条记录
2025-06-15 00:00:23,945 - INFO - Request Parameters - Page 6:
2025-06-15 00:00:23,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:23,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:24,555 - INFO - Response - Page 6:
2025-06-15 00:00:24,758 - INFO - 第 6 页获取到 100 条记录
2025-06-15 00:00:24,758 - INFO - Request Parameters - Page 7:
2025-06-15 00:00:24,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:24,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:25,227 - INFO - Response - Page 7:
2025-06-15 00:00:25,430 - INFO - 第 7 页获取到 56 条记录
2025-06-15 00:00:25,430 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 00:00:25,430 - INFO - 获取到 656 条表单数据
2025-06-15 00:00:25,445 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 00:00:25,445 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 00:00:25,445 - INFO - 开始处理日期: 2025-05
2025-06-15 00:00:25,445 - INFO - Request Parameters - Page 1:
2025-06-15 00:00:25,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:25,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:26,008 - INFO - Response - Page 1:
2025-06-15 00:00:26,211 - INFO - 第 1 页获取到 100 条记录
2025-06-15 00:00:26,211 - INFO - Request Parameters - Page 2:
2025-06-15 00:00:26,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:26,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:26,727 - INFO - Response - Page 2:
2025-06-15 00:00:26,930 - INFO - 第 2 页获取到 100 条记录
2025-06-15 00:00:26,930 - INFO - Request Parameters - Page 3:
2025-06-15 00:00:26,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:26,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:27,492 - INFO - Response - Page 3:
2025-06-15 00:00:27,695 - INFO - 第 3 页获取到 100 条记录
2025-06-15 00:00:27,695 - INFO - Request Parameters - Page 4:
2025-06-15 00:00:27,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:27,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:28,273 - INFO - Response - Page 4:
2025-06-15 00:00:28,476 - INFO - 第 4 页获取到 100 条记录
2025-06-15 00:00:28,476 - INFO - Request Parameters - Page 5:
2025-06-15 00:00:28,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:28,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:29,023 - INFO - Response - Page 5:
2025-06-15 00:00:29,226 - INFO - 第 5 页获取到 100 条记录
2025-06-15 00:00:29,226 - INFO - Request Parameters - Page 6:
2025-06-15 00:00:29,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:29,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:29,679 - INFO - Response - Page 6:
2025-06-15 00:00:29,883 - INFO - 第 6 页获取到 100 条记录
2025-06-15 00:00:29,883 - INFO - Request Parameters - Page 7:
2025-06-15 00:00:29,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:29,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:30,304 - INFO - Response - Page 7:
2025-06-15 00:00:30,508 - INFO - 第 7 页获取到 40 条记录
2025-06-15 00:00:30,508 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 00:00:30,508 - INFO - 获取到 640 条表单数据
2025-06-15 00:00:30,508 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 00:00:30,523 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 00:00:30,523 - INFO - 开始处理日期: 2025-06
2025-06-15 00:00:30,523 - INFO - Request Parameters - Page 1:
2025-06-15 00:00:30,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:30,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:31,039 - INFO - Response - Page 1:
2025-06-15 00:00:31,242 - INFO - 第 1 页获取到 100 条记录
2025-06-15 00:00:31,242 - INFO - Request Parameters - Page 2:
2025-06-15 00:00:31,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:31,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:31,742 - INFO - Response - Page 2:
2025-06-15 00:00:31,945 - INFO - 第 2 页获取到 100 条记录
2025-06-15 00:00:31,945 - INFO - Request Parameters - Page 3:
2025-06-15 00:00:31,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:31,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:32,523 - INFO - Response - Page 3:
2025-06-15 00:00:32,726 - INFO - 第 3 页获取到 100 条记录
2025-06-15 00:00:32,726 - INFO - Request Parameters - Page 4:
2025-06-15 00:00:32,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:32,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:33,242 - INFO - Response - Page 4:
2025-06-15 00:00:33,445 - INFO - 第 4 页获取到 100 条记录
2025-06-15 00:00:33,445 - INFO - Request Parameters - Page 5:
2025-06-15 00:00:33,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:33,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:33,976 - INFO - Response - Page 5:
2025-06-15 00:00:34,179 - INFO - 第 5 页获取到 100 条记录
2025-06-15 00:00:34,179 - INFO - Request Parameters - Page 6:
2025-06-15 00:00:34,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:34,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:34,695 - INFO - Response - Page 6:
2025-06-15 00:00:34,898 - INFO - 第 6 页获取到 100 条记录
2025-06-15 00:00:34,898 - INFO - Request Parameters - Page 7:
2025-06-15 00:00:34,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 00:00:34,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 00:00:35,195 - INFO - Response - Page 7:
2025-06-15 00:00:35,398 - INFO - 第 7 页获取到 21 条记录
2025-06-15 00:00:35,398 - INFO - 查询完成，共获取到 621 条记录
2025-06-15 00:00:35,398 - INFO - 获取到 621 条表单数据
2025-06-15 00:00:35,398 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 00:00:35,398 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-15 00:00:35,914 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-15 00:00:35,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29041.3, 'new_value': 30443.8}, {'field': 'total_amount', 'old_value': 29041.3, 'new_value': 30443.8}, {'field': 'order_count', 'old_value': 303, 'new_value': 310}]
2025-06-15 00:00:35,914 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-15 00:00:36,398 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-15 00:00:36,398 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39836.0, 'new_value': 42959.0}, {'field': 'offline_amount', 'old_value': 53329.0, 'new_value': 57461.0}, {'field': 'total_amount', 'old_value': 93165.0, 'new_value': 100420.0}, {'field': 'order_count', 'old_value': 1984, 'new_value': 2116}]
2025-06-15 00:00:36,398 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-15 00:00:36,851 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-15 00:00:36,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 429680.0, 'new_value': 470975.0}, {'field': 'total_amount', 'old_value': 429680.0, 'new_value': 470975.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 102}]
2025-06-15 00:00:36,851 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-15 00:00:37,351 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-15 00:00:37,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 751666.43, 'new_value': 816934.98}, {'field': 'total_amount', 'old_value': 751666.43, 'new_value': 816934.98}, {'field': 'order_count', 'old_value': 8303, 'new_value': 8942}]
2025-06-15 00:00:37,351 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-15 00:00:37,820 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-15 00:00:37,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35644.29, 'new_value': 37937.94}, {'field': 'offline_amount', 'old_value': 46585.72, 'new_value': 49907.65}, {'field': 'total_amount', 'old_value': 82230.01, 'new_value': 87845.59}, {'field': 'order_count', 'old_value': 2763, 'new_value': 2949}]
2025-06-15 00:00:37,820 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-15 00:00:38,226 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-15 00:00:38,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2438.0, 'new_value': 2537.0}, {'field': 'total_amount', 'old_value': 2438.0, 'new_value': 2537.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-15 00:00:38,226 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-15 00:00:38,648 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-15 00:00:38,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36219.0, 'new_value': 38388.0}, {'field': 'total_amount', 'old_value': 36869.0, 'new_value': 39038.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-06-15 00:00:38,648 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-15 00:00:39,132 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-15 00:00:39,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354385.0, 'new_value': 359167.0}, {'field': 'total_amount', 'old_value': 355141.0, 'new_value': 359923.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 109}]
2025-06-15 00:00:39,132 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-15 00:00:39,507 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-15 00:00:39,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4075.0, 'new_value': 4693.0}, {'field': 'total_amount', 'old_value': 4075.0, 'new_value': 4693.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-15 00:00:39,507 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-15 00:00:39,991 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-15 00:00:39,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12656.4, 'new_value': 13478.4}, {'field': 'total_amount', 'old_value': 12656.4, 'new_value': 13478.4}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-06-15 00:00:39,991 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-15 00:00:40,491 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-15 00:00:40,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25432.39, 'new_value': 27407.23}, {'field': 'offline_amount', 'old_value': 350398.34, 'new_value': 377945.44}, {'field': 'total_amount', 'old_value': 375830.73, 'new_value': 405352.67}, {'field': 'order_count', 'old_value': 1579, 'new_value': 1702}]
2025-06-15 00:00:40,491 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-15 00:00:40,882 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-15 00:00:40,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12995.0, 'new_value': 13929.0}, {'field': 'total_amount', 'old_value': 12995.0, 'new_value': 13929.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 57}]
2025-06-15 00:00:40,882 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-15 00:00:41,351 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-15 00:00:41,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227720.0, 'new_value': 284441.0}, {'field': 'total_amount', 'old_value': 231636.0, 'new_value': 288357.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 41}]
2025-06-15 00:00:41,351 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-15 00:00:41,726 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-15 00:00:41,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88271.5, 'new_value': 113634.5}, {'field': 'total_amount', 'old_value': 88271.5, 'new_value': 113634.5}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-06-15 00:00:41,726 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-15 00:00:42,179 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-15 00:00:42,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16779.0, 'new_value': 17676.0}, {'field': 'total_amount', 'old_value': 16779.0, 'new_value': 17676.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-06-15 00:00:42,179 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-15 00:00:42,601 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-15 00:00:42,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174605.12, 'new_value': 193328.27}, {'field': 'total_amount', 'old_value': 174605.12, 'new_value': 193328.27}, {'field': 'order_count', 'old_value': 859, 'new_value': 938}]
2025-06-15 00:00:42,601 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-15 00:00:43,101 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-15 00:00:43,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59923.0, 'new_value': 61002.0}, {'field': 'total_amount', 'old_value': 60206.0, 'new_value': 61285.0}, {'field': 'order_count', 'old_value': 5599, 'new_value': 5600}]
2025-06-15 00:00:43,101 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-15 00:00:43,538 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-15 00:00:43,538 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3906.2, 'new_value': 32666.4}, {'field': 'offline_amount', 'old_value': 78813.0, 'new_value': 97388.0}, {'field': 'total_amount', 'old_value': 82719.2, 'new_value': 130054.4}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-06-15 00:00:43,538 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-15 00:00:43,944 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-15 00:00:43,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29938.0, 'new_value': 36152.0}, {'field': 'total_amount', 'old_value': 34569.0, 'new_value': 40783.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-06-15 00:00:43,944 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-15 00:00:44,397 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-15 00:00:44,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25583.0, 'new_value': 27927.0}, {'field': 'total_amount', 'old_value': 25583.0, 'new_value': 27927.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 78}]
2025-06-15 00:00:44,397 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-15 00:00:44,913 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-15 00:00:44,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64501.7, 'new_value': 68086.7}, {'field': 'total_amount', 'old_value': 74714.0, 'new_value': 78299.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-06-15 00:00:44,913 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-15 00:00:45,319 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-15 00:00:45,319 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24141.2, 'new_value': 27626.56}, {'field': 'offline_amount', 'old_value': 244387.39, 'new_value': 270230.82}, {'field': 'total_amount', 'old_value': 268528.59, 'new_value': 297857.38}, {'field': 'order_count', 'old_value': 1802, 'new_value': 2150}]
2025-06-15 00:00:45,319 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-15 00:00:45,741 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-15 00:00:45,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118894.53, 'new_value': 136079.53}, {'field': 'total_amount', 'old_value': 118894.53, 'new_value': 136079.53}, {'field': 'order_count', 'old_value': 63, 'new_value': 71}]
2025-06-15 00:00:45,741 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-15 00:00:46,194 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-15 00:00:46,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 866.37, 'new_value': 916.37}, {'field': 'offline_amount', 'old_value': 8859.16, 'new_value': 10381.8}, {'field': 'total_amount', 'old_value': 9725.53, 'new_value': 11298.17}, {'field': 'order_count', 'old_value': 200, 'new_value': 221}]
2025-06-15 00:00:46,194 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-15 00:00:46,632 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-15 00:00:46,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24537.0, 'new_value': 26637.0}, {'field': 'total_amount', 'old_value': 24537.0, 'new_value': 26637.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 41}]
2025-06-15 00:00:46,632 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-15 00:00:47,038 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-15 00:00:47,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25067.0, 'new_value': 30925.0}, {'field': 'total_amount', 'old_value': 25067.0, 'new_value': 30925.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-06-15 00:00:47,038 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-15 00:00:47,428 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-15 00:00:47,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41433.5, 'new_value': 47614.6}, {'field': 'total_amount', 'old_value': 41433.5, 'new_value': 47614.6}, {'field': 'order_count', 'old_value': 151, 'new_value': 170}]
2025-06-15 00:00:47,428 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-15 00:00:47,882 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-15 00:00:47,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27695.83, 'new_value': 29722.26}, {'field': 'offline_amount', 'old_value': 159136.81, 'new_value': 176572.41}, {'field': 'total_amount', 'old_value': 186832.64, 'new_value': 206294.67}, {'field': 'order_count', 'old_value': 1281, 'new_value': 1406}]
2025-06-15 00:00:47,882 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-15 00:00:48,257 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-15 00:00:48,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39414.01, 'new_value': 44079.83}, {'field': 'total_amount', 'old_value': 39414.01, 'new_value': 44079.83}, {'field': 'order_count', 'old_value': 1267, 'new_value': 1403}]
2025-06-15 00:00:48,257 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-15 00:00:48,616 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-15 00:00:48,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26474.0, 'new_value': 29258.0}, {'field': 'total_amount', 'old_value': 26474.0, 'new_value': 29258.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 70}]
2025-06-15 00:00:48,616 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-15 00:00:49,053 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-15 00:00:49,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15200.9, 'new_value': 17273.9}, {'field': 'total_amount', 'old_value': 15200.9, 'new_value': 17273.9}, {'field': 'order_count', 'old_value': 96, 'new_value': 108}]
2025-06-15 00:00:49,053 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-15 00:00:49,506 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-15 00:00:49,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161797.9, 'new_value': 177028.9}, {'field': 'total_amount', 'old_value': 161797.9, 'new_value': 177028.9}, {'field': 'order_count', 'old_value': 1603, 'new_value': 1770}]
2025-06-15 00:00:49,506 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-15 00:00:49,944 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-15 00:00:49,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28213.3, 'new_value': 31220.3}, {'field': 'total_amount', 'old_value': 34838.7, 'new_value': 37845.7}, {'field': 'order_count', 'old_value': 85, 'new_value': 92}]
2025-06-15 00:00:49,944 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-15 00:00:50,444 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-15 00:00:50,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 513544.0, 'new_value': 558740.0}, {'field': 'total_amount', 'old_value': 513544.0, 'new_value': 558740.0}, {'field': 'order_count', 'old_value': 529, 'new_value': 592}]
2025-06-15 00:00:50,444 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-15 00:00:50,850 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-15 00:00:50,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54122.79, 'new_value': 59402.79}, {'field': 'total_amount', 'old_value': 54122.79, 'new_value': 59402.79}, {'field': 'order_count', 'old_value': 4484, 'new_value': 4953}]
2025-06-15 00:00:50,850 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-15 00:00:51,272 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-15 00:00:51,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53388.02, 'new_value': 56753.08}, {'field': 'offline_amount', 'old_value': 26886.87, 'new_value': 29700.35}, {'field': 'total_amount', 'old_value': 80274.89, 'new_value': 86453.43}, {'field': 'order_count', 'old_value': 4755, 'new_value': 5112}]
2025-06-15 00:00:51,272 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-15 00:00:51,694 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-15 00:00:51,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201103.55, 'new_value': 230943.33}, {'field': 'total_amount', 'old_value': 276898.03, 'new_value': 306737.81}, {'field': 'order_count', 'old_value': 914, 'new_value': 1000}]
2025-06-15 00:00:51,694 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-15 00:00:52,225 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-15 00:00:52,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28832.59, 'new_value': 32203.3}, {'field': 'offline_amount', 'old_value': 42916.59, 'new_value': 47446.35}, {'field': 'total_amount', 'old_value': 71749.18, 'new_value': 79649.65}, {'field': 'order_count', 'old_value': 2740, 'new_value': 3039}]
2025-06-15 00:00:52,225 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-15 00:00:52,647 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-15 00:00:52,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31998.89, 'new_value': 36038.44}, {'field': 'offline_amount', 'old_value': 44666.64, 'new_value': 49145.78}, {'field': 'total_amount', 'old_value': 76665.53, 'new_value': 85184.22}, {'field': 'order_count', 'old_value': 3779, 'new_value': 4216}]
2025-06-15 00:00:52,647 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-15 00:00:53,100 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-15 00:00:53,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47833.05, 'new_value': 53113.69}, {'field': 'total_amount', 'old_value': 47833.05, 'new_value': 53113.69}, {'field': 'order_count', 'old_value': 1759, 'new_value': 1943}]
2025-06-15 00:00:53,100 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-15 00:00:53,647 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-15 00:00:53,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201964.09, 'new_value': 225019.79}, {'field': 'total_amount', 'old_value': 201964.09, 'new_value': 225019.79}, {'field': 'order_count', 'old_value': 1233, 'new_value': 1327}]
2025-06-15 00:00:53,647 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-15 00:00:54,084 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-15 00:00:54,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10443.6, 'new_value': 12570.6}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'total_amount', 'old_value': 10443.6, 'new_value': 13070.6}, {'field': 'order_count', 'old_value': 51, 'new_value': 60}]
2025-06-15 00:00:54,084 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-15 00:00:54,475 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-15 00:00:54,475 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21381.62, 'new_value': 22459.07}, {'field': 'offline_amount', 'old_value': 11606.45, 'new_value': 12422.05}, {'field': 'total_amount', 'old_value': 32988.07, 'new_value': 34881.12}, {'field': 'order_count', 'old_value': 1381, 'new_value': 1473}]
2025-06-15 00:00:54,475 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-15 00:00:55,053 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-15 00:00:55,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8921.15, 'new_value': 9561.48}, {'field': 'offline_amount', 'old_value': 6285.79, 'new_value': 6987.39}, {'field': 'total_amount', 'old_value': 15206.94, 'new_value': 16548.87}, {'field': 'order_count', 'old_value': 1198, 'new_value': 1298}]
2025-06-15 00:00:55,053 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-15 00:00:55,491 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-15 00:00:55,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199275.0, 'new_value': 215393.0}, {'field': 'total_amount', 'old_value': 199275.0, 'new_value': 215393.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 57}]
2025-06-15 00:00:55,491 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-15 00:00:56,053 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-15 00:00:56,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12398.58, 'new_value': 14451.04}, {'field': 'offline_amount', 'old_value': 130551.02, 'new_value': 145842.6}, {'field': 'total_amount', 'old_value': 142949.6, 'new_value': 160293.64}, {'field': 'order_count', 'old_value': 682, 'new_value': 762}]
2025-06-15 00:00:56,053 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-15 00:00:56,444 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-15 00:00:56,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71469.6, 'new_value': 84268.2}, {'field': 'total_amount', 'old_value': 71469.6, 'new_value': 84268.2}, {'field': 'order_count', 'old_value': 166, 'new_value': 194}]
2025-06-15 00:00:56,444 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-15 00:00:56,912 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-15 00:00:56,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131279.67, 'new_value': 147472.08}, {'field': 'offline_amount', 'old_value': 40768.39, 'new_value': 44149.49}, {'field': 'total_amount', 'old_value': 172048.06, 'new_value': 191621.57}, {'field': 'order_count', 'old_value': 1074, 'new_value': 1168}]
2025-06-15 00:00:56,912 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-15 00:00:57,412 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-15 00:00:57,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68214.0, 'new_value': 76724.0}, {'field': 'total_amount', 'old_value': 72781.0, 'new_value': 81291.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 131}]
2025-06-15 00:00:57,428 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-15 00:00:57,834 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-15 00:00:57,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46642.16, 'new_value': 49307.33}, {'field': 'offline_amount', 'old_value': 161947.51, 'new_value': 185148.59}, {'field': 'total_amount', 'old_value': 208589.67, 'new_value': 234455.92}, {'field': 'order_count', 'old_value': 2256, 'new_value': 2541}]
2025-06-15 00:00:57,834 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-15 00:00:58,240 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-15 00:00:58,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17568.86, 'new_value': 19568.86}, {'field': 'offline_amount', 'old_value': 20033.05, 'new_value': 21295.94}, {'field': 'total_amount', 'old_value': 37601.91, 'new_value': 40864.8}, {'field': 'order_count', 'old_value': 1740, 'new_value': 1888}]
2025-06-15 00:00:58,240 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-15 00:00:58,662 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-15 00:00:58,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43158.27, 'new_value': 46367.52}, {'field': 'offline_amount', 'old_value': 17058.42, 'new_value': 19289.93}, {'field': 'total_amount', 'old_value': 60216.69, 'new_value': 65657.45}, {'field': 'order_count', 'old_value': 3621, 'new_value': 3896}]
2025-06-15 00:00:58,662 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-15 00:00:59,115 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-15 00:00:59,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20339.0, 'new_value': 24366.0}, {'field': 'offline_amount', 'old_value': 63570.46, 'new_value': 74229.46}, {'field': 'total_amount', 'old_value': 83909.46, 'new_value': 98595.46}, {'field': 'order_count', 'old_value': 126, 'new_value': 141}]
2025-06-15 00:00:59,115 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-15 00:00:59,584 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-15 00:00:59,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248565.51, 'new_value': 276735.01}, {'field': 'total_amount', 'old_value': 248565.51, 'new_value': 276735.01}, {'field': 'order_count', 'old_value': 3609, 'new_value': 3971}]
2025-06-15 00:00:59,584 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-15 00:01:00,022 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-15 00:01:00,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47924.4, 'new_value': 55703.03}, {'field': 'offline_amount', 'old_value': 129357.76, 'new_value': 140730.06}, {'field': 'total_amount', 'old_value': 177282.16, 'new_value': 196433.09}, {'field': 'order_count', 'old_value': 2249, 'new_value': 2436}]
2025-06-15 00:01:00,022 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-15 00:01:00,506 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-15 00:01:00,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3008.01, 'new_value': 3281.61}, {'field': 'total_amount', 'old_value': 15748.01, 'new_value': 16021.61}, {'field': 'order_count', 'old_value': 53, 'new_value': 57}]
2025-06-15 00:01:00,506 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-15 00:01:01,037 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-15 00:01:01,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9110.0, 'new_value': 9623.0}, {'field': 'total_amount', 'old_value': 9110.0, 'new_value': 9623.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 115}]
2025-06-15 00:01:01,037 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-15 00:01:01,490 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-15 00:01:01,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32650.91, 'new_value': 35335.69}, {'field': 'offline_amount', 'old_value': 117266.19, 'new_value': 129615.97}, {'field': 'total_amount', 'old_value': 149917.1, 'new_value': 164951.66}, {'field': 'order_count', 'old_value': 3357, 'new_value': 3672}]
2025-06-15 00:01:01,490 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-15 00:01:01,959 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-15 00:01:01,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217064.0, 'new_value': 238267.0}, {'field': 'total_amount', 'old_value': 217064.0, 'new_value': 238267.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 53}]
2025-06-15 00:01:01,959 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-15 00:01:02,396 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-15 00:01:02,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50697.0, 'new_value': 57558.0}, {'field': 'offline_amount', 'old_value': 32446.4, 'new_value': 34149.3}, {'field': 'total_amount', 'old_value': 83143.4, 'new_value': 91707.3}, {'field': 'order_count', 'old_value': 541, 'new_value': 593}]
2025-06-15 00:01:02,396 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-15 00:01:02,803 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-15 00:01:02,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13459.0, 'new_value': 14225.0}, {'field': 'total_amount', 'old_value': 13459.0, 'new_value': 14225.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 59}]
2025-06-15 00:01:02,803 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-15 00:01:03,209 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-15 00:01:03,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18351.14, 'new_value': 18962.54}, {'field': 'total_amount', 'old_value': 18351.14, 'new_value': 18962.54}, {'field': 'order_count', 'old_value': 85, 'new_value': 96}]
2025-06-15 00:01:03,209 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-15 00:01:03,599 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-15 00:01:03,599 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129088.5, 'new_value': 148222.43}, {'field': 'offline_amount', 'old_value': 341371.44, 'new_value': 385762.49}, {'field': 'total_amount', 'old_value': 470459.94, 'new_value': 533984.92}, {'field': 'order_count', 'old_value': 2927, 'new_value': 3290}]
2025-06-15 00:01:03,599 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-15 00:01:04,068 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-15 00:01:04,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57217.61, 'new_value': 61405.13}, {'field': 'offline_amount', 'old_value': 124831.21, 'new_value': 137756.91}, {'field': 'total_amount', 'old_value': 182048.82, 'new_value': 199162.04}, {'field': 'order_count', 'old_value': 6459, 'new_value': 7021}]
2025-06-15 00:01:04,068 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-15 00:01:04,459 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-15 00:01:04,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89181.5, 'new_value': 99181.5}, {'field': 'offline_amount', 'old_value': 108534.91, 'new_value': 119196.93}, {'field': 'total_amount', 'old_value': 197716.41, 'new_value': 218378.43}, {'field': 'order_count', 'old_value': 635, 'new_value': 698}]
2025-06-15 00:01:04,459 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEL
2025-06-15 00:01:04,865 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEL
2025-06-15 00:01:04,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113106.13, 'new_value': 128359.99}, {'field': 'total_amount', 'old_value': 113106.13, 'new_value': 128359.99}, {'field': 'order_count', 'old_value': 669, 'new_value': 753}]
2025-06-15 00:01:04,865 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-15 00:01:05,381 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-15 00:01:05,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 380017.86, 'new_value': 423620.8}, {'field': 'total_amount', 'old_value': 380017.86, 'new_value': 423620.8}, {'field': 'order_count', 'old_value': 2394, 'new_value': 2704}]
2025-06-15 00:01:05,381 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-15 00:01:05,896 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-15 00:01:05,896 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20083.98, 'new_value': 21753.03}, {'field': 'offline_amount', 'old_value': 23160.27, 'new_value': 25308.52}, {'field': 'total_amount', 'old_value': 43244.25, 'new_value': 47061.55}, {'field': 'order_count', 'old_value': 3744, 'new_value': 4069}]
2025-06-15 00:01:05,896 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-15 00:01:06,365 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-15 00:01:06,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8298.0, 'new_value': 8866.0}, {'field': 'total_amount', 'old_value': 8298.0, 'new_value': 8866.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-15 00:01:06,365 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-15 00:01:06,771 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-15 00:01:06,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45035.8, 'new_value': 51732.5}, {'field': 'total_amount', 'old_value': 45035.8, 'new_value': 51732.5}, {'field': 'order_count', 'old_value': 103, 'new_value': 118}]
2025-06-15 00:01:06,771 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-15 00:01:07,334 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-15 00:01:07,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147654.36, 'new_value': 151779.31}, {'field': 'offline_amount', 'old_value': 714752.17, 'new_value': 801758.58}, {'field': 'total_amount', 'old_value': 862406.53, 'new_value': 953537.89}, {'field': 'order_count', 'old_value': 4160, 'new_value': 4576}]
2025-06-15 00:01:07,334 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-15 00:01:07,740 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-15 00:01:07,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 525221.0, 'new_value': 535715.0}, {'field': 'total_amount', 'old_value': 525221.0, 'new_value': 535715.0}, {'field': 'order_count', 'old_value': 299, 'new_value': 308}]
2025-06-15 00:01:07,740 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-15 00:01:08,115 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-15 00:01:08,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 35000.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 35000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-15 00:01:08,115 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-15 00:01:08,568 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-15 00:01:08,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212463.95, 'new_value': 250826.1}, {'field': 'total_amount', 'old_value': 212463.95, 'new_value': 250826.1}, {'field': 'order_count', 'old_value': 867, 'new_value': 988}]
2025-06-15 00:01:08,568 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-15 00:01:09,005 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-15 00:01:09,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16796.0, 'new_value': 17195.0}, {'field': 'total_amount', 'old_value': 16796.0, 'new_value': 17195.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-15 00:01:09,005 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-15 00:01:09,380 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-15 00:01:09,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114355.8, 'new_value': 124070.4}, {'field': 'total_amount', 'old_value': 114355.8, 'new_value': 124070.4}, {'field': 'order_count', 'old_value': 159, 'new_value': 174}]
2025-06-15 00:01:09,380 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-15 00:01:09,849 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-15 00:01:09,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 811566.0, 'new_value': 898604.0}, {'field': 'total_amount', 'old_value': 811566.0, 'new_value': 898604.0}, {'field': 'order_count', 'old_value': 3710, 'new_value': 4086}]
2025-06-15 00:01:09,849 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-15 00:01:10,271 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-15 00:01:10,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157305.79, 'new_value': 171658.59}, {'field': 'total_amount', 'old_value': 171300.79, 'new_value': 185653.59}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-06-15 00:01:10,271 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-15 00:01:10,677 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-15 00:01:10,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67789.8, 'new_value': 74826.6}, {'field': 'offline_amount', 'old_value': 53248.6, 'new_value': 56647.3}, {'field': 'total_amount', 'old_value': 121038.4, 'new_value': 131473.9}, {'field': 'order_count', 'old_value': 2896, 'new_value': 3143}]
2025-06-15 00:01:10,677 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-15 00:01:11,115 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-15 00:01:11,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35943.0, 'new_value': 36242.0}, {'field': 'total_amount', 'old_value': 48530.0, 'new_value': 48829.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-15 00:01:11,115 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-15 00:01:11,552 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-15 00:01:11,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36630.74, 'new_value': 42932.57}, {'field': 'offline_amount', 'old_value': 54675.7, 'new_value': 60200.85}, {'field': 'total_amount', 'old_value': 91306.44, 'new_value': 103133.42}, {'field': 'order_count', 'old_value': 1016, 'new_value': 1151}]
2025-06-15 00:01:11,552 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-15 00:01:12,036 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-15 00:01:12,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129683.25, 'new_value': 150850.88}, {'field': 'total_amount', 'old_value': 129683.25, 'new_value': 150850.88}, {'field': 'order_count', 'old_value': 676, 'new_value': 773}]
2025-06-15 00:01:12,036 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-15 00:01:12,443 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-15 00:01:12,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47663.0, 'new_value': 50544.0}, {'field': 'offline_amount', 'old_value': 82269.0, 'new_value': 87191.0}, {'field': 'total_amount', 'old_value': 129932.0, 'new_value': 137735.0}, {'field': 'order_count', 'old_value': 2686, 'new_value': 2857}]
2025-06-15 00:01:12,443 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-15 00:01:12,880 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-15 00:01:12,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23910.4, 'new_value': 27229.2}, {'field': 'total_amount', 'old_value': 23910.4, 'new_value': 27229.2}, {'field': 'order_count', 'old_value': 193, 'new_value': 226}]
2025-06-15 00:01:12,880 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-15 00:01:13,427 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-15 00:01:13,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2774.1, 'new_value': 4554.9}, {'field': 'total_amount', 'old_value': 2774.1, 'new_value': 4554.9}, {'field': 'order_count', 'old_value': 58, 'new_value': 106}]
2025-06-15 00:01:13,427 - INFO - 开始批量插入 1 条新记录
2025-06-15 00:01:13,630 - INFO - 批量插入响应状态码: 200
2025-06-15 00:01:13,630 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 14 Jun 2025 16:01:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '23BAA4A5-BEA4-75F0-830A-D4D644F1CE75', 'x-acs-trace-id': '487ba1cfc08fd8acc59cd0c24eec8aff', 'etag': '6TeoAdIaHa4tb/LkSKyei6w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-15 00:01:13,630 - INFO - 批量插入响应体: {'result': ['FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C']}
2025-06-15 00:01:13,630 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-15 00:01:13,630 - INFO - 成功插入的数据ID: ['FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C']
2025-06-15 00:01:16,646 - INFO - 批量插入完成，共 1 条记录
2025-06-15 00:01:16,646 - INFO - 日期 2025-06 处理完成 - 更新: 85 条，插入: 1 条，错误: 0 条
2025-06-15 00:01:16,646 - INFO - 数据同步完成！更新: 85 条，插入: 1 条，错误: 0 条
2025-06-15 00:01:16,646 - INFO - =================同步完成====================
2025-06-15 03:00:02,726 - INFO - =================使用默认全量同步=============
2025-06-15 03:00:04,366 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 03:00:04,366 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 03:00:04,413 - INFO - 开始处理日期: 2025-01
2025-06-15 03:00:04,413 - INFO - Request Parameters - Page 1:
2025-06-15 03:00:04,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:04,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:06,022 - INFO - Response - Page 1:
2025-06-15 03:00:06,226 - INFO - 第 1 页获取到 100 条记录
2025-06-15 03:00:06,226 - INFO - Request Parameters - Page 2:
2025-06-15 03:00:06,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:06,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:06,772 - INFO - Response - Page 2:
2025-06-15 03:00:06,976 - INFO - 第 2 页获取到 100 条记录
2025-06-15 03:00:06,976 - INFO - Request Parameters - Page 3:
2025-06-15 03:00:06,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:06,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:07,522 - INFO - Response - Page 3:
2025-06-15 03:00:07,725 - INFO - 第 3 页获取到 100 条记录
2025-06-15 03:00:07,725 - INFO - Request Parameters - Page 4:
2025-06-15 03:00:07,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:07,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:08,225 - INFO - Response - Page 4:
2025-06-15 03:00:08,429 - INFO - 第 4 页获取到 100 条记录
2025-06-15 03:00:08,429 - INFO - Request Parameters - Page 5:
2025-06-15 03:00:08,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:08,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:08,960 - INFO - Response - Page 5:
2025-06-15 03:00:09,163 - INFO - 第 5 页获取到 100 条记录
2025-06-15 03:00:09,163 - INFO - Request Parameters - Page 6:
2025-06-15 03:00:09,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:09,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:09,710 - INFO - Response - Page 6:
2025-06-15 03:00:09,913 - INFO - 第 6 页获取到 100 条记录
2025-06-15 03:00:09,913 - INFO - Request Parameters - Page 7:
2025-06-15 03:00:09,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:09,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:10,428 - INFO - Response - Page 7:
2025-06-15 03:00:10,632 - INFO - 第 7 页获取到 82 条记录
2025-06-15 03:00:10,632 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 03:00:10,632 - INFO - 获取到 682 条表单数据
2025-06-15 03:00:10,632 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 03:00:10,647 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:10,647 - INFO - 开始处理日期: 2025-02
2025-06-15 03:00:10,647 - INFO - Request Parameters - Page 1:
2025-06-15 03:00:10,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:10,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:11,147 - INFO - Response - Page 1:
2025-06-15 03:00:11,350 - INFO - 第 1 页获取到 100 条记录
2025-06-15 03:00:11,350 - INFO - Request Parameters - Page 2:
2025-06-15 03:00:11,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:11,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:11,850 - INFO - Response - Page 2:
2025-06-15 03:00:12,053 - INFO - 第 2 页获取到 100 条记录
2025-06-15 03:00:12,053 - INFO - Request Parameters - Page 3:
2025-06-15 03:00:12,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:12,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:12,506 - INFO - Response - Page 3:
2025-06-15 03:00:12,710 - INFO - 第 3 页获取到 100 条记录
2025-06-15 03:00:12,710 - INFO - Request Parameters - Page 4:
2025-06-15 03:00:12,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:12,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:13,163 - INFO - Response - Page 4:
2025-06-15 03:00:13,381 - INFO - 第 4 页获取到 100 条记录
2025-06-15 03:00:13,381 - INFO - Request Parameters - Page 5:
2025-06-15 03:00:13,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:13,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:13,850 - INFO - Response - Page 5:
2025-06-15 03:00:14,053 - INFO - 第 5 页获取到 100 条记录
2025-06-15 03:00:14,053 - INFO - Request Parameters - Page 6:
2025-06-15 03:00:14,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:14,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:14,553 - INFO - Response - Page 6:
2025-06-15 03:00:14,756 - INFO - 第 6 页获取到 100 条记录
2025-06-15 03:00:14,756 - INFO - Request Parameters - Page 7:
2025-06-15 03:00:14,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:14,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:15,319 - INFO - Response - Page 7:
2025-06-15 03:00:15,522 - INFO - 第 7 页获取到 70 条记录
2025-06-15 03:00:15,522 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 03:00:15,522 - INFO - 获取到 670 条表单数据
2025-06-15 03:00:15,522 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 03:00:15,538 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:15,538 - INFO - 开始处理日期: 2025-03
2025-06-15 03:00:15,538 - INFO - Request Parameters - Page 1:
2025-06-15 03:00:15,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:15,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:16,116 - INFO - Response - Page 1:
2025-06-15 03:00:16,319 - INFO - 第 1 页获取到 100 条记录
2025-06-15 03:00:16,319 - INFO - Request Parameters - Page 2:
2025-06-15 03:00:16,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:16,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:16,928 - INFO - Response - Page 2:
2025-06-15 03:00:17,131 - INFO - 第 2 页获取到 100 条记录
2025-06-15 03:00:17,131 - INFO - Request Parameters - Page 3:
2025-06-15 03:00:17,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:17,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:17,662 - INFO - Response - Page 3:
2025-06-15 03:00:17,865 - INFO - 第 3 页获取到 100 条记录
2025-06-15 03:00:17,865 - INFO - Request Parameters - Page 4:
2025-06-15 03:00:17,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:17,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:18,272 - INFO - Response - Page 4:
2025-06-15 03:00:18,475 - INFO - 第 4 页获取到 100 条记录
2025-06-15 03:00:18,475 - INFO - Request Parameters - Page 5:
2025-06-15 03:00:18,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:18,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:19,053 - INFO - Response - Page 5:
2025-06-15 03:00:19,256 - INFO - 第 5 页获取到 100 条记录
2025-06-15 03:00:19,256 - INFO - Request Parameters - Page 6:
2025-06-15 03:00:19,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:19,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:19,740 - INFO - Response - Page 6:
2025-06-15 03:00:19,944 - INFO - 第 6 页获取到 100 条记录
2025-06-15 03:00:19,944 - INFO - Request Parameters - Page 7:
2025-06-15 03:00:19,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:19,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:20,334 - INFO - Response - Page 7:
2025-06-15 03:00:20,537 - INFO - 第 7 页获取到 61 条记录
2025-06-15 03:00:20,537 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 03:00:20,537 - INFO - 获取到 661 条表单数据
2025-06-15 03:00:20,537 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 03:00:20,553 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:20,553 - INFO - 开始处理日期: 2025-04
2025-06-15 03:00:20,553 - INFO - Request Parameters - Page 1:
2025-06-15 03:00:20,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:20,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:21,068 - INFO - Response - Page 1:
2025-06-15 03:00:21,272 - INFO - 第 1 页获取到 100 条记录
2025-06-15 03:00:21,272 - INFO - Request Parameters - Page 2:
2025-06-15 03:00:21,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:21,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:21,834 - INFO - Response - Page 2:
2025-06-15 03:00:22,037 - INFO - 第 2 页获取到 100 条记录
2025-06-15 03:00:22,037 - INFO - Request Parameters - Page 3:
2025-06-15 03:00:22,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:22,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:22,521 - INFO - Response - Page 3:
2025-06-15 03:00:22,725 - INFO - 第 3 页获取到 100 条记录
2025-06-15 03:00:22,725 - INFO - Request Parameters - Page 4:
2025-06-15 03:00:22,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:22,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:23,303 - INFO - Response - Page 4:
2025-06-15 03:00:23,506 - INFO - 第 4 页获取到 100 条记录
2025-06-15 03:00:23,506 - INFO - Request Parameters - Page 5:
2025-06-15 03:00:23,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:23,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:23,975 - INFO - Response - Page 5:
2025-06-15 03:00:24,178 - INFO - 第 5 页获取到 100 条记录
2025-06-15 03:00:24,178 - INFO - Request Parameters - Page 6:
2025-06-15 03:00:24,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:24,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:24,678 - INFO - Response - Page 6:
2025-06-15 03:00:24,881 - INFO - 第 6 页获取到 100 条记录
2025-06-15 03:00:24,881 - INFO - Request Parameters - Page 7:
2025-06-15 03:00:24,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:24,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:25,365 - INFO - Response - Page 7:
2025-06-15 03:00:25,568 - INFO - 第 7 页获取到 56 条记录
2025-06-15 03:00:25,568 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 03:00:25,568 - INFO - 获取到 656 条表单数据
2025-06-15 03:00:25,568 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 03:00:25,584 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:25,584 - INFO - 开始处理日期: 2025-05
2025-06-15 03:00:25,584 - INFO - Request Parameters - Page 1:
2025-06-15 03:00:25,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:25,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:26,037 - INFO - Response - Page 1:
2025-06-15 03:00:26,240 - INFO - 第 1 页获取到 100 条记录
2025-06-15 03:00:26,240 - INFO - Request Parameters - Page 2:
2025-06-15 03:00:26,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:26,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:26,709 - INFO - Response - Page 2:
2025-06-15 03:00:26,912 - INFO - 第 2 页获取到 100 条记录
2025-06-15 03:00:26,912 - INFO - Request Parameters - Page 3:
2025-06-15 03:00:26,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:26,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:27,427 - INFO - Response - Page 3:
2025-06-15 03:00:27,646 - INFO - 第 3 页获取到 100 条记录
2025-06-15 03:00:27,646 - INFO - Request Parameters - Page 4:
2025-06-15 03:00:27,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:27,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:28,146 - INFO - Response - Page 4:
2025-06-15 03:00:28,349 - INFO - 第 4 页获取到 100 条记录
2025-06-15 03:00:28,349 - INFO - Request Parameters - Page 5:
2025-06-15 03:00:28,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:28,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:28,865 - INFO - Response - Page 5:
2025-06-15 03:00:29,068 - INFO - 第 5 页获取到 100 条记录
2025-06-15 03:00:29,068 - INFO - Request Parameters - Page 6:
2025-06-15 03:00:29,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:29,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:29,615 - INFO - Response - Page 6:
2025-06-15 03:00:29,818 - INFO - 第 6 页获取到 100 条记录
2025-06-15 03:00:29,818 - INFO - Request Parameters - Page 7:
2025-06-15 03:00:29,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:29,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:30,224 - INFO - Response - Page 7:
2025-06-15 03:00:30,427 - INFO - 第 7 页获取到 40 条记录
2025-06-15 03:00:30,427 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 03:00:30,427 - INFO - 获取到 640 条表单数据
2025-06-15 03:00:30,427 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 03:00:30,443 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:30,443 - INFO - 开始处理日期: 2025-06
2025-06-15 03:00:30,443 - INFO - Request Parameters - Page 1:
2025-06-15 03:00:30,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:30,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:31,005 - INFO - Response - Page 1:
2025-06-15 03:00:31,208 - INFO - 第 1 页获取到 100 条记录
2025-06-15 03:00:31,208 - INFO - Request Parameters - Page 2:
2025-06-15 03:00:31,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:31,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:31,771 - INFO - Response - Page 2:
2025-06-15 03:00:31,974 - INFO - 第 2 页获取到 100 条记录
2025-06-15 03:00:31,974 - INFO - Request Parameters - Page 3:
2025-06-15 03:00:31,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:31,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:32,474 - INFO - Response - Page 3:
2025-06-15 03:00:32,677 - INFO - 第 3 页获取到 100 条记录
2025-06-15 03:00:32,677 - INFO - Request Parameters - Page 4:
2025-06-15 03:00:32,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:32,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:33,115 - INFO - Response - Page 4:
2025-06-15 03:00:33,318 - INFO - 第 4 页获取到 100 条记录
2025-06-15 03:00:33,318 - INFO - Request Parameters - Page 5:
2025-06-15 03:00:33,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:33,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:33,865 - INFO - Response - Page 5:
2025-06-15 03:00:34,068 - INFO - 第 5 页获取到 100 条记录
2025-06-15 03:00:34,068 - INFO - Request Parameters - Page 6:
2025-06-15 03:00:34,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:34,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:34,568 - INFO - Response - Page 6:
2025-06-15 03:00:34,771 - INFO - 第 6 页获取到 100 条记录
2025-06-15 03:00:34,771 - INFO - Request Parameters - Page 7:
2025-06-15 03:00:34,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 03:00:34,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 03:00:35,146 - INFO - Response - Page 7:
2025-06-15 03:00:35,349 - INFO - 第 7 页获取到 22 条记录
2025-06-15 03:00:35,349 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 03:00:35,349 - INFO - 获取到 622 条表单数据
2025-06-15 03:00:35,349 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 03:00:35,364 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:35,364 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 03:00:35,364 - INFO - =================同步完成====================
2025-06-15 06:00:03,206 - INFO - =================使用默认全量同步=============
2025-06-15 06:00:04,893 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 06:00:04,893 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 06:00:04,925 - INFO - 开始处理日期: 2025-01
2025-06-15 06:00:04,940 - INFO - Request Parameters - Page 1:
2025-06-15 06:00:04,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:04,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:06,253 - INFO - Response - Page 1:
2025-06-15 06:00:06,456 - INFO - 第 1 页获取到 100 条记录
2025-06-15 06:00:06,456 - INFO - Request Parameters - Page 2:
2025-06-15 06:00:06,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:06,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:07,018 - INFO - Response - Page 2:
2025-06-15 06:00:07,222 - INFO - 第 2 页获取到 100 条记录
2025-06-15 06:00:07,222 - INFO - Request Parameters - Page 3:
2025-06-15 06:00:07,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:07,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:08,143 - INFO - Response - Page 3:
2025-06-15 06:00:08,347 - INFO - 第 3 页获取到 100 条记录
2025-06-15 06:00:08,347 - INFO - Request Parameters - Page 4:
2025-06-15 06:00:08,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:08,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:08,893 - INFO - Response - Page 4:
2025-06-15 06:00:09,097 - INFO - 第 4 页获取到 100 条记录
2025-06-15 06:00:09,097 - INFO - Request Parameters - Page 5:
2025-06-15 06:00:09,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:09,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:09,643 - INFO - Response - Page 5:
2025-06-15 06:00:09,847 - INFO - 第 5 页获取到 100 条记录
2025-06-15 06:00:09,847 - INFO - Request Parameters - Page 6:
2025-06-15 06:00:09,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:09,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:10,472 - INFO - Response - Page 6:
2025-06-15 06:00:10,675 - INFO - 第 6 页获取到 100 条记录
2025-06-15 06:00:10,675 - INFO - Request Parameters - Page 7:
2025-06-15 06:00:10,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:10,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:11,222 - INFO - Response - Page 7:
2025-06-15 06:00:11,425 - INFO - 第 7 页获取到 82 条记录
2025-06-15 06:00:11,425 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 06:00:11,425 - INFO - 获取到 682 条表单数据
2025-06-15 06:00:11,425 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 06:00:11,440 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:11,440 - INFO - 开始处理日期: 2025-02
2025-06-15 06:00:11,440 - INFO - Request Parameters - Page 1:
2025-06-15 06:00:11,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:11,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:11,956 - INFO - Response - Page 1:
2025-06-15 06:00:12,159 - INFO - 第 1 页获取到 100 条记录
2025-06-15 06:00:12,159 - INFO - Request Parameters - Page 2:
2025-06-15 06:00:12,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:12,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:12,659 - INFO - Response - Page 2:
2025-06-15 06:00:12,862 - INFO - 第 2 页获取到 100 条记录
2025-06-15 06:00:12,862 - INFO - Request Parameters - Page 3:
2025-06-15 06:00:12,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:12,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:13,362 - INFO - Response - Page 3:
2025-06-15 06:00:13,565 - INFO - 第 3 页获取到 100 条记录
2025-06-15 06:00:13,565 - INFO - Request Parameters - Page 4:
2025-06-15 06:00:13,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:13,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:14,034 - INFO - Response - Page 4:
2025-06-15 06:00:14,237 - INFO - 第 4 页获取到 100 条记录
2025-06-15 06:00:14,237 - INFO - Request Parameters - Page 5:
2025-06-15 06:00:14,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:14,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:14,831 - INFO - Response - Page 5:
2025-06-15 06:00:15,034 - INFO - 第 5 页获取到 100 条记录
2025-06-15 06:00:15,034 - INFO - Request Parameters - Page 6:
2025-06-15 06:00:15,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:15,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:15,612 - INFO - Response - Page 6:
2025-06-15 06:00:15,815 - INFO - 第 6 页获取到 100 条记录
2025-06-15 06:00:15,815 - INFO - Request Parameters - Page 7:
2025-06-15 06:00:15,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:15,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:16,315 - INFO - Response - Page 7:
2025-06-15 06:00:16,518 - INFO - 第 7 页获取到 70 条记录
2025-06-15 06:00:16,518 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 06:00:16,518 - INFO - 获取到 670 条表单数据
2025-06-15 06:00:16,518 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 06:00:16,534 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:16,534 - INFO - 开始处理日期: 2025-03
2025-06-15 06:00:16,534 - INFO - Request Parameters - Page 1:
2025-06-15 06:00:16,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:16,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:17,018 - INFO - Response - Page 1:
2025-06-15 06:00:17,221 - INFO - 第 1 页获取到 100 条记录
2025-06-15 06:00:17,221 - INFO - Request Parameters - Page 2:
2025-06-15 06:00:17,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:17,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:17,737 - INFO - Response - Page 2:
2025-06-15 06:00:17,940 - INFO - 第 2 页获取到 100 条记录
2025-06-15 06:00:17,940 - INFO - Request Parameters - Page 3:
2025-06-15 06:00:17,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:17,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:18,518 - INFO - Response - Page 3:
2025-06-15 06:00:18,721 - INFO - 第 3 页获取到 100 条记录
2025-06-15 06:00:18,721 - INFO - Request Parameters - Page 4:
2025-06-15 06:00:18,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:18,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:19,190 - INFO - Response - Page 4:
2025-06-15 06:00:19,393 - INFO - 第 4 页获取到 100 条记录
2025-06-15 06:00:19,393 - INFO - Request Parameters - Page 5:
2025-06-15 06:00:19,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:19,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:19,940 - INFO - Response - Page 5:
2025-06-15 06:00:20,143 - INFO - 第 5 页获取到 100 条记录
2025-06-15 06:00:20,143 - INFO - Request Parameters - Page 6:
2025-06-15 06:00:20,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:20,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:20,628 - INFO - Response - Page 6:
2025-06-15 06:00:20,831 - INFO - 第 6 页获取到 100 条记录
2025-06-15 06:00:20,831 - INFO - Request Parameters - Page 7:
2025-06-15 06:00:20,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:20,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:21,315 - INFO - Response - Page 7:
2025-06-15 06:00:21,518 - INFO - 第 7 页获取到 61 条记录
2025-06-15 06:00:21,518 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 06:00:21,518 - INFO - 获取到 661 条表单数据
2025-06-15 06:00:21,518 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 06:00:21,534 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:21,534 - INFO - 开始处理日期: 2025-04
2025-06-15 06:00:21,534 - INFO - Request Parameters - Page 1:
2025-06-15 06:00:21,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:21,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:21,987 - INFO - Response - Page 1:
2025-06-15 06:00:22,190 - INFO - 第 1 页获取到 100 条记录
2025-06-15 06:00:22,190 - INFO - Request Parameters - Page 2:
2025-06-15 06:00:22,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:22,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:22,768 - INFO - Response - Page 2:
2025-06-15 06:00:22,971 - INFO - 第 2 页获取到 100 条记录
2025-06-15 06:00:22,971 - INFO - Request Parameters - Page 3:
2025-06-15 06:00:22,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:22,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:23,565 - INFO - Response - Page 3:
2025-06-15 06:00:23,768 - INFO - 第 3 页获取到 100 条记录
2025-06-15 06:00:23,768 - INFO - Request Parameters - Page 4:
2025-06-15 06:00:23,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:23,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:24,268 - INFO - Response - Page 4:
2025-06-15 06:00:24,471 - INFO - 第 4 页获取到 100 条记录
2025-06-15 06:00:24,471 - INFO - Request Parameters - Page 5:
2025-06-15 06:00:24,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:24,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:24,940 - INFO - Response - Page 5:
2025-06-15 06:00:25,143 - INFO - 第 5 页获取到 100 条记录
2025-06-15 06:00:25,143 - INFO - Request Parameters - Page 6:
2025-06-15 06:00:25,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:25,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:25,675 - INFO - Response - Page 6:
2025-06-15 06:00:25,878 - INFO - 第 6 页获取到 100 条记录
2025-06-15 06:00:25,878 - INFO - Request Parameters - Page 7:
2025-06-15 06:00:25,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:25,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:26,425 - INFO - Response - Page 7:
2025-06-15 06:00:26,628 - INFO - 第 7 页获取到 56 条记录
2025-06-15 06:00:26,628 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 06:00:26,628 - INFO - 获取到 656 条表单数据
2025-06-15 06:00:26,628 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 06:00:26,643 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:26,643 - INFO - 开始处理日期: 2025-05
2025-06-15 06:00:26,643 - INFO - Request Parameters - Page 1:
2025-06-15 06:00:26,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:26,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:27,096 - INFO - Response - Page 1:
2025-06-15 06:00:27,300 - INFO - 第 1 页获取到 100 条记录
2025-06-15 06:00:27,300 - INFO - Request Parameters - Page 2:
2025-06-15 06:00:27,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:27,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:27,721 - INFO - Response - Page 2:
2025-06-15 06:00:27,925 - INFO - 第 2 页获取到 100 条记录
2025-06-15 06:00:27,925 - INFO - Request Parameters - Page 3:
2025-06-15 06:00:27,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:27,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:28,440 - INFO - Response - Page 3:
2025-06-15 06:00:28,643 - INFO - 第 3 页获取到 100 条记录
2025-06-15 06:00:28,643 - INFO - Request Parameters - Page 4:
2025-06-15 06:00:28,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:28,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:29,190 - INFO - Response - Page 4:
2025-06-15 06:00:29,393 - INFO - 第 4 页获取到 100 条记录
2025-06-15 06:00:29,393 - INFO - Request Parameters - Page 5:
2025-06-15 06:00:29,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:29,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:29,909 - INFO - Response - Page 5:
2025-06-15 06:00:30,112 - INFO - 第 5 页获取到 100 条记录
2025-06-15 06:00:30,112 - INFO - Request Parameters - Page 6:
2025-06-15 06:00:30,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:30,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:30,643 - INFO - Response - Page 6:
2025-06-15 06:00:30,846 - INFO - 第 6 页获取到 100 条记录
2025-06-15 06:00:30,846 - INFO - Request Parameters - Page 7:
2025-06-15 06:00:30,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:30,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:31,253 - INFO - Response - Page 7:
2025-06-15 06:00:31,456 - INFO - 第 7 页获取到 40 条记录
2025-06-15 06:00:31,456 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 06:00:31,456 - INFO - 获取到 640 条表单数据
2025-06-15 06:00:31,456 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 06:00:31,471 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:31,471 - INFO - 开始处理日期: 2025-06
2025-06-15 06:00:31,471 - INFO - Request Parameters - Page 1:
2025-06-15 06:00:31,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:31,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:31,909 - INFO - Response - Page 1:
2025-06-15 06:00:32,112 - INFO - 第 1 页获取到 100 条记录
2025-06-15 06:00:32,112 - INFO - Request Parameters - Page 2:
2025-06-15 06:00:32,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:32,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:32,565 - INFO - Response - Page 2:
2025-06-15 06:00:32,768 - INFO - 第 2 页获取到 100 条记录
2025-06-15 06:00:32,768 - INFO - Request Parameters - Page 3:
2025-06-15 06:00:32,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:32,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:33,237 - INFO - Response - Page 3:
2025-06-15 06:00:33,440 - INFO - 第 3 页获取到 100 条记录
2025-06-15 06:00:33,440 - INFO - Request Parameters - Page 4:
2025-06-15 06:00:33,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:33,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:33,924 - INFO - Response - Page 4:
2025-06-15 06:00:34,128 - INFO - 第 4 页获取到 100 条记录
2025-06-15 06:00:34,128 - INFO - Request Parameters - Page 5:
2025-06-15 06:00:34,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:34,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:34,612 - INFO - Response - Page 5:
2025-06-15 06:00:34,815 - INFO - 第 5 页获取到 100 条记录
2025-06-15 06:00:34,815 - INFO - Request Parameters - Page 6:
2025-06-15 06:00:34,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:34,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:35,284 - INFO - Response - Page 6:
2025-06-15 06:00:35,487 - INFO - 第 6 页获取到 100 条记录
2025-06-15 06:00:35,487 - INFO - Request Parameters - Page 7:
2025-06-15 06:00:35,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 06:00:35,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 06:00:35,831 - INFO - Response - Page 7:
2025-06-15 06:00:36,034 - INFO - 第 7 页获取到 22 条记录
2025-06-15 06:00:36,034 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 06:00:36,034 - INFO - 获取到 622 条表单数据
2025-06-15 06:00:36,034 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 06:00:36,034 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-15 06:00:36,549 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-15 06:00:36,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106202.0, 'new_value': 116622.0}, {'field': 'total_amount', 'old_value': 106202.0, 'new_value': 116622.0}, {'field': 'order_count', 'old_value': 1634, 'new_value': 1802}]
2025-06-15 06:00:36,549 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-15 06:00:36,987 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-15 06:00:36,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74410.95, 'new_value': 82520.78}, {'field': 'total_amount', 'old_value': 74410.95, 'new_value': 82520.78}, {'field': 'order_count', 'old_value': 1907, 'new_value': 2112}]
2025-06-15 06:00:36,987 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-15 06:00:37,424 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-15 06:00:37,424 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11174.71, 'new_value': 15174.71}, {'field': 'offline_amount', 'old_value': 91691.51, 'new_value': 92387.51}, {'field': 'total_amount', 'old_value': 102866.22, 'new_value': 107562.22}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-06-15 06:00:37,424 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-15 06:00:37,940 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-15 06:00:37,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82187.0, 'new_value': 90330.0}, {'field': 'total_amount', 'old_value': 82187.0, 'new_value': 90330.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 336}]
2025-06-15 06:00:37,940 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-15 06:00:38,378 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-15 06:00:38,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152173.0, 'new_value': 164714.0}, {'field': 'total_amount', 'old_value': 187173.0, 'new_value': 199714.0}, {'field': 'order_count', 'old_value': 5634, 'new_value': 5766}]
2025-06-15 06:00:38,378 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-15 06:00:38,878 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-15 06:00:38,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341474.69, 'new_value': 376018.08}, {'field': 'total_amount', 'old_value': 341474.69, 'new_value': 376018.08}, {'field': 'order_count', 'old_value': 5703, 'new_value': 6072}]
2025-06-15 06:00:38,878 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-15 06:00:39,456 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-15 06:00:39,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22013.23, 'new_value': 23540.7}, {'field': 'offline_amount', 'old_value': 150564.59, 'new_value': 165443.05}, {'field': 'total_amount', 'old_value': 172577.82, 'new_value': 188983.75}, {'field': 'order_count', 'old_value': 3827, 'new_value': 4262}]
2025-06-15 06:00:39,456 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-15 06:00:39,878 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-15 06:00:39,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5492.43, 'new_value': 5853.73}, {'field': 'offline_amount', 'old_value': 15759.79, 'new_value': 17473.15}, {'field': 'total_amount', 'old_value': 21252.22, 'new_value': 23326.88}, {'field': 'order_count', 'old_value': 726, 'new_value': 825}]
2025-06-15 06:00:39,878 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-15 06:00:40,268 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-15 06:00:40,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101667.24, 'new_value': 112943.98}, {'field': 'total_amount', 'old_value': 139088.65, 'new_value': 150365.39}, {'field': 'order_count', 'old_value': 6129, 'new_value': 6688}]
2025-06-15 06:00:40,284 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-15 06:00:40,753 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-15 06:00:40,753 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40454.87, 'new_value': 43420.14}, {'field': 'offline_amount', 'old_value': 38189.7, 'new_value': 41862.47}, {'field': 'total_amount', 'old_value': 78644.57, 'new_value': 85282.61}, {'field': 'order_count', 'old_value': 3613, 'new_value': 4023}]
2025-06-15 06:00:40,753 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-15 06:00:41,237 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-15 06:00:41,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6180.1, 'new_value': 6895.92}, {'field': 'offline_amount', 'old_value': 15926.91, 'new_value': 17761.21}, {'field': 'total_amount', 'old_value': 22107.01, 'new_value': 24657.13}, {'field': 'order_count', 'old_value': 1042, 'new_value': 1168}]
2025-06-15 06:00:41,237 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-15 06:00:41,690 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-15 06:00:41,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30686.6, 'new_value': 33827.8}, {'field': 'offline_amount', 'old_value': 85443.0, 'new_value': 89830.0}, {'field': 'total_amount', 'old_value': 116129.6, 'new_value': 123657.8}, {'field': 'order_count', 'old_value': 2334, 'new_value': 2484}]
2025-06-15 06:00:41,690 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-15 06:00:42,065 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-15 06:00:42,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1944.64, 'new_value': 2976.22}, {'field': 'offline_amount', 'old_value': 182216.16, 'new_value': 199365.16}, {'field': 'total_amount', 'old_value': 184160.8, 'new_value': 202341.38}, {'field': 'order_count', 'old_value': 9285, 'new_value': 10137}]
2025-06-15 06:00:42,065 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-15 06:00:42,549 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-15 06:00:42,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15091.62, 'new_value': 15866.42}, {'field': 'offline_amount', 'old_value': 103626.2, 'new_value': 116775.2}, {'field': 'total_amount', 'old_value': 118717.82, 'new_value': 132641.62}, {'field': 'order_count', 'old_value': 3731, 'new_value': 4180}]
2025-06-15 06:00:42,549 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-15 06:00:42,940 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-15 06:00:42,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85533.0, 'new_value': 93871.0}, {'field': 'total_amount', 'old_value': 85533.0, 'new_value': 93871.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 76}]
2025-06-15 06:00:42,940 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-15 06:00:43,362 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-15 06:00:43,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76075.53, 'new_value': 85018.43}, {'field': 'total_amount', 'old_value': 76075.53, 'new_value': 85018.43}, {'field': 'order_count', 'old_value': 3481, 'new_value': 3861}]
2025-06-15 06:00:43,362 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-15 06:00:43,862 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-15 06:00:43,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110487.04, 'new_value': 124098.41}, {'field': 'total_amount', 'old_value': 110487.04, 'new_value': 124098.41}, {'field': 'order_count', 'old_value': 874, 'new_value': 967}]
2025-06-15 06:00:43,862 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-15 06:00:44,253 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-15 06:00:44,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142668.69, 'new_value': 158305.6}, {'field': 'total_amount', 'old_value': 142668.69, 'new_value': 158305.6}, {'field': 'order_count', 'old_value': 2943, 'new_value': 3295}]
2025-06-15 06:00:44,253 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-15 06:00:44,706 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-15 06:00:44,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214235.37, 'new_value': 240373.9}, {'field': 'total_amount', 'old_value': 214235.37, 'new_value': 240373.9}, {'field': 'order_count', 'old_value': 8759, 'new_value': 9702}]
2025-06-15 06:00:44,706 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-15 06:00:45,159 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-15 06:00:45,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27863.97, 'new_value': 30707.61}, {'field': 'offline_amount', 'old_value': 190948.86, 'new_value': 213896.67}, {'field': 'total_amount', 'old_value': 218812.83, 'new_value': 244604.28}, {'field': 'order_count', 'old_value': 1805, 'new_value': 1998}]
2025-06-15 06:00:45,159 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-15 06:00:45,659 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-15 06:00:45,659 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 165297.69, 'new_value': 179823.89}, {'field': 'offline_amount', 'old_value': 15265.59, 'new_value': 16051.29}, {'field': 'total_amount', 'old_value': 180563.28, 'new_value': 195875.18}, {'field': 'order_count', 'old_value': 6978, 'new_value': 7577}]
2025-06-15 06:00:45,659 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-15 06:00:46,253 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-15 06:00:46,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68615.0, 'new_value': 73777.0}, {'field': 'total_amount', 'old_value': 68615.0, 'new_value': 73777.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 123}]
2025-06-15 06:00:46,253 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-15 06:00:46,706 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-15 06:00:46,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1976.0, 'new_value': 2250.0}, {'field': 'offline_amount', 'old_value': 14759.6, 'new_value': 16461.6}, {'field': 'total_amount', 'old_value': 16735.6, 'new_value': 18711.6}, {'field': 'order_count', 'old_value': 559, 'new_value': 629}]
2025-06-15 06:00:46,706 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-15 06:00:47,237 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-15 06:00:47,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67662.4, 'new_value': 74229.35}, {'field': 'offline_amount', 'old_value': 107818.21, 'new_value': 117901.27}, {'field': 'total_amount', 'old_value': 175480.61, 'new_value': 192130.62}, {'field': 'order_count', 'old_value': 5749, 'new_value': 6261}]
2025-06-15 06:00:47,237 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-15 06:00:47,690 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-15 06:00:47,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116226.73, 'new_value': 126987.83}, {'field': 'offline_amount', 'old_value': 160114.51, 'new_value': 177813.81}, {'field': 'total_amount', 'old_value': 276341.24, 'new_value': 304801.64}, {'field': 'order_count', 'old_value': 8681, 'new_value': 9472}]
2025-06-15 06:00:47,690 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-15 06:00:48,112 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-15 06:00:48,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79406.99, 'new_value': 88017.5}, {'field': 'total_amount', 'old_value': 101477.22, 'new_value': 110087.73}, {'field': 'order_count', 'old_value': 5691, 'new_value': 6192}]
2025-06-15 06:00:48,128 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-15 06:00:48,565 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-15 06:00:48,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47951.83, 'new_value': 51353.29}, {'field': 'total_amount', 'old_value': 47951.83, 'new_value': 51353.29}, {'field': 'order_count', 'old_value': 3077, 'new_value': 3283}]
2025-06-15 06:00:48,565 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-15 06:00:49,003 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-15 06:00:49,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5250.49, 'new_value': 6045.17}, {'field': 'offline_amount', 'old_value': 39653.26, 'new_value': 44019.81}, {'field': 'total_amount', 'old_value': 44903.75, 'new_value': 50064.98}, {'field': 'order_count', 'old_value': 1434, 'new_value': 1577}]
2025-06-15 06:00:49,003 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-15 06:00:49,487 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-15 06:00:49,487 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29557.7, 'new_value': 38557.7}, {'field': 'offline_amount', 'old_value': 55945.74, 'new_value': 56324.64}, {'field': 'total_amount', 'old_value': 85503.44, 'new_value': 94882.34}, {'field': 'order_count', 'old_value': 2128, 'new_value': 2362}]
2025-06-15 06:00:49,487 - INFO - 日期 2025-06 处理完成 - 更新: 29 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:49,487 - INFO - 数据同步完成！更新: 29 条，插入: 0 条，错误: 0 条
2025-06-15 06:00:49,487 - INFO - =================同步完成====================
2025-06-15 09:00:03,181 - INFO - =================使用默认全量同步=============
2025-06-15 09:00:04,853 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 09:00:04,853 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 09:00:04,884 - INFO - 开始处理日期: 2025-01
2025-06-15 09:00:04,884 - INFO - Request Parameters - Page 1:
2025-06-15 09:00:04,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:04,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:05,791 - INFO - Response - Page 1:
2025-06-15 09:00:05,994 - INFO - 第 1 页获取到 100 条记录
2025-06-15 09:00:05,994 - INFO - Request Parameters - Page 2:
2025-06-15 09:00:05,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:05,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:06,962 - INFO - Response - Page 2:
2025-06-15 09:00:07,166 - INFO - 第 2 页获取到 100 条记录
2025-06-15 09:00:07,166 - INFO - Request Parameters - Page 3:
2025-06-15 09:00:07,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:07,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:07,697 - INFO - Response - Page 3:
2025-06-15 09:00:07,900 - INFO - 第 3 页获取到 100 条记录
2025-06-15 09:00:07,900 - INFO - Request Parameters - Page 4:
2025-06-15 09:00:07,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:07,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:08,462 - INFO - Response - Page 4:
2025-06-15 09:00:08,666 - INFO - 第 4 页获取到 100 条记录
2025-06-15 09:00:08,666 - INFO - Request Parameters - Page 5:
2025-06-15 09:00:08,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:08,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:09,150 - INFO - Response - Page 5:
2025-06-15 09:00:09,353 - INFO - 第 5 页获取到 100 条记录
2025-06-15 09:00:09,353 - INFO - Request Parameters - Page 6:
2025-06-15 09:00:09,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:09,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:09,978 - INFO - Response - Page 6:
2025-06-15 09:00:10,181 - INFO - 第 6 页获取到 100 条记录
2025-06-15 09:00:10,181 - INFO - Request Parameters - Page 7:
2025-06-15 09:00:10,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:10,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:10,759 - INFO - Response - Page 7:
2025-06-15 09:00:10,962 - INFO - 第 7 页获取到 82 条记录
2025-06-15 09:00:10,962 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 09:00:10,962 - INFO - 获取到 682 条表单数据
2025-06-15 09:00:10,962 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 09:00:10,978 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:10,978 - INFO - 开始处理日期: 2025-02
2025-06-15 09:00:10,978 - INFO - Request Parameters - Page 1:
2025-06-15 09:00:10,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:10,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:11,509 - INFO - Response - Page 1:
2025-06-15 09:00:11,712 - INFO - 第 1 页获取到 100 条记录
2025-06-15 09:00:11,712 - INFO - Request Parameters - Page 2:
2025-06-15 09:00:11,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:11,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:12,166 - INFO - Response - Page 2:
2025-06-15 09:00:12,369 - INFO - 第 2 页获取到 100 条记录
2025-06-15 09:00:12,369 - INFO - Request Parameters - Page 3:
2025-06-15 09:00:12,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:12,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:12,947 - INFO - Response - Page 3:
2025-06-15 09:00:13,150 - INFO - 第 3 页获取到 100 条记录
2025-06-15 09:00:13,150 - INFO - Request Parameters - Page 4:
2025-06-15 09:00:13,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:13,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:13,728 - INFO - Response - Page 4:
2025-06-15 09:00:13,931 - INFO - 第 4 页获取到 100 条记录
2025-06-15 09:00:13,931 - INFO - Request Parameters - Page 5:
2025-06-15 09:00:13,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:13,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:14,416 - INFO - Response - Page 5:
2025-06-15 09:00:14,619 - INFO - 第 5 页获取到 100 条记录
2025-06-15 09:00:14,619 - INFO - Request Parameters - Page 6:
2025-06-15 09:00:14,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:14,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:15,103 - INFO - Response - Page 6:
2025-06-15 09:00:15,306 - INFO - 第 6 页获取到 100 条记录
2025-06-15 09:00:15,306 - INFO - Request Parameters - Page 7:
2025-06-15 09:00:15,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:15,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:15,759 - INFO - Response - Page 7:
2025-06-15 09:00:15,962 - INFO - 第 7 页获取到 70 条记录
2025-06-15 09:00:15,962 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 09:00:15,962 - INFO - 获取到 670 条表单数据
2025-06-15 09:00:15,962 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 09:00:15,978 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:15,978 - INFO - 开始处理日期: 2025-03
2025-06-15 09:00:15,978 - INFO - Request Parameters - Page 1:
2025-06-15 09:00:15,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:15,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:16,494 - INFO - Response - Page 1:
2025-06-15 09:00:16,697 - INFO - 第 1 页获取到 100 条记录
2025-06-15 09:00:16,697 - INFO - Request Parameters - Page 2:
2025-06-15 09:00:16,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:16,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:17,228 - INFO - Response - Page 2:
2025-06-15 09:00:17,431 - INFO - 第 2 页获取到 100 条记录
2025-06-15 09:00:17,431 - INFO - Request Parameters - Page 3:
2025-06-15 09:00:17,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:17,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:18,056 - INFO - Response - Page 3:
2025-06-15 09:00:18,259 - INFO - 第 3 页获取到 100 条记录
2025-06-15 09:00:18,259 - INFO - Request Parameters - Page 4:
2025-06-15 09:00:18,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:18,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:18,728 - INFO - Response - Page 4:
2025-06-15 09:00:18,931 - INFO - 第 4 页获取到 100 条记录
2025-06-15 09:00:18,931 - INFO - Request Parameters - Page 5:
2025-06-15 09:00:18,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:18,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:19,509 - INFO - Response - Page 5:
2025-06-15 09:00:19,712 - INFO - 第 5 页获取到 100 条记录
2025-06-15 09:00:19,712 - INFO - Request Parameters - Page 6:
2025-06-15 09:00:19,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:19,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:20,212 - INFO - Response - Page 6:
2025-06-15 09:00:20,416 - INFO - 第 6 页获取到 100 条记录
2025-06-15 09:00:20,416 - INFO - Request Parameters - Page 7:
2025-06-15 09:00:20,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:20,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:20,853 - INFO - Response - Page 7:
2025-06-15 09:00:21,056 - INFO - 第 7 页获取到 61 条记录
2025-06-15 09:00:21,056 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 09:00:21,056 - INFO - 获取到 661 条表单数据
2025-06-15 09:00:21,056 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 09:00:21,072 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:21,072 - INFO - 开始处理日期: 2025-04
2025-06-15 09:00:21,072 - INFO - Request Parameters - Page 1:
2025-06-15 09:00:21,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:21,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:21,525 - INFO - Response - Page 1:
2025-06-15 09:00:21,728 - INFO - 第 1 页获取到 100 条记录
2025-06-15 09:00:21,728 - INFO - Request Parameters - Page 2:
2025-06-15 09:00:21,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:21,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:22,212 - INFO - Response - Page 2:
2025-06-15 09:00:22,416 - INFO - 第 2 页获取到 100 条记录
2025-06-15 09:00:22,416 - INFO - Request Parameters - Page 3:
2025-06-15 09:00:22,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:22,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:22,978 - INFO - Response - Page 3:
2025-06-15 09:00:23,181 - INFO - 第 3 页获取到 100 条记录
2025-06-15 09:00:23,181 - INFO - Request Parameters - Page 4:
2025-06-15 09:00:23,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:23,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:23,681 - INFO - Response - Page 4:
2025-06-15 09:00:23,884 - INFO - 第 4 页获取到 100 条记录
2025-06-15 09:00:23,884 - INFO - Request Parameters - Page 5:
2025-06-15 09:00:23,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:23,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:24,556 - INFO - Response - Page 5:
2025-06-15 09:00:24,759 - INFO - 第 5 页获取到 100 条记录
2025-06-15 09:00:24,759 - INFO - Request Parameters - Page 6:
2025-06-15 09:00:24,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:24,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:25,259 - INFO - Response - Page 6:
2025-06-15 09:00:25,462 - INFO - 第 6 页获取到 100 条记录
2025-06-15 09:00:25,462 - INFO - Request Parameters - Page 7:
2025-06-15 09:00:25,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:25,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:25,869 - INFO - Response - Page 7:
2025-06-15 09:00:26,072 - INFO - 第 7 页获取到 56 条记录
2025-06-15 09:00:26,072 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 09:00:26,072 - INFO - 获取到 656 条表单数据
2025-06-15 09:00:26,072 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 09:00:26,087 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:26,087 - INFO - 开始处理日期: 2025-05
2025-06-15 09:00:26,087 - INFO - Request Parameters - Page 1:
2025-06-15 09:00:26,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:26,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:26,634 - INFO - Response - Page 1:
2025-06-15 09:00:26,837 - INFO - 第 1 页获取到 100 条记录
2025-06-15 09:00:26,837 - INFO - Request Parameters - Page 2:
2025-06-15 09:00:26,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:26,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:27,322 - INFO - Response - Page 2:
2025-06-15 09:00:27,525 - INFO - 第 2 页获取到 100 条记录
2025-06-15 09:00:27,525 - INFO - Request Parameters - Page 3:
2025-06-15 09:00:27,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:27,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:27,962 - INFO - Response - Page 3:
2025-06-15 09:00:28,165 - INFO - 第 3 页获取到 100 条记录
2025-06-15 09:00:28,165 - INFO - Request Parameters - Page 4:
2025-06-15 09:00:28,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:28,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:28,665 - INFO - Response - Page 4:
2025-06-15 09:00:28,869 - INFO - 第 4 页获取到 100 条记录
2025-06-15 09:00:28,869 - INFO - Request Parameters - Page 5:
2025-06-15 09:00:28,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:28,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:29,400 - INFO - Response - Page 5:
2025-06-15 09:00:29,603 - INFO - 第 5 页获取到 100 条记录
2025-06-15 09:00:29,603 - INFO - Request Parameters - Page 6:
2025-06-15 09:00:29,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:29,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:30,056 - INFO - Response - Page 6:
2025-06-15 09:00:30,259 - INFO - 第 6 页获取到 100 条记录
2025-06-15 09:00:30,259 - INFO - Request Parameters - Page 7:
2025-06-15 09:00:30,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:30,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:30,650 - INFO - Response - Page 7:
2025-06-15 09:00:30,853 - INFO - 第 7 页获取到 40 条记录
2025-06-15 09:00:30,853 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 09:00:30,853 - INFO - 获取到 640 条表单数据
2025-06-15 09:00:30,853 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 09:00:30,869 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:30,869 - INFO - 开始处理日期: 2025-06
2025-06-15 09:00:30,869 - INFO - Request Parameters - Page 1:
2025-06-15 09:00:30,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:30,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:31,369 - INFO - Response - Page 1:
2025-06-15 09:00:31,572 - INFO - 第 1 页获取到 100 条记录
2025-06-15 09:00:31,572 - INFO - Request Parameters - Page 2:
2025-06-15 09:00:31,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:31,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:32,103 - INFO - Response - Page 2:
2025-06-15 09:00:32,306 - INFO - 第 2 页获取到 100 条记录
2025-06-15 09:00:32,306 - INFO - Request Parameters - Page 3:
2025-06-15 09:00:32,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:32,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:32,806 - INFO - Response - Page 3:
2025-06-15 09:00:33,009 - INFO - 第 3 页获取到 100 条记录
2025-06-15 09:00:33,009 - INFO - Request Parameters - Page 4:
2025-06-15 09:00:33,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:33,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:33,525 - INFO - Response - Page 4:
2025-06-15 09:00:33,728 - INFO - 第 4 页获取到 100 条记录
2025-06-15 09:00:33,728 - INFO - Request Parameters - Page 5:
2025-06-15 09:00:33,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:33,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:34,181 - INFO - Response - Page 5:
2025-06-15 09:00:34,384 - INFO - 第 5 页获取到 100 条记录
2025-06-15 09:00:34,384 - INFO - Request Parameters - Page 6:
2025-06-15 09:00:34,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:34,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:34,884 - INFO - Response - Page 6:
2025-06-15 09:00:35,087 - INFO - 第 6 页获取到 100 条记录
2025-06-15 09:00:35,087 - INFO - Request Parameters - Page 7:
2025-06-15 09:00:35,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 09:00:35,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 09:00:35,462 - INFO - Response - Page 7:
2025-06-15 09:00:35,665 - INFO - 第 7 页获取到 22 条记录
2025-06-15 09:00:35,665 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 09:00:35,665 - INFO - 获取到 622 条表单数据
2025-06-15 09:00:35,665 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 09:00:35,665 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-15 09:00:36,181 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-15 09:00:36,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20495.0, 'new_value': 30095.0}, {'field': 'total_amount', 'old_value': 20495.0, 'new_value': 30095.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-15 09:00:36,181 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-15 09:00:36,665 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-15 09:00:36,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46637.0, 'new_value': 61637.0}, {'field': 'total_amount', 'old_value': 46637.0, 'new_value': 61637.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-15 09:00:36,665 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-15 09:00:37,165 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-15 09:00:37,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53633.97, 'new_value': 62370.52}, {'field': 'total_amount', 'old_value': 54666.27, 'new_value': 63402.82}, {'field': 'order_count', 'old_value': 1235, 'new_value': 1431}]
2025-06-15 09:00:37,181 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-15 09:00:37,619 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-15 09:00:37,619 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62358.89, 'new_value': 65390.99}, {'field': 'offline_amount', 'old_value': 556032.24, 'new_value': 609947.4}, {'field': 'total_amount', 'old_value': 618391.13, 'new_value': 675338.39}, {'field': 'order_count', 'old_value': 5356, 'new_value': 5783}]
2025-06-15 09:00:37,619 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-15 09:00:38,119 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-15 09:00:38,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3548.36, 'new_value': 4357.44}, {'field': 'offline_amount', 'old_value': 92121.05, 'new_value': 98689.17}, {'field': 'total_amount', 'old_value': 95669.41, 'new_value': 103046.61}, {'field': 'order_count', 'old_value': 607, 'new_value': 660}]
2025-06-15 09:00:38,119 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-15 09:00:38,540 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-15 09:00:38,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13312.0, 'new_value': 14344.0}, {'field': 'total_amount', 'old_value': 13372.0, 'new_value': 14404.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-06-15 09:00:38,540 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-15 09:00:38,947 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-15 09:00:38,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6002.36, 'new_value': 8096.16}, {'field': 'offline_amount', 'old_value': 147631.49, 'new_value': 159776.39}, {'field': 'total_amount', 'old_value': 153633.85, 'new_value': 167872.55}, {'field': 'order_count', 'old_value': 8227, 'new_value': 9097}]
2025-06-15 09:00:38,962 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-15 09:00:39,369 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-15 09:00:39,369 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20543.25, 'new_value': 23057.01}, {'field': 'offline_amount', 'old_value': 15259.0, 'new_value': 17029.0}, {'field': 'total_amount', 'old_value': 35802.25, 'new_value': 40086.01}, {'field': 'order_count', 'old_value': 479, 'new_value': 523}]
2025-06-15 09:00:39,369 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-15 09:00:39,759 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-15 09:00:39,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38914.0, 'new_value': 45883.32}, {'field': 'total_amount', 'old_value': 38914.0, 'new_value': 45883.32}, {'field': 'order_count', 'old_value': 211, 'new_value': 240}]
2025-06-15 09:00:39,759 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-15 09:00:40,165 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-15 09:00:40,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84108.2, 'new_value': 94986.0}, {'field': 'total_amount', 'old_value': 84108.2, 'new_value': 94986.0}, {'field': 'order_count', 'old_value': 1111, 'new_value': 1225}]
2025-06-15 09:00:40,165 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-15 09:00:40,572 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-15 09:00:40,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28025.3, 'new_value': 30994.3}, {'field': 'offline_amount', 'old_value': 172516.47, 'new_value': 197958.94}, {'field': 'total_amount', 'old_value': 200541.77, 'new_value': 228953.24}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1427}]
2025-06-15 09:00:40,572 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-15 09:00:41,040 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-15 09:00:41,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14531.0, 'new_value': 14961.0}, {'field': 'total_amount', 'old_value': 14531.0, 'new_value': 14961.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-06-15 09:00:41,040 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-15 09:00:41,494 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-15 09:00:41,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72260.0, 'new_value': 96756.0}, {'field': 'total_amount', 'old_value': 72260.0, 'new_value': 96756.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-06-15 09:00:41,494 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-15 09:00:42,072 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-15 09:00:42,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20015.22, 'new_value': 21418.12}, {'field': 'offline_amount', 'old_value': 546999.72, 'new_value': 600997.6}, {'field': 'total_amount', 'old_value': 567014.94, 'new_value': 622415.72}, {'field': 'order_count', 'old_value': 2592, 'new_value': 2865}]
2025-06-15 09:00:42,072 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-15 09:00:42,525 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-15 09:00:42,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47702.0, 'new_value': 52051.0}, {'field': 'total_amount', 'old_value': 47702.0, 'new_value': 52051.0}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1553}]
2025-06-15 09:00:42,540 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-15 09:00:42,993 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-15 09:00:42,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42975.0, 'new_value': 47357.0}, {'field': 'total_amount', 'old_value': 42975.0, 'new_value': 47357.0}, {'field': 'order_count', 'old_value': 355, 'new_value': 386}]
2025-06-15 09:00:42,993 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-15 09:00:43,478 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-15 09:00:43,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 950000.0, 'new_value': 1050000.0}, {'field': 'total_amount', 'old_value': 1050000.0, 'new_value': 1150000.0}, {'field': 'order_count', 'old_value': 365, 'new_value': 366}]
2025-06-15 09:00:43,478 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-15 09:00:44,072 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-15 09:00:44,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5800000.0, 'new_value': 6400000.0}, {'field': 'total_amount', 'old_value': 5800000.0, 'new_value': 6400000.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 33}]
2025-06-15 09:00:44,072 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-15 09:00:44,525 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-15 09:00:44,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35820.8, 'new_value': 38118.53}, {'field': 'total_amount', 'old_value': 35820.8, 'new_value': 38118.53}, {'field': 'order_count', 'old_value': 677, 'new_value': 701}]
2025-06-15 09:00:44,525 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-15 09:00:44,947 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-15 09:00:44,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137766.24, 'new_value': 149920.24}, {'field': 'offline_amount', 'old_value': 2062.0, 'new_value': 2714.0}, {'field': 'total_amount', 'old_value': 139828.24, 'new_value': 152634.24}, {'field': 'order_count', 'old_value': 10850, 'new_value': 11532}]
2025-06-15 09:00:44,947 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-15 09:00:45,447 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-15 09:00:45,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9199.0, 'new_value': 10448.0}, {'field': 'offline_amount', 'old_value': 14914.0, 'new_value': 15744.0}, {'field': 'total_amount', 'old_value': 24113.0, 'new_value': 26192.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 214}]
2025-06-15 09:00:45,447 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-15 09:00:45,869 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-15 09:00:45,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37715.0, 'new_value': 40724.0}, {'field': 'total_amount', 'old_value': 37715.0, 'new_value': 40724.0}, {'field': 'order_count', 'old_value': 1750, 'new_value': 1894}]
2025-06-15 09:00:45,869 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-15 09:00:46,384 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-15 09:00:46,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150388.0, 'new_value': 168927.0}, {'field': 'total_amount', 'old_value': 150388.0, 'new_value': 168927.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 210}]
2025-06-15 09:00:46,384 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-15 09:00:46,822 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-15 09:00:46,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50979.0, 'new_value': 56978.0}, {'field': 'total_amount', 'old_value': 50979.0, 'new_value': 56978.0}, {'field': 'order_count', 'old_value': 5742, 'new_value': 6056}]
2025-06-15 09:00:46,822 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-15 09:00:47,306 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-15 09:00:47,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1279588.0, 'new_value': 1379588.0}, {'field': 'total_amount', 'old_value': 1279588.0, 'new_value': 1379588.0}, {'field': 'order_count', 'old_value': 1453, 'new_value': 1815}]
2025-06-15 09:00:47,306 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-15 09:00:47,806 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-15 09:00:47,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105000.0, 'new_value': 115000.0}, {'field': 'total_amount', 'old_value': 105000.0, 'new_value': 115000.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 301}]
2025-06-15 09:00:47,806 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-15 09:00:48,322 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-15 09:00:48,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105000.0, 'new_value': 115000.0}, {'field': 'total_amount', 'old_value': 115000.0, 'new_value': 125000.0}, {'field': 'order_count', 'old_value': 222, 'new_value': 420}]
2025-06-15 09:00:48,322 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-15 09:00:48,806 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-15 09:00:48,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12820.0, 'new_value': 37220.0}, {'field': 'total_amount', 'old_value': 12820.0, 'new_value': 37220.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-06-15 09:00:48,806 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-15 09:00:49,290 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-15 09:00:49,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105000.0, 'new_value': 115000.0}, {'field': 'total_amount', 'old_value': 105000.0, 'new_value': 115000.0}, {'field': 'order_count', 'old_value': 162, 'new_value': 315}]
2025-06-15 09:00:49,290 - INFO - 日期 2025-06 处理完成 - 更新: 29 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:49,290 - INFO - 数据同步完成！更新: 29 条，插入: 0 条，错误: 0 条
2025-06-15 09:00:49,290 - INFO - =================同步完成====================
2025-06-15 12:00:02,906 - INFO - =================使用默认全量同步=============
2025-06-15 12:00:04,594 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 12:00:04,594 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 12:00:04,625 - INFO - 开始处理日期: 2025-01
2025-06-15 12:00:04,625 - INFO - Request Parameters - Page 1:
2025-06-15 12:00:04,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:04,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:05,875 - INFO - Response - Page 1:
2025-06-15 12:00:06,078 - INFO - 第 1 页获取到 100 条记录
2025-06-15 12:00:06,078 - INFO - Request Parameters - Page 2:
2025-06-15 12:00:06,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:06,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:06,609 - INFO - Response - Page 2:
2025-06-15 12:00:06,812 - INFO - 第 2 页获取到 100 条记录
2025-06-15 12:00:06,812 - INFO - Request Parameters - Page 3:
2025-06-15 12:00:06,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:06,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:07,547 - INFO - Response - Page 3:
2025-06-15 12:00:07,750 - INFO - 第 3 页获取到 100 条记录
2025-06-15 12:00:07,750 - INFO - Request Parameters - Page 4:
2025-06-15 12:00:07,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:07,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:08,297 - INFO - Response - Page 4:
2025-06-15 12:00:08,500 - INFO - 第 4 页获取到 100 条记录
2025-06-15 12:00:08,500 - INFO - Request Parameters - Page 5:
2025-06-15 12:00:08,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:08,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:09,156 - INFO - Response - Page 5:
2025-06-15 12:00:09,359 - INFO - 第 5 页获取到 100 条记录
2025-06-15 12:00:09,359 - INFO - Request Parameters - Page 6:
2025-06-15 12:00:09,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:09,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:09,875 - INFO - Response - Page 6:
2025-06-15 12:00:10,078 - INFO - 第 6 页获取到 100 条记录
2025-06-15 12:00:10,078 - INFO - Request Parameters - Page 7:
2025-06-15 12:00:10,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:10,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:10,625 - INFO - Response - Page 7:
2025-06-15 12:00:10,828 - INFO - 第 7 页获取到 82 条记录
2025-06-15 12:00:10,828 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 12:00:10,828 - INFO - 获取到 682 条表单数据
2025-06-15 12:00:10,828 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 12:00:10,844 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 12:00:10,844 - INFO - 开始处理日期: 2025-02
2025-06-15 12:00:10,844 - INFO - Request Parameters - Page 1:
2025-06-15 12:00:10,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:10,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:11,375 - INFO - Response - Page 1:
2025-06-15 12:00:11,578 - INFO - 第 1 页获取到 100 条记录
2025-06-15 12:00:11,578 - INFO - Request Parameters - Page 2:
2025-06-15 12:00:11,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:11,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:12,078 - INFO - Response - Page 2:
2025-06-15 12:00:12,281 - INFO - 第 2 页获取到 100 条记录
2025-06-15 12:00:12,281 - INFO - Request Parameters - Page 3:
2025-06-15 12:00:12,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:12,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:12,812 - INFO - Response - Page 3:
2025-06-15 12:00:13,015 - INFO - 第 3 页获取到 100 条记录
2025-06-15 12:00:13,015 - INFO - Request Parameters - Page 4:
2025-06-15 12:00:13,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:13,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:13,531 - INFO - Response - Page 4:
2025-06-15 12:00:13,734 - INFO - 第 4 页获取到 100 条记录
2025-06-15 12:00:13,734 - INFO - Request Parameters - Page 5:
2025-06-15 12:00:13,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:13,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:14,250 - INFO - Response - Page 5:
2025-06-15 12:00:14,453 - INFO - 第 5 页获取到 100 条记录
2025-06-15 12:00:14,453 - INFO - Request Parameters - Page 6:
2025-06-15 12:00:14,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:14,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:15,000 - INFO - Response - Page 6:
2025-06-15 12:00:15,203 - INFO - 第 6 页获取到 100 条记录
2025-06-15 12:00:15,203 - INFO - Request Parameters - Page 7:
2025-06-15 12:00:15,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:15,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:15,672 - INFO - Response - Page 7:
2025-06-15 12:00:15,875 - INFO - 第 7 页获取到 70 条记录
2025-06-15 12:00:15,875 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 12:00:15,875 - INFO - 获取到 670 条表单数据
2025-06-15 12:00:15,875 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 12:00:15,890 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 12:00:15,890 - INFO - 开始处理日期: 2025-03
2025-06-15 12:00:15,890 - INFO - Request Parameters - Page 1:
2025-06-15 12:00:15,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:15,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:16,422 - INFO - Response - Page 1:
2025-06-15 12:00:16,625 - INFO - 第 1 页获取到 100 条记录
2025-06-15 12:00:16,625 - INFO - Request Parameters - Page 2:
2025-06-15 12:00:16,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:16,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:17,109 - INFO - Response - Page 2:
2025-06-15 12:00:17,312 - INFO - 第 2 页获取到 100 条记录
2025-06-15 12:00:17,312 - INFO - Request Parameters - Page 3:
2025-06-15 12:00:17,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:17,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:17,781 - INFO - Response - Page 3:
2025-06-15 12:00:17,984 - INFO - 第 3 页获取到 100 条记录
2025-06-15 12:00:17,984 - INFO - Request Parameters - Page 4:
2025-06-15 12:00:17,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:17,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:18,422 - INFO - Response - Page 4:
2025-06-15 12:00:18,625 - INFO - 第 4 页获取到 100 条记录
2025-06-15 12:00:18,625 - INFO - Request Parameters - Page 5:
2025-06-15 12:00:18,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:18,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:19,140 - INFO - Response - Page 5:
2025-06-15 12:00:19,344 - INFO - 第 5 页获取到 100 条记录
2025-06-15 12:00:19,344 - INFO - Request Parameters - Page 6:
2025-06-15 12:00:19,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:19,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:19,797 - INFO - Response - Page 6:
2025-06-15 12:00:20,000 - INFO - 第 6 页获取到 100 条记录
2025-06-15 12:00:20,000 - INFO - Request Parameters - Page 7:
2025-06-15 12:00:20,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:20,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:20,484 - INFO - Response - Page 7:
2025-06-15 12:00:20,687 - INFO - 第 7 页获取到 61 条记录
2025-06-15 12:00:20,687 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 12:00:20,687 - INFO - 获取到 661 条表单数据
2025-06-15 12:00:20,687 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 12:00:20,703 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 12:00:20,703 - INFO - 开始处理日期: 2025-04
2025-06-15 12:00:20,703 - INFO - Request Parameters - Page 1:
2025-06-15 12:00:20,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:20,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:21,297 - INFO - Response - Page 1:
2025-06-15 12:00:21,500 - INFO - 第 1 页获取到 100 条记录
2025-06-15 12:00:21,500 - INFO - Request Parameters - Page 2:
2025-06-15 12:00:21,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:21,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:22,000 - INFO - Response - Page 2:
2025-06-15 12:00:22,203 - INFO - 第 2 页获取到 100 条记录
2025-06-15 12:00:22,203 - INFO - Request Parameters - Page 3:
2025-06-15 12:00:22,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:22,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:22,640 - INFO - Response - Page 3:
2025-06-15 12:00:22,844 - INFO - 第 3 页获取到 100 条记录
2025-06-15 12:00:22,844 - INFO - Request Parameters - Page 4:
2025-06-15 12:00:22,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:22,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:23,562 - INFO - Response - Page 4:
2025-06-15 12:00:23,765 - INFO - 第 4 页获取到 100 条记录
2025-06-15 12:00:23,765 - INFO - Request Parameters - Page 5:
2025-06-15 12:00:23,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:23,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:24,390 - INFO - Response - Page 5:
2025-06-15 12:00:24,593 - INFO - 第 5 页获取到 100 条记录
2025-06-15 12:00:24,593 - INFO - Request Parameters - Page 6:
2025-06-15 12:00:24,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:24,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:25,156 - INFO - Response - Page 6:
2025-06-15 12:00:25,359 - INFO - 第 6 页获取到 100 条记录
2025-06-15 12:00:25,359 - INFO - Request Parameters - Page 7:
2025-06-15 12:00:25,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:25,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:25,875 - INFO - Response - Page 7:
2025-06-15 12:00:26,078 - INFO - 第 7 页获取到 56 条记录
2025-06-15 12:00:26,078 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 12:00:26,078 - INFO - 获取到 656 条表单数据
2025-06-15 12:00:26,078 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 12:00:26,093 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 12:00:26,093 - INFO - 开始处理日期: 2025-05
2025-06-15 12:00:26,093 - INFO - Request Parameters - Page 1:
2025-06-15 12:00:26,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:26,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:26,672 - INFO - Response - Page 1:
2025-06-15 12:00:26,875 - INFO - 第 1 页获取到 100 条记录
2025-06-15 12:00:26,875 - INFO - Request Parameters - Page 2:
2025-06-15 12:00:26,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:26,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:27,343 - INFO - Response - Page 2:
2025-06-15 12:00:27,547 - INFO - 第 2 页获取到 100 条记录
2025-06-15 12:00:27,547 - INFO - Request Parameters - Page 3:
2025-06-15 12:00:27,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:27,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:28,156 - INFO - Response - Page 3:
2025-06-15 12:00:28,359 - INFO - 第 3 页获取到 100 条记录
2025-06-15 12:00:28,359 - INFO - Request Parameters - Page 4:
2025-06-15 12:00:28,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:28,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:28,906 - INFO - Response - Page 4:
2025-06-15 12:00:29,109 - INFO - 第 4 页获取到 100 条记录
2025-06-15 12:00:29,109 - INFO - Request Parameters - Page 5:
2025-06-15 12:00:29,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:29,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:29,625 - INFO - Response - Page 5:
2025-06-15 12:00:29,828 - INFO - 第 5 页获取到 100 条记录
2025-06-15 12:00:29,828 - INFO - Request Parameters - Page 6:
2025-06-15 12:00:29,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:29,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:30,437 - INFO - Response - Page 6:
2025-06-15 12:00:30,640 - INFO - 第 6 页获取到 100 条记录
2025-06-15 12:00:30,640 - INFO - Request Parameters - Page 7:
2025-06-15 12:00:30,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:30,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:31,047 - INFO - Response - Page 7:
2025-06-15 12:00:31,250 - INFO - 第 7 页获取到 40 条记录
2025-06-15 12:00:31,250 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 12:00:31,250 - INFO - 获取到 640 条表单数据
2025-06-15 12:00:31,250 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 12:00:31,265 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 12:00:31,265 - INFO - 开始处理日期: 2025-06
2025-06-15 12:00:31,265 - INFO - Request Parameters - Page 1:
2025-06-15 12:00:31,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:31,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:31,781 - INFO - Response - Page 1:
2025-06-15 12:00:31,984 - INFO - 第 1 页获取到 100 条记录
2025-06-15 12:00:31,984 - INFO - Request Parameters - Page 2:
2025-06-15 12:00:31,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:31,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:32,484 - INFO - Response - Page 2:
2025-06-15 12:00:32,687 - INFO - 第 2 页获取到 100 条记录
2025-06-15 12:00:32,687 - INFO - Request Parameters - Page 3:
2025-06-15 12:00:32,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:32,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:33,203 - INFO - Response - Page 3:
2025-06-15 12:00:33,406 - INFO - 第 3 页获取到 100 条记录
2025-06-15 12:00:33,406 - INFO - Request Parameters - Page 4:
2025-06-15 12:00:33,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:33,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:33,922 - INFO - Response - Page 4:
2025-06-15 12:00:34,125 - INFO - 第 4 页获取到 100 条记录
2025-06-15 12:00:34,125 - INFO - Request Parameters - Page 5:
2025-06-15 12:00:34,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:34,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:34,625 - INFO - Response - Page 5:
2025-06-15 12:00:34,828 - INFO - 第 5 页获取到 100 条记录
2025-06-15 12:00:34,828 - INFO - Request Parameters - Page 6:
2025-06-15 12:00:34,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:34,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:35,312 - INFO - Response - Page 6:
2025-06-15 12:00:35,515 - INFO - 第 6 页获取到 100 条记录
2025-06-15 12:00:35,515 - INFO - Request Parameters - Page 7:
2025-06-15 12:00:35,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 12:00:35,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 12:00:35,875 - INFO - Response - Page 7:
2025-06-15 12:00:36,078 - INFO - 第 7 页获取到 22 条记录
2025-06-15 12:00:36,078 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 12:00:36,078 - INFO - 获取到 622 条表单数据
2025-06-15 12:00:36,078 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 12:00:36,078 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-15 12:00:36,468 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-15 12:00:36,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374537.0, 'new_value': 417585.0}, {'field': 'total_amount', 'old_value': 374537.0, 'new_value': 417585.0}, {'field': 'order_count', 'old_value': 2640, 'new_value': 2866}]
2025-06-15 12:00:36,468 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-15 12:00:36,937 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-15 12:00:36,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33840.0, 'new_value': 44640.0}, {'field': 'total_amount', 'old_value': 33840.0, 'new_value': 44640.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-15 12:00:36,937 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-15 12:00:37,422 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-15 12:00:37,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94800.0, 'new_value': 126200.0}, {'field': 'total_amount', 'old_value': 94800.0, 'new_value': 126200.0}]
2025-06-15 12:00:37,422 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-15 12:00:37,843 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-15 12:00:37,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16500.0, 'new_value': 22180.0}, {'field': 'total_amount', 'old_value': 183020.0, 'new_value': 188700.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 127}]
2025-06-15 12:00:37,843 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-15 12:00:38,297 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-15 12:00:38,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27120.0, 'new_value': 35520.0}, {'field': 'total_amount', 'old_value': 27120.0, 'new_value': 35520.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-06-15 12:00:38,297 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-15 12:00:38,734 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-15 12:00:38,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 246262.0, 'new_value': 270562.0}, {'field': 'offline_amount', 'old_value': 106444.0, 'new_value': 113285.0}, {'field': 'total_amount', 'old_value': 352706.0, 'new_value': 383847.0}, {'field': 'order_count', 'old_value': 393, 'new_value': 423}]
2025-06-15 12:00:38,734 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMVX
2025-06-15 12:00:39,156 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMVX
2025-06-15 12:00:39,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18970.0, 'new_value': 23480.5}, {'field': 'total_amount', 'old_value': 57370.0, 'new_value': 61880.5}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-15 12:00:39,156 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-15 12:00:39,625 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-15 12:00:39,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97600.0, 'new_value': 103860.0}, {'field': 'total_amount', 'old_value': 97600.0, 'new_value': 103860.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-15 12:00:39,625 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-15 12:00:40,109 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-15 12:00:40,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10284.82, 'new_value': 12703.82}, {'field': 'total_amount', 'old_value': 10284.82, 'new_value': 12703.82}, {'field': 'order_count', 'old_value': 39, 'new_value': 45}]
2025-06-15 12:00:40,109 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-15 12:00:40,609 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-15 12:00:40,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3130.0, 'new_value': 3928.0}, {'field': 'offline_amount', 'old_value': 41943.0, 'new_value': 48981.0}, {'field': 'total_amount', 'old_value': 45073.0, 'new_value': 52909.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 59}]
2025-06-15 12:00:40,609 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-15 12:00:41,031 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-15 12:00:41,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15822.5, 'new_value': 16480.5}, {'field': 'total_amount', 'old_value': 15822.5, 'new_value': 16480.5}, {'field': 'order_count', 'old_value': 3221, 'new_value': 3347}]
2025-06-15 12:00:41,031 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-15 12:00:41,468 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-15 12:00:41,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25720.0, 'new_value': 28160.0}, {'field': 'total_amount', 'old_value': 32700.0, 'new_value': 35140.0}, {'field': 'order_count', 'old_value': 337, 'new_value': 366}]
2025-06-15 12:00:41,468 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-15 12:00:41,937 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-15 12:00:41,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73306.0, 'new_value': 75456.0}, {'field': 'total_amount', 'old_value': 73306.0, 'new_value': 75456.0}, {'field': 'order_count', 'old_value': 49117, 'new_value': 49119}]
2025-06-15 12:00:41,937 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-15 12:00:42,453 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-15 12:00:42,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140743.4, 'new_value': 166568.2}, {'field': 'total_amount', 'old_value': 140743.4, 'new_value': 166568.2}, {'field': 'order_count', 'old_value': 2132, 'new_value': 2392}]
2025-06-15 12:00:42,453 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-15 12:00:42,953 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-15 12:00:42,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64745.8, 'new_value': 72849.9}, {'field': 'total_amount', 'old_value': 64745.8, 'new_value': 72849.9}, {'field': 'order_count', 'old_value': 318, 'new_value': 355}]
2025-06-15 12:00:42,953 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-15 12:00:43,500 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-15 12:00:43,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2316.0, 'new_value': 2759.0}, {'field': 'offline_amount', 'old_value': 32694.43, 'new_value': 35108.43}, {'field': 'total_amount', 'old_value': 35010.43, 'new_value': 37867.43}, {'field': 'order_count', 'old_value': 251, 'new_value': 286}]
2025-06-15 12:00:43,500 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-15 12:00:43,984 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-15 12:00:43,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21036.0, 'new_value': 24742.0}, {'field': 'offline_amount', 'old_value': 99790.0, 'new_value': 107548.0}, {'field': 'total_amount', 'old_value': 120826.0, 'new_value': 132290.0}, {'field': 'order_count', 'old_value': 844, 'new_value': 924}]
2025-06-15 12:00:43,984 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-15 12:00:44,406 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-15 12:00:44,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24551.26, 'new_value': 25995.87}, {'field': 'offline_amount', 'old_value': 56733.12, 'new_value': 60443.12}, {'field': 'total_amount', 'old_value': 81284.38, 'new_value': 86438.99}, {'field': 'order_count', 'old_value': 922, 'new_value': 981}]
2025-06-15 12:00:44,406 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-15 12:00:44,859 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-15 12:00:44,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15146.0, 'new_value': 15759.0}, {'field': 'offline_amount', 'old_value': 28835.74, 'new_value': 31352.74}, {'field': 'total_amount', 'old_value': 43981.74, 'new_value': 47111.74}, {'field': 'order_count', 'old_value': 66, 'new_value': 71}]
2025-06-15 12:00:44,859 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-15 12:00:45,359 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-15 12:00:45,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31423.0, 'new_value': 33161.0}, {'field': 'total_amount', 'old_value': 31423.0, 'new_value': 33161.0}, {'field': 'order_count', 'old_value': 606, 'new_value': 641}]
2025-06-15 12:00:45,359 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-15 12:00:45,828 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-15 12:00:45,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18272.15, 'new_value': 19452.76}, {'field': 'offline_amount', 'old_value': 51205.12, 'new_value': 55998.92}, {'field': 'total_amount', 'old_value': 69477.27, 'new_value': 75451.68}, {'field': 'order_count', 'old_value': 987, 'new_value': 1072}]
2025-06-15 12:00:45,828 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-15 12:00:46,281 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-15 12:00:46,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77025.0, 'new_value': 86069.0}, {'field': 'offline_amount', 'old_value': 27046.95, 'new_value': 29137.59}, {'field': 'total_amount', 'old_value': 104071.95, 'new_value': 115206.59}, {'field': 'order_count', 'old_value': 711, 'new_value': 791}]
2025-06-15 12:00:46,281 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-15 12:00:46,703 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-15 12:00:46,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6993.0, 'new_value': 7973.0}, {'field': 'total_amount', 'old_value': 7561.2, 'new_value': 8541.2}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-15 12:00:46,703 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-15 12:00:47,125 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-15 12:00:47,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3689.0, 'new_value': 3748.0}, {'field': 'offline_amount', 'old_value': 2454.0, 'new_value': 3147.0}, {'field': 'total_amount', 'old_value': 6143.0, 'new_value': 6895.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 118}]
2025-06-15 12:00:47,125 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-15 12:00:47,578 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-15 12:00:47,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63037.0, 'new_value': 68037.0}, {'field': 'total_amount', 'old_value': 72112.08, 'new_value': 77112.08}, {'field': 'order_count', 'old_value': 1131, 'new_value': 1166}]
2025-06-15 12:00:47,578 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-15 12:00:48,031 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-15 12:00:48,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100532.2, 'new_value': 108719.36}, {'field': 'total_amount', 'old_value': 100532.2, 'new_value': 108719.36}, {'field': 'order_count', 'old_value': 611, 'new_value': 677}]
2025-06-15 12:00:48,031 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-15 12:00:48,437 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-15 12:00:48,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45443.0, 'new_value': 45785.0}, {'field': 'offline_amount', 'old_value': 298393.0, 'new_value': 313139.0}, {'field': 'total_amount', 'old_value': 343836.0, 'new_value': 358924.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 59}]
2025-06-15 12:00:48,437 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-15 12:00:48,859 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-15 12:00:48,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12617.15, 'new_value': 14052.58}, {'field': 'offline_amount', 'old_value': 13831.9, 'new_value': 15503.32}, {'field': 'total_amount', 'old_value': 26449.05, 'new_value': 29555.9}, {'field': 'order_count', 'old_value': 1302, 'new_value': 1452}]
2025-06-15 12:00:48,859 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-15 12:00:49,437 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-15 12:00:49,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355413.43, 'new_value': 379855.88}, {'field': 'total_amount', 'old_value': 355413.43, 'new_value': 379855.88}]
2025-06-15 12:00:49,437 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-15 12:00:49,921 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-15 12:00:49,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6672.7, 'new_value': 7566.7}, {'field': 'total_amount', 'old_value': 6672.7, 'new_value': 7566.7}, {'field': 'order_count', 'old_value': 82, 'new_value': 96}]
2025-06-15 12:00:49,921 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-15 12:00:50,390 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-15 12:00:50,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 442466.5, 'new_value': 500171.3}, {'field': 'total_amount', 'old_value': 442466.5, 'new_value': 500171.3}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-06-15 12:00:50,390 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-15 12:00:50,843 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-15 12:00:50,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76097.0, 'new_value': 78949.0}, {'field': 'total_amount', 'old_value': 76097.0, 'new_value': 78949.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-06-15 12:00:50,843 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-15 12:00:51,328 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-15 12:00:51,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4778.0, 'new_value': 5157.0}, {'field': 'total_amount', 'old_value': 15755.0, 'new_value': 16134.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-06-15 12:00:51,328 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-15 12:00:51,796 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-15 12:00:51,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95138.0, 'new_value': 106602.0}, {'field': 'total_amount', 'old_value': 95138.0, 'new_value': 106602.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 191}]
2025-06-15 12:00:51,796 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-15 12:00:52,281 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-15 12:00:52,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5564.45, 'new_value': 6030.66}, {'field': 'offline_amount', 'old_value': 81513.45, 'new_value': 88066.0}, {'field': 'total_amount', 'old_value': 87077.9, 'new_value': 94096.66}, {'field': 'order_count', 'old_value': 969, 'new_value': 1045}]
2025-06-15 12:00:52,281 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-15 12:00:52,765 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-15 12:00:52,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5983.8, 'new_value': 6234.4}, {'field': 'total_amount', 'old_value': 37129.8, 'new_value': 37380.4}, {'field': 'order_count', 'old_value': 42, 'new_value': 45}]
2025-06-15 12:00:52,765 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-15 12:00:53,250 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-15 12:00:53,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69531.13, 'new_value': 75051.23}, {'field': 'total_amount', 'old_value': 69531.13, 'new_value': 75051.23}, {'field': 'order_count', 'old_value': 332, 'new_value': 361}]
2025-06-15 12:00:53,250 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-15 12:00:53,734 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-15 12:00:53,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11767.0, 'new_value': 15308.0}, {'field': 'total_amount', 'old_value': 11767.0, 'new_value': 15308.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 69}]
2025-06-15 12:00:53,734 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-15 12:00:54,171 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-15 12:00:54,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92808.0, 'new_value': 99155.0}, {'field': 'total_amount', 'old_value': 92808.0, 'new_value': 99155.0}, {'field': 'order_count', 'old_value': 3565, 'new_value': 3803}]
2025-06-15 12:00:54,171 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-15 12:00:54,671 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-15 12:00:54,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79945.9, 'new_value': 86302.1}, {'field': 'total_amount', 'old_value': 79945.9, 'new_value': 86302.1}, {'field': 'order_count', 'old_value': 334, 'new_value': 362}]
2025-06-15 12:00:54,671 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-15 12:00:55,125 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-15 12:00:55,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70594.0, 'new_value': 77640.0}, {'field': 'offline_amount', 'old_value': 28773.85, 'new_value': 31709.65}, {'field': 'total_amount', 'old_value': 99367.85, 'new_value': 109349.65}, {'field': 'order_count', 'old_value': 702, 'new_value': 763}]
2025-06-15 12:00:55,125 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-15 12:00:55,640 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-15 12:00:55,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73055.52, 'new_value': 78639.43}, {'field': 'total_amount', 'old_value': 73055.52, 'new_value': 78639.43}, {'field': 'order_count', 'old_value': 2533, 'new_value': 2740}]
2025-06-15 12:00:55,640 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-15 12:00:56,203 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-15 12:00:56,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30154.0, 'new_value': 33649.0}, {'field': 'offline_amount', 'old_value': 99820.0, 'new_value': 110801.0}, {'field': 'total_amount', 'old_value': 129974.0, 'new_value': 144450.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 111}]
2025-06-15 12:00:56,203 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-15 12:00:56,687 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-15 12:00:56,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8283.06, 'new_value': 8939.34}, {'field': 'offline_amount', 'old_value': 179201.5, 'new_value': 201785.57}, {'field': 'total_amount', 'old_value': 187484.56, 'new_value': 210724.91}, {'field': 'order_count', 'old_value': 943, 'new_value': 1038}]
2025-06-15 12:00:56,687 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-15 12:00:57,203 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-15 12:00:57,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30892.0, 'new_value': 35290.0}, {'field': 'total_amount', 'old_value': 34438.0, 'new_value': 38836.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 272}]
2025-06-15 12:00:57,203 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-15 12:00:57,687 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-15 12:00:57,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63544.0, 'new_value': 72184.0}, {'field': 'total_amount', 'old_value': 63544.0, 'new_value': 72184.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-15 12:00:57,687 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-15 12:00:58,203 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-15 12:00:58,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39687.34, 'new_value': 42726.64}, {'field': 'total_amount', 'old_value': 39687.34, 'new_value': 42726.64}, {'field': 'order_count', 'old_value': 2104, 'new_value': 2260}]
2025-06-15 12:00:58,203 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-15 12:00:58,640 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-15 12:00:58,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82191.0, 'new_value': 87813.0}, {'field': 'total_amount', 'old_value': 82191.0, 'new_value': 87813.0}, {'field': 'order_count', 'old_value': 2127, 'new_value': 2280}]
2025-06-15 12:00:58,640 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-15 12:00:59,109 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-15 12:00:59,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70161.94, 'new_value': 74949.9}, {'field': 'total_amount', 'old_value': 70161.94, 'new_value': 74949.9}, {'field': 'order_count', 'old_value': 891, 'new_value': 958}]
2025-06-15 12:00:59,109 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-15 12:00:59,593 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-15 12:00:59,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40001.0, 'new_value': 44612.0}, {'field': 'total_amount', 'old_value': 40001.0, 'new_value': 44612.0}, {'field': 'order_count', 'old_value': 2295, 'new_value': 2522}]
2025-06-15 12:00:59,593 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-15 12:01:00,046 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-15 12:01:00,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41661.54, 'new_value': 44232.74}, {'field': 'total_amount', 'old_value': 41661.54, 'new_value': 44232.74}, {'field': 'order_count', 'old_value': 161, 'new_value': 171}]
2025-06-15 12:01:00,046 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-15 12:01:00,531 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-15 12:01:00,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4976.0, 'new_value': 5632.0}, {'field': 'offline_amount', 'old_value': 6843.0, 'new_value': 8722.0}, {'field': 'total_amount', 'old_value': 11819.0, 'new_value': 14354.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 37}]
2025-06-15 12:01:00,531 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-15 12:01:00,984 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-15 12:01:00,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6891.99, 'new_value': 7806.79}, {'field': 'offline_amount', 'old_value': 13033.37, 'new_value': 14067.45}, {'field': 'total_amount', 'old_value': 19925.36, 'new_value': 21874.24}, {'field': 'order_count', 'old_value': 658, 'new_value': 727}]
2025-06-15 12:01:00,984 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-15 12:01:01,484 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-15 12:01:01,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95274.4, 'new_value': 106744.82}, {'field': 'offline_amount', 'old_value': 182341.48, 'new_value': 206721.75}, {'field': 'total_amount', 'old_value': 277615.88, 'new_value': 313466.57}, {'field': 'order_count', 'old_value': 2048, 'new_value': 2296}]
2025-06-15 12:01:01,484 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-15 12:01:01,906 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-15 12:01:01,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12634.45, 'new_value': 16596.45}, {'field': 'total_amount', 'old_value': 12661.45, 'new_value': 16623.45}, {'field': 'order_count', 'old_value': 62, 'new_value': 72}]
2025-06-15 12:01:01,906 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-15 12:01:02,390 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-15 12:01:02,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19685.0, 'new_value': 22131.0}, {'field': 'total_amount', 'old_value': 19685.0, 'new_value': 22131.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-15 12:01:02,390 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-15 12:01:02,874 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-15 12:01:02,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 205121.8, 'new_value': 231413.7}, {'field': 'offline_amount', 'old_value': 49856.1, 'new_value': 54221.1}, {'field': 'total_amount', 'old_value': 254977.9, 'new_value': 285634.8}, {'field': 'order_count', 'old_value': 317, 'new_value': 359}]
2025-06-15 12:01:02,874 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-15 12:01:03,312 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-15 12:01:03,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12040.04, 'new_value': 14210.04}, {'field': 'total_amount', 'old_value': 12040.04, 'new_value': 14210.04}, {'field': 'order_count', 'old_value': 538, 'new_value': 606}]
2025-06-15 12:01:03,312 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-15 12:01:03,859 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-15 12:01:03,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51800.0, 'new_value': 58440.0}, {'field': 'total_amount', 'old_value': 51800.0, 'new_value': 58440.0}, {'field': 'order_count', 'old_value': 198, 'new_value': 215}]
2025-06-15 12:01:03,859 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-15 12:01:04,468 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-15 12:01:04,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317476.94, 'new_value': 344180.77}, {'field': 'total_amount', 'old_value': 317476.94, 'new_value': 344180.77}, {'field': 'order_count', 'old_value': 2436, 'new_value': 2638}]
2025-06-15 12:01:04,468 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-15 12:01:04,937 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-15 12:01:04,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25299.0, 'new_value': 26797.0}, {'field': 'total_amount', 'old_value': 25299.0, 'new_value': 26797.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-15 12:01:04,937 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-15 12:01:05,406 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-15 12:01:05,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51644.29, 'new_value': 54852.4}, {'field': 'total_amount', 'old_value': 51644.29, 'new_value': 54852.4}, {'field': 'order_count', 'old_value': 1497, 'new_value': 1580}]
2025-06-15 12:01:05,406 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-15 12:01:05,874 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-15 12:01:05,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82946.0, 'new_value': 87010.0}, {'field': 'total_amount', 'old_value': 82946.0, 'new_value': 87010.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-06-15 12:01:05,874 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-15 12:01:06,312 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-15 12:01:06,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7173.7, 'new_value': 10143.2}, {'field': 'offline_amount', 'old_value': 18162.9, 'new_value': 18644.9}, {'field': 'total_amount', 'old_value': 25336.6, 'new_value': 28788.1}, {'field': 'order_count', 'old_value': 87, 'new_value': 99}]
2025-06-15 12:01:06,312 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-15 12:01:06,749 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-15 12:01:06,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29870.65, 'new_value': 33776.55}, {'field': 'total_amount', 'old_value': 29871.65, 'new_value': 33777.55}, {'field': 'order_count', 'old_value': 104, 'new_value': 110}]
2025-06-15 12:01:06,749 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-15 12:01:07,218 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-15 12:01:07,218 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114489.6, 'new_value': 121748.6}, {'field': 'offline_amount', 'old_value': 57405.15, 'new_value': 63862.15}, {'field': 'total_amount', 'old_value': 171894.75, 'new_value': 185610.75}, {'field': 'order_count', 'old_value': 747, 'new_value': 814}]
2025-06-15 12:01:07,218 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-15 12:01:07,687 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-15 12:01:07,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205700.0, 'new_value': 228770.0}, {'field': 'total_amount', 'old_value': 239759.0, 'new_value': 262829.0}, {'field': 'order_count', 'old_value': 1884, 'new_value': 1988}]
2025-06-15 12:01:07,687 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-15 12:01:08,203 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-15 12:01:08,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64090.0, 'new_value': 72539.0}, {'field': 'total_amount', 'old_value': 64090.0, 'new_value': 72539.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-06-15 12:01:08,203 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-15 12:01:08,687 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-15 12:01:08,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13800.0, 'new_value': 19080.0}, {'field': 'offline_amount', 'old_value': 44960.0, 'new_value': 58760.0}, {'field': 'total_amount', 'old_value': 58760.0, 'new_value': 77840.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-15 12:01:08,687 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOC
2025-06-15 12:01:09,140 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOC
2025-06-15 12:01:09,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34293.0, 'new_value': 45923.0}, {'field': 'total_amount', 'old_value': 34293.0, 'new_value': 45923.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 12}]
2025-06-15 12:01:09,140 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-15 12:01:09,546 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-15 12:01:09,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192286.76, 'new_value': 210142.49}, {'field': 'total_amount', 'old_value': 192286.76, 'new_value': 210142.49}, {'field': 'order_count', 'old_value': 606, 'new_value': 664}]
2025-06-15 12:01:09,546 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-15 12:01:09,968 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-15 12:01:09,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25733.0, 'new_value': 26506.0}, {'field': 'total_amount', 'old_value': 25733.0, 'new_value': 26506.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 42}]
2025-06-15 12:01:09,968 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-15 12:01:10,453 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-15 12:01:10,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5971.54, 'new_value': 8188.89}, {'field': 'total_amount', 'old_value': 10600.04, 'new_value': 12817.39}, {'field': 'order_count', 'old_value': 51, 'new_value': 59}]
2025-06-15 12:01:10,453 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-15 12:01:10,984 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-15 12:01:10,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5262.75, 'new_value': 5519.9}, {'field': 'offline_amount', 'old_value': 42666.0, 'new_value': 45921.0}, {'field': 'total_amount', 'old_value': 47928.75, 'new_value': 51440.9}, {'field': 'order_count', 'old_value': 916, 'new_value': 1040}]
2025-06-15 12:01:10,984 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-15 12:01:11,562 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-15 12:01:11,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14486.0, 'new_value': 15073.0}, {'field': 'total_amount', 'old_value': 14486.0, 'new_value': 15073.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 69}]
2025-06-15 12:01:11,562 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-15 12:01:12,015 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-15 12:01:12,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24549.7, 'new_value': 26549.7}, {'field': 'offline_amount', 'old_value': 14684.9, 'new_value': 15491.7}, {'field': 'total_amount', 'old_value': 39234.6, 'new_value': 42041.4}, {'field': 'order_count', 'old_value': 138, 'new_value': 143}]
2025-06-15 12:01:12,015 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-15 12:01:12,578 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-15 12:01:12,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7431.0, 'new_value': 8589.0}, {'field': 'offline_amount', 'old_value': 41870.16, 'new_value': 45886.86}, {'field': 'total_amount', 'old_value': 49301.16, 'new_value': 54475.86}, {'field': 'order_count', 'old_value': 438, 'new_value': 498}]
2025-06-15 12:01:12,578 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-15 12:01:13,031 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-15 12:01:13,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2389.0, 'new_value': 2534.0}, {'field': 'offline_amount', 'old_value': 14133.6, 'new_value': 15292.9}, {'field': 'total_amount', 'old_value': 16522.6, 'new_value': 17826.9}, {'field': 'order_count', 'old_value': 615, 'new_value': 658}]
2025-06-15 12:01:13,031 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-15 12:01:13,453 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-15 12:01:13,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11006.58, 'new_value': 11910.01}, {'field': 'offline_amount', 'old_value': 24225.28, 'new_value': 25781.98}, {'field': 'total_amount', 'old_value': 35231.86, 'new_value': 37691.99}, {'field': 'order_count', 'old_value': 1288, 'new_value': 1379}]
2025-06-15 12:01:13,453 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-15 12:01:13,999 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-15 12:01:13,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10387.0, 'new_value': 10686.0}, {'field': 'total_amount', 'old_value': 10387.0, 'new_value': 10686.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 58}]
2025-06-15 12:01:13,999 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-15 12:01:14,531 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-15 12:01:14,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42030.17, 'new_value': 50865.97}, {'field': 'offline_amount', 'old_value': 80259.16, 'new_value': 91656.16}, {'field': 'total_amount', 'old_value': 122289.33, 'new_value': 142522.13}, {'field': 'order_count', 'old_value': 710, 'new_value': 866}]
2025-06-15 12:01:14,531 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-15 12:01:14,984 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-15 12:01:14,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38756.62, 'new_value': 42184.12}, {'field': 'total_amount', 'old_value': 38756.62, 'new_value': 42184.12}, {'field': 'order_count', 'old_value': 1072, 'new_value': 1170}]
2025-06-15 12:01:14,984 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-15 12:01:15,515 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-15 12:01:15,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20155.49, 'new_value': 20247.26}, {'field': 'offline_amount', 'old_value': 27578.35, 'new_value': 34642.72}, {'field': 'total_amount', 'old_value': 47733.84, 'new_value': 54889.98}, {'field': 'order_count', 'old_value': 291, 'new_value': 324}]
2025-06-15 12:01:15,515 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFD
2025-06-15 12:01:15,953 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFD
2025-06-15 12:01:15,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36988.0, 'new_value': 39167.0}, {'field': 'total_amount', 'old_value': 36988.0, 'new_value': 39167.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-15 12:01:15,953 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-15 12:01:16,406 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-15 12:01:16,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9532.0, 'new_value': 10507.0}, {'field': 'total_amount', 'old_value': 9532.0, 'new_value': 10507.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-15 12:01:16,406 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-15 12:01:16,843 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-15 12:01:16,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9674.16, 'new_value': 10228.29}, {'field': 'offline_amount', 'old_value': 134806.03, 'new_value': 148884.97}, {'field': 'total_amount', 'old_value': 144480.19, 'new_value': 159113.26}, {'field': 'order_count', 'old_value': 961, 'new_value': 1039}]
2025-06-15 12:01:16,843 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-15 12:01:17,328 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-15 12:01:17,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24070.7, 'new_value': 26170.6}, {'field': 'total_amount', 'old_value': 24070.7, 'new_value': 26170.6}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-06-15 12:01:17,328 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-15 12:01:17,781 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-15 12:01:17,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80804.02, 'new_value': 90252.79}, {'field': 'offline_amount', 'old_value': 81786.55, 'new_value': 87223.65}, {'field': 'total_amount', 'old_value': 162590.57, 'new_value': 177476.44}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1554}]
2025-06-15 12:01:17,781 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-15 12:01:18,296 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-15 12:01:18,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16688.0, 'new_value': 20208.0}, {'field': 'total_amount', 'old_value': 49997.0, 'new_value': 53517.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-06-15 12:01:18,296 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0E
2025-06-15 12:01:18,765 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0E
2025-06-15 12:01:18,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92983.0, 'new_value': 98984.0}, {'field': 'total_amount', 'old_value': 92983.0, 'new_value': 98984.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-15 12:01:18,765 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-15 12:01:19,265 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-15 12:01:19,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53497.6, 'new_value': 56835.6}, {'field': 'total_amount', 'old_value': 53497.6, 'new_value': 56835.6}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-15 12:01:19,265 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-15 12:01:19,718 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-15 12:01:19,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4921.1, 'new_value': 5387.1}, {'field': 'offline_amount', 'old_value': 46653.0, 'new_value': 50153.0}, {'field': 'total_amount', 'old_value': 51574.1, 'new_value': 55540.1}, {'field': 'order_count', 'old_value': 37, 'new_value': 41}]
2025-06-15 12:01:19,718 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYQ
2025-06-15 12:01:20,234 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYQ
2025-06-15 12:01:20,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 1799.0}, {'field': 'total_amount', 'old_value': 47888.0, 'new_value': 48687.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-15 12:01:20,234 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-15 12:01:20,671 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-15 12:01:20,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28618.0, 'new_value': 30765.0}, {'field': 'total_amount', 'old_value': 28618.0, 'new_value': 30765.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 95}]
2025-06-15 12:01:20,671 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-15 12:01:21,202 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-15 12:01:21,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69950.95, 'new_value': 76323.37}, {'field': 'total_amount', 'old_value': 69950.95, 'new_value': 76323.37}, {'field': 'order_count', 'old_value': 1947, 'new_value': 2143}]
2025-06-15 12:01:21,202 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-15 12:01:21,671 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-15 12:01:21,671 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33492.91, 'new_value': 36587.83}, {'field': 'offline_amount', 'old_value': 45492.15, 'new_value': 49787.15}, {'field': 'total_amount', 'old_value': 78985.06, 'new_value': 86374.98}, {'field': 'order_count', 'old_value': 57, 'new_value': 65}]
2025-06-15 12:01:21,671 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-15 12:01:22,124 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-15 12:01:22,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8301.0, 'new_value': 9345.0}, {'field': 'total_amount', 'old_value': 8301.0, 'new_value': 9345.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 159}]
2025-06-15 12:01:22,124 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAR
2025-06-15 12:01:22,577 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAR
2025-06-15 12:01:22,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 542.0, 'new_value': 12201.0}, {'field': 'total_amount', 'old_value': 542.0, 'new_value': 12201.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 5}]
2025-06-15 12:01:22,577 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-15 12:01:23,015 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-15 12:01:23,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46933.77, 'new_value': 51633.13}, {'field': 'total_amount', 'old_value': 46933.77, 'new_value': 51633.13}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-06-15 12:01:23,015 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIR
2025-06-15 12:01:23,468 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIR
2025-06-15 12:01:23,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6945.0, 'new_value': 8470.0}, {'field': 'total_amount', 'old_value': 6945.0, 'new_value': 8470.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-06-15 12:01:23,468 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-15 12:01:24,015 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-15 12:01:24,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142850.0, 'new_value': 160390.0}, {'field': 'total_amount', 'old_value': 142850.0, 'new_value': 160390.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 69}]
2025-06-15 12:01:24,015 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-15 12:01:24,515 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-15 12:01:24,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56320.3, 'new_value': 64592.8}, {'field': 'total_amount', 'old_value': 56320.3, 'new_value': 64592.8}, {'field': 'order_count', 'old_value': 140, 'new_value': 161}]
2025-06-15 12:01:24,515 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-15 12:01:25,015 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-15 12:01:25,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29480.2, 'new_value': 36794.25}, {'field': 'total_amount', 'old_value': 29480.2, 'new_value': 36794.25}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-15 12:01:25,015 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-15 12:01:25,499 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-15 12:01:25,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14180.4, 'new_value': 15586.4}, {'field': 'total_amount', 'old_value': 14180.4, 'new_value': 15586.4}, {'field': 'order_count', 'old_value': 133, 'new_value': 147}]
2025-06-15 12:01:25,499 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-15 12:01:25,952 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-15 12:01:25,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28073.1, 'new_value': 34532.9}, {'field': 'total_amount', 'old_value': 28073.1, 'new_value': 34532.9}, {'field': 'order_count', 'old_value': 182, 'new_value': 201}]
2025-06-15 12:01:25,952 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-15 12:01:26,437 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-15 12:01:26,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24197.05, 'new_value': 26162.45}, {'field': 'total_amount', 'old_value': 24206.05, 'new_value': 26171.45}, {'field': 'order_count', 'old_value': 969, 'new_value': 1051}]
2025-06-15 12:01:26,437 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-15 12:01:26,937 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-15 12:01:26,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179101.44, 'new_value': 205945.44}, {'field': 'total_amount', 'old_value': 199436.44, 'new_value': 226280.44}, {'field': 'order_count', 'old_value': 1038, 'new_value': 1182}]
2025-06-15 12:01:26,937 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-15 12:01:27,421 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-15 12:01:27,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40080.0, 'new_value': 44779.0}, {'field': 'total_amount', 'old_value': 42895.0, 'new_value': 47594.0}, {'field': 'order_count', 'old_value': 162, 'new_value': 180}]
2025-06-15 12:01:27,421 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-15 12:01:27,906 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-15 12:01:27,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102919.34, 'new_value': 114683.84}, {'field': 'offline_amount', 'old_value': 22441.27, 'new_value': 24614.54}, {'field': 'total_amount', 'old_value': 125360.61, 'new_value': 139298.38}, {'field': 'order_count', 'old_value': 505, 'new_value': 569}]
2025-06-15 12:01:27,906 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-15 12:01:28,343 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-15 12:01:28,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15238.93, 'new_value': 16551.19}, {'field': 'offline_amount', 'old_value': 10563.79, 'new_value': 11114.78}, {'field': 'total_amount', 'old_value': 25802.72, 'new_value': 27665.97}, {'field': 'order_count', 'old_value': 1582, 'new_value': 1694}]
2025-06-15 12:01:28,343 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-15 12:01:28,859 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-15 12:01:28,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30942.0, 'new_value': 37084.0}, {'field': 'total_amount', 'old_value': 30942.0, 'new_value': 37084.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-15 12:01:28,859 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-15 12:01:29,327 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-15 12:01:29,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365393.0, 'new_value': 397435.0}, {'field': 'total_amount', 'old_value': 365393.0, 'new_value': 397435.0}, {'field': 'order_count', 'old_value': 1151, 'new_value': 1260}]
2025-06-15 12:01:29,327 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-15 12:01:29,796 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-15 12:01:29,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15718.1, 'new_value': 17610.1}, {'field': 'total_amount', 'old_value': 15718.1, 'new_value': 17610.1}, {'field': 'order_count', 'old_value': 42, 'new_value': 47}]
2025-06-15 12:01:29,796 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-15 12:01:30,327 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-15 12:01:30,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19781.0, 'new_value': 20472.0}, {'field': 'total_amount', 'old_value': 20000.0, 'new_value': 20691.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 84}]
2025-06-15 12:01:30,327 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-15 12:01:30,906 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-15 12:01:30,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55920.0, 'new_value': 62469.0}, {'field': 'offline_amount', 'old_value': 244020.0, 'new_value': 262629.0}, {'field': 'total_amount', 'old_value': 299940.0, 'new_value': 325098.0}, {'field': 'order_count', 'old_value': 380, 'new_value': 410}]
2025-06-15 12:01:30,906 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-15 12:01:31,359 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-15 12:01:31,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2261.02, 'new_value': 2347.82}, {'field': 'offline_amount', 'old_value': 77508.73, 'new_value': 83562.17}, {'field': 'total_amount', 'old_value': 79769.75, 'new_value': 85909.99}, {'field': 'order_count', 'old_value': 1190, 'new_value': 1279}]
2025-06-15 12:01:31,359 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-15 12:01:31,859 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-15 12:01:31,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136978.0, 'new_value': 191132.0}, {'field': 'total_amount', 'old_value': 136978.0, 'new_value': 191132.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-06-15 12:01:31,859 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-15 12:01:32,374 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-15 12:01:32,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95186.0, 'new_value': 131912.0}, {'field': 'total_amount', 'old_value': 95186.0, 'new_value': 131912.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-06-15 12:01:32,374 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-15 12:01:32,859 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-15 12:01:32,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 813390.7, 'new_value': 924722.41}, {'field': 'total_amount', 'old_value': 871869.4, 'new_value': 983201.11}, {'field': 'order_count', 'old_value': 1612, 'new_value': 1809}]
2025-06-15 12:01:32,859 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-15 12:01:33,374 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-15 12:01:33,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14866.83, 'new_value': 16342.13}, {'field': 'total_amount', 'old_value': 14866.83, 'new_value': 16342.13}, {'field': 'order_count', 'old_value': 27, 'new_value': 31}]
2025-06-15 12:01:33,374 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-15 12:01:33,812 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-15 12:01:33,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 266.4, 'new_value': 1248.0}, {'field': 'offline_amount', 'old_value': 22124.4, 'new_value': 23863.1}, {'field': 'total_amount', 'old_value': 22390.8, 'new_value': 25111.1}, {'field': 'order_count', 'old_value': 79, 'new_value': 84}]
2025-06-15 12:01:33,812 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-15 12:01:34,265 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-15 12:01:34,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 282275.0, 'new_value': 361273.0}, {'field': 'total_amount', 'old_value': 282275.0, 'new_value': 361273.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-06-15 12:01:34,265 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-15 12:01:34,812 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-15 12:01:34,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143041.0, 'new_value': 154800.3}, {'field': 'total_amount', 'old_value': 143041.0, 'new_value': 154800.3}, {'field': 'order_count', 'old_value': 4308, 'new_value': 4642}]
2025-06-15 12:01:34,812 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-15 12:01:35,343 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-15 12:01:35,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18284.8, 'new_value': 21506.8}, {'field': 'total_amount', 'old_value': 18284.8, 'new_value': 21506.8}, {'field': 'order_count', 'old_value': 255, 'new_value': 295}]
2025-06-15 12:01:35,343 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-15 12:01:35,812 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-15 12:01:35,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76632.0, 'new_value': 77882.0}, {'field': 'total_amount', 'old_value': 76632.0, 'new_value': 77882.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-15 12:01:35,812 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-15 12:01:36,343 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-15 12:01:36,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350792.0, 'new_value': 419675.0}, {'field': 'total_amount', 'old_value': 350792.0, 'new_value': 419675.0}, {'field': 'order_count', 'old_value': 1681, 'new_value': 1948}]
2025-06-15 12:01:36,343 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-15 12:01:36,765 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-15 12:01:36,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171713.98, 'new_value': 171882.98}, {'field': 'offline_amount', 'old_value': 51008.5, 'new_value': 60931.18}, {'field': 'total_amount', 'old_value': 222722.48, 'new_value': 232814.16}, {'field': 'order_count', 'old_value': 445, 'new_value': 469}]
2025-06-15 12:01:36,765 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJT
2025-06-15 12:01:37,234 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJT
2025-06-15 12:01:37,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15448.0, 'new_value': 17412.0}, {'field': 'total_amount', 'old_value': 15448.0, 'new_value': 17412.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 37}]
2025-06-15 12:01:37,234 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-15 12:01:37,749 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-15 12:01:37,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23862.1, 'new_value': 25581.7}, {'field': 'total_amount', 'old_value': 23862.1, 'new_value': 25581.7}, {'field': 'order_count', 'old_value': 153, 'new_value': 162}]
2025-06-15 12:01:37,749 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-15 12:01:38,156 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-15 12:01:38,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235243.0, 'new_value': 248280.0}, {'field': 'total_amount', 'old_value': 235243.0, 'new_value': 248280.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 252}]
2025-06-15 12:01:38,156 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-15 12:01:38,734 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-15 12:01:38,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109845.0, 'new_value': 122261.0}, {'field': 'total_amount', 'old_value': 109845.0, 'new_value': 122261.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 130}]
2025-06-15 12:01:38,734 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-15 12:01:39,202 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-15 12:01:39,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18321.2, 'new_value': 20276.06}, {'field': 'total_amount', 'old_value': 18321.2, 'new_value': 20276.06}, {'field': 'order_count', 'old_value': 78, 'new_value': 87}]
2025-06-15 12:01:39,202 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-15 12:01:39,655 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-15 12:01:39,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49413.0, 'new_value': 53903.0}, {'field': 'offline_amount', 'old_value': 73473.0, 'new_value': 77865.0}, {'field': 'total_amount', 'old_value': 122886.0, 'new_value': 131768.0}, {'field': 'order_count', 'old_value': 2772, 'new_value': 2974}]
2025-06-15 12:01:39,655 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-15 12:01:40,109 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-15 12:01:40,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14873.19, 'new_value': 16270.0}, {'field': 'offline_amount', 'old_value': 14703.82, 'new_value': 15675.37}, {'field': 'total_amount', 'old_value': 29577.01, 'new_value': 31945.37}, {'field': 'order_count', 'old_value': 1334, 'new_value': 1445}]
2025-06-15 12:01:40,109 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-15 12:01:40,530 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-15 12:01:40,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67858.79, 'new_value': 74340.94}, {'field': 'total_amount', 'old_value': 73173.98, 'new_value': 79656.13}, {'field': 'order_count', 'old_value': 3850, 'new_value': 4162}]
2025-06-15 12:01:40,530 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-15 12:01:40,984 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-15 12:01:40,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26011.91, 'new_value': 28859.7}, {'field': 'offline_amount', 'old_value': 18127.36, 'new_value': 19869.28}, {'field': 'total_amount', 'old_value': 44139.27, 'new_value': 48728.98}, {'field': 'order_count', 'old_value': 2553, 'new_value': 2828}]
2025-06-15 12:01:40,984 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-15 12:01:41,515 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-15 12:01:41,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14473.0, 'new_value': 16143.0}, {'field': 'total_amount', 'old_value': 14473.0, 'new_value': 16143.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 151}]
2025-06-15 12:01:41,530 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-15 12:01:42,046 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-15 12:01:42,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46175.42, 'new_value': 50502.25}, {'field': 'offline_amount', 'old_value': 48022.29, 'new_value': 52587.15}, {'field': 'total_amount', 'old_value': 94197.71, 'new_value': 103089.4}, {'field': 'order_count', 'old_value': 3931, 'new_value': 4304}]
2025-06-15 12:01:42,046 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-15 12:01:42,468 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-15 12:01:42,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326449.67, 'new_value': 362883.22}, {'field': 'total_amount', 'old_value': 326449.67, 'new_value': 362883.22}, {'field': 'order_count', 'old_value': 3635, 'new_value': 3992}]
2025-06-15 12:01:42,468 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-15 12:01:42,921 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-15 12:01:42,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66452.0, 'new_value': 72260.0}, {'field': 'total_amount', 'old_value': 66452.0, 'new_value': 72260.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 256}]
2025-06-15 12:01:42,921 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-15 12:01:43,390 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-15 12:01:43,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162033.71, 'new_value': 180958.14}, {'field': 'offline_amount', 'old_value': 57997.4, 'new_value': 61971.0}, {'field': 'total_amount', 'old_value': 220031.11, 'new_value': 242929.14}, {'field': 'order_count', 'old_value': 680, 'new_value': 756}]
2025-06-15 12:01:43,390 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-15 12:01:43,984 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-15 12:01:43,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124800.0, 'new_value': 134820.0}, {'field': 'total_amount', 'old_value': 124800.0, 'new_value': 134820.0}, {'field': 'order_count', 'old_value': 10400, 'new_value': 11235}]
2025-06-15 12:01:43,984 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-15 12:01:44,499 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-15 12:01:44,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70631.0, 'new_value': 131863.0}, {'field': 'total_amount', 'old_value': 70631.0, 'new_value': 131863.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 28}]
2025-06-15 12:01:44,499 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-15 12:01:45,437 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-15 12:01:45,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83102.23, 'new_value': 89537.29}, {'field': 'offline_amount', 'old_value': 215910.01, 'new_value': 233688.59}, {'field': 'total_amount', 'old_value': 299012.24, 'new_value': 323225.88}, {'field': 'order_count', 'old_value': 2749, 'new_value': 2988}]
2025-06-15 12:01:45,437 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-15 12:01:45,937 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-15 12:01:45,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57794.95, 'new_value': 69887.9}, {'field': 'offline_amount', 'old_value': 167806.77, 'new_value': 186840.97}, {'field': 'total_amount', 'old_value': 225601.72, 'new_value': 256728.87}, {'field': 'order_count', 'old_value': 1805, 'new_value': 2093}]
2025-06-15 12:01:45,937 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHL
2025-06-15 12:01:46,421 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHL
2025-06-15 12:01:46,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12000.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 12500.0, 'new_value': 30500.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-15 12:01:46,421 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-15 12:01:46,890 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-15 12:01:46,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34640.6, 'new_value': 36638.6}, {'field': 'total_amount', 'old_value': 34640.6, 'new_value': 36638.6}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-15 12:01:46,890 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-15 12:01:47,343 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-15 12:01:47,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4208.9, 'new_value': 4808.9}, {'field': 'offline_amount', 'old_value': 24566.7, 'new_value': 25963.7}, {'field': 'total_amount', 'old_value': 28775.6, 'new_value': 30772.6}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-06-15 12:01:47,343 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-15 12:01:47,765 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-15 12:01:47,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49570.0, 'new_value': 54370.0}, {'field': 'total_amount', 'old_value': 49570.0, 'new_value': 54370.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-15 12:01:47,765 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-15 12:01:48,218 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-15 12:01:48,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117640.46, 'new_value': 131470.94}, {'field': 'total_amount', 'old_value': 117640.46, 'new_value': 131470.94}, {'field': 'order_count', 'old_value': 417, 'new_value': 463}]
2025-06-15 12:01:48,218 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-15 12:01:48,687 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-15 12:01:48,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2612.0}, {'field': 'total_amount', 'old_value': 24962.0, 'new_value': 27574.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 69}]
2025-06-15 12:01:48,687 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-15 12:01:49,234 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-15 12:01:49,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1624.1, 'new_value': 1852.1}, {'field': 'offline_amount', 'old_value': 44795.1, 'new_value': 47795.1}, {'field': 'total_amount', 'old_value': 46419.2, 'new_value': 49647.2}, {'field': 'order_count', 'old_value': 268, 'new_value': 293}]
2025-06-15 12:01:49,234 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-15 12:01:49,718 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-15 12:01:49,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179031.5, 'new_value': 196666.79}, {'field': 'total_amount', 'old_value': 179031.5, 'new_value': 196666.79}, {'field': 'order_count', 'old_value': 626, 'new_value': 684}]
2025-06-15 12:01:49,718 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-15 12:01:50,249 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-15 12:01:50,249 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 567.9, 'new_value': 667.9}, {'field': 'offline_amount', 'old_value': 33060.1, 'new_value': 36388.0}, {'field': 'total_amount', 'old_value': 33628.0, 'new_value': 37055.9}, {'field': 'order_count', 'old_value': 237, 'new_value': 260}]
2025-06-15 12:01:50,249 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-15 12:01:50,718 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-15 12:01:50,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15905.0, 'new_value': 17765.1}, {'field': 'total_amount', 'old_value': 15905.0, 'new_value': 17765.1}, {'field': 'order_count', 'old_value': 128, 'new_value': 143}]
2025-06-15 12:01:50,718 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-15 12:01:51,140 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-15 12:01:51,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78399.0, 'new_value': 83442.0}, {'field': 'total_amount', 'old_value': 87018.0, 'new_value': 92061.0}, {'field': 'order_count', 'old_value': 5992, 'new_value': 6355}]
2025-06-15 12:01:51,140 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-15 12:01:51,640 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-15 12:01:51,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175491.52, 'new_value': 190173.52}, {'field': 'offline_amount', 'old_value': 135929.24, 'new_value': 160059.24}, {'field': 'total_amount', 'old_value': 311420.76, 'new_value': 350232.76}, {'field': 'order_count', 'old_value': 2126, 'new_value': 2345}]
2025-06-15 12:01:51,640 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-15 12:01:52,187 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-15 12:01:52,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5630.0, 'new_value': 6389.0}, {'field': 'offline_amount', 'old_value': 1415.0, 'new_value': 1745.0}, {'field': 'total_amount', 'old_value': 7045.0, 'new_value': 8134.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 77}]
2025-06-15 12:01:52,187 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-15 12:01:52,640 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-15 12:01:52,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95380.0, 'new_value': 98466.0}, {'field': 'total_amount', 'old_value': 95380.0, 'new_value': 98466.0}, {'field': 'order_count', 'old_value': 3043, 'new_value': 3139}]
2025-06-15 12:01:52,640 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-15 12:01:53,093 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-15 12:01:53,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131961.77, 'new_value': 147695.15}, {'field': 'total_amount', 'old_value': 131961.77, 'new_value': 147695.15}, {'field': 'order_count', 'old_value': 781, 'new_value': 867}]
2025-06-15 12:01:53,093 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-15 12:01:53,562 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-15 12:01:53,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74851.0, 'new_value': 83567.0}, {'field': 'offline_amount', 'old_value': 43790.0, 'new_value': 48592.0}, {'field': 'total_amount', 'old_value': 118641.0, 'new_value': 132159.0}, {'field': 'order_count', 'old_value': 1730, 'new_value': 1899}]
2025-06-15 12:01:53,562 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-15 12:01:53,999 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-15 12:01:53,999 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13289.94, 'new_value': 14729.94}, {'field': 'offline_amount', 'old_value': 17289.6, 'new_value': 19289.6}, {'field': 'total_amount', 'old_value': 30579.54, 'new_value': 34019.54}, {'field': 'order_count', 'old_value': 6591, 'new_value': 6603}]
2025-06-15 12:01:53,999 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-15 12:01:54,468 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-15 12:01:54,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24705.42, 'new_value': 27729.85}, {'field': 'offline_amount', 'old_value': 240673.87, 'new_value': 264241.78}, {'field': 'total_amount', 'old_value': 265379.29, 'new_value': 291971.63}, {'field': 'order_count', 'old_value': 1207, 'new_value': 1314}]
2025-06-15 12:01:54,468 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-15 12:01:54,952 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-15 12:01:54,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64166.8, 'new_value': 77013.8}, {'field': 'total_amount', 'old_value': 163501.1, 'new_value': 176348.1}, {'field': 'order_count', 'old_value': 4197, 'new_value': 4538}]
2025-06-15 12:01:54,952 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-15 12:01:55,421 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-15 12:01:55,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40379.8, 'new_value': 64454.2}, {'field': 'offline_amount', 'old_value': 295446.2, 'new_value': 302446.2}, {'field': 'total_amount', 'old_value': 335826.0, 'new_value': 366900.4}, {'field': 'order_count', 'old_value': 1268, 'new_value': 1634}]
2025-06-15 12:01:55,421 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-15 12:01:55,859 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-15 12:01:55,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4717.75, 'new_value': 8408.65}, {'field': 'offline_amount', 'old_value': 16064.2, 'new_value': 17557.6}, {'field': 'total_amount', 'old_value': 20781.95, 'new_value': 25966.25}, {'field': 'order_count', 'old_value': 147, 'new_value': 169}]
2025-06-15 12:01:55,859 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-15 12:01:56,343 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-15 12:01:56,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162759.44, 'new_value': 179675.9}, {'field': 'total_amount', 'old_value': 162759.44, 'new_value': 179675.9}, {'field': 'order_count', 'old_value': 1472, 'new_value': 1636}]
2025-06-15 12:01:56,343 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-15 12:01:56,796 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-15 12:01:56,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94187.01, 'new_value': 106026.51}, {'field': 'offline_amount', 'old_value': 152769.88, 'new_value': 166236.16}, {'field': 'total_amount', 'old_value': 246956.89, 'new_value': 272262.67}, {'field': 'order_count', 'old_value': 2186, 'new_value': 2419}]
2025-06-15 12:01:56,796 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-15 12:01:57,343 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-15 12:01:57,343 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 107, 'new_value': 117}]
2025-06-15 12:01:57,343 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-15 12:01:57,812 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-15 12:01:57,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57518.0, 'new_value': 61574.0}, {'field': 'total_amount', 'old_value': 57518.0, 'new_value': 61574.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 149}]
2025-06-15 12:01:57,812 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-15 12:01:58,327 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-15 12:01:58,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24106.0, 'new_value': 25528.0}, {'field': 'total_amount', 'old_value': 24106.0, 'new_value': 25528.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-15 12:01:58,327 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-15 12:01:58,796 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-15 12:01:58,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 404598.0, 'new_value': 444100.0}, {'field': 'total_amount', 'old_value': 404598.0, 'new_value': 444100.0}, {'field': 'order_count', 'old_value': 515, 'new_value': 569}]
2025-06-15 12:01:58,796 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-15 12:01:59,265 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-15 12:01:59,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50210.4, 'new_value': 53696.9}, {'field': 'total_amount', 'old_value': 50210.4, 'new_value': 53696.9}, {'field': 'order_count', 'old_value': 228, 'new_value': 244}]
2025-06-15 12:01:59,265 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-15 12:01:59,718 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-15 12:01:59,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19317.42, 'new_value': 21123.44}, {'field': 'offline_amount', 'old_value': 24188.2, 'new_value': 25670.55}, {'field': 'total_amount', 'old_value': 43505.62, 'new_value': 46793.99}, {'field': 'order_count', 'old_value': 2267, 'new_value': 2395}]
2025-06-15 12:01:59,718 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-15 12:02:00,233 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-15 12:02:00,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71420.7, 'new_value': 78641.7}, {'field': 'total_amount', 'old_value': 71420.7, 'new_value': 78641.7}, {'field': 'order_count', 'old_value': 337, 'new_value': 373}]
2025-06-15 12:02:00,233 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-15 12:02:00,733 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-15 12:02:00,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48560.0, 'new_value': 55540.0}, {'field': 'total_amount', 'old_value': 48560.0, 'new_value': 55540.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-15 12:02:00,733 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-15 12:02:01,233 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-15 12:02:01,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21435.5, 'new_value': 27169.5}, {'field': 'total_amount', 'old_value': 21435.5, 'new_value': 27169.5}, {'field': 'order_count', 'old_value': 37, 'new_value': 43}]
2025-06-15 12:02:01,233 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-15 12:02:01,640 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-15 12:02:01,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16825.0, 'new_value': 18761.0}, {'field': 'total_amount', 'old_value': 16825.0, 'new_value': 18761.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-06-15 12:02:01,640 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-15 12:02:02,124 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-15 12:02:02,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8097.03, 'new_value': 9571.59}, {'field': 'offline_amount', 'old_value': 59657.0, 'new_value': 70499.0}, {'field': 'total_amount', 'old_value': 67754.03, 'new_value': 80070.59}, {'field': 'order_count', 'old_value': 34, 'new_value': 42}]
2025-06-15 12:02:02,124 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-15 12:02:02,593 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-15 12:02:02,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26924.0, 'new_value': 28726.0}, {'field': 'total_amount', 'old_value': 26924.0, 'new_value': 28726.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 28}]
2025-06-15 12:02:02,593 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-15 12:02:03,062 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-15 12:02:03,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71826.7, 'new_value': 80066.9}, {'field': 'total_amount', 'old_value': 71826.7, 'new_value': 80066.9}, {'field': 'order_count', 'old_value': 2005, 'new_value': 2254}]
2025-06-15 12:02:03,062 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-15 12:02:03,515 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-15 12:02:03,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114725.9, 'new_value': 122642.9}, {'field': 'total_amount', 'old_value': 114725.9, 'new_value': 122642.9}, {'field': 'order_count', 'old_value': 503, 'new_value': 540}]
2025-06-15 12:02:03,515 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-15 12:02:03,983 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-15 12:02:03,983 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3456.23, 'new_value': 3549.23}, {'field': 'offline_amount', 'old_value': 8628.29, 'new_value': 10167.33}, {'field': 'total_amount', 'old_value': 12084.52, 'new_value': 13716.56}, {'field': 'order_count', 'old_value': 124, 'new_value': 134}]
2025-06-15 12:02:03,983 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-15 12:02:04,468 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-15 12:02:04,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156087.17, 'new_value': 175902.13}, {'field': 'total_amount', 'old_value': 190268.92, 'new_value': 210083.88}, {'field': 'order_count', 'old_value': 3100, 'new_value': 3313}]
2025-06-15 12:02:04,468 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-15 12:02:04,937 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-15 12:02:04,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179537.0, 'new_value': 192938.0}, {'field': 'total_amount', 'old_value': 179537.0, 'new_value': 192938.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 155}]
2025-06-15 12:02:04,937 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-15 12:02:05,405 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-15 12:02:05,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61911.0, 'new_value': 65219.0}, {'field': 'total_amount', 'old_value': 62079.0, 'new_value': 65387.0}, {'field': 'order_count', 'old_value': 198, 'new_value': 209}]
2025-06-15 12:02:05,405 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-15 12:02:05,858 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-15 12:02:05,858 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 471.0, 'new_value': 1101.0}, {'field': 'offline_amount', 'old_value': 34480.51, 'new_value': 38044.01}, {'field': 'total_amount', 'old_value': 34951.51, 'new_value': 39145.01}, {'field': 'order_count', 'old_value': 218, 'new_value': 238}]
2025-06-15 12:02:05,858 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-15 12:02:06,327 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-15 12:02:06,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22235.39, 'new_value': 30522.47}, {'field': 'total_amount', 'old_value': 94572.29, 'new_value': 102859.37}, {'field': 'order_count', 'old_value': 5419, 'new_value': 5901}]
2025-06-15 12:02:06,327 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-15 12:02:06,796 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-15 12:02:06,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162844.4, 'new_value': 177631.4}, {'field': 'total_amount', 'old_value': 162844.4, 'new_value': 177631.4}, {'field': 'order_count', 'old_value': 224, 'new_value': 240}]
2025-06-15 12:02:06,796 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-15 12:02:07,327 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-15 12:02:07,327 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44103.0, 'new_value': 47214.0}, {'field': 'offline_amount', 'old_value': 122650.0, 'new_value': 132432.0}, {'field': 'total_amount', 'old_value': 166753.0, 'new_value': 179646.0}, {'field': 'order_count', 'old_value': 753, 'new_value': 818}]
2025-06-15 12:02:07,327 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-15 12:02:07,780 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-15 12:02:07,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52719.71, 'new_value': 56305.46}, {'field': 'offline_amount', 'old_value': 63798.98, 'new_value': 68988.26}, {'field': 'total_amount', 'old_value': 116518.69, 'new_value': 125293.72}, {'field': 'order_count', 'old_value': 3039, 'new_value': 3250}]
2025-06-15 12:02:07,780 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-15 12:02:08,265 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-15 12:02:08,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100462.52, 'new_value': 112036.12}, {'field': 'total_amount', 'old_value': 100462.52, 'new_value': 112036.12}, {'field': 'order_count', 'old_value': 435, 'new_value': 475}]
2025-06-15 12:02:08,265 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-15 12:02:08,702 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-15 12:02:08,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24149.0, 'new_value': 32265.0}, {'field': 'total_amount', 'old_value': 24149.0, 'new_value': 32265.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-15 12:02:08,702 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-15 12:02:09,202 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-15 12:02:09,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55762.2, 'new_value': 71755.2}, {'field': 'total_amount', 'old_value': 150297.45, 'new_value': 166290.45}, {'field': 'order_count', 'old_value': 260, 'new_value': 285}]
2025-06-15 12:02:09,202 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-15 12:02:09,655 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-15 12:02:09,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1425.0, 'new_value': 1525.0}, {'field': 'offline_amount', 'old_value': 19185.0, 'new_value': 20085.0}, {'field': 'total_amount', 'old_value': 20610.0, 'new_value': 21610.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 284}]
2025-06-15 12:02:09,655 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-15 12:02:10,155 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-15 12:02:10,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78267.0, 'new_value': 87790.0}, {'field': 'offline_amount', 'old_value': 95274.0, 'new_value': 97273.0}, {'field': 'total_amount', 'old_value': 173541.0, 'new_value': 185063.0}, {'field': 'order_count', 'old_value': 111067, 'new_value': 122589}]
2025-06-15 12:02:10,155 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-15 12:02:10,593 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-15 12:02:10,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27475.05, 'new_value': 31337.67}, {'field': 'offline_amount', 'old_value': 37578.69, 'new_value': 39394.08}, {'field': 'total_amount', 'old_value': 65053.74, 'new_value': 70731.75}, {'field': 'order_count', 'old_value': 3241, 'new_value': 3560}]
2025-06-15 12:02:10,593 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-15 12:02:10,983 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-15 12:02:10,983 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2678.02, 'new_value': 3113.92}, {'field': 'offline_amount', 'old_value': 12249.68, 'new_value': 12447.68}, {'field': 'total_amount', 'old_value': 14927.7, 'new_value': 15561.6}, {'field': 'order_count', 'old_value': 53, 'new_value': 57}]
2025-06-15 12:02:10,983 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-15 12:02:11,483 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-15 12:02:11,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45406.0, 'new_value': 49407.0}, {'field': 'total_amount', 'old_value': 45406.0, 'new_value': 49407.0}, {'field': 'order_count', 'old_value': 6578, 'new_value': 7089}]
2025-06-15 12:02:11,483 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-15 12:02:11,921 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-15 12:02:11,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30268.0, 'new_value': 32935.0}, {'field': 'total_amount', 'old_value': 30268.0, 'new_value': 32935.0}, {'field': 'order_count', 'old_value': 6578, 'new_value': 7089}]
2025-06-15 12:02:11,921 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-15 12:02:12,343 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-15 12:02:12,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11974.89, 'new_value': 12827.03}, {'field': 'offline_amount', 'old_value': 6797.69, 'new_value': 7093.69}, {'field': 'total_amount', 'old_value': 18772.58, 'new_value': 19920.72}, {'field': 'order_count', 'old_value': 698, 'new_value': 723}]
2025-06-15 12:02:12,343 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-15 12:02:12,780 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-15 12:02:12,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285437.0, 'new_value': 388706.0}, {'field': 'total_amount', 'old_value': 285437.0, 'new_value': 388706.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 52}]
2025-06-15 12:02:12,780 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-15 12:02:13,233 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-15 12:02:13,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11449.0, 'new_value': 12498.0}, {'field': 'total_amount', 'old_value': 11449.0, 'new_value': 12498.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-15 12:02:13,233 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-15 12:02:13,702 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-15 12:02:13,702 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7523.51, 'new_value': 8000.64}, {'field': 'offline_amount', 'old_value': 30821.13, 'new_value': 32782.23}, {'field': 'total_amount', 'old_value': 38344.64, 'new_value': 40782.87}, {'field': 'order_count', 'old_value': 839, 'new_value': 895}]
2025-06-15 12:02:13,702 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-15 12:02:14,155 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-15 12:02:14,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101692.0, 'new_value': 106474.0}, {'field': 'offline_amount', 'old_value': 598095.0, 'new_value': 643213.0}, {'field': 'total_amount', 'old_value': 699787.0, 'new_value': 749687.0}, {'field': 'order_count', 'old_value': 17389, 'new_value': 18752}]
2025-06-15 12:02:14,155 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-15 12:02:14,655 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-15 12:02:14,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157987.0, 'new_value': 170231.0}, {'field': 'total_amount', 'old_value': 157987.0, 'new_value': 170231.0}, {'field': 'order_count', 'old_value': 3660, 'new_value': 3942}]
2025-06-15 12:02:14,655 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-15 12:02:15,124 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-15 12:02:15,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3363.0, 'new_value': 4572.5}, {'field': 'total_amount', 'old_value': 8863.0, 'new_value': 10072.5}, {'field': 'order_count', 'old_value': 87, 'new_value': 93}]
2025-06-15 12:02:15,124 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-15 12:02:15,577 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-15 12:02:15,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8363.0, 'new_value': 9270.0}, {'field': 'total_amount', 'old_value': 8363.0, 'new_value': 9270.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-15 12:02:15,577 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-15 12:02:16,077 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-15 12:02:16,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21873.82, 'new_value': 25547.42}, {'field': 'total_amount', 'old_value': 21873.82, 'new_value': 25547.42}, {'field': 'order_count', 'old_value': 668, 'new_value': 728}]
2025-06-15 12:02:16,077 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-15 12:02:16,561 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-15 12:02:16,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26940.0, 'new_value': 28540.0}, {'field': 'total_amount', 'old_value': 26940.0, 'new_value': 28540.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-15 12:02:16,561 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-15 12:02:16,968 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-15 12:02:16,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25366.9, 'new_value': 27382.6}, {'field': 'total_amount', 'old_value': 25366.9, 'new_value': 27382.6}, {'field': 'order_count', 'old_value': 137, 'new_value': 147}]
2025-06-15 12:02:16,968 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-15 12:02:17,436 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-15 12:02:17,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374528.04, 'new_value': 418962.85}, {'field': 'total_amount', 'old_value': 374528.04, 'new_value': 418962.85}, {'field': 'order_count', 'old_value': 3103, 'new_value': 3479}]
2025-06-15 12:02:17,436 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-15 12:02:17,952 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-15 12:02:17,952 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124565.0, 'new_value': 138108.0}, {'field': 'offline_amount', 'old_value': 7792.0, 'new_value': 8304.0}, {'field': 'total_amount', 'old_value': 132357.0, 'new_value': 146412.0}, {'field': 'order_count', 'old_value': 1273, 'new_value': 1405}]
2025-06-15 12:02:17,952 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-15 12:02:18,483 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-15 12:02:18,483 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16107.1, 'new_value': 17524.4}, {'field': 'offline_amount', 'old_value': 28356.9, 'new_value': 31368.0}, {'field': 'total_amount', 'old_value': 44464.0, 'new_value': 48892.4}, {'field': 'order_count', 'old_value': 1854, 'new_value': 2015}]
2025-06-15 12:02:18,483 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-15 12:02:18,905 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-15 12:02:18,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 394137.97, 'new_value': 422412.63}, {'field': 'total_amount', 'old_value': 394137.97, 'new_value': 422412.63}, {'field': 'order_count', 'old_value': 2396, 'new_value': 2524}]
2025-06-15 12:02:18,905 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-15 12:02:19,374 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-15 12:02:19,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18522.37, 'new_value': 20444.21}, {'field': 'total_amount', 'old_value': 19288.37, 'new_value': 21210.21}, {'field': 'order_count', 'old_value': 180, 'new_value': 202}]
2025-06-15 12:02:19,374 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-15 12:02:19,858 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-15 12:02:19,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16295.32, 'new_value': 24313.32}, {'field': 'total_amount', 'old_value': 16295.32, 'new_value': 24313.32}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-06-15 12:02:19,858 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-15 12:02:20,327 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-15 12:02:20,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225610.56, 'new_value': 251170.44}, {'field': 'total_amount', 'old_value': 241843.49, 'new_value': 267403.37}, {'field': 'order_count', 'old_value': 298, 'new_value': 319}]
2025-06-15 12:02:20,327 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-15 12:02:20,749 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-15 12:02:20,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2593256.18, 'new_value': 2893392.49}, {'field': 'total_amount', 'old_value': 2593256.18, 'new_value': 2893392.49}, {'field': 'order_count', 'old_value': 54657, 'new_value': 60007}]
2025-06-15 12:02:20,749 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-15 12:02:21,249 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-15 12:02:21,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44519.0, 'new_value': 47876.0}, {'field': 'total_amount', 'old_value': 44519.0, 'new_value': 47876.0}, {'field': 'order_count', 'old_value': 240, 'new_value': 258}]
2025-06-15 12:02:21,249 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-15 12:02:21,702 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-15 12:02:21,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6185.0, 'new_value': 6418.9}, {'field': 'total_amount', 'old_value': 6185.0, 'new_value': 6418.9}, {'field': 'order_count', 'old_value': 532, 'new_value': 555}]
2025-06-15 12:02:21,702 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-15 12:02:22,077 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-15 12:02:22,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20588.42, 'new_value': 24453.24}, {'field': 'total_amount', 'old_value': 51107.27, 'new_value': 54972.09}, {'field': 'order_count', 'old_value': 3399, 'new_value': 3665}]
2025-06-15 12:02:22,077 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-15 12:02:22,546 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-15 12:02:22,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36969.96, 'new_value': 44640.54}, {'field': 'total_amount', 'old_value': 91155.73, 'new_value': 98826.31}, {'field': 'order_count', 'old_value': 6078, 'new_value': 6580}]
2025-06-15 12:02:22,546 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-15 12:02:22,983 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-15 12:02:22,983 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2900.0, 'new_value': 18840.0}, {'field': 'total_amount', 'old_value': 38580.0, 'new_value': 54520.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-06-15 12:02:22,983 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-15 12:02:23,421 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-15 12:02:23,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34622.0, 'new_value': 37256.0}, {'field': 'total_amount', 'old_value': 34622.0, 'new_value': 37256.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-15 12:02:23,421 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-15 12:02:23,890 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-15 12:02:23,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355529.53, 'new_value': 402503.7}, {'field': 'total_amount', 'old_value': 355529.53, 'new_value': 402503.7}, {'field': 'order_count', 'old_value': 2368, 'new_value': 2698}]
2025-06-15 12:02:23,890 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-15 12:02:24,374 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-15 12:02:24,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238595.0, 'new_value': 261404.0}, {'field': 'total_amount', 'old_value': 238595.0, 'new_value': 261404.0}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1775}]
2025-06-15 12:02:24,374 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-15 12:02:24,796 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-15 12:02:24,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4160.95, 'new_value': 4725.85}, {'field': 'offline_amount', 'old_value': 27281.7, 'new_value': 29222.4}, {'field': 'total_amount', 'old_value': 31442.65, 'new_value': 33948.25}, {'field': 'order_count', 'old_value': 208, 'new_value': 237}]
2025-06-15 12:02:24,796 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-15 12:02:25,311 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-15 12:02:25,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75157.02, 'new_value': 82285.19}, {'field': 'total_amount', 'old_value': 75157.02, 'new_value': 82285.19}, {'field': 'order_count', 'old_value': 5352, 'new_value': 5865}]
2025-06-15 12:02:25,311 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-15 12:02:25,780 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-15 12:02:25,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84340.04, 'new_value': 92896.34}, {'field': 'offline_amount', 'old_value': 22512.44, 'new_value': 24150.44}, {'field': 'total_amount', 'old_value': 106852.48, 'new_value': 117046.78}, {'field': 'order_count', 'old_value': 461, 'new_value': 499}]
2025-06-15 12:02:25,780 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-15 12:02:26,265 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-15 12:02:26,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61197.0, 'new_value': 69201.0}, {'field': 'total_amount', 'old_value': 79945.5, 'new_value': 87949.5}, {'field': 'order_count', 'old_value': 1031, 'new_value': 1159}]
2025-06-15 12:02:26,265 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-15 12:02:26,796 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-15 12:02:26,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106222.47, 'new_value': 115060.03}, {'field': 'total_amount', 'old_value': 106222.47, 'new_value': 115060.03}, {'field': 'order_count', 'old_value': 1716, 'new_value': 1872}]
2025-06-15 12:02:26,811 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-15 12:02:27,155 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-15 12:02:27,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251413.0, 'new_value': 279468.0}, {'field': 'total_amount', 'old_value': 251413.0, 'new_value': 279468.0}, {'field': 'order_count', 'old_value': 5769, 'new_value': 6394}]
2025-06-15 12:02:27,155 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-15 12:02:27,655 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-15 12:02:27,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69700.35, 'new_value': 76040.35}, {'field': 'total_amount', 'old_value': 69700.35, 'new_value': 76040.35}, {'field': 'order_count', 'old_value': 1781, 'new_value': 1926}]
2025-06-15 12:02:27,655 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-15 12:02:28,061 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-15 12:02:28,061 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60682.64, 'new_value': 80760.0}, {'field': 'total_amount', 'old_value': 159264.85, 'new_value': 179342.21}, {'field': 'order_count', 'old_value': 1180, 'new_value': 1339}]
2025-06-15 12:02:28,061 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-15 12:02:28,530 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-15 12:02:28,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44294.0, 'new_value': 47799.0}, {'field': 'total_amount', 'old_value': 44294.0, 'new_value': 47799.0}, {'field': 'order_count', 'old_value': 407, 'new_value': 446}]
2025-06-15 12:02:28,530 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-15 12:02:29,155 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-15 12:02:29,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45864.0, 'new_value': 48406.0}, {'field': 'total_amount', 'old_value': 45864.0, 'new_value': 48406.0}, {'field': 'order_count', 'old_value': 687, 'new_value': 741}]
2025-06-15 12:02:29,155 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-15 12:02:29,686 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-15 12:02:29,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56128.0, 'new_value': 64148.0}, {'field': 'total_amount', 'old_value': 56128.0, 'new_value': 64148.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 297}]
2025-06-15 12:02:29,686 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-15 12:02:30,202 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-15 12:02:30,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27302.4, 'new_value': 46161.4}, {'field': 'total_amount', 'old_value': 27302.4, 'new_value': 46161.4}, {'field': 'order_count', 'old_value': 12, 'new_value': 20}]
2025-06-15 12:02:30,202 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM62
2025-06-15 12:02:30,640 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM62
2025-06-15 12:02:30,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9600.0, 'new_value': 17200.0}, {'field': 'total_amount', 'old_value': 9600.0, 'new_value': 17200.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-06-15 12:02:30,640 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-15 12:02:31,108 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-15 12:02:31,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129759.0, 'new_value': 148005.0}, {'field': 'total_amount', 'old_value': 161199.0, 'new_value': 179445.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 57}]
2025-06-15 12:02:31,108 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-15 12:02:31,546 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-15 12:02:31,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32595.0, 'new_value': 34450.0}, {'field': 'total_amount', 'old_value': 36040.0, 'new_value': 37895.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 143}]
2025-06-15 12:02:31,546 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-15 12:02:32,108 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-15 12:02:32,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22879.66, 'new_value': 24651.53}, {'field': 'total_amount', 'old_value': 22879.66, 'new_value': 24651.53}, {'field': 'order_count', 'old_value': 2898, 'new_value': 3114}]
2025-06-15 12:02:32,108 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-15 12:02:32,530 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-15 12:02:32,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158368.25, 'new_value': 180811.9}, {'field': 'total_amount', 'old_value': 158368.25, 'new_value': 180811.9}, {'field': 'order_count', 'old_value': 432, 'new_value': 492}]
2025-06-15 12:02:32,530 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-15 12:02:33,061 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-15 12:02:33,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57199.0, 'new_value': 61779.0}, {'field': 'total_amount', 'old_value': 57199.0, 'new_value': 61779.0}, {'field': 'order_count', 'old_value': 3181, 'new_value': 3440}]
2025-06-15 12:02:33,061 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-15 12:02:33,499 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-15 12:02:33,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1577361.08, 'new_value': 1731515.93}, {'field': 'total_amount', 'old_value': 1577361.08, 'new_value': 1731515.93}, {'field': 'order_count', 'old_value': 2985, 'new_value': 3258}]
2025-06-15 12:02:33,499 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-15 12:02:33,968 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-15 12:02:33,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 424864.0, 'new_value': 463163.0}, {'field': 'total_amount', 'old_value': 424864.0, 'new_value': 463163.0}, {'field': 'order_count', 'old_value': 2070, 'new_value': 2268}]
2025-06-15 12:02:33,968 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-15 12:02:34,452 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-15 12:02:34,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4955266.73, 'new_value': 5401062.73}, {'field': 'total_amount', 'old_value': 4955266.73, 'new_value': 5401062.73}, {'field': 'order_count', 'old_value': 18353, 'new_value': 20030}]
2025-06-15 12:02:34,452 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-15 12:02:34,858 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-15 12:02:34,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86575.95, 'new_value': 94876.83}, {'field': 'total_amount', 'old_value': 86575.95, 'new_value': 94876.83}, {'field': 'order_count', 'old_value': 9464, 'new_value': 10275}]
2025-06-15 12:02:34,858 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-15 12:02:35,343 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-15 12:02:35,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110582.8, 'new_value': 118467.08}, {'field': 'offline_amount', 'old_value': 95548.22, 'new_value': 102721.75}, {'field': 'total_amount', 'old_value': 206131.02, 'new_value': 221188.83}, {'field': 'order_count', 'old_value': 8894, 'new_value': 9603}]
2025-06-15 12:02:35,343 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-15 12:02:35,796 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-15 12:02:35,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65485.0, 'new_value': 73985.0}, {'field': 'total_amount', 'old_value': 65485.0, 'new_value': 73985.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-15 12:02:35,796 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-15 12:02:36,218 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-15 12:02:36,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113465.9, 'new_value': 120286.9}, {'field': 'total_amount', 'old_value': 113465.9, 'new_value': 120286.9}, {'field': 'order_count', 'old_value': 3926, 'new_value': 4161}]
2025-06-15 12:02:36,233 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-15 12:02:36,671 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-15 12:02:36,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88199.37, 'new_value': 93969.0}, {'field': 'total_amount', 'old_value': 88199.37, 'new_value': 93969.0}, {'field': 'order_count', 'old_value': 6710, 'new_value': 7142}]
2025-06-15 12:02:36,671 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-15 12:02:37,139 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-15 12:02:37,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9809.25, 'new_value': 11381.95}, {'field': 'total_amount', 'old_value': 12900.4, 'new_value': 14473.1}, {'field': 'order_count', 'old_value': 1320, 'new_value': 1488}]
2025-06-15 12:02:37,139 - INFO - 日期 2025-06 处理完成 - 更新: 254 条，插入: 0 条，错误: 0 条
2025-06-15 12:02:37,139 - INFO - 数据同步完成！更新: 254 条，插入: 0 条，错误: 0 条
2025-06-15 12:02:37,139 - INFO - =================同步完成====================
2025-06-15 15:00:02,897 - INFO - =================使用默认全量同步=============
2025-06-15 15:00:04,647 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 15:00:04,647 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 15:00:04,678 - INFO - 开始处理日期: 2025-01
2025-06-15 15:00:04,678 - INFO - Request Parameters - Page 1:
2025-06-15 15:00:04,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:04,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:05,678 - INFO - Response - Page 1:
2025-06-15 15:00:05,881 - INFO - 第 1 页获取到 100 条记录
2025-06-15 15:00:05,881 - INFO - Request Parameters - Page 2:
2025-06-15 15:00:05,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:05,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:06,865 - INFO - Response - Page 2:
2025-06-15 15:00:07,068 - INFO - 第 2 页获取到 100 条记录
2025-06-15 15:00:07,068 - INFO - Request Parameters - Page 3:
2025-06-15 15:00:07,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:07,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:07,600 - INFO - Response - Page 3:
2025-06-15 15:00:07,803 - INFO - 第 3 页获取到 100 条记录
2025-06-15 15:00:07,803 - INFO - Request Parameters - Page 4:
2025-06-15 15:00:07,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:07,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:08,397 - INFO - Response - Page 4:
2025-06-15 15:00:08,600 - INFO - 第 4 页获取到 100 条记录
2025-06-15 15:00:08,600 - INFO - Request Parameters - Page 5:
2025-06-15 15:00:08,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:08,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:09,240 - INFO - Response - Page 5:
2025-06-15 15:00:09,443 - INFO - 第 5 页获取到 100 条记录
2025-06-15 15:00:09,443 - INFO - Request Parameters - Page 6:
2025-06-15 15:00:09,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:09,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:10,006 - INFO - Response - Page 6:
2025-06-15 15:00:10,209 - INFO - 第 6 页获取到 100 条记录
2025-06-15 15:00:10,209 - INFO - Request Parameters - Page 7:
2025-06-15 15:00:10,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:10,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:10,772 - INFO - Response - Page 7:
2025-06-15 15:00:10,975 - INFO - 第 7 页获取到 82 条记录
2025-06-15 15:00:10,975 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 15:00:10,975 - INFO - 获取到 682 条表单数据
2025-06-15 15:00:10,975 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 15:00:10,990 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:10,990 - INFO - 开始处理日期: 2025-02
2025-06-15 15:00:10,990 - INFO - Request Parameters - Page 1:
2025-06-15 15:00:10,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:10,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:11,506 - INFO - Response - Page 1:
2025-06-15 15:00:11,709 - INFO - 第 1 页获取到 100 条记录
2025-06-15 15:00:11,709 - INFO - Request Parameters - Page 2:
2025-06-15 15:00:11,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:11,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:12,209 - INFO - Response - Page 2:
2025-06-15 15:00:12,412 - INFO - 第 2 页获取到 100 条记录
2025-06-15 15:00:12,412 - INFO - Request Parameters - Page 3:
2025-06-15 15:00:12,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:12,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:12,928 - INFO - Response - Page 3:
2025-06-15 15:00:13,131 - INFO - 第 3 页获取到 100 条记录
2025-06-15 15:00:13,131 - INFO - Request Parameters - Page 4:
2025-06-15 15:00:13,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:13,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:13,693 - INFO - Response - Page 4:
2025-06-15 15:00:13,897 - INFO - 第 4 页获取到 100 条记录
2025-06-15 15:00:13,897 - INFO - Request Parameters - Page 5:
2025-06-15 15:00:13,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:13,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:14,459 - INFO - Response - Page 5:
2025-06-15 15:00:14,662 - INFO - 第 5 页获取到 100 条记录
2025-06-15 15:00:14,662 - INFO - Request Parameters - Page 6:
2025-06-15 15:00:14,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:14,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:15,162 - INFO - Response - Page 6:
2025-06-15 15:00:15,365 - INFO - 第 6 页获取到 100 条记录
2025-06-15 15:00:15,365 - INFO - Request Parameters - Page 7:
2025-06-15 15:00:15,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:15,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:15,834 - INFO - Response - Page 7:
2025-06-15 15:00:16,037 - INFO - 第 7 页获取到 70 条记录
2025-06-15 15:00:16,037 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 15:00:16,037 - INFO - 获取到 670 条表单数据
2025-06-15 15:00:16,037 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 15:00:16,053 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:16,053 - INFO - 开始处理日期: 2025-03
2025-06-15 15:00:16,053 - INFO - Request Parameters - Page 1:
2025-06-15 15:00:16,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:16,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:16,647 - INFO - Response - Page 1:
2025-06-15 15:00:16,850 - INFO - 第 1 页获取到 100 条记录
2025-06-15 15:00:16,850 - INFO - Request Parameters - Page 2:
2025-06-15 15:00:16,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:16,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:17,397 - INFO - Response - Page 2:
2025-06-15 15:00:17,600 - INFO - 第 2 页获取到 100 条记录
2025-06-15 15:00:17,600 - INFO - Request Parameters - Page 3:
2025-06-15 15:00:17,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:17,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:18,084 - INFO - Response - Page 3:
2025-06-15 15:00:18,287 - INFO - 第 3 页获取到 100 条记录
2025-06-15 15:00:18,287 - INFO - Request Parameters - Page 4:
2025-06-15 15:00:18,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:18,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:18,897 - INFO - Response - Page 4:
2025-06-15 15:00:19,100 - INFO - 第 4 页获取到 100 条记录
2025-06-15 15:00:19,100 - INFO - Request Parameters - Page 5:
2025-06-15 15:00:19,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:19,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:19,568 - INFO - Response - Page 5:
2025-06-15 15:00:19,772 - INFO - 第 5 页获取到 100 条记录
2025-06-15 15:00:19,772 - INFO - Request Parameters - Page 6:
2025-06-15 15:00:19,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:19,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:20,240 - INFO - Response - Page 6:
2025-06-15 15:00:20,443 - INFO - 第 6 页获取到 100 条记录
2025-06-15 15:00:20,443 - INFO - Request Parameters - Page 7:
2025-06-15 15:00:20,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:20,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:20,865 - INFO - Response - Page 7:
2025-06-15 15:00:21,068 - INFO - 第 7 页获取到 61 条记录
2025-06-15 15:00:21,068 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 15:00:21,068 - INFO - 获取到 661 条表单数据
2025-06-15 15:00:21,068 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 15:00:21,084 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:21,084 - INFO - 开始处理日期: 2025-04
2025-06-15 15:00:21,084 - INFO - Request Parameters - Page 1:
2025-06-15 15:00:21,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:21,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:21,584 - INFO - Response - Page 1:
2025-06-15 15:00:21,787 - INFO - 第 1 页获取到 100 条记录
2025-06-15 15:00:21,787 - INFO - Request Parameters - Page 2:
2025-06-15 15:00:21,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:21,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:22,350 - INFO - Response - Page 2:
2025-06-15 15:00:22,553 - INFO - 第 2 页获取到 100 条记录
2025-06-15 15:00:22,553 - INFO - Request Parameters - Page 3:
2025-06-15 15:00:22,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:22,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:23,178 - INFO - Response - Page 3:
2025-06-15 15:00:23,381 - INFO - 第 3 页获取到 100 条记录
2025-06-15 15:00:23,381 - INFO - Request Parameters - Page 4:
2025-06-15 15:00:23,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:23,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:23,959 - INFO - Response - Page 4:
2025-06-15 15:00:24,162 - INFO - 第 4 页获取到 100 条记录
2025-06-15 15:00:24,162 - INFO - Request Parameters - Page 5:
2025-06-15 15:00:24,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:24,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:24,709 - INFO - Response - Page 5:
2025-06-15 15:00:24,912 - INFO - 第 5 页获取到 100 条记录
2025-06-15 15:00:24,912 - INFO - Request Parameters - Page 6:
2025-06-15 15:00:24,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:24,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:25,506 - INFO - Response - Page 6:
2025-06-15 15:00:25,709 - INFO - 第 6 页获取到 100 条记录
2025-06-15 15:00:25,709 - INFO - Request Parameters - Page 7:
2025-06-15 15:00:25,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:25,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:26,225 - INFO - Response - Page 7:
2025-06-15 15:00:26,428 - INFO - 第 7 页获取到 56 条记录
2025-06-15 15:00:26,428 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 15:00:26,428 - INFO - 获取到 656 条表单数据
2025-06-15 15:00:26,428 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 15:00:26,443 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:26,443 - INFO - 开始处理日期: 2025-05
2025-06-15 15:00:26,443 - INFO - Request Parameters - Page 1:
2025-06-15 15:00:26,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:26,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:26,943 - INFO - Response - Page 1:
2025-06-15 15:00:27,146 - INFO - 第 1 页获取到 100 条记录
2025-06-15 15:00:27,146 - INFO - Request Parameters - Page 2:
2025-06-15 15:00:27,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:27,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:27,693 - INFO - Response - Page 2:
2025-06-15 15:00:27,896 - INFO - 第 2 页获取到 100 条记录
2025-06-15 15:00:27,896 - INFO - Request Parameters - Page 3:
2025-06-15 15:00:27,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:27,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:28,365 - INFO - Response - Page 3:
2025-06-15 15:00:28,568 - INFO - 第 3 页获取到 100 条记录
2025-06-15 15:00:28,568 - INFO - Request Parameters - Page 4:
2025-06-15 15:00:28,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:28,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:29,115 - INFO - Response - Page 4:
2025-06-15 15:00:29,318 - INFO - 第 4 页获取到 100 条记录
2025-06-15 15:00:29,318 - INFO - Request Parameters - Page 5:
2025-06-15 15:00:29,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:29,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:29,787 - INFO - Response - Page 5:
2025-06-15 15:00:29,990 - INFO - 第 5 页获取到 100 条记录
2025-06-15 15:00:29,990 - INFO - Request Parameters - Page 6:
2025-06-15 15:00:29,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:29,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:30,568 - INFO - Response - Page 6:
2025-06-15 15:00:30,771 - INFO - 第 6 页获取到 100 条记录
2025-06-15 15:00:30,771 - INFO - Request Parameters - Page 7:
2025-06-15 15:00:30,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:30,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:31,209 - INFO - Response - Page 7:
2025-06-15 15:00:31,412 - INFO - 第 7 页获取到 40 条记录
2025-06-15 15:00:31,412 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 15:00:31,412 - INFO - 获取到 640 条表单数据
2025-06-15 15:00:31,412 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 15:00:31,428 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:31,428 - INFO - 开始处理日期: 2025-06
2025-06-15 15:00:31,428 - INFO - Request Parameters - Page 1:
2025-06-15 15:00:31,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:31,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:31,943 - INFO - Response - Page 1:
2025-06-15 15:00:32,146 - INFO - 第 1 页获取到 100 条记录
2025-06-15 15:00:32,146 - INFO - Request Parameters - Page 2:
2025-06-15 15:00:32,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:32,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:32,709 - INFO - Response - Page 2:
2025-06-15 15:00:32,912 - INFO - 第 2 页获取到 100 条记录
2025-06-15 15:00:32,912 - INFO - Request Parameters - Page 3:
2025-06-15 15:00:32,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:32,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:33,412 - INFO - Response - Page 3:
2025-06-15 15:00:33,615 - INFO - 第 3 页获取到 100 条记录
2025-06-15 15:00:33,615 - INFO - Request Parameters - Page 4:
2025-06-15 15:00:33,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:33,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:34,131 - INFO - Response - Page 4:
2025-06-15 15:00:34,334 - INFO - 第 4 页获取到 100 条记录
2025-06-15 15:00:34,334 - INFO - Request Parameters - Page 5:
2025-06-15 15:00:34,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:34,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:34,912 - INFO - Response - Page 5:
2025-06-15 15:00:35,115 - INFO - 第 5 页获取到 100 条记录
2025-06-15 15:00:35,115 - INFO - Request Parameters - Page 6:
2025-06-15 15:00:35,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:35,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:35,646 - INFO - Response - Page 6:
2025-06-15 15:00:35,850 - INFO - 第 6 页获取到 100 条记录
2025-06-15 15:00:35,850 - INFO - Request Parameters - Page 7:
2025-06-15 15:00:35,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 15:00:35,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 15:00:36,162 - INFO - Response - Page 7:
2025-06-15 15:00:36,365 - INFO - 第 7 页获取到 22 条记录
2025-06-15 15:00:36,365 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 15:00:36,365 - INFO - 获取到 622 条表单数据
2025-06-15 15:00:36,365 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 15:00:36,365 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-15 15:00:36,912 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-15 15:00:36,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13205.0, 'new_value': 18285.0}, {'field': 'total_amount', 'old_value': 23805.0, 'new_value': 28885.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-15 15:00:36,928 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:36,928 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-15 15:00:36,928 - INFO - =================同步完成====================
2025-06-15 18:00:02,341 - INFO - =================使用默认全量同步=============
2025-06-15 18:00:04,044 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 18:00:04,044 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 18:00:04,075 - INFO - 开始处理日期: 2025-01
2025-06-15 18:00:04,075 - INFO - Request Parameters - Page 1:
2025-06-15 18:00:04,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:04,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:05,544 - INFO - Response - Page 1:
2025-06-15 18:00:05,747 - INFO - 第 1 页获取到 100 条记录
2025-06-15 18:00:05,747 - INFO - Request Parameters - Page 2:
2025-06-15 18:00:05,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:05,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:06,403 - INFO - Response - Page 2:
2025-06-15 18:00:06,606 - INFO - 第 2 页获取到 100 条记录
2025-06-15 18:00:06,606 - INFO - Request Parameters - Page 3:
2025-06-15 18:00:06,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:06,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:07,169 - INFO - Response - Page 3:
2025-06-15 18:00:07,372 - INFO - 第 3 页获取到 100 条记录
2025-06-15 18:00:07,372 - INFO - Request Parameters - Page 4:
2025-06-15 18:00:07,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:07,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:08,309 - INFO - Response - Page 4:
2025-06-15 18:00:08,512 - INFO - 第 4 页获取到 100 条记录
2025-06-15 18:00:08,512 - INFO - Request Parameters - Page 5:
2025-06-15 18:00:08,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:08,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:09,044 - INFO - Response - Page 5:
2025-06-15 18:00:09,247 - INFO - 第 5 页获取到 100 条记录
2025-06-15 18:00:09,247 - INFO - Request Parameters - Page 6:
2025-06-15 18:00:09,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:09,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:09,872 - INFO - Response - Page 6:
2025-06-15 18:00:10,075 - INFO - 第 6 页获取到 100 条记录
2025-06-15 18:00:10,075 - INFO - Request Parameters - Page 7:
2025-06-15 18:00:10,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:10,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:10,544 - INFO - Response - Page 7:
2025-06-15 18:00:10,747 - INFO - 第 7 页获取到 82 条记录
2025-06-15 18:00:10,747 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 18:00:10,747 - INFO - 获取到 682 条表单数据
2025-06-15 18:00:10,747 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 18:00:10,762 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:10,762 - INFO - 开始处理日期: 2025-02
2025-06-15 18:00:10,762 - INFO - Request Parameters - Page 1:
2025-06-15 18:00:10,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:10,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:11,262 - INFO - Response - Page 1:
2025-06-15 18:00:11,465 - INFO - 第 1 页获取到 100 条记录
2025-06-15 18:00:11,465 - INFO - Request Parameters - Page 2:
2025-06-15 18:00:11,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:11,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:11,950 - INFO - Response - Page 2:
2025-06-15 18:00:12,153 - INFO - 第 2 页获取到 100 条记录
2025-06-15 18:00:12,153 - INFO - Request Parameters - Page 3:
2025-06-15 18:00:12,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:12,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:12,731 - INFO - Response - Page 3:
2025-06-15 18:00:12,934 - INFO - 第 3 页获取到 100 条记录
2025-06-15 18:00:12,934 - INFO - Request Parameters - Page 4:
2025-06-15 18:00:12,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:12,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:13,497 - INFO - Response - Page 4:
2025-06-15 18:00:13,700 - INFO - 第 4 页获取到 100 条记录
2025-06-15 18:00:13,700 - INFO - Request Parameters - Page 5:
2025-06-15 18:00:13,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:13,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:14,169 - INFO - Response - Page 5:
2025-06-15 18:00:14,372 - INFO - 第 5 页获取到 100 条记录
2025-06-15 18:00:14,372 - INFO - Request Parameters - Page 6:
2025-06-15 18:00:14,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:14,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:14,934 - INFO - Response - Page 6:
2025-06-15 18:00:15,137 - INFO - 第 6 页获取到 100 条记录
2025-06-15 18:00:15,137 - INFO - Request Parameters - Page 7:
2025-06-15 18:00:15,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:15,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:15,606 - INFO - Response - Page 7:
2025-06-15 18:00:15,809 - INFO - 第 7 页获取到 70 条记录
2025-06-15 18:00:15,809 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 18:00:15,809 - INFO - 获取到 670 条表单数据
2025-06-15 18:00:15,809 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 18:00:15,825 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:15,825 - INFO - 开始处理日期: 2025-03
2025-06-15 18:00:15,825 - INFO - Request Parameters - Page 1:
2025-06-15 18:00:15,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:15,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:16,356 - INFO - Response - Page 1:
2025-06-15 18:00:16,559 - INFO - 第 1 页获取到 100 条记录
2025-06-15 18:00:16,559 - INFO - Request Parameters - Page 2:
2025-06-15 18:00:16,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:16,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:17,075 - INFO - Response - Page 2:
2025-06-15 18:00:17,278 - INFO - 第 2 页获取到 100 条记录
2025-06-15 18:00:17,278 - INFO - Request Parameters - Page 3:
2025-06-15 18:00:17,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:17,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:17,794 - INFO - Response - Page 3:
2025-06-15 18:00:17,997 - INFO - 第 3 页获取到 100 条记录
2025-06-15 18:00:17,997 - INFO - Request Parameters - Page 4:
2025-06-15 18:00:17,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:17,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:18,575 - INFO - Response - Page 4:
2025-06-15 18:00:18,778 - INFO - 第 4 页获取到 100 条记录
2025-06-15 18:00:18,778 - INFO - Request Parameters - Page 5:
2025-06-15 18:00:18,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:18,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:19,247 - INFO - Response - Page 5:
2025-06-15 18:00:19,450 - INFO - 第 5 页获取到 100 条记录
2025-06-15 18:00:19,450 - INFO - Request Parameters - Page 6:
2025-06-15 18:00:19,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:19,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:19,903 - INFO - Response - Page 6:
2025-06-15 18:00:20,106 - INFO - 第 6 页获取到 100 条记录
2025-06-15 18:00:20,106 - INFO - Request Parameters - Page 7:
2025-06-15 18:00:20,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:20,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:20,606 - INFO - Response - Page 7:
2025-06-15 18:00:20,809 - INFO - 第 7 页获取到 61 条记录
2025-06-15 18:00:20,809 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 18:00:20,809 - INFO - 获取到 661 条表单数据
2025-06-15 18:00:20,809 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 18:00:20,825 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:20,825 - INFO - 开始处理日期: 2025-04
2025-06-15 18:00:20,825 - INFO - Request Parameters - Page 1:
2025-06-15 18:00:20,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:20,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:21,340 - INFO - Response - Page 1:
2025-06-15 18:00:21,544 - INFO - 第 1 页获取到 100 条记录
2025-06-15 18:00:21,544 - INFO - Request Parameters - Page 2:
2025-06-15 18:00:21,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:21,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:21,997 - INFO - Response - Page 2:
2025-06-15 18:00:22,200 - INFO - 第 2 页获取到 100 条记录
2025-06-15 18:00:22,200 - INFO - Request Parameters - Page 3:
2025-06-15 18:00:22,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:22,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:22,715 - INFO - Response - Page 3:
2025-06-15 18:00:22,918 - INFO - 第 3 页获取到 100 条记录
2025-06-15 18:00:22,918 - INFO - Request Parameters - Page 4:
2025-06-15 18:00:22,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:22,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:23,434 - INFO - Response - Page 4:
2025-06-15 18:00:23,637 - INFO - 第 4 页获取到 100 条记录
2025-06-15 18:00:23,637 - INFO - Request Parameters - Page 5:
2025-06-15 18:00:23,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:23,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:24,184 - INFO - Response - Page 5:
2025-06-15 18:00:24,387 - INFO - 第 5 页获取到 100 条记录
2025-06-15 18:00:24,387 - INFO - Request Parameters - Page 6:
2025-06-15 18:00:24,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:24,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:24,872 - INFO - Response - Page 6:
2025-06-15 18:00:25,075 - INFO - 第 6 页获取到 100 条记录
2025-06-15 18:00:25,075 - INFO - Request Parameters - Page 7:
2025-06-15 18:00:25,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:25,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:25,622 - INFO - Response - Page 7:
2025-06-15 18:00:25,825 - INFO - 第 7 页获取到 56 条记录
2025-06-15 18:00:25,825 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 18:00:25,825 - INFO - 获取到 656 条表单数据
2025-06-15 18:00:25,825 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 18:00:25,840 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:25,840 - INFO - 开始处理日期: 2025-05
2025-06-15 18:00:25,840 - INFO - Request Parameters - Page 1:
2025-06-15 18:00:25,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:25,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:26,340 - INFO - Response - Page 1:
2025-06-15 18:00:26,543 - INFO - 第 1 页获取到 100 条记录
2025-06-15 18:00:26,543 - INFO - Request Parameters - Page 2:
2025-06-15 18:00:26,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:26,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:26,997 - INFO - Response - Page 2:
2025-06-15 18:00:27,200 - INFO - 第 2 页获取到 100 条记录
2025-06-15 18:00:27,200 - INFO - Request Parameters - Page 3:
2025-06-15 18:00:27,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:27,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:27,653 - INFO - Response - Page 3:
2025-06-15 18:00:27,856 - INFO - 第 3 页获取到 100 条记录
2025-06-15 18:00:27,856 - INFO - Request Parameters - Page 4:
2025-06-15 18:00:27,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:27,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:28,372 - INFO - Response - Page 4:
2025-06-15 18:00:28,575 - INFO - 第 4 页获取到 100 条记录
2025-06-15 18:00:28,575 - INFO - Request Parameters - Page 5:
2025-06-15 18:00:28,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:28,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:29,090 - INFO - Response - Page 5:
2025-06-15 18:00:29,293 - INFO - 第 5 页获取到 100 条记录
2025-06-15 18:00:29,293 - INFO - Request Parameters - Page 6:
2025-06-15 18:00:29,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:29,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:29,809 - INFO - Response - Page 6:
2025-06-15 18:00:30,012 - INFO - 第 6 页获取到 100 条记录
2025-06-15 18:00:30,012 - INFO - Request Parameters - Page 7:
2025-06-15 18:00:30,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:30,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:30,403 - INFO - Response - Page 7:
2025-06-15 18:00:30,606 - INFO - 第 7 页获取到 40 条记录
2025-06-15 18:00:30,606 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 18:00:30,606 - INFO - 获取到 640 条表单数据
2025-06-15 18:00:30,606 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 18:00:30,622 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:30,622 - INFO - 开始处理日期: 2025-06
2025-06-15 18:00:30,622 - INFO - Request Parameters - Page 1:
2025-06-15 18:00:30,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:30,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:31,153 - INFO - Response - Page 1:
2025-06-15 18:00:31,356 - INFO - 第 1 页获取到 100 条记录
2025-06-15 18:00:31,356 - INFO - Request Parameters - Page 2:
2025-06-15 18:00:31,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:31,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:31,856 - INFO - Response - Page 2:
2025-06-15 18:00:32,059 - INFO - 第 2 页获取到 100 条记录
2025-06-15 18:00:32,059 - INFO - Request Parameters - Page 3:
2025-06-15 18:00:32,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:32,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:32,590 - INFO - Response - Page 3:
2025-06-15 18:00:32,793 - INFO - 第 3 页获取到 100 条记录
2025-06-15 18:00:32,793 - INFO - Request Parameters - Page 4:
2025-06-15 18:00:32,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:32,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:33,309 - INFO - Response - Page 4:
2025-06-15 18:00:33,512 - INFO - 第 4 页获取到 100 条记录
2025-06-15 18:00:33,512 - INFO - Request Parameters - Page 5:
2025-06-15 18:00:33,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:33,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:33,981 - INFO - Response - Page 5:
2025-06-15 18:00:34,184 - INFO - 第 5 页获取到 100 条记录
2025-06-15 18:00:34,184 - INFO - Request Parameters - Page 6:
2025-06-15 18:00:34,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:34,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:34,747 - INFO - Response - Page 6:
2025-06-15 18:00:34,950 - INFO - 第 6 页获取到 100 条记录
2025-06-15 18:00:34,950 - INFO - Request Parameters - Page 7:
2025-06-15 18:00:34,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 18:00:34,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 18:00:35,262 - INFO - Response - Page 7:
2025-06-15 18:00:35,465 - INFO - 第 7 页获取到 22 条记录
2025-06-15 18:00:35,465 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 18:00:35,465 - INFO - 获取到 622 条表单数据
2025-06-15 18:00:35,481 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 18:00:35,481 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-15 18:00:35,965 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-15 18:00:35,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 421773.0, 'new_value': 455348.0}, {'field': 'total_amount', 'old_value': 421773.0, 'new_value': 455348.0}, {'field': 'order_count', 'old_value': 885, 'new_value': 963}]
2025-06-15 18:00:35,965 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-15 18:00:36,403 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-15 18:00:36,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91170.0, 'new_value': 112170.0}, {'field': 'total_amount', 'old_value': 116240.0, 'new_value': 137240.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-06-15 18:00:36,403 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBT
2025-06-15 18:00:36,809 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBT
2025-06-15 18:00:36,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15734.0, 'new_value': 16734.0}, {'field': 'total_amount', 'old_value': 15734.0, 'new_value': 16734.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 93}]
2025-06-15 18:00:36,809 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM831
2025-06-15 18:00:37,293 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM831
2025-06-15 18:00:37,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97431.48, 'new_value': 98051.48}, {'field': 'total_amount', 'old_value': 97431.48, 'new_value': 98051.48}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-06-15 18:00:37,293 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-15 18:00:37,793 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-15 18:00:37,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7402.0, 'new_value': 8436.0}, {'field': 'total_amount', 'old_value': 9601.0, 'new_value': 10635.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-15 18:00:37,793 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-15 18:00:38,231 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-15 18:00:38,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20585.0, 'new_value': 22024.0}, {'field': 'total_amount', 'old_value': 20585.0, 'new_value': 22024.0}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2168}]
2025-06-15 18:00:38,231 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-15 18:00:38,731 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-15 18:00:38,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 629000.0, 'new_value': 684000.0}, {'field': 'total_amount', 'old_value': 629000.0, 'new_value': 684000.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-15 18:00:38,731 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-15 18:00:39,262 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-15 18:00:39,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178723.0, 'new_value': 193312.0}, {'field': 'total_amount', 'old_value': 178723.0, 'new_value': 193312.0}, {'field': 'order_count', 'old_value': 5089, 'new_value': 5467}]
2025-06-15 18:00:39,262 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM03
2025-06-15 18:00:39,793 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM03
2025-06-15 18:00:39,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66608.0, 'new_value': 71604.0}, {'field': 'total_amount', 'old_value': 66608.0, 'new_value': 71604.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-15 18:00:39,793 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-15 18:00:40,309 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-15 18:00:40,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58992.0, 'new_value': 98072.0}, {'field': 'total_amount', 'old_value': 58992.0, 'new_value': 98072.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-06-15 18:00:40,309 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM23
2025-06-15 18:00:40,778 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM23
2025-06-15 18:00:40,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15630.0, 'new_value': 18510.0}, {'field': 'total_amount', 'old_value': 15630.0, 'new_value': 18510.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-15 18:00:40,778 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-15 18:00:41,309 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-15 18:00:41,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21415.0, 'new_value': 31175.0}, {'field': 'total_amount', 'old_value': 21415.0, 'new_value': 31175.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-15 18:00:41,309 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-15 18:00:41,825 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-15 18:00:41,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75794.0, 'new_value': 84484.0}, {'field': 'total_amount', 'old_value': 75794.0, 'new_value': 84484.0}, {'field': 'order_count', 'old_value': 1710, 'new_value': 1926}]
2025-06-15 18:00:41,825 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-15 18:00:42,262 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-15 18:00:42,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14760.0, 'new_value': 15897.0}, {'field': 'total_amount', 'old_value': 14760.0, 'new_value': 15897.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-15 18:00:42,262 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-15 18:00:42,746 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-15 18:00:42,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112000.0, 'new_value': 126000.0}, {'field': 'total_amount', 'old_value': 112000.0, 'new_value': 126000.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-15 18:00:42,746 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-15 18:00:43,200 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-15 18:00:43,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106032.0, 'new_value': 108912.0}, {'field': 'total_amount', 'old_value': 106032.0, 'new_value': 108912.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-15 18:00:43,200 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-15 18:00:43,637 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-15 18:00:43,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69602.0, 'new_value': 73705.0}, {'field': 'total_amount', 'old_value': 69602.0, 'new_value': 73705.0}, {'field': 'order_count', 'old_value': 2975, 'new_value': 3121}]
2025-06-15 18:00:43,637 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-15 18:00:44,012 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-15 18:00:44,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79537.88, 'new_value': 87192.88}, {'field': 'total_amount', 'old_value': 79537.88, 'new_value': 87192.88}, {'field': 'order_count', 'old_value': 506, 'new_value': 540}]
2025-06-15 18:00:44,012 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-15 18:00:44,403 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-15 18:00:44,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51207.0, 'new_value': 57267.0}, {'field': 'total_amount', 'old_value': 51207.0, 'new_value': 57267.0}, {'field': 'order_count', 'old_value': 395, 'new_value': 429}]
2025-06-15 18:00:44,403 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-15 18:00:44,887 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-15 18:00:44,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18552.0, 'new_value': 19771.0}, {'field': 'total_amount', 'old_value': 18552.0, 'new_value': 19771.0}, {'field': 'order_count', 'old_value': 1742, 'new_value': 1850}]
2025-06-15 18:00:44,887 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-15 18:00:45,387 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-15 18:00:45,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6751.0, 'new_value': 7354.0}, {'field': 'total_amount', 'old_value': 6751.0, 'new_value': 7354.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 193}]
2025-06-15 18:00:45,387 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-15 18:00:45,903 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-15 18:00:45,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 36380.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 36380.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-15 18:00:45,903 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-15 18:00:46,325 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-15 18:00:46,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150609.9, 'new_value': 169261.9}, {'field': 'total_amount', 'old_value': 150609.9, 'new_value': 169261.9}, {'field': 'order_count', 'old_value': 1145, 'new_value': 1177}]
2025-06-15 18:00:46,325 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-15 18:00:46,715 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-15 18:00:46,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2870.0, 'new_value': 3069.0}, {'field': 'total_amount', 'old_value': 2870.0, 'new_value': 3069.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-15 18:00:46,715 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-15 18:00:47,231 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-15 18:00:47,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18653.0, 'new_value': 19557.0}, {'field': 'total_amount', 'old_value': 18653.0, 'new_value': 19557.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-15 18:00:47,231 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-15 18:00:47,887 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-15 18:00:47,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3009.9, 'new_value': 6031.9}, {'field': 'total_amount', 'old_value': 3009.9, 'new_value': 6031.9}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-15 18:00:47,887 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-15 18:00:48,356 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-15 18:00:48,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2910160.0, 'new_value': 3206301.0}, {'field': 'total_amount', 'old_value': 2910160.0, 'new_value': 3206301.0}, {'field': 'order_count', 'old_value': 54243, 'new_value': 58595}]
2025-06-15 18:00:48,356 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-15 18:00:48,809 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-15 18:00:48,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36989.0, 'new_value': 39488.0}, {'field': 'total_amount', 'old_value': 36989.0, 'new_value': 39488.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-15 18:00:48,809 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-15 18:00:49,262 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-15 18:00:49,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3169860.0, 'new_value': 3511460.0}, {'field': 'total_amount', 'old_value': 3169860.0, 'new_value': 3511460.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-15 18:00:49,262 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-15 18:00:49,700 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-15 18:00:49,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86726.0, 'new_value': 108318.0}, {'field': 'total_amount', 'old_value': 86726.0, 'new_value': 108318.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 117}]
2025-06-15 18:00:49,700 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-15 18:00:50,168 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-15 18:00:50,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18480.48, 'new_value': 20649.89}, {'field': 'total_amount', 'old_value': 18480.48, 'new_value': 20649.89}, {'field': 'order_count', 'old_value': 730, 'new_value': 832}]
2025-06-15 18:00:50,168 - INFO - 日期 2025-06 处理完成 - 更新: 31 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:50,168 - INFO - 数据同步完成！更新: 31 条，插入: 0 条，错误: 0 条
2025-06-15 18:00:50,168 - INFO - =================同步完成====================
2025-06-15 21:00:02,876 - INFO - =================使用默认全量同步=============
2025-06-15 21:00:04,579 - INFO - MySQL查询成功，共获取 3931 条记录
2025-06-15 21:00:04,579 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-15 21:00:04,611 - INFO - 开始处理日期: 2025-01
2025-06-15 21:00:04,611 - INFO - Request Parameters - Page 1:
2025-06-15 21:00:04,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:04,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:06,079 - INFO - Response - Page 1:
2025-06-15 21:00:06,283 - INFO - 第 1 页获取到 100 条记录
2025-06-15 21:00:06,283 - INFO - Request Parameters - Page 2:
2025-06-15 21:00:06,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:06,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:06,830 - INFO - Response - Page 2:
2025-06-15 21:00:07,033 - INFO - 第 2 页获取到 100 条记录
2025-06-15 21:00:07,033 - INFO - Request Parameters - Page 3:
2025-06-15 21:00:07,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:07,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:07,705 - INFO - Response - Page 3:
2025-06-15 21:00:07,908 - INFO - 第 3 页获取到 100 条记录
2025-06-15 21:00:07,908 - INFO - Request Parameters - Page 4:
2025-06-15 21:00:07,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:07,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:08,455 - INFO - Response - Page 4:
2025-06-15 21:00:08,658 - INFO - 第 4 页获取到 100 条记录
2025-06-15 21:00:08,658 - INFO - Request Parameters - Page 5:
2025-06-15 21:00:08,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:08,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:09,205 - INFO - Response - Page 5:
2025-06-15 21:00:09,408 - INFO - 第 5 页获取到 100 条记录
2025-06-15 21:00:09,408 - INFO - Request Parameters - Page 6:
2025-06-15 21:00:09,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:09,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:09,955 - INFO - Response - Page 6:
2025-06-15 21:00:10,158 - INFO - 第 6 页获取到 100 条记录
2025-06-15 21:00:10,158 - INFO - Request Parameters - Page 7:
2025-06-15 21:00:10,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:10,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:10,674 - INFO - Response - Page 7:
2025-06-15 21:00:10,877 - INFO - 第 7 页获取到 82 条记录
2025-06-15 21:00:10,877 - INFO - 查询完成，共获取到 682 条记录
2025-06-15 21:00:10,877 - INFO - 获取到 682 条表单数据
2025-06-15 21:00:10,877 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-15 21:00:10,893 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:10,893 - INFO - 开始处理日期: 2025-02
2025-06-15 21:00:10,893 - INFO - Request Parameters - Page 1:
2025-06-15 21:00:10,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:10,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:11,377 - INFO - Response - Page 1:
2025-06-15 21:00:11,580 - INFO - 第 1 页获取到 100 条记录
2025-06-15 21:00:11,580 - INFO - Request Parameters - Page 2:
2025-06-15 21:00:11,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:11,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:12,033 - INFO - Response - Page 2:
2025-06-15 21:00:12,236 - INFO - 第 2 页获取到 100 条记录
2025-06-15 21:00:12,236 - INFO - Request Parameters - Page 3:
2025-06-15 21:00:12,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:12,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:12,768 - INFO - Response - Page 3:
2025-06-15 21:00:12,971 - INFO - 第 3 页获取到 100 条记录
2025-06-15 21:00:12,971 - INFO - Request Parameters - Page 4:
2025-06-15 21:00:12,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:12,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:13,471 - INFO - Response - Page 4:
2025-06-15 21:00:13,674 - INFO - 第 4 页获取到 100 条记录
2025-06-15 21:00:13,674 - INFO - Request Parameters - Page 5:
2025-06-15 21:00:13,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:13,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:14,143 - INFO - Response - Page 5:
2025-06-15 21:00:14,346 - INFO - 第 5 页获取到 100 条记录
2025-06-15 21:00:14,346 - INFO - Request Parameters - Page 6:
2025-06-15 21:00:14,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:14,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:14,862 - INFO - Response - Page 6:
2025-06-15 21:00:15,065 - INFO - 第 6 页获取到 100 条记录
2025-06-15 21:00:15,065 - INFO - Request Parameters - Page 7:
2025-06-15 21:00:15,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:15,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:15,799 - INFO - Response - Page 7:
2025-06-15 21:00:16,002 - INFO - 第 7 页获取到 70 条记录
2025-06-15 21:00:16,002 - INFO - 查询完成，共获取到 670 条记录
2025-06-15 21:00:16,002 - INFO - 获取到 670 条表单数据
2025-06-15 21:00:16,002 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-15 21:00:16,018 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:16,018 - INFO - 开始处理日期: 2025-03
2025-06-15 21:00:16,018 - INFO - Request Parameters - Page 1:
2025-06-15 21:00:16,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:16,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:16,549 - INFO - Response - Page 1:
2025-06-15 21:00:16,753 - INFO - 第 1 页获取到 100 条记录
2025-06-15 21:00:16,753 - INFO - Request Parameters - Page 2:
2025-06-15 21:00:16,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:16,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:17,268 - INFO - Response - Page 2:
2025-06-15 21:00:17,471 - INFO - 第 2 页获取到 100 条记录
2025-06-15 21:00:17,471 - INFO - Request Parameters - Page 3:
2025-06-15 21:00:17,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:17,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:18,050 - INFO - Response - Page 3:
2025-06-15 21:00:18,253 - INFO - 第 3 页获取到 100 条记录
2025-06-15 21:00:18,253 - INFO - Request Parameters - Page 4:
2025-06-15 21:00:18,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:18,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:18,800 - INFO - Response - Page 4:
2025-06-15 21:00:19,003 - INFO - 第 4 页获取到 100 条记录
2025-06-15 21:00:19,003 - INFO - Request Parameters - Page 5:
2025-06-15 21:00:19,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:19,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:19,487 - INFO - Response - Page 5:
2025-06-15 21:00:19,690 - INFO - 第 5 页获取到 100 条记录
2025-06-15 21:00:19,690 - INFO - Request Parameters - Page 6:
2025-06-15 21:00:19,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:19,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:20,112 - INFO - Response - Page 6:
2025-06-15 21:00:20,315 - INFO - 第 6 页获取到 100 条记录
2025-06-15 21:00:20,315 - INFO - Request Parameters - Page 7:
2025-06-15 21:00:20,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:20,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:20,784 - INFO - Response - Page 7:
2025-06-15 21:00:20,987 - INFO - 第 7 页获取到 61 条记录
2025-06-15 21:00:20,987 - INFO - 查询完成，共获取到 661 条记录
2025-06-15 21:00:20,987 - INFO - 获取到 661 条表单数据
2025-06-15 21:00:20,987 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-15 21:00:21,003 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:21,003 - INFO - 开始处理日期: 2025-04
2025-06-15 21:00:21,003 - INFO - Request Parameters - Page 1:
2025-06-15 21:00:21,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:21,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:21,503 - INFO - Response - Page 1:
2025-06-15 21:00:21,706 - INFO - 第 1 页获取到 100 条记录
2025-06-15 21:00:21,706 - INFO - Request Parameters - Page 2:
2025-06-15 21:00:21,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:21,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:22,300 - INFO - Response - Page 2:
2025-06-15 21:00:22,503 - INFO - 第 2 页获取到 100 条记录
2025-06-15 21:00:22,503 - INFO - Request Parameters - Page 3:
2025-06-15 21:00:22,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:22,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:23,003 - INFO - Response - Page 3:
2025-06-15 21:00:23,206 - INFO - 第 3 页获取到 100 条记录
2025-06-15 21:00:23,206 - INFO - Request Parameters - Page 4:
2025-06-15 21:00:23,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:23,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:23,722 - INFO - Response - Page 4:
2025-06-15 21:00:23,925 - INFO - 第 4 页获取到 100 条记录
2025-06-15 21:00:23,925 - INFO - Request Parameters - Page 5:
2025-06-15 21:00:23,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:23,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:24,425 - INFO - Response - Page 5:
2025-06-15 21:00:24,628 - INFO - 第 5 页获取到 100 条记录
2025-06-15 21:00:24,628 - INFO - Request Parameters - Page 6:
2025-06-15 21:00:24,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:24,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:25,332 - INFO - Response - Page 6:
2025-06-15 21:00:25,535 - INFO - 第 6 页获取到 100 条记录
2025-06-15 21:00:25,535 - INFO - Request Parameters - Page 7:
2025-06-15 21:00:25,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:25,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:26,035 - INFO - Response - Page 7:
2025-06-15 21:00:26,238 - INFO - 第 7 页获取到 56 条记录
2025-06-15 21:00:26,238 - INFO - 查询完成，共获取到 656 条记录
2025-06-15 21:00:26,238 - INFO - 获取到 656 条表单数据
2025-06-15 21:00:26,238 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-15 21:00:26,254 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:26,254 - INFO - 开始处理日期: 2025-05
2025-06-15 21:00:26,254 - INFO - Request Parameters - Page 1:
2025-06-15 21:00:26,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:26,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:26,785 - INFO - Response - Page 1:
2025-06-15 21:00:26,988 - INFO - 第 1 页获取到 100 条记录
2025-06-15 21:00:26,988 - INFO - Request Parameters - Page 2:
2025-06-15 21:00:26,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:26,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:27,535 - INFO - Response - Page 2:
2025-06-15 21:00:27,738 - INFO - 第 2 页获取到 100 条记录
2025-06-15 21:00:27,738 - INFO - Request Parameters - Page 3:
2025-06-15 21:00:27,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:27,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:28,473 - INFO - Response - Page 3:
2025-06-15 21:00:28,676 - INFO - 第 3 页获取到 100 条记录
2025-06-15 21:00:28,676 - INFO - Request Parameters - Page 4:
2025-06-15 21:00:28,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:28,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:29,191 - INFO - Response - Page 4:
2025-06-15 21:00:29,395 - INFO - 第 4 页获取到 100 条记录
2025-06-15 21:00:29,395 - INFO - Request Parameters - Page 5:
2025-06-15 21:00:29,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:29,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:29,863 - INFO - Response - Page 5:
2025-06-15 21:00:30,067 - INFO - 第 5 页获取到 100 条记录
2025-06-15 21:00:30,067 - INFO - Request Parameters - Page 6:
2025-06-15 21:00:30,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:30,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:30,598 - INFO - Response - Page 6:
2025-06-15 21:00:30,801 - INFO - 第 6 页获取到 100 条记录
2025-06-15 21:00:30,801 - INFO - Request Parameters - Page 7:
2025-06-15 21:00:30,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:30,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:31,160 - INFO - Response - Page 7:
2025-06-15 21:00:31,364 - INFO - 第 7 页获取到 40 条记录
2025-06-15 21:00:31,364 - INFO - 查询完成，共获取到 640 条记录
2025-06-15 21:00:31,364 - INFO - 获取到 640 条表单数据
2025-06-15 21:00:31,364 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-15 21:00:31,379 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:31,379 - INFO - 开始处理日期: 2025-06
2025-06-15 21:00:31,379 - INFO - Request Parameters - Page 1:
2025-06-15 21:00:31,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:31,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:31,848 - INFO - Response - Page 1:
2025-06-15 21:00:32,051 - INFO - 第 1 页获取到 100 条记录
2025-06-15 21:00:32,051 - INFO - Request Parameters - Page 2:
2025-06-15 21:00:32,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:32,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:32,582 - INFO - Response - Page 2:
2025-06-15 21:00:32,786 - INFO - 第 2 页获取到 100 条记录
2025-06-15 21:00:32,786 - INFO - Request Parameters - Page 3:
2025-06-15 21:00:32,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:32,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:33,270 - INFO - Response - Page 3:
2025-06-15 21:00:33,473 - INFO - 第 3 页获取到 100 条记录
2025-06-15 21:00:33,473 - INFO - Request Parameters - Page 4:
2025-06-15 21:00:33,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:33,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:33,942 - INFO - Response - Page 4:
2025-06-15 21:00:34,145 - INFO - 第 4 页获取到 100 条记录
2025-06-15 21:00:34,145 - INFO - Request Parameters - Page 5:
2025-06-15 21:00:34,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:34,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:34,692 - INFO - Response - Page 5:
2025-06-15 21:00:34,895 - INFO - 第 5 页获取到 100 条记录
2025-06-15 21:00:34,895 - INFO - Request Parameters - Page 6:
2025-06-15 21:00:34,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:34,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:35,583 - INFO - Response - Page 6:
2025-06-15 21:00:35,786 - INFO - 第 6 页获取到 100 条记录
2025-06-15 21:00:35,786 - INFO - Request Parameters - Page 7:
2025-06-15 21:00:35,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-15 21:00:35,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-15 21:00:36,114 - INFO - Response - Page 7:
2025-06-15 21:00:36,317 - INFO - 第 7 页获取到 22 条记录
2025-06-15 21:00:36,317 - INFO - 查询完成，共获取到 622 条记录
2025-06-15 21:00:36,317 - INFO - 获取到 622 条表单数据
2025-06-15 21:00:36,317 - INFO - 当前日期 2025-06 有 622 条MySQL数据需要处理
2025-06-15 21:00:36,333 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-15 21:00:36,770 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-15 21:00:36,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14497.38, 'new_value': 22094.82}, {'field': 'total_amount', 'old_value': 68091.61, 'new_value': 75689.05}, {'field': 'order_count', 'old_value': 3806, 'new_value': 4222}]
2025-06-15 21:00:36,770 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-15 21:00:37,192 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-15 21:00:37,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92187.0, 'new_value': 96237.0}, {'field': 'total_amount', 'old_value': 92187.0, 'new_value': 96237.0}, {'field': 'order_count', 'old_value': 412, 'new_value': 437}]
2025-06-15 21:00:37,192 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-15 21:00:37,692 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-15 21:00:37,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126735.34, 'new_value': 129424.23}, {'field': 'total_amount', 'old_value': 126735.34, 'new_value': 129424.23}, {'field': 'order_count', 'old_value': 240, 'new_value': 252}]
2025-06-15 21:00:37,692 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-15 21:00:38,161 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-15 21:00:38,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4205.0, 'new_value': 5123.5}, {'field': 'total_amount', 'old_value': 4205.0, 'new_value': 5123.5}, {'field': 'order_count', 'old_value': 26, 'new_value': 32}]
2025-06-15 21:00:38,161 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-15 21:00:38,536 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-15 21:00:38,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242000.0, 'new_value': 289000.0}, {'field': 'total_amount', 'old_value': 242000.0, 'new_value': 289000.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-15 21:00:38,536 - INFO - 日期 2025-06 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:38,536 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-06-15 21:00:38,552 - INFO - =================同步完成====================
