#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
com.gooagoo.exportbill 批量账单导入测试工具
基于生产环境配置的批量数据生成和导入工具

功能：
1. 批量生成测试账单数据
2. 按规则随机生成各项参数
3. 调用生产环境接口进行数据导入
4. 支持指定日期和记录数量

作者：AI Assistant
创建时间：2025-01-06
"""

import hashlib
import json
import logging
import time
import requests
import random
import mysql.connector
from datetime import datetime, timedelta
from typing import Dict, Any

# ========== 配置参数区域 ==========

# 指定测试日期（格式：YYYY-MM-DD）
TEST_DATE = "2025-01-18"

# 指定生成的记录数量
RECORD_COUNT = 10000

# ========== 数据库配置 ==========

DB_CONFIG = {
    'host': 'localhost',
    'port': 43306,
    'user': 'root',
    'password': 'Hxp@1987!@#',  # 请修改为实际密码
    'database': 'mydatabase',      # 请修改为实际数据库名
    'charset': 'utf8mb4'
}

# MySQL建表语句
CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS `bill_import_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `exact_bill_type` varchar(20) NOT NULL COMMENT '细分账单类型',
  `bill_serial_number` varchar(100) NOT NULL COMMENT '票据流水号',
  `terminal_number` varchar(50) NOT NULL COMMENT '设备编号',
  `sale_time` datetime NOT NULL COMMENT '销售时间',
  `third_party_order_no` varchar(100) NOT NULL COMMENT '第三方订单号',
  `receivable_amount` decimal(10,2) NOT NULL COMMENT '实收金额',
  `total_num` decimal(10,2) NOT NULL COMMENT '商品数量',
  `total_fee` decimal(10,2) NOT NULL COMMENT '应收金额',
  `paid_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `bill_type` varchar(10) NOT NULL COMMENT '账单类型',
  `api_response` text COMMENT 'API响应结果',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_serial` (`bill_serial_number`),
  UNIQUE KEY `uk_third_party_order` (`third_party_order_no`),
  KEY `idx_terminal_number` (`terminal_number`),
  KEY `idx_sale_time` (`sale_time`),
  KEY `idx_exact_bill_type` (`exact_bill_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单导入记录表';
"""

# ========== 生产环境配置 ==========
API_CONFIG = {
    'appId': '0df229fdf08a45b1a61893db35d94bd6',
    'appKey': '2c9a4b5d97d35a000197df5707610001',
    'apiSecret': '7ECF1B4964034AB87527BDB32C51F457',
    'method': 'gogo.open.auto.routing',
    'lowerMethod': 'com.gooagoo.exportbill',
    'url': 'http://api.gooagoo.com/oapi/rest'
}

# 设备编号固定列表
TERMINAL_NUMBERS = [
    'BBBB00000271','BBBB00000283','BBBB00000288','BBBB00000297','BBBB00000300','BBBB00000302','BBBB00000305','BBBB00000306','BBBB00000308','BBBB00000313','BBBB00000314','BBBB00000319','BBBB00000321','BBBB00000323','BBBB00000325','BBBB00000326','BBBB00000331','BBBB00000332','BBBB00000400','BBBB00000401','BBBB00001274','BBBB00001276','BBBB00568956','BBBB00568962','BBBB00568964','BBBB00568965','BBBB00568966','BBBB00568976','BBBB00568978'
]

# 细分账单类型列表
EXACT_BILL_TYPES = ["1", "10102", "10103"]

# 账单类型映射
BILL_TYPE_MAPPING = {
    "1": "普通结账单",
    "10102": "美团外卖单",
    "10103": "饿了么外卖单"
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'batch_bill_test_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def generate_sign(params: Dict[str, Any], api_secret: str) -> str:
    """
    生成签名，采用MD5算法
    
    Args:
        params: 参数字典
        api_secret: API密钥
        
    Returns:
        str: 签名字符串
    """
    # 删除空值的参数
    params = {k: v for k, v in params.items() if v is not None and v != ""}
    
    # 按ASCII码从小到大排序
    sorted_keys = sorted(params)
    
    # 拼接成stringA
    stringA = '&'.join([f"{k}={params[k]}" for k in sorted_keys])
    
    # 拼接API密钥
    stringSignTemp = f"{stringA}&key={api_secret}"
    
    # MD5运算并转换为大写
    sign = hashlib.md5(stringSignTemp.encode('utf-8')).hexdigest().upper()
    
    logging.debug(f"签名字符串: {stringSignTemp}")
    logging.debug(f"生成签名: {sign}")
    
    return sign

def init_database():
    """初始化数据库和表"""
    try:
        # 连接数据库（不指定具体数据库）
        conn = mysql.connector.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            charset=DB_CONFIG['charset']
        )
        cursor = conn.cursor()

        # 创建数据库（如果不存在）
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{DB_CONFIG['database']}` DEFAULT CHARACTER SET utf8mb4")
        cursor.execute(f"USE `{DB_CONFIG['database']}`")

        # 创建表
        cursor.execute(CREATE_TABLE_SQL)

        conn.commit()
        cursor.close()
        conn.close()

        logging.info("✅ 数据库和表初始化成功")
        return True

    except mysql.connector.Error as e:
        logging.error(f"❌ 数据库初始化失败: {e}")
        return False
    except Exception as e:
        logging.error(f"❌ 数据库初始化异常: {e}")
        return False

def batch_insert_bill_records(success_records: list) -> int:
    """
    批量插入成功的账单数据到数据库

    Args:
        success_records: 成功的记录列表，每个元素包含(bill_data, api_response)

    Returns:
        int: 成功插入的记录数
    """
    if not success_records:
        return 0

    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 准备批量插入SQL
        insert_sql = """
        INSERT INTO bill_import_records (
            exact_bill_type, bill_serial_number, terminal_number, sale_time,
            third_party_order_no, receivable_amount, total_num, total_fee,
            paid_amount, bill_type, api_response
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        # 准备批量数据
        batch_data = []
        for bill_data, api_response in success_records:
            # 转换销售时间格式
            sale_time = datetime.strptime(bill_data['saleTime'], '%Y-%m-%d %H:%M:%S')

            # 准备单条数据
            data = (
                bill_data['exactBillType'],
                bill_data['billSerialNumber'],
                bill_data['terminalNumber'],
                sale_time,
                bill_data['thirdPartyOrderNo'],
                bill_data['receivableAmount'],
                bill_data['totalNum'],
                bill_data['totalFee'],
                bill_data['paidAmount'],
                bill_data['billType'],
                json.dumps(api_response, ensure_ascii=False)
            )
            batch_data.append(data)

        # 执行批量插入
        cursor.executemany(insert_sql, batch_data)
        conn.commit()

        inserted_count = cursor.rowcount

        cursor.close()
        conn.close()

        logging.info(f"✅ 批量插入数据库成功，共插入 {inserted_count} 条记录")
        return inserted_count

    except mysql.connector.Error as e:
        logging.error(f"❌ 批量数据库插入失败: {e}")
        return 0
    except Exception as e:
        logging.error(f"❌ 批量数据库插入异常: {e}")
        return 0

def call_api(business_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    调用账单导入接口
    
    Args:
        business_data: 业务数据
        
    Returns:
        Dict: 接口响应结果
    """
    try:
        timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        
        # 公共参数
        public_params = {
            "appId": API_CONFIG['appId'],
            "appKey": API_CONFIG['appKey'],
            "method": API_CONFIG['method'],
            "lowerMethod": API_CONFIG['lowerMethod'],
            "timestamp": timestamp,
            "messageFormat": "Json",
            "v": "1.0",
            "signMethod": "MD5"
        }
        
        # 将业务数据封装到 "data" 参数中
        data_param = json.dumps(business_data, ensure_ascii=False)
        all_params = {**public_params, "data": data_param}
        
        # 生成签名
        all_params["sign"] = generate_sign(all_params, API_CONFIG['apiSecret'])
        
        # 发送请求
        response = requests.post(API_CONFIG['url'], data=all_params, timeout=30)
        result = response.json()
        
        return result
        
    except requests.exceptions.Timeout:
        return {"error": "请求超时"}
    except requests.exceptions.RequestException as e:
        return {"error": f"请求异常: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"JSON解析失败: {str(e)}"}
    except Exception as e:
        return {"error": f"API调用失败: {str(e)}"}

def generate_random_time(date_str: str) -> str:
    """
    生成指定日期的随机时间（10:10-21:55）
    
    Args:
        date_str: 日期字符串，格式：YYYY-MM-DD
        
    Returns:
        str: 随机时间字符串，格式：YYYY-MM-DD HH:MM:SS
    """
    # 解析日期
    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    
    # 设置时间范围：10:10 到 21:55
    start_hour, start_minute = 10, 10
    end_hour, end_minute = 21, 55
    
    # 计算总分钟数范围
    start_minutes = start_hour * 60 + start_minute
    end_minutes = end_hour * 60 + end_minute
    
    # 随机选择分钟数
    random_minutes = random.randint(start_minutes, end_minutes)
    
    # 转换为小时和分钟
    hour = random_minutes // 60
    minute = random_minutes % 60
    second = random.randint(0, 59)
    
    # 构建完整时间
    random_time = date_obj.replace(hour=hour, minute=minute, second=second)
    
    return random_time.strftime('%Y-%m-%d %H:%M:%S')

def generate_bill_data(date_str: str) -> Dict[str, Any]:
    """
    生成单条账单测试数据
    
    Args:
        date_str: 日期字符串
        
    Returns:
        Dict: 账单数据
    """
    # 1. 随机选择设备编号
    terminal_number = random.choice(TERMINAL_NUMBERS)
    
    # 2. 生成销售时间
    sale_time = generate_random_time(date_str)
    
    # 3. 生成票据流水号：设备编号前6位 + 销售时间 + 随机数
    time_part = datetime.strptime(sale_time, '%Y-%m-%d %H:%M:%S').strftime('%Y%m%d%H%M%S')
    random_num = random.randint(1, 10000)
    bill_serial = f"{terminal_number[:6]}{time_part}{random_num:04d}"
    
    # 4. 生成第三方订单号
    third_party_order = f"{terminal_number[-8:]}{int(time.time())}{random.randint(100, 999)}"
    
    # 5. 固定金额
    total_fee = 0.01
    receivable_amount = 0.00
    
    # 6. 随机商品数量
    total_num = float(random.randint(1, 10))
    
    # 7. 随机选择账单类型
    exact_bill_type = random.choice(EXACT_BILL_TYPES)
    
    # 8. 根据exactBillType确定billType
    if exact_bill_type == "1":
        bill_type = "1"  # 普通结账单
    else:
        bill_type = "1"  # 外卖单也是结账单
    
    return {
        "exactBillType": exact_bill_type,
        "billSerialNumber": bill_serial,
        "terminalNumber": terminal_number,
        "saleTime": sale_time,
        "thirdPartyOrderNo": third_party_order,
        "receivableAmount": receivable_amount,
        "totalNum": total_num,
        "totalFee": total_fee,
        "paidAmount": receivable_amount,
        "billType": bill_type
    }

def batch_import_bills(date_str: str, count: int):
    """
    批量导入账单数据

    Args:
        date_str: 测试日期
        count: 记录数量
    """
    logging.info("=" * 80)
    logging.info("开始批量账单导入测试")
    logging.info("=" * 80)
    logging.info(f"📅 测试日期: {date_str}")
    logging.info(f"📊 记录数量: {count}")
    logging.info(f"🌐 接口地址: {API_CONFIG['url']}")
    logging.info(f"🔧 设备列表: {len(TERMINAL_NUMBERS)} 个设备")
    logging.info(f"💾 数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    logging.info("=" * 80)

    # 初始化数据库
    if not init_database():
        logging.error("❌ 数据库初始化失败，程序退出")
        return

    success_count = 0
    fail_count = 0
    success_records = []  # 存储成功的记录，用于批量插入
    
    for i in range(1, count + 1):
        logging.info(f"\n🔄 处理第 {i}/{count} 条记录...")
        
        # 生成测试数据
        bill_data = generate_bill_data(date_str)
        
        # 显示生成的数据
        logging.info(f"   设备编号: {bill_data['terminalNumber']}")
        logging.info(f"   销售时间: {bill_data['saleTime']}")
        logging.info(f"   账单类型: {bill_data['exactBillType']} - {BILL_TYPE_MAPPING.get(bill_data['exactBillType'], '未知')}")
        logging.info(f"   流水号: {bill_data['billSerialNumber']}")
        logging.info(f"   第三方订单号: {bill_data['thirdPartyOrderNo']}")
        logging.info(f"   商品数量: {bill_data['totalNum']}")
        
        # 调用接口
        result = call_api(bill_data)
        
        # 处理结果
        if "error" in result:
            logging.error(f"   ❌ 第 {i} 条记录导入失败: {result['error']}")
            fail_count += 1
        elif result.get('rescode') == 'OPEN_SUCCESS':
            logging.info(f"   ✅ 第 {i} 条记录导入成功")
            success_count += 1

            # 收集成功的记录，用于后续批量插入数据库
            success_records.append((bill_data, result))
        else:
            logging.error(f"   ❌ 第 {i} 条记录导入失败: {result.get('resmsg', '未知错误')}")
            fail_count += 1
        
        # 避免请求过快，间隔0.5秒
        if i < count:
            time.sleep(0.5)

    # 批量插入成功的记录到数据库
    logging.info(f"\n💾 开始批量插入数据库，共 {len(success_records)} 条成功记录...")
    db_insert_count = batch_insert_bill_records(success_records)

    # 输出统计结果
    logging.info("\n" + "=" * 80)
    logging.info("批量导入完成 - 统计结果")
    logging.info("=" * 80)
    logging.info(f"📊 总记录数: {count}")
    logging.info(f"✅ 接口成功: {success_count}")
    logging.info(f"❌ 接口失败: {fail_count}")
    logging.info(f"� 数据库保存: {db_insert_count}")
    logging.info(f"�📈 接口成功率: {(success_count/count*100):.1f}%")
    logging.info(f"💽 数据库成功率: {(db_insert_count/success_count*100):.1f}%" if success_count > 0 else "💽 数据库成功率: 0.0%")
    logging.info("=" * 80)

def main():
    """主函数"""
    logging.info("🚀 批量账单导入测试工具启动")
    logging.info(f"📅 配置测试日期: {TEST_DATE}")
    logging.info(f"📊 配置记录数量: {RECORD_COUNT}")
    
    # 验证日期格式
    try:
        datetime.strptime(TEST_DATE, '%Y-%m-%d')
    except ValueError:
        logging.error("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        return
    
    # 验证记录数量
    if RECORD_COUNT <= 0:
        logging.error("❌ 记录数量必须大于0")
        return
    
    # 开始批量导入
    batch_import_bills(TEST_DATE, RECORD_COUNT)

if __name__ == "__main__":
    main()
