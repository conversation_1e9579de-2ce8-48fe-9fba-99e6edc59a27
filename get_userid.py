import requests
from get_token import token
# 替换为你的实际 access_token
access_token = token.get_token()

# 钉钉 API 地址
url = f"https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token={access_token}"

# 请求正文
data = {
    "mobile": "13570575357"
}

# 发送 POST 请求
response = requests.post(url, json=data)

# 检查响应状态码
if response.status_code == 200:
    # 解析 JSON 响应
    result = response.json()
    print("Response:", result)
else:
    print(f"Failed to get user info. Status code: {response.status_code}")
    print("Response:", response.text)




