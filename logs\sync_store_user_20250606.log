2025-06-06 12:00:02,719 - INFO - 数据库连接成功
2025-06-06 12:00:02,875 - INFO - 获取钉钉access_token成功
2025-06-06 12:00:02,875 - INFO - 宜搭客户端初始化成功
2025-06-06 12:00:02,875 - INFO - 正在获取数据库店铺信息...
2025-06-06 12:00:02,922 - INFO - 获取店铺信息成功，共 1258 条记录
2025-06-06 12:00:02,922 - INFO - 成功获取数据库店铺信息，共 1258 条记录
2025-06-06 12:00:02,922 - INFO - 正在获取宜搭店铺信息...
2025-06-06 12:00:05,483 - INFO - 店铺 100101308 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100101309 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100100886 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100098583 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100100670 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100100646 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100100279 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100099988 的userid值为空
2025-06-06 12:00:05,499 - INFO - 店铺 100098589 的userid值为空
2025-06-06 12:00:06,608 - INFO - 店铺 100100882 的userid值为空
2025-06-06 12:00:06,608 - INFO - 店铺 100100006 的userid值为空
2025-06-06 12:00:06,608 - INFO - 店铺 100100004 的userid值为空
2025-06-06 12:00:06,608 - INFO - 店铺 100098572 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100893 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099980 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100233 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100101181 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099978 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099985 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099870 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099983 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100894 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099928 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099984 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100964 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099930 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099824 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100875 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100447 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099936 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100099829 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100880 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100456 的userid值为空
2025-06-06 12:00:10,357 - INFO - 店铺 100100881 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101303 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101304 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101288 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101275 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101265 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099305 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100278 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099189 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100328 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099216 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100369 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099270 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100376 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100926 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099275 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099291 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101124 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100269 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100327 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100368 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100466 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101123 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099274 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099290 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099307 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099338 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100238 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099171 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100291 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099191 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100335 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099218 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100373 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099249 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100378 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099293 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099190 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099217 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099271 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100377 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101125 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099277 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099292 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099313 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099193 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099226 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100375 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100380 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100664 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099281 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099295 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099192 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100339 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100374 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099237 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099256 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100663 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100379 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101129 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099280 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099294 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100249 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099177 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100314 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099198 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100434 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099283 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099176 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099197 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100665 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100433 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099282 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100437 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099322 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099345 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099185 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100321 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100436 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099317 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100262 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099199 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100435 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099261 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100101118 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099298 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099796 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099148 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100265 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099187 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099259 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099297 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099323 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099346 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100100217 的userid值为空
2025-06-06 12:00:10,998 - INFO - 店铺 100099147 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100264 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099186 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099264 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100865 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101120 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100236 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099153 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099263 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101119 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099284 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099299 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099332 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100224 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099150 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099188 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099212 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099266 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100438 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099289 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101122 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099304 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099931 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100361 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099231 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099243 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099265 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101121 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099272 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100900 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099303 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101302 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101297 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101295 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101296 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101287 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101282 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101284 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101283 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101266 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101155 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101132 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101156 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101154 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100918 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101256 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101242 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100084 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101224 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101241 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100101243 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099101 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100082 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100060 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100098405 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100042 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100229 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100098358 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100199 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100453 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100833 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100848 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100098436 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100061 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100044 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100098391 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100293 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100024 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100099903 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100200 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100832 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100847 的userid值为空
2025-06-06 12:00:11,669 - INFO - 店铺 100100097 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098448 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098253 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100076 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098433 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100055 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100040 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100838 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098268 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098449 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100081 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100056 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100041 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100020 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100834 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100047 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098396 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098247 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100320 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100028 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100099906 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098362 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100207 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100185 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100099119 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100160 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100100661 的userid值为空
2025-06-06 12:00:11,685 - INFO - 店铺 100098397 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100029 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099913 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100210 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099816 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100187 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099793 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100162 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098258 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098423 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100045 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100300 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100026 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098377 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098360 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100201 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100182 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098246 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098424 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100046 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100027 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099905 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100183 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099118 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100215 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100194 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099806 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100169 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100144 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098369 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100216 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099847 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100195 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100172 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100146 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100030 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100260 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100212 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100189 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099794 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100142 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100859 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100261 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098367 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100214 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100192 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099797 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100166 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100143 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099811 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100178 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100150 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100861 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098348 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100180 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100152 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100860 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098317 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100196 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100175 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100148 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100904 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100198 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100177 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100870 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098598 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100128 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100101180 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100154 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098350 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098488 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100156 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098351 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098597 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098342 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100099109 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098297 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100133 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100106 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098284 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098343 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100134 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100108 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100083 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100850 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098295 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098477 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100129 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100849 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098355 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100130 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098296 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100101 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100840 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098486 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100852 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098273 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100112 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100089 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100100070 的userid值为空
2025-06-06 12:00:12,404 - INFO - 店铺 100098260 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100839 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098289 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100851 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100140 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098303 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100113 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100091 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098440 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098274 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100071 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098427 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100048 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098300 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100842 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100854 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100135 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100110 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100085 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098437 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100099117 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100841 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098485 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098287 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100853 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100136 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100111 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100088 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100067 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100121 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100095 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098445 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100074 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100052 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100035 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098384 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100844 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100856 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098461 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100096 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098447 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100075 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100053 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100371 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100037 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098385 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100843 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100855 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100141 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098275 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100114 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100092 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098249 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098428 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100049 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098398 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100829 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100846 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100858 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098474 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100117 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100094 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098276 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100073 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098237 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100050 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100098250 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100033 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100845 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100101291 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100101278 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100101285 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100101299 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100101301 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100099585 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100099650 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100099629 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100099636 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100810 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100099587 的userid值为空
2025-06-06 12:00:13,091 - INFO - 店铺 100100266 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100100251 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100099992 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100100016 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100099522 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100099588 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100099503 的userid值为空
2025-06-06 12:00:13,716 - INFO - 店铺 100099525 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100303 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099499 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099681 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099500 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099615 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099675 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099613 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100341 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100887 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099956 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099539 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100101292 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100098588 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100366 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100461 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100460 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100258 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099541 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100459 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099555 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100465 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100100462 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100098559 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100099831 的userid值为空
2025-06-06 12:00:13,731 - INFO - 店铺 100101200 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100481 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100342 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100883 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099835 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101246 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100480 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100365 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101190 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101236 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101272 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100478 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101306 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099507 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099910 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101254 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099506 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099553 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100922 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099861 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100885 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099104 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100098596 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100917 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100098594 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099995 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099899 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100255 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099987 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100098561 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100280 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100890 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100888 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100884 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100889 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099911 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100944 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101162 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100891 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099803 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100818 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100098579 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100347 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099853 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099863 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101153 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100359 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100101173 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100099804 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100667 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100344 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 100100241 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 ********* 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 ********* 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 ********* 的userid值为空
2025-06-06 12:00:14,294 - INFO - 店铺 ********* 的userid值为空
2025-06-06 12:00:14,294 - INFO - 获取宜搭店铺信息成功，共 1257 条记录
2025-06-06 12:00:14,294 - INFO - 成功获取宜搭店铺信息，共 1257 条记录
2025-06-06 12:00:14,294 - INFO - 正在获取用户信息并处理oa_account...
2025-06-06 12:00:14,309 - INFO - 获取用户信息成功，共 69 条记录
2025-06-06 12:00:14,591 - INFO - 需要查询的手机号数量: 41
2025-06-06 12:00:20,730 - INFO - 处理oa_account完成，缓存大小: 41
2025-06-06 12:00:20,730 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid']
2025-06-06 12:00:20,730 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'form_instance_id']
2025-06-06 12:00:20,730 - INFO - 开始对比数据库和宜搭数据...
2025-06-06 12:00:20,730 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid']
2025-06-06 12:00:20,730 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'form_instance_id']
2025-06-06 12:00:20,730 - INFO - 仅在数据库存在的记录数: 1
2025-06-06 12:00:20,730 - INFO - 需要插入的记录: ['*********']
2025-06-06 12:00:20,730 - INFO - 仅在宜搭存在的记录数: 0
2025-06-06 12:00:20,745 - INFO - 店铺 ********* 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,745 - INFO - 店铺 ********* userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,745 - INFO - 店铺 ********* - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,745 - INFO - 店铺 100100828 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,745 - INFO - 店铺 100100828 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,745 - INFO - 店铺 100100828 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,745 - INFO - 店铺 100100345 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,745 - INFO - 店铺 100100345 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,745 - INFO - 店铺 100100345 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,745 - INFO - 店铺 100098264 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,745 - INFO - 店铺 100098264 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,745 - INFO - 店铺 100098264 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,745 - INFO - 店铺 100098262 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098262 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098262 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098290 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100100002 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100100002 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100100002 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100100814 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100100814 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100100814 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098252 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098252 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098252 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098239 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098239 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098239 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098266 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098266 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098266 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100100272 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100100272 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100100272 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098238 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098238 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098238 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098401 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098265 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098265 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098265 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098251 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098251 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098251 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098272 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098272 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098272 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098259 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,761 - INFO - 店铺 100098259 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,761 - INFO - 店铺 100098259 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098271 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100098271 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098271 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100099116 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100099116 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100099116 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098345 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100098345 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098345 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098248 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100098248 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098248 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098261 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100098261 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098261 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100100139 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100100139 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100100139 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098341 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,777 - INFO - 店铺 100098341 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,777 - INFO - 店铺 100098341 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100101226 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100101226 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100101226 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098354 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098354 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098354 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098340 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098340 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098340 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100101206 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100101206 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100101206 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098270 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098270 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098270 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098298 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098298 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098298 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100099115 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100099115 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100099115 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098450 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098450 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098450 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098357 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098357 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098357 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100100941 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100100941 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100100941 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100100942 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100100942 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100100942 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098353 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100098353 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100098353 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,792 - INFO - 店铺 100100938 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,792 - INFO - 店铺 100100938 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,792 - INFO - 店铺 100100938 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098352 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100098352 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098352 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100101095 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100101095 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100101095 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098347 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100098347 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098347 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098349 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100098349 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098349 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100101077 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100101077 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100101077 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100099964 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100099964 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100099964 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100100163 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100100163 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100100163 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098366 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100098366 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098366 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100099948 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100099948 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100099948 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098236 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100098236 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,808 - INFO - 店铺 100098236 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,808 - INFO - 店铺 100101139 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,808 - INFO - 店铺 100101139 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100101139 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100099807 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100099807 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100099807 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098368 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100098368 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098368 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100101196 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100101196 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100101196 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098361 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100098361 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098361 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098378 存在字段差异: ['status_diff', 'fz_store_code_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100100313 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100100313 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100313 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100235 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100100235 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100235 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098245 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100098245 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098245 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100656 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100100656 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100656 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098365 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100098365 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098365 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100259 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100100259 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100259 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098235 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,823 - INFO - 店铺 100098235 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,823 - INFO - 店铺 100098235 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,823 - INFO - 店铺 100100326 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100100326 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100326 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100254 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100100254 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100254 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098230 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100098230 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098230 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100469 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100100469 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100469 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100286 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100100286 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100286 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098254 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100098254 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098254 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098241 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100098241 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098241 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100218 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100100218 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100218 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098240 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100098240 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098240 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098267 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100098267 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100098267 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100443 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,839 - INFO - 店铺 100100443 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,839 - INFO - 店铺 100100443 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098359 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100098359 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098359 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100100234 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100100234 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100100234 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098257 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100098257 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098257 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098244 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100098244 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098244 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100100287 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100100287 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100100287 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098242 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100098242 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098242 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098269 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100098269 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098269 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098256 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100098256 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100098256 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100101223 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100101223 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100101223 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100101252 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,855 - INFO - 店铺 100101252 userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,855 - INFO - 店铺 100101252 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,855 - INFO - 店铺 100101235 存在字段差异: ['userid_diff']
2025-06-06 12:00:20,870 - INFO - 店铺 100101235 userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:20,870 - INFO - 店铺 100101235 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:20,902 - INFO - 店铺 100099300 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:20,902 - INFO - 店铺 100100865 存在字段差异: ['status_diff', 'fz_store_code_diff']
2025-06-06 12:00:20,917 - INFO - 店铺 100100003 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:21,011 - INFO - 店铺 100100901 存在字段差异: ['status_diff']
2025-06-06 12:00:21,027 - INFO - 数据对比完成：
2025-06-06 12:00:21,027 - INFO - - 需要插入的记录数: 1
2025-06-06 12:00:21,027 - INFO - - 需要更新状态为禁用的记录数: 0
2025-06-06 12:00:21,027 - INFO - - 需要更新的记录数: 87
2025-06-06 12:00:21,027 - INFO - 生成差异报告...
2025-06-06 12:00:21,433 - INFO - 差异报告已保存到文件: store_info_diff_report_20250606.xlsx
2025-06-06 12:00:21,433 - INFO - 开始更新宜搭表单...
2025-06-06 12:00:21,433 - INFO - 开始更新宜搭表单数据...
2025-06-06 12:00:21,433 - INFO - 数据库记录数: 1258
2025-06-06 12:00:21,433 - INFO - 宜搭记录数: 1257
2025-06-06 12:00:21,433 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid']
2025-06-06 12:00:21,433 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'form_instance_id']
2025-06-06 12:00:21,433 - INFO - 仅在数据库存在的记录数: 1
2025-06-06 12:00:21,433 - INFO - 需要插入的记录: ['*********']
2025-06-06 12:00:21,433 - INFO - 仅在宜搭存在的记录数: 0
2025-06-06 12:00:21,448 - INFO - 店铺 ********* 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 ********* userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 ********* - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100828 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100100828 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100828 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100345 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100100345 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100345 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,448 - INFO - 店铺 100098264 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100098264 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 100098264 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,448 - INFO - 店铺 100098262 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100098262 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 100098262 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,448 - INFO - 店铺 100098290 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100100002 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100100002 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100002 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100814 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,448 - INFO - 店铺 100100814 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,448 - INFO - 店铺 100100814 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098252 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098252 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098252 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098239 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098239 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098239 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098266 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098266 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098266 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100100272 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100100272 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100100272 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098238 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098238 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098238 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098401 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098265 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098265 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098265 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098251 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098251 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098251 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098272 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098272 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098272 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098259 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098259 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098259 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098271 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100098271 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,464 - INFO - 店铺 100098271 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,464 - INFO - 店铺 100099116 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,464 - INFO - 店铺 100099116 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100099116 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098345 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098345 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098345 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098248 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098248 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098248 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098261 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098261 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098261 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100100139 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100100139 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100100139 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098341 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098341 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098341 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100101226 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100101226 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100101226 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098354 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098354 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098354 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098340 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098340 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098340 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100101206 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100101206 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100101206 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098270 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098270 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098270 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098298 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,480 - INFO - 店铺 100098298 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,480 - INFO - 店铺 100098298 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100099115 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100099115 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100099115 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098450 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100098450 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098450 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098357 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100098357 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098357 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100100941 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100100941 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100100941 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100100942 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100100942 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100100942 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098353 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100098353 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098353 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100100938 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100100938 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100100938 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098352 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100098352 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098352 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100101095 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100101095 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100101095 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098347 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,495 - INFO - 店铺 100098347 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,495 - INFO - 店铺 100098347 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098349 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100098349 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098349 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100101077 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100101077 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100101077 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100099964 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100099964 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100099964 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100100163 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100100163 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100100163 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098366 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100098366 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098366 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100099948 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100099948 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100099948 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098236 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100098236 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098236 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100101139 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100101139 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100101139 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100099807 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100099807 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100099807 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098368 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100098368 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100098368 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,511 - INFO - 店铺 100101196 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,511 - INFO - 店铺 100101196 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,511 - INFO - 店铺 100101196 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098361 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100098361 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098361 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098378 存在字段差异: ['status_diff', 'fz_store_code_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100313 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100313 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100313 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100235 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100235 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100235 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098245 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100098245 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098245 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100656 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100656 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100656 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098365 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100098365 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098365 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100259 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100259 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100259 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098235 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100098235 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098235 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100326 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100326 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100326 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100254 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100254 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100254 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098230 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100098230 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100098230 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100469 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,526 - INFO - 店铺 100100469 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,526 - INFO - 店铺 100100469 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100286 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100100286 userid差异 - 数据库: {'189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '189f858c407c7b3d480039a4090b4c13', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100286 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098254 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098254 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098254 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098241 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098241 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098241 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100218 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100100218 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100218 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098240 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098240 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098240 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098267 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098267 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098267 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100443 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100100443 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100443 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098359 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098359 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098359 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100234 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100100234 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100234 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098257 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098257 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098257 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098244 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100098244 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100098244 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100287 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,542 - INFO - 店铺 100100287 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,542 - INFO - 店铺 100100287 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,558 - INFO - 店铺 100098242 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,558 - INFO - 店铺 100098242 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,558 - INFO - 店铺 100098242 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,558 - INFO - 店铺 100098269 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,558 - INFO - 店铺 100098269 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,558 - INFO - 店铺 100098269 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,558 - INFO - 店铺 100098256 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,558 - INFO - 店铺 100098256 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,558 - INFO - 店铺 100098256 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,558 - INFO - 店铺 100101223 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,558 - INFO - 店铺 100101223 userid差异 - 数据库: {'16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '16340e95afb25e94fcd338840d78edb8', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,558 - INFO - 店铺 100101223 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,558 - INFO - 店铺 100101252 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,558 - INFO - 店铺 100101252 userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,558 - INFO - 店铺 100101252 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,558 - INFO - 店铺 100101235 存在字段差异: ['userid_diff']
2025-06-06 12:00:21,558 - INFO - 店铺 100101235 userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}, 宜搭: {'191b18809915f2af8c45f784f50a0c46', '18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d'}
2025-06-06 12:00:21,558 - INFO - 店铺 100101235 - 仅在宜搭存在的userid: {'191b18809915f2af8c45f784f50a0c46'}
2025-06-06 12:00:21,589 - INFO - 店铺 100099300 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:21,589 - INFO - 店铺 100100865 存在字段差异: ['status_diff', 'fz_store_code_diff']
2025-06-06 12:00:21,620 - INFO - 店铺 100100003 存在字段差异: ['fz_store_code_diff']
2025-06-06 12:00:21,698 - INFO - 店铺 100100901 存在字段差异: ['status_diff']
2025-06-06 12:00:21,714 - INFO - 数据对比完成：
2025-06-06 12:00:21,714 - INFO - - 需要插入的记录数: 1
2025-06-06 12:00:21,714 - INFO - - 需要更新状态为禁用的记录数: 0
2025-06-06 12:00:21,714 - INFO - - 需要更新的记录数: 87
2025-06-06 12:00:21,714 - INFO - 开始处理需要插入的记录，共 1 条
2025-06-06 12:00:21,714 - INFO - 正在处理第 1 条插入记录 - store_code: *********
2025-06-06 12:00:21,714 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "自然美", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0057"}
2025-06-06 12:00:21,714 - INFO - 处理剩余 1 条插入记录
2025-06-06 12:00:21,901 - INFO - 批量创建表单数据成功: 1 条记录
2025-06-06 12:00:21,901 - INFO - 批量插入成功，form_instance_ids: ['FINST-KLF66WC1GT1W8EAW9SM52AG14FFH3WH32AKBMJ8']
2025-06-06 12:00:21,901 - INFO - 开始处理需要更新的记录，共 87 条
2025-06-06 12:00:21,901 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-06-06 12:00:21,901 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "-多经 TOPTOY潮玩盲盒机", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0133"}
2025-06-06 12:00:21,901 - INFO - 正在处理第 2 条更新记录 - store_code: 100100828
2025-06-06 12:00:21,901 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100828", "textField_m8e8g3lu": "犁人坊", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0336"}
2025-06-06 12:00:21,901 - INFO - 正在处理第 3 条更新记录 - store_code: 100100345
2025-06-06 12:00:21,901 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100345", "textField_m8e8g3lu": "金利来", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110876", "textField_mb7rs39i": "金利来（中国）有限公司广州分公司", "textField_mbc1lbzm": "P0299L23CO0192"}
2025-06-06 12:00:21,901 - INFO - 正在处理第 4 条更新记录 - store_code: 100098264
2025-06-06 12:00:21,901 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098264", "textField_m8e8g3lu": "奈雪の茶", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104055", "textField_mb7rs39i": "广州市奈雪餐饮管理有限公司", "textField_mbc1lbzm": "P0299L25CO0032"}
2025-06-06 12:00:21,901 - INFO - 正在处理第 5 条更新记录 - store_code: 100098262
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098262", "textField_m8e8g3lu": "DQ", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103425", "textField_mb7rs39i": "上海适达餐饮管理有限公司", "textField_mbc1lbzm": "P0299L25CO0043"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 6 条更新记录 - store_code: 100098290
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098290", "textField_m8e8g3lu": "OPPO", "employeeField_m8e8g3lw": ["195cbfaa191990374b814d747ed92fe3", "178911c91380e3f9961bcc84847a615f", "18b0e1e4b78fd2c55dc90c74e3b9f3cc", "16340e95afb25e94fcd338840d78edb8", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111429", "textField_mb7rs39i": "广州市平实通讯器材有限公司", "textField_mbc1lbzm": "P0299L25CO0059"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 7 条更新记录 - store_code: 100100002
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100002", "textField_m8e8g3lu": "鸳鸯王世家", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109672", "textField_mb7rs39i": "杨智斌", "textField_mbc1lbzm": "P0299L23CO0013"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 8 条更新记录 - store_code: 100100814
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100814", "textField_m8e8g3lu": "拾稻湘现炒下饭菜", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111295", "textField_mb7rs39i": "张海兵", "textField_mbc1lbzm": "P0299L24CO0028"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 9 条更新记录 - store_code: 100098252
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098252", "textField_m8e8g3lu": "鄰里", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108395", "textField_mb7rs39i": "李奕军", "textField_mbc1lbzm": "P0299L24CO0230"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 10 条更新记录 - store_code: 100098239
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098239", "textField_m8e8g3lu": "马小火", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103482", "textField_mb7rs39i": "牛啊芳", "textField_mbc1lbzm": "P0299L20CO0066"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 11 条更新记录 - store_code: 100098266
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098266", "textField_m8e8g3lu": "骆驼", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103570", "textField_mb7rs39i": "广州哥伦布传媒有限公司", "textField_mbc1lbzm": "P0299L20CO0154"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 12 条更新记录 - store_code: 100100272
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100272", "textField_m8e8g3lu": "奈尔宝(餐饮)", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L23CO0071"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 13 条更新记录 - store_code: 100098238
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098238", "textField_m8e8g3lu": "肯德基", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103397", "textField_mb7rs39i": "百胜餐饮（广东）有限公司", "textField_mbc1lbzm": "P0299L20CO0037"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 14 条更新记录 - store_code: 100098401
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098401", "textField_m8e8g3lu": "欧派", "employeeField_m8e8g3lw": ["1953c39472ad2995a15b085419b99165", "18f13290b6949adbfcd0691437491a5a", "15e62a6607d73f5229798af46ed94466"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108436", "textField_mb7rs39i": "广州欧派创意家居设计有限公司", "textField_mbc1lbzm": "P0299L25CO0056"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 15 条更新记录 - store_code: 100098265
2025-06-06 12:00:21,917 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098265", "textField_m8e8g3lu": "争鲜", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103730", "textField_mb7rs39i": "争鲜（上海）食品有限公司广州第十分公司", "textField_mbc1lbzm": "P0299L20CO0217"}
2025-06-06 12:00:21,917 - INFO - 正在处理第 16 条更新记录 - store_code: 100098251
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098251", "textField_m8e8g3lu": "黑白电视", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103369", "textField_mb7rs39i": "王龙", "textField_mbc1lbzm": "P0299L23CO0054"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 17 条更新记录 - store_code: 100098272
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098272", "textField_m8e8g3lu": "遇见小面", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103637", "textField_mb7rs39i": "广州遇见小面餐饮股份有限公司", "textField_mbc1lbzm": "P0299L25CO0013"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 18 条更新记录 - store_code: 100098259
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098259", "textField_m8e8g3lu": "正壹淘乐屋", "employeeField_m8e8g3lw": ["16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109879", "textField_mb7rs39i": "广州市淘乐潮玩技术有限公司", "textField_mbc1lbzm": "P0299L23CO0011"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 19 条更新记录 - store_code: 100098271
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098271", "textField_m8e8g3lu": "名创优品x漫威", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103377", "textField_mb7rs39i": "肖嘉玲", "textField_mbc1lbzm": "P0299L24CO0257"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 20 条更新记录 - store_code: 100099116
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100099116", "textField_m8e8g3lu": "亚乔辛娜", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L23CO0202"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 21 条更新记录 - store_code: 100098345
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098345", "textField_m8e8g3lu": "谭木匠", "employeeField_m8e8g3lw": ["16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103548", "textField_mb7rs39i": "孙翔", "textField_mbc1lbzm": "P0299L24CO0252"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 22 条更新记录 - store_code: 100098248
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098248", "textField_m8e8g3lu": "樊文花", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110358", "textField_mb7rs39i": "广州思远文化用品有限公司", "textField_mbc1lbzm": "P0299L23CO0034"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 23 条更新记录 - store_code: 100098261
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098261", "textField_m8e8g3lu": "丘大叔柠檬茶", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103612", "textField_mb7rs39i": "广州山丘餐饮管理有限公司", "textField_mbc1lbzm": "P0299L23CO0042"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 24 条更新记录 - store_code: 100100139
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100139", "textField_m8e8g3lu": "茉莉奶白", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111037", "textField_mb7rs39i": "广州市荔湾区茉莉餐饮店", "textField_mbc1lbzm": "P0299L24CO0001"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 25 条更新记录 - store_code: 100098341
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098341", "textField_m8e8g3lu": "海澜之家", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111483", "textField_mb7rs39i": "江阴吉尔曼销售管理有限公司", "textField_mbc1lbzm": "P0299L25CO0008"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 26 条更新记录 - store_code: 100101226
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101226", "textField_m8e8g3lu": "美特斯邦威", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0344"}
2025-06-06 12:00:21,933 - INFO - 正在处理第 27 条更新记录 - store_code: 100098354
2025-06-06 12:00:21,933 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098354", "textField_m8e8g3lu": "EAMIEYZ", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L20CO0116"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 28 条更新记录 - store_code: 100098340
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098340", "textField_m8e8g3lu": "屈臣氏", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103410", "textField_mb7rs39i": "广州屈臣氏个人用品商店有限公司", "textField_mbc1lbzm": "P0299L20CO0098"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 29 条更新记录 - store_code: 100101206
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101206", "textField_m8e8g3lu": "欧昵雪", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "44228705", "textField_mb7rs39i": "李桂玲", "textField_mbc1lbzm": "P0299L24CO0328"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 30 条更新记录 - store_code: 100098270
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098270", "textField_m8e8g3lu": "面包新语", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103373", "textField_mb7rs39i": "罗圣怡", "textField_mbc1lbzm": "P0299L20CO0038"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 31 条更新记录 - store_code: 100098298
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098298", "textField_m8e8g3lu": "特斯拉", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108585", "textField_mb7rs39i": "特斯拉汽车销售服务（广州）有限公司", "textField_mbc1lbzm": "P0299L24CO0309"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 32 条更新记录 - store_code: 100099115
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100099115", "textField_m8e8g3lu": "斯凯奇", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0343"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 33 条更新记录 - store_code: 100098450
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098450", "textField_m8e8g3lu": "小鹿的屋", "employeeField_m8e8g3lw": ["16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108370", "textField_mb7rs39i": "深圳市力达动漫有限公司", "textField_mbc1lbzm": "P0299L23CO0092"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 34 条更新记录 - store_code: 100098357
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098357", "textField_m8e8g3lu": "花点时间", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103828", "textField_mb7rs39i": "黄永梅", "textField_mbc1lbzm": "P0299L25CO0038"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 35 条更新记录 - store_code: 100100941
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100941", "textField_m8e8g3lu": "西行家", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111373", "textField_mb7rs39i": "马文翰", "textField_mbc1lbzm": "P0299L25CO0039"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 36 条更新记录 - store_code: 100100942
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100942", "textField_m8e8g3lu": "李友友茶餐研食社", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111526", "textField_mb7rs39i": "广东任李行餐饮管理有限公司广州悦汇城分公司", "textField_mbc1lbzm": "P0299L24CO0325"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 37 条更新记录 - store_code: 100098353
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098353", "textField_m8e8g3lu": "宝岛眼镜", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110511", "textField_mb7rs39i": "广东星创视光光学科技有限公司", "textField_mbc1lbzm": "P0299L24CO0206"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 38 条更新记录 - store_code: 100100938
2025-06-06 12:00:21,948 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100938", "textField_m8e8g3lu": "富莉富蕾", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111198", "textField_mb7rs39i": "广州千与千寻电子商务贸易有限公司", "textField_mbc1lbzm": "P0299L24CO0220"}
2025-06-06 12:00:21,948 - INFO - 正在处理第 39 条更新记录 - store_code: 100098352
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098352", "textField_m8e8g3lu": "以纯", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103503", "textField_mb7rs39i": "何晓治", "textField_mbc1lbzm": "P0299L25CO0053"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 40 条更新记录 - store_code: 100101095
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101095", "textField_m8e8g3lu": "网鱼电竞", "employeeField_m8e8g3lw": ["16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111436", "textField_mb7rs39i": "曾进添", "textField_mbc1lbzm": "P0299L24CO0250"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 41 条更新记录 - store_code: 100098347
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098347", "textField_m8e8g3lu": "芭芭多", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110674", "textField_mb7rs39i": "梁艳清", "textField_mbc1lbzm": "P0299L24CO0277"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 42 条更新记录 - store_code: 100098349
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098349", "textField_m8e8g3lu": "上海", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103762", "textField_mb7rs39i": "广州市上广表业有限公司", "textField_mbc1lbzm": "P0299L24CO0347"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 43 条更新记录 - store_code: 100101077
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101077", "textField_m8e8g3lu": "好特卖HOT MAXX", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111387", "textField_mb7rs39i": "芯果科技（广州）有限公司", "textField_mbc1lbzm": "P0299L24CO0238"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 44 条更新记录 - store_code: 100099964
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100099964", "textField_m8e8g3lu": "cot x", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110130", "textField_mb7rs39i": "程颖", "textField_mbc1lbzm": "P0299L23CO0026"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 45 条更新记录 - store_code: 100100163
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100163", "textField_m8e8g3lu": "回力1927", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110425", "textField_mb7rs39i": "广州辰方贸易有限公司", "textField_mbc1lbzm": "P0299L23CO0045"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 46 条更新记录 - store_code: 100098366
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098366", "textField_m8e8g3lu": "悦舍", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103792", "textField_mb7rs39i": "广州悦舍商业发展有限公司", "textField_mbc1lbzm": "P0299L20CO0088"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 47 条更新记录 - store_code: 100099948
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100099948", "textField_m8e8g3lu": "陈家生煎", "employeeField_m8e8g3lw": ["16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L20CO0103"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 48 条更新记录 - store_code: 100098236
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098236", "textField_m8e8g3lu": "大家乐", "employeeField_m8e8g3lw": ["16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103450", "textField_mb7rs39i": "大家乐（广州）食品有限公司", "textField_mbc1lbzm": "P0299L20CO0067"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 49 条更新记录 - store_code: 100101139
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101139", "textField_m8e8g3lu": "東坡酥", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111470", "textField_mb7rs39i": "黄吉鹰", "textField_mbc1lbzm": "P0299L24CO0282"}
2025-06-06 12:00:21,964 - INFO - 正在处理第 50 条更新记录 - store_code: 100099807
2025-06-06 12:00:21,964 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100099807", "textField_m8e8g3lu": "悦汇城茶理宜世", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "44217839", "textField_mb7rs39i": "广州市荔广饮品有限公司", "textField_mbc1lbzm": "P0299L25CO0031"}
2025-06-06 12:00:21,964 - INFO - 达到批量处理大小，开始批量更新 50 条记录
2025-06-06 12:00:22,401 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:22,979 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:23,448 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:23,901 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:24,354 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:24,854 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:25,322 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:25,775 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:26,275 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:26,744 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:27,134 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:27,603 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:28,056 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:28,556 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:29,165 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:29,665 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:30,118 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:30,602 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:31,040 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:31,524 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:32,008 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:32,461 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:32,899 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:33,383 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:33,789 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:34,242 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:34,679 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:35,133 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:35,617 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:36,023 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:36,538 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:37,023 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:37,491 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:37,944 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:38,429 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:38,897 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:39,397 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:39,897 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:40,334 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:40,834 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:41,256 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:41,709 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:42,225 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:42,678 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:43,131 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:43,584 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:44,099 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:44,536 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:45,052 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:45,567 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:45,567 - INFO - 批量更新成功，form_instance_ids: ['FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMNA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMPA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMRA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBM1B', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMCB', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMIB', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMRB', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3QDHADCBM1', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBM5', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBM8', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBME', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBMI', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBMK', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBMO', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBMQ', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBMT', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBM11', 'FINST-00D66K71FTWV94MYAV5LZ5DHGPMU3RDHADCBM41', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBM48', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBMC8', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBME8', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBMF8', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBMM8', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBM69', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBM35', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBM85', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBME5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBMF5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBMH5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBMJ5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBMN5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBMT5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBMW5', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBM46', 'FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBM86', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBM75', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMJ5', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMM5', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMQ5', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMS5', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMU5', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMB6', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBMBF', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBMJF', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBMQF', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBMVF', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBMWF', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBM0G', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBM1G', 'FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBM4G']
2025-06-06 12:00:45,567 - INFO - 正在处理第 1 条更新记录 - store_code: 100098368
2025-06-06 12:00:45,567 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098368", "textField_m8e8g3lu": "麦当劳", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103474", "textField_mb7rs39i": "广东三元麦当劳食品有限公司", "textField_mbc1lbzm": "P0299L20CO0091"}
2025-06-06 12:00:45,567 - INFO - 正在处理第 2 条更新记录 - store_code: 100101196
2025-06-06 12:00:45,567 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101196", "textField_m8e8g3lu": "岐悦阁", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111515", "textField_mb7rs39i": "广州岐悦商贸发展有限公司", "textField_mbc1lbzm": "P0299L24CO0322"}
2025-06-06 12:00:45,567 - INFO - 正在处理第 3 条更新记录 - store_code: 100098361
2025-06-06 12:00:45,567 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098361", "textField_m8e8g3lu": "三枪", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108729", "textField_mb7rs39i": "张惠仪", "textField_mbc1lbzm": "P0299L24CO0335"}
2025-06-06 12:00:45,567 - INFO - 正在处理第 4 条更新记录 - store_code: 100098378
2025-06-06 12:00:45,567 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098378", "textField_m8e8g3lu": "源氏木语", "employeeField_m8e8g3lw": ["1953c39472ad2995a15b085419b99165", "15e62a6607d73f5229798af46ed94466", "18f13290b6949adbfcd0691437491a5a"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110056", "textField_mb7rs39i": "舒梦居（广州）家具有限公司", "textField_mbc1lbzm": "P0299L25CO0058"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 5 条更新记录 - store_code: 100100313
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100313", "textField_m8e8g3lu": "TOPTOY", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110829", "textField_mb7rs39i": "那是家大潮玩（广东）商业发展有限公司", "textField_mbc1lbzm": "P0299L23CO0180"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 6 条更新记录 - store_code: 100100235
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100235", "textField_m8e8g3lu": "露丝卡文世界冠军面包", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110658", "textField_mb7rs39i": "汪飞燕", "textField_mbc1lbzm": "P0299L23CO0081"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 7 条更新记录 - store_code: 100098245
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098245", "textField_m8e8g3lu": "良品铺子", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103412", "textField_mb7rs39i": "龚政", "textField_mbc1lbzm": "P0299L23CO0043"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 8 条更新记录 - store_code: 100100656
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100656", "textField_m8e8g3lu": "潮蕴半盏", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111438", "textField_mb7rs39i": "广州悦潮餐饮管理有限公司", "textField_mbc1lbzm": "P0299L24CO0248"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 9 条更新记录 - store_code: 100098365
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098365", "textField_m8e8g3lu": "畹町", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103591", "textField_mb7rs39i": "刘一霖", "textField_mbc1lbzm": "P0299L20CO0149"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 10 条更新记录 - store_code: 100100259
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100259", "textField_m8e8g3lu": "高恒现烤面包", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110742", "textField_mb7rs39i": "广东高品食品贸易有限公司", "textField_mbc1lbzm": "P0299L23CO0087"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 11 条更新记录 - store_code: 100098235
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098235", "textField_m8e8g3lu": "KKV", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103777", "textField_mb7rs39i": "东莞市快客商业管理有限公司广州第六分公司", "textField_mbc1lbzm": "P0299L20CO0006"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 12 条更新记录 - store_code: 100100326
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100326", "textField_m8e8g3lu": "班尼路", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0338"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 13 条更新记录 - store_code: 100100254
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100254", "textField_m8e8g3lu": "霸王茶姬", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110704", "textField_mb7rs39i": "邹琦润", "textField_mbc1lbzm": "P0299L23CO0084"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 14 条更新记录 - store_code: 100098230
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098230", "textField_m8e8g3lu": "七鲜超市", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103391", "textField_mb7rs39i": "广东期选信息技术有限公司", "textField_mbc1lbzm": "P0299L20CO0053"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 15 条更新记录 - store_code: 100100469
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100469", "textField_m8e8g3lu": "壹品冰果乐", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111234", "textField_mb7rs39i": "广州金诺餐饮有限公司", "textField_mbc1lbzm": "P0299L24CO0019"}
2025-06-06 12:00:45,583 - INFO - 正在处理第 16 条更新记录 - store_code: 100100286
2025-06-06 12:00:45,583 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100286", "textField_m8e8g3lu": "极星", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111219", "textField_mb7rs39i": "极星时代科技（南京）有限公司", "textField_mbc1lbzm": "P0299L24CO0219"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 17 条更新记录 - store_code: 100098254
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098254", "textField_m8e8g3lu": "启然", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108458", "textField_mb7rs39i": "广州佳然至美生物科技有限公司", "textField_mbc1lbzm": "P0299L24CO0251"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 18 条更新记录 - store_code: 100098241
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098241", "textField_m8e8g3lu": "酷乐潮玩", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103500", "textField_mb7rs39i": "冯希霞", "textField_mbc1lbzm": "P0299L24CO0330"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 19 条更新记录 - store_code: 100100218
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100218", "textField_m8e8g3lu": "个芙", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110528", "textField_mb7rs39i": "李小樑", "textField_mbc1lbzm": "P0299L23CO0058"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 20 条更新记录 - store_code: 100098240
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098240", "textField_m8e8g3lu": "大师兄", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103393", "textField_mb7rs39i": "广州大师兄餐饮管理连锁发展有限公司", "textField_mbc1lbzm": "P0299L20CO0180"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 21 条更新记录 - store_code: 100098267
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098267", "textField_m8e8g3lu": "乐凯撒比萨", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103574", "textField_mb7rs39i": "广州市乐凯撒比萨餐饮管理有限公司", "textField_mbc1lbzm": "P0299L20CO0166"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 22 条更新记录 - store_code: 100100443
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100443", "textField_m8e8g3lu": "黄饷咖哩蛋包饭", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110987", "textField_mb7rs39i": "孔伟亮", "textField_mbc1lbzm": "P0299L23CO0203"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 23 条更新记录 - store_code: 100098359
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098359", "textField_m8e8g3lu": "歌力诺", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108536", "textField_mb7rs39i": "刘子华", "textField_mbc1lbzm": "P0299L25CO0012"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 24 条更新记录 - store_code: 100100234
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100234", "textField_m8e8g3lu": "叹叹蔬果汁", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110626", "textField_mb7rs39i": "广州利搭利好和味食品有限公司", "textField_mbc1lbzm": "P0299L23CO0080"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 25 条更新记录 - store_code: 100098257
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098257", "textField_m8e8g3lu": "瑞幸咖啡", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104602", "textField_mb7rs39i": "瑞幸咖啡（广州）有限公司", "textField_mbc1lbzm": "P0299L24CO0334"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 26 条更新记录 - store_code: 100098244
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098244", "textField_m8e8g3lu": "Pelicana 百利家", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103616", "textField_mb7rs39i": "徐佰伟", "textField_mbc1lbzm": "P0299L23CO0039"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 27 条更新记录 - store_code: 100100287
2025-06-06 12:00:45,599 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100100287", "textField_m8e8g3lu": "乐赢LEVVV", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0009"}
2025-06-06 12:00:45,599 - INFO - 正在处理第 28 条更新记录 - store_code: 100098242
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098242", "textField_m8e8g3lu": "萨莉亚", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103597", "textField_mb7rs39i": "广州萨莉亚餐饮有限公司", "textField_mbc1lbzm": "P0299L20CO0152"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 29 条更新记录 - store_code: 100098269
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098269", "textField_m8e8g3lu": "喜茶", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103465", "textField_mb7rs39i": "广州灵感之茶餐饮管理有限公司", "textField_mbc1lbzm": "P0299L25CO0037"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 30 条更新记录 - store_code: 100098256
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100098256", "textField_m8e8g3lu": "谷田稻香", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108540", "textField_mb7rs39i": "深圳谷强餐饮有限公司", "textField_mbc1lbzm": "P0299L21CO0667"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 31 条更新记录 - store_code: 100101223
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101223", "textField_m8e8g3lu": "洗脸熊", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0345"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 32 条更新记录 - store_code: 100101252
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101252", "textField_m8e8g3lu": "蒙自源过桥米线", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0018"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 33 条更新记录 - store_code: 100101235
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101235", "textField_m8e8g3lu": "合德记", "employeeField_m8e8g3lw": ["1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0002"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 34 条更新记录 - store_code: 100099300
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099300", "textField_m8e8g3lu": "学乐英语", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21", "17b9f98a4d9b9ad2bb3a745401180c78"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103993", "textField_mb7rs39i": "佛山市利和文化传播有限公司", "textField_mbc1lbzm": "10295L25CO0019"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 35 条更新记录 - store_code: 100100865
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100865", "textField_m8e8g3lu": "洗车店", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0021"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 36 条更新记录 - store_code: 100100003
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100003", "textField_m8e8g3lu": "蒙自源过桥米线", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21", "17b9f98a4d9b9ad2bb3a745401180c78"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110447", "textField_mb7rs39i": "段添", "textField_mbc1lbzm": "10295L25CO0018"}
2025-06-06 12:00:45,614 - INFO - 正在处理第 37 条更新记录 - store_code: 100100901
2025-06-06 12:00:45,614 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100100901", "textField_m8e8g3lu": "透响麻辣香锅", "employeeField_m8e8g3lw": ["16d2416e996837cef3e7d79485192fc3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108437", "textField_mb7rs39i": "龚卫锋", "textField_mbc1lbzm": "10630L24CO0023"}
2025-06-06 12:00:45,614 - INFO - 处理剩余 37 条更新记录
2025-06-06 12:00:46,192 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:46,692 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:47,114 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:47,629 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:48,129 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:48,598 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:49,067 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:49,504 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:49,957 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:50,535 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:51,035 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:51,472 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:51,957 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:52,441 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:52,894 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:53,362 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:53,769 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:54,284 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:54,706 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:55,190 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:55,690 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:56,159 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:56,690 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:57,205 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:57,736 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:58,236 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:58,705 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:59,205 - INFO - 批量更新表单数据成功: 
2025-06-06 12:00:59,642 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:00,204 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:00,642 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:01,142 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:01,579 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:02,001 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:02,454 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:03,032 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:03,469 - INFO - 批量更新表单数据成功: 
2025-06-06 12:01:03,469 - INFO - 批量更新成功，form_instance_ids: ['FINST-VRA66VA15FVV1MKF72RX399V0YPD2D3IADCBMHG', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMS9', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMX9', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBM0A', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBM2A', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMDA', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMHA', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMNA', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMTA', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMVA', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMWA', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBMZA', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMI5', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMP5', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMR5', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMW5', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMZ5', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBM26', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBM96', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMD6', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMI6', 'FINST-7PF66H7197TV7DSYAZZ3A6C41CSI3QHIADCBMR6', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2TNIADCBM53', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2TNIADCBM73', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2TNIADCBMD3', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2TNIADCBMG3', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2TNIADCBMR3', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2UNIADCBMS3', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2UNIADCBMX3', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2UNIADCBM04', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2UNIADCBM44', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2UNIADCBMC4', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMYJ', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMN7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMR7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMM7', 'FINST-74766M71KLVVZ08V6MS6OAP8GM3F2UBLADCBMY5']
2025-06-06 12:01:03,469 - INFO - 宜搭表单更新完成
2025-06-06 12:01:03,469 - INFO - 数据处理完成
2025-06-06 12:01:03,469 - INFO - 数据库连接已关闭
