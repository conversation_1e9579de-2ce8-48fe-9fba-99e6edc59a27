2025-06-20 01:30:33,603 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 01:30:33,603 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 01:30:33,603 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 01:30:33,697 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 0 条记录
2025-06-20 01:30:33,697 - ERROR - 未获取到MySQL数据
2025-06-20 01:31:33,712 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 01:31:33,712 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 01:31:33,712 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 01:31:33,837 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 90 条记录
2025-06-20 01:31:33,837 - INFO - 获取到 1 个日期需要处理: ['2025-06-19']
2025-06-20 01:31:33,837 - INFO - 开始处理日期: 2025-06-19
2025-06-20 01:31:33,837 - INFO - Request Parameters - Page 1:
2025-06-20 01:31:33,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 01:31:33,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 01:31:41,962 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A5C6A256-FB27-7937-9D9A-2BAF0BDF1757 Response: {'code': 'ServiceUnavailable', 'requestid': 'A5C6A256-FB27-7937-9D9A-2BAF0BDF1757', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A5C6A256-FB27-7937-9D9A-2BAF0BDF1757)
2025-06-20 01:31:41,962 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-20 01:31:41,962 - INFO - 同步完成
2025-06-20 04:30:33,764 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 04:30:33,764 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 04:30:33,764 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 04:30:33,889 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 3 条记录
2025-06-20 04:30:33,889 - INFO - 获取到 1 个日期需要处理: ['2025-06-19']
2025-06-20 04:30:33,889 - INFO - 开始处理日期: 2025-06-19
2025-06-20 04:30:33,889 - INFO - Request Parameters - Page 1:
2025-06-20 04:30:33,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 04:30:33,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 04:30:42,014 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EBC1340F-92C0-7680-AE84-BAE652833AA4 Response: {'code': 'ServiceUnavailable', 'requestid': 'EBC1340F-92C0-7680-AE84-BAE652833AA4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EBC1340F-92C0-7680-AE84-BAE652833AA4)
2025-06-20 04:30:42,014 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-20 04:31:42,029 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 04:31:42,029 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 04:31:42,029 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 04:31:42,154 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 93 条记录
2025-06-20 04:31:42,154 - INFO - 获取到 1 个日期需要处理: ['2025-06-19']
2025-06-20 04:31:42,154 - INFO - 开始处理日期: 2025-06-19
2025-06-20 04:31:42,154 - INFO - Request Parameters - Page 1:
2025-06-20 04:31:42,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 04:31:42,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 04:31:42,920 - INFO - Response - Page 1:
2025-06-20 04:31:42,920 - INFO - 第 1 页获取到 49 条记录
2025-06-20 04:31:43,436 - INFO - 查询完成，共获取到 49 条记录
2025-06-20 04:31:43,436 - INFO - 获取到 49 条表单数据
2025-06-20 04:31:43,436 - INFO - 当前日期 2025-06-19 有 91 条MySQL数据需要处理
2025-06-20 04:31:43,436 - INFO - 开始批量插入 42 条新记录
2025-06-20 04:31:43,686 - INFO - 批量插入响应状态码: 200
2025-06-20 04:31:43,686 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 19 Jun 2025 20:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2028', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E597855C-5BDD-7354-9CEC-C0C464021FFA', 'x-acs-trace-id': '69157ae4d0f41127dfe2c941b7f2ed50', 'etag': '2ZH0phuzTIY0GLWP5sQ6AYg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 04:31:43,686 - INFO - 批量插入响应体: {'result': ['FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMUA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMVA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMWA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMXA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMYA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMZA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM0B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM1B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM2B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM3B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM4B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM5B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM6B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM7B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM8B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM9B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMAB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMBB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMCB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMDB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMEB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMFB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMGB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMHB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMIB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMJB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMKB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMLB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMMB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMNB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMOB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMPB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMQB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMRB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMSB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMTB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMUB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMVB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMWB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMXB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMYB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMZB']}
2025-06-20 04:31:43,686 - INFO - 批量插入表单数据成功，批次 1，共 42 条记录
2025-06-20 04:31:43,686 - INFO - 成功插入的数据ID: ['FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMUA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMVA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMWA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMXA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMYA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMZA', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM0B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM1B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM2B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM3B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM4B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM5B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM6B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM7B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM8B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CM9B', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMAB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMBB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMCB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMDB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMEB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMFB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMGB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMHB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMIB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMJB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMKB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMLB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMMB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMNB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMOB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMPB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMQB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMRB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMSB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMTB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMUB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMVB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMWB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMXB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMYB', 'FINST-6PF666917UFWAX3AAOU9N9224UMN27VT6U3CMZB']
2025-06-20 04:31:48,701 - INFO - 批量插入完成，共 42 条记录
2025-06-20 04:31:48,701 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 42 条，错误: 0 条
2025-06-20 04:31:48,701 - INFO - 数据同步完成！更新: 0 条，插入: 42 条，错误: 0 条
2025-06-20 04:31:48,701 - INFO - 同步完成
2025-06-20 07:30:33,597 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 07:30:33,597 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 07:30:33,597 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 07:30:33,737 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 3 条记录
2025-06-20 07:30:33,737 - INFO - 获取到 1 个日期需要处理: ['2025-06-19']
2025-06-20 07:30:33,737 - INFO - 开始处理日期: 2025-06-19
2025-06-20 07:30:33,737 - INFO - Request Parameters - Page 1:
2025-06-20 07:30:33,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 07:30:33,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 07:30:40,081 - INFO - Response - Page 1:
2025-06-20 07:30:40,081 - INFO - 第 1 页获取到 50 条记录
2025-06-20 07:30:40,597 - INFO - Request Parameters - Page 2:
2025-06-20 07:30:40,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 07:30:40,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 07:30:48,722 - ERROR - 处理日期 2025-06-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A6B23DF9-3C1B-730E-9F8C-E704CF6942D6 Response: {'code': 'ServiceUnavailable', 'requestid': 'A6B23DF9-3C1B-730E-9F8C-E704CF6942D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A6B23DF9-3C1B-730E-9F8C-E704CF6942D6)
2025-06-20 07:30:48,722 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-20 07:31:48,737 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 07:31:48,737 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 07:31:48,737 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 07:31:48,862 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 93 条记录
2025-06-20 07:31:48,862 - INFO - 获取到 1 个日期需要处理: ['2025-06-19']
2025-06-20 07:31:48,862 - INFO - 开始处理日期: 2025-06-19
2025-06-20 07:31:48,862 - INFO - Request Parameters - Page 1:
2025-06-20 07:31:48,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 07:31:48,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 07:31:49,596 - INFO - Response - Page 1:
2025-06-20 07:31:49,596 - INFO - 第 1 页获取到 50 条记录
2025-06-20 07:31:50,112 - INFO - Request Parameters - Page 2:
2025-06-20 07:31:50,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 07:31:50,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 07:31:50,737 - INFO - Response - Page 2:
2025-06-20 07:31:50,737 - INFO - 第 2 页获取到 41 条记录
2025-06-20 07:31:51,237 - INFO - 查询完成，共获取到 91 条记录
2025-06-20 07:31:51,237 - INFO - 获取到 91 条表单数据
2025-06-20 07:31:51,237 - INFO - 当前日期 2025-06-19 有 91 条MySQL数据需要处理
2025-06-20 07:31:51,237 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 07:31:51,237 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 07:31:51,237 - INFO - 同步完成
2025-06-20 10:30:33,587 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 10:30:33,587 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 10:30:33,587 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 10:30:33,727 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 139 条记录
2025-06-20 10:30:33,727 - INFO - 获取到 3 个日期需要处理: ['2025-06-18', '2025-06-19', '2025-06-20']
2025-06-20 10:30:33,727 - INFO - 开始处理日期: 2025-06-18
2025-06-20 10:30:33,727 - INFO - Request Parameters - Page 1:
2025-06-20 10:30:33,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:30:33,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:30:39,602 - INFO - Response - Page 1:
2025-06-20 10:30:39,602 - INFO - 第 1 页获取到 50 条记录
2025-06-20 10:30:40,102 - INFO - Request Parameters - Page 2:
2025-06-20 10:30:40,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:30:40,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:30:48,212 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 14C9EA67-B6CE-794C-9B36-CC9BC1BE6A06 Response: {'code': 'ServiceUnavailable', 'requestid': '14C9EA67-B6CE-794C-9B36-CC9BC1BE6A06', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 14C9EA67-B6CE-794C-9B36-CC9BC1BE6A06)
2025-06-20 10:30:48,212 - INFO - 开始处理日期: 2025-06-19
2025-06-20 10:30:48,212 - INFO - Request Parameters - Page 1:
2025-06-20 10:30:48,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:30:48,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:30:48,821 - INFO - Response - Page 1:
2025-06-20 10:30:48,821 - INFO - 第 1 页获取到 50 条记录
2025-06-20 10:30:49,337 - INFO - Request Parameters - Page 2:
2025-06-20 10:30:49,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:30:49,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:30:49,962 - INFO - Response - Page 2:
2025-06-20 10:30:49,962 - INFO - 第 2 页获取到 41 条记录
2025-06-20 10:30:50,462 - INFO - 查询完成，共获取到 91 条记录
2025-06-20 10:30:50,462 - INFO - 获取到 91 条表单数据
2025-06-20 10:30:50,462 - INFO - 当前日期 2025-06-19 有 132 条MySQL数据需要处理
2025-06-20 10:30:50,462 - INFO - 开始批量插入 129 条新记录
2025-06-20 10:30:50,727 - INFO - 批量插入响应状态码: 200
2025-06-20 10:30:50,727 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D2A5BE53-D8A2-7EE1-B01C-860F21F52D0F', 'x-acs-trace-id': '239c33bf199b17d1dab1a38605cfeeb1', 'etag': '2oM36LX5tHGqv6Ibsuf/9oQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:30:50,727 - INFO - 批量插入响应体: {'result': ['FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMFD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMGD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMHD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMID', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMJD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMKD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMLD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMMD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMND', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMOD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMPD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMQD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMRD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMSD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMTD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMUD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMVD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMWD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMXD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMYD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMZD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM0E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM1E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM2E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM3E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM4E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM5E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM6E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM7E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM8E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM9E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMAE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMBE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMCE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMDE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMEE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMFE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMGE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMHE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMIE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMJE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMKE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMLE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMME', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMNE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMOE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMPE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMQE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMRE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMSE']}
2025-06-20 10:30:50,727 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-20 10:30:50,727 - INFO - 成功插入的数据ID: ['FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMFD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMGD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMHD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMID', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMJD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMKD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMLD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMMD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMND', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMOD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMPD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMQD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMRD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMSD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMTD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMUD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMVD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMWD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMXD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMYD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMZD', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM0E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM1E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM2E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM3E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM4E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM5E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM6E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM7E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM8E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CM9E', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMAE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMBE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMCE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMDE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMEE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMFE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMGE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMHE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMIE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMJE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMKE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMLE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMME', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMNE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMOE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMPE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMQE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMRE', 'FINST-OLC66Z61D8EWZ0U6D1QRY9VGSMKG2HSN074CMSE']
2025-06-20 10:30:55,946 - INFO - 批量插入响应状态码: 200
2025-06-20 10:30:55,962 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A775C3A-C773-7A40-8BE6-95951C23328C', 'x-acs-trace-id': '9f3a736e7c502590f8360e556f1c57cb', 'etag': '2vptV5ZBXezMMAUPLFVX4cg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:30:55,962 - INFO - 批量插入响应体: {'result': ['FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM6F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM7F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM8F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM9F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMAF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMBF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMCF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMDF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMEF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMFF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMGF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMHF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMIF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMJF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMKF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMLF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMMF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMNF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMOF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMPF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMQF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMRF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMSF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMTF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMUF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMVF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMWF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMXF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMYF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMZF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM0G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM1G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM2G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM3G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM4G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM5G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM6G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM7G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM8G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM9G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMAG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMBG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMCG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMDG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMEG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMFG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMGG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMHG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMIG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMJG']}
2025-06-20 10:30:55,962 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-20 10:30:55,962 - INFO - 成功插入的数据ID: ['FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM6F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM7F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM8F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM9F', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMAF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMBF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMCF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMDF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMEF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMFF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMGF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMHF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMIF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMJF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMKF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMLF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMMF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMNF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMOF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMPF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMQF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMRF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMSF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMTF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMUF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMVF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMWF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMXF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMYF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMZF', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM0G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM1G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM2G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM3G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM4G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM5G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM6G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM7G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM8G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM9G', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMAG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMBG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMCG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMDG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMEG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMFG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMGG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMHG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMIG', 'FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMJG']
2025-06-20 10:31:01,212 - INFO - 批量插入响应状态码: 200
2025-06-20 10:31:01,212 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1404', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2BEB988E-97BA-7ED2-B5B3-587014A2586C', 'x-acs-trace-id': '7b509b41b8873d4128d17b1f6f3fff2b', 'etag': '1IHdITCAhRztcavi+m7dFiA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:31:01,212 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM03', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM13', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM23', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM33', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM43', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM53', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM63', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM73', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM83', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM93', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMA3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMB3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMC3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMD3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CME3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMF3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMG3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMH3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMI3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMJ3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMK3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CML3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMM3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMN3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMO3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMP3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMQ3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMR3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMS3']}
2025-06-20 10:31:01,212 - INFO - 批量插入表单数据成功，批次 3，共 29 条记录
2025-06-20 10:31:01,212 - INFO - 成功插入的数据ID: ['FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM03', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM13', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM23', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM33', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM43', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM53', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM63', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM73', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM83', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CM93', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMA3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMB3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMC3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMD3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CME3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMF3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMG3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMH3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMI3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMJ3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMK3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CML3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMM3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMN3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMO3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMP3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMQ3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMR3', 'FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMS3']
2025-06-20 10:31:06,227 - INFO - 批量插入完成，共 129 条记录
2025-06-20 10:31:06,227 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 129 条，错误: 0 条
2025-06-20 10:31:06,227 - INFO - 开始处理日期: 2025-06-20
2025-06-20 10:31:06,227 - INFO - Request Parameters - Page 1:
2025-06-20 10:31:06,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:31:06,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:31:06,618 - INFO - Response - Page 1:
2025-06-20 10:31:06,633 - INFO - 查询完成，共获取到 0 条记录
2025-06-20 10:31:06,633 - INFO - 获取到 0 条表单数据
2025-06-20 10:31:06,633 - INFO - 当前日期 2025-06-20 有 2 条MySQL数据需要处理
2025-06-20 10:31:06,633 - INFO - 开始批量插入 2 条新记录
2025-06-20 10:31:06,790 - INFO - 批量插入响应状态码: 200
2025-06-20 10:31:06,790 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CD1F9E82-BC80-7A8D-9CB8-B9584EC9315B', 'x-acs-trace-id': 'b175225c28af98f87dd12b1b39c2826d', 'etag': '1qE25j06ya8LYFVnPyIUFmQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:31:06,790 - INFO - 批量插入响应体: {'result': ['FINST-NS866I91HVFW03VS7T5DJ8KJG9KJ3G60174CM42', 'FINST-NS866I91HVFW03VS7T5DJ8KJG9KJ3G60174CM52']}
2025-06-20 10:31:06,790 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-20 10:31:06,790 - INFO - 成功插入的数据ID: ['FINST-NS866I91HVFW03VS7T5DJ8KJG9KJ3G60174CM42', 'FINST-NS866I91HVFW03VS7T5DJ8KJG9KJ3G60174CM52']
2025-06-20 10:31:11,805 - INFO - 批量插入完成，共 2 条记录
2025-06-20 10:31:11,805 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-20 10:31:11,805 - INFO - 数据同步完成！更新: 0 条，插入: 131 条，错误: 1 条
2025-06-20 10:32:11,820 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 10:32:11,820 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 10:32:11,820 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 10:32:11,961 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 535 条记录
2025-06-20 10:32:11,961 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-20 10:32:11,961 - INFO - 开始处理日期: 2025-06-19
2025-06-20 10:32:11,961 - INFO - Request Parameters - Page 1:
2025-06-20 10:32:11,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:32:11,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:32:12,727 - INFO - Response - Page 1:
2025-06-20 10:32:12,727 - INFO - 第 1 页获取到 50 条记录
2025-06-20 10:32:13,242 - INFO - Request Parameters - Page 2:
2025-06-20 10:32:13,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:32:13,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:32:13,852 - INFO - Response - Page 2:
2025-06-20 10:32:13,852 - INFO - 第 2 页获取到 50 条记录
2025-06-20 10:32:14,367 - INFO - Request Parameters - Page 3:
2025-06-20 10:32:14,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:32:14,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:32:15,070 - INFO - Response - Page 3:
2025-06-20 10:32:15,070 - INFO - 第 3 页获取到 50 条记录
2025-06-20 10:32:15,570 - INFO - Request Parameters - Page 4:
2025-06-20 10:32:15,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:32:15,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:32:16,211 - INFO - Response - Page 4:
2025-06-20 10:32:16,211 - INFO - 第 4 页获取到 50 条记录
2025-06-20 10:32:16,727 - INFO - Request Parameters - Page 5:
2025-06-20 10:32:16,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:32:16,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:32:17,336 - INFO - Response - Page 5:
2025-06-20 10:32:17,336 - INFO - 第 5 页获取到 20 条记录
2025-06-20 10:32:17,852 - INFO - 查询完成，共获取到 220 条记录
2025-06-20 10:32:17,852 - INFO - 获取到 220 条表单数据
2025-06-20 10:32:17,852 - INFO - 当前日期 2025-06-19 有 518 条MySQL数据需要处理
2025-06-20 10:32:17,852 - INFO - 开始批量插入 298 条新记录
2025-06-20 10:32:18,086 - INFO - 批量插入响应状态码: 200
2025-06-20 10:32:18,086 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:32:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2396', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A2F47D3E-DEB8-7EF9-8BED-C074AC1051EE', 'x-acs-trace-id': '4724203a1eba33d457de7d84e48178e2', 'etag': '22qAD0FolY670/NGRTKiarw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:32:18,086 - INFO - 批量插入响应体: {'result': ['FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMK', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CML', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMM', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMN', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMO', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMP', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMQ', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMR', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMS', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMT', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMU', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMV', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMW', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMX', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMY', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMZ', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM01', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM11', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM21', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM31', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM41', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM51', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM61', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM71', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM81', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM91', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMA1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMB1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMC1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMD1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CME1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMF1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMG1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMH1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMI1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMJ1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMK1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CML1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMM1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMN1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMO1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMP1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMQ1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMR1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMS1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMT1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMU1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMV1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMW1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMX1']}
2025-06-20 10:32:18,086 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-20 10:32:18,086 - INFO - 成功插入的数据ID: ['FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMK', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CML', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMM', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMN', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMO', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMP', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMQ', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMR', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMS', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMT', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMU', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMV', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMW', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMX', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMY', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMZ', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM01', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM11', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM21', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM31', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM41', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM51', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM61', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM71', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM81', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CM91', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMA1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMB1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMC1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMD1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CME1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMF1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMG1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMH1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMI1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMJ1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMK1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CML1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMM1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMN1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMO1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMP1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMQ1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMR1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMS1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMT1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMU1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMV1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMW1', 'FINST-5A966081JUFW0K5KA67415HLWY6Y3S6J274CMX1']
2025-06-20 10:32:23,352 - INFO - 批量插入响应状态码: 200
2025-06-20 10:32:23,352 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2393', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7796BA55-4CC1-70BD-8679-B5DCE1609A4C', 'x-acs-trace-id': 'c730a7062236b08d8b4b3b6734e1ce16', 'etag': '2WNACrX8oSHeeEbXRih4oLw3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:32:23,352 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMH', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMI', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMJ', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMK', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CML', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMM', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMN', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMO', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMP', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMQ', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMR', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMS', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMT', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMU', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMV', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMW', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMX', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMY', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMZ', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM01', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM11', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM21', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM31', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM41', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM51', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM61', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM71', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM81', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM91', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMA1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMB1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMC1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMD1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CME1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMF1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMG1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMH1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMI1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMJ1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMK1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CML1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMM1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMN1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMO1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMP1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMQ1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMR1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMS1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMT1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMU1']}
2025-06-20 10:32:23,352 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-20 10:32:23,352 - INFO - 成功插入的数据ID: ['FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMH', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMI', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMJ', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMK', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CML', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMM', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMN', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMO', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMP', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMQ', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMR', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMS', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMT', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMU', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMV', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMW', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMX', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMY', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ3Z8N274CMZ', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM01', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM11', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM21', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM31', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM41', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM51', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM61', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM71', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM81', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CM91', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMA1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMB1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMC1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMD1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CME1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMF1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMG1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMH1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMI1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMJ1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMK1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CML1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMM1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMN1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMO1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMP1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMQ1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMR1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMS1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMT1', 'FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMU1']
2025-06-20 10:32:28,633 - INFO - 批量插入响应状态码: 200
2025-06-20 10:32:28,633 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:32:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C8955514-E41E-7EF6-9427-0C6AE62CFE95', 'x-acs-trace-id': 'dfb93a8bcde4fc1886c366b1b78648b3', 'etag': '27eZ6lo3qkoXxXzo67sWjKQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:32:28,633 - INFO - 批量插入响应体: {'result': ['FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMXF', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMYF', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMZF', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM0G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM1G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM2G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM3G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM4G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM5G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM6G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM7G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM8G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM9G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMAG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMBG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMCG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMDG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMEG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMFG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMGG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMHG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMIG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMJG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMKG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMLG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMMG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMNG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMOG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMPG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMQG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMRG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMSG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMTG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMUG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMVG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMWG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMXG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMYG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMZG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM0H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM1H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM2H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM3H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM4H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM5H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM6H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM7H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM8H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM9H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMAH']}
2025-06-20 10:32:28,633 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-20 10:32:28,633 - INFO - 成功插入的数据ID: ['FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMXF', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMYF', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMZF', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM0G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM1G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM2G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM3G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM4G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM5G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM6G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM7G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM8G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM9G', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMAG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMBG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMCG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMDG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMEG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMFG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMGG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMHG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMIG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMJG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMKG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMLG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMMG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMNG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMOG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMPG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMQG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMRG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMSG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMTG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMUG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMVG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMWG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMXG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMYG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMZG', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM0H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM1H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM2H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM3H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM4H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM5H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM6H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM7H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM8H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CM9H', 'FINST-BD766BC1KUFW6CFCB5K334SM1T9524CR274CMAH']
2025-06-20 10:32:33,852 - INFO - 批量插入响应状态码: 200
2025-06-20 10:32:33,852 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B4E0387D-964E-7488-9F8C-E9F7F8654A5C', 'x-acs-trace-id': 'cf68b77d6789df1f29e833490eb141d6', 'etag': '2J22ywFaoYtvXWeZKPjtkXA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:32:33,852 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMQ8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMR8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMS8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMT8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMU8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMV8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMW8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMX8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMY8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMZ8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM09', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM19', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM29', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM39', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM49', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM59', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM69', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM79', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM89', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM99', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMA9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMB9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMC9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMD9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CME9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMF9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMG9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMH9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMI9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMJ9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMK9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CML9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMM9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMN9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMO9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMP9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMQ9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMR9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMS9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMT9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMU9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMV9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMW9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMX9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMY9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMZ9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM0A', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM1A', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM2A', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM3A']}
2025-06-20 10:32:33,852 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-20 10:32:33,852 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMQ8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMR8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMS8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMT8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMU8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMV8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMW8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMX8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMY8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMZ8', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM09', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM19', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM29', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM39', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM49', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM59', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM69', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM79', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM89', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM99', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMA9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMB9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMC9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMD9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CME9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMF9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMG9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMH9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMI9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMJ9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMK9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CML9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMM9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMN9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMO9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMP9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMQ9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMR9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMS9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMT9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMU9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMV9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMW9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMX9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMY9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CMZ9', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM0A', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM1A', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM2A', 'FINST-W4G66DA1XWEWQHCGDZW9E8PKVYN23WCV274CM3A']
2025-06-20 10:32:39,102 - INFO - 批量插入响应状态码: 200
2025-06-20 10:32:39,102 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:32:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6EE46A0A-3863-7797-9F0C-832009BF5CD5', 'x-acs-trace-id': 'f6f980ce3f885c9a6064b1ca2350feaf', 'etag': '25m9CaWjiBxwMM3VczTlgaQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:32:39,102 - INFO - 批量插入响应体: {'result': ['FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMW4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMX4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMY4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMZ4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM05', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM15', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM25', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM35', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM45', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM55', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM65', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM75', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM85', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM95', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMA5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMB5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMC5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMD5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CME5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMF5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMG5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMH5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMI5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMJ5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMK5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CML5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMM5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMN5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMO5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMP5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMQ5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMR5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMS5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMT5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMU5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMV5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMW5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMX5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMY5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMZ5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM06', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM16', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM26', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM36', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM46', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM56', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM66', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM76', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM86', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM96']}
2025-06-20 10:32:39,102 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-20 10:32:39,102 - INFO - 成功插入的数据ID: ['FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMW4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMX4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMY4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMZ4', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM05', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM15', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM25', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM35', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM45', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM55', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM65', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM75', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM85', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CM95', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMA5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMB5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMC5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMD5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CME5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMF5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMG5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMH5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMI5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMJ5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMK5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CML5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMM5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMN5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMO5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMP5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62TEZ274CMQ5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMR5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMS5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMT5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMU5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMV5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMW5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMX5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMY5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CMZ5', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM06', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM16', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM26', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM36', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM46', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM56', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM66', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM76', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM86', 'FINST-4OD66CC1L3EW864NA3C7FANTUEW62UEZ274CM96']
2025-06-20 10:32:44,351 - INFO - 批量插入响应状态码: 200
2025-06-20 10:32:44,351 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 02:32:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2316', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F84B31E-453B-76B7-BBE5-F176714EF48C', 'x-acs-trace-id': 'b5e46135bda23af0cb1d25e5a39a6d85', 'etag': '2Gd3Gj5TZk69ws24eAg2YZA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 10:32:44,351 - INFO - 批量插入响应体: {'result': ['FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMDC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMEC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMFC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMGC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMHC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMIC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMJC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMKC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMLC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMMC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMNC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMOC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMPC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMQC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMRC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMSC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMTC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMUC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMVC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMWC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMXC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMYC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMZC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM0D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM1D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM2D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM3D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM4D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM5D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM6D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM7D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM8D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM9D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMAD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMBD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMCD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMDD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMED', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMFD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMGD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMHD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMID', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMJD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMKD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMLD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMMD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMND', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMOD']}
2025-06-20 10:32:44,351 - INFO - 批量插入表单数据成功，批次 6，共 48 条记录
2025-06-20 10:32:44,351 - INFO - 成功插入的数据ID: ['FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMDC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMEC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMFC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMGC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMHC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMIC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMJC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMKC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMLC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMMC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMNC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMOC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMPC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMQC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMRC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMSC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMTC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMUC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMVC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMWC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMXC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMYC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMZC', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM0D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM1D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM2D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM3D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM4D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM5D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM6D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM7D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM8D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CM9D', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMAD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMBD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMCD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMDD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMED', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMFD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMGD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMHD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMID', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMJD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMKD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMLD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMMD', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMND', 'FINST-SED66Q61WUFWEJOFETMQED4ECWA72MG3374CMOD']
2025-06-20 10:32:49,367 - INFO - 批量插入完成，共 298 条记录
2025-06-20 10:32:49,367 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 298 条，错误: 0 条
2025-06-20 10:32:49,367 - INFO - 开始处理日期: 2025-06-20
2025-06-20 10:32:49,367 - INFO - Request Parameters - Page 1:
2025-06-20 10:32:49,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 10:32:49,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 10:32:49,789 - INFO - Response - Page 1:
2025-06-20 10:32:49,789 - INFO - 第 1 页获取到 2 条记录
2025-06-20 10:32:50,305 - INFO - 查询完成，共获取到 2 条记录
2025-06-20 10:32:50,305 - INFO - 获取到 2 条表单数据
2025-06-20 10:32:50,305 - INFO - 当前日期 2025-06-20 有 2 条MySQL数据需要处理
2025-06-20 10:32:50,305 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 10:32:50,305 - INFO - 数据同步完成！更新: 0 条，插入: 298 条，错误: 0 条
2025-06-20 10:32:50,305 - INFO - 同步完成
2025-06-20 13:30:33,576 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 13:30:33,576 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 13:30:33,576 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 13:30:33,716 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 153 条记录
2025-06-20 13:30:33,716 - INFO - 获取到 3 个日期需要处理: ['2025-06-18', '2025-06-19', '2025-06-20']
2025-06-20 13:30:33,716 - INFO - 开始处理日期: 2025-06-18
2025-06-20 13:30:33,732 - INFO - Request Parameters - Page 1:
2025-06-20 13:30:33,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:33,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:39,982 - INFO - Response - Page 1:
2025-06-20 13:30:39,982 - INFO - 第 1 页获取到 50 条记录
2025-06-20 13:30:40,497 - INFO - Request Parameters - Page 2:
2025-06-20 13:30:40,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:40,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:48,591 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 51FCA653-04D4-7B9E-94BD-DEC65F904335 Response: {'code': 'ServiceUnavailable', 'requestid': '51FCA653-04D4-7B9E-94BD-DEC65F904335', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 51FCA653-04D4-7B9E-94BD-DEC65F904335)
2025-06-20 13:30:48,591 - INFO - 开始处理日期: 2025-06-19
2025-06-20 13:30:48,591 - INFO - Request Parameters - Page 1:
2025-06-20 13:30:48,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:48,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:50,279 - INFO - Response - Page 1:
2025-06-20 13:30:50,279 - INFO - 第 1 页获取到 50 条记录
2025-06-20 13:30:50,794 - INFO - Request Parameters - Page 2:
2025-06-20 13:30:50,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:50,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:51,466 - INFO - Response - Page 2:
2025-06-20 13:30:51,466 - INFO - 第 2 页获取到 50 条记录
2025-06-20 13:30:51,966 - INFO - Request Parameters - Page 3:
2025-06-20 13:30:51,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:51,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:52,638 - INFO - Response - Page 3:
2025-06-20 13:30:52,638 - INFO - 第 3 页获取到 50 条记录
2025-06-20 13:30:53,154 - INFO - Request Parameters - Page 4:
2025-06-20 13:30:53,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:53,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:53,841 - INFO - Response - Page 4:
2025-06-20 13:30:53,841 - INFO - 第 4 页获取到 50 条记录
2025-06-20 13:30:54,341 - INFO - Request Parameters - Page 5:
2025-06-20 13:30:54,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:54,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:55,091 - INFO - Response - Page 5:
2025-06-20 13:30:55,091 - INFO - 第 5 页获取到 50 条记录
2025-06-20 13:30:55,607 - INFO - Request Parameters - Page 6:
2025-06-20 13:30:55,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:55,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:56,263 - INFO - Response - Page 6:
2025-06-20 13:30:56,263 - INFO - 第 6 页获取到 50 条记录
2025-06-20 13:30:56,779 - INFO - Request Parameters - Page 7:
2025-06-20 13:30:56,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:56,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:57,450 - INFO - Response - Page 7:
2025-06-20 13:30:57,450 - INFO - 第 7 页获取到 50 条记录
2025-06-20 13:30:57,950 - INFO - Request Parameters - Page 8:
2025-06-20 13:30:57,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:57,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:58,575 - INFO - Response - Page 8:
2025-06-20 13:30:58,575 - INFO - 第 8 页获取到 50 条记录
2025-06-20 13:30:59,091 - INFO - Request Parameters - Page 9:
2025-06-20 13:30:59,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:30:59,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:30:59,763 - INFO - Response - Page 9:
2025-06-20 13:30:59,779 - INFO - 第 9 页获取到 50 条记录
2025-06-20 13:31:00,294 - INFO - Request Parameters - Page 10:
2025-06-20 13:31:00,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:31:00,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:31:00,919 - INFO - Response - Page 10:
2025-06-20 13:31:00,919 - INFO - 第 10 页获取到 50 条记录
2025-06-20 13:31:01,435 - INFO - Request Parameters - Page 11:
2025-06-20 13:31:01,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:31:01,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:31:01,966 - INFO - Response - Page 11:
2025-06-20 13:31:01,966 - INFO - 第 11 页获取到 18 条记录
2025-06-20 13:31:02,482 - INFO - 查询完成，共获取到 518 条记录
2025-06-20 13:31:02,482 - INFO - 获取到 518 条表单数据
2025-06-20 13:31:02,482 - INFO - 当前日期 2025-06-19 有 145 条MySQL数据需要处理
2025-06-20 13:31:02,482 - INFO - 开始批量插入 13 条新记录
2025-06-20 13:31:02,653 - INFO - 批量插入响应状态码: 200
2025-06-20 13:31:02,653 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 05:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0BF49596-E83F-7DE1-ADDC-5E9B9E181E95', 'x-acs-trace-id': '426c66616e0521f5fe65af96a5b0f0c7', 'etag': '6+3UseWFA92lSXGTZZFmi6g6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 13:31:02,653 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMB5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMC5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMD5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CME5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMF5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMG5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMH5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMI5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMJ5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMK5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CML5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMM5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMN5']}
2025-06-20 13:31:02,653 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-06-20 13:31:02,653 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMB5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMC5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMD5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CME5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMF5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMG5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMH5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMI5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMJ5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMK5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CML5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMM5', 'FINST-7PF66BA1WQFWCS9EETNX5CMRWHAN2EDEGD4CMN5']
2025-06-20 13:31:07,669 - INFO - 批量插入完成，共 13 条记录
2025-06-20 13:31:07,669 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-06-20 13:31:07,669 - INFO - 开始处理日期: 2025-06-20
2025-06-20 13:31:07,669 - INFO - Request Parameters - Page 1:
2025-06-20 13:31:07,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:31:07,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:31:08,107 - INFO - Response - Page 1:
2025-06-20 13:31:08,107 - INFO - 第 1 页获取到 2 条记录
2025-06-20 13:31:08,607 - INFO - 查询完成，共获取到 2 条记录
2025-06-20 13:31:08,607 - INFO - 获取到 2 条表单数据
2025-06-20 13:31:08,607 - INFO - 当前日期 2025-06-20 有 3 条MySQL数据需要处理
2025-06-20 13:31:08,607 - INFO - 开始批量插入 1 条新记录
2025-06-20 13:31:08,778 - INFO - 批量插入响应状态码: 200
2025-06-20 13:31:08,778 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 05:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '685139B6-E1E3-7E5A-AC06-0247FBC865FB', 'x-acs-trace-id': '08474c34ceff3668cfbbee08ecafd847', 'etag': '6u7m7F5O3lh8EB1vhkeMbPg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 13:31:08,778 - INFO - 批量插入响应体: {'result': ['FINST-PPA666711XEW95O3DELTB59NTDDE3R3JGD4CMYH']}
2025-06-20 13:31:08,778 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-20 13:31:08,778 - INFO - 成功插入的数据ID: ['FINST-PPA666711XEW95O3DELTB59NTDDE3R3JGD4CMYH']
2025-06-20 13:31:13,794 - INFO - 批量插入完成，共 1 条记录
2025-06-20 13:31:13,794 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-20 13:31:13,794 - INFO - 数据同步完成！更新: 0 条，插入: 14 条，错误: 1 条
2025-06-20 13:32:13,809 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 13:32:13,809 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 13:32:13,809 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 13:32:13,950 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 566 条记录
2025-06-20 13:32:13,950 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-20 13:32:13,950 - INFO - 开始处理日期: 2025-06-19
2025-06-20 13:32:13,950 - INFO - Request Parameters - Page 1:
2025-06-20 13:32:13,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:13,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:14,700 - INFO - Response - Page 1:
2025-06-20 13:32:14,700 - INFO - 第 1 页获取到 50 条记录
2025-06-20 13:32:15,215 - INFO - Request Parameters - Page 2:
2025-06-20 13:32:15,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:15,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:15,919 - INFO - Response - Page 2:
2025-06-20 13:32:15,919 - INFO - 第 2 页获取到 50 条记录
2025-06-20 13:32:16,434 - INFO - Request Parameters - Page 3:
2025-06-20 13:32:16,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:16,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:17,075 - INFO - Response - Page 3:
2025-06-20 13:32:17,075 - INFO - 第 3 页获取到 50 条记录
2025-06-20 13:32:17,590 - INFO - Request Parameters - Page 4:
2025-06-20 13:32:17,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:17,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:18,372 - INFO - Response - Page 4:
2025-06-20 13:32:18,372 - INFO - 第 4 页获取到 50 条记录
2025-06-20 13:32:18,887 - INFO - Request Parameters - Page 5:
2025-06-20 13:32:18,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:18,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:19,497 - INFO - Response - Page 5:
2025-06-20 13:32:19,497 - INFO - 第 5 页获取到 50 条记录
2025-06-20 13:32:20,012 - INFO - Request Parameters - Page 6:
2025-06-20 13:32:20,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:20,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:20,606 - INFO - Response - Page 6:
2025-06-20 13:32:20,606 - INFO - 第 6 页获取到 50 条记录
2025-06-20 13:32:21,106 - INFO - Request Parameters - Page 7:
2025-06-20 13:32:21,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:21,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:21,762 - INFO - Response - Page 7:
2025-06-20 13:32:21,762 - INFO - 第 7 页获取到 50 条记录
2025-06-20 13:32:22,278 - INFO - Request Parameters - Page 8:
2025-06-20 13:32:22,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:22,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:22,934 - INFO - Response - Page 8:
2025-06-20 13:32:22,934 - INFO - 第 8 页获取到 50 条记录
2025-06-20 13:32:23,450 - INFO - Request Parameters - Page 9:
2025-06-20 13:32:23,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:23,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:24,059 - INFO - Response - Page 9:
2025-06-20 13:32:24,059 - INFO - 第 9 页获取到 50 条记录
2025-06-20 13:32:24,575 - INFO - Request Parameters - Page 10:
2025-06-20 13:32:24,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:24,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:25,231 - INFO - Response - Page 10:
2025-06-20 13:32:25,231 - INFO - 第 10 页获取到 50 条记录
2025-06-20 13:32:25,731 - INFO - Request Parameters - Page 11:
2025-06-20 13:32:25,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:25,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:26,372 - INFO - Response - Page 11:
2025-06-20 13:32:26,372 - INFO - 第 11 页获取到 31 条记录
2025-06-20 13:32:26,887 - INFO - 查询完成，共获取到 531 条记录
2025-06-20 13:32:26,887 - INFO - 获取到 531 条表单数据
2025-06-20 13:32:26,887 - INFO - 当前日期 2025-06-19 有 548 条MySQL数据需要处理
2025-06-20 13:32:26,903 - INFO - 开始批量插入 17 条新记录
2025-06-20 13:32:27,075 - INFO - 批量插入响应状态码: 200
2025-06-20 13:32:27,075 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 05:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '024DE450-EADF-77C0-92C9-FA1D2FA5D468', 'x-acs-trace-id': '92a747999c6fed8a7d93e14bd606cdf4', 'etag': '83NiNaaJvNZgTlvdKs62y+g8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 13:32:27,075 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CM82', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CM92', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMA2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMB2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMC2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMD2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CME2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMF2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMG2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMH2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMI2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMJ2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMK2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CML2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMM2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMN2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMO2']}
2025-06-20 13:32:27,075 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-06-20 13:32:27,075 - INFO - 成功插入的数据ID: ['FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CM82', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CM92', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMA2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMB2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMC2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMD2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CME2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMF2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMG2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMH2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMI2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMJ2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMK2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CML2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMM2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMN2', 'FINST-8PF66V71INGW4TLJENBFP7E32RXD2II7ID4CMO2']
2025-06-20 13:32:32,090 - INFO - 批量插入完成，共 17 条记录
2025-06-20 13:32:32,090 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 17 条，错误: 0 条
2025-06-20 13:32:32,090 - INFO - 开始处理日期: 2025-06-20
2025-06-20 13:32:32,090 - INFO - Request Parameters - Page 1:
2025-06-20 13:32:32,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 13:32:32,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 13:32:32,528 - INFO - Response - Page 1:
2025-06-20 13:32:32,528 - INFO - 第 1 页获取到 3 条记录
2025-06-20 13:32:33,043 - INFO - 查询完成，共获取到 3 条记录
2025-06-20 13:32:33,043 - INFO - 获取到 3 条表单数据
2025-06-20 13:32:33,043 - INFO - 当前日期 2025-06-20 有 3 条MySQL数据需要处理
2025-06-20 13:32:33,043 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 13:32:33,043 - INFO - 数据同步完成！更新: 0 条，插入: 17 条，错误: 0 条
2025-06-20 13:32:33,043 - INFO - 同步完成
2025-06-20 16:30:33,742 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 16:30:33,742 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 16:30:33,742 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 16:30:33,898 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 156 条记录
2025-06-20 16:30:33,898 - INFO - 获取到 5 个日期需要处理: ['2025-05-31', '2025-06-17', '2025-06-18', '2025-06-19', '2025-06-20']
2025-06-20 16:30:33,898 - INFO - 开始处理日期: 2025-05-31
2025-06-20 16:30:33,898 - INFO - Request Parameters - Page 1:
2025-06-20 16:30:33,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:33,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:42,023 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5498472F-7E36-7233-A44F-A42575E41485 Response: {'code': 'ServiceUnavailable', 'requestid': '5498472F-7E36-7233-A44F-A42575E41485', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5498472F-7E36-7233-A44F-A42575E41485)
2025-06-20 16:30:42,023 - INFO - 开始处理日期: 2025-06-17
2025-06-20 16:30:42,023 - INFO - Request Parameters - Page 1:
2025-06-20 16:30:42,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:42,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:48,836 - INFO - Response - Page 1:
2025-06-20 16:30:48,836 - INFO - 第 1 页获取到 50 条记录
2025-06-20 16:30:49,336 - INFO - Request Parameters - Page 2:
2025-06-20 16:30:49,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:49,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:49,992 - INFO - Response - Page 2:
2025-06-20 16:30:49,992 - INFO - 第 2 页获取到 50 条记录
2025-06-20 16:30:50,507 - INFO - Request Parameters - Page 3:
2025-06-20 16:30:50,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:50,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:51,211 - INFO - Response - Page 3:
2025-06-20 16:30:51,211 - INFO - 第 3 页获取到 50 条记录
2025-06-20 16:30:51,711 - INFO - Request Parameters - Page 4:
2025-06-20 16:30:51,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:51,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:52,398 - INFO - Response - Page 4:
2025-06-20 16:30:52,398 - INFO - 第 4 页获取到 50 条记录
2025-06-20 16:30:52,898 - INFO - Request Parameters - Page 5:
2025-06-20 16:30:52,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:52,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:53,601 - INFO - Response - Page 5:
2025-06-20 16:30:53,601 - INFO - 第 5 页获取到 50 条记录
2025-06-20 16:30:54,117 - INFO - Request Parameters - Page 6:
2025-06-20 16:30:54,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:54,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:54,757 - INFO - Response - Page 6:
2025-06-20 16:30:54,757 - INFO - 第 6 页获取到 50 条记录
2025-06-20 16:30:55,273 - INFO - Request Parameters - Page 7:
2025-06-20 16:30:55,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:55,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:55,976 - INFO - Response - Page 7:
2025-06-20 16:30:55,976 - INFO - 第 7 页获取到 50 条记录
2025-06-20 16:30:56,492 - INFO - Request Parameters - Page 8:
2025-06-20 16:30:56,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:56,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:57,132 - INFO - Response - Page 8:
2025-06-20 16:30:57,132 - INFO - 第 8 页获取到 50 条记录
2025-06-20 16:30:57,648 - INFO - Request Parameters - Page 9:
2025-06-20 16:30:57,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:57,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:58,320 - INFO - Response - Page 9:
2025-06-20 16:30:58,320 - INFO - 第 9 页获取到 50 条记录
2025-06-20 16:30:58,835 - INFO - Request Parameters - Page 10:
2025-06-20 16:30:58,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:30:58,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:30:59,492 - INFO - Response - Page 10:
2025-06-20 16:30:59,492 - INFO - 第 10 页获取到 50 条记录
2025-06-20 16:31:00,007 - INFO - Request Parameters - Page 11:
2025-06-20 16:31:00,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:00,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:00,742 - INFO - Response - Page 11:
2025-06-20 16:31:00,742 - INFO - 第 11 页获取到 37 条记录
2025-06-20 16:31:01,257 - INFO - 查询完成，共获取到 537 条记录
2025-06-20 16:31:01,257 - INFO - 获取到 537 条表单数据
2025-06-20 16:31:01,257 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-06-20 16:31:01,257 - INFO - 开始批量插入 1 条新记录
2025-06-20 16:31:01,414 - INFO - 批量插入响应状态码: 200
2025-06-20 16:31:01,414 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 08:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '06BFE9D6-9BF9-7A9D-9800-A97BECABF86E', 'x-acs-trace-id': '4627bfe2f58924e0d827496dd16e3594', 'etag': '5sGgqWJjzcfBZlRXuL8SHzQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 16:31:01,414 - INFO - 批量插入响应体: {'result': ['FINST-8LC66GC1XVFW6BPPAXD4F6HL7VE43DTUVJ4CMG']}
2025-06-20 16:31:01,414 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-20 16:31:01,414 - INFO - 成功插入的数据ID: ['FINST-8LC66GC1XVFW6BPPAXD4F6HL7VE43DTUVJ4CMG']
2025-06-20 16:31:06,429 - INFO - 批量插入完成，共 1 条记录
2025-06-20 16:31:06,429 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-20 16:31:06,429 - INFO - 开始处理日期: 2025-06-18
2025-06-20 16:31:06,429 - INFO - Request Parameters - Page 1:
2025-06-20 16:31:06,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:06,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:07,101 - INFO - Response - Page 1:
2025-06-20 16:31:07,101 - INFO - 第 1 页获取到 50 条记录
2025-06-20 16:31:07,601 - INFO - Request Parameters - Page 2:
2025-06-20 16:31:07,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:07,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:08,257 - INFO - Response - Page 2:
2025-06-20 16:31:08,257 - INFO - 第 2 页获取到 50 条记录
2025-06-20 16:31:08,773 - INFO - Request Parameters - Page 3:
2025-06-20 16:31:08,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:08,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:09,445 - INFO - Response - Page 3:
2025-06-20 16:31:09,445 - INFO - 第 3 页获取到 50 条记录
2025-06-20 16:31:09,960 - INFO - Request Parameters - Page 4:
2025-06-20 16:31:09,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:09,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:10,617 - INFO - Response - Page 4:
2025-06-20 16:31:10,617 - INFO - 第 4 页获取到 50 条记录
2025-06-20 16:31:11,132 - INFO - Request Parameters - Page 5:
2025-06-20 16:31:11,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:11,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:11,773 - INFO - Response - Page 5:
2025-06-20 16:31:11,773 - INFO - 第 5 页获取到 50 条记录
2025-06-20 16:31:12,289 - INFO - Request Parameters - Page 6:
2025-06-20 16:31:12,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:12,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:12,898 - INFO - Response - Page 6:
2025-06-20 16:31:12,898 - INFO - 第 6 页获取到 50 条记录
2025-06-20 16:31:13,398 - INFO - Request Parameters - Page 7:
2025-06-20 16:31:13,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:13,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:14,070 - INFO - Response - Page 7:
2025-06-20 16:31:14,070 - INFO - 第 7 页获取到 50 条记录
2025-06-20 16:31:14,570 - INFO - Request Parameters - Page 8:
2025-06-20 16:31:14,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:14,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:15,320 - INFO - Response - Page 8:
2025-06-20 16:31:15,320 - INFO - 第 8 页获取到 50 条记录
2025-06-20 16:31:15,835 - INFO - Request Parameters - Page 9:
2025-06-20 16:31:15,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:15,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:16,492 - INFO - Response - Page 9:
2025-06-20 16:31:16,492 - INFO - 第 9 页获取到 50 条记录
2025-06-20 16:31:17,007 - INFO - Request Parameters - Page 10:
2025-06-20 16:31:17,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:17,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:17,679 - INFO - Response - Page 10:
2025-06-20 16:31:17,679 - INFO - 第 10 页获取到 50 条记录
2025-06-20 16:31:18,179 - INFO - Request Parameters - Page 11:
2025-06-20 16:31:18,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:18,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:18,804 - INFO - Response - Page 11:
2025-06-20 16:31:18,804 - INFO - 第 11 页获取到 50 条记录
2025-06-20 16:31:19,320 - INFO - Request Parameters - Page 12:
2025-06-20 16:31:19,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:19,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:19,898 - INFO - Response - Page 12:
2025-06-20 16:31:19,898 - INFO - 第 12 页获取到 13 条记录
2025-06-20 16:31:20,398 - INFO - 查询完成，共获取到 563 条记录
2025-06-20 16:31:20,398 - INFO - 获取到 563 条表单数据
2025-06-20 16:31:20,398 - INFO - 当前日期 2025-06-18 有 1 条MySQL数据需要处理
2025-06-20 16:31:20,398 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71AJEW7791ADC30A9F3UZ72WQDMR2CMBS
2025-06-20 16:31:20,851 - INFO - 更新表单数据成功: FINST-RNA66D71AJEW7791ADC30A9F3UZ72WQDMR2CMBS
2025-06-20 16:31:20,851 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 1, 'new_value': 6}]
2025-06-20 16:31:20,851 - INFO - 日期 2025-06-18 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-20 16:31:20,851 - INFO - 开始处理日期: 2025-06-19
2025-06-20 16:31:20,851 - INFO - Request Parameters - Page 1:
2025-06-20 16:31:20,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:20,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:21,460 - INFO - Response - Page 1:
2025-06-20 16:31:21,460 - INFO - 第 1 页获取到 50 条记录
2025-06-20 16:31:21,976 - INFO - Request Parameters - Page 2:
2025-06-20 16:31:21,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:21,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:22,695 - INFO - Response - Page 2:
2025-06-20 16:31:22,695 - INFO - 第 2 页获取到 50 条记录
2025-06-20 16:31:23,210 - INFO - Request Parameters - Page 3:
2025-06-20 16:31:23,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:23,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:23,898 - INFO - Response - Page 3:
2025-06-20 16:31:23,898 - INFO - 第 3 页获取到 50 条记录
2025-06-20 16:31:24,398 - INFO - Request Parameters - Page 4:
2025-06-20 16:31:24,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:24,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:25,117 - INFO - Response - Page 4:
2025-06-20 16:31:25,117 - INFO - 第 4 页获取到 50 条记录
2025-06-20 16:31:25,632 - INFO - Request Parameters - Page 5:
2025-06-20 16:31:25,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:25,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:26,367 - INFO - Response - Page 5:
2025-06-20 16:31:26,367 - INFO - 第 5 页获取到 50 条记录
2025-06-20 16:31:26,867 - INFO - Request Parameters - Page 6:
2025-06-20 16:31:26,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:26,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:27,538 - INFO - Response - Page 6:
2025-06-20 16:31:27,538 - INFO - 第 6 页获取到 50 条记录
2025-06-20 16:31:28,038 - INFO - Request Parameters - Page 7:
2025-06-20 16:31:28,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:28,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:28,648 - INFO - Response - Page 7:
2025-06-20 16:31:28,648 - INFO - 第 7 页获取到 50 条记录
2025-06-20 16:31:29,148 - INFO - Request Parameters - Page 8:
2025-06-20 16:31:29,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:29,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:29,804 - INFO - Response - Page 8:
2025-06-20 16:31:29,804 - INFO - 第 8 页获取到 50 条记录
2025-06-20 16:31:30,304 - INFO - Request Parameters - Page 9:
2025-06-20 16:31:30,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:30,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:31,023 - INFO - Response - Page 9:
2025-06-20 16:31:31,023 - INFO - 第 9 页获取到 50 条记录
2025-06-20 16:31:31,538 - INFO - Request Parameters - Page 10:
2025-06-20 16:31:31,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:31,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:32,179 - INFO - Response - Page 10:
2025-06-20 16:31:32,179 - INFO - 第 10 页获取到 50 条记录
2025-06-20 16:31:32,695 - INFO - Request Parameters - Page 11:
2025-06-20 16:31:32,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:32,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:33,382 - INFO - Response - Page 11:
2025-06-20 16:31:33,382 - INFO - 第 11 页获取到 48 条记录
2025-06-20 16:31:33,898 - INFO - 查询完成，共获取到 548 条记录
2025-06-20 16:31:33,898 - INFO - 获取到 548 条表单数据
2025-06-20 16:31:33,898 - INFO - 当前日期 2025-06-19 有 146 条MySQL数据需要处理
2025-06-20 16:31:33,913 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMI3
2025-06-20 16:31:34,413 - INFO - 更新表单数据成功: FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMI3
2025-06-20 16:31:34,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 398.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 398.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/29ee9f5c61664c8f8c79d2dccc0eefc5.jpg?Expires=2060149888&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=37J6Wl5BQ2%2FVE5h6Th34lPDXBic%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/0d45141c2ef543bf9a889a0c738bf16c.jpg?Expires=2060151182&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=JSCIl9vc4S9pDB9ubCyT1j25ZMY%3D'}]
2025-06-20 16:31:34,413 - INFO - 开始批量插入 1 条新记录
2025-06-20 16:31:34,585 - INFO - 批量插入响应状态码: 200
2025-06-20 16:31:34,585 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 08:31:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8F83520D-C572-71B5-B147-7635DC08DDFA', 'x-acs-trace-id': '19c1888bd171859e4bcc9aebe89c564f', 'etag': '6ib0EQGs1hI3enBSya2pPAw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 16:31:34,585 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1PLGWS50QDUTA8AZA7ZTG2OEKWJ4CM82']}
2025-06-20 16:31:34,585 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-20 16:31:34,585 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1PLGWS50QDUTA8AZA7ZTG2OEKWJ4CM82']
2025-06-20 16:31:39,601 - INFO - 批量插入完成，共 1 条记录
2025-06-20 16:31:39,601 - INFO - 日期 2025-06-19 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-06-20 16:31:39,601 - INFO - 开始处理日期: 2025-06-20
2025-06-20 16:31:39,601 - INFO - Request Parameters - Page 1:
2025-06-20 16:31:39,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:31:39,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:31:40,007 - INFO - Response - Page 1:
2025-06-20 16:31:40,007 - INFO - 第 1 页获取到 3 条记录
2025-06-20 16:31:40,523 - INFO - 查询完成，共获取到 3 条记录
2025-06-20 16:31:40,523 - INFO - 获取到 3 条表单数据
2025-06-20 16:31:40,523 - INFO - 当前日期 2025-06-20 有 3 条MySQL数据需要处理
2025-06-20 16:31:40,523 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 16:31:40,523 - INFO - 数据同步完成！更新: 2 条，插入: 2 条，错误: 1 条
2025-06-20 16:32:40,538 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 16:32:40,538 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 16:32:40,538 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 16:32:40,679 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 567 条记录
2025-06-20 16:32:40,679 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-20 16:32:40,679 - INFO - 开始处理日期: 2025-06-19
2025-06-20 16:32:40,679 - INFO - Request Parameters - Page 1:
2025-06-20 16:32:40,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:40,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:41,491 - INFO - Response - Page 1:
2025-06-20 16:32:41,491 - INFO - 第 1 页获取到 50 条记录
2025-06-20 16:32:42,007 - INFO - Request Parameters - Page 2:
2025-06-20 16:32:42,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:42,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:42,725 - INFO - Response - Page 2:
2025-06-20 16:32:42,725 - INFO - 第 2 页获取到 50 条记录
2025-06-20 16:32:43,241 - INFO - Request Parameters - Page 3:
2025-06-20 16:32:43,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:43,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:43,929 - INFO - Response - Page 3:
2025-06-20 16:32:43,929 - INFO - 第 3 页获取到 50 条记录
2025-06-20 16:32:44,444 - INFO - Request Parameters - Page 4:
2025-06-20 16:32:44,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:44,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:45,132 - INFO - Response - Page 4:
2025-06-20 16:32:45,132 - INFO - 第 4 页获取到 50 条记录
2025-06-20 16:32:45,647 - INFO - Request Parameters - Page 5:
2025-06-20 16:32:45,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:45,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:46,288 - INFO - Response - Page 5:
2025-06-20 16:32:46,288 - INFO - 第 5 页获取到 50 条记录
2025-06-20 16:32:46,788 - INFO - Request Parameters - Page 6:
2025-06-20 16:32:46,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:46,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:47,460 - INFO - Response - Page 6:
2025-06-20 16:32:47,460 - INFO - 第 6 页获取到 50 条记录
2025-06-20 16:32:47,960 - INFO - Request Parameters - Page 7:
2025-06-20 16:32:47,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:47,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:48,694 - INFO - Response - Page 7:
2025-06-20 16:32:48,694 - INFO - 第 7 页获取到 50 条记录
2025-06-20 16:32:49,210 - INFO - Request Parameters - Page 8:
2025-06-20 16:32:49,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:49,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:49,928 - INFO - Response - Page 8:
2025-06-20 16:32:49,928 - INFO - 第 8 页获取到 50 条记录
2025-06-20 16:32:50,444 - INFO - Request Parameters - Page 9:
2025-06-20 16:32:50,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:50,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:51,132 - INFO - Response - Page 9:
2025-06-20 16:32:51,132 - INFO - 第 9 页获取到 50 条记录
2025-06-20 16:32:51,647 - INFO - Request Parameters - Page 10:
2025-06-20 16:32:51,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:51,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:52,288 - INFO - Response - Page 10:
2025-06-20 16:32:52,288 - INFO - 第 10 页获取到 50 条记录
2025-06-20 16:32:52,788 - INFO - Request Parameters - Page 11:
2025-06-20 16:32:52,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:52,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:53,460 - INFO - Response - Page 11:
2025-06-20 16:32:53,460 - INFO - 第 11 页获取到 49 条记录
2025-06-20 16:32:53,960 - INFO - 查询完成，共获取到 549 条记录
2025-06-20 16:32:53,960 - INFO - 获取到 549 条表单数据
2025-06-20 16:32:53,960 - INFO - 当前日期 2025-06-19 有 549 条MySQL数据需要处理
2025-06-20 16:32:53,975 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 16:32:53,975 - INFO - 开始处理日期: 2025-06-20
2025-06-20 16:32:53,975 - INFO - Request Parameters - Page 1:
2025-06-20 16:32:53,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 16:32:53,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 16:32:54,413 - INFO - Response - Page 1:
2025-06-20 16:32:54,413 - INFO - 第 1 页获取到 3 条记录
2025-06-20 16:32:54,928 - INFO - 查询完成，共获取到 3 条记录
2025-06-20 16:32:54,928 - INFO - 获取到 3 条表单数据
2025-06-20 16:32:54,928 - INFO - 当前日期 2025-06-20 有 3 条MySQL数据需要处理
2025-06-20 16:32:54,928 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 16:32:54,928 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 16:32:54,928 - INFO - 同步完成
2025-06-20 19:30:34,208 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 19:30:34,208 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 19:30:34,208 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 19:30:34,349 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 158 条记录
2025-06-20 19:30:34,349 - INFO - 获取到 5 个日期需要处理: ['2025-05-31', '2025-06-17', '2025-06-18', '2025-06-19', '2025-06-20']
2025-06-20 19:30:34,349 - INFO - 开始处理日期: 2025-05-31
2025-06-20 19:30:34,349 - INFO - Request Parameters - Page 1:
2025-06-20 19:30:34,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:34,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:42,459 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CBD0312E-6A8A-791F-89EB-31CEFE16E189 Response: {'code': 'ServiceUnavailable', 'requestid': 'CBD0312E-6A8A-791F-89EB-31CEFE16E189', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CBD0312E-6A8A-791F-89EB-31CEFE16E189)
2025-06-20 19:30:42,459 - INFO - 开始处理日期: 2025-06-17
2025-06-20 19:30:42,459 - INFO - Request Parameters - Page 1:
2025-06-20 19:30:42,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:42,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:43,194 - INFO - Response - Page 1:
2025-06-20 19:30:43,194 - INFO - 第 1 页获取到 50 条记录
2025-06-20 19:30:43,709 - INFO - Request Parameters - Page 2:
2025-06-20 19:30:43,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:43,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:44,365 - INFO - Response - Page 2:
2025-06-20 19:30:44,365 - INFO - 第 2 页获取到 50 条记录
2025-06-20 19:30:44,866 - INFO - Request Parameters - Page 3:
2025-06-20 19:30:44,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:44,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:45,538 - INFO - Response - Page 3:
2025-06-20 19:30:45,538 - INFO - 第 3 页获取到 50 条记录
2025-06-20 19:30:46,038 - INFO - Request Parameters - Page 4:
2025-06-20 19:30:46,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:46,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:46,725 - INFO - Response - Page 4:
2025-06-20 19:30:46,725 - INFO - 第 4 页获取到 50 条记录
2025-06-20 19:30:47,241 - INFO - Request Parameters - Page 5:
2025-06-20 19:30:47,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:47,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:47,897 - INFO - Response - Page 5:
2025-06-20 19:30:47,897 - INFO - 第 5 页获取到 50 条记录
2025-06-20 19:30:48,397 - INFO - Request Parameters - Page 6:
2025-06-20 19:30:48,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:48,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:48,991 - INFO - Response - Page 6:
2025-06-20 19:30:48,991 - INFO - 第 6 页获取到 50 条记录
2025-06-20 19:30:49,491 - INFO - Request Parameters - Page 7:
2025-06-20 19:30:49,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:49,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:50,116 - INFO - Response - Page 7:
2025-06-20 19:30:50,116 - INFO - 第 7 页获取到 50 条记录
2025-06-20 19:30:50,616 - INFO - Request Parameters - Page 8:
2025-06-20 19:30:50,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:50,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:51,257 - INFO - Response - Page 8:
2025-06-20 19:30:51,257 - INFO - 第 8 页获取到 50 条记录
2025-06-20 19:30:51,773 - INFO - Request Parameters - Page 9:
2025-06-20 19:30:51,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:51,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:52,429 - INFO - Response - Page 9:
2025-06-20 19:30:52,429 - INFO - 第 9 页获取到 50 条记录
2025-06-20 19:30:52,945 - INFO - Request Parameters - Page 10:
2025-06-20 19:30:52,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:52,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:53,585 - INFO - Response - Page 10:
2025-06-20 19:30:53,585 - INFO - 第 10 页获取到 50 条记录
2025-06-20 19:30:54,085 - INFO - Request Parameters - Page 11:
2025-06-20 19:30:54,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:54,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:54,679 - INFO - Response - Page 11:
2025-06-20 19:30:54,679 - INFO - 第 11 页获取到 38 条记录
2025-06-20 19:30:55,179 - INFO - 查询完成，共获取到 538 条记录
2025-06-20 19:30:55,179 - INFO - 获取到 538 条表单数据
2025-06-20 19:30:55,179 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-06-20 19:30:55,179 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 19:30:55,179 - INFO - 开始处理日期: 2025-06-18
2025-06-20 19:30:55,179 - INFO - Request Parameters - Page 1:
2025-06-20 19:30:55,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:55,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:55,836 - INFO - Response - Page 1:
2025-06-20 19:30:55,836 - INFO - 第 1 页获取到 50 条记录
2025-06-20 19:30:56,336 - INFO - Request Parameters - Page 2:
2025-06-20 19:30:56,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:56,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:57,023 - INFO - Response - Page 2:
2025-06-20 19:30:57,023 - INFO - 第 2 页获取到 50 条记录
2025-06-20 19:30:57,539 - INFO - Request Parameters - Page 3:
2025-06-20 19:30:57,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:57,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:58,211 - INFO - Response - Page 3:
2025-06-20 19:30:58,211 - INFO - 第 3 页获取到 50 条记录
2025-06-20 19:30:58,711 - INFO - Request Parameters - Page 4:
2025-06-20 19:30:58,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:58,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:30:59,383 - INFO - Response - Page 4:
2025-06-20 19:30:59,383 - INFO - 第 4 页获取到 50 条记录
2025-06-20 19:30:59,899 - INFO - Request Parameters - Page 5:
2025-06-20 19:30:59,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:30:59,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:00,586 - INFO - Response - Page 5:
2025-06-20 19:31:00,586 - INFO - 第 5 页获取到 50 条记录
2025-06-20 19:31:01,086 - INFO - Request Parameters - Page 6:
2025-06-20 19:31:01,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:01,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:01,805 - INFO - Response - Page 6:
2025-06-20 19:31:01,805 - INFO - 第 6 页获取到 50 条记录
2025-06-20 19:31:02,321 - INFO - Request Parameters - Page 7:
2025-06-20 19:31:02,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:02,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:03,024 - INFO - Response - Page 7:
2025-06-20 19:31:03,024 - INFO - 第 7 页获取到 50 条记录
2025-06-20 19:31:03,524 - INFO - Request Parameters - Page 8:
2025-06-20 19:31:03,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:03,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:04,211 - INFO - Response - Page 8:
2025-06-20 19:31:04,211 - INFO - 第 8 页获取到 50 条记录
2025-06-20 19:31:04,712 - INFO - Request Parameters - Page 9:
2025-06-20 19:31:04,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:04,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:05,352 - INFO - Response - Page 9:
2025-06-20 19:31:05,352 - INFO - 第 9 页获取到 50 条记录
2025-06-20 19:31:05,852 - INFO - Request Parameters - Page 10:
2025-06-20 19:31:05,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:05,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:06,509 - INFO - Response - Page 10:
2025-06-20 19:31:06,509 - INFO - 第 10 页获取到 50 条记录
2025-06-20 19:31:07,024 - INFO - Request Parameters - Page 11:
2025-06-20 19:31:07,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:07,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:07,618 - INFO - Response - Page 11:
2025-06-20 19:31:07,618 - INFO - 第 11 页获取到 50 条记录
2025-06-20 19:31:08,118 - INFO - Request Parameters - Page 12:
2025-06-20 19:31:08,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:08,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:08,665 - INFO - Response - Page 12:
2025-06-20 19:31:08,665 - INFO - 第 12 页获取到 13 条记录
2025-06-20 19:31:09,181 - INFO - 查询完成，共获取到 563 条记录
2025-06-20 19:31:09,181 - INFO - 获取到 563 条表单数据
2025-06-20 19:31:09,181 - INFO - 当前日期 2025-06-18 有 1 条MySQL数据需要处理
2025-06-20 19:31:09,181 - INFO - 日期 2025-06-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 19:31:09,181 - INFO - 开始处理日期: 2025-06-19
2025-06-20 19:31:09,181 - INFO - Request Parameters - Page 1:
2025-06-20 19:31:09,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:09,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:09,790 - INFO - Response - Page 1:
2025-06-20 19:31:09,806 - INFO - 第 1 页获取到 50 条记录
2025-06-20 19:31:10,322 - INFO - Request Parameters - Page 2:
2025-06-20 19:31:10,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:10,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:10,947 - INFO - Response - Page 2:
2025-06-20 19:31:10,947 - INFO - 第 2 页获取到 50 条记录
2025-06-20 19:31:11,447 - INFO - Request Parameters - Page 3:
2025-06-20 19:31:11,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:11,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:12,119 - INFO - Response - Page 3:
2025-06-20 19:31:12,119 - INFO - 第 3 页获取到 50 条记录
2025-06-20 19:31:12,619 - INFO - Request Parameters - Page 4:
2025-06-20 19:31:12,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:12,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:13,212 - INFO - Response - Page 4:
2025-06-20 19:31:13,212 - INFO - 第 4 页获取到 50 条记录
2025-06-20 19:31:13,728 - INFO - Request Parameters - Page 5:
2025-06-20 19:31:13,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:13,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:14,384 - INFO - Response - Page 5:
2025-06-20 19:31:14,384 - INFO - 第 5 页获取到 50 条记录
2025-06-20 19:31:14,885 - INFO - Request Parameters - Page 6:
2025-06-20 19:31:14,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:14,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:15,572 - INFO - Response - Page 6:
2025-06-20 19:31:15,572 - INFO - 第 6 页获取到 50 条记录
2025-06-20 19:31:16,088 - INFO - Request Parameters - Page 7:
2025-06-20 19:31:16,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:16,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:16,728 - INFO - Response - Page 7:
2025-06-20 19:31:16,728 - INFO - 第 7 页获取到 50 条记录
2025-06-20 19:31:17,244 - INFO - Request Parameters - Page 8:
2025-06-20 19:31:17,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:17,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:17,916 - INFO - Response - Page 8:
2025-06-20 19:31:17,916 - INFO - 第 8 页获取到 50 条记录
2025-06-20 19:31:18,416 - INFO - Request Parameters - Page 9:
2025-06-20 19:31:18,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:18,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:19,057 - INFO - Response - Page 9:
2025-06-20 19:31:19,057 - INFO - 第 9 页获取到 50 条记录
2025-06-20 19:31:19,573 - INFO - Request Parameters - Page 10:
2025-06-20 19:31:19,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:19,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:20,213 - INFO - Response - Page 10:
2025-06-20 19:31:20,213 - INFO - 第 10 页获取到 50 条记录
2025-06-20 19:31:20,729 - INFO - Request Parameters - Page 11:
2025-06-20 19:31:20,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:20,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:21,432 - INFO - Response - Page 11:
2025-06-20 19:31:21,432 - INFO - 第 11 页获取到 49 条记录
2025-06-20 19:31:21,932 - INFO - 查询完成，共获取到 549 条记录
2025-06-20 19:31:21,932 - INFO - 获取到 549 条表单数据
2025-06-20 19:31:21,932 - INFO - 当前日期 2025-06-19 有 147 条MySQL数据需要处理
2025-06-20 19:31:21,932 - INFO - 开始更新记录 - 表单实例ID: FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM5G
2025-06-20 19:31:22,401 - INFO - 更新表单数据成功: FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CM5G
2025-06-20 19:31:22,401 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-20 19:31:22,401 - INFO - 开始更新记录 - 表单实例ID: FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMFG
2025-06-20 19:31:22,823 - INFO - 更新表单数据成功: FINST-3Z966E91J1GWC9JC70CGY5CLO2Y93LTR074CMFG
2025-06-20 19:31:22,823 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-20 19:31:22,839 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMF3
2025-06-20 19:31:23,229 - INFO - 更新表单数据成功: FINST-EZD66RB11MFWWZIZ6NPJXCAM6WXE2KVV074CMF3
2025-06-20 19:31:23,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2688.22, 'new_value': 3530.59}, {'field': 'total_amount', 'old_value': 2688.22, 'new_value': 3530.59}, {'field': 'order_count', 'old_value': 119, 'new_value': 146}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-20 19:31:23,245 - INFO - 开始批量插入 1 条新记录
2025-06-20 19:31:23,385 - INFO - 批量插入响应状态码: 200
2025-06-20 19:31:23,385 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 11:31:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3279D523-2AF1-7F8C-88AC-384D4159CDB6', 'x-acs-trace-id': '1e4c469dd03fb0ae944f66ba6c3ee960', 'etag': '5qDGM2S/PPZoezLSTzpd0nA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 19:31:23,385 - INFO - 批量插入响应体: {'result': ['FINST-PAB66N71WWGW3LPIBX38F7FVOPSQ23QSBQ4CM9']}
2025-06-20 19:31:23,385 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-20 19:31:23,385 - INFO - 成功插入的数据ID: ['FINST-PAB66N71WWGW3LPIBX38F7FVOPSQ23QSBQ4CM9']
2025-06-20 19:31:28,402 - INFO - 批量插入完成，共 1 条记录
2025-06-20 19:31:28,402 - INFO - 日期 2025-06-19 处理完成 - 更新: 3 条，插入: 1 条，错误: 0 条
2025-06-20 19:31:28,402 - INFO - 开始处理日期: 2025-06-20
2025-06-20 19:31:28,402 - INFO - Request Parameters - Page 1:
2025-06-20 19:31:28,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:31:28,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:31:28,824 - INFO - Response - Page 1:
2025-06-20 19:31:28,824 - INFO - 第 1 页获取到 3 条记录
2025-06-20 19:31:29,339 - INFO - 查询完成，共获取到 3 条记录
2025-06-20 19:31:29,339 - INFO - 获取到 3 条表单数据
2025-06-20 19:31:29,339 - INFO - 当前日期 2025-06-20 有 4 条MySQL数据需要处理
2025-06-20 19:31:29,339 - INFO - 开始批量插入 1 条新记录
2025-06-20 19:31:29,496 - INFO - 批量插入响应状态码: 200
2025-06-20 19:31:29,496 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 11:31:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CFE3C048-E07E-7461-AD0B-9666DED85BE5', 'x-acs-trace-id': 'da083e2cfff1e6679fc17443ed6d11b6', 'etag': '5kqjWOXugzrqGYjhMvON6gw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 19:31:29,496 - INFO - 批量插入响应体: {'result': ['FINST-X3766I91DXGW71G9AQSYKDTYJDXF3WFXBQ4CM9']}
2025-06-20 19:31:29,496 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-20 19:31:29,496 - INFO - 成功插入的数据ID: ['FINST-X3766I91DXGW71G9AQSYKDTYJDXF3WFXBQ4CM9']
2025-06-20 19:31:34,512 - INFO - 批量插入完成，共 1 条记录
2025-06-20 19:31:34,512 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-20 19:31:34,512 - INFO - 数据同步完成！更新: 3 条，插入: 2 条，错误: 1 条
2025-06-20 19:32:34,534 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 19:32:34,534 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 19:32:34,534 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 19:32:34,675 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 570 条记录
2025-06-20 19:32:34,675 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-20 19:32:34,675 - INFO - 开始处理日期: 2025-06-19
2025-06-20 19:32:34,675 - INFO - Request Parameters - Page 1:
2025-06-20 19:32:34,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:34,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:35,362 - INFO - Response - Page 1:
2025-06-20 19:32:35,362 - INFO - 第 1 页获取到 50 条记录
2025-06-20 19:32:35,878 - INFO - Request Parameters - Page 2:
2025-06-20 19:32:35,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:35,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:36,487 - INFO - Response - Page 2:
2025-06-20 19:32:36,487 - INFO - 第 2 页获取到 50 条记录
2025-06-20 19:32:37,003 - INFO - Request Parameters - Page 3:
2025-06-20 19:32:37,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:37,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:37,675 - INFO - Response - Page 3:
2025-06-20 19:32:37,675 - INFO - 第 3 页获取到 50 条记录
2025-06-20 19:32:38,191 - INFO - Request Parameters - Page 4:
2025-06-20 19:32:38,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:38,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:38,863 - INFO - Response - Page 4:
2025-06-20 19:32:38,863 - INFO - 第 4 页获取到 50 条记录
2025-06-20 19:32:39,363 - INFO - Request Parameters - Page 5:
2025-06-20 19:32:39,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:39,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:39,972 - INFO - Response - Page 5:
2025-06-20 19:32:39,972 - INFO - 第 5 页获取到 50 条记录
2025-06-20 19:32:40,488 - INFO - Request Parameters - Page 6:
2025-06-20 19:32:40,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:40,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:41,129 - INFO - Response - Page 6:
2025-06-20 19:32:41,129 - INFO - 第 6 页获取到 50 条记录
2025-06-20 19:32:41,644 - INFO - Request Parameters - Page 7:
2025-06-20 19:32:41,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:41,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:42,301 - INFO - Response - Page 7:
2025-06-20 19:32:42,301 - INFO - 第 7 页获取到 50 条记录
2025-06-20 19:32:42,801 - INFO - Request Parameters - Page 8:
2025-06-20 19:32:42,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:42,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:43,473 - INFO - Response - Page 8:
2025-06-20 19:32:43,473 - INFO - 第 8 页获取到 50 条记录
2025-06-20 19:32:43,988 - INFO - Request Parameters - Page 9:
2025-06-20 19:32:43,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:43,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:44,645 - INFO - Response - Page 9:
2025-06-20 19:32:44,645 - INFO - 第 9 页获取到 50 条记录
2025-06-20 19:32:45,160 - INFO - Request Parameters - Page 10:
2025-06-20 19:32:45,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:45,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:45,832 - INFO - Response - Page 10:
2025-06-20 19:32:45,832 - INFO - 第 10 页获取到 50 条记录
2025-06-20 19:32:46,332 - INFO - Request Parameters - Page 11:
2025-06-20 19:32:46,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:46,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:46,911 - INFO - Response - Page 11:
2025-06-20 19:32:46,911 - INFO - 第 11 页获取到 50 条记录
2025-06-20 19:32:47,426 - INFO - Request Parameters - Page 12:
2025-06-20 19:32:47,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:47,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:47,817 - INFO - Response - Page 12:
2025-06-20 19:32:47,817 - INFO - 查询完成，共获取到 550 条记录
2025-06-20 19:32:47,817 - INFO - 获取到 550 条表单数据
2025-06-20 19:32:47,833 - INFO - 当前日期 2025-06-19 有 551 条MySQL数据需要处理
2025-06-20 19:32:47,848 - INFO - 开始批量插入 1 条新记录
2025-06-20 19:32:47,989 - INFO - 批量插入响应状态码: 200
2025-06-20 19:32:47,989 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 11:32:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '435F40BA-C417-704C-BFB7-0738EC3CEA6C', 'x-acs-trace-id': '3d6b9693db1ebeadfd5692ff0173e080', 'etag': '5dd9DM+Vrc/+0MzKxBVBr9w9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 19:32:47,989 - INFO - 批量插入响应体: {'result': ['FINST-IQG66AD1QXGWUGTJ6HZC2DVLYE83220MDQ4CM2']}
2025-06-20 19:32:47,989 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-20 19:32:47,989 - INFO - 成功插入的数据ID: ['FINST-IQG66AD1QXGWUGTJ6HZC2DVLYE83220MDQ4CM2']
2025-06-20 19:32:53,005 - INFO - 批量插入完成，共 1 条记录
2025-06-20 19:32:53,005 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-20 19:32:53,005 - INFO - 开始处理日期: 2025-06-20
2025-06-20 19:32:53,005 - INFO - Request Parameters - Page 1:
2025-06-20 19:32:53,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 19:32:53,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 19:32:53,505 - INFO - Response - Page 1:
2025-06-20 19:32:53,505 - INFO - 第 1 页获取到 4 条记录
2025-06-20 19:32:54,021 - INFO - 查询完成，共获取到 4 条记录
2025-06-20 19:32:54,021 - INFO - 获取到 4 条表单数据
2025-06-20 19:32:54,021 - INFO - 当前日期 2025-06-20 有 4 条MySQL数据需要处理
2025-06-20 19:32:54,021 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 19:32:54,021 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-20 19:32:54,021 - INFO - 同步完成
2025-06-20 22:30:33,884 - INFO - 使用默认增量同步（当天更新数据）
2025-06-20 22:30:33,884 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-20 22:30:33,884 - INFO - 查询参数: ('2025-06-20',)
2025-06-20 22:30:34,025 - INFO - MySQL查询成功，增量数据（日期: 2025-06-20），共获取 242 条记录
2025-06-20 22:30:34,025 - INFO - 获取到 5 个日期需要处理: ['2025-05-31', '2025-06-17', '2025-06-18', '2025-06-19', '2025-06-20']
2025-06-20 22:30:34,041 - INFO - 开始处理日期: 2025-05-31
2025-06-20 22:30:34,041 - INFO - Request Parameters - Page 1:
2025-06-20 22:30:34,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:34,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:42,151 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2B2E5B97-28AB-7113-81BF-280A92072103 Response: {'code': 'ServiceUnavailable', 'requestid': '2B2E5B97-28AB-7113-81BF-280A92072103', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2B2E5B97-28AB-7113-81BF-280A92072103)
2025-06-20 22:30:42,151 - INFO - 开始处理日期: 2025-06-17
2025-06-20 22:30:42,151 - INFO - Request Parameters - Page 1:
2025-06-20 22:30:42,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:42,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:43,370 - INFO - Response - Page 1:
2025-06-20 22:30:43,370 - INFO - 第 1 页获取到 50 条记录
2025-06-20 22:30:43,885 - INFO - Request Parameters - Page 2:
2025-06-20 22:30:43,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:43,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:44,526 - INFO - Response - Page 2:
2025-06-20 22:30:44,526 - INFO - 第 2 页获取到 50 条记录
2025-06-20 22:30:45,026 - INFO - Request Parameters - Page 3:
2025-06-20 22:30:45,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:45,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:50,964 - INFO - Response - Page 3:
2025-06-20 22:30:50,964 - INFO - 第 3 页获取到 50 条记录
2025-06-20 22:30:51,464 - INFO - Request Parameters - Page 4:
2025-06-20 22:30:51,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:51,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:52,168 - INFO - Response - Page 4:
2025-06-20 22:30:52,168 - INFO - 第 4 页获取到 50 条记录
2025-06-20 22:30:52,668 - INFO - Request Parameters - Page 5:
2025-06-20 22:30:52,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:52,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:53,293 - INFO - Response - Page 5:
2025-06-20 22:30:53,293 - INFO - 第 5 页获取到 50 条记录
2025-06-20 22:30:53,793 - INFO - Request Parameters - Page 6:
2025-06-20 22:30:53,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:53,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:54,449 - INFO - Response - Page 6:
2025-06-20 22:30:54,449 - INFO - 第 6 页获取到 50 条记录
2025-06-20 22:30:54,949 - INFO - Request Parameters - Page 7:
2025-06-20 22:30:54,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:54,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:55,574 - INFO - Response - Page 7:
2025-06-20 22:30:55,574 - INFO - 第 7 页获取到 50 条记录
2025-06-20 22:30:56,090 - INFO - Request Parameters - Page 8:
2025-06-20 22:30:56,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:56,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:56,731 - INFO - Response - Page 8:
2025-06-20 22:30:56,731 - INFO - 第 8 页获取到 50 条记录
2025-06-20 22:30:57,231 - INFO - Request Parameters - Page 9:
2025-06-20 22:30:57,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:57,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:57,918 - INFO - Response - Page 9:
2025-06-20 22:30:57,918 - INFO - 第 9 页获取到 50 条记录
2025-06-20 22:30:58,418 - INFO - Request Parameters - Page 10:
2025-06-20 22:30:58,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:58,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:30:59,106 - INFO - Response - Page 10:
2025-06-20 22:30:59,106 - INFO - 第 10 页获取到 50 条记录
2025-06-20 22:30:59,622 - INFO - Request Parameters - Page 11:
2025-06-20 22:30:59,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:30:59,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:00,169 - INFO - Response - Page 11:
2025-06-20 22:31:00,169 - INFO - 第 11 页获取到 38 条记录
2025-06-20 22:31:00,684 - INFO - 查询完成，共获取到 538 条记录
2025-06-20 22:31:00,684 - INFO - 获取到 538 条表单数据
2025-06-20 22:31:00,684 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-06-20 22:31:00,684 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 22:31:00,684 - INFO - 开始处理日期: 2025-06-18
2025-06-20 22:31:00,684 - INFO - Request Parameters - Page 1:
2025-06-20 22:31:00,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:00,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:01,356 - INFO - Response - Page 1:
2025-06-20 22:31:01,356 - INFO - 第 1 页获取到 50 条记录
2025-06-20 22:31:01,872 - INFO - Request Parameters - Page 2:
2025-06-20 22:31:01,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:01,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:02,513 - INFO - Response - Page 2:
2025-06-20 22:31:02,513 - INFO - 第 2 页获取到 50 条记录
2025-06-20 22:31:03,028 - INFO - Request Parameters - Page 3:
2025-06-20 22:31:03,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:03,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:03,794 - INFO - Response - Page 3:
2025-06-20 22:31:03,794 - INFO - 第 3 页获取到 50 条记录
2025-06-20 22:31:04,294 - INFO - Request Parameters - Page 4:
2025-06-20 22:31:04,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:04,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:04,950 - INFO - Response - Page 4:
2025-06-20 22:31:04,950 - INFO - 第 4 页获取到 50 条记录
2025-06-20 22:31:05,466 - INFO - Request Parameters - Page 5:
2025-06-20 22:31:05,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:05,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:06,107 - INFO - Response - Page 5:
2025-06-20 22:31:06,107 - INFO - 第 5 页获取到 50 条记录
2025-06-20 22:31:06,607 - INFO - Request Parameters - Page 6:
2025-06-20 22:31:06,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:06,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:07,232 - INFO - Response - Page 6:
2025-06-20 22:31:07,232 - INFO - 第 6 页获取到 50 条记录
2025-06-20 22:31:07,732 - INFO - Request Parameters - Page 7:
2025-06-20 22:31:07,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:07,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:08,341 - INFO - Response - Page 7:
2025-06-20 22:31:08,341 - INFO - 第 7 页获取到 50 条记录
2025-06-20 22:31:08,857 - INFO - Request Parameters - Page 8:
2025-06-20 22:31:08,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:08,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:09,482 - INFO - Response - Page 8:
2025-06-20 22:31:09,482 - INFO - 第 8 页获取到 50 条记录
2025-06-20 22:31:09,982 - INFO - Request Parameters - Page 9:
2025-06-20 22:31:09,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:09,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:10,623 - INFO - Response - Page 9:
2025-06-20 22:31:10,623 - INFO - 第 9 页获取到 50 条记录
2025-06-20 22:31:11,123 - INFO - Request Parameters - Page 10:
2025-06-20 22:31:11,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:11,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:11,795 - INFO - Response - Page 10:
2025-06-20 22:31:11,795 - INFO - 第 10 页获取到 50 条记录
2025-06-20 22:31:12,295 - INFO - Request Parameters - Page 11:
2025-06-20 22:31:12,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:12,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:12,967 - INFO - Response - Page 11:
2025-06-20 22:31:12,967 - INFO - 第 11 页获取到 50 条记录
2025-06-20 22:31:13,467 - INFO - Request Parameters - Page 12:
2025-06-20 22:31:13,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:13,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:13,983 - INFO - Response - Page 12:
2025-06-20 22:31:13,983 - INFO - 第 12 页获取到 13 条记录
2025-06-20 22:31:14,498 - INFO - 查询完成，共获取到 563 条记录
2025-06-20 22:31:14,498 - INFO - 获取到 563 条表单数据
2025-06-20 22:31:14,498 - INFO - 当前日期 2025-06-18 有 1 条MySQL数据需要处理
2025-06-20 22:31:14,498 - INFO - 日期 2025-06-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 22:31:14,498 - INFO - 开始处理日期: 2025-06-19
2025-06-20 22:31:14,498 - INFO - Request Parameters - Page 1:
2025-06-20 22:31:14,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:14,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:15,123 - INFO - Response - Page 1:
2025-06-20 22:31:15,123 - INFO - 第 1 页获取到 50 条记录
2025-06-20 22:31:15,639 - INFO - Request Parameters - Page 2:
2025-06-20 22:31:15,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:15,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:16,342 - INFO - Response - Page 2:
2025-06-20 22:31:16,342 - INFO - 第 2 页获取到 50 条记录
2025-06-20 22:31:16,858 - INFO - Request Parameters - Page 3:
2025-06-20 22:31:16,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:16,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:17,530 - INFO - Response - Page 3:
2025-06-20 22:31:17,530 - INFO - 第 3 页获取到 50 条记录
2025-06-20 22:31:18,030 - INFO - Request Parameters - Page 4:
2025-06-20 22:31:18,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:18,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:18,780 - INFO - Response - Page 4:
2025-06-20 22:31:18,780 - INFO - 第 4 页获取到 50 条记录
2025-06-20 22:31:19,296 - INFO - Request Parameters - Page 5:
2025-06-20 22:31:19,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:19,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:19,999 - INFO - Response - Page 5:
2025-06-20 22:31:19,999 - INFO - 第 5 页获取到 50 条记录
2025-06-20 22:31:20,515 - INFO - Request Parameters - Page 6:
2025-06-20 22:31:20,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:20,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:21,171 - INFO - Response - Page 6:
2025-06-20 22:31:21,171 - INFO - 第 6 页获取到 50 条记录
2025-06-20 22:31:21,687 - INFO - Request Parameters - Page 7:
2025-06-20 22:31:21,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:21,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:22,359 - INFO - Response - Page 7:
2025-06-20 22:31:22,374 - INFO - 第 7 页获取到 50 条记录
2025-06-20 22:31:22,890 - INFO - Request Parameters - Page 8:
2025-06-20 22:31:22,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:22,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:23,562 - INFO - Response - Page 8:
2025-06-20 22:31:23,562 - INFO - 第 8 页获取到 50 条记录
2025-06-20 22:31:24,077 - INFO - Request Parameters - Page 9:
2025-06-20 22:31:24,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:24,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:24,765 - INFO - Response - Page 9:
2025-06-20 22:31:24,765 - INFO - 第 9 页获取到 50 条记录
2025-06-20 22:31:25,265 - INFO - Request Parameters - Page 10:
2025-06-20 22:31:25,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:25,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:25,937 - INFO - Response - Page 10:
2025-06-20 22:31:25,937 - INFO - 第 10 页获取到 50 条记录
2025-06-20 22:31:26,453 - INFO - Request Parameters - Page 11:
2025-06-20 22:31:26,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:26,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:27,047 - INFO - Response - Page 11:
2025-06-20 22:31:27,047 - INFO - 第 11 页获取到 50 条记录
2025-06-20 22:31:27,547 - INFO - Request Parameters - Page 12:
2025-06-20 22:31:27,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:27,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:27,984 - INFO - Response - Page 12:
2025-06-20 22:31:27,984 - INFO - 第 12 页获取到 1 条记录
2025-06-20 22:31:28,500 - INFO - 查询完成，共获取到 551 条记录
2025-06-20 22:31:28,500 - INFO - 获取到 551 条表单数据
2025-06-20 22:31:28,500 - INFO - 当前日期 2025-06-19 有 147 条MySQL数据需要处理
2025-06-20 22:31:28,500 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 22:31:28,500 - INFO - 开始处理日期: 2025-06-20
2025-06-20 22:31:28,500 - INFO - Request Parameters - Page 1:
2025-06-20 22:31:28,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:31:28,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:31:29,016 - INFO - Response - Page 1:
2025-06-20 22:31:29,016 - INFO - 第 1 页获取到 4 条记录
2025-06-20 22:31:29,531 - INFO - 查询完成，共获取到 4 条记录
2025-06-20 22:31:29,531 - INFO - 获取到 4 条表单数据
2025-06-20 22:31:29,531 - INFO - 当前日期 2025-06-20 有 86 条MySQL数据需要处理
2025-06-20 22:31:29,531 - INFO - 开始批量插入 82 条新记录
2025-06-20 22:31:29,813 - INFO - 批量插入响应状态码: 200
2025-06-20 22:31:29,813 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 14:31:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2384', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7503EC76-A1DC-778E-A8FA-45C169AFCEDE', 'x-acs-trace-id': '5da2e08f0608a74c88b9b846551fbc73', 'etag': '2b/Up1ZWPczzM4gbEnI5W3A4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 22:31:29,813 - INFO - 批量插入响应体: {'result': ['FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM8', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM9', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMA', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMB', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMC', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMD', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CME', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMF', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMG', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMH', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMI', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMJ', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMK', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CML', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMM', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMN', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMO', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMP', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMQ', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMR', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMS', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMT', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMU', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMV', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMW', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMX', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMY', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMZ', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM01', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM11', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM21', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM31', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM41', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM51', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM61', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM71', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM81', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM91', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMA1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMB1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMC1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMD1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CME1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMF1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMG1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMH1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMI1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMJ1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMK1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CML1']}
2025-06-20 22:31:29,813 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-20 22:31:29,813 - INFO - 成功插入的数据ID: ['FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM8', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM9', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMA', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMB', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMC', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMD', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CME', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMF', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMG', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMH', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMI', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMJ', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMK', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CML', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMM', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMN', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMO', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMP', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMQ', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMR', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMS', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMT', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMU', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMV', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMW', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMX', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMY', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMZ', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM01', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM11', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM21', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM31', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM41', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM51', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM61', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM71', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM81', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CM91', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMA1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMB1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMC1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMD1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CME1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMF1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMG1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMH1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMI1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMJ1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CMK1', 'FINST-AI866781GXGWNE4VAYQOD938ZT363B2ERW4CML1']
2025-06-20 22:31:35,048 - INFO - 批量插入响应状态码: 200
2025-06-20 22:31:35,048 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 14:31:33 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1525', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '57112E17-32D1-7FAF-BCFB-93BE47B040E5', 'x-acs-trace-id': '7f1b544e51bdd5503708a97537befeeb', 'etag': '1geyr+aDnMHjDhD4ZQBV1WQ5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 22:31:35,048 - INFO - 批量插入响应体: {'result': ['FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMD', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CME', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMF', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMG', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMH', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMI', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMJ', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMK', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CML', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMM', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMN', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMO', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMP', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMQ', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMR', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMS', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMT', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMU', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMV', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMW', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMX', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMY', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMZ', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM01', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM11', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM21', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM31', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM41', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM51', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM61', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM71', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM81']}
2025-06-20 22:31:35,048 - INFO - 批量插入表单数据成功，批次 2，共 32 条记录
2025-06-20 22:31:35,048 - INFO - 成功插入的数据ID: ['FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMD', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CME', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMF', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMG', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMH', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMI', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMJ', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMK', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CML', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMM', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMN', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMO', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMP', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMQ', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMR', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMS', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMT', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMU', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMV', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMW', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMX', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMY', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CMZ', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM01', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM11', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM21', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM31', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM41', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM51', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM61', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM71', 'FINST-XRF66A81TXGW2KDDB1FDLDO1SOWM2M3IRW4CM81']
2025-06-20 22:31:40,064 - INFO - 批量插入完成，共 82 条记录
2025-06-20 22:31:40,064 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 82 条，错误: 0 条
2025-06-20 22:31:40,064 - INFO - 数据同步完成！更新: 0 条，插入: 82 条，错误: 1 条
2025-06-20 22:32:40,086 - INFO - 开始同步昨天与今天的销售数据: 2025-06-19 至 2025-06-20
2025-06-20 22:32:40,086 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-20 22:32:40,086 - INFO - 查询参数: ('2025-06-19', '2025-06-20')
2025-06-20 22:32:40,227 - INFO - MySQL查询成功，时间段: 2025-06-19 至 2025-06-20，共获取 663 条记录
2025-06-20 22:32:40,227 - INFO - 获取到 2 个日期需要处理: ['2025-06-19', '2025-06-20']
2025-06-20 22:32:40,227 - INFO - 开始处理日期: 2025-06-19
2025-06-20 22:32:40,227 - INFO - Request Parameters - Page 1:
2025-06-20 22:32:40,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:40,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:40,930 - INFO - Response - Page 1:
2025-06-20 22:32:40,930 - INFO - 第 1 页获取到 50 条记录
2025-06-20 22:32:41,430 - INFO - Request Parameters - Page 2:
2025-06-20 22:32:41,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:41,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:42,055 - INFO - Response - Page 2:
2025-06-20 22:32:42,055 - INFO - 第 2 页获取到 50 条记录
2025-06-20 22:32:42,555 - INFO - Request Parameters - Page 3:
2025-06-20 22:32:42,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:42,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:43,196 - INFO - Response - Page 3:
2025-06-20 22:32:43,196 - INFO - 第 3 页获取到 50 条记录
2025-06-20 22:32:43,712 - INFO - Request Parameters - Page 4:
2025-06-20 22:32:43,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:43,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:44,352 - INFO - Response - Page 4:
2025-06-20 22:32:44,352 - INFO - 第 4 页获取到 50 条记录
2025-06-20 22:32:44,868 - INFO - Request Parameters - Page 5:
2025-06-20 22:32:44,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:44,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:45,571 - INFO - Response - Page 5:
2025-06-20 22:32:45,571 - INFO - 第 5 页获取到 50 条记录
2025-06-20 22:32:46,087 - INFO - Request Parameters - Page 6:
2025-06-20 22:32:46,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:46,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:46,790 - INFO - Response - Page 6:
2025-06-20 22:32:46,790 - INFO - 第 6 页获取到 50 条记录
2025-06-20 22:32:47,306 - INFO - Request Parameters - Page 7:
2025-06-20 22:32:47,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:47,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:47,931 - INFO - Response - Page 7:
2025-06-20 22:32:47,931 - INFO - 第 7 页获取到 50 条记录
2025-06-20 22:32:48,447 - INFO - Request Parameters - Page 8:
2025-06-20 22:32:48,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:48,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:49,056 - INFO - Response - Page 8:
2025-06-20 22:32:49,056 - INFO - 第 8 页获取到 50 条记录
2025-06-20 22:32:49,556 - INFO - Request Parameters - Page 9:
2025-06-20 22:32:49,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:49,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:50,244 - INFO - Response - Page 9:
2025-06-20 22:32:50,244 - INFO - 第 9 页获取到 50 条记录
2025-06-20 22:32:50,759 - INFO - Request Parameters - Page 10:
2025-06-20 22:32:50,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:50,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:51,431 - INFO - Response - Page 10:
2025-06-20 22:32:51,431 - INFO - 第 10 页获取到 50 条记录
2025-06-20 22:32:51,947 - INFO - Request Parameters - Page 11:
2025-06-20 22:32:51,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:51,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:52,556 - INFO - Response - Page 11:
2025-06-20 22:32:52,556 - INFO - 第 11 页获取到 50 条记录
2025-06-20 22:32:53,072 - INFO - Request Parameters - Page 12:
2025-06-20 22:32:53,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:53,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750262400000, 1750348799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:53,494 - INFO - Response - Page 12:
2025-06-20 22:32:53,494 - INFO - 第 12 页获取到 1 条记录
2025-06-20 22:32:53,994 - INFO - 查询完成，共获取到 551 条记录
2025-06-20 22:32:53,994 - INFO - 获取到 551 条表单数据
2025-06-20 22:32:53,994 - INFO - 当前日期 2025-06-19 有 551 条MySQL数据需要处理
2025-06-20 22:32:54,010 - INFO - 日期 2025-06-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-20 22:32:54,010 - INFO - 开始处理日期: 2025-06-20
2025-06-20 22:32:54,010 - INFO - Request Parameters - Page 1:
2025-06-20 22:32:54,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:54,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:54,650 - INFO - Response - Page 1:
2025-06-20 22:32:54,650 - INFO - 第 1 页获取到 50 条记录
2025-06-20 22:32:55,166 - INFO - Request Parameters - Page 2:
2025-06-20 22:32:55,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-20 22:32:55,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-20 22:32:55,682 - INFO - Response - Page 2:
2025-06-20 22:32:55,682 - INFO - 第 2 页获取到 36 条记录
2025-06-20 22:32:56,197 - INFO - 查询完成，共获取到 86 条记录
2025-06-20 22:32:56,197 - INFO - 获取到 86 条表单数据
2025-06-20 22:32:56,197 - INFO - 当前日期 2025-06-20 有 95 条MySQL数据需要处理
2025-06-20 22:32:56,197 - INFO - 开始批量插入 9 条新记录
2025-06-20 22:32:56,354 - INFO - 批量插入响应状态码: 200
2025-06-20 22:32:56,354 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 20 Jun 2025 14:32:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DBEC38C2-C147-71EE-BB2E-70A428E9644B', 'x-acs-trace-id': '6854b656f72f8ad0f043f6b9ef4345ce', 'etag': '4U2LbWWqvvvty8C4ANDGreA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-20 22:32:56,354 - INFO - 批量插入响应体: {'result': ['FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMH1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMI1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMJ1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMK1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CML1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMM1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMN1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMO1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMP1']}
2025-06-20 22:32:56,354 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-06-20 22:32:56,354 - INFO - 成功插入的数据ID: ['FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMH1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMI1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMJ1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMK1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CML1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMM1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMN1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMO1', 'FINST-DO566BD1HWGWER9OAP1KZ79GGY012ST8TW4CMP1']
2025-06-20 22:33:01,370 - INFO - 批量插入完成，共 9 条记录
2025-06-20 22:33:01,370 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-06-20 22:33:01,370 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-06-20 22:33:01,370 - INFO - 同步完成
