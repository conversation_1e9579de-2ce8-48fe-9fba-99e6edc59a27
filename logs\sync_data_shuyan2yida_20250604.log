2025-06-04 08:00:03,114 - INFO - ==================================================
2025-06-04 08:00:03,114 - INFO - 程序启动 - 版本 v1.0.0
2025-06-04 08:00:03,114 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250604.log
2025-06-04 08:00:03,114 - INFO - ==================================================
2025-06-04 08:00:03,114 - INFO - 程序入口点: __main__
2025-06-04 08:00:03,114 - INFO - ==================================================
2025-06-04 08:00:03,114 - INFO - 程序启动 - 版本 v1.0.1
2025-06-04 08:00:03,114 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250604.log
2025-06-04 08:00:03,114 - INFO - ==================================================
2025-06-04 08:00:03,411 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-04 08:00:03,411 - INFO - sales_data表已存在，无需创建
2025-06-04 08:00:03,411 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-04 08:00:03,411 - INFO - DataSyncManager初始化完成
2025-06-04 08:00:03,411 - INFO - 未提供日期参数，使用默认值
2025-06-04 08:00:03,411 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-04 08:00:03,411 - INFO - 开始综合数据同步流程...
2025-06-04 08:00:03,411 - INFO - 正在获取数衍平台日销售数据...
2025-06-04 08:00:03,411 - INFO - 查询数衍平台数据，时间段为: 2025-04-04, 2025-06-03
2025-06-04 08:00:03,411 - INFO - 正在获取********至********的数据
2025-06-04 08:00:03,411 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:03,411 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '876CCB0431D540A703DADFF06CAB81A0'}
2025-06-04 08:00:05,395 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:05,395 - INFO - 过滤后保留 433 条记录
2025-06-04 08:00:07,411 - INFO - 正在获取********至********的数据
2025-06-04 08:00:07,411 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:07,411 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '319D625E9102DEE3EEF13FFD2E520463'}
2025-06-04 08:00:08,692 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:08,692 - INFO - 过滤后保留 427 条记录
2025-06-04 08:00:10,708 - INFO - 正在获取********至********的数据
2025-06-04 08:00:10,708 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:10,708 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6D7EA1CA4B4EB25677F84E1562B14554'}
2025-06-04 08:00:12,052 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:12,052 - INFO - 过滤后保留 426 条记录
2025-06-04 08:00:14,067 - INFO - 正在获取********至********的数据
2025-06-04 08:00:14,067 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:14,067 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CF0BBF874EBED5A6F3E4C0A267114B8B'}
2025-06-04 08:00:15,333 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:15,333 - INFO - 过滤后保留 422 条记录
2025-06-04 08:00:17,348 - INFO - 正在获取********至********的数据
2025-06-04 08:00:17,348 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:17,348 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0DFF05626C0C917AC7670CF4328B1409'}
2025-06-04 08:00:18,395 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:18,395 - INFO - 过滤后保留 440 条记录
2025-06-04 08:00:20,411 - INFO - 正在获取********至********的数据
2025-06-04 08:00:20,411 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:20,411 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '810F5BE7D93038743B5BD89DE43DA492'}
2025-06-04 08:00:21,426 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:21,442 - INFO - 过滤后保留 429 条记录
2025-06-04 08:00:23,458 - INFO - 正在获取********至********的数据
2025-06-04 08:00:23,458 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:23,458 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '493A27D0BC2C4D36100B997CF6D2CAD9'}
2025-06-04 08:00:24,348 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:24,348 - INFO - 过滤后保留 429 条记录
2025-06-04 08:00:26,364 - INFO - 正在获取********至********的数据
2025-06-04 08:00:26,364 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:26,364 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6FCEF8719F30FDE9E0F57CE457708EAF'}
2025-06-04 08:00:27,395 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:27,411 - INFO - 过滤后保留 432 条记录
2025-06-04 08:00:29,426 - INFO - 正在获取********至********的数据
2025-06-04 08:00:29,426 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:29,426 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EDD13E589C6F9FBE091643A2089D04E4'}
2025-06-04 08:00:30,380 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:30,395 - INFO - 过滤后保留 429 条记录
2025-06-04 08:00:32,411 - INFO - 正在获取********至********的数据
2025-06-04 08:00:32,411 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:32,411 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '63C3AC0ED2DFB0FF37E8A7CC384AE8BE'}
2025-06-04 08:00:33,536 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:33,536 - INFO - 过滤后保留 419 条记录
2025-06-04 08:00:35,551 - INFO - 正在获取********至********的数据
2025-06-04 08:00:35,551 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:35,551 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DE5D30B74DD12B2BC8EB941930E217B3'}
2025-06-04 08:00:36,614 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:36,614 - INFO - 过滤后保留 416 条记录
2025-06-04 08:00:38,629 - INFO - 正在获取********至********的数据
2025-06-04 08:00:38,629 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:38,629 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '31503A86C5FF3EA8D504953C2DE68AE7'}
2025-06-04 08:00:39,645 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:39,645 - INFO - 过滤后保留 434 条记录
2025-06-04 08:00:41,661 - INFO - 正在获取********至********的数据
2025-06-04 08:00:41,661 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:41,661 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-04 08:00:42,692 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:42,692 - INFO - 过滤后保留 424 条记录
2025-06-04 08:00:44,708 - INFO - 正在获取********至********的数据
2025-06-04 08:00:44,708 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:44,708 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '16E8C56B81FCA37805DF0C42F0784F32'}
2025-06-04 08:00:45,786 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:45,786 - INFO - 过滤后保留 431 条记录
2025-06-04 08:00:47,801 - INFO - 正在获取********至********的数据
2025-06-04 08:00:47,801 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:47,801 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9E1688010A09995F713A0BA7BB586D2D'}
2025-06-04 08:00:48,973 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:48,973 - INFO - 过滤后保留 421 条记录
2025-06-04 08:00:50,989 - INFO - 正在获取********至********的数据
2025-06-04 08:00:50,989 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:50,989 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BE8B1015E37CA2CCC14A04E9F54543C2'}
2025-06-04 08:00:52,208 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:52,223 - INFO - 过滤后保留 424 条记录
2025-06-04 08:00:54,239 - INFO - 正在获取********至********的数据
2025-06-04 08:00:54,239 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:54,239 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4A496F7CBB5E07055BDE22D4B54650FA'}
2025-06-04 08:00:55,473 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:55,489 - INFO - 过滤后保留 407 条记录
2025-06-04 08:00:57,489 - INFO - 正在获取********至********的数据
2025-06-04 08:00:57,489 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:00:57,489 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '06D75C16B164F897EC4583BA26FED5DD'}
2025-06-04 08:00:58,707 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:00:58,723 - INFO - 过滤后保留 421 条记录
2025-06-04 08:01:00,739 - INFO - 正在获取********至********的数据
2025-06-04 08:01:00,739 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:00,739 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C21371AB630B935D3C244675E9AFD20B'}
2025-06-04 08:01:01,692 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:01,692 - INFO - 过滤后保留 435 条记录
2025-06-04 08:01:03,707 - INFO - 正在获取********至********的数据
2025-06-04 08:01:03,707 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:03,707 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5350A4D66057627F157790EE593C70D0'}
2025-06-04 08:01:04,770 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:04,770 - INFO - 过滤后保留 424 条记录
2025-06-04 08:01:06,786 - INFO - 正在获取********至********的数据
2025-06-04 08:01:06,786 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:06,786 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '25E7B47A0CAA2EC4C892E7AC3D08529F'}
2025-06-04 08:01:08,036 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:08,036 - INFO - 过滤后保留 417 条记录
2025-06-04 08:01:10,051 - INFO - 正在获取********至********的数据
2025-06-04 08:01:10,051 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:10,051 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D89D610E6F31C0E89E1807CF8B40671D'}
2025-06-04 08:01:11,082 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:11,082 - INFO - 过滤后保留 425 条记录
2025-06-04 08:01:13,098 - INFO - 正在获取********至********的数据
2025-06-04 08:01:13,098 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:13,098 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F20B9A72F1378DC4AFA379B3149D471E'}
2025-06-04 08:01:14,098 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:14,098 - INFO - 过滤后保留 426 条记录
2025-06-04 08:01:16,114 - INFO - 正在获取********至********的数据
2025-06-04 08:01:16,114 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:16,114 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9C535056C7563DD6F940CF640A229174'}
2025-06-04 08:01:16,989 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:17,004 - INFO - 过滤后保留 421 条记录
2025-06-04 08:01:19,020 - INFO - 正在获取********至********的数据
2025-06-04 08:01:19,020 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:19,020 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5361F886058934F4252F035A06C04B37'}
2025-06-04 08:01:19,926 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:19,926 - INFO - 过滤后保留 416 条记录
2025-06-04 08:01:21,942 - INFO - 正在获取********至********的数据
2025-06-04 08:01:21,942 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:21,942 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8D870864CC54C48FBA06A31856292586'}
2025-06-04 08:01:22,989 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:22,989 - INFO - 过滤后保留 424 条记录
2025-06-04 08:01:25,004 - INFO - 正在获取********至********的数据
2025-06-04 08:01:25,004 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:25,004 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B28F307914E68706387C8026BA837368'}
2025-06-04 08:01:26,129 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:26,129 - INFO - 过滤后保留 410 条记录
2025-06-04 08:01:28,129 - INFO - 正在获取********至********的数据
2025-06-04 08:01:28,129 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:28,129 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '57FC37990E58F57A1CD79500A6BCDA39'}
2025-06-04 08:01:29,223 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:29,223 - INFO - 过滤后保留 414 条记录
2025-06-04 08:01:31,239 - INFO - 正在获取********至********的数据
2025-06-04 08:01:31,239 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:31,239 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D5AC47041578B43687D16D57F46DC8E2'}
2025-06-04 08:01:32,192 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:32,207 - INFO - 过滤后保留 416 条记录
2025-06-04 08:01:34,223 - INFO - 正在获取********至********的数据
2025-06-04 08:01:34,223 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:34,223 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6162A97701621D268DFED56BFCCA44F7'}
2025-06-04 08:01:35,067 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:35,067 - INFO - 过滤后保留 406 条记录
2025-06-04 08:01:37,082 - INFO - 正在获取********至********的数据
2025-06-04 08:01:37,082 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-04 08:01:37,082 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EC26457BFBB197D44F4CE578626C77A4'}
2025-06-04 08:01:37,707 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-04 08:01:37,707 - INFO - 过滤后保留 198 条记录
2025-06-04 08:01:39,723 - INFO - 开始保存数据到SQLite数据库，共 12896 条记录待处理
2025-06-04 08:01:40,535 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=BF554F536BF14762AEB7110E7BD583B7, sale_time=2025-05-27
2025-06-04 08:01:40,535 - INFO - 变更字段: amount: 38388 -> 40557, count: 51 -> 52, instore_amount: 38388.9 -> 40557.9, instore_count: 51 -> 52
2025-06-04 08:01:40,551 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=BF554F536BF14762AEB7110E7BD583B7, sale_time=2025-05-29
2025-06-04 08:01:40,551 - INFO - 变更字段: amount: 43012 -> 43703, count: 56 -> 57, instore_amount: 43012.79 -> 43703.79, instore_count: 56 -> 57
2025-06-04 08:01:40,551 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-29
2025-06-04 08:01:40,551 - INFO - 变更字段: recommend_amount: 0.0 -> 7425.66, daily_bill_amount: 0.0 -> 7425.66
2025-06-04 08:01:40,551 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-28
2025-06-04 08:01:40,551 - INFO - 变更字段: recommend_amount: 0.0 -> 7964.56, daily_bill_amount: 0.0 -> 7964.56
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-31
2025-06-04 08:01:40,582 - INFO - 变更字段: amount: 32766 -> 33574, count: 258 -> 259, instore_amount: 27422.3 -> 28230.3, instore_count: 166 -> 167
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-31
2025-06-04 08:01:40,582 - INFO - 变更字段: recommend_amount: 0.0 -> 19358.29, daily_bill_amount: 0.0 -> 19358.29
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-30
2025-06-04 08:01:40,582 - INFO - 变更字段: recommend_amount: 0.0 -> 9805.18, daily_bill_amount: 0.0 -> 9805.18
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-05-31
2025-06-04 08:01:40,582 - INFO - 变更字段: amount: 9161 -> 9164, count: 482 -> 483, online_amount: 157.4 -> 160.4, online_count: 7 -> 8
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-30
2025-06-04 08:01:40,582 - INFO - 变更字段: recommend_amount: 3448.12 -> 3447.43, amount: 3448 -> 3447
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR, sale_time=2025-05-30
2025-06-04 08:01:40,582 - INFO - 变更字段: recommend_amount: 7725.24 -> 9465.24, amount: 7725 -> 9465, count: 97 -> 99, instore_amount: 7412.1 -> 9152.1, instore_count: 86 -> 88
2025-06-04 08:01:40,582 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-02
2025-06-04 08:01:40,582 - INFO - 变更字段: recommend_amount: 0.0 -> 3410.7, daily_bill_amount: 0.0 -> 3410.7
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: daily_bill_amount: 0.0 -> 12440.0
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HVORJ88U7D2IL1AIB692RTFU8001185, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: 2594 -> 2608, count: 26 -> 27, online_amount: 131.5 -> 145.5, online_count: 3 -> 4
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: 3572 -> 3541, online_amount: 1731.82 -> 1701.22
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: 996 -> 997, count: 58 -> 61, online_amount: 742.0 -> 743.02, online_count: 41 -> 44
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-06-01
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: 912 -> 916, count: 33 -> 34, online_amount: 601.14 -> 604.64, online_count: 26 -> 27
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 10608.2 -> 10592.1, amount: 10608 -> 10592
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: 836 -> 4298, count: 3 -> 4, instore_amount: 836.0 -> 4298.0, instore_count: 3 -> 4
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G, sale_time=2025-06-01
2025-06-04 08:01:40,598 - INFO - 变更字段: count: 23 -> 24, instore_count: 23 -> 24
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 0.0 -> 7119.3, daily_bill_amount: 0.0 -> 7119.3
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: -23541 -> -19105, count: 67 -> 68, instore_amount: 38728.77 -> 43164.77, instore_count: 67 -> 68
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 3332.0 -> 3351.0, amount: 3332 -> 3351, instore_amount: 3332.0 -> 3351.0
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 2339.71 -> 2362.01, amount: 2339 -> 2362, count: 142 -> 145, instore_amount: 560.0 -> 562.2, instore_count: 26 -> 27, online_amount: 1813.71 -> 1833.81, online_count: 116 -> 118
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 10007.1 -> 11258.3, daily_bill_amount: 10007.1 -> 11258.3
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 6653.24 -> 7176.74, amount: 6653 -> 7176, count: 126 -> 130, instore_amount: 5801.95 -> 6325.45, instore_count: 106 -> 110
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-01
2025-06-04 08:01:40,598 - INFO - 变更字段: recommend_amount: 5306.21 -> 5829.71, amount: 5306 -> 5829, count: 115 -> 120, instore_amount: 3723.3 -> 4246.8, instore_count: 81 -> 86
2025-06-04 08:01:40,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-02
2025-06-04 08:01:40,598 - INFO - 变更字段: amount: 3140 -> 3147, count: 197 -> 198, online_amount: 2946.0 -> 2953.0, online_count: 158 -> 159
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: recommend_amount: 5717.81 -> 5774.61, amount: 5717 -> 5774, count: 289 -> 294, online_amount: 4282.82 -> 4339.62, online_count: 224 -> 229
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: recommend_amount: 0.0 -> 7461.0, daily_bill_amount: 0.0 -> 7461.0
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: recommend_amount: 0.0 -> 13441.79, daily_bill_amount: 0.0 -> 13441.79
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 26528 -> 26778, count: 209 -> 210, instore_amount: 24424.25 -> 24674.25, instore_count: 125 -> 126
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 49101 -> 49836, count: 337 -> 339, instore_amount: 29126.5 -> 29861.5, instore_count: 147 -> 149
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 19231 -> 19629, count: 108 -> 109, instore_amount: 19472.1 -> 19870.1, instore_count: 108 -> 109
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-01
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 6319 -> 6414, count: 235 -> 241, instore_amount: 2480.8 -> 2493.4, instore_count: 86 -> 87, online_amount: 4100.17 -> 4181.95, online_count: 149 -> 154
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 34766 -> 34801, count: 326 -> 329, online_amount: 4023.37 -> 4058.77, online_count: 177 -> 180
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-06-01
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 45661 -> 45666, count: 335 -> 336, instore_amount: 41500.47 -> 41505.87, instore_count: 193 -> 194
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 5381 -> 5543, count: 42 -> 45, instore_amount: 4633.17 -> 4794.48, instore_count: 34 -> 37
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9H11376450I86N3H2U19G001EIM, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: recommend_amount: 718.0 -> 1002.0, amount: 718 -> 1002, count: 2 -> 3, instore_amount: 718.0 -> 1002.0, instore_count: 2 -> 3
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 4706 -> 4754, count: 333 -> 340, online_amount: 4486.84 -> 4534.74, online_count: 306 -> 313
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-01
2025-06-04 08:01:40,613 - INFO - 变更字段: count: 440 -> 441, online_amount: 5371.82 -> 5371.83, online_count: 402 -> 403
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 7261 -> 7284, count: 394 -> 396, instore_amount: 7560.25 -> 7583.25, instore_count: 390 -> 392
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQQG9THS10I86N3H2U108001E9E, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 12989 -> 12990, count: 398 -> 399, instore_amount: 13023.0 -> 13024.8, instore_count: 398 -> 399
2025-06-04 08:01:40,613 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-02
2025-06-04 08:01:40,613 - INFO - 变更字段: amount: 6615 -> 6626, count: 453 -> 456, instore_amount: 4938.4 -> 4974.09, instore_count: 316 -> 322, online_amount: 1826.79 -> 1802.1, online_count: 137 -> 134
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 17776 -> 17740
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: recommend_amount: 3077.21 -> 3112.21, amount: 3077 -> 3112, count: 186 -> 187, instore_amount: 1940.24 -> 1975.24, instore_count: 104 -> 105
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: recommend_amount: 0.0 -> 5468.13, daily_bill_amount: 0.0 -> 5468.13
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 21943 -> 21953, count: 193 -> 194, instore_amount: 21508.31 -> 21518.21, instore_count: 128 -> 129
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: recommend_amount: 7941.37 -> 8307.37, amount: 7941 -> 8307, count: 102 -> 103, instore_amount: 7631.2 -> 7997.2, instore_count: 86 -> 87
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 18705 -> 20154, count: 29 -> 30, instore_amount: 18193.0 -> 19642.0, instore_count: 26 -> 27
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE29O5UH0D6AJB6QM8HA7I0011QG, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 55951 -> 56590, count: 126 -> 128, instore_amount: 57291.0 -> 57930.0, instore_count: 126 -> 128
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 16586 -> 18457, count: 96 -> 99, instore_amount: 13954.2 -> 15825.2, instore_count: 72 -> 75
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 102 -> 133, count: 4 -> 5, instore_amount: 102.9 -> 133.3, instore_count: 4 -> 5
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-02
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 677 -> 660
2025-06-04 08:01:40,629 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-01
2025-06-04 08:01:40,629 - INFO - 变更字段: amount: 1056 -> 1045
2025-06-04 08:01:40,879 - INFO - SQLite数据保存完成，统计信息：
2025-06-04 08:01:40,879 - INFO - - 总记录数: 12896
2025-06-04 08:01:40,879 - INFO - - 成功插入: 200
2025-06-04 08:01:40,879 - INFO - - 成功更新: 54
2025-06-04 08:01:40,879 - INFO - - 无需更新: 12642
2025-06-04 08:01:40,879 - INFO - - 处理失败: 0
2025-06-04 08:01:46,254 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250604.xlsx
2025-06-04 08:01:46,270 - INFO - 成功获取数衍平台数据，共 12896 条记录
2025-06-04 08:01:46,270 - INFO - 正在更新SQLite月度汇总数据...
2025-06-04 08:01:46,270 - INFO - 月度数据sqllite清空完成
2025-06-04 08:01:46,567 - INFO - 月度汇总数据更新完成，处理了 1402 条汇总记录
2025-06-04 08:01:46,567 - INFO - 成功更新月度汇总数据，共 1402 条记录
2025-06-04 08:01:46,567 - INFO - 正在获取宜搭日销售表单数据...
2025-06-04 08:01:46,567 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-04 00:00:00 至 2025-06-03 23:59:59
2025-06-04 08:01:46,567 - INFO - 查询分段 1: 2025-04-04 至 2025-04-05
2025-06-04 08:01:46,567 - INFO - 查询日期范围: 2025-04-04 至 2025-04-05，使用分页查询，每页 100 条记录
2025-06-04 08:01:46,582 - INFO - Request Parameters - Page 1:
2025-06-04 08:01:46,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:01:46,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000567, 1743782400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:01:54,738 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0B79CA8B-DA5E-7BF3-8C2A-5761F446C24A Response: {'code': 'ServiceUnavailable', 'requestid': '0B79CA8B-DA5E-7BF3-8C2A-5761F446C24A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-04 08:01:54,738 - ERROR - 服务不可用，将等待后重试
2025-06-04 08:01:54,738 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0B79CA8B-DA5E-7BF3-8C2A-5761F446C24A Response: {'code': 'ServiceUnavailable', 'requestid': '0B79CA8B-DA5E-7BF3-8C2A-5761F446C24A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-04 08:01:54,738 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-06-04 08:02:00,754 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-06-04 08:02:00,754 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0B79CA8B-DA5E-7BF3-8C2A-5761F446C24A Response: {'code': 'ServiceUnavailable', 'requestid': '0B79CA8B-DA5E-7BF3-8C2A-5761F446C24A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-06-04 08:02:10,770 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:10,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:10,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000567, 1743782400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:11,629 - INFO - API请求耗时: 859ms
2025-06-04 08:02:11,629 - INFO - Response - Page 1
2025-06-04 08:02:11,629 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:12,145 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:12,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:12,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000567, 1743782400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:16,519 - INFO - API请求耗时: 4375ms
2025-06-04 08:02:16,519 - INFO - Response - Page 2
2025-06-04 08:02:16,519 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:17,020 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:17,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:17,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000567, 1743782400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:17,769 - INFO - API请求耗时: 750ms
2025-06-04 08:02:17,769 - INFO - Response - Page 3
2025-06-04 08:02:17,769 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:18,269 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:18,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:18,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000567, 1743782400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:19,004 - INFO - API请求耗时: 734ms
2025-06-04 08:02:19,004 - INFO - Response - Page 4
2025-06-04 08:02:19,004 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:19,519 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:19,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:19,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000567, 1743782400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:20,098 - INFO - API请求耗时: 578ms
2025-06-04 08:02:20,098 - INFO - Response - Page 5
2025-06-04 08:02:20,098 - INFO - 第 5 页获取到 32 条记录
2025-06-04 08:02:20,098 - INFO - 查询完成，共获取到 432 条记录
2025-06-04 08:02:20,098 - INFO - 分段 1 查询成功，获取到 432 条记录
2025-06-04 08:02:21,113 - INFO - 查询分段 2: 2025-04-06 至 2025-04-07
2025-06-04 08:02:21,113 - INFO - 查询日期范围: 2025-04-06 至 2025-04-07，使用分页查询，每页 100 条记录
2025-06-04 08:02:21,113 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:21,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:21,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800567, 1743955200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:21,816 - INFO - API请求耗时: 703ms
2025-06-04 08:02:21,816 - INFO - Response - Page 1
2025-06-04 08:02:21,816 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:22,332 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:22,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:22,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800567, 1743955200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:23,160 - INFO - API请求耗时: 828ms
2025-06-04 08:02:23,160 - INFO - Response - Page 2
2025-06-04 08:02:23,160 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:23,676 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:23,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:23,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800567, 1743955200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:24,379 - INFO - API请求耗时: 703ms
2025-06-04 08:02:24,379 - INFO - Response - Page 3
2025-06-04 08:02:24,379 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:24,894 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:24,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:24,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800567, 1743955200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:25,629 - INFO - API请求耗时: 734ms
2025-06-04 08:02:25,629 - INFO - Response - Page 4
2025-06-04 08:02:25,629 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:26,144 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:26,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:26,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800567, 1743955200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:26,660 - INFO - API请求耗时: 516ms
2025-06-04 08:02:26,660 - INFO - Response - Page 5
2025-06-04 08:02:26,660 - INFO - 第 5 页获取到 24 条记录
2025-06-04 08:02:26,660 - INFO - 查询完成，共获取到 424 条记录
2025-06-04 08:02:26,660 - INFO - 分段 2 查询成功，获取到 424 条记录
2025-06-04 08:02:27,660 - INFO - 查询分段 3: 2025-04-08 至 2025-04-09
2025-06-04 08:02:27,660 - INFO - 查询日期范围: 2025-04-08 至 2025-04-09，使用分页查询，每页 100 条记录
2025-06-04 08:02:27,660 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:27,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:27,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600567, 1744128000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:28,394 - INFO - API请求耗时: 734ms
2025-06-04 08:02:28,394 - INFO - Response - Page 1
2025-06-04 08:02:28,394 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:28,894 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:28,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:28,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600567, 1744128000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:29,848 - INFO - API请求耗时: 953ms
2025-06-04 08:02:29,848 - INFO - Response - Page 2
2025-06-04 08:02:29,848 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:30,363 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:30,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:30,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600567, 1744128000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:31,129 - INFO - API请求耗时: 766ms
2025-06-04 08:02:31,129 - INFO - Response - Page 3
2025-06-04 08:02:31,129 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:31,629 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:31,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:31,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600567, 1744128000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:32,301 - INFO - API请求耗时: 672ms
2025-06-04 08:02:32,301 - INFO - Response - Page 4
2025-06-04 08:02:32,301 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:32,801 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:32,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:32,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600567, 1744128000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:33,285 - INFO - API请求耗时: 484ms
2025-06-04 08:02:33,285 - INFO - Response - Page 5
2025-06-04 08:02:33,285 - INFO - 第 5 页获取到 24 条记录
2025-06-04 08:02:33,285 - INFO - 查询完成，共获取到 424 条记录
2025-06-04 08:02:33,285 - INFO - 分段 3 查询成功，获取到 424 条记录
2025-06-04 08:02:34,301 - INFO - 查询分段 4: 2025-04-10 至 2025-04-11
2025-06-04 08:02:34,301 - INFO - 查询日期范围: 2025-04-10 至 2025-04-11，使用分页查询，每页 100 条记录
2025-06-04 08:02:34,301 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:34,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:34,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400567, 1744300800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:34,957 - INFO - API请求耗时: 656ms
2025-06-04 08:02:34,957 - INFO - Response - Page 1
2025-06-04 08:02:34,957 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:35,457 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:35,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:35,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400567, 1744300800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:36,097 - INFO - API请求耗时: 641ms
2025-06-04 08:02:36,097 - INFO - Response - Page 2
2025-06-04 08:02:36,097 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:36,597 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:36,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:36,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400567, 1744300800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:37,285 - INFO - API请求耗时: 687ms
2025-06-04 08:02:37,285 - INFO - Response - Page 3
2025-06-04 08:02:37,285 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:37,785 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:37,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:37,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400567, 1744300800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:38,504 - INFO - API请求耗时: 719ms
2025-06-04 08:02:38,504 - INFO - Response - Page 4
2025-06-04 08:02:38,504 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:39,004 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:39,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:39,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400567, 1744300800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:39,551 - INFO - API请求耗时: 547ms
2025-06-04 08:02:39,551 - INFO - Response - Page 5
2025-06-04 08:02:39,551 - INFO - 第 5 页获取到 22 条记录
2025-06-04 08:02:39,551 - INFO - 查询完成，共获取到 422 条记录
2025-06-04 08:02:39,551 - INFO - 分段 4 查询成功，获取到 422 条记录
2025-06-04 08:02:40,551 - INFO - 查询分段 5: 2025-04-12 至 2025-04-13
2025-06-04 08:02:40,551 - INFO - 查询日期范围: 2025-04-12 至 2025-04-13，使用分页查询，每页 100 条记录
2025-06-04 08:02:40,551 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:40,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:40,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200567, 1744473600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:41,269 - INFO - API请求耗时: 719ms
2025-06-04 08:02:41,269 - INFO - Response - Page 1
2025-06-04 08:02:41,269 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:41,785 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:41,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:41,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200567, 1744473600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:42,488 - INFO - API请求耗时: 703ms
2025-06-04 08:02:42,488 - INFO - Response - Page 2
2025-06-04 08:02:42,488 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:43,004 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:43,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:43,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200567, 1744473600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:43,722 - INFO - API请求耗时: 719ms
2025-06-04 08:02:43,722 - INFO - Response - Page 3
2025-06-04 08:02:43,722 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:44,222 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:44,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:44,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200567, 1744473600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:45,051 - INFO - API请求耗时: 828ms
2025-06-04 08:02:45,051 - INFO - Response - Page 4
2025-06-04 08:02:45,066 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:45,582 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:45,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:45,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200567, 1744473600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:46,160 - INFO - API请求耗时: 578ms
2025-06-04 08:02:46,160 - INFO - Response - Page 5
2025-06-04 08:02:46,160 - INFO - 第 5 页获取到 38 条记录
2025-06-04 08:02:46,160 - INFO - 查询完成，共获取到 438 条记录
2025-06-04 08:02:46,160 - INFO - 分段 5 查询成功，获取到 438 条记录
2025-06-04 08:02:47,160 - INFO - 查询分段 6: 2025-04-14 至 2025-04-15
2025-06-04 08:02:47,160 - INFO - 查询日期范围: 2025-04-14 至 2025-04-15，使用分页查询，每页 100 条记录
2025-06-04 08:02:47,160 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:47,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:47,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000567, 1744646400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:48,082 - INFO - API请求耗时: 922ms
2025-06-04 08:02:48,082 - INFO - Response - Page 1
2025-06-04 08:02:48,082 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:48,582 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:48,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:48,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000567, 1744646400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:49,519 - INFO - API请求耗时: 937ms
2025-06-04 08:02:49,519 - INFO - Response - Page 2
2025-06-04 08:02:49,519 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:50,035 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:50,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:50,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000567, 1744646400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:50,785 - INFO - API请求耗时: 750ms
2025-06-04 08:02:50,785 - INFO - Response - Page 3
2025-06-04 08:02:50,785 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:51,285 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:51,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:51,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000567, 1744646400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:51,988 - INFO - API请求耗时: 703ms
2025-06-04 08:02:51,988 - INFO - Response - Page 4
2025-06-04 08:02:51,988 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:52,488 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:52,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:52,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000567, 1744646400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:53,066 - INFO - API请求耗时: 578ms
2025-06-04 08:02:53,066 - INFO - Response - Page 5
2025-06-04 08:02:53,066 - INFO - 第 5 页获取到 28 条记录
2025-06-04 08:02:53,066 - INFO - 查询完成，共获取到 428 条记录
2025-06-04 08:02:53,066 - INFO - 分段 6 查询成功，获取到 428 条记录
2025-06-04 08:02:54,066 - INFO - 查询分段 7: 2025-04-16 至 2025-04-17
2025-06-04 08:02:54,066 - INFO - 查询日期范围: 2025-04-16 至 2025-04-17，使用分页查询，每页 100 条记录
2025-06-04 08:02:54,066 - INFO - Request Parameters - Page 1:
2025-06-04 08:02:54,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:54,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800567, 1744819200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:54,769 - INFO - API请求耗时: 703ms
2025-06-04 08:02:54,769 - INFO - Response - Page 1
2025-06-04 08:02:54,769 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:02:55,269 - INFO - Request Parameters - Page 2:
2025-06-04 08:02:55,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:55,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800567, 1744819200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:55,988 - INFO - API请求耗时: 719ms
2025-06-04 08:02:55,988 - INFO - Response - Page 2
2025-06-04 08:02:56,004 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:02:56,504 - INFO - Request Parameters - Page 3:
2025-06-04 08:02:56,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:56,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800567, 1744819200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:57,191 - INFO - API请求耗时: 687ms
2025-06-04 08:02:57,191 - INFO - Response - Page 3
2025-06-04 08:02:57,191 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:02:57,707 - INFO - Request Parameters - Page 4:
2025-06-04 08:02:57,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:57,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800567, 1744819200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:58,425 - INFO - API请求耗时: 719ms
2025-06-04 08:02:58,425 - INFO - Response - Page 4
2025-06-04 08:02:58,425 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:02:58,925 - INFO - Request Parameters - Page 5:
2025-06-04 08:02:58,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:02:58,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800567, 1744819200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:02:59,472 - INFO - API请求耗时: 547ms
2025-06-04 08:02:59,472 - INFO - Response - Page 5
2025-06-04 08:02:59,472 - INFO - 第 5 页获取到 38 条记录
2025-06-04 08:02:59,488 - INFO - 查询完成，共获取到 438 条记录
2025-06-04 08:02:59,488 - INFO - 分段 7 查询成功，获取到 438 条记录
2025-06-04 08:03:00,504 - INFO - 查询分段 8: 2025-04-18 至 2025-04-19
2025-06-04 08:03:00,504 - INFO - 查询日期范围: 2025-04-18 至 2025-04-19，使用分页查询，每页 100 条记录
2025-06-04 08:03:00,504 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:00,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:00,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600567, 1744992000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:01,175 - INFO - API请求耗时: 672ms
2025-06-04 08:03:01,175 - INFO - Response - Page 1
2025-06-04 08:03:01,175 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:01,691 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:01,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:01,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600567, 1744992000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:02,363 - INFO - API请求耗时: 672ms
2025-06-04 08:03:02,363 - INFO - Response - Page 2
2025-06-04 08:03:02,363 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:02,863 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:02,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:02,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600567, 1744992000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:03,691 - INFO - API请求耗时: 828ms
2025-06-04 08:03:03,691 - INFO - Response - Page 3
2025-06-04 08:03:03,691 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:04,207 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:04,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:04,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600567, 1744992000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:04,894 - INFO - API请求耗时: 687ms
2025-06-04 08:03:04,894 - INFO - Response - Page 4
2025-06-04 08:03:04,894 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:05,410 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:05,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:05,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600567, 1744992000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:06,050 - INFO - API请求耗时: 641ms
2025-06-04 08:03:06,050 - INFO - Response - Page 5
2025-06-04 08:03:06,066 - INFO - 第 5 页获取到 30 条记录
2025-06-04 08:03:06,066 - INFO - 查询完成，共获取到 430 条记录
2025-06-04 08:03:06,066 - INFO - 分段 8 查询成功，获取到 430 条记录
2025-06-04 08:03:07,082 - INFO - 查询分段 9: 2025-04-20 至 2025-04-21
2025-06-04 08:03:07,082 - INFO - 查询日期范围: 2025-04-20 至 2025-04-21，使用分页查询，每页 100 条记录
2025-06-04 08:03:07,082 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:07,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:07,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400567, 1745164800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:07,754 - INFO - API请求耗时: 672ms
2025-06-04 08:03:07,754 - INFO - Response - Page 1
2025-06-04 08:03:07,754 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:08,254 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:08,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:08,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400567, 1745164800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:08,972 - INFO - API请求耗时: 719ms
2025-06-04 08:03:08,972 - INFO - Response - Page 2
2025-06-04 08:03:08,972 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:09,472 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:09,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:09,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400567, 1745164800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:10,175 - INFO - API请求耗时: 703ms
2025-06-04 08:03:10,175 - INFO - Response - Page 3
2025-06-04 08:03:10,175 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:10,691 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:10,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:10,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400567, 1745164800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:11,300 - INFO - API请求耗时: 609ms
2025-06-04 08:03:11,300 - INFO - Response - Page 4
2025-06-04 08:03:11,300 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:11,800 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:11,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:11,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400567, 1745164800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:12,347 - INFO - API请求耗时: 547ms
2025-06-04 08:03:12,347 - INFO - Response - Page 5
2025-06-04 08:03:12,347 - INFO - 第 5 页获取到 26 条记录
2025-06-04 08:03:12,347 - INFO - 查询完成，共获取到 426 条记录
2025-06-04 08:03:12,347 - INFO - 分段 9 查询成功，获取到 426 条记录
2025-06-04 08:03:13,363 - INFO - 查询分段 10: 2025-04-22 至 2025-04-23
2025-06-04 08:03:13,363 - INFO - 查询日期范围: 2025-04-22 至 2025-04-23，使用分页查询，每页 100 条记录
2025-06-04 08:03:13,363 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:13,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:13,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200567, 1745337600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:14,160 - INFO - API请求耗时: 797ms
2025-06-04 08:03:14,160 - INFO - Response - Page 1
2025-06-04 08:03:14,160 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:14,660 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:14,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:14,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200567, 1745337600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:15,363 - INFO - API请求耗时: 703ms
2025-06-04 08:03:15,363 - INFO - Response - Page 2
2025-06-04 08:03:15,363 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:15,863 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:15,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:15,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200567, 1745337600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:16,582 - INFO - API请求耗时: 719ms
2025-06-04 08:03:16,582 - INFO - Response - Page 3
2025-06-04 08:03:16,582 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:17,097 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:17,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:17,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200567, 1745337600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:17,785 - INFO - API请求耗时: 687ms
2025-06-04 08:03:17,785 - INFO - Response - Page 4
2025-06-04 08:03:17,785 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:18,300 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:18,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:18,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200567, 1745337600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:18,738 - INFO - API请求耗时: 437ms
2025-06-04 08:03:18,738 - INFO - Response - Page 5
2025-06-04 08:03:18,738 - INFO - 第 5 页获取到 14 条记录
2025-06-04 08:03:18,738 - INFO - 查询完成，共获取到 414 条记录
2025-06-04 08:03:18,738 - INFO - 分段 10 查询成功，获取到 414 条记录
2025-06-04 08:03:19,738 - INFO - 查询分段 11: 2025-04-24 至 2025-04-25
2025-06-04 08:03:19,738 - INFO - 查询日期范围: 2025-04-24 至 2025-04-25，使用分页查询，每页 100 条记录
2025-06-04 08:03:19,738 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:19,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:19,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000567, 1745510400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:20,410 - INFO - API请求耗时: 672ms
2025-06-04 08:03:20,410 - INFO - Response - Page 1
2025-06-04 08:03:20,410 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:20,925 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:20,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:20,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000567, 1745510400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:21,832 - INFO - API请求耗时: 906ms
2025-06-04 08:03:21,832 - INFO - Response - Page 2
2025-06-04 08:03:21,832 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:22,347 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:22,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:22,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000567, 1745510400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:23,050 - INFO - API请求耗时: 703ms
2025-06-04 08:03:23,050 - INFO - Response - Page 3
2025-06-04 08:03:23,050 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:23,550 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:23,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:23,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000567, 1745510400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:24,238 - INFO - API请求耗时: 687ms
2025-06-04 08:03:24,238 - INFO - Response - Page 4
2025-06-04 08:03:24,238 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:24,738 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:24,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:24,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000567, 1745510400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:25,191 - INFO - API请求耗时: 453ms
2025-06-04 08:03:25,191 - INFO - Response - Page 5
2025-06-04 08:03:25,207 - INFO - 第 5 页获取到 18 条记录
2025-06-04 08:03:25,207 - INFO - 查询完成，共获取到 418 条记录
2025-06-04 08:03:25,207 - INFO - 分段 11 查询成功，获取到 418 条记录
2025-06-04 08:03:26,222 - INFO - 查询分段 12: 2025-04-26 至 2025-04-27
2025-06-04 08:03:26,222 - INFO - 查询日期范围: 2025-04-26 至 2025-04-27，使用分页查询，每页 100 条记录
2025-06-04 08:03:26,222 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:26,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:26,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800567, 1745683200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:26,925 - INFO - API请求耗时: 703ms
2025-06-04 08:03:26,925 - INFO - Response - Page 1
2025-06-04 08:03:26,925 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:27,441 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:27,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:27,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800567, 1745683200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:28,128 - INFO - API请求耗时: 688ms
2025-06-04 08:03:28,128 - INFO - Response - Page 2
2025-06-04 08:03:28,128 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:28,644 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:28,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:28,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800567, 1745683200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:29,457 - INFO - API请求耗时: 813ms
2025-06-04 08:03:29,457 - INFO - Response - Page 3
2025-06-04 08:03:29,457 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:29,972 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:29,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:29,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800567, 1745683200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:30,691 - INFO - API请求耗时: 719ms
2025-06-04 08:03:30,691 - INFO - Response - Page 4
2025-06-04 08:03:30,707 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:31,207 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:31,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:31,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800567, 1745683200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:31,707 - INFO - API请求耗时: 500ms
2025-06-04 08:03:31,707 - INFO - Response - Page 5
2025-06-04 08:03:31,722 - INFO - 第 5 页获取到 32 条记录
2025-06-04 08:03:31,722 - INFO - 查询完成，共获取到 432 条记录
2025-06-04 08:03:31,722 - INFO - 分段 12 查询成功，获取到 432 条记录
2025-06-04 08:03:32,738 - INFO - 查询分段 13: 2025-04-28 至 2025-04-29
2025-06-04 08:03:32,738 - INFO - 查询日期范围: 2025-04-28 至 2025-04-29，使用分页查询，每页 100 条记录
2025-06-04 08:03:32,738 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:32,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:32,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600567, 1745856000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:33,410 - INFO - API请求耗时: 672ms
2025-06-04 08:03:33,410 - INFO - Response - Page 1
2025-06-04 08:03:33,410 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:33,925 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:33,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:33,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600567, 1745856000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:34,581 - INFO - API请求耗时: 656ms
2025-06-04 08:03:34,581 - INFO - Response - Page 2
2025-06-04 08:03:34,581 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:35,097 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:35,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:35,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600567, 1745856000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:35,941 - INFO - API请求耗时: 844ms
2025-06-04 08:03:35,941 - INFO - Response - Page 3
2025-06-04 08:03:35,941 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:36,441 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:36,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:36,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600567, 1745856000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:37,097 - INFO - API请求耗时: 656ms
2025-06-04 08:03:37,097 - INFO - Response - Page 4
2025-06-04 08:03:37,097 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:37,597 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:37,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:37,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600567, 1745856000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:38,144 - INFO - API请求耗时: 547ms
2025-06-04 08:03:38,144 - INFO - Response - Page 5
2025-06-04 08:03:38,144 - INFO - 第 5 页获取到 24 条记录
2025-06-04 08:03:38,144 - INFO - 查询完成，共获取到 424 条记录
2025-06-04 08:03:38,144 - INFO - 分段 13 查询成功，获取到 424 条记录
2025-06-04 08:03:39,160 - INFO - 查询分段 14: 2025-04-30 至 2025-05-01
2025-06-04 08:03:39,160 - INFO - 查询日期范围: 2025-04-30 至 2025-05-01，使用分页查询，每页 100 条记录
2025-06-04 08:03:39,160 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:39,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:39,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400567, 1746028800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:40,019 - INFO - API请求耗时: 859ms
2025-06-04 08:03:40,019 - INFO - Response - Page 1
2025-06-04 08:03:40,019 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:40,519 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:40,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:40,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400567, 1746028800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:41,316 - INFO - API请求耗时: 797ms
2025-06-04 08:03:41,316 - INFO - Response - Page 2
2025-06-04 08:03:41,316 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:41,816 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:41,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:41,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400567, 1746028800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:42,613 - INFO - API请求耗时: 797ms
2025-06-04 08:03:42,613 - INFO - Response - Page 3
2025-06-04 08:03:42,613 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:43,128 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:43,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:43,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400567, 1746028800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:43,910 - INFO - API请求耗时: 781ms
2025-06-04 08:03:43,910 - INFO - Response - Page 4
2025-06-04 08:03:43,910 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:44,410 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:44,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:44,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400567, 1746028800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:44,863 - INFO - API请求耗时: 453ms
2025-06-04 08:03:44,863 - INFO - Response - Page 5
2025-06-04 08:03:44,863 - INFO - 第 5 页获取到 28 条记录
2025-06-04 08:03:44,863 - INFO - 查询完成，共获取到 428 条记录
2025-06-04 08:03:44,863 - INFO - 分段 14 查询成功，获取到 428 条记录
2025-06-04 08:03:45,878 - INFO - 查询分段 15: 2025-05-02 至 2025-05-03
2025-06-04 08:03:45,878 - INFO - 查询日期范围: 2025-05-02 至 2025-05-03，使用分页查询，每页 100 条记录
2025-06-04 08:03:45,878 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:45,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:45,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200567, 1746201600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:46,785 - INFO - API请求耗时: 906ms
2025-06-04 08:03:46,785 - INFO - Response - Page 1
2025-06-04 08:03:46,785 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:47,300 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:47,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:47,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200567, 1746201600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:48,019 - INFO - API请求耗时: 719ms
2025-06-04 08:03:48,019 - INFO - Response - Page 2
2025-06-04 08:03:48,019 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:48,519 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:48,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:48,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200567, 1746201600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:49,175 - INFO - API请求耗时: 656ms
2025-06-04 08:03:49,175 - INFO - Response - Page 3
2025-06-04 08:03:49,175 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:49,675 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:49,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:49,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200567, 1746201600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:50,363 - INFO - API请求耗时: 687ms
2025-06-04 08:03:50,363 - INFO - Response - Page 4
2025-06-04 08:03:50,363 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:50,878 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:50,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:50,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200567, 1746201600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:51,331 - INFO - API请求耗时: 453ms
2025-06-04 08:03:51,331 - INFO - Response - Page 5
2025-06-04 08:03:51,331 - INFO - 第 5 页获取到 24 条记录
2025-06-04 08:03:51,331 - INFO - 查询完成，共获取到 424 条记录
2025-06-04 08:03:51,331 - INFO - 分段 15 查询成功，获取到 424 条记录
2025-06-04 08:03:52,347 - INFO - 查询分段 16: 2025-05-04 至 2025-05-05
2025-06-04 08:03:52,347 - INFO - 查询日期范围: 2025-05-04 至 2025-05-05，使用分页查询，每页 100 条记录
2025-06-04 08:03:52,347 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:52,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:52,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000567, 1746374400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:53,034 - INFO - API请求耗时: 687ms
2025-06-04 08:03:53,034 - INFO - Response - Page 1
2025-06-04 08:03:53,034 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:03:53,534 - INFO - Request Parameters - Page 2:
2025-06-04 08:03:53,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:53,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000567, 1746374400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:54,300 - INFO - API请求耗时: 766ms
2025-06-04 08:03:54,300 - INFO - Response - Page 2
2025-06-04 08:03:54,300 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:03:54,800 - INFO - Request Parameters - Page 3:
2025-06-04 08:03:54,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:54,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000567, 1746374400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:55,738 - INFO - API请求耗时: 937ms
2025-06-04 08:03:55,738 - INFO - Response - Page 3
2025-06-04 08:03:55,738 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:03:56,238 - INFO - Request Parameters - Page 4:
2025-06-04 08:03:56,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:56,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000567, 1746374400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:56,925 - INFO - API请求耗时: 687ms
2025-06-04 08:03:56,925 - INFO - Response - Page 4
2025-06-04 08:03:56,925 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:03:57,425 - INFO - Request Parameters - Page 5:
2025-06-04 08:03:57,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:57,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000567, 1746374400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:57,894 - INFO - API请求耗时: 469ms
2025-06-04 08:03:57,894 - INFO - Response - Page 5
2025-06-04 08:03:57,894 - INFO - 第 5 页获取到 20 条记录
2025-06-04 08:03:57,894 - INFO - 查询完成，共获取到 420 条记录
2025-06-04 08:03:57,894 - INFO - 分段 16 查询成功，获取到 420 条记录
2025-06-04 08:03:58,894 - INFO - 查询分段 17: 2025-05-06 至 2025-05-07
2025-06-04 08:03:58,894 - INFO - 查询日期范围: 2025-05-06 至 2025-05-07，使用分页查询，每页 100 条记录
2025-06-04 08:03:58,894 - INFO - Request Parameters - Page 1:
2025-06-04 08:03:58,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:03:58,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800567, 1746547200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:03:59,659 - INFO - API请求耗时: 766ms
2025-06-04 08:03:59,659 - INFO - Response - Page 1
2025-06-04 08:03:59,659 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:00,175 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:00,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:00,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800567, 1746547200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:01,034 - INFO - API请求耗时: 859ms
2025-06-04 08:04:01,034 - INFO - Response - Page 2
2025-06-04 08:04:01,050 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:01,550 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:01,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:01,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800567, 1746547200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:02,284 - INFO - API请求耗时: 734ms
2025-06-04 08:04:02,284 - INFO - Response - Page 3
2025-06-04 08:04:02,284 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:02,800 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:02,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:02,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800567, 1746547200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:03,644 - INFO - API请求耗时: 844ms
2025-06-04 08:04:03,644 - INFO - Response - Page 4
2025-06-04 08:04:03,644 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:04,159 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:04,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:04,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800567, 1746547200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:04,597 - INFO - API请求耗时: 438ms
2025-06-04 08:04:04,597 - INFO - Response - Page 5
2025-06-04 08:04:04,597 - INFO - 第 5 页获取到 12 条记录
2025-06-04 08:04:04,597 - INFO - 查询完成，共获取到 412 条记录
2025-06-04 08:04:04,597 - INFO - 分段 17 查询成功，获取到 412 条记录
2025-06-04 08:04:05,613 - INFO - 查询分段 18: 2025-05-08 至 2025-05-09
2025-06-04 08:04:05,613 - INFO - 查询日期范围: 2025-05-08 至 2025-05-09，使用分页查询，每页 100 条记录
2025-06-04 08:04:05,613 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:05,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:05,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600567, 1746720000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:06,300 - INFO - API请求耗时: 688ms
2025-06-04 08:04:06,300 - INFO - Response - Page 1
2025-06-04 08:04:06,300 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:06,816 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:06,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:06,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600567, 1746720000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:07,519 - INFO - API请求耗时: 703ms
2025-06-04 08:04:07,519 - INFO - Response - Page 2
2025-06-04 08:04:07,519 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:08,034 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:08,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:08,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600567, 1746720000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:08,831 - INFO - API请求耗时: 797ms
2025-06-04 08:04:08,831 - INFO - Response - Page 3
2025-06-04 08:04:08,847 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:09,347 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:09,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:09,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600567, 1746720000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:10,081 - INFO - API请求耗时: 734ms
2025-06-04 08:04:10,081 - INFO - Response - Page 4
2025-06-04 08:04:10,081 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:10,581 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:10,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:10,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600567, 1746720000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:11,128 - INFO - API请求耗时: 547ms
2025-06-04 08:04:11,128 - INFO - Response - Page 5
2025-06-04 08:04:11,128 - INFO - 第 5 页获取到 28 条记录
2025-06-04 08:04:11,128 - INFO - 查询完成，共获取到 428 条记录
2025-06-04 08:04:11,128 - INFO - 分段 18 查询成功，获取到 428 条记录
2025-06-04 08:04:12,144 - INFO - 查询分段 19: 2025-05-10 至 2025-05-11
2025-06-04 08:04:12,144 - INFO - 查询日期范围: 2025-05-10 至 2025-05-11，使用分页查询，每页 100 条记录
2025-06-04 08:04:12,144 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:12,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:12,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400567, 1746892800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:12,909 - INFO - API请求耗时: 766ms
2025-06-04 08:04:12,909 - INFO - Response - Page 1
2025-06-04 08:04:12,909 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:13,409 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:13,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:13,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400567, 1746892800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:14,144 - INFO - API请求耗时: 734ms
2025-06-04 08:04:14,144 - INFO - Response - Page 2
2025-06-04 08:04:14,144 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:14,659 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:14,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:14,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400567, 1746892800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:15,425 - INFO - API请求耗时: 766ms
2025-06-04 08:04:15,441 - INFO - Response - Page 3
2025-06-04 08:04:15,441 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:15,941 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:15,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:15,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400567, 1746892800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:16,722 - INFO - API请求耗时: 781ms
2025-06-04 08:04:16,722 - INFO - Response - Page 4
2025-06-04 08:04:16,722 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:17,237 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:17,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:17,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400567, 1746892800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:17,816 - INFO - API请求耗时: 578ms
2025-06-04 08:04:17,816 - INFO - Response - Page 5
2025-06-04 08:04:17,831 - INFO - 第 5 页获取到 34 条记录
2025-06-04 08:04:17,831 - INFO - 查询完成，共获取到 434 条记录
2025-06-04 08:04:17,831 - INFO - 分段 19 查询成功，获取到 434 条记录
2025-06-04 08:04:18,847 - INFO - 查询分段 20: 2025-05-12 至 2025-05-13
2025-06-04 08:04:18,847 - INFO - 查询日期范围: 2025-05-12 至 2025-05-13，使用分页查询，每页 100 条记录
2025-06-04 08:04:18,847 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:18,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:18,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200567, 1747065600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:19,534 - INFO - API请求耗时: 687ms
2025-06-04 08:04:19,534 - INFO - Response - Page 1
2025-06-04 08:04:19,534 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:20,050 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:20,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:20,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200567, 1747065600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:20,862 - INFO - API请求耗时: 812ms
2025-06-04 08:04:20,862 - INFO - Response - Page 2
2025-06-04 08:04:20,862 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:21,362 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:21,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:21,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200567, 1747065600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:22,066 - INFO - API请求耗时: 703ms
2025-06-04 08:04:22,066 - INFO - Response - Page 3
2025-06-04 08:04:22,066 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:22,581 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:22,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:22,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200567, 1747065600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:23,362 - INFO - API请求耗时: 781ms
2025-06-04 08:04:23,362 - INFO - Response - Page 4
2025-06-04 08:04:23,362 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:23,862 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:23,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:23,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200567, 1747065600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:24,347 - INFO - API请求耗时: 484ms
2025-06-04 08:04:24,347 - INFO - Response - Page 5
2025-06-04 08:04:24,347 - INFO - 第 5 页获取到 20 条记录
2025-06-04 08:04:24,347 - INFO - 查询完成，共获取到 420 条记录
2025-06-04 08:04:24,347 - INFO - 分段 20 查询成功，获取到 420 条记录
2025-06-04 08:04:25,362 - INFO - 查询分段 21: 2025-05-14 至 2025-05-15
2025-06-04 08:04:25,362 - INFO - 查询日期范围: 2025-05-14 至 2025-05-15，使用分页查询，每页 100 条记录
2025-06-04 08:04:25,362 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:25,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:25,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000567, 1747238400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:26,144 - INFO - API请求耗时: 781ms
2025-06-04 08:04:26,144 - INFO - Response - Page 1
2025-06-04 08:04:26,144 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:26,659 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:26,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:26,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000567, 1747238400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:27,331 - INFO - API请求耗时: 672ms
2025-06-04 08:04:27,347 - INFO - Response - Page 2
2025-06-04 08:04:27,347 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:27,862 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:27,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:27,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000567, 1747238400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:28,550 - INFO - API请求耗时: 687ms
2025-06-04 08:04:28,550 - INFO - Response - Page 3
2025-06-04 08:04:28,550 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:29,065 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:29,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:29,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000567, 1747238400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:29,737 - INFO - API请求耗时: 672ms
2025-06-04 08:04:29,737 - INFO - Response - Page 4
2025-06-04 08:04:29,737 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:30,253 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:30,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:30,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000567, 1747238400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:30,753 - INFO - API请求耗时: 500ms
2025-06-04 08:04:30,753 - INFO - Response - Page 5
2025-06-04 08:04:30,753 - INFO - 第 5 页获取到 20 条记录
2025-06-04 08:04:30,753 - INFO - 查询完成，共获取到 420 条记录
2025-06-04 08:04:30,753 - INFO - 分段 21 查询成功，获取到 420 条记录
2025-06-04 08:04:31,769 - INFO - 查询分段 22: 2025-05-16 至 2025-05-17
2025-06-04 08:04:31,769 - INFO - 查询日期范围: 2025-05-16 至 2025-05-17，使用分页查询，每页 100 条记录
2025-06-04 08:04:31,769 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:31,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:31,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800567, 1747411200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:32,425 - INFO - API请求耗时: 656ms
2025-06-04 08:04:32,440 - INFO - Response - Page 1
2025-06-04 08:04:32,440 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:32,956 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:32,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:32,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800567, 1747411200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:33,706 - INFO - API请求耗时: 734ms
2025-06-04 08:04:33,706 - INFO - Response - Page 2
2025-06-04 08:04:33,706 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:34,222 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:34,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:34,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800567, 1747411200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:34,878 - INFO - API请求耗时: 656ms
2025-06-04 08:04:34,878 - INFO - Response - Page 3
2025-06-04 08:04:34,878 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:35,394 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:35,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:35,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800567, 1747411200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:36,300 - INFO - API请求耗时: 906ms
2025-06-04 08:04:36,300 - INFO - Response - Page 4
2025-06-04 08:04:36,300 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:36,800 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:36,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:36,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800567, 1747411200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:37,315 - INFO - API请求耗时: 516ms
2025-06-04 08:04:37,315 - INFO - Response - Page 5
2025-06-04 08:04:37,315 - INFO - 第 5 页获取到 30 条记录
2025-06-04 08:04:37,315 - INFO - 查询完成，共获取到 430 条记录
2025-06-04 08:04:37,315 - INFO - 分段 22 查询成功，获取到 430 条记录
2025-06-04 08:04:38,331 - INFO - 查询分段 23: 2025-05-18 至 2025-05-19
2025-06-04 08:04:38,331 - INFO - 查询日期范围: 2025-05-18 至 2025-05-19，使用分页查询，每页 100 条记录
2025-06-04 08:04:38,331 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:38,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:38,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600567, 1747584000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:39,003 - INFO - API请求耗时: 672ms
2025-06-04 08:04:39,003 - INFO - Response - Page 1
2025-06-04 08:04:39,003 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:39,503 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:39,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:39,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600567, 1747584000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:40,222 - INFO - API请求耗时: 719ms
2025-06-04 08:04:40,222 - INFO - Response - Page 2
2025-06-04 08:04:40,222 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:40,722 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:40,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:40,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600567, 1747584000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:41,472 - INFO - API请求耗时: 750ms
2025-06-04 08:04:41,472 - INFO - Response - Page 3
2025-06-04 08:04:41,472 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:41,972 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:41,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:41,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600567, 1747584000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:42,565 - INFO - API请求耗时: 594ms
2025-06-04 08:04:42,565 - INFO - Response - Page 4
2025-06-04 08:04:42,565 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:43,081 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:43,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:43,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600567, 1747584000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:43,612 - INFO - API请求耗时: 531ms
2025-06-04 08:04:43,612 - INFO - Response - Page 5
2025-06-04 08:04:43,612 - INFO - 第 5 页获取到 20 条记录
2025-06-04 08:04:43,612 - INFO - 查询完成，共获取到 420 条记录
2025-06-04 08:04:43,612 - INFO - 分段 23 查询成功，获取到 420 条记录
2025-06-04 08:04:44,628 - INFO - 查询分段 24: 2025-05-20 至 2025-05-21
2025-06-04 08:04:44,628 - INFO - 查询日期范围: 2025-05-20 至 2025-05-21，使用分页查询，每页 100 条记录
2025-06-04 08:04:44,628 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:44,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:44,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400567, 1747756800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:45,315 - INFO - API请求耗时: 687ms
2025-06-04 08:04:45,315 - INFO - Response - Page 1
2025-06-04 08:04:45,315 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:45,815 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:45,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:45,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400567, 1747756800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:46,472 - INFO - API请求耗时: 656ms
2025-06-04 08:04:46,472 - INFO - Response - Page 2
2025-06-04 08:04:46,472 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:46,987 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:46,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:46,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400567, 1747756800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:47,690 - INFO - API请求耗时: 703ms
2025-06-04 08:04:47,690 - INFO - Response - Page 3
2025-06-04 08:04:47,690 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:48,206 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:48,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:48,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400567, 1747756800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:48,862 - INFO - API请求耗时: 656ms
2025-06-04 08:04:48,862 - INFO - Response - Page 4
2025-06-04 08:04:48,862 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:49,378 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:49,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:49,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400567, 1747756800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:49,847 - INFO - API请求耗时: 469ms
2025-06-04 08:04:49,847 - INFO - Response - Page 5
2025-06-04 08:04:49,847 - INFO - 第 5 页获取到 16 条记录
2025-06-04 08:04:49,847 - INFO - 查询完成，共获取到 416 条记录
2025-06-04 08:04:49,847 - INFO - 分段 24 查询成功，获取到 416 条记录
2025-06-04 08:04:50,862 - INFO - 查询分段 25: 2025-05-22 至 2025-05-23
2025-06-04 08:04:50,862 - INFO - 查询日期范围: 2025-05-22 至 2025-05-23，使用分页查询，每页 100 条记录
2025-06-04 08:04:50,862 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:50,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:50,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200567, 1747929600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:51,518 - INFO - API请求耗时: 656ms
2025-06-04 08:04:51,518 - INFO - Response - Page 1
2025-06-04 08:04:51,518 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:52,018 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:52,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:52,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200567, 1747929600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:52,737 - INFO - API请求耗时: 719ms
2025-06-04 08:04:52,737 - INFO - Response - Page 2
2025-06-04 08:04:52,737 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:53,253 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:53,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:53,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200567, 1747929600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:54,050 - INFO - API请求耗时: 797ms
2025-06-04 08:04:54,050 - INFO - Response - Page 3
2025-06-04 08:04:54,050 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:04:54,550 - INFO - Request Parameters - Page 4:
2025-06-04 08:04:54,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:54,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200567, 1747929600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:55,268 - INFO - API请求耗时: 719ms
2025-06-04 08:04:55,268 - INFO - Response - Page 4
2025-06-04 08:04:55,268 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:04:55,784 - INFO - Request Parameters - Page 5:
2025-06-04 08:04:55,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:55,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200567, 1747929600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:56,300 - INFO - API请求耗时: 516ms
2025-06-04 08:04:56,300 - INFO - Response - Page 5
2025-06-04 08:04:56,315 - INFO - 第 5 页获取到 16 条记录
2025-06-04 08:04:56,315 - INFO - 查询完成，共获取到 416 条记录
2025-06-04 08:04:56,315 - INFO - 分段 25 查询成功，获取到 416 条记录
2025-06-04 08:04:57,315 - INFO - 查询分段 26: 2025-05-24 至 2025-05-25
2025-06-04 08:04:57,315 - INFO - 查询日期范围: 2025-05-24 至 2025-05-25，使用分页查询，每页 100 条记录
2025-06-04 08:04:57,315 - INFO - Request Parameters - Page 1:
2025-06-04 08:04:57,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:57,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000567, 1748102400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:58,018 - INFO - API请求耗时: 703ms
2025-06-04 08:04:58,018 - INFO - Response - Page 1
2025-06-04 08:04:58,018 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:04:58,518 - INFO - Request Parameters - Page 2:
2025-06-04 08:04:58,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:58,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000567, 1748102400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:04:59,268 - INFO - API请求耗时: 750ms
2025-06-04 08:04:59,268 - INFO - Response - Page 2
2025-06-04 08:04:59,284 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:04:59,784 - INFO - Request Parameters - Page 3:
2025-06-04 08:04:59,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:04:59,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000567, 1748102400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:00,597 - INFO - API请求耗时: 812ms
2025-06-04 08:05:00,597 - INFO - Response - Page 3
2025-06-04 08:05:00,597 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:05:01,112 - INFO - Request Parameters - Page 4:
2025-06-04 08:05:01,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:01,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000567, 1748102400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:01,972 - INFO - API请求耗时: 859ms
2025-06-04 08:05:01,972 - INFO - Response - Page 4
2025-06-04 08:05:01,972 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:05:02,487 - INFO - Request Parameters - Page 5:
2025-06-04 08:05:02,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:02,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000567, 1748102400567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:02,972 - INFO - API请求耗时: 484ms
2025-06-04 08:05:02,972 - INFO - Response - Page 5
2025-06-04 08:05:02,972 - INFO - 第 5 页获取到 18 条记录
2025-06-04 08:05:02,972 - INFO - 查询完成，共获取到 418 条记录
2025-06-04 08:05:02,972 - INFO - 分段 26 查询成功，获取到 418 条记录
2025-06-04 08:05:04,003 - INFO - 查询分段 27: 2025-05-26 至 2025-05-27
2025-06-04 08:05:04,003 - INFO - 查询日期范围: 2025-05-26 至 2025-05-27，使用分页查询，每页 100 条记录
2025-06-04 08:05:04,003 - INFO - Request Parameters - Page 1:
2025-06-04 08:05:04,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:04,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800567, 1748275200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:04,612 - INFO - API请求耗时: 609ms
2025-06-04 08:05:04,612 - INFO - Response - Page 1
2025-06-04 08:05:04,612 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:05:05,128 - INFO - Request Parameters - Page 2:
2025-06-04 08:05:05,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:05,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800567, 1748275200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:05,987 - INFO - API请求耗时: 859ms
2025-06-04 08:05:05,987 - INFO - Response - Page 2
2025-06-04 08:05:05,987 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:05:06,503 - INFO - Request Parameters - Page 3:
2025-06-04 08:05:06,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:06,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800567, 1748275200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:07,284 - INFO - API请求耗时: 781ms
2025-06-04 08:05:07,284 - INFO - Response - Page 3
2025-06-04 08:05:07,284 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:05:07,800 - INFO - Request Parameters - Page 4:
2025-06-04 08:05:07,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:07,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800567, 1748275200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:08,534 - INFO - API请求耗时: 734ms
2025-06-04 08:05:08,534 - INFO - Response - Page 4
2025-06-04 08:05:08,534 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:05:09,034 - INFO - Request Parameters - Page 5:
2025-06-04 08:05:09,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:09,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800567, 1748275200567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:09,456 - INFO - API请求耗时: 422ms
2025-06-04 08:05:09,456 - INFO - Response - Page 5
2025-06-04 08:05:09,456 - INFO - 第 5 页获取到 10 条记录
2025-06-04 08:05:09,456 - INFO - 查询完成，共获取到 410 条记录
2025-06-04 08:05:09,456 - INFO - 分段 27 查询成功，获取到 410 条记录
2025-06-04 08:05:10,471 - INFO - 查询分段 28: 2025-05-28 至 2025-05-29
2025-06-04 08:05:10,471 - INFO - 查询日期范围: 2025-05-28 至 2025-05-29，使用分页查询，每页 100 条记录
2025-06-04 08:05:10,471 - INFO - Request Parameters - Page 1:
2025-06-04 08:05:10,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:10,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600567, 1748448000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:11,159 - INFO - API请求耗时: 687ms
2025-06-04 08:05:11,159 - INFO - Response - Page 1
2025-06-04 08:05:11,159 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:05:11,675 - INFO - Request Parameters - Page 2:
2025-06-04 08:05:11,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:11,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600567, 1748448000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:12,378 - INFO - API请求耗时: 703ms
2025-06-04 08:05:12,378 - INFO - Response - Page 2
2025-06-04 08:05:12,378 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:05:12,893 - INFO - Request Parameters - Page 3:
2025-06-04 08:05:12,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:12,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600567, 1748448000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:13,565 - INFO - API请求耗时: 672ms
2025-06-04 08:05:13,565 - INFO - Response - Page 3
2025-06-04 08:05:13,565 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:05:14,081 - INFO - Request Parameters - Page 4:
2025-06-04 08:05:14,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:14,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600567, 1748448000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:14,815 - INFO - API请求耗时: 734ms
2025-06-04 08:05:14,815 - INFO - Response - Page 4
2025-06-04 08:05:14,815 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:05:15,331 - INFO - Request Parameters - Page 5:
2025-06-04 08:05:15,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:15,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600567, 1748448000567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:15,800 - INFO - API请求耗时: 469ms
2025-06-04 08:05:15,800 - INFO - Response - Page 5
2025-06-04 08:05:15,800 - INFO - 第 5 页获取到 12 条记录
2025-06-04 08:05:15,800 - INFO - 查询完成，共获取到 412 条记录
2025-06-04 08:05:15,800 - INFO - 分段 28 查询成功，获取到 412 条记录
2025-06-04 08:05:16,815 - INFO - 查询分段 29: 2025-05-30 至 2025-05-31
2025-06-04 08:05:16,815 - INFO - 查询日期范围: 2025-05-30 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-04 08:05:16,815 - INFO - Request Parameters - Page 1:
2025-06-04 08:05:16,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:16,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400567, 1748620800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:17,581 - INFO - API请求耗时: 766ms
2025-06-04 08:05:17,581 - INFO - Response - Page 1
2025-06-04 08:05:17,581 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:05:18,096 - INFO - Request Parameters - Page 2:
2025-06-04 08:05:18,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:18,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400567, 1748620800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:19,034 - INFO - API请求耗时: 937ms
2025-06-04 08:05:19,034 - INFO - Response - Page 2
2025-06-04 08:05:19,034 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:05:19,550 - INFO - Request Parameters - Page 3:
2025-06-04 08:05:19,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:19,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400567, 1748620800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:20,206 - INFO - API请求耗时: 656ms
2025-06-04 08:05:20,206 - INFO - Response - Page 3
2025-06-04 08:05:20,206 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:05:20,706 - INFO - Request Parameters - Page 4:
2025-06-04 08:05:20,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:20,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400567, 1748620800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:21,378 - INFO - API请求耗时: 672ms
2025-06-04 08:05:21,378 - INFO - Response - Page 4
2025-06-04 08:05:21,378 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:05:21,893 - INFO - Request Parameters - Page 5:
2025-06-04 08:05:21,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:21,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400567, 1748620800567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:22,425 - INFO - API请求耗时: 531ms
2025-06-04 08:05:22,425 - INFO - Response - Page 5
2025-06-04 08:05:22,425 - INFO - 第 5 页获取到 16 条记录
2025-06-04 08:05:22,425 - INFO - 查询完成，共获取到 416 条记录
2025-06-04 08:05:22,425 - INFO - 分段 29 查询成功，获取到 416 条记录
2025-06-04 08:05:23,440 - INFO - 查询分段 30: 2025-06-01 至 2025-06-02
2025-06-04 08:05:23,440 - INFO - 查询日期范围: 2025-06-01 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-04 08:05:23,440 - INFO - Request Parameters - Page 1:
2025-06-04 08:05:23,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:23,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200567, 1748793600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:24,159 - INFO - API请求耗时: 719ms
2025-06-04 08:05:24,159 - INFO - Response - Page 1
2025-06-04 08:05:24,159 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:05:24,659 - INFO - Request Parameters - Page 2:
2025-06-04 08:05:24,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:24,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200567, 1748793600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:25,393 - INFO - API请求耗时: 734ms
2025-06-04 08:05:25,393 - INFO - Response - Page 2
2025-06-04 08:05:25,393 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:05:25,909 - INFO - Request Parameters - Page 3:
2025-06-04 08:05:25,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:25,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200567, 1748793600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:26,518 - INFO - API请求耗时: 609ms
2025-06-04 08:05:26,518 - INFO - Response - Page 3
2025-06-04 08:05:26,518 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:05:27,034 - INFO - Request Parameters - Page 4:
2025-06-04 08:05:27,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:27,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200567, 1748793600567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:27,581 - INFO - API请求耗时: 547ms
2025-06-04 08:05:27,581 - INFO - Response - Page 4
2025-06-04 08:05:27,581 - INFO - 第 4 页获取到 45 条记录
2025-06-04 08:05:27,581 - INFO - 查询完成，共获取到 345 条记录
2025-06-04 08:05:27,581 - INFO - 分段 30 查询成功，获取到 345 条记录
2025-06-04 08:05:28,596 - INFO - 查询分段 31: 2025-06-03 至 2025-06-03
2025-06-04 08:05:28,596 - INFO - 查询日期范围: 2025-06-03 至 2025-06-03，使用分页查询，每页 100 条记录
2025-06-04 08:05:28,596 - INFO - Request Parameters - Page 1:
2025-06-04 08:05:28,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:05:28,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000567, 1748966399567], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:05:29,081 - INFO - API请求耗时: 484ms
2025-06-04 08:05:29,081 - INFO - Response - Page 1
2025-06-04 08:05:29,081 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:05:29,081 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:05:29,081 - WARNING - 分段 31 查询返回空数据
2025-06-04 08:05:30,096 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 12619 条记录，失败 0 次
2025-06-04 08:05:30,096 - INFO - 成功获取宜搭日销售表单数据，共 12619 条记录
2025-06-04 08:05:30,096 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-04 08:05:30,096 - INFO - 开始对比和同步日销售数据...
2025-06-04 08:05:30,424 - INFO - 成功创建宜搭日销售数据索引，共 6336 条记录
2025-06-04 08:05:30,424 - INFO - 开始处理数衍数据，共 12896 条记录
2025-06-04 08:05:31,253 - INFO - 更新表单数据成功: FINST-6PF66691GDSVWTNYCTVNHBULRWKO28C92LEBMET
2025-06-04 08:05:31,253 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_********, 变更字段: [{'field': 'amount', 'old_value': 38388.9, 'new_value': 40557.9}, {'field': 'count', 'old_value': 51, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 38388.9, 'new_value': 40557.9}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 52}]
2025-06-04 08:05:31,706 - INFO - 更新表单数据成功: FINST-F7D66UA197SVSZMPF3TX6BZD7NRJ2UYJ2LEBM4M
2025-06-04 08:05:31,706 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_********, 变更字段: [{'field': 'amount', 'old_value': 43012.79, 'new_value': 43703.79}, {'field': 'count', 'old_value': 56, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 43012.79, 'new_value': 43703.79}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 57}]
2025-06-04 08:05:32,159 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMFC
2025-06-04 08:05:32,159 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7425.66}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7425.66}]
2025-06-04 08:05:32,674 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBML6
2025-06-04 08:05:32,674 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6898.33, 'new_value': 6903.33}, {'field': 'amount', 'old_value': 6898.33, 'new_value': 6903.33}, {'field': 'count', 'old_value': 322, 'new_value': 323}, {'field': 'onlineAmount', 'old_value': 4583.7, 'new_value': 4588.7}, {'field': 'onlineCount', 'old_value': 222, 'new_value': 223}]
2025-06-04 08:05:33,112 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBM27
2025-06-04 08:05:33,112 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_********, 变更字段: [{'field': 'amount', 'old_value': 32766.04, 'new_value': 33574.04}, {'field': 'count', 'old_value': 258, 'new_value': 259}, {'field': 'instoreAmount', 'old_value': 27422.3, 'new_value': 28230.3}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 167}]
2025-06-04 08:05:33,659 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMW8
2025-06-04 08:05:33,659 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19358.29}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19358.29}]
2025-06-04 08:05:34,096 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMOA
2025-06-04 08:05:34,096 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 5407.599999999999, 'new_value': 5415.099999999999}, {'field': 'count', 'old_value': 459, 'new_value': 462}, {'field': 'onlineAmount', 'old_value': 5577.94, 'new_value': 5585.44}, {'field': 'onlineCount', 'old_value': 428, 'new_value': 431}]
2025-06-04 08:05:34,549 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMQA
2025-06-04 08:05:34,549 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_********, 变更字段: [{'field': 'amount', 'old_value': 9161.27, 'new_value': 9164.27}, {'field': 'count', 'old_value': 482, 'new_value': 483}, {'field': 'onlineAmount', 'old_value': 157.4, 'new_value': 160.4}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 8}]
2025-06-04 08:05:34,971 - INFO - 更新表单数据成功: FINST-3PF662718TYV6IF17AZU09WVIY733GYFFRFBM42
2025-06-04 08:05:34,971 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3410.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3410.7}]
2025-06-04 08:05:35,424 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBM7E
2025-06-04 08:05:35,424 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16632.72}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16632.72}, {'field': 'amount', 'old_value': 564.0, 'new_value': 16632.72}, {'field': 'count', 'old_value': 2, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 564.0, 'new_value': 16632.72}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 51}]
2025-06-04 08:05:35,862 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBM8E
2025-06-04 08:05:35,862 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_********, 变更字段: [{'field': 'amount', 'old_value': 396.8, 'new_value': 4342.8}, {'field': 'count', 'old_value': 3, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 396.8, 'new_value': 4342.8}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 28}]
2025-06-04 08:05:36,315 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBM9E
2025-06-04 08:05:36,315 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7999.0, 'new_value': 14385.0}, {'field': 'amount', 'old_value': 7999.0, 'new_value': 14385.0}, {'field': 'count', 'old_value': 1, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 7999.0, 'new_value': 14385.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 4}]
2025-06-04 08:05:36,737 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMAE
2025-06-04 08:05:36,737 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10.8, 'new_value': 2712.94}, {'field': 'amount', 'old_value': 10.8, 'new_value': 2712.94}, {'field': 'count', 'old_value': 1, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 10.8, 'new_value': 2712.94}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 24}]
2025-06-04 08:05:37,284 - INFO - 更新表单数据成功: FINST-3PF662718TYV6IF17AZU09WVIY733GYFFRFBMD2
2025-06-04 08:05:37,299 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12440.0}]
2025-06-04 08:05:37,753 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMBE
2025-06-04 08:05:37,753 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1909.57}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1909.57}, {'field': 'amount', 'old_value': 164.2, 'new_value': 2012.97}, {'field': 'count', 'old_value': 6, 'new_value': 58}, {'field': 'instoreAmount', 'old_value': 164.2, 'new_value': 2012.97}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 58}]
2025-06-04 08:05:38,221 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMCE
2025-06-04 08:05:38,221 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'amount', 'old_value': 256.0, 'new_value': 498.7}, {'field': 'count', 'old_value': 1, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 256.0, 'new_value': 498.7}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 6}]
2025-06-04 08:05:38,753 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMDE
2025-06-04 08:05:38,753 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6017.18}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6017.18}, {'field': 'amount', 'old_value': 106.0, 'new_value': 2608.2999999999997}, {'field': 'count', 'old_value': 2, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 106.0, 'new_value': 2528.6}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 23}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 145.5}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 4}]
2025-06-04 08:05:39,190 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMEE
2025-06-04 08:05:39,190 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 14446.38}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 14446.38}, {'field': 'amount', 'old_value': 3027.0, 'new_value': 16659.8}, {'field': 'count', 'old_value': 9, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 3027.0, 'new_value': 16659.8}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 85}]
2025-06-04 08:05:39,643 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMFE
2025-06-04 08:05:39,643 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16016.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16016.4}, {'field': 'amount', 'old_value': 1590.4, 'new_value': 6702.67}, {'field': 'count', 'old_value': 17, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 431.0, 'new_value': 3455.67}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 20}, {'field': 'onlineAmount', 'old_value': 1159.4, 'new_value': 3247.0}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 39}]
2025-06-04 08:05:40,143 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBM7W
2025-06-04 08:05:40,143 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6653.58}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6653.58}, {'field': 'amount', 'old_value': 179.0, 'new_value': 2820.9}, {'field': 'count', 'old_value': 1, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 179.0, 'new_value': 2820.9}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 10}]
2025-06-04 08:05:40,674 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBM8W
2025-06-04 08:05:40,674 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 375.18, 'new_value': 1431.21}, {'field': 'amount', 'old_value': 375.18, 'new_value': 1431.21}, {'field': 'count', 'old_value': 18, 'new_value': 75}, {'field': 'instoreAmount', 'old_value': 408.7, 'new_value': 1599.5}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 75}]
2025-06-04 08:05:41,003 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBM9W
2025-06-04 08:05:41,003 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10901.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10901.0}, {'field': 'amount', 'old_value': 158.0, 'new_value': 12902.0}, {'field': 'count', 'old_value': 1, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 158.0, 'new_value': 12902.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 46}]
2025-06-04 08:05:41,487 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMAW
2025-06-04 08:05:41,487 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6138.96}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6138.96}, {'field': 'amount', 'old_value': 198.0, 'new_value': 6289.71}, {'field': 'count', 'old_value': 1, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 198.0, 'new_value': 5798.18}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 25}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 491.53}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 3}]
2025-06-04 08:05:41,924 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMBW
2025-06-04 08:05:41,924 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1561.0, 'new_value': 6706.99}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6683.68}, {'field': 'amount', 'old_value': 1561.0, 'new_value': 6706.99}, {'field': 'count', 'old_value': 16, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 1561.0, 'new_value': 6597.62}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 80}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 109.37}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-06-04 08:05:42,377 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMCW
2025-06-04 08:05:42,377 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 465.28, 'new_value': 5544.18}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5544.18}, {'field': 'amount', 'old_value': 465.28000000000003, 'new_value': 3773.26}, {'field': 'count', 'old_value': 14, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 506.18, 'new_value': 3735.11}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 92}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 208.65}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 7}]
2025-06-04 08:05:42,815 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMDW
2025-06-04 08:05:42,815 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 300.8, 'new_value': 6262.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6262.1}, {'field': 'amount', 'old_value': 300.79999999999995, 'new_value': 6262.1}, {'field': 'count', 'old_value': 2, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 538.8, 'new_value': 6500.1}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 6}]
2025-06-04 08:05:43,237 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMEW
2025-06-04 08:05:43,237 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5811.16}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5811.16}, {'field': 'amount', 'old_value': 268.0, 'new_value': 5507.16}, {'field': 'count', 'old_value': 3, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 268.0, 'new_value': 5507.16}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 21}]
2025-06-04 08:05:43,627 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMFW
2025-06-04 08:05:43,627 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16762.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16762.0}, {'field': 'amount', 'old_value': 1152.0, 'new_value': 7146.0}, {'field': 'count', 'old_value': 3, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 1152.0, 'new_value': 7146.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 20}]
2025-06-04 08:05:44,174 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMGW
2025-06-04 08:05:44,174 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 635.2, 'new_value': 5280.8}, {'field': 'amount', 'old_value': 635.2, 'new_value': 5280.8}, {'field': 'count', 'old_value': 18, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 635.2, 'new_value': 5280.8}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 90}]
2025-06-04 08:05:44,596 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25Y73LEBMHW
2025-06-04 08:05:44,596 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_********, 变更字段: [{'field': 'amount', 'old_value': 196.0, 'new_value': 1305.89}, {'field': 'count', 'old_value': 2, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 196.0, 'new_value': 1305.89}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 23}]
2025-06-04 08:05:45,049 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMIW
2025-06-04 08:05:45,049 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2390.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2390.5}, {'field': 'amount', 'old_value': 351.21, 'new_value': 2410.1}, {'field': 'count', 'old_value': 15, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 212.0, 'new_value': 1620.7}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 55}, {'field': 'onlineAmount', 'old_value': 139.21, 'new_value': 822.3}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 37}]
2025-06-04 08:05:45,549 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMJW
2025-06-04 08:05:45,549 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3562.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3562.0}, {'field': 'amount', 'old_value': 518.6500000000001, 'new_value': 3541.9300000000003}, {'field': 'count', 'old_value': 36, 'new_value': 218}, {'field': 'instoreAmount', 'old_value': 184.77, 'new_value': 1881.64}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 106}, {'field': 'onlineAmount', 'old_value': 341.81, 'new_value': 1701.22}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 112}]
2025-06-04 08:05:45,971 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMKW
2025-06-04 08:05:45,971 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 169.0, 'new_value': 2229.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2229.0}, {'field': 'amount', 'old_value': 169.0, 'new_value': 2229.0}, {'field': 'count', 'old_value': 5, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 169.0, 'new_value': 2229.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 49}]
2025-06-04 08:05:46,471 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMLW
2025-06-04 08:05:46,471 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7969.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7969.3}, {'field': 'amount', 'old_value': 486.86, 'new_value': 3715.34}, {'field': 'count', 'old_value': 34, 'new_value': 169}, {'field': 'instoreAmount', 'old_value': 502.76, 'new_value': 3768.34}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 169}]
2025-06-04 08:05:46,924 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMMW
2025-06-04 08:05:46,924 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10835.32}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10835.32}, {'field': 'amount', 'old_value': 864.0, 'new_value': 6738.8}, {'field': 'count', 'old_value': 5, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 864.0, 'new_value': 6838.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 31}]
2025-06-04 08:05:47,362 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMNW
2025-06-04 08:05:47,362 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2115.88}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2115.88}, {'field': 'amount', 'old_value': 189.6, 'new_value': 997.1099999999999}, {'field': 'count', 'old_value': 12, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 46.0, 'new_value': 294.59}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 17}, {'field': 'onlineAmount', 'old_value': 143.6, 'new_value': 743.02}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 44}]
2025-06-04 08:05:47,815 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMOW
2025-06-04 08:05:47,815 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1169.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1169.0}, {'field': 'amount', 'old_value': 300.0, 'new_value': 708.0}, {'field': 'count', 'old_value': 9, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 300.0, 'new_value': 708.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 29}]
2025-06-04 08:05:48,221 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMPW
2025-06-04 08:05:48,221 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5841.2}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5841.2}, {'field': 'amount', 'old_value': 543.93, 'new_value': 1334.01}, {'field': 'count', 'old_value': 11, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 515.69, 'new_value': 1141.59}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 25}, {'field': 'onlineAmount', 'old_value': 28.24, 'new_value': 192.42}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 5}]
2025-06-04 08:05:48,752 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMQW
2025-06-04 08:05:48,752 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 180.42, 'new_value': 2831.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2831.74}, {'field': 'amount', 'old_value': 180.42, 'new_value': 701.35}, {'field': 'count', 'old_value': 9, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 50.0, 'new_value': 190.8}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 4}, {'field': 'onlineAmount', 'old_value': 130.42, 'new_value': 510.55}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 22}]
2025-06-04 08:05:49,190 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMRW
2025-06-04 08:05:49,190 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2300.1, 'new_value': 10592.1}, {'field': 'amount', 'old_value': 2300.1, 'new_value': 10592.1}, {'field': 'count', 'old_value': 64, 'new_value': 289}, {'field': 'instoreAmount', 'old_value': 1931.2, 'new_value': 10023.3}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 275}, {'field': 'onlineAmount', 'old_value': 368.9, 'new_value': 584.9}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 14}]
2025-06-04 08:05:49,737 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMSW
2025-06-04 08:05:49,737 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6644.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6644.4}, {'field': 'amount', 'old_value': 239.49, 'new_value': 443.99}, {'field': 'count', 'old_value': 12, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 254.19, 'new_value': 458.69}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 24}]
2025-06-04 08:05:50,143 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMTW
2025-06-04 08:05:50,143 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 17274.62}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 17274.62}, {'field': 'amount', 'old_value': 7946.0, 'new_value': 23353.3}, {'field': 'count', 'old_value': 23, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 7946.0, 'new_value': 23353.3}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 101}]
2025-06-04 08:05:50,596 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMUW
2025-06-04 08:05:50,596 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'amount', 'old_value': 190.0, 'new_value': 4298.0}, {'field': 'count', 'old_value': 1, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 190.0, 'new_value': 4298.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 4}]
2025-06-04 08:05:51,096 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMVW
2025-06-04 08:05:51,096 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2043.12, 'new_value': 5617.36}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5186.04}, {'field': 'amount', 'old_value': 2043.12, 'new_value': 5617.36}, {'field': 'count', 'old_value': 70, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 1731.62, 'new_value': 5096.36}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 167}, {'field': 'onlineAmount', 'old_value': 311.5, 'new_value': 521.0}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 16}]
2025-06-04 08:05:51,581 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMWW
2025-06-04 08:05:51,581 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 397.3, 'new_value': 6198.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6198.7}, {'field': 'amount', 'old_value': 397.3, 'new_value': 3898.25}, {'field': 'count', 'old_value': 7, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 397.3, 'new_value': 4144.25}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 56}]
2025-06-04 08:05:52,065 - INFO - 更新表单数据成功: FINST-FPB66VB1EISVN61MEKFO65HXL71P31NIFRFBMRL
2025-06-04 08:05:52,065 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7119.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7119.3}]
2025-06-04 08:05:52,549 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMXW
2025-06-04 08:05:52,549 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 78.0, 'new_value': 63543.37}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 63543.37}, {'field': 'amount', 'old_value': 78.0, 'new_value': -19105.240000000005}, {'field': 'count', 'old_value': 2, 'new_value': 68}, {'field': 'instoreAmount', 'old_value': 621.0, 'new_value': 43164.77}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 68}]
2025-06-04 08:05:53,002 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMYW
2025-06-04 08:05:53,002 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7337.95}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7337.95}, {'field': 'amount', 'old_value': 1687.0, 'new_value': 8003.0}, {'field': 'count', 'old_value': 11, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 1671.0, 'new_value': 7880.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 56}, {'field': 'onlineAmount', 'old_value': 16.0, 'new_value': 123.0}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 3}]
2025-06-04 08:05:53,377 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMZW
2025-06-04 08:05:53,377 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2260.48}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2260.48}, {'field': 'amount', 'old_value': 52.7, 'new_value': 2142.8}, {'field': 'count', 'old_value': 1, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 2090.1}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 15}]
2025-06-04 08:05:53,924 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM0X
2025-06-04 08:05:53,940 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3200.83}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3200.83}, {'field': 'amount', 'old_value': 218.1, 'new_value': 2532.43}, {'field': 'count', 'old_value': 8, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 218.1, 'new_value': 2456.63}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 74}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 75.8}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 2}]
2025-06-04 08:05:54,362 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM1X
2025-06-04 08:05:54,362 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2890.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2890.0}, {'field': 'amount', 'old_value': 800.0, 'new_value': 2800.0}, {'field': 'count', 'old_value': 2, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 800.0, 'new_value': 2800.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 16}]
2025-06-04 08:05:54,799 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM2X
2025-06-04 08:05:54,799 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 100.0, 'new_value': 3351.0}, {'field': 'amount', 'old_value': 100.0, 'new_value': 3351.0}, {'field': 'count', 'old_value': 2, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 100.0, 'new_value': 3351.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 41}]
2025-06-04 08:05:55,268 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM3X
2025-06-04 08:05:55,268 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3172.92}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3172.92}, {'field': 'amount', 'old_value': 272.0, 'new_value': 2313.5}, {'field': 'count', 'old_value': 3, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 272.0, 'new_value': 2313.5}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 22}]
2025-06-04 08:05:55,706 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM4X
2025-06-04 08:05:55,706 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 611.9, 'new_value': 2362.01}, {'field': 'amount', 'old_value': 611.9, 'new_value': 2362.01}, {'field': 'count', 'old_value': 34, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 86.0, 'new_value': 562.2}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 27}, {'field': 'onlineAmount', 'old_value': 525.9, 'new_value': 1833.81}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 118}]
2025-06-04 08:05:56,159 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM5X
2025-06-04 08:05:56,159 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1374.07}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1374.07}, {'field': 'amount', 'old_value': 82.6, 'new_value': 877.39}, {'field': 'count', 'old_value': 5, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 82.6, 'new_value': 877.39}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 32}]
2025-06-04 08:05:56,596 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM6X
2025-06-04 08:05:56,596 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 185.84, 'new_value': 2189.15}, {'field': 'amount', 'old_value': 185.84, 'new_value': 2189.15}, {'field': 'count', 'old_value': 9, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 89.51, 'new_value': 1169.65}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 52}, {'field': 'onlineAmount', 'old_value': 96.33, 'new_value': 1027.22}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 46}]
2025-06-04 08:05:57,034 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM7X
2025-06-04 08:05:57,034 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2651.65}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2651.65}, {'field': 'amount', 'old_value': 984.69, 'new_value': 2563.69}, {'field': 'count', 'old_value': 44, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 761.0, 'new_value': 2288.5}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 110}, {'field': 'onlineAmount', 'old_value': 223.69, 'new_value': 275.19}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 11}]
2025-06-04 08:05:57,456 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM8X
2025-06-04 08:05:57,456 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 314.85, 'new_value': 1905.6}, {'field': 'amount', 'old_value': 314.85, 'new_value': 1905.6000000000001}, {'field': 'count', 'old_value': 25, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 314.85, 'new_value': 1913.63}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 125}]
2025-06-04 08:05:57,877 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM9X
2025-06-04 08:05:57,877 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4105.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4105.9}, {'field': 'amount', 'old_value': 829.0, 'new_value': 4105.9}, {'field': 'count', 'old_value': 3, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 829.0, 'new_value': 4343.7}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 24}]
2025-06-04 08:05:58,315 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMAX
2025-06-04 08:05:58,315 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11258.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11258.3}, {'field': 'amount', 'old_value': 3018.5, 'new_value': 8962.990000000002}, {'field': 'count', 'old_value': 68, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 2766.8, 'new_value': 8499.39}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 239}, {'field': 'onlineAmount', 'old_value': 251.7, 'new_value': 555.4}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 10}]
2025-06-04 08:05:58,784 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMBX
2025-06-04 08:05:58,784 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2500.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2500.0}, {'field': 'amount', 'old_value': 1064.6, 'new_value': 2383.3}, {'field': 'count', 'old_value': 6, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 1064.6, 'new_value': 2383.3}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 18}]
2025-06-04 08:05:59,237 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMCX
2025-06-04 08:05:59,237 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1142.6, 'new_value': 3497.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 104.8}, {'field': 'amount', 'old_value': 1142.6, 'new_value': 3497.4}, {'field': 'count', 'old_value': 4, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 1142.6, 'new_value': 3497.4}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 15}]
2025-06-04 08:05:59,674 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMDX
2025-06-04 08:05:59,674 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3178.12, 'new_value': 7176.74}, {'field': 'amount', 'old_value': 3178.12, 'new_value': 7176.74}, {'field': 'count', 'old_value': 35, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 2867.83, 'new_value': 6325.45}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 110}, {'field': 'onlineAmount', 'old_value': 310.29, 'new_value': 851.29}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 20}]
2025-06-04 08:06:00,143 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMEX
2025-06-04 08:06:00,143 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 658.7, 'new_value': 3147.76}, {'field': 'count', 'old_value': 42, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 27.0, 'new_value': 307.86}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 39}, {'field': 'onlineAmount', 'old_value': 631.7, 'new_value': 2953.0}, {'field': 'onlineCount', 'old_value': 35, 'new_value': 159}]
2025-06-04 08:06:00,596 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMFX
2025-06-04 08:06:00,596 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13147.85}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13147.85}, {'field': 'amount', 'old_value': 327.0, 'new_value': 11038.0}, {'field': 'count', 'old_value': 4, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 327.0, 'new_value': 12065.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 56}]
2025-06-04 08:06:00,987 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMGX
2025-06-04 08:06:00,987 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 294.48, 'new_value': 2004.89}, {'field': 'amount', 'old_value': 294.48, 'new_value': 2004.89}, {'field': 'count', 'old_value': 18, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 110.0, 'new_value': 1094.3}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 57}, {'field': 'onlineAmount', 'old_value': 184.48, 'new_value': 973.23}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 64}]
2025-06-04 08:06:01,424 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMHX
2025-06-04 08:06:01,424 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 446.6, 'new_value': 9801.56}, {'field': 'amount', 'old_value': 446.6, 'new_value': 9801.56}, {'field': 'count', 'old_value': 10, 'new_value': 231}, {'field': 'instoreAmount', 'old_value': 446.6, 'new_value': 9801.56}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 231}]
2025-06-04 08:06:01,831 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMIX
2025-06-04 08:06:01,831 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 172.1, 'new_value': 20464.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 20464.74}, {'field': 'amount', 'old_value': 172.1, 'new_value': 8645.699999999999}, {'field': 'count', 'old_value': 6, 'new_value': 157}, {'field': 'instoreAmount', 'old_value': 172.1, 'new_value': 8667.4}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 157}]
2025-06-04 08:06:02,284 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMJX
2025-06-04 08:06:02,284 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1559.0, 'new_value': 5774.61}, {'field': 'amount', 'old_value': 1559.0, 'new_value': 5774.610000000001}, {'field': 'count', 'old_value': 90, 'new_value': 294}, {'field': 'instoreAmount', 'old_value': 219.9, 'new_value': 1506.89}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 65}, {'field': 'onlineAmount', 'old_value': 1362.1, 'new_value': 4339.62}, {'field': 'onlineCount', 'old_value': 82, 'new_value': 229}]
2025-06-04 08:06:02,690 - INFO - 更新表单数据成功: FINST-FPB66VB1EISVN61MEKFO65HXL71P32NIFRFBMNM
2025-06-04 08:06:02,690 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7461.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7461.0}]
2025-06-04 08:06:03,252 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMKX
2025-06-04 08:06:03,252 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16301.53}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16301.53}, {'field': 'amount', 'old_value': 727.96, 'new_value': 7431.94}, {'field': 'count', 'old_value': 26, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 308.97, 'new_value': 5731.38}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 40}, {'field': 'onlineAmount', 'old_value': 418.99, 'new_value': 1700.56}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 75}]
2025-06-04 08:06:03,674 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMLX
2025-06-04 08:06:03,674 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16343.41}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16343.41}, {'field': 'amount', 'old_value': 358.02, 'new_value': 16013.28}, {'field': 'count', 'old_value': 3, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 282.73, 'new_value': 15059.25}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 81}, {'field': 'onlineAmount', 'old_value': 75.29, 'new_value': 954.03}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 26}]
2025-06-04 08:06:04,080 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMMX
2025-06-04 08:06:04,080 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 23307.34}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 23307.34}, {'field': 'amount', 'old_value': 246.2, 'new_value': 6314.7}, {'field': 'count', 'old_value': 2, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 246.2, 'new_value': 6229.1}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 27}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 85.6}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-06-04 08:06:04,565 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMNX
2025-06-04 08:06:04,565 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13441.79}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13441.79}, {'field': 'amount', 'old_value': 437.0, 'new_value': 17939.76}, {'field': 'count', 'old_value': 2, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 437.0, 'new_value': 17939.76}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 89}]
2025-06-04 08:06:04,987 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMOX
2025-06-04 08:06:04,987 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16637.86}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16637.86}, {'field': 'amount', 'old_value': 12.0, 'new_value': -15992.94}, {'field': 'count', 'old_value': 1, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 285.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 15}, {'field': 'onlineAmount', 'old_value': 12.0, 'new_value': 609.06}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 25}]
2025-06-04 08:06:05,393 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMPX
2025-06-04 08:06:05,393 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 694.0, 'new_value': 6769.0}, {'field': 'amount', 'old_value': 694.0, 'new_value': 6769.0}, {'field': 'count', 'old_value': 2, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 694.0, 'new_value': 6769.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 38}]
2025-06-04 08:06:05,909 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMQX
2025-06-04 08:06:05,909 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 26724.46}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 26724.46}, {'field': 'amount', 'old_value': 457.85, 'new_value': 26778.96}, {'field': 'count', 'old_value': 15, 'new_value': 210}, {'field': 'instoreAmount', 'old_value': 150.0, 'new_value': 24674.25}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 126}, {'field': 'onlineAmount', 'old_value': 307.85, 'new_value': 2130.21}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 84}]
2025-06-04 08:06:06,362 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMRX
2025-06-04 08:06:06,362 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 27573.18}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 27573.18}, {'field': 'amount', 'old_value': 2082.47, 'new_value': 27717.73}, {'field': 'count', 'old_value': 33, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 799.3, 'new_value': 20490.86}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 134}, {'field': 'onlineAmount', 'old_value': 1283.17, 'new_value': 7294.21}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 128}]
2025-06-04 08:06:06,752 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMSX
2025-06-04 08:06:06,752 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 42005.77}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 42005.77}, {'field': 'amount', 'old_value': 4095.4, 'new_value': 49836.01}, {'field': 'count', 'old_value': 36, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 487.7, 'new_value': 29861.5}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 149}, {'field': 'onlineAmount', 'old_value': 3607.7, 'new_value': 20596.5}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 190}]
2025-06-04 08:06:07,174 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMTX
2025-06-04 08:06:07,174 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1727.6, 'new_value': 45243.2}, {'field': 'amount', 'old_value': 1727.6, 'new_value': 45243.2}, {'field': 'count', 'old_value': 13, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 1727.6, 'new_value': 45243.2}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 314}]
2025-06-04 08:06:07,596 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMUX
2025-06-04 08:06:07,596 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 14233.53}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 14233.53}, {'field': 'amount', 'old_value': 1031.61, 'new_value': 19244.91}, {'field': 'count', 'old_value': 7, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 714.0, 'new_value': 18072.8}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 70}, {'field': 'onlineAmount', 'old_value': 317.61, 'new_value': 1377.31}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 21}]
2025-06-04 08:06:08,237 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMVX
2025-06-04 08:06:08,237 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 49697.99}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 49697.99}, {'field': 'amount', 'old_value': 2914.8, 'new_value': 55273.01}, {'field': 'count', 'old_value': 33, 'new_value': 327}, {'field': 'instoreAmount', 'old_value': 803.6, 'new_value': 42465.32}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 185}, {'field': 'onlineAmount', 'old_value': 2111.2, 'new_value': 12829.9}, {'field': 'onlineCount', 'old_value': 30, 'new_value': 142}]
2025-06-04 08:06:08,659 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMWX
2025-06-04 08:06:08,659 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 21229.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 21229.9}, {'field': 'amount', 'old_value': 191.9, 'new_value': 19629.1}, {'field': 'count', 'old_value': 2, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 191.9, 'new_value': 19870.1}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 109}]
2025-06-04 08:06:09,112 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMXX
2025-06-04 08:06:09,112 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 18378.14}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 18378.14}, {'field': 'amount', 'old_value': 1571.7, 'new_value': 18027.61}, {'field': 'count', 'old_value': 17, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 13763.21}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 75}, {'field': 'onlineAmount', 'old_value': 1571.7, 'new_value': 4264.4}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 71}]
2025-06-04 08:06:09,549 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMYX
2025-06-04 08:06:09,565 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5266.27}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5266.27}, {'field': 'amount', 'old_value': 261.8, 'new_value': 2474.6}, {'field': 'count', 'old_value': 24, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 14.5, 'new_value': 675.9}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 22}, {'field': 'onlineAmount', 'old_value': 247.3, 'new_value': 1798.7}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 136}]
2025-06-04 08:06:10,018 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMZX
2025-06-04 08:06:10,018 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_********, 变更字段: [{'field': 'count', 'old_value': 2, 'new_value': 7}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 7}]
2025-06-04 08:06:10,455 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM0Y
2025-06-04 08:06:10,455 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4065.19}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4065.19}, {'field': 'amount', 'old_value': 919.5, 'new_value': 5971.77}, {'field': 'count', 'old_value': 36, 'new_value': 277}, {'field': 'instoreAmount', 'old_value': 264.5, 'new_value': 3185.87}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 164}, {'field': 'onlineAmount', 'old_value': 655.0, 'new_value': 2811.6}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 113}]
2025-06-04 08:06:10,799 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM1Y
2025-06-04 08:06:10,799 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 34937.62}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 34937.62}, {'field': 'amount', 'old_value': 1688.67, 'new_value': 34801.89}, {'field': 'count', 'old_value': 58, 'new_value': 329}, {'field': 'instoreAmount', 'old_value': 651.45, 'new_value': 30955.18}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 149}, {'field': 'onlineAmount', 'old_value': 1069.32, 'new_value': 4058.77}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 180}]
2025-06-04 08:06:11,221 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM2Y
2025-06-04 08:06:11,221 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4393.43}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4393.43}, {'field': 'amount', 'old_value': 164.5, 'new_value': 2048.03}, {'field': 'count', 'old_value': 25, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 184.8, 'new_value': 2113.13}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 197}]
2025-06-04 08:06:11,690 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM3Y
2025-06-04 08:06:11,690 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1058.0, 'new_value': 7842.0}, {'field': 'amount', 'old_value': 1058.0, 'new_value': 7842.0}, {'field': 'count', 'old_value': 4, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 1058.0, 'new_value': 8190.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 56}]
2025-06-04 08:06:12,205 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM4Y
2025-06-04 08:06:12,205 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1009.92, 'new_value': 18477.64}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16677.8}, {'field': 'amount', 'old_value': 1009.92, 'new_value': 18477.64}, {'field': 'count', 'old_value': 10, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 1009.92, 'new_value': 18477.64}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 87}]
2025-06-04 08:06:12,721 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM5Y
2025-06-04 08:06:12,721 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1734.0, 'new_value': 2692.0}, {'field': 'amount', 'old_value': 1734.0, 'new_value': 2692.0}, {'field': 'count', 'old_value': 1, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1734.0, 'new_value': 2692.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 5}]
2025-06-04 08:06:13,127 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM6Y
2025-06-04 08:06:13,127 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 54567.57}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 54567.57}, {'field': 'amount', 'old_value': 517.54, 'new_value': 5543.26}, {'field': 'count', 'old_value': 5, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 282.7, 'new_value': 4794.48}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 37}, {'field': 'onlineAmount', 'old_value': 234.84, 'new_value': 748.78}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 8}]
2025-06-04 08:06:13,565 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM7Y
2025-06-04 08:06:13,565 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 464.0, 'new_value': 2761.8}, {'field': 'amount', 'old_value': 464.0, 'new_value': 2761.8}, {'field': 'count', 'old_value': 3, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 464.0, 'new_value': 2869.8}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 12}]
2025-06-04 08:06:14,159 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM8Y
2025-06-04 08:06:14,159 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_********, 变更字段: [{'field': 'amount', 'old_value': 236.7, 'new_value': 414.4}, {'field': 'count', 'old_value': 1, 'new_value': 3}, {'field': 'onlineAmount', 'old_value': 236.7, 'new_value': 414.4}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 3}]
2025-06-04 08:06:14,580 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBM9Y
2025-06-04 08:06:14,580 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11336.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11336.6}, {'field': 'amount', 'old_value': 669.5999999999999, 'new_value': 10503.4}, {'field': 'count', 'old_value': 2, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 1048.6, 'new_value': 10882.4}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 16}]
2025-06-04 08:06:15,065 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMAY
2025-06-04 08:06:15,065 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1008.0, 'new_value': 4496.0}, {'field': 'amount', 'old_value': 1008.0, 'new_value': 4496.0}, {'field': 'count', 'old_value': 1, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1008.0, 'new_value': 4496.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 5}]
2025-06-04 08:06:15,580 - INFO - 更新表单数据成功: FINST-FPB66VB1EISVN61MEKFO65HXL71P32NIFRFBMNN
2025-06-04 08:06:15,580 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 718.0, 'new_value': 1002.0}, {'field': 'amount', 'old_value': 718.0, 'new_value': 1002.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 718.0, 'new_value': 1002.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-04 08:06:16,002 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMBY
2025-06-04 08:06:16,002 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1350.0, 'new_value': 4162.0}, {'field': 'amount', 'old_value': 1350.0, 'new_value': 4162.0}, {'field': 'count', 'old_value': 1, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 1350.0, 'new_value': 4162.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 4}]
2025-06-04 08:06:16,471 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMCY
2025-06-04 08:06:16,471 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9472.2}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9472.2}, {'field': 'amount', 'old_value': 498.5, 'new_value': 9221.699999999999}, {'field': 'count', 'old_value': 2, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 498.5, 'new_value': 9655.3}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 22}]
2025-06-04 08:06:16,940 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMDY
2025-06-04 08:06:16,940 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 25341.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 25341.0}, {'field': 'amount', 'old_value': 1411.0, 'new_value': 26071.0}, {'field': 'count', 'old_value': 1, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 1411.0, 'new_value': 26071.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 14}]
2025-06-04 08:06:17,393 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMEY
2025-06-04 08:06:17,393 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5267.26}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5267.26}, {'field': 'amount', 'old_value': 291.44, 'new_value': 5480.44}, {'field': 'count', 'old_value': 11, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 291.44, 'new_value': 5442.54}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 187}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 37.9}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-06-04 08:06:17,862 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMFY
2025-06-04 08:06:17,862 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2942.57}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2942.57}, {'field': 'amount', 'old_value': 588.24, 'new_value': 3329.27}, {'field': 'count', 'old_value': 22, 'new_value': 126}, {'field': 'instoreAmount', 'old_value': 106.0, 'new_value': 1484.57}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 55}, {'field': 'onlineAmount', 'old_value': 482.24, 'new_value': 1932.7}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 71}]
2025-06-04 08:06:18,284 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMGY
2025-06-04 08:06:18,284 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10851.64}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10851.64}, {'field': 'amount', 'old_value': 991.4, 'new_value': 9376.92}, {'field': 'count', 'old_value': 18, 'new_value': 275}, {'field': 'instoreAmount', 'old_value': 737.6, 'new_value': 6057.12}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 219}, {'field': 'onlineAmount', 'old_value': 253.8, 'new_value': 3634.8}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 56}]
2025-06-04 08:06:18,674 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMHY
2025-06-04 08:06:18,674 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 734.12, 'new_value': 4754.58}, {'field': 'count', 'old_value': 56, 'new_value': 340}, {'field': 'instoreAmount', 'old_value': 32.0, 'new_value': 355.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 27}, {'field': 'onlineAmount', 'old_value': 728.12, 'new_value': 4534.74}, {'field': 'onlineCount', 'old_value': 55, 'new_value': 313}]
2025-06-04 08:06:19,159 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMIY
2025-06-04 08:06:19,159 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_********, 变更字段: [{'field': 'amount', 'old_value': 869.18, 'new_value': 7284.35}, {'field': 'count', 'old_value': 65, 'new_value': 396}, {'field': 'instoreAmount', 'old_value': 869.18, 'new_value': 7583.25}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 392}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 87.5}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 4}]
2025-06-04 08:06:19,643 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMJY
2025-06-04 08:06:19,643 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 591.21, 'new_value': 4324.55}, {'field': 'amount', 'old_value': 591.21, 'new_value': 4324.55}, {'field': 'count', 'old_value': 12, 'new_value': 159}, {'field': 'instoreAmount', 'old_value': 555.11, 'new_value': 4059.45}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 155}, {'field': 'onlineAmount', 'old_value': 36.1, 'new_value': 265.1}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 4}]
2025-06-04 08:06:20,158 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMKY
2025-06-04 08:06:20,158 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8462.72}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8462.72}, {'field': 'amount', 'old_value': 533.9, 'new_value': 4890.4800000000005}, {'field': 'count', 'old_value': 24, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 56.0, 'new_value': 907.88}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 53}, {'field': 'onlineAmount', 'old_value': 492.6, 'new_value': 4018.0}, {'field': 'onlineCount', 'old_value': 21, 'new_value': 153}]
2025-06-04 08:06:20,659 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMLY
2025-06-04 08:06:20,659 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7583.34}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7583.34}, {'field': 'amount', 'old_value': 487.3, 'new_value': 1444.1}, {'field': 'count', 'old_value': 11, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 487.3, 'new_value': 1444.1}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 43}]
2025-06-04 08:06:21,049 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMMY
2025-06-04 08:06:21,049 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 587.59, 'new_value': 4810.04}, {'field': 'amount', 'old_value': 587.59, 'new_value': 4810.04}, {'field': 'count', 'old_value': 33, 'new_value': 279}, {'field': 'instoreAmount', 'old_value': 305.87, 'new_value': 2311.7}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 160}, {'field': 'onlineAmount', 'old_value': 281.72, 'new_value': 2498.34}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 119}]
2025-06-04 08:06:21,565 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMNY
2025-06-04 08:06:21,565 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13057.64}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13057.64}, {'field': 'amount', 'old_value': 1315.9, 'new_value': 12990.8}, {'field': 'count', 'old_value': 40, 'new_value': 399}, {'field': 'instoreAmount', 'old_value': 1315.9, 'new_value': 13024.8}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 399}]
2025-06-04 08:06:21,987 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMOY
2025-06-04 08:06:21,987 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'amount', 'old_value': 917.64, 'new_value': 6626.2699999999995}, {'field': 'count', 'old_value': 62, 'new_value': 456}, {'field': 'instoreAmount', 'old_value': 636.79, 'new_value': 4974.09}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 322}, {'field': 'onlineAmount', 'old_value': 323.84, 'new_value': 1802.1}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 134}]
2025-06-04 08:06:22,533 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMPY
2025-06-04 08:06:22,533 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_********, 变更字段: [{'field': 'amount', 'old_value': -29.0, 'new_value': -1300.0}, {'field': 'count', 'old_value': 1, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 280.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 2}, {'field': 'onlineAmount', 'old_value': 18.0, 'new_value': 87.0}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 4}]
2025-06-04 08:06:23,033 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMQY
2025-06-04 08:06:23,033 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5321.6, 'new_value': 23247.86}, {'field': 'amount', 'old_value': 5321.6, 'new_value': 23247.86}, {'field': 'count', 'old_value': 139, 'new_value': 510}, {'field': 'instoreAmount', 'old_value': 3846.5, 'new_value': 17791.36}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 361}, {'field': 'onlineAmount', 'old_value': 1475.1, 'new_value': 5456.5}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 149}]
2025-06-04 08:06:23,424 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMRY
2025-06-04 08:06:23,424 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 37343.06}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 37343.06}, {'field': 'amount', 'old_value': 654.78, 'new_value': 24565.29}, {'field': 'count', 'old_value': 20, 'new_value': 453}, {'field': 'instoreAmount', 'old_value': 582.6, 'new_value': 23381.37}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 423}, {'field': 'onlineAmount', 'old_value': 241.18, 'new_value': 1382.72}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 30}]
2025-06-04 08:06:23,877 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMSY
2025-06-04 08:06:23,877 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1121.8, 'new_value': 15191.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24627.9}, {'field': 'amount', 'old_value': 1121.8, 'new_value': 15191.800000000001}, {'field': 'count', 'old_value': 4, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 1121.8, 'new_value': 15579.7}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 55}]
2025-06-04 08:06:24,283 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMTY
2025-06-04 08:06:24,299 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 17753.51}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 17753.51}, {'field': 'amount', 'old_value': 728.37, 'new_value': 7102.660000000001}, {'field': 'count', 'old_value': 15, 'new_value': 169}, {'field': 'instoreAmount', 'old_value': 662.52, 'new_value': 7135.26}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 165}, {'field': 'onlineAmount', 'old_value': 65.85, 'new_value': 141.3}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 4}]
2025-06-04 08:06:24,752 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMUY
2025-06-04 08:06:24,752 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15144.77}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15144.77}, {'field': 'amount', 'old_value': 73.5, 'new_value': 1166.28}, {'field': 'count', 'old_value': 1, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 73.5, 'new_value': 1314.48}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 34}]
2025-06-04 08:06:25,237 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMVY
2025-06-04 08:06:25,237 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1520.62, 'new_value': 8449.93}, {'field': 'amount', 'old_value': 1520.62, 'new_value': 8449.93}, {'field': 'count', 'old_value': 16, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 597.7, 'new_value': 5805.76}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 62}, {'field': 'onlineAmount', 'old_value': 922.92, 'new_value': 2706.27}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 37}]
2025-06-04 08:06:25,658 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMWY
2025-06-04 08:06:25,658 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 25005.46}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 25005.46}, {'field': 'amount', 'old_value': 751.6, 'new_value': 17740.0}, {'field': 'count', 'old_value': 18, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 210.0, 'new_value': 16246.1}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 71}, {'field': 'onlineAmount', 'old_value': 541.6, 'new_value': 1553.4}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 43}]
2025-06-04 08:06:26,174 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMXY
2025-06-04 08:06:26,174 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 795.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 795.6}, {'field': 'amount', 'old_value': 32.7, 'new_value': 828.3}, {'field': 'count', 'old_value': 1, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 198.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 1}, {'field': 'onlineAmount', 'old_value': 32.7, 'new_value': 630.3}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 8}]
2025-06-04 08:06:26,627 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26Y73LEBMYY
2025-06-04 08:06:26,627 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 21243.71}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 21243.71}, {'field': 'amount', 'old_value': 297.2, 'new_value': 30959.2}, {'field': 'count', 'old_value': 3, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 160.0, 'new_value': 30822.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 106}]
2025-06-04 08:06:27,158 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMMJ
2025-06-04 08:06:27,158 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 25640.49}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 25640.49}, {'field': 'amount', 'old_value': 651.9, 'new_value': 19643.1}, {'field': 'count', 'old_value': 3, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 651.9, 'new_value': 19643.1}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 95}]
2025-06-04 08:06:27,580 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMNJ
2025-06-04 08:06:27,580 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 12209.63}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12209.63}, {'field': 'amount', 'old_value': 199.0, 'new_value': 12447.08}, {'field': 'count', 'old_value': 1, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 199.0, 'new_value': 11359.78}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 62}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 1146.2}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 12}]
2025-06-04 08:06:28,065 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMOJ
2025-06-04 08:06:28,065 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 22070.76}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 22070.76}, {'field': 'amount', 'old_value': 3213.3, 'new_value': 13585.8}, {'field': 'count', 'old_value': 29, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 1045.5, 'new_value': 6476.8}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 38}, {'field': 'onlineAmount', 'old_value': 2167.8, 'new_value': 7109.0}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 74}]
2025-06-04 08:06:28,596 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMPJ
2025-06-04 08:06:28,596 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 476.38, 'new_value': 4114.55}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4249.67}, {'field': 'amount', 'old_value': 476.38, 'new_value': 4114.55}, {'field': 'count', 'old_value': 15, 'new_value': 116}, {'field': 'instoreAmount', 'old_value': 401.3, 'new_value': 3681.66}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 80}, {'field': 'onlineAmount', 'old_value': 75.08, 'new_value': 432.89}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 36}]
2025-06-04 08:06:29,018 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMQJ
2025-06-04 08:06:29,018 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_********, 变更字段: [{'field': 'amount', 'old_value': 258.0, 'new_value': 10928.48}, {'field': 'count', 'old_value': 2, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 258.0, 'new_value': 10928.48}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 92}]
2025-06-04 08:06:29,440 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMRJ
2025-06-04 08:06:29,440 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 640.13, 'new_value': 3112.21}, {'field': 'amount', 'old_value': 640.13, 'new_value': 3112.21}, {'field': 'count', 'old_value': 33, 'new_value': 187}, {'field': 'instoreAmount', 'old_value': 364.54, 'new_value': 1975.24}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 105}, {'field': 'onlineAmount', 'old_value': 275.59, 'new_value': 1281.44}, {'field': 'onlineCount', 'old_value': 20, 'new_value': 82}]
2025-06-04 08:06:29,940 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMSJ
2025-06-04 08:06:29,940 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 816.76, 'new_value': 5086.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5076.15}, {'field': 'amount', 'old_value': 816.76, 'new_value': 5086.0}, {'field': 'count', 'old_value': 47, 'new_value': 257}, {'field': 'instoreAmount', 'old_value': 287.2, 'new_value': 2414.77}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 104}, {'field': 'onlineAmount', 'old_value': 547.36, 'new_value': 2692.03}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 153}]
2025-06-04 08:06:30,393 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMTJ
2025-06-04 08:06:30,393 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5468.13}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5468.13}, {'field': 'amount', 'old_value': 143.22, 'new_value': 1970.1200000000001}, {'field': 'count', 'old_value': 2, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 1327.3}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 7}, {'field': 'onlineAmount', 'old_value': 143.22, 'new_value': 666.62}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 12}]
2025-06-04 08:06:30,862 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMUJ
2025-06-04 08:06:30,862 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 18996.61}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 18996.61}, {'field': 'amount', 'old_value': 707.83, 'new_value': 21953.69}, {'field': 'count', 'old_value': 27, 'new_value': 194}, {'field': 'instoreAmount', 'old_value': 435.59, 'new_value': 21518.21}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 129}, {'field': 'onlineAmount', 'old_value': 272.24, 'new_value': 1531.32}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 65}]
2025-06-04 08:06:31,315 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMVJ
2025-06-04 08:06:31,330 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 97.0, 'new_value': 1781.34}, {'field': 'amount', 'old_value': 97.0, 'new_value': 1781.34}, {'field': 'count', 'old_value': 5, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 97.0, 'new_value': 1532.84}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 66}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 248.5}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 6}]
2025-06-04 08:06:31,924 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMWJ
2025-06-04 08:06:31,924 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1334.59, 'new_value': 8307.37}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6773.2}, {'field': 'amount', 'old_value': 1334.59, 'new_value': 8307.37}, {'field': 'count', 'old_value': 15, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 1218.0, 'new_value': 7997.2}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 87}, {'field': 'onlineAmount', 'old_value': 116.59, 'new_value': 310.17}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 16}]
2025-06-04 08:06:32,424 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMXJ
2025-06-04 08:06:32,424 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13299.03}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13299.03}, {'field': 'amount', 'old_value': 1800.0, 'new_value': 14837.0}, {'field': 'count', 'old_value': 8, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 1800.0, 'new_value': 14837.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 104}]
2025-06-04 08:06:32,846 - INFO - 更新表单数据成功: FINST-AJF66F71NESV3RKYCSNEXDBXW8TK2V8LFRFBMWV
2025-06-04 08:06:32,846 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_********, 变更字段: [{'field': 'amount', 'old_value': 18705.8, 'new_value': 20154.8}, {'field': 'count', 'old_value': 29, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 18193.0, 'new_value': 19642.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-06-04 08:06:33,299 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMYJ
2025-06-04 08:06:33,299 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 36333.05}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 36333.05}, {'field': 'amount', 'old_value': 481.18, 'new_value': 36333.05}, {'field': 'count', 'old_value': 4, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 481.18, 'new_value': 36333.05}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 163}]
2025-06-04 08:06:33,737 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMZJ
2025-06-04 08:06:33,752 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 12266.79}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12266.79}, {'field': 'amount', 'old_value': 581.13, 'new_value': 12242.79}, {'field': 'count', 'old_value': 5, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 338.0, 'new_value': 11542.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 53}, {'field': 'onlineAmount', 'old_value': 243.13, 'new_value': 736.79}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 10}]
2025-06-04 08:06:34,346 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM0K
2025-06-04 08:06:34,346 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 61191.73}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 61191.73}, {'field': 'amount', 'old_value': 349.0, 'new_value': 56590.0}, {'field': 'count', 'old_value': 1, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 349.0, 'new_value': 57930.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 128}]
2025-06-04 08:06:34,846 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM1K
2025-06-04 08:06:34,846 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 24590.31}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24590.31}, {'field': 'amount', 'old_value': 553.2, 'new_value': 17754.32}, {'field': 'count', 'old_value': 12, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 31.0, 'new_value': 18111.95}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 46}, {'field': 'onlineAmount', 'old_value': 522.2, 'new_value': 735.3}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 19}]
2025-06-04 08:06:35,221 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM2K
2025-06-04 08:06:35,221 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 223.0, 'new_value': 40410.05}, {'field': 'amount', 'old_value': 223.0, 'new_value': 40410.05}, {'field': 'count', 'old_value': 2, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 223.0, 'new_value': 40410.05}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 133}]
2025-06-04 08:06:35,752 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM3K
2025-06-04 08:06:35,752 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 28073.28}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 28073.28}, {'field': 'amount', 'old_value': 726.42, 'new_value': 27845.89}, {'field': 'count', 'old_value': 9, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 454.0, 'new_value': 27720.74}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 55}, {'field': 'onlineAmount', 'old_value': 272.42, 'new_value': 477.69}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 9}]
2025-06-04 08:06:36,158 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM4K
2025-06-04 08:06:36,158 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15375.95}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15375.95}, {'field': 'amount', 'old_value': 934.7, 'new_value': 17528.22}, {'field': 'count', 'old_value': 33, 'new_value': 435}, {'field': 'instoreAmount', 'old_value': 762.0, 'new_value': 16231.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 378}, {'field': 'onlineAmount', 'old_value': 214.7, 'new_value': 1409.97}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 57}]
2025-06-04 08:06:36,596 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM5K
2025-06-04 08:06:36,596 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 255.31, 'new_value': 2441.99}, {'field': 'amount', 'old_value': 255.31, 'new_value': 2441.99}, {'field': 'count', 'old_value': 3, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 255.31, 'new_value': 2441.99}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 25}]
2025-06-04 08:06:37,002 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM6K
2025-06-04 08:06:37,002 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 52.98, 'new_value': 598.24}, {'field': 'amount', 'old_value': 52.98, 'new_value': 598.24}, {'field': 'count', 'old_value': 2, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 52.98, 'new_value': 598.24}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 16}]
2025-06-04 08:06:37,440 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM7K
2025-06-04 08:06:37,440 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 747.41, 'new_value': 18457.809999999998}, {'field': 'count', 'old_value': 4, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 15825.2}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 75}, {'field': 'onlineAmount', 'old_value': 747.41, 'new_value': 2890.17}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 24}]
2025-06-04 08:06:37,862 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM8K
2025-06-04 08:06:37,862 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 505.42, 'new_value': 26035.95}, {'field': 'amount', 'old_value': 505.42, 'new_value': 26035.95}, {'field': 'count', 'old_value': 9, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 318.0, 'new_value': 23854.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 86}, {'field': 'onlineAmount', 'old_value': 187.42, 'new_value': 2181.95}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 59}]
2025-06-04 08:06:38,268 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBM9K
2025-06-04 08:06:38,268 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 296.27, 'new_value': 17629.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15638.03}, {'field': 'amount', 'old_value': 296.27, 'new_value': 17629.74}, {'field': 'count', 'old_value': 9, 'new_value': 102}, {'field': 'instoreAmount', 'old_value': 72.0, 'new_value': 15636.31}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 68}, {'field': 'onlineAmount', 'old_value': 224.27, 'new_value': 1993.43}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 34}]
2025-06-04 08:06:38,783 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMAK
2025-06-04 08:06:38,783 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1546.6, 'new_value': 36711.3}, {'field': 'amount', 'old_value': 1546.6, 'new_value': 36711.3}, {'field': 'count', 'old_value': 5, 'new_value': 144}, {'field': 'instoreAmount', 'old_value': 1546.6, 'new_value': 36711.3}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 144}]
2025-06-04 08:06:39,190 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMBK
2025-06-04 08:06:39,190 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 35335.47}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 35335.47}, {'field': 'amount', 'old_value': 2195.7, 'new_value': 28795.8}, {'field': 'count', 'old_value': 11, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 258.8, 'new_value': 22427.9}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 167}, {'field': 'onlineAmount', 'old_value': 1936.9, 'new_value': 6367.9}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 31}]
2025-06-04 08:06:39,658 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMCK
2025-06-04 08:06:39,658 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6468.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6468.3}, {'field': 'amount', 'old_value': 2603.61, 'new_value': 3926.04}, {'field': 'count', 'old_value': 58, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 1138.9, 'new_value': 2260.9}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 43}, {'field': 'onlineAmount', 'old_value': 1514.71, 'new_value': 2089.14}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 57}]
2025-06-04 08:06:40,096 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMDK
2025-06-04 08:06:40,096 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 320.61, 'new_value': 1592.84}, {'field': 'amount', 'old_value': 320.61, 'new_value': 1592.84}, {'field': 'count', 'old_value': 11, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 262.5, 'new_value': 1386.5}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 32}, {'field': 'onlineAmount', 'old_value': 58.11, 'new_value': 266.34}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 5}]
2025-06-04 08:06:40,486 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMEK
2025-06-04 08:06:40,502 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 263.96, 'new_value': 2655.02}, {'field': 'amount', 'old_value': 263.96, 'new_value': 2655.02}, {'field': 'count', 'old_value': 5, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 84.16, 'new_value': 1255.97}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 59}, {'field': 'onlineAmount', 'old_value': 179.8, 'new_value': 1399.05}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 31}]
2025-06-04 08:06:40,955 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMFK
2025-06-04 08:06:40,955 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1338.42}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1338.42}, {'field': 'amount', 'old_value': 20.58, 'new_value': 661.95}, {'field': 'count', 'old_value': 3, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 334.9}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 11}, {'field': 'onlineAmount', 'old_value': 20.58, 'new_value': 327.05}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 20}]
2025-06-04 08:06:41,424 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMGK
2025-06-04 08:06:41,424 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 414.2, 'new_value': 2590.27}, {'field': 'amount', 'old_value': 414.2, 'new_value': 2590.27}, {'field': 'count', 'old_value': 30, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 414.2, 'new_value': 2621.1}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 131}]
2025-06-04 08:06:41,908 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMHK
2025-06-04 08:06:41,908 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 306.65, 'new_value': 3681.89}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1814.7}, {'field': 'amount', 'old_value': 306.65, 'new_value': 3681.89}, {'field': 'count', 'old_value': 10, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 203.0, 'new_value': 2117.87}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 63}, {'field': 'onlineAmount', 'old_value': 103.65, 'new_value': 1628.02}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 47}]
2025-06-04 08:06:42,361 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMIK
2025-06-04 08:06:42,361 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'amount', 'old_value': 45.7, 'new_value': 133.3}, {'field': 'count', 'old_value': 2, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 45.7, 'new_value': 133.3}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 5}]
2025-06-04 08:06:42,815 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMJK
2025-06-04 08:06:42,815 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 941.53, 'new_value': 3617.2}, {'field': 'amount', 'old_value': 941.53, 'new_value': 3617.2}, {'field': 'count', 'old_value': 30, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 108.9, 'new_value': 654.78}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 17}, {'field': 'onlineAmount', 'old_value': 832.63, 'new_value': 2976.38}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 124}]
2025-06-04 08:06:43,315 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMKK
2025-06-04 08:06:43,315 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1730.57, 'new_value': 8944.91}, {'field': 'amount', 'old_value': 1730.57, 'new_value': 8944.91}, {'field': 'count', 'old_value': 23, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 1369.0, 'new_value': 7869.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 61}, {'field': 'onlineAmount', 'old_value': 361.57, 'new_value': 1099.81}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 20}]
2025-06-04 08:06:43,768 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMLK
2025-06-04 08:06:43,768 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_********, 变更字段: [{'field': 'amount', 'old_value': 66.34, 'new_value': 486.78000000000003}, {'field': 'count', 'old_value': 5, 'new_value': 21}, {'field': 'onlineAmount', 'old_value': 66.34, 'new_value': 509.68}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 21}]
2025-06-04 08:06:44,408 - INFO - 更新表单数据成功: FINST-ORA66F810LVVYDC897WQ89701R3X3GHA3LEBMMK
2025-06-04 08:06:44,408 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4748.04}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4748.04}, {'field': 'amount', 'old_value': 88.3, 'new_value': 660.4699999999999}, {'field': 'count', 'old_value': 4, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 88.3, 'new_value': 688.17}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 20}]
2025-06-04 08:06:44,549 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-04 08:06:45,018 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-04 08:06:48,033 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-04 08:06:48,455 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-04 08:06:51,471 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-04 08:06:52,002 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-04 08:06:55,018 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-04 08:06:55,440 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-04 08:06:58,455 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-04 08:06:58,939 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-04 08:07:01,955 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-04 08:07:02,439 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-04 08:07:05,455 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-04 08:07:05,939 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-04 08:07:08,955 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-04 08:07:09,346 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-04 08:07:12,361 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-04 08:07:12,846 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-04 08:07:15,861 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-04 08:07:16,252 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-04 08:07:19,283 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-04 08:07:19,736 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-04 08:07:22,752 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-04 08:07:23,127 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-04 08:07:26,142 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-04 08:07:26,549 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-04 08:07:29,564 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-04 08:07:30,064 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-04 08:07:33,080 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-04 08:07:33,533 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-04 08:07:36,533 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-04 08:07:36,971 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-04 08:07:39,986 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-04 08:07:40,408 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-04 08:07:43,424 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-04 08:07:43,877 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-04 08:07:46,892 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-04 08:07:47,377 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-04 08:07:50,392 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-04 08:07:50,767 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-04 08:07:53,783 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-04 08:07:54,174 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-04 08:07:57,189 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-04 08:07:57,705 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-04 08:08:00,720 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-04 08:08:01,189 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-04 08:08:04,205 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-04 08:08:04,627 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-04 08:08:07,642 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-04 08:08:08,017 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-04 08:08:11,033 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-04 08:08:11,486 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-04 08:08:14,502 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-04 08:08:15,064 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-04 08:08:18,080 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-04 08:08:18,533 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-04 08:08:21,548 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-04 08:08:22,001 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-04 08:08:25,017 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-04 08:08:25,533 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-04 08:08:28,548 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-04 08:08:28,970 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-04 08:08:31,986 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-04 08:08:32,486 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-04 08:08:35,501 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-04 08:08:35,908 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-04 08:08:38,923 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-04 08:08:39,392 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-04 08:08:42,408 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-04 08:08:42,814 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-04 08:08:45,829 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-04 08:08:46,220 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-04 08:08:49,236 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-04 08:08:49,720 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-04 08:08:52,736 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-04 08:08:53,158 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-04 08:08:56,173 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-04 08:08:56,579 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-04 08:08:59,595 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-04 08:09:00,032 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-04 08:09:03,048 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-04 08:09:03,579 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-04 08:09:06,595 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-04 08:09:06,986 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-04 08:09:10,001 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-04 08:09:10,361 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-04 08:09:13,376 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-04 08:09:13,829 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-04 08:09:16,845 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-04 08:09:17,376 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-04 08:09:20,392 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-04 08:09:20,798 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-04 08:09:23,814 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-04 08:09:24,267 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-04 08:09:27,282 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-04 08:09:27,751 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-04 08:09:30,767 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-04 08:09:31,142 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-04 08:09:34,157 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-04 08:09:34,595 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-04 08:09:37,610 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-04 08:09:38,001 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-04 08:09:41,017 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-04 08:09:41,454 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-04 08:09:44,470 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-04 08:09:45,032 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-04 08:09:48,048 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-04 08:09:48,454 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-04 08:09:51,470 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-04 08:09:51,985 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-04 08:09:55,001 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-04 08:09:55,423 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-04 08:09:58,438 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-04 08:09:58,860 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-04 08:10:01,876 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-04 08:10:02,282 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-04 08:10:05,298 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-04 08:10:05,751 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-04 08:10:08,766 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-04 08:10:09,204 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-04 08:10:12,219 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-04 08:10:12,719 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-04 08:10:15,735 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-04 08:10:16,204 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-04 08:10:19,219 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-04 08:10:19,766 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-04 08:10:22,782 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-04 08:10:23,266 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-04 08:10:26,282 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-04 08:10:26,735 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-04 08:10:29,751 - INFO - 正在批量插入每日数据，批次 66/66，共 60 条记录
2025-06-04 08:10:30,048 - INFO - 批量插入每日数据成功，批次 66，60 条记录
2025-06-04 08:10:33,063 - INFO - 批量插入每日数据完成: 总计 6560 条，成功 6560 条，失败 0 条
2025-06-04 08:10:33,063 - INFO - 批量插入日销售数据完成，共 6560 条记录
2025-06-04 08:10:33,063 - INFO - 日销售数据同步完成！更新: 160 条，插入: 6560 条，错误: 0 条，跳过: 6176 条
2025-06-04 08:10:33,063 - INFO - 正在获取宜搭月销售表单数据...
2025-06-04 08:10:33,063 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-04 08:10:33,063 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-04 08:10:33,063 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-04 08:10:33,063 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:33,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:33,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:33,657 - INFO - API请求耗时: 594ms
2025-06-04 08:10:33,657 - INFO - Response - Page 1
2025-06-04 08:10:33,657 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:33,657 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:33,657 - WARNING - 月度分段 1 查询返回空数据
2025-06-04 08:10:33,657 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-04 08:10:33,657 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-04 08:10:33,657 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:33,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:33,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:33,876 - INFO - API请求耗时: 219ms
2025-06-04 08:10:33,876 - INFO - Response - Page 1
2025-06-04 08:10:33,876 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:33,876 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:33,876 - WARNING - 单月查询返回空数据: 2024-06
2025-06-04 08:10:34,391 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-04 08:10:34,391 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:34,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:34,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:34,860 - INFO - API请求耗时: 469ms
2025-06-04 08:10:34,860 - INFO - Response - Page 1
2025-06-04 08:10:34,860 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:34,860 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:34,860 - WARNING - 单月查询返回空数据: 2024-07
2025-06-04 08:10:35,376 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-04 08:10:35,376 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:35,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:35,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:35,594 - INFO - API请求耗时: 219ms
2025-06-04 08:10:35,594 - INFO - Response - Page 1
2025-06-04 08:10:35,594 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:35,594 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:35,594 - WARNING - 单月查询返回空数据: 2024-08
2025-06-04 08:10:37,126 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-04 08:10:37,126 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-04 08:10:37,126 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:37,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:37,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:37,329 - INFO - API请求耗时: 203ms
2025-06-04 08:10:37,329 - INFO - Response - Page 1
2025-06-04 08:10:37,329 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:37,329 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:37,329 - WARNING - 月度分段 2 查询返回空数据
2025-06-04 08:10:37,329 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-04 08:10:37,329 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-04 08:10:37,329 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:37,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:37,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:37,532 - INFO - API请求耗时: 203ms
2025-06-04 08:10:37,547 - INFO - Response - Page 1
2025-06-04 08:10:37,547 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:37,547 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:37,547 - WARNING - 单月查询返回空数据: 2024-09
2025-06-04 08:10:38,063 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-04 08:10:38,063 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:38,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:38,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:38,266 - INFO - API请求耗时: 203ms
2025-06-04 08:10:38,266 - INFO - Response - Page 1
2025-06-04 08:10:38,266 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:38,266 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:38,266 - WARNING - 单月查询返回空数据: 2024-10
2025-06-04 08:10:38,782 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-04 08:10:38,782 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:38,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:38,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:39,001 - INFO - API请求耗时: 219ms
2025-06-04 08:10:39,001 - INFO - Response - Page 1
2025-06-04 08:10:39,001 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-04 08:10:39,001 - INFO - 查询完成，共获取到 0 条记录
2025-06-04 08:10:39,001 - WARNING - 单月查询返回空数据: 2024-11
2025-06-04 08:10:40,532 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-04 08:10:40,532 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-04 08:10:40,532 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:40,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:40,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:41,141 - INFO - API请求耗时: 609ms
2025-06-04 08:10:41,141 - INFO - Response - Page 1
2025-06-04 08:10:41,141 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:10:41,657 - INFO - Request Parameters - Page 2:
2025-06-04 08:10:41,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:41,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:42,172 - INFO - API请求耗时: 516ms
2025-06-04 08:10:42,172 - INFO - Response - Page 2
2025-06-04 08:10:42,172 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:10:42,672 - INFO - Request Parameters - Page 3:
2025-06-04 08:10:42,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:42,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:43,266 - INFO - API请求耗时: 594ms
2025-06-04 08:10:43,266 - INFO - Response - Page 3
2025-06-04 08:10:43,266 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:10:43,782 - INFO - Request Parameters - Page 4:
2025-06-04 08:10:43,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:43,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:44,329 - INFO - API请求耗时: 547ms
2025-06-04 08:10:44,329 - INFO - Response - Page 4
2025-06-04 08:10:44,329 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:10:44,844 - INFO - Request Parameters - Page 5:
2025-06-04 08:10:44,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:44,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:45,422 - INFO - API请求耗时: 578ms
2025-06-04 08:10:45,422 - INFO - Response - Page 5
2025-06-04 08:10:45,422 - INFO - 第 5 页获取到 94 条记录
2025-06-04 08:10:45,422 - INFO - 查询完成，共获取到 494 条记录
2025-06-04 08:10:45,422 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-04 08:10:46,422 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-04 08:10:46,422 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-04 08:10:46,422 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:46,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:46,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:46,922 - INFO - API请求耗时: 500ms
2025-06-04 08:10:46,922 - INFO - Response - Page 1
2025-06-04 08:10:46,922 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:10:47,438 - INFO - Request Parameters - Page 2:
2025-06-04 08:10:47,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:47,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:47,985 - INFO - API请求耗时: 547ms
2025-06-04 08:10:47,985 - INFO - Response - Page 2
2025-06-04 08:10:47,985 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:10:48,500 - INFO - Request Parameters - Page 3:
2025-06-04 08:10:48,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:48,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:49,047 - INFO - API请求耗时: 547ms
2025-06-04 08:10:49,047 - INFO - Response - Page 3
2025-06-04 08:10:49,047 - INFO - 第 3 页获取到 100 条记录
2025-06-04 08:10:49,563 - INFO - Request Parameters - Page 4:
2025-06-04 08:10:49,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:49,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:50,250 - INFO - API请求耗时: 687ms
2025-06-04 08:10:50,250 - INFO - Response - Page 4
2025-06-04 08:10:50,250 - INFO - 第 4 页获取到 100 条记录
2025-06-04 08:10:50,751 - INFO - Request Parameters - Page 5:
2025-06-04 08:10:50,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:50,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:51,235 - INFO - API请求耗时: 484ms
2025-06-04 08:10:51,235 - INFO - Response - Page 5
2025-06-04 08:10:51,235 - INFO - 第 5 页获取到 100 条记录
2025-06-04 08:10:51,735 - INFO - Request Parameters - Page 6:
2025-06-04 08:10:51,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:51,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:52,344 - INFO - API请求耗时: 609ms
2025-06-04 08:10:52,344 - INFO - Response - Page 6
2025-06-04 08:10:52,344 - INFO - 第 6 页获取到 100 条记录
2025-06-04 08:10:52,860 - INFO - Request Parameters - Page 7:
2025-06-04 08:10:52,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:52,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:53,438 - INFO - API请求耗时: 578ms
2025-06-04 08:10:53,454 - INFO - Response - Page 7
2025-06-04 08:10:53,454 - INFO - 第 7 页获取到 98 条记录
2025-06-04 08:10:53,454 - INFO - 查询完成，共获取到 698 条记录
2025-06-04 08:10:53,454 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-04 08:10:54,454 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-04 08:10:54,454 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-04 08:10:54,454 - INFO - Request Parameters - Page 1:
2025-06-04 08:10:54,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:54,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:54,969 - INFO - API请求耗时: 516ms
2025-06-04 08:10:54,969 - INFO - Response - Page 1
2025-06-04 08:10:54,969 - INFO - 第 1 页获取到 100 条记录
2025-06-04 08:10:55,469 - INFO - Request Parameters - Page 2:
2025-06-04 08:10:55,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:55,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:56,063 - INFO - API请求耗时: 594ms
2025-06-04 08:10:56,063 - INFO - Response - Page 2
2025-06-04 08:10:56,063 - INFO - 第 2 页获取到 100 条记录
2025-06-04 08:10:56,579 - INFO - Request Parameters - Page 3:
2025-06-04 08:10:56,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-04 08:10:56,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-04 08:10:56,844 - INFO - API请求耗时: 266ms
2025-06-04 08:10:56,844 - INFO - Response - Page 3
2025-06-04 08:10:56,844 - INFO - 第 3 页获取到 9 条记录
2025-06-04 08:10:56,844 - INFO - 查询完成，共获取到 209 条记录
2025-06-04 08:10:56,844 - INFO - 月度分段 5 查询成功，获取到 209 条记录
2025-06-04 08:10:57,860 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1401 条记录，失败 0 次
2025-06-04 08:10:57,860 - INFO - 成功获取宜搭月销售表单数据，共 1401 条记录
2025-06-04 08:10:57,860 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-04 08:10:57,860 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-04 08:10:57,860 - INFO - 成功获取SQLite月度汇总数据，共 1402 条记录
2025-06-04 08:10:57,938 - INFO - 成功创建宜搭月销售数据索引，共 1401 条记录
2025-06-04 08:10:57,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:10:58,469 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-04 08:10:58,469 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21957.940000000002, 'new_value': 27389.15}, {'field': 'dailyBillAmount', 'old_value': 21957.940000000002, 'new_value': 27389.15}, {'field': 'amount', 'old_value': 336.0, 'new_value': 692.4}, {'field': 'count', 'old_value': 4, 'new_value': 7}, {'field': 'onlineAmount', 'old_value': 336.0, 'new_value': 692.4}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 7}]
2025-06-04 08:10:58,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:10:58,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-04 08:10:58,938 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56444.67, 'new_value': 67931.37}, {'field': 'dailyBillAmount', 'old_value': 56444.67, 'new_value': 67931.37}, {'field': 'amount', 'old_value': 29282.7, 'new_value': 37809.1}, {'field': 'count', 'old_value': 242, 'new_value': 341}, {'field': 'instoreAmount', 'old_value': 13599.1, 'new_value': 15976.0}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 116}, {'field': 'onlineAmount', 'old_value': 15684.4, 'new_value': 21833.9}, {'field': 'onlineCount', 'old_value': 153, 'new_value': 225}]
2025-06-04 08:10:58,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:10:59,422 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-04 08:10:59,422 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34053.96, 'new_value': 40124.91}, {'field': 'dailyBillAmount', 'old_value': 34053.96, 'new_value': 40124.91}, {'field': 'amount', 'old_value': 34333.96, 'new_value': 40701.76}, {'field': 'count', 'old_value': 205, 'new_value': 255}, {'field': 'instoreAmount', 'old_value': 32531.93, 'new_value': 37375.83}, {'field': 'instoreCount', 'old_value': 183, 'new_value': 215}, {'field': 'onlineAmount', 'old_value': 1861.01, 'new_value': 3384.91}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 40}]
2025-06-04 08:10:59,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:10:59,922 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-04 08:10:59,922 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 51397.3, 'new_value': 68602.0}, {'field': 'count', 'old_value': 251, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 51397.399999999994, 'new_value': 68602.1}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 331}]
2025-06-04 08:10:59,922 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:00,422 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-04 08:11:00,422 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67320.69, 'new_value': 74897.76000000001}, {'field': 'dailyBillAmount', 'old_value': 67320.69, 'new_value': 74897.76000000001}, {'field': 'amount', 'old_value': 81208.0, 'new_value': 96079.6}, {'field': 'count', 'old_value': 263, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 80910.0, 'new_value': 95696.0}, {'field': 'instoreCount', 'old_value': 261, 'new_value': 327}, {'field': 'onlineAmount', 'old_value': 298.2, 'new_value': 383.79999999999995}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}]
2025-06-04 08:11:00,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:00,922 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-04 08:11:00,922 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3546.2, 'new_value': 4965.1}, {'field': 'dailyBillAmount', 'old_value': 3546.2, 'new_value': 4965.1}, {'field': 'amount', 'old_value': 3780.1, 'new_value': 5400.4}, {'field': 'count', 'old_value': 19, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 2388.0, 'new_value': 2776.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}, {'field': 'onlineAmount', 'old_value': 1392.4, 'new_value': 3012.7}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 32}]
2025-06-04 08:11:00,922 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:01,344 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-04 08:11:01,344 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7988.0, 'new_value': 9676.0}, {'field': 'amount', 'old_value': 7988.0, 'new_value': 9676.0}, {'field': 'count', 'old_value': 6, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 7988.0, 'new_value': 9676.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 8}]
2025-06-04 08:11:01,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:01,766 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-04 08:11:01,766 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64306.44, 'new_value': 82276.52}, {'field': 'dailyBillAmount', 'old_value': 64306.44, 'new_value': 82276.52}, {'field': 'amount', 'old_value': 43192.4, 'new_value': 53371.69}, {'field': 'count', 'old_value': 244, 'new_value': 358}, {'field': 'instoreAmount', 'old_value': 41366.6, 'new_value': 49733.99}, {'field': 'instoreCount', 'old_value': 172, 'new_value': 216}, {'field': 'onlineAmount', 'old_value': 2669.9, 'new_value': 4816.200000000001}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 142}]
2025-06-04 08:11:01,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:02,250 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-04 08:11:02,250 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59484.68, 'new_value': 78762.62}, {'field': 'dailyBillAmount', 'old_value': 59484.68, 'new_value': 78762.62}, {'field': 'amount', 'old_value': 11485.0, 'new_value': 15349.0}, {'field': 'count', 'old_value': 53, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 11485.0, 'new_value': 15349.0}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 70}]
2025-06-04 08:11:02,266 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-04 08:11:02,688 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-06-04 08:11:02,688 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1301017.21, 'new_value': 1303875.52}, {'field': 'count', 'old_value': 1612, 'new_value': 1614}, {'field': 'instoreAmount', 'old_value': 1301017.3800000001, 'new_value': 1303877.3800000001}, {'field': 'instoreCount', 'old_value': 1612, 'new_value': 1614}]
2025-06-04 08:11:02,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:03,188 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAI
2025-06-04 08:11:03,188 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10659.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10659.3}]
2025-06-04 08:11:03,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:03,625 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-04 08:11:03,625 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2782.0, 'new_value': 8018.0}, {'field': 'count', 'old_value': 7, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 2782.0, 'new_value': 8018.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 13}]
2025-06-04 08:11:03,625 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:04,047 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-04 08:11:04,047 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137678.51, 'new_value': 163475.92}, {'field': 'dailyBillAmount', 'old_value': 137678.51, 'new_value': 163475.92}, {'field': 'amount', 'old_value': -45265.770000000004, 'new_value': -59077.32000000001}, {'field': 'count', 'old_value': 145, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 93827.38, 'new_value': 104616.52}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 164}]
2025-06-04 08:11:04,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:04,485 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-04 08:11:04,485 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29094.0, 'new_value': 44231.0}, {'field': 'amount', 'old_value': 29094.0, 'new_value': 44231.0}, {'field': 'count', 'old_value': 128, 'new_value': 167}, {'field': 'instoreAmount', 'old_value': 29094.0, 'new_value': 44231.0}, {'field': 'instoreCount', 'old_value': 128, 'new_value': 167}]
2025-06-04 08:11:04,485 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:04,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-04 08:11:04,938 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46602.74, 'new_value': 56117.619999999995}, {'field': 'dailyBillAmount', 'old_value': 39865.54, 'new_value': 48687.020000000004}, {'field': 'amount', 'old_value': 46602.74, 'new_value': 56117.619999999995}, {'field': 'count', 'old_value': 149, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 46602.74, 'new_value': 56117.619999999995}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 181}]
2025-06-04 08:11:04,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:05,485 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-04 08:11:05,485 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7642.9, 'new_value': 14762.2}, {'field': 'dailyBillAmount', 'old_value': 7642.9, 'new_value': 14762.2}, {'field': 'amount', 'old_value': 569.9, 'new_value': 684.9}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 629.0, 'new_value': 744.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-04 08:11:05,485 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:05,969 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFI
2025-06-04 08:11:05,969 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11112.05, 'new_value': 14082.79}, {'field': 'dailyBillAmount', 'old_value': 11112.05, 'new_value': 14082.79}, {'field': 'amount', 'old_value': 5911.32, 'new_value': 7719.52}, {'field': 'count', 'old_value': 109, 'new_value': 134}, {'field': 'instoreAmount', 'old_value': 6183.57, 'new_value': 7991.77}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 134}]
2025-06-04 08:11:05,969 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:06,391 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-04 08:11:06,391 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10229.27, 'new_value': 16561.71}, {'field': 'amount', 'old_value': 10228.91, 'new_value': 16561.35}, {'field': 'count', 'old_value': 317, 'new_value': 538}, {'field': 'instoreAmount', 'old_value': 9453.46, 'new_value': 15108.68}, {'field': 'instoreCount', 'old_value': 293, 'new_value': 497}, {'field': 'onlineAmount', 'old_value': 775.81, 'new_value': 1453.03}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 41}]
2025-06-04 08:11:06,407 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:06,797 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-04 08:11:06,797 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19255.0, 'new_value': 21461.69}, {'field': 'dailyBillAmount', 'old_value': 19255.0, 'new_value': 21420.0}, {'field': 'amount', 'old_value': 13248.0, 'new_value': 15454.69}, {'field': 'count', 'old_value': 26, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 13248.0, 'new_value': 15413.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 29}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 41.69}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-06-04 08:11:06,797 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:07,204 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-04 08:11:07,204 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31596.769999999997, 'new_value': 43536.11}, {'field': 'dailyBillAmount', 'old_value': 31596.769999999997, 'new_value': 43536.11}, {'field': 'amount', 'old_value': 31596.769999999997, 'new_value': 43536.11}, {'field': 'count', 'old_value': 36, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 31596.769999999997, 'new_value': 43536.11}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 49}]
2025-06-04 08:11:07,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:07,625 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJI
2025-06-04 08:11:07,625 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1470.0, 'new_value': 5256.0}, {'field': 'count', 'old_value': 4, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 1470.0, 'new_value': 5256.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 7}]
2025-06-04 08:11:07,625 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:08,110 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-04 08:11:08,110 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10005.4, 'new_value': 12227.099999999999}, {'field': 'dailyBillAmount', 'old_value': 10005.4, 'new_value': 12227.099999999999}, {'field': 'amount', 'old_value': 10800.4, 'new_value': 13676.099999999999}, {'field': 'count', 'old_value': 28, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 10800.4, 'new_value': 13676.099999999999}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 40}]
2025-06-04 08:11:08,110 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:08,594 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-04 08:11:08,594 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3159.0, 'new_value': 5138.0}, {'field': 'amount', 'old_value': 3159.0, 'new_value': 5138.0}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 3159.0, 'new_value': 5138.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-06-04 08:11:08,594 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:09,000 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-04 08:11:09,000 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29350.62, 'new_value': 34701.619999999995}, {'field': 'dailyBillAmount', 'old_value': 29350.62, 'new_value': 34701.619999999995}, {'field': 'amount', 'old_value': 39953.0, 'new_value': 47142.0}, {'field': 'count', 'old_value': 183, 'new_value': 224}, {'field': 'instoreAmount', 'old_value': 39953.3, 'new_value': 47142.3}, {'field': 'instoreCount', 'old_value': 183, 'new_value': 224}]
2025-06-04 08:11:09,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:09,469 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-04 08:11:09,469 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11603.46, 'new_value': 18762.42}, {'field': 'dailyBillAmount', 'old_value': 11603.46, 'new_value': 18762.42}, {'field': 'amount', 'old_value': 341.49, 'new_value': 756.13}, {'field': 'count', 'old_value': 47, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 860.48, 'new_value': 1337.62}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 90}]
2025-06-04 08:11:09,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:09,953 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOI
2025-06-04 08:11:09,953 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22647.82, 'new_value': 22631.72}, {'field': 'amount', 'old_value': 22647.0, 'new_value': 22631.0}]
2025-06-04 08:11:09,953 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:10,328 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-04 08:11:10,328 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36345.8, 'new_value': 41053.6}, {'field': 'dailyBillAmount', 'old_value': 36345.8, 'new_value': 41053.6}, {'field': 'amount', 'old_value': 36401.8, 'new_value': 41109.6}, {'field': 'count', 'old_value': 90, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 36401.8, 'new_value': 41109.6}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 100}]
2025-06-04 08:11:10,328 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:10,750 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-04 08:11:10,750 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26782.800000000003, 'new_value': 33371.47}, {'field': 'dailyBillAmount', 'old_value': 26782.800000000003, 'new_value': 33371.47}, {'field': 'amount', 'old_value': 10458.4, 'new_value': 11907.4}, {'field': 'count', 'old_value': 25, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 10458.4, 'new_value': 11907.4}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 28}]
2025-06-04 08:11:10,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:11,313 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-04 08:11:11,313 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37610.83, 'new_value': 46698.4}, {'field': 'dailyBillAmount', 'old_value': 37610.83, 'new_value': 46698.4}, {'field': 'amount', 'old_value': 14963.6, 'new_value': 17954.6}, {'field': 'count', 'old_value': 60, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 14963.6, 'new_value': 17954.6}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 73}]
2025-06-04 08:11:11,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:11,985 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMH1
2025-06-04 08:11:11,985 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2372.0, 'new_value': 3962.0}, {'field': 'amount', 'old_value': 2372.0, 'new_value': 3962.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 2372.0, 'new_value': 3962.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-04 08:11:11,985 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:12,375 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-04 08:11:12,375 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7479.94, 'new_value': 11075.57}, {'field': 'dailyBillAmount', 'old_value': 7479.94, 'new_value': 11075.57}, {'field': 'amount', 'old_value': 1613.8400000000001, 'new_value': 2242.34}, {'field': 'count', 'old_value': 59, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 502.5, 'new_value': 651.3}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 15}, {'field': 'onlineAmount', 'old_value': 1111.69, 'new_value': 1591.73}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 73}]
2025-06-04 08:11:12,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:12,828 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-04 08:11:12,828 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13924.11, 'new_value': 19174.98}, {'field': 'dailyBillAmount', 'old_value': 13924.11, 'new_value': 19174.98}, {'field': 'amount', 'old_value': 2806.0, 'new_value': 4009.6}, {'field': 'count', 'old_value': 65, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 2546.9700000000003, 'new_value': 3400.26}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 73}, {'field': 'onlineAmount', 'old_value': 270.1, 'new_value': 620.41}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 15}]
2025-06-04 08:11:12,828 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:13,407 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-04 08:11:13,407 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6890.6, 'new_value': 6910.6}, {'field': 'dailyBillAmount', 'old_value': 6890.6, 'new_value': 6910.6}, {'field': 'amount', 'old_value': 6483.6, 'new_value': 6503.6}, {'field': 'count', 'old_value': 141, 'new_value': 151}, {'field': 'instoreAmount', 'old_value': 6483.6, 'new_value': 6503.6}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 151}]
2025-06-04 08:11:13,407 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:13,860 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-04 08:11:13,860 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6291.57, 'new_value': 7491.879999999999}, {'field': 'dailyBillAmount', 'old_value': 6291.57, 'new_value': 7491.879999999999}, {'field': 'amount', 'old_value': 2630.24, 'new_value': 3454.94}, {'field': 'count', 'old_value': 150, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 927.52, 'new_value': 1140.52}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 56}, {'field': 'onlineAmount', 'old_value': 1747.31, 'new_value': 2359.0299999999997}, {'field': 'onlineCount', 'old_value': 104, 'new_value': 145}]
2025-06-04 08:11:13,860 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:14,313 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-04 08:11:14,313 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26563.36, 'new_value': 39512.48}, {'field': 'dailyBillAmount', 'old_value': 26563.36, 'new_value': 39512.48}, {'field': 'amount', 'old_value': 15391.52, 'new_value': 24166.97}, {'field': 'count', 'old_value': 69, 'new_value': 106}, {'field': 'instoreAmount', 'old_value': 16188.12, 'new_value': 24999.52}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 106}]
2025-06-04 08:11:14,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:14,735 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-04 08:11:14,735 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17273.37, 'new_value': 23303.54}, {'field': 'dailyBillAmount', 'old_value': 17273.37, 'new_value': 23303.54}, {'field': 'amount', 'old_value': 8173.38, 'new_value': 10782.630000000001}, {'field': 'count', 'old_value': 386, 'new_value': 524}, {'field': 'instoreAmount', 'old_value': 8451.92, 'new_value': 11107.17}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 524}]
2025-06-04 08:11:14,735 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:15,172 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-04 08:11:15,188 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64372.3, 'new_value': 73925.3}, {'field': 'dailyBillAmount', 'old_value': 64372.3, 'new_value': 73925.3}, {'field': 'amount', 'old_value': 64372.3, 'new_value': 73925.3}, {'field': 'count', 'old_value': 80, 'new_value': 94}, {'field': 'instoreAmount', 'old_value': 64372.3, 'new_value': 73925.3}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 94}]
2025-06-04 08:11:15,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:15,563 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-04 08:11:15,563 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47334.0, 'new_value': 51099.7}, {'field': 'dailyBillAmount', 'old_value': 47334.0, 'new_value': 51099.7}, {'field': 'amount', 'old_value': 34990.6, 'new_value': 37887.9}, {'field': 'count', 'old_value': 85, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 34990.6, 'new_value': 37887.9}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 91}]
2025-06-04 08:11:15,563 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:16,000 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-04 08:11:16,000 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3901.0, 'new_value': 6046.0}, {'field': 'dailyBillAmount', 'old_value': 3901.0, 'new_value': 6046.0}, {'field': 'amount', 'old_value': 3901.0, 'new_value': 6046.0}, {'field': 'count', 'old_value': 79, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 3901.0, 'new_value': 6046.0}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 121}]
2025-06-04 08:11:16,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:16,453 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-04 08:11:16,453 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8587.05, 'new_value': 11467.43}, {'field': 'dailyBillAmount', 'old_value': 8587.05, 'new_value': 11467.43}, {'field': 'amount', 'old_value': 8614.39, 'new_value': 11548.37}, {'field': 'count', 'old_value': 467, 'new_value': 603}, {'field': 'instoreAmount', 'old_value': 4863.9400000000005, 'new_value': 6205.320000000001}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 321}, {'field': 'onlineAmount', 'old_value': 3902.6499999999996, 'new_value': 5534.15}, {'field': 'onlineCount', 'old_value': 216, 'new_value': 282}]
2025-06-04 08:11:16,453 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:16,922 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-04 08:11:16,922 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5209.95, 'new_value': 7985.9}, {'field': 'dailyBillAmount', 'old_value': 5209.95, 'new_value': 7985.9}, {'field': 'amount', 'old_value': 5190.86, 'new_value': 7966.959999999999}, {'field': 'count', 'old_value': 185, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 3663.5, 'new_value': 5642.4}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 183}, {'field': 'onlineAmount', 'old_value': 1630.1599999999999, 'new_value': 2427.36}, {'field': 'onlineCount', 'old_value': 75, 'new_value': 114}]
2025-06-04 08:11:16,922 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:17,360 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-04 08:11:17,360 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2190.1, 'new_value': 4163.22}, {'field': 'count', 'old_value': 41, 'new_value': 71}, {'field': 'instoreAmount', 'old_value': 2190.9900000000002, 'new_value': 4164.11}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 71}]
2025-06-04 08:11:17,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:17,813 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-04 08:11:17,813 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11393.0, 'new_value': 12806.8}, {'field': 'amount', 'old_value': 11392.2, 'new_value': 12806.0}, {'field': 'count', 'old_value': 223, 'new_value': 269}, {'field': 'instoreAmount', 'old_value': 11393.0, 'new_value': 12857.2}, {'field': 'instoreCount', 'old_value': 223, 'new_value': 269}]
2025-06-04 08:11:17,813 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:18,313 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-04 08:11:18,313 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47864.0, 'new_value': 53273.0}, {'field': 'dailyBillAmount', 'old_value': 47864.0, 'new_value': 53273.0}, {'field': 'amount', 'old_value': 18347.0, 'new_value': 21350.0}, {'field': 'count', 'old_value': 49, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 18347.0, 'new_value': 21350.0}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 61}]
2025-06-04 08:11:18,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:18,672 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-04 08:11:18,672 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9299.16, 'new_value': 12774.5}, {'field': 'dailyBillAmount', 'old_value': 9299.16, 'new_value': 12774.5}, {'field': 'amount', 'old_value': 8847.0, 'new_value': 12322.34}, {'field': 'count', 'old_value': 39, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 9086.16, 'new_value': 12561.5}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 51}]
2025-06-04 08:11:18,672 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:19,141 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-04 08:11:19,141 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8275.0, 'new_value': 8998.0}, {'field': 'dailyBillAmount', 'old_value': 8275.0, 'new_value': 8998.0}, {'field': 'amount', 'old_value': 10097.0, 'new_value': 11285.0}, {'field': 'count', 'old_value': 12, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 10097.0, 'new_value': 11285.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 15}]
2025-06-04 08:11:19,157 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:19,610 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-04 08:11:19,610 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7853.8, 'new_value': 14189.900000000001}, {'field': 'dailyBillAmount', 'old_value': 7853.8, 'new_value': 14189.900000000001}, {'field': 'amount', 'old_value': 7853.7, 'new_value': 14189.8}, {'field': 'count', 'old_value': 17, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 8091.8, 'new_value': 15000.5}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 33}]
2025-06-04 08:11:19,610 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:19,985 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-04 08:11:19,985 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19821.0, 'new_value': 23141.2}, {'field': 'dailyBillAmount', 'old_value': 19821.0, 'new_value': 23141.2}, {'field': 'amount', 'old_value': 12156.0, 'new_value': 13575.55}, {'field': 'count', 'old_value': 269, 'new_value': 313}, {'field': 'instoreAmount', 'old_value': 11743.06, 'new_value': 13035.16}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 280}, {'field': 'onlineAmount', 'old_value': 1232.71, 'new_value': 1360.1599999999999}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 33}]
2025-06-04 08:11:19,985 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:20,438 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-04 08:11:20,438 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10911.369999999999, 'new_value': 18027.27}, {'field': 'dailyBillAmount', 'old_value': 10869.45, 'new_value': 17973.620000000003}, {'field': 'amount', 'old_value': 10910.380000000001, 'new_value': 18026.28}, {'field': 'count', 'old_value': 129, 'new_value': 223}, {'field': 'instoreAmount', 'old_value': 10717.619999999999, 'new_value': 17785.62}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 220}, {'field': 'onlineAmount', 'old_value': 193.75, 'new_value': 241.65}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}]
2025-06-04 08:11:20,438 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:20,907 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-04 08:11:20,907 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10341.46, 'new_value': 12259.37}, {'field': 'dailyBillAmount', 'old_value': 10341.46, 'new_value': 12259.37}, {'field': 'amount', 'old_value': 10715.0, 'new_value': 12752.27}, {'field': 'count', 'old_value': 57, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 9560.880000000001, 'new_value': 11401.26}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 59}, {'field': 'onlineAmount', 'old_value': 1155.4499999999998, 'new_value': 1352.34}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 11}]
2025-06-04 08:11:20,907 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:21,344 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-04 08:11:21,344 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19315.0, 'new_value': 23637.9}, {'field': 'dailyBillAmount', 'old_value': 19315.0, 'new_value': 23637.9}, {'field': 'amount', 'old_value': 23424.0, 'new_value': 28511.9}, {'field': 'count', 'old_value': 86, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 23636.0, 'new_value': 29323.9}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 107}]
2025-06-04 08:11:21,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:21,735 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-04 08:11:21,735 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4194.0, 'new_value': 5500.0}, {'field': 'dailyBillAmount', 'old_value': 4194.0, 'new_value': 5500.0}, {'field': 'amount', 'old_value': 4194.0, 'new_value': 5500.0}, {'field': 'count', 'old_value': 10, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 4273.0, 'new_value': 5579.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 14}]
2025-06-04 08:11:21,735 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:21,735 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:22,188 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-04 08:11:22,188 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3814.14, 'new_value': 6652.86}, {'field': 'amount', 'old_value': 3813.9300000000003, 'new_value': 6652.65}, {'field': 'count', 'old_value': 184, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 4116.55, 'new_value': 7108.4400000000005}, {'field': 'instoreCount', 'old_value': 184, 'new_value': 318}]
2025-06-04 08:11:22,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:22,594 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-04 08:11:22,594 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16682.21, 'new_value': 19694.559999999998}, {'field': 'dailyBillAmount', 'old_value': 16682.21, 'new_value': 19694.559999999998}, {'field': 'amount', 'old_value': 10365.8, 'new_value': 12489.48}, {'field': 'count', 'old_value': 42, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 10247.7, 'new_value': 12335.7}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 51}, {'field': 'onlineAmount', 'old_value': 119.0, 'new_value': 155.1}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 3}]
2025-06-04 08:11:22,594 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:23,063 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-04 08:11:23,063 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37579.17, 'new_value': 48982.520000000004}, {'field': 'dailyBillAmount', 'old_value': 37579.17, 'new_value': 48982.520000000004}, {'field': 'amount', 'old_value': 14389.5, 'new_value': 18746.43}, {'field': 'count', 'old_value': 126, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 8898.07, 'new_value': 10612.9}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 76}, {'field': 'onlineAmount', 'old_value': 5492.1, 'new_value': 8134.2}, {'field': 'onlineCount', 'old_value': 68, 'new_value': 105}]
2025-06-04 08:11:23,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:23,469 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-04 08:11:23,469 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'amount', 'old_value': 34124.0, 'new_value': 39772.0}, {'field': 'count', 'old_value': 177, 'new_value': 208}, {'field': 'instoreAmount', 'old_value': 34124.8, 'new_value': 39772.8}, {'field': 'instoreCount', 'old_value': 177, 'new_value': 208}]
2025-06-04 08:11:23,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:24,016 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-04 08:11:24,016 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12797.880000000001, 'new_value': 19171.67}, {'field': 'dailyBillAmount', 'old_value': 12797.880000000001, 'new_value': 19171.67}, {'field': 'amount', 'old_value': 5887.1900000000005, 'new_value': 8765.04}, {'field': 'count', 'old_value': 61, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 5699.889999999999, 'new_value': 8670.89}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 87}, {'field': 'onlineAmount', 'old_value': 253.4, 'new_value': 381.4}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 10}]
2025-06-04 08:11:24,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:24,422 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-04 08:11:24,422 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 623.85, 'new_value': 672.85}, {'field': 'count', 'old_value': 9, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 624.55, 'new_value': 673.55}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-06-04 08:11:24,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:24,875 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-04 08:11:24,875 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20421.27, 'new_value': 27426.42}, {'field': 'dailyBillAmount', 'old_value': 20421.27, 'new_value': 27426.42}, {'field': 'amount', 'old_value': 20421.27, 'new_value': 27426.42}, {'field': 'count', 'old_value': 73, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 20421.27, 'new_value': 27426.42}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 98}]
2025-06-04 08:11:24,875 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:25,250 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-04 08:11:25,250 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4004.96, 'new_value': 5014.04}, {'field': 'dailyBillAmount', 'old_value': 4004.96, 'new_value': 5014.04}, {'field': 'amount', 'old_value': 4372.29, 'new_value': 5404.37}, {'field': 'count', 'old_value': 119, 'new_value': 149}, {'field': 'instoreAmount', 'old_value': 4373.26, 'new_value': 5405.34}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 149}]
2025-06-04 08:11:25,250 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:25,656 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-04 08:11:25,656 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25986.0, 'new_value': 37578.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12440.0}, {'field': 'amount', 'old_value': 25986.0, 'new_value': 37578.0}, {'field': 'count', 'old_value': 45, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 25986.0, 'new_value': 37578.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 64}]
2025-06-04 08:11:25,656 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:26,125 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-04 08:11:26,125 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3665.34, 'new_value': 5783.35}, {'field': 'amount', 'old_value': 3664.4, 'new_value': 5782.41}, {'field': 'count', 'old_value': 36, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 3665.34, 'new_value': 5783.35}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 51}]
2025-06-04 08:11:26,125 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:26,547 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-04 08:11:26,547 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38424.0, 'new_value': 52772.0}, {'field': 'amount', 'old_value': 38424.0, 'new_value': 52772.0}, {'field': 'count', 'old_value': 12, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 38424.0, 'new_value': 52772.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 14}]
2025-06-04 08:11:26,547 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:26,953 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-04 08:11:26,953 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21653.0, 'new_value': 27408.0}, {'field': 'dailyBillAmount', 'old_value': 21653.0, 'new_value': 24096.0}, {'field': 'amount', 'old_value': 21653.0, 'new_value': 27408.0}, {'field': 'count', 'old_value': 104, 'new_value': 134}, {'field': 'instoreAmount', 'old_value': 21653.0, 'new_value': 27408.0}, {'field': 'instoreCount', 'old_value': 104, 'new_value': 134}]
2025-06-04 08:11:26,953 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:27,469 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-04 08:11:27,469 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6867.0, 'new_value': 8237.7}, {'field': 'count', 'old_value': 54, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 6867.8, 'new_value': 8238.5}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 72}]
2025-06-04 08:11:27,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:28,000 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-04 08:11:28,000 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1324.9, 'new_value': 2390.9}, {'field': 'dailyBillAmount', 'old_value': 1324.9, 'new_value': 2390.9}, {'field': 'amount', 'old_value': 1324.9, 'new_value': 2390.9}, {'field': 'count', 'old_value': 2, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 1324.9, 'new_value': 2390.9}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 4}]
2025-06-04 08:11:28,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:28,469 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-04 08:11:28,469 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35649.72, 'new_value': 45924.72}, {'field': 'dailyBillAmount', 'old_value': 35649.72, 'new_value': 45924.72}, {'field': 'amount', 'old_value': 35649.0, 'new_value': 45924.0}, {'field': 'count', 'old_value': 109, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 35649.72, 'new_value': 45924.72}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 141}]
2025-06-04 08:11:28,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:28,797 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-04 08:11:28,797 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45706.0, 'new_value': 72098.0}, {'field': 'dailyBillAmount', 'old_value': 45706.0, 'new_value': 72098.0}, {'field': 'amount', 'old_value': 87463.55, 'new_value': 111916.88}, {'field': 'count', 'old_value': 117, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 87463.55, 'new_value': 111916.88}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 147}]
2025-06-04 08:11:28,797 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:29,188 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-04 08:11:29,188 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9435.4, 'new_value': 12846.099999999999}, {'field': 'dailyBillAmount', 'old_value': 9435.4, 'new_value': 12846.099999999999}, {'field': 'amount', 'old_value': 4247.5, 'new_value': 5838.4}, {'field': 'count', 'old_value': 17, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 4247.5, 'new_value': 5838.4}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 23}]
2025-06-04 08:11:29,188 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-04 08:11:29,781 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-06-04 08:11:29,781 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 213466.68, 'new_value': 213469.68}, {'field': 'count', 'old_value': 8926, 'new_value': 8927}, {'field': 'onlineAmount', 'old_value': 2805.0099999999998, 'new_value': 2808.0099999999998}, {'field': 'onlineCount', 'old_value': 88, 'new_value': 89}]
2025-06-04 08:11:29,781 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-04 08:11:30,235 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMP71
2025-06-04 08:11:30,235 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 351491.77, 'new_value': 396045.46}, {'field': 'dailyBillAmount', 'old_value': 351491.77, 'new_value': 396045.46}]
2025-06-04 08:11:30,235 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-04 08:11:30,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-06-04 08:11:30,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 570069.96, 'new_value': 570877.96}, {'field': 'count', 'old_value': 6311, 'new_value': 6312}, {'field': 'instoreAmount', 'old_value': 388590.62, 'new_value': 389398.62}, {'field': 'instoreCount', 'old_value': 2679, 'new_value': 2680}]
2025-06-04 08:11:30,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:31,094 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-04 08:11:31,094 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35240.68, 'new_value': 37831.9}, {'field': 'amount', 'old_value': 35239.75, 'new_value': 37830.97}, {'field': 'count', 'old_value': 339, 'new_value': 379}, {'field': 'instoreAmount', 'old_value': 28128.83, 'new_value': 29056.01}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 262}, {'field': 'onlineAmount', 'old_value': 7827.049999999999, 'new_value': 9491.09}, {'field': 'onlineCount', 'old_value': 90, 'new_value': 117}]
2025-06-04 08:11:31,094 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:31,453 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-04 08:11:31,453 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34436.95, 'new_value': 44665.78}, {'field': 'dailyBillAmount', 'old_value': 34436.95, 'new_value': 44665.78}, {'field': 'amount', 'old_value': 3514.52, 'new_value': 3792.36}, {'field': 'count', 'old_value': 69, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 3718.2, 'new_value': 4116.639999999999}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 92}]
2025-06-04 08:11:31,453 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:31,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-04 08:11:31,938 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56513.0, 'new_value': 62038.89}, {'field': 'dailyBillAmount', 'old_value': 56513.0, 'new_value': 62038.89}, {'field': 'amount', 'old_value': 21258.4, 'new_value': 25891.23}, {'field': 'count', 'old_value': 460, 'new_value': 540}, {'field': 'instoreAmount', 'old_value': 21587.559999999998, 'new_value': 25425.91}, {'field': 'instoreCount', 'old_value': 455, 'new_value': 520}, {'field': 'onlineAmount', 'old_value': 194.0, 'new_value': 988.48}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 20}]
2025-06-04 08:11:31,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:32,360 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-04 08:11:32,375 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31740.1, 'new_value': 36348.4}, {'field': 'dailyBillAmount', 'old_value': 51834.8, 'new_value': 59018.0}, {'field': 'amount', 'old_value': 31739.0, 'new_value': 36347.3}, {'field': 'count', 'old_value': 121, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 32862.0, 'new_value': 37470.3}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 138}]
2025-06-04 08:11:32,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:32,860 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-04 08:11:32,860 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'amount', 'old_value': 111871.0, 'new_value': 115965.63}, {'field': 'count', 'old_value': 1910, 'new_value': 1978}, {'field': 'instoreAmount', 'old_value': 106902.89, 'new_value': 109979.53}, {'field': 'instoreCount', 'old_value': 1814, 'new_value': 1859}, {'field': 'onlineAmount', 'old_value': 5396.81, 'new_value': 6414.8}, {'field': 'onlineCount', 'old_value': 96, 'new_value': 119}]
2025-06-04 08:11:32,860 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:33,281 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-04 08:11:33,281 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47100.56, 'new_value': 65348.46000000001}, {'field': 'amount', 'old_value': 47099.7, 'new_value': 65347.600000000006}, {'field': 'count', 'old_value': 1019, 'new_value': 1463}, {'field': 'instoreAmount', 'old_value': 35851.06, 'new_value': 47889.46}, {'field': 'instoreCount', 'old_value': 705, 'new_value': 971}, {'field': 'onlineAmount', 'old_value': 11249.5, 'new_value': 17459.0}, {'field': 'onlineCount', 'old_value': 314, 'new_value': 492}]
2025-06-04 08:11:33,281 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:33,750 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-04 08:11:33,750 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -2143.46, 'new_value': -2122.46}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'onlineAmount', 'old_value': 136.0, 'new_value': 157.0}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 7}]
2025-06-04 08:11:33,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:34,172 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-04 08:11:34,172 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'amount', 'old_value': 14464.0, 'new_value': 18957.25}, {'field': 'count', 'old_value': 954, 'new_value': 1243}, {'field': 'instoreAmount', 'old_value': 11125.25, 'new_value': 14225.19}, {'field': 'instoreCount', 'old_value': 648, 'new_value': 839}, {'field': 'onlineAmount', 'old_value': 3735.44, 'new_value': 5194.45}, {'field': 'onlineCount', 'old_value': 306, 'new_value': 404}]
2025-06-04 08:11:34,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:34,547 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-04 08:11:34,547 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39256.8, 'new_value': 44675.659999999996}, {'field': 'dailyBillAmount', 'old_value': 39256.8, 'new_value': 44675.659999999996}, {'field': 'amount', 'old_value': 39277.86, 'new_value': 44051.86}, {'field': 'count', 'old_value': 1121, 'new_value': 1269}, {'field': 'instoreAmount', 'old_value': 39385.259999999995, 'new_value': 44160.06}, {'field': 'instoreCount', 'old_value': 1121, 'new_value': 1269}]
2025-06-04 08:11:34,547 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:34,969 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-04 08:11:34,969 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13949.330000000002, 'new_value': 16654.48}, {'field': 'amount', 'old_value': 13949.29, 'new_value': 16654.440000000002}, {'field': 'count', 'old_value': 770, 'new_value': 927}, {'field': 'instoreAmount', 'old_value': 8406.39, 'new_value': 9252.84}, {'field': 'instoreCount', 'old_value': 524, 'new_value': 588}, {'field': 'onlineAmount', 'old_value': 5542.9400000000005, 'new_value': 7401.64}, {'field': 'onlineCount', 'old_value': 246, 'new_value': 339}]
2025-06-04 08:11:34,969 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:35,375 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-04 08:11:35,375 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19931.66, 'new_value': 23133.75}, {'field': 'dailyBillAmount', 'old_value': 19931.66, 'new_value': 23133.75}, {'field': 'amount', 'old_value': 3685.3, 'new_value': 4412.42}, {'field': 'count', 'old_value': 106, 'new_value': 134}, {'field': 'instoreAmount', 'old_value': 3685.4, 'new_value': 4439.88}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 134}]
2025-06-04 08:11:35,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:35,813 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-04 08:11:35,813 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20355.47, 'new_value': 23321.26}, {'field': 'dailyBillAmount', 'old_value': 20355.47, 'new_value': 23321.26}, {'field': 'amount', 'old_value': 10521.18, 'new_value': 12690.060000000001}, {'field': 'count', 'old_value': 511, 'new_value': 625}, {'field': 'instoreAmount', 'old_value': 2330.06, 'new_value': 2663.79}, {'field': 'instoreCount', 'old_value': 156, 'new_value': 176}, {'field': 'onlineAmount', 'old_value': 8453.7, 'new_value': 10374.8}, {'field': 'onlineCount', 'old_value': 355, 'new_value': 449}]
2025-06-04 08:11:35,813 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:36,235 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-04 08:11:36,235 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15567.490000000002, 'new_value': 18612.43}, {'field': 'amount', 'old_value': 15566.94, 'new_value': 18611.88}, {'field': 'count', 'old_value': 479, 'new_value': 546}, {'field': 'instoreAmount', 'old_value': 14745.82, 'new_value': 17749.66}, {'field': 'instoreCount', 'old_value': 468, 'new_value': 534}, {'field': 'onlineAmount', 'old_value': 828.97, 'new_value': 870.07}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 12}]
2025-06-04 08:11:36,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:36,688 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-04 08:11:36,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 15135.720000000001, 'new_value': 20074.03}, {'field': 'count', 'old_value': 857, 'new_value': 1112}, {'field': 'instoreAmount', 'old_value': 16178.62, 'new_value': 21163.33}, {'field': 'instoreCount', 'old_value': 844, 'new_value': 1099}]
2025-06-04 08:11:36,703 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:37,172 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-04 08:11:37,172 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 25031.87}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 25031.87}, {'field': 'amount', 'old_value': 10281.0, 'new_value': 14071.02}, {'field': 'count', 'old_value': 773, 'new_value': 1017}, {'field': 'instoreAmount', 'old_value': 948.0, 'new_value': 1018.0}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 69}, {'field': 'onlineAmount', 'old_value': 9858.66, 'new_value': 13731.189999999999}, {'field': 'onlineCount', 'old_value': 708, 'new_value': 948}]
2025-06-04 08:11:37,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:37,563 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-04 08:11:37,563 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30337.57, 'new_value': 34043.63}, {'field': 'dailyBillAmount', 'old_value': 30337.57, 'new_value': 34043.63}, {'field': 'amount', 'old_value': 23051.0, 'new_value': 26876.2}, {'field': 'count', 'old_value': 725, 'new_value': 811}, {'field': 'instoreAmount', 'old_value': 14291.73, 'new_value': 15814.73}, {'field': 'instoreCount', 'old_value': 586, 'new_value': 643}, {'field': 'onlineAmount', 'old_value': 9447.2, 'new_value': 12071.4}, {'field': 'onlineCount', 'old_value': 139, 'new_value': 168}]
2025-06-04 08:11:37,563 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:38,000 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-04 08:11:38,000 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7166.049999999999, 'new_value': 9051.59}, {'field': 'dailyBillAmount', 'old_value': 7166.049999999999, 'new_value': 9051.59}, {'field': 'amount', 'old_value': 7928.26, 'new_value': 10211.29}, {'field': 'count', 'old_value': 266, 'new_value': 344}, {'field': 'instoreAmount', 'old_value': 3714.8900000000003, 'new_value': 3960.56}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 150}, {'field': 'onlineAmount', 'old_value': 4301.64, 'new_value': 6355.0}, {'field': 'onlineCount', 'old_value': 134, 'new_value': 194}]
2025-06-04 08:11:38,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:38,422 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-04 08:11:38,422 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18589.58, 'new_value': 19899.57}, {'field': 'dailyBillAmount', 'old_value': 18589.58, 'new_value': 19899.57}, {'field': 'amount', 'old_value': 19122.510000000002, 'new_value': 20560.52}, {'field': 'count', 'old_value': 583, 'new_value': 647}, {'field': 'instoreAmount', 'old_value': 19144.95, 'new_value': 20582.96}, {'field': 'instoreCount', 'old_value': 582, 'new_value': 646}]
2025-06-04 08:11:38,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:38,953 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-04 08:11:38,953 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'amount', 'old_value': 49004.0, 'new_value': 53410.0}, {'field': 'count', 'old_value': 35, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 51106.0, 'new_value': 55512.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 40}]
2025-06-04 08:11:38,953 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:39,438 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-04 08:11:39,438 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'amount', 'old_value': 26193.0, 'new_value': 28755.6}, {'field': 'count', 'old_value': 51, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 26627.41, 'new_value': 29190.01}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 59}]
2025-06-04 08:11:39,438 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:39,906 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-04 08:11:39,906 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6800.0, 'new_value': 7961.0}, {'field': 'dailyBillAmount', 'old_value': 6800.0, 'new_value': 7961.0}, {'field': 'amount', 'old_value': 1535.0, 'new_value': 1834.0}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 1535.0, 'new_value': 1834.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-06-04 08:11:39,906 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:40,391 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-04 08:11:40,391 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7250.0, 'new_value': 8372.0}, {'field': 'amount', 'old_value': 7250.0, 'new_value': 8372.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 7250.0, 'new_value': 8372.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-04 08:11:40,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:40,844 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-04 08:11:40,844 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10454.5, 'new_value': 11846.5}, {'field': 'amount', 'old_value': 10454.5, 'new_value': 11846.5}, {'field': 'count', 'old_value': 25, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 10454.5, 'new_value': 11846.5}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 29}]
2025-06-04 08:11:40,844 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:41,297 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-04 08:11:41,297 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53915.0, 'new_value': 72312.0}, {'field': 'dailyBillAmount', 'old_value': 53915.0, 'new_value': 72312.0}, {'field': 'amount', 'old_value': 53915.0, 'new_value': 72312.0}, {'field': 'count', 'old_value': 6, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 53915.0, 'new_value': 72312.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 9}]
2025-06-04 08:11:41,297 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:41,828 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-04 08:11:41,828 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2514.0, 'new_value': 4474.0}, {'field': 'amount', 'old_value': 2514.0, 'new_value': 4474.0}, {'field': 'count', 'old_value': 5, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 2514.0, 'new_value': 4474.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 8}]
2025-06-04 08:11:41,828 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:42,234 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-04 08:11:42,234 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33203.8, 'new_value': 39135.8}, {'field': 'dailyBillAmount', 'old_value': 33203.8, 'new_value': 39135.8}, {'field': 'amount', 'old_value': 32370.2, 'new_value': 38302.2}, {'field': 'count', 'old_value': 44, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 32749.6, 'new_value': 39030.6}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 51}]
2025-06-04 08:11:42,234 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:42,688 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-04 08:11:42,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 683.1, 'new_value': 1727.1999999999998}, {'field': 'count', 'old_value': 9, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 968.6}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 9}, {'field': 'onlineAmount', 'old_value': 683.5, 'new_value': 759.0}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 11}]
2025-06-04 08:11:42,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:43,110 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-04 08:11:43,110 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2089.0, 'new_value': 3365.0}, {'field': 'dailyBillAmount', 'old_value': 2089.0, 'new_value': 3365.0}, {'field': 'amount', 'old_value': 2084.0, 'new_value': 3572.0}, {'field': 'count', 'old_value': 9, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 2275.0, 'new_value': 3763.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 14}]
2025-06-04 08:11:43,110 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:43,578 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-04 08:11:43,578 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5636.700000000001, 'new_value': 6695.700000000001}, {'field': 'amount', 'old_value': 5635.9, 'new_value': 6694.9}, {'field': 'count', 'old_value': 30, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 5744.700000000001, 'new_value': 6803.700000000001}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 37}]
2025-06-04 08:11:43,578 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:44,000 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-04 08:11:44,000 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1079.0, 'new_value': 1277.0}, {'field': 'dailyBillAmount', 'old_value': 1079.0, 'new_value': 1277.0}, {'field': 'amount', 'old_value': 9799.0, 'new_value': 10195.0}, {'field': 'count', 'old_value': 19, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 9799.0, 'new_value': 10195.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 21}]
2025-06-04 08:11:44,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:44,453 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-04 08:11:44,453 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 207213.9, 'new_value': 221790.78}, {'field': 'dailyBillAmount', 'old_value': 207213.9, 'new_value': 221790.78}, {'field': 'amount', 'old_value': 12242.0, 'new_value': 13574.84}, {'field': 'count', 'old_value': 117, 'new_value': 137}, {'field': 'instoreAmount', 'old_value': 9606.369999999999, 'new_value': 10748.859999999999}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 100}, {'field': 'onlineAmount', 'old_value': 2636.8, 'new_value': 2944.2599999999998}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 37}]
2025-06-04 08:11:44,453 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:44,891 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-04 08:11:44,891 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5391.0, 'new_value': 6859.0}, {'field': 'amount', 'old_value': 5391.0, 'new_value': 6859.0}, {'field': 'count', 'old_value': 10, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 5790.0, 'new_value': 7258.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 12}]
2025-06-04 08:11:44,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:45,359 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVR
2025-06-04 08:11:45,359 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 747.0, 'new_value': 1046.0}, {'field': 'amount', 'old_value': 747.0, 'new_value': 1046.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 747.0, 'new_value': 1046.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-04 08:11:45,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:45,813 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-04 08:11:45,813 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5222.799999999999, 'new_value': 5738.799999999999}, {'field': 'amount', 'old_value': 5222.799999999999, 'new_value': 5738.799999999999}, {'field': 'count', 'old_value': 15, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 5222.799999999999, 'new_value': 5738.799999999999}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 18}]
2025-06-04 08:11:45,813 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:46,297 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-04 08:11:46,297 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2789.0, 'new_value': 3526.0}, {'field': 'dailyBillAmount', 'old_value': 2789.0, 'new_value': 3526.0}, {'field': 'amount', 'old_value': 2842.0, 'new_value': 3579.0}, {'field': 'count', 'old_value': 12, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 4477.0, 'new_value': 5214.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 14}]
2025-06-04 08:11:46,297 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:46,688 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-04 08:11:46,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38555.08, 'new_value': 42567.08}, {'field': 'dailyBillAmount', 'old_value': 36755.24, 'new_value': 40767.24}, {'field': 'amount', 'old_value': 38416.46, 'new_value': 40432.03}, {'field': 'count', 'old_value': 222, 'new_value': 239}, {'field': 'instoreAmount', 'old_value': 38417.1, 'new_value': 40432.67}, {'field': 'instoreCount', 'old_value': 222, 'new_value': 239}]
2025-06-04 08:11:46,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:47,125 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-04 08:11:47,125 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28024.0, 'new_value': 29358.0}, {'field': 'amount', 'old_value': 28024.0, 'new_value': 29358.0}, {'field': 'count', 'old_value': 146, 'new_value': 151}, {'field': 'instoreAmount', 'old_value': 29068.0, 'new_value': 30402.0}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 151}]
2025-06-04 08:11:47,125 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:47,641 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0S
2025-06-04 08:11:47,641 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5940.0, 'new_value': 8780.0}, {'field': 'amount', 'old_value': 5940.0, 'new_value': 8780.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 5940.0, 'new_value': 8780.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-04 08:11:47,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:48,078 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-04 08:11:48,078 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35078.5, 'new_value': 39878.5}, {'field': 'dailyBillAmount', 'old_value': 35078.5, 'new_value': 39878.5}, {'field': 'amount', 'old_value': 34782.5, 'new_value': 39582.5}, {'field': 'count', 'old_value': 184, 'new_value': 215}, {'field': 'instoreAmount', 'old_value': 34782.5, 'new_value': 39582.5}, {'field': 'instoreCount', 'old_value': 184, 'new_value': 215}]
2025-06-04 08:11:48,078 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:48,719 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-04 08:11:48,719 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10588.1, 'new_value': 12326.470000000001}, {'field': 'dailyBillAmount', 'old_value': 10588.1, 'new_value': 12326.470000000001}, {'field': 'amount', 'old_value': 7190.0, 'new_value': 7710.52}, {'field': 'count', 'old_value': 562, 'new_value': 633}, {'field': 'instoreAmount', 'old_value': 7415.91, 'new_value': 7936.43}, {'field': 'instoreCount', 'old_value': 562, 'new_value': 633}]
2025-06-04 08:11:48,719 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:49,219 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-04 08:11:49,219 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 79276.76000000001, 'new_value': 91100.37}, {'field': 'dailyBillAmount', 'old_value': 79276.76000000001, 'new_value': 91100.37}, {'field': 'amount', 'old_value': 80427.0, 'new_value': 90162.38}, {'field': 'count', 'old_value': 661, 'new_value': 881}, {'field': 'instoreAmount', 'old_value': 72455.65, 'new_value': 79198.81}, {'field': 'instoreCount', 'old_value': 342, 'new_value': 386}, {'field': 'onlineAmount', 'old_value': 8249.91, 'new_value': 11378.68}, {'field': 'onlineCount', 'old_value': 319, 'new_value': 495}]
2025-06-04 08:11:49,219 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:49,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-04 08:11:49,750 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34914.5, 'new_value': 35466.5}, {'field': 'amount', 'old_value': 34914.5, 'new_value': 35466.5}, {'field': 'count', 'old_value': 201, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 34914.5, 'new_value': 35466.5}, {'field': 'instoreCount', 'old_value': 201, 'new_value': 206}]
2025-06-04 08:11:49,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:50,172 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-04 08:11:50,172 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6319.67, 'new_value': 16562.84}, {'field': 'count', 'old_value': 235, 'new_value': 622}, {'field': 'instoreAmount', 'old_value': 2480.8, 'new_value': 4481.9}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 162}, {'field': 'onlineAmount', 'old_value': 4100.17, 'new_value': 12429.59}, {'field': 'onlineCount', 'old_value': 149, 'new_value': 460}]
2025-06-04 08:11:50,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:50,656 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-04 08:11:50,656 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9773.66, 'new_value': 11596.130000000001}, {'field': 'dailyBillAmount', 'old_value': 9773.66, 'new_value': 11596.130000000001}, {'field': 'amount', 'old_value': 13462.0, 'new_value': 16316.2}, {'field': 'count', 'old_value': 666, 'new_value': 795}, {'field': 'instoreAmount', 'old_value': 8883.29, 'new_value': 9899.99}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 512}, {'field': 'onlineAmount', 'old_value': 4719.719999999999, 'new_value': 6571.92}, {'field': 'onlineCount', 'old_value': 219, 'new_value': 283}]
2025-06-04 08:11:50,656 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:51,188 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-04 08:11:51,188 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15317.25, 'new_value': 17740.92}, {'field': 'dailyBillAmount', 'old_value': 15317.25, 'new_value': 17740.92}, {'field': 'amount', 'old_value': 5885.110000000001, 'new_value': 7507.43}, {'field': 'count', 'old_value': 345, 'new_value': 452}, {'field': 'instoreAmount', 'old_value': 1696.6999999999998, 'new_value': 1746.6}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 79}, {'field': 'onlineAmount', 'old_value': 4189.01, 'new_value': 5761.43}, {'field': 'onlineCount', 'old_value': 269, 'new_value': 373}]
2025-06-04 08:11:51,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:51,609 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-04 08:11:51,609 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46707.130000000005, 'new_value': 55972.07}, {'field': 'dailyBillAmount', 'old_value': 46707.130000000005, 'new_value': 55972.07}, {'field': 'amount', 'old_value': 44388.0, 'new_value': 52797.91}, {'field': 'count', 'old_value': 297, 'new_value': 403}, {'field': 'instoreAmount', 'old_value': 38129.71, 'new_value': 43694.82}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 243}, {'field': 'onlineAmount', 'old_value': 6259.4, 'new_value': 9104.2}, {'field': 'onlineCount', 'old_value': 99, 'new_value': 160}]
2025-06-04 08:11:51,609 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:52,016 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-04 08:11:52,016 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56996.44, 'new_value': 62976.82}, {'field': 'dailyBillAmount', 'old_value': 56996.44, 'new_value': 62976.82}, {'field': 'amount', 'old_value': 53807.0, 'new_value': 58832.6}, {'field': 'count', 'old_value': 284, 'new_value': 312}, {'field': 'instoreAmount', 'old_value': 54216.5, 'new_value': 59242.1}, {'field': 'instoreCount', 'old_value': 284, 'new_value': 312}]
2025-06-04 08:11:52,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:52,406 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-04 08:11:52,406 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 121928.91, 'new_value': 146854.93}, {'field': 'dailyBillAmount', 'old_value': 121928.91, 'new_value': 146854.93}, {'field': 'amount', 'old_value': 134841.0, 'new_value': 162201.96}, {'field': 'count', 'old_value': 667, 'new_value': 860}, {'field': 'instoreAmount', 'old_value': 111667.35999999999, 'new_value': 129685.7}, {'field': 'instoreCount', 'old_value': 431, 'new_value': 503}, {'field': 'onlineAmount', 'old_value': 23229.6, 'new_value': 32832.42}, {'field': 'onlineCount', 'old_value': 236, 'new_value': 357}]
2025-06-04 08:11:52,406 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:52,828 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-04 08:11:52,828 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49341.29, 'new_value': 53388.3}, {'field': 'dailyBillAmount', 'old_value': 49341.29, 'new_value': 53388.3}, {'field': 'amount', 'old_value': 65720.1, 'new_value': 71810.6}, {'field': 'count', 'old_value': 281, 'new_value': 319}, {'field': 'instoreAmount', 'old_value': 62955.600000000006, 'new_value': 67784.2}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 262}, {'field': 'onlineAmount', 'old_value': 2970.6099999999997, 'new_value': 4232.51}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 57}]
2025-06-04 08:11:52,828 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:53,297 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-04 08:11:53,297 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53586.04000000001, 'new_value': 60594.94}, {'field': 'dailyBillAmount', 'old_value': 53586.04000000001, 'new_value': 60594.94}, {'field': 'amount', 'old_value': 51169.1, 'new_value': 57812.5}, {'field': 'count', 'old_value': 209, 'new_value': 237}, {'field': 'instoreAmount', 'old_value': 52129.0, 'new_value': 58829.4}, {'field': 'instoreCount', 'old_value': 209, 'new_value': 237}]
2025-06-04 08:11:53,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:53,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-04 08:11:53,750 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 113842.61, 'new_value': 132559.93}, {'field': 'amount', 'old_value': 113842.41, 'new_value': 132559.73}, {'field': 'count', 'old_value': 710, 'new_value': 910}, {'field': 'instoreAmount', 'old_value': 113842.61, 'new_value': 132559.93}, {'field': 'instoreCount', 'old_value': 710, 'new_value': 910}]
2025-06-04 08:11:53,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:54,203 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-04 08:11:54,203 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96733.10999999999, 'new_value': 114711.54999999999}, {'field': 'dailyBillAmount', 'old_value': 96733.10999999999, 'new_value': 114711.54999999999}, {'field': 'amount', 'old_value': 110963.0, 'new_value': 132156.9}, {'field': 'count', 'old_value': 710, 'new_value': 875}, {'field': 'instoreAmount', 'old_value': 74353.8, 'new_value': 81498.3}, {'field': 'instoreCount', 'old_value': 371, 'new_value': 407}, {'field': 'onlineAmount', 'old_value': 38048.8, 'new_value': 52483.2}, {'field': 'onlineCount', 'old_value': 339, 'new_value': 468}]
2025-06-04 08:11:54,203 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:54,641 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-04 08:11:54,641 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 74064.98000000001, 'new_value': 80017.26000000001}, {'field': 'dailyBillAmount', 'old_value': 74064.98000000001, 'new_value': 80017.26000000001}, {'field': 'amount', 'old_value': 74993.95999999999, 'new_value': 80107.86}, {'field': 'count', 'old_value': 587, 'new_value': 686}, {'field': 'instoreAmount', 'old_value': 62555.06, 'new_value': 64413.759999999995}, {'field': 'instoreCount', 'old_value': 368, 'new_value': 389}, {'field': 'onlineAmount', 'old_value': 12557.93, 'new_value': 15813.130000000001}, {'field': 'onlineCount', 'old_value': 219, 'new_value': 297}]
2025-06-04 08:11:54,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:55,078 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-04 08:11:55,078 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69082.09, 'new_value': 80558.9}, {'field': 'dailyBillAmount', 'old_value': 69082.09, 'new_value': 80558.9}, {'field': 'amount', 'old_value': 68935.53, 'new_value': 80713.32}, {'field': 'count', 'old_value': 477, 'new_value': 624}, {'field': 'instoreAmount', 'old_value': 64618.59, 'new_value': 73395.12}, {'field': 'instoreCount', 'old_value': 314, 'new_value': 372}, {'field': 'onlineAmount', 'old_value': 4343.4, 'new_value': 7344.66}, {'field': 'onlineCount', 'old_value': 163, 'new_value': 252}]
2025-06-04 08:11:55,078 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:55,516 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-04 08:11:55,516 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11762.0, 'new_value': 14812.4}, {'field': 'amount', 'old_value': 11762.0, 'new_value': 14812.4}, {'field': 'count', 'old_value': 70, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 11762.0, 'new_value': 14812.4}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 88}]
2025-06-04 08:11:55,516 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:56,016 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-04 08:11:56,016 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75343.12, 'new_value': 79141.62}, {'field': 'dailyBillAmount', 'old_value': 75343.12, 'new_value': 79141.62}, {'field': 'amount', 'old_value': -48274.0, 'new_value': -52095.9}, {'field': 'count', 'old_value': 110, 'new_value': 122}, {'field': 'instoreAmount', 'old_value': 1278.5, 'new_value': 1304.5}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 56}, {'field': 'onlineAmount', 'old_value': 1546.12, 'new_value': 1754.2199999999998}, {'field': 'onlineCount', 'old_value': 55, 'new_value': 66}]
2025-06-04 08:11:56,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:56,500 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-04 08:11:56,500 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24879.33, 'new_value': 38321.12}, {'field': 'dailyBillAmount', 'old_value': 24879.33, 'new_value': 38321.12}, {'field': 'amount', 'old_value': 59332.06, 'new_value': 66459.22}, {'field': 'count', 'old_value': 251, 'new_value': 287}, {'field': 'instoreAmount', 'old_value': 59332.81999999999, 'new_value': 66459.98}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 287}]
2025-06-04 08:11:56,500 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:56,922 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-04 08:11:56,922 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70156.5, 'new_value': 81396.21}, {'field': 'dailyBillAmount', 'old_value': 70156.5, 'new_value': 81396.21}, {'field': 'amount', 'old_value': 24775.0, 'new_value': 26792.5}, {'field': 'count', 'old_value': 111, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 25580.5, 'new_value': 27598.0}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 116}]
2025-06-04 08:11:56,922 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:57,359 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-04 08:11:57,359 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50883.64, 'new_value': 53954.01}, {'field': 'dailyBillAmount', 'old_value': 50883.64, 'new_value': 53954.01}, {'field': 'amount', 'old_value': 50270.81, 'new_value': 53164.56}, {'field': 'count', 'old_value': 262, 'new_value': 278}, {'field': 'instoreAmount', 'old_value': 49066.99, 'new_value': 51940.899999999994}, {'field': 'instoreCount', 'old_value': 228, 'new_value': 243}, {'field': 'onlineAmount', 'old_value': 1204.1, 'new_value': 1223.94}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 35}]
2025-06-04 08:11:57,359 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:57,875 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-04 08:11:57,875 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48219.79, 'new_value': 54895.07}, {'field': 'dailyBillAmount', 'old_value': 48219.79, 'new_value': 54895.07}, {'field': 'amount', 'old_value': 21707.760000000002, 'new_value': 24949.94}, {'field': 'count', 'old_value': 251, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 17688.25, 'new_value': 19689.940000000002}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 120}, {'field': 'onlineAmount', 'old_value': 4020.45, 'new_value': 5260.94}, {'field': 'onlineCount', 'old_value': 144, 'new_value': 194}]
2025-06-04 08:11:57,875 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:58,313 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMK1
2025-06-04 08:11:58,313 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7461.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7461.0}, {'field': 'amount', 'old_value': 7461.0, 'new_value': 8084.0}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 7461.0, 'new_value': 8084.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-06-04 08:11:58,328 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:58,828 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-04 08:11:58,828 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13048.82, 'new_value': 18099.45}, {'field': 'amount', 'old_value': 13048.01, 'new_value': 18098.84}, {'field': 'count', 'old_value': 651, 'new_value': 895}, {'field': 'instoreAmount', 'old_value': 3031.59, 'new_value': 4507.62}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 197}, {'field': 'onlineAmount', 'old_value': 10279.33, 'new_value': 13964.43}, {'field': 'onlineCount', 'old_value': 501, 'new_value': 698}]
2025-06-04 08:11:58,828 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:59,328 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-04 08:11:59,328 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4791.0, 'new_value': 8426.0}, {'field': 'amount', 'old_value': 4791.0, 'new_value': 8426.0}, {'field': 'count', 'old_value': 21, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 4791.0, 'new_value': 8426.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 37}]
2025-06-04 08:11:59,328 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:11:59,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-04 08:11:59,766 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56032.33, 'new_value': 65027.93}, {'field': 'dailyBillAmount', 'old_value': 56032.33, 'new_value': 65027.93}, {'field': 'amount', 'old_value': 24917.0, 'new_value': 28673.9}, {'field': 'count', 'old_value': 421, 'new_value': 486}, {'field': 'instoreAmount', 'old_value': 24982.6, 'new_value': 28774.5}, {'field': 'instoreCount', 'old_value': 421, 'new_value': 486}]
2025-06-04 08:11:59,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:00,219 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSS
2025-06-04 08:12:00,219 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29403.379999999997, 'new_value': 32257.2}, {'field': 'amount', 'old_value': 29402.0, 'new_value': 32255.82}, {'field': 'count', 'old_value': 634, 'new_value': 705}, {'field': 'instoreAmount', 'old_value': 29403.379999999997, 'new_value': 32257.2}, {'field': 'instoreCount', 'old_value': 634, 'new_value': 705}]
2025-06-04 08:12:00,219 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:00,672 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-04 08:12:00,672 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5058.2, 'new_value': 5911.68}, {'field': 'amount', 'old_value': 5057.0, 'new_value': 5910.48}, {'field': 'count', 'old_value': 276, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 2945.8999999999996, 'new_value': 3122.6}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 151}, {'field': 'onlineAmount', 'old_value': 2221.94, 'new_value': 2911.32}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 188}]
2025-06-04 08:12:00,672 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:01,125 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-04 08:12:01,125 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11866.9, 'new_value': 12709.2}, {'field': 'amount', 'old_value': 11866.9, 'new_value': 12709.2}, {'field': 'count', 'old_value': 29, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 11866.9, 'new_value': 12709.2}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 32}]
2025-06-04 08:12:01,125 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-04 08:12:01,547 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-06-04 08:12:01,547 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231138.65, 'new_value': 232878.65}, {'field': 'amount', 'old_value': 231137.97, 'new_value': 232877.73}, {'field': 'count', 'old_value': 3330, 'new_value': 3332}, {'field': 'instoreAmount', 'old_value': 220034.2, 'new_value': 221774.2}, {'field': 'instoreCount', 'old_value': 2879, 'new_value': 2881}]
2025-06-04 08:12:01,547 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-04 08:12:02,047 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-06-04 08:12:02,047 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96718.68, 'new_value': 96717.99}, {'field': 'amount', 'old_value': 96704.98, 'new_value': 96703.86}]
2025-06-04 08:12:02,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:02,453 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-04 08:12:02,453 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22410.63, 'new_value': 28478.06}, {'field': 'dailyBillAmount', 'old_value': 19394.1, 'new_value': 24168.1}, {'field': 'amount', 'old_value': 22410.260000000002, 'new_value': 28477.690000000002}, {'field': 'count', 'old_value': 264, 'new_value': 363}, {'field': 'instoreAmount', 'old_value': 21990.7, 'new_value': 27686.6}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 323}, {'field': 'onlineAmount', 'old_value': 448.93, 'new_value': 820.46}, {'field': 'onlineCount', 'old_value': 21, 'new_value': 40}]
2025-06-04 08:12:02,453 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:02,906 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-04 08:12:02,906 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3975.0, 'new_value': 4827.0}, {'field': 'amount', 'old_value': 3974.66, 'new_value': 4826.66}, {'field': 'count', 'old_value': 164, 'new_value': 205}, {'field': 'instoreAmount', 'old_value': 3541.5, 'new_value': 4191.7}, {'field': 'instoreCount', 'old_value': 152, 'new_value': 186}, {'field': 'onlineAmount', 'old_value': 467.9, 'new_value': 669.7}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 19}]
2025-06-04 08:12:02,906 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:03,359 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-04 08:12:03,359 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61172.73, 'new_value': 70041.52}, {'field': 'dailyBillAmount', 'old_value': 61172.73, 'new_value': 70041.52}, {'field': 'amount', 'old_value': 76885.0, 'new_value': 88481.74}, {'field': 'count', 'old_value': 454, 'new_value': 622}, {'field': 'instoreAmount', 'old_value': 75727.96, 'new_value': 85988.58}, {'field': 'instoreCount', 'old_value': 341, 'new_value': 443}, {'field': 'onlineAmount', 'old_value': 2758.46, 'new_value': 4517.48}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 179}]
2025-06-04 08:12:03,359 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:03,813 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-04 08:12:03,813 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10796.93, 'new_value': 16265.060000000001}, {'field': 'dailyBillAmount', 'old_value': 10796.93, 'new_value': 16265.060000000001}, {'field': 'amount', 'old_value': 5581.3, 'new_value': 6132.17}, {'field': 'count', 'old_value': 46, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 3874.8199999999997, 'new_value': 4301.6}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 35}, {'field': 'onlineAmount', 'old_value': 1730.4, 'new_value': 1854.49}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 28}]
2025-06-04 08:12:03,813 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:04,297 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-04 08:12:04,297 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9344.77, 'new_value': 14140.14}, {'field': 'dailyBillAmount', 'old_value': 9317.009999999998, 'new_value': 14170.93}, {'field': 'amount', 'old_value': 9344.77, 'new_value': 14140.14}, {'field': 'count', 'old_value': 482, 'new_value': 754}, {'field': 'instoreAmount', 'old_value': 4379.13, 'new_value': 6828.849999999999}, {'field': 'instoreCount', 'old_value': 197, 'new_value': 308}, {'field': 'onlineAmount', 'old_value': 4997.39, 'new_value': 7488.040000000001}, {'field': 'onlineCount', 'old_value': 285, 'new_value': 446}]
2025-06-04 08:12:04,297 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:04,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-04 08:12:04,750 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7151.67, 'new_value': 9688.98}, {'field': 'amount', 'old_value': 7151.46, 'new_value': 9688.77}, {'field': 'count', 'old_value': 408, 'new_value': 586}, {'field': 'instoreAmount', 'old_value': 4769.44, 'new_value': 5671.25}, {'field': 'instoreCount', 'old_value': 238, 'new_value': 301}, {'field': 'onlineAmount', 'old_value': 2679.91, 'new_value': 4444.95}, {'field': 'onlineCount', 'old_value': 170, 'new_value': 285}]
2025-06-04 08:12:04,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:05,234 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-04 08:12:05,234 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 27731.62, 'new_value': 29766.92}, {'field': 'count', 'old_value': 215, 'new_value': 250}, {'field': 'instoreAmount', 'old_value': 27745.1, 'new_value': 29780.399999999998}, {'field': 'instoreCount', 'old_value': 215, 'new_value': 250}]
2025-06-04 08:12:05,234 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:05,641 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-04 08:12:05,641 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11811.060000000001, 'new_value': 14269.07}, {'field': 'dailyBillAmount', 'old_value': 12091.58, 'new_value': 14686.27}, {'field': 'amount', 'old_value': 11810.51, 'new_value': 14268.52}, {'field': 'count', 'old_value': 279, 'new_value': 376}, {'field': 'instoreAmount', 'old_value': 11052.55, 'new_value': 12983.26}, {'field': 'instoreCount', 'old_value': 217, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 758.51, 'new_value': 1285.81}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 109}]
2025-06-04 08:12:05,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:06,172 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-04 08:12:06,172 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10314.98, 'new_value': 19672.27}, {'field': 'dailyBillAmount', 'old_value': 10314.98, 'new_value': 19672.27}, {'field': 'amount', 'old_value': 1733.17, 'new_value': 2699.08}, {'field': 'count', 'old_value': 50, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 1779.6399999999999, 'new_value': 3027.82}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 98}]
2025-06-04 08:12:06,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:06,656 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-04 08:12:06,656 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62265.46, 'new_value': 75932.94}, {'field': 'dailyBillAmount', 'old_value': 62265.46, 'new_value': 75932.94}, {'field': 'amount', 'old_value': 4427.200000000001, 'new_value': 6271.0}, {'field': 'count', 'old_value': 23, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 4427.200000000001, 'new_value': 6271.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 34}]
2025-06-04 08:12:06,656 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:07,141 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-04 08:12:07,141 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1035.3600000000001, 'new_value': 1968.85}, {'field': 'count', 'old_value': 44, 'new_value': 98}, {'field': 'onlineAmount', 'old_value': 1059.04, 'new_value': 1992.53}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 98}]
2025-06-04 08:12:07,141 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:07,547 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-04 08:12:07,547 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25185.760000000002, 'new_value': 40777.55}, {'field': 'amount', 'old_value': 25184.85, 'new_value': 40776.64}, {'field': 'count', 'old_value': 212, 'new_value': 388}, {'field': 'instoreAmount', 'old_value': 23440.2, 'new_value': 37772.4}, {'field': 'instoreCount', 'old_value': 170, 'new_value': 312}, {'field': 'onlineAmount', 'old_value': 2005.27, 'new_value': 3275.76}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 76}]
2025-06-04 08:12:07,547 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:07,984 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-04 08:12:07,984 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12549.07, 'new_value': 18169.5}, {'field': 'dailyBillAmount', 'old_value': 8931.87, 'new_value': 14552.300000000001}, {'field': 'amount', 'old_value': 9232.33, 'new_value': 13176.61}, {'field': 'count', 'old_value': 316, 'new_value': 432}, {'field': 'instoreAmount', 'old_value': 2293.1800000000003, 'new_value': 2897.58}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 7020.33, 'new_value': 10360.21}, {'field': 'onlineCount', 'old_value': 282, 'new_value': 383}]
2025-06-04 08:12:08,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:08,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-04 08:12:08,391 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'amount', 'old_value': 237.74, 'new_value': 587.24}, {'field': 'count', 'old_value': 9, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 238.64000000000001, 'new_value': 587.54}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 22}]
2025-06-04 08:12:08,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:08,812 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-04 08:12:08,812 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 506.98, 'new_value': 940.23}, {'field': 'count', 'old_value': 24, 'new_value': 46}, {'field': 'onlineAmount', 'old_value': 526.06, 'new_value': 994.3}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 46}]
2025-06-04 08:12:08,812 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:09,250 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-04 08:12:09,250 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9902.49, 'new_value': 12371.89}, {'field': 'dailyBillAmount', 'old_value': 6037.22, 'new_value': 7143.320000000001}, {'field': 'amount', 'old_value': 9901.6, 'new_value': 12371.0}, {'field': 'count', 'old_value': 257, 'new_value': 328}, {'field': 'instoreAmount', 'old_value': 6642.59, 'new_value': 7708.49}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 195}, {'field': 'onlineAmount', 'old_value': 3531.14, 'new_value': 4934.639999999999}, {'field': 'onlineCount', 'old_value': 90, 'new_value': 133}]
2025-06-04 08:12:09,250 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:09,734 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-04 08:12:09,734 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8727.86, 'new_value': 13851.98}, {'field': 'amount', 'old_value': 8727.59, 'new_value': 13851.71}, {'field': 'count', 'old_value': 414, 'new_value': 665}, {'field': 'instoreAmount', 'old_value': 8827.46, 'new_value': 14025.43}, {'field': 'instoreCount', 'old_value': 414, 'new_value': 665}]
2025-06-04 08:12:09,734 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:10,156 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-04 08:12:10,156 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4179.83, 'new_value': 5902.96}, {'field': 'dailyBillAmount', 'old_value': 4179.83, 'new_value': 5902.96}, {'field': 'amount', 'old_value': 1931.01, 'new_value': 3295.52}, {'field': 'count', 'old_value': 101, 'new_value': 165}, {'field': 'instoreAmount', 'old_value': 930.8, 'new_value': 1402.6}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 55}, {'field': 'onlineAmount', 'old_value': 1001.1600000000001, 'new_value': 1893.8700000000001}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 110}]
2025-06-04 08:12:10,156 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:10,594 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-04 08:12:10,594 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6042.26, 'new_value': 8007.34}, {'field': 'amount', 'old_value': 6042.24, 'new_value': 8007.32}, {'field': 'count', 'old_value': 197, 'new_value': 266}, {'field': 'instoreAmount', 'old_value': 3097.16, 'new_value': 4046.33}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 167}, {'field': 'onlineAmount', 'old_value': 2945.1, 'new_value': 3961.0099999999998}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 99}]
2025-06-04 08:12:10,594 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:11,125 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-04 08:12:11,125 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3763.45, 'new_value': 4820.45}, {'field': 'amount', 'old_value': 3762.61, 'new_value': 4819.610000000001}, {'field': 'count', 'old_value': 91, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 3251.9, 'new_value': 4346.9}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 107}]
2025-06-04 08:12:11,125 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:11,656 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-04 08:12:11,656 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15823.099999999999, 'new_value': 24995.26}, {'field': 'dailyBillAmount', 'old_value': 15823.099999999999, 'new_value': 24995.26}, {'field': 'amount', 'old_value': 10233.369999999999, 'new_value': 15279.0}, {'field': 'count', 'old_value': 246, 'new_value': 389}, {'field': 'instoreAmount', 'old_value': 6226.700000000001, 'new_value': 9254.5}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 194}, {'field': 'onlineAmount', 'old_value': 4574.71, 'new_value': 7502.73}, {'field': 'onlineCount', 'old_value': 126, 'new_value': 195}]
2025-06-04 08:12:11,656 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:12,203 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-04 08:12:12,203 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96721.81, 'new_value': 114287.20999999999}, {'field': 'dailyBillAmount', 'old_value': 96721.81, 'new_value': 114287.20999999999}, {'field': 'amount', 'old_value': 77254.7, 'new_value': 91469.8}, {'field': 'count', 'old_value': 505, 'new_value': 590}, {'field': 'instoreAmount', 'old_value': 63308.200000000004, 'new_value': 72540.8}, {'field': 'instoreCount', 'old_value': 438, 'new_value': 500}, {'field': 'onlineAmount', 'old_value': 13947.3, 'new_value': 18929.8}, {'field': 'onlineCount', 'old_value': 67, 'new_value': 90}]
2025-06-04 08:12:12,203 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:12,656 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-04 08:12:12,656 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 99819.76000000001, 'new_value': 133273.76}, {'field': 'amount', 'old_value': 99819.45999999999, 'new_value': 133273.46}, {'field': 'count', 'old_value': 352, 'new_value': 456}, {'field': 'instoreAmount', 'old_value': 99819.76000000001, 'new_value': 133130.76}, {'field': 'instoreCount', 'old_value': 352, 'new_value': 455}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 143.0}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-06-04 08:12:12,656 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:13,047 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-04 08:12:13,047 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50212.14, 'new_value': 64456.89}, {'field': 'dailyBillAmount', 'old_value': 46711.94, 'new_value': 58065.33}, {'field': 'amount', 'old_value': 50211.4, 'new_value': 64456.15}, {'field': 'count', 'old_value': 262, 'new_value': 390}, {'field': 'instoreAmount', 'old_value': 46754.24, 'new_value': 58657.95}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 254}, {'field': 'onlineAmount', 'old_value': 3457.9, 'new_value': 5842.5}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 136}]
2025-06-04 08:12:13,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:13,469 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-04 08:12:13,469 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36698.7, 'new_value': 48182.13}, {'field': 'dailyBillAmount', 'old_value': 23025.28, 'new_value': 34396.04}, {'field': 'amount', 'old_value': 36698.7, 'new_value': 48182.13}, {'field': 'count', 'old_value': 125, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 32019.0, 'new_value': 43023.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 114}, {'field': 'onlineAmount', 'old_value': 4846.530000000001, 'new_value': 5325.96}, {'field': 'onlineCount', 'old_value': 38, 'new_value': 42}]
2025-06-04 08:12:13,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:14,000 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-04 08:12:14,000 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73451.23, 'new_value': 91941.69}, {'field': 'amount', 'old_value': 73450.28, 'new_value': 91940.73999999999}, {'field': 'count', 'old_value': 366, 'new_value': 500}, {'field': 'instoreAmount', 'old_value': 68554.0, 'new_value': 84875.1}, {'field': 'instoreCount', 'old_value': 246, 'new_value': 303}, {'field': 'onlineAmount', 'old_value': 4897.23, 'new_value': 7066.59}, {'field': 'onlineCount', 'old_value': 120, 'new_value': 197}]
2025-06-04 08:12:14,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:14,422 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-04 08:12:14,422 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72899.7, 'new_value': 93235.51999999999}, {'field': 'dailyBillAmount', 'old_value': 72899.7, 'new_value': 93235.51999999999}, {'field': 'amount', 'old_value': 55604.0, 'new_value': 76869.98}, {'field': 'count', 'old_value': 298, 'new_value': 406}, {'field': 'instoreAmount', 'old_value': 50336.59, 'new_value': 70196.93}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 337}, {'field': 'onlineAmount', 'old_value': 5526.01, 'new_value': 6931.650000000001}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 69}]
2025-06-04 08:12:14,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:14,891 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-04 08:12:14,891 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15640.0, 'new_value': 19979.0}, {'field': 'dailyBillAmount', 'old_value': 15640.0, 'new_value': 19979.0}, {'field': 'amount', 'old_value': 15171.0, 'new_value': 19428.0}, {'field': 'count', 'old_value': 22, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 15171.0, 'new_value': 19428.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 33}]
2025-06-04 08:12:14,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:15,312 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-04 08:12:15,312 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13770.32, 'new_value': 15724.119999999999}, {'field': 'dailyBillAmount', 'old_value': 13770.32, 'new_value': 15724.119999999999}, {'field': 'amount', 'old_value': 13421.0, 'new_value': 16349.8}, {'field': 'count', 'old_value': 18, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 14297.09, 'new_value': 17225.89}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 19}]
2025-06-04 08:12:15,312 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:15,812 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-04 08:12:15,812 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1838.81, 'new_value': 2512.15}, {'field': 'amount', 'old_value': 1838.57, 'new_value': 2511.91}, {'field': 'count', 'old_value': 41, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 1838.81, 'new_value': 2512.15}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 48}]
2025-06-04 08:12:15,812 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:16,375 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-04 08:12:16,375 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6097.49, 'new_value': 7305.3099999999995}, {'field': 'amount', 'old_value': 6096.5, 'new_value': 7304.32}, {'field': 'count', 'old_value': 58, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 6097.49, 'new_value': 7305.3099999999995}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 73}]
2025-06-04 08:12:16,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:16,812 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-04 08:12:16,812 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32850.34, 'new_value': 43040.99}, {'field': 'dailyBillAmount', 'old_value': 32850.34, 'new_value': 43040.99}, {'field': 'amount', 'old_value': 35483.880000000005, 'new_value': 46738.86}, {'field': 'count', 'old_value': 926, 'new_value': 1240}, {'field': 'instoreAmount', 'old_value': 33709.8, 'new_value': 43961.65}, {'field': 'instoreCount', 'old_value': 825, 'new_value': 1092}, {'field': 'onlineAmount', 'old_value': 2195.1, 'new_value': 3235.73}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 148}]
2025-06-04 08:12:16,812 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:17,234 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-04 08:12:17,234 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51533.92, 'new_value': 67227.68}, {'field': 'dailyBillAmount', 'old_value': 51533.92, 'new_value': 67227.68}, {'field': 'amount', 'old_value': 51405.16, 'new_value': 67705.36}, {'field': 'count', 'old_value': 130, 'new_value': 204}, {'field': 'instoreAmount', 'old_value': 51181.380000000005, 'new_value': 66875.14}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 170}, {'field': 'onlineAmount', 'old_value': 577.21, 'new_value': 1183.65}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 34}]
2025-06-04 08:12:17,234 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:17,781 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-04 08:12:17,781 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 98246.36, 'new_value': 126650.76000000001}, {'field': 'amount', 'old_value': 98246.31, 'new_value': 126650.70999999999}, {'field': 'count', 'old_value': 345, 'new_value': 447}, {'field': 'instoreAmount', 'old_value': 98246.36, 'new_value': 128595.76000000001}, {'field': 'instoreCount', 'old_value': 345, 'new_value': 447}]
2025-06-04 08:12:17,781 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:18,266 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-04 08:12:18,266 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 41431.69, 'new_value': 66843.39}, {'field': 'count', 'old_value': 155, 'new_value': 269}, {'field': 'instoreAmount', 'old_value': 41406.14, 'new_value': 67164.35}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 156}, {'field': 'onlineAmount', 'old_value': 1799.7, 'new_value': 3693.5}, {'field': 'onlineCount', 'old_value': 50, 'new_value': 113}]
2025-06-04 08:12:18,266 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:18,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-04 08:12:18,750 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 145548.74, 'new_value': 210108.96}, {'field': 'dailyBillAmount', 'old_value': 145548.74, 'new_value': 210108.96}, {'field': 'amount', 'old_value': 118818.0, 'new_value': 174273.0}, {'field': 'count', 'old_value': 286, 'new_value': 407}, {'field': 'instoreAmount', 'old_value': 125454.0, 'new_value': 185555.0}, {'field': 'instoreCount', 'old_value': 286, 'new_value': 407}]
2025-06-04 08:12:18,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:19,172 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-04 08:12:19,187 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28015.53, 'new_value': 36526.32}, {'field': 'dailyBillAmount', 'old_value': 28015.53, 'new_value': 36526.32}, {'field': 'amount', 'old_value': 27974.739999999998, 'new_value': 36276.53}, {'field': 'count', 'old_value': 132, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 26392.0, 'new_value': 34366.7}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 155}, {'field': 'onlineAmount', 'old_value': 1729.15, 'new_value': 2283.24}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 28}]
2025-06-04 08:12:19,187 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:19,609 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-04 08:12:19,609 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 54835.3, 'new_value': 86414.9}, {'field': 'dailyBillAmount', 'old_value': 54835.3, 'new_value': 86414.9}, {'field': 'amount', 'old_value': 91097.17, 'new_value': 122676.76999999999}, {'field': 'count', 'old_value': 371, 'new_value': 526}, {'field': 'instoreAmount', 'old_value': 91097.22, 'new_value': 122676.82}, {'field': 'instoreCount', 'old_value': 371, 'new_value': 526}]
2025-06-04 08:12:19,609 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:20,047 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-04 08:12:20,047 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32048.18, 'new_value': 37042.63}, {'field': 'dailyBillAmount', 'old_value': 32048.18, 'new_value': 37042.63}, {'field': 'amount', 'old_value': 55047.8, 'new_value': 65410.3}, {'field': 'count', 'old_value': 78, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 54170.0, 'new_value': 64455.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 99}, {'field': 'onlineAmount', 'old_value': 878.0, 'new_value': 956.3}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-06-04 08:12:20,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:20,469 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-04 08:12:20,469 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41685.42, 'new_value': 45399.49}, {'field': 'dailyBillAmount', 'old_value': 41685.42, 'new_value': 45399.49}, {'field': 'amount', 'old_value': 46422.3, 'new_value': 49868.3}, {'field': 'count', 'old_value': 293, 'new_value': 323}, {'field': 'instoreAmount', 'old_value': 46422.3, 'new_value': 49868.3}, {'field': 'instoreCount', 'old_value': 293, 'new_value': 323}]
2025-06-04 08:12:20,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:20,891 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-04 08:12:20,891 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27914.95, 'new_value': 35254.51}, {'field': 'dailyBillAmount', 'old_value': 27914.95, 'new_value': 35254.51}, {'field': 'amount', 'old_value': 19312.2, 'new_value': 26078.2}, {'field': 'count', 'old_value': 117, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 20178.0, 'new_value': 26827.0}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 154}, {'field': 'onlineAmount', 'old_value': 161.2, 'new_value': 278.2}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-06-04 08:12:20,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:21,344 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-04 08:12:21,344 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8555.189999999999, 'new_value': 11024.95}, {'field': 'count', 'old_value': 483, 'new_value': 623}, {'field': 'instoreAmount', 'old_value': 1076.25, 'new_value': 1502.71}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 131}, {'field': 'onlineAmount', 'old_value': 8009.8, 'new_value': 10177.8}, {'field': 'onlineCount', 'old_value': 395, 'new_value': 492}]
2025-06-04 08:12:21,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:21,875 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-04 08:12:21,875 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11959.45, 'new_value': 21637.29}, {'field': 'amount', 'old_value': 11959.21, 'new_value': 21635.84}, {'field': 'count', 'old_value': 241, 'new_value': 405}, {'field': 'instoreAmount', 'old_value': 9525.25, 'new_value': 18622.45}, {'field': 'instoreCount', 'old_value': 187, 'new_value': 333}, {'field': 'onlineAmount', 'old_value': 2434.2, 'new_value': 3014.84}, {'field': 'onlineCount', 'old_value': 54, 'new_value': 72}]
2025-06-04 08:12:21,875 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:22,328 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-04 08:12:22,328 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4112.4, 'new_value': 5049.8}, {'field': 'dailyBillAmount', 'old_value': 153.8, 'new_value': 3233.8}, {'field': 'amount', 'old_value': 4112.0, 'new_value': 5049.4}, {'field': 'count', 'old_value': 21, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 4112.4, 'new_value': 5049.8}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 27}]
2025-06-04 08:12:22,328 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:22,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-04 08:12:22,750 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2716.0, 'new_value': 2856.0}, {'field': 'dailyBillAmount', 'old_value': 2716.0, 'new_value': 2856.0}, {'field': 'amount', 'old_value': 3030.8, 'new_value': 4005.0}, {'field': 'count', 'old_value': 24, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 3031.1000000000004, 'new_value': 4173.3}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 40}]
2025-06-04 08:12:22,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:23,187 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-04 08:12:23,187 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5938.0, 'new_value': 8067.0}, {'field': 'dailyBillAmount', 'old_value': 5938.0, 'new_value': 8067.0}]
2025-06-04 08:12:23,187 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:23,594 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-04 08:12:23,594 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22908.4, 'new_value': 31629.399999999998}, {'field': 'dailyBillAmount', 'old_value': 22908.4, 'new_value': 31629.399999999998}, {'field': 'amount', 'old_value': 19319.2, 'new_value': 23217.800000000003}, {'field': 'count', 'old_value': 509, 'new_value': 666}, {'field': 'instoreAmount', 'old_value': 17991.41, 'new_value': 21682.01}, {'field': 'instoreCount', 'old_value': 478, 'new_value': 625}, {'field': 'onlineAmount', 'old_value': 1420.58, 'new_value': 1721.78}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 41}]
2025-06-04 08:12:23,594 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:24,047 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-04 08:12:24,047 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5810.4, 'new_value': 6763.099999999999}, {'field': 'dailyBillAmount', 'old_value': 5810.4, 'new_value': 6763.099999999999}, {'field': 'amount', 'old_value': 5809.5, 'new_value': 6762.2}, {'field': 'count', 'old_value': 34, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 6048.2, 'new_value': 7000.9}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 41}]
2025-06-04 08:12:24,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:24,484 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-04 08:12:24,484 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2828.73, 'new_value': 6356.48}, {'field': 'dailyBillAmount', 'old_value': 2828.73, 'new_value': 6356.48}]
2025-06-04 08:12:24,484 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:24,875 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-04 08:12:24,875 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3651.92, 'new_value': 6082.83}, {'field': 'amount', 'old_value': 3651.32, 'new_value': 6082.2300000000005}, {'field': 'count', 'old_value': 235, 'new_value': 377}, {'field': 'instoreAmount', 'old_value': 3695.05, 'new_value': 6135.64}, {'field': 'instoreCount', 'old_value': 235, 'new_value': 377}]
2025-06-04 08:12:24,875 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:25,312 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-04 08:12:25,312 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5427.5599999999995, 'new_value': 8472.95}, {'field': 'dailyBillAmount', 'old_value': 5427.5599999999995, 'new_value': 8472.95}, {'field': 'amount', 'old_value': 5194.52, 'new_value': 8373.7}, {'field': 'count', 'old_value': 249, 'new_value': 405}, {'field': 'instoreAmount', 'old_value': 4637.0, 'new_value': 7655.1}, {'field': 'instoreCount', 'old_value': 221, 'new_value': 366}, {'field': 'onlineAmount', 'old_value': 558.21, 'new_value': 739.17}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 39}]
2025-06-04 08:12:25,312 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:25,765 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-04 08:12:25,765 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5783.16, 'new_value': 7457.7300000000005}, {'field': 'amount', 'old_value': 5783.01, 'new_value': 7457.58}, {'field': 'count', 'old_value': 232, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 3078.32, 'new_value': 4128.280000000001}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 181}, {'field': 'onlineAmount', 'old_value': 2742.66, 'new_value': 3367.27}, {'field': 'onlineCount', 'old_value': 105, 'new_value': 137}]
2025-06-04 08:12:25,765 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:26,234 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-04 08:12:26,250 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3434.4700000000003, 'new_value': 5060.59}, {'field': 'dailyBillAmount', 'old_value': 3434.4700000000003, 'new_value': 5060.59}, {'field': 'amount', 'old_value': 2133.4700000000003, 'new_value': 3347.3100000000004}, {'field': 'count', 'old_value': 77, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 2205.45, 'new_value': 3447.85}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 125}]
2025-06-04 08:12:26,250 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:26,703 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-04 08:12:26,703 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5845.73, 'new_value': 7586.99}, {'field': 'amount', 'old_value': 5845.0, 'new_value': 7586.96}, {'field': 'count', 'old_value': 360, 'new_value': 478}, {'field': 'instoreAmount', 'old_value': 1193.5900000000001, 'new_value': 1671.74}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 85}, {'field': 'onlineAmount', 'old_value': 4737.91, 'new_value': 6001.0199999999995}, {'field': 'onlineCount', 'old_value': 298, 'new_value': 393}]
2025-06-04 08:12:26,703 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:27,156 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-04 08:12:27,156 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6727.68, 'new_value': 13227.82}, {'field': 'dailyBillAmount', 'old_value': 6727.68, 'new_value': 13227.82}, {'field': 'amount', 'old_value': 4481.2, 'new_value': 9736.9}, {'field': 'count', 'old_value': 43, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 4481.7, 'new_value': 9737.4}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 87}]
2025-06-04 08:12:27,156 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:27,562 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-04 08:12:27,562 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17809.0, 'new_value': 19706.8}, {'field': 'dailyBillAmount', 'old_value': 17809.0, 'new_value': 19706.8}, {'field': 'amount', 'old_value': 21064.0, 'new_value': 23356.0}, {'field': 'count', 'old_value': 81, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 21064.0, 'new_value': 23356.0}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 90}]
2025-06-04 08:12:27,562 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:28,047 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-04 08:12:28,047 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1848.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1848.0}, {'field': 'amount', 'old_value': 7005.0, 'new_value': 8303.0}, {'field': 'count', 'old_value': 34, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 7005.0, 'new_value': 8303.0}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 43}]
2025-06-04 08:12:28,062 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:28,500 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-04 08:12:28,500 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8408.0, 'new_value': 11710.0}, {'field': 'amount', 'old_value': 8408.0, 'new_value': 11710.0}, {'field': 'count', 'old_value': 106, 'new_value': 143}, {'field': 'instoreAmount', 'old_value': 8408.0, 'new_value': 11710.0}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 143}]
2025-06-04 08:12:28,500 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:28,984 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-04 08:12:28,984 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1066.8000000000002, 'new_value': 1631.04}, {'field': 'dailyBillAmount', 'old_value': 1066.8000000000002, 'new_value': 1631.04}, {'field': 'amount', 'old_value': 219.59999999999997, 'new_value': 319.79999999999995}, {'field': 'count', 'old_value': 10, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 330.5, 'new_value': 450.59999999999997}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 12}]
2025-06-04 08:12:28,984 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:29,500 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-04 08:12:29,500 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'amount', 'old_value': 4785.0, 'new_value': 5765.0}, {'field': 'count', 'old_value': 25, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 4785.0, 'new_value': 5765.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 32}]
2025-06-04 08:12:29,500 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:30,015 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-04 08:12:30,015 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5665.33, 'new_value': 8415.52}, {'field': 'dailyBillAmount', 'old_value': 5665.33, 'new_value': 8415.52}, {'field': 'amount', 'old_value': 4109.5, 'new_value': 6218.84}, {'field': 'count', 'old_value': 127, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 3918.83, 'new_value': 6028.17}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 189}]
2025-06-04 08:12:30,015 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:30,531 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-04 08:12:30,531 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6944.5599999999995, 'new_value': 9795.84}, {'field': 'dailyBillAmount', 'old_value': 6944.5599999999995, 'new_value': 9795.84}, {'field': 'amount', 'old_value': 6652.6, 'new_value': 9440.400000000001}, {'field': 'count', 'old_value': 39, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 6651.4, 'new_value': 9410.4}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 82.0, 'new_value': 110.80000000000001}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}]
2025-06-04 08:12:30,531 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-04 08:12:30,937 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-04 08:12:30,937 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19705.78, 'new_value': 23705.97}, {'field': 'dailyBillAmount', 'old_value': 19705.78, 'new_value': 23705.97}, {'field': 'amount', 'old_value': 21087.6, 'new_value': 25286.0}, {'field': 'count', 'old_value': 140, 'new_value': 167}, {'field': 'instoreAmount', 'old_value': 20091.0, 'new_value': 23936.0}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 141}, {'field': 'onlineAmount', 'old_value': 1028.6, 'new_value': 1382.0}, {'field': 'onlineCount', 'old_value': 21, 'new_value': 26}]
2025-06-04 08:12:30,937 - INFO - 正在批量插入月度数据，批次 1/1，共 1 条记录
2025-06-04 08:12:31,109 - INFO - 批量插入月度数据成功，批次 1，1 条记录
2025-06-04 08:12:34,125 - INFO - 批量插入月度数据完成: 总计 1 条，成功 1 条，失败 0 条
2025-06-04 08:12:34,125 - INFO - 批量插入月销售数据完成，共 1 条记录
2025-06-04 08:12:34,125 - INFO - 月销售数据同步完成！更新: 204 条，插入: 1 条，错误: 0 条，跳过: 1197 条
2025-06-04 08:12:34,125 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-7 至 2025-6
2025-06-04 08:12:34,765 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250604.xlsx
2025-06-04 08:12:34,765 - INFO - 综合数据同步流程完成！
2025-06-04 08:12:34,828 - INFO - 综合数据同步完成
2025-06-04 08:12:34,828 - INFO - ==================================================
2025-06-04 08:12:34,828 - INFO - 程序退出
2025-06-04 08:12:34,828 - INFO - ==================================================
