2025-05-27 08:00:03,606 - INFO - ==================================================
2025-05-27 08:00:03,606 - INFO - 程序启动 - 版本 v1.0.0
2025-05-27 08:00:03,606 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250527.log
2025-05-27 08:00:03,606 - INFO - ==================================================
2025-05-27 08:00:03,606 - INFO - 程序入口点: __main__
2025-05-27 08:00:03,606 - INFO - ==================================================
2025-05-27 08:00:03,606 - INFO - 程序启动 - 版本 v1.0.1
2025-05-27 08:00:03,606 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250527.log
2025-05-27 08:00:03,606 - INFO - ==================================================
2025-05-27 08:00:03,981 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-27 08:00:03,981 - INFO - sales_data表已存在，无需创建
2025-05-27 08:00:03,981 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-27 08:00:03,981 - INFO - DataSyncManager初始化完成
2025-05-27 08:00:03,981 - INFO - 未提供日期参数，使用默认值
2025-05-27 08:00:03,981 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-27 08:00:03,981 - INFO - 开始综合数据同步流程...
2025-05-27 08:00:03,981 - INFO - 正在获取数衍平台日销售数据...
2025-05-27 08:00:03,981 - INFO - 查询数衍平台数据，时间段为: 2025-03-27, 2025-05-26
2025-05-27 08:00:03,981 - INFO - 正在获取********至********的数据
2025-05-27 08:00:03,981 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:03,981 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9A373C6442966B8B681208DE85AE1661'}
2025-05-27 08:00:08,465 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:08,481 - INFO - 过滤后保留 1542 条记录
2025-05-27 08:00:10,496 - INFO - 正在获取********至********的数据
2025-05-27 08:00:10,496 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:10,496 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EFB0CBDDE59632D0E0625B3FC42FB2B5'}
2025-05-27 08:00:14,043 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:14,059 - INFO - 过滤后保留 1503 条记录
2025-05-27 08:00:16,059 - INFO - 正在获取********至********的数据
2025-05-27 08:00:16,059 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:16,059 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '67B91E6122F4986036FB86E03A813E6F'}
2025-05-27 08:00:18,965 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:18,981 - INFO - 过滤后保留 1501 条记录
2025-05-27 08:00:20,981 - INFO - 正在获取********至********的数据
2025-05-27 08:00:20,981 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:20,981 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2F8289B8F3E61FD25D4DFBD0D56E2C1A'}
2025-05-27 08:00:24,012 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:24,028 - INFO - 过滤后保留 1499 条记录
2025-05-27 08:00:26,028 - INFO - 正在获取********至********的数据
2025-05-27 08:00:26,028 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:26,028 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FABF1E6AE469F00DDDC848887697CC0C'}
2025-05-27 08:00:28,762 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:28,762 - INFO - 过滤后保留 1491 条记录
2025-05-27 08:00:30,778 - INFO - 正在获取********至********的数据
2025-05-27 08:00:30,778 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:30,778 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '78C2FA12128BA0F49E4B28CC197E60A5'}
2025-05-27 08:00:33,293 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:33,309 - INFO - 过滤后保留 1466 条记录
2025-05-27 08:00:35,309 - INFO - 正在获取********至********的数据
2025-05-27 08:00:35,309 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:35,309 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '649701504FB94FC08A20D34503AE4BB2'}
2025-05-27 08:00:37,512 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:37,527 - INFO - 过滤后保留 1487 条记录
2025-05-27 08:00:39,543 - INFO - 正在获取********至********的数据
2025-05-27 08:00:39,543 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:39,543 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DE38870D3DF92F135B35A2CF590A2F37'}
2025-05-27 08:00:41,809 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:41,809 - INFO - 过滤后保留 1475 条记录
2025-05-27 08:00:43,824 - INFO - 正在获取********至********的数据
2025-05-27 08:00:43,824 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-27 08:00:43,824 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EB1F1B56511DE9177BCEA95FCED1F57A'}
2025-05-27 08:00:45,387 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-27 08:00:45,387 - INFO - 过滤后保留 1035 条记录
2025-05-27 08:00:47,402 - INFO - 开始保存数据到SQLite数据库，共 12999 条记录待处理
2025-05-27 08:00:48,012 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2PLB45826AJB6QM8HA7Q0011QO, sale_time=2025-05-02
2025-05-27 08:00:48,012 - INFO - 变更字段: amount: 28205 -> 29315, count: 121 -> 122, instore_amount: 28205.45 -> 29315.65, instore_count: 121 -> 122
2025-05-27 08:00:48,074 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS34G4B697AV8LHQQGIDL001EJU, sale_time=2025-05-14
2025-05-27 08:00:48,074 - INFO - 变更字段: recommend_amount: 21477.64 -> 21507.64, amount: 21477 -> 21507, instore_amount: 20135.0 -> 20165.0
2025-05-27 08:00:48,137 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-05-19
2025-05-27 08:00:48,137 - INFO - 变更字段: amount: 10942 -> 11173, count: 119 -> 120, instore_amount: 5393.4 -> 5624.4, instore_count: 55 -> 56
2025-05-27 08:00:48,137 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-20
2025-05-27 08:00:48,137 - INFO - 变更字段: recommend_amount: 0.0 -> 56069.49, daily_bill_amount: 0.0 -> 56069.49
2025-05-27 08:00:48,137 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-19
2025-05-27 08:00:48,137 - INFO - 变更字段: recommend_amount: 0.0 -> 38630.68, daily_bill_amount: 0.0 -> 38630.68
2025-05-27 08:00:48,152 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-25
2025-05-27 08:00:48,152 - INFO - 变更字段: recommend_amount: 0.0 -> 4005.8, daily_bill_amount: 0.0 -> 4005.8
2025-05-27 08:00:48,152 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HVOQTVVFR41OA22BBK6R0G53G001SV2, sale_time=2025-05-25
2025-05-27 08:00:48,152 - INFO - 变更字段: recommend_amount: 89.0 -> 427.0, amount: 89 -> 427, count: 1 -> 2, instore_amount: 89.0 -> 427.0, instore_count: 1 -> 2
2025-05-27 08:00:48,152 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-05-25
2025-05-27 08:00:48,152 - INFO - 变更字段: amount: 5923 -> 6734, count: 71 -> 80, instore_amount: 2360.17 -> 3171.63, instore_count: 29 -> 38
2025-05-27 08:00:48,152 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-05-25
2025-05-27 08:00:48,152 - INFO - 变更字段: amount: 3393 -> 3471, count: 87 -> 88, online_amount: 151.6 -> 229.91, online_count: 4 -> 5
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDV0D9P6J27Q2OV4FVC7DG0014A1, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: recommend_amount: 2627.5 -> 2600.5, daily_bill_amount: 2627.5 -> 2600.5
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: amount: 1169 -> 1176, count: 62 -> 63, online_amount: 572.8 -> 579.8, online_count: 31 -> 32
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: amount: 1333 -> 1370, count: 38 -> 40, online_amount: 1081.83 -> 1119.51, online_count: 33 -> 35
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDJ5HVP5F47Q2OV4FVC77O001449, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: amount: 1899 -> 2546, count: 4 -> 5, instore_amount: 1899.0 -> 2546.0, instore_count: 4 -> 5
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: recommend_amount: 0.0 -> 7673.48, daily_bill_amount: 0.0 -> 7673.48
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: recommend_amount: 22246.0 -> 23893.0, amount: 22246 -> 23893, count: 73 -> 74, instore_amount: 22246.0 -> 23893.0, instore_count: 73 -> 74
2025-05-27 08:00:48,168 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-25
2025-05-27 08:00:48,168 - INFO - 变更字段: amount: 3429 -> 3634, count: 8 -> 9, instore_amount: 3429.0 -> 3634.0, instore_count: 8 -> 9
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQ5682UCT42F6DB81RHA6001P6E, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 0.0 -> 1651.46, daily_bill_amount: 0.0 -> 1651.46
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 2731.03 -> 2714.66, amount: 2731 -> 2714, count: 122 -> 121, instore_amount: 2768.54 -> 2758.71, instore_count: 122 -> 121
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 9597.99 -> 9712.59, amount: 9597 -> 9712, count: 196 -> 198, instore_amount: 7658.66 -> 7773.26, instore_count: 160 -> 162
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-24
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 9262.24 -> 9872.44, amount: 9262 -> 9872, count: 180 -> 186, instore_amount: 7998.49 -> 8608.69, instore_count: 144 -> 150
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: amount: 5371 -> 5460, count: 264 -> 267, online_amount: 2594.75 -> 2683.65, online_count: 121 -> 124
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 1447.85 -> 1443.87, amount: 1447 -> 1443, online_amount: 855.19 -> 851.21
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1IFRR231GUB4QN7QBECDAL3H28001J69, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: amount: 10659 -> 10749, count: 212 -> 213, instore_amount: 10733.3 -> 10823.2, instore_count: 212 -> 213
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 5148.34 -> 5246.04, amount: 5148 -> 5246, count: 274 -> 277, instore_amount: 1747.39 -> 1840.09, instore_count: 74 -> 76, online_amount: 3668.55 -> 3673.55, online_count: 200 -> 201
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-24
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 6653.26 -> 6658.86, amount: 6653 -> 6658, count: 308 -> 309, online_amount: 4688.05 -> 4693.65, online_count: 210 -> 211
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 0.0 -> 1560.0, daily_bill_amount: 0.0 -> 1560.0
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: recommend_amount: 0.0 -> 15739.61, daily_bill_amount: 0.0 -> 15739.61
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: amount: 27128 -> 27426, count: 215 -> 217, instore_amount: 24666.37 -> 24963.47, instore_count: 137 -> 139
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: amount: 25017 -> 25702, count: 259 -> 260, instore_amount: 18579.0 -> 19264.0, instore_count: 126 -> 127
2025-05-27 08:00:48,184 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-25
2025-05-27 08:00:48,184 - INFO - 变更字段: amount: 40007 -> 41392, count: 284 -> 289, instore_amount: 21954.4 -> 23339.4, instore_count: 115 -> 120
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE4GES1U770I86N3H2U1SF001F5L, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: recommend_amount: 40658.6 -> 42304.4, amount: 40658 -> 42304, count: 301 -> 310, instore_amount: 40658.6 -> 42304.4, instore_count: 301 -> 310
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: amount: 49237 -> 49218
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: amount: 21979 -> 22496, count: 127 -> 128, instore_amount: 21133.8 -> 21650.8, instore_count: 118 -> 119
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: amount: 17672 -> 17689, count: 155 -> 156, online_amount: 4514.61 -> 4531.91, online_count: 64 -> 65
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: recommend_amount: 0.0 -> 3913.91, daily_bill_amount: 0.0 -> 3913.91, amount: 719 -> 1670, count: 80 -> 161, instore_amount: 767.1 -> 1733.7, instore_count: 80 -> 161
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDCE3748SO0I86N3H2U1FP001EOV, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: count: 3 -> 4, instore_count: 3 -> 4
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: recommend_amount: 0.0 -> 14136.0, daily_bill_amount: 0.0 -> 14136.0
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: amount: 2586 -> 2607, count: 105 -> 106, online_amount: 1560.1 -> 1580.5, online_count: 65 -> 66
2025-05-27 08:00:48,199 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-25
2025-05-27 08:00:48,199 - INFO - 变更字段: amount: 4269 -> 4295, count: 304 -> 312, online_amount: 3926.0 -> 3952.01, online_count: 268 -> 276
2025-05-27 08:00:48,215 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-05-25
2025-05-27 08:00:48,215 - INFO - 变更字段: amount: 3446 -> 3477, count: 186 -> 187, online_amount: 2773.51 -> 2804.51, online_count: 127 -> 128
2025-05-27 08:00:48,215 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-25
2025-05-27 08:00:48,215 - INFO - 变更字段: count: 525 -> 528, instore_amount: 5056.44 -> 5094.59, instore_count: 355 -> 361, online_amount: 2379.44 -> 2368.79, online_count: 170 -> 167
2025-05-27 08:00:48,215 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q, sale_time=2025-05-25
2025-05-27 08:00:48,215 - INFO - 变更字段: recommend_amount: 32123.1 -> 32167.1, amount: 32123 -> 32167, count: 638 -> 639, online_amount: 9810.5 -> 9854.5, online_count: 214 -> 215
2025-05-27 08:00:48,215 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-25
2025-05-27 08:00:48,215 - INFO - 变更字段: recommend_amount: 3427.91 -> 3417.92, amount: 3427 -> 3417
2025-05-27 08:00:48,215 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-25
2025-05-27 08:00:48,215 - INFO - 变更字段: amount: 24849 -> 25193, count: 187 -> 188, instore_amount: 23245.84 -> 23589.64, instore_count: 127 -> 128
2025-05-27 08:00:48,215 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-22
2025-05-27 08:00:48,215 - INFO - 变更字段: recommend_amount: 0.0 -> 41171.64, daily_bill_amount: 0.0 -> 41171.64
2025-05-27 08:00:48,231 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-25
2025-05-27 08:00:48,231 - INFO - 变更字段: amount: 17813 -> 18897, count: 100 -> 102, instore_amount: 15434.62 -> 16518.62, instore_count: 80 -> 82
2025-05-27 08:00:48,231 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-23
2025-05-27 08:00:48,231 - INFO - 变更字段: amount: 8516 -> 9069, count: 48 -> 49, instore_amount: 7994.1 -> 8547.1, instore_count: 39 -> 40
2025-05-27 08:00:48,231 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-25
2025-05-27 08:00:48,231 - INFO - 变更字段: amount: 680 -> 663
2025-05-27 08:00:48,465 - INFO - SQLite数据保存完成，统计信息：
2025-05-27 08:00:48,465 - INFO - - 总记录数: 12999
2025-05-27 08:00:48,465 - INFO - - 成功插入: 202
2025-05-27 08:00:48,465 - INFO - - 成功更新: 48
2025-05-27 08:00:48,465 - INFO - - 无需更新: 12749
2025-05-27 08:00:48,465 - INFO - - 处理失败: 0
2025-05-27 08:00:53,840 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250527.xlsx
2025-05-27 08:00:53,840 - INFO - 成功获取数衍平台数据，共 12999 条记录
2025-05-27 08:00:53,855 - INFO - 正在更新SQLite月度汇总数据...
2025-05-27 08:00:53,855 - INFO - 月度数据sqllite清空完成
2025-05-27 08:00:54,090 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-27 08:00:54,090 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-27 08:00:54,090 - INFO - 正在获取宜搭日销售表单数据...
2025-05-27 08:00:54,090 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-27 00:00:00 至 2025-05-26 23:59:59
2025-05-27 08:00:54,090 - INFO - 查询分段 1: 2025-03-27 至 2025-04-02
2025-05-27 08:00:54,090 - INFO - 查询日期范围: 2025-03-27 至 2025-04-02，使用分页查询，每页 100 条记录
2025-05-27 08:00:54,090 - INFO - Request Parameters - Page 1:
2025-05-27 08:00:54,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:00:54,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:02,215 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B9EBBBFC-2AD0-7518-AEED-B128B6F66055 Response: {'code': 'ServiceUnavailable', 'requestid': 'B9EBBBFC-2AD0-7518-AEED-B128B6F66055', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-27 08:01:02,215 - ERROR - 服务不可用，将等待后重试
2025-05-27 08:01:02,215 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B9EBBBFC-2AD0-7518-AEED-B128B6F66055 Response: {'code': 'ServiceUnavailable', 'requestid': 'B9EBBBFC-2AD0-7518-AEED-B128B6F66055', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-27 08:01:02,215 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-05-27 08:01:08,230 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-05-27 08:01:08,230 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B9EBBBFC-2AD0-7518-AEED-B128B6F66055 Response: {'code': 'ServiceUnavailable', 'requestid': 'B9EBBBFC-2AD0-7518-AEED-B128B6F66055', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-05-27 08:01:18,246 - INFO - Request Parameters - Page 1:
2025-05-27 08:01:18,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:18,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:19,090 - INFO - API请求耗时: 844ms
2025-05-27 08:01:19,105 - INFO - Response - Page 1
2025-05-27 08:01:19,105 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:01:19,621 - INFO - Request Parameters - Page 2:
2025-05-27 08:01:19,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:19,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:20,558 - INFO - API请求耗时: 938ms
2025-05-27 08:01:20,558 - INFO - Response - Page 2
2025-05-27 08:01:20,558 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:01:21,074 - INFO - Request Parameters - Page 3:
2025-05-27 08:01:21,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:21,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:21,793 - INFO - API请求耗时: 719ms
2025-05-27 08:01:21,793 - INFO - Response - Page 3
2025-05-27 08:01:21,793 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:01:22,308 - INFO - Request Parameters - Page 4:
2025-05-27 08:01:22,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:22,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:23,027 - INFO - API请求耗时: 719ms
2025-05-27 08:01:23,027 - INFO - Response - Page 4
2025-05-27 08:01:23,027 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:01:23,543 - INFO - Request Parameters - Page 5:
2025-05-27 08:01:23,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:23,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:24,230 - INFO - API请求耗时: 687ms
2025-05-27 08:01:24,246 - INFO - Response - Page 5
2025-05-27 08:01:24,246 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:01:24,762 - INFO - Request Parameters - Page 6:
2025-05-27 08:01:24,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:24,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:25,590 - INFO - API请求耗时: 828ms
2025-05-27 08:01:25,590 - INFO - Response - Page 6
2025-05-27 08:01:25,590 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:01:26,090 - INFO - Request Parameters - Page 7:
2025-05-27 08:01:26,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:26,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:26,793 - INFO - API请求耗时: 703ms
2025-05-27 08:01:26,793 - INFO - Response - Page 7
2025-05-27 08:01:26,793 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:01:27,308 - INFO - Request Parameters - Page 8:
2025-05-27 08:01:27,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:27,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:28,121 - INFO - API请求耗时: 812ms
2025-05-27 08:01:28,121 - INFO - Response - Page 8
2025-05-27 08:01:28,121 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:01:28,621 - INFO - Request Parameters - Page 9:
2025-05-27 08:01:28,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:28,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:29,308 - INFO - API请求耗时: 687ms
2025-05-27 08:01:29,308 - INFO - Response - Page 9
2025-05-27 08:01:29,308 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:01:29,808 - INFO - Request Parameters - Page 10:
2025-05-27 08:01:29,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:29,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:30,668 - INFO - API请求耗时: 859ms
2025-05-27 08:01:30,668 - INFO - Response - Page 10
2025-05-27 08:01:30,668 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:01:31,183 - INFO - Request Parameters - Page 11:
2025-05-27 08:01:31,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:31,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:31,902 - INFO - API请求耗时: 719ms
2025-05-27 08:01:31,902 - INFO - Response - Page 11
2025-05-27 08:01:31,902 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:01:32,418 - INFO - Request Parameters - Page 12:
2025-05-27 08:01:32,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:32,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:33,105 - INFO - API请求耗时: 688ms
2025-05-27 08:01:33,121 - INFO - Response - Page 12
2025-05-27 08:01:33,121 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:01:33,637 - INFO - Request Parameters - Page 13:
2025-05-27 08:01:33,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:33,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:34,324 - INFO - API请求耗时: 687ms
2025-05-27 08:01:34,324 - INFO - Response - Page 13
2025-05-27 08:01:34,324 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:01:34,824 - INFO - Request Parameters - Page 14:
2025-05-27 08:01:34,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:34,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:35,543 - INFO - API请求耗时: 719ms
2025-05-27 08:01:35,543 - INFO - Response - Page 14
2025-05-27 08:01:35,543 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:01:36,058 - INFO - Request Parameters - Page 15:
2025-05-27 08:01:36,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:36,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:36,746 - INFO - API请求耗时: 687ms
2025-05-27 08:01:36,746 - INFO - Response - Page 15
2025-05-27 08:01:36,746 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:01:37,261 - INFO - Request Parameters - Page 16:
2025-05-27 08:01:37,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:37,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:37,980 - INFO - API请求耗时: 719ms
2025-05-27 08:01:37,980 - INFO - Response - Page 16
2025-05-27 08:01:37,980 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:01:38,496 - INFO - Request Parameters - Page 17:
2025-05-27 08:01:38,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:38,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:39,293 - INFO - API请求耗时: 797ms
2025-05-27 08:01:39,293 - INFO - Response - Page 17
2025-05-27 08:01:39,293 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:01:39,793 - INFO - Request Parameters - Page 18:
2025-05-27 08:01:39,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:39,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:40,511 - INFO - API请求耗时: 719ms
2025-05-27 08:01:40,511 - INFO - Response - Page 18
2025-05-27 08:01:40,511 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:01:41,011 - INFO - Request Parameters - Page 19:
2025-05-27 08:01:41,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:41,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:41,746 - INFO - API请求耗时: 734ms
2025-05-27 08:01:41,746 - INFO - Response - Page 19
2025-05-27 08:01:41,746 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:01:42,246 - INFO - Request Parameters - Page 20:
2025-05-27 08:01:42,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:42,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:42,980 - INFO - API请求耗时: 734ms
2025-05-27 08:01:42,980 - INFO - Response - Page 20
2025-05-27 08:01:42,980 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:01:43,480 - INFO - Request Parameters - Page 21:
2025-05-27 08:01:43,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:43,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:44,230 - INFO - API请求耗时: 750ms
2025-05-27 08:01:44,230 - INFO - Response - Page 21
2025-05-27 08:01:44,230 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:01:44,746 - INFO - Request Parameters - Page 22:
2025-05-27 08:01:44,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:44,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:45,511 - INFO - API请求耗时: 766ms
2025-05-27 08:01:45,511 - INFO - Response - Page 22
2025-05-27 08:01:45,511 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:01:46,011 - INFO - Request Parameters - Page 23:
2025-05-27 08:01:46,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:46,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:46,668 - INFO - API请求耗时: 656ms
2025-05-27 08:01:46,668 - INFO - Response - Page 23
2025-05-27 08:01:46,668 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:01:47,168 - INFO - Request Parameters - Page 24:
2025-05-27 08:01:47,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:47,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:47,824 - INFO - API请求耗时: 656ms
2025-05-27 08:01:47,824 - INFO - Response - Page 24
2025-05-27 08:01:47,824 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:01:48,324 - INFO - Request Parameters - Page 25:
2025-05-27 08:01:48,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:48,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:49,011 - INFO - API请求耗时: 688ms
2025-05-27 08:01:49,011 - INFO - Response - Page 25
2025-05-27 08:01:49,011 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:01:49,527 - INFO - Request Parameters - Page 26:
2025-05-27 08:01:49,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:49,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:50,230 - INFO - API请求耗时: 703ms
2025-05-27 08:01:50,230 - INFO - Response - Page 26
2025-05-27 08:01:50,230 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:01:50,730 - INFO - Request Parameters - Page 27:
2025-05-27 08:01:50,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:50,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:51,371 - INFO - API请求耗时: 641ms
2025-05-27 08:01:51,371 - INFO - Response - Page 27
2025-05-27 08:01:51,371 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:01:51,886 - INFO - Request Parameters - Page 28:
2025-05-27 08:01:51,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:51,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:52,511 - INFO - API请求耗时: 625ms
2025-05-27 08:01:52,511 - INFO - Response - Page 28
2025-05-27 08:01:52,511 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:01:53,011 - INFO - Request Parameters - Page 29:
2025-05-27 08:01:53,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:53,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:53,793 - INFO - API请求耗时: 781ms
2025-05-27 08:01:53,793 - INFO - Response - Page 29
2025-05-27 08:01:53,793 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:01:54,308 - INFO - Request Parameters - Page 30:
2025-05-27 08:01:54,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:54,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:54,980 - INFO - API请求耗时: 672ms
2025-05-27 08:01:54,980 - INFO - Response - Page 30
2025-05-27 08:01:54,980 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:01:55,480 - INFO - Request Parameters - Page 31:
2025-05-27 08:01:55,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:55,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:56,230 - INFO - API请求耗时: 750ms
2025-05-27 08:01:56,230 - INFO - Response - Page 31
2025-05-27 08:01:56,230 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:01:56,746 - INFO - Request Parameters - Page 32:
2025-05-27 08:01:56,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:56,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:57,480 - INFO - API请求耗时: 734ms
2025-05-27 08:01:57,480 - INFO - Response - Page 32
2025-05-27 08:01:57,480 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:01:57,980 - INFO - Request Parameters - Page 33:
2025-05-27 08:01:57,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:57,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:01:58,668 - INFO - API请求耗时: 687ms
2025-05-27 08:01:58,668 - INFO - Response - Page 33
2025-05-27 08:01:58,668 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:01:59,183 - INFO - Request Parameters - Page 34:
2025-05-27 08:01:59,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:01:59,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:00,089 - INFO - API请求耗时: 906ms
2025-05-27 08:02:00,089 - INFO - Response - Page 34
2025-05-27 08:02:00,089 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:02:00,605 - INFO - Request Parameters - Page 35:
2025-05-27 08:02:00,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:00,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:01,355 - INFO - API请求耗时: 750ms
2025-05-27 08:02:01,355 - INFO - Response - Page 35
2025-05-27 08:02:01,355 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:02:01,871 - INFO - Request Parameters - Page 36:
2025-05-27 08:02:01,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:01,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800090, 1743523200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:02,324 - INFO - API请求耗时: 453ms
2025-05-27 08:02:02,324 - INFO - Response - Page 36
2025-05-27 08:02:02,324 - INFO - 第 36 页获取到 10 条记录
2025-05-27 08:02:02,324 - INFO - 查询完成，共获取到 3510 条记录
2025-05-27 08:02:02,324 - INFO - 分段 1 查询成功，获取到 3510 条记录
2025-05-27 08:02:03,339 - INFO - 查询分段 2: 2025-04-03 至 2025-04-09
2025-05-27 08:02:03,339 - INFO - 查询日期范围: 2025-04-03 至 2025-04-09，使用分页查询，每页 100 条记录
2025-05-27 08:02:03,339 - INFO - Request Parameters - Page 1:
2025-05-27 08:02:03,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:03,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:04,168 - INFO - API请求耗时: 828ms
2025-05-27 08:02:04,168 - INFO - Response - Page 1
2025-05-27 08:02:04,168 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:02:04,683 - INFO - Request Parameters - Page 2:
2025-05-27 08:02:04,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:04,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:05,511 - INFO - API请求耗时: 828ms
2025-05-27 08:02:05,511 - INFO - Response - Page 2
2025-05-27 08:02:05,511 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:02:06,027 - INFO - Request Parameters - Page 3:
2025-05-27 08:02:06,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:06,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:06,730 - INFO - API请求耗时: 703ms
2025-05-27 08:02:06,730 - INFO - Response - Page 3
2025-05-27 08:02:06,730 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:02:07,246 - INFO - Request Parameters - Page 4:
2025-05-27 08:02:07,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:07,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:07,949 - INFO - API请求耗时: 703ms
2025-05-27 08:02:07,949 - INFO - Response - Page 4
2025-05-27 08:02:07,949 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:02:08,464 - INFO - Request Parameters - Page 5:
2025-05-27 08:02:08,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:08,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:09,230 - INFO - API请求耗时: 766ms
2025-05-27 08:02:09,230 - INFO - Response - Page 5
2025-05-27 08:02:09,230 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:02:09,746 - INFO - Request Parameters - Page 6:
2025-05-27 08:02:09,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:09,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:10,527 - INFO - API请求耗时: 781ms
2025-05-27 08:02:10,527 - INFO - Response - Page 6
2025-05-27 08:02:10,527 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:02:11,043 - INFO - Request Parameters - Page 7:
2025-05-27 08:02:11,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:11,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:11,793 - INFO - API请求耗时: 750ms
2025-05-27 08:02:11,793 - INFO - Response - Page 7
2025-05-27 08:02:11,793 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:02:12,308 - INFO - Request Parameters - Page 8:
2025-05-27 08:02:12,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:12,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:13,074 - INFO - API请求耗时: 766ms
2025-05-27 08:02:13,074 - INFO - Response - Page 8
2025-05-27 08:02:13,074 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:02:13,589 - INFO - Request Parameters - Page 9:
2025-05-27 08:02:13,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:13,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:14,292 - INFO - API请求耗时: 703ms
2025-05-27 08:02:14,292 - INFO - Response - Page 9
2025-05-27 08:02:14,292 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:02:14,808 - INFO - Request Parameters - Page 10:
2025-05-27 08:02:14,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:14,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:15,792 - INFO - API请求耗时: 984ms
2025-05-27 08:02:15,792 - INFO - Response - Page 10
2025-05-27 08:02:15,792 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:02:16,308 - INFO - Request Parameters - Page 11:
2025-05-27 08:02:16,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:16,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:17,027 - INFO - API请求耗时: 719ms
2025-05-27 08:02:17,027 - INFO - Response - Page 11
2025-05-27 08:02:17,027 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:02:17,527 - INFO - Request Parameters - Page 12:
2025-05-27 08:02:17,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:17,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:18,308 - INFO - API请求耗时: 781ms
2025-05-27 08:02:18,308 - INFO - Response - Page 12
2025-05-27 08:02:18,308 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:02:18,808 - INFO - Request Parameters - Page 13:
2025-05-27 08:02:18,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:18,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:19,527 - INFO - API请求耗时: 719ms
2025-05-27 08:02:19,527 - INFO - Response - Page 13
2025-05-27 08:02:19,527 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:02:20,042 - INFO - Request Parameters - Page 14:
2025-05-27 08:02:20,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:20,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:20,855 - INFO - API请求耗时: 812ms
2025-05-27 08:02:20,855 - INFO - Response - Page 14
2025-05-27 08:02:20,855 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:02:21,355 - INFO - Request Parameters - Page 15:
2025-05-27 08:02:21,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:21,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:22,058 - INFO - API请求耗时: 703ms
2025-05-27 08:02:22,058 - INFO - Response - Page 15
2025-05-27 08:02:22,074 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:02:22,589 - INFO - Request Parameters - Page 16:
2025-05-27 08:02:22,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:22,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:23,386 - INFO - API请求耗时: 797ms
2025-05-27 08:02:23,386 - INFO - Response - Page 16
2025-05-27 08:02:23,386 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:02:23,902 - INFO - Request Parameters - Page 17:
2025-05-27 08:02:23,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:23,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:24,636 - INFO - API请求耗时: 734ms
2025-05-27 08:02:24,636 - INFO - Response - Page 17
2025-05-27 08:02:24,636 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:02:25,152 - INFO - Request Parameters - Page 18:
2025-05-27 08:02:25,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:25,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:25,839 - INFO - API请求耗时: 687ms
2025-05-27 08:02:25,839 - INFO - Response - Page 18
2025-05-27 08:02:25,839 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:02:26,355 - INFO - Request Parameters - Page 19:
2025-05-27 08:02:26,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:26,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:27,011 - INFO - API请求耗时: 656ms
2025-05-27 08:02:27,011 - INFO - Response - Page 19
2025-05-27 08:02:27,011 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:02:27,511 - INFO - Request Parameters - Page 20:
2025-05-27 08:02:27,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:27,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:28,355 - INFO - API请求耗时: 844ms
2025-05-27 08:02:28,355 - INFO - Response - Page 20
2025-05-27 08:02:28,355 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:02:28,855 - INFO - Request Parameters - Page 21:
2025-05-27 08:02:28,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:28,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:29,511 - INFO - API请求耗时: 656ms
2025-05-27 08:02:29,511 - INFO - Response - Page 21
2025-05-27 08:02:29,511 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:02:30,011 - INFO - Request Parameters - Page 22:
2025-05-27 08:02:30,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:30,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:30,933 - INFO - API请求耗时: 922ms
2025-05-27 08:02:30,933 - INFO - Response - Page 22
2025-05-27 08:02:30,933 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:02:31,433 - INFO - Request Parameters - Page 23:
2025-05-27 08:02:31,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:31,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:32,152 - INFO - API请求耗时: 719ms
2025-05-27 08:02:32,152 - INFO - Response - Page 23
2025-05-27 08:02:32,152 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:02:32,667 - INFO - Request Parameters - Page 24:
2025-05-27 08:02:32,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:32,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:33,339 - INFO - API请求耗时: 672ms
2025-05-27 08:02:33,339 - INFO - Response - Page 24
2025-05-27 08:02:33,355 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:02:33,870 - INFO - Request Parameters - Page 25:
2025-05-27 08:02:33,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:33,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:34,527 - INFO - API请求耗时: 656ms
2025-05-27 08:02:34,527 - INFO - Response - Page 25
2025-05-27 08:02:34,527 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:02:35,042 - INFO - Request Parameters - Page 26:
2025-05-27 08:02:35,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:35,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:35,808 - INFO - API请求耗时: 766ms
2025-05-27 08:02:35,808 - INFO - Response - Page 26
2025-05-27 08:02:35,808 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:02:36,324 - INFO - Request Parameters - Page 27:
2025-05-27 08:02:36,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:36,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:37,042 - INFO - API请求耗时: 719ms
2025-05-27 08:02:37,042 - INFO - Response - Page 27
2025-05-27 08:02:37,042 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:02:37,558 - INFO - Request Parameters - Page 28:
2025-05-27 08:02:37,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:37,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:38,277 - INFO - API请求耗时: 719ms
2025-05-27 08:02:38,277 - INFO - Response - Page 28
2025-05-27 08:02:38,277 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:02:38,777 - INFO - Request Parameters - Page 29:
2025-05-27 08:02:38,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:38,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:39,542 - INFO - API请求耗时: 766ms
2025-05-27 08:02:39,542 - INFO - Response - Page 29
2025-05-27 08:02:39,542 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:02:40,058 - INFO - Request Parameters - Page 30:
2025-05-27 08:02:40,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:40,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:40,870 - INFO - API请求耗时: 812ms
2025-05-27 08:02:40,870 - INFO - Response - Page 30
2025-05-27 08:02:40,870 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:02:41,386 - INFO - Request Parameters - Page 31:
2025-05-27 08:02:41,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:41,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:42,042 - INFO - API请求耗时: 656ms
2025-05-27 08:02:42,042 - INFO - Response - Page 31
2025-05-27 08:02:42,058 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:02:42,558 - INFO - Request Parameters - Page 32:
2025-05-27 08:02:42,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:42,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:43,261 - INFO - API请求耗时: 703ms
2025-05-27 08:02:43,261 - INFO - Response - Page 32
2025-05-27 08:02:43,261 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:02:43,777 - INFO - Request Parameters - Page 33:
2025-05-27 08:02:43,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:43,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:44,495 - INFO - API请求耗时: 719ms
2025-05-27 08:02:44,495 - INFO - Response - Page 33
2025-05-27 08:02:44,495 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:02:44,995 - INFO - Request Parameters - Page 34:
2025-05-27 08:02:44,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:44,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:45,667 - INFO - API请求耗时: 672ms
2025-05-27 08:02:45,667 - INFO - Response - Page 34
2025-05-27 08:02:45,667 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:02:46,183 - INFO - Request Parameters - Page 35:
2025-05-27 08:02:46,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:46,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:46,886 - INFO - API请求耗时: 703ms
2025-05-27 08:02:46,886 - INFO - Response - Page 35
2025-05-27 08:02:46,886 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:02:47,386 - INFO - Request Parameters - Page 36:
2025-05-27 08:02:47,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:47,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:48,042 - INFO - API请求耗时: 656ms
2025-05-27 08:02:48,042 - INFO - Response - Page 36
2025-05-27 08:02:48,042 - INFO - 第 36 页获取到 100 条记录
2025-05-27 08:02:48,558 - INFO - Request Parameters - Page 37:
2025-05-27 08:02:48,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:48,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600090, 1744128000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:49,136 - INFO - API请求耗时: 578ms
2025-05-27 08:02:49,136 - INFO - Response - Page 37
2025-05-27 08:02:49,136 - INFO - 第 37 页获取到 37 条记录
2025-05-27 08:02:49,136 - INFO - 查询完成，共获取到 3637 条记录
2025-05-27 08:02:49,136 - INFO - 分段 2 查询成功，获取到 3637 条记录
2025-05-27 08:02:50,152 - INFO - 查询分段 3: 2025-04-10 至 2025-04-16
2025-05-27 08:02:50,152 - INFO - 查询日期范围: 2025-04-10 至 2025-04-16，使用分页查询，每页 100 条记录
2025-05-27 08:02:50,152 - INFO - Request Parameters - Page 1:
2025-05-27 08:02:50,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:50,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:50,995 - INFO - API请求耗时: 844ms
2025-05-27 08:02:50,995 - INFO - Response - Page 1
2025-05-27 08:02:51,011 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:02:51,511 - INFO - Request Parameters - Page 2:
2025-05-27 08:02:51,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:51,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:52,198 - INFO - API请求耗时: 687ms
2025-05-27 08:02:52,198 - INFO - Response - Page 2
2025-05-27 08:02:52,198 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:02:52,714 - INFO - Request Parameters - Page 3:
2025-05-27 08:02:52,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:52,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:53,464 - INFO - API请求耗时: 750ms
2025-05-27 08:02:53,464 - INFO - Response - Page 3
2025-05-27 08:02:53,464 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:02:53,964 - INFO - Request Parameters - Page 4:
2025-05-27 08:02:53,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:53,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:54,683 - INFO - API请求耗时: 719ms
2025-05-27 08:02:54,683 - INFO - Response - Page 4
2025-05-27 08:02:54,683 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:02:55,198 - INFO - Request Parameters - Page 5:
2025-05-27 08:02:55,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:55,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:55,823 - INFO - API请求耗时: 625ms
2025-05-27 08:02:55,823 - INFO - Response - Page 5
2025-05-27 08:02:55,839 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:02:56,339 - INFO - Request Parameters - Page 6:
2025-05-27 08:02:56,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:56,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:57,042 - INFO - API请求耗时: 703ms
2025-05-27 08:02:57,042 - INFO - Response - Page 6
2025-05-27 08:02:57,042 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:02:57,558 - INFO - Request Parameters - Page 7:
2025-05-27 08:02:57,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:57,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:58,277 - INFO - API请求耗时: 719ms
2025-05-27 08:02:58,277 - INFO - Response - Page 7
2025-05-27 08:02:58,277 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:02:58,792 - INFO - Request Parameters - Page 8:
2025-05-27 08:02:58,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:02:58,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:02:59,511 - INFO - API请求耗时: 703ms
2025-05-27 08:02:59,511 - INFO - Response - Page 8
2025-05-27 08:02:59,511 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:03:00,027 - INFO - Request Parameters - Page 9:
2025-05-27 08:03:00,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:00,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:00,730 - INFO - API请求耗时: 687ms
2025-05-27 08:03:00,730 - INFO - Response - Page 9
2025-05-27 08:03:00,730 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:03:01,230 - INFO - Request Parameters - Page 10:
2025-05-27 08:03:01,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:01,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:02,089 - INFO - API请求耗时: 859ms
2025-05-27 08:03:02,089 - INFO - Response - Page 10
2025-05-27 08:03:02,089 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:03:02,605 - INFO - Request Parameters - Page 11:
2025-05-27 08:03:02,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:02,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:03,370 - INFO - API请求耗时: 766ms
2025-05-27 08:03:03,370 - INFO - Response - Page 11
2025-05-27 08:03:03,370 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:03:03,870 - INFO - Request Parameters - Page 12:
2025-05-27 08:03:03,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:03,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:04,605 - INFO - API请求耗时: 734ms
2025-05-27 08:03:04,605 - INFO - Response - Page 12
2025-05-27 08:03:04,605 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:03:05,120 - INFO - Request Parameters - Page 13:
2025-05-27 08:03:05,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:05,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:05,823 - INFO - API请求耗时: 703ms
2025-05-27 08:03:05,823 - INFO - Response - Page 13
2025-05-27 08:03:05,823 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:03:06,339 - INFO - Request Parameters - Page 14:
2025-05-27 08:03:06,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:06,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:07,011 - INFO - API请求耗时: 672ms
2025-05-27 08:03:07,011 - INFO - Response - Page 14
2025-05-27 08:03:07,011 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:03:07,511 - INFO - Request Parameters - Page 15:
2025-05-27 08:03:07,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:07,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:08,261 - INFO - API请求耗时: 750ms
2025-05-27 08:03:08,261 - INFO - Response - Page 15
2025-05-27 08:03:08,261 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:03:08,777 - INFO - Request Parameters - Page 16:
2025-05-27 08:03:08,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:08,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:09,511 - INFO - API请求耗时: 734ms
2025-05-27 08:03:09,511 - INFO - Response - Page 16
2025-05-27 08:03:09,511 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:03:10,011 - INFO - Request Parameters - Page 17:
2025-05-27 08:03:10,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:10,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:10,698 - INFO - API请求耗时: 687ms
2025-05-27 08:03:10,698 - INFO - Response - Page 17
2025-05-27 08:03:10,698 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:03:11,198 - INFO - Request Parameters - Page 18:
2025-05-27 08:03:11,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:11,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:11,933 - INFO - API请求耗时: 734ms
2025-05-27 08:03:11,933 - INFO - Response - Page 18
2025-05-27 08:03:11,933 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:03:12,433 - INFO - Request Parameters - Page 19:
2025-05-27 08:03:12,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:12,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:13,105 - INFO - API请求耗时: 672ms
2025-05-27 08:03:13,105 - INFO - Response - Page 19
2025-05-27 08:03:13,105 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:03:13,620 - INFO - Request Parameters - Page 20:
2025-05-27 08:03:13,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:13,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:14,323 - INFO - API请求耗时: 703ms
2025-05-27 08:03:14,323 - INFO - Response - Page 20
2025-05-27 08:03:14,323 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:03:14,823 - INFO - Request Parameters - Page 21:
2025-05-27 08:03:14,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:14,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:15,542 - INFO - API请求耗时: 719ms
2025-05-27 08:03:15,542 - INFO - Response - Page 21
2025-05-27 08:03:15,542 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:03:16,058 - INFO - Request Parameters - Page 22:
2025-05-27 08:03:16,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:16,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:16,745 - INFO - API请求耗时: 687ms
2025-05-27 08:03:16,745 - INFO - Response - Page 22
2025-05-27 08:03:16,745 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:03:17,261 - INFO - Request Parameters - Page 23:
2025-05-27 08:03:17,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:17,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:17,917 - INFO - API请求耗时: 656ms
2025-05-27 08:03:17,917 - INFO - Response - Page 23
2025-05-27 08:03:17,917 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:03:18,417 - INFO - Request Parameters - Page 24:
2025-05-27 08:03:18,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:18,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:19,214 - INFO - API请求耗时: 797ms
2025-05-27 08:03:19,214 - INFO - Response - Page 24
2025-05-27 08:03:19,214 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:03:19,714 - INFO - Request Parameters - Page 25:
2025-05-27 08:03:19,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:19,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:20,417 - INFO - API请求耗时: 703ms
2025-05-27 08:03:20,417 - INFO - Response - Page 25
2025-05-27 08:03:20,417 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:03:20,933 - INFO - Request Parameters - Page 26:
2025-05-27 08:03:20,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:20,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:21,730 - INFO - API请求耗时: 797ms
2025-05-27 08:03:21,730 - INFO - Response - Page 26
2025-05-27 08:03:21,730 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:03:22,230 - INFO - Request Parameters - Page 27:
2025-05-27 08:03:22,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:22,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:22,901 - INFO - API请求耗时: 672ms
2025-05-27 08:03:22,901 - INFO - Response - Page 27
2025-05-27 08:03:22,901 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:03:23,417 - INFO - Request Parameters - Page 28:
2025-05-27 08:03:23,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:23,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:24,151 - INFO - API请求耗时: 734ms
2025-05-27 08:03:24,151 - INFO - Response - Page 28
2025-05-27 08:03:24,151 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:03:24,667 - INFO - Request Parameters - Page 29:
2025-05-27 08:03:24,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:24,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:25,323 - INFO - API请求耗时: 656ms
2025-05-27 08:03:25,323 - INFO - Response - Page 29
2025-05-27 08:03:25,323 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:03:25,839 - INFO - Request Parameters - Page 30:
2025-05-27 08:03:25,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:25,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:26,558 - INFO - API请求耗时: 719ms
2025-05-27 08:03:26,558 - INFO - Response - Page 30
2025-05-27 08:03:26,558 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:03:27,058 - INFO - Request Parameters - Page 31:
2025-05-27 08:03:27,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:27,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:27,761 - INFO - API请求耗时: 703ms
2025-05-27 08:03:27,761 - INFO - Response - Page 31
2025-05-27 08:03:27,761 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:03:28,276 - INFO - Request Parameters - Page 32:
2025-05-27 08:03:28,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:28,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:28,964 - INFO - API请求耗时: 688ms
2025-05-27 08:03:28,964 - INFO - Response - Page 32
2025-05-27 08:03:28,964 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:03:29,480 - INFO - Request Parameters - Page 33:
2025-05-27 08:03:29,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:29,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:30,136 - INFO - API请求耗时: 656ms
2025-05-27 08:03:30,136 - INFO - Response - Page 33
2025-05-27 08:03:30,136 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:03:30,636 - INFO - Request Parameters - Page 34:
2025-05-27 08:03:30,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:30,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:31,339 - INFO - API请求耗时: 703ms
2025-05-27 08:03:31,339 - INFO - Response - Page 34
2025-05-27 08:03:31,339 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:03:31,854 - INFO - Request Parameters - Page 35:
2025-05-27 08:03:31,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:31,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:32,542 - INFO - API请求耗时: 688ms
2025-05-27 08:03:32,542 - INFO - Response - Page 35
2025-05-27 08:03:32,542 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:03:33,058 - INFO - Request Parameters - Page 36:
2025-05-27 08:03:33,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:33,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:33,792 - INFO - API请求耗时: 734ms
2025-05-27 08:03:33,792 - INFO - Response - Page 36
2025-05-27 08:03:33,792 - INFO - 第 36 页获取到 100 条记录
2025-05-27 08:03:34,308 - INFO - Request Parameters - Page 37:
2025-05-27 08:03:34,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:34,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400090, 1744732800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:34,979 - INFO - API请求耗时: 672ms
2025-05-27 08:03:34,979 - INFO - Response - Page 37
2025-05-27 08:03:34,995 - INFO - 第 37 页获取到 53 条记录
2025-05-27 08:03:34,995 - INFO - 查询完成，共获取到 3653 条记录
2025-05-27 08:03:34,995 - INFO - 分段 3 查询成功，获取到 3653 条记录
2025-05-27 08:03:36,011 - INFO - 查询分段 4: 2025-04-17 至 2025-04-23
2025-05-27 08:03:36,011 - INFO - 查询日期范围: 2025-04-17 至 2025-04-23，使用分页查询，每页 100 条记录
2025-05-27 08:03:36,011 - INFO - Request Parameters - Page 1:
2025-05-27 08:03:36,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:36,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:36,729 - INFO - API请求耗时: 719ms
2025-05-27 08:03:36,729 - INFO - Response - Page 1
2025-05-27 08:03:36,729 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:03:37,229 - INFO - Request Parameters - Page 2:
2025-05-27 08:03:37,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:37,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:37,964 - INFO - API请求耗时: 734ms
2025-05-27 08:03:37,964 - INFO - Response - Page 2
2025-05-27 08:03:37,964 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:03:38,464 - INFO - Request Parameters - Page 3:
2025-05-27 08:03:38,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:38,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:39,229 - INFO - API请求耗时: 766ms
2025-05-27 08:03:39,229 - INFO - Response - Page 3
2025-05-27 08:03:39,229 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:03:39,745 - INFO - Request Parameters - Page 4:
2025-05-27 08:03:39,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:39,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:40,589 - INFO - API请求耗时: 844ms
2025-05-27 08:03:40,589 - INFO - Response - Page 4
2025-05-27 08:03:40,589 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:03:41,104 - INFO - Request Parameters - Page 5:
2025-05-27 08:03:41,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:41,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:41,823 - INFO - API请求耗时: 719ms
2025-05-27 08:03:41,823 - INFO - Response - Page 5
2025-05-27 08:03:41,823 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:03:42,323 - INFO - Request Parameters - Page 6:
2025-05-27 08:03:42,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:42,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:42,995 - INFO - API请求耗时: 672ms
2025-05-27 08:03:42,995 - INFO - Response - Page 6
2025-05-27 08:03:42,995 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:03:43,495 - INFO - Request Parameters - Page 7:
2025-05-27 08:03:43,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:43,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:44,261 - INFO - API请求耗时: 766ms
2025-05-27 08:03:44,261 - INFO - Response - Page 7
2025-05-27 08:03:44,261 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:03:44,761 - INFO - Request Parameters - Page 8:
2025-05-27 08:03:44,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:44,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:45,495 - INFO - API请求耗时: 734ms
2025-05-27 08:03:45,495 - INFO - Response - Page 8
2025-05-27 08:03:45,495 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:03:46,011 - INFO - Request Parameters - Page 9:
2025-05-27 08:03:46,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:46,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:46,698 - INFO - API请求耗时: 687ms
2025-05-27 08:03:46,698 - INFO - Response - Page 9
2025-05-27 08:03:46,698 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:03:47,214 - INFO - Request Parameters - Page 10:
2025-05-27 08:03:47,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:47,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:48,073 - INFO - API请求耗时: 859ms
2025-05-27 08:03:48,073 - INFO - Response - Page 10
2025-05-27 08:03:48,073 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:03:48,589 - INFO - Request Parameters - Page 11:
2025-05-27 08:03:48,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:48,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:49,229 - INFO - API请求耗时: 641ms
2025-05-27 08:03:49,229 - INFO - Response - Page 11
2025-05-27 08:03:49,229 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:03:49,745 - INFO - Request Parameters - Page 12:
2025-05-27 08:03:49,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:49,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:50,557 - INFO - API请求耗时: 812ms
2025-05-27 08:03:50,557 - INFO - Response - Page 12
2025-05-27 08:03:50,557 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:03:51,073 - INFO - Request Parameters - Page 13:
2025-05-27 08:03:51,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:51,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:51,807 - INFO - API请求耗时: 734ms
2025-05-27 08:03:51,807 - INFO - Response - Page 13
2025-05-27 08:03:51,807 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:03:52,323 - INFO - Request Parameters - Page 14:
2025-05-27 08:03:52,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:52,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:53,042 - INFO - API请求耗时: 719ms
2025-05-27 08:03:53,042 - INFO - Response - Page 14
2025-05-27 08:03:53,042 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:03:53,542 - INFO - Request Parameters - Page 15:
2025-05-27 08:03:53,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:53,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:54,229 - INFO - API请求耗时: 688ms
2025-05-27 08:03:54,229 - INFO - Response - Page 15
2025-05-27 08:03:54,229 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:03:54,745 - INFO - Request Parameters - Page 16:
2025-05-27 08:03:54,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:54,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:55,464 - INFO - API请求耗时: 719ms
2025-05-27 08:03:55,464 - INFO - Response - Page 16
2025-05-27 08:03:55,464 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:03:55,964 - INFO - Request Parameters - Page 17:
2025-05-27 08:03:55,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:55,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:56,776 - INFO - API请求耗时: 812ms
2025-05-27 08:03:56,776 - INFO - Response - Page 17
2025-05-27 08:03:56,776 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:03:57,276 - INFO - Request Parameters - Page 18:
2025-05-27 08:03:57,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:57,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:58,073 - INFO - API请求耗时: 797ms
2025-05-27 08:03:58,073 - INFO - Response - Page 18
2025-05-27 08:03:58,073 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:03:58,589 - INFO - Request Parameters - Page 19:
2025-05-27 08:03:58,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:58,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:03:59,323 - INFO - API请求耗时: 734ms
2025-05-27 08:03:59,323 - INFO - Response - Page 19
2025-05-27 08:03:59,323 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:03:59,823 - INFO - Request Parameters - Page 20:
2025-05-27 08:03:59,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:03:59,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:00,526 - INFO - API请求耗时: 703ms
2025-05-27 08:04:00,526 - INFO - Response - Page 20
2025-05-27 08:04:00,526 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:04:01,042 - INFO - Request Parameters - Page 21:
2025-05-27 08:04:01,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:01,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:01,714 - INFO - API请求耗时: 672ms
2025-05-27 08:04:01,714 - INFO - Response - Page 21
2025-05-27 08:04:01,714 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:04:02,229 - INFO - Request Parameters - Page 22:
2025-05-27 08:04:02,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:02,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:02,995 - INFO - API请求耗时: 766ms
2025-05-27 08:04:02,995 - INFO - Response - Page 22
2025-05-27 08:04:02,995 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:04:03,511 - INFO - Request Parameters - Page 23:
2025-05-27 08:04:03,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:03,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:04,276 - INFO - API请求耗时: 766ms
2025-05-27 08:04:04,276 - INFO - Response - Page 23
2025-05-27 08:04:04,276 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:04:04,792 - INFO - Request Parameters - Page 24:
2025-05-27 08:04:04,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:04,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:05,448 - INFO - API请求耗时: 656ms
2025-05-27 08:04:05,448 - INFO - Response - Page 24
2025-05-27 08:04:05,448 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:04:05,964 - INFO - Request Parameters - Page 25:
2025-05-27 08:04:05,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:05,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:06,682 - INFO - API请求耗时: 719ms
2025-05-27 08:04:06,682 - INFO - Response - Page 25
2025-05-27 08:04:06,682 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:04:07,198 - INFO - Request Parameters - Page 26:
2025-05-27 08:04:07,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:07,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:08,089 - INFO - API请求耗时: 891ms
2025-05-27 08:04:08,089 - INFO - Response - Page 26
2025-05-27 08:04:08,089 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:04:08,620 - INFO - Request Parameters - Page 27:
2025-05-27 08:04:08,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:08,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:09,589 - INFO - API请求耗时: 969ms
2025-05-27 08:04:09,589 - INFO - Response - Page 27
2025-05-27 08:04:09,604 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:04:10,104 - INFO - Request Parameters - Page 28:
2025-05-27 08:04:10,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:10,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:10,839 - INFO - API请求耗时: 734ms
2025-05-27 08:04:10,839 - INFO - Response - Page 28
2025-05-27 08:04:10,839 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:04:11,354 - INFO - Request Parameters - Page 29:
2025-05-27 08:04:11,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:11,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:12,073 - INFO - API请求耗时: 719ms
2025-05-27 08:04:12,073 - INFO - Response - Page 29
2025-05-27 08:04:12,073 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:04:12,589 - INFO - Request Parameters - Page 30:
2025-05-27 08:04:12,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:12,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:13,292 - INFO - API请求耗时: 703ms
2025-05-27 08:04:13,292 - INFO - Response - Page 30
2025-05-27 08:04:13,292 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:04:13,807 - INFO - Request Parameters - Page 31:
2025-05-27 08:04:13,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:13,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:14,620 - INFO - API请求耗时: 812ms
2025-05-27 08:04:14,620 - INFO - Response - Page 31
2025-05-27 08:04:14,620 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:04:15,135 - INFO - Request Parameters - Page 32:
2025-05-27 08:04:15,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:15,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:15,885 - INFO - API请求耗时: 750ms
2025-05-27 08:04:15,885 - INFO - Response - Page 32
2025-05-27 08:04:15,885 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:04:16,401 - INFO - Request Parameters - Page 33:
2025-05-27 08:04:16,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:16,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:17,089 - INFO - API请求耗时: 688ms
2025-05-27 08:04:17,089 - INFO - Response - Page 33
2025-05-27 08:04:17,089 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:04:17,604 - INFO - Request Parameters - Page 34:
2025-05-27 08:04:17,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:17,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:18,276 - INFO - API请求耗时: 672ms
2025-05-27 08:04:18,276 - INFO - Response - Page 34
2025-05-27 08:04:18,276 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:04:18,776 - INFO - Request Parameters - Page 35:
2025-05-27 08:04:18,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:18,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:19,542 - INFO - API请求耗时: 766ms
2025-05-27 08:04:19,542 - INFO - Response - Page 35
2025-05-27 08:04:19,542 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:04:20,042 - INFO - Request Parameters - Page 36:
2025-05-27 08:04:20,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:20,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:20,729 - INFO - API请求耗时: 687ms
2025-05-27 08:04:20,729 - INFO - Response - Page 36
2025-05-27 08:04:20,729 - INFO - 第 36 页获取到 100 条记录
2025-05-27 08:04:21,229 - INFO - Request Parameters - Page 37:
2025-05-27 08:04:21,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:21,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200090, 1745337600090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:21,714 - INFO - API请求耗时: 484ms
2025-05-27 08:04:21,714 - INFO - Response - Page 37
2025-05-27 08:04:21,714 - INFO - 第 37 页获取到 15 条记录
2025-05-27 08:04:21,714 - INFO - 查询完成，共获取到 3615 条记录
2025-05-27 08:04:21,714 - INFO - 分段 4 查询成功，获取到 3615 条记录
2025-05-27 08:04:22,729 - INFO - 查询分段 5: 2025-04-24 至 2025-04-30
2025-05-27 08:04:22,729 - INFO - 查询日期范围: 2025-04-24 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-27 08:04:22,729 - INFO - Request Parameters - Page 1:
2025-05-27 08:04:22,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:22,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:23,417 - INFO - API请求耗时: 687ms
2025-05-27 08:04:23,417 - INFO - Response - Page 1
2025-05-27 08:04:23,417 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:04:23,917 - INFO - Request Parameters - Page 2:
2025-05-27 08:04:23,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:23,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:24,635 - INFO - API请求耗时: 719ms
2025-05-27 08:04:24,635 - INFO - Response - Page 2
2025-05-27 08:04:24,635 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:04:25,151 - INFO - Request Parameters - Page 3:
2025-05-27 08:04:25,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:25,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:25,839 - INFO - API请求耗时: 687ms
2025-05-27 08:04:25,839 - INFO - Response - Page 3
2025-05-27 08:04:25,839 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:04:26,354 - INFO - Request Parameters - Page 4:
2025-05-27 08:04:26,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:26,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:27,089 - INFO - API请求耗时: 734ms
2025-05-27 08:04:27,089 - INFO - Response - Page 4
2025-05-27 08:04:27,089 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:04:27,604 - INFO - Request Parameters - Page 5:
2025-05-27 08:04:27,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:27,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:28,354 - INFO - API请求耗时: 750ms
2025-05-27 08:04:28,354 - INFO - Response - Page 5
2025-05-27 08:04:28,354 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:04:28,854 - INFO - Request Parameters - Page 6:
2025-05-27 08:04:28,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:28,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:29,651 - INFO - API请求耗时: 797ms
2025-05-27 08:04:29,651 - INFO - Response - Page 6
2025-05-27 08:04:29,651 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:04:30,167 - INFO - Request Parameters - Page 7:
2025-05-27 08:04:30,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:30,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:30,854 - INFO - API请求耗时: 687ms
2025-05-27 08:04:30,854 - INFO - Response - Page 7
2025-05-27 08:04:30,854 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:04:31,370 - INFO - Request Parameters - Page 8:
2025-05-27 08:04:31,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:31,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:32,120 - INFO - API请求耗时: 750ms
2025-05-27 08:04:32,120 - INFO - Response - Page 8
2025-05-27 08:04:32,135 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:04:32,651 - INFO - Request Parameters - Page 9:
2025-05-27 08:04:32,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:32,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:33,323 - INFO - API请求耗时: 672ms
2025-05-27 08:04:33,323 - INFO - Response - Page 9
2025-05-27 08:04:33,323 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:04:33,838 - INFO - Request Parameters - Page 10:
2025-05-27 08:04:33,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:33,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:34,495 - INFO - API请求耗时: 656ms
2025-05-27 08:04:34,495 - INFO - Response - Page 10
2025-05-27 08:04:34,495 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:04:34,995 - INFO - Request Parameters - Page 11:
2025-05-27 08:04:34,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:34,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:35,745 - INFO - API请求耗时: 750ms
2025-05-27 08:04:35,745 - INFO - Response - Page 11
2025-05-27 08:04:35,745 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:04:36,245 - INFO - Request Parameters - Page 12:
2025-05-27 08:04:36,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:36,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:36,917 - INFO - API请求耗时: 672ms
2025-05-27 08:04:36,917 - INFO - Response - Page 12
2025-05-27 08:04:36,917 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:04:37,432 - INFO - Request Parameters - Page 13:
2025-05-27 08:04:37,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:37,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:38,135 - INFO - API请求耗时: 703ms
2025-05-27 08:04:38,135 - INFO - Response - Page 13
2025-05-27 08:04:38,135 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:04:38,635 - INFO - Request Parameters - Page 14:
2025-05-27 08:04:38,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:38,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:39,307 - INFO - API请求耗时: 672ms
2025-05-27 08:04:39,307 - INFO - Response - Page 14
2025-05-27 08:04:39,307 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:04:39,807 - INFO - Request Parameters - Page 15:
2025-05-27 08:04:39,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:39,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:40,510 - INFO - API请求耗时: 703ms
2025-05-27 08:04:40,510 - INFO - Response - Page 15
2025-05-27 08:04:40,510 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:04:41,026 - INFO - Request Parameters - Page 16:
2025-05-27 08:04:41,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:41,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:41,792 - INFO - API请求耗时: 766ms
2025-05-27 08:04:41,792 - INFO - Response - Page 16
2025-05-27 08:04:41,792 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:04:42,307 - INFO - Request Parameters - Page 17:
2025-05-27 08:04:42,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:42,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:42,932 - INFO - API请求耗时: 625ms
2025-05-27 08:04:42,932 - INFO - Response - Page 17
2025-05-27 08:04:42,932 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:04:43,432 - INFO - Request Parameters - Page 18:
2025-05-27 08:04:43,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:43,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:44,167 - INFO - API请求耗时: 734ms
2025-05-27 08:04:44,167 - INFO - Response - Page 18
2025-05-27 08:04:44,167 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:04:44,682 - INFO - Request Parameters - Page 19:
2025-05-27 08:04:44,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:44,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:45,463 - INFO - API请求耗时: 781ms
2025-05-27 08:04:45,463 - INFO - Response - Page 19
2025-05-27 08:04:45,463 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:04:45,979 - INFO - Request Parameters - Page 20:
2025-05-27 08:04:45,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:45,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:46,854 - INFO - API请求耗时: 875ms
2025-05-27 08:04:46,854 - INFO - Response - Page 20
2025-05-27 08:04:46,854 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:04:47,354 - INFO - Request Parameters - Page 21:
2025-05-27 08:04:47,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:47,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:48,041 - INFO - API请求耗时: 687ms
2025-05-27 08:04:48,041 - INFO - Response - Page 21
2025-05-27 08:04:48,041 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:04:48,541 - INFO - Request Parameters - Page 22:
2025-05-27 08:04:48,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:48,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:49,276 - INFO - API请求耗时: 734ms
2025-05-27 08:04:49,276 - INFO - Response - Page 22
2025-05-27 08:04:49,276 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:04:49,791 - INFO - Request Parameters - Page 23:
2025-05-27 08:04:49,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:49,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:50,510 - INFO - API请求耗时: 719ms
2025-05-27 08:04:50,510 - INFO - Response - Page 23
2025-05-27 08:04:50,510 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:04:51,010 - INFO - Request Parameters - Page 24:
2025-05-27 08:04:51,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:51,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:51,713 - INFO - API请求耗时: 703ms
2025-05-27 08:04:51,713 - INFO - Response - Page 24
2025-05-27 08:04:51,713 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:04:52,213 - INFO - Request Parameters - Page 25:
2025-05-27 08:04:52,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:52,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:53,073 - INFO - API请求耗时: 859ms
2025-05-27 08:04:53,073 - INFO - Response - Page 25
2025-05-27 08:04:53,073 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:04:53,588 - INFO - Request Parameters - Page 26:
2025-05-27 08:04:53,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:53,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:54,323 - INFO - API请求耗时: 734ms
2025-05-27 08:04:54,323 - INFO - Response - Page 26
2025-05-27 08:04:54,323 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:04:54,823 - INFO - Request Parameters - Page 27:
2025-05-27 08:04:54,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:54,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:55,604 - INFO - API请求耗时: 781ms
2025-05-27 08:04:55,604 - INFO - Response - Page 27
2025-05-27 08:04:55,604 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:04:56,120 - INFO - Request Parameters - Page 28:
2025-05-27 08:04:56,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:56,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:56,807 - INFO - API请求耗时: 687ms
2025-05-27 08:04:56,807 - INFO - Response - Page 28
2025-05-27 08:04:56,807 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:04:57,323 - INFO - Request Parameters - Page 29:
2025-05-27 08:04:57,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:57,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:57,995 - INFO - API请求耗时: 672ms
2025-05-27 08:04:57,995 - INFO - Response - Page 29
2025-05-27 08:04:57,995 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:04:58,510 - INFO - Request Parameters - Page 30:
2025-05-27 08:04:58,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:58,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:04:59,291 - INFO - API请求耗时: 781ms
2025-05-27 08:04:59,291 - INFO - Response - Page 30
2025-05-27 08:04:59,291 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:04:59,791 - INFO - Request Parameters - Page 31:
2025-05-27 08:04:59,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:04:59,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:00,416 - INFO - API请求耗时: 625ms
2025-05-27 08:05:00,416 - INFO - Response - Page 31
2025-05-27 08:05:00,416 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:05:00,916 - INFO - Request Parameters - Page 32:
2025-05-27 08:05:00,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:00,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:01,604 - INFO - API请求耗时: 688ms
2025-05-27 08:05:01,604 - INFO - Response - Page 32
2025-05-27 08:05:01,604 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:05:02,135 - INFO - Request Parameters - Page 33:
2025-05-27 08:05:02,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:02,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:02,838 - INFO - API请求耗时: 703ms
2025-05-27 08:05:02,838 - INFO - Response - Page 33
2025-05-27 08:05:02,838 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:05:03,338 - INFO - Request Parameters - Page 34:
2025-05-27 08:05:03,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:03,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:04,057 - INFO - API请求耗时: 719ms
2025-05-27 08:05:04,057 - INFO - Response - Page 34
2025-05-27 08:05:04,057 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:05:04,573 - INFO - Request Parameters - Page 35:
2025-05-27 08:05:04,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:04,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:05,291 - INFO - API请求耗时: 719ms
2025-05-27 08:05:05,291 - INFO - Response - Page 35
2025-05-27 08:05:05,291 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:05:05,791 - INFO - Request Parameters - Page 36:
2025-05-27 08:05:05,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:05,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:06,557 - INFO - API请求耗时: 766ms
2025-05-27 08:05:06,557 - INFO - Response - Page 36
2025-05-27 08:05:06,557 - INFO - 第 36 页获取到 100 条记录
2025-05-27 08:05:07,057 - INFO - Request Parameters - Page 37:
2025-05-27 08:05:07,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:07,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000090, 1745942400090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:07,619 - INFO - API请求耗时: 562ms
2025-05-27 08:05:07,619 - INFO - Response - Page 37
2025-05-27 08:05:07,619 - INFO - 第 37 页获取到 28 条记录
2025-05-27 08:05:07,619 - INFO - 查询完成，共获取到 3628 条记录
2025-05-27 08:05:07,619 - INFO - 分段 5 查询成功，获取到 3628 条记录
2025-05-27 08:05:08,619 - INFO - 查询分段 6: 2025-05-01 至 2025-05-07
2025-05-27 08:05:08,619 - INFO - 查询日期范围: 2025-05-01 至 2025-05-07，使用分页查询，每页 100 条记录
2025-05-27 08:05:08,619 - INFO - Request Parameters - Page 1:
2025-05-27 08:05:08,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:08,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:09,291 - INFO - API请求耗时: 672ms
2025-05-27 08:05:09,291 - INFO - Response - Page 1
2025-05-27 08:05:09,291 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:05:09,807 - INFO - Request Parameters - Page 2:
2025-05-27 08:05:09,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:09,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:10,698 - INFO - API请求耗时: 891ms
2025-05-27 08:05:10,698 - INFO - Response - Page 2
2025-05-27 08:05:10,698 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:05:11,198 - INFO - Request Parameters - Page 3:
2025-05-27 08:05:11,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:11,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:11,885 - INFO - API请求耗时: 687ms
2025-05-27 08:05:11,885 - INFO - Response - Page 3
2025-05-27 08:05:11,885 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:05:12,401 - INFO - Request Parameters - Page 4:
2025-05-27 08:05:12,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:12,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:13,244 - INFO - API请求耗时: 844ms
2025-05-27 08:05:13,244 - INFO - Response - Page 4
2025-05-27 08:05:13,244 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:05:13,760 - INFO - Request Parameters - Page 5:
2025-05-27 08:05:13,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:13,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:14,448 - INFO - API请求耗时: 688ms
2025-05-27 08:05:14,448 - INFO - Response - Page 5
2025-05-27 08:05:14,448 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:05:14,963 - INFO - Request Parameters - Page 6:
2025-05-27 08:05:14,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:14,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:15,698 - INFO - API请求耗时: 734ms
2025-05-27 08:05:15,698 - INFO - Response - Page 6
2025-05-27 08:05:15,698 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:05:16,213 - INFO - Request Parameters - Page 7:
2025-05-27 08:05:16,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:16,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:16,948 - INFO - API请求耗时: 734ms
2025-05-27 08:05:16,963 - INFO - Response - Page 7
2025-05-27 08:05:16,963 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:05:17,479 - INFO - Request Parameters - Page 8:
2025-05-27 08:05:17,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:17,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:18,135 - INFO - API请求耗时: 656ms
2025-05-27 08:05:18,135 - INFO - Response - Page 8
2025-05-27 08:05:18,135 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:05:18,651 - INFO - Request Parameters - Page 9:
2025-05-27 08:05:18,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:18,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:19,291 - INFO - API请求耗时: 641ms
2025-05-27 08:05:19,291 - INFO - Response - Page 9
2025-05-27 08:05:19,291 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:05:19,791 - INFO - Request Parameters - Page 10:
2025-05-27 08:05:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:20,463 - INFO - API请求耗时: 672ms
2025-05-27 08:05:20,463 - INFO - Response - Page 10
2025-05-27 08:05:20,463 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:05:20,963 - INFO - Request Parameters - Page 11:
2025-05-27 08:05:20,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:20,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:21,666 - INFO - API请求耗时: 703ms
2025-05-27 08:05:21,666 - INFO - Response - Page 11
2025-05-27 08:05:21,666 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:05:22,166 - INFO - Request Parameters - Page 12:
2025-05-27 08:05:22,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:22,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:22,869 - INFO - API请求耗时: 703ms
2025-05-27 08:05:22,869 - INFO - Response - Page 12
2025-05-27 08:05:22,869 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:05:23,369 - INFO - Request Parameters - Page 13:
2025-05-27 08:05:23,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:23,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:24,057 - INFO - API请求耗时: 687ms
2025-05-27 08:05:24,057 - INFO - Response - Page 13
2025-05-27 08:05:24,057 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:05:24,572 - INFO - Request Parameters - Page 14:
2025-05-27 08:05:24,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:24,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:25,385 - INFO - API请求耗时: 813ms
2025-05-27 08:05:25,385 - INFO - Response - Page 14
2025-05-27 08:05:25,385 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:05:25,885 - INFO - Request Parameters - Page 15:
2025-05-27 08:05:25,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:25,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:26,588 - INFO - API请求耗时: 703ms
2025-05-27 08:05:26,588 - INFO - Response - Page 15
2025-05-27 08:05:26,588 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:05:27,088 - INFO - Request Parameters - Page 16:
2025-05-27 08:05:27,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:27,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:27,744 - INFO - API请求耗时: 656ms
2025-05-27 08:05:27,744 - INFO - Response - Page 16
2025-05-27 08:05:27,744 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:05:28,260 - INFO - Request Parameters - Page 17:
2025-05-27 08:05:28,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:28,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:28,916 - INFO - API请求耗时: 656ms
2025-05-27 08:05:28,932 - INFO - Response - Page 17
2025-05-27 08:05:28,932 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:05:29,447 - INFO - Request Parameters - Page 18:
2025-05-27 08:05:29,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:29,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:30,057 - INFO - API请求耗时: 609ms
2025-05-27 08:05:30,057 - INFO - Response - Page 18
2025-05-27 08:05:30,057 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:05:30,572 - INFO - Request Parameters - Page 19:
2025-05-27 08:05:30,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:30,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:31,244 - INFO - API请求耗时: 672ms
2025-05-27 08:05:31,244 - INFO - Response - Page 19
2025-05-27 08:05:31,244 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:05:31,760 - INFO - Request Parameters - Page 20:
2025-05-27 08:05:31,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:31,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:32,463 - INFO - API请求耗时: 703ms
2025-05-27 08:05:32,463 - INFO - Response - Page 20
2025-05-27 08:05:32,463 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:05:32,963 - INFO - Request Parameters - Page 21:
2025-05-27 08:05:32,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:32,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:33,776 - INFO - API请求耗时: 813ms
2025-05-27 08:05:33,776 - INFO - Response - Page 21
2025-05-27 08:05:33,776 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:05:34,291 - INFO - Request Parameters - Page 22:
2025-05-27 08:05:34,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:34,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:35,072 - INFO - API请求耗时: 781ms
2025-05-27 08:05:35,072 - INFO - Response - Page 22
2025-05-27 08:05:35,072 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:05:35,588 - INFO - Request Parameters - Page 23:
2025-05-27 08:05:35,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:35,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:36,322 - INFO - API请求耗时: 734ms
2025-05-27 08:05:36,322 - INFO - Response - Page 23
2025-05-27 08:05:36,322 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:05:36,822 - INFO - Request Parameters - Page 24:
2025-05-27 08:05:36,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:36,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:37,510 - INFO - API请求耗时: 687ms
2025-05-27 08:05:37,510 - INFO - Response - Page 24
2025-05-27 08:05:37,510 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:05:38,010 - INFO - Request Parameters - Page 25:
2025-05-27 08:05:38,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:38,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:38,713 - INFO - API请求耗时: 703ms
2025-05-27 08:05:38,713 - INFO - Response - Page 25
2025-05-27 08:05:38,713 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:05:39,229 - INFO - Request Parameters - Page 26:
2025-05-27 08:05:39,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:39,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:39,932 - INFO - API请求耗时: 703ms
2025-05-27 08:05:39,932 - INFO - Response - Page 26
2025-05-27 08:05:39,932 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:05:40,447 - INFO - Request Parameters - Page 27:
2025-05-27 08:05:40,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:40,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:41,151 - INFO - API请求耗时: 703ms
2025-05-27 08:05:41,151 - INFO - Response - Page 27
2025-05-27 08:05:41,151 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:05:41,666 - INFO - Request Parameters - Page 28:
2025-05-27 08:05:41,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:41,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:42,369 - INFO - API请求耗时: 703ms
2025-05-27 08:05:42,369 - INFO - Response - Page 28
2025-05-27 08:05:42,369 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:05:42,869 - INFO - Request Parameters - Page 29:
2025-05-27 08:05:42,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:42,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:43,604 - INFO - API请求耗时: 734ms
2025-05-27 08:05:43,604 - INFO - Response - Page 29
2025-05-27 08:05:43,604 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:05:44,119 - INFO - Request Parameters - Page 30:
2025-05-27 08:05:44,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:44,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:44,854 - INFO - API请求耗时: 734ms
2025-05-27 08:05:44,854 - INFO - Response - Page 30
2025-05-27 08:05:44,854 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:05:45,369 - INFO - Request Parameters - Page 31:
2025-05-27 08:05:45,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:45,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:46,072 - INFO - API请求耗时: 703ms
2025-05-27 08:05:46,072 - INFO - Response - Page 31
2025-05-27 08:05:46,072 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:05:46,588 - INFO - Request Parameters - Page 32:
2025-05-27 08:05:46,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:46,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:47,276 - INFO - API请求耗时: 688ms
2025-05-27 08:05:47,276 - INFO - Response - Page 32
2025-05-27 08:05:47,276 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:05:47,791 - INFO - Request Parameters - Page 33:
2025-05-27 08:05:47,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:47,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:48,479 - INFO - API请求耗时: 688ms
2025-05-27 08:05:48,479 - INFO - Response - Page 33
2025-05-27 08:05:48,479 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:05:48,994 - INFO - Request Parameters - Page 34:
2025-05-27 08:05:48,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:48,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:49,697 - INFO - API请求耗时: 703ms
2025-05-27 08:05:49,697 - INFO - Response - Page 34
2025-05-27 08:05:49,697 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:05:50,213 - INFO - Request Parameters - Page 35:
2025-05-27 08:05:50,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:50,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:50,885 - INFO - API请求耗时: 672ms
2025-05-27 08:05:50,885 - INFO - Response - Page 35
2025-05-27 08:05:50,885 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:05:51,400 - INFO - Request Parameters - Page 36:
2025-05-27 08:05:51,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:51,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800090, 1746547200090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:51,979 - INFO - API请求耗时: 578ms
2025-05-27 08:05:51,979 - INFO - Response - Page 36
2025-05-27 08:05:51,979 - INFO - 第 36 页获取到 27 条记录
2025-05-27 08:05:51,979 - INFO - 查询完成，共获取到 3527 条记录
2025-05-27 08:05:51,979 - INFO - 分段 6 查询成功，获取到 3527 条记录
2025-05-27 08:05:52,994 - INFO - 查询分段 7: 2025-05-08 至 2025-05-14
2025-05-27 08:05:52,994 - INFO - 查询日期范围: 2025-05-08 至 2025-05-14，使用分页查询，每页 100 条记录
2025-05-27 08:05:52,994 - INFO - Request Parameters - Page 1:
2025-05-27 08:05:52,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:52,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:53,744 - INFO - API请求耗时: 750ms
2025-05-27 08:05:53,744 - INFO - Response - Page 1
2025-05-27 08:05:53,744 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:05:54,244 - INFO - Request Parameters - Page 2:
2025-05-27 08:05:54,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:54,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:55,010 - INFO - API请求耗时: 766ms
2025-05-27 08:05:55,010 - INFO - Response - Page 2
2025-05-27 08:05:55,010 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:05:55,525 - INFO - Request Parameters - Page 3:
2025-05-27 08:05:55,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:55,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:56,260 - INFO - API请求耗时: 734ms
2025-05-27 08:05:56,260 - INFO - Response - Page 3
2025-05-27 08:05:56,260 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:05:56,775 - INFO - Request Parameters - Page 4:
2025-05-27 08:05:56,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:56,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:57,447 - INFO - API请求耗时: 672ms
2025-05-27 08:05:57,447 - INFO - Response - Page 4
2025-05-27 08:05:57,447 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:05:57,947 - INFO - Request Parameters - Page 5:
2025-05-27 08:05:57,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:57,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:58,635 - INFO - API请求耗时: 687ms
2025-05-27 08:05:58,635 - INFO - Response - Page 5
2025-05-27 08:05:58,635 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:05:59,150 - INFO - Request Parameters - Page 6:
2025-05-27 08:05:59,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:05:59,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:05:59,869 - INFO - API请求耗时: 719ms
2025-05-27 08:05:59,869 - INFO - Response - Page 6
2025-05-27 08:05:59,869 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:06:00,385 - INFO - Request Parameters - Page 7:
2025-05-27 08:06:00,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:00,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:01,088 - INFO - API请求耗时: 703ms
2025-05-27 08:06:01,088 - INFO - Response - Page 7
2025-05-27 08:06:01,088 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:06:01,588 - INFO - Request Parameters - Page 8:
2025-05-27 08:06:01,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:01,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:02,244 - INFO - API请求耗时: 656ms
2025-05-27 08:06:02,244 - INFO - Response - Page 8
2025-05-27 08:06:02,244 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:06:02,760 - INFO - Request Parameters - Page 9:
2025-05-27 08:06:02,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:02,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:03,479 - INFO - API请求耗时: 719ms
2025-05-27 08:06:03,479 - INFO - Response - Page 9
2025-05-27 08:06:03,479 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:06:03,994 - INFO - Request Parameters - Page 10:
2025-05-27 08:06:03,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:03,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:04,666 - INFO - API请求耗时: 672ms
2025-05-27 08:06:04,666 - INFO - Response - Page 10
2025-05-27 08:06:04,666 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:06:05,166 - INFO - Request Parameters - Page 11:
2025-05-27 08:06:05,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:05,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:05,869 - INFO - API请求耗时: 703ms
2025-05-27 08:06:05,869 - INFO - Response - Page 11
2025-05-27 08:06:05,869 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:06:06,385 - INFO - Request Parameters - Page 12:
2025-05-27 08:06:06,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:06,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:07,088 - INFO - API请求耗时: 703ms
2025-05-27 08:06:07,088 - INFO - Response - Page 12
2025-05-27 08:06:07,088 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:06:07,603 - INFO - Request Parameters - Page 13:
2025-05-27 08:06:07,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:07,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:08,275 - INFO - API请求耗时: 672ms
2025-05-27 08:06:08,275 - INFO - Response - Page 13
2025-05-27 08:06:08,275 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:06:08,791 - INFO - Request Parameters - Page 14:
2025-05-27 08:06:08,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:08,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:09,416 - INFO - API请求耗时: 625ms
2025-05-27 08:06:09,416 - INFO - Response - Page 14
2025-05-27 08:06:09,416 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:06:09,932 - INFO - Request Parameters - Page 15:
2025-05-27 08:06:09,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:09,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:10,619 - INFO - API请求耗时: 687ms
2025-05-27 08:06:10,619 - INFO - Response - Page 15
2025-05-27 08:06:10,619 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:06:11,119 - INFO - Request Parameters - Page 16:
2025-05-27 08:06:11,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:11,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:11,822 - INFO - API请求耗时: 703ms
2025-05-27 08:06:11,822 - INFO - Response - Page 16
2025-05-27 08:06:11,822 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:06:12,322 - INFO - Request Parameters - Page 17:
2025-05-27 08:06:12,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:12,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:12,947 - INFO - API请求耗时: 625ms
2025-05-27 08:06:12,947 - INFO - Response - Page 17
2025-05-27 08:06:12,963 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:06:13,478 - INFO - Request Parameters - Page 18:
2025-05-27 08:06:13,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:13,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:14,182 - INFO - API请求耗时: 703ms
2025-05-27 08:06:14,182 - INFO - Response - Page 18
2025-05-27 08:06:14,182 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:06:14,697 - INFO - Request Parameters - Page 19:
2025-05-27 08:06:14,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:14,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:15,369 - INFO - API请求耗时: 672ms
2025-05-27 08:06:15,369 - INFO - Response - Page 19
2025-05-27 08:06:15,369 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:06:15,869 - INFO - Request Parameters - Page 20:
2025-05-27 08:06:15,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:15,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:16,541 - INFO - API请求耗时: 672ms
2025-05-27 08:06:16,541 - INFO - Response - Page 20
2025-05-27 08:06:16,557 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:06:17,072 - INFO - Request Parameters - Page 21:
2025-05-27 08:06:17,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:17,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:17,775 - INFO - API请求耗时: 703ms
2025-05-27 08:06:17,775 - INFO - Response - Page 21
2025-05-27 08:06:17,775 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:06:18,291 - INFO - Request Parameters - Page 22:
2025-05-27 08:06:18,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:18,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:19,025 - INFO - API请求耗时: 734ms
2025-05-27 08:06:19,025 - INFO - Response - Page 22
2025-05-27 08:06:19,025 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:06:19,525 - INFO - Request Parameters - Page 23:
2025-05-27 08:06:19,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:19,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:20,353 - INFO - API请求耗时: 828ms
2025-05-27 08:06:20,353 - INFO - Response - Page 23
2025-05-27 08:06:20,353 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:06:20,869 - INFO - Request Parameters - Page 24:
2025-05-27 08:06:20,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:20,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:21,619 - INFO - API请求耗时: 750ms
2025-05-27 08:06:21,619 - INFO - Response - Page 24
2025-05-27 08:06:21,619 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:06:22,135 - INFO - Request Parameters - Page 25:
2025-05-27 08:06:22,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:22,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:22,853 - INFO - API请求耗时: 719ms
2025-05-27 08:06:22,853 - INFO - Response - Page 25
2025-05-27 08:06:22,853 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:06:23,385 - INFO - Request Parameters - Page 26:
2025-05-27 08:06:23,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:23,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:24,166 - INFO - API请求耗时: 781ms
2025-05-27 08:06:24,181 - INFO - Response - Page 26
2025-05-27 08:06:24,181 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:06:24,697 - INFO - Request Parameters - Page 27:
2025-05-27 08:06:24,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:24,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:25,369 - INFO - API请求耗时: 672ms
2025-05-27 08:06:25,369 - INFO - Response - Page 27
2025-05-27 08:06:25,369 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:06:25,869 - INFO - Request Parameters - Page 28:
2025-05-27 08:06:25,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:25,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:26,572 - INFO - API请求耗时: 703ms
2025-05-27 08:06:26,572 - INFO - Response - Page 28
2025-05-27 08:06:26,572 - INFO - 第 28 页获取到 100 条记录
2025-05-27 08:06:27,072 - INFO - Request Parameters - Page 29:
2025-05-27 08:06:27,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:27,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:27,744 - INFO - API请求耗时: 672ms
2025-05-27 08:06:27,744 - INFO - Response - Page 29
2025-05-27 08:06:27,744 - INFO - 第 29 页获取到 100 条记录
2025-05-27 08:06:28,244 - INFO - Request Parameters - Page 30:
2025-05-27 08:06:28,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:28,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:28,963 - INFO - API请求耗时: 719ms
2025-05-27 08:06:28,963 - INFO - Response - Page 30
2025-05-27 08:06:28,963 - INFO - 第 30 页获取到 100 条记录
2025-05-27 08:06:29,478 - INFO - Request Parameters - Page 31:
2025-05-27 08:06:29,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:29,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:30,166 - INFO - API请求耗时: 687ms
2025-05-27 08:06:30,166 - INFO - Response - Page 31
2025-05-27 08:06:30,166 - INFO - 第 31 页获取到 100 条记录
2025-05-27 08:06:30,666 - INFO - Request Parameters - Page 32:
2025-05-27 08:06:30,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:30,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:31,385 - INFO - API请求耗时: 719ms
2025-05-27 08:06:31,385 - INFO - Response - Page 32
2025-05-27 08:06:31,385 - INFO - 第 32 页获取到 100 条记录
2025-05-27 08:06:31,885 - INFO - Request Parameters - Page 33:
2025-05-27 08:06:31,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:31,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:32,572 - INFO - API请求耗时: 687ms
2025-05-27 08:06:32,572 - INFO - Response - Page 33
2025-05-27 08:06:32,572 - INFO - 第 33 页获取到 100 条记录
2025-05-27 08:06:33,072 - INFO - Request Parameters - Page 34:
2025-05-27 08:06:33,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:33,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:33,744 - INFO - API请求耗时: 672ms
2025-05-27 08:06:33,744 - INFO - Response - Page 34
2025-05-27 08:06:33,760 - INFO - 第 34 页获取到 100 条记录
2025-05-27 08:06:34,275 - INFO - Request Parameters - Page 35:
2025-05-27 08:06:34,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:34,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:35,119 - INFO - API请求耗时: 844ms
2025-05-27 08:06:35,119 - INFO - Response - Page 35
2025-05-27 08:06:35,119 - INFO - 第 35 页获取到 100 条记录
2025-05-27 08:06:35,619 - INFO - Request Parameters - Page 36:
2025-05-27 08:06:35,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:35,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600090, 1747152000090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:36,260 - INFO - API请求耗时: 641ms
2025-05-27 08:06:36,260 - INFO - Response - Page 36
2025-05-27 08:06:36,260 - INFO - 第 36 页获取到 94 条记录
2025-05-27 08:06:36,260 - INFO - 查询完成，共获取到 3594 条记录
2025-05-27 08:06:36,260 - INFO - 分段 7 查询成功，获取到 3594 条记录
2025-05-27 08:06:37,275 - INFO - 查询分段 8: 2025-05-15 至 2025-05-21
2025-05-27 08:06:37,275 - INFO - 查询日期范围: 2025-05-15 至 2025-05-21，使用分页查询，每页 100 条记录
2025-05-27 08:06:37,275 - INFO - Request Parameters - Page 1:
2025-05-27 08:06:37,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:37,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:38,010 - INFO - API请求耗时: 734ms
2025-05-27 08:06:38,010 - INFO - Response - Page 1
2025-05-27 08:06:38,010 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:06:38,510 - INFO - Request Parameters - Page 2:
2025-05-27 08:06:38,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:38,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:39,213 - INFO - API请求耗时: 703ms
2025-05-27 08:06:39,213 - INFO - Response - Page 2
2025-05-27 08:06:39,228 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:06:39,728 - INFO - Request Parameters - Page 3:
2025-05-27 08:06:39,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:39,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:40,400 - INFO - API请求耗时: 672ms
2025-05-27 08:06:40,400 - INFO - Response - Page 3
2025-05-27 08:06:40,400 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:06:40,916 - INFO - Request Parameters - Page 4:
2025-05-27 08:06:40,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:40,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:41,635 - INFO - API请求耗时: 719ms
2025-05-27 08:06:41,635 - INFO - Response - Page 4
2025-05-27 08:06:41,635 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:06:42,150 - INFO - Request Parameters - Page 5:
2025-05-27 08:06:42,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:42,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:42,791 - INFO - API请求耗时: 641ms
2025-05-27 08:06:42,791 - INFO - Response - Page 5
2025-05-27 08:06:42,791 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:06:43,306 - INFO - Request Parameters - Page 6:
2025-05-27 08:06:43,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:43,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:44,056 - INFO - API请求耗时: 750ms
2025-05-27 08:06:44,056 - INFO - Response - Page 6
2025-05-27 08:06:44,056 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:06:44,556 - INFO - Request Parameters - Page 7:
2025-05-27 08:06:44,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:44,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:45,166 - INFO - API请求耗时: 609ms
2025-05-27 08:06:45,166 - INFO - Response - Page 7
2025-05-27 08:06:45,166 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:06:45,681 - INFO - Request Parameters - Page 8:
2025-05-27 08:06:45,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:45,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:46,400 - INFO - API请求耗时: 719ms
2025-05-27 08:06:46,400 - INFO - Response - Page 8
2025-05-27 08:06:46,400 - INFO - 第 8 页获取到 100 条记录
2025-05-27 08:06:46,900 - INFO - Request Parameters - Page 9:
2025-05-27 08:06:46,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:46,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:47,588 - INFO - API请求耗时: 687ms
2025-05-27 08:06:47,588 - INFO - Response - Page 9
2025-05-27 08:06:47,588 - INFO - 第 9 页获取到 100 条记录
2025-05-27 08:06:48,103 - INFO - Request Parameters - Page 10:
2025-05-27 08:06:48,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:48,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:48,806 - INFO - API请求耗时: 703ms
2025-05-27 08:06:48,806 - INFO - Response - Page 10
2025-05-27 08:06:48,806 - INFO - 第 10 页获取到 100 条记录
2025-05-27 08:06:49,322 - INFO - Request Parameters - Page 11:
2025-05-27 08:06:49,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:49,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:49,994 - INFO - API请求耗时: 672ms
2025-05-27 08:06:50,009 - INFO - Response - Page 11
2025-05-27 08:06:50,009 - INFO - 第 11 页获取到 100 条记录
2025-05-27 08:06:50,525 - INFO - Request Parameters - Page 12:
2025-05-27 08:06:50,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:50,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:51,259 - INFO - API请求耗时: 734ms
2025-05-27 08:06:51,259 - INFO - Response - Page 12
2025-05-27 08:06:51,259 - INFO - 第 12 页获取到 100 条记录
2025-05-27 08:06:51,759 - INFO - Request Parameters - Page 13:
2025-05-27 08:06:51,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:51,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:52,416 - INFO - API请求耗时: 656ms
2025-05-27 08:06:52,416 - INFO - Response - Page 13
2025-05-27 08:06:52,416 - INFO - 第 13 页获取到 100 条记录
2025-05-27 08:06:52,916 - INFO - Request Parameters - Page 14:
2025-05-27 08:06:52,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:52,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:53,634 - INFO - API请求耗时: 719ms
2025-05-27 08:06:53,634 - INFO - Response - Page 14
2025-05-27 08:06:53,634 - INFO - 第 14 页获取到 100 条记录
2025-05-27 08:06:54,134 - INFO - Request Parameters - Page 15:
2025-05-27 08:06:54,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:54,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:54,963 - INFO - API请求耗时: 828ms
2025-05-27 08:06:54,963 - INFO - Response - Page 15
2025-05-27 08:06:54,963 - INFO - 第 15 页获取到 100 条记录
2025-05-27 08:06:55,478 - INFO - Request Parameters - Page 16:
2025-05-27 08:06:55,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:55,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:56,150 - INFO - API请求耗时: 672ms
2025-05-27 08:06:56,150 - INFO - Response - Page 16
2025-05-27 08:06:56,150 - INFO - 第 16 页获取到 100 条记录
2025-05-27 08:06:56,650 - INFO - Request Parameters - Page 17:
2025-05-27 08:06:56,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:56,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:57,369 - INFO - API请求耗时: 719ms
2025-05-27 08:06:57,369 - INFO - Response - Page 17
2025-05-27 08:06:57,369 - INFO - 第 17 页获取到 100 条记录
2025-05-27 08:06:57,869 - INFO - Request Parameters - Page 18:
2025-05-27 08:06:57,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:57,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:58,525 - INFO - API请求耗时: 656ms
2025-05-27 08:06:58,525 - INFO - Response - Page 18
2025-05-27 08:06:58,525 - INFO - 第 18 页获取到 100 条记录
2025-05-27 08:06:59,025 - INFO - Request Parameters - Page 19:
2025-05-27 08:06:59,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:06:59,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:06:59,775 - INFO - API请求耗时: 750ms
2025-05-27 08:06:59,775 - INFO - Response - Page 19
2025-05-27 08:06:59,775 - INFO - 第 19 页获取到 100 条记录
2025-05-27 08:07:00,291 - INFO - Request Parameters - Page 20:
2025-05-27 08:07:00,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:00,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:00,994 - INFO - API请求耗时: 703ms
2025-05-27 08:07:00,994 - INFO - Response - Page 20
2025-05-27 08:07:00,994 - INFO - 第 20 页获取到 100 条记录
2025-05-27 08:07:01,494 - INFO - Request Parameters - Page 21:
2025-05-27 08:07:01,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:01,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:02,213 - INFO - API请求耗时: 719ms
2025-05-27 08:07:02,213 - INFO - Response - Page 21
2025-05-27 08:07:02,213 - INFO - 第 21 页获取到 100 条记录
2025-05-27 08:07:02,713 - INFO - Request Parameters - Page 22:
2025-05-27 08:07:02,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:02,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:03,400 - INFO - API请求耗时: 687ms
2025-05-27 08:07:03,400 - INFO - Response - Page 22
2025-05-27 08:07:03,400 - INFO - 第 22 页获取到 100 条记录
2025-05-27 08:07:03,916 - INFO - Request Parameters - Page 23:
2025-05-27 08:07:03,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:03,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:04,525 - INFO - API请求耗时: 609ms
2025-05-27 08:07:04,525 - INFO - Response - Page 23
2025-05-27 08:07:04,525 - INFO - 第 23 页获取到 100 条记录
2025-05-27 08:07:05,025 - INFO - Request Parameters - Page 24:
2025-05-27 08:07:05,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:05,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:05,806 - INFO - API请求耗时: 781ms
2025-05-27 08:07:05,806 - INFO - Response - Page 24
2025-05-27 08:07:05,806 - INFO - 第 24 页获取到 100 条记录
2025-05-27 08:07:06,322 - INFO - Request Parameters - Page 25:
2025-05-27 08:07:06,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:06,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:06,978 - INFO - API请求耗时: 656ms
2025-05-27 08:07:06,978 - INFO - Response - Page 25
2025-05-27 08:07:06,978 - INFO - 第 25 页获取到 100 条记录
2025-05-27 08:07:07,494 - INFO - Request Parameters - Page 26:
2025-05-27 08:07:07,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:07,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:08,181 - INFO - API请求耗时: 688ms
2025-05-27 08:07:08,181 - INFO - Response - Page 26
2025-05-27 08:07:08,181 - INFO - 第 26 页获取到 100 条记录
2025-05-27 08:07:08,681 - INFO - Request Parameters - Page 27:
2025-05-27 08:07:08,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:08,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:09,384 - INFO - API请求耗时: 703ms
2025-05-27 08:07:09,384 - INFO - Response - Page 27
2025-05-27 08:07:09,384 - INFO - 第 27 页获取到 100 条记录
2025-05-27 08:07:09,884 - INFO - Request Parameters - Page 28:
2025-05-27 08:07:09,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:09,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400090, 1747756800090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:10,306 - INFO - API请求耗时: 422ms
2025-05-27 08:07:10,306 - INFO - Response - Page 28
2025-05-27 08:07:10,306 - INFO - 第 28 页获取到 9 条记录
2025-05-27 08:07:10,306 - INFO - 查询完成，共获取到 2709 条记录
2025-05-27 08:07:10,306 - INFO - 分段 8 查询成功，获取到 2709 条记录
2025-05-27 08:07:11,322 - INFO - 查询分段 9: 2025-05-22 至 2025-05-26
2025-05-27 08:07:11,322 - INFO - 查询日期范围: 2025-05-22 至 2025-05-26，使用分页查询，每页 100 条记录
2025-05-27 08:07:11,322 - INFO - Request Parameters - Page 1:
2025-05-27 08:07:11,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:11,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200090, 1748275199090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:11,962 - INFO - API请求耗时: 641ms
2025-05-27 08:07:11,962 - INFO - Response - Page 1
2025-05-27 08:07:11,962 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:07:12,478 - INFO - Request Parameters - Page 2:
2025-05-27 08:07:12,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:12,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200090, 1748275199090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:13,212 - INFO - API请求耗时: 734ms
2025-05-27 08:07:13,212 - INFO - Response - Page 2
2025-05-27 08:07:13,212 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:07:13,728 - INFO - Request Parameters - Page 3:
2025-05-27 08:07:13,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:13,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200090, 1748275199090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:14,416 - INFO - API请求耗时: 688ms
2025-05-27 08:07:14,416 - INFO - Response - Page 3
2025-05-27 08:07:14,416 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:07:14,916 - INFO - Request Parameters - Page 4:
2025-05-27 08:07:14,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:14,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200090, 1748275199090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:15,619 - INFO - API请求耗时: 703ms
2025-05-27 08:07:15,619 - INFO - Response - Page 4
2025-05-27 08:07:15,634 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:07:16,150 - INFO - Request Parameters - Page 5:
2025-05-27 08:07:16,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:16,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200090, 1748275199090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:16,853 - INFO - API请求耗时: 687ms
2025-05-27 08:07:16,853 - INFO - Response - Page 5
2025-05-27 08:07:16,853 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:07:17,369 - INFO - Request Parameters - Page 6:
2025-05-27 08:07:17,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:07:17,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200090, 1748275199090], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:07:18,009 - INFO - API请求耗时: 641ms
2025-05-27 08:07:18,009 - INFO - Response - Page 6
2025-05-27 08:07:18,009 - INFO - 第 6 页获取到 86 条记录
2025-05-27 08:07:18,009 - INFO - 查询完成，共获取到 586 条记录
2025-05-27 08:07:18,009 - INFO - 分段 9 查询成功，获取到 586 条记录
2025-05-27 08:07:19,025 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 28459 条记录，失败 0 次
2025-05-27 08:07:19,025 - INFO - 成功获取宜搭日销售表单数据，共 28459 条记录
2025-05-27 08:07:19,025 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-27 08:07:19,025 - INFO - 开始对比和同步日销售数据...
2025-05-27 08:07:19,869 - INFO - 成功创建宜搭日销售数据索引，共 10837 条记录
2025-05-27 08:07:19,869 - INFO - 开始处理数衍数据，共 12999 条记录
2025-05-27 08:07:20,447 - INFO - 更新表单数据成功: FINST-1MD668B12VFVXKDR9KMIMCRMJBIS3N0J79PAMOA
2025-05-27 08:07:20,447 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_20250502, 变更字段: [{'field': 'amount', 'old_value': 28205.45, 'new_value': 29315.65}, {'field': 'count', 'old_value': 121, 'new_value': 122}, {'field': 'instoreAmount', 'old_value': 28205.45, 'new_value': 29315.65}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 122}]
2025-05-27 08:07:20,931 - INFO - 更新表单数据成功: FINST-XL866HB1NAGVD1TYEI9H79REHI3F3TWF99PAMT5
2025-05-27 08:07:20,931 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 21477.64, 'new_value': 21507.64}, {'field': 'amount', 'old_value': 21477.64, 'new_value': 21507.64}, {'field': 'instoreAmount', 'old_value': 20135.0, 'new_value': 20165.0}]
2025-05-27 08:07:21,400 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2J254MYAM9B
2025-05-27 08:07:21,400 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_20250519, 变更字段: [{'field': 'amount', 'old_value': 10942.2, 'new_value': 11173.2}, {'field': 'count', 'old_value': 119, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 5393.4, 'new_value': 5624.4}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 56}]
2025-05-27 08:07:21,869 - INFO - 更新表单数据成功: FINST-LLF66J71Q4NVCL9C6OFDU6A1JF0Z2KQYQUYAMU2
2025-05-27 08:07:21,869 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250520, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 56069.49}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 56069.49}]
2025-05-27 08:07:22,259 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK23QI7RVAMPB
2025-05-27 08:07:22,259 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 38630.68}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 38630.68}]
2025-05-27 08:07:22,744 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BMOB
2025-05-27 08:07:22,744 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4005.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4005.8}]
2025-05-27 08:07:23,181 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BMEC
2025-05-27 08:07:23,181 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 89.0, 'new_value': 427.0}, {'field': 'amount', 'old_value': 89.0, 'new_value': 427.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 89.0, 'new_value': 427.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-05-27 08:07:23,603 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BMHC
2025-05-27 08:07:23,603 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250525, 变更字段: [{'field': 'amount', 'old_value': 5923.17, 'new_value': 6734.63}, {'field': 'count', 'old_value': 71, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 2360.17, 'new_value': 3171.63}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 38}]
2025-05-27 08:07:24,087 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BMZC
2025-05-27 08:07:24,087 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_20250525, 变更字段: [{'field': 'amount', 'old_value': 3393.4700000000003, 'new_value': 3471.78}, {'field': 'count', 'old_value': 87, 'new_value': 88}, {'field': 'onlineAmount', 'old_value': 151.6, 'new_value': 229.91}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-05-27 08:07:24,572 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BMFD
2025-05-27 08:07:24,572 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 2627.5, 'new_value': 2600.5}, {'field': 'dailyBillAmount', 'old_value': 2627.5, 'new_value': 2600.5}]
2025-05-27 08:07:25,072 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BMYD
2025-05-27 08:07:25,072 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_20250525, 变更字段: [{'field': 'amount', 'old_value': 1169.8, 'new_value': 1176.8}, {'field': 'count', 'old_value': 62, 'new_value': 63}, {'field': 'onlineAmount', 'old_value': 572.8, 'new_value': 579.8}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 32}]
2025-05-27 08:07:25,509 - INFO - 更新表单数据成功: FINST-KLF66WC142OVFZ9SEEZ4141TTYL53D1VWB4BM5E
2025-05-27 08:07:25,509 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_20250525, 变更字段: [{'field': 'amount', 'old_value': 1333.23, 'new_value': 1370.91}, {'field': 'count', 'old_value': 38, 'new_value': 40}, {'field': 'onlineAmount', 'old_value': 1081.83, 'new_value': 1119.51}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 35}]
2025-05-27 08:07:26,009 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3NPXWB4BMI
2025-05-27 08:07:26,009 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_20250525, 变更字段: [{'field': 'amount', 'old_value': 1899.0, 'new_value': 2546.0}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1899.0, 'new_value': 2546.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-05-27 08:07:26,494 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3NPXWB4BM71
2025-05-27 08:07:26,494 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7673.48}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7673.48}]
2025-05-27 08:07:26,947 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3NPXWB4BMC1
2025-05-27 08:07:26,947 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 22246.0, 'new_value': 23893.0}, {'field': 'amount', 'old_value': 22246.0, 'new_value': 23893.0}, {'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 22246.0, 'new_value': 23893.0}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 74}]
2025-05-27 08:07:27,384 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3NPXWB4BMI1
2025-05-27 08:07:27,384 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250525, 变更字段: [{'field': 'amount', 'old_value': 3429.0, 'new_value': 3634.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 3429.0, 'new_value': 3634.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-05-27 08:07:27,900 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3OPXWB4BMD2
2025-05-27 08:07:27,900 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1651.46}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1651.46}]
2025-05-27 08:07:28,337 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3OPXWB4BMJ2
2025-05-27 08:07:28,337 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 2731.03, 'new_value': 2714.66}, {'field': 'amount', 'old_value': 2731.0299999999997, 'new_value': 2714.66}, {'field': 'count', 'old_value': 122, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 2768.54, 'new_value': 2758.71}, {'field': 'instoreCount', 'old_value': 122, 'new_value': 121}]
2025-05-27 08:07:28,790 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3OPXWB4BM03
2025-05-27 08:07:28,790 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 9597.99, 'new_value': 9712.59}, {'field': 'amount', 'old_value': 9597.99, 'new_value': 9712.59}, {'field': 'count', 'old_value': 196, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 7658.66, 'new_value': 7773.26}, {'field': 'instoreCount', 'old_value': 160, 'new_value': 162}]
2025-05-27 08:07:29,306 - INFO - 更新表单数据成功: FINST-YPE66RB1WEOVLBAO8KI89ATQQE0327HWGW2BMVE
2025-05-27 08:07:29,322 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250524, 变更字段: [{'field': 'recommendAmount', 'old_value': 9262.24, 'new_value': 9872.44}, {'field': 'amount', 'old_value': 9262.24, 'new_value': 9872.44}, {'field': 'count', 'old_value': 180, 'new_value': 186}, {'field': 'instoreAmount', 'old_value': 7998.49, 'new_value': 8608.69}, {'field': 'instoreCount', 'old_value': 144, 'new_value': 150}]
2025-05-27 08:07:29,790 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3OPXWB4BM33
2025-05-27 08:07:29,790 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250525, 变更字段: [{'field': 'amount', 'old_value': 5371.349999999999, 'new_value': 5460.25}, {'field': 'count', 'old_value': 264, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 2594.75, 'new_value': 2683.65}, {'field': 'onlineCount', 'old_value': 121, 'new_value': 124}]
2025-05-27 08:07:30,244 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A28B0XB4BMGY
2025-05-27 08:07:30,244 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 1447.85, 'new_value': 1443.87}, {'field': 'amount', 'old_value': 1447.8500000000001, 'new_value': 1443.8700000000001}, {'field': 'onlineAmount', 'old_value': 855.19, 'new_value': 851.21}]
2025-05-27 08:07:30,681 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A28B0XB4BMKY
2025-05-27 08:07:30,681 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_20250525, 变更字段: [{'field': 'amount', 'old_value': 10659.3, 'new_value': 10749.2}, {'field': 'count', 'old_value': 212, 'new_value': 213}, {'field': 'instoreAmount', 'old_value': 10733.3, 'new_value': 10823.2}, {'field': 'instoreCount', 'old_value': 212, 'new_value': 213}]
2025-05-27 08:07:31,087 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A28B0XB4BMOY
2025-05-27 08:07:31,087 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 5148.34, 'new_value': 5246.04}, {'field': 'amount', 'old_value': 5148.339999999999, 'new_value': 5246.04}, {'field': 'count', 'old_value': 274, 'new_value': 277}, {'field': 'instoreAmount', 'old_value': 1747.39, 'new_value': 1840.09}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 76}, {'field': 'onlineAmount', 'old_value': 3668.55, 'new_value': 3673.55}, {'field': 'onlineCount', 'old_value': 200, 'new_value': 201}]
2025-05-27 08:07:31,572 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A28B0XB4BMRY
2025-05-27 08:07:31,572 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1560.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1560.0}]
2025-05-27 08:07:32,009 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A28B0XB4BMYY
2025-05-27 08:07:32,009 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15739.61}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15739.61}]
2025-05-27 08:07:32,431 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BM5Z
2025-05-27 08:07:32,431 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_20250525, 变更字段: [{'field': 'amount', 'old_value': 27128.98, 'new_value': 27426.079999999998}, {'field': 'count', 'old_value': 215, 'new_value': 217}, {'field': 'instoreAmount', 'old_value': 24666.37, 'new_value': 24963.47}, {'field': 'instoreCount', 'old_value': 137, 'new_value': 139}]
2025-05-27 08:07:32,915 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BM8Z
2025-05-27 08:07:32,915 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_20250525, 变更字段: [{'field': 'amount', 'old_value': 25017.48, 'new_value': 25702.48}, {'field': 'count', 'old_value': 259, 'new_value': 260}, {'field': 'instoreAmount', 'old_value': 18579.0, 'new_value': 19264.0}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 127}]
2025-05-27 08:07:33,369 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BMAZ
2025-05-27 08:07:33,369 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250525, 变更字段: [{'field': 'amount', 'old_value': 40007.100000000006, 'new_value': 41392.100000000006}, {'field': 'count', 'old_value': 284, 'new_value': 289}, {'field': 'instoreAmount', 'old_value': 21954.4, 'new_value': 23339.4}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 120}]
2025-05-27 08:07:33,775 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BMDZ
2025-05-27 08:07:33,775 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 40658.6, 'new_value': 42304.4}, {'field': 'amount', 'old_value': 40658.6, 'new_value': 42304.4}, {'field': 'count', 'old_value': 301, 'new_value': 310}, {'field': 'instoreAmount', 'old_value': 40658.6, 'new_value': 42304.4}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 310}]
2025-05-27 08:07:34,306 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BMJZ
2025-05-27 08:07:34,306 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250525, 变更字段: [{'field': 'amount', 'old_value': 49237.52, 'new_value': 49218.52}]
2025-05-27 08:07:34,759 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BMMZ
2025-05-27 08:07:34,759 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_20250525, 变更字段: [{'field': 'amount', 'old_value': 21979.8, 'new_value': 22496.8}, {'field': 'count', 'old_value': 127, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 21133.8, 'new_value': 21650.8}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 119}]
2025-05-27 08:07:35,150 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BMPZ
2025-05-27 08:07:35,150 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_20250525, 变更字段: [{'field': 'amount', 'old_value': 17672.61, 'new_value': 17689.91}, {'field': 'count', 'old_value': 155, 'new_value': 156}, {'field': 'onlineAmount', 'old_value': 4514.61, 'new_value': 4531.91}, {'field': 'onlineCount', 'old_value': 64, 'new_value': 65}]
2025-05-27 08:07:35,603 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BM101
2025-05-27 08:07:35,603 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3913.91}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3913.91}, {'field': 'amount', 'old_value': 719.9200000000001, 'new_value': 1670.51}, {'field': 'count', 'old_value': 80, 'new_value': 161}, {'field': 'instoreAmount', 'old_value': 767.1, 'new_value': 1733.7}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 161}]
2025-05-27 08:07:36,212 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BMS01
2025-05-27 08:07:36,212 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_20250525, 变更字段: [{'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-05-27 08:07:36,650 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093MY2XB4BM7M
2025-05-27 08:07:36,650 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 14136.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 14136.0}]
2025-05-27 08:07:37,181 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093MY2XB4BMBM
2025-05-27 08:07:37,181 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_20250525, 变更字段: [{'field': 'amount', 'old_value': 2586.71, 'new_value': 2607.11}, {'field': 'count', 'old_value': 105, 'new_value': 106}, {'field': 'onlineAmount', 'old_value': 1560.1, 'new_value': 1580.5}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 66}]
2025-05-27 08:07:37,509 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093MY2XB4BMHM
2025-05-27 08:07:37,509 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250525, 变更字段: [{'field': 'amount', 'old_value': 4269.61, 'new_value': 4295.62}, {'field': 'count', 'old_value': 304, 'new_value': 312}, {'field': 'onlineAmount', 'old_value': 3926.0, 'new_value': 3952.01}, {'field': 'onlineCount', 'old_value': 268, 'new_value': 276}]
2025-05-27 08:07:37,962 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093MY2XB4BMPM
2025-05-27 08:07:37,962 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_20250525, 变更字段: [{'field': 'amount', 'old_value': 3446.49, 'new_value': 3477.49}, {'field': 'count', 'old_value': 186, 'new_value': 187}, {'field': 'onlineAmount', 'old_value': 2773.51, 'new_value': 2804.51}, {'field': 'onlineCount', 'old_value': 127, 'new_value': 128}]
2025-05-27 08:07:38,462 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093MY2XB4BMXM
2025-05-27 08:07:38,462 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250525, 变更字段: [{'field': 'count', 'old_value': 525, 'new_value': 528}, {'field': 'instoreAmount', 'old_value': 5056.44, 'new_value': 5094.59}, {'field': 'instoreCount', 'old_value': 355, 'new_value': 361}, {'field': 'onlineAmount', 'old_value': 2379.44, 'new_value': 2368.79}, {'field': 'onlineCount', 'old_value': 170, 'new_value': 167}]
2025-05-27 08:07:38,900 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093NY2XB4BM2N
2025-05-27 08:07:38,900 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 32123.1, 'new_value': 32167.1}, {'field': 'amount', 'old_value': 32123.1, 'new_value': 32167.1}, {'field': 'count', 'old_value': 638, 'new_value': 639}, {'field': 'onlineAmount', 'old_value': 9810.5, 'new_value': 9854.5}, {'field': 'onlineCount', 'old_value': 214, 'new_value': 215}]
2025-05-27 08:07:39,353 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093NY2XB4BM3O
2025-05-27 08:07:39,353 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 3427.91, 'new_value': 3417.92}, {'field': 'amount', 'old_value': 3427.91, 'new_value': 3417.9199999999996}]
2025-05-27 08:07:39,822 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093NY2XB4BMBO
2025-05-27 08:07:39,822 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250525, 变更字段: [{'field': 'amount', 'old_value': 24849.46, 'new_value': 25193.26}, {'field': 'count', 'old_value': 187, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 23245.84, 'new_value': 23589.64}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 128}]
2025-05-27 08:07:40,290 - INFO - 更新表单数据成功: FINST-XBF66071KGPVICNMCWOCX40SX2TD26K5XB4BM69
2025-05-27 08:07:40,290 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250525, 变更字段: [{'field': 'amount', 'old_value': 17813.78, 'new_value': 18897.78}, {'field': 'count', 'old_value': 100, 'new_value': 102}, {'field': 'instoreAmount', 'old_value': 15434.62, 'new_value': 16518.62}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 82}]
2025-05-27 08:07:40,775 - INFO - 更新表单数据成功: FINST-XBF66071KGPVICNMCWOCX40SX2TD26K5XB4BM79
2025-05-27 08:07:40,775 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250523, 变更字段: [{'field': 'amount', 'old_value': 8516.28, 'new_value': 9069.28}, {'field': 'count', 'old_value': 48, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 7994.1, 'new_value': 8547.1}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 40}]
2025-05-27 08:07:41,259 - INFO - 更新表单数据成功: FINST-XBF66071KGPVICNMCWOCX40SX2TD26K5XB4BM7A
2025-05-27 08:07:41,259 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_20250525, 变更字段: [{'field': 'amount', 'old_value': 680.5, 'new_value': 663.6999999999999}]
2025-05-27 08:07:41,337 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-27 08:07:41,728 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-27 08:07:44,743 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-27 08:07:45,228 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-27 08:07:48,243 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-27 08:07:48,697 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-27 08:07:51,712 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-27 08:07:52,165 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-27 08:07:55,181 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-27 08:07:55,525 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-27 08:07:58,540 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-27 08:07:58,993 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-27 08:08:02,009 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-27 08:08:02,509 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-27 08:08:05,525 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-27 08:08:05,946 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-27 08:08:08,962 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-27 08:08:09,400 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-27 08:08:12,415 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-27 08:08:12,868 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-27 08:08:15,884 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-27 08:08:16,306 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-27 08:08:19,321 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-27 08:08:19,743 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-27 08:08:22,759 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-27 08:08:23,243 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-27 08:08:26,259 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-27 08:08:26,681 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-27 08:08:29,696 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-27 08:08:30,196 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-27 08:08:33,212 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-27 08:08:33,618 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-27 08:08:36,634 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-27 08:08:37,056 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-27 08:08:40,071 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-27 08:08:40,524 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-27 08:08:43,540 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-27 08:08:43,931 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-27 08:08:46,946 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-27 08:08:47,415 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-27 08:08:50,431 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-27 08:08:50,884 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-27 08:08:53,899 - INFO - 正在批量插入每日数据，批次 22/22，共 62 条记录
2025-05-27 08:08:54,290 - INFO - 批量插入每日数据成功，批次 22，62 条记录
2025-05-27 08:08:57,306 - INFO - 批量插入每日数据完成: 总计 2162 条，成功 2162 条，失败 0 条
2025-05-27 08:08:57,306 - INFO - 批量插入日销售数据完成，共 2162 条记录
2025-05-27 08:08:57,306 - INFO - 日销售数据同步完成！更新: 46 条，插入: 2162 条，错误: 0 条，跳过: 10791 条
2025-05-27 08:08:57,306 - INFO - 正在获取宜搭月销售表单数据...
2025-05-27 08:08:57,306 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-27 08:08:57,306 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-27 08:08:57,306 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-27 08:08:57,306 - INFO - Request Parameters - Page 1:
2025-05-27 08:08:57,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:08:57,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:08:58,024 - INFO - API请求耗时: 719ms
2025-05-27 08:08:58,024 - INFO - Response - Page 1
2025-05-27 08:08:58,024 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:08:58,024 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:08:58,024 - WARNING - 月度分段 1 查询返回空数据
2025-05-27 08:08:58,024 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-27 08:08:58,024 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-27 08:08:58,024 - INFO - Request Parameters - Page 1:
2025-05-27 08:08:58,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:08:58,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:08:58,227 - INFO - API请求耗时: 203ms
2025-05-27 08:08:58,227 - INFO - Response - Page 1
2025-05-27 08:08:58,227 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:08:58,227 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:08:58,227 - WARNING - 单月查询返回空数据: 2024-05
2025-05-27 08:08:58,743 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-27 08:08:58,743 - INFO - Request Parameters - Page 1:
2025-05-27 08:08:58,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:08:58,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:08:58,962 - INFO - API请求耗时: 219ms
2025-05-27 08:08:58,962 - INFO - Response - Page 1
2025-05-27 08:08:58,962 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:08:58,962 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:08:58,962 - WARNING - 单月查询返回空数据: 2024-06
2025-05-27 08:08:59,477 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-27 08:08:59,477 - INFO - Request Parameters - Page 1:
2025-05-27 08:08:59,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:08:59,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:08:59,680 - INFO - API请求耗时: 203ms
2025-05-27 08:08:59,680 - INFO - Response - Page 1
2025-05-27 08:08:59,680 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:08:59,680 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:08:59,680 - WARNING - 单月查询返回空数据: 2024-07
2025-05-27 08:09:01,196 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-27 08:09:01,196 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-27 08:09:01,196 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:01,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:01,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:01,430 - INFO - API请求耗时: 234ms
2025-05-27 08:09:01,430 - INFO - Response - Page 1
2025-05-27 08:09:01,430 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:09:01,430 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:09:01,430 - WARNING - 月度分段 2 查询返回空数据
2025-05-27 08:09:01,430 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-27 08:09:01,430 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-27 08:09:01,430 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:01,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:01,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:01,649 - INFO - API请求耗时: 219ms
2025-05-27 08:09:01,649 - INFO - Response - Page 1
2025-05-27 08:09:01,649 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:09:01,649 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:09:01,649 - WARNING - 单月查询返回空数据: 2024-08
2025-05-27 08:09:02,165 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-27 08:09:02,165 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:02,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:02,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:02,352 - INFO - API请求耗时: 187ms
2025-05-27 08:09:02,352 - INFO - Response - Page 1
2025-05-27 08:09:02,352 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:09:02,352 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:09:02,352 - WARNING - 单月查询返回空数据: 2024-09
2025-05-27 08:09:02,868 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-27 08:09:02,868 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:02,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:02,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:03,087 - INFO - API请求耗时: 219ms
2025-05-27 08:09:03,087 - INFO - Response - Page 1
2025-05-27 08:09:03,087 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-27 08:09:03,087 - INFO - 查询完成，共获取到 0 条记录
2025-05-27 08:09:03,087 - WARNING - 单月查询返回空数据: 2024-10
2025-05-27 08:09:04,618 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-27 08:09:04,618 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-27 08:09:04,618 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:04,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:04,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:05,134 - INFO - API请求耗时: 516ms
2025-05-27 08:09:05,149 - INFO - Response - Page 1
2025-05-27 08:09:05,149 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:09:05,665 - INFO - Request Parameters - Page 2:
2025-05-27 08:09:05,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:05,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:06,212 - INFO - API请求耗时: 547ms
2025-05-27 08:09:06,212 - INFO - Response - Page 2
2025-05-27 08:09:06,212 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:09:06,727 - INFO - Request Parameters - Page 3:
2025-05-27 08:09:06,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:06,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:07,180 - INFO - API请求耗时: 453ms
2025-05-27 08:09:07,180 - INFO - Response - Page 3
2025-05-27 08:09:07,180 - INFO - 第 3 页获取到 48 条记录
2025-05-27 08:09:07,180 - INFO - 查询完成，共获取到 248 条记录
2025-05-27 08:09:07,180 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-27 08:09:08,196 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-27 08:09:08,196 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-27 08:09:08,196 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:08,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:08,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:08,759 - INFO - API请求耗时: 563ms
2025-05-27 08:09:08,759 - INFO - Response - Page 1
2025-05-27 08:09:08,759 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:09:09,274 - INFO - Request Parameters - Page 2:
2025-05-27 08:09:09,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:09,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:09,821 - INFO - API请求耗时: 547ms
2025-05-27 08:09:09,821 - INFO - Response - Page 2
2025-05-27 08:09:09,821 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:09:10,337 - INFO - Request Parameters - Page 3:
2025-05-27 08:09:10,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:10,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:10,915 - INFO - API请求耗时: 578ms
2025-05-27 08:09:10,915 - INFO - Response - Page 3
2025-05-27 08:09:10,915 - INFO - 第 3 页获取到 100 条记录
2025-05-27 08:09:11,415 - INFO - Request Parameters - Page 4:
2025-05-27 08:09:11,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:11,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:11,930 - INFO - API请求耗时: 516ms
2025-05-27 08:09:11,930 - INFO - Response - Page 4
2025-05-27 08:09:11,930 - INFO - 第 4 页获取到 100 条记录
2025-05-27 08:09:12,446 - INFO - Request Parameters - Page 5:
2025-05-27 08:09:12,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:12,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:13,040 - INFO - API请求耗时: 594ms
2025-05-27 08:09:13,040 - INFO - Response - Page 5
2025-05-27 08:09:13,040 - INFO - 第 5 页获取到 100 条记录
2025-05-27 08:09:13,540 - INFO - Request Parameters - Page 6:
2025-05-27 08:09:13,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:13,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:14,009 - INFO - API请求耗时: 469ms
2025-05-27 08:09:14,009 - INFO - Response - Page 6
2025-05-27 08:09:14,009 - INFO - 第 6 页获取到 100 条记录
2025-05-27 08:09:14,524 - INFO - Request Parameters - Page 7:
2025-05-27 08:09:14,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:14,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:15,071 - INFO - API请求耗时: 547ms
2025-05-27 08:09:15,071 - INFO - Response - Page 7
2025-05-27 08:09:15,071 - INFO - 第 7 页获取到 100 条记录
2025-05-27 08:09:15,587 - INFO - Request Parameters - Page 8:
2025-05-27 08:09:15,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:15,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:15,915 - INFO - API请求耗时: 328ms
2025-05-27 08:09:15,915 - INFO - Response - Page 8
2025-05-27 08:09:15,915 - INFO - 第 8 页获取到 16 条记录
2025-05-27 08:09:15,915 - INFO - 查询完成，共获取到 716 条记录
2025-05-27 08:09:15,915 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-27 08:09:16,930 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-27 08:09:16,930 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-27 08:09:16,930 - INFO - Request Parameters - Page 1:
2025-05-27 08:09:16,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:16,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:17,477 - INFO - API请求耗时: 547ms
2025-05-27 08:09:17,477 - INFO - Response - Page 1
2025-05-27 08:09:17,477 - INFO - 第 1 页获取到 100 条记录
2025-05-27 08:09:17,977 - INFO - Request Parameters - Page 2:
2025-05-27 08:09:17,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:17,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:18,509 - INFO - API请求耗时: 531ms
2025-05-27 08:09:18,509 - INFO - Response - Page 2
2025-05-27 08:09:18,509 - INFO - 第 2 页获取到 100 条记录
2025-05-27 08:09:19,024 - INFO - Request Parameters - Page 3:
2025-05-27 08:09:19,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 08:09:19,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 08:09:19,399 - INFO - API请求耗时: 375ms
2025-05-27 08:09:19,399 - INFO - Response - Page 3
2025-05-27 08:09:19,399 - INFO - 第 3 页获取到 24 条记录
2025-05-27 08:09:19,399 - INFO - 查询完成，共获取到 224 条记录
2025-05-27 08:09:19,399 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-27 08:09:20,399 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-27 08:09:20,399 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-27 08:09:20,399 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-27 08:09:20,399 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-27 08:09:20,399 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-27 08:09:20,462 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-27 08:09:20,930 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-27 08:09:20,930 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 180873.02, 'new_value': 185494.08000000002}, {'field': 'dailyBillAmount', 'old_value': 180873.02, 'new_value': 185494.08000000002}, {'field': 'amount', 'old_value': 4921.8, 'new_value': 5310.3}, {'field': 'count', 'old_value': 70, 'new_value': 74}, {'field': 'onlineAmount', 'old_value': 4997.8, 'new_value': 5386.3}, {'field': 'onlineCount', 'old_value': 70, 'new_value': 74}]
2025-05-27 08:09:21,415 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-27 08:09:21,415 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 462405.05, 'new_value': 472707.05}, {'field': 'dailyBillAmount', 'old_value': 462405.05, 'new_value': 472707.05}, {'field': 'amount', 'old_value': 245507.8, 'new_value': 252658.8}, {'field': 'count', 'old_value': 2300, 'new_value': 2369}, {'field': 'instoreAmount', 'old_value': 100476.3, 'new_value': 103397.4}, {'field': 'instoreCount', 'old_value': 773, 'new_value': 804}, {'field': 'onlineAmount', 'old_value': 145383.9, 'new_value': 149613.8}, {'field': 'onlineCount', 'old_value': 1527, 'new_value': 1565}]
2025-05-27 08:09:21,915 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-27 08:09:21,915 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 318173.63, 'new_value': 323325.93}, {'field': 'dailyBillAmount', 'old_value': 318173.63, 'new_value': 323325.93}, {'field': 'amount', 'old_value': 320959.82, 'new_value': 326104.03}, {'field': 'count', 'old_value': 2119, 'new_value': 2159}, {'field': 'instoreAmount', 'old_value': 304186.68, 'new_value': 309156.48}, {'field': 'instoreCount', 'old_value': 1876, 'new_value': 1911}, {'field': 'onlineAmount', 'old_value': 17015.34, 'new_value': 17189.75}, {'field': 'onlineCount', 'old_value': 243, 'new_value': 248}]
2025-05-27 08:09:22,462 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-27 08:09:22,462 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 677240.67, 'new_value': 698735.48}, {'field': 'dailyBillAmount', 'old_value': 677240.67, 'new_value': 698735.48}, {'field': 'amount', 'old_value': 490326.98, 'new_value': 503697.68}, {'field': 'count', 'old_value': 2371, 'new_value': 2436}, {'field': 'instoreAmount', 'old_value': 490326.98, 'new_value': 503697.68}, {'field': 'instoreCount', 'old_value': 2371, 'new_value': 2436}]
2025-05-27 08:09:22,868 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-27 08:09:22,883 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 538215.1, 'new_value': 548742.6}, {'field': 'dailyBillAmount', 'old_value': 538215.1, 'new_value': 548742.6}, {'field': 'amount', 'old_value': 883535.0, 'new_value': 902658.0}, {'field': 'count', 'old_value': 3058, 'new_value': 3129}, {'field': 'instoreAmount', 'old_value': 884785.0, 'new_value': 903908.0}, {'field': 'instoreCount', 'old_value': 3058, 'new_value': 3129}]
2025-05-27 08:09:23,258 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-27 08:09:23,258 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61223.7, 'new_value': 64492.9}, {'field': 'dailyBillAmount', 'old_value': 61223.7, 'new_value': 64492.9}, {'field': 'amount', 'old_value': 81698.81, 'new_value': 85054.31}, {'field': 'count', 'old_value': 314, 'new_value': 325}, {'field': 'instoreAmount', 'old_value': 43914.6, 'new_value': 45418.6}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 46}, {'field': 'onlineAmount', 'old_value': 41881.12, 'new_value': 43732.62}, {'field': 'onlineCount', 'old_value': 269, 'new_value': 279}]
2025-05-27 08:09:23,665 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-27 08:09:23,665 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 217811.9, 'new_value': 219790.9}, {'field': 'amount', 'old_value': 217811.9, 'new_value': 219790.9}, {'field': 'count', 'old_value': 126, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 217811.9, 'new_value': 219790.9}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 129}]
2025-05-27 08:09:24,118 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-27 08:09:24,118 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 739744.52, 'new_value': 764162.41}, {'field': 'dailyBillAmount', 'old_value': 739744.52, 'new_value': 764162.41}, {'field': 'amount', 'old_value': 658132.85, 'new_value': 675541.35}, {'field': 'count', 'old_value': 4646, 'new_value': 4793}, {'field': 'instoreAmount', 'old_value': 542592.41, 'new_value': 557335.41}, {'field': 'instoreCount', 'old_value': 2331, 'new_value': 2408}, {'field': 'onlineAmount', 'old_value': 119547.67, 'new_value': 122288.17}, {'field': 'onlineCount', 'old_value': 2315, 'new_value': 2385}]
2025-05-27 08:09:24,587 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-27 08:09:24,587 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 737272.69, 'new_value': 757619.32}, {'field': 'dailyBillAmount', 'old_value': 737272.69, 'new_value': 757619.32}, {'field': 'amount', 'old_value': 205080.04, 'new_value': 208465.04}, {'field': 'count', 'old_value': 1165, 'new_value': 1182}, {'field': 'instoreAmount', 'old_value': 205080.04, 'new_value': 208465.04}, {'field': 'instoreCount', 'old_value': 1165, 'new_value': 1182}]
2025-05-27 08:09:25,008 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-27 08:09:25,008 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 50604.0, 'new_value': 73097.0}, {'field': 'count', 'old_value': 73, 'new_value': 77}, {'field': 'instoreAmount', 'old_value': 50604.0, 'new_value': 73097.0}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 77}]
2025-05-27 08:09:25,446 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-27 08:09:25,446 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 107022.2, 'new_value': 109570.2}, {'field': 'count', 'old_value': 297, 'new_value': 303}, {'field': 'instoreAmount', 'old_value': 107023.9, 'new_value': 109571.9}, {'field': 'instoreCount', 'old_value': 297, 'new_value': 303}]
2025-05-27 08:09:25,852 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-27 08:09:25,852 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1017795.5599999999, 'new_value': 1039195.7}, {'field': 'dailyBillAmount', 'old_value': 1017795.5599999999, 'new_value': 1039195.7}, {'field': 'amount', 'old_value': -368715.41000000003, 'new_value': -378179.93}, {'field': 'count', 'old_value': 1113, 'new_value': 1139}, {'field': 'instoreAmount', 'old_value': 655923.81, 'new_value': 665773.51}, {'field': 'instoreCount', 'old_value': 1113, 'new_value': 1139}]
2025-05-27 08:09:26,337 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-27 08:09:26,337 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 435506.0, 'new_value': 449580.0}, {'field': 'amount', 'old_value': 435506.0, 'new_value': 449580.0}, {'field': 'count', 'old_value': 1432, 'new_value': 1473}, {'field': 'instoreAmount', 'old_value': 435506.0, 'new_value': 449580.0}, {'field': 'instoreCount', 'old_value': 1432, 'new_value': 1473}]
2025-05-27 08:09:26,774 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-27 08:09:26,774 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 401310.04, 'new_value': 411007.69}, {'field': 'dailyBillAmount', 'old_value': 309085.54, 'new_value': 317989.19}, {'field': 'amount', 'old_value': 401310.04, 'new_value': 411007.69}, {'field': 'count', 'old_value': 1359, 'new_value': 1390}, {'field': 'instoreAmount', 'old_value': 401310.04, 'new_value': 411007.69}, {'field': 'instoreCount', 'old_value': 1359, 'new_value': 1390}]
2025-05-27 08:09:27,180 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-27 08:09:27,180 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 202055.23, 'new_value': 209728.71}, {'field': 'dailyBillAmount', 'old_value': 202055.23, 'new_value': 209728.71}, {'field': 'amount', 'old_value': 13677.4, 'new_value': 14589.8}, {'field': 'count', 'old_value': 101, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 16090.9, 'new_value': 17003.3}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 104}]
2025-05-27 08:09:27,649 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-27 08:09:27,649 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104895.18, 'new_value': 107133.56}, {'field': 'dailyBillAmount', 'old_value': 104895.18, 'new_value': 107133.56}, {'field': 'amount', 'old_value': 64375.87, 'new_value': 65303.47}, {'field': 'count', 'old_value': 957, 'new_value': 975}, {'field': 'instoreAmount', 'old_value': 66341.77, 'new_value': 67269.37}, {'field': 'instoreCount', 'old_value': 957, 'new_value': 975}]
2025-05-27 08:09:28,102 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-27 08:09:28,102 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149718.91999999998, 'new_value': 155773.31}, {'field': 'dailyBillAmount', 'old_value': 82358.29000000001, 'new_value': 88132.45}, {'field': 'amount', 'old_value': 149718.06, 'new_value': 155772.45}, {'field': 'count', 'old_value': 5115, 'new_value': 5346}, {'field': 'instoreAmount', 'old_value': 130468.16, 'new_value': 135536.68}, {'field': 'instoreCount', 'old_value': 4634, 'new_value': 4837}, {'field': 'onlineAmount', 'old_value': 19250.76, 'new_value': 20236.63}, {'field': 'onlineCount', 'old_value': 481, 'new_value': 509}]
2025-05-27 08:09:28,696 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-27 08:09:28,696 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 308248.22, 'new_value': 315343.22}, {'field': 'dailyBillAmount', 'old_value': 302858.0, 'new_value': 309953.0}, {'field': 'amount', 'old_value': 254582.01, 'new_value': 261677.01}, {'field': 'count', 'old_value': 239, 'new_value': 244}, {'field': 'instoreAmount', 'old_value': 254349.0, 'new_value': 261444.0}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 241}]
2025-05-27 08:09:29,149 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-27 08:09:29,149 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 591980.22, 'new_value': 600227.55}, {'field': 'dailyBillAmount', 'old_value': 591475.67, 'new_value': 599723.0}, {'field': 'amount', 'old_value': 591980.22, 'new_value': 600227.55}, {'field': 'count', 'old_value': 522, 'new_value': 537}, {'field': 'instoreAmount', 'old_value': 591981.22, 'new_value': 600228.55}, {'field': 'instoreCount', 'old_value': 522, 'new_value': 537}]
2025-05-27 08:09:29,680 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-27 08:09:29,680 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 106342.5, 'new_value': 109801.1}, {'field': 'dailyBillAmount', 'old_value': 106342.5, 'new_value': 109801.1}, {'field': 'amount', 'old_value': 115553.9, 'new_value': 119362.5}, {'field': 'count', 'old_value': 308, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 115559.8, 'new_value': 119368.4}, {'field': 'instoreCount', 'old_value': 308, 'new_value': 318}]
2025-05-27 08:09:30,118 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-27 08:09:30,118 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 159289.7, 'new_value': 172122.7}, {'field': 'amount', 'old_value': 159289.7, 'new_value': 172122.7}, {'field': 'count', 'old_value': 191, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 159416.7, 'new_value': 172249.7}, {'field': 'instoreCount', 'old_value': 191, 'new_value': 198}]
2025-05-27 08:09:30,508 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-27 08:09:30,508 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 236236.38, 'new_value': 241951.38}, {'field': 'dailyBillAmount', 'old_value': 236236.38, 'new_value': 241951.38}, {'field': 'amount', 'old_value': 248714.55, 'new_value': 255036.55}, {'field': 'count', 'old_value': 1646, 'new_value': 1687}, {'field': 'instoreAmount', 'old_value': 249923.55, 'new_value': 256245.55}, {'field': 'instoreCount', 'old_value': 1646, 'new_value': 1687}]
2025-05-27 08:09:31,087 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-27 08:09:31,087 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 152122.52, 'new_value': 152884.72}, {'field': 'dailyBillAmount', 'old_value': 152122.52, 'new_value': 152884.72}, {'field': 'amount', 'old_value': 15080.92, 'new_value': 15657.06}, {'field': 'count', 'old_value': 1381, 'new_value': 1452}, {'field': 'instoreAmount', 'old_value': 20160.93, 'new_value': 20911.22}, {'field': 'instoreCount', 'old_value': 1381, 'new_value': 1452}]
2025-05-27 08:09:31,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-27 08:09:31,540 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 284204.31, 'new_value': 295512.41}, {'field': 'amount', 'old_value': 284200.16000000003, 'new_value': 295508.26}, {'field': 'count', 'old_value': 6576, 'new_value': 6814}, {'field': 'instoreAmount', 'old_value': 277470.66, 'new_value': 288595.86}, {'field': 'instoreCount', 'old_value': 6345, 'new_value': 6576}, {'field': 'onlineAmount', 'old_value': 10894.53, 'new_value': 11144.13}, {'field': 'onlineCount', 'old_value': 231, 'new_value': 238}]
2025-05-27 08:09:32,008 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-27 08:09:32,008 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 241128.2, 'new_value': 250304.1}, {'field': 'dailyBillAmount', 'old_value': 241128.2, 'new_value': 250304.1}, {'field': 'amount', 'old_value': 241128.2, 'new_value': 250304.1}, {'field': 'count', 'old_value': 722, 'new_value': 744}, {'field': 'instoreAmount', 'old_value': 241128.2, 'new_value': 250304.1}, {'field': 'instoreCount', 'old_value': 722, 'new_value': 744}]
2025-05-27 08:09:32,462 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-27 08:09:32,462 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 214732.2, 'new_value': 220450.83}, {'field': 'dailyBillAmount', 'old_value': 214732.2, 'new_value': 220450.83}, {'field': 'amount', 'old_value': 71430.2, 'new_value': 73572.2}, {'field': 'count', 'old_value': 166, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 71430.2, 'new_value': 73572.2}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 172}]
2025-05-27 08:09:32,915 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-27 08:09:32,915 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 424052.34, 'new_value': 434260.18}, {'field': 'dailyBillAmount', 'old_value': 424052.34, 'new_value': 434260.18}, {'field': 'amount', 'old_value': 175849.6, 'new_value': 180465.7}, {'field': 'count', 'old_value': 655, 'new_value': 675}, {'field': 'instoreAmount', 'old_value': 175849.86, 'new_value': 180465.96}, {'field': 'instoreCount', 'old_value': 655, 'new_value': 675}]
2025-05-27 08:09:33,352 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-27 08:09:33,352 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 91422.53, 'new_value': 95151.12}, {'field': 'dailyBillAmount', 'old_value': 91422.53, 'new_value': 95151.12}, {'field': 'amount', 'old_value': 27668.46, 'new_value': 28644.65}, {'field': 'count', 'old_value': 1007, 'new_value': 1042}, {'field': 'instoreAmount', 'old_value': 6407.03, 'new_value': 6740.53}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 176}, {'field': 'onlineAmount', 'old_value': 21561.77, 'new_value': 22227.17}, {'field': 'onlineCount', 'old_value': 838, 'new_value': 866}]
2025-05-27 08:09:33,852 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-27 08:09:33,852 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146427.94, 'new_value': 151351.01}, {'field': 'dailyBillAmount', 'old_value': 146427.94, 'new_value': 151351.01}, {'field': 'amount', 'old_value': 24598.36, 'new_value': 25384.78}, {'field': 'count', 'old_value': 590, 'new_value': 616}, {'field': 'instoreAmount', 'old_value': 21396.25, 'new_value': 22062.86}, {'field': 'instoreCount', 'old_value': 525, 'new_value': 548}, {'field': 'onlineAmount', 'old_value': 3202.8, 'new_value': 3322.61}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 68}]
2025-05-27 08:09:34,352 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-27 08:09:34,352 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21021.43, 'new_value': 21221.43}, {'field': 'dailyBillAmount', 'old_value': 21021.43, 'new_value': 21221.43}, {'field': 'amount', 'old_value': 16457.58, 'new_value': 16657.58}, {'field': 'count', 'old_value': 601, 'new_value': 608}, {'field': 'instoreAmount', 'old_value': 16825.18, 'new_value': 17025.18}, {'field': 'instoreCount', 'old_value': 601, 'new_value': 608}]
2025-05-27 08:09:34,790 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-27 08:09:34,790 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46910.26, 'new_value': 48067.14}, {'field': 'dailyBillAmount', 'old_value': 46910.26, 'new_value': 48067.14}, {'field': 'amount', 'old_value': 29911.47, 'new_value': 32530.48}, {'field': 'count', 'old_value': 1600, 'new_value': 1646}, {'field': 'instoreAmount', 'old_value': 15348.72, 'new_value': 17329.72}, {'field': 'instoreCount', 'old_value': 650, 'new_value': 660}, {'field': 'onlineAmount', 'old_value': 15475.87, 'new_value': 16144.88}, {'field': 'onlineCount', 'old_value': 950, 'new_value': 986}]
2025-05-27 08:09:35,243 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-27 08:09:35,243 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 326567.19, 'new_value': 339664.22}, {'field': 'dailyBillAmount', 'old_value': 326567.19, 'new_value': 339664.22}, {'field': 'amount', 'old_value': 149645.06, 'new_value': 157497.46}, {'field': 'count', 'old_value': 624, 'new_value': 662}, {'field': 'instoreAmount', 'old_value': 154232.9, 'new_value': 162147.9}, {'field': 'instoreCount', 'old_value': 624, 'new_value': 662}]
2025-05-27 08:09:35,680 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-27 08:09:35,680 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 18382.55, 'new_value': 18499.55}, {'field': 'count', 'old_value': 158, 'new_value': 159}, {'field': 'instoreAmount', 'old_value': 18456.79, 'new_value': 18573.79}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 159}]
2025-05-27 08:09:36,180 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-27 08:09:36,180 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 202278.18, 'new_value': 208737.63}, {'field': 'dailyBillAmount', 'old_value': 202278.18, 'new_value': 208737.63}, {'field': 'amount', 'old_value': 98325.5, 'new_value': 101944.95}, {'field': 'count', 'old_value': 4191, 'new_value': 4359}, {'field': 'instoreAmount', 'old_value': 100365.84, 'new_value': 104069.38}, {'field': 'instoreCount', 'old_value': 4191, 'new_value': 4359}]
2025-05-27 08:09:36,587 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-27 08:09:36,587 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 477938.8, 'new_value': 498196.2}, {'field': 'dailyBillAmount', 'old_value': 477938.8, 'new_value': 498196.2}, {'field': 'amount', 'old_value': 477938.8, 'new_value': 498196.2}, {'field': 'count', 'old_value': 609, 'new_value': 630}, {'field': 'instoreAmount', 'old_value': 477938.8, 'new_value': 498196.2}, {'field': 'instoreCount', 'old_value': 609, 'new_value': 630}]
2025-05-27 08:09:37,055 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-27 08:09:37,055 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 212526.39, 'new_value': 217157.13}, {'field': 'dailyBillAmount', 'old_value': 212526.39, 'new_value': 217157.13}, {'field': 'amount', 'old_value': 122530.35, 'new_value': 124756.25}, {'field': 'count', 'old_value': 321, 'new_value': 328}, {'field': 'instoreAmount', 'old_value': 123946.95, 'new_value': 126172.85}, {'field': 'instoreCount', 'old_value': 321, 'new_value': 328}]
2025-05-27 08:09:37,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-27 08:09:37,540 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52145.0, 'new_value': 54216.0}, {'field': 'dailyBillAmount', 'old_value': 52145.0, 'new_value': 54216.0}, {'field': 'amount', 'old_value': 52145.0, 'new_value': 54216.0}, {'field': 'count', 'old_value': 1022, 'new_value': 1064}, {'field': 'instoreAmount', 'old_value': 52184.0, 'new_value': 54255.0}, {'field': 'instoreCount', 'old_value': 1022, 'new_value': 1064}]
2025-05-27 08:09:37,993 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-27 08:09:37,993 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 91219.85, 'new_value': 94475.84}, {'field': 'dailyBillAmount', 'old_value': 91219.85, 'new_value': 94475.84}, {'field': 'amount', 'old_value': 94342.78, 'new_value': 97751.14}, {'field': 'count', 'old_value': 4974, 'new_value': 5134}, {'field': 'instoreAmount', 'old_value': 45591.31, 'new_value': 47220.4}, {'field': 'instoreCount', 'old_value': 2280, 'new_value': 2359}, {'field': 'onlineAmount', 'old_value': 50039.56, 'new_value': 51833.83}, {'field': 'onlineCount', 'old_value': 2694, 'new_value': 2775}]
2025-05-27 08:09:38,399 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-27 08:09:38,399 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32283.69, 'new_value': 33762.2}, {'field': 'dailyBillAmount', 'old_value': 32283.69, 'new_value': 33762.2}, {'field': 'amount', 'old_value': 44057.64, 'new_value': 45895.75}, {'field': 'count', 'old_value': 1281, 'new_value': 1330}, {'field': 'instoreAmount', 'old_value': 40297.99, 'new_value': 42136.1}, {'field': 'instoreCount', 'old_value': 1130, 'new_value': 1179}]
2025-05-27 08:09:38,805 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-27 08:09:38,821 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66139.13, 'new_value': 68814.5}, {'field': 'dailyBillAmount', 'old_value': 66139.13, 'new_value': 68814.5}, {'field': 'amount', 'old_value': 66037.83, 'new_value': 68740.39}, {'field': 'count', 'old_value': 2561, 'new_value': 2664}, {'field': 'instoreAmount', 'old_value': 42986.44, 'new_value': 44956.34}, {'field': 'instoreCount', 'old_value': 1525, 'new_value': 1595}, {'field': 'onlineAmount', 'old_value': 23352.02, 'new_value': 24106.88}, {'field': 'onlineCount', 'old_value': 1036, 'new_value': 1069}]
2025-05-27 08:09:39,290 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-27 08:09:39,290 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 64303.48, 'new_value': 67371.08}, {'field': 'count', 'old_value': 799, 'new_value': 838}, {'field': 'instoreAmount', 'old_value': 64768.38, 'new_value': 67899.98}, {'field': 'instoreCount', 'old_value': 799, 'new_value': 838}]
2025-05-27 08:09:39,743 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-27 08:09:39,743 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 71151.9, 'new_value': 73446.7}, {'field': 'amount', 'old_value': 71151.4, 'new_value': 73446.2}, {'field': 'count', 'old_value': 1826, 'new_value': 1882}, {'field': 'instoreAmount', 'old_value': 72221.7, 'new_value': 74536.5}, {'field': 'instoreCount', 'old_value': 1826, 'new_value': 1882}]
2025-05-27 08:09:40,258 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-27 08:09:40,258 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 332987.42, 'new_value': 343172.42}, {'field': 'dailyBillAmount', 'old_value': 332987.42, 'new_value': 343172.42}, {'field': 'amount', 'old_value': 103269.82, 'new_value': 108408.82}, {'field': 'count', 'old_value': 364, 'new_value': 375}, {'field': 'instoreAmount', 'old_value': 103269.82, 'new_value': 108408.82}, {'field': 'instoreCount', 'old_value': 364, 'new_value': 375}]
2025-05-27 08:09:40,696 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-27 08:09:40,696 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88927.76, 'new_value': 91534.86}, {'field': 'dailyBillAmount', 'old_value': 88927.76, 'new_value': 91534.86}, {'field': 'amount', 'old_value': 90229.56, 'new_value': 92720.66}, {'field': 'count', 'old_value': 330, 'new_value': 337}, {'field': 'instoreAmount', 'old_value': 92364.19, 'new_value': 94855.29}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 337}]
2025-05-27 08:09:41,227 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-27 08:09:41,227 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51219.0, 'new_value': 52096.0}, {'field': 'dailyBillAmount', 'old_value': 51219.0, 'new_value': 52096.0}, {'field': 'amount', 'old_value': 63388.0, 'new_value': 64265.0}, {'field': 'count', 'old_value': 121, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 68343.0, 'new_value': 69220.0}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 123}]
2025-05-27 08:09:41,727 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-27 08:09:41,727 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 89042.15, 'new_value': 94671.75}, {'field': 'dailyBillAmount', 'old_value': 86548.95, 'new_value': 92178.55}, {'field': 'amount', 'old_value': 89039.55, 'new_value': 94669.15}, {'field': 'count', 'old_value': 274, 'new_value': 294}, {'field': 'instoreAmount', 'old_value': 99932.25, 'new_value': 106252.65}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 294}]
2025-05-27 08:09:42,274 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-27 08:09:42,274 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121123.78, 'new_value': 123433.2}, {'field': 'dailyBillAmount', 'old_value': 121123.78, 'new_value': 123433.2}, {'field': 'amount', 'old_value': 67587.8, 'new_value': 69900.22}, {'field': 'count', 'old_value': 1844, 'new_value': 1890}, {'field': 'instoreAmount', 'old_value': 59073.74, 'new_value': 61200.1}, {'field': 'instoreCount', 'old_value': 1563, 'new_value': 1604}, {'field': 'onlineAmount', 'old_value': 9642.57, 'new_value': 9837.31}, {'field': 'onlineCount', 'old_value': 281, 'new_value': 286}]
2025-05-27 08:09:42,696 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-27 08:09:42,696 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 168828.42, 'new_value': 177620.42}, {'field': 'dailyBillAmount', 'old_value': 163662.5, 'new_value': 172454.5}, {'field': 'amount', 'old_value': 168828.42, 'new_value': 177620.42}, {'field': 'count', 'old_value': 2075, 'new_value': 2183}, {'field': 'instoreAmount', 'old_value': 160654.85, 'new_value': 169446.85}, {'field': 'instoreCount', 'old_value': 1980, 'new_value': 2088}]
2025-05-27 08:09:43,149 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-27 08:09:43,149 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77856.41, 'new_value': 79117.53}, {'field': 'dailyBillAmount', 'old_value': 77856.41, 'new_value': 79117.53}, {'field': 'amount', 'old_value': 104136.43, 'new_value': 105397.55}, {'field': 'count', 'old_value': 462, 'new_value': 468}, {'field': 'instoreAmount', 'old_value': 100539.45999999999, 'new_value': 101800.58}, {'field': 'instoreCount', 'old_value': 414, 'new_value': 420}]
2025-05-27 08:09:43,618 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-27 08:09:43,618 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 198482.3, 'new_value': 200470.6}, {'field': 'dailyBillAmount', 'old_value': 198482.3, 'new_value': 200470.6}, {'field': 'amount', 'old_value': 202304.8, 'new_value': 204914.2}, {'field': 'count', 'old_value': 745, 'new_value': 758}, {'field': 'instoreAmount', 'old_value': 205189.7, 'new_value': 208479.1}, {'field': 'instoreCount', 'old_value': 745, 'new_value': 758}]
2025-05-27 08:09:44,024 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-27 08:09:44,024 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45989.0, 'new_value': 47994.0}, {'field': 'dailyBillAmount', 'old_value': 45989.0, 'new_value': 47994.0}, {'field': 'amount', 'old_value': 43187.0, 'new_value': 45192.0}, {'field': 'count', 'old_value': 109, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 43780.0, 'new_value': 45785.0}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 114}]
2025-05-27 08:09:44,446 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-27 08:09:44,446 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 18332.14, 'new_value': 18803.04}, {'field': 'count', 'old_value': 35, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 18613.54, 'new_value': 19084.44}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 37}]
2025-05-27 08:09:44,915 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-27 08:09:44,915 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 39712.98, 'new_value': 42983.58}, {'field': 'count', 'old_value': 214, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 37955.4, 'new_value': 41122.4}, {'field': 'instoreCount', 'old_value': 165, 'new_value': 184}, {'field': 'onlineAmount', 'old_value': 2598.33, 'new_value': 2701.93}, {'field': 'onlineCount', 'old_value': 49, 'new_value': 51}]
2025-05-27 08:09:45,415 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-27 08:09:45,415 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47384.49, 'new_value': 50109.94}, {'field': 'amount', 'old_value': 47382.85, 'new_value': 50108.3}, {'field': 'count', 'old_value': 2375, 'new_value': 2504}, {'field': 'instoreAmount', 'old_value': 54709.99, 'new_value': 57570.04}, {'field': 'instoreCount', 'old_value': 2375, 'new_value': 2504}]
2025-05-27 08:09:45,946 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-27 08:09:45,946 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 133374.97, 'new_value': 137471.15}, {'field': 'dailyBillAmount', 'old_value': 133374.97, 'new_value': 137471.15}, {'field': 'amount', 'old_value': 106770.2, 'new_value': 110165.1}, {'field': 'count', 'old_value': 437, 'new_value': 450}, {'field': 'instoreAmount', 'old_value': 106770.2, 'new_value': 110165.1}, {'field': 'instoreCount', 'old_value': 436, 'new_value': 449}]
2025-05-27 08:09:46,415 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-27 08:09:46,415 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 377995.77999999997, 'new_value': 390591.64}, {'field': 'dailyBillAmount', 'old_value': 377995.77999999997, 'new_value': 390591.64}, {'field': 'amount', 'old_value': 216651.88999999998, 'new_value': 224278.75}, {'field': 'count', 'old_value': 2499, 'new_value': 2582}, {'field': 'instoreAmount', 'old_value': 95197.3, 'new_value': 99857.99}, {'field': 'instoreCount', 'old_value': 1074, 'new_value': 1116}, {'field': 'onlineAmount', 'old_value': 121457.06, 'new_value': 124423.86}, {'field': 'onlineCount', 'old_value': 1425, 'new_value': 1466}]
2025-05-27 08:09:46,758 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-27 08:09:46,758 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 223298.75, 'new_value': 228004.33}, {'field': 'dailyBillAmount', 'old_value': 223298.75, 'new_value': 228004.33}, {'field': 'amount', 'old_value': 235618.19999999998, 'new_value': 240448.19999999998}, {'field': 'count', 'old_value': 1437, 'new_value': 1467}, {'field': 'instoreAmount', 'old_value': 236398.1, 'new_value': 241228.1}, {'field': 'instoreCount', 'old_value': 1437, 'new_value': 1467}]
2025-05-27 08:09:47,180 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-27 08:09:47,180 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74636.89, 'new_value': 75353.89}, {'field': 'amount', 'old_value': 74636.89, 'new_value': 75353.89}, {'field': 'count', 'old_value': 33, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 74636.89, 'new_value': 75353.89}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 35}]
2025-05-27 08:09:47,680 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-27 08:09:47,680 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 150517.27, 'new_value': 157969.94}, {'field': 'dailyBillAmount', 'old_value': 150517.27, 'new_value': 157969.94}, {'field': 'amount', 'old_value': 96537.28, 'new_value': 100075.41}, {'field': 'count', 'old_value': 1089, 'new_value': 1125}, {'field': 'instoreAmount', 'old_value': 87977.34, 'new_value': 91286.37}, {'field': 'instoreCount', 'old_value': 774, 'new_value': 807}, {'field': 'onlineAmount', 'old_value': 10351.11, 'new_value': 10580.21}, {'field': 'onlineCount', 'old_value': 315, 'new_value': 318}]
2025-05-27 08:09:48,149 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-27 08:09:48,149 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 199484.04, 'new_value': 206324.44}, {'field': 'dailyBillAmount', 'old_value': 194678.29, 'new_value': 201518.69}, {'field': 'amount', 'old_value': 199484.04, 'new_value': 206324.44}, {'field': 'count', 'old_value': 839, 'new_value': 867}, {'field': 'instoreAmount', 'old_value': 199484.04, 'new_value': 206324.44}, {'field': 'instoreCount', 'old_value': 839, 'new_value': 867}]
2025-05-27 08:09:48,555 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-27 08:09:48,555 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24299.26, 'new_value': 25112.66}, {'field': 'dailyBillAmount', 'old_value': 24299.26, 'new_value': 25112.66}, {'field': 'amount', 'old_value': 28951.760000000002, 'new_value': 29808.16}, {'field': 'count', 'old_value': 853, 'new_value': 883}, {'field': 'instoreAmount', 'old_value': 28971.56, 'new_value': 29827.96}, {'field': 'instoreCount', 'old_value': 853, 'new_value': 883}]
2025-05-27 08:09:49,024 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-27 08:09:49,024 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 308123.8, 'new_value': 315767.8}, {'field': 'amount', 'old_value': 308123.8, 'new_value': 315767.8}, {'field': 'count', 'old_value': 475, 'new_value': 488}, {'field': 'instoreAmount', 'old_value': 308123.8, 'new_value': 315767.8}, {'field': 'instoreCount', 'old_value': 475, 'new_value': 488}]
2025-05-27 08:09:49,461 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-27 08:09:49,461 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45804.33, 'new_value': 46799.59}, {'field': 'amount', 'old_value': 45804.33, 'new_value': 46799.59}, {'field': 'count', 'old_value': 385, 'new_value': 394}, {'field': 'instoreAmount', 'old_value': 45804.33, 'new_value': 46799.59}, {'field': 'instoreCount', 'old_value': 385, 'new_value': 394}]
2025-05-27 08:09:49,915 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-27 08:09:49,915 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 311410.0, 'new_value': 329507.0}, {'field': 'amount', 'old_value': 311410.0, 'new_value': 329507.0}, {'field': 'count', 'old_value': 71, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 311410.0, 'new_value': 329507.0}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 74}]
2025-05-27 08:09:50,368 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-27 08:09:50,368 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 50944.99, 'new_value': 52612.090000000004}, {'field': 'count', 'old_value': 530, 'new_value': 552}, {'field': 'instoreAmount', 'old_value': 50944.99, 'new_value': 52612.090000000004}, {'field': 'instoreCount', 'old_value': 530, 'new_value': 552}]
2025-05-27 08:09:50,946 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-27 08:09:50,946 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41516.9, 'new_value': 45238.2}, {'field': 'dailyBillAmount', 'old_value': 41516.9, 'new_value': 45238.2}, {'field': 'amount', 'old_value': 42980.2, 'new_value': 46701.5}, {'field': 'count', 'old_value': 53, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 43878.2, 'new_value': 47599.5}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 57}]
2025-05-27 08:09:51,415 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-27 08:09:51,415 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 442132.08, 'new_value': 458209.32}, {'field': 'dailyBillAmount', 'old_value': 442132.08, 'new_value': 458209.32}, {'field': 'amount', 'old_value': 449303.08, 'new_value': 465380.32}, {'field': 'count', 'old_value': 1433, 'new_value': 1486}, {'field': 'instoreAmount', 'old_value': 449303.08, 'new_value': 465380.32}, {'field': 'instoreCount', 'old_value': 1433, 'new_value': 1486}]
2025-05-27 08:09:51,821 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-27 08:09:51,836 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1023057.84, 'new_value': 1051950.69}, {'field': 'count', 'old_value': 1286, 'new_value': 1323}, {'field': 'instoreAmount', 'old_value': 1023058.01, 'new_value': 1051950.86}, {'field': 'instoreCount', 'old_value': 1286, 'new_value': 1323}]
2025-05-27 08:09:52,274 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMH01
2025-05-27 08:09:52,274 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-05, 变更字段: [{'field': 'count', 'old_value': 20, 'new_value': 21}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 21}]
2025-05-27 08:09:52,743 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-27 08:09:52,743 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 152938.3, 'new_value': 156944.1}, {'field': 'dailyBillAmount', 'old_value': 152938.3, 'new_value': 156944.1}, {'field': 'amount', 'old_value': 32264.7, 'new_value': 33524.6}, {'field': 'count', 'old_value': 125, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 32266.2, 'new_value': 33526.1}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 129}]
2025-05-27 08:09:53,196 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-27 08:09:53,196 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 198549.48, 'new_value': 201714.36}, {'field': 'amount', 'old_value': 198546.96, 'new_value': 201711.84}, {'field': 'count', 'old_value': 2086, 'new_value': 2127}, {'field': 'instoreAmount', 'old_value': 127617.22, 'new_value': 129124.65}, {'field': 'instoreCount', 'old_value': 1179, 'new_value': 1194}, {'field': 'onlineAmount', 'old_value': 76286.46, 'new_value': 77993.31}, {'field': 'onlineCount', 'old_value': 907, 'new_value': 933}]
2025-05-27 08:09:53,649 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-27 08:09:53,649 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 334742.17, 'new_value': 346532.26}, {'field': 'dailyBillAmount', 'old_value': 334742.17, 'new_value': 346532.26}, {'field': 'amount', 'old_value': 29894.36, 'new_value': 30681.47}, {'field': 'count', 'old_value': 918, 'new_value': 938}, {'field': 'instoreAmount', 'old_value': 35049.43, 'new_value': 35836.54}, {'field': 'instoreCount', 'old_value': 918, 'new_value': 938}]
2025-05-27 08:09:54,102 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-27 08:09:54,102 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 311721.18, 'new_value': 316947.63}, {'field': 'dailyBillAmount', 'old_value': 311721.18, 'new_value': 316947.63}, {'field': 'amount', 'old_value': 158450.03, 'new_value': 161902.9}, {'field': 'count', 'old_value': 3585, 'new_value': 3656}, {'field': 'instoreAmount', 'old_value': 132327.54, 'new_value': 134702.56}, {'field': 'instoreCount', 'old_value': 2993, 'new_value': 3042}, {'field': 'onlineAmount', 'old_value': 28343.05, 'new_value': 29447.5}, {'field': 'onlineCount', 'old_value': 592, 'new_value': 614}]
2025-05-27 08:09:54,602 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-27 08:09:54,602 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 277438.4, 'new_value': 286137.9}, {'field': 'amount', 'old_value': 277436.8, 'new_value': 286136.3}, {'field': 'count', 'old_value': 1104, 'new_value': 1135}, {'field': 'instoreAmount', 'old_value': 280647.3, 'new_value': 289516.7}, {'field': 'instoreCount', 'old_value': 1104, 'new_value': 1135}]
2025-05-27 08:09:55,040 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-27 08:09:55,040 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 474657.74, 'new_value': 486505.22}, {'field': 'dailyBillAmount', 'old_value': 474657.74, 'new_value': 486505.22}, {'field': 'amount', 'old_value': 454542.25, 'new_value': 455916.66}, {'field': 'count', 'old_value': 8580, 'new_value': 8623}, {'field': 'instoreAmount', 'old_value': 424765.57, 'new_value': 425765.42}, {'field': 'instoreCount', 'old_value': 8002, 'new_value': 8035}, {'field': 'onlineAmount', 'old_value': 31549.29, 'new_value': 31923.85}, {'field': 'onlineCount', 'old_value': 578, 'new_value': 588}]
2025-05-27 08:09:55,399 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-27 08:09:55,399 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 372468.42, 'new_value': 394111.52}, {'field': 'dailyBillAmount', 'old_value': 68024.42, 'new_value': 84306.85}, {'field': 'amount', 'old_value': 340183.15, 'new_value': 361826.15}, {'field': 'count', 'old_value': 8018, 'new_value': 8523}, {'field': 'instoreAmount', 'old_value': 265525.1, 'new_value': 280902.2}, {'field': 'instoreCount', 'old_value': 5752, 'new_value': 6082}, {'field': 'onlineAmount', 'old_value': 74817.85, 'new_value': 81083.85}, {'field': 'onlineCount', 'old_value': 2266, 'new_value': 2441}]
2025-05-27 08:09:55,821 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-27 08:09:55,821 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 101738.69, 'new_value': 108167.04}, {'field': 'dailyBillAmount', 'old_value': 101738.69, 'new_value': 108167.04}, {'field': 'amount', 'old_value': 166569.73, 'new_value': 171960.38}, {'field': 'count', 'old_value': 11367, 'new_value': 11746}, {'field': 'instoreAmount', 'old_value': 134228.1, 'new_value': 138363.01}, {'field': 'instoreCount', 'old_value': 8901, 'new_value': 9174}, {'field': 'onlineAmount', 'old_value': 36259.4, 'new_value': 37589.14}, {'field': 'onlineCount', 'old_value': 2466, 'new_value': 2572}]
2025-05-27 08:09:56,289 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-27 08:09:56,289 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 275531.91, 'new_value': 281651.83}, {'field': 'dailyBillAmount', 'old_value': 275531.91, 'new_value': 281651.83}, {'field': 'amount', 'old_value': 266943.66, 'new_value': 272787.96}, {'field': 'count', 'old_value': 7821, 'new_value': 8001}, {'field': 'instoreAmount', 'old_value': 268623.77, 'new_value': 274489.77}, {'field': 'instoreCount', 'old_value': 7821, 'new_value': 8001}]
2025-05-27 08:09:56,743 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-27 08:09:56,743 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80967.68000000001, 'new_value': 82954.42}, {'field': 'amount', 'old_value': 80964.43000000001, 'new_value': 82951.17}, {'field': 'count', 'old_value': 4378, 'new_value': 4496}, {'field': 'instoreAmount', 'old_value': 45510.75, 'new_value': 46021.89}, {'field': 'instoreCount', 'old_value': 2658, 'new_value': 2702}, {'field': 'onlineAmount', 'old_value': 35456.93, 'new_value': 36932.53}, {'field': 'onlineCount', 'old_value': 1720, 'new_value': 1794}]
2025-05-27 08:09:57,258 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-27 08:09:57,258 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 142279.55, 'new_value': 145767.26}, {'field': 'dailyBillAmount', 'old_value': 142279.55, 'new_value': 145767.26}, {'field': 'amount', 'old_value': 29115.04, 'new_value': 29951.190000000002}, {'field': 'count', 'old_value': 1044, 'new_value': 1077}, {'field': 'instoreAmount', 'old_value': 30130.88, 'new_value': 30967.84}, {'field': 'instoreCount', 'old_value': 1044, 'new_value': 1077}]
2025-05-27 08:09:57,680 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-27 08:09:57,680 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 94812.58, 'new_value': 97375.49}, {'field': 'count', 'old_value': 4731, 'new_value': 4852}, {'field': 'instoreAmount', 'old_value': 20305.49, 'new_value': 20736.68}, {'field': 'instoreCount', 'old_value': 1470, 'new_value': 1495}, {'field': 'onlineAmount', 'old_value': 76056.33, 'new_value': 78200.54}, {'field': 'onlineCount', 'old_value': 3261, 'new_value': 3357}]
2025-05-27 08:09:58,180 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-27 08:09:58,196 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109924.41, 'new_value': 113282.81}, {'field': 'amount', 'old_value': 109923.0, 'new_value': 113281.4}, {'field': 'count', 'old_value': 2828, 'new_value': 2902}, {'field': 'instoreAmount', 'old_value': 104829.52, 'new_value': 107490.12}, {'field': 'instoreCount', 'old_value': 2752, 'new_value': 2816}, {'field': 'onlineAmount', 'old_value': 6153.41, 'new_value': 6851.21}, {'field': 'onlineCount', 'old_value': 76, 'new_value': 86}]
2025-05-27 08:09:58,618 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-27 08:09:58,618 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81238.54000000001, 'new_value': 90911.28}, {'field': 'dailyBillAmount', 'old_value': 81238.54000000001, 'new_value': 90911.28}, {'field': 'amount', 'old_value': 164133.77, 'new_value': 169059.05}, {'field': 'count', 'old_value': 6773, 'new_value': 6960}, {'field': 'instoreAmount', 'old_value': 167464.75, 'new_value': 172412.93}, {'field': 'instoreCount', 'old_value': 6714, 'new_value': 6899}, {'field': 'onlineAmount', 'old_value': 2119.41, 'new_value': 2292.41}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 61}]
2025-05-27 08:09:59,086 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-27 08:09:59,086 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 159751.77, 'new_value': 185471.43}, {'field': 'dailyBillAmount', 'old_value': 159751.77, 'new_value': 185471.43}, {'field': 'amount', 'old_value': 119466.45, 'new_value': 122862.8}, {'field': 'count', 'old_value': 9719, 'new_value': 9952}, {'field': 'instoreAmount', 'old_value': 8726.82, 'new_value': 9063.65}, {'field': 'instoreCount', 'old_value': 492, 'new_value': 515}, {'field': 'onlineAmount', 'old_value': 115986.94, 'new_value': 119162.38}, {'field': 'onlineCount', 'old_value': 9227, 'new_value': 9437}]
2025-05-27 08:09:59,414 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-27 08:09:59,414 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 164508.25, 'new_value': 167296.7}, {'field': 'dailyBillAmount', 'old_value': 164508.25, 'new_value': 167296.7}, {'field': 'amount', 'old_value': 139685.54, 'new_value': 142196.61}, {'field': 'count', 'old_value': 4624, 'new_value': 4722}, {'field': 'instoreAmount', 'old_value': 76387.1, 'new_value': 77742.66}, {'field': 'instoreCount', 'old_value': 3336, 'new_value': 3403}, {'field': 'onlineAmount', 'old_value': 72293.72, 'new_value': 73658.23}, {'field': 'onlineCount', 'old_value': 1288, 'new_value': 1319}]
2025-05-27 08:09:59,868 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-27 08:09:59,868 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 119905.45, 'new_value': 121704.45}, {'field': 'amount', 'old_value': 119904.92, 'new_value': 121703.92}, {'field': 'count', 'old_value': 80, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 119905.45, 'new_value': 121704.45}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 83}]
2025-05-27 08:10:00,414 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-27 08:10:00,414 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58407.7, 'new_value': 60141.979999999996}, {'field': 'dailyBillAmount', 'old_value': 58407.7, 'new_value': 60141.979999999996}, {'field': 'amount', 'old_value': 77215.85, 'new_value': 79378.0}, {'field': 'count', 'old_value': 3018, 'new_value': 3111}, {'field': 'instoreAmount', 'old_value': 25008.78, 'new_value': 25533.79}, {'field': 'instoreCount', 'old_value': 1072, 'new_value': 1096}, {'field': 'onlineAmount', 'old_value': 53315.33, 'new_value': 54983.38}, {'field': 'onlineCount', 'old_value': 1946, 'new_value': 2015}]
2025-05-27 08:10:00,868 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-27 08:10:00,868 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98950.48, 'new_value': 100774.41}, {'field': 'dailyBillAmount', 'old_value': 98950.48, 'new_value': 100774.41}, {'field': 'amount', 'old_value': 101901.69, 'new_value': 103754.69}, {'field': 'count', 'old_value': 3579, 'new_value': 3653}, {'field': 'instoreAmount', 'old_value': 101901.69, 'new_value': 103754.69}, {'field': 'instoreCount', 'old_value': 3579, 'new_value': 3653}]
2025-05-27 08:10:01,352 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-27 08:10:01,352 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 305588.0, 'new_value': 319724.0}, {'field': 'dailyBillAmount', 'old_value': 305588.0, 'new_value': 319724.0}, {'field': 'amount', 'old_value': 345255.0, 'new_value': 355977.0}, {'field': 'count', 'old_value': 280, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 375434.0, 'new_value': 386156.0}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 285}]
2025-05-27 08:10:01,883 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-27 08:10:01,883 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 235864.95, 'new_value': 238704.85}, {'field': 'dailyBillAmount', 'old_value': 235864.95, 'new_value': 238704.85}, {'field': 'amount', 'old_value': 240199.31, 'new_value': 243049.21}, {'field': 'count', 'old_value': 470, 'new_value': 476}, {'field': 'instoreAmount', 'old_value': 243668.81, 'new_value': 246518.71}, {'field': 'instoreCount', 'old_value': 470, 'new_value': 476}]
2025-05-27 08:10:02,352 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-27 08:10:02,352 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43328.0, 'new_value': 46251.0}, {'field': 'amount', 'old_value': 43328.0, 'new_value': 46251.0}, {'field': 'count', 'old_value': 94, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 43328.0, 'new_value': 46251.0}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 100}]
2025-05-27 08:10:02,836 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-27 08:10:02,836 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76108.0, 'new_value': 79161.0}, {'field': 'dailyBillAmount', 'old_value': 76108.0, 'new_value': 79161.0}]
2025-05-27 08:10:03,305 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-27 08:10:03,305 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66471.0, 'new_value': 71557.0}, {'field': 'dailyBillAmount', 'old_value': 46317.0, 'new_value': 51403.0}, {'field': 'amount', 'old_value': 62229.0, 'new_value': 63167.0}, {'field': 'count', 'old_value': 85, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 62229.0, 'new_value': 63167.0}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 87}]
2025-05-27 08:10:03,805 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-27 08:10:03,805 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68262.1, 'new_value': 69643.1}, {'field': 'amount', 'old_value': 68259.9, 'new_value': 69640.9}, {'field': 'count', 'old_value': 183, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 68750.8, 'new_value': 70131.8}, {'field': 'instoreCount', 'old_value': 183, 'new_value': 188}]
2025-05-27 08:10:04,274 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-27 08:10:04,274 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 606288.0, 'new_value': 621285.0}, {'field': 'dailyBillAmount', 'old_value': 606288.0, 'new_value': 621285.0}]
2025-05-27 08:10:04,711 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-27 08:10:04,711 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'count', 'old_value': 77, 'new_value': 78}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 78}]
2025-05-27 08:10:05,149 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-27 08:10:05,149 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 250901.9, 'new_value': 265782.6}, {'field': 'dailyBillAmount', 'old_value': 250901.9, 'new_value': 265782.6}, {'field': 'amount', 'old_value': 338971.0, 'new_value': 353851.7}, {'field': 'count', 'old_value': 425, 'new_value': 442}, {'field': 'instoreAmount', 'old_value': 352317.36, 'new_value': 368004.56}, {'field': 'instoreCount', 'old_value': 425, 'new_value': 442}]
2025-05-27 08:10:05,618 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-27 08:10:05,618 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 112112.84, 'new_value': 113874.84}, {'field': 'dailyBillAmount', 'old_value': 112112.84, 'new_value': 113874.84}, {'field': 'amount', 'old_value': 51464.6, 'new_value': 53599.4}, {'field': 'count', 'old_value': 506, 'new_value': 532}, {'field': 'instoreAmount', 'old_value': 50410.68, 'new_value': 52293.48}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 470}, {'field': 'onlineAmount', 'old_value': 3616.02, 'new_value': 3868.02}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 62}]
2025-05-27 08:10:06,102 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-27 08:10:06,102 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14499.0, 'new_value': 15127.0}, {'field': 'amount', 'old_value': 14499.0, 'new_value': 15127.0}, {'field': 'count', 'old_value': 38, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 14499.0, 'new_value': 15127.0}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 39}]
2025-05-27 08:10:06,571 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-27 08:10:06,571 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35932.0, 'new_value': 38388.0}, {'field': 'dailyBillAmount', 'old_value': 35932.0, 'new_value': 38388.0}, {'field': 'amount', 'old_value': 41219.0, 'new_value': 43674.0}, {'field': 'count', 'old_value': 132, 'new_value': 136}, {'field': 'instoreAmount', 'old_value': 41219.0, 'new_value': 43674.0}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 136}]
2025-05-27 08:10:07,149 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-27 08:10:07,149 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 8290.0, 'new_value': 8452.0}, {'field': 'dailyBillAmount', 'old_value': 8290.0, 'new_value': 8452.0}, {'field': 'amount', 'old_value': 41185.0, 'new_value': 41831.0}, {'field': 'count', 'old_value': 129, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 41960.0, 'new_value': 42606.0}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 133}]
2025-05-27 08:10:07,711 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-27 08:10:07,711 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 820962.93, 'new_value': 833568.18}, {'field': 'dailyBillAmount', 'old_value': 820962.93, 'new_value': 833568.18}, {'field': 'amount', 'old_value': 51607.94, 'new_value': 52345.24}, {'field': 'count', 'old_value': 510, 'new_value': 518}, {'field': 'instoreAmount', 'old_value': 41242.25, 'new_value': 41935.37}, {'field': 'instoreCount', 'old_value': 361, 'new_value': 368}, {'field': 'onlineAmount', 'old_value': 11390.8, 'new_value': 11434.98}, {'field': 'onlineCount', 'old_value': 149, 'new_value': 150}]
2025-05-27 08:10:08,180 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-27 08:10:08,180 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81071.5, 'new_value': 81969.5}, {'field': 'amount', 'old_value': 80871.5, 'new_value': 81769.5}, {'field': 'count', 'old_value': 103, 'new_value': 108}, {'field': 'instoreAmount', 'old_value': 83418.0, 'new_value': 84316.0}, {'field': 'instoreCount', 'old_value': 103, 'new_value': 108}]
2025-05-27 08:10:08,602 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-27 08:10:08,602 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24130.47, 'new_value': 25639.79}, {'field': 'amount', 'old_value': 24129.77, 'new_value': 25639.09}, {'field': 'count', 'old_value': 97, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 24130.47, 'new_value': 25639.79}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 101}]
2025-05-27 08:10:09,008 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-27 08:10:09,008 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43583.0, 'new_value': 44429.0}, {'field': 'dailyBillAmount', 'old_value': 43583.0, 'new_value': 44429.0}, {'field': 'amount', 'old_value': 43782.0, 'new_value': 44645.0}, {'field': 'count', 'old_value': 107, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 45028.0, 'new_value': 45891.0}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 110}]
2025-05-27 08:10:09,446 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-27 08:10:09,446 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 354226.25, 'new_value': 357309.67}, {'field': 'dailyBillAmount', 'old_value': 327812.1, 'new_value': 330287.6}, {'field': 'amount', 'old_value': 352246.22, 'new_value': 355329.64}, {'field': 'count', 'old_value': 917, 'new_value': 936}, {'field': 'instoreAmount', 'old_value': 355734.27999999997, 'new_value': 358817.7}, {'field': 'instoreCount', 'old_value': 917, 'new_value': 936}]
2025-05-27 08:10:09,868 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-27 08:10:09,868 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'count', 'old_value': 317, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 74757.0, 'new_value': 74873.0}, {'field': 'instoreCount', 'old_value': 317, 'new_value': 318}]
2025-05-27 08:10:10,258 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-27 08:10:10,258 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 101344.79, 'new_value': 106413.79}, {'field': 'dailyBillAmount', 'old_value': 101344.79, 'new_value': 106413.79}, {'field': 'amount', 'old_value': 104291.31, 'new_value': 109360.31}, {'field': 'count', 'old_value': 630, 'new_value': 664}, {'field': 'instoreAmount', 'old_value': 104291.31, 'new_value': 109360.31}, {'field': 'instoreCount', 'old_value': 630, 'new_value': 664}]
2025-05-27 08:10:10,743 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-27 08:10:10,743 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99352.66, 'new_value': 105362.66}, {'field': 'dailyBillAmount', 'old_value': 99352.66, 'new_value': 105362.66}, {'field': 'amount', 'old_value': 36037.69, 'new_value': 37441.97}, {'field': 'count', 'old_value': 3536, 'new_value': 3674}, {'field': 'instoreAmount', 'old_value': 38333.65, 'new_value': 39817.85}, {'field': 'instoreCount', 'old_value': 3536, 'new_value': 3674}]
2025-05-27 08:10:11,164 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-27 08:10:11,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 623921.34, 'new_value': 637093.76}, {'field': 'dailyBillAmount', 'old_value': 623921.34, 'new_value': 637093.76}, {'field': 'amount', 'old_value': 639631.27, 'new_value': 652303.9400000001}, {'field': 'count', 'old_value': 6278, 'new_value': 6484}, {'field': 'instoreAmount', 'old_value': 487895.5, 'new_value': 496577.27}, {'field': 'instoreCount', 'old_value': 2440, 'new_value': 2498}, {'field': 'onlineAmount', 'old_value': 157055.49, 'new_value': 161279.29}, {'field': 'onlineCount', 'old_value': 3838, 'new_value': 3986}]
2025-05-27 08:10:11,711 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-27 08:10:11,711 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 199645.08, 'new_value': 202245.98}, {'field': 'amount', 'old_value': 199645.08, 'new_value': 202245.98}, {'field': 'count', 'old_value': 1337, 'new_value': 1361}, {'field': 'instoreAmount', 'old_value': 200080.08, 'new_value': 202680.98}, {'field': 'instoreCount', 'old_value': 1337, 'new_value': 1361}]
2025-05-27 08:10:12,164 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-27 08:10:12,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93969.6, 'new_value': 95638.82}, {'field': 'dailyBillAmount', 'old_value': 93969.6, 'new_value': 95638.82}, {'field': 'amount', 'old_value': 115170.4, 'new_value': 118093.28}, {'field': 'count', 'old_value': 5393, 'new_value': 5551}, {'field': 'instoreAmount', 'old_value': 58918.12, 'new_value': 60148.18}, {'field': 'instoreCount', 'old_value': 3095, 'new_value': 3176}, {'field': 'onlineAmount', 'old_value': 57522.08, 'new_value': 59223.91}, {'field': 'onlineCount', 'old_value': 2298, 'new_value': 2375}]
2025-05-27 08:10:12,586 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-27 08:10:12,586 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'count', 'old_value': 120, 'new_value': 121}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 121}]
2025-05-27 08:10:12,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-27 08:10:12,977 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66193.0, 'new_value': 66292.0}, {'field': 'amount', 'old_value': 66193.0, 'new_value': 66292.0}, {'field': 'count', 'old_value': 34, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 66193.0, 'new_value': 66292.0}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 35}]
2025-05-27 08:10:13,399 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-27 08:10:13,399 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122124.23, 'new_value': 124025.58}, {'field': 'dailyBillAmount', 'old_value': 122124.23, 'new_value': 124025.58}, {'field': 'amount', 'old_value': 59102.08, 'new_value': 59871.99}, {'field': 'count', 'old_value': 4214, 'new_value': 4275}, {'field': 'instoreAmount', 'old_value': 8179.56, 'new_value': 8222.14}, {'field': 'instoreCount', 'old_value': 345, 'new_value': 349}, {'field': 'onlineAmount', 'old_value': 50922.52, 'new_value': 51649.85}, {'field': 'onlineCount', 'old_value': 3869, 'new_value': 3926}]
2025-05-27 08:10:13,836 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-27 08:10:13,836 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 354780.63, 'new_value': 363036.83}, {'field': 'dailyBillAmount', 'old_value': 354780.63, 'new_value': 363036.83}, {'field': 'amount', 'old_value': 332324.79, 'new_value': 340917.67}, {'field': 'count', 'old_value': 2930, 'new_value': 3010}, {'field': 'instoreAmount', 'old_value': 242136.49, 'new_value': 248833.18}, {'field': 'instoreCount', 'old_value': 1244, 'new_value': 1285}, {'field': 'onlineAmount', 'old_value': 90189.52, 'new_value': 92086.62}, {'field': 'onlineCount', 'old_value': 1686, 'new_value': 1725}]
2025-05-27 08:10:14,274 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-27 08:10:14,274 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 395883.89, 'new_value': 404207.87}, {'field': 'dailyBillAmount', 'old_value': 395883.89, 'new_value': 404207.87}, {'field': 'amount', 'old_value': 406171.46, 'new_value': 414259.26}, {'field': 'count', 'old_value': 2461, 'new_value': 2519}, {'field': 'instoreAmount', 'old_value': 369882.76, 'new_value': 377218.56}, {'field': 'instoreCount', 'old_value': 2077, 'new_value': 2124}, {'field': 'onlineAmount', 'old_value': 42430.1, 'new_value': 43346.9}, {'field': 'onlineCount', 'old_value': 384, 'new_value': 395}]
2025-05-27 08:10:14,696 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-27 08:10:14,696 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 997924.29, 'new_value': 1015996.22}, {'field': 'dailyBillAmount', 'old_value': 997924.29, 'new_value': 1015996.22}, {'field': 'amount', 'old_value': 1106588.6, 'new_value': 1126472.06}, {'field': 'count', 'old_value': 6160, 'new_value': 6304}, {'field': 'instoreAmount', 'old_value': 836917.74, 'new_value': 848604.72}, {'field': 'instoreCount', 'old_value': 3378, 'new_value': 3429}, {'field': 'onlineAmount', 'old_value': 278484.8, 'new_value': 286747.22}, {'field': 'onlineCount', 'old_value': 2782, 'new_value': 2875}]
2025-05-27 08:10:15,164 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-27 08:10:15,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 329803.15, 'new_value': 335809.75}, {'field': 'dailyBillAmount', 'old_value': 329803.15, 'new_value': 335809.75}, {'field': 'amount', 'old_value': 468669.51, 'new_value': 477915.11}, {'field': 'count', 'old_value': 2206, 'new_value': 2262}, {'field': 'instoreAmount', 'old_value': 440792.16, 'new_value': 448431.26}, {'field': 'instoreCount', 'old_value': 1773, 'new_value': 1807}, {'field': 'onlineAmount', 'old_value': 28637.55, 'new_value': 30244.05}, {'field': 'onlineCount', 'old_value': 433, 'new_value': 455}]
2025-05-27 08:10:15,571 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-27 08:10:15,571 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 388032.58, 'new_value': 393265.84}, {'field': 'dailyBillAmount', 'old_value': 388032.58, 'new_value': 393265.84}, {'field': 'amount', 'old_value': 364853.6, 'new_value': 369532.1}, {'field': 'count', 'old_value': 1644, 'new_value': 1671}, {'field': 'instoreAmount', 'old_value': 371525.2, 'new_value': 376203.7}, {'field': 'instoreCount', 'old_value': 1644, 'new_value': 1671}]
2025-05-27 08:10:16,102 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-27 08:10:16,102 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 799400.25, 'new_value': 815126.85}, {'field': 'amount', 'old_value': 799399.55, 'new_value': 815125.75}, {'field': 'count', 'old_value': 6363, 'new_value': 6519}, {'field': 'instoreAmount', 'old_value': 799400.25, 'new_value': 815126.85}, {'field': 'instoreCount', 'old_value': 6363, 'new_value': 6519}]
2025-05-27 08:10:16,586 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-27 08:10:16,602 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 653875.99, 'new_value': 673436.72}, {'field': 'dailyBillAmount', 'old_value': 653875.99, 'new_value': 673436.72}, {'field': 'amount', 'old_value': 806539.31, 'new_value': 830409.31}, {'field': 'count', 'old_value': 5611, 'new_value': 5800}, {'field': 'instoreAmount', 'old_value': 447966.6, 'new_value': 457467.6}, {'field': 'instoreCount', 'old_value': 2359, 'new_value': 2411}, {'field': 'onlineAmount', 'old_value': 368732.9, 'new_value': 383515.7}, {'field': 'onlineCount', 'old_value': 3252, 'new_value': 3389}]
2025-05-27 08:10:17,055 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-27 08:10:17,055 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 420964.83, 'new_value': 430387.01}, {'field': 'dailyBillAmount', 'old_value': 420964.83, 'new_value': 430387.01}, {'field': 'amount', 'old_value': 490168.48, 'new_value': 499692.94}, {'field': 'count', 'old_value': 5357, 'new_value': 5494}, {'field': 'instoreAmount', 'old_value': 338331.82, 'new_value': 343098.82}, {'field': 'instoreCount', 'old_value': 2319, 'new_value': 2357}, {'field': 'onlineAmount', 'old_value': 153827.51, 'new_value': 158635.96}, {'field': 'onlineCount', 'old_value': 3038, 'new_value': 3137}]
2025-05-27 08:10:17,508 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-27 08:10:17,508 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 520101.22, 'new_value': 532633.16}, {'field': 'dailyBillAmount', 'old_value': 520101.22, 'new_value': 532633.16}, {'field': 'amount', 'old_value': 526689.83, 'new_value': 539757.37}, {'field': 'count', 'old_value': 4961, 'new_value': 5121}, {'field': 'instoreAmount', 'old_value': 459892.81, 'new_value': 471126.79}, {'field': 'instoreCount', 'old_value': 2648, 'new_value': 2732}, {'field': 'onlineAmount', 'old_value': 67871.11, 'new_value': 69750.95}, {'field': 'onlineCount', 'old_value': 2313, 'new_value': 2389}]
2025-05-27 08:10:17,930 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-27 08:10:17,930 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 123150.8, 'new_value': 126422.8}, {'field': 'amount', 'old_value': 123150.3, 'new_value': 126422.3}, {'field': 'count', 'old_value': 581, 'new_value': 597}, {'field': 'instoreAmount', 'old_value': 123150.8, 'new_value': 126422.8}, {'field': 'instoreCount', 'old_value': 581, 'new_value': 597}]
2025-05-27 08:10:18,336 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-27 08:10:18,336 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 361665.25, 'new_value': 366312.68}, {'field': 'dailyBillAmount', 'old_value': 361665.25, 'new_value': 366312.68}, {'field': 'amount', 'old_value': -283165.88, 'new_value': -287528.18}, {'field': 'count', 'old_value': 973, 'new_value': 996}, {'field': 'instoreAmount', 'old_value': 6971.1, 'new_value': 7178.1}, {'field': 'instoreCount', 'old_value': 323, 'new_value': 329}, {'field': 'onlineAmount', 'old_value': 20131.67, 'new_value': 20607.37}, {'field': 'onlineCount', 'old_value': 650, 'new_value': 667}]
2025-05-27 08:10:18,821 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-27 08:10:18,821 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 612370.31, 'new_value': 628109.92}, {'field': 'dailyBillAmount', 'old_value': 612370.31, 'new_value': 628109.92}, {'field': 'amount', 'old_value': 461689.2, 'new_value': 468814.51}, {'field': 'count', 'old_value': 1947, 'new_value': 1981}, {'field': 'instoreAmount', 'old_value': 461689.2, 'new_value': 468814.51}, {'field': 'instoreCount', 'old_value': 1947, 'new_value': 1981}]
2025-05-27 08:10:19,274 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-27 08:10:19,274 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 399396.37, 'new_value': 412055.37}, {'field': 'dailyBillAmount', 'old_value': 399396.37, 'new_value': 412055.37}, {'field': 'amount', 'old_value': 165875.2, 'new_value': 172179.7}, {'field': 'count', 'old_value': 687, 'new_value': 711}, {'field': 'instoreAmount', 'old_value': 171985.3, 'new_value': 178147.1}, {'field': 'instoreCount', 'old_value': 665, 'new_value': 687}, {'field': 'onlineAmount', 'old_value': 1505.1, 'new_value': 1647.8}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 24}]
2025-05-27 08:10:19,742 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-27 08:10:19,742 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 317380.03, 'new_value': 321249.81}, {'field': 'dailyBillAmount', 'old_value': 317380.03, 'new_value': 321249.81}, {'field': 'amount', 'old_value': 307647.95, 'new_value': 311496.69}, {'field': 'count', 'old_value': 2035, 'new_value': 2073}, {'field': 'instoreAmount', 'old_value': 289542.71, 'new_value': 292789.41000000003}, {'field': 'instoreCount', 'old_value': 1552, 'new_value': 1570}, {'field': 'onlineAmount', 'old_value': 18269.39, 'new_value': 18871.43}, {'field': 'onlineCount', 'old_value': 483, 'new_value': 503}]
2025-05-27 08:10:20,164 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-27 08:10:20,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335824.52999999997, 'new_value': 343600.48}, {'field': 'dailyBillAmount', 'old_value': 335824.52999999997, 'new_value': 343600.48}, {'field': 'amount', 'old_value': 141711.83, 'new_value': 145079.61000000002}, {'field': 'count', 'old_value': 2392, 'new_value': 2482}, {'field': 'instoreAmount', 'old_value': 81931.03, 'new_value': 83879.12}, {'field': 'instoreCount', 'old_value': 628, 'new_value': 656}, {'field': 'onlineAmount', 'old_value': 59784.11, 'new_value': 61203.8}, {'field': 'onlineCount', 'old_value': 1764, 'new_value': 1826}]
2025-05-27 08:10:20,586 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-27 08:10:20,586 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26262.0, 'new_value': 27822.0}, {'field': 'dailyBillAmount', 'old_value': 26262.0, 'new_value': 27822.0}]
2025-05-27 08:10:21,071 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-27 08:10:21,071 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 139482.29, 'new_value': 143802.98}, {'field': 'amount', 'old_value': 139471.31, 'new_value': 143791.36}, {'field': 'count', 'old_value': 6418, 'new_value': 6662}, {'field': 'instoreAmount', 'old_value': 50303.95, 'new_value': 51511.39}, {'field': 'instoreCount', 'old_value': 1990, 'new_value': 2043}, {'field': 'onlineAmount', 'old_value': 96629.99, 'new_value': 99859.44}, {'field': 'onlineCount', 'old_value': 4428, 'new_value': 4619}]
2025-05-27 08:10:21,508 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-27 08:10:21,508 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43930.9, 'new_value': 44649.9}, {'field': 'amount', 'old_value': 43930.9, 'new_value': 44649.9}, {'field': 'count', 'old_value': 199, 'new_value': 203}, {'field': 'instoreAmount', 'old_value': 43930.9, 'new_value': 44649.9}, {'field': 'instoreCount', 'old_value': 199, 'new_value': 203}]
2025-05-27 08:10:21,961 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-27 08:10:21,961 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 402590.51, 'new_value': 413063.64}, {'field': 'dailyBillAmount', 'old_value': 402590.51, 'new_value': 413063.64}, {'field': 'amount', 'old_value': 163174.5, 'new_value': 167144.2}, {'field': 'count', 'old_value': 3058, 'new_value': 3140}, {'field': 'instoreAmount', 'old_value': 164457.8, 'new_value': 168440.1}, {'field': 'instoreCount', 'old_value': 3058, 'new_value': 3140}]
2025-05-27 08:10:22,461 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-27 08:10:22,461 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 155641.44, 'new_value': 164348.06}, {'field': 'amount', 'old_value': 155640.24, 'new_value': 164346.86000000002}, {'field': 'count', 'old_value': 3715, 'new_value': 3927}, {'field': 'instoreAmount', 'old_value': 155898.92, 'new_value': 164605.54}, {'field': 'instoreCount', 'old_value': 3715, 'new_value': 3927}]
2025-05-27 08:10:22,883 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-27 08:10:22,883 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31229.29, 'new_value': 32268.34}, {'field': 'amount', 'old_value': 31224.51, 'new_value': 32262.69}, {'field': 'count', 'old_value': 1927, 'new_value': 1997}, {'field': 'instoreAmount', 'old_value': 16113.94, 'new_value': 16476.87}, {'field': 'instoreCount', 'old_value': 805, 'new_value': 822}, {'field': 'onlineAmount', 'old_value': 15656.19, 'new_value': 16363.76}, {'field': 'onlineCount', 'old_value': 1122, 'new_value': 1175}]
2025-05-27 08:10:23,430 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-27 08:10:23,430 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53152.6, 'new_value': 53880.6}, {'field': 'amount', 'old_value': 53152.6, 'new_value': 53880.6}, {'field': 'count', 'old_value': 136, 'new_value': 139}, {'field': 'instoreAmount', 'old_value': 53152.6, 'new_value': 53880.6}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 139}]
2025-05-27 08:10:23,883 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-27 08:10:23,883 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 192419.03, 'new_value': 197263.43}, {'field': 'dailyBillAmount', 'old_value': 159639.5, 'new_value': 163818.1}, {'field': 'amount', 'old_value': 192418.35, 'new_value': 197262.75}, {'field': 'count', 'old_value': 2707, 'new_value': 2790}, {'field': 'instoreAmount', 'old_value': 183865.9, 'new_value': 188321.3}, {'field': 'instoreCount', 'old_value': 2356, 'new_value': 2423}, {'field': 'onlineAmount', 'old_value': 8805.53, 'new_value': 9194.53}, {'field': 'onlineCount', 'old_value': 351, 'new_value': 367}]
2025-05-27 08:10:24,352 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-27 08:10:24,352 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29990.34, 'new_value': 30694.16}, {'field': 'amount', 'old_value': 29989.54, 'new_value': 30693.36}, {'field': 'count', 'old_value': 1287, 'new_value': 1318}, {'field': 'instoreAmount', 'old_value': 25060.14, 'new_value': 25583.06}, {'field': 'instoreCount', 'old_value': 1147, 'new_value': 1173}, {'field': 'onlineAmount', 'old_value': 5015.9, 'new_value': 5196.8}, {'field': 'onlineCount', 'old_value': 140, 'new_value': 145}]
2025-05-27 08:10:24,774 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-27 08:10:24,774 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 398615.60000000003, 'new_value': 406345.27}, {'field': 'dailyBillAmount', 'old_value': 398615.60000000003, 'new_value': 406345.27}, {'field': 'amount', 'old_value': 513286.12, 'new_value': 524677.32}, {'field': 'count', 'old_value': 5237, 'new_value': 5379}, {'field': 'instoreAmount', 'old_value': 483451.57, 'new_value': 493769.45}, {'field': 'instoreCount', 'old_value': 3619, 'new_value': 3712}, {'field': 'onlineAmount', 'old_value': 39847.18, 'new_value': 40920.76}, {'field': 'onlineCount', 'old_value': 1618, 'new_value': 1667}]
2025-05-27 08:10:25,164 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-27 08:10:25,164 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 154712.42, 'new_value': 158759.68}, {'field': 'dailyBillAmount', 'old_value': 154712.42, 'new_value': 158759.68}, {'field': 'amount', 'old_value': 38468.96, 'new_value': 39545.65}, {'field': 'count', 'old_value': 621, 'new_value': 647}, {'field': 'instoreAmount', 'old_value': 25115.25, 'new_value': 25983.33}, {'field': 'instoreCount', 'old_value': 332, 'new_value': 349}, {'field': 'onlineAmount', 'old_value': 14243.85, 'new_value': 14452.46}, {'field': 'onlineCount', 'old_value': 289, 'new_value': 298}]
2025-05-27 08:10:25,633 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-27 08:10:25,633 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151861.19, 'new_value': 157009.71}, {'field': 'dailyBillAmount', 'old_value': 136939.28, 'new_value': 142089.43}, {'field': 'amount', 'old_value': 151858.62, 'new_value': 157007.13999999998}, {'field': 'count', 'old_value': 8564, 'new_value': 8872}, {'field': 'instoreAmount', 'old_value': 93548.24, 'new_value': 96345.04}, {'field': 'instoreCount', 'old_value': 5155, 'new_value': 5316}, {'field': 'onlineAmount', 'old_value': 60163.9, 'new_value': 62549.96}, {'field': 'onlineCount', 'old_value': 3409, 'new_value': 3556}]
2025-05-27 08:10:26,055 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-27 08:10:26,055 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81211.22, 'new_value': 82938.1}, {'field': 'amount', 'old_value': 81200.35, 'new_value': 82926.31}, {'field': 'count', 'old_value': 5202, 'new_value': 5316}, {'field': 'instoreAmount', 'old_value': 36223.82, 'new_value': 36906.82}, {'field': 'instoreCount', 'old_value': 2088, 'new_value': 2137}, {'field': 'onlineAmount', 'old_value': 47450.35, 'new_value': 48607.17}, {'field': 'onlineCount', 'old_value': 3114, 'new_value': 3179}]
2025-05-27 08:10:26,539 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-27 08:10:26,539 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 160398.13, 'new_value': 163148.89}, {'field': 'count', 'old_value': 1600, 'new_value': 1634}, {'field': 'instoreAmount', 'old_value': 160663.41, 'new_value': 163440.37}, {'field': 'instoreCount', 'old_value': 1600, 'new_value': 1634}]
2025-05-27 08:10:27,024 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-27 08:10:27,024 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129801.94, 'new_value': 133311.92}, {'field': 'dailyBillAmount', 'old_value': 134254.21, 'new_value': 138031.27}, {'field': 'amount', 'old_value': 129796.13, 'new_value': 133306.11}, {'field': 'count', 'old_value': 2683, 'new_value': 2779}, {'field': 'instoreAmount', 'old_value': 124111.87, 'new_value': 127319.06}, {'field': 'instoreCount', 'old_value': 2243, 'new_value': 2312}, {'field': 'onlineAmount', 'old_value': 5798.39, 'new_value': 6114.68}, {'field': 'onlineCount', 'old_value': 440, 'new_value': 467}]
2025-05-27 08:10:27,492 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-27 08:10:27,492 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 201179.12, 'new_value': 216938.93}, {'field': 'dailyBillAmount', 'old_value': 201179.12, 'new_value': 216938.93}, {'field': 'amount', 'old_value': 25973.61, 'new_value': 27452.55}, {'field': 'count', 'old_value': 1017, 'new_value': 1064}, {'field': 'instoreAmount', 'old_value': 29718.6, 'new_value': 31268.04}, {'field': 'instoreCount', 'old_value': 1017, 'new_value': 1064}]
2025-05-27 08:10:27,946 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-27 08:10:27,946 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 545237.16, 'new_value': 558440.96}, {'field': 'dailyBillAmount', 'old_value': 545237.16, 'new_value': 558440.96}, {'field': 'amount', 'old_value': 54356.36, 'new_value': 56764.26}, {'field': 'count', 'old_value': 263, 'new_value': 274}, {'field': 'instoreAmount', 'old_value': 54582.36, 'new_value': 56990.26}, {'field': 'instoreCount', 'old_value': 263, 'new_value': 274}]
2025-05-27 08:10:28,367 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-27 08:10:28,367 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 17240.06, 'new_value': 17793.71}, {'field': 'count', 'old_value': 884, 'new_value': 914}, {'field': 'onlineAmount', 'old_value': 17461.72, 'new_value': 18015.37}, {'field': 'onlineCount', 'old_value': 884, 'new_value': 914}]
2025-05-27 08:10:28,774 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-27 08:10:28,774 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 316481.15, 'new_value': 328768.22}, {'field': 'amount', 'old_value': 316327.37, 'new_value': 328614.44}, {'field': 'count', 'old_value': 3227, 'new_value': 3369}, {'field': 'instoreAmount', 'old_value': 301482.9, 'new_value': 313048.4}, {'field': 'instoreCount', 'old_value': 2724, 'new_value': 2843}, {'field': 'onlineAmount', 'old_value': 20627.74, 'new_value': 21458.15}, {'field': 'onlineCount', 'old_value': 503, 'new_value': 526}]
2025-05-27 08:10:29,227 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-27 08:10:29,227 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 177444.1, 'new_value': 184189.01}, {'field': 'dailyBillAmount', 'old_value': 173643.63, 'new_value': 180388.54}, {'field': 'amount', 'old_value': 131336.37, 'new_value': 135978.29}, {'field': 'count', 'old_value': 4752, 'new_value': 4937}, {'field': 'instoreAmount', 'old_value': 55514.67, 'new_value': 56710.810000000005}, {'field': 'instoreCount', 'old_value': 1900, 'new_value': 1940}, {'field': 'onlineAmount', 'old_value': 77642.82, 'new_value': 81107.5}, {'field': 'onlineCount', 'old_value': 2852, 'new_value': 2997}]
2025-05-27 08:10:29,617 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-27 08:10:29,617 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 5108.72, 'new_value': 5689.4800000000005}, {'field': 'count', 'old_value': 233, 'new_value': 258}, {'field': 'instoreAmount', 'old_value': 5108.92, 'new_value': 5689.68}, {'field': 'instoreCount', 'old_value': 233, 'new_value': 258}]
2025-05-27 08:10:30,039 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-27 08:10:30,039 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 6183.5199999999995, 'new_value': 6525.0}, {'field': 'count', 'old_value': 267, 'new_value': 281}, {'field': 'onlineAmount', 'old_value': 6183.5199999999995, 'new_value': 6525.0}, {'field': 'onlineCount', 'old_value': 267, 'new_value': 281}]
2025-05-27 08:10:30,508 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-27 08:10:30,508 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 106889.47, 'new_value': 109261.36}, {'field': 'dailyBillAmount', 'old_value': 53284.14, 'new_value': 53839.94}, {'field': 'amount', 'old_value': 106888.88, 'new_value': 109260.77}, {'field': 'count', 'old_value': 2650, 'new_value': 2719}, {'field': 'instoreAmount', 'old_value': 57746.55, 'new_value': 58533.35}, {'field': 'instoreCount', 'old_value': 1407, 'new_value': 1433}, {'field': 'onlineAmount', 'old_value': 52080.05, 'new_value': 53731.14}, {'field': 'onlineCount', 'old_value': 1243, 'new_value': 1286}]
2025-05-27 08:10:31,024 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-27 08:10:31,024 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53400.91, 'new_value': 57343.97}, {'field': 'amount', 'old_value': 53400.91, 'new_value': 57343.97}, {'field': 'count', 'old_value': 2012, 'new_value': 2195}, {'field': 'instoreAmount', 'old_value': 54071.57, 'new_value': 58026.16}, {'field': 'instoreCount', 'old_value': 2012, 'new_value': 2195}]
2025-05-27 08:10:31,477 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-27 08:10:31,477 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51034.02, 'new_value': 52847.15}, {'field': 'dailyBillAmount', 'old_value': 51034.02, 'new_value': 52847.15}, {'field': 'amount', 'old_value': 39147.56, 'new_value': 40334.64}, {'field': 'count', 'old_value': 1827, 'new_value': 1906}, {'field': 'instoreAmount', 'old_value': 21035.11, 'new_value': 21260.81}, {'field': 'instoreCount', 'old_value': 721, 'new_value': 730}, {'field': 'onlineAmount', 'old_value': 18194.67, 'new_value': 19156.05}, {'field': 'onlineCount', 'old_value': 1106, 'new_value': 1176}]
2025-05-27 08:10:31,914 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-27 08:10:31,914 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83019.52, 'new_value': 85868.25}, {'field': 'amount', 'old_value': 83019.52, 'new_value': 85868.25}, {'field': 'count', 'old_value': 2533, 'new_value': 2615}, {'field': 'instoreAmount', 'old_value': 33667.020000000004, 'new_value': 34851.35}, {'field': 'instoreCount', 'old_value': 1266, 'new_value': 1318}, {'field': 'onlineAmount', 'old_value': 49453.99, 'new_value': 51118.39}, {'field': 'onlineCount', 'old_value': 1267, 'new_value': 1297}]
2025-05-27 08:10:32,414 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-27 08:10:32,414 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49817.62, 'new_value': 52071.41}, {'field': 'amount', 'old_value': 49816.72, 'new_value': 52070.51}, {'field': 'count', 'old_value': 1201, 'new_value': 1241}, {'field': 'instoreAmount', 'old_value': 38307.7, 'new_value': 40389.22}, {'field': 'instoreCount', 'old_value': 958, 'new_value': 992}, {'field': 'onlineAmount', 'old_value': 11985.93, 'new_value': 12158.2}, {'field': 'onlineCount', 'old_value': 243, 'new_value': 249}]
2025-05-27 08:10:32,821 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-27 08:10:32,821 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231916.53, 'new_value': 242746.71}, {'field': 'dailyBillAmount', 'old_value': 231916.53, 'new_value': 242746.71}, {'field': 'amount', 'old_value': 156326.32, 'new_value': 163873.82}, {'field': 'count', 'old_value': 3958, 'new_value': 4129}, {'field': 'instoreAmount', 'old_value': 99444.62, 'new_value': 104108.12}, {'field': 'instoreCount', 'old_value': 1968, 'new_value': 2052}, {'field': 'onlineAmount', 'old_value': 70237.04, 'new_value': 73607.04}, {'field': 'onlineCount', 'old_value': 1990, 'new_value': 2077}]
2025-05-27 08:10:33,320 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-27 08:10:33,320 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 699544.89, 'new_value': 719178.78}, {'field': 'dailyBillAmount', 'old_value': 699544.89, 'new_value': 719178.78}, {'field': 'amount', 'old_value': 691058.4, 'new_value': 705617.7}, {'field': 'count', 'old_value': 4120, 'new_value': 4221}, {'field': 'instoreAmount', 'old_value': 492876.6, 'new_value': 501019.1}, {'field': 'instoreCount', 'old_value': 3191, 'new_value': 3262}, {'field': 'onlineAmount', 'old_value': 198184.7, 'new_value': 204601.5}, {'field': 'onlineCount', 'old_value': 929, 'new_value': 959}]
2025-05-27 08:10:33,758 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-27 08:10:33,758 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1061945.02, 'new_value': 1090093.22}, {'field': 'amount', 'old_value': 1061944.52, 'new_value': 1090092.72}, {'field': 'count', 'old_value': 3745, 'new_value': 3847}, {'field': 'instoreAmount', 'old_value': 1061945.02, 'new_value': 1090093.22}, {'field': 'instoreCount', 'old_value': 3745, 'new_value': 3847}]
2025-05-27 08:10:34,211 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-27 08:10:34,227 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 603564.03, 'new_value': 621624.52}, {'field': 'dailyBillAmount', 'old_value': 536113.76, 'new_value': 550436.38}, {'field': 'amount', 'old_value': 603564.03, 'new_value': 621624.52}, {'field': 'count', 'old_value': 3716, 'new_value': 3866}, {'field': 'instoreAmount', 'old_value': 551575.01, 'new_value': 566542.31}, {'field': 'instoreCount', 'old_value': 2363, 'new_value': 2428}, {'field': 'onlineAmount', 'old_value': 52361.0, 'new_value': 55454.19}, {'field': 'onlineCount', 'old_value': 1353, 'new_value': 1438}]
2025-05-27 08:10:34,696 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-27 08:10:34,696 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 602427.75, 'new_value': 613611.78}, {'field': 'dailyBillAmount', 'old_value': 585581.26, 'new_value': 596484.11}, {'field': 'amount', 'old_value': 602421.16, 'new_value': 613605.19}, {'field': 'count', 'old_value': 1476, 'new_value': 1508}, {'field': 'instoreAmount', 'old_value': 560128.9, 'new_value': 570322.9}, {'field': 'instoreCount', 'old_value': 1145, 'new_value': 1171}, {'field': 'onlineAmount', 'old_value': 42426.13, 'new_value': 43416.16}, {'field': 'onlineCount', 'old_value': 331, 'new_value': 337}]
2025-05-27 08:10:35,117 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-27 08:10:35,117 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 738268.16, 'new_value': 755502.61}, {'field': 'amount', 'old_value': 738267.48, 'new_value': 755501.29}, {'field': 'count', 'old_value': 3895, 'new_value': 4044}, {'field': 'instoreAmount', 'old_value': 693955.46, 'new_value': 708508.46}, {'field': 'instoreCount', 'old_value': 2570, 'new_value': 2629}, {'field': 'onlineAmount', 'old_value': 44457.5, 'new_value': 47138.95}, {'field': 'onlineCount', 'old_value': 1325, 'new_value': 1415}]
2025-05-27 08:10:35,570 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-27 08:10:35,570 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 807984.42, 'new_value': 827626.77}, {'field': 'dailyBillAmount', 'old_value': 807984.42, 'new_value': 827626.77}, {'field': 'amount', 'old_value': 734295.85, 'new_value': 756121.14}, {'field': 'count', 'old_value': 3670, 'new_value': 3776}, {'field': 'instoreAmount', 'old_value': 672907.05, 'new_value': 692567.55}, {'field': 'instoreCount', 'old_value': 3037, 'new_value': 3114}, {'field': 'onlineAmount', 'old_value': 62181.22, 'new_value': 64386.89}, {'field': 'onlineCount', 'old_value': 633, 'new_value': 662}]
2025-05-27 08:10:35,992 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-27 08:10:35,992 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 203077.84, 'new_value': 208976.84}, {'field': 'dailyBillAmount', 'old_value': 201671.29, 'new_value': 207570.29}, {'field': 'amount', 'old_value': 199266.66, 'new_value': 205165.66}, {'field': 'count', 'old_value': 291, 'new_value': 295}, {'field': 'instoreAmount', 'old_value': 199266.66, 'new_value': 205165.66}, {'field': 'instoreCount', 'old_value': 291, 'new_value': 295}]
2025-05-27 08:10:36,477 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-27 08:10:36,492 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 171015.03, 'new_value': 173418.33000000002}, {'field': 'dailyBillAmount', 'old_value': 171015.03, 'new_value': 173418.33000000002}, {'field': 'amount', 'old_value': 149464.15, 'new_value': 153735.95}, {'field': 'count', 'old_value': 247, 'new_value': 252}, {'field': 'instoreAmount', 'old_value': 146522.1, 'new_value': 150793.9}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 232}]
2025-05-27 08:10:36,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-27 08:10:36,977 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22641.09, 'new_value': 22889.82}, {'field': 'amount', 'old_value': 22641.09, 'new_value': 22889.82}, {'field': 'count', 'old_value': 471, 'new_value': 478}, {'field': 'instoreAmount', 'old_value': 22641.09, 'new_value': 22889.82}, {'field': 'instoreCount', 'old_value': 471, 'new_value': 478}]
2025-05-27 08:10:37,430 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-27 08:10:37,430 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94896.8, 'new_value': 97402.76}, {'field': 'amount', 'old_value': 94896.8, 'new_value': 97402.76}, {'field': 'count', 'old_value': 802, 'new_value': 825}, {'field': 'instoreAmount', 'old_value': 95447.64, 'new_value': 97953.6}, {'field': 'instoreCount', 'old_value': 802, 'new_value': 825}]
2025-05-27 08:10:38,039 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-27 08:10:38,039 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 319491.79, 'new_value': 329841.89}, {'field': 'dailyBillAmount', 'old_value': 319491.79, 'new_value': 329841.89}, {'field': 'amount', 'old_value': 336732.11, 'new_value': 349041.57}, {'field': 'count', 'old_value': 9235, 'new_value': 9589}, {'field': 'instoreAmount', 'old_value': 318702.34, 'new_value': 329717.09}, {'field': 'instoreCount', 'old_value': 8315, 'new_value': 8610}, {'field': 'onlineAmount', 'old_value': 23208.04, 'new_value': 24540.25}, {'field': 'onlineCount', 'old_value': 920, 'new_value': 979}]
2025-05-27 08:10:38,570 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-27 08:10:38,570 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 108976.18, 'new_value': 115678.9}, {'field': 'dailyBillAmount', 'old_value': 108976.18, 'new_value': 115678.9}, {'field': 'amount', 'old_value': 111280.18, 'new_value': 120796.9}, {'field': 'count', 'old_value': 84, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 111280.18, 'new_value': 120796.9}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 88}]
2025-05-27 08:10:38,945 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-27 08:10:38,961 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 826844.23, 'new_value': 847619.89}, {'field': 'dailyBillAmount', 'old_value': 826844.23, 'new_value': 847619.89}, {'field': 'amount', 'old_value': 741871.17, 'new_value': 752664.85}, {'field': 'count', 'old_value': 1912, 'new_value': 1969}, {'field': 'instoreAmount', 'old_value': 778163.95, 'new_value': 793111.15}, {'field': 'instoreCount', 'old_value': 1590, 'new_value': 1635}, {'field': 'onlineAmount', 'old_value': 7489.21, 'new_value': 7751.91}, {'field': 'onlineCount', 'old_value': 322, 'new_value': 334}]
2025-05-27 08:10:39,445 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-27 08:10:39,445 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1254174.6, 'new_value': 1292980.8}, {'field': 'amount', 'old_value': 1254174.6, 'new_value': 1292980.8}, {'field': 'count', 'old_value': 4038, 'new_value': 4151}, {'field': 'instoreAmount', 'old_value': 1255385.6, 'new_value': 1294191.8}, {'field': 'instoreCount', 'old_value': 4038, 'new_value': 4151}]
2025-05-27 08:10:39,930 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-27 08:10:39,930 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 835195.69, 'new_value': 1012081.4199999999}, {'field': 'dailyBillAmount', 'old_value': 835195.69, 'new_value': 1012081.4199999999}, {'field': 'amount', 'old_value': 771741.02, 'new_value': 805109.59}, {'field': 'count', 'old_value': 2759, 'new_value': 2866}, {'field': 'instoreAmount', 'old_value': 752092.98, 'new_value': 785630.91}, {'field': 'instoreCount', 'old_value': 1696, 'new_value': 1769}, {'field': 'onlineAmount', 'old_value': 32778.92, 'new_value': 33660.32}, {'field': 'onlineCount', 'old_value': 1063, 'new_value': 1097}]
2025-05-27 08:10:40,352 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-27 08:10:40,352 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1821067.29, 'new_value': 1878971.07}, {'field': 'dailyBillAmount', 'old_value': 1821067.29, 'new_value': 1878971.07}, {'field': 'amount', 'old_value': 1873291.0, 'new_value': 1924740.0}, {'field': 'count', 'old_value': 4910, 'new_value': 5020}, {'field': 'instoreAmount', 'old_value': 1873291.0, 'new_value': 1926366.0}, {'field': 'instoreCount', 'old_value': 4910, 'new_value': 5020}]
2025-05-27 08:10:40,805 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-27 08:10:40,805 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'amount', 'old_value': 301413.91000000003, 'new_value': 307315.45}, {'field': 'count', 'old_value': 1612, 'new_value': 1650}, {'field': 'instoreAmount', 'old_value': 292860.4, 'new_value': 298134.7}, {'field': 'instoreCount', 'old_value': 1367, 'new_value': 1392}, {'field': 'onlineAmount', 'old_value': 15333.39, 'new_value': 16012.03}, {'field': 'onlineCount', 'old_value': 245, 'new_value': 258}]
2025-05-27 08:10:41,242 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-27 08:10:41,242 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 985202.1900000001, 'new_value': 1013964.0700000001}, {'field': 'dailyBillAmount', 'old_value': 985202.1900000001, 'new_value': 1013964.0700000001}, {'field': 'amount', 'old_value': 1042269.26, 'new_value': 1072141.1400000001}, {'field': 'count', 'old_value': 4356, 'new_value': 4499}, {'field': 'instoreAmount', 'old_value': 1042269.71, 'new_value': 1072141.79}, {'field': 'instoreCount', 'old_value': 4356, 'new_value': 4499}]
2025-05-27 08:10:41,711 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-27 08:10:41,711 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 440437.16000000003, 'new_value': 449065.16000000003}, {'field': 'dailyBillAmount', 'old_value': 440437.16000000003, 'new_value': 449065.16000000003}, {'field': 'amount', 'old_value': 707505.2999999999, 'new_value': 721541.72}, {'field': 'count', 'old_value': 1192, 'new_value': 1220}, {'field': 'instoreAmount', 'old_value': 702262.68, 'new_value': 716225.8}, {'field': 'instoreCount', 'old_value': 1155, 'new_value': 1182}, {'field': 'onlineAmount', 'old_value': 5575.4, 'new_value': 5648.7}, {'field': 'onlineCount', 'old_value': 37, 'new_value': 38}]
2025-05-27 08:10:42,211 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-27 08:10:42,211 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 260605.29, 'new_value': 264536.5}, {'field': 'dailyBillAmount', 'old_value': 260605.29, 'new_value': 264536.5}, {'field': 'amount', 'old_value': 298492.3, 'new_value': 302392.3}, {'field': 'count', 'old_value': 2081, 'new_value': 2116}, {'field': 'instoreAmount', 'old_value': 302527.3, 'new_value': 306427.3}, {'field': 'instoreCount', 'old_value': 2081, 'new_value': 2116}]
2025-05-27 08:10:42,617 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-27 08:10:42,617 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 171233.77, 'new_value': 178506.75}, {'field': 'dailyBillAmount', 'old_value': 171233.77, 'new_value': 178506.75}, {'field': 'amount', 'old_value': 139870.87, 'new_value': 146067.37}, {'field': 'count', 'old_value': 944, 'new_value': 984}, {'field': 'instoreAmount', 'old_value': 140012.0, 'new_value': 146384.0}, {'field': 'instoreCount', 'old_value': 892, 'new_value': 928}, {'field': 'onlineAmount', 'old_value': 2596.87, 'new_value': 2927.37}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 56}]
2025-05-27 08:10:43,070 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-27 08:10:43,070 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50803.5, 'new_value': 55070.5}, {'field': 'dailyBillAmount', 'old_value': 50803.5, 'new_value': 55070.5}]
2025-05-27 08:10:43,524 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-27 08:10:43,524 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 152655.49, 'new_value': 158461.95}, {'field': 'count', 'old_value': 7396, 'new_value': 7634}, {'field': 'instoreAmount', 'old_value': 80630.65, 'new_value': 83820.86}, {'field': 'instoreCount', 'old_value': 4116, 'new_value': 4234}, {'field': 'onlineAmount', 'old_value': 76228.56, 'new_value': 78932.96}, {'field': 'onlineCount', 'old_value': 3280, 'new_value': 3400}]
2025-05-27 08:10:43,930 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-27 08:10:43,930 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 218902.92, 'new_value': 228354.77}, {'field': 'amount', 'old_value': 218893.39, 'new_value': 228344.21}, {'field': 'count', 'old_value': 4141, 'new_value': 4354}, {'field': 'instoreAmount', 'old_value': 199422.47, 'new_value': 207552.59}, {'field': 'instoreCount', 'old_value': 3752, 'new_value': 3932}, {'field': 'onlineAmount', 'old_value': 19480.45, 'new_value': 20802.18}, {'field': 'onlineCount', 'old_value': 389, 'new_value': 422}]
2025-05-27 08:10:44,367 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-27 08:10:44,367 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32960.5, 'new_value': 34141.9}, {'field': 'amount', 'old_value': 32960.5, 'new_value': 34141.9}, {'field': 'count', 'old_value': 222, 'new_value': 231}, {'field': 'instoreAmount', 'old_value': 32960.5, 'new_value': 34141.9}, {'field': 'instoreCount', 'old_value': 222, 'new_value': 231}]
2025-05-27 08:10:44,852 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-27 08:10:44,867 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65809.7, 'new_value': 66045.7}, {'field': 'dailyBillAmount', 'old_value': 65809.7, 'new_value': 66045.7}, {'field': 'amount', 'old_value': 49943.6, 'new_value': 50733.700000000004}, {'field': 'count', 'old_value': 449, 'new_value': 459}, {'field': 'instoreAmount', 'old_value': 50164.0, 'new_value': 51081.1}, {'field': 'instoreCount', 'old_value': 449, 'new_value': 459}]
2025-05-27 08:10:45,320 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-27 08:10:45,336 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 241086.9, 'new_value': 254546.0}, {'field': 'dailyBillAmount', 'old_value': 241086.9, 'new_value': 254546.0}, {'field': 'amount', 'old_value': 186911.53, 'new_value': 192273.33}, {'field': 'count', 'old_value': 5202, 'new_value': 5370}, {'field': 'instoreAmount', 'old_value': 182674.76, 'new_value': 188068.36}, {'field': 'instoreCount', 'old_value': 5017, 'new_value': 5184}, {'field': 'onlineAmount', 'old_value': 7311.4800000000005, 'new_value': 7334.68}, {'field': 'onlineCount', 'old_value': 185, 'new_value': 186}]
2025-05-27 08:10:45,774 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-27 08:10:45,774 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53421.7, 'new_value': 54502.2}, {'field': 'dailyBillAmount', 'old_value': 53421.7, 'new_value': 54502.2}, {'field': 'amount', 'old_value': 53467.2, 'new_value': 54525.7}, {'field': 'count', 'old_value': 311, 'new_value': 324}, {'field': 'instoreAmount', 'old_value': 55983.4, 'new_value': 57245.6}, {'field': 'instoreCount', 'old_value': 308, 'new_value': 320}, {'field': 'onlineAmount', 'old_value': 387.9, 'new_value': 415.9}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-05-27 08:10:46,227 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-27 08:10:46,227 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72971.49, 'new_value': 76069.41}, {'field': 'dailyBillAmount', 'old_value': 72971.49, 'new_value': 76069.41}]
2025-05-27 08:10:46,602 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-27 08:10:46,602 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49349.950000000004, 'new_value': 51320.33}, {'field': 'amount', 'old_value': 49349.71, 'new_value': 51319.43}, {'field': 'count', 'old_value': 2858, 'new_value': 2988}, {'field': 'instoreAmount', 'old_value': 50179.340000000004, 'new_value': 52186.69}, {'field': 'instoreCount', 'old_value': 2858, 'new_value': 2988}]
2025-05-27 08:10:47,070 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-27 08:10:47,070 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77261.87, 'new_value': 80322.66}, {'field': 'dailyBillAmount', 'old_value': 77261.87, 'new_value': 80322.66}, {'field': 'amount', 'old_value': 79544.06, 'new_value': 82635.08}, {'field': 'count', 'old_value': 3912, 'new_value': 4077}, {'field': 'instoreAmount', 'old_value': 73849.7, 'new_value': 76733.3}, {'field': 'instoreCount', 'old_value': 3662, 'new_value': 3815}, {'field': 'onlineAmount', 'old_value': 5776.41, 'new_value': 5983.83}, {'field': 'onlineCount', 'old_value': 250, 'new_value': 262}]
2025-05-27 08:10:47,539 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-27 08:10:47,539 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54243.909999999996, 'new_value': 55675.74}, {'field': 'amount', 'old_value': 54243.909999999996, 'new_value': 55675.74}, {'field': 'count', 'old_value': 2642, 'new_value': 2727}, {'field': 'instoreAmount', 'old_value': 33486.19, 'new_value': 34393.63}, {'field': 'instoreCount', 'old_value': 1730, 'new_value': 1787}, {'field': 'onlineAmount', 'old_value': 20858.13, 'new_value': 21406.26}, {'field': 'onlineCount', 'old_value': 912, 'new_value': 940}]
2025-05-27 08:10:48,008 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-27 08:10:48,008 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36566.83, 'new_value': 39882.33}, {'field': 'dailyBillAmount', 'old_value': 36566.83, 'new_value': 39882.33}, {'field': 'amount', 'old_value': 26531.45, 'new_value': 27795.87}, {'field': 'count', 'old_value': 1066, 'new_value': 1117}, {'field': 'instoreAmount', 'old_value': 26803.97, 'new_value': 28081.87}, {'field': 'instoreCount', 'old_value': 1066, 'new_value': 1117}]
2025-05-27 08:10:48,477 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-27 08:10:48,477 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70511.84, 'new_value': 72344.8}, {'field': 'amount', 'old_value': 70503.41, 'new_value': 72336.37}, {'field': 'count', 'old_value': 4180, 'new_value': 4292}, {'field': 'instoreAmount', 'old_value': 18399.51, 'new_value': 18766.86}, {'field': 'instoreCount', 'old_value': 1057, 'new_value': 1074}, {'field': 'onlineAmount', 'old_value': 54105.06, 'new_value': 55612.97}, {'field': 'onlineCount', 'old_value': 3123, 'new_value': 3218}]
2025-05-27 08:10:49,024 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-27 08:10:49,024 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132467.71, 'new_value': 137827.51}, {'field': 'dailyBillAmount', 'old_value': 132467.71, 'new_value': 137827.51}, {'field': 'amount', 'old_value': 110136.06, 'new_value': 114681.27}, {'field': 'count', 'old_value': 1066, 'new_value': 1113}, {'field': 'instoreAmount', 'old_value': 110136.06, 'new_value': 114681.27}, {'field': 'instoreCount', 'old_value': 1066, 'new_value': 1113}]
2025-05-27 08:10:49,492 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-27 08:10:49,492 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 108022.15, 'new_value': 109556.15}, {'field': 'dailyBillAmount', 'old_value': 108022.15, 'new_value': 109556.15}, {'field': 'amount', 'old_value': 124120.8, 'new_value': 126289.8}, {'field': 'count', 'old_value': 529, 'new_value': 536}, {'field': 'instoreAmount', 'old_value': 124120.8, 'new_value': 126289.8}, {'field': 'instoreCount', 'old_value': 529, 'new_value': 536}]
2025-05-27 08:10:49,961 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-27 08:10:49,961 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69712.6, 'new_value': 71309.6}, {'field': 'dailyBillAmount', 'old_value': 69712.6, 'new_value': 71309.6}, {'field': 'amount', 'old_value': 56625.55, 'new_value': 57715.49}, {'field': 'count', 'old_value': 303, 'new_value': 312}, {'field': 'instoreAmount', 'old_value': 58062.55, 'new_value': 59152.49}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 312}]
2025-05-27 08:10:50,336 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-27 08:10:50,336 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 130165.0, 'new_value': 135974.0}, {'field': 'amount', 'old_value': 130165.0, 'new_value': 135974.0}, {'field': 'count', 'old_value': 1338, 'new_value': 1386}, {'field': 'instoreAmount', 'old_value': 130165.0, 'new_value': 135974.0}, {'field': 'instoreCount', 'old_value': 1338, 'new_value': 1386}]
2025-05-27 08:10:50,820 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-27 08:10:50,820 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31872.47, 'new_value': 33988.13}, {'field': 'dailyBillAmount', 'old_value': 31872.47, 'new_value': 33988.13}, {'field': 'amount', 'old_value': 4317.29, 'new_value': 4468.79}, {'field': 'count', 'old_value': 184, 'new_value': 186}, {'field': 'instoreAmount', 'old_value': 4782.0199999999995, 'new_value': 4946.32}, {'field': 'instoreCount', 'old_value': 184, 'new_value': 186}]
2025-05-27 08:10:51,274 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-27 08:10:51,274 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22936.9, 'new_value': 24234.47}, {'field': 'dailyBillAmount', 'old_value': 22936.9, 'new_value': 24234.47}, {'field': 'amount', 'old_value': 23690.7, 'new_value': 25014.64}, {'field': 'count', 'old_value': 642, 'new_value': 677}, {'field': 'instoreAmount', 'old_value': 23615.02, 'new_value': 24920.58}, {'field': 'instoreCount', 'old_value': 637, 'new_value': 671}, {'field': 'onlineAmount', 'old_value': 144.2, 'new_value': 170.57999999999998}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 6}]
2025-05-27 08:10:51,711 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-27 08:10:51,711 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46294.8, 'new_value': 47476.4}, {'field': 'dailyBillAmount', 'old_value': 46294.8, 'new_value': 47476.4}, {'field': 'amount', 'old_value': 69380.5, 'new_value': 70819.5}, {'field': 'count', 'old_value': 280, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 69569.5, 'new_value': 71008.5}, {'field': 'instoreCount', 'old_value': 279, 'new_value': 284}]
2025-05-27 08:10:52,164 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-27 08:10:52,164 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44127.0, 'new_value': 45467.0}, {'field': 'dailyBillAmount', 'old_value': 44127.0, 'new_value': 45467.0}, {'field': 'amount', 'old_value': 47439.0, 'new_value': 48779.0}, {'field': 'count', 'old_value': 262, 'new_value': 269}, {'field': 'instoreAmount', 'old_value': 47453.0, 'new_value': 48793.0}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 269}]
2025-05-27 08:10:52,649 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-27 08:10:52,649 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 75318.25, 'new_value': 77456.65}, {'field': 'dailyBillAmount', 'old_value': 75318.25, 'new_value': 77456.65}, {'field': 'amount', 'old_value': 66970.68, 'new_value': 68947.53}, {'field': 'count', 'old_value': 2255, 'new_value': 2328}, {'field': 'instoreAmount', 'old_value': 61051.92, 'new_value': 62913.46}, {'field': 'instoreCount', 'old_value': 1973, 'new_value': 2038}, {'field': 'onlineAmount', 'old_value': 5955.2, 'new_value': 6070.51}, {'field': 'onlineCount', 'old_value': 282, 'new_value': 290}]
2025-05-27 08:10:53,195 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-27 08:10:53,195 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45104.43, 'new_value': 48317.83}, {'field': 'dailyBillAmount', 'old_value': 45104.43, 'new_value': 48317.83}, {'field': 'amount', 'old_value': 50751.33, 'new_value': 54595.99}, {'field': 'count', 'old_value': 333, 'new_value': 358}, {'field': 'instoreAmount', 'old_value': 49345.61, 'new_value': 53157.11}, {'field': 'instoreCount', 'old_value': 272, 'new_value': 289}, {'field': 'onlineAmount', 'old_value': 1556.32, 'new_value': 1589.48}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 69}]
2025-05-27 08:10:53,617 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-27 08:10:53,617 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 211154.24, 'new_value': 219263.35}, {'field': 'dailyBillAmount', 'old_value': 211154.24, 'new_value': 219263.35}, {'field': 'amount', 'old_value': 219105.8, 'new_value': 226454.3}, {'field': 'count', 'old_value': 1444, 'new_value': 1477}, {'field': 'instoreAmount', 'old_value': 211478.7, 'new_value': 219551.7}, {'field': 'instoreCount', 'old_value': 1294, 'new_value': 1323}, {'field': 'onlineAmount', 'old_value': 10568.1, 'new_value': 10869.6}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 154}]
2025-05-27 08:10:53,617 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-27 08:10:53,617 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-27 08:10:53,617 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-27 08:10:53,617 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-27 08:10:53,617 - INFO - 正在批量插入月度数据，批次 1/1，共 4 条记录
2025-05-27 08:10:53,773 - INFO - 批量插入月度数据成功，批次 1，4 条记录
2025-05-27 08:10:56,789 - INFO - 批量插入月度数据完成: 总计 4 条，成功 4 条，失败 0 条
2025-05-27 08:10:56,789 - INFO - 批量插入月销售数据完成，共 4 条记录
2025-05-27 08:10:56,789 - INFO - 月销售数据同步完成！更新: 203 条，插入: 4 条，错误: 0 条，跳过: 985 条
2025-05-27 08:10:56,789 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-27 08:10:57,273 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250527.xlsx
2025-05-27 08:10:57,273 - INFO - 综合数据同步流程完成！
2025-05-27 08:10:57,336 - INFO - 综合数据同步完成
2025-05-27 08:10:57,336 - INFO - ==================================================
2025-05-27 08:10:57,336 - INFO - 程序退出
2025-05-27 08:10:57,336 - INFO - ==================================================
