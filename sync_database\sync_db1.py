import mysql.connector
import os
import json
import logging
import schedule
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 同步配置
SYNC_TABLE_SOURCE = 'yx_b_sales_record'  # 源数据库表名
SYNC_TABLE_TARGET = 'sales_record'       # 目标数据库表名
BATCH_SIZE = 1000                         # 每次分页拉取的记录数
LAST_SYNC_FILE = "last_sync_time.json"   # 存储上次同步时间的文件

# 数据库B连接配置（源数据库，只读）
DB_B_CONFIG = {
    'host': '**************',  # 源数据库地址
    'port': 3306,
    'user': 'c_hxp_ro_prod',  # 只读用户
    'password': 'xm9P06O7ezGi6PZt',
    'database': 'yx_business',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_0900_ai_ci'
}

# 数据库A连接配置（目标数据库，本地）
DB_A_CONFIG = {
    'host': 'localhost',
    'port': 43306,
    'user': 'root',
    'password': 'Hxp@1987!@#',
    'database': 'mydatabase',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_0900_ai_ci'
}

def get_last_sync_time():
    """
    从文件中读取上次同步时间
    """
    if not os.path.exists(LAST_SYNC_FILE):
        return None
    with open(LAST_SYNC_FILE, 'r') as f:
        data = json.load(f)
    last_time_str = data.get(SYNC_TABLE_SOURCE)
    if last_time_str:
        return datetime.fromisoformat(last_time_str)
    return None

def save_last_sync_time(current_time):
    """
    将本次同步时间写入文件
    """
    data = {}
    if os.path.exists(LAST_SYNC_FILE):
        with open(LAST_SYNC_FILE, 'r') as f:
            data = json.load(f)
    data[SYNC_TABLE_SOURCE] = current_time.isoformat()
    with open(LAST_SYNC_FILE, 'w') as f:
        json.dump(data, f)

def get_max_updated_time(cursor):
    """
    获取源数据库中表的最大 updated_time
    """
    cursor.execute(f"SELECT MAX(updated_time) AS max_time FROM {SYNC_TABLE_SOURCE}")
    result = cursor.fetchone()
    return result['max_time'] if result and result['max_time'] else None

def sync_table_incremental():
    """
    增量同步主函数
    """
    last_time = get_last_sync_time()
    if last_time:
        logging.info(f"开始增量同步，上次同步时间为：{last_time}")
    else:
        logging.info("首次同步，将同步全部数据")

    try:
        # 连接源数据库（数据库B）
        conn_b = mysql.connector.connect(**DB_B_CONFIG)
        cursor_b = conn_b.cursor(dictionary=True)

        # 连接目标数据库（数据库A）
        conn_a = mysql.connector.connect(**DB_A_CONFIG)
        cursor_a = conn_a.cursor()

        # 查询条件：根据 updated_time 或 created_time 判断是否为新数据
        if last_time:
            query = f"""
                SELECT * FROM {SYNC_TABLE_SOURCE}
                WHERE updated_time > %s OR created_time > %s
            """
            cursor_b.execute(query, (last_time, last_time))
        else:
            query = f"SELECT * FROM {SYNC_TABLE_SOURCE}"
            cursor_b.execute(query)

        total_records = 0  # 统计本次同步记录数

        while True:
            rows = cursor_b.fetchmany(BATCH_SIZE)
            if not rows:
                break

            total_records += len(rows)
            logging.info(f"正在同步 {len(rows)} 条记录...")

            for row in rows:
                columns = ', '.join(row.keys())
                placeholders = ', '.join(['%s'] * len(row))

                # 只更新特定字段（可选，也可以更新所有字段）
                update_fields = [
                    'code', 'store_code', 'project_code', 'sales_time',
                    'online_amount', 'offline_amount', 'total_amount',
                    'order_count', 'status', 'approval_flow',
                    'approval_time', 'approval_remark', 'report_source',
                    'created_by', 'created_user_id', 'created_time',
                    'updated_by', 'updated_user_id', 'updated_time',
                    'remark', 'deleted'
                ]

                update_clause = ', '.join([f"{field} = VALUES({field})" for field in update_fields])

                insert_sql = f"""
                    INSERT INTO {SYNC_TABLE_TARGET} ({columns})
                    VALUES ({placeholders})
                    ON DUPLICATE KEY UPDATE {update_clause}
                """
                cursor_a.execute(insert_sql, list(row.values()))

            conn_a.commit()

        # 获取最新的 updated_time
        latest_time = get_max_updated_time(cursor_b)
        if latest_time:
            save_last_sync_time(latest_time)
            logging.info(f"同步完成，共同步 {total_records} 条记录，最新 updated_time 为：{latest_time}")
        else:
            logging.warning("没有找到 updated_time，可能数据为空")

        # 关闭连接
        cursor_b.close()
        cursor_a.close()
        conn_b.close()
        conn_a.close()

    except Exception as e:
        logging.error(f"同步过程中发生错误：{str(e)}")

# 定时任务：每天凌晨2点执行同步
def schedule_sync():
    schedule.every().day.at("02:00").do(sync_table_incremental)
    logging.info("定时任务已启动，等待执行...")
    while True:
        schedule.run_pending()
        time.sleep(60)

# 主程序入口
if __name__ == '__main__':
    sync_table_incremental()  # 执行一次同步
    # schedule_sync()  # 如果需要定时运行，取消注释此行