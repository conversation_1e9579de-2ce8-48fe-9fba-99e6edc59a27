# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_dingtalk.yida_2_0.client import Client as dingtalkyida_2_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient



# 宜搭配置
YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30'
}

form_data_json1 = '{"textField_m9nw1k6w": "100098214", "textField_m9nw1k6x": "武汉国金天地", "textField_m9nw1k6y": "100098568", "textField_m9nw1k6z": "美斯蒂克", "dateField_m9nw1k71": "2025-04-18", "numberField_m9nw1k73": 258.02, "numberField_m9nw1k75": 3980.0, "numberField_m9nw1k77": 4238.02, "numberField_m9nw1k79": 1}'

class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> dingtalkyida_2_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkyida_2_0Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        save_form_data_headers = dingtalkyida__2__0_models.SaveFormDataHeaders()
        save_form_data_headers.x_acs_dingtalk_access_token = '8df7c073a0ae37eb8b3f2d7a3a024708'
        save_form_data_request = dingtalkyida__2__0_models.SaveFormDataRequest(
            app_type='APP_D7E6ZB94ZUL5Q1GUAOLD',
            system_token='BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
            user_id='hexuepeng',
            language='zh_CN',
            form_uuid='9AE718C1C466495DA7D1537C946B1F17XE30',
            form_data_json='{"textField_m9nw1k6w": "100098214", "textField_m9nw1k6x": "武汉国金天地", "textField_m9nw1k6y": "100098568", "textField_m9nw1k6z": "美斯蒂克", "dateField_m9nw1k71": "2025-04-18", "numberField_m9nw1k73": 258.02, "numberField_m9nw1k75": 3980.0, "numberField_m9nw1k77": 4238.02, "numberField_m9nw1k79": 1}',
            use_alias=False
        )
        try:
            response = client.save_form_data_with_options(save_form_data_request, save_form_data_headers, util_models.RuntimeOptions())
            print(response.body)
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                print(err.code)
                print(err.message)
                pass

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        save_form_data_headers = dingtalkyida__2__0_models.SaveFormDataHeaders()
        save_form_data_headers.x_acs_dingtalk_access_token = '<your access token>'
        save_form_data_request = dingtalkyida__2__0_models.SaveFormDataRequest(
            app_type='APP_PBKxxx',
            system_token='hexxxx',
            user_id='manager123',
            language='zh_CN',
            form_uuid='FORM-EF6xxx',
            form_data_json='{"textField_jcpm6agt": "单行","employeeField_jcos0sar": ["workno"]}',
            use_alias=False
        )
        try:
            await client.save_form_data_with_options_async(save_form_data_request, save_form_data_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass


if __name__ == '__main__':
    Sample.main(sys.argv[1:])