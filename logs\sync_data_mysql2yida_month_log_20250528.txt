2025-05-28 00:00:02,049 - INFO - =================使用默认全量同步=============
2025-05-28 00:00:03,550 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 00:00:03,550 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 00:00:03,581 - INFO - 开始处理日期: 2025-01
2025-05-28 00:00:03,581 - INFO - Request Parameters - Page 1:
2025-05-28 00:00:03,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:03,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:04,956 - INFO - Response - Page 1:
2025-05-28 00:00:05,159 - INFO - 第 1 页获取到 100 条记录
2025-05-28 00:00:05,159 - INFO - Request Parameters - Page 2:
2025-05-28 00:00:05,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:05,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:05,737 - INFO - Response - Page 2:
2025-05-28 00:00:05,940 - INFO - 第 2 页获取到 100 条记录
2025-05-28 00:00:05,940 - INFO - Request Parameters - Page 3:
2025-05-28 00:00:05,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:05,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:06,472 - INFO - Response - Page 3:
2025-05-28 00:00:06,675 - INFO - 第 3 页获取到 100 条记录
2025-05-28 00:00:06,675 - INFO - Request Parameters - Page 4:
2025-05-28 00:00:06,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:06,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:07,175 - INFO - Response - Page 4:
2025-05-28 00:00:07,378 - INFO - 第 4 页获取到 100 条记录
2025-05-28 00:00:07,378 - INFO - Request Parameters - Page 5:
2025-05-28 00:00:07,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:07,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:07,847 - INFO - Response - Page 5:
2025-05-28 00:00:08,050 - INFO - 第 5 页获取到 100 条记录
2025-05-28 00:00:08,050 - INFO - Request Parameters - Page 6:
2025-05-28 00:00:08,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:08,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:08,566 - INFO - Response - Page 6:
2025-05-28 00:00:08,769 - INFO - 第 6 页获取到 100 条记录
2025-05-28 00:00:08,769 - INFO - Request Parameters - Page 7:
2025-05-28 00:00:08,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:08,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:09,222 - INFO - Response - Page 7:
2025-05-28 00:00:09,425 - INFO - 第 7 页获取到 82 条记录
2025-05-28 00:00:09,425 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 00:00:09,425 - INFO - 获取到 682 条表单数据
2025-05-28 00:00:09,425 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 00:00:09,441 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 00:00:09,441 - INFO - 开始处理日期: 2025-02
2025-05-28 00:00:09,441 - INFO - Request Parameters - Page 1:
2025-05-28 00:00:09,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:09,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:09,909 - INFO - Response - Page 1:
2025-05-28 00:00:10,113 - INFO - 第 1 页获取到 100 条记录
2025-05-28 00:00:10,113 - INFO - Request Parameters - Page 2:
2025-05-28 00:00:10,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:10,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:10,597 - INFO - Response - Page 2:
2025-05-28 00:00:10,800 - INFO - 第 2 页获取到 100 条记录
2025-05-28 00:00:10,800 - INFO - Request Parameters - Page 3:
2025-05-28 00:00:10,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:10,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:11,269 - INFO - Response - Page 3:
2025-05-28 00:00:11,472 - INFO - 第 3 页获取到 100 条记录
2025-05-28 00:00:11,472 - INFO - Request Parameters - Page 4:
2025-05-28 00:00:11,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:11,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:11,956 - INFO - Response - Page 4:
2025-05-28 00:00:12,160 - INFO - 第 4 页获取到 100 条记录
2025-05-28 00:00:12,160 - INFO - Request Parameters - Page 5:
2025-05-28 00:00:12,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:12,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:12,691 - INFO - Response - Page 5:
2025-05-28 00:00:12,894 - INFO - 第 5 页获取到 100 条记录
2025-05-28 00:00:12,894 - INFO - Request Parameters - Page 6:
2025-05-28 00:00:12,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:12,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:13,410 - INFO - Response - Page 6:
2025-05-28 00:00:13,613 - INFO - 第 6 页获取到 100 条记录
2025-05-28 00:00:13,613 - INFO - Request Parameters - Page 7:
2025-05-28 00:00:13,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:13,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:14,019 - INFO - Response - Page 7:
2025-05-28 00:00:14,222 - INFO - 第 7 页获取到 70 条记录
2025-05-28 00:00:14,222 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 00:00:14,222 - INFO - 获取到 670 条表单数据
2025-05-28 00:00:14,222 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 00:00:14,238 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 00:00:14,238 - INFO - 开始处理日期: 2025-03
2025-05-28 00:00:14,238 - INFO - Request Parameters - Page 1:
2025-05-28 00:00:14,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:14,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:14,738 - INFO - Response - Page 1:
2025-05-28 00:00:14,941 - INFO - 第 1 页获取到 100 条记录
2025-05-28 00:00:14,941 - INFO - Request Parameters - Page 2:
2025-05-28 00:00:14,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:14,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:15,379 - INFO - Response - Page 2:
2025-05-28 00:00:15,582 - INFO - 第 2 页获取到 100 条记录
2025-05-28 00:00:15,582 - INFO - Request Parameters - Page 3:
2025-05-28 00:00:15,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:15,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:16,175 - INFO - Response - Page 3:
2025-05-28 00:00:16,379 - INFO - 第 3 页获取到 100 条记录
2025-05-28 00:00:16,379 - INFO - Request Parameters - Page 4:
2025-05-28 00:00:16,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:16,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:16,847 - INFO - Response - Page 4:
2025-05-28 00:00:17,050 - INFO - 第 4 页获取到 100 条记录
2025-05-28 00:00:17,050 - INFO - Request Parameters - Page 5:
2025-05-28 00:00:17,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:17,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:17,551 - INFO - Response - Page 5:
2025-05-28 00:00:17,754 - INFO - 第 5 页获取到 100 条记录
2025-05-28 00:00:17,754 - INFO - Request Parameters - Page 6:
2025-05-28 00:00:17,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:17,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:18,222 - INFO - Response - Page 6:
2025-05-28 00:00:18,426 - INFO - 第 6 页获取到 100 条记录
2025-05-28 00:00:18,426 - INFO - Request Parameters - Page 7:
2025-05-28 00:00:18,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:18,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:18,801 - INFO - Response - Page 7:
2025-05-28 00:00:19,004 - INFO - 第 7 页获取到 61 条记录
2025-05-28 00:00:19,004 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 00:00:19,004 - INFO - 获取到 661 条表单数据
2025-05-28 00:00:19,004 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 00:00:19,019 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 00:00:19,019 - INFO - 开始处理日期: 2025-04
2025-05-28 00:00:19,019 - INFO - Request Parameters - Page 1:
2025-05-28 00:00:19,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:19,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:19,629 - INFO - Response - Page 1:
2025-05-28 00:00:19,832 - INFO - 第 1 页获取到 100 条记录
2025-05-28 00:00:19,832 - INFO - Request Parameters - Page 2:
2025-05-28 00:00:19,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:19,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:20,332 - INFO - Response - Page 2:
2025-05-28 00:00:20,535 - INFO - 第 2 页获取到 100 条记录
2025-05-28 00:00:20,535 - INFO - Request Parameters - Page 3:
2025-05-28 00:00:20,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:20,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:21,020 - INFO - Response - Page 3:
2025-05-28 00:00:21,223 - INFO - 第 3 页获取到 100 条记录
2025-05-28 00:00:21,223 - INFO - Request Parameters - Page 4:
2025-05-28 00:00:21,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:21,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:21,723 - INFO - Response - Page 4:
2025-05-28 00:00:21,926 - INFO - 第 4 页获取到 100 条记录
2025-05-28 00:00:21,926 - INFO - Request Parameters - Page 5:
2025-05-28 00:00:21,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:21,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:22,363 - INFO - Response - Page 5:
2025-05-28 00:00:22,566 - INFO - 第 5 页获取到 100 条记录
2025-05-28 00:00:22,566 - INFO - Request Parameters - Page 6:
2025-05-28 00:00:22,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:22,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:23,067 - INFO - Response - Page 6:
2025-05-28 00:00:23,270 - INFO - 第 6 页获取到 100 条记录
2025-05-28 00:00:23,270 - INFO - Request Parameters - Page 7:
2025-05-28 00:00:23,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:23,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:23,676 - INFO - Response - Page 7:
2025-05-28 00:00:23,879 - INFO - 第 7 页获取到 56 条记录
2025-05-28 00:00:23,879 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 00:00:23,879 - INFO - 获取到 656 条表单数据
2025-05-28 00:00:23,879 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 00:00:23,895 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 00:00:23,895 - INFO - 开始处理日期: 2025-05
2025-05-28 00:00:23,895 - INFO - Request Parameters - Page 1:
2025-05-28 00:00:23,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:23,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:24,395 - INFO - Response - Page 1:
2025-05-28 00:00:24,598 - INFO - 第 1 页获取到 100 条记录
2025-05-28 00:00:24,598 - INFO - Request Parameters - Page 2:
2025-05-28 00:00:24,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:24,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:25,067 - INFO - Response - Page 2:
2025-05-28 00:00:25,270 - INFO - 第 2 页获取到 100 条记录
2025-05-28 00:00:25,270 - INFO - Request Parameters - Page 3:
2025-05-28 00:00:25,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:25,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:25,754 - INFO - Response - Page 3:
2025-05-28 00:00:25,957 - INFO - 第 3 页获取到 100 条记录
2025-05-28 00:00:25,957 - INFO - Request Parameters - Page 4:
2025-05-28 00:00:25,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:25,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:26,489 - INFO - Response - Page 4:
2025-05-28 00:00:26,692 - INFO - 第 4 页获取到 100 条记录
2025-05-28 00:00:26,692 - INFO - Request Parameters - Page 5:
2025-05-28 00:00:26,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:26,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:27,161 - INFO - Response - Page 5:
2025-05-28 00:00:27,364 - INFO - 第 5 页获取到 100 条记录
2025-05-28 00:00:27,364 - INFO - Request Parameters - Page 6:
2025-05-28 00:00:27,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:27,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:27,848 - INFO - Response - Page 6:
2025-05-28 00:00:28,051 - INFO - 第 6 页获取到 100 条记录
2025-05-28 00:00:28,051 - INFO - Request Parameters - Page 7:
2025-05-28 00:00:28,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 00:00:28,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 00:00:28,426 - INFO - Response - Page 7:
2025-05-28 00:00:28,629 - INFO - 第 7 页获取到 34 条记录
2025-05-28 00:00:28,629 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 00:00:28,629 - INFO - 获取到 634 条表单数据
2025-05-28 00:00:28,629 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 00:00:28,629 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-28 00:00:29,067 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-28 00:00:29,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85176.0, 'new_value': 88284.0}, {'field': 'offline_amount', 'old_value': 110014.0, 'new_value': 114125.0}, {'field': 'total_amount', 'old_value': 195190.0, 'new_value': 202409.0}, {'field': 'order_count', 'old_value': 4442, 'new_value': 4599}]
2025-05-28 00:00:29,067 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-28 00:00:29,489 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-28 00:00:29,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54298.78, 'new_value': 55973.59}, {'field': 'offline_amount', 'old_value': 710253.26, 'new_value': 738963.27}, {'field': 'total_amount', 'old_value': 764552.04, 'new_value': 794936.86}, {'field': 'order_count', 'old_value': 3169, 'new_value': 3315}]
2025-05-28 00:00:29,489 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-28 00:00:29,958 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-28 00:00:29,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 419015.3, 'new_value': 400545.9}, {'field': 'offline_amount', 'old_value': 97054.3, 'new_value': 115608.1}, {'field': 'total_amount', 'old_value': 516069.6, 'new_value': 516154.0}]
2025-05-28 00:00:29,973 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-28 00:00:30,380 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-28 00:00:30,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1809597.06, 'new_value': 1862931.08}, {'field': 'total_amount', 'old_value': 1809597.06, 'new_value': 1862931.08}, {'field': 'order_count', 'old_value': 15581, 'new_value': 16196}]
2025-05-28 00:00:30,380 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-28 00:00:30,848 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-28 00:00:30,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21141.35, 'new_value': 21845.67}, {'field': 'total_amount', 'old_value': 22891.35, 'new_value': 23595.67}, {'field': 'order_count', 'old_value': 448, 'new_value': 459}]
2025-05-28 00:00:30,848 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-28 00:00:31,301 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-28 00:00:31,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 764357.14, 'new_value': 782409.03}, {'field': 'total_amount', 'old_value': 764357.14, 'new_value': 782409.03}, {'field': 'order_count', 'old_value': 5356, 'new_value': 5496}]
2025-05-28 00:00:31,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-28 00:00:31,755 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-28 00:00:31,755 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 216537.53, 'new_value': 220379.0}, {'field': 'offline_amount', 'old_value': 379946.92, 'new_value': 383946.92}, {'field': 'total_amount', 'old_value': 596484.45, 'new_value': 604325.92}, {'field': 'order_count', 'old_value': 1508, 'new_value': 1543}]
2025-05-28 00:00:31,755 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-28 00:00:32,192 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-28 00:00:32,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 473883.0, 'new_value': 484011.0}, {'field': 'total_amount', 'old_value': 478906.0, 'new_value': 489034.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 98}]
2025-05-28 00:00:32,192 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-28 00:00:32,598 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-28 00:00:32,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5634.63, 'new_value': 7079.75}, {'field': 'offline_amount', 'old_value': 51709.34, 'new_value': 54690.7}, {'field': 'total_amount', 'old_value': 57343.97, 'new_value': 61770.45}, {'field': 'order_count', 'old_value': 2299, 'new_value': 2547}]
2025-05-28 00:00:32,598 - INFO - 日期 2025-05 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-05-28 00:00:32,598 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-05-28 00:00:32,598 - INFO - =================同步完成====================
2025-05-28 03:00:02,014 - INFO - =================使用默认全量同步=============
2025-05-28 03:00:03,482 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 03:00:03,482 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 03:00:03,514 - INFO - 开始处理日期: 2025-01
2025-05-28 03:00:03,529 - INFO - Request Parameters - Page 1:
2025-05-28 03:00:03,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:03,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:04,686 - INFO - Response - Page 1:
2025-05-28 03:00:04,889 - INFO - 第 1 页获取到 100 条记录
2025-05-28 03:00:04,889 - INFO - Request Parameters - Page 2:
2025-05-28 03:00:04,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:04,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:05,686 - INFO - Response - Page 2:
2025-05-28 03:00:05,889 - INFO - 第 2 页获取到 100 条记录
2025-05-28 03:00:05,889 - INFO - Request Parameters - Page 3:
2025-05-28 03:00:05,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:05,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:06,389 - INFO - Response - Page 3:
2025-05-28 03:00:06,592 - INFO - 第 3 页获取到 100 条记录
2025-05-28 03:00:06,592 - INFO - Request Parameters - Page 4:
2025-05-28 03:00:06,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:06,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:07,045 - INFO - Response - Page 4:
2025-05-28 03:00:07,248 - INFO - 第 4 页获取到 100 条记录
2025-05-28 03:00:07,248 - INFO - Request Parameters - Page 5:
2025-05-28 03:00:07,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:07,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:07,795 - INFO - Response - Page 5:
2025-05-28 03:00:07,998 - INFO - 第 5 页获取到 100 条记录
2025-05-28 03:00:07,998 - INFO - Request Parameters - Page 6:
2025-05-28 03:00:07,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:07,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:08,498 - INFO - Response - Page 6:
2025-05-28 03:00:08,702 - INFO - 第 6 页获取到 100 条记录
2025-05-28 03:00:08,702 - INFO - Request Parameters - Page 7:
2025-05-28 03:00:08,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:08,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:09,186 - INFO - Response - Page 7:
2025-05-28 03:00:09,389 - INFO - 第 7 页获取到 82 条记录
2025-05-28 03:00:09,389 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 03:00:09,389 - INFO - 获取到 682 条表单数据
2025-05-28 03:00:09,389 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 03:00:09,405 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 03:00:09,405 - INFO - 开始处理日期: 2025-02
2025-05-28 03:00:09,405 - INFO - Request Parameters - Page 1:
2025-05-28 03:00:09,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:09,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:09,873 - INFO - Response - Page 1:
2025-05-28 03:00:10,077 - INFO - 第 1 页获取到 100 条记录
2025-05-28 03:00:10,077 - INFO - Request Parameters - Page 2:
2025-05-28 03:00:10,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:10,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:10,530 - INFO - Response - Page 2:
2025-05-28 03:00:10,733 - INFO - 第 2 页获取到 100 条记录
2025-05-28 03:00:10,733 - INFO - Request Parameters - Page 3:
2025-05-28 03:00:10,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:10,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:11,295 - INFO - Response - Page 3:
2025-05-28 03:00:11,499 - INFO - 第 3 页获取到 100 条记录
2025-05-28 03:00:11,499 - INFO - Request Parameters - Page 4:
2025-05-28 03:00:11,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:11,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:12,061 - INFO - Response - Page 4:
2025-05-28 03:00:12,264 - INFO - 第 4 页获取到 100 条记录
2025-05-28 03:00:12,264 - INFO - Request Parameters - Page 5:
2025-05-28 03:00:12,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:12,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:12,827 - INFO - Response - Page 5:
2025-05-28 03:00:13,030 - INFO - 第 5 页获取到 100 条记录
2025-05-28 03:00:13,030 - INFO - Request Parameters - Page 6:
2025-05-28 03:00:13,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:13,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:13,561 - INFO - Response - Page 6:
2025-05-28 03:00:13,764 - INFO - 第 6 页获取到 100 条记录
2025-05-28 03:00:13,764 - INFO - Request Parameters - Page 7:
2025-05-28 03:00:13,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:13,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:14,218 - INFO - Response - Page 7:
2025-05-28 03:00:14,421 - INFO - 第 7 页获取到 70 条记录
2025-05-28 03:00:14,421 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 03:00:14,421 - INFO - 获取到 670 条表单数据
2025-05-28 03:00:14,421 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 03:00:14,436 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 03:00:14,436 - INFO - 开始处理日期: 2025-03
2025-05-28 03:00:14,436 - INFO - Request Parameters - Page 1:
2025-05-28 03:00:14,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:14,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:14,936 - INFO - Response - Page 1:
2025-05-28 03:00:15,139 - INFO - 第 1 页获取到 100 条记录
2025-05-28 03:00:15,139 - INFO - Request Parameters - Page 2:
2025-05-28 03:00:15,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:15,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:15,593 - INFO - Response - Page 2:
2025-05-28 03:00:15,796 - INFO - 第 2 页获取到 100 条记录
2025-05-28 03:00:15,796 - INFO - Request Parameters - Page 3:
2025-05-28 03:00:15,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:15,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:16,280 - INFO - Response - Page 3:
2025-05-28 03:00:16,483 - INFO - 第 3 页获取到 100 条记录
2025-05-28 03:00:16,483 - INFO - Request Parameters - Page 4:
2025-05-28 03:00:16,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:16,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:16,968 - INFO - Response - Page 4:
2025-05-28 03:00:17,171 - INFO - 第 4 页获取到 100 条记录
2025-05-28 03:00:17,171 - INFO - Request Parameters - Page 5:
2025-05-28 03:00:17,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:17,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:17,718 - INFO - Response - Page 5:
2025-05-28 03:00:17,921 - INFO - 第 5 页获取到 100 条记录
2025-05-28 03:00:17,921 - INFO - Request Parameters - Page 6:
2025-05-28 03:00:17,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:17,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:18,452 - INFO - Response - Page 6:
2025-05-28 03:00:18,655 - INFO - 第 6 页获取到 100 条记录
2025-05-28 03:00:18,655 - INFO - Request Parameters - Page 7:
2025-05-28 03:00:18,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:18,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:19,077 - INFO - Response - Page 7:
2025-05-28 03:00:19,280 - INFO - 第 7 页获取到 61 条记录
2025-05-28 03:00:19,280 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 03:00:19,280 - INFO - 获取到 661 条表单数据
2025-05-28 03:00:19,280 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 03:00:19,296 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 03:00:19,296 - INFO - 开始处理日期: 2025-04
2025-05-28 03:00:19,296 - INFO - Request Parameters - Page 1:
2025-05-28 03:00:19,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:19,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:19,874 - INFO - Response - Page 1:
2025-05-28 03:00:20,077 - INFO - 第 1 页获取到 100 条记录
2025-05-28 03:00:20,077 - INFO - Request Parameters - Page 2:
2025-05-28 03:00:20,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:20,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:20,577 - INFO - Response - Page 2:
2025-05-28 03:00:20,780 - INFO - 第 2 页获取到 100 条记录
2025-05-28 03:00:20,780 - INFO - Request Parameters - Page 3:
2025-05-28 03:00:20,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:20,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:21,249 - INFO - Response - Page 3:
2025-05-28 03:00:21,452 - INFO - 第 3 页获取到 100 条记录
2025-05-28 03:00:21,452 - INFO - Request Parameters - Page 4:
2025-05-28 03:00:21,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:21,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:21,937 - INFO - Response - Page 4:
2025-05-28 03:00:22,140 - INFO - 第 4 页获取到 100 条记录
2025-05-28 03:00:22,140 - INFO - Request Parameters - Page 5:
2025-05-28 03:00:22,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:22,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:22,640 - INFO - Response - Page 5:
2025-05-28 03:00:22,843 - INFO - 第 5 页获取到 100 条记录
2025-05-28 03:00:22,843 - INFO - Request Parameters - Page 6:
2025-05-28 03:00:22,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:22,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:23,328 - INFO - Response - Page 6:
2025-05-28 03:00:23,531 - INFO - 第 6 页获取到 100 条记录
2025-05-28 03:00:23,531 - INFO - Request Parameters - Page 7:
2025-05-28 03:00:23,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:23,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:23,921 - INFO - Response - Page 7:
2025-05-28 03:00:24,124 - INFO - 第 7 页获取到 56 条记录
2025-05-28 03:00:24,124 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 03:00:24,124 - INFO - 获取到 656 条表单数据
2025-05-28 03:00:24,124 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 03:00:24,140 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 03:00:24,140 - INFO - 开始处理日期: 2025-05
2025-05-28 03:00:24,140 - INFO - Request Parameters - Page 1:
2025-05-28 03:00:24,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:24,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:24,625 - INFO - Response - Page 1:
2025-05-28 03:00:24,828 - INFO - 第 1 页获取到 100 条记录
2025-05-28 03:00:24,828 - INFO - Request Parameters - Page 2:
2025-05-28 03:00:24,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:24,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:25,296 - INFO - Response - Page 2:
2025-05-28 03:00:25,500 - INFO - 第 2 页获取到 100 条记录
2025-05-28 03:00:25,500 - INFO - Request Parameters - Page 3:
2025-05-28 03:00:25,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:25,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:26,000 - INFO - Response - Page 3:
2025-05-28 03:00:26,203 - INFO - 第 3 页获取到 100 条记录
2025-05-28 03:00:26,203 - INFO - Request Parameters - Page 4:
2025-05-28 03:00:26,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:26,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:26,656 - INFO - Response - Page 4:
2025-05-28 03:00:26,859 - INFO - 第 4 页获取到 100 条记录
2025-05-28 03:00:26,859 - INFO - Request Parameters - Page 5:
2025-05-28 03:00:26,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:26,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:27,562 - INFO - Response - Page 5:
2025-05-28 03:00:27,765 - INFO - 第 5 页获取到 100 条记录
2025-05-28 03:00:27,765 - INFO - Request Parameters - Page 6:
2025-05-28 03:00:27,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:27,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:28,297 - INFO - Response - Page 6:
2025-05-28 03:00:28,500 - INFO - 第 6 页获取到 100 条记录
2025-05-28 03:00:28,500 - INFO - Request Parameters - Page 7:
2025-05-28 03:00:28,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 03:00:28,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 03:00:28,890 - INFO - Response - Page 7:
2025-05-28 03:00:29,094 - INFO - 第 7 页获取到 34 条记录
2025-05-28 03:00:29,094 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 03:00:29,094 - INFO - 获取到 634 条表单数据
2025-05-28 03:00:29,094 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 03:00:29,094 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVT
2025-05-28 03:00:29,625 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVT
2025-05-28 03:00:29,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7240.0, 'new_value': 9750.0}, {'field': 'total_amount', 'old_value': 7240.0, 'new_value': 9750.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-28 03:00:29,625 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-28 03:00:30,094 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-28 03:00:30,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6446.16, 'new_value': 6814.16}, {'field': 'offline_amount', 'old_value': 64050.0, 'new_value': 66538.0}, {'field': 'total_amount', 'old_value': 70496.16, 'new_value': 73352.16}, {'field': 'order_count', 'old_value': 462, 'new_value': 466}]
2025-05-28 03:00:30,094 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-28 03:00:30,594 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-28 03:00:30,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62483.74, 'new_value': 65081.73}, {'field': 'offline_amount', 'old_value': 105784.81, 'new_value': 108715.39}, {'field': 'total_amount', 'old_value': 168268.55, 'new_value': 173797.12}, {'field': 'order_count', 'old_value': 5813, 'new_value': 6017}]
2025-05-28 03:00:30,594 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-28 03:00:31,031 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-28 03:00:31,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336098.0, 'new_value': 348831.0}, {'field': 'total_amount', 'old_value': 336098.0, 'new_value': 348831.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 212}]
2025-05-28 03:00:31,031 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-28 03:00:31,562 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-28 03:00:31,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24688.5, 'new_value': 24745.65}, {'field': 'total_amount', 'old_value': 24754.05, 'new_value': 24811.2}, {'field': 'order_count', 'old_value': 227, 'new_value': 228}]
2025-05-28 03:00:31,562 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-28 03:00:31,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-28 03:00:31,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58543.0, 'new_value': 58642.0}, {'field': 'total_amount', 'old_value': 58891.0, 'new_value': 58990.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 121}]
2025-05-28 03:00:31,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-28 03:00:32,344 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-28 03:00:32,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 880154.0, 'new_value': 913614.0}, {'field': 'total_amount', 'old_value': 880154.0, 'new_value': 913614.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 168}]
2025-05-28 03:00:32,359 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-28 03:00:32,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-28 03:00:32,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 252939.31, 'new_value': 257966.3}, {'field': 'offline_amount', 'old_value': 110306.74, 'new_value': 112200.86}, {'field': 'total_amount', 'old_value': 363246.05, 'new_value': 370167.16}, {'field': 'order_count', 'old_value': 1449, 'new_value': 1491}]
2025-05-28 03:00:32,797 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-28 03:00:33,328 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-28 03:00:33,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23521.19, 'new_value': 24200.77}, {'field': 'offline_amount', 'old_value': 312288.56, 'new_value': 315043.51}, {'field': 'total_amount', 'old_value': 335809.75, 'new_value': 339244.28}, {'field': 'order_count', 'old_value': 1585, 'new_value': 1599}]
2025-05-28 03:00:33,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-28 03:00:33,703 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-28 03:00:33,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39483.52, 'new_value': 41141.02}, {'field': 'offline_amount', 'old_value': 576655.59, 'new_value': 586678.09}, {'field': 'total_amount', 'old_value': 616139.11, 'new_value': 627819.11}, {'field': 'order_count', 'old_value': 3294, 'new_value': 3338}]
2025-05-28 03:00:33,703 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-28 03:00:34,203 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-28 03:00:34,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312732.0, 'new_value': 315410.0}, {'field': 'total_amount', 'old_value': 312732.0, 'new_value': 315410.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 264}]
2025-05-28 03:00:34,203 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-28 03:00:34,735 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-28 03:00:34,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 331802.4, 'new_value': 338162.8}, {'field': 'total_amount', 'old_value': 331802.4, 'new_value': 338162.8}, {'field': 'order_count', 'old_value': 3424, 'new_value': 3488}]
2025-05-28 03:00:34,735 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-28 03:00:35,110 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-28 03:00:35,110 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121917.1, 'new_value': 134630.77}, {'field': 'total_amount', 'old_value': 637055.94, 'new_value': 649769.61}, {'field': 'order_count', 'old_value': 2545, 'new_value': 2588}]
2025-05-28 03:00:35,110 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-28 03:00:35,547 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-28 03:00:35,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183111.0, 'new_value': 196054.0}, {'field': 'total_amount', 'old_value': 183244.0, 'new_value': 196187.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 144}]
2025-05-28 03:00:35,547 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-28 03:00:35,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-28 03:00:35,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109372.58, 'new_value': 111099.58}, {'field': 'total_amount', 'old_value': 114984.1, 'new_value': 116711.1}]
2025-05-28 03:00:35,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-28 03:00:36,469 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-28 03:00:36,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76090.0, 'new_value': 79191.0}, {'field': 'total_amount', 'old_value': 76090.0, 'new_value': 79191.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 120}]
2025-05-28 03:00:36,469 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-28 03:00:36,938 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-28 03:00:36,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 393554.84, 'new_value': 400619.72}, {'field': 'total_amount', 'old_value': 393554.84, 'new_value': 400619.72}, {'field': 'order_count', 'old_value': 1952, 'new_value': 1997}]
2025-05-28 03:00:36,938 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-28 03:00:37,344 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-28 03:00:37,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81052.57, 'new_value': 84335.62}, {'field': 'offline_amount', 'old_value': 262709.61, 'new_value': 267158.15}, {'field': 'total_amount', 'old_value': 343762.18, 'new_value': 351493.77}, {'field': 'order_count', 'old_value': 4115, 'new_value': 4168}]
2025-05-28 03:00:37,344 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-28 03:00:37,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-28 03:00:37,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69410.3, 'new_value': 71605.3}, {'field': 'total_amount', 'old_value': 69410.3, 'new_value': 71605.3}, {'field': 'order_count', 'old_value': 1640, 'new_value': 1645}]
2025-05-28 03:00:37,797 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-28 03:00:38,204 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-28 03:00:38,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 699551.9, 'new_value': 712591.0}, {'field': 'total_amount', 'old_value': 699551.9, 'new_value': 712591.0}, {'field': 'order_count', 'old_value': 1999, 'new_value': 2042}]
2025-05-28 03:00:38,204 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-28 03:00:38,641 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-28 03:00:38,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 892594.0, 'new_value': 893515.0}, {'field': 'total_amount', 'old_value': 892594.0, 'new_value': 893515.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 126}]
2025-05-28 03:00:38,641 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-28 03:00:39,094 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-28 03:00:39,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2264.0, 'new_value': 2296.0}, {'field': 'offline_amount', 'old_value': 614884.0, 'new_value': 623573.0}, {'field': 'total_amount', 'old_value': 617148.0, 'new_value': 625869.0}, {'field': 'order_count', 'old_value': 281, 'new_value': 284}]
2025-05-28 03:00:39,094 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-28 03:00:39,501 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-28 03:00:39,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200392.0, 'new_value': 212222.0}, {'field': 'total_amount', 'old_value': 200392.0, 'new_value': 212222.0}, {'field': 'order_count', 'old_value': 3362, 'new_value': 3380}]
2025-05-28 03:00:39,501 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-28 03:00:39,938 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-28 03:00:39,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 307984.0, 'new_value': 320519.0}, {'field': 'total_amount', 'old_value': 307984.0, 'new_value': 320519.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 85}]
2025-05-28 03:00:39,938 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-28 03:00:40,454 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-28 03:00:40,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 741854.0, 'new_value': 751343.0}, {'field': 'total_amount', 'old_value': 741854.0, 'new_value': 751343.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 182}]
2025-05-28 03:00:40,454 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-28 03:00:40,844 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-28 03:00:40,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79977.0, 'new_value': 82641.0}, {'field': 'total_amount', 'old_value': 79977.0, 'new_value': 82641.0}, {'field': 'order_count', 'old_value': 214, 'new_value': 221}]
2025-05-28 03:00:40,844 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-28 03:00:41,219 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-28 03:00:41,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135013.8, 'new_value': 138277.6}, {'field': 'total_amount', 'old_value': 135013.8, 'new_value': 138277.6}, {'field': 'order_count', 'old_value': 263, 'new_value': 272}]
2025-05-28 03:00:41,219 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-28 03:00:41,641 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-28 03:00:41,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122706.7, 'new_value': 123866.7}, {'field': 'offline_amount', 'old_value': 79429.88, 'new_value': 79784.78}, {'field': 'total_amount', 'old_value': 202136.58, 'new_value': 203651.48}, {'field': 'order_count', 'old_value': 1359, 'new_value': 1372}]
2025-05-28 03:00:41,641 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-28 03:00:42,048 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-28 03:00:42,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367324.1, 'new_value': 376315.5}, {'field': 'total_amount', 'old_value': 367324.1, 'new_value': 376315.5}, {'field': 'order_count', 'old_value': 453, 'new_value': 463}]
2025-05-28 03:00:42,048 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-28 03:00:42,501 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-28 03:00:42,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 955559.0, 'new_value': 969118.0}, {'field': 'total_amount', 'old_value': 955559.0, 'new_value': 969118.0}, {'field': 'order_count', 'old_value': 51046, 'new_value': 51061}]
2025-05-28 03:00:42,501 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-28 03:00:42,954 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-28 03:00:42,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 689275.0, 'new_value': 716772.0}, {'field': 'total_amount', 'old_value': 689275.0, 'new_value': 716772.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 83}]
2025-05-28 03:00:42,954 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-28 03:00:43,345 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-28 03:00:43,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139516.2, 'new_value': 143443.8}, {'field': 'offline_amount', 'old_value': 115538.4, 'new_value': 119789.8}, {'field': 'total_amount', 'old_value': 255054.6, 'new_value': 263233.6}, {'field': 'order_count', 'old_value': 5997, 'new_value': 6190}]
2025-05-28 03:00:43,345 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-28 03:00:43,813 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-28 03:00:43,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259952.74, 'new_value': 264084.24}, {'field': 'total_amount', 'old_value': 259952.74, 'new_value': 264084.24}, {'field': 'order_count', 'old_value': 1591, 'new_value': 1622}]
2025-05-28 03:00:43,813 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-28 03:00:44,220 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-28 03:00:44,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 247641.84, 'new_value': 254268.93}, {'field': 'offline_amount', 'old_value': 760557.36, 'new_value': 779993.09}, {'field': 'total_amount', 'old_value': 1008199.2, 'new_value': 1034262.02}, {'field': 'order_count', 'old_value': 6040, 'new_value': 6225}]
2025-05-28 03:00:44,220 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-28 03:00:44,673 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-28 03:00:44,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33627.84, 'new_value': 35541.39}, {'field': 'offline_amount', 'old_value': 366411.64, 'new_value': 375245.25}, {'field': 'total_amount', 'old_value': 400039.48, 'new_value': 410786.64}, {'field': 'order_count', 'old_value': 9944, 'new_value': 10035}]
2025-05-28 03:00:44,673 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-28 03:00:45,095 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-28 03:00:45,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95274.1, 'new_value': 110945.1}, {'field': 'total_amount', 'old_value': 96079.1, 'new_value': 111750.1}, {'field': 'order_count', 'old_value': 16308, 'new_value': 16315}]
2025-05-28 03:00:45,095 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-28 03:00:45,579 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-28 03:00:45,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58950.74, 'new_value': 61084.76}, {'field': 'offline_amount', 'old_value': 46420.2, 'new_value': 47447.81}, {'field': 'total_amount', 'old_value': 105370.94, 'new_value': 108532.57}, {'field': 'order_count', 'old_value': 8840, 'new_value': 9149}]
2025-05-28 03:00:45,579 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-28 03:00:46,017 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-28 03:00:46,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 583943.0, 'new_value': 586014.0}, {'field': 'total_amount', 'old_value': 613943.0, 'new_value': 616014.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 96}]
2025-05-28 03:00:46,017 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-28 03:00:46,439 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-28 03:00:46,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 627005.0, 'new_value': 636749.15}, {'field': 'total_amount', 'old_value': 627005.0, 'new_value': 636749.15}, {'field': 'order_count', 'old_value': 495, 'new_value': 509}]
2025-05-28 03:00:46,439 - INFO - 日期 2025-05 处理完成 - 更新: 39 条，插入: 0 条，错误: 0 条
2025-05-28 03:00:46,439 - INFO - 数据同步完成！更新: 39 条，插入: 0 条，错误: 0 条
2025-05-28 03:00:46,439 - INFO - =================同步完成====================
2025-05-28 06:00:01,854 - INFO - =================使用默认全量同步=============
2025-05-28 06:00:03,323 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 06:00:03,323 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 06:00:03,354 - INFO - 开始处理日期: 2025-01
2025-05-28 06:00:03,354 - INFO - Request Parameters - Page 1:
2025-05-28 06:00:03,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:03,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:04,433 - INFO - Response - Page 1:
2025-05-28 06:00:04,636 - INFO - 第 1 页获取到 100 条记录
2025-05-28 06:00:04,636 - INFO - Request Parameters - Page 2:
2025-05-28 06:00:04,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:04,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:05,433 - INFO - Response - Page 2:
2025-05-28 06:00:05,636 - INFO - 第 2 页获取到 100 条记录
2025-05-28 06:00:05,636 - INFO - Request Parameters - Page 3:
2025-05-28 06:00:05,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:05,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:06,073 - INFO - Response - Page 3:
2025-05-28 06:00:06,276 - INFO - 第 3 页获取到 100 条记录
2025-05-28 06:00:06,276 - INFO - Request Parameters - Page 4:
2025-05-28 06:00:06,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:06,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:06,745 - INFO - Response - Page 4:
2025-05-28 06:00:06,948 - INFO - 第 4 页获取到 100 条记录
2025-05-28 06:00:06,948 - INFO - Request Parameters - Page 5:
2025-05-28 06:00:06,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:06,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:07,464 - INFO - Response - Page 5:
2025-05-28 06:00:07,667 - INFO - 第 5 页获取到 100 条记录
2025-05-28 06:00:07,667 - INFO - Request Parameters - Page 6:
2025-05-28 06:00:07,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:07,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:08,151 - INFO - Response - Page 6:
2025-05-28 06:00:08,354 - INFO - 第 6 页获取到 100 条记录
2025-05-28 06:00:08,354 - INFO - Request Parameters - Page 7:
2025-05-28 06:00:08,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:08,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:08,886 - INFO - Response - Page 7:
2025-05-28 06:00:09,089 - INFO - 第 7 页获取到 82 条记录
2025-05-28 06:00:09,089 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 06:00:09,089 - INFO - 获取到 682 条表单数据
2025-05-28 06:00:09,089 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 06:00:09,104 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 06:00:09,104 - INFO - 开始处理日期: 2025-02
2025-05-28 06:00:09,104 - INFO - Request Parameters - Page 1:
2025-05-28 06:00:09,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:09,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:09,729 - INFO - Response - Page 1:
2025-05-28 06:00:09,933 - INFO - 第 1 页获取到 100 条记录
2025-05-28 06:00:09,933 - INFO - Request Parameters - Page 2:
2025-05-28 06:00:09,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:09,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:10,479 - INFO - Response - Page 2:
2025-05-28 06:00:10,683 - INFO - 第 2 页获取到 100 条记录
2025-05-28 06:00:10,683 - INFO - Request Parameters - Page 3:
2025-05-28 06:00:10,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:10,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:11,229 - INFO - Response - Page 3:
2025-05-28 06:00:11,433 - INFO - 第 3 页获取到 100 条记录
2025-05-28 06:00:11,433 - INFO - Request Parameters - Page 4:
2025-05-28 06:00:11,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:11,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:12,011 - INFO - Response - Page 4:
2025-05-28 06:00:12,214 - INFO - 第 4 页获取到 100 条记录
2025-05-28 06:00:12,214 - INFO - Request Parameters - Page 5:
2025-05-28 06:00:12,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:12,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:12,761 - INFO - Response - Page 5:
2025-05-28 06:00:12,964 - INFO - 第 5 页获取到 100 条记录
2025-05-28 06:00:12,964 - INFO - Request Parameters - Page 6:
2025-05-28 06:00:12,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:12,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:13,526 - INFO - Response - Page 6:
2025-05-28 06:00:13,729 - INFO - 第 6 页获取到 100 条记录
2025-05-28 06:00:13,729 - INFO - Request Parameters - Page 7:
2025-05-28 06:00:13,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:13,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:14,104 - INFO - Response - Page 7:
2025-05-28 06:00:14,307 - INFO - 第 7 页获取到 70 条记录
2025-05-28 06:00:14,307 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 06:00:14,307 - INFO - 获取到 670 条表单数据
2025-05-28 06:00:14,307 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 06:00:14,323 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 06:00:14,323 - INFO - 开始处理日期: 2025-03
2025-05-28 06:00:14,323 - INFO - Request Parameters - Page 1:
2025-05-28 06:00:14,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:14,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:14,854 - INFO - Response - Page 1:
2025-05-28 06:00:15,057 - INFO - 第 1 页获取到 100 条记录
2025-05-28 06:00:15,057 - INFO - Request Parameters - Page 2:
2025-05-28 06:00:15,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:15,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:15,542 - INFO - Response - Page 2:
2025-05-28 06:00:15,745 - INFO - 第 2 页获取到 100 条记录
2025-05-28 06:00:15,745 - INFO - Request Parameters - Page 3:
2025-05-28 06:00:15,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:15,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:16,214 - INFO - Response - Page 3:
2025-05-28 06:00:16,432 - INFO - 第 3 页获取到 100 条记录
2025-05-28 06:00:16,432 - INFO - Request Parameters - Page 4:
2025-05-28 06:00:16,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:16,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:16,870 - INFO - Response - Page 4:
2025-05-28 06:00:17,073 - INFO - 第 4 页获取到 100 条记录
2025-05-28 06:00:17,073 - INFO - Request Parameters - Page 5:
2025-05-28 06:00:17,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:17,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:17,557 - INFO - Response - Page 5:
2025-05-28 06:00:17,761 - INFO - 第 5 页获取到 100 条记录
2025-05-28 06:00:17,761 - INFO - Request Parameters - Page 6:
2025-05-28 06:00:17,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:17,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:18,276 - INFO - Response - Page 6:
2025-05-28 06:00:18,479 - INFO - 第 6 页获取到 100 条记录
2025-05-28 06:00:18,479 - INFO - Request Parameters - Page 7:
2025-05-28 06:00:18,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:18,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:18,964 - INFO - Response - Page 7:
2025-05-28 06:00:19,167 - INFO - 第 7 页获取到 61 条记录
2025-05-28 06:00:19,167 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 06:00:19,167 - INFO - 获取到 661 条表单数据
2025-05-28 06:00:19,167 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 06:00:19,182 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 06:00:19,182 - INFO - 开始处理日期: 2025-04
2025-05-28 06:00:19,182 - INFO - Request Parameters - Page 1:
2025-05-28 06:00:19,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:19,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:19,807 - INFO - Response - Page 1:
2025-05-28 06:00:20,011 - INFO - 第 1 页获取到 100 条记录
2025-05-28 06:00:20,011 - INFO - Request Parameters - Page 2:
2025-05-28 06:00:20,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:20,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:20,464 - INFO - Response - Page 2:
2025-05-28 06:00:20,667 - INFO - 第 2 页获取到 100 条记录
2025-05-28 06:00:20,667 - INFO - Request Parameters - Page 3:
2025-05-28 06:00:20,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:20,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:21,229 - INFO - Response - Page 3:
2025-05-28 06:00:21,432 - INFO - 第 3 页获取到 100 条记录
2025-05-28 06:00:21,432 - INFO - Request Parameters - Page 4:
2025-05-28 06:00:21,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:21,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:21,901 - INFO - Response - Page 4:
2025-05-28 06:00:22,104 - INFO - 第 4 页获取到 100 条记录
2025-05-28 06:00:22,104 - INFO - Request Parameters - Page 5:
2025-05-28 06:00:22,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:22,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:22,604 - INFO - Response - Page 5:
2025-05-28 06:00:22,807 - INFO - 第 5 页获取到 100 条记录
2025-05-28 06:00:22,807 - INFO - Request Parameters - Page 6:
2025-05-28 06:00:22,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:22,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:23,323 - INFO - Response - Page 6:
2025-05-28 06:00:23,526 - INFO - 第 6 页获取到 100 条记录
2025-05-28 06:00:23,526 - INFO - Request Parameters - Page 7:
2025-05-28 06:00:23,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:23,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:23,964 - INFO - Response - Page 7:
2025-05-28 06:00:24,167 - INFO - 第 7 页获取到 56 条记录
2025-05-28 06:00:24,167 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 06:00:24,167 - INFO - 获取到 656 条表单数据
2025-05-28 06:00:24,167 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 06:00:24,182 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 06:00:24,182 - INFO - 开始处理日期: 2025-05
2025-05-28 06:00:24,182 - INFO - Request Parameters - Page 1:
2025-05-28 06:00:24,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:24,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:24,714 - INFO - Response - Page 1:
2025-05-28 06:00:24,917 - INFO - 第 1 页获取到 100 条记录
2025-05-28 06:00:24,917 - INFO - Request Parameters - Page 2:
2025-05-28 06:00:24,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:24,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:25,448 - INFO - Response - Page 2:
2025-05-28 06:00:25,651 - INFO - 第 2 页获取到 100 条记录
2025-05-28 06:00:25,651 - INFO - Request Parameters - Page 3:
2025-05-28 06:00:25,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:25,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:26,136 - INFO - Response - Page 3:
2025-05-28 06:00:26,339 - INFO - 第 3 页获取到 100 条记录
2025-05-28 06:00:26,339 - INFO - Request Parameters - Page 4:
2025-05-28 06:00:26,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:26,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:26,979 - INFO - Response - Page 4:
2025-05-28 06:00:27,182 - INFO - 第 4 页获取到 100 条记录
2025-05-28 06:00:27,182 - INFO - Request Parameters - Page 5:
2025-05-28 06:00:27,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:27,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:27,745 - INFO - Response - Page 5:
2025-05-28 06:00:27,948 - INFO - 第 5 页获取到 100 条记录
2025-05-28 06:00:27,948 - INFO - Request Parameters - Page 6:
2025-05-28 06:00:27,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:27,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:28,495 - INFO - Response - Page 6:
2025-05-28 06:00:28,698 - INFO - 第 6 页获取到 100 条记录
2025-05-28 06:00:28,698 - INFO - Request Parameters - Page 7:
2025-05-28 06:00:28,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 06:00:28,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 06:00:29,089 - INFO - Response - Page 7:
2025-05-28 06:00:29,292 - INFO - 第 7 页获取到 34 条记录
2025-05-28 06:00:29,292 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 06:00:29,292 - INFO - 获取到 634 条表单数据
2025-05-28 06:00:29,292 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 06:00:29,307 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-28 06:00:29,761 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-28 06:00:29,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85827.0, 'new_value': 89982.0}, {'field': 'total_amount', 'old_value': 87677.0, 'new_value': 91832.0}, {'field': 'order_count', 'old_value': 490, 'new_value': 510}]
2025-05-28 06:00:29,761 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-28 06:00:29,761 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-28 06:00:29,761 - INFO - =================同步完成====================
2025-05-28 09:00:01,845 - INFO - =================使用默认全量同步=============
2025-05-28 09:00:03,330 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 09:00:03,330 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 09:00:03,345 - INFO - 开始处理日期: 2025-01
2025-05-28 09:00:03,361 - INFO - Request Parameters - Page 1:
2025-05-28 09:00:03,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:03,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:04,470 - INFO - Response - Page 1:
2025-05-28 09:00:04,674 - INFO - 第 1 页获取到 100 条记录
2025-05-28 09:00:04,674 - INFO - Request Parameters - Page 2:
2025-05-28 09:00:04,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:04,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:05,502 - INFO - Response - Page 2:
2025-05-28 09:00:05,705 - INFO - 第 2 页获取到 100 条记录
2025-05-28 09:00:05,705 - INFO - Request Parameters - Page 3:
2025-05-28 09:00:05,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:05,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:06,377 - INFO - Response - Page 3:
2025-05-28 09:00:06,580 - INFO - 第 3 页获取到 100 条记录
2025-05-28 09:00:06,580 - INFO - Request Parameters - Page 4:
2025-05-28 09:00:06,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:06,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:07,064 - INFO - Response - Page 4:
2025-05-28 09:00:07,267 - INFO - 第 4 页获取到 100 条记录
2025-05-28 09:00:07,267 - INFO - Request Parameters - Page 5:
2025-05-28 09:00:07,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:07,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:07,830 - INFO - Response - Page 5:
2025-05-28 09:00:08,033 - INFO - 第 5 页获取到 100 条记录
2025-05-28 09:00:08,033 - INFO - Request Parameters - Page 6:
2025-05-28 09:00:08,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:08,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:08,611 - INFO - Response - Page 6:
2025-05-28 09:00:08,814 - INFO - 第 6 页获取到 100 条记录
2025-05-28 09:00:08,814 - INFO - Request Parameters - Page 7:
2025-05-28 09:00:08,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:08,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:09,408 - INFO - Response - Page 7:
2025-05-28 09:00:09,611 - INFO - 第 7 页获取到 82 条记录
2025-05-28 09:00:09,611 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 09:00:09,611 - INFO - 获取到 682 条表单数据
2025-05-28 09:00:09,611 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 09:00:09,627 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 09:00:09,627 - INFO - 开始处理日期: 2025-02
2025-05-28 09:00:09,627 - INFO - Request Parameters - Page 1:
2025-05-28 09:00:09,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:09,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:10,064 - INFO - Response - Page 1:
2025-05-28 09:00:10,267 - INFO - 第 1 页获取到 100 条记录
2025-05-28 09:00:10,267 - INFO - Request Parameters - Page 2:
2025-05-28 09:00:10,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:10,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:10,705 - INFO - Response - Page 2:
2025-05-28 09:00:10,908 - INFO - 第 2 页获取到 100 条记录
2025-05-28 09:00:10,908 - INFO - Request Parameters - Page 3:
2025-05-28 09:00:10,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:10,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:11,439 - INFO - Response - Page 3:
2025-05-28 09:00:11,642 - INFO - 第 3 页获取到 100 条记录
2025-05-28 09:00:11,642 - INFO - Request Parameters - Page 4:
2025-05-28 09:00:11,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:11,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:12,189 - INFO - Response - Page 4:
2025-05-28 09:00:12,392 - INFO - 第 4 页获取到 100 条记录
2025-05-28 09:00:12,392 - INFO - Request Parameters - Page 5:
2025-05-28 09:00:12,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:12,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:13,017 - INFO - Response - Page 5:
2025-05-28 09:00:13,220 - INFO - 第 5 页获取到 100 条记录
2025-05-28 09:00:13,220 - INFO - Request Parameters - Page 6:
2025-05-28 09:00:13,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:13,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:13,705 - INFO - Response - Page 6:
2025-05-28 09:00:13,908 - INFO - 第 6 页获取到 100 条记录
2025-05-28 09:00:13,908 - INFO - Request Parameters - Page 7:
2025-05-28 09:00:13,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:13,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:14,392 - INFO - Response - Page 7:
2025-05-28 09:00:14,595 - INFO - 第 7 页获取到 70 条记录
2025-05-28 09:00:14,595 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 09:00:14,595 - INFO - 获取到 670 条表单数据
2025-05-28 09:00:14,595 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 09:00:14,611 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 09:00:14,611 - INFO - 开始处理日期: 2025-03
2025-05-28 09:00:14,611 - INFO - Request Parameters - Page 1:
2025-05-28 09:00:14,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:14,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:15,127 - INFO - Response - Page 1:
2025-05-28 09:00:15,330 - INFO - 第 1 页获取到 100 条记录
2025-05-28 09:00:15,330 - INFO - Request Parameters - Page 2:
2025-05-28 09:00:15,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:15,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:15,830 - INFO - Response - Page 2:
2025-05-28 09:00:16,033 - INFO - 第 2 页获取到 100 条记录
2025-05-28 09:00:16,033 - INFO - Request Parameters - Page 3:
2025-05-28 09:00:16,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:16,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:16,517 - INFO - Response - Page 3:
2025-05-28 09:00:16,720 - INFO - 第 3 页获取到 100 条记录
2025-05-28 09:00:16,720 - INFO - Request Parameters - Page 4:
2025-05-28 09:00:16,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:16,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:17,236 - INFO - Response - Page 4:
2025-05-28 09:00:17,439 - INFO - 第 4 页获取到 100 条记录
2025-05-28 09:00:17,439 - INFO - Request Parameters - Page 5:
2025-05-28 09:00:17,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:17,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:17,861 - INFO - Response - Page 5:
2025-05-28 09:00:18,064 - INFO - 第 5 页获取到 100 条记录
2025-05-28 09:00:18,064 - INFO - Request Parameters - Page 6:
2025-05-28 09:00:18,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:18,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:18,502 - INFO - Response - Page 6:
2025-05-28 09:00:18,705 - INFO - 第 6 页获取到 100 条记录
2025-05-28 09:00:18,705 - INFO - Request Parameters - Page 7:
2025-05-28 09:00:18,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:18,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:19,158 - INFO - Response - Page 7:
2025-05-28 09:00:19,361 - INFO - 第 7 页获取到 61 条记录
2025-05-28 09:00:19,361 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 09:00:19,361 - INFO - 获取到 661 条表单数据
2025-05-28 09:00:19,361 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 09:00:19,377 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 09:00:19,377 - INFO - 开始处理日期: 2025-04
2025-05-28 09:00:19,377 - INFO - Request Parameters - Page 1:
2025-05-28 09:00:19,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:19,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:19,986 - INFO - Response - Page 1:
2025-05-28 09:00:20,189 - INFO - 第 1 页获取到 100 条记录
2025-05-28 09:00:20,189 - INFO - Request Parameters - Page 2:
2025-05-28 09:00:20,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:20,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:20,642 - INFO - Response - Page 2:
2025-05-28 09:00:20,845 - INFO - 第 2 页获取到 100 条记录
2025-05-28 09:00:20,845 - INFO - Request Parameters - Page 3:
2025-05-28 09:00:20,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:20,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:21,392 - INFO - Response - Page 3:
2025-05-28 09:00:21,595 - INFO - 第 3 页获取到 100 条记录
2025-05-28 09:00:21,595 - INFO - Request Parameters - Page 4:
2025-05-28 09:00:21,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:21,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:22,127 - INFO - Response - Page 4:
2025-05-28 09:00:22,330 - INFO - 第 4 页获取到 100 条记录
2025-05-28 09:00:22,330 - INFO - Request Parameters - Page 5:
2025-05-28 09:00:22,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:22,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:22,830 - INFO - Response - Page 5:
2025-05-28 09:00:23,033 - INFO - 第 5 页获取到 100 条记录
2025-05-28 09:00:23,033 - INFO - Request Parameters - Page 6:
2025-05-28 09:00:23,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:23,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:23,627 - INFO - Response - Page 6:
2025-05-28 09:00:23,830 - INFO - 第 6 页获取到 100 条记录
2025-05-28 09:00:23,830 - INFO - Request Parameters - Page 7:
2025-05-28 09:00:23,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:23,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:24,267 - INFO - Response - Page 7:
2025-05-28 09:00:24,470 - INFO - 第 7 页获取到 56 条记录
2025-05-28 09:00:24,470 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 09:00:24,470 - INFO - 获取到 656 条表单数据
2025-05-28 09:00:24,470 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 09:00:24,486 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 09:00:24,486 - INFO - 开始处理日期: 2025-05
2025-05-28 09:00:24,486 - INFO - Request Parameters - Page 1:
2025-05-28 09:00:24,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:24,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:24,970 - INFO - Response - Page 1:
2025-05-28 09:00:25,189 - INFO - 第 1 页获取到 100 条记录
2025-05-28 09:00:25,189 - INFO - Request Parameters - Page 2:
2025-05-28 09:00:25,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:25,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:25,705 - INFO - Response - Page 2:
2025-05-28 09:00:25,908 - INFO - 第 2 页获取到 100 条记录
2025-05-28 09:00:25,908 - INFO - Request Parameters - Page 3:
2025-05-28 09:00:25,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:25,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:26,408 - INFO - Response - Page 3:
2025-05-28 09:00:26,611 - INFO - 第 3 页获取到 100 条记录
2025-05-28 09:00:26,611 - INFO - Request Parameters - Page 4:
2025-05-28 09:00:26,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:26,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:27,142 - INFO - Response - Page 4:
2025-05-28 09:00:27,345 - INFO - 第 4 页获取到 100 条记录
2025-05-28 09:00:27,345 - INFO - Request Parameters - Page 5:
2025-05-28 09:00:27,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:27,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:27,798 - INFO - Response - Page 5:
2025-05-28 09:00:28,002 - INFO - 第 5 页获取到 100 条记录
2025-05-28 09:00:28,002 - INFO - Request Parameters - Page 6:
2025-05-28 09:00:28,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:28,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:28,595 - INFO - Response - Page 6:
2025-05-28 09:00:28,798 - INFO - 第 6 页获取到 100 条记录
2025-05-28 09:00:28,798 - INFO - Request Parameters - Page 7:
2025-05-28 09:00:28,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 09:00:28,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 09:00:29,173 - INFO - Response - Page 7:
2025-05-28 09:00:29,376 - INFO - 第 7 页获取到 34 条记录
2025-05-28 09:00:29,376 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 09:00:29,376 - INFO - 获取到 634 条表单数据
2025-05-28 09:00:29,376 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 09:00:29,376 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-28 09:00:29,845 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-28 09:00:29,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10500080.0, 'new_value': 10900080.0}, {'field': 'total_amount', 'old_value': 10600080.0, 'new_value': 11000080.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-28 09:00:29,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-28 09:00:30,314 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-28 09:00:30,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54653.78, 'new_value': 56303.78}, {'field': 'total_amount', 'old_value': 54653.78, 'new_value': 56303.78}, {'field': 'order_count', 'old_value': 130, 'new_value': 132}]
2025-05-28 09:00:30,314 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-28 09:00:30,798 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-28 09:00:30,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53380.0, 'new_value': 55480.0}, {'field': 'total_amount', 'old_value': 57500.0, 'new_value': 59600.0}, {'field': 'order_count', 'old_value': 554, 'new_value': 582}]
2025-05-28 09:00:30,798 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-28 09:00:31,220 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-28 09:00:31,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7564.9, 'new_value': 7624.8}, {'field': 'offline_amount', 'old_value': 86021.7, 'new_value': 86022.7}, {'field': 'total_amount', 'old_value': 93586.6, 'new_value': 93647.5}, {'field': 'order_count', 'old_value': 2122, 'new_value': 2123}]
2025-05-28 09:00:31,220 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-28 09:00:31,736 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-28 09:00:31,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59797.6, 'new_value': 60115.6}, {'field': 'total_amount', 'old_value': 75602.3, 'new_value': 75920.3}, {'field': 'order_count', 'old_value': 743, 'new_value': 746}]
2025-05-28 09:00:31,736 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-28 09:00:32,205 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-28 09:00:32,205 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203512.99, 'new_value': 211868.9}, {'field': 'offline_amount', 'old_value': 446262.26, 'new_value': 454238.45}, {'field': 'total_amount', 'old_value': 649775.25, 'new_value': 666107.35}, {'field': 'order_count', 'old_value': 4794, 'new_value': 4949}]
2025-05-28 09:00:32,205 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-28 09:00:32,595 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-28 09:00:32,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8817.0, 'new_value': 9201.0}, {'field': 'total_amount', 'old_value': 8817.0, 'new_value': 9201.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-28 09:00:32,595 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-28 09:00:33,064 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-28 09:00:33,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39486.9, 'new_value': 40059.9}, {'field': 'total_amount', 'old_value': 39486.9, 'new_value': 40059.9}, {'field': 'order_count', 'old_value': 176, 'new_value': 179}]
2025-05-28 09:00:33,064 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-28 09:00:33,501 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-28 09:00:33,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121243.0, 'new_value': 122731.0}, {'field': 'total_amount', 'old_value': 121246.0, 'new_value': 122734.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-05-28 09:00:33,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-28 09:00:33,986 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-28 09:00:33,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79391.59, 'new_value': 80321.49}, {'field': 'total_amount', 'old_value': 83160.69, 'new_value': 84090.59}, {'field': 'order_count', 'old_value': 415, 'new_value': 418}]
2025-05-28 09:00:33,986 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-28 09:00:34,314 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-28 09:00:34,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120768.64, 'new_value': 121116.64}, {'field': 'total_amount', 'old_value': 126108.64, 'new_value': 126456.64}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-28 09:00:34,314 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-28 09:00:34,720 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-28 09:00:34,720 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42414.6, 'new_value': 42609.6}, {'field': 'offline_amount', 'old_value': 574.0, 'new_value': 577.0}, {'field': 'total_amount', 'old_value': 42988.6, 'new_value': 43186.6}, {'field': 'order_count', 'old_value': 177, 'new_value': 178}]
2025-05-28 09:00:34,720 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-28 09:00:35,330 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-28 09:00:35,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9568.3, 'new_value': 10161.3}, {'field': 'offline_amount', 'old_value': 54843.1, 'new_value': 56143.1}, {'field': 'total_amount', 'old_value': 64411.4, 'new_value': 66304.4}, {'field': 'order_count', 'old_value': 79, 'new_value': 81}]
2025-05-28 09:00:35,330 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-28 09:00:35,798 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-28 09:00:35,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172687.9, 'new_value': 176568.5}, {'field': 'total_amount', 'old_value': 172687.9, 'new_value': 176568.5}, {'field': 'order_count', 'old_value': 405, 'new_value': 416}]
2025-05-28 09:00:35,798 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-28 09:00:36,236 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-28 09:00:36,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202677.67, 'new_value': 203688.28}, {'field': 'offline_amount', 'old_value': 118400.74, 'new_value': 120434.84}, {'field': 'total_amount', 'old_value': 321078.41, 'new_value': 324123.12}, {'field': 'order_count', 'old_value': 3316, 'new_value': 3372}]
2025-05-28 09:00:36,236 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-28 09:00:36,673 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-28 09:00:36,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44429.0, 'new_value': 45171.0}, {'field': 'total_amount', 'old_value': 44429.0, 'new_value': 45171.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 93}]
2025-05-28 09:00:36,673 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-28 09:00:37,111 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-28 09:00:37,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55581.0, 'new_value': 57296.0}, {'field': 'total_amount', 'old_value': 58735.0, 'new_value': 60450.0}, {'field': 'order_count', 'old_value': 218, 'new_value': 227}]
2025-05-28 09:00:37,111 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-28 09:00:37,595 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-28 09:00:37,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6.0}, {'field': 'offline_amount', 'old_value': 238098.26, 'new_value': 240176.16}, {'field': 'total_amount', 'old_value': 238098.26, 'new_value': 240182.16}, {'field': 'order_count', 'old_value': 438, 'new_value': 444}]
2025-05-28 09:00:37,595 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-28 09:00:38,033 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-28 09:00:38,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32847.47, 'new_value': 33313.47}, {'field': 'total_amount', 'old_value': 64722.37, 'new_value': 65188.37}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-28 09:00:38,033 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-28 09:00:38,439 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-28 09:00:38,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51488.0, 'new_value': 54087.0}, {'field': 'total_amount', 'old_value': 51488.0, 'new_value': 54087.0}, {'field': 'order_count', 'old_value': 17603, 'new_value': 17604}]
2025-05-28 09:00:38,439 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-28 09:00:38,923 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-28 09:00:38,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105784.7, 'new_value': 105784.8}, {'field': 'total_amount', 'old_value': 113134.7, 'new_value': 113134.8}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-28 09:00:38,923 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-28 09:00:39,345 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-28 09:00:39,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51887.3, 'new_value': 53149.5}, {'field': 'total_amount', 'old_value': 54187.6, 'new_value': 55449.8}, {'field': 'order_count', 'old_value': 168, 'new_value': 171}]
2025-05-28 09:00:39,361 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-28 09:00:39,814 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-28 09:00:39,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314300.8, 'new_value': 334460.8}, {'field': 'total_amount', 'old_value': 314300.8, 'new_value': 334460.8}, {'field': 'order_count', 'old_value': 138, 'new_value': 149}]
2025-05-28 09:00:39,814 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-28 09:00:40,251 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-28 09:00:40,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45930.75, 'new_value': 47620.31}, {'field': 'total_amount', 'old_value': 45930.75, 'new_value': 47620.31}, {'field': 'order_count', 'old_value': 187, 'new_value': 193}]
2025-05-28 09:00:40,251 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-28 09:00:40,767 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-28 09:00:40,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200928.41, 'new_value': 210261.41}, {'field': 'total_amount', 'old_value': 200928.41, 'new_value': 210261.41}, {'field': 'order_count', 'old_value': 8357, 'new_value': 8783}]
2025-05-28 09:00:40,767 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-28 09:00:41,236 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-28 09:00:41,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111156.48, 'new_value': 112213.48}, {'field': 'total_amount', 'old_value': 142863.78, 'new_value': 143920.78}, {'field': 'order_count', 'old_value': 113, 'new_value': 115}]
2025-05-28 09:00:41,236 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-28 09:00:41,736 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-28 09:00:41,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21258.0, 'new_value': 26758.0}, {'field': 'total_amount', 'old_value': 21258.0, 'new_value': 26758.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-28 09:00:41,736 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-28 09:00:42,189 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-28 09:00:42,189 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10674.1, 'new_value': 10873.1}, {'field': 'total_amount', 'old_value': 38927.0, 'new_value': 39126.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 105}]
2025-05-28 09:00:42,205 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-28 09:00:42,642 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-28 09:00:42,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342485.62, 'new_value': 349654.12}, {'field': 'total_amount', 'old_value': 342485.62, 'new_value': 349654.12}, {'field': 'order_count', 'old_value': 3282, 'new_value': 3372}]
2025-05-28 09:00:42,642 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-28 09:00:43,064 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-28 09:00:43,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35318.0, 'new_value': 35977.0}, {'field': 'total_amount', 'old_value': 42952.0, 'new_value': 43611.0}, {'field': 'order_count', 'old_value': 325, 'new_value': 329}]
2025-05-28 09:00:43,064 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-28 09:00:43,486 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-28 09:00:43,486 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63710.78, 'new_value': 66088.21}, {'field': 'offline_amount', 'old_value': 468922.47, 'new_value': 481757.98}, {'field': 'total_amount', 'old_value': 532633.25, 'new_value': 547846.19}, {'field': 'order_count', 'old_value': 2598, 'new_value': 2676}]
2025-05-28 09:00:43,486 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-28 09:00:44,001 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-28 09:00:44,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149655.0, 'new_value': 152531.0}, {'field': 'total_amount', 'old_value': 149655.0, 'new_value': 152531.0}, {'field': 'order_count', 'old_value': 4787, 'new_value': 4879}]
2025-05-28 09:00:44,001 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-28 09:00:44,455 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-28 09:00:44,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129510.07, 'new_value': 134806.43}, {'field': 'offline_amount', 'old_value': 373561.75, 'new_value': 376733.08}, {'field': 'total_amount', 'old_value': 503071.82, 'new_value': 511539.51}, {'field': 'order_count', 'old_value': 4339, 'new_value': 4476}]
2025-05-28 09:00:44,455 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-28 09:00:44,923 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-28 09:00:44,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 221961.92, 'new_value': 225424.92}, {'field': 'offline_amount', 'old_value': 115591.9, 'new_value': 116290.9}, {'field': 'total_amount', 'old_value': 337553.82, 'new_value': 341715.82}, {'field': 'order_count', 'old_value': 621, 'new_value': 629}]
2025-05-28 09:00:44,923 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-28 09:00:45,501 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-28 09:00:45,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 725629.72, 'new_value': 745919.72}, {'field': 'total_amount', 'old_value': 725629.72, 'new_value': 745919.72}, {'field': 'order_count', 'old_value': 5528, 'new_value': 5727}]
2025-05-28 09:00:45,501 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-28 09:00:45,908 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-28 09:00:45,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2280000.0, 'new_value': 2330000.0}, {'field': 'total_amount', 'old_value': 2280000.0, 'new_value': 2330000.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 284}]
2025-05-28 09:00:45,908 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-28 09:00:46,330 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-28 09:00:46,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101464.0, 'new_value': 109124.0}, {'field': 'total_amount', 'old_value': 101464.0, 'new_value': 109124.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-28 09:00:46,330 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-28 09:00:46,830 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-28 09:00:46,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203630.0, 'new_value': 206336.7}, {'field': 'offline_amount', 'old_value': 475027.2, 'new_value': 475527.2}, {'field': 'total_amount', 'old_value': 678657.2, 'new_value': 681863.9}, {'field': 'order_count', 'old_value': 4766, 'new_value': 4796}]
2025-05-28 09:00:46,830 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-28 09:00:47,220 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-28 09:00:47,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87499.3, 'new_value': 89415.0}, {'field': 'total_amount', 'old_value': 89655.0, 'new_value': 91570.7}, {'field': 'order_count', 'old_value': 569, 'new_value': 579}]
2025-05-28 09:00:47,220 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-28 09:00:47,720 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-28 09:00:47,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51580.0, 'new_value': 52160.0}, {'field': 'total_amount', 'old_value': 51580.0, 'new_value': 52160.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 125}]
2025-05-28 09:00:47,720 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-28 09:00:48,220 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-28 09:00:48,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 525000.0, 'new_value': 530000.0}, {'field': 'total_amount', 'old_value': 525000.0, 'new_value': 530000.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 151}]
2025-05-28 09:00:48,220 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-28 09:00:48,642 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-28 09:00:48,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 505000.0, 'new_value': 510000.0}, {'field': 'total_amount', 'old_value': 505000.0, 'new_value': 510000.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 150}]
2025-05-28 09:00:48,642 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-28 09:00:49,126 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-28 09:00:49,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3398674.0, 'new_value': 3448674.0}, {'field': 'total_amount', 'old_value': 3398674.0, 'new_value': 3448674.0}, {'field': 'order_count', 'old_value': 303, 'new_value': 304}]
2025-05-28 09:00:49,126 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-28 09:00:49,548 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-28 09:00:49,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37256.0, 'new_value': 37575.0}, {'field': 'total_amount', 'old_value': 37615.0, 'new_value': 37934.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-05-28 09:00:49,548 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-28 09:00:49,986 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-28 09:00:49,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17980.5, 'new_value': 18511.15}, {'field': 'offline_amount', 'old_value': 52552.8, 'new_value': 53552.8}, {'field': 'total_amount', 'old_value': 70533.3, 'new_value': 72063.95}, {'field': 'order_count', 'old_value': 647, 'new_value': 654}]
2025-05-28 09:00:49,986 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-28 09:00:50,392 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-28 09:00:50,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111832.0, 'new_value': 115770.0}, {'field': 'total_amount', 'old_value': 111832.0, 'new_value': 115770.0}, {'field': 'order_count', 'old_value': 473, 'new_value': 491}]
2025-05-28 09:00:50,408 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-28 09:00:50,876 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-28 09:00:50,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136050.12, 'new_value': 142736.81}, {'field': 'total_amount', 'old_value': 136058.12, 'new_value': 142744.81}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-28 09:00:50,876 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-28 09:00:51,314 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-28 09:00:51,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 282298.46, 'new_value': 285365.66}, {'field': 'total_amount', 'old_value': 304785.86, 'new_value': 307853.06}, {'field': 'order_count', 'old_value': 1699, 'new_value': 1722}]
2025-05-28 09:00:51,314 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-28 09:00:51,861 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-28 09:00:51,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 283329.58, 'new_value': 285078.56}, {'field': 'total_amount', 'old_value': 283329.58, 'new_value': 285078.56}, {'field': 'order_count', 'old_value': 1617, 'new_value': 1626}]
2025-05-28 09:00:51,861 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-28 09:00:52,486 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-28 09:00:52,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110399.0, 'new_value': 111761.0}, {'field': 'total_amount', 'old_value': 110399.0, 'new_value': 111761.0}, {'field': 'order_count', 'old_value': 1696, 'new_value': 1697}]
2025-05-28 09:00:52,486 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-28 09:00:52,923 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-28 09:00:52,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96407.56, 'new_value': 98624.56}, {'field': 'total_amount', 'old_value': 96407.56, 'new_value': 98624.56}, {'field': 'order_count', 'old_value': 4976, 'new_value': 5076}]
2025-05-28 09:00:52,923 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-28 09:00:53,361 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-28 09:00:53,361 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67964.0, 'new_value': 70274.0}, {'field': 'offline_amount', 'old_value': 321443.0, 'new_value': 339056.0}, {'field': 'total_amount', 'old_value': 389407.0, 'new_value': 409330.0}, {'field': 'order_count', 'old_value': 1561, 'new_value': 1611}]
2025-05-28 09:00:53,361 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-28 09:00:53,720 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-28 09:00:53,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302595.0, 'new_value': 306293.0}, {'field': 'total_amount', 'old_value': 302595.0, 'new_value': 306293.0}, {'field': 'order_count', 'old_value': 373, 'new_value': 379}]
2025-05-28 09:00:53,720 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-28 09:00:54,189 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-28 09:00:54,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 798938.13, 'new_value': 838028.42}, {'field': 'total_amount', 'old_value': 798938.13, 'new_value': 838028.42}, {'field': 'order_count', 'old_value': 4295, 'new_value': 4425}]
2025-05-28 09:00:54,189 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-28 09:00:54,767 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-28 09:00:54,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145197.47, 'new_value': 148440.95}, {'field': 'total_amount', 'old_value': 145197.47, 'new_value': 148440.95}, {'field': 'order_count', 'old_value': 10099, 'new_value': 10338}]
2025-05-28 09:00:54,767 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-28 09:00:55,220 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-28 09:00:55,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 456253.0, 'new_value': 460985.0}, {'field': 'total_amount', 'old_value': 456253.0, 'new_value': 460985.0}, {'field': 'order_count', 'old_value': 10367, 'new_value': 10476}]
2025-05-28 09:00:55,220 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-28 09:00:55,798 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-28 09:00:55,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104109.0, 'new_value': 106778.0}, {'field': 'total_amount', 'old_value': 104109.0, 'new_value': 106778.0}, {'field': 'order_count', 'old_value': 7023, 'new_value': 7207}]
2025-05-28 09:00:55,798 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-28 09:00:56,298 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-28 09:00:56,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78068.2, 'new_value': 78744.2}, {'field': 'total_amount', 'old_value': 78348.0, 'new_value': 79024.0}, {'field': 'order_count', 'old_value': 1158, 'new_value': 1166}]
2025-05-28 09:00:56,314 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-28 09:00:56,751 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-28 09:00:56,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128107.0, 'new_value': 135584.0}, {'field': 'total_amount', 'old_value': 128187.0, 'new_value': 135664.0}, {'field': 'order_count', 'old_value': 12720, 'new_value': 13550}]
2025-05-28 09:00:56,751 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-28 09:00:57,204 - INFO - 更新表单数据成功: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-28 09:00:57,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25000.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 25000.0, 'new_value': 30000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-28 09:00:57,204 - INFO - 日期 2025-05 处理完成 - 更新: 60 条，插入: 0 条，错误: 0 条
2025-05-28 09:00:57,204 - INFO - 数据同步完成！更新: 60 条，插入: 0 条，错误: 0 条
2025-05-28 09:00:57,204 - INFO - =================同步完成====================
2025-05-28 12:00:01,884 - INFO - =================使用默认全量同步=============
2025-05-28 12:00:03,353 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 12:00:03,353 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 12:00:03,384 - INFO - 开始处理日期: 2025-01
2025-05-28 12:00:03,384 - INFO - Request Parameters - Page 1:
2025-05-28 12:00:03,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:03,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:04,665 - INFO - Response - Page 1:
2025-05-28 12:00:04,869 - INFO - 第 1 页获取到 100 条记录
2025-05-28 12:00:04,869 - INFO - Request Parameters - Page 2:
2025-05-28 12:00:04,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:04,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:05,400 - INFO - Response - Page 2:
2025-05-28 12:00:05,619 - INFO - 第 2 页获取到 100 条记录
2025-05-28 12:00:05,619 - INFO - Request Parameters - Page 3:
2025-05-28 12:00:05,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:05,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:06,134 - INFO - Response - Page 3:
2025-05-28 12:00:06,337 - INFO - 第 3 页获取到 100 条记录
2025-05-28 12:00:06,337 - INFO - Request Parameters - Page 4:
2025-05-28 12:00:06,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:06,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:06,869 - INFO - Response - Page 4:
2025-05-28 12:00:07,072 - INFO - 第 4 页获取到 100 条记录
2025-05-28 12:00:07,072 - INFO - Request Parameters - Page 5:
2025-05-28 12:00:07,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:07,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:07,619 - INFO - Response - Page 5:
2025-05-28 12:00:07,822 - INFO - 第 5 页获取到 100 条记录
2025-05-28 12:00:07,822 - INFO - Request Parameters - Page 6:
2025-05-28 12:00:07,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:07,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:08,306 - INFO - Response - Page 6:
2025-05-28 12:00:08,509 - INFO - 第 6 页获取到 100 条记录
2025-05-28 12:00:08,509 - INFO - Request Parameters - Page 7:
2025-05-28 12:00:08,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:08,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:09,072 - INFO - Response - Page 7:
2025-05-28 12:00:09,290 - INFO - 第 7 页获取到 82 条记录
2025-05-28 12:00:09,290 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 12:00:09,290 - INFO - 获取到 682 条表单数据
2025-05-28 12:00:09,306 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 12:00:09,322 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 12:00:09,322 - INFO - 开始处理日期: 2025-02
2025-05-28 12:00:09,322 - INFO - Request Parameters - Page 1:
2025-05-28 12:00:09,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:09,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:09,853 - INFO - Response - Page 1:
2025-05-28 12:00:10,056 - INFO - 第 1 页获取到 100 条记录
2025-05-28 12:00:10,056 - INFO - Request Parameters - Page 2:
2025-05-28 12:00:10,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:10,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:10,634 - INFO - Response - Page 2:
2025-05-28 12:00:10,837 - INFO - 第 2 页获取到 100 条记录
2025-05-28 12:00:10,837 - INFO - Request Parameters - Page 3:
2025-05-28 12:00:10,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:10,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:11,306 - INFO - Response - Page 3:
2025-05-28 12:00:11,509 - INFO - 第 3 页获取到 100 条记录
2025-05-28 12:00:11,509 - INFO - Request Parameters - Page 4:
2025-05-28 12:00:11,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:11,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:12,056 - INFO - Response - Page 4:
2025-05-28 12:00:12,259 - INFO - 第 4 页获取到 100 条记录
2025-05-28 12:00:12,259 - INFO - Request Parameters - Page 5:
2025-05-28 12:00:12,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:12,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:12,822 - INFO - Response - Page 5:
2025-05-28 12:00:13,025 - INFO - 第 5 页获取到 100 条记录
2025-05-28 12:00:13,025 - INFO - Request Parameters - Page 6:
2025-05-28 12:00:13,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:13,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:13,572 - INFO - Response - Page 6:
2025-05-28 12:00:13,775 - INFO - 第 6 页获取到 100 条记录
2025-05-28 12:00:13,775 - INFO - Request Parameters - Page 7:
2025-05-28 12:00:13,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:13,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:14,306 - INFO - Response - Page 7:
2025-05-28 12:00:14,509 - INFO - 第 7 页获取到 70 条记录
2025-05-28 12:00:14,509 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 12:00:14,509 - INFO - 获取到 670 条表单数据
2025-05-28 12:00:14,509 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 12:00:14,525 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 12:00:14,525 - INFO - 开始处理日期: 2025-03
2025-05-28 12:00:14,525 - INFO - Request Parameters - Page 1:
2025-05-28 12:00:14,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:14,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:14,994 - INFO - Response - Page 1:
2025-05-28 12:00:15,197 - INFO - 第 1 页获取到 100 条记录
2025-05-28 12:00:15,197 - INFO - Request Parameters - Page 2:
2025-05-28 12:00:15,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:15,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:15,837 - INFO - Response - Page 2:
2025-05-28 12:00:16,040 - INFO - 第 2 页获取到 100 条记录
2025-05-28 12:00:16,040 - INFO - Request Parameters - Page 3:
2025-05-28 12:00:16,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:16,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:16,603 - INFO - Response - Page 3:
2025-05-28 12:00:16,806 - INFO - 第 3 页获取到 100 条记录
2025-05-28 12:00:16,806 - INFO - Request Parameters - Page 4:
2025-05-28 12:00:16,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:16,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:17,275 - INFO - Response - Page 4:
2025-05-28 12:00:17,478 - INFO - 第 4 页获取到 100 条记录
2025-05-28 12:00:17,478 - INFO - Request Parameters - Page 5:
2025-05-28 12:00:17,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:17,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:18,056 - INFO - Response - Page 5:
2025-05-28 12:00:18,259 - INFO - 第 5 页获取到 100 条记录
2025-05-28 12:00:18,259 - INFO - Request Parameters - Page 6:
2025-05-28 12:00:18,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:18,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:18,837 - INFO - Response - Page 6:
2025-05-28 12:00:19,040 - INFO - 第 6 页获取到 100 条记录
2025-05-28 12:00:19,040 - INFO - Request Parameters - Page 7:
2025-05-28 12:00:19,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:19,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:19,493 - INFO - Response - Page 7:
2025-05-28 12:00:19,697 - INFO - 第 7 页获取到 61 条记录
2025-05-28 12:00:19,697 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 12:00:19,697 - INFO - 获取到 661 条表单数据
2025-05-28 12:00:19,697 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 12:00:19,712 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 12:00:19,712 - INFO - 开始处理日期: 2025-04
2025-05-28 12:00:19,712 - INFO - Request Parameters - Page 1:
2025-05-28 12:00:19,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:19,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:20,243 - INFO - Response - Page 1:
2025-05-28 12:00:20,447 - INFO - 第 1 页获取到 100 条记录
2025-05-28 12:00:20,447 - INFO - Request Parameters - Page 2:
2025-05-28 12:00:20,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:20,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:20,962 - INFO - Response - Page 2:
2025-05-28 12:00:21,165 - INFO - 第 2 页获取到 100 条记录
2025-05-28 12:00:21,165 - INFO - Request Parameters - Page 3:
2025-05-28 12:00:21,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:21,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:21,790 - INFO - Response - Page 3:
2025-05-28 12:00:21,993 - INFO - 第 3 页获取到 100 条记录
2025-05-28 12:00:21,993 - INFO - Request Parameters - Page 4:
2025-05-28 12:00:21,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:21,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:22,478 - INFO - Response - Page 4:
2025-05-28 12:00:22,681 - INFO - 第 4 页获取到 100 条记录
2025-05-28 12:00:22,681 - INFO - Request Parameters - Page 5:
2025-05-28 12:00:22,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:22,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:23,587 - INFO - Response - Page 5:
2025-05-28 12:00:23,790 - INFO - 第 5 页获取到 100 条记录
2025-05-28 12:00:23,790 - INFO - Request Parameters - Page 6:
2025-05-28 12:00:23,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:23,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:24,275 - INFO - Response - Page 6:
2025-05-28 12:00:24,478 - INFO - 第 6 页获取到 100 条记录
2025-05-28 12:00:24,478 - INFO - Request Parameters - Page 7:
2025-05-28 12:00:24,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:24,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:25,009 - INFO - Response - Page 7:
2025-05-28 12:00:25,212 - INFO - 第 7 页获取到 56 条记录
2025-05-28 12:00:25,212 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 12:00:25,212 - INFO - 获取到 656 条表单数据
2025-05-28 12:00:25,212 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 12:00:25,228 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 12:00:25,228 - INFO - 开始处理日期: 2025-05
2025-05-28 12:00:25,228 - INFO - Request Parameters - Page 1:
2025-05-28 12:00:25,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:25,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:25,775 - INFO - Response - Page 1:
2025-05-28 12:00:25,978 - INFO - 第 1 页获取到 100 条记录
2025-05-28 12:00:25,978 - INFO - Request Parameters - Page 2:
2025-05-28 12:00:25,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:25,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:26,431 - INFO - Response - Page 2:
2025-05-28 12:00:26,634 - INFO - 第 2 页获取到 100 条记录
2025-05-28 12:00:26,634 - INFO - Request Parameters - Page 3:
2025-05-28 12:00:26,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:26,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:27,072 - INFO - Response - Page 3:
2025-05-28 12:00:27,275 - INFO - 第 3 页获取到 100 条记录
2025-05-28 12:00:27,275 - INFO - Request Parameters - Page 4:
2025-05-28 12:00:27,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:27,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:28,243 - INFO - Response - Page 4:
2025-05-28 12:00:28,447 - INFO - 第 4 页获取到 100 条记录
2025-05-28 12:00:28,447 - INFO - Request Parameters - Page 5:
2025-05-28 12:00:28,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:28,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:28,962 - INFO - Response - Page 5:
2025-05-28 12:00:29,165 - INFO - 第 5 页获取到 100 条记录
2025-05-28 12:00:29,165 - INFO - Request Parameters - Page 6:
2025-05-28 12:00:29,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:29,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:29,618 - INFO - Response - Page 6:
2025-05-28 12:00:29,822 - INFO - 第 6 页获取到 100 条记录
2025-05-28 12:00:29,822 - INFO - Request Parameters - Page 7:
2025-05-28 12:00:29,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 12:00:29,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 12:00:30,165 - INFO - Response - Page 7:
2025-05-28 12:00:30,368 - INFO - 第 7 页获取到 34 条记录
2025-05-28 12:00:30,368 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 12:00:30,368 - INFO - 获取到 634 条表单数据
2025-05-28 12:00:30,368 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 12:00:30,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-28 12:00:30,853 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-28 12:00:30,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2067.0, 'new_value': 2132.0}, {'field': 'offline_amount', 'old_value': 43698.0, 'new_value': 45318.0}, {'field': 'total_amount', 'old_value': 45765.0, 'new_value': 47450.0}, {'field': 'order_count', 'old_value': 634, 'new_value': 652}]
2025-05-28 12:00:30,853 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-28 12:00:31,322 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-28 12:00:31,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392991.0, 'new_value': 400685.0}, {'field': 'total_amount', 'old_value': 392991.0, 'new_value': 400685.0}, {'field': 'order_count', 'old_value': 294, 'new_value': 300}]
2025-05-28 12:00:31,322 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC1SAZUPWCXCKCYHBKMKFBZ2I5M8U4AMM6
2025-05-28 12:00:31,743 - INFO - 更新表单数据成功: FINST-KLF66WC1SAZUPWCXCKCYHBKMKFBZ2I5M8U4AMM6
2025-05-28 12:00:31,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 350000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 350000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-28 12:00:31,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-28 12:00:32,228 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-28 12:00:32,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51940.0, 'new_value': 56445.0}, {'field': 'total_amount', 'old_value': 53530.0, 'new_value': 58035.0}, {'field': 'order_count', 'old_value': 202, 'new_value': 219}]
2025-05-28 12:00:32,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-28 12:00:32,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-28 12:00:32,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209035.0, 'new_value': 250720.0}, {'field': 'total_amount', 'old_value': 209035.0, 'new_value': 250720.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-28 12:00:32,681 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-28 12:00:33,087 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-28 12:00:33,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190649.36, 'new_value': 199039.74}, {'field': 'total_amount', 'old_value': 190649.36, 'new_value': 199039.74}, {'field': 'order_count', 'old_value': 7113, 'new_value': 7379}]
2025-05-28 12:00:33,087 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-28 12:00:33,556 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-28 12:00:33,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 763707.98, 'new_value': 785530.98}, {'field': 'total_amount', 'old_value': 763707.98, 'new_value': 785530.98}, {'field': 'order_count', 'old_value': 2397, 'new_value': 2479}]
2025-05-28 12:00:33,556 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-28 12:00:34,009 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-28 12:00:34,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45889.48, 'new_value': 48078.1}, {'field': 'total_amount', 'old_value': 45889.48, 'new_value': 48078.1}, {'field': 'order_count', 'old_value': 9002, 'new_value': 9430}]
2025-05-28 12:00:34,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-28 12:00:34,462 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-28 12:00:34,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81564.0, 'new_value': 94514.0}, {'field': 'total_amount', 'old_value': 84461.0, 'new_value': 97411.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 44}]
2025-05-28 12:00:34,462 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-28 12:00:34,915 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-28 12:00:34,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66967.84, 'new_value': 74786.83}, {'field': 'total_amount', 'old_value': 73274.35, 'new_value': 81093.34}, {'field': 'order_count', 'old_value': 2624, 'new_value': 2951}]
2025-05-28 12:00:34,915 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-28 12:00:35,384 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-28 12:00:35,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34592.87, 'new_value': 36726.87}, {'field': 'offline_amount', 'old_value': 16483.57, 'new_value': 16890.57}, {'field': 'total_amount', 'old_value': 51076.44, 'new_value': 53617.44}, {'field': 'order_count', 'old_value': 2560, 'new_value': 2701}]
2025-05-28 12:00:35,384 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-28 12:00:35,822 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-28 12:00:35,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401980.0, 'new_value': 422860.0}, {'field': 'total_amount', 'old_value': 401980.0, 'new_value': 422860.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 259}]
2025-05-28 12:00:35,837 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-28 12:00:36,384 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-28 12:00:36,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89001.0, 'new_value': 94001.0}, {'field': 'total_amount', 'old_value': 97200.0, 'new_value': 102200.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-28 12:00:36,384 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-28 12:00:36,853 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-28 12:00:36,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 446856.0, 'new_value': 463208.0}, {'field': 'offline_amount', 'old_value': 339314.0, 'new_value': 351335.0}, {'field': 'total_amount', 'old_value': 786170.0, 'new_value': 814543.0}, {'field': 'order_count', 'old_value': 863, 'new_value': 895}]
2025-05-28 12:00:36,853 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-28 12:00:37,368 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-28 12:00:37,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118225.0, 'new_value': 123329.0}, {'field': 'total_amount', 'old_value': 118225.0, 'new_value': 123329.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 139}]
2025-05-28 12:00:37,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-28 12:00:37,775 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-28 12:00:37,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121359.0, 'new_value': 124138.0}, {'field': 'offline_amount', 'old_value': 140641.28, 'new_value': 147297.28}, {'field': 'total_amount', 'old_value': 262000.28, 'new_value': 271435.28}, {'field': 'order_count', 'old_value': 5614, 'new_value': 5823}]
2025-05-28 12:00:37,775 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-28 12:00:38,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-28 12:00:38,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51604.5, 'new_value': 52804.5}, {'field': 'total_amount', 'old_value': 55564.5, 'new_value': 56764.5}, {'field': 'order_count', 'old_value': 421, 'new_value': 438}]
2025-05-28 12:00:38,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-28 12:00:38,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-28 12:00:38,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58602.38, 'new_value': 61870.69}, {'field': 'offline_amount', 'old_value': 117059.85, 'new_value': 121741.85}, {'field': 'total_amount', 'old_value': 175662.23, 'new_value': 183612.54}, {'field': 'order_count', 'old_value': 2017, 'new_value': 2102}]
2025-05-28 12:00:38,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-28 12:00:39,259 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-28 12:00:39,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21910.31, 'new_value': 22674.41}, {'field': 'offline_amount', 'old_value': 27428.76, 'new_value': 27945.97}, {'field': 'total_amount', 'old_value': 49339.07, 'new_value': 50620.38}, {'field': 'order_count', 'old_value': 2421, 'new_value': 2497}]
2025-05-28 12:00:39,259 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-28 12:00:39,759 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-28 12:00:39,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311658.9, 'new_value': 314569.4}, {'field': 'total_amount', 'old_value': 426678.6, 'new_value': 429589.1}, {'field': 'order_count', 'old_value': 3488, 'new_value': 3584}]
2025-05-28 12:00:39,759 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-28 12:00:40,243 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-28 12:00:40,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108677.0, 'new_value': 112712.0}, {'field': 'total_amount', 'old_value': 108677.0, 'new_value': 112712.0}, {'field': 'order_count', 'old_value': 5955, 'new_value': 6191}]
2025-05-28 12:00:40,243 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-28 12:00:40,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-28 12:00:40,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68908.0, 'new_value': 70008.0}, {'field': 'total_amount', 'old_value': 69207.92, 'new_value': 70307.92}, {'field': 'order_count', 'old_value': 111, 'new_value': 114}]
2025-05-28 12:00:40,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-28 12:00:41,134 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-28 12:00:41,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146881.02, 'new_value': 153150.01}, {'field': 'total_amount', 'old_value': 146881.02, 'new_value': 153150.01}, {'field': 'order_count', 'old_value': 1743, 'new_value': 1806}]
2025-05-28 12:00:41,134 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-28 12:00:41,587 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-28 12:00:41,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150536.52, 'new_value': 222933.53}, {'field': 'offline_amount', 'old_value': 17595.55, 'new_value': 18917.45}, {'field': 'total_amount', 'old_value': 168132.07, 'new_value': 241850.98}, {'field': 'order_count', 'old_value': 5642, 'new_value': 6164}]
2025-05-28 12:00:41,587 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-28 12:00:42,103 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-28 12:00:42,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 980331.0, 'new_value': 990079.3}, {'field': 'total_amount', 'old_value': 1022967.2, 'new_value': 1032715.5}, {'field': 'order_count', 'old_value': 94, 'new_value': 97}]
2025-05-28 12:00:42,103 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-28 12:00:42,525 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-28 12:00:42,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85978.0, 'new_value': 88778.0}, {'field': 'total_amount', 'old_value': 163023.0, 'new_value': 165823.0}, {'field': 'order_count', 'old_value': 2217, 'new_value': 2299}]
2025-05-28 12:00:42,525 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-28 12:00:42,978 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-28 12:00:42,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26702.0, 'new_value': 31862.0}, {'field': 'total_amount', 'old_value': 33274.0, 'new_value': 38434.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 80}]
2025-05-28 12:00:42,978 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-28 12:00:43,368 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-28 12:00:43,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183932.0, 'new_value': 186776.0}, {'field': 'total_amount', 'old_value': 183932.0, 'new_value': 186776.0}, {'field': 'order_count', 'old_value': 378, 'new_value': 386}]
2025-05-28 12:00:43,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-28 12:00:43,915 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-28 12:00:43,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175365.24, 'new_value': 176910.57}, {'field': 'total_amount', 'old_value': 175365.24, 'new_value': 176910.57}, {'field': 'order_count', 'old_value': 275, 'new_value': 285}]
2025-05-28 12:00:43,915 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-28 12:00:44,321 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-28 12:00:44,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15127.0, 'new_value': 15813.0}, {'field': 'total_amount', 'old_value': 15127.0, 'new_value': 15813.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-28 12:00:44,321 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-28 12:00:44,806 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-28 12:00:44,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83919.0, 'new_value': 86443.0}, {'field': 'total_amount', 'old_value': 83919.0, 'new_value': 86443.0}, {'field': 'order_count', 'old_value': 717, 'new_value': 737}]
2025-05-28 12:00:44,806 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-28 12:00:45,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-28 12:00:45,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183479.88, 'new_value': 188183.19}, {'field': 'offline_amount', 'old_value': 30768.66, 'new_value': 31436.81}, {'field': 'total_amount', 'old_value': 214248.54, 'new_value': 219620.0}, {'field': 'order_count', 'old_value': 789, 'new_value': 812}]
2025-05-28 12:00:45,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-28 12:00:45,712 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-28 12:00:45,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174619.0, 'new_value': 178192.0}, {'field': 'offline_amount', 'old_value': 65674.01, 'new_value': 66934.91}, {'field': 'total_amount', 'old_value': 240293.01, 'new_value': 245126.91}, {'field': 'order_count', 'old_value': 1560, 'new_value': 1599}]
2025-05-28 12:00:45,712 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-28 12:00:46,290 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-28 12:00:46,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95123.42, 'new_value': 98863.42}, {'field': 'total_amount', 'old_value': 95123.42, 'new_value': 98863.42}, {'field': 'order_count', 'old_value': 2558, 'new_value': 2671}]
2025-05-28 12:00:46,290 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-28 12:00:46,759 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-28 12:00:46,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199613.1, 'new_value': 240436.3}, {'field': 'total_amount', 'old_value': 199613.1, 'new_value': 240436.3}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-05-28 12:00:46,759 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-28 12:00:47,181 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-28 12:00:47,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168026.0, 'new_value': 171164.0}, {'field': 'offline_amount', 'old_value': 73925.38, 'new_value': 75666.28}, {'field': 'total_amount', 'old_value': 241951.38, 'new_value': 246830.28}, {'field': 'order_count', 'old_value': 1693, 'new_value': 1732}]
2025-05-28 12:00:47,181 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-28 12:00:47,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-28 12:00:47,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11344.62, 'new_value': 11989.65}, {'field': 'offline_amount', 'old_value': 181653.01, 'new_value': 187606.46}, {'field': 'total_amount', 'old_value': 192997.63, 'new_value': 199596.11}, {'field': 'order_count', 'old_value': 2109, 'new_value': 2196}]
2025-05-28 12:00:47,681 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-28 12:00:48,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-28 12:00:48,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88855.0, 'new_value': 92419.0}, {'field': 'total_amount', 'old_value': 238424.0, 'new_value': 241988.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-05-28 12:00:48,212 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-28 12:00:48,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-28 12:00:48,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33414.0, 'new_value': 33984.0}, {'field': 'total_amount', 'old_value': 35114.0, 'new_value': 35684.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 130}]
2025-05-28 12:00:48,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-28 12:00:49,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-28 12:00:49,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7795.77, 'new_value': 7866.15}, {'field': 'offline_amount', 'old_value': 111342.12, 'new_value': 113726.29}, {'field': 'total_amount', 'old_value': 119137.89, 'new_value': 121592.44}, {'field': 'order_count', 'old_value': 2879, 'new_value': 2944}]
2025-05-28 12:00:49,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-28 12:00:49,665 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-28 12:00:49,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 230570.0, 'new_value': 238402.0}, {'field': 'total_amount', 'old_value': 230570.0, 'new_value': 238402.0}, {'field': 'order_count', 'old_value': 1208, 'new_value': 1246}]
2025-05-28 12:00:49,665 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-28 12:00:50,118 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-28 12:00:50,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220450.83, 'new_value': 227343.43}, {'field': 'total_amount', 'old_value': 220450.83, 'new_value': 227343.43}, {'field': 'order_count', 'old_value': 747, 'new_value': 774}]
2025-05-28 12:00:50,118 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-28 12:00:50,556 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-28 12:00:50,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80920.92, 'new_value': 83103.04}, {'field': 'total_amount', 'old_value': 85729.87, 'new_value': 87911.99}, {'field': 'order_count', 'old_value': 5086, 'new_value': 5227}]
2025-05-28 12:00:50,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-28 12:00:51,025 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-28 12:00:51,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151341.0, 'new_value': 157449.0}, {'field': 'total_amount', 'old_value': 151341.0, 'new_value': 157449.0}, {'field': 'order_count', 'old_value': 3796, 'new_value': 3964}]
2025-05-28 12:00:51,025 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-28 12:00:51,462 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-28 12:00:51,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89825.0, 'new_value': 93069.99}, {'field': 'offline_amount', 'old_value': 902867.36, 'new_value': 934041.36}, {'field': 'total_amount', 'old_value': 992692.36, 'new_value': 1027111.35}, {'field': 'order_count', 'old_value': 3134, 'new_value': 3243}]
2025-05-28 12:00:51,462 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-28 12:00:51,884 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-28 12:00:51,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34388.6, 'new_value': 36071.5}, {'field': 'total_amount', 'old_value': 34388.6, 'new_value': 36071.5}, {'field': 'order_count', 'old_value': 64, 'new_value': 68}]
2025-05-28 12:00:51,884 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-28 12:00:52,446 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-28 12:00:52,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24724.65, 'new_value': 24790.47}, {'field': 'total_amount', 'old_value': 24724.65, 'new_value': 24790.47}, {'field': 'order_count', 'old_value': 148, 'new_value': 151}]
2025-05-28 12:00:52,446 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-28 12:00:53,009 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-28 12:00:53,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175396.0, 'new_value': 182637.0}, {'field': 'total_amount', 'old_value': 175396.0, 'new_value': 182637.0}, {'field': 'order_count', 'old_value': 6605, 'new_value': 6876}]
2025-05-28 12:00:53,009 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-28 12:00:53,493 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-28 12:00:53,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 726740.93, 'new_value': 753238.37}, {'field': 'total_amount', 'old_value': 726740.93, 'new_value': 753238.37}, {'field': 'order_count', 'old_value': 4093, 'new_value': 4331}]
2025-05-28 12:00:53,493 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-28 12:00:53,915 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-28 12:00:53,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 502380.2, 'new_value': 548135.2}, {'field': 'total_amount', 'old_value': 536726.2, 'new_value': 582481.2}, {'field': 'order_count', 'old_value': 95, 'new_value': 98}]
2025-05-28 12:00:53,915 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-28 12:00:54,400 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-28 12:00:54,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145156.3, 'new_value': 150997.15}, {'field': 'total_amount', 'old_value': 145156.3, 'new_value': 150997.15}, {'field': 'order_count', 'old_value': 5279, 'new_value': 5482}]
2025-05-28 12:00:54,400 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-28 12:00:54,821 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-28 12:00:54,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31545.79, 'new_value': 38116.59}, {'field': 'total_amount', 'old_value': 65114.01, 'new_value': 71684.81}, {'field': 'order_count', 'old_value': 64, 'new_value': 69}]
2025-05-28 12:00:54,821 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-28 12:00:55,306 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-28 12:00:55,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18800.04, 'new_value': 22275.76}, {'field': 'total_amount', 'old_value': 18800.04, 'new_value': 22275.76}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-28 12:00:55,306 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-28 12:00:55,774 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-28 12:00:55,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21000.0, 'new_value': 22725.0}, {'field': 'total_amount', 'old_value': 25698.0, 'new_value': 27423.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 159}]
2025-05-28 12:00:55,774 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-28 12:00:56,290 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-28 12:00:56,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52350.0, 'new_value': 52449.0}, {'field': 'total_amount', 'old_value': 52350.0, 'new_value': 52449.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 111}]
2025-05-28 12:00:56,290 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-28 12:00:56,728 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-28 12:00:56,728 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133280.52, 'new_value': 150026.52}, {'field': 'offline_amount', 'old_value': 542564.3, 'new_value': 544760.17}, {'field': 'total_amount', 'old_value': 675844.82, 'new_value': 694786.69}, {'field': 'order_count', 'old_value': 888, 'new_value': 916}]
2025-05-28 12:00:56,728 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-28 12:00:57,196 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-28 12:00:57,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191043.37, 'new_value': 196656.15}, {'field': 'total_amount', 'old_value': 191043.37, 'new_value': 196656.15}, {'field': 'order_count', 'old_value': 1101, 'new_value': 1130}]
2025-05-28 12:00:57,196 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-28 12:00:57,603 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-28 12:00:57,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118307.94, 'new_value': 131062.56}, {'field': 'offline_amount', 'old_value': 94941.94, 'new_value': 111164.18}, {'field': 'total_amount', 'old_value': 213249.88, 'new_value': 242226.74}, {'field': 'order_count', 'old_value': 7780, 'new_value': 8694}]
2025-05-28 12:00:57,603 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-28 12:00:58,087 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-28 12:00:58,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29331.57, 'new_value': 30280.47}, {'field': 'total_amount', 'old_value': 29331.57, 'new_value': 30280.47}, {'field': 'order_count', 'old_value': 172, 'new_value': 176}]
2025-05-28 12:00:58,087 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-28 12:00:58,571 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-28 12:00:58,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212193.77, 'new_value': 216335.52}, {'field': 'total_amount', 'old_value': 219263.35, 'new_value': 223405.1}, {'field': 'order_count', 'old_value': 1366, 'new_value': 1406}]
2025-05-28 12:00:58,571 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-28 12:00:58,978 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-28 12:00:58,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196578.0, 'new_value': 207465.0}, {'field': 'total_amount', 'old_value': 196578.0, 'new_value': 207465.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-05-28 12:00:58,978 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-28 12:00:59,431 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-28 12:00:59,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1784644.98, 'new_value': 1825535.58}, {'field': 'total_amount', 'old_value': 1838090.08, 'new_value': 1878980.68}, {'field': 'order_count', 'old_value': 3348, 'new_value': 3431}]
2025-05-28 12:00:59,431 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-28 12:00:59,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-28 12:00:59,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 268780.65, 'new_value': 274227.65}, {'field': 'offline_amount', 'old_value': 130381.0, 'new_value': 133838.0}, {'field': 'total_amount', 'old_value': 399161.65, 'new_value': 408065.65}, {'field': 'order_count', 'old_value': 2022, 'new_value': 2078}]
2025-05-28 12:00:59,946 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-28 12:01:00,415 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-28 12:01:00,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201093.62, 'new_value': 208113.62}, {'field': 'total_amount', 'old_value': 201093.62, 'new_value': 208113.62}, {'field': 'order_count', 'old_value': 11644, 'new_value': 11794}]
2025-05-28 12:01:00,415 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-28 12:01:00,931 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-28 12:01:00,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65299.0, 'new_value': 70297.0}, {'field': 'total_amount', 'old_value': 93945.0, 'new_value': 98943.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-28 12:01:00,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-28 12:01:01,321 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-28 12:01:01,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8223.96, 'new_value': 8544.35}, {'field': 'offline_amount', 'old_value': 17822.67, 'new_value': 18355.96}, {'field': 'total_amount', 'old_value': 26046.63, 'new_value': 26900.31}, {'field': 'order_count', 'old_value': 879, 'new_value': 914}]
2025-05-28 12:01:01,321 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-28 12:01:01,743 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-28 12:01:01,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188833.07, 'new_value': 195829.72}, {'field': 'offline_amount', 'old_value': 150783.25, 'new_value': 158104.31}, {'field': 'total_amount', 'old_value': 339616.32, 'new_value': 353934.03}, {'field': 'order_count', 'old_value': 3088, 'new_value': 3217}]
2025-05-28 12:01:01,743 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-28 12:01:02,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-28 12:01:02,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81306.98, 'new_value': 89748.31}, {'field': 'total_amount', 'old_value': 81330.08, 'new_value': 89771.41}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-05-28 12:01:02,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-28 12:01:02,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-28 12:01:02,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28882.0, 'new_value': 31348.0}, {'field': 'total_amount', 'old_value': 28882.0, 'new_value': 31348.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 84}]
2025-05-28 12:01:02,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-28 12:01:03,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-28 12:01:03,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68341.0, 'new_value': 69895.0}, {'field': 'total_amount', 'old_value': 69090.0, 'new_value': 70644.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-05-28 12:01:03,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-28 12:01:03,634 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-28 12:01:03,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173755.8, 'new_value': 177759.8}, {'field': 'total_amount', 'old_value': 181331.6, 'new_value': 185335.6}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-28 12:01:03,634 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-28 12:01:04,009 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-28 12:01:04,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13264.98, 'new_value': 16135.35}, {'field': 'offline_amount', 'old_value': 223526.41, 'new_value': 240751.97}, {'field': 'total_amount', 'old_value': 236791.39, 'new_value': 256887.32}, {'field': 'order_count', 'old_value': 4699, 'new_value': 4912}]
2025-05-28 12:01:04,009 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-28 12:01:04,509 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-28 12:01:04,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48817.23, 'new_value': 50470.4}, {'field': 'offline_amount', 'old_value': 96999.56, 'new_value': 98790.66}, {'field': 'total_amount', 'old_value': 145816.79, 'new_value': 149261.06}, {'field': 'order_count', 'old_value': 5337, 'new_value': 5475}]
2025-05-28 12:01:04,509 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-28 12:01:05,009 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-28 12:01:05,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 545237.16, 'new_value': 573423.56}, {'field': 'total_amount', 'old_value': 545237.16, 'new_value': 573423.56}, {'field': 'order_count', 'old_value': 5443, 'new_value': 5739}]
2025-05-28 12:01:05,009 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-28 12:01:05,446 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-28 12:01:05,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63408.0, 'new_value': 68408.0}, {'field': 'offline_amount', 'old_value': 175935.0, 'new_value': 178435.0}, {'field': 'total_amount', 'old_value': 239343.0, 'new_value': 246843.0}, {'field': 'order_count', 'old_value': 5317, 'new_value': 5496}]
2025-05-28 12:01:05,446 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-28 12:01:05,993 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-28 12:01:05,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14513.45, 'new_value': 15423.87}, {'field': 'offline_amount', 'old_value': 309484.48, 'new_value': 321623.68}, {'field': 'total_amount', 'old_value': 323997.93, 'new_value': 337047.55}, {'field': 'order_count', 'old_value': 2219, 'new_value': 2307}]
2025-05-28 12:01:05,993 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-28 12:01:06,462 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-28 12:01:06,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26384.0, 'new_value': 26542.0}, {'field': 'offline_amount', 'old_value': 297270.8, 'new_value': 297469.8}, {'field': 'total_amount', 'old_value': 323654.8, 'new_value': 324011.8}, {'field': 'order_count', 'old_value': 88, 'new_value': 90}]
2025-05-28 12:01:06,462 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-28 12:01:06,978 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-28 12:01:06,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159013.74, 'new_value': 163988.81}, {'field': 'offline_amount', 'old_value': 121977.45, 'new_value': 122947.45}, {'field': 'total_amount', 'old_value': 280991.19, 'new_value': 286936.26}, {'field': 'order_count', 'old_value': 2791, 'new_value': 2835}]
2025-05-28 12:01:06,978 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-28 12:01:07,478 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-28 12:01:07,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 533317.04, 'new_value': 534427.04}, {'field': 'offline_amount', 'old_value': 246699.9, 'new_value': 246774.9}, {'field': 'total_amount', 'old_value': 780016.94, 'new_value': 781201.94}, {'field': 'order_count', 'old_value': 6800, 'new_value': 6819}]
2025-05-28 12:01:07,478 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-28 12:01:07,931 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-28 12:01:07,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84081.2, 'new_value': 88081.2}, {'field': 'offline_amount', 'old_value': 8405.45, 'new_value': 9298.75}, {'field': 'total_amount', 'old_value': 92486.65, 'new_value': 97379.95}, {'field': 'order_count', 'old_value': 285, 'new_value': 301}]
2025-05-28 12:01:07,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-28 12:01:08,368 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-28 12:01:08,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73577.55, 'new_value': 75857.41}, {'field': 'total_amount', 'old_value': 73577.55, 'new_value': 75857.41}, {'field': 'order_count', 'old_value': 2109, 'new_value': 2171}]
2025-05-28 12:01:08,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-28 12:01:09,071 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-28 12:01:09,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 640757.0, 'new_value': 680147.0}, {'field': 'total_amount', 'old_value': 640757.0, 'new_value': 680147.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 82}]
2025-05-28 12:01:09,071 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-28 12:01:09,556 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-28 12:01:09,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139195.1, 'new_value': 147379.84}, {'field': 'offline_amount', 'old_value': 450733.2, 'new_value': 466993.13}, {'field': 'total_amount', 'old_value': 589928.3, 'new_value': 614372.97}, {'field': 'order_count', 'old_value': 2937, 'new_value': 2994}]
2025-05-28 12:01:09,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-28 12:01:10,024 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-28 12:01:10,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 440355.51, 'new_value': 442332.34}, {'field': 'total_amount', 'old_value': 440355.51, 'new_value': 442332.34}, {'field': 'order_count', 'old_value': 560, 'new_value': 570}]
2025-05-28 12:01:10,024 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-28 12:01:10,446 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-28 12:01:10,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17749.97, 'new_value': 18544.74}, {'field': 'offline_amount', 'old_value': 465553.34, 'new_value': 476104.94}, {'field': 'total_amount', 'old_value': 483303.31, 'new_value': 494649.68}, {'field': 'order_count', 'old_value': 1912, 'new_value': 1979}]
2025-05-28 12:01:10,446 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-28 12:01:10,853 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-28 12:01:10,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88505.0, 'new_value': 91386.0}, {'field': 'offline_amount', 'old_value': 85956.76, 'new_value': 91519.76}, {'field': 'total_amount', 'old_value': 174461.76, 'new_value': 182905.76}, {'field': 'order_count', 'old_value': 202, 'new_value': 213}]
2025-05-28 12:01:10,853 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-28 12:01:11,321 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-28 12:01:11,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14927.0, 'new_value': 15440.0}, {'field': 'total_amount', 'old_value': 14927.0, 'new_value': 15440.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-28 12:01:11,321 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-28 12:01:11,790 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-28 12:01:11,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100784.41, 'new_value': 102407.87}, {'field': 'total_amount', 'old_value': 100784.41, 'new_value': 102407.87}, {'field': 'order_count', 'old_value': 3843, 'new_value': 3910}]
2025-05-28 12:01:11,790 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-28 12:01:12,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-28 12:01:12,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46926.0, 'new_value': 47221.0}, {'field': 'total_amount', 'old_value': 46926.0, 'new_value': 47221.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-28 12:01:12,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-28 12:01:12,649 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-28 12:01:12,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97173.12, 'new_value': 100073.12}, {'field': 'offline_amount', 'old_value': 1205075.89, 'new_value': 1249918.16}, {'field': 'total_amount', 'old_value': 1302249.01, 'new_value': 1349991.28}, {'field': 'order_count', 'old_value': 10536, 'new_value': 10962}]
2025-05-28 12:01:12,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-28 12:01:13,024 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-28 12:01:13,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10698.33, 'new_value': 11140.93}, {'field': 'total_amount', 'old_value': 26274.93, 'new_value': 26717.53}, {'field': 'order_count', 'old_value': 110, 'new_value': 112}]
2025-05-28 12:01:13,024 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-28 12:01:13,556 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-28 12:01:13,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112397.88, 'new_value': 113851.44}, {'field': 'total_amount', 'old_value': 112397.88, 'new_value': 113851.44}, {'field': 'order_count', 'old_value': 819, 'new_value': 829}]
2025-05-28 12:01:13,556 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-28 12:01:13,993 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-28 12:01:13,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84763.59, 'new_value': 85730.59}, {'field': 'total_amount', 'old_value': 84763.59, 'new_value': 85730.59}, {'field': 'order_count', 'old_value': 487, 'new_value': 491}]
2025-05-28 12:01:13,993 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-28 12:01:14,477 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-28 12:01:14,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106964.3, 'new_value': 109399.2}, {'field': 'total_amount', 'old_value': 106964.3, 'new_value': 109399.2}, {'field': 'order_count', 'old_value': 329, 'new_value': 340}]
2025-05-28 12:01:14,477 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-28 12:01:14,946 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-28 12:01:14,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13344.01, 'new_value': 14556.38}, {'field': 'offline_amount', 'old_value': 130466.48, 'new_value': 134610.77}, {'field': 'total_amount', 'old_value': 143810.49, 'new_value': 149167.15}, {'field': 'order_count', 'old_value': 3767, 'new_value': 3912}]
2025-05-28 12:01:14,946 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-28 12:01:15,368 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-28 12:01:15,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 213690.33, 'new_value': 222747.25}, {'field': 'offline_amount', 'old_value': 363104.13, 'new_value': 380429.5}, {'field': 'total_amount', 'old_value': 576794.46, 'new_value': 603176.75}, {'field': 'order_count', 'old_value': 16434, 'new_value': 17058}]
2025-05-28 12:01:15,368 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-28 12:01:15,821 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-28 12:01:15,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45986.0, 'new_value': 46532.0}, {'field': 'total_amount', 'old_value': 45986.0, 'new_value': 46532.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 130}]
2025-05-28 12:01:15,821 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-28 12:01:16,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-28 12:01:16,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8107.42, 'new_value': 8756.85}, {'field': 'offline_amount', 'old_value': 32571.0, 'new_value': 32868.0}, {'field': 'total_amount', 'old_value': 40678.42, 'new_value': 41624.85}, {'field': 'order_count', 'old_value': 211, 'new_value': 221}]
2025-05-28 12:01:16,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-28 12:01:16,696 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-28 12:01:16,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38388.0, 'new_value': 38887.0}, {'field': 'total_amount', 'old_value': 38388.0, 'new_value': 38887.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 117}]
2025-05-28 12:01:16,696 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-28 12:01:17,321 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-28 12:01:17,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46845.16, 'new_value': 48009.66}, {'field': 'total_amount', 'old_value': 47366.76, 'new_value': 48531.26}, {'field': 'order_count', 'old_value': 416, 'new_value': 424}]
2025-05-28 12:01:17,321 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-28 12:01:17,790 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-28 12:01:17,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6509.5, 'new_value': 6825.1}, {'field': 'total_amount', 'old_value': 6509.5, 'new_value': 6825.1}, {'field': 'order_count', 'old_value': 496, 'new_value': 523}]
2025-05-28 12:01:17,790 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-28 12:01:18,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-28 12:01:18,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6066.0, 'new_value': 6348.0}, {'field': 'offline_amount', 'old_value': 27116.8, 'new_value': 28241.2}, {'field': 'total_amount', 'old_value': 33182.8, 'new_value': 34589.2}, {'field': 'order_count', 'old_value': 1283, 'new_value': 1328}]
2025-05-28 12:01:18,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-28 12:01:18,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-28 12:01:18,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92720.66, 'new_value': 93641.66}, {'field': 'total_amount', 'old_value': 92720.66, 'new_value': 93641.66}, {'field': 'order_count', 'old_value': 344, 'new_value': 349}]
2025-05-28 12:01:18,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-28 12:01:19,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-28 12:01:19,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1326000.0, 'new_value': 1366000.0}, {'field': 'total_amount', 'old_value': 1326000.0, 'new_value': 1366000.0}, {'field': 'order_count', 'old_value': 345, 'new_value': 346}]
2025-05-28 12:01:19,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-28 12:01:19,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-28 12:01:19,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29543.0, 'new_value': 30123.6}, {'field': 'offline_amount', 'old_value': 22684.5, 'new_value': 22926.5}, {'field': 'total_amount', 'old_value': 52227.5, 'new_value': 53050.1}, {'field': 'order_count', 'old_value': 282, 'new_value': 289}]
2025-05-28 12:01:19,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-28 12:01:20,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-28 12:01:20,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69785.0, 'new_value': 77011.0}, {'field': 'total_amount', 'old_value': 69785.0, 'new_value': 77011.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-05-28 12:01:20,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-28 12:01:20,571 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-28 12:01:20,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195129.6, 'new_value': 201758.3}, {'field': 'total_amount', 'old_value': 195129.6, 'new_value': 201758.3}, {'field': 'order_count', 'old_value': 733, 'new_value': 759}]
2025-05-28 12:01:20,571 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-28 12:01:20,977 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-28 12:01:20,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17856.72, 'new_value': 18568.09}, {'field': 'offline_amount', 'old_value': 283387.54, 'new_value': 293509.14}, {'field': 'total_amount', 'old_value': 301244.26, 'new_value': 312077.23}, {'field': 'order_count', 'old_value': 16690, 'new_value': 17317}]
2025-05-28 12:01:20,977 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-28 12:01:21,384 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-28 12:01:21,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6602.0, 'new_value': 7399.0}, {'field': 'total_amount', 'old_value': 6602.0, 'new_value': 7399.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-28 12:01:21,384 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-28 12:01:21,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-28 12:01:21,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305508.97, 'new_value': 314873.6}, {'field': 'total_amount', 'old_value': 305508.97, 'new_value': 314873.6}, {'field': 'order_count', 'old_value': 8467, 'new_value': 8756}]
2025-05-28 12:01:21,821 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-28 12:01:22,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-28 12:01:22,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52508.15, 'new_value': 53465.81}, {'field': 'offline_amount', 'old_value': 36276.0, 'new_value': 37198.0}, {'field': 'total_amount', 'old_value': 88784.15, 'new_value': 90663.81}, {'field': 'order_count', 'old_value': 1106, 'new_value': 1128}]
2025-05-28 12:01:22,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-28 12:01:22,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-28 12:01:22,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154164.25, 'new_value': 161273.32}, {'field': 'total_amount', 'old_value': 154164.25, 'new_value': 161273.32}, {'field': 'order_count', 'old_value': 763, 'new_value': 802}]
2025-05-28 12:01:22,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-28 12:01:23,243 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-28 12:01:23,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24086.17, 'new_value': 25032.8}, {'field': 'offline_amount', 'old_value': 44728.34, 'new_value': 46583.18}, {'field': 'total_amount', 'old_value': 68814.51, 'new_value': 71615.98}, {'field': 'order_count', 'old_value': 2478, 'new_value': 2582}]
2025-05-28 12:01:23,243 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-28 12:01:23,727 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-28 12:01:23,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76592.0, 'new_value': 80105.0}, {'field': 'total_amount', 'old_value': 79000.0, 'new_value': 82513.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 342}]
2025-05-28 12:01:23,727 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-28 12:01:24,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-28 12:01:24,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63936.0, 'new_value': 64752.0}, {'field': 'total_amount', 'old_value': 63936.0, 'new_value': 64752.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-28 12:01:24,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-28 12:01:24,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-28 12:01:24,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64604.96, 'new_value': 66107.04}, {'field': 'total_amount', 'old_value': 85654.16, 'new_value': 87156.24}, {'field': 'order_count', 'old_value': 953, 'new_value': 971}]
2025-05-28 12:01:24,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-28 12:01:25,181 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-28 12:01:25,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104012.8, 'new_value': 105020.4}, {'field': 'offline_amount', 'old_value': 146320.3, 'new_value': 149868.3}, {'field': 'total_amount', 'old_value': 250333.1, 'new_value': 254888.7}, {'field': 'order_count', 'old_value': 1579, 'new_value': 1608}]
2025-05-28 12:01:25,181 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-28 12:01:25,602 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-28 12:01:25,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8737.0, 'new_value': 9836.0}, {'field': 'total_amount', 'old_value': 8737.0, 'new_value': 9836.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-28 12:01:25,602 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-28 12:01:26,087 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-28 12:01:26,087 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14643.76, 'new_value': 14967.26}, {'field': 'offline_amount', 'old_value': 94712.46, 'new_value': 98071.25}, {'field': 'total_amount', 'old_value': 109356.22, 'new_value': 113038.51}, {'field': 'order_count', 'old_value': 2929, 'new_value': 3004}]
2025-05-28 12:01:26,087 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-28 12:01:26,509 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-28 12:01:26,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23790.07, 'new_value': 24940.29}, {'field': 'offline_amount', 'old_value': 43943.87, 'new_value': 46035.03}, {'field': 'total_amount', 'old_value': 67733.94, 'new_value': 70975.32}, {'field': 'order_count', 'old_value': 3564, 'new_value': 3730}]
2025-05-28 12:01:26,509 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-28 12:01:27,009 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-28 12:01:27,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54413.59, 'new_value': 58792.62}, {'field': 'offline_amount', 'old_value': 265988.29, 'new_value': 282807.29}, {'field': 'total_amount', 'old_value': 320401.88, 'new_value': 341599.91}, {'field': 'order_count', 'old_value': 9979, 'new_value': 10717}]
2025-05-28 12:01:27,009 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-28 12:01:27,509 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-28 12:01:27,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11572.61, 'new_value': 12274.17}, {'field': 'offline_amount', 'old_value': 12850.11, 'new_value': 13059.41}, {'field': 'total_amount', 'old_value': 24422.72, 'new_value': 25333.58}, {'field': 'order_count', 'old_value': 1988, 'new_value': 2073}]
2025-05-28 12:01:27,509 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-28 12:01:27,946 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-28 12:01:27,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51747.0, 'new_value': 55862.0}, {'field': 'total_amount', 'old_value': 52096.0, 'new_value': 56211.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 105}]
2025-05-28 12:01:27,946 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-28 12:01:28,477 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-28 12:01:28,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6654713.0, 'new_value': 6870171.0}, {'field': 'total_amount', 'old_value': 6654713.0, 'new_value': 6870171.0}, {'field': 'order_count', 'old_value': 112621, 'new_value': 116502}]
2025-05-28 12:01:28,477 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-28 12:01:28,962 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-28 12:01:28,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53362.0, 'new_value': 53974.0}, {'field': 'total_amount', 'old_value': 53362.0, 'new_value': 53974.0}, {'field': 'order_count', 'old_value': 385, 'new_value': 389}]
2025-05-28 12:01:28,962 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-28 12:01:29,493 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-28 12:01:29,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59376.91, 'new_value': 61659.52}, {'field': 'offline_amount', 'old_value': 413114.82, 'new_value': 417669.84}, {'field': 'total_amount', 'old_value': 472491.73, 'new_value': 479329.36}, {'field': 'order_count', 'old_value': 3939, 'new_value': 3995}]
2025-05-28 12:01:29,493 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-28 12:01:30,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-28 12:01:30,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46755.98, 'new_value': 47184.98}, {'field': 'offline_amount', 'old_value': 497267.5, 'new_value': 509267.5}, {'field': 'total_amount', 'old_value': 544023.48, 'new_value': 556452.48}, {'field': 'order_count', 'old_value': 4265, 'new_value': 4371}]
2025-05-28 12:01:30,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-28 12:01:30,477 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-28 12:01:30,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124339.53, 'new_value': 127325.23}, {'field': 'total_amount', 'old_value': 124339.53, 'new_value': 127325.23}, {'field': 'order_count', 'old_value': 3623, 'new_value': 3712}]
2025-05-28 12:01:30,477 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-28 12:01:30,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-28 12:01:30,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36588.2, 'new_value': 49202.2}, {'field': 'total_amount', 'old_value': 36782.2, 'new_value': 49396.2}, {'field': 'order_count', 'old_value': 18, 'new_value': 24}]
2025-05-28 12:01:30,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-28 12:01:31,399 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-28 12:01:31,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128230.87, 'new_value': 133709.79}, {'field': 'offline_amount', 'old_value': 278597.68, 'new_value': 287219.34}, {'field': 'total_amount', 'old_value': 406828.55, 'new_value': 420929.13}, {'field': 'order_count', 'old_value': 4786, 'new_value': 4942}]
2025-05-28 12:01:31,399 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-28 12:01:31,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-28 12:01:31,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34058.48, 'new_value': 35993.08}, {'field': 'total_amount', 'old_value': 34058.48, 'new_value': 35993.08}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-28 12:01:31,868 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-28 12:01:32,305 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-28 12:01:32,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126300.0, 'new_value': 130200.0}, {'field': 'total_amount', 'old_value': 126300.0, 'new_value': 130200.0}, {'field': 'order_count', 'old_value': 4543, 'new_value': 4544}]
2025-05-28 12:01:32,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-28 12:01:32,852 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-28 12:01:32,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90320.21, 'new_value': 93046.17}, {'field': 'offline_amount', 'old_value': 40234.71, 'new_value': 40632.21}, {'field': 'total_amount', 'old_value': 130554.92, 'new_value': 133678.38}, {'field': 'order_count', 'old_value': 8015, 'new_value': 8222}]
2025-05-28 12:01:32,852 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-28 12:01:33,368 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-28 12:01:33,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32427.07, 'new_value': 33527.07}, {'field': 'total_amount', 'old_value': 32427.07, 'new_value': 33527.07}, {'field': 'order_count', 'old_value': 3100, 'new_value': 3101}]
2025-05-28 12:01:33,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-28 12:01:33,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-28 12:01:33,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 234420.0, 'new_value': 243324.0}, {'field': 'total_amount', 'old_value': 234420.0, 'new_value': 243324.0}, {'field': 'order_count', 'old_value': 19535, 'new_value': 20277}]
2025-05-28 12:01:33,805 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-28 12:01:34,305 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-28 12:01:34,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113906.98, 'new_value': 115102.98}, {'field': 'total_amount', 'old_value': 113906.98, 'new_value': 115102.98}, {'field': 'order_count', 'old_value': 1026, 'new_value': 1051}]
2025-05-28 12:01:34,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-28 12:01:34,774 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-28 12:01:34,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101518.73, 'new_value': 105293.51}, {'field': 'offline_amount', 'old_value': 263711.88, 'new_value': 269513.33}, {'field': 'total_amount', 'old_value': 365230.61, 'new_value': 374806.84}, {'field': 'order_count', 'old_value': 12268, 'new_value': 12653}]
2025-05-28 12:01:34,774 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-28 12:01:35,243 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-28 12:01:35,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7346800.0, 'new_value': 7376800.0}, {'field': 'total_amount', 'old_value': 7346800.0, 'new_value': 7376800.0}, {'field': 'order_count', 'old_value': 222, 'new_value': 223}]
2025-05-28 12:01:35,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-28 12:01:35,649 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-28 12:01:35,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46701.5, 'new_value': 47360.0}, {'field': 'total_amount', 'old_value': 46701.5, 'new_value': 47360.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 69}]
2025-05-28 12:01:35,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-28 12:01:36,118 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-28 12:01:36,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 321593.74, 'new_value': 332976.53}, {'field': 'offline_amount', 'old_value': 19354.62, 'new_value': 19644.14}, {'field': 'total_amount', 'old_value': 340948.36, 'new_value': 352620.67}, {'field': 'order_count', 'old_value': 13599, 'new_value': 14023}]
2025-05-28 12:01:36,118 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-28 12:01:36,571 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-28 12:01:36,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120148.9, 'new_value': 122767.9}, {'field': 'total_amount', 'old_value': 141802.8, 'new_value': 144421.8}, {'field': 'order_count', 'old_value': 195, 'new_value': 198}]
2025-05-28 12:01:36,571 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-28 12:01:37,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-28 12:01:37,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47038.35, 'new_value': 48508.1}, {'field': 'total_amount', 'old_value': 47038.35, 'new_value': 48508.1}, {'field': 'order_count', 'old_value': 2096, 'new_value': 2160}]
2025-05-28 12:01:37,040 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-28 12:01:37,618 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-28 12:01:37,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 437311.0, 'new_value': 438311.0}, {'field': 'total_amount', 'old_value': 446129.99, 'new_value': 447129.99}, {'field': 'order_count', 'old_value': 79, 'new_value': 80}]
2025-05-28 12:01:37,618 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-28 12:01:38,055 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-28 12:01:38,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138483.82, 'new_value': 143651.06}, {'field': 'offline_amount', 'old_value': 281742.35, 'new_value': 286766.09}, {'field': 'total_amount', 'old_value': 420226.17, 'new_value': 430417.15}, {'field': 'order_count', 'old_value': 5214, 'new_value': 5322}]
2025-05-28 12:01:38,055 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-28 12:01:38,477 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-28 12:01:38,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127627.6, 'new_value': 134537.6}, {'field': 'total_amount', 'old_value': 259091.95, 'new_value': 266001.95}, {'field': 'order_count', 'old_value': 6852, 'new_value': 7075}]
2025-05-28 12:01:38,477 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-28 12:01:38,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-28 12:01:38,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81710.35, 'new_value': 82335.65}, {'field': 'total_amount', 'old_value': 81710.35, 'new_value': 82335.65}, {'field': 'order_count', 'old_value': 591, 'new_value': 599}]
2025-05-28 12:01:38,884 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-28 12:01:39,384 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-28 12:01:39,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 432591.0, 'new_value': 445142.0}, {'field': 'total_amount', 'old_value': 432591.0, 'new_value': 445142.0}, {'field': 'order_count', 'old_value': 11795, 'new_value': 12207}]
2025-05-28 12:01:39,384 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-28 12:01:39,774 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-28 12:01:39,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 443927.86, 'new_value': 453927.86}, {'field': 'total_amount', 'old_value': 443927.86, 'new_value': 453927.86}, {'field': 'order_count', 'old_value': 833, 'new_value': 834}]
2025-05-28 12:01:39,774 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-28 12:01:40,259 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-28 12:01:40,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106325.44, 'new_value': 113200.64}, {'field': 'total_amount', 'old_value': 106325.44, 'new_value': 113200.64}, {'field': 'order_count', 'old_value': 3090, 'new_value': 3275}]
2025-05-28 12:01:40,259 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-28 12:01:40,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-28 12:01:40,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16675.0, 'new_value': 16914.0}, {'field': 'total_amount', 'old_value': 16675.0, 'new_value': 16914.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-28 12:01:40,743 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-28 12:01:41,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-28 12:01:41,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31068.0, 'new_value': 31518.0}, {'field': 'total_amount', 'old_value': 31068.0, 'new_value': 31518.0}, {'field': 'order_count', 'old_value': 301, 'new_value': 304}]
2025-05-28 12:01:41,227 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-28 12:01:41,712 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-28 12:01:41,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210261.41, 'new_value': 218769.83}, {'field': 'total_amount', 'old_value': 210261.41, 'new_value': 218769.83}, {'field': 'order_count', 'old_value': 8783, 'new_value': 9200}]
2025-05-28 12:01:41,712 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-28 12:01:42,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-28 12:01:42,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59442.97, 'new_value': 61820.47}, {'field': 'offline_amount', 'old_value': 35032.87, 'new_value': 36210.3}, {'field': 'total_amount', 'old_value': 94475.84, 'new_value': 98030.77}, {'field': 'order_count', 'old_value': 5125, 'new_value': 5328}]
2025-05-28 12:01:42,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-28 12:01:42,680 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-28 12:01:42,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83679.0, 'new_value': 92498.0}, {'field': 'total_amount', 'old_value': 93590.0, 'new_value': 102409.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-05-28 12:01:42,680 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-28 12:01:43,087 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-28 12:01:43,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95597.0, 'new_value': 95797.0}, {'field': 'total_amount', 'old_value': 95597.0, 'new_value': 95797.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-28 12:01:43,087 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-28 12:01:43,493 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-28 12:01:43,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28876.0, 'new_value': 29712.0}, {'field': 'total_amount', 'old_value': 28876.0, 'new_value': 29712.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 314}]
2025-05-28 12:01:43,493 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-28 12:01:43,930 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-28 12:01:43,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21759.58, 'new_value': 22056.08}, {'field': 'total_amount', 'old_value': 21759.58, 'new_value': 22056.08}, {'field': 'order_count', 'old_value': 181, 'new_value': 187}]
2025-05-28 12:01:43,930 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-28 12:01:44,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-28 12:01:44,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 434260.18, 'new_value': 447221.48}, {'field': 'total_amount', 'old_value': 434260.18, 'new_value': 447221.48}, {'field': 'order_count', 'old_value': 1550, 'new_value': 1593}]
2025-05-28 12:01:44,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-28 12:01:44,930 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-28 12:01:44,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316371.4, 'new_value': 332482.3}, {'field': 'total_amount', 'old_value': 327647.6, 'new_value': 343758.5}, {'field': 'order_count', 'old_value': 8225, 'new_value': 8535}]
2025-05-28 12:01:44,930 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-28 12:01:45,399 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-28 12:01:45,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40835.44, 'new_value': 42456.51}, {'field': 'total_amount', 'old_value': 40835.44, 'new_value': 42456.51}, {'field': 'order_count', 'old_value': 5272, 'new_value': 5477}]
2025-05-28 12:01:45,399 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-28 12:01:45,821 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-28 12:01:45,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30600.54, 'new_value': 32127.7}, {'field': 'offline_amount', 'old_value': 64296.26, 'new_value': 68564.12}, {'field': 'total_amount', 'old_value': 94896.8, 'new_value': 100691.82}, {'field': 'order_count', 'old_value': 817, 'new_value': 866}]
2025-05-28 12:01:45,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-28 12:01:46,227 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-28 12:01:46,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26898.5, 'new_value': 27963.43}, {'field': 'offline_amount', 'old_value': 34547.33, 'new_value': 35604.18}, {'field': 'total_amount', 'old_value': 61445.83, 'new_value': 63567.61}, {'field': 'order_count', 'old_value': 2785, 'new_value': 2882}]
2025-05-28 12:01:46,227 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-28 12:01:46,696 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-28 12:01:46,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83550.97, 'new_value': 85005.04}, {'field': 'offline_amount', 'old_value': 122064.69, 'new_value': 123263.59}, {'field': 'total_amount', 'old_value': 205615.66, 'new_value': 208268.63}, {'field': 'order_count', 'old_value': 2146, 'new_value': 2180}]
2025-05-28 12:01:46,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-28 12:01:47,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-28 12:01:47,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89666.0, 'new_value': 96072.0}, {'field': 'total_amount', 'old_value': 94867.0, 'new_value': 101273.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 293}]
2025-05-28 12:01:47,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-28 12:01:47,587 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-28 12:01:47,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1787053.02, 'new_value': 1866959.51}, {'field': 'total_amount', 'old_value': 1949493.32, 'new_value': 2029399.81}, {'field': 'order_count', 'old_value': 6732, 'new_value': 7004}]
2025-05-28 12:01:47,587 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-28 12:01:48,071 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-28 12:01:48,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34675.0, 'new_value': 35875.0}, {'field': 'total_amount', 'old_value': 36051.0, 'new_value': 37251.0}, {'field': 'order_count', 'old_value': 3638, 'new_value': 3639}]
2025-05-28 12:01:48,071 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-28 12:01:48,602 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-28 12:01:48,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319969.1, 'new_value': 320265.1}, {'field': 'total_amount', 'old_value': 319969.1, 'new_value': 320265.1}, {'field': 'order_count', 'old_value': 78, 'new_value': 81}]
2025-05-28 12:01:48,602 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-28 12:01:49,102 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-28 12:01:49,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17815.0, 'new_value': 18525.9}, {'field': 'total_amount', 'old_value': 64492.9, 'new_value': 65203.8}, {'field': 'order_count', 'old_value': 199, 'new_value': 204}]
2025-05-28 12:01:49,102 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-28 12:01:49,571 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-28 12:01:49,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 470991.32, 'new_value': 486817.32}, {'field': 'total_amount', 'old_value': 470991.32, 'new_value': 486817.32}, {'field': 'order_count', 'old_value': 2388, 'new_value': 2487}]
2025-05-28 12:01:49,571 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-28 12:01:50,055 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-28 12:01:50,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11128.0, 'new_value': 11578.0}, {'field': 'total_amount', 'old_value': 13134.0, 'new_value': 13584.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 132}]
2025-05-28 12:01:50,055 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-28 12:01:50,462 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-28 12:01:50,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29649.0, 'new_value': 30101.0}, {'field': 'total_amount', 'old_value': 29649.0, 'new_value': 30101.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 139}]
2025-05-28 12:01:50,462 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-28 12:01:50,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-28 12:01:50,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106270.2, 'new_value': 113701.7}, {'field': 'offline_amount', 'old_value': 152796.1, 'new_value': 153708.5}, {'field': 'total_amount', 'old_value': 259066.3, 'new_value': 267410.2}, {'field': 'order_count', 'old_value': 5229, 'new_value': 5396}]
2025-05-28 12:01:50,884 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-28 12:01:51,462 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-28 12:01:51,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 491214.52, 'new_value': 502801.71}, {'field': 'total_amount', 'old_value': 491214.52, 'new_value': 502801.71}, {'field': 'order_count', 'old_value': 6738, 'new_value': 6925}]
2025-05-28 12:01:51,462 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-28 12:01:51,915 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-28 12:01:51,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 628596.72, 'new_value': 655376.52}, {'field': 'total_amount', 'old_value': 630509.77, 'new_value': 657289.57}, {'field': 'order_count', 'old_value': 1504, 'new_value': 1569}]
2025-05-28 12:01:51,915 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-28 12:01:52,305 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-28 12:01:52,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185150.0, 'new_value': 191590.0}, {'field': 'total_amount', 'old_value': 185151.0, 'new_value': 191591.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-28 12:01:52,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-28 12:01:52,805 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-28 12:01:52,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92504.83, 'new_value': 94582.91}, {'field': 'total_amount', 'old_value': 92504.83, 'new_value': 94582.91}, {'field': 'order_count', 'old_value': 2888, 'new_value': 2959}]
2025-05-28 12:01:52,805 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-28 12:01:53,258 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-28 12:01:53,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9938.45, 'new_value': 10372.75}, {'field': 'offline_amount', 'old_value': 33213.67, 'new_value': 34047.86}, {'field': 'total_amount', 'old_value': 43152.12, 'new_value': 44420.61}, {'field': 'order_count', 'old_value': 1522, 'new_value': 1559}]
2025-05-28 12:01:53,258 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-28 12:01:53,743 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-28 12:01:53,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45573.18, 'new_value': 47494.36}, {'field': 'offline_amount', 'old_value': 55769.67, 'new_value': 55876.69}, {'field': 'total_amount', 'old_value': 101342.85, 'new_value': 103371.05}, {'field': 'order_count', 'old_value': 353, 'new_value': 366}]
2025-05-28 12:01:53,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-28 12:01:54,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-28 12:01:54,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153054.4, 'new_value': 155295.03}, {'field': 'total_amount', 'old_value': 153054.4, 'new_value': 155295.03}, {'field': 'order_count', 'old_value': 3963, 'new_value': 4028}]
2025-05-28 12:01:54,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-28 12:01:54,633 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-28 12:01:54,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36904.39, 'new_value': 37858.92}, {'field': 'offline_amount', 'old_value': 325686.72, 'new_value': 333392.87}, {'field': 'total_amount', 'old_value': 362591.11, 'new_value': 371251.79}, {'field': 'order_count', 'old_value': 8460, 'new_value': 8616}]
2025-05-28 12:01:54,633 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-28 12:01:55,133 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-28 12:01:55,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 331495.92, 'new_value': 336711.92}, {'field': 'offline_amount', 'old_value': 11576.5, 'new_value': 11596.5}, {'field': 'total_amount', 'old_value': 343072.42, 'new_value': 348308.42}, {'field': 'order_count', 'old_value': 2983, 'new_value': 3025}]
2025-05-28 12:01:55,133 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-28 12:01:55,618 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-28 12:01:55,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22120.0, 'new_value': 23147.0}, {'field': 'total_amount', 'old_value': 22120.0, 'new_value': 23147.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 128}]
2025-05-28 12:01:55,618 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-28 12:01:56,118 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-28 12:01:56,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431919.0, 'new_value': 439664.8}, {'field': 'total_amount', 'old_value': 431919.0, 'new_value': 439664.8}, {'field': 'order_count', 'old_value': 2123, 'new_value': 2161}]
2025-05-28 12:01:56,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-28 12:01:56,540 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-28 12:01:56,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28838.8, 'new_value': 29546.0}, {'field': 'total_amount', 'old_value': 28838.8, 'new_value': 29546.0}, {'field': 'order_count', 'old_value': 790, 'new_value': 818}]
2025-05-28 12:01:56,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-28 12:01:56,993 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-28 12:01:56,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138042.23, 'new_value': 141547.32}, {'field': 'offline_amount', 'old_value': 56304.6, 'new_value': 57781.13}, {'field': 'total_amount', 'old_value': 194346.83, 'new_value': 199328.45}, {'field': 'order_count', 'old_value': 11874, 'new_value': 12173}]
2025-05-28 12:01:56,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-28 12:01:57,399 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-28 12:01:57,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7044.0, 'new_value': 7305.0}, {'field': 'offline_amount', 'old_value': 51020.79, 'new_value': 52897.79}, {'field': 'total_amount', 'old_value': 58064.79, 'new_value': 60202.79}, {'field': 'order_count', 'old_value': 555, 'new_value': 558}]
2025-05-28 12:01:57,399 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-28 12:01:57,915 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-28 12:01:57,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39332.8, 'new_value': 46631.8}, {'field': 'total_amount', 'old_value': 56074.4, 'new_value': 63373.4}, {'field': 'order_count', 'old_value': 103, 'new_value': 108}]
2025-05-28 12:01:57,930 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-28 12:01:58,368 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-28 12:01:58,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214544.0, 'new_value': 221364.0}, {'field': 'total_amount', 'old_value': 214544.0, 'new_value': 221364.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 262}]
2025-05-28 12:01:58,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-28 12:01:58,837 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-28 12:01:58,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 732702.84, 'new_value': 744311.02}, {'field': 'total_amount', 'old_value': 732702.84, 'new_value': 744311.02}, {'field': 'order_count', 'old_value': 13905, 'new_value': 14133}]
2025-05-28 12:01:58,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-28 12:01:59,321 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-28 12:01:59,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277566.28, 'new_value': 285675.25}, {'field': 'total_amount', 'old_value': 289617.25, 'new_value': 297726.22}, {'field': 'order_count', 'old_value': 12486, 'new_value': 12866}]
2025-05-28 12:01:59,321 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-28 12:01:59,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-28 12:01:59,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33759.2, 'new_value': 35230.72}, {'field': 'total_amount', 'old_value': 33759.2, 'new_value': 35230.72}, {'field': 'order_count', 'old_value': 1250, 'new_value': 1313}]
2025-05-28 12:01:59,805 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-28 12:02:00,258 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-28 12:02:00,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 757619.32, 'new_value': 779219.03}, {'field': 'total_amount', 'old_value': 757619.32, 'new_value': 779219.03}, {'field': 'order_count', 'old_value': 5669, 'new_value': 5857}]
2025-05-28 12:02:00,258 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-28 12:02:00,743 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-28 12:02:00,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218323.0, 'new_value': 223626.0}, {'field': 'total_amount', 'old_value': 218323.0, 'new_value': 223626.0}, {'field': 'order_count', 'old_value': 683, 'new_value': 705}]
2025-05-28 12:02:00,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-28 12:02:01,212 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-28 12:02:01,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1855.2, 'new_value': 1898.63}, {'field': 'offline_amount', 'old_value': 23130.02, 'new_value': 23333.82}, {'field': 'total_amount', 'old_value': 24985.22, 'new_value': 25232.45}, {'field': 'order_count', 'old_value': 897, 'new_value': 905}]
2025-05-28 12:02:01,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-28 12:02:01,774 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-28 12:02:01,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 862154.0, 'new_value': 883010.0}, {'field': 'total_amount', 'old_value': 862154.0, 'new_value': 883010.0}, {'field': 'order_count', 'old_value': 3888, 'new_value': 3988}]
2025-05-28 12:02:01,774 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-28 12:02:02,243 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-28 12:02:02,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37103.0, 'new_value': 38102.0}, {'field': 'total_amount', 'old_value': 37103.0, 'new_value': 38102.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-28 12:02:02,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-28 12:02:02,727 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-28 12:02:02,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7381.83, 'new_value': 7591.13}, {'field': 'offline_amount', 'old_value': 386975.04, 'new_value': 396389.04}, {'field': 'total_amount', 'old_value': 394356.87, 'new_value': 403980.17}, {'field': 'order_count', 'old_value': 18948, 'new_value': 19413}]
2025-05-28 12:02:02,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-28 12:02:03,212 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-28 12:02:03,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52906.23, 'new_value': 55160.43}, {'field': 'offline_amount', 'old_value': 361612.59, 'new_value': 370659.66}, {'field': 'total_amount', 'old_value': 414518.82, 'new_value': 425820.09}, {'field': 'order_count', 'old_value': 2641, 'new_value': 2706}]
2025-05-28 12:02:03,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-28 12:02:03,618 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-28 12:02:03,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225321.7, 'new_value': 231127.74}, {'field': 'total_amount', 'old_value': 225321.7, 'new_value': 231127.74}, {'field': 'order_count', 'old_value': 1251, 'new_value': 1283}]
2025-05-28 12:02:03,618 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-28 12:02:04,087 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-28 12:02:04,087 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75420.0, 'new_value': 76409.0}, {'field': 'total_amount', 'old_value': 75420.0, 'new_value': 76409.0}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2248}]
2025-05-28 12:02:04,087 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-28 12:02:04,571 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-28 12:02:04,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67758.12, 'new_value': 70124.24}, {'field': 'offline_amount', 'old_value': 87196.17, 'new_value': 88331.33}, {'field': 'total_amount', 'old_value': 154954.29, 'new_value': 158455.57}, {'field': 'order_count', 'old_value': 7160, 'new_value': 7323}]
2025-05-28 12:02:04,571 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-28 12:02:05,133 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-28 12:02:05,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 389670.7, 'new_value': 398819.08}, {'field': 'total_amount', 'old_value': 411833.82, 'new_value': 420982.2}, {'field': 'order_count', 'old_value': 17506, 'new_value': 17964}]
2025-05-28 12:02:05,133 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-28 12:02:05,602 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-28 12:02:05,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30047.56, 'new_value': 31846.8}, {'field': 'offline_amount', 'old_value': 246353.44, 'new_value': 250531.74}, {'field': 'total_amount', 'old_value': 276401.0, 'new_value': 282378.54}, {'field': 'order_count', 'old_value': 8814, 'new_value': 9021}]
2025-05-28 12:02:05,602 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-28 12:02:06,196 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-28 12:02:06,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 396259.87, 'new_value': 400759.87}, {'field': 'total_amount', 'old_value': 396259.87, 'new_value': 400759.87}, {'field': 'order_count', 'old_value': 2842, 'new_value': 2843}]
2025-05-28 12:02:06,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-28 12:02:06,696 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-28 12:02:06,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94592.0, 'new_value': 95985.0}, {'field': 'total_amount', 'old_value': 109796.0, 'new_value': 111189.0}, {'field': 'order_count', 'old_value': 2541, 'new_value': 2578}]
2025-05-28 12:02:06,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-28 12:02:07,196 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-28 12:02:07,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106652.0, 'new_value': 110852.0}, {'field': 'total_amount', 'old_value': 106652.0, 'new_value': 110852.0}, {'field': 'order_count', 'old_value': 716, 'new_value': 717}]
2025-05-28 12:02:07,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-28 12:02:07,633 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-28 12:02:07,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22515.0, 'new_value': 24515.0}, {'field': 'total_amount', 'old_value': 22515.0, 'new_value': 24515.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-28 12:02:07,633 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-28 12:02:08,102 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-28 12:02:08,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1129900.0, 'new_value': 1465900.0}, {'field': 'total_amount', 'old_value': 1129900.0, 'new_value': 1465900.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 76}]
2025-05-28 12:02:08,102 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-28 12:02:08,727 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-28 12:02:08,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97687.36, 'new_value': 102019.74}, {'field': 'offline_amount', 'old_value': 111063.95, 'new_value': 114479.73}, {'field': 'total_amount', 'old_value': 208751.31, 'new_value': 216499.47}, {'field': 'order_count', 'old_value': 8539, 'new_value': 8866}]
2025-05-28 12:02:08,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-28 12:02:09,196 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-28 12:02:09,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247003.38, 'new_value': 251070.82}, {'field': 'total_amount', 'old_value': 266176.81, 'new_value': 270244.25}, {'field': 'order_count', 'old_value': 5516, 'new_value': 5599}]
2025-05-28 12:02:09,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-28 12:02:09,665 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-28 12:02:09,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56478.0, 'new_value': 56577.0}, {'field': 'total_amount', 'old_value': 56478.0, 'new_value': 56577.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 128}]
2025-05-28 12:02:09,665 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-28 12:02:10,133 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-28 12:02:10,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107195.29, 'new_value': 113393.29}, {'field': 'total_amount', 'old_value': 107195.29, 'new_value': 113393.29}, {'field': 'order_count', 'old_value': 2648, 'new_value': 2826}]
2025-05-28 12:02:10,133 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-28 12:02:10,586 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-28 12:02:10,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28663.0, 'new_value': 29473.0}, {'field': 'total_amount', 'old_value': 28663.0, 'new_value': 29473.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 172}]
2025-05-28 12:02:10,586 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-28 12:02:11,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-28 12:02:11,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 332653.74, 'new_value': 336058.94}, {'field': 'offline_amount', 'old_value': 1321468.68, 'new_value': 1355591.84}, {'field': 'total_amount', 'old_value': 1654122.42, 'new_value': 1691650.78}, {'field': 'order_count', 'old_value': 8233, 'new_value': 8435}]
2025-05-28 12:02:11,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-28 12:02:11,618 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-28 12:02:11,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81237.86, 'new_value': 83123.15}, {'field': 'total_amount', 'old_value': 81237.86, 'new_value': 83123.15}, {'field': 'order_count', 'old_value': 4706, 'new_value': 4834}]
2025-05-28 12:02:11,618 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-28 12:02:12,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-28 12:02:12,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60170.55, 'new_value': 61783.44}, {'field': 'offline_amount', 'old_value': 46963.4, 'new_value': 48448.02}, {'field': 'total_amount', 'old_value': 107133.95, 'new_value': 110231.46}, {'field': 'order_count', 'old_value': 2146, 'new_value': 2211}]
2025-05-28 12:02:12,040 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-28 12:02:12,524 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-28 12:02:12,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15781.45, 'new_value': 16372.45}, {'field': 'offline_amount', 'old_value': 296027.0, 'new_value': 303742.0}, {'field': 'total_amount', 'old_value': 311808.45, 'new_value': 320114.45}, {'field': 'order_count', 'old_value': 1647, 'new_value': 1705}]
2025-05-28 12:02:12,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-28 12:02:13,008 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-28 12:02:13,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 12000.0}, {'field': 'total_amount', 'old_value': 65400.0, 'new_value': 74400.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 27}]
2025-05-28 12:02:13,008 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-28 12:02:13,493 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-28 12:02:13,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193539.9, 'new_value': 198368.7}, {'field': 'total_amount', 'old_value': 193539.9, 'new_value': 198368.7}, {'field': 'order_count', 'old_value': 2482, 'new_value': 2558}]
2025-05-28 12:02:13,493 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-28 12:02:13,915 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-28 12:02:13,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34383.7, 'new_value': 35602.7}, {'field': 'total_amount', 'old_value': 34383.7, 'new_value': 35602.7}, {'field': 'order_count', 'old_value': 201, 'new_value': 208}]
2025-05-28 12:02:13,915 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-28 12:02:14,352 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-28 12:02:14,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44703.55, 'new_value': 45593.55}, {'field': 'offline_amount', 'old_value': 1153629.73, 'new_value': 1182229.52}, {'field': 'total_amount', 'old_value': 1198333.28, 'new_value': 1227823.07}, {'field': 'order_count', 'old_value': 5876, 'new_value': 6047}]
2025-05-28 12:02:14,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-28 12:02:14,790 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-28 12:02:14,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328875.0, 'new_value': 341035.0}, {'field': 'total_amount', 'old_value': 355965.0, 'new_value': 368125.0}, {'field': 'order_count', 'old_value': 7562, 'new_value': 7872}]
2025-05-28 12:02:14,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-28 12:02:15,227 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-28 12:02:15,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80680.23, 'new_value': 82212.37}, {'field': 'offline_amount', 'old_value': 207749.88, 'new_value': 210718.5}, {'field': 'total_amount', 'old_value': 288430.11, 'new_value': 292930.87}, {'field': 'order_count', 'old_value': 5502, 'new_value': 5633}]
2025-05-28 12:02:15,227 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-28 12:02:15,649 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-28 12:02:15,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54453.2, 'new_value': 55178.2}, {'field': 'total_amount', 'old_value': 57894.0, 'new_value': 58619.0}, {'field': 'order_count', 'old_value': 218, 'new_value': 222}]
2025-05-28 12:02:15,649 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-28 12:02:16,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-28 12:02:16,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 729567.37, 'new_value': 745289.95}, {'field': 'total_amount', 'old_value': 729567.37, 'new_value': 745289.95}, {'field': 'order_count', 'old_value': 8475, 'new_value': 8689}]
2025-05-28 12:02:16,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-28 12:02:16,555 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-28 12:02:16,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187833.0, 'new_value': 189722.0}, {'field': 'total_amount', 'old_value': 187833.0, 'new_value': 189722.0}, {'field': 'order_count', 'old_value': 3150, 'new_value': 3191}]
2025-05-28 12:02:16,555 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-28 12:02:16,977 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-28 12:02:16,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189874.45, 'new_value': 193250.74}, {'field': 'total_amount', 'old_value': 189874.45, 'new_value': 193250.74}, {'field': 'order_count', 'old_value': 8091, 'new_value': 8249}]
2025-05-28 12:02:16,977 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-28 12:02:17,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-28 12:02:17,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600009.0, 'new_value': 617571.0}, {'field': 'total_amount', 'old_value': 600009.0, 'new_value': 617571.0}, {'field': 'order_count', 'old_value': 541, 'new_value': 557}]
2025-05-28 12:02:17,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-28 12:02:17,915 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-28 12:02:17,915 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 277307.06, 'new_value': 284866.86}, {'field': 'offline_amount', 'old_value': 152166.02, 'new_value': 155614.82}, {'field': 'total_amount', 'old_value': 429473.08, 'new_value': 440481.68}, {'field': 'order_count', 'old_value': 3275, 'new_value': 3307}]
2025-05-28 12:02:17,915 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-28 12:02:18,336 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-28 12:02:18,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244706.16, 'new_value': 250732.73}, {'field': 'total_amount', 'old_value': 244706.16, 'new_value': 250732.73}, {'field': 'order_count', 'old_value': 1892, 'new_value': 1946}]
2025-05-28 12:02:18,336 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-28 12:02:18,790 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-28 12:02:18,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95790.73, 'new_value': 98110.73}, {'field': 'total_amount', 'old_value': 165292.33, 'new_value': 167612.33}, {'field': 'order_count', 'old_value': 4612, 'new_value': 4680}]
2025-05-28 12:02:18,790 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-28 12:02:19,149 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-28 12:02:19,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211157.92, 'new_value': 219651.92}, {'field': 'total_amount', 'old_value': 235958.92, 'new_value': 244452.92}, {'field': 'order_count', 'old_value': 10988, 'new_value': 11279}]
2025-05-28 12:02:19,149 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-28 12:02:19,586 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-28 12:02:19,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103468.0, 'new_value': 111666.0}, {'field': 'offline_amount', 'old_value': 1138829.0, 'new_value': 1249631.0}, {'field': 'total_amount', 'old_value': 1242297.0, 'new_value': 1361297.0}, {'field': 'order_count', 'old_value': 31549, 'new_value': 34319}]
2025-05-28 12:02:19,586 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-28 12:02:20,008 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-28 12:02:20,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 397484.12, 'new_value': 407781.92}, {'field': 'total_amount', 'old_value': 410994.6, 'new_value': 421292.4}, {'field': 'order_count', 'old_value': 1319, 'new_value': 1351}]
2025-05-28 12:02:20,008 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-28 12:02:20,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-28 12:02:20,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53709.0, 'new_value': 54397.0}, {'field': 'offline_amount', 'old_value': 256244.0, 'new_value': 268427.0}, {'field': 'total_amount', 'old_value': 309953.0, 'new_value': 322824.0}, {'field': 'order_count', 'old_value': 281, 'new_value': 289}]
2025-05-28 12:02:20,461 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-28 12:02:20,883 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-28 12:02:20,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38211.03, 'new_value': 39310.07}, {'field': 'offline_amount', 'old_value': 49116.44, 'new_value': 50116.44}, {'field': 'total_amount', 'old_value': 87327.47, 'new_value': 89426.51}, {'field': 'order_count', 'old_value': 4280, 'new_value': 4375}]
2025-05-28 12:02:20,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-28 12:02:21,415 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-28 12:02:21,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23917.43, 'new_value': 24776.43}, {'field': 'offline_amount', 'old_value': 16482.94, 'new_value': 16844.94}, {'field': 'total_amount', 'old_value': 40400.37, 'new_value': 41621.37}, {'field': 'order_count', 'old_value': 1722, 'new_value': 1733}]
2025-05-28 12:02:21,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-28 12:02:21,868 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-28 12:02:21,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304644.0, 'new_value': 315966.0}, {'field': 'total_amount', 'old_value': 304644.0, 'new_value': 315966.0}, {'field': 'order_count', 'old_value': 467, 'new_value': 482}]
2025-05-28 12:02:21,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-28 12:02:22,383 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-28 12:02:22,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125885.26, 'new_value': 130052.9}, {'field': 'offline_amount', 'old_value': 220772.82, 'new_value': 227561.3}, {'field': 'total_amount', 'old_value': 346658.08, 'new_value': 357614.2}, {'field': 'order_count', 'old_value': 10699, 'new_value': 11053}]
2025-05-28 12:02:22,399 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-28 12:02:22,821 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-28 12:02:22,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134319.0, 'new_value': 148559.0}, {'field': 'total_amount', 'old_value': 134319.0, 'new_value': 148559.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-05-28 12:02:22,821 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-28 12:02:23,274 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-28 12:02:23,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176405.57, 'new_value': 182436.49}, {'field': 'offline_amount', 'old_value': 54831.99, 'new_value': 55614.77}, {'field': 'total_amount', 'old_value': 231237.56, 'new_value': 238051.26}, {'field': 'order_count', 'old_value': 13177, 'new_value': 13606}]
2025-05-28 12:02:23,274 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-28 12:02:23,852 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-28 12:02:23,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128446.8, 'new_value': 134495.4}, {'field': 'offline_amount', 'old_value': 33036.9, 'new_value': 33730.9}, {'field': 'total_amount', 'old_value': 161483.7, 'new_value': 168226.3}, {'field': 'order_count', 'old_value': 13249, 'new_value': 13743}]
2025-05-28 12:02:23,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-28 12:02:24,383 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-28 12:02:24,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176638.86, 'new_value': 182102.83}, {'field': 'total_amount', 'old_value': 176638.86, 'new_value': 182102.83}, {'field': 'order_count', 'old_value': 8928, 'new_value': 9249}]
2025-05-28 12:02:24,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-28 12:02:24,883 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-28 12:02:24,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167948.2, 'new_value': 171200.1}, {'field': 'total_amount', 'old_value': 167948.2, 'new_value': 171200.1}, {'field': 'order_count', 'old_value': 757, 'new_value': 774}]
2025-05-28 12:02:24,883 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-28 12:02:25,336 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-28 12:02:25,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139112.4, 'new_value': 140275.7}, {'field': 'total_amount', 'old_value': 139112.4, 'new_value': 140275.7}, {'field': 'order_count', 'old_value': 3843, 'new_value': 3875}]
2025-05-28 12:02:25,336 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-28 12:02:25,774 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-28 12:02:25,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10494.0, 'new_value': 10776.0}, {'field': 'total_amount', 'old_value': 21968.0, 'new_value': 22250.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 126}]
2025-05-28 12:02:25,774 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-28 12:02:26,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-28 12:02:26,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155251.47, 'new_value': 159756.27}, {'field': 'offline_amount', 'old_value': 295482.91, 'new_value': 305331.15}, {'field': 'total_amount', 'old_value': 450734.38, 'new_value': 465087.42}, {'field': 'order_count', 'old_value': 3761, 'new_value': 3908}]
2025-05-28 12:02:26,227 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-28 12:02:26,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-28 12:02:26,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1949114.0, 'new_value': 1979322.0}, {'field': 'total_amount', 'old_value': 1949114.0, 'new_value': 1979322.0}, {'field': 'order_count', 'old_value': 7782, 'new_value': 7931}]
2025-05-28 12:02:26,711 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-28 12:02:27,211 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-28 12:02:27,211 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108219.0, 'new_value': 112242.5}, {'field': 'total_amount', 'old_value': 108219.0, 'new_value': 112242.5}, {'field': 'order_count', 'old_value': 520, 'new_value': 537}]
2025-05-28 12:02:27,211 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-28 12:02:27,649 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-28 12:02:27,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43600.47, 'new_value': 45020.6}, {'field': 'offline_amount', 'old_value': 25800.41, 'new_value': 26239.32}, {'field': 'total_amount', 'old_value': 69400.88, 'new_value': 71259.92}, {'field': 'order_count', 'old_value': 3062, 'new_value': 3152}]
2025-05-28 12:02:27,649 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-28 12:02:28,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-28 12:02:28,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15310.72, 'new_value': 15637.6}, {'field': 'offline_amount', 'old_value': 35901.6, 'new_value': 36697.1}, {'field': 'total_amount', 'old_value': 51212.32, 'new_value': 52334.7}, {'field': 'order_count', 'old_value': 2038, 'new_value': 2074}]
2025-05-28 12:02:28,086 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-28 12:02:28,524 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-28 12:02:28,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 381734.9, 'new_value': 383333.9}, {'field': 'total_amount', 'old_value': 381734.9, 'new_value': 383333.9}]
2025-05-28 12:02:28,524 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-28 12:02:28,946 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-28 12:02:28,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2413.31, 'new_value': 5573.77}, {'field': 'total_amount', 'old_value': 89909.09, 'new_value': 93069.55}, {'field': 'order_count', 'old_value': 427, 'new_value': 444}]
2025-05-28 12:02:28,946 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-28 12:02:29,461 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-28 12:02:29,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 803885.24, 'new_value': 828024.74}, {'field': 'total_amount', 'old_value': 803885.24, 'new_value': 828024.74}, {'field': 'order_count', 'old_value': 6458, 'new_value': 6706}]
2025-05-28 12:02:29,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-28 12:02:29,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-28 12:02:29,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 529691.0, 'new_value': 537874.0}, {'field': 'total_amount', 'old_value': 529691.0, 'new_value': 537874.0}, {'field': 'order_count', 'old_value': 3803, 'new_value': 3900}]
2025-05-28 12:02:29,930 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-28 12:02:30,368 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-28 12:02:30,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56720.8, 'new_value': 58388.87}, {'field': 'offline_amount', 'old_value': 57024.74, 'new_value': 59629.58}, {'field': 'total_amount', 'old_value': 113745.54, 'new_value': 118018.45}, {'field': 'order_count', 'old_value': 5665, 'new_value': 5879}]
2025-05-28 12:02:30,368 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-28 12:02:30,836 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-28 12:02:30,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26695.44, 'new_value': 27655.56}, {'field': 'offline_amount', 'old_value': 29641.08, 'new_value': 30566.78}, {'field': 'total_amount', 'old_value': 56336.52, 'new_value': 58222.34}, {'field': 'order_count', 'old_value': 2732, 'new_value': 2823}]
2025-05-28 12:02:30,836 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-28 12:02:31,477 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-28 12:02:31,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3821.0, 'new_value': 3945.0}, {'field': 'offline_amount', 'old_value': 29890.8, 'new_value': 30203.8}, {'field': 'total_amount', 'old_value': 33711.8, 'new_value': 34148.8}, {'field': 'order_count', 'old_value': 1223, 'new_value': 1239}]
2025-05-28 12:02:31,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-28 12:02:31,883 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-28 12:02:31,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108153.18, 'new_value': 113604.82}, {'field': 'offline_amount', 'old_value': 121567.87, 'new_value': 126074.15}, {'field': 'total_amount', 'old_value': 229721.05, 'new_value': 239678.97}, {'field': 'order_count', 'old_value': 5801, 'new_value': 6063}]
2025-05-28 12:02:31,883 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-28 12:02:32,368 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-28 12:02:32,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85169.0, 'new_value': 88168.0}, {'field': 'total_amount', 'old_value': 85169.0, 'new_value': 88168.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-28 12:02:32,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-28 12:02:32,821 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-28 12:02:32,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1011076.0, 'new_value': 1049464.0}, {'field': 'total_amount', 'old_value': 1011076.0, 'new_value': 1049464.0}, {'field': 'order_count', 'old_value': 1160, 'new_value': 1209}]
2025-05-28 12:02:32,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-28 12:02:33,243 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-28 12:02:33,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212678.5, 'new_value': 221638.4}, {'field': 'total_amount', 'old_value': 218628.8, 'new_value': 227588.7}, {'field': 'order_count', 'old_value': 428, 'new_value': 443}]
2025-05-28 12:02:33,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-28 12:02:33,743 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-28 12:02:33,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45113.15, 'new_value': 46479.15}, {'field': 'offline_amount', 'old_value': 131515.0, 'new_value': 137192.0}, {'field': 'total_amount', 'old_value': 176628.15, 'new_value': 183671.15}, {'field': 'order_count', 'old_value': 1917, 'new_value': 1996}]
2025-05-28 12:02:33,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-28 12:02:34,211 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-28 12:02:34,211 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144272.0, 'new_value': 147823.0}, {'field': 'offline_amount', 'old_value': 100443.0, 'new_value': 101860.0}, {'field': 'total_amount', 'old_value': 244715.0, 'new_value': 249683.0}, {'field': 'order_count', 'old_value': 3215, 'new_value': 3330}]
2025-05-28 12:02:34,211 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-28 12:02:34,649 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-28 12:02:34,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7734.3, 'new_value': 7971.3}, {'field': 'offline_amount', 'old_value': 24441.64, 'new_value': 25243.93}, {'field': 'total_amount', 'old_value': 32175.94, 'new_value': 33215.23}, {'field': 'order_count', 'old_value': 316, 'new_value': 324}]
2025-05-28 12:02:34,649 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-28 12:02:35,180 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-28 12:02:35,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121168.84, 'new_value': 127173.91}, {'field': 'total_amount', 'old_value': 128397.91, 'new_value': 134402.98}, {'field': 'order_count', 'old_value': 693, 'new_value': 724}]
2025-05-28 12:02:35,180 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-28 12:02:35,618 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-28 12:02:35,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33359.66, 'new_value': 34984.66}, {'field': 'offline_amount', 'old_value': 32792.82, 'new_value': 34140.82}, {'field': 'total_amount', 'old_value': 66152.48, 'new_value': 69125.48}, {'field': 'order_count', 'old_value': 296, 'new_value': 309}]
2025-05-28 12:02:35,618 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-28 12:02:36,055 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-28 12:02:36,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7273.0, 'new_value': 7606.0}, {'field': 'offline_amount', 'old_value': 56447.0, 'new_value': 57885.0}, {'field': 'total_amount', 'old_value': 63720.0, 'new_value': 65491.0}, {'field': 'order_count', 'old_value': 496, 'new_value': 509}]
2025-05-28 12:02:36,055 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-28 12:02:36,571 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-28 12:02:36,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230369.5, 'new_value': 238124.5}, {'field': 'total_amount', 'old_value': 230369.5, 'new_value': 238124.5}, {'field': 'order_count', 'old_value': 1130, 'new_value': 1165}]
2025-05-28 12:02:36,571 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-28 12:02:37,039 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-28 12:02:37,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5212.0, 'new_value': 5300.0}, {'field': 'offline_amount', 'old_value': 21704.0, 'new_value': 22349.0}, {'field': 'total_amount', 'old_value': 26916.0, 'new_value': 27649.0}, {'field': 'order_count', 'old_value': 209, 'new_value': 214}]
2025-05-28 12:02:37,039 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-28 12:02:37,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-28 12:02:37,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54951.77, 'new_value': 58082.78}, {'field': 'total_amount', 'old_value': 61617.81, 'new_value': 64748.82}, {'field': 'order_count', 'old_value': 574, 'new_value': 602}]
2025-05-28 12:02:37,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-28 12:02:37,914 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-28 12:02:37,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206349.44, 'new_value': 214690.34}, {'field': 'total_amount', 'old_value': 206349.44, 'new_value': 214690.34}, {'field': 'order_count', 'old_value': 786, 'new_value': 821}]
2025-05-28 12:02:37,914 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-28 12:02:38,368 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-28 12:02:38,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1075900.0, 'new_value': 1100871.0}, {'field': 'total_amount', 'old_value': 1075900.0, 'new_value': 1100871.0}, {'field': 'order_count', 'old_value': 4751, 'new_value': 4854}]
2025-05-28 12:02:38,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-28 12:02:38,914 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-28 12:02:38,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13175779.0, 'new_value': 13418416.0}, {'field': 'total_amount', 'old_value': 13175779.0, 'new_value': 13418416.0}, {'field': 'order_count', 'old_value': 41807, 'new_value': 42898}]
2025-05-28 12:02:38,914 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-28 12:02:39,461 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-28 12:02:39,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3828105.82, 'new_value': 3926339.47}, {'field': 'total_amount', 'old_value': 3828105.82, 'new_value': 3926339.47}, {'field': 'order_count', 'old_value': 6604, 'new_value': 6767}]
2025-05-28 12:02:39,461 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-28 12:02:39,977 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-28 12:02:39,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169442.75, 'new_value': 175987.8}, {'field': 'total_amount', 'old_value': 176882.39, 'new_value': 183427.44}, {'field': 'order_count', 'old_value': 12373, 'new_value': 12836}]
2025-05-28 12:02:39,977 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-28 12:02:40,430 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-28 12:02:40,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204929.0, 'new_value': 217968.0}, {'field': 'total_amount', 'old_value': 204929.0, 'new_value': 217968.0}, {'field': 'order_count', 'old_value': 464, 'new_value': 469}]
2025-05-28 12:02:40,430 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-28 12:02:40,961 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-28 12:02:40,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 299868.73, 'new_value': 306720.56}, {'field': 'offline_amount', 'old_value': 213500.09, 'new_value': 218651.56}, {'field': 'total_amount', 'old_value': 513368.82, 'new_value': 525372.12}, {'field': 'order_count', 'old_value': 20785, 'new_value': 21322}]
2025-05-28 12:02:40,961 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-28 12:02:41,477 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-28 12:02:41,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69395.11, 'new_value': 73383.96}, {'field': 'total_amount', 'old_value': 118632.7, 'new_value': 122621.55}, {'field': 'order_count', 'old_value': 2582, 'new_value': 2583}]
2025-05-28 12:02:41,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-28 12:02:41,961 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-28 12:02:41,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175625.6, 'new_value': 184076.6}, {'field': 'total_amount', 'old_value': 335841.38, 'new_value': 344292.38}]
2025-05-28 12:02:41,961 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-28 12:02:42,430 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-28 12:02:42,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326764.7, 'new_value': 329912.5}, {'field': 'total_amount', 'old_value': 326764.7, 'new_value': 329912.5}, {'field': 'order_count', 'old_value': 7131, 'new_value': 7202}]
2025-05-28 12:02:42,430 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-28 12:02:42,899 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-28 12:02:42,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55365.3, 'new_value': 56902.2}, {'field': 'total_amount', 'old_value': 55365.3, 'new_value': 56902.2}, {'field': 'order_count', 'old_value': 302, 'new_value': 312}]
2025-05-28 12:02:42,899 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-28 12:02:43,524 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-28 12:02:43,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 204182.0, 'new_value': 209881.0}, {'field': 'offline_amount', 'old_value': 187532.0, 'new_value': 190756.0}, {'field': 'total_amount', 'old_value': 391714.0, 'new_value': 400637.0}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1117}]
2025-05-28 12:02:43,524 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-28 12:02:44,008 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-28 12:02:44,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149113.0, 'new_value': 155895.0}, {'field': 'total_amount', 'old_value': 153852.0, 'new_value': 160634.0}, {'field': 'order_count', 'old_value': 11400, 'new_value': 11919}]
2025-05-28 12:02:44,008 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-28 12:02:44,477 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-28 12:02:44,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34062.0, 'new_value': 34163.0}, {'field': 'total_amount', 'old_value': 34062.0, 'new_value': 34163.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-28 12:02:44,477 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-28 12:02:44,914 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-28 12:02:44,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24441.0, 'new_value': 25694.0}, {'field': 'total_amount', 'old_value': 29094.6, 'new_value': 30347.6}, {'field': 'order_count', 'old_value': 729, 'new_value': 757}]
2025-05-28 12:02:44,914 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-28 12:02:45,368 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-28 12:02:45,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200796.27, 'new_value': 207466.92}, {'field': 'total_amount', 'old_value': 200796.27, 'new_value': 207466.92}, {'field': 'order_count', 'old_value': 14335, 'new_value': 14857}]
2025-05-28 12:02:45,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-28 12:02:45,821 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-28 12:02:45,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1254000.0, 'new_value': 2086405.75}, {'field': 'total_amount', 'old_value': 2819250.0, 'new_value': 3651655.75}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-28 12:02:45,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-28 12:02:46,258 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-28 12:02:46,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28160.5, 'new_value': 29517.6}, {'field': 'offline_amount', 'old_value': 64974.4, 'new_value': 67812.9}, {'field': 'total_amount', 'old_value': 93134.9, 'new_value': 97330.5}, {'field': 'order_count', 'old_value': 3514, 'new_value': 3658}]
2025-05-28 12:02:46,258 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-28 12:02:46,680 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-28 12:02:46,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39239.41, 'new_value': 40404.28}, {'field': 'total_amount', 'old_value': 39239.41, 'new_value': 40404.28}, {'field': 'order_count', 'old_value': 1842, 'new_value': 1904}]
2025-05-28 12:02:46,680 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-28 12:02:47,133 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-28 12:02:47,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69746.18, 'new_value': 71603.05}, {'field': 'total_amount', 'old_value': 75106.15, 'new_value': 76963.02}, {'field': 'order_count', 'old_value': 1355, 'new_value': 1380}]
2025-05-28 12:02:47,133 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-28 12:02:47,649 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-28 12:02:47,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5315313.67, 'new_value': 5469707.67}, {'field': 'total_amount', 'old_value': 5315313.67, 'new_value': 5469707.67}, {'field': 'order_count', 'old_value': 109492, 'new_value': 113040}]
2025-05-28 12:02:47,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-28 12:02:48,071 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-28 12:02:48,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25465.88, 'new_value': 26595.88}, {'field': 'total_amount', 'old_value': 25465.88, 'new_value': 26595.88}, {'field': 'order_count', 'old_value': 112, 'new_value': 120}]
2025-05-28 12:02:48,071 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-28 12:02:48,524 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-28 12:02:48,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 556993.39, 'new_value': 567937.39}, {'field': 'total_amount', 'old_value': 562539.75, 'new_value': 573483.75}, {'field': 'order_count', 'old_value': 5739, 'new_value': 5864}]
2025-05-28 12:02:48,524 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-28 12:02:49,149 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-28 12:02:49,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212145.44, 'new_value': 219193.66}, {'field': 'total_amount', 'old_value': 212145.44, 'new_value': 219193.66}, {'field': 'order_count', 'old_value': 3884, 'new_value': 4013}]
2025-05-28 12:02:49,149 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-28 12:02:49,524 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-28 12:02:49,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342510.0, 'new_value': 360764.0}, {'field': 'total_amount', 'old_value': 342510.0, 'new_value': 360764.0}, {'field': 'order_count', 'old_value': 7491, 'new_value': 7835}]
2025-05-28 12:02:49,524 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-28 12:02:49,946 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-28 12:02:49,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82889.23, 'new_value': 85706.86}, {'field': 'total_amount', 'old_value': 82889.23, 'new_value': 85706.86}, {'field': 'order_count', 'old_value': 8646, 'new_value': 8975}]
2025-05-28 12:02:49,946 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-28 12:02:50,524 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-28 12:02:50,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 493088.0, 'new_value': 493686.0}, {'field': 'total_amount', 'old_value': 493088.0, 'new_value': 493686.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 88}]
2025-05-28 12:02:50,539 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-28 12:02:50,992 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-28 12:02:50,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133778.03, 'new_value': 137588.43}, {'field': 'total_amount', 'old_value': 138746.23, 'new_value': 142556.63}, {'field': 'order_count', 'old_value': 3510, 'new_value': 3624}]
2025-05-28 12:02:50,992 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-28 12:02:51,461 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-28 12:02:51,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37012.0, 'new_value': 38043.0}, {'field': 'total_amount', 'old_value': 37012.0, 'new_value': 38043.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 119}]
2025-05-28 12:02:51,461 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-28 12:02:51,899 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-28 12:02:51,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106722.04, 'new_value': 110076.04}, {'field': 'offline_amount', 'old_value': 419266.0, 'new_value': 425598.0}, {'field': 'total_amount', 'old_value': 525988.04, 'new_value': 535674.04}, {'field': 'order_count', 'old_value': 3813, 'new_value': 3927}]
2025-05-28 12:02:51,899 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-28 12:02:52,289 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-28 12:02:52,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61290.0, 'new_value': 64995.41}, {'field': 'total_amount', 'old_value': 95642.5, 'new_value': 99347.91}, {'field': 'order_count', 'old_value': 6264, 'new_value': 6514}]
2025-05-28 12:02:52,289 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-28 12:02:52,711 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-28 12:02:52,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106512.36, 'new_value': 113329.28}, {'field': 'total_amount', 'old_value': 166366.75, 'new_value': 173183.67}, {'field': 'order_count', 'old_value': 10969, 'new_value': 11406}]
2025-05-28 12:02:52,711 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-28 12:02:53,149 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-28 12:02:53,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1227778.39, 'new_value': 1264249.99}, {'field': 'total_amount', 'old_value': 1227778.39, 'new_value': 1264249.99}, {'field': 'order_count', 'old_value': 3607, 'new_value': 3717}]
2025-05-28 12:02:53,149 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-28 12:02:53,680 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-28 12:02:53,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192886.8, 'new_value': 203327.8}, {'field': 'total_amount', 'old_value': 192886.8, 'new_value': 203327.8}, {'field': 'order_count', 'old_value': 6754, 'new_value': 7100}]
2025-05-28 12:02:53,680 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-28 12:02:54,133 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-28 12:02:54,133 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-05-28 12:02:54,133 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-28 12:02:54,586 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-28 12:02:54,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 757036.25, 'new_value': 781794.52}, {'field': 'total_amount', 'old_value': 757036.25, 'new_value': 781794.52}, {'field': 'order_count', 'old_value': 4047, 'new_value': 4225}]
2025-05-28 12:02:54,586 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-28 12:02:55,196 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-28 12:02:55,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 857796.41, 'new_value': 886053.01}, {'field': 'total_amount', 'old_value': 857796.41, 'new_value': 886053.01}, {'field': 'order_count', 'old_value': 2418, 'new_value': 2494}]
2025-05-28 12:02:55,196 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-28 12:02:55,633 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-28 12:02:55,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44765.0, 'new_value': 46873.0}, {'field': 'total_amount', 'old_value': 44765.0, 'new_value': 46873.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-28 12:02:55,633 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-28 12:02:56,102 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-28 12:02:56,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24318.0, 'new_value': 25552.0}, {'field': 'offline_amount', 'old_value': 26718.0, 'new_value': 27952.0}, {'field': 'total_amount', 'old_value': 51036.0, 'new_value': 53504.0}, {'field': 'order_count', 'old_value': 24356, 'new_value': 25590}]
2025-05-28 12:02:56,102 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-28 12:02:56,680 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-28 12:02:56,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315263.81, 'new_value': 320390.93}, {'field': 'total_amount', 'old_value': 315263.81, 'new_value': 320390.93}, {'field': 'order_count', 'old_value': 873, 'new_value': 885}]
2025-05-28 12:02:56,680 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-28 12:02:57,086 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-28 12:02:57,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135584.0, 'new_value': 139450.0}, {'field': 'total_amount', 'old_value': 135664.0, 'new_value': 139530.0}, {'field': 'order_count', 'old_value': 13550, 'new_value': 13992}]
2025-05-28 12:02:57,086 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-28 12:02:57,524 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-28 12:02:57,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65878.0, 'new_value': 68339.0}, {'field': 'total_amount', 'old_value': 81383.0, 'new_value': 83844.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 130}]
2025-05-28 12:02:57,524 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-28 12:02:58,024 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-28 12:02:58,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128727.0, 'new_value': 132199.0}, {'field': 'total_amount', 'old_value': 128727.0, 'new_value': 132199.0}, {'field': 'order_count', 'old_value': 631, 'new_value': 632}]
2025-05-28 12:02:58,024 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-28 12:02:58,524 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-28 12:02:58,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148072.0, 'new_value': 154436.0}, {'field': 'total_amount', 'old_value': 148072.0, 'new_value': 154436.0}, {'field': 'order_count', 'old_value': 601, 'new_value': 620}]
2025-05-28 12:02:58,524 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-28 12:02:58,992 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-28 12:02:58,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287885.0, 'new_value': 303985.0}, {'field': 'total_amount', 'old_value': 287885.0, 'new_value': 303985.0}, {'field': 'order_count', 'old_value': 677, 'new_value': 715}]
2025-05-28 12:02:58,992 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-28 12:02:59,461 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-28 12:02:59,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54216.0, 'new_value': 57035.0}, {'field': 'total_amount', 'old_value': 54216.0, 'new_value': 57035.0}, {'field': 'order_count', 'old_value': 1045, 'new_value': 1094}]
2025-05-28 12:02:59,461 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-28 12:02:59,836 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-28 12:02:59,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203849.0, 'new_value': 213714.0}, {'field': 'total_amount', 'old_value': 203849.0, 'new_value': 213714.0}, {'field': 'order_count', 'old_value': 21539, 'new_value': 22640}]
2025-05-28 12:02:59,836 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-28 12:03:00,258 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-28 12:03:00,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121049.0, 'new_value': 123102.0}, {'field': 'total_amount', 'old_value': 121049.0, 'new_value': 123102.0}, {'field': 'order_count', 'old_value': 1233, 'new_value': 1266}]
2025-05-28 12:03:00,258 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-28 12:03:00,758 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-28 12:03:00,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160151.82, 'new_value': 162351.82}, {'field': 'total_amount', 'old_value': 160151.82, 'new_value': 162351.82}, {'field': 'order_count', 'old_value': 1371, 'new_value': 1372}]
2025-05-28 12:03:00,758 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-28 12:03:01,196 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-28 12:03:01,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36769.0, 'new_value': 40049.0}, {'field': 'total_amount', 'old_value': 76894.4, 'new_value': 80174.4}, {'field': 'order_count', 'old_value': 2801, 'new_value': 2804}]
2025-05-28 12:03:01,196 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-28 12:03:01,680 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-28 12:03:01,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50417.97, 'new_value': 52205.43}, {'field': 'total_amount', 'old_value': 50417.97, 'new_value': 52205.43}, {'field': 'order_count', 'old_value': 865, 'new_value': 898}]
2025-05-28 12:03:01,680 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-28 12:03:02,180 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-28 12:03:02,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129450.87, 'new_value': 132179.17}, {'field': 'offline_amount', 'old_value': 718169.02, 'new_value': 729665.54}, {'field': 'total_amount', 'old_value': 847619.89, 'new_value': 861844.71}, {'field': 'order_count', 'old_value': 1945, 'new_value': 2001}]
2025-05-28 12:03:02,180 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-28 12:03:02,617 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-28 12:03:02,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92054.37, 'new_value': 95349.75}, {'field': 'offline_amount', 'old_value': 920053.53, 'new_value': 963226.44}, {'field': 'total_amount', 'old_value': 1010233.57, 'new_value': 1056701.86}, {'field': 'order_count', 'old_value': 4773, 'new_value': 4974}]
2025-05-28 12:03:02,617 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-28 12:03:03,164 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-28 12:03:03,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121565.0, 'new_value': 128390.0}, {'field': 'total_amount', 'old_value': 121565.0, 'new_value': 128390.0}, {'field': 'order_count', 'old_value': 378, 'new_value': 390}]
2025-05-28 12:03:03,164 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-28 12:03:03,664 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-28 12:03:03,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12880.0, 'new_value': 22680.0}, {'field': 'total_amount', 'old_value': 14818.95, 'new_value': 24618.95}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-28 12:03:03,664 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-28 12:03:04,055 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-28 12:03:04,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18084105.92, 'new_value': 18749253.78}, {'field': 'total_amount', 'old_value': 18084105.92, 'new_value': 18749253.78}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-28 12:03:04,055 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-28 12:03:04,461 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-28 12:03:04,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8598.0, 'new_value': 11534.0}, {'field': 'offline_amount', 'old_value': 40861.36, 'new_value': 41961.36}, {'field': 'total_amount', 'old_value': 49459.36, 'new_value': 53495.36}, {'field': 'order_count', 'old_value': 3060, 'new_value': 3285}]
2025-05-28 12:03:04,461 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-28 12:03:04,867 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-28 12:03:04,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188383.58, 'new_value': 194426.77}, {'field': 'total_amount', 'old_value': 188383.58, 'new_value': 194426.77}, {'field': 'order_count', 'old_value': 19948, 'new_value': 20658}]
2025-05-28 12:03:04,867 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-28 12:03:05,336 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-28 12:03:05,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19035.0, 'new_value': 19104.0}, {'field': 'total_amount', 'old_value': 19035.0, 'new_value': 19104.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 125}]
2025-05-28 12:03:05,336 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-28 12:03:05,852 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-28 12:03:05,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5823.38, 'new_value': 10856.44}, {'field': 'total_amount', 'old_value': 63841.47, 'new_value': 68874.53}, {'field': 'order_count', 'old_value': 4044, 'new_value': 4349}]
2025-05-28 12:03:05,852 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-28 12:03:06,305 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-28 12:03:06,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47799.22, 'new_value': 50365.48}, {'field': 'offline_amount', 'old_value': 59883.77, 'new_value': 61603.77}, {'field': 'total_amount', 'old_value': 107682.99, 'new_value': 111969.25}, {'field': 'order_count', 'old_value': 483, 'new_value': 511}]
2025-05-28 12:03:06,305 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-28 12:03:06,805 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-28 12:03:06,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113811.38, 'new_value': 128316.04}, {'field': 'total_amount', 'old_value': 113811.38, 'new_value': 128316.04}, {'field': 'order_count', 'old_value': 5224, 'new_value': 5889}]
2025-05-28 12:03:06,805 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-28 12:03:07,289 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-28 12:03:07,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3375.43, 'new_value': 3816.75}, {'field': 'total_amount', 'old_value': 3375.43, 'new_value': 3816.75}, {'field': 'order_count', 'old_value': 150, 'new_value': 190}]
2025-05-28 12:03:07,289 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-28 12:03:07,774 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-28 12:03:07,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18663.61, 'new_value': 20108.3}, {'field': 'total_amount', 'old_value': 18663.61, 'new_value': 20108.3}, {'field': 'order_count', 'old_value': 808, 'new_value': 859}]
2025-05-28 12:03:07,774 - INFO - 日期 2025-05 处理完成 - 更新: 335 条，插入: 0 条，错误: 0 条
2025-05-28 12:03:07,774 - INFO - 数据同步完成！更新: 335 条，插入: 0 条，错误: 0 条
2025-05-28 12:03:07,774 - INFO - =================同步完成====================
2025-05-28 15:00:01,873 - INFO - =================使用默认全量同步=============
2025-05-28 15:00:03,389 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 15:00:03,389 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 15:00:03,405 - INFO - 开始处理日期: 2025-01
2025-05-28 15:00:03,420 - INFO - Request Parameters - Page 1:
2025-05-28 15:00:03,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:03,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:04,795 - INFO - Response - Page 1:
2025-05-28 15:00:04,998 - INFO - 第 1 页获取到 100 条记录
2025-05-28 15:00:04,998 - INFO - Request Parameters - Page 2:
2025-05-28 15:00:04,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:04,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:05,561 - INFO - Response - Page 2:
2025-05-28 15:00:05,764 - INFO - 第 2 页获取到 100 条记录
2025-05-28 15:00:05,764 - INFO - Request Parameters - Page 3:
2025-05-28 15:00:05,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:05,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:06,280 - INFO - Response - Page 3:
2025-05-28 15:00:06,483 - INFO - 第 3 页获取到 100 条记录
2025-05-28 15:00:06,483 - INFO - Request Parameters - Page 4:
2025-05-28 15:00:06,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:06,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:06,998 - INFO - Response - Page 4:
2025-05-28 15:00:07,217 - INFO - 第 4 页获取到 100 条记录
2025-05-28 15:00:07,217 - INFO - Request Parameters - Page 5:
2025-05-28 15:00:07,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:07,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:07,701 - INFO - Response - Page 5:
2025-05-28 15:00:07,905 - INFO - 第 5 页获取到 100 条记录
2025-05-28 15:00:07,905 - INFO - Request Parameters - Page 6:
2025-05-28 15:00:07,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:07,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:08,420 - INFO - Response - Page 6:
2025-05-28 15:00:08,639 - INFO - 第 6 页获取到 100 条记录
2025-05-28 15:00:08,639 - INFO - Request Parameters - Page 7:
2025-05-28 15:00:08,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:08,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:09,155 - INFO - Response - Page 7:
2025-05-28 15:00:09,358 - INFO - 第 7 页获取到 82 条记录
2025-05-28 15:00:09,358 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 15:00:09,358 - INFO - 获取到 682 条表单数据
2025-05-28 15:00:09,358 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 15:00:09,373 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 15:00:09,373 - INFO - 开始处理日期: 2025-02
2025-05-28 15:00:09,373 - INFO - Request Parameters - Page 1:
2025-05-28 15:00:09,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:09,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:09,920 - INFO - Response - Page 1:
2025-05-28 15:00:10,123 - INFO - 第 1 页获取到 100 条记录
2025-05-28 15:00:10,123 - INFO - Request Parameters - Page 2:
2025-05-28 15:00:10,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:10,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:11,248 - INFO - Response - Page 2:
2025-05-28 15:00:11,451 - INFO - 第 2 页获取到 100 条记录
2025-05-28 15:00:11,451 - INFO - Request Parameters - Page 3:
2025-05-28 15:00:11,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:11,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:12,061 - INFO - Response - Page 3:
2025-05-28 15:00:12,264 - INFO - 第 3 页获取到 100 条记录
2025-05-28 15:00:12,264 - INFO - Request Parameters - Page 4:
2025-05-28 15:00:12,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:12,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:12,873 - INFO - Response - Page 4:
2025-05-28 15:00:13,076 - INFO - 第 4 页获取到 100 条记录
2025-05-28 15:00:13,076 - INFO - Request Parameters - Page 5:
2025-05-28 15:00:13,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:13,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:13,608 - INFO - Response - Page 5:
2025-05-28 15:00:13,811 - INFO - 第 5 页获取到 100 条记录
2025-05-28 15:00:13,811 - INFO - Request Parameters - Page 6:
2025-05-28 15:00:13,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:13,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:14,342 - INFO - Response - Page 6:
2025-05-28 15:00:14,545 - INFO - 第 6 页获取到 100 条记录
2025-05-28 15:00:14,545 - INFO - Request Parameters - Page 7:
2025-05-28 15:00:14,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:14,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:15,014 - INFO - Response - Page 7:
2025-05-28 15:00:15,217 - INFO - 第 7 页获取到 70 条记录
2025-05-28 15:00:15,217 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 15:00:15,217 - INFO - 获取到 670 条表单数据
2025-05-28 15:00:15,217 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 15:00:15,233 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 15:00:15,233 - INFO - 开始处理日期: 2025-03
2025-05-28 15:00:15,233 - INFO - Request Parameters - Page 1:
2025-05-28 15:00:15,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:15,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:15,764 - INFO - Response - Page 1:
2025-05-28 15:00:15,967 - INFO - 第 1 页获取到 100 条记录
2025-05-28 15:00:15,967 - INFO - Request Parameters - Page 2:
2025-05-28 15:00:15,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:15,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:16,545 - INFO - Response - Page 2:
2025-05-28 15:00:16,748 - INFO - 第 2 页获取到 100 条记录
2025-05-28 15:00:16,748 - INFO - Request Parameters - Page 3:
2025-05-28 15:00:16,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:16,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:17,295 - INFO - Response - Page 3:
2025-05-28 15:00:17,498 - INFO - 第 3 页获取到 100 条记录
2025-05-28 15:00:17,498 - INFO - Request Parameters - Page 4:
2025-05-28 15:00:17,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:17,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:17,967 - INFO - Response - Page 4:
2025-05-28 15:00:18,170 - INFO - 第 4 页获取到 100 条记录
2025-05-28 15:00:18,170 - INFO - Request Parameters - Page 5:
2025-05-28 15:00:18,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:18,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:18,686 - INFO - Response - Page 5:
2025-05-28 15:00:18,889 - INFO - 第 5 页获取到 100 条记录
2025-05-28 15:00:18,889 - INFO - Request Parameters - Page 6:
2025-05-28 15:00:18,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:18,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:19,530 - INFO - Response - Page 6:
2025-05-28 15:00:19,733 - INFO - 第 6 页获取到 100 条记录
2025-05-28 15:00:19,733 - INFO - Request Parameters - Page 7:
2025-05-28 15:00:19,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:19,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:20,155 - INFO - Response - Page 7:
2025-05-28 15:00:20,358 - INFO - 第 7 页获取到 61 条记录
2025-05-28 15:00:20,358 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 15:00:20,358 - INFO - 获取到 661 条表单数据
2025-05-28 15:00:20,358 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 15:00:20,373 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 15:00:20,373 - INFO - 开始处理日期: 2025-04
2025-05-28 15:00:20,373 - INFO - Request Parameters - Page 1:
2025-05-28 15:00:20,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:20,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:20,951 - INFO - Response - Page 1:
2025-05-28 15:00:21,154 - INFO - 第 1 页获取到 100 条记录
2025-05-28 15:00:21,154 - INFO - Request Parameters - Page 2:
2025-05-28 15:00:21,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:21,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:21,686 - INFO - Response - Page 2:
2025-05-28 15:00:21,889 - INFO - 第 2 页获取到 100 条记录
2025-05-28 15:00:21,889 - INFO - Request Parameters - Page 3:
2025-05-28 15:00:21,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:21,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:22,779 - INFO - Response - Page 3:
2025-05-28 15:00:22,983 - INFO - 第 3 页获取到 100 条记录
2025-05-28 15:00:22,983 - INFO - Request Parameters - Page 4:
2025-05-28 15:00:22,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:22,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:23,389 - INFO - Response - Page 4:
2025-05-28 15:00:23,592 - INFO - 第 4 页获取到 100 条记录
2025-05-28 15:00:23,592 - INFO - Request Parameters - Page 5:
2025-05-28 15:00:23,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:23,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:24,123 - INFO - Response - Page 5:
2025-05-28 15:00:24,326 - INFO - 第 5 页获取到 100 条记录
2025-05-28 15:00:24,326 - INFO - Request Parameters - Page 6:
2025-05-28 15:00:24,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:24,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:24,826 - INFO - Response - Page 6:
2025-05-28 15:00:25,029 - INFO - 第 6 页获取到 100 条记录
2025-05-28 15:00:25,029 - INFO - Request Parameters - Page 7:
2025-05-28 15:00:25,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:25,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:25,404 - INFO - Response - Page 7:
2025-05-28 15:00:25,608 - INFO - 第 7 页获取到 56 条记录
2025-05-28 15:00:25,608 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 15:00:25,608 - INFO - 获取到 656 条表单数据
2025-05-28 15:00:25,608 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 15:00:25,623 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 15:00:25,623 - INFO - 开始处理日期: 2025-05
2025-05-28 15:00:25,623 - INFO - Request Parameters - Page 1:
2025-05-28 15:00:25,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:25,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:26,108 - INFO - Response - Page 1:
2025-05-28 15:00:26,311 - INFO - 第 1 页获取到 100 条记录
2025-05-28 15:00:26,311 - INFO - Request Parameters - Page 2:
2025-05-28 15:00:26,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:26,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:26,826 - INFO - Response - Page 2:
2025-05-28 15:00:27,029 - INFO - 第 2 页获取到 100 条记录
2025-05-28 15:00:27,029 - INFO - Request Parameters - Page 3:
2025-05-28 15:00:27,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:27,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:27,529 - INFO - Response - Page 3:
2025-05-28 15:00:27,733 - INFO - 第 3 页获取到 100 条记录
2025-05-28 15:00:27,733 - INFO - Request Parameters - Page 4:
2025-05-28 15:00:27,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:27,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:28,295 - INFO - Response - Page 4:
2025-05-28 15:00:28,498 - INFO - 第 4 页获取到 100 条记录
2025-05-28 15:00:28,498 - INFO - Request Parameters - Page 5:
2025-05-28 15:00:28,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:28,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:29,061 - INFO - Response - Page 5:
2025-05-28 15:00:29,264 - INFO - 第 5 页获取到 100 条记录
2025-05-28 15:00:29,264 - INFO - Request Parameters - Page 6:
2025-05-28 15:00:29,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:29,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:29,795 - INFO - Response - Page 6:
2025-05-28 15:00:29,998 - INFO - 第 6 页获取到 100 条记录
2025-05-28 15:00:29,998 - INFO - Request Parameters - Page 7:
2025-05-28 15:00:29,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 15:00:29,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 15:00:30,404 - INFO - Response - Page 7:
2025-05-28 15:00:30,608 - INFO - 第 7 页获取到 34 条记录
2025-05-28 15:00:30,608 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 15:00:30,608 - INFO - 获取到 634 条表单数据
2025-05-28 15:00:30,608 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 15:00:30,608 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-28 15:00:31,061 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-28 15:00:31,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24521.0, 'new_value': 27127.0}, {'field': 'total_amount', 'old_value': 25513.0, 'new_value': 28119.0}, {'field': 'order_count', 'old_value': 2762, 'new_value': 2938}]
2025-05-28 15:00:31,076 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-28 15:00:31,592 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-28 15:00:31,592 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53230.0, 'new_value': 55747.0}, {'field': 'offline_amount', 'old_value': 186849.98, 'new_value': 193392.98}, {'field': 'total_amount', 'old_value': 240079.98, 'new_value': 249139.98}, {'field': 'order_count', 'old_value': 1643, 'new_value': 1698}]
2025-05-28 15:00:31,592 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-28 15:00:32,061 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-28 15:00:32,061 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25813.36, 'new_value': 27915.11}, {'field': 'offline_amount', 'old_value': 106697.06, 'new_value': 115398.37}, {'field': 'total_amount', 'old_value': 132510.42, 'new_value': 143313.48}, {'field': 'order_count', 'old_value': 1809, 'new_value': 1971}]
2025-05-28 15:00:32,061 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-28 15:00:32,561 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-28 15:00:32,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96947.95, 'new_value': 99277.54}, {'field': 'total_amount', 'old_value': 96947.95, 'new_value': 99277.54}, {'field': 'order_count', 'old_value': 3738, 'new_value': 3847}]
2025-05-28 15:00:32,561 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-28 15:00:33,014 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-28 15:00:33,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27749.68, 'new_value': 30312.72}, {'field': 'offline_amount', 'old_value': 21611.39, 'new_value': 23123.61}, {'field': 'total_amount', 'old_value': 49361.07, 'new_value': 53436.33}, {'field': 'order_count', 'old_value': 2810, 'new_value': 3076}]
2025-05-28 15:00:33,014 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-28 15:00:33,561 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-28 15:00:33,561 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5491.36, 'new_value': 5879.36}, {'field': 'offline_amount', 'old_value': 118558.52, 'new_value': 132243.52}, {'field': 'total_amount', 'old_value': 124049.88, 'new_value': 138122.88}, {'field': 'order_count', 'old_value': 2020, 'new_value': 2215}]
2025-05-28 15:00:33,561 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-28 15:00:34,045 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-28 15:00:34,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77275.9, 'new_value': 82865.89}, {'field': 'total_amount', 'old_value': 77275.9, 'new_value': 82865.89}, {'field': 'order_count', 'old_value': 4037, 'new_value': 4349}]
2025-05-28 15:00:34,045 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-28 15:00:34,467 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-28 15:00:34,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55070.6, 'new_value': 59498.6}, {'field': 'total_amount', 'old_value': 55070.6, 'new_value': 59498.6}, {'field': 'order_count', 'old_value': 237, 'new_value': 251}]
2025-05-28 15:00:34,467 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-28 15:00:34,889 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-28 15:00:34,889 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1366000.0, 'new_value': 1369000.0}, {'field': 'total_amount', 'old_value': 1366000.0, 'new_value': 1369000.0}]
2025-05-28 15:00:34,889 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-28 15:00:35,358 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-28 15:00:35,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 936410.16, 'new_value': 965106.16}, {'field': 'total_amount', 'old_value': 936410.16, 'new_value': 965106.16}, {'field': 'order_count', 'old_value': 3677, 'new_value': 3811}]
2025-05-28 15:00:35,358 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-28 15:00:35,811 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-28 15:00:35,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248689.6, 'new_value': 253279.0}, {'field': 'total_amount', 'old_value': 287323.5, 'new_value': 291912.9}, {'field': 'order_count', 'old_value': 2340, 'new_value': 2393}]
2025-05-28 15:00:35,811 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-28 15:00:36,295 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-28 15:00:36,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151483.54, 'new_value': 157261.54}, {'field': 'total_amount', 'old_value': 151483.54, 'new_value': 157261.54}, {'field': 'order_count', 'old_value': 13608, 'new_value': 14086}]
2025-05-28 15:00:36,295 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-28 15:00:36,779 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-28 15:00:36,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67850.0, 'new_value': 70797.0}, {'field': 'total_amount', 'old_value': 67850.0, 'new_value': 70797.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 74}]
2025-05-28 15:00:36,779 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-28 15:00:37,217 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-28 15:00:37,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130200.0, 'new_value': 131298.0}, {'field': 'total_amount', 'old_value': 130200.0, 'new_value': 131298.0}, {'field': 'order_count', 'old_value': 4544, 'new_value': 4761}]
2025-05-28 15:00:37,217 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-28 15:00:37,670 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-28 15:00:37,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33527.07, 'new_value': 33744.07}, {'field': 'total_amount', 'old_value': 33527.07, 'new_value': 33744.07}, {'field': 'order_count', 'old_value': 3101, 'new_value': 3228}]
2025-05-28 15:00:37,670 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-28 15:00:38,092 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-28 15:00:38,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7376800.0, 'new_value': 7640000.0}, {'field': 'total_amount', 'old_value': 7376800.0, 'new_value': 7640000.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 70}]
2025-05-28 15:00:38,092 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-28 15:00:38,529 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-28 15:00:38,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77163.0, 'new_value': 81780.0}, {'field': 'total_amount', 'old_value': 77163.0, 'new_value': 81780.0}, {'field': 'order_count', 'old_value': 4356, 'new_value': 4629}]
2025-05-28 15:00:38,529 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-28 15:00:39,014 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-28 15:00:39,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 438311.0, 'new_value': 447719.0}, {'field': 'total_amount', 'old_value': 447129.99, 'new_value': 456537.99}, {'field': 'order_count', 'old_value': 80, 'new_value': 81}]
2025-05-28 15:00:39,014 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-28 15:00:39,623 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-28 15:00:39,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 453927.86, 'new_value': 479892.17}, {'field': 'total_amount', 'old_value': 453927.86, 'new_value': 479892.17}, {'field': 'order_count', 'old_value': 834, 'new_value': 861}]
2025-05-28 15:00:39,623 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-28 15:00:40,107 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-28 15:00:40,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95797.0, 'new_value': 95697.0}, {'field': 'total_amount', 'old_value': 95797.0, 'new_value': 95697.0}]
2025-05-28 15:00:40,107 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-28 15:00:40,545 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-28 15:00:40,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109715.15, 'new_value': 113400.15}, {'field': 'total_amount', 'old_value': 109715.15, 'new_value': 113400.15}, {'field': 'order_count', 'old_value': 542, 'new_value': 561}]
2025-05-28 15:00:40,545 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-28 15:00:40,998 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-28 15:00:40,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35875.0, 'new_value': 36111.0}, {'field': 'total_amount', 'old_value': 37251.0, 'new_value': 37487.0}, {'field': 'order_count', 'old_value': 3639, 'new_value': 3771}]
2025-05-28 15:00:40,998 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-28 15:00:41,420 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-28 15:00:41,420 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90761.55, 'new_value': 95652.55}, {'field': 'total_amount', 'old_value': 229815.05, 'new_value': 234706.05}, {'field': 'order_count', 'old_value': 9918, 'new_value': 10299}]
2025-05-28 15:00:41,420 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-28 15:00:41,904 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-28 15:00:41,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 989505.0, 'new_value': 1025194.0}, {'field': 'total_amount', 'old_value': 989505.0, 'new_value': 1025194.0}, {'field': 'order_count', 'old_value': 2127, 'new_value': 2199}]
2025-05-28 15:00:41,904 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-28 15:00:42,404 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-28 15:00:42,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400759.87, 'new_value': 405119.34}, {'field': 'total_amount', 'old_value': 400759.87, 'new_value': 405119.34}, {'field': 'order_count', 'old_value': 2843, 'new_value': 2925}]
2025-05-28 15:00:42,404 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-28 15:00:42,842 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-28 15:00:42,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110852.0, 'new_value': 107694.0}, {'field': 'total_amount', 'old_value': 110852.0, 'new_value': 107694.0}, {'field': 'order_count', 'old_value': 717, 'new_value': 727}]
2025-05-28 15:00:42,842 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-28 15:00:43,373 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-28 15:00:43,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23272.0, 'new_value': 27272.0}, {'field': 'total_amount', 'old_value': 23272.0, 'new_value': 27272.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-28 15:00:43,373 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-28 15:00:43,811 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-28 15:00:43,811 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1302.0, 'new_value': 1471.0}, {'field': 'total_amount', 'old_value': 308787.46, 'new_value': 308956.46}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-05-28 15:00:43,811 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-28 15:00:44,295 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-28 15:00:44,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13018.67, 'new_value': 13494.67}, {'field': 'total_amount', 'old_value': 13018.67, 'new_value': 13494.67}, {'field': 'order_count', 'old_value': 371, 'new_value': 383}]
2025-05-28 15:00:44,295 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-28 15:00:44,686 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-28 15:00:44,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85852.0, 'new_value': 89854.0}, {'field': 'total_amount', 'old_value': 85852.0, 'new_value': 89854.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-28 15:00:44,686 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-28 15:00:45,139 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-28 15:00:45,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60151.0, 'new_value': 60417.0}, {'field': 'total_amount', 'old_value': 60151.0, 'new_value': 60417.0}, {'field': 'order_count', 'old_value': 417, 'new_value': 436}]
2025-05-28 15:00:45,139 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-28 15:00:45,732 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-28 15:00:45,732 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12643.4, 'new_value': 12698.4}, {'field': 'offline_amount', 'old_value': 36553.0, 'new_value': 41960.0}, {'field': 'total_amount', 'old_value': 49196.4, 'new_value': 54658.4}, {'field': 'order_count', 'old_value': 61, 'new_value': 64}]
2025-05-28 15:00:45,732 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-28 15:00:46,217 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-28 15:00:46,217 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11851.84, 'new_value': 12463.78}, {'field': 'offline_amount', 'old_value': 63514.68, 'new_value': 67250.15}, {'field': 'total_amount', 'old_value': 75366.52, 'new_value': 79713.93}, {'field': 'order_count', 'old_value': 1709, 'new_value': 1818}]
2025-05-28 15:00:46,217 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-28 15:00:46,670 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-28 15:00:46,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65477.0, 'new_value': 70786.0}, {'field': 'total_amount', 'old_value': 65477.0, 'new_value': 70786.0}, {'field': 'order_count', 'old_value': 12666, 'new_value': 13827}]
2025-05-28 15:00:46,670 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-28 15:00:47,264 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-28 15:00:47,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96861.0, 'new_value': 104826.0}, {'field': 'total_amount', 'old_value': 96861.0, 'new_value': 104826.0}, {'field': 'order_count', 'old_value': 12666, 'new_value': 13827}]
2025-05-28 15:00:47,264 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-28 15:00:47,717 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-28 15:00:47,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101242.0, 'new_value': 107754.0}, {'field': 'total_amount', 'old_value': 101242.0, 'new_value': 107754.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 267}]
2025-05-28 15:00:47,717 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-28 15:00:48,154 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-28 15:00:48,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1647.5, 'new_value': 1906.5}, {'field': 'offline_amount', 'old_value': 43761.6, 'new_value': 49465.6}, {'field': 'total_amount', 'old_value': 45409.1, 'new_value': 51372.1}, {'field': 'order_count', 'old_value': 304, 'new_value': 345}]
2025-05-28 15:00:48,154 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-28 15:00:48,592 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-28 15:00:48,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162351.82, 'new_value': 164909.82}, {'field': 'total_amount', 'old_value': 162351.82, 'new_value': 164909.82}, {'field': 'order_count', 'old_value': 1372, 'new_value': 1406}]
2025-05-28 15:00:48,592 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-28 15:00:49,123 - INFO - 更新表单数据成功: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-28 15:00:49,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198562.0, 'new_value': 219502.0}, {'field': 'total_amount', 'old_value': 198562.0, 'new_value': 219502.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 185}]
2025-05-28 15:00:49,123 - INFO - 日期 2025-05 处理完成 - 更新: 39 条，插入: 0 条，错误: 0 条
2025-05-28 15:00:49,123 - INFO - 数据同步完成！更新: 39 条，插入: 0 条，错误: 0 条
2025-05-28 15:00:49,123 - INFO - =================同步完成====================
2025-05-28 18:00:01,854 - INFO - =================使用默认全量同步=============
2025-05-28 18:00:03,338 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 18:00:03,338 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 18:00:03,370 - INFO - 开始处理日期: 2025-01
2025-05-28 18:00:03,370 - INFO - Request Parameters - Page 1:
2025-05-28 18:00:03,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:03,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:04,198 - INFO - Response - Page 1:
2025-05-28 18:00:04,401 - INFO - 第 1 页获取到 100 条记录
2025-05-28 18:00:04,401 - INFO - Request Parameters - Page 2:
2025-05-28 18:00:04,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:04,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:05,245 - INFO - Response - Page 2:
2025-05-28 18:00:05,448 - INFO - 第 2 页获取到 100 条记录
2025-05-28 18:00:05,448 - INFO - Request Parameters - Page 3:
2025-05-28 18:00:05,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:05,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:05,948 - INFO - Response - Page 3:
2025-05-28 18:00:06,151 - INFO - 第 3 页获取到 100 条记录
2025-05-28 18:00:06,151 - INFO - Request Parameters - Page 4:
2025-05-28 18:00:06,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:06,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:06,651 - INFO - Response - Page 4:
2025-05-28 18:00:06,854 - INFO - 第 4 页获取到 100 条记录
2025-05-28 18:00:06,854 - INFO - Request Parameters - Page 5:
2025-05-28 18:00:06,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:06,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:07,338 - INFO - Response - Page 5:
2025-05-28 18:00:07,541 - INFO - 第 5 页获取到 100 条记录
2025-05-28 18:00:07,541 - INFO - Request Parameters - Page 6:
2025-05-28 18:00:07,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:07,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:08,104 - INFO - Response - Page 6:
2025-05-28 18:00:08,307 - INFO - 第 6 页获取到 100 条记录
2025-05-28 18:00:08,307 - INFO - Request Parameters - Page 7:
2025-05-28 18:00:08,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:08,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:08,838 - INFO - Response - Page 7:
2025-05-28 18:00:09,041 - INFO - 第 7 页获取到 82 条记录
2025-05-28 18:00:09,041 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 18:00:09,041 - INFO - 获取到 682 条表单数据
2025-05-28 18:00:09,041 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 18:00:09,057 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 18:00:09,057 - INFO - 开始处理日期: 2025-02
2025-05-28 18:00:09,057 - INFO - Request Parameters - Page 1:
2025-05-28 18:00:09,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:09,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:09,620 - INFO - Response - Page 1:
2025-05-28 18:00:09,823 - INFO - 第 1 页获取到 100 条记录
2025-05-28 18:00:09,823 - INFO - Request Parameters - Page 2:
2025-05-28 18:00:09,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:09,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:10,338 - INFO - Response - Page 2:
2025-05-28 18:00:10,542 - INFO - 第 2 页获取到 100 条记录
2025-05-28 18:00:10,542 - INFO - Request Parameters - Page 3:
2025-05-28 18:00:10,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:10,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:11,073 - INFO - Response - Page 3:
2025-05-28 18:00:11,276 - INFO - 第 3 页获取到 100 条记录
2025-05-28 18:00:11,276 - INFO - Request Parameters - Page 4:
2025-05-28 18:00:11,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:11,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:11,823 - INFO - Response - Page 4:
2025-05-28 18:00:12,026 - INFO - 第 4 页获取到 100 条记录
2025-05-28 18:00:12,026 - INFO - Request Parameters - Page 5:
2025-05-28 18:00:12,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:12,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:12,557 - INFO - Response - Page 5:
2025-05-28 18:00:12,760 - INFO - 第 5 页获取到 100 条记录
2025-05-28 18:00:12,760 - INFO - Request Parameters - Page 6:
2025-05-28 18:00:12,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:12,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:13,416 - INFO - Response - Page 6:
2025-05-28 18:00:13,635 - INFO - 第 6 页获取到 100 条记录
2025-05-28 18:00:13,635 - INFO - Request Parameters - Page 7:
2025-05-28 18:00:13,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:13,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:14,135 - INFO - Response - Page 7:
2025-05-28 18:00:14,338 - INFO - 第 7 页获取到 70 条记录
2025-05-28 18:00:14,338 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 18:00:14,338 - INFO - 获取到 670 条表单数据
2025-05-28 18:00:14,338 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 18:00:14,354 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 18:00:14,354 - INFO - 开始处理日期: 2025-03
2025-05-28 18:00:14,354 - INFO - Request Parameters - Page 1:
2025-05-28 18:00:14,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:14,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:14,979 - INFO - Response - Page 1:
2025-05-28 18:00:15,182 - INFO - 第 1 页获取到 100 条记录
2025-05-28 18:00:15,182 - INFO - Request Parameters - Page 2:
2025-05-28 18:00:15,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:15,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:15,666 - INFO - Response - Page 2:
2025-05-28 18:00:15,870 - INFO - 第 2 页获取到 100 条记录
2025-05-28 18:00:15,870 - INFO - Request Parameters - Page 3:
2025-05-28 18:00:15,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:15,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:16,385 - INFO - Response - Page 3:
2025-05-28 18:00:16,588 - INFO - 第 3 页获取到 100 条记录
2025-05-28 18:00:16,588 - INFO - Request Parameters - Page 4:
2025-05-28 18:00:16,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:16,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:17,323 - INFO - Response - Page 4:
2025-05-28 18:00:17,526 - INFO - 第 4 页获取到 100 条记录
2025-05-28 18:00:17,526 - INFO - Request Parameters - Page 5:
2025-05-28 18:00:17,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:17,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:17,979 - INFO - Response - Page 5:
2025-05-28 18:00:18,182 - INFO - 第 5 页获取到 100 条记录
2025-05-28 18:00:18,182 - INFO - Request Parameters - Page 6:
2025-05-28 18:00:18,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:18,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:18,651 - INFO - Response - Page 6:
2025-05-28 18:00:18,854 - INFO - 第 6 页获取到 100 条记录
2025-05-28 18:00:18,854 - INFO - Request Parameters - Page 7:
2025-05-28 18:00:18,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:18,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:19,323 - INFO - Response - Page 7:
2025-05-28 18:00:19,526 - INFO - 第 7 页获取到 61 条记录
2025-05-28 18:00:19,526 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 18:00:19,526 - INFO - 获取到 661 条表单数据
2025-05-28 18:00:19,526 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 18:00:19,541 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 18:00:19,541 - INFO - 开始处理日期: 2025-04
2025-05-28 18:00:19,541 - INFO - Request Parameters - Page 1:
2025-05-28 18:00:19,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:19,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:20,057 - INFO - Response - Page 1:
2025-05-28 18:00:20,260 - INFO - 第 1 页获取到 100 条记录
2025-05-28 18:00:20,260 - INFO - Request Parameters - Page 2:
2025-05-28 18:00:20,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:20,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:20,963 - INFO - Response - Page 2:
2025-05-28 18:00:21,166 - INFO - 第 2 页获取到 100 条记录
2025-05-28 18:00:21,166 - INFO - Request Parameters - Page 3:
2025-05-28 18:00:21,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:21,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:21,698 - INFO - Response - Page 3:
2025-05-28 18:00:21,901 - INFO - 第 3 页获取到 100 条记录
2025-05-28 18:00:21,901 - INFO - Request Parameters - Page 4:
2025-05-28 18:00:21,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:21,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:22,432 - INFO - Response - Page 4:
2025-05-28 18:00:22,635 - INFO - 第 4 页获取到 100 条记录
2025-05-28 18:00:22,635 - INFO - Request Parameters - Page 5:
2025-05-28 18:00:22,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:22,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:23,119 - INFO - Response - Page 5:
2025-05-28 18:00:23,323 - INFO - 第 5 页获取到 100 条记录
2025-05-28 18:00:23,323 - INFO - Request Parameters - Page 6:
2025-05-28 18:00:23,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:23,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:23,869 - INFO - Response - Page 6:
2025-05-28 18:00:24,073 - INFO - 第 6 页获取到 100 条记录
2025-05-28 18:00:24,073 - INFO - Request Parameters - Page 7:
2025-05-28 18:00:24,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:24,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:24,541 - INFO - Response - Page 7:
2025-05-28 18:00:24,744 - INFO - 第 7 页获取到 56 条记录
2025-05-28 18:00:24,744 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 18:00:24,744 - INFO - 获取到 656 条表单数据
2025-05-28 18:00:24,744 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 18:00:24,760 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 18:00:24,760 - INFO - 开始处理日期: 2025-05
2025-05-28 18:00:24,760 - INFO - Request Parameters - Page 1:
2025-05-28 18:00:24,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:24,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:25,291 - INFO - Response - Page 1:
2025-05-28 18:00:25,494 - INFO - 第 1 页获取到 100 条记录
2025-05-28 18:00:25,494 - INFO - Request Parameters - Page 2:
2025-05-28 18:00:25,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:25,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:25,979 - INFO - Response - Page 2:
2025-05-28 18:00:26,182 - INFO - 第 2 页获取到 100 条记录
2025-05-28 18:00:26,182 - INFO - Request Parameters - Page 3:
2025-05-28 18:00:26,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:26,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:26,682 - INFO - Response - Page 3:
2025-05-28 18:00:26,885 - INFO - 第 3 页获取到 100 条记录
2025-05-28 18:00:26,885 - INFO - Request Parameters - Page 4:
2025-05-28 18:00:26,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:26,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:27,385 - INFO - Response - Page 4:
2025-05-28 18:00:27,588 - INFO - 第 4 页获取到 100 条记录
2025-05-28 18:00:27,588 - INFO - Request Parameters - Page 5:
2025-05-28 18:00:27,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:27,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:28,041 - INFO - Response - Page 5:
2025-05-28 18:00:28,244 - INFO - 第 5 页获取到 100 条记录
2025-05-28 18:00:28,244 - INFO - Request Parameters - Page 6:
2025-05-28 18:00:28,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:28,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:28,729 - INFO - Response - Page 6:
2025-05-28 18:00:28,932 - INFO - 第 6 页获取到 100 条记录
2025-05-28 18:00:28,932 - INFO - Request Parameters - Page 7:
2025-05-28 18:00:28,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 18:00:28,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 18:00:29,323 - INFO - Response - Page 7:
2025-05-28 18:00:29,526 - INFO - 第 7 页获取到 34 条记录
2025-05-28 18:00:29,526 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 18:00:29,526 - INFO - 获取到 634 条表单数据
2025-05-28 18:00:29,526 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 18:00:29,526 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-28 18:00:29,948 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-28 18:00:29,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28936.7, 'new_value': 29598.9}, {'field': 'total_amount', 'old_value': 28936.7, 'new_value': 29598.9}, {'field': 'order_count', 'old_value': 195, 'new_value': 202}]
2025-05-28 18:00:29,948 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-28 18:00:30,463 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-28 18:00:30,463 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36726.87, 'new_value': 38388.87}, {'field': 'offline_amount', 'old_value': 16890.57, 'new_value': 17416.57}, {'field': 'total_amount', 'old_value': 53617.44, 'new_value': 55805.44}, {'field': 'order_count', 'old_value': 2701, 'new_value': 2800}]
2025-05-28 18:00:30,463 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-28 18:00:30,901 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-28 18:00:30,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31579.88, 'new_value': 33434.63}, {'field': 'offline_amount', 'old_value': 98229.15, 'new_value': 101838.58}, {'field': 'total_amount', 'old_value': 129809.03, 'new_value': 135273.21}, {'field': 'order_count', 'old_value': 2999, 'new_value': 3086}]
2025-05-28 18:00:30,901 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-28 18:00:31,276 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-28 18:00:31,276 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102860.67, 'new_value': 107611.57}, {'field': 'offline_amount', 'old_value': 57960.15, 'new_value': 58476.95}, {'field': 'total_amount', 'old_value': 160820.82, 'new_value': 166088.52}, {'field': 'order_count', 'old_value': 9044, 'new_value': 9355}]
2025-05-28 18:00:31,276 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-28 18:00:31,760 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-28 18:00:31,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118823.9, 'new_value': 120516.9}, {'field': 'total_amount', 'old_value': 118823.9, 'new_value': 120516.9}, {'field': 'order_count', 'old_value': 90, 'new_value': 91}]
2025-05-28 18:00:31,760 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-28 18:00:32,276 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-28 18:00:32,276 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2995.13, 'new_value': 3092.15}, {'field': 'offline_amount', 'old_value': 25723.84, 'new_value': 26411.08}, {'field': 'total_amount', 'old_value': 28718.97, 'new_value': 29503.23}, {'field': 'order_count', 'old_value': 1321, 'new_value': 1353}]
2025-05-28 18:00:32,276 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-28 18:00:32,635 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-28 18:00:32,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117744.0, 'new_value': 119552.0}, {'field': 'total_amount', 'old_value': 117744.0, 'new_value': 119552.0}, {'field': 'order_count', 'old_value': 1174, 'new_value': 1199}]
2025-05-28 18:00:32,635 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-28 18:00:33,119 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-28 18:00:33,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60777.7, 'new_value': 61688.1}, {'field': 'offline_amount', 'old_value': 35987.14, 'new_value': 37467.64}, {'field': 'total_amount', 'old_value': 96764.84, 'new_value': 99155.74}, {'field': 'order_count', 'old_value': 11710, 'new_value': 11880}]
2025-05-28 18:00:33,119 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-28 18:00:33,604 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-28 18:00:33,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51214.29, 'new_value': 54824.87}, {'field': 'offline_amount', 'old_value': 254230.84, 'new_value': 258068.3}, {'field': 'total_amount', 'old_value': 305445.13, 'new_value': 312893.17}, {'field': 'order_count', 'old_value': 5770, 'new_value': 6067}]
2025-05-28 18:00:33,604 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-28 18:00:34,229 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-28 18:00:34,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44632.0, 'new_value': 46358.0}, {'field': 'total_amount', 'old_value': 45009.0, 'new_value': 46735.0}, {'field': 'order_count', 'old_value': 240, 'new_value': 250}]
2025-05-28 18:00:34,229 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-28 18:00:34,682 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-28 18:00:34,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326220.94, 'new_value': 335394.38}, {'field': 'total_amount', 'old_value': 359301.15, 'new_value': 368474.59}, {'field': 'order_count', 'old_value': 15139, 'new_value': 15553}]
2025-05-28 18:00:34,682 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-28 18:00:35,182 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-28 18:00:35,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21845.67, 'new_value': 23594.14}, {'field': 'total_amount', 'old_value': 23595.67, 'new_value': 25344.14}, {'field': 'order_count', 'old_value': 459, 'new_value': 491}]
2025-05-28 18:00:35,182 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-28 18:00:35,651 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-28 18:00:35,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129779.6, 'new_value': 135572.6}, {'field': 'total_amount', 'old_value': 185709.65, 'new_value': 191502.65}, {'field': 'order_count', 'old_value': 10203, 'new_value': 10483}]
2025-05-28 18:00:35,651 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-28 18:00:36,135 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-28 18:00:36,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83335.0, 'new_value': 85695.0}, {'field': 'total_amount', 'old_value': 83335.0, 'new_value': 85695.0}, {'field': 'order_count', 'old_value': 581, 'new_value': 594}]
2025-05-28 18:00:36,135 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-28 18:00:36,588 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-28 18:00:36,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8802.13, 'new_value': 9117.94}, {'field': 'offline_amount', 'old_value': 133337.7, 'new_value': 137345.5}, {'field': 'total_amount', 'old_value': 142139.83, 'new_value': 146463.44}, {'field': 'order_count', 'old_value': 7140, 'new_value': 7341}]
2025-05-28 18:00:36,588 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-28 18:00:37,041 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-28 18:00:37,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18525.73, 'new_value': 18897.19}, {'field': 'offline_amount', 'old_value': 137497.03, 'new_value': 140601.74}, {'field': 'total_amount', 'old_value': 156022.76, 'new_value': 159498.93}, {'field': 'order_count', 'old_value': 4683, 'new_value': 4778}]
2025-05-28 18:00:37,041 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-28 18:00:37,463 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-28 18:00:37,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107852.3, 'new_value': 109537.5}, {'field': 'total_amount', 'old_value': 107852.3, 'new_value': 109537.5}, {'field': 'order_count', 'old_value': 5372, 'new_value': 5478}]
2025-05-28 18:00:37,463 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-28 18:00:37,948 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-28 18:00:37,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13515.0, 'new_value': 13560.0}, {'field': 'total_amount', 'old_value': 13515.0, 'new_value': 13560.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-28 18:00:37,948 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-28 18:00:38,541 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-28 18:00:38,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14069.0, 'new_value': 14169.0}, {'field': 'total_amount', 'old_value': 14069.0, 'new_value': 14169.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-28 18:00:38,541 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-28 18:00:38,994 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-28 18:00:38,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343028.5, 'new_value': 352974.5}, {'field': 'total_amount', 'old_value': 343028.5, 'new_value': 352974.5}, {'field': 'order_count', 'old_value': 60, 'new_value': 63}]
2025-05-28 18:00:38,994 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-28 18:00:39,447 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-28 18:00:39,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9777.0, 'new_value': 10176.0}, {'field': 'total_amount', 'old_value': 14571.0, 'new_value': 14970.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-28 18:00:39,447 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-28 18:00:40,010 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-28 18:00:40,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132199.0, 'new_value': 134350.0}, {'field': 'total_amount', 'old_value': 132199.0, 'new_value': 134350.0}, {'field': 'order_count', 'old_value': 632, 'new_value': 658}]
2025-05-28 18:00:40,010 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-05-28 18:00:40,010 - INFO - 数据同步完成！更新: 22 条，插入: 0 条，错误: 0 条
2025-05-28 18:00:40,010 - INFO - =================同步完成====================
2025-05-28 21:00:02,324 - INFO - =================使用默认全量同步=============
2025-05-28 21:00:03,809 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-28 21:00:03,824 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-28 21:00:03,840 - INFO - 开始处理日期: 2025-01
2025-05-28 21:00:03,840 - INFO - Request Parameters - Page 1:
2025-05-28 21:00:03,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:03,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:05,106 - INFO - Response - Page 1:
2025-05-28 21:00:05,309 - INFO - 第 1 页获取到 100 条记录
2025-05-28 21:00:05,309 - INFO - Request Parameters - Page 2:
2025-05-28 21:00:05,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:05,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:05,856 - INFO - Response - Page 2:
2025-05-28 21:00:06,059 - INFO - 第 2 页获取到 100 条记录
2025-05-28 21:00:06,059 - INFO - Request Parameters - Page 3:
2025-05-28 21:00:06,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:06,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:06,560 - INFO - Response - Page 3:
2025-05-28 21:00:06,763 - INFO - 第 3 页获取到 100 条记录
2025-05-28 21:00:06,763 - INFO - Request Parameters - Page 4:
2025-05-28 21:00:06,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:06,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:07,325 - INFO - Response - Page 4:
2025-05-28 21:00:07,529 - INFO - 第 4 页获取到 100 条记录
2025-05-28 21:00:07,529 - INFO - Request Parameters - Page 5:
2025-05-28 21:00:07,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:07,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:08,169 - INFO - Response - Page 5:
2025-05-28 21:00:08,373 - INFO - 第 5 页获取到 100 条记录
2025-05-28 21:00:08,373 - INFO - Request Parameters - Page 6:
2025-05-28 21:00:08,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:08,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:08,873 - INFO - Response - Page 6:
2025-05-28 21:00:09,076 - INFO - 第 6 页获取到 100 条记录
2025-05-28 21:00:09,076 - INFO - Request Parameters - Page 7:
2025-05-28 21:00:09,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:09,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:09,623 - INFO - Response - Page 7:
2025-05-28 21:00:09,826 - INFO - 第 7 页获取到 82 条记录
2025-05-28 21:00:09,826 - INFO - 查询完成，共获取到 682 条记录
2025-05-28 21:00:09,826 - INFO - 获取到 682 条表单数据
2025-05-28 21:00:09,826 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-28 21:00:09,842 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 21:00:09,842 - INFO - 开始处理日期: 2025-02
2025-05-28 21:00:09,842 - INFO - Request Parameters - Page 1:
2025-05-28 21:00:09,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:09,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:10,420 - INFO - Response - Page 1:
2025-05-28 21:00:10,623 - INFO - 第 1 页获取到 100 条记录
2025-05-28 21:00:10,623 - INFO - Request Parameters - Page 2:
2025-05-28 21:00:10,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:10,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:11,155 - INFO - Response - Page 2:
2025-05-28 21:00:11,358 - INFO - 第 2 页获取到 100 条记录
2025-05-28 21:00:11,358 - INFO - Request Parameters - Page 3:
2025-05-28 21:00:11,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:11,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:11,905 - INFO - Response - Page 3:
2025-05-28 21:00:12,108 - INFO - 第 3 页获取到 100 条记录
2025-05-28 21:00:12,108 - INFO - Request Parameters - Page 4:
2025-05-28 21:00:12,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:12,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:12,624 - INFO - Response - Page 4:
2025-05-28 21:00:12,827 - INFO - 第 4 页获取到 100 条记录
2025-05-28 21:00:12,827 - INFO - Request Parameters - Page 5:
2025-05-28 21:00:12,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:12,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:13,421 - INFO - Response - Page 5:
2025-05-28 21:00:13,624 - INFO - 第 5 页获取到 100 条记录
2025-05-28 21:00:13,624 - INFO - Request Parameters - Page 6:
2025-05-28 21:00:13,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:13,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:14,124 - INFO - Response - Page 6:
2025-05-28 21:00:14,327 - INFO - 第 6 页获取到 100 条记录
2025-05-28 21:00:14,327 - INFO - Request Parameters - Page 7:
2025-05-28 21:00:14,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:14,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:14,749 - INFO - Response - Page 7:
2025-05-28 21:00:14,953 - INFO - 第 7 页获取到 70 条记录
2025-05-28 21:00:14,953 - INFO - 查询完成，共获取到 670 条记录
2025-05-28 21:00:14,953 - INFO - 获取到 670 条表单数据
2025-05-28 21:00:14,953 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-28 21:00:14,968 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 21:00:14,968 - INFO - 开始处理日期: 2025-03
2025-05-28 21:00:14,968 - INFO - Request Parameters - Page 1:
2025-05-28 21:00:14,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:14,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:15,500 - INFO - Response - Page 1:
2025-05-28 21:00:15,703 - INFO - 第 1 页获取到 100 条记录
2025-05-28 21:00:15,703 - INFO - Request Parameters - Page 2:
2025-05-28 21:00:15,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:15,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:16,234 - INFO - Response - Page 2:
2025-05-28 21:00:16,437 - INFO - 第 2 页获取到 100 条记录
2025-05-28 21:00:16,437 - INFO - Request Parameters - Page 3:
2025-05-28 21:00:16,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:16,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:16,922 - INFO - Response - Page 3:
2025-05-28 21:00:17,125 - INFO - 第 3 页获取到 100 条记录
2025-05-28 21:00:17,125 - INFO - Request Parameters - Page 4:
2025-05-28 21:00:17,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:17,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:17,625 - INFO - Response - Page 4:
2025-05-28 21:00:17,828 - INFO - 第 4 页获取到 100 条记录
2025-05-28 21:00:17,828 - INFO - Request Parameters - Page 5:
2025-05-28 21:00:17,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:17,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:18,329 - INFO - Response - Page 5:
2025-05-28 21:00:18,532 - INFO - 第 5 页获取到 100 条记录
2025-05-28 21:00:18,532 - INFO - Request Parameters - Page 6:
2025-05-28 21:00:18,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:18,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:19,313 - INFO - Response - Page 6:
2025-05-28 21:00:19,516 - INFO - 第 6 页获取到 100 条记录
2025-05-28 21:00:19,516 - INFO - Request Parameters - Page 7:
2025-05-28 21:00:19,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:19,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:19,954 - INFO - Response - Page 7:
2025-05-28 21:00:20,157 - INFO - 第 7 页获取到 61 条记录
2025-05-28 21:00:20,157 - INFO - 查询完成，共获取到 661 条记录
2025-05-28 21:00:20,157 - INFO - 获取到 661 条表单数据
2025-05-28 21:00:20,157 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-28 21:00:20,173 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 21:00:20,173 - INFO - 开始处理日期: 2025-04
2025-05-28 21:00:20,173 - INFO - Request Parameters - Page 1:
2025-05-28 21:00:20,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:20,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:20,704 - INFO - Response - Page 1:
2025-05-28 21:00:20,907 - INFO - 第 1 页获取到 100 条记录
2025-05-28 21:00:20,907 - INFO - Request Parameters - Page 2:
2025-05-28 21:00:20,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:20,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:21,454 - INFO - Response - Page 2:
2025-05-28 21:00:21,658 - INFO - 第 2 页获取到 100 条记录
2025-05-28 21:00:21,658 - INFO - Request Parameters - Page 3:
2025-05-28 21:00:21,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:21,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:22,173 - INFO - Response - Page 3:
2025-05-28 21:00:22,377 - INFO - 第 3 页获取到 100 条记录
2025-05-28 21:00:22,377 - INFO - Request Parameters - Page 4:
2025-05-28 21:00:22,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:22,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:22,877 - INFO - Response - Page 4:
2025-05-28 21:00:23,080 - INFO - 第 4 页获取到 100 条记录
2025-05-28 21:00:23,080 - INFO - Request Parameters - Page 5:
2025-05-28 21:00:23,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:23,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:23,596 - INFO - Response - Page 5:
2025-05-28 21:00:23,799 - INFO - 第 5 页获取到 100 条记录
2025-05-28 21:00:23,799 - INFO - Request Parameters - Page 6:
2025-05-28 21:00:23,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:23,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:24,283 - INFO - Response - Page 6:
2025-05-28 21:00:24,487 - INFO - 第 6 页获取到 100 条记录
2025-05-28 21:00:24,487 - INFO - Request Parameters - Page 7:
2025-05-28 21:00:24,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:24,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:24,893 - INFO - Response - Page 7:
2025-05-28 21:00:25,096 - INFO - 第 7 页获取到 56 条记录
2025-05-28 21:00:25,096 - INFO - 查询完成，共获取到 656 条记录
2025-05-28 21:00:25,096 - INFO - 获取到 656 条表单数据
2025-05-28 21:00:25,096 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-28 21:00:25,112 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-28 21:00:25,112 - INFO - 开始处理日期: 2025-05
2025-05-28 21:00:25,112 - INFO - Request Parameters - Page 1:
2025-05-28 21:00:25,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:25,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:25,721 - INFO - Response - Page 1:
2025-05-28 21:00:25,924 - INFO - 第 1 页获取到 100 条记录
2025-05-28 21:00:25,924 - INFO - Request Parameters - Page 2:
2025-05-28 21:00:25,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:25,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:26,550 - INFO - Response - Page 2:
2025-05-28 21:00:26,753 - INFO - 第 2 页获取到 100 条记录
2025-05-28 21:00:26,753 - INFO - Request Parameters - Page 3:
2025-05-28 21:00:26,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:26,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:27,269 - INFO - Response - Page 3:
2025-05-28 21:00:27,472 - INFO - 第 3 页获取到 100 条记录
2025-05-28 21:00:27,472 - INFO - Request Parameters - Page 4:
2025-05-28 21:00:27,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:27,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:28,238 - INFO - Response - Page 4:
2025-05-28 21:00:28,441 - INFO - 第 4 页获取到 100 条记录
2025-05-28 21:00:28,441 - INFO - Request Parameters - Page 5:
2025-05-28 21:00:28,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:28,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:28,941 - INFO - Response - Page 5:
2025-05-28 21:00:29,144 - INFO - 第 5 页获取到 100 条记录
2025-05-28 21:00:29,144 - INFO - Request Parameters - Page 6:
2025-05-28 21:00:29,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:29,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:29,644 - INFO - Response - Page 6:
2025-05-28 21:00:29,847 - INFO - 第 6 页获取到 100 条记录
2025-05-28 21:00:29,847 - INFO - Request Parameters - Page 7:
2025-05-28 21:00:29,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 21:00:29,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 21:00:30,254 - INFO - Response - Page 7:
2025-05-28 21:00:30,457 - INFO - 第 7 页获取到 34 条记录
2025-05-28 21:00:30,457 - INFO - 查询完成，共获取到 634 条记录
2025-05-28 21:00:30,457 - INFO - 获取到 634 条表单数据
2025-05-28 21:00:30,457 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-28 21:00:30,457 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-28 21:00:30,941 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-28 21:00:30,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 449382.71, 'new_value': 460711.71}, {'field': 'total_amount', 'old_value': 482565.71, 'new_value': 493894.71}, {'field': 'order_count', 'old_value': 458, 'new_value': 468}]
2025-05-28 21:00:30,941 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-28 21:00:31,395 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-28 21:00:31,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1866959.51, 'new_value': 1946866.0}, {'field': 'total_amount', 'old_value': 2029399.81, 'new_value': 2109306.3}, {'field': 'order_count', 'old_value': 7004, 'new_value': 7276}]
2025-05-28 21:00:31,395 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-28 21:00:31,848 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-28 21:00:31,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193818.25, 'new_value': 200172.24}, {'field': 'total_amount', 'old_value': 193818.25, 'new_value': 200172.24}, {'field': 'order_count', 'old_value': 2518, 'new_value': 2621}]
2025-05-28 21:00:31,848 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-28 21:00:31,848 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-05-28 21:00:31,848 - INFO - =================同步完成====================
