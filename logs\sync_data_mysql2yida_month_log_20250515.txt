2025-05-15 00:00:03,983 - INFO - =================使用默认全量同步=============
2025-05-15 00:00:05,375 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 00:00:05,375 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 00:00:05,406 - INFO - 开始处理日期: 2025-01
2025-05-15 00:00:05,406 - INFO - Request Parameters - Page 1:
2025-05-15 00:00:05,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:05,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:06,438 - INFO - Response - Page 1:
2025-05-15 00:00:06,641 - INFO - 第 1 页获取到 100 条记录
2025-05-15 00:00:06,641 - INFO - Request Parameters - Page 2:
2025-05-15 00:00:06,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:06,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:07,157 - INFO - Response - Page 2:
2025-05-15 00:00:07,361 - INFO - 第 2 页获取到 100 条记录
2025-05-15 00:00:07,361 - INFO - Request Parameters - Page 3:
2025-05-15 00:00:07,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:07,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:07,845 - INFO - Response - Page 3:
2025-05-15 00:00:08,049 - INFO - 第 3 页获取到 100 条记录
2025-05-15 00:00:08,049 - INFO - Request Parameters - Page 4:
2025-05-15 00:00:08,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:08,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:08,549 - INFO - Response - Page 4:
2025-05-15 00:00:08,752 - INFO - 第 4 页获取到 100 条记录
2025-05-15 00:00:08,752 - INFO - Request Parameters - Page 5:
2025-05-15 00:00:08,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:08,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:09,550 - INFO - Response - Page 5:
2025-05-15 00:00:09,753 - INFO - 第 5 页获取到 100 条记录
2025-05-15 00:00:09,753 - INFO - Request Parameters - Page 6:
2025-05-15 00:00:09,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:09,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:10,222 - INFO - Response - Page 6:
2025-05-15 00:00:10,425 - INFO - 第 6 页获取到 100 条记录
2025-05-15 00:00:10,425 - INFO - Request Parameters - Page 7:
2025-05-15 00:00:10,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:10,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:10,926 - INFO - Response - Page 7:
2025-05-15 00:00:11,129 - INFO - 第 7 页获取到 82 条记录
2025-05-15 00:00:11,129 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 00:00:11,129 - INFO - 获取到 682 条表单数据
2025-05-15 00:00:11,129 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 00:00:11,145 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 00:00:11,145 - INFO - 开始处理日期: 2025-02
2025-05-15 00:00:11,145 - INFO - Request Parameters - Page 1:
2025-05-15 00:00:11,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:11,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:11,708 - INFO - Response - Page 1:
2025-05-15 00:00:11,911 - INFO - 第 1 页获取到 100 条记录
2025-05-15 00:00:11,911 - INFO - Request Parameters - Page 2:
2025-05-15 00:00:11,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:11,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:12,333 - INFO - Response - Page 2:
2025-05-15 00:00:12,536 - INFO - 第 2 页获取到 100 条记录
2025-05-15 00:00:12,536 - INFO - Request Parameters - Page 3:
2025-05-15 00:00:12,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:12,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:13,052 - INFO - Response - Page 3:
2025-05-15 00:00:13,256 - INFO - 第 3 页获取到 100 条记录
2025-05-15 00:00:13,256 - INFO - Request Parameters - Page 4:
2025-05-15 00:00:13,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:13,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:13,709 - INFO - Response - Page 4:
2025-05-15 00:00:13,928 - INFO - 第 4 页获取到 100 条记录
2025-05-15 00:00:13,928 - INFO - Request Parameters - Page 5:
2025-05-15 00:00:13,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:13,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:14,428 - INFO - Response - Page 5:
2025-05-15 00:00:14,631 - INFO - 第 5 页获取到 100 条记录
2025-05-15 00:00:14,631 - INFO - Request Parameters - Page 6:
2025-05-15 00:00:14,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:14,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:15,147 - INFO - Response - Page 6:
2025-05-15 00:00:15,351 - INFO - 第 6 页获取到 100 条记录
2025-05-15 00:00:15,351 - INFO - Request Parameters - Page 7:
2025-05-15 00:00:15,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:15,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:15,835 - INFO - Response - Page 7:
2025-05-15 00:00:16,039 - INFO - 第 7 页获取到 70 条记录
2025-05-15 00:00:16,039 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 00:00:16,039 - INFO - 获取到 670 条表单数据
2025-05-15 00:00:16,039 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 00:00:16,054 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 00:00:16,054 - INFO - 开始处理日期: 2025-03
2025-05-15 00:00:16,054 - INFO - Request Parameters - Page 1:
2025-05-15 00:00:16,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:16,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:16,523 - INFO - Response - Page 1:
2025-05-15 00:00:16,727 - INFO - 第 1 页获取到 100 条记录
2025-05-15 00:00:16,727 - INFO - Request Parameters - Page 2:
2025-05-15 00:00:16,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:16,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:17,180 - INFO - Response - Page 2:
2025-05-15 00:00:17,383 - INFO - 第 2 页获取到 100 条记录
2025-05-15 00:00:17,383 - INFO - Request Parameters - Page 3:
2025-05-15 00:00:17,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:17,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:17,853 - INFO - Response - Page 3:
2025-05-15 00:00:18,056 - INFO - 第 3 页获取到 100 条记录
2025-05-15 00:00:18,056 - INFO - Request Parameters - Page 4:
2025-05-15 00:00:18,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:18,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:18,541 - INFO - Response - Page 4:
2025-05-15 00:00:18,744 - INFO - 第 4 页获取到 100 条记录
2025-05-15 00:00:18,744 - INFO - Request Parameters - Page 5:
2025-05-15 00:00:18,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:18,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:19,385 - INFO - Response - Page 5:
2025-05-15 00:00:19,588 - INFO - 第 5 页获取到 100 条记录
2025-05-15 00:00:19,588 - INFO - Request Parameters - Page 6:
2025-05-15 00:00:19,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:19,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:20,151 - INFO - Response - Page 6:
2025-05-15 00:00:20,354 - INFO - 第 6 页获取到 100 条记录
2025-05-15 00:00:20,354 - INFO - Request Parameters - Page 7:
2025-05-15 00:00:20,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:20,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:20,761 - INFO - Response - Page 7:
2025-05-15 00:00:20,964 - INFO - 第 7 页获取到 61 条记录
2025-05-15 00:00:20,964 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 00:00:20,964 - INFO - 获取到 661 条表单数据
2025-05-15 00:00:20,964 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 00:00:20,980 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 00:00:20,980 - INFO - 开始处理日期: 2025-04
2025-05-15 00:00:20,980 - INFO - Request Parameters - Page 1:
2025-05-15 00:00:20,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:20,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:21,433 - INFO - Response - Page 1:
2025-05-15 00:00:21,636 - INFO - 第 1 页获取到 100 条记录
2025-05-15 00:00:21,636 - INFO - Request Parameters - Page 2:
2025-05-15 00:00:21,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:21,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:22,090 - INFO - Response - Page 2:
2025-05-15 00:00:22,293 - INFO - 第 2 页获取到 100 条记录
2025-05-15 00:00:22,293 - INFO - Request Parameters - Page 3:
2025-05-15 00:00:22,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:22,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:22,778 - INFO - Response - Page 3:
2025-05-15 00:00:22,981 - INFO - 第 3 页获取到 100 条记录
2025-05-15 00:00:22,981 - INFO - Request Parameters - Page 4:
2025-05-15 00:00:22,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:22,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:23,622 - INFO - Response - Page 4:
2025-05-15 00:00:23,826 - INFO - 第 4 页获取到 100 条记录
2025-05-15 00:00:23,826 - INFO - Request Parameters - Page 5:
2025-05-15 00:00:23,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:23,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:24,279 - INFO - Response - Page 5:
2025-05-15 00:00:24,482 - INFO - 第 5 页获取到 100 条记录
2025-05-15 00:00:24,482 - INFO - Request Parameters - Page 6:
2025-05-15 00:00:24,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:24,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:24,967 - INFO - Response - Page 6:
2025-05-15 00:00:25,170 - INFO - 第 6 页获取到 100 条记录
2025-05-15 00:00:25,170 - INFO - Request Parameters - Page 7:
2025-05-15 00:00:25,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:25,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:25,545 - INFO - Response - Page 7:
2025-05-15 00:00:25,749 - INFO - 第 7 页获取到 54 条记录
2025-05-15 00:00:25,749 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 00:00:25,749 - INFO - 获取到 654 条表单数据
2025-05-15 00:00:25,749 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 00:00:25,764 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 00:00:25,764 - INFO - 开始处理日期: 2025-05
2025-05-15 00:00:25,764 - INFO - Request Parameters - Page 1:
2025-05-15 00:00:25,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:25,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:26,218 - INFO - Response - Page 1:
2025-05-15 00:00:26,421 - INFO - 第 1 页获取到 100 条记录
2025-05-15 00:00:26,421 - INFO - Request Parameters - Page 2:
2025-05-15 00:00:26,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:26,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:26,953 - INFO - Response - Page 2:
2025-05-15 00:00:27,156 - INFO - 第 2 页获取到 100 条记录
2025-05-15 00:00:27,156 - INFO - Request Parameters - Page 3:
2025-05-15 00:00:27,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:27,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:27,672 - INFO - Response - Page 3:
2025-05-15 00:00:27,875 - INFO - 第 3 页获取到 100 条记录
2025-05-15 00:00:27,875 - INFO - Request Parameters - Page 4:
2025-05-15 00:00:27,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:27,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:28,297 - INFO - Response - Page 4:
2025-05-15 00:00:28,501 - INFO - 第 4 页获取到 100 条记录
2025-05-15 00:00:28,501 - INFO - Request Parameters - Page 5:
2025-05-15 00:00:28,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:28,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:29,204 - INFO - Response - Page 5:
2025-05-15 00:00:29,408 - INFO - 第 5 页获取到 100 条记录
2025-05-15 00:00:29,408 - INFO - Request Parameters - Page 6:
2025-05-15 00:00:29,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:29,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:29,892 - INFO - Response - Page 6:
2025-05-15 00:00:30,096 - INFO - 第 6 页获取到 100 条记录
2025-05-15 00:00:30,096 - INFO - Request Parameters - Page 7:
2025-05-15 00:00:30,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 00:00:30,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 00:00:30,408 - INFO - Response - Page 7:
2025-05-15 00:00:30,612 - INFO - 第 7 页获取到 25 条记录
2025-05-15 00:00:30,612 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 00:00:30,612 - INFO - 获取到 625 条表单数据
2025-05-15 00:00:30,612 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 00:00:30,612 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-15 00:00:31,049 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-15 00:00:31,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66425.0, 'new_value': 70239.0}, {'field': 'offline_amount', 'old_value': 67205.28, 'new_value': 74267.28}, {'field': 'total_amount', 'old_value': 133630.28, 'new_value': 144506.28}, {'field': 'order_count', 'old_value': 2844, 'new_value': 3092}]
2025-05-15 00:00:31,049 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-15 00:00:31,456 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-15 00:00:31,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12189.2, 'new_value': 13393.2}, {'field': 'offline_amount', 'old_value': 69197.66, 'new_value': 74364.66}, {'field': 'total_amount', 'old_value': 81386.86, 'new_value': 87757.86}, {'field': 'order_count', 'old_value': 135, 'new_value': 148}]
2025-05-15 00:00:31,456 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-15 00:00:31,862 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-15 00:00:31,862 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25921.2, 'new_value': 27345.45}, {'field': 'offline_amount', 'old_value': 358397.34, 'new_value': 389072.74}, {'field': 'total_amount', 'old_value': 384318.54, 'new_value': 416418.19}, {'field': 'order_count', 'old_value': 1560, 'new_value': 1686}]
2025-05-15 00:00:31,862 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-15 00:00:32,253 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-15 00:00:32,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1012239.65, 'new_value': 1064804.23}, {'field': 'total_amount', 'old_value': 1012239.65, 'new_value': 1064804.23}, {'field': 'order_count', 'old_value': 7692, 'new_value': 8267}]
2025-05-15 00:00:32,253 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-15 00:00:32,676 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-15 00:00:32,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3953.93, 'new_value': 4290.85}, {'field': 'offline_amount', 'old_value': 7715.64, 'new_value': 7887.64}, {'field': 'total_amount', 'old_value': 11669.57, 'new_value': 12178.49}, {'field': 'order_count', 'old_value': 868, 'new_value': 910}]
2025-05-15 00:00:32,691 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-15 00:00:33,113 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-15 00:00:33,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28082.03, 'new_value': 29152.23}, {'field': 'offline_amount', 'old_value': 238712.66, 'new_value': 244467.82}, {'field': 'total_amount', 'old_value': 266794.69, 'new_value': 273620.05}, {'field': 'order_count', 'old_value': 2218, 'new_value': 2292}]
2025-05-15 00:00:33,113 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-15 00:00:33,520 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-15 00:00:33,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47854.98, 'new_value': 51387.72}, {'field': 'offline_amount', 'old_value': 24896.56, 'new_value': 25448.2}, {'field': 'total_amount', 'old_value': 72751.54, 'new_value': 76835.92}, {'field': 'order_count', 'old_value': 4663, 'new_value': 4923}]
2025-05-15 00:00:33,520 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-15 00:00:33,989 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-15 00:00:33,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167181.63, 'new_value': 174800.96}, {'field': 'offline_amount', 'old_value': 10253.95, 'new_value': 10865.9}, {'field': 'total_amount', 'old_value': 177435.58, 'new_value': 185666.86}, {'field': 'order_count', 'old_value': 6578, 'new_value': 6906}]
2025-05-15 00:00:33,989 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-15 00:00:34,489 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-15 00:00:34,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 850.0, 'new_value': 1350.0}, {'field': 'offline_amount', 'old_value': 13185.5, 'new_value': 13345.17}, {'field': 'total_amount', 'old_value': 14035.5, 'new_value': 14695.17}, {'field': 'order_count', 'old_value': 270, 'new_value': 281}]
2025-05-15 00:00:34,489 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-15 00:00:34,927 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-15 00:00:34,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57479.2, 'new_value': 58536.8}, {'field': 'offline_amount', 'old_value': 85828.4, 'new_value': 87547.4}, {'field': 'total_amount', 'old_value': 143307.6, 'new_value': 146084.2}, {'field': 'order_count', 'old_value': 2886, 'new_value': 2942}]
2025-05-15 00:00:34,927 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-15 00:00:35,334 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-15 00:00:35,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51718.25, 'new_value': 53617.96}, {'field': 'total_amount', 'old_value': 51718.25, 'new_value': 53617.96}, {'field': 'order_count', 'old_value': 1558, 'new_value': 1616}]
2025-05-15 00:00:35,334 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-15 00:00:35,693 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-15 00:00:35,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19089.39, 'new_value': 20023.28}, {'field': 'offline_amount', 'old_value': 195254.79, 'new_value': 200858.69}, {'field': 'total_amount', 'old_value': 214344.18, 'new_value': 220881.97}, {'field': 'order_count', 'old_value': 4997, 'new_value': 5133}]
2025-05-15 00:00:35,693 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-15 00:00:36,131 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-15 00:00:36,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 268505.7, 'new_value': 279013.4}, {'field': 'total_amount', 'old_value': 268505.7, 'new_value': 279013.4}, {'field': 'order_count', 'old_value': 1323, 'new_value': 1378}]
2025-05-15 00:00:36,131 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-15 00:00:36,585 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-15 00:00:36,585 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84875.14, 'new_value': 88163.25}, {'field': 'offline_amount', 'old_value': 30807.51, 'new_value': 31824.1}, {'field': 'total_amount', 'old_value': 115682.65, 'new_value': 119987.35}, {'field': 'order_count', 'old_value': 7264, 'new_value': 7554}]
2025-05-15 00:00:36,585 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-15 00:00:37,007 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-15 00:00:37,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431958.4, 'new_value': 445909.89}, {'field': 'total_amount', 'old_value': 431958.4, 'new_value': 445909.89}, {'field': 'order_count', 'old_value': 8669, 'new_value': 8868}]
2025-05-15 00:00:37,007 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-15 00:00:37,413 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-15 00:00:37,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154441.42, 'new_value': 163911.64}, {'field': 'total_amount', 'old_value': 154441.42, 'new_value': 163911.64}, {'field': 'order_count', 'old_value': 6409, 'new_value': 6841}]
2025-05-15 00:00:37,413 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-15 00:00:37,789 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-15 00:00:37,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401221.29, 'new_value': 420987.88}, {'field': 'total_amount', 'old_value': 401221.29, 'new_value': 420987.88}, {'field': 'order_count', 'old_value': 2695, 'new_value': 2831}]
2025-05-15 00:00:37,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-15 00:00:38,305 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-15 00:00:38,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36592.49, 'new_value': 39230.55}, {'field': 'offline_amount', 'old_value': 50685.22, 'new_value': 52872.14}, {'field': 'total_amount', 'old_value': 87277.71, 'new_value': 92102.69}, {'field': 'order_count', 'old_value': 4023, 'new_value': 4223}]
2025-05-15 00:00:38,305 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-15 00:00:38,711 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-15 00:00:38,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14194.56, 'new_value': 16156.72}, {'field': 'offline_amount', 'old_value': 140222.74, 'new_value': 144342.24}, {'field': 'total_amount', 'old_value': 154417.3, 'new_value': 160498.96}, {'field': 'order_count', 'old_value': 4747, 'new_value': 4955}]
2025-05-15 00:00:38,727 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-15 00:00:39,133 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-15 00:00:39,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19633.02, 'new_value': 20701.41}, {'field': 'total_amount', 'old_value': 19633.02, 'new_value': 20701.41}, {'field': 'order_count', 'old_value': 752, 'new_value': 805}]
2025-05-15 00:00:39,133 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-15 00:00:39,571 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-15 00:00:39,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1144.36, 'new_value': 1542.96}, {'field': 'offline_amount', 'old_value': 9755.88, 'new_value': 14063.14}, {'field': 'total_amount', 'old_value': 10900.24, 'new_value': 15606.1}, {'field': 'order_count', 'old_value': 455, 'new_value': 643}]
2025-05-15 00:00:39,571 - INFO - 日期 2025-05 处理完成 - 更新: 21 条，插入: 0 条，错误: 0 条
2025-05-15 00:00:39,571 - INFO - 数据同步完成！更新: 21 条，插入: 0 条，错误: 0 条
2025-05-15 00:00:39,571 - INFO - =================同步完成====================
2025-05-15 03:00:03,446 - INFO - =================使用默认全量同步=============
2025-05-15 03:00:04,807 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 03:00:04,807 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 03:00:04,838 - INFO - 开始处理日期: 2025-01
2025-05-15 03:00:04,838 - INFO - Request Parameters - Page 1:
2025-05-15 03:00:04,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:04,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:05,901 - INFO - Response - Page 1:
2025-05-15 03:00:06,104 - INFO - 第 1 页获取到 100 条记录
2025-05-15 03:00:06,104 - INFO - Request Parameters - Page 2:
2025-05-15 03:00:06,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:06,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:06,839 - INFO - Response - Page 2:
2025-05-15 03:00:07,043 - INFO - 第 2 页获取到 100 条记录
2025-05-15 03:00:07,043 - INFO - Request Parameters - Page 3:
2025-05-15 03:00:07,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:07,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:07,574 - INFO - Response - Page 3:
2025-05-15 03:00:07,777 - INFO - 第 3 页获取到 100 条记录
2025-05-15 03:00:07,777 - INFO - Request Parameters - Page 4:
2025-05-15 03:00:07,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:07,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:08,262 - INFO - Response - Page 4:
2025-05-15 03:00:08,465 - INFO - 第 4 页获取到 100 条记录
2025-05-15 03:00:08,465 - INFO - Request Parameters - Page 5:
2025-05-15 03:00:08,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:08,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:08,997 - INFO - Response - Page 5:
2025-05-15 03:00:09,200 - INFO - 第 5 页获取到 100 条记录
2025-05-15 03:00:09,200 - INFO - Request Parameters - Page 6:
2025-05-15 03:00:09,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:09,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:09,669 - INFO - Response - Page 6:
2025-05-15 03:00:09,873 - INFO - 第 6 页获取到 100 条记录
2025-05-15 03:00:09,873 - INFO - Request Parameters - Page 7:
2025-05-15 03:00:09,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:09,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:10,357 - INFO - Response - Page 7:
2025-05-15 03:00:10,561 - INFO - 第 7 页获取到 82 条记录
2025-05-15 03:00:10,561 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 03:00:10,561 - INFO - 获取到 682 条表单数据
2025-05-15 03:00:10,576 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 03:00:10,576 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 03:00:10,576 - INFO - 开始处理日期: 2025-02
2025-05-15 03:00:10,576 - INFO - Request Parameters - Page 1:
2025-05-15 03:00:10,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:10,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:11,108 - INFO - Response - Page 1:
2025-05-15 03:00:11,311 - INFO - 第 1 页获取到 100 条记录
2025-05-15 03:00:11,311 - INFO - Request Parameters - Page 2:
2025-05-15 03:00:11,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:11,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:11,812 - INFO - Response - Page 2:
2025-05-15 03:00:12,015 - INFO - 第 2 页获取到 100 条记录
2025-05-15 03:00:12,015 - INFO - Request Parameters - Page 3:
2025-05-15 03:00:12,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:12,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:12,500 - INFO - Response - Page 3:
2025-05-15 03:00:12,703 - INFO - 第 3 页获取到 100 条记录
2025-05-15 03:00:12,703 - INFO - Request Parameters - Page 4:
2025-05-15 03:00:12,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:12,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:13,188 - INFO - Response - Page 4:
2025-05-15 03:00:13,391 - INFO - 第 4 页获取到 100 条记录
2025-05-15 03:00:13,391 - INFO - Request Parameters - Page 5:
2025-05-15 03:00:13,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:13,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:13,860 - INFO - Response - Page 5:
2025-05-15 03:00:14,063 - INFO - 第 5 页获取到 100 条记录
2025-05-15 03:00:14,063 - INFO - Request Parameters - Page 6:
2025-05-15 03:00:14,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:14,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:14,564 - INFO - Response - Page 6:
2025-05-15 03:00:14,767 - INFO - 第 6 页获取到 100 条记录
2025-05-15 03:00:14,767 - INFO - Request Parameters - Page 7:
2025-05-15 03:00:14,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:14,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:15,205 - INFO - Response - Page 7:
2025-05-15 03:00:15,408 - INFO - 第 7 页获取到 70 条记录
2025-05-15 03:00:15,408 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 03:00:15,408 - INFO - 获取到 670 条表单数据
2025-05-15 03:00:15,424 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 03:00:15,424 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 03:00:15,424 - INFO - 开始处理日期: 2025-03
2025-05-15 03:00:15,424 - INFO - Request Parameters - Page 1:
2025-05-15 03:00:15,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:15,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:15,939 - INFO - Response - Page 1:
2025-05-15 03:00:16,143 - INFO - 第 1 页获取到 100 条记录
2025-05-15 03:00:16,143 - INFO - Request Parameters - Page 2:
2025-05-15 03:00:16,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:16,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:16,659 - INFO - Response - Page 2:
2025-05-15 03:00:16,862 - INFO - 第 2 页获取到 100 条记录
2025-05-15 03:00:16,862 - INFO - Request Parameters - Page 3:
2025-05-15 03:00:16,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:16,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:17,331 - INFO - Response - Page 3:
2025-05-15 03:00:17,550 - INFO - 第 3 页获取到 100 条记录
2025-05-15 03:00:17,550 - INFO - Request Parameters - Page 4:
2025-05-15 03:00:17,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:17,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:18,066 - INFO - Response - Page 4:
2025-05-15 03:00:18,269 - INFO - 第 4 页获取到 100 条记录
2025-05-15 03:00:18,269 - INFO - Request Parameters - Page 5:
2025-05-15 03:00:18,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:18,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:18,785 - INFO - Response - Page 5:
2025-05-15 03:00:18,989 - INFO - 第 5 页获取到 100 条记录
2025-05-15 03:00:18,989 - INFO - Request Parameters - Page 6:
2025-05-15 03:00:18,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:18,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:19,567 - INFO - Response - Page 6:
2025-05-15 03:00:19,770 - INFO - 第 6 页获取到 100 条记录
2025-05-15 03:00:19,770 - INFO - Request Parameters - Page 7:
2025-05-15 03:00:19,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:19,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:20,177 - INFO - Response - Page 7:
2025-05-15 03:00:20,380 - INFO - 第 7 页获取到 61 条记录
2025-05-15 03:00:20,380 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 03:00:20,380 - INFO - 获取到 661 条表单数据
2025-05-15 03:00:20,380 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 03:00:20,396 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 03:00:20,396 - INFO - 开始处理日期: 2025-04
2025-05-15 03:00:20,396 - INFO - Request Parameters - Page 1:
2025-05-15 03:00:20,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:20,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:20,912 - INFO - Response - Page 1:
2025-05-15 03:00:21,115 - INFO - 第 1 页获取到 100 条记录
2025-05-15 03:00:21,115 - INFO - Request Parameters - Page 2:
2025-05-15 03:00:21,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:21,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:21,553 - INFO - Response - Page 2:
2025-05-15 03:00:21,756 - INFO - 第 2 页获取到 100 条记录
2025-05-15 03:00:21,756 - INFO - Request Parameters - Page 3:
2025-05-15 03:00:21,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:21,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:22,303 - INFO - Response - Page 3:
2025-05-15 03:00:22,507 - INFO - 第 3 页获取到 100 条记录
2025-05-15 03:00:22,507 - INFO - Request Parameters - Page 4:
2025-05-15 03:00:22,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:22,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:22,944 - INFO - Response - Page 4:
2025-05-15 03:00:23,148 - INFO - 第 4 页获取到 100 条记录
2025-05-15 03:00:23,148 - INFO - Request Parameters - Page 5:
2025-05-15 03:00:23,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:23,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:23,617 - INFO - Response - Page 5:
2025-05-15 03:00:23,820 - INFO - 第 5 页获取到 100 条记录
2025-05-15 03:00:23,820 - INFO - Request Parameters - Page 6:
2025-05-15 03:00:23,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:23,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:24,336 - INFO - Response - Page 6:
2025-05-15 03:00:24,539 - INFO - 第 6 页获取到 100 条记录
2025-05-15 03:00:24,539 - INFO - Request Parameters - Page 7:
2025-05-15 03:00:24,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:24,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:24,883 - INFO - Response - Page 7:
2025-05-15 03:00:25,087 - INFO - 第 7 页获取到 54 条记录
2025-05-15 03:00:25,087 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 03:00:25,087 - INFO - 获取到 654 条表单数据
2025-05-15 03:00:25,087 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 03:00:25,102 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 03:00:25,102 - INFO - 开始处理日期: 2025-05
2025-05-15 03:00:25,102 - INFO - Request Parameters - Page 1:
2025-05-15 03:00:25,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:25,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:25,587 - INFO - Response - Page 1:
2025-05-15 03:00:25,790 - INFO - 第 1 页获取到 100 条记录
2025-05-15 03:00:25,790 - INFO - Request Parameters - Page 2:
2025-05-15 03:00:25,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:25,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:26,244 - INFO - Response - Page 2:
2025-05-15 03:00:26,447 - INFO - 第 2 页获取到 100 条记录
2025-05-15 03:00:26,447 - INFO - Request Parameters - Page 3:
2025-05-15 03:00:26,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:26,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:26,947 - INFO - Response - Page 3:
2025-05-15 03:00:27,151 - INFO - 第 3 页获取到 100 条记录
2025-05-15 03:00:27,151 - INFO - Request Parameters - Page 4:
2025-05-15 03:00:27,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:27,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:27,604 - INFO - Response - Page 4:
2025-05-15 03:00:27,807 - INFO - 第 4 页获取到 100 条记录
2025-05-15 03:00:27,807 - INFO - Request Parameters - Page 5:
2025-05-15 03:00:27,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:27,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:28,276 - INFO - Response - Page 5:
2025-05-15 03:00:28,480 - INFO - 第 5 页获取到 100 条记录
2025-05-15 03:00:28,480 - INFO - Request Parameters - Page 6:
2025-05-15 03:00:28,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:28,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:28,964 - INFO - Response - Page 6:
2025-05-15 03:00:29,168 - INFO - 第 6 页获取到 100 条记录
2025-05-15 03:00:29,168 - INFO - Request Parameters - Page 7:
2025-05-15 03:00:29,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 03:00:29,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 03:00:29,543 - INFO - Response - Page 7:
2025-05-15 03:00:29,746 - INFO - 第 7 页获取到 25 条记录
2025-05-15 03:00:29,746 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 03:00:29,746 - INFO - 获取到 625 条表单数据
2025-05-15 03:00:29,746 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 03:00:29,762 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 03:00:29,762 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 03:00:29,762 - INFO - =================同步完成====================
2025-05-15 06:00:03,519 - INFO - =================使用默认全量同步=============
2025-05-15 06:00:04,879 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 06:00:04,879 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 06:00:04,910 - INFO - 开始处理日期: 2025-01
2025-05-15 06:00:04,910 - INFO - Request Parameters - Page 1:
2025-05-15 06:00:04,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:04,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:05,880 - INFO - Response - Page 1:
2025-05-15 06:00:06,083 - INFO - 第 1 页获取到 100 条记录
2025-05-15 06:00:06,083 - INFO - Request Parameters - Page 2:
2025-05-15 06:00:06,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:06,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:06,943 - INFO - Response - Page 2:
2025-05-15 06:00:07,146 - INFO - 第 2 页获取到 100 条记录
2025-05-15 06:00:07,146 - INFO - Request Parameters - Page 3:
2025-05-15 06:00:07,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:07,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:07,584 - INFO - Response - Page 3:
2025-05-15 06:00:07,788 - INFO - 第 3 页获取到 100 条记录
2025-05-15 06:00:07,788 - INFO - Request Parameters - Page 4:
2025-05-15 06:00:07,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:07,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:08,304 - INFO - Response - Page 4:
2025-05-15 06:00:08,507 - INFO - 第 4 页获取到 100 条记录
2025-05-15 06:00:08,507 - INFO - Request Parameters - Page 5:
2025-05-15 06:00:08,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:08,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:09,054 - INFO - Response - Page 5:
2025-05-15 06:00:09,257 - INFO - 第 5 页获取到 100 条记录
2025-05-15 06:00:09,257 - INFO - Request Parameters - Page 6:
2025-05-15 06:00:09,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:09,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:09,773 - INFO - Response - Page 6:
2025-05-15 06:00:09,977 - INFO - 第 6 页获取到 100 条记录
2025-05-15 06:00:09,977 - INFO - Request Parameters - Page 7:
2025-05-15 06:00:09,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:09,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:10,430 - INFO - Response - Page 7:
2025-05-15 06:00:10,633 - INFO - 第 7 页获取到 82 条记录
2025-05-15 06:00:10,633 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 06:00:10,633 - INFO - 获取到 682 条表单数据
2025-05-15 06:00:10,633 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 06:00:10,649 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 06:00:10,649 - INFO - 开始处理日期: 2025-02
2025-05-15 06:00:10,649 - INFO - Request Parameters - Page 1:
2025-05-15 06:00:10,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:10,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:11,102 - INFO - Response - Page 1:
2025-05-15 06:00:11,306 - INFO - 第 1 页获取到 100 条记录
2025-05-15 06:00:11,306 - INFO - Request Parameters - Page 2:
2025-05-15 06:00:11,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:11,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:11,775 - INFO - Response - Page 2:
2025-05-15 06:00:11,978 - INFO - 第 2 页获取到 100 条记录
2025-05-15 06:00:11,978 - INFO - Request Parameters - Page 3:
2025-05-15 06:00:11,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:11,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:12,478 - INFO - Response - Page 3:
2025-05-15 06:00:12,682 - INFO - 第 3 页获取到 100 条记录
2025-05-15 06:00:12,682 - INFO - Request Parameters - Page 4:
2025-05-15 06:00:12,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:12,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:13,261 - INFO - Response - Page 4:
2025-05-15 06:00:13,464 - INFO - 第 4 页获取到 100 条记录
2025-05-15 06:00:13,464 - INFO - Request Parameters - Page 5:
2025-05-15 06:00:13,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:13,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:14,027 - INFO - Response - Page 5:
2025-05-15 06:00:14,230 - INFO - 第 5 页获取到 100 条记录
2025-05-15 06:00:14,230 - INFO - Request Parameters - Page 6:
2025-05-15 06:00:14,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:14,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:14,715 - INFO - Response - Page 6:
2025-05-15 06:00:14,918 - INFO - 第 6 页获取到 100 条记录
2025-05-15 06:00:14,918 - INFO - Request Parameters - Page 7:
2025-05-15 06:00:14,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:14,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:15,403 - INFO - Response - Page 7:
2025-05-15 06:00:15,606 - INFO - 第 7 页获取到 70 条记录
2025-05-15 06:00:15,606 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 06:00:15,606 - INFO - 获取到 670 条表单数据
2025-05-15 06:00:15,606 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 06:00:15,622 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 06:00:15,622 - INFO - 开始处理日期: 2025-03
2025-05-15 06:00:15,622 - INFO - Request Parameters - Page 1:
2025-05-15 06:00:15,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:15,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:16,153 - INFO - Response - Page 1:
2025-05-15 06:00:16,357 - INFO - 第 1 页获取到 100 条记录
2025-05-15 06:00:16,357 - INFO - Request Parameters - Page 2:
2025-05-15 06:00:16,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:16,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:16,857 - INFO - Response - Page 2:
2025-05-15 06:00:17,060 - INFO - 第 2 页获取到 100 条记录
2025-05-15 06:00:17,060 - INFO - Request Parameters - Page 3:
2025-05-15 06:00:17,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:17,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:17,498 - INFO - Response - Page 3:
2025-05-15 06:00:17,701 - INFO - 第 3 页获取到 100 条记录
2025-05-15 06:00:17,701 - INFO - Request Parameters - Page 4:
2025-05-15 06:00:17,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:17,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:18,171 - INFO - Response - Page 4:
2025-05-15 06:00:18,374 - INFO - 第 4 页获取到 100 条记录
2025-05-15 06:00:18,374 - INFO - Request Parameters - Page 5:
2025-05-15 06:00:18,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:18,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:18,843 - INFO - Response - Page 5:
2025-05-15 06:00:19,046 - INFO - 第 5 页获取到 100 条记录
2025-05-15 06:00:19,046 - INFO - Request Parameters - Page 6:
2025-05-15 06:00:19,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:19,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:19,531 - INFO - Response - Page 6:
2025-05-15 06:00:19,734 - INFO - 第 6 页获取到 100 条记录
2025-05-15 06:00:19,734 - INFO - Request Parameters - Page 7:
2025-05-15 06:00:19,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:19,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:20,219 - INFO - Response - Page 7:
2025-05-15 06:00:20,422 - INFO - 第 7 页获取到 61 条记录
2025-05-15 06:00:20,422 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 06:00:20,422 - INFO - 获取到 661 条表单数据
2025-05-15 06:00:20,422 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 06:00:20,438 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 06:00:20,453 - INFO - 开始处理日期: 2025-04
2025-05-15 06:00:20,453 - INFO - Request Parameters - Page 1:
2025-05-15 06:00:20,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:20,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:20,985 - INFO - Response - Page 1:
2025-05-15 06:00:21,188 - INFO - 第 1 页获取到 100 条记录
2025-05-15 06:00:21,188 - INFO - Request Parameters - Page 2:
2025-05-15 06:00:21,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:21,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:21,704 - INFO - Response - Page 2:
2025-05-15 06:00:21,908 - INFO - 第 2 页获取到 100 条记录
2025-05-15 06:00:21,908 - INFO - Request Parameters - Page 3:
2025-05-15 06:00:21,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:21,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:22,424 - INFO - Response - Page 3:
2025-05-15 06:00:22,627 - INFO - 第 3 页获取到 100 条记录
2025-05-15 06:00:22,627 - INFO - Request Parameters - Page 4:
2025-05-15 06:00:22,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:22,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:23,065 - INFO - Response - Page 4:
2025-05-15 06:00:23,268 - INFO - 第 4 页获取到 100 条记录
2025-05-15 06:00:23,268 - INFO - Request Parameters - Page 5:
2025-05-15 06:00:23,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:23,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:24,003 - INFO - Response - Page 5:
2025-05-15 06:00:24,206 - INFO - 第 5 页获取到 100 条记录
2025-05-15 06:00:24,206 - INFO - Request Parameters - Page 6:
2025-05-15 06:00:24,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:24,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:24,706 - INFO - Response - Page 6:
2025-05-15 06:00:24,910 - INFO - 第 6 页获取到 100 条记录
2025-05-15 06:00:24,910 - INFO - Request Parameters - Page 7:
2025-05-15 06:00:24,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:24,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:25,348 - INFO - Response - Page 7:
2025-05-15 06:00:25,551 - INFO - 第 7 页获取到 54 条记录
2025-05-15 06:00:25,551 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 06:00:25,551 - INFO - 获取到 654 条表单数据
2025-05-15 06:00:25,551 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 06:00:25,566 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 06:00:25,566 - INFO - 开始处理日期: 2025-05
2025-05-15 06:00:25,566 - INFO - Request Parameters - Page 1:
2025-05-15 06:00:25,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:25,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:26,114 - INFO - Response - Page 1:
2025-05-15 06:00:26,317 - INFO - 第 1 页获取到 100 条记录
2025-05-15 06:00:26,317 - INFO - Request Parameters - Page 2:
2025-05-15 06:00:26,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:26,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:26,833 - INFO - Response - Page 2:
2025-05-15 06:00:27,036 - INFO - 第 2 页获取到 100 条记录
2025-05-15 06:00:27,036 - INFO - Request Parameters - Page 3:
2025-05-15 06:00:27,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:27,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:27,521 - INFO - Response - Page 3:
2025-05-15 06:00:27,724 - INFO - 第 3 页获取到 100 条记录
2025-05-15 06:00:27,724 - INFO - Request Parameters - Page 4:
2025-05-15 06:00:27,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:27,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:28,178 - INFO - Response - Page 4:
2025-05-15 06:00:28,381 - INFO - 第 4 页获取到 100 条记录
2025-05-15 06:00:28,381 - INFO - Request Parameters - Page 5:
2025-05-15 06:00:28,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:28,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:28,913 - INFO - Response - Page 5:
2025-05-15 06:00:29,116 - INFO - 第 5 页获取到 100 条记录
2025-05-15 06:00:29,116 - INFO - Request Parameters - Page 6:
2025-05-15 06:00:29,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:29,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:29,554 - INFO - Response - Page 6:
2025-05-15 06:00:29,757 - INFO - 第 6 页获取到 100 条记录
2025-05-15 06:00:29,757 - INFO - Request Parameters - Page 7:
2025-05-15 06:00:29,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 06:00:29,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 06:00:30,117 - INFO - Response - Page 7:
2025-05-15 06:00:30,320 - INFO - 第 7 页获取到 25 条记录
2025-05-15 06:00:30,320 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 06:00:30,320 - INFO - 获取到 625 条表单数据
2025-05-15 06:00:30,320 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 06:00:30,335 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-15 06:00:30,773 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-15 06:00:30,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45938.0, 'new_value': 47945.0}, {'field': 'total_amount', 'old_value': 47788.0, 'new_value': 49795.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 290}]
2025-05-15 06:00:30,789 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-15 06:00:30,789 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-15 06:00:30,789 - INFO - =================同步完成====================
2025-05-15 09:00:01,868 - INFO - =================使用默认全量同步=============
2025-05-15 09:00:03,227 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 09:00:03,227 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 09:00:03,259 - INFO - 开始处理日期: 2025-01
2025-05-15 09:00:03,259 - INFO - Request Parameters - Page 1:
2025-05-15 09:00:03,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:03,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:04,212 - INFO - Response - Page 1:
2025-05-15 09:00:04,415 - INFO - 第 1 页获取到 100 条记录
2025-05-15 09:00:04,415 - INFO - Request Parameters - Page 2:
2025-05-15 09:00:04,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:04,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:05,352 - INFO - Response - Page 2:
2025-05-15 09:00:05,555 - INFO - 第 2 页获取到 100 条记录
2025-05-15 09:00:05,555 - INFO - Request Parameters - Page 3:
2025-05-15 09:00:05,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:05,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:06,040 - INFO - Response - Page 3:
2025-05-15 09:00:06,243 - INFO - 第 3 页获取到 100 条记录
2025-05-15 09:00:06,243 - INFO - Request Parameters - Page 4:
2025-05-15 09:00:06,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:06,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:06,759 - INFO - Response - Page 4:
2025-05-15 09:00:06,962 - INFO - 第 4 页获取到 100 条记录
2025-05-15 09:00:06,962 - INFO - Request Parameters - Page 5:
2025-05-15 09:00:06,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:06,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:07,430 - INFO - Response - Page 5:
2025-05-15 09:00:07,633 - INFO - 第 5 页获取到 100 条记录
2025-05-15 09:00:07,633 - INFO - Request Parameters - Page 6:
2025-05-15 09:00:07,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:07,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:08,118 - INFO - Response - Page 6:
2025-05-15 09:00:08,321 - INFO - 第 6 页获取到 100 条记录
2025-05-15 09:00:08,321 - INFO - Request Parameters - Page 7:
2025-05-15 09:00:08,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:08,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:08,774 - INFO - Response - Page 7:
2025-05-15 09:00:08,977 - INFO - 第 7 页获取到 82 条记录
2025-05-15 09:00:08,977 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 09:00:08,977 - INFO - 获取到 682 条表单数据
2025-05-15 09:00:08,977 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 09:00:08,993 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 09:00:08,993 - INFO - 开始处理日期: 2025-02
2025-05-15 09:00:08,993 - INFO - Request Parameters - Page 1:
2025-05-15 09:00:08,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:08,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:09,540 - INFO - Response - Page 1:
2025-05-15 09:00:09,743 - INFO - 第 1 页获取到 100 条记录
2025-05-15 09:00:09,743 - INFO - Request Parameters - Page 2:
2025-05-15 09:00:09,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:09,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:10,180 - INFO - Response - Page 2:
2025-05-15 09:00:10,383 - INFO - 第 2 页获取到 100 条记录
2025-05-15 09:00:10,383 - INFO - Request Parameters - Page 3:
2025-05-15 09:00:10,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:10,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:10,868 - INFO - Response - Page 3:
2025-05-15 09:00:11,071 - INFO - 第 3 页获取到 100 条记录
2025-05-15 09:00:11,071 - INFO - Request Parameters - Page 4:
2025-05-15 09:00:11,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:11,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:11,618 - INFO - Response - Page 4:
2025-05-15 09:00:11,821 - INFO - 第 4 页获取到 100 条记录
2025-05-15 09:00:11,821 - INFO - Request Parameters - Page 5:
2025-05-15 09:00:11,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:11,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:12,274 - INFO - Response - Page 5:
2025-05-15 09:00:12,477 - INFO - 第 5 页获取到 100 条记录
2025-05-15 09:00:12,477 - INFO - Request Parameters - Page 6:
2025-05-15 09:00:12,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:12,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:12,946 - INFO - Response - Page 6:
2025-05-15 09:00:13,149 - INFO - 第 6 页获取到 100 条记录
2025-05-15 09:00:13,149 - INFO - Request Parameters - Page 7:
2025-05-15 09:00:13,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:13,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:13,602 - INFO - Response - Page 7:
2025-05-15 09:00:13,805 - INFO - 第 7 页获取到 70 条记录
2025-05-15 09:00:13,805 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 09:00:13,805 - INFO - 获取到 670 条表单数据
2025-05-15 09:00:13,805 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 09:00:13,821 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 09:00:13,821 - INFO - 开始处理日期: 2025-03
2025-05-15 09:00:13,821 - INFO - Request Parameters - Page 1:
2025-05-15 09:00:13,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:13,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:14,258 - INFO - Response - Page 1:
2025-05-15 09:00:14,462 - INFO - 第 1 页获取到 100 条记录
2025-05-15 09:00:14,462 - INFO - Request Parameters - Page 2:
2025-05-15 09:00:14,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:14,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:14,993 - INFO - Response - Page 2:
2025-05-15 09:00:15,196 - INFO - 第 2 页获取到 100 条记录
2025-05-15 09:00:15,196 - INFO - Request Parameters - Page 3:
2025-05-15 09:00:15,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:15,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:15,633 - INFO - Response - Page 3:
2025-05-15 09:00:15,837 - INFO - 第 3 页获取到 100 条记录
2025-05-15 09:00:15,837 - INFO - Request Parameters - Page 4:
2025-05-15 09:00:15,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:15,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:16,321 - INFO - Response - Page 4:
2025-05-15 09:00:16,524 - INFO - 第 4 页获取到 100 条记录
2025-05-15 09:00:16,524 - INFO - Request Parameters - Page 5:
2025-05-15 09:00:16,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:16,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:17,008 - INFO - Response - Page 5:
2025-05-15 09:00:17,212 - INFO - 第 5 页获取到 100 条记录
2025-05-15 09:00:17,212 - INFO - Request Parameters - Page 6:
2025-05-15 09:00:17,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:17,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:17,696 - INFO - Response - Page 6:
2025-05-15 09:00:17,899 - INFO - 第 6 页获取到 100 条记录
2025-05-15 09:00:17,899 - INFO - Request Parameters - Page 7:
2025-05-15 09:00:17,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:17,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:18,368 - INFO - Response - Page 7:
2025-05-15 09:00:18,571 - INFO - 第 7 页获取到 61 条记录
2025-05-15 09:00:18,571 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 09:00:18,571 - INFO - 获取到 661 条表单数据
2025-05-15 09:00:18,587 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 09:00:18,602 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 09:00:18,618 - INFO - 开始处理日期: 2025-04
2025-05-15 09:00:18,618 - INFO - Request Parameters - Page 1:
2025-05-15 09:00:18,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:18,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:19,180 - INFO - Response - Page 1:
2025-05-15 09:00:19,383 - INFO - 第 1 页获取到 100 条记录
2025-05-15 09:00:19,383 - INFO - Request Parameters - Page 2:
2025-05-15 09:00:19,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:19,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:19,977 - INFO - Response - Page 2:
2025-05-15 09:00:20,180 - INFO - 第 2 页获取到 100 条记录
2025-05-15 09:00:20,180 - INFO - Request Parameters - Page 3:
2025-05-15 09:00:20,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:20,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:20,743 - INFO - Response - Page 3:
2025-05-15 09:00:20,946 - INFO - 第 3 页获取到 100 条记录
2025-05-15 09:00:20,946 - INFO - Request Parameters - Page 4:
2025-05-15 09:00:20,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:20,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:21,430 - INFO - Response - Page 4:
2025-05-15 09:00:21,633 - INFO - 第 4 页获取到 100 条记录
2025-05-15 09:00:21,633 - INFO - Request Parameters - Page 5:
2025-05-15 09:00:21,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:21,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:22,133 - INFO - Response - Page 5:
2025-05-15 09:00:22,337 - INFO - 第 5 页获取到 100 条记录
2025-05-15 09:00:22,337 - INFO - Request Parameters - Page 6:
2025-05-15 09:00:22,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:22,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:22,790 - INFO - Response - Page 6:
2025-05-15 09:00:22,993 - INFO - 第 6 页获取到 100 条记录
2025-05-15 09:00:22,993 - INFO - Request Parameters - Page 7:
2025-05-15 09:00:22,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:22,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:23,415 - INFO - Response - Page 7:
2025-05-15 09:00:23,618 - INFO - 第 7 页获取到 54 条记录
2025-05-15 09:00:23,618 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 09:00:23,618 - INFO - 获取到 654 条表单数据
2025-05-15 09:00:23,618 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 09:00:23,633 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 09:00:23,633 - INFO - 开始处理日期: 2025-05
2025-05-15 09:00:23,633 - INFO - Request Parameters - Page 1:
2025-05-15 09:00:23,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:23,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:24,133 - INFO - Response - Page 1:
2025-05-15 09:00:24,337 - INFO - 第 1 页获取到 100 条记录
2025-05-15 09:00:24,337 - INFO - Request Parameters - Page 2:
2025-05-15 09:00:24,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:24,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:24,868 - INFO - Response - Page 2:
2025-05-15 09:00:25,071 - INFO - 第 2 页获取到 100 条记录
2025-05-15 09:00:25,071 - INFO - Request Parameters - Page 3:
2025-05-15 09:00:25,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:25,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:25,524 - INFO - Response - Page 3:
2025-05-15 09:00:25,727 - INFO - 第 3 页获取到 100 条记录
2025-05-15 09:00:25,727 - INFO - Request Parameters - Page 4:
2025-05-15 09:00:25,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:25,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:26,196 - INFO - Response - Page 4:
2025-05-15 09:00:26,399 - INFO - 第 4 页获取到 100 条记录
2025-05-15 09:00:26,399 - INFO - Request Parameters - Page 5:
2025-05-15 09:00:26,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:26,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:26,899 - INFO - Response - Page 5:
2025-05-15 09:00:27,102 - INFO - 第 5 页获取到 100 条记录
2025-05-15 09:00:27,102 - INFO - Request Parameters - Page 6:
2025-05-15 09:00:27,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:27,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:27,571 - INFO - Response - Page 6:
2025-05-15 09:00:27,774 - INFO - 第 6 页获取到 100 条记录
2025-05-15 09:00:27,774 - INFO - Request Parameters - Page 7:
2025-05-15 09:00:27,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 09:00:27,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 09:00:28,118 - INFO - Response - Page 7:
2025-05-15 09:00:28,321 - INFO - 第 7 页获取到 25 条记录
2025-05-15 09:00:28,321 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 09:00:28,321 - INFO - 获取到 625 条表单数据
2025-05-15 09:00:28,321 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 09:00:28,321 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-15 09:00:28,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-15 09:00:28,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5600000.0, 'new_value': 5800000.0}, {'field': 'total_amount', 'old_value': 5700000.0, 'new_value': 5900000.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-15 09:00:28,821 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-15 09:00:29,274 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-15 09:00:29,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91149.17, 'new_value': 100245.47}, {'field': 'total_amount', 'old_value': 91149.17, 'new_value': 100245.47}, {'field': 'order_count', 'old_value': 3789, 'new_value': 4153}]
2025-05-15 09:00:29,274 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-15 09:00:29,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-15 09:00:29,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 429073.6, 'new_value': 454999.1}, {'field': 'total_amount', 'old_value': 430331.0, 'new_value': 456256.5}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-05-15 09:00:29,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-15 09:00:30,133 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-15 09:00:30,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4128.32, 'new_value': 4496.32}, {'field': 'offline_amount', 'old_value': 36498.0, 'new_value': 39875.0}, {'field': 'total_amount', 'old_value': 40626.32, 'new_value': 44371.32}, {'field': 'order_count', 'old_value': 430, 'new_value': 434}]
2025-05-15 09:00:30,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-15 09:00:30,540 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-15 09:00:30,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25438.24, 'new_value': 27029.14}, {'field': 'offline_amount', 'old_value': 64122.08, 'new_value': 66960.08}, {'field': 'total_amount', 'old_value': 89560.32, 'new_value': 93989.22}, {'field': 'order_count', 'old_value': 3017, 'new_value': 3195}]
2025-05-15 09:00:30,540 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-15 09:00:31,024 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-15 09:00:31,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212084.0, 'new_value': 219223.0}, {'field': 'total_amount', 'old_value': 212084.0, 'new_value': 219223.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 101}]
2025-05-15 09:00:31,024 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-15 09:00:31,524 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-15 09:00:31,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17426.65, 'new_value': 17599.55}, {'field': 'total_amount', 'old_value': 17492.2, 'new_value': 17665.1}, {'field': 'order_count', 'old_value': 173, 'new_value': 174}]
2025-05-15 09:00:31,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-15 09:00:31,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-15 09:00:31,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94825.0, 'new_value': 102984.0}, {'field': 'total_amount', 'old_value': 124982.0, 'new_value': 133141.0}, {'field': 'order_count', 'old_value': 2611, 'new_value': 2818}]
2025-05-15 09:00:31,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-15 09:00:32,461 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-15 09:00:32,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7698.03, 'new_value': 8296.78}, {'field': 'offline_amount', 'old_value': 143663.7, 'new_value': 150709.1}, {'field': 'total_amount', 'old_value': 151361.73, 'new_value': 159005.88}, {'field': 'order_count', 'old_value': 1053, 'new_value': 1117}]
2025-05-15 09:00:32,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-15 09:00:32,883 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-15 09:00:32,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75605.0, 'new_value': 85204.0}, {'field': 'total_amount', 'old_value': 99227.48, 'new_value': 108826.48}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-15 09:00:32,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-15 09:00:33,321 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-15 09:00:33,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64797.44, 'new_value': 70894.18}, {'field': 'offline_amount', 'old_value': 231378.28, 'new_value': 245636.98}, {'field': 'total_amount', 'old_value': 296175.72, 'new_value': 316531.16}, {'field': 'order_count', 'old_value': 1660, 'new_value': 1843}]
2025-05-15 09:00:33,321 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-15 09:00:33,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-15 09:00:33,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 442326.0, 'new_value': 485663.0}, {'field': 'total_amount', 'old_value': 442326.0, 'new_value': 485663.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 87}]
2025-05-15 09:00:33,821 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-15 09:00:34,274 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-15 09:00:34,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123292.46, 'new_value': 129527.15}, {'field': 'offline_amount', 'old_value': 72814.38, 'new_value': 74595.28}, {'field': 'total_amount', 'old_value': 196106.84, 'new_value': 204122.43}, {'field': 'order_count', 'old_value': 747, 'new_value': 774}]
2025-05-15 09:00:34,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-15 09:00:34,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-15 09:00:34,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12051.83, 'new_value': 12384.01}, {'field': 'offline_amount', 'old_value': 160981.65, 'new_value': 166617.35}, {'field': 'total_amount', 'old_value': 173033.48, 'new_value': 179001.36}, {'field': 'order_count', 'old_value': 813, 'new_value': 841}]
2025-05-15 09:00:34,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-15 09:00:35,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-15 09:00:35,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19066.62, 'new_value': 20027.26}, {'field': 'offline_amount', 'old_value': 320092.36, 'new_value': 336272.86}, {'field': 'total_amount', 'old_value': 339158.98, 'new_value': 356300.12}, {'field': 'order_count', 'old_value': 1890, 'new_value': 1965}]
2025-05-15 09:00:35,133 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-15 09:00:35,555 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-15 09:00:35,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192029.0, 'new_value': 199359.0}, {'field': 'total_amount', 'old_value': 192029.0, 'new_value': 199359.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 167}]
2025-05-15 09:00:35,555 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-15 09:00:35,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-15 09:00:35,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189051.6, 'new_value': 191857.6}, {'field': 'total_amount', 'old_value': 189051.6, 'new_value': 191857.6}, {'field': 'order_count', 'old_value': 2133, 'new_value': 2164}]
2025-05-15 09:00:35,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-15 09:00:36,399 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-15 09:00:36,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159256.3, 'new_value': 166567.9}, {'field': 'total_amount', 'old_value': 159256.3, 'new_value': 166567.9}, {'field': 'order_count', 'old_value': 258, 'new_value': 274}]
2025-05-15 09:00:36,399 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-15 09:00:36,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-15 09:00:36,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42599.48, 'new_value': 46630.55}, {'field': 'offline_amount', 'old_value': 315311.84, 'new_value': 325311.84}, {'field': 'total_amount', 'old_value': 357911.32, 'new_value': 371942.39}, {'field': 'order_count', 'old_value': 1505, 'new_value': 1635}]
2025-05-15 09:00:36,821 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-15 09:00:37,243 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-15 09:00:37,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124446.0, 'new_value': 128632.0}, {'field': 'total_amount', 'old_value': 124446.0, 'new_value': 128632.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-05-15 09:00:37,243 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-15 09:00:37,665 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-15 09:00:37,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61543.07, 'new_value': 64635.07}, {'field': 'total_amount', 'old_value': 67154.59, 'new_value': 70246.59}, {'field': 'order_count', 'old_value': 5871, 'new_value': 6258}]
2025-05-15 09:00:37,665 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-15 09:00:38,211 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-15 09:00:38,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49666.0, 'new_value': 50592.0}, {'field': 'total_amount', 'old_value': 49666.0, 'new_value': 50592.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 63}]
2025-05-15 09:00:38,211 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-15 09:00:38,633 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-15 09:00:38,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31391.0, 'new_value': 33334.0}, {'field': 'total_amount', 'old_value': 31391.0, 'new_value': 33334.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 227}]
2025-05-15 09:00:38,633 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-15 09:00:39,165 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-15 09:00:39,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233446.69, 'new_value': 240924.42}, {'field': 'total_amount', 'old_value': 233446.69, 'new_value': 240924.42}, {'field': 'order_count', 'old_value': 1084, 'new_value': 1129}]
2025-05-15 09:00:39,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-15 09:00:39,618 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-15 09:00:39,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41316.35, 'new_value': 43894.49}, {'field': 'offline_amount', 'old_value': 144760.94, 'new_value': 150029.46}, {'field': 'total_amount', 'old_value': 186077.29, 'new_value': 193923.95}, {'field': 'order_count', 'old_value': 2588, 'new_value': 2644}]
2025-05-15 09:00:39,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-15 09:00:40,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-15 09:00:40,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44315.2, 'new_value': 45340.2}, {'field': 'total_amount', 'old_value': 44315.2, 'new_value': 45340.2}, {'field': 'order_count', 'old_value': 119, 'new_value': 123}]
2025-05-15 09:00:40,227 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-15 09:00:40,633 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-15 09:00:40,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 444256.7, 'new_value': 469246.7}, {'field': 'total_amount', 'old_value': 444256.7, 'new_value': 469246.7}, {'field': 'order_count', 'old_value': 1161, 'new_value': 1206}]
2025-05-15 09:00:40,633 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-15 09:00:41,165 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-15 09:00:41,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373529.0, 'new_value': 386083.0}, {'field': 'total_amount', 'old_value': 373529.0, 'new_value': 386083.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 54}]
2025-05-15 09:00:41,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-15 09:00:41,571 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-15 09:00:41,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12671.0, 'new_value': 17471.0}, {'field': 'total_amount', 'old_value': 12671.0, 'new_value': 17471.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-05-15 09:00:41,571 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-15 09:00:42,133 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-15 09:00:42,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9999.2, 'new_value': 10103.6}, {'field': 'offline_amount', 'old_value': 32554.2, 'new_value': 36839.1}, {'field': 'total_amount', 'old_value': 42553.4, 'new_value': 46942.7}, {'field': 'order_count', 'old_value': 102, 'new_value': 109}]
2025-05-15 09:00:42,133 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-15 09:00:42,571 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-15 09:00:42,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374426.0, 'new_value': 385435.0}, {'field': 'total_amount', 'old_value': 374866.0, 'new_value': 385875.0}, {'field': 'order_count', 'old_value': 158, 'new_value': 167}]
2025-05-15 09:00:42,571 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-15 09:00:43,071 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-15 09:00:43,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111181.0, 'new_value': 119918.0}, {'field': 'total_amount', 'old_value': 111181.0, 'new_value': 119918.0}, {'field': 'order_count', 'old_value': 177, 'new_value': 192}]
2025-05-15 09:00:43,071 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-15 09:00:43,508 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-15 09:00:43,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152331.0, 'new_value': 152855.0}, {'field': 'total_amount', 'old_value': 152331.0, 'new_value': 152855.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-15 09:00:43,508 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-15 09:00:43,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-15 09:00:43,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 404739.92, 'new_value': 432227.86}, {'field': 'total_amount', 'old_value': 404739.92, 'new_value': 432227.86}, {'field': 'order_count', 'old_value': 2933, 'new_value': 3129}]
2025-05-15 09:00:43,930 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-15 09:00:44,415 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-15 09:00:44,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 409104.0, 'new_value': 448522.0}, {'field': 'total_amount', 'old_value': 409104.0, 'new_value': 448522.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 113}]
2025-05-15 09:00:44,415 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-15 09:00:44,883 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-15 09:00:44,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48781.0, 'new_value': 50113.0}, {'field': 'total_amount', 'old_value': 48781.0, 'new_value': 50113.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 129}]
2025-05-15 09:00:44,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-15 09:00:45,336 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-15 09:00:45,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7621.5, 'new_value': 7822.2}, {'field': 'offline_amount', 'old_value': 7000.0, 'new_value': 10500.0}, {'field': 'total_amount', 'old_value': 14621.5, 'new_value': 18322.2}, {'field': 'order_count', 'old_value': 118, 'new_value': 122}]
2025-05-15 09:00:45,336 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-15 09:00:45,868 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-15 09:00:45,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86698.3, 'new_value': 87820.7}, {'field': 'total_amount', 'old_value': 86698.3, 'new_value': 87820.7}, {'field': 'order_count', 'old_value': 160, 'new_value': 162}]
2025-05-15 09:00:45,868 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-15 09:00:46,274 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-15 09:00:46,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63431.0, 'new_value': 65746.0}, {'field': 'offline_amount', 'old_value': 49538.86, 'new_value': 50158.36}, {'field': 'total_amount', 'old_value': 112969.86, 'new_value': 115904.36}, {'field': 'order_count', 'old_value': 737, 'new_value': 761}]
2025-05-15 09:00:46,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-15 09:00:46,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-15 09:00:46,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 544003.0, 'new_value': 564394.0}, {'field': 'total_amount', 'old_value': 544003.0, 'new_value': 564394.0}, {'field': 'order_count', 'old_value': 624, 'new_value': 663}]
2025-05-15 09:00:46,711 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-15 09:00:47,149 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-15 09:00:47,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373026.0, 'new_value': 387623.0}, {'field': 'total_amount', 'old_value': 373026.0, 'new_value': 387623.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-05-15 09:00:47,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-15 09:00:47,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-15 09:00:47,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202645.83, 'new_value': 208198.32}, {'field': 'offline_amount', 'old_value': 710847.77, 'new_value': 740435.55}, {'field': 'total_amount', 'old_value': 913493.6, 'new_value': 948633.87}, {'field': 'order_count', 'old_value': 4576, 'new_value': 4767}]
2025-05-15 09:00:47,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-15 09:00:48,196 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-15 09:00:48,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71103.0, 'new_value': 75488.8}, {'field': 'offline_amount', 'old_value': 59705.7, 'new_value': 63975.9}, {'field': 'total_amount', 'old_value': 130808.7, 'new_value': 139464.7}, {'field': 'order_count', 'old_value': 3082, 'new_value': 3287}]
2025-05-15 09:00:48,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-15 09:00:48,539 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-15 09:00:48,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1430000.0, 'new_value': 1480000.0}, {'field': 'total_amount', 'old_value': 1430000.0, 'new_value': 1480000.0}, {'field': 'order_count', 'old_value': 270, 'new_value': 271}]
2025-05-15 09:00:48,539 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-15 09:00:48,977 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-15 09:00:48,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150285.19, 'new_value': 153521.95}, {'field': 'total_amount', 'old_value': 150285.19, 'new_value': 153521.95}, {'field': 'order_count', 'old_value': 917, 'new_value': 939}]
2025-05-15 09:00:48,977 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-15 09:00:49,461 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-15 09:00:49,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118816.86, 'new_value': 125509.07}, {'field': 'offline_amount', 'old_value': 435912.22, 'new_value': 453358.57}, {'field': 'total_amount', 'old_value': 554729.08, 'new_value': 578867.64}, {'field': 'order_count', 'old_value': 3085, 'new_value': 3250}]
2025-05-15 09:00:49,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-15 09:00:49,883 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-15 09:00:49,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17397.29, 'new_value': 18465.98}, {'field': 'offline_amount', 'old_value': 207295.97, 'new_value': 214785.07}, {'field': 'total_amount', 'old_value': 224693.26, 'new_value': 233251.05}, {'field': 'order_count', 'old_value': 8827, 'new_value': 8885}]
2025-05-15 09:00:49,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-15 09:00:50,321 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-15 09:00:50,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100458.94, 'new_value': 109480.94}, {'field': 'offline_amount', 'old_value': 18013.3, 'new_value': 18557.3}, {'field': 'total_amount', 'old_value': 118472.24, 'new_value': 128038.24}, {'field': 'order_count', 'old_value': 5574, 'new_value': 6170}]
2025-05-15 09:00:50,321 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-15 09:00:50,743 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-15 09:00:50,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 440000.0, 'new_value': 445000.0}, {'field': 'total_amount', 'old_value': 440000.0, 'new_value': 445000.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 138}]
2025-05-15 09:00:50,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-15 09:00:51,243 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-15 09:00:51,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420000.0, 'new_value': 425000.0}, {'field': 'total_amount', 'old_value': 420000.0, 'new_value': 425000.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 137}]
2025-05-15 09:00:51,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-15 09:00:51,696 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-15 09:00:51,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2548674.0, 'new_value': 2598674.0}, {'field': 'total_amount', 'old_value': 2548674.0, 'new_value': 2598674.0}, {'field': 'order_count', 'old_value': 290, 'new_value': 291}]
2025-05-15 09:00:51,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-15 09:00:52,196 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-15 09:00:52,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11673.54, 'new_value': 12636.54}, {'field': 'offline_amount', 'old_value': 9425.4, 'new_value': 9946.4}, {'field': 'total_amount', 'old_value': 21098.94, 'new_value': 22582.94}, {'field': 'order_count', 'old_value': 961, 'new_value': 1024}]
2025-05-15 09:00:52,196 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-15 09:00:52,743 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-15 09:00:52,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1256894.0, 'new_value': 1304821.0}, {'field': 'total_amount', 'old_value': 1256894.0, 'new_value': 1304821.0}, {'field': 'order_count', 'old_value': 4757, 'new_value': 4944}]
2025-05-15 09:00:52,743 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-15 09:00:53,336 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-15 09:00:53,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211314.84, 'new_value': 212125.84}, {'field': 'total_amount', 'old_value': 211314.84, 'new_value': 212125.84}, {'field': 'order_count', 'old_value': 1102, 'new_value': 1106}]
2025-05-15 09:00:53,336 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-15 09:00:53,789 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-15 09:00:53,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25215.72, 'new_value': 27922.04}, {'field': 'offline_amount', 'old_value': 28937.46, 'new_value': 29808.29}, {'field': 'total_amount', 'old_value': 54153.18, 'new_value': 57730.33}, {'field': 'order_count', 'old_value': 4158, 'new_value': 4444}]
2025-05-15 09:00:53,789 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-15 09:00:54,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-15 09:00:54,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233455.4, 'new_value': 235334.3}, {'field': 'total_amount', 'old_value': 233455.4, 'new_value': 235334.3}]
2025-05-15 09:00:54,227 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-15 09:00:54,727 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-15 09:00:54,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 361633.0, 'new_value': 390640.0}, {'field': 'total_amount', 'old_value': 361633.0, 'new_value': 390640.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 51}]
2025-05-15 09:00:54,727 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-15 09:00:55,211 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-15 09:00:55,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 366179.0, 'new_value': 401148.0}, {'field': 'total_amount', 'old_value': 366179.0, 'new_value': 401148.0}, {'field': 'order_count', 'old_value': 230, 'new_value': 248}]
2025-05-15 09:00:55,211 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-15 09:00:55,649 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-15 09:00:55,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148387.35, 'new_value': 162055.82}, {'field': 'offline_amount', 'old_value': 120349.87, 'new_value': 127075.32}, {'field': 'total_amount', 'old_value': 268737.22, 'new_value': 289131.14}, {'field': 'order_count', 'old_value': 10585, 'new_value': 11483}]
2025-05-15 09:00:55,649 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-15 09:00:56,071 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-15 09:00:56,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171436.0, 'new_value': 180521.0}, {'field': 'total_amount', 'old_value': 171436.0, 'new_value': 180521.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 204}]
2025-05-15 09:00:56,071 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-15 09:00:56,524 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-15 09:00:56,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447839.85, 'new_value': 463387.81}, {'field': 'total_amount', 'old_value': 447839.85, 'new_value': 463387.81}, {'field': 'order_count', 'old_value': 2323, 'new_value': 2430}]
2025-05-15 09:00:56,524 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-15 09:00:56,977 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-15 09:00:56,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80360.43, 'new_value': 83577.52}, {'field': 'total_amount', 'old_value': 80360.43, 'new_value': 83577.52}, {'field': 'order_count', 'old_value': 5489, 'new_value': 5717}]
2025-05-15 09:00:56,977 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-15 09:00:57,383 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-15 09:00:57,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321929.0, 'new_value': 325648.0}, {'field': 'total_amount', 'old_value': 321929.0, 'new_value': 325648.0}, {'field': 'order_count', 'old_value': 7288, 'new_value': 7370}]
2025-05-15 09:00:57,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-15 09:00:57,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-15 09:00:57,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57892.0, 'new_value': 60838.0}, {'field': 'total_amount', 'old_value': 57892.0, 'new_value': 60838.0}, {'field': 'order_count', 'old_value': 3987, 'new_value': 4177}]
2025-05-15 09:00:57,805 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-15 09:00:58,258 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-15 09:00:58,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14993.0, 'new_value': 16548.0}, {'field': 'total_amount', 'old_value': 14993.0, 'new_value': 16548.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 56}]
2025-05-15 09:00:58,258 - INFO - 日期 2025-05 处理完成 - 更新: 65 条，插入: 0 条，错误: 0 条
2025-05-15 09:00:58,258 - INFO - 数据同步完成！更新: 65 条，插入: 0 条，错误: 0 条
2025-05-15 09:00:58,258 - INFO - =================同步完成====================
2025-05-15 12:00:01,871 - INFO - =================使用默认全量同步=============
2025-05-15 12:00:03,246 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 12:00:03,246 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 12:00:03,277 - INFO - 开始处理日期: 2025-01
2025-05-15 12:00:03,277 - INFO - Request Parameters - Page 1:
2025-05-15 12:00:03,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:03,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:04,246 - INFO - Response - Page 1:
2025-05-15 12:00:04,449 - INFO - 第 1 页获取到 100 条记录
2025-05-15 12:00:04,449 - INFO - Request Parameters - Page 2:
2025-05-15 12:00:04,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:04,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:04,980 - INFO - Response - Page 2:
2025-05-15 12:00:05,183 - INFO - 第 2 页获取到 100 条记录
2025-05-15 12:00:05,183 - INFO - Request Parameters - Page 3:
2025-05-15 12:00:05,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:05,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:05,699 - INFO - Response - Page 3:
2025-05-15 12:00:05,902 - INFO - 第 3 页获取到 100 条记录
2025-05-15 12:00:05,902 - INFO - Request Parameters - Page 4:
2025-05-15 12:00:05,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:05,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:06,574 - INFO - Response - Page 4:
2025-05-15 12:00:06,777 - INFO - 第 4 页获取到 100 条记录
2025-05-15 12:00:06,777 - INFO - Request Parameters - Page 5:
2025-05-15 12:00:06,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:06,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:07,261 - INFO - Response - Page 5:
2025-05-15 12:00:07,464 - INFO - 第 5 页获取到 100 条记录
2025-05-15 12:00:07,464 - INFO - Request Parameters - Page 6:
2025-05-15 12:00:07,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:07,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:07,949 - INFO - Response - Page 6:
2025-05-15 12:00:08,152 - INFO - 第 6 页获取到 100 条记录
2025-05-15 12:00:08,152 - INFO - Request Parameters - Page 7:
2025-05-15 12:00:08,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:08,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:08,574 - INFO - Response - Page 7:
2025-05-15 12:00:08,777 - INFO - 第 7 页获取到 82 条记录
2025-05-15 12:00:08,777 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 12:00:08,777 - INFO - 获取到 682 条表单数据
2025-05-15 12:00:08,777 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 12:00:08,792 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 12:00:08,792 - INFO - 开始处理日期: 2025-02
2025-05-15 12:00:08,792 - INFO - Request Parameters - Page 1:
2025-05-15 12:00:08,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:08,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:09,324 - INFO - Response - Page 1:
2025-05-15 12:00:09,527 - INFO - 第 1 页获取到 100 条记录
2025-05-15 12:00:09,527 - INFO - Request Parameters - Page 2:
2025-05-15 12:00:09,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:09,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:10,011 - INFO - Response - Page 2:
2025-05-15 12:00:10,214 - INFO - 第 2 页获取到 100 条记录
2025-05-15 12:00:10,214 - INFO - Request Parameters - Page 3:
2025-05-15 12:00:10,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:10,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:10,699 - INFO - Response - Page 3:
2025-05-15 12:00:10,902 - INFO - 第 3 页获取到 100 条记录
2025-05-15 12:00:10,902 - INFO - Request Parameters - Page 4:
2025-05-15 12:00:10,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:10,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:11,355 - INFO - Response - Page 4:
2025-05-15 12:00:11,558 - INFO - 第 4 页获取到 100 条记录
2025-05-15 12:00:11,558 - INFO - Request Parameters - Page 5:
2025-05-15 12:00:11,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:11,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:12,074 - INFO - Response - Page 5:
2025-05-15 12:00:12,277 - INFO - 第 5 页获取到 100 条记录
2025-05-15 12:00:12,277 - INFO - Request Parameters - Page 6:
2025-05-15 12:00:12,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:12,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:12,683 - INFO - Response - Page 6:
2025-05-15 12:00:12,886 - INFO - 第 6 页获取到 100 条记录
2025-05-15 12:00:12,886 - INFO - Request Parameters - Page 7:
2025-05-15 12:00:12,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:12,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:13,324 - INFO - Response - Page 7:
2025-05-15 12:00:13,527 - INFO - 第 7 页获取到 70 条记录
2025-05-15 12:00:13,527 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 12:00:13,527 - INFO - 获取到 670 条表单数据
2025-05-15 12:00:13,527 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 12:00:13,542 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 12:00:13,542 - INFO - 开始处理日期: 2025-03
2025-05-15 12:00:13,542 - INFO - Request Parameters - Page 1:
2025-05-15 12:00:13,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:13,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:14,027 - INFO - Response - Page 1:
2025-05-15 12:00:14,230 - INFO - 第 1 页获取到 100 条记录
2025-05-15 12:00:14,230 - INFO - Request Parameters - Page 2:
2025-05-15 12:00:14,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:14,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:14,745 - INFO - Response - Page 2:
2025-05-15 12:00:14,949 - INFO - 第 2 页获取到 100 条记录
2025-05-15 12:00:14,949 - INFO - Request Parameters - Page 3:
2025-05-15 12:00:14,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:14,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:15,464 - INFO - Response - Page 3:
2025-05-15 12:00:15,667 - INFO - 第 3 页获取到 100 条记录
2025-05-15 12:00:15,667 - INFO - Request Parameters - Page 4:
2025-05-15 12:00:15,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:15,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:16,152 - INFO - Response - Page 4:
2025-05-15 12:00:16,355 - INFO - 第 4 页获取到 100 条记录
2025-05-15 12:00:16,355 - INFO - Request Parameters - Page 5:
2025-05-15 12:00:16,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:16,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:16,870 - INFO - Response - Page 5:
2025-05-15 12:00:17,074 - INFO - 第 5 页获取到 100 条记录
2025-05-15 12:00:17,074 - INFO - Request Parameters - Page 6:
2025-05-15 12:00:17,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:17,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:17,527 - INFO - Response - Page 6:
2025-05-15 12:00:17,730 - INFO - 第 6 页获取到 100 条记录
2025-05-15 12:00:17,730 - INFO - Request Parameters - Page 7:
2025-05-15 12:00:17,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:17,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:18,136 - INFO - Response - Page 7:
2025-05-15 12:00:18,339 - INFO - 第 7 页获取到 61 条记录
2025-05-15 12:00:18,339 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 12:00:18,339 - INFO - 获取到 661 条表单数据
2025-05-15 12:00:18,339 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 12:00:18,355 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 12:00:18,355 - INFO - 开始处理日期: 2025-04
2025-05-15 12:00:18,355 - INFO - Request Parameters - Page 1:
2025-05-15 12:00:18,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:18,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:18,902 - INFO - Response - Page 1:
2025-05-15 12:00:19,105 - INFO - 第 1 页获取到 100 条记录
2025-05-15 12:00:19,105 - INFO - Request Parameters - Page 2:
2025-05-15 12:00:19,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:19,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:19,745 - INFO - Response - Page 2:
2025-05-15 12:00:19,949 - INFO - 第 2 页获取到 100 条记录
2025-05-15 12:00:19,949 - INFO - Request Parameters - Page 3:
2025-05-15 12:00:19,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:19,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:20,417 - INFO - Response - Page 3:
2025-05-15 12:00:20,621 - INFO - 第 3 页获取到 100 条记录
2025-05-15 12:00:20,621 - INFO - Request Parameters - Page 4:
2025-05-15 12:00:20,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:20,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:21,120 - INFO - Response - Page 4:
2025-05-15 12:00:21,339 - INFO - 第 4 页获取到 100 条记录
2025-05-15 12:00:21,339 - INFO - Request Parameters - Page 5:
2025-05-15 12:00:21,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:21,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:21,792 - INFO - Response - Page 5:
2025-05-15 12:00:21,995 - INFO - 第 5 页获取到 100 条记录
2025-05-15 12:00:21,995 - INFO - Request Parameters - Page 6:
2025-05-15 12:00:21,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:21,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:22,464 - INFO - Response - Page 6:
2025-05-15 12:00:22,667 - INFO - 第 6 页获取到 100 条记录
2025-05-15 12:00:22,667 - INFO - Request Parameters - Page 7:
2025-05-15 12:00:22,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:22,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:23,105 - INFO - Response - Page 7:
2025-05-15 12:00:23,308 - INFO - 第 7 页获取到 54 条记录
2025-05-15 12:00:23,308 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 12:00:23,308 - INFO - 获取到 654 条表单数据
2025-05-15 12:00:23,308 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 12:00:23,324 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 12:00:23,324 - INFO - 开始处理日期: 2025-05
2025-05-15 12:00:23,324 - INFO - Request Parameters - Page 1:
2025-05-15 12:00:23,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:23,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:23,839 - INFO - Response - Page 1:
2025-05-15 12:00:24,042 - INFO - 第 1 页获取到 100 条记录
2025-05-15 12:00:24,042 - INFO - Request Parameters - Page 2:
2025-05-15 12:00:24,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:24,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:24,480 - INFO - Response - Page 2:
2025-05-15 12:00:24,683 - INFO - 第 2 页获取到 100 条记录
2025-05-15 12:00:24,683 - INFO - Request Parameters - Page 3:
2025-05-15 12:00:24,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:24,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:25,183 - INFO - Response - Page 3:
2025-05-15 12:00:25,386 - INFO - 第 3 页获取到 100 条记录
2025-05-15 12:00:25,386 - INFO - Request Parameters - Page 4:
2025-05-15 12:00:25,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:25,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:25,855 - INFO - Response - Page 4:
2025-05-15 12:00:26,058 - INFO - 第 4 页获取到 100 条记录
2025-05-15 12:00:26,058 - INFO - Request Parameters - Page 5:
2025-05-15 12:00:26,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:26,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:26,542 - INFO - Response - Page 5:
2025-05-15 12:00:26,745 - INFO - 第 5 页获取到 100 条记录
2025-05-15 12:00:26,745 - INFO - Request Parameters - Page 6:
2025-05-15 12:00:26,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:26,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:27,214 - INFO - Response - Page 6:
2025-05-15 12:00:27,417 - INFO - 第 6 页获取到 100 条记录
2025-05-15 12:00:27,417 - INFO - Request Parameters - Page 7:
2025-05-15 12:00:27,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 12:00:27,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 12:00:27,777 - INFO - Response - Page 7:
2025-05-15 12:00:27,980 - INFO - 第 7 页获取到 25 条记录
2025-05-15 12:00:27,980 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 12:00:27,980 - INFO - 获取到 625 条表单数据
2025-05-15 12:00:27,980 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 12:00:27,980 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-15 12:00:28,480 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-15 12:00:28,480 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1235.0, 'new_value': 1305.0}, {'field': 'offline_amount', 'old_value': 24705.0, 'new_value': 25510.0}, {'field': 'total_amount', 'old_value': 25940.0, 'new_value': 26815.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 340}]
2025-05-15 12:00:28,480 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-15 12:00:28,949 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-15 12:00:28,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191415.0, 'new_value': 202395.0}, {'field': 'total_amount', 'old_value': 191415.0, 'new_value': 202395.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 146}]
2025-05-15 12:00:28,949 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-15 12:00:29,433 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-15 12:00:29,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20140.0, 'new_value': 22525.0}, {'field': 'total_amount', 'old_value': 21730.0, 'new_value': 24115.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 91}]
2025-05-15 12:00:29,433 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-15 12:00:29,964 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-15 12:00:29,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131441.0, 'new_value': 132441.0}, {'field': 'total_amount', 'old_value': 131441.0, 'new_value': 132441.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-15 12:00:29,964 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-15 12:00:30,464 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-15 12:00:30,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39670.0, 'new_value': 51220.0}, {'field': 'total_amount', 'old_value': 39670.0, 'new_value': 51220.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-15 12:00:30,464 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-15 12:00:30,917 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-15 12:00:30,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8387.0, 'new_value': 12081.0}, {'field': 'total_amount', 'old_value': 11284.0, 'new_value': 14978.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-15 12:00:30,917 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-15 12:00:31,339 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-15 12:00:31,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14458.92, 'new_value': 16852.92}, {'field': 'offline_amount', 'old_value': 7907.47, 'new_value': 8591.47}, {'field': 'total_amount', 'old_value': 22366.39, 'new_value': 25444.39}, {'field': 'order_count', 'old_value': 1120, 'new_value': 1303}]
2025-05-15 12:00:31,339 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-15 12:00:31,823 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-15 12:00:31,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33027.62, 'new_value': 34854.78}, {'field': 'total_amount', 'old_value': 33027.62, 'new_value': 34854.78}, {'field': 'order_count', 'old_value': 68, 'new_value': 78}]
2025-05-15 12:00:31,823 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-15 12:00:32,261 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-15 12:00:32,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157220.0, 'new_value': 172200.0}, {'field': 'total_amount', 'old_value': 157220.0, 'new_value': 172200.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 94}]
2025-05-15 12:00:32,261 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-15 12:00:32,808 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-15 12:00:32,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 248214.0, 'new_value': 265190.0}, {'field': 'offline_amount', 'old_value': 135515.0, 'new_value': 146871.0}, {'field': 'total_amount', 'old_value': 383729.0, 'new_value': 412061.0}, {'field': 'order_count', 'old_value': 415, 'new_value': 452}]
2025-05-15 12:00:32,808 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-15 12:00:33,261 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-15 12:00:33,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57049.0, 'new_value': 57514.0}, {'field': 'total_amount', 'old_value': 57049.0, 'new_value': 57514.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-15 12:00:33,261 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-15 12:00:33,745 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-15 12:00:33,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44560.41, 'new_value': 49447.58}, {'field': 'total_amount', 'old_value': 64953.59, 'new_value': 69840.76}, {'field': 'order_count', 'old_value': 1490, 'new_value': 1596}]
2025-05-15 12:00:33,745 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-15 12:00:34,152 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-15 12:00:34,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22220.0, 'new_value': 25080.0}, {'field': 'total_amount', 'old_value': 26340.0, 'new_value': 29200.0}, {'field': 'order_count', 'old_value': 257, 'new_value': 286}]
2025-05-15 12:00:34,152 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-15 12:00:34,605 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-15 12:00:34,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18343.0, 'new_value': 24783.0}, {'field': 'total_amount', 'old_value': 18343.0, 'new_value': 24783.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-15 12:00:34,605 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-15 12:00:35,105 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-15 12:00:35,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25879.0, 'new_value': 28938.8}, {'field': 'total_amount', 'old_value': 29839.0, 'new_value': 32898.8}, {'field': 'order_count', 'old_value': 225, 'new_value': 242}]
2025-05-15 12:00:35,105 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-15 12:00:35,589 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-15 12:00:35,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97621.0, 'new_value': 97889.0}, {'field': 'total_amount', 'old_value': 97621.0, 'new_value': 97889.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-15 12:00:35,589 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-15 12:00:35,995 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-15 12:00:35,995 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30142.23, 'new_value': 32482.39}, {'field': 'offline_amount', 'old_value': 60038.0, 'new_value': 64971.0}, {'field': 'total_amount', 'old_value': 90180.23, 'new_value': 97453.39}, {'field': 'order_count', 'old_value': 1109, 'new_value': 1186}]
2025-05-15 12:00:35,995 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-15 12:00:36,495 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-15 12:00:36,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12458.51, 'new_value': 13435.8}, {'field': 'offline_amount', 'old_value': 14745.81, 'new_value': 15549.81}, {'field': 'total_amount', 'old_value': 27204.32, 'new_value': 28985.61}, {'field': 'order_count', 'old_value': 1304, 'new_value': 1389}]
2025-05-15 12:00:36,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-15 12:00:36,933 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-15 12:00:36,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192669.9, 'new_value': 196525.5}, {'field': 'total_amount', 'old_value': 307689.6, 'new_value': 311545.2}, {'field': 'order_count', 'old_value': 1993, 'new_value': 2074}]
2025-05-15 12:00:36,933 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-15 12:00:37,386 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-15 12:00:37,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53814.0, 'new_value': 57809.0}, {'field': 'total_amount', 'old_value': 53814.0, 'new_value': 57809.0}, {'field': 'order_count', 'old_value': 2902, 'new_value': 3101}]
2025-05-15 12:00:37,386 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-15 12:00:37,855 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-15 12:00:37,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38066.0, 'new_value': 44810.0}, {'field': 'total_amount', 'old_value': 38066.0, 'new_value': 44810.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 77}]
2025-05-15 12:00:37,855 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-15 12:00:38,386 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-15 12:00:38,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215067.48, 'new_value': 231658.48}, {'field': 'total_amount', 'old_value': 215067.48, 'new_value': 231658.48}, {'field': 'order_count', 'old_value': 242, 'new_value': 254}]
2025-05-15 12:00:38,386 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-15 12:00:38,839 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-15 12:00:38,839 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2464.78, 'new_value': 2568.66}, {'field': 'offline_amount', 'old_value': 46464.13, 'new_value': 49384.49}, {'field': 'total_amount', 'old_value': 48928.91, 'new_value': 51953.15}, {'field': 'order_count', 'old_value': 1859, 'new_value': 1987}]
2025-05-15 12:00:38,839 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-15 12:00:39,355 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-15 12:00:39,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73379.85, 'new_value': 81066.41}, {'field': 'total_amount', 'old_value': 73379.85, 'new_value': 81066.41}, {'field': 'order_count', 'old_value': 829, 'new_value': 909}]
2025-05-15 12:00:39,355 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-15 12:00:39,761 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-15 12:00:39,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 600050.0, 'new_value': 648829.0}, {'field': 'offline_amount', 'old_value': 174494.0, 'new_value': 185366.0}, {'field': 'total_amount', 'old_value': 774544.0, 'new_value': 834195.0}, {'field': 'order_count', 'old_value': 861, 'new_value': 930}]
2025-05-15 12:00:39,761 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-15 12:00:40,183 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-15 12:00:40,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6529.02, 'new_value': 7065.6}, {'field': 'offline_amount', 'old_value': 21721.98, 'new_value': 23389.59}, {'field': 'total_amount', 'old_value': 28251.0, 'new_value': 30455.19}, {'field': 'order_count', 'old_value': 488, 'new_value': 541}]
2025-05-15 12:00:40,183 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-15 12:00:40,667 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-15 12:00:40,667 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 15800.0}, {'field': 'total_amount', 'old_value': 6877.0, 'new_value': 22677.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-15 12:00:40,667 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-15 12:00:41,152 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-15 12:00:41,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5124.1, 'new_value': 5194.0}, {'field': 'offline_amount', 'old_value': 59753.7, 'new_value': 59982.7}, {'field': 'total_amount', 'old_value': 64877.8, 'new_value': 65176.7}, {'field': 'order_count', 'old_value': 1310, 'new_value': 1322}]
2025-05-15 12:00:41,152 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-15 12:00:41,620 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-15 12:00:41,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174350.0, 'new_value': 178100.0}, {'field': 'total_amount', 'old_value': 174350.0, 'new_value': 178100.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-15 12:00:41,620 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-15 12:00:42,105 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-15 12:00:42,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221535.89, 'new_value': 227420.89}, {'field': 'total_amount', 'old_value': 221535.89, 'new_value': 227420.89}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-15 12:00:42,105 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-15 12:00:42,542 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-15 12:00:42,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176190.13, 'new_value': 185960.13}, {'field': 'total_amount', 'old_value': 215550.13, 'new_value': 225320.13}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-15 12:00:42,542 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-15 12:00:43,027 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-15 12:00:43,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14530.8, 'new_value': 14630.7}, {'field': 'offline_amount', 'old_value': 30459.6, 'new_value': 31077.6}, {'field': 'total_amount', 'old_value': 44990.4, 'new_value': 45708.3}, {'field': 'order_count', 'old_value': 564, 'new_value': 567}]
2025-05-15 12:00:43,027 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-15 12:00:43,542 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-15 12:00:43,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108612.0, 'new_value': 120338.0}, {'field': 'total_amount', 'old_value': 108612.0, 'new_value': 120338.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 213}]
2025-05-15 12:00:43,542 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-15 12:00:43,995 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-15 12:00:43,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100368.42, 'new_value': 112560.57}, {'field': 'total_amount', 'old_value': 100368.42, 'new_value': 112560.57}, {'field': 'order_count', 'old_value': 121, 'new_value': 142}]
2025-05-15 12:00:43,995 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-15 12:00:44,433 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-15 12:00:44,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56259.44, 'new_value': 58735.21}, {'field': 'total_amount', 'old_value': 56259.44, 'new_value': 58735.21}, {'field': 'order_count', 'old_value': 2084, 'new_value': 2200}]
2025-05-15 12:00:44,433 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-15 12:00:44,995 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-15 12:00:44,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26826.63, 'new_value': 28426.63}, {'field': 'total_amount', 'old_value': 81980.86, 'new_value': 83580.86}, {'field': 'order_count', 'old_value': 4698, 'new_value': 4699}]
2025-05-15 12:00:44,995 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-15 12:00:45,448 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-15 12:00:45,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39680.0, 'new_value': 41636.0}, {'field': 'total_amount', 'old_value': 39680.0, 'new_value': 41636.0}, {'field': 'order_count', 'old_value': 340, 'new_value': 360}]
2025-05-15 12:00:45,448 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-15 12:00:45,902 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-15 12:00:45,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18532.77, 'new_value': 21389.18}, {'field': 'total_amount', 'old_value': 18532.77, 'new_value': 21389.18}, {'field': 'order_count', 'old_value': 1415, 'new_value': 1617}]
2025-05-15 12:00:45,902 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-15 12:00:46,370 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-15 12:00:46,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102895.36, 'new_value': 108046.56}, {'field': 'offline_amount', 'old_value': 17793.92, 'new_value': 18688.82}, {'field': 'total_amount', 'old_value': 120689.28, 'new_value': 126735.38}, {'field': 'order_count', 'old_value': 430, 'new_value': 451}]
2025-05-15 12:00:46,370 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-15 12:00:46,870 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-15 12:00:46,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106783.0, 'new_value': 109237.0}, {'field': 'offline_amount', 'old_value': 37287.66, 'new_value': 38424.56}, {'field': 'total_amount', 'old_value': 144070.66, 'new_value': 147661.56}, {'field': 'order_count', 'old_value': 869, 'new_value': 894}]
2025-05-15 12:00:46,870 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-15 12:00:47,417 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-15 12:00:47,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24824.0, 'new_value': 26603.0}, {'field': 'total_amount', 'old_value': 28400.0, 'new_value': 30179.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 127}]
2025-05-15 12:00:47,417 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-15 12:00:47,808 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-15 12:00:47,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23024.88, 'new_value': 24522.79}, {'field': 'offline_amount', 'old_value': 29336.09, 'new_value': 30709.36}, {'field': 'total_amount', 'old_value': 52360.97, 'new_value': 55232.15}, {'field': 'order_count', 'old_value': 2622, 'new_value': 2775}]
2025-05-15 12:00:47,808 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-15 12:00:48,214 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-15 12:00:48,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4211.0, 'new_value': 4310.0}, {'field': 'total_amount', 'old_value': 5329.0, 'new_value': 5428.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 105}]
2025-05-15 12:00:48,214 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-15 12:00:48,652 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-15 12:00:48,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46995.32, 'new_value': 50992.47}, {'field': 'total_amount', 'old_value': 46995.32, 'new_value': 50992.47}, {'field': 'order_count', 'old_value': 1211, 'new_value': 1344}]
2025-05-15 12:00:48,652 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-15 12:00:49,152 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-15 12:00:49,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42448.38, 'new_value': 44936.38}, {'field': 'total_amount', 'old_value': 42448.38, 'new_value': 44936.38}, {'field': 'order_count', 'old_value': 70, 'new_value': 76}]
2025-05-15 12:00:49,152 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-15 12:00:49,605 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-15 12:00:49,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3225.0, 'new_value': 4725.0}, {'field': 'total_amount', 'old_value': 3225.0, 'new_value': 4725.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 297}]
2025-05-15 12:00:49,605 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-15 12:00:50,183 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-15 12:00:50,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103341.0, 'new_value': 106342.0}, {'field': 'total_amount', 'old_value': 103341.0, 'new_value': 106342.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-15 12:00:50,183 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-15 12:00:50,636 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-15 12:00:50,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84219.0, 'new_value': 87158.0}, {'field': 'offline_amount', 'old_value': 36365.22, 'new_value': 39004.22}, {'field': 'total_amount', 'old_value': 120584.22, 'new_value': 126162.22}, {'field': 'order_count', 'old_value': 843, 'new_value': 889}]
2025-05-15 12:00:50,636 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-15 12:00:51,073 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-15 12:00:51,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29095.97, 'new_value': 32120.93}, {'field': 'offline_amount', 'old_value': 293398.48, 'new_value': 314419.73}, {'field': 'total_amount', 'old_value': 322494.45, 'new_value': 346540.66}, {'field': 'order_count', 'old_value': 992, 'new_value': 1072}]
2025-05-15 12:00:51,073 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-15 12:00:51,480 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-15 12:00:51,480 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5519.58, 'new_value': 5814.67}, {'field': 'offline_amount', 'old_value': 68841.45, 'new_value': 73344.51}, {'field': 'total_amount', 'old_value': 74361.03, 'new_value': 79159.18}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1247}]
2025-05-15 12:00:51,480 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-15 12:00:51,933 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-15 12:00:51,933 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6205.64, 'new_value': 6527.37}, {'field': 'offline_amount', 'old_value': 96240.85, 'new_value': 103413.67}, {'field': 'total_amount', 'old_value': 102446.49, 'new_value': 109941.04}, {'field': 'order_count', 'old_value': 1107, 'new_value': 1196}]
2025-05-15 12:00:51,933 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-15 12:00:52,417 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-15 12:00:52,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26826.5, 'new_value': 29030.7}, {'field': 'total_amount', 'old_value': 26828.5, 'new_value': 29032.7}, {'field': 'order_count', 'old_value': 152, 'new_value': 169}]
2025-05-15 12:00:52,417 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-15 12:00:52,917 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-15 12:00:52,917 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34902.0, 'new_value': 45342.0}, {'field': 'offline_amount', 'old_value': 33696.0, 'new_value': 39942.0}, {'field': 'total_amount', 'old_value': 68598.0, 'new_value': 85284.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-05-15 12:00:52,917 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-15 12:00:53,386 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-15 12:00:53,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1100.0, 'new_value': 1200.0}, {'field': 'offline_amount', 'old_value': 16681.0, 'new_value': 17447.0}, {'field': 'total_amount', 'old_value': 17781.0, 'new_value': 18647.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 70}]
2025-05-15 12:00:53,386 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-15 12:00:53,839 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-15 12:00:53,839 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3219.94, 'new_value': 5180.61}, {'field': 'offline_amount', 'old_value': 58428.97, 'new_value': 58492.23}, {'field': 'total_amount', 'old_value': 61648.91, 'new_value': 63672.84}, {'field': 'order_count', 'old_value': 1441, 'new_value': 1501}]
2025-05-15 12:00:53,839 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-15 12:00:54,276 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-15 12:00:54,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105941.94, 'new_value': 109859.0}, {'field': 'total_amount', 'old_value': 105941.94, 'new_value': 109859.0}, {'field': 'order_count', 'old_value': 328, 'new_value': 346}]
2025-05-15 12:00:54,276 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-15 12:00:54,792 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-15 12:00:54,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40568.95, 'new_value': 43604.43}, {'field': 'offline_amount', 'old_value': 22697.11, 'new_value': 24185.19}, {'field': 'total_amount', 'old_value': 63266.06, 'new_value': 67789.62}, {'field': 'order_count', 'old_value': 2109, 'new_value': 2289}]
2025-05-15 12:00:54,792 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-15 12:00:55,183 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-15 12:00:55,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73864.0, 'new_value': 79468.0}, {'field': 'total_amount', 'old_value': 73864.0, 'new_value': 79468.0}, {'field': 'order_count', 'old_value': 1780, 'new_value': 1924}]
2025-05-15 12:00:55,183 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-15 12:00:55,636 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-15 12:00:55,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7949.65, 'new_value': 8209.65}, {'field': 'total_amount', 'old_value': 7949.65, 'new_value': 8209.65}, {'field': 'order_count', 'old_value': 132, 'new_value': 135}]
2025-05-15 12:00:55,636 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-15 12:00:56,058 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-15 12:00:56,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44672.49, 'new_value': 47966.64}, {'field': 'offline_amount', 'old_value': 483806.31, 'new_value': 508984.31}, {'field': 'total_amount', 'old_value': 528478.8, 'new_value': 556950.95}, {'field': 'order_count', 'old_value': 1715, 'new_value': 1812}]
2025-05-15 12:00:56,058 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-15 12:00:56,511 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-15 12:00:56,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13419.17, 'new_value': 14043.51}, {'field': 'total_amount', 'old_value': 13419.17, 'new_value': 14043.51}, {'field': 'order_count', 'old_value': 59, 'new_value': 64}]
2025-05-15 12:00:56,511 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-15 12:00:56,964 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-15 12:00:56,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83750.0, 'new_value': 90767.0}, {'field': 'total_amount', 'old_value': 83750.0, 'new_value': 90767.0}, {'field': 'order_count', 'old_value': 3063, 'new_value': 3347}]
2025-05-15 12:00:56,964 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-15 12:00:57,433 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-15 12:00:57,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379947.9, 'new_value': 409755.7}, {'field': 'total_amount', 'old_value': 379947.9, 'new_value': 409755.7}, {'field': 'order_count', 'old_value': 2767, 'new_value': 2970}]
2025-05-15 12:00:57,433 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-15 12:00:57,886 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-15 12:00:57,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27116.0, 'new_value': 28116.0}, {'field': 'total_amount', 'old_value': 27116.0, 'new_value': 28116.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-15 12:00:57,886 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-15 12:00:58,417 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-15 12:00:58,417 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14603.0, 'new_value': 15558.0}, {'field': 'offline_amount', 'old_value': 270215.0, 'new_value': 289866.0}, {'field': 'total_amount', 'old_value': 284818.0, 'new_value': 305424.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-05-15 12:00:58,417 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-15 12:00:58,870 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-15 12:00:58,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53685.0, 'new_value': 55684.0}, {'field': 'total_amount', 'old_value': 53685.0, 'new_value': 55684.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-15 12:00:58,870 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-15 12:00:59,339 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-15 12:00:59,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70208.95, 'new_value': 76588.86}, {'field': 'total_amount', 'old_value': 70208.95, 'new_value': 76588.86}, {'field': 'order_count', 'old_value': 2590, 'new_value': 2807}]
2025-05-15 12:00:59,339 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-15 12:00:59,808 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-15 12:00:59,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89054.0, 'new_value': 90553.0}, {'field': 'total_amount', 'old_value': 126697.0, 'new_value': 128196.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-15 12:00:59,808 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-15 12:01:00,292 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-15 12:01:00,292 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25427.86, 'new_value': 25437.44}, {'field': 'total_amount', 'old_value': 25427.86, 'new_value': 25437.44}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-15 12:01:00,292 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-15 12:01:00,776 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-15 12:01:00,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12757.98, 'new_value': 13390.28}, {'field': 'total_amount', 'old_value': 12757.98, 'new_value': 13390.28}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-05-15 12:01:00,776 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-15 12:01:01,214 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-15 12:01:01,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137562.06, 'new_value': 150656.96}, {'field': 'total_amount', 'old_value': 138319.06, 'new_value': 151413.96}, {'field': 'order_count', 'old_value': 1622, 'new_value': 1783}]
2025-05-15 12:01:01,214 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-15 12:01:01,636 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-15 12:01:01,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76499.97, 'new_value': 80144.43}, {'field': 'total_amount', 'old_value': 78188.81, 'new_value': 81833.27}, {'field': 'order_count', 'old_value': 352, 'new_value': 377}]
2025-05-15 12:01:01,636 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-15 12:01:02,183 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-15 12:01:02,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46656.0, 'new_value': 47165.0}, {'field': 'total_amount', 'old_value': 46656.0, 'new_value': 47165.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-05-15 12:01:02,183 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-15 12:01:02,651 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-15 12:01:02,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37977.28, 'new_value': 41475.28}, {'field': 'offline_amount', 'old_value': 233942.3, 'new_value': 243760.3}, {'field': 'total_amount', 'old_value': 271919.58, 'new_value': 285235.58}, {'field': 'order_count', 'old_value': 425, 'new_value': 458}]
2025-05-15 12:01:02,651 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-15 12:01:03,151 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-15 12:01:03,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93082.33, 'new_value': 97760.42}, {'field': 'total_amount', 'old_value': 93082.33, 'new_value': 97760.42}, {'field': 'order_count', 'old_value': 492, 'new_value': 523}]
2025-05-15 12:01:03,151 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-15 12:01:03,589 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-15 12:01:03,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98517.86, 'new_value': 104606.41}, {'field': 'offline_amount', 'old_value': 275662.63, 'new_value': 282903.18}, {'field': 'total_amount', 'old_value': 374180.49, 'new_value': 387509.59}, {'field': 'order_count', 'old_value': 2535, 'new_value': 2663}]
2025-05-15 12:01:03,589 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-15 12:01:04,105 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-15 12:01:04,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17669.7, 'new_value': 18450.7}, {'field': 'total_amount', 'old_value': 17669.7, 'new_value': 18450.7}, {'field': 'order_count', 'old_value': 98, 'new_value': 102}]
2025-05-15 12:01:04,105 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-15 12:01:04,573 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-15 12:01:04,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3807.37, 'new_value': 3925.4}, {'field': 'offline_amount', 'old_value': 99641.03, 'new_value': 108230.03}, {'field': 'total_amount', 'old_value': 103448.4, 'new_value': 112155.43}, {'field': 'order_count', 'old_value': 702, 'new_value': 746}]
2025-05-15 12:01:04,573 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-15 12:01:05,042 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-15 12:01:05,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84983.0, 'new_value': 90882.0}, {'field': 'total_amount', 'old_value': 84983.0, 'new_value': 90882.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-15 12:01:05,042 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-15 12:01:05,558 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-15 12:01:05,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38038.09, 'new_value': 40205.63}, {'field': 'total_amount', 'old_value': 38038.09, 'new_value': 40205.63}, {'field': 'order_count', 'old_value': 1743, 'new_value': 1856}]
2025-05-15 12:01:05,558 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-15 12:01:06,011 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-15 12:01:06,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1028862.1, 'new_value': 1060487.1}, {'field': 'total_amount', 'old_value': 1082307.2, 'new_value': 1113932.2}, {'field': 'order_count', 'old_value': 1835, 'new_value': 1908}]
2025-05-15 12:01:06,011 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-15 12:01:06,448 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-15 12:01:06,448 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77159.65, 'new_value': 85139.65}, {'field': 'offline_amount', 'old_value': 48431.0, 'new_value': 53101.0}, {'field': 'total_amount', 'old_value': 125590.65, 'new_value': 138240.65}, {'field': 'order_count', 'old_value': 673, 'new_value': 735}]
2025-05-15 12:01:06,448 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-15 12:01:06,901 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-15 12:01:06,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2557.0, 'new_value': 3437.0}, {'field': 'total_amount', 'old_value': 2557.0, 'new_value': 3437.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-15 12:01:06,917 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-15 12:01:07,355 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-15 12:01:07,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24481.9, 'new_value': 25272.9}, {'field': 'total_amount', 'old_value': 24481.9, 'new_value': 25272.9}, {'field': 'order_count', 'old_value': 108, 'new_value': 111}]
2025-05-15 12:01:07,355 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-15 12:01:07,792 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-15 12:01:07,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27719.0, 'new_value': 29654.0}, {'field': 'total_amount', 'old_value': 56365.0, 'new_value': 58300.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-15 12:01:07,792 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-15 12:01:08,230 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-15 12:01:08,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13637.0, 'new_value': 13695.0}, {'field': 'total_amount', 'old_value': 13637.0, 'new_value': 13695.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 236}]
2025-05-15 12:01:08,230 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-15 12:01:08,620 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-15 12:01:08,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74988.0, 'new_value': 79276.0}, {'field': 'total_amount', 'old_value': 74989.0, 'new_value': 79277.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-15 12:01:08,620 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-15 12:01:09,089 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-15 12:01:09,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3406.06, 'new_value': 3705.86}, {'field': 'offline_amount', 'old_value': 8654.64, 'new_value': 9129.53}, {'field': 'total_amount', 'old_value': 12060.7, 'new_value': 12835.39}, {'field': 'order_count', 'old_value': 422, 'new_value': 451}]
2025-05-15 12:01:09,089 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-15 12:01:09,542 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-15 12:01:09,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91909.42, 'new_value': 97219.38}, {'field': 'offline_amount', 'old_value': 74152.26, 'new_value': 80027.26}, {'field': 'total_amount', 'old_value': 166061.68, 'new_value': 177246.64}, {'field': 'order_count', 'old_value': 1413, 'new_value': 1516}]
2025-05-15 12:01:09,542 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-15 12:01:09,964 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-15 12:01:09,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35972.11, 'new_value': 39111.71}, {'field': 'total_amount', 'old_value': 35975.41, 'new_value': 39115.01}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-15 12:01:09,964 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-15 12:01:10,417 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-15 12:01:10,417 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197999.9, 'new_value': 206071.9}, {'field': 'offline_amount', 'old_value': 43692.0, 'new_value': 50314.0}, {'field': 'total_amount', 'old_value': 241691.9, 'new_value': 256385.9}, {'field': 'order_count', 'old_value': 298, 'new_value': 314}]
2025-05-15 12:01:10,417 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-15 12:01:10,995 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-15 12:01:10,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18857.0, 'new_value': 19076.0}, {'field': 'total_amount', 'old_value': 18857.0, 'new_value': 19076.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-05-15 12:01:10,995 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-15 12:01:11,433 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-15 12:01:11,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46847.0, 'new_value': 49707.0}, {'field': 'total_amount', 'old_value': 46847.0, 'new_value': 49707.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 34}]
2025-05-15 12:01:11,433 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-15 12:01:11,776 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-15 12:01:11,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91520.0, 'new_value': 92357.0}, {'field': 'total_amount', 'old_value': 99095.8, 'new_value': 99932.8}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-15 12:01:11,776 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-15 12:01:12,198 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-15 12:01:12,198 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22195.22, 'new_value': 23888.92}, {'field': 'offline_amount', 'old_value': 55430.5, 'new_value': 57143.57}, {'field': 'total_amount', 'old_value': 77625.72, 'new_value': 81032.49}, {'field': 'order_count', 'old_value': 2780, 'new_value': 2919}]
2025-05-15 12:01:12,198 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-15 12:01:12,636 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-15 12:01:12,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57023.29, 'new_value': 57553.19}, {'field': 'total_amount', 'old_value': 60792.39, 'new_value': 61322.29}, {'field': 'order_count', 'old_value': 313, 'new_value': 316}]
2025-05-15 12:01:12,636 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-15 12:01:13,104 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-15 12:01:13,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8296.78, 'new_value': 8554.58}, {'field': 'offline_amount', 'old_value': 150709.1, 'new_value': 156757.3}, {'field': 'total_amount', 'old_value': 159005.88, 'new_value': 165311.88}, {'field': 'order_count', 'old_value': 1117, 'new_value': 1173}]
2025-05-15 12:01:13,104 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-15 12:01:13,511 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-15 12:01:13,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49053.0, 'new_value': 49212.0}, {'field': 'total_amount', 'old_value': 54194.0, 'new_value': 54353.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-15 12:01:13,511 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-15 12:01:13,995 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-15 12:01:13,995 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 542.0, 'new_value': 710.0}, {'field': 'offline_amount', 'old_value': 9015.5, 'new_value': 9083.5}, {'field': 'total_amount', 'old_value': 9557.5, 'new_value': 9793.5}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-15 12:01:13,995 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-15 12:01:14,448 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-15 12:01:14,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96459.0, 'new_value': 115282.0}, {'field': 'total_amount', 'old_value': 122587.0, 'new_value': 141410.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-05-15 12:01:14,448 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-15 12:01:14,886 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-15 12:01:14,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79942.14, 'new_value': 82199.5}, {'field': 'offline_amount', 'old_value': 73103.45, 'new_value': 75270.45}, {'field': 'total_amount', 'old_value': 153045.59, 'new_value': 157469.95}, {'field': 'order_count', 'old_value': 1507, 'new_value': 1563}]
2025-05-15 12:01:14,886 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-15 12:01:15,323 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-15 12:01:15,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 431413.04, 'new_value': 432423.04}, {'field': 'offline_amount', 'old_value': 143599.9, 'new_value': 143644.9}, {'field': 'total_amount', 'old_value': 575012.94, 'new_value': 576067.94}, {'field': 'order_count', 'old_value': 5426, 'new_value': 5440}]
2025-05-15 12:01:15,323 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-15 12:01:15,776 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-15 12:01:15,776 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43423.8, 'new_value': 48641.2}, {'field': 'total_amount', 'old_value': 46061.35, 'new_value': 51278.75}, {'field': 'order_count', 'old_value': 123, 'new_value': 136}]
2025-05-15 12:01:15,776 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-15 12:01:16,261 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-15 12:01:16,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41823.89, 'new_value': 43859.87}, {'field': 'total_amount', 'old_value': 41823.89, 'new_value': 43859.87}, {'field': 'order_count', 'old_value': 1131, 'new_value': 1199}]
2025-05-15 12:01:16,261 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-15 12:01:16,698 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-15 12:01:16,698 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5180.4, 'new_value': 5189.2}, {'field': 'offline_amount', 'old_value': 28139.0, 'new_value': 29376.0}, {'field': 'total_amount', 'old_value': 33319.4, 'new_value': 34565.2}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-05-15 12:01:16,698 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-15 12:01:17,136 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-15 12:01:17,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186421.0, 'new_value': 216821.0}, {'field': 'total_amount', 'old_value': 186421.0, 'new_value': 216821.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 31}]
2025-05-15 12:01:17,136 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-15 12:01:17,558 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-15 12:01:17,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212883.43, 'new_value': 241518.42}, {'field': 'total_amount', 'old_value': 212883.43, 'new_value': 241518.42}, {'field': 'order_count', 'old_value': 341, 'new_value': 359}]
2025-05-15 12:01:17,558 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-15 12:01:17,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-15 12:01:17,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8041.99, 'new_value': 8691.96}, {'field': 'offline_amount', 'old_value': 243976.69, 'new_value': 255009.27}, {'field': 'total_amount', 'old_value': 252018.68, 'new_value': 263701.23}, {'field': 'order_count', 'old_value': 987, 'new_value': 1044}]
2025-05-15 12:01:17,979 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-15 12:01:18,433 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-15 12:01:18,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44819.0, 'new_value': 47525.0}, {'field': 'offline_amount', 'old_value': 43657.12, 'new_value': 49388.46}, {'field': 'total_amount', 'old_value': 88476.12, 'new_value': 96913.46}, {'field': 'order_count', 'old_value': 105, 'new_value': 115}]
2025-05-15 12:01:18,433 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-15 12:01:18,901 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-15 12:01:18,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94776.4, 'new_value': 98060.8}, {'field': 'total_amount', 'old_value': 94776.4, 'new_value': 98060.8}, {'field': 'order_count', 'old_value': 212, 'new_value': 219}]
2025-05-15 12:01:18,901 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-15 12:01:19,370 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-15 12:01:19,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122400.96, 'new_value': 129742.45}, {'field': 'total_amount', 'old_value': 183263.56, 'new_value': 190605.05}, {'field': 'order_count', 'old_value': 2126, 'new_value': 2201}]
2025-05-15 12:01:19,370 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-15 12:01:19,792 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-15 12:01:19,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57280.1, 'new_value': 58764.5}, {'field': 'total_amount', 'old_value': 57280.1, 'new_value': 58764.5}, {'field': 'order_count', 'old_value': 2184, 'new_value': 2249}]
2025-05-15 12:01:19,792 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-15 12:01:20,214 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-15 12:01:20,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43589.1, 'new_value': 47023.58}, {'field': 'total_amount', 'old_value': 43589.1, 'new_value': 47023.58}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-15 12:01:20,214 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-15 12:01:20,683 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-15 12:01:20,683 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41700.69, 'new_value': 44901.79}, {'field': 'offline_amount', 'old_value': 603573.52, 'new_value': 646556.38}, {'field': 'total_amount', 'old_value': 645274.21, 'new_value': 691458.17}, {'field': 'order_count', 'old_value': 5073, 'new_value': 5479}]
2025-05-15 12:01:20,683 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-15 12:01:21,167 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-15 12:01:21,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7776.82, 'new_value': 9090.22}, {'field': 'total_amount', 'old_value': 13918.86, 'new_value': 15232.26}, {'field': 'order_count', 'old_value': 60, 'new_value': 63}]
2025-05-15 12:01:21,167 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-15 12:01:21,620 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-15 12:01:21,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26357.0, 'new_value': 28515.0}, {'field': 'total_amount', 'old_value': 26357.0, 'new_value': 28515.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-15 12:01:21,620 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-15 12:01:22,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-15 12:01:22,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73305.95, 'new_value': 74746.2}, {'field': 'total_amount', 'old_value': 73305.95, 'new_value': 74746.2}, {'field': 'order_count', 'old_value': 470, 'new_value': 480}]
2025-05-15 12:01:22,136 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-15 12:01:22,558 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-15 12:01:22,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47908.98, 'new_value': 50471.18}, {'field': 'total_amount', 'old_value': 47908.98, 'new_value': 50471.18}, {'field': 'order_count', 'old_value': 260, 'new_value': 281}]
2025-05-15 12:01:22,558 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-15 12:01:23,089 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-15 12:01:23,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1077.79, 'new_value': 1231.41}, {'field': 'offline_amount', 'old_value': 13880.54, 'new_value': 14335.34}, {'field': 'total_amount', 'old_value': 14958.33, 'new_value': 15566.75}, {'field': 'order_count', 'old_value': 674, 'new_value': 705}]
2025-05-15 12:01:23,089 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-15 12:01:23,558 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-15 12:01:23,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34691.0, 'new_value': 36253.0}, {'field': 'total_amount', 'old_value': 34691.0, 'new_value': 36253.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-15 12:01:23,558 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-15 12:01:24,104 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-15 12:01:24,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3078.0, 'new_value': 3228.0}, {'field': 'total_amount', 'old_value': 3078.0, 'new_value': 3228.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-15 12:01:24,104 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-15 12:01:24,558 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-15 12:01:24,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60765.2, 'new_value': 62237.0}, {'field': 'total_amount', 'old_value': 60765.2, 'new_value': 62237.0}, {'field': 'order_count', 'old_value': 183, 'new_value': 190}]
2025-05-15 12:01:24,558 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-15 12:01:24,995 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-15 12:01:24,995 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84615.18, 'new_value': 92809.93}, {'field': 'offline_amount', 'old_value': 211006.65, 'new_value': 217466.76}, {'field': 'total_amount', 'old_value': 295621.83, 'new_value': 310276.69}, {'field': 'order_count', 'old_value': 7564, 'new_value': 8100}]
2025-05-15 12:01:24,995 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-15 12:01:25,417 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-15 12:01:25,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29719.0, 'new_value': 30216.0}, {'field': 'total_amount', 'old_value': 29719.0, 'new_value': 30216.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 77}]
2025-05-15 12:01:25,417 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-15 12:01:25,854 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-15 12:01:25,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4977.0, 'new_value': 4986.9}, {'field': 'offline_amount', 'old_value': 19684.0, 'new_value': 21290.0}, {'field': 'total_amount', 'old_value': 24661.0, 'new_value': 26276.9}, {'field': 'order_count', 'old_value': 119, 'new_value': 123}]
2025-05-15 12:01:25,870 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-15 12:01:26,339 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-15 12:01:26,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 860464.94, 'new_value': 862564.94}, {'field': 'total_amount', 'old_value': 860464.94, 'new_value': 862564.94}, {'field': 'order_count', 'old_value': 534, 'new_value': 535}]
2025-05-15 12:01:26,339 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-15 12:01:26,761 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-15 12:01:26,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23157.0, 'new_value': 23616.0}, {'field': 'total_amount', 'old_value': 23157.0, 'new_value': 23616.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-05-15 12:01:26,761 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-15 12:01:27,276 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-15 12:01:27,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26919.52, 'new_value': 27968.6}, {'field': 'total_amount', 'old_value': 27116.32, 'new_value': 28165.4}, {'field': 'order_count', 'old_value': 240, 'new_value': 248}]
2025-05-15 12:01:27,276 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-15 12:01:27,729 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-15 12:01:27,729 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2740.0, 'new_value': 2901.0}, {'field': 'offline_amount', 'old_value': 13428.2, 'new_value': 13958.5}, {'field': 'total_amount', 'old_value': 16168.2, 'new_value': 16859.5}, {'field': 'order_count', 'old_value': 657, 'new_value': 684}]
2025-05-15 12:01:27,729 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-15 12:01:28,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-15 12:01:28,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58536.64, 'new_value': 61358.08}, {'field': 'total_amount', 'old_value': 58536.64, 'new_value': 61358.08}, {'field': 'order_count', 'old_value': 192, 'new_value': 204}]
2025-05-15 12:01:28,136 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-15 12:01:28,620 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-15 12:01:28,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 668000.0, 'new_value': 718000.0}, {'field': 'total_amount', 'old_value': 668000.0, 'new_value': 718000.0}, {'field': 'order_count', 'old_value': 332, 'new_value': 333}]
2025-05-15 12:01:28,620 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-15 12:01:29,057 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-15 12:01:29,057 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16362.2, 'new_value': 16961.5}, {'field': 'offline_amount', 'old_value': 13085.7, 'new_value': 13480.7}, {'field': 'total_amount', 'old_value': 29447.9, 'new_value': 30442.2}, {'field': 'order_count', 'old_value': 158, 'new_value': 163}]
2025-05-15 12:01:29,057 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-15 12:01:29,495 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-15 12:01:29,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9514.34, 'new_value': 10669.34}, {'field': 'offline_amount', 'old_value': 16844.26, 'new_value': 18615.26}, {'field': 'total_amount', 'old_value': 26358.6, 'new_value': 29284.6}, {'field': 'order_count', 'old_value': 1100, 'new_value': 1221}]
2025-05-15 12:01:29,495 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-15 12:01:29,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-15 12:01:29,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113870.7, 'new_value': 119575.6}, {'field': 'total_amount', 'old_value': 113870.7, 'new_value': 119575.6}, {'field': 'order_count', 'old_value': 405, 'new_value': 431}]
2025-05-15 12:01:29,979 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-15 12:01:30,386 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-15 12:01:30,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 509528.16, 'new_value': 543573.16}, {'field': 'total_amount', 'old_value': 509528.16, 'new_value': 543573.16}, {'field': 'order_count', 'old_value': 1906, 'new_value': 2048}]
2025-05-15 12:01:30,386 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-15 12:01:30,823 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-15 12:01:30,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30282.0, 'new_value': 30796.0}, {'field': 'total_amount', 'old_value': 31798.0, 'new_value': 32312.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 128}]
2025-05-15 12:01:30,823 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-15 12:01:31,198 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-15 12:01:31,198 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15198.6, 'new_value': 15792.1}, {'field': 'offline_amount', 'old_value': 149306.0, 'new_value': 153417.6}, {'field': 'total_amount', 'old_value': 164504.6, 'new_value': 169209.7}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1314}]
2025-05-15 12:01:31,198 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-15 12:01:31,651 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-15 12:01:31,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8200.3, 'new_value': 8582.61}, {'field': 'offline_amount', 'old_value': 138391.94, 'new_value': 146340.04}, {'field': 'total_amount', 'old_value': 146592.24, 'new_value': 154922.65}, {'field': 'order_count', 'old_value': 7857, 'new_value': 8416}]
2025-05-15 12:01:31,651 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-15 12:01:32,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-15 12:01:32,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4033.0, 'new_value': 4132.0}, {'field': 'total_amount', 'old_value': 4033.0, 'new_value': 4132.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-15 12:01:32,136 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-15 12:01:32,698 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-15 12:01:32,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152263.02, 'new_value': 165828.11}, {'field': 'total_amount', 'old_value': 152263.02, 'new_value': 165828.11}, {'field': 'order_count', 'old_value': 4047, 'new_value': 4430}]
2025-05-15 12:01:32,698 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-15 12:01:33,151 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-15 12:01:33,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24690.0, 'new_value': 26490.0}, {'field': 'total_amount', 'old_value': 24690.0, 'new_value': 26490.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-15 12:01:33,151 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-15 12:01:33,682 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-15 12:01:33,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27628.1, 'new_value': 28330.44}, {'field': 'offline_amount', 'old_value': 17118.0, 'new_value': 18064.0}, {'field': 'total_amount', 'old_value': 44746.1, 'new_value': 46394.44}, {'field': 'order_count', 'old_value': 567, 'new_value': 589}]
2025-05-15 12:01:33,682 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-15 12:01:34,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-15 12:01:34,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84917.55, 'new_value': 89407.35}, {'field': 'total_amount', 'old_value': 84917.55, 'new_value': 89407.35}, {'field': 'order_count', 'old_value': 409, 'new_value': 433}]
2025-05-15 12:01:34,136 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-15 12:01:34,589 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-15 12:01:34,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12292.36, 'new_value': 13041.93}, {'field': 'offline_amount', 'old_value': 22151.36, 'new_value': 23886.8}, {'field': 'total_amount', 'old_value': 34443.72, 'new_value': 36928.73}, {'field': 'order_count', 'old_value': 1240, 'new_value': 1329}]
2025-05-15 12:01:34,589 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-15 12:01:35,026 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-15 12:01:35,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36985.0, 'new_value': 39030.0}, {'field': 'total_amount', 'old_value': 39393.0, 'new_value': 41438.0}, {'field': 'order_count', 'old_value': 156, 'new_value': 169}]
2025-05-15 12:01:35,026 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-15 12:01:35,495 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-15 12:01:35,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77270.75, 'new_value': 82888.75}, {'field': 'total_amount', 'old_value': 77270.75, 'new_value': 82888.75}, {'field': 'order_count', 'old_value': 7057, 'new_value': 7539}]
2025-05-15 12:01:35,495 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-15 12:01:35,932 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-15 12:01:35,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37825.0, 'new_value': 38697.0}, {'field': 'total_amount', 'old_value': 37825.0, 'new_value': 38697.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-15 12:01:35,932 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-15 12:01:36,370 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-15 12:01:36,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13805.7, 'new_value': 14159.7}, {'field': 'offline_amount', 'old_value': 33880.37, 'new_value': 34705.84}, {'field': 'total_amount', 'old_value': 47686.07, 'new_value': 48865.54}, {'field': 'order_count', 'old_value': 529, 'new_value': 545}]
2025-05-15 12:01:36,370 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-15 12:01:36,886 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-15 12:01:36,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52497.7, 'new_value': 55664.5}, {'field': 'offline_amount', 'old_value': 67728.33, 'new_value': 72267.33}, {'field': 'total_amount', 'old_value': 120226.03, 'new_value': 127931.83}, {'field': 'order_count', 'old_value': 821, 'new_value': 886}]
2025-05-15 12:01:36,886 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-15 12:01:37,323 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-15 12:01:37,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50427.63, 'new_value': 55010.17}, {'field': 'offline_amount', 'old_value': 37755.5, 'new_value': 40808.98}, {'field': 'total_amount', 'old_value': 88183.13, 'new_value': 95819.15}, {'field': 'order_count', 'old_value': 3601, 'new_value': 3928}]
2025-05-15 12:01:37,323 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-15 12:01:37,761 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-15 12:01:37,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4845.03, 'new_value': 5144.75}, {'field': 'offline_amount', 'old_value': 49470.87, 'new_value': 51184.95}, {'field': 'total_amount', 'old_value': 54315.9, 'new_value': 56329.7}, {'field': 'order_count', 'old_value': 1603, 'new_value': 1676}]
2025-05-15 12:01:37,761 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-15 12:01:38,198 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-15 12:01:38,198 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9667.05, 'new_value': 10623.04}, {'field': 'offline_amount', 'old_value': 20613.96, 'new_value': 22339.36}, {'field': 'total_amount', 'old_value': 30281.01, 'new_value': 32962.4}, {'field': 'order_count', 'old_value': 1603, 'new_value': 1757}]
2025-05-15 12:01:38,198 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-15 12:01:38,651 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-15 12:01:38,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59030.0, 'new_value': 64700.0}, {'field': 'total_amount', 'old_value': 59030.0, 'new_value': 64700.0}, {'field': 'order_count', 'old_value': 3157, 'new_value': 3319}]
2025-05-15 12:01:38,651 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-15 12:01:39,120 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-15 12:01:39,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19535.1, 'new_value': 20988.1}, {'field': 'total_amount', 'old_value': 21322.0, 'new_value': 22775.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-15 12:01:39,120 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-15 12:01:39,636 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-15 12:01:39,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30257.0, 'new_value': 33212.0}, {'field': 'total_amount', 'old_value': 30606.0, 'new_value': 33561.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 56}]
2025-05-15 12:01:39,636 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-15 12:01:40,089 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-15 12:01:40,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3317813.0, 'new_value': 3507813.0}, {'field': 'total_amount', 'old_value': 3317813.0, 'new_value': 3507813.0}, {'field': 'order_count', 'old_value': 55592, 'new_value': 55593}]
2025-05-15 12:01:40,089 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-15 12:01:40,511 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-15 12:01:40,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248142.5, 'new_value': 258888.5}, {'field': 'total_amount', 'old_value': 289129.48, 'new_value': 299875.48}, {'field': 'order_count', 'old_value': 2217, 'new_value': 2304}]
2025-05-15 12:01:40,511 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-15 12:01:40,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-15 12:01:40,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66946.21, 'new_value': 70778.69}, {'field': 'total_amount', 'old_value': 66946.21, 'new_value': 70778.69}, {'field': 'order_count', 'old_value': 1940, 'new_value': 2044}]
2025-05-15 12:01:40,901 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-15 12:01:41,386 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-15 12:01:41,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16617.0, 'new_value': 17039.0}, {'field': 'total_amount', 'old_value': 16617.0, 'new_value': 17039.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-15 12:01:41,386 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-15 12:01:41,839 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-15 12:01:41,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46711.74, 'new_value': 48711.74}, {'field': 'total_amount', 'old_value': 46711.74, 'new_value': 48711.74}, {'field': 'order_count', 'old_value': 1608, 'new_value': 1609}]
2025-05-15 12:01:41,839 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-15 12:01:42,292 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-15 12:01:42,292 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60522.1, 'new_value': 67954.03}, {'field': 'offline_amount', 'old_value': 151416.56, 'new_value': 157752.73}, {'field': 'total_amount', 'old_value': 211938.66, 'new_value': 225706.76}, {'field': 'order_count', 'old_value': 2453, 'new_value': 2658}]
2025-05-15 12:01:42,292 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-15 12:01:42,745 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-15 12:01:42,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66221.0, 'new_value': 70721.0}, {'field': 'total_amount', 'old_value': 66221.0, 'new_value': 70721.0}, {'field': 'order_count', 'old_value': 2565, 'new_value': 2566}]
2025-05-15 12:01:42,745 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-15 12:01:43,214 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-15 12:01:43,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16376.07, 'new_value': 17376.07}, {'field': 'total_amount', 'old_value': 16376.07, 'new_value': 17376.07}, {'field': 'order_count', 'old_value': 1623, 'new_value': 1624}]
2025-05-15 12:01:43,214 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-15 12:01:43,682 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-15 12:01:43,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85592.82, 'new_value': 93854.42}, {'field': 'total_amount', 'old_value': 85592.82, 'new_value': 93854.42}, {'field': 'order_count', 'old_value': 140, 'new_value': 152}]
2025-05-15 12:01:43,682 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-15 12:01:44,136 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-15 12:01:44,136 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113256.0, 'new_value': 121620.0}, {'field': 'total_amount', 'old_value': 113256.0, 'new_value': 121620.0}, {'field': 'order_count', 'old_value': 9438, 'new_value': 10135}]
2025-05-15 12:01:44,136 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-15 12:01:44,589 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-15 12:01:44,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20890.6, 'new_value': 22030.8}, {'field': 'offline_amount', 'old_value': 17105.24, 'new_value': 18167.71}, {'field': 'total_amount', 'old_value': 37995.84, 'new_value': 40198.51}, {'field': 'order_count', 'old_value': 6136, 'new_value': 6363}]
2025-05-15 12:01:44,589 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-15 12:01:44,995 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-15 12:01:44,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63116.7, 'new_value': 68844.68}, {'field': 'total_amount', 'old_value': 63116.7, 'new_value': 68844.68}, {'field': 'order_count', 'old_value': 544, 'new_value': 572}]
2025-05-15 12:01:44,995 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-15 12:01:45,589 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-15 12:01:45,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48466.92, 'new_value': 52125.34}, {'field': 'offline_amount', 'old_value': 144293.16, 'new_value': 150830.52}, {'field': 'total_amount', 'old_value': 192760.08, 'new_value': 202955.86}, {'field': 'order_count', 'old_value': 6144, 'new_value': 6518}]
2025-05-15 12:01:45,589 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-15 12:01:46,089 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-15 12:01:46,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63502.76, 'new_value': 67365.1}, {'field': 'offline_amount', 'old_value': 177011.56, 'new_value': 182423.98}, {'field': 'total_amount', 'old_value': 240514.32, 'new_value': 249789.08}, {'field': 'order_count', 'old_value': 2601, 'new_value': 2818}]
2025-05-15 12:01:46,089 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-15 12:01:46,510 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-15 12:01:46,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26516.0, 'new_value': 26768.7}, {'field': 'total_amount', 'old_value': 26516.0, 'new_value': 26768.7}, {'field': 'order_count', 'old_value': 284, 'new_value': 288}]
2025-05-15 12:01:46,510 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-15 12:01:46,932 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-15 12:01:46,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4585700.0, 'new_value': 4610700.0}, {'field': 'total_amount', 'old_value': 4585700.0, 'new_value': 4610700.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-05-15 12:01:46,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-15 12:01:47,386 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-15 12:01:47,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27827.4, 'new_value': 28964.4}, {'field': 'total_amount', 'old_value': 27827.4, 'new_value': 28964.4}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-15 12:01:47,386 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-15 12:01:47,854 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-15 12:01:47,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25453.5, 'new_value': 27567.5}, {'field': 'total_amount', 'old_value': 25453.5, 'new_value': 27567.5}, {'field': 'order_count', 'old_value': 1260, 'new_value': 1376}]
2025-05-15 12:01:47,854 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-15 12:01:48,323 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-15 12:01:48,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61923.8, 'new_value': 63289.8}, {'field': 'total_amount', 'old_value': 78493.3, 'new_value': 79859.3}, {'field': 'order_count', 'old_value': 111, 'new_value': 114}]
2025-05-15 12:01:48,323 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-15 12:01:48,714 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-15 12:01:48,714 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24521.15, 'new_value': 25957.55}, {'field': 'total_amount', 'old_value': 24521.15, 'new_value': 25957.55}, {'field': 'order_count', 'old_value': 1076, 'new_value': 1141}]
2025-05-15 12:01:48,714 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-15 12:01:49,167 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-15 12:01:49,167 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8915.53, 'new_value': 9608.91}, {'field': 'offline_amount', 'old_value': 133437.68, 'new_value': 144460.96}, {'field': 'total_amount', 'old_value': 142353.21, 'new_value': 154069.87}, {'field': 'order_count', 'old_value': 1841, 'new_value': 2022}]
2025-05-15 12:01:49,167 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-15 12:01:49,667 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-15 12:01:49,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26108.0, 'new_value': 26894.0}, {'field': 'total_amount', 'old_value': 26108.0, 'new_value': 26894.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 139}]
2025-05-15 12:01:49,667 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-15 12:01:50,135 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-15 12:01:50,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175545.0, 'new_value': 185545.0}, {'field': 'total_amount', 'old_value': 184363.99, 'new_value': 194363.99}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-15 12:01:50,135 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-15 12:01:50,620 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-15 12:01:50,620 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66897.64, 'new_value': 67983.64}, {'field': 'offline_amount', 'old_value': 158974.55, 'new_value': 165405.55}, {'field': 'total_amount', 'old_value': 225872.19, 'new_value': 233389.19}, {'field': 'order_count', 'old_value': 2719, 'new_value': 2867}]
2025-05-15 12:01:50,620 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-15 12:01:51,026 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-15 12:01:51,026 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15812.0, 'new_value': 22997.0}, {'field': 'offline_amount', 'old_value': 130317.0, 'new_value': 144217.0}, {'field': 'total_amount', 'old_value': 146129.0, 'new_value': 167214.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 148}]
2025-05-15 12:01:51,026 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-15 12:01:51,479 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-15 12:01:51,479 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55844.95, 'new_value': 62860.65}, {'field': 'total_amount', 'old_value': 128570.65, 'new_value': 135586.35}, {'field': 'order_count', 'old_value': 3330, 'new_value': 3535}]
2025-05-15 12:01:51,479 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-15 12:01:51,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-15 12:01:51,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133526.0, 'new_value': 141948.0}, {'field': 'total_amount', 'old_value': 133526.0, 'new_value': 141948.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 66}]
2025-05-15 12:01:51,979 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-15 12:01:52,401 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-15 12:01:52,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51029.8, 'new_value': 51929.2}, {'field': 'total_amount', 'old_value': 51029.8, 'new_value': 51929.2}, {'field': 'order_count', 'old_value': 384, 'new_value': 391}]
2025-05-15 12:01:52,401 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-15 12:01:52,839 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-15 12:01:52,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155006.12, 'new_value': 167273.85}, {'field': 'total_amount', 'old_value': 188086.33, 'new_value': 200354.06}, {'field': 'order_count', 'old_value': 7780, 'new_value': 8343}]
2025-05-15 12:01:52,839 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-15 12:01:53,292 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-15 12:01:53,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11673.0, 'new_value': 11948.0}, {'field': 'total_amount', 'old_value': 11673.0, 'new_value': 11948.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-15 12:01:53,292 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-15 12:01:53,792 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-15 12:01:53,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17895.0, 'new_value': 19173.0}, {'field': 'total_amount', 'old_value': 17895.0, 'new_value': 19173.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 190}]
2025-05-15 12:01:53,792 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-15 12:01:54,276 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-15 12:01:54,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22163.75, 'new_value': 24023.82}, {'field': 'total_amount', 'old_value': 22163.75, 'new_value': 24023.82}, {'field': 'order_count', 'old_value': 90, 'new_value': 97}]
2025-05-15 12:01:54,276 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-15 12:01:54,714 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-15 12:01:54,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28440.43, 'new_value': 30787.56}, {'field': 'offline_amount', 'old_value': 18282.49, 'new_value': 19099.19}, {'field': 'total_amount', 'old_value': 46722.92, 'new_value': 49886.75}, {'field': 'order_count', 'old_value': 2564, 'new_value': 2740}]
2025-05-15 12:01:54,714 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-15 12:01:55,182 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-15 12:01:55,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 611.0, 'new_value': 4496.0}, {'field': 'total_amount', 'old_value': 43212.0, 'new_value': 47097.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-15 12:01:55,182 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-15 12:01:55,620 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-15 12:01:55,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63255.0, 'new_value': 63375.0}, {'field': 'total_amount', 'old_value': 72629.53, 'new_value': 72749.53}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-15 12:01:55,620 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-15 12:01:56,073 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-15 12:01:56,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81069.74, 'new_value': 87344.74}, {'field': 'total_amount', 'old_value': 94278.46, 'new_value': 100553.46}, {'field': 'order_count', 'old_value': 5371, 'new_value': 5720}]
2025-05-15 12:01:56,073 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-15 12:01:56,510 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-15 12:01:56,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18884.0, 'new_value': 19755.0}, {'field': 'total_amount', 'old_value': 18884.0, 'new_value': 19755.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 186}]
2025-05-15 12:01:56,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-15 12:01:56,964 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-15 12:01:56,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10032.18, 'new_value': 10367.18}, {'field': 'total_amount', 'old_value': 10032.18, 'new_value': 10367.18}, {'field': 'order_count', 'old_value': 94, 'new_value': 96}]
2025-05-15 12:01:56,964 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-15 12:01:57,417 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-15 12:01:57,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226207.59, 'new_value': 234364.75}, {'field': 'total_amount', 'old_value': 226207.59, 'new_value': 234364.75}, {'field': 'order_count', 'old_value': 812, 'new_value': 845}]
2025-05-15 12:01:57,417 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-15 12:01:57,839 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-15 12:01:57,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173233.5, 'new_value': 184281.0}, {'field': 'total_amount', 'old_value': 173233.5, 'new_value': 184281.0}, {'field': 'order_count', 'old_value': 4178, 'new_value': 4486}]
2025-05-15 12:01:57,839 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-15 12:01:58,292 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-15 12:01:58,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20412.66, 'new_value': 21955.59}, {'field': 'total_amount', 'old_value': 20412.66, 'new_value': 21955.59}, {'field': 'order_count', 'old_value': 2609, 'new_value': 2794}]
2025-05-15 12:01:58,292 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-15 12:01:58,729 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-15 12:01:58,729 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13610.1, 'new_value': 14631.57}, {'field': 'offline_amount', 'old_value': 17988.6, 'new_value': 18943.4}, {'field': 'total_amount', 'old_value': 31598.7, 'new_value': 33574.97}, {'field': 'order_count', 'old_value': 1381, 'new_value': 1476}]
2025-05-15 12:01:58,729 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-15 12:01:59,214 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-15 12:01:59,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45851.13, 'new_value': 47713.35}, {'field': 'offline_amount', 'old_value': 77564.71, 'new_value': 78735.58}, {'field': 'total_amount', 'old_value': 123415.84, 'new_value': 126448.93}, {'field': 'order_count', 'old_value': 1204, 'new_value': 1247}]
2025-05-15 12:01:59,214 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-15 12:01:59,682 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-15 12:01:59,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37818.0, 'new_value': 43139.0}, {'field': 'total_amount', 'old_value': 43019.0, 'new_value': 48340.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 143}]
2025-05-15 12:01:59,682 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-15 12:02:00,135 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-15 12:02:00,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 866902.53, 'new_value': 938296.03}, {'field': 'offline_amount', 'old_value': 137798.3, 'new_value': 140363.3}, {'field': 'total_amount', 'old_value': 1004700.83, 'new_value': 1078659.33}, {'field': 'order_count', 'old_value': 3525, 'new_value': 3796}]
2025-05-15 12:02:00,135 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-15 12:02:00,557 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-15 12:02:00,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16632.0, 'new_value': 17732.0}, {'field': 'total_amount', 'old_value': 18008.0, 'new_value': 19108.0}, {'field': 'order_count', 'old_value': 1964, 'new_value': 1965}]
2025-05-15 12:02:00,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-15 12:02:01,042 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-15 12:02:01,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171937.2, 'new_value': 173016.6}, {'field': 'total_amount', 'old_value': 171937.2, 'new_value': 173016.6}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-15 12:02:01,042 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-15 12:02:01,557 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-15 12:02:01,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5594.0, 'new_value': 5892.0}, {'field': 'total_amount', 'old_value': 5594.0, 'new_value': 5892.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 43}]
2025-05-15 12:02:01,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-15 12:02:01,979 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-15 12:02:01,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258248.04, 'new_value': 271343.96}, {'field': 'total_amount', 'old_value': 258248.04, 'new_value': 271343.96}, {'field': 'order_count', 'old_value': 1287, 'new_value': 1360}]
2025-05-15 12:02:01,979 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-15 12:02:02,401 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-15 12:02:02,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 516.0, 'new_value': 828.0}, {'field': 'total_amount', 'old_value': 6245.6, 'new_value': 6557.6}, {'field': 'order_count', 'old_value': 63, 'new_value': 67}]
2025-05-15 12:02:02,401 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-15 12:02:02,901 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-15 12:02:02,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18460.0, 'new_value': 18731.0}, {'field': 'total_amount', 'old_value': 18460.0, 'new_value': 18731.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 81}]
2025-05-15 12:02:02,901 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-15 12:02:03,339 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-15 12:02:03,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45590.0, 'new_value': 49310.0}, {'field': 'total_amount', 'old_value': 45590.0, 'new_value': 49310.0}, {'field': 'order_count', 'old_value': 325, 'new_value': 344}]
2025-05-15 12:02:03,339 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-15 12:02:03,776 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-15 12:02:03,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273603.02, 'new_value': 284615.94}, {'field': 'total_amount', 'old_value': 273603.02, 'new_value': 284615.94}, {'field': 'order_count', 'old_value': 3642, 'new_value': 3830}]
2025-05-15 12:02:03,776 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-15 12:02:04,229 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-15 12:02:04,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6403.4, 'new_value': 6502.4}, {'field': 'total_amount', 'old_value': 12573.3, 'new_value': 12672.3}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-05-15 12:02:04,229 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-15 12:02:04,698 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-15 12:02:04,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329210.8, 'new_value': 349992.7}, {'field': 'total_amount', 'old_value': 329737.81, 'new_value': 350519.71}, {'field': 'order_count', 'old_value': 767, 'new_value': 822}]
2025-05-15 12:02:04,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-15 12:02:05,151 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-15 12:02:05,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87890.0, 'new_value': 96750.0}, {'field': 'total_amount', 'old_value': 87890.0, 'new_value': 96750.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-15 12:02:05,151 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-15 12:02:05,682 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-15 12:02:05,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5099.4, 'new_value': 5509.4}, {'field': 'offline_amount', 'old_value': 17161.88, 'new_value': 17890.78}, {'field': 'total_amount', 'old_value': 22261.28, 'new_value': 23400.18}, {'field': 'order_count', 'old_value': 792, 'new_value': 829}]
2025-05-15 12:02:05,682 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-15 12:02:06,104 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-15 12:02:06,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 720372.0, 'new_value': 766259.0}, {'field': 'total_amount', 'old_value': 720372.0, 'new_value': 766259.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 89}]
2025-05-15 12:02:06,104 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-15 12:02:06,542 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-15 12:02:06,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27482.11, 'new_value': 30485.88}, {'field': 'offline_amount', 'old_value': 28839.61, 'new_value': 28903.59}, {'field': 'total_amount', 'old_value': 56321.72, 'new_value': 59389.47}, {'field': 'order_count', 'old_value': 180, 'new_value': 194}]
2025-05-15 12:02:06,542 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-15 12:02:07,073 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-15 12:02:07,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87327.32, 'new_value': 89598.12}, {'field': 'total_amount', 'old_value': 87327.32, 'new_value': 89598.12}, {'field': 'order_count', 'old_value': 2211, 'new_value': 2281}]
2025-05-15 12:02:07,073 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-15 12:02:07,573 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-15 12:02:07,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198006.85, 'new_value': 203648.11}, {'field': 'total_amount', 'old_value': 198006.85, 'new_value': 203648.11}, {'field': 'order_count', 'old_value': 1654, 'new_value': 1760}]
2025-05-15 12:02:07,573 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-15 12:02:08,057 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-15 12:02:08,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52435.0, 'new_value': 53497.0}, {'field': 'total_amount', 'old_value': 52435.0, 'new_value': 53497.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 253}]
2025-05-15 12:02:08,057 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-15 12:02:08,479 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-15 12:02:08,479 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4680.54, 'new_value': 5047.79}, {'field': 'offline_amount', 'old_value': 67267.8, 'new_value': 71098.1}, {'field': 'total_amount', 'old_value': 71948.34, 'new_value': 76145.89}, {'field': 'order_count', 'old_value': 4333, 'new_value': 4519}]
2025-05-15 12:02:08,479 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-15 12:02:08,963 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-15 12:02:08,963 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188254.12, 'new_value': 195370.52}, {'field': 'total_amount', 'old_value': 194024.12, 'new_value': 201140.52}, {'field': 'order_count', 'old_value': 1498, 'new_value': 1576}]
2025-05-15 12:02:08,963 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-15 12:02:09,417 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-15 12:02:09,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12010.0, 'new_value': 13276.0}, {'field': 'total_amount', 'old_value': 12010.0, 'new_value': 13276.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 66}]
2025-05-15 12:02:09,417 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-15 12:02:09,885 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-15 12:02:09,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11138.1, 'new_value': 11509.7}, {'field': 'total_amount', 'old_value': 11138.1, 'new_value': 11509.7}, {'field': 'order_count', 'old_value': 385, 'new_value': 403}]
2025-05-15 12:02:09,885 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-15 12:02:10,338 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-15 12:02:10,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10100.03, 'new_value': 10632.76}, {'field': 'offline_amount', 'old_value': 78962.41, 'new_value': 81935.17}, {'field': 'total_amount', 'old_value': 89062.44, 'new_value': 92567.93}, {'field': 'order_count', 'old_value': 2608, 'new_value': 2720}]
2025-05-15 12:02:10,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-15 12:02:10,838 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-15 12:02:10,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7465.7, 'new_value': 7520.6}, {'field': 'total_amount', 'old_value': 7894.7, 'new_value': 7949.6}, {'field': 'order_count', 'old_value': 115, 'new_value': 118}]
2025-05-15 12:02:10,838 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-15 12:02:11,276 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-15 12:02:11,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55321.9, 'new_value': 58486.1}, {'field': 'total_amount', 'old_value': 55321.9, 'new_value': 58486.1}, {'field': 'order_count', 'old_value': 2723, 'new_value': 2886}]
2025-05-15 12:02:11,276 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-15 12:02:11,776 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-15 12:02:11,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20494.6, 'new_value': 21559.1}, {'field': 'total_amount', 'old_value': 25134.1, 'new_value': 26198.6}, {'field': 'order_count', 'old_value': 277, 'new_value': 295}]
2025-05-15 12:02:11,792 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-15 12:02:12,213 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-15 12:02:12,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2479.0, 'new_value': 3027.0}, {'field': 'offline_amount', 'old_value': 20680.0, 'new_value': 20779.0}, {'field': 'total_amount', 'old_value': 23159.0, 'new_value': 23806.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 186}]
2025-05-15 12:02:12,213 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-15 12:02:12,682 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-15 12:02:12,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23846.5, 'new_value': 32270.5}, {'field': 'total_amount', 'old_value': 111213.71, 'new_value': 119637.71}, {'field': 'order_count', 'old_value': 4802, 'new_value': 5225}]
2025-05-15 12:02:12,682 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-15 12:02:13,151 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-15 12:02:13,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13827.0, 'new_value': 14003.0}, {'field': 'total_amount', 'old_value': 22074.0, 'new_value': 22250.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-05-15 12:02:13,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-15 12:02:13,588 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-15 12:02:13,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118369.0, 'new_value': 125483.0}, {'field': 'total_amount', 'old_value': 118369.0, 'new_value': 125483.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 142}]
2025-05-15 12:02:13,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-15 12:02:13,995 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-15 12:02:13,995 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44390.49, 'new_value': 48519.36}, {'field': 'offline_amount', 'old_value': 123382.17, 'new_value': 133771.4}, {'field': 'total_amount', 'old_value': 167772.66, 'new_value': 182290.76}, {'field': 'order_count', 'old_value': 7650, 'new_value': 8368}]
2025-05-15 12:02:13,995 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-15 12:02:14,463 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-15 12:02:14,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 491905.0, 'new_value': 525226.0}, {'field': 'total_amount', 'old_value': 491905.0, 'new_value': 525226.0}, {'field': 'order_count', 'old_value': 1027, 'new_value': 1108}]
2025-05-15 12:02:14,463 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-15 12:02:14,917 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-15 12:02:14,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15965.79, 'new_value': 17546.83}, {'field': 'total_amount', 'old_value': 15965.79, 'new_value': 17546.83}, {'field': 'order_count', 'old_value': 593, 'new_value': 652}]
2025-05-15 12:02:14,917 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-15 12:02:15,354 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-15 12:02:15,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8889.0, 'new_value': 9269.0}, {'field': 'total_amount', 'old_value': 8889.0, 'new_value': 9269.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-15 12:02:15,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-15 12:02:15,760 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-15 12:02:15,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133153.0, 'new_value': 137374.0}, {'field': 'total_amount', 'old_value': 133153.0, 'new_value': 137374.0}, {'field': 'order_count', 'old_value': 394, 'new_value': 411}]
2025-05-15 12:02:15,760 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-15 12:02:16,229 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-15 12:02:16,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33133.12, 'new_value': 35427.72}, {'field': 'offline_amount', 'old_value': 255320.26, 'new_value': 265472.66}, {'field': 'total_amount', 'old_value': 288453.38, 'new_value': 300900.38}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1324}]
2025-05-15 12:02:16,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-15 12:02:16,792 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-15 12:02:16,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 853.09, 'new_value': 870.06}, {'field': 'offline_amount', 'old_value': 15996.77, 'new_value': 16216.21}, {'field': 'total_amount', 'old_value': 16849.86, 'new_value': 17086.27}, {'field': 'order_count', 'old_value': 594, 'new_value': 603}]
2025-05-15 12:02:16,792 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-15 12:02:17,370 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-15 12:02:17,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 514422.0, 'new_value': 534772.0}, {'field': 'total_amount', 'old_value': 514422.0, 'new_value': 534772.0}, {'field': 'order_count', 'old_value': 2321, 'new_value': 2412}]
2025-05-15 12:02:17,370 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-15 12:02:17,854 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-15 12:02:17,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8689.0, 'new_value': 11689.0}, {'field': 'total_amount', 'old_value': 8689.0, 'new_value': 11689.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-15 12:02:17,854 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-15 12:02:18,354 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-15 12:02:18,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3738.92, 'new_value': 4030.32}, {'field': 'offline_amount', 'old_value': 227382.94, 'new_value': 235876.54}, {'field': 'total_amount', 'old_value': 231121.86, 'new_value': 239906.86}, {'field': 'order_count', 'old_value': 10368, 'new_value': 10802}]
2025-05-15 12:02:18,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-15 12:02:18,792 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-15 12:02:18,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26352.02, 'new_value': 27952.42}, {'field': 'offline_amount', 'old_value': 185119.6, 'new_value': 196259.4}, {'field': 'total_amount', 'old_value': 211471.62, 'new_value': 224211.82}, {'field': 'order_count', 'old_value': 1309, 'new_value': 1380}]
2025-05-15 12:02:18,792 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-15 12:02:19,213 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-15 12:02:19,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107113.55, 'new_value': 115629.55}, {'field': 'total_amount', 'old_value': 107113.55, 'new_value': 115629.55}, {'field': 'order_count', 'old_value': 587, 'new_value': 635}]
2025-05-15 12:02:19,213 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-15 12:02:19,667 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-15 12:02:19,667 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55105.0, 'new_value': 56005.0}, {'field': 'total_amount', 'old_value': 55105.0, 'new_value': 56005.0}, {'field': 'order_count', 'old_value': 1635, 'new_value': 1662}]
2025-05-15 12:02:19,667 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-15 12:02:20,120 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-15 12:02:20,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201413.46, 'new_value': 211870.91}, {'field': 'total_amount', 'old_value': 223575.84, 'new_value': 234033.29}, {'field': 'order_count', 'old_value': 9255, 'new_value': 9727}]
2025-05-15 12:02:20,120 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-15 12:02:20,729 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-15 12:02:20,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233997.47, 'new_value': 243616.26}, {'field': 'total_amount', 'old_value': 233997.47, 'new_value': 243616.26}, {'field': 'order_count', 'old_value': 1548, 'new_value': 1639}]
2025-05-15 12:02:20,729 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-15 12:02:21,213 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-15 12:02:21,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67718.0, 'new_value': 68723.0}, {'field': 'total_amount', 'old_value': 82922.0, 'new_value': 83927.0}, {'field': 'order_count', 'old_value': 1851, 'new_value': 1876}]
2025-05-15 12:02:21,213 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-15 12:02:21,698 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-15 12:02:21,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58553.0, 'new_value': 62053.0}, {'field': 'total_amount', 'old_value': 58553.0, 'new_value': 62053.0}, {'field': 'order_count', 'old_value': 375, 'new_value': 376}]
2025-05-15 12:02:21,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-15 12:02:22,151 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-15 12:02:22,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101090.92, 'new_value': 106096.37}, {'field': 'total_amount', 'old_value': 101090.92, 'new_value': 106096.37}, {'field': 'order_count', 'old_value': 1317, 'new_value': 1399}]
2025-05-15 12:02:22,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-15 12:02:22,588 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-15 12:02:22,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15706.0, 'new_value': 16705.0}, {'field': 'total_amount', 'old_value': 15706.0, 'new_value': 16705.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-15 12:02:22,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-15 12:02:23,073 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-15 12:02:23,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94461.0, 'new_value': 96568.0}, {'field': 'total_amount', 'old_value': 94461.0, 'new_value': 96568.0}, {'field': 'order_count', 'old_value': 3031, 'new_value': 3096}]
2025-05-15 12:02:23,073 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-15 12:02:23,510 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-15 12:02:23,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51528.36, 'new_value': 56984.86}, {'field': 'offline_amount', 'old_value': 239682.48, 'new_value': 242800.28}, {'field': 'total_amount', 'old_value': 291210.84, 'new_value': 299785.14}, {'field': 'order_count', 'old_value': 2071, 'new_value': 2205}]
2025-05-15 12:02:23,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-15 12:02:23,963 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-15 12:02:23,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68350.34, 'new_value': 158162.46}, {'field': 'total_amount', 'old_value': 68350.34, 'new_value': 158162.46}, {'field': 'order_count', 'old_value': 22, 'new_value': 28}]
2025-05-15 12:02:23,963 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-15 12:02:24,432 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-15 12:02:24,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189100.0, 'new_value': 192700.0}, {'field': 'total_amount', 'old_value': 189100.0, 'new_value': 192700.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-15 12:02:24,432 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-15 12:02:24,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-15 12:02:24,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47187.92, 'new_value': 52423.43}, {'field': 'offline_amount', 'old_value': 59425.7, 'new_value': 62886.61}, {'field': 'total_amount', 'old_value': 106613.62, 'new_value': 115310.04}, {'field': 'order_count', 'old_value': 4313, 'new_value': 4640}]
2025-05-15 12:02:24,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-15 12:02:25,385 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-15 12:02:25,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49106.04, 'new_value': 56426.18}, {'field': 'total_amount', 'old_value': 207069.1, 'new_value': 214389.24}, {'field': 'order_count', 'old_value': 363, 'new_value': 377}]
2025-05-15 12:02:25,385 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-15 12:02:25,776 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-15 12:02:25,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135894.31, 'new_value': 140491.14}, {'field': 'total_amount', 'old_value': 155067.74, 'new_value': 159664.57}, {'field': 'order_count', 'old_value': 3264, 'new_value': 3357}]
2025-05-15 12:02:25,776 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-15 12:02:26,167 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-15 12:02:26,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32357.0, 'new_value': 34774.0}, {'field': 'total_amount', 'old_value': 32357.0, 'new_value': 34774.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 71}]
2025-05-15 12:02:26,167 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-15 12:02:26,682 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-15 12:02:26,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50206.0, 'new_value': 52846.0}, {'field': 'total_amount', 'old_value': 50206.0, 'new_value': 52846.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-15 12:02:26,682 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-15 12:02:27,120 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-15 12:02:27,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217325.7, 'new_value': 220035.3}, {'field': 'total_amount', 'old_value': 217325.7, 'new_value': 220035.3}, {'field': 'order_count', 'old_value': 267, 'new_value': 274}]
2025-05-15 12:02:27,120 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-15 12:02:27,557 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-15 12:02:27,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392117.58, 'new_value': 406562.58}, {'field': 'total_amount', 'old_value': 392117.58, 'new_value': 406562.58}, {'field': 'order_count', 'old_value': 2965, 'new_value': 3088}]
2025-05-15 12:02:27,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-15 12:02:27,979 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-15 12:02:27,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13976.0, 'new_value': 14676.0}, {'field': 'total_amount', 'old_value': 13976.0, 'new_value': 14676.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 78}]
2025-05-15 12:02:27,979 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-15 12:02:28,479 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-15 12:02:28,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45631.39, 'new_value': 47238.97}, {'field': 'total_amount', 'old_value': 45631.39, 'new_value': 47238.97}, {'field': 'order_count', 'old_value': 2607, 'new_value': 2714}]
2025-05-15 12:02:28,495 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-15 12:02:28,963 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-15 12:02:28,963 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31252.23, 'new_value': 32945.23}, {'field': 'offline_amount', 'old_value': 27420.33, 'new_value': 28846.33}, {'field': 'total_amount', 'old_value': 58672.56, 'new_value': 61791.56}, {'field': 'order_count', 'old_value': 1161, 'new_value': 1236}]
2025-05-15 12:02:28,963 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-15 12:02:29,463 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-15 12:02:29,463 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7942.45, 'new_value': 8513.45}, {'field': 'offline_amount', 'old_value': 150301.0, 'new_value': 156614.0}, {'field': 'total_amount', 'old_value': 158243.45, 'new_value': 165127.45}, {'field': 'order_count', 'old_value': 781, 'new_value': 827}]
2025-05-15 12:02:29,463 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-15 12:02:29,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-15 12:02:29,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81840.0, 'new_value': 91640.0}, {'field': 'total_amount', 'old_value': 81840.0, 'new_value': 91640.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-15 12:02:29,901 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-15 12:02:30,338 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-15 12:02:30,338 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-15 12:02:30,338 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-15 12:02:30,854 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-15 12:02:30,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106305.5, 'new_value': 113413.0}, {'field': 'total_amount', 'old_value': 106305.5, 'new_value': 113413.0}, {'field': 'order_count', 'old_value': 1355, 'new_value': 1455}]
2025-05-15 12:02:30,854 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-15 12:02:31,307 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-15 12:02:31,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19658.2, 'new_value': 20251.2}, {'field': 'total_amount', 'old_value': 19658.2, 'new_value': 20251.2}, {'field': 'order_count', 'old_value': 112, 'new_value': 117}]
2025-05-15 12:02:31,307 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-15 12:02:31,807 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-15 12:02:31,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27716.83, 'new_value': 52213.83}, {'field': 'total_amount', 'old_value': 27716.83, 'new_value': 52213.83}, {'field': 'order_count', 'old_value': 75, 'new_value': 81}]
2025-05-15 12:02:31,807 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-15 12:02:32,229 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-15 12:02:32,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22558.04, 'new_value': 24074.14}, {'field': 'offline_amount', 'old_value': 629167.83, 'new_value': 666478.09}, {'field': 'total_amount', 'old_value': 651725.87, 'new_value': 690552.23}, {'field': 'order_count', 'old_value': 2936, 'new_value': 3201}]
2025-05-15 12:02:32,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-15 12:02:32,745 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-15 12:02:32,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170394.0, 'new_value': 183284.0}, {'field': 'total_amount', 'old_value': 184169.0, 'new_value': 197059.0}, {'field': 'order_count', 'old_value': 3778, 'new_value': 4098}]
2025-05-15 12:02:32,745 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-15 12:02:33,182 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-15 12:02:33,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42850.58, 'new_value': 44841.72}, {'field': 'offline_amount', 'old_value': 115036.26, 'new_value': 118679.38}, {'field': 'total_amount', 'old_value': 157886.84, 'new_value': 163521.1}, {'field': 'order_count', 'old_value': 2656, 'new_value': 2832}]
2025-05-15 12:02:33,182 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-15 12:02:33,698 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-15 12:02:33,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161183.0, 'new_value': 171920.0}, {'field': 'total_amount', 'old_value': 165283.0, 'new_value': 176020.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 118}]
2025-05-15 12:02:33,698 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-15 12:02:34,135 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-15 12:02:34,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31005.85, 'new_value': 33827.85}, {'field': 'total_amount', 'old_value': 31005.85, 'new_value': 33827.85}, {'field': 'order_count', 'old_value': 118, 'new_value': 126}]
2025-05-15 12:02:34,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-15 12:02:34,682 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-15 12:02:34,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87056.0, 'new_value': 94764.0}, {'field': 'total_amount', 'old_value': 87056.0, 'new_value': 94764.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-15 12:02:34,682 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-15 12:02:35,135 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-15 12:02:35,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 413767.75, 'new_value': 432086.8}, {'field': 'total_amount', 'old_value': 413767.75, 'new_value': 432086.8}, {'field': 'order_count', 'old_value': 4675, 'new_value': 4970}]
2025-05-15 12:02:35,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-15 12:02:35,698 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-15 12:02:35,698 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143733.4, 'new_value': 145687.6}, {'field': 'offline_amount', 'old_value': 315141.0, 'new_value': 318709.0}, {'field': 'total_amount', 'old_value': 458874.4, 'new_value': 464396.6}, {'field': 'order_count', 'old_value': 3045, 'new_value': 3102}]
2025-05-15 12:02:35,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-15 12:02:36,135 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-15 12:02:36,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121539.0, 'new_value': 123119.0}, {'field': 'total_amount', 'old_value': 121539.0, 'new_value': 123119.0}, {'field': 'order_count', 'old_value': 1982, 'new_value': 2013}]
2025-05-15 12:02:36,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-15 12:02:36,666 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-15 12:02:36,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109248.35, 'new_value': 112932.61}, {'field': 'total_amount', 'old_value': 109248.35, 'new_value': 112932.61}, {'field': 'order_count', 'old_value': 4566, 'new_value': 4733}]
2025-05-15 12:02:36,666 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-15 12:02:37,088 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-15 12:02:37,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258690.0, 'new_value': 281376.0}, {'field': 'total_amount', 'old_value': 258690.0, 'new_value': 281376.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 265}]
2025-05-15 12:02:37,088 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-15 12:02:37,526 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-15 12:02:37,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151980.65, 'new_value': 158546.25}, {'field': 'offline_amount', 'old_value': 93880.31, 'new_value': 96584.11}, {'field': 'total_amount', 'old_value': 245860.96, 'new_value': 255130.36}, {'field': 'order_count', 'old_value': 2234, 'new_value': 2342}]
2025-05-15 12:02:37,526 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-15 12:02:37,963 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-15 12:02:37,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130136.65, 'new_value': 135397.3}, {'field': 'total_amount', 'old_value': 130136.65, 'new_value': 135397.3}, {'field': 'order_count', 'old_value': 960, 'new_value': 1011}]
2025-05-15 12:02:37,963 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-15 12:02:38,416 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-15 12:02:38,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55500.6, 'new_value': 57170.6}, {'field': 'total_amount', 'old_value': 57029.4, 'new_value': 58699.4}, {'field': 'order_count', 'old_value': 345, 'new_value': 350}]
2025-05-15 12:02:38,416 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-15 12:02:38,838 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-15 12:02:38,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19039.6, 'new_value': 21856.6}, {'field': 'total_amount', 'old_value': 94692.67, 'new_value': 97509.67}, {'field': 'order_count', 'old_value': 2709, 'new_value': 2796}]
2025-05-15 12:02:38,838 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-15 12:02:39,354 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-15 12:02:39,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46697.0, 'new_value': 49934.0}, {'field': 'offline_amount', 'old_value': 570903.0, 'new_value': 608866.0}, {'field': 'total_amount', 'old_value': 617600.0, 'new_value': 658800.0}, {'field': 'order_count', 'old_value': 14476, 'new_value': 15633}]
2025-05-15 12:02:39,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-15 12:02:39,760 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-15 12:02:39,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223353.46, 'new_value': 235271.92}, {'field': 'total_amount', 'old_value': 223353.46, 'new_value': 235271.92}, {'field': 'order_count', 'old_value': 754, 'new_value': 793}]
2025-05-15 12:02:39,760 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-15 12:02:40,260 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-15 12:02:40,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105339.0, 'new_value': 113647.0}, {'field': 'total_amount', 'old_value': 124968.0, 'new_value': 133276.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 138}]
2025-05-15 12:02:40,260 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-15 12:02:40,729 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-15 12:02:40,729 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17776.83, 'new_value': 19960.66}, {'field': 'offline_amount', 'old_value': 28257.1, 'new_value': 29257.1}, {'field': 'total_amount', 'old_value': 46033.93, 'new_value': 49217.76}, {'field': 'order_count', 'old_value': 2075, 'new_value': 2234}]
2025-05-15 12:02:40,729 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-15 12:02:41,135 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-15 12:02:41,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182815.0, 'new_value': 188043.0}, {'field': 'total_amount', 'old_value': 182815.0, 'new_value': 188043.0}, {'field': 'order_count', 'old_value': 259, 'new_value': 280}]
2025-05-15 12:02:41,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-15 12:02:41,557 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-15 12:02:41,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21888.0, 'new_value': 22988.0}, {'field': 'total_amount', 'old_value': 21888.0, 'new_value': 22988.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 123}]
2025-05-15 12:02:41,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-15 12:02:41,995 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-15 12:02:42,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28259.6, 'new_value': 28298.6}, {'field': 'total_amount', 'old_value': 38014.1, 'new_value': 38053.1}, {'field': 'order_count', 'old_value': 426, 'new_value': 427}]
2025-05-15 12:02:42,010 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-15 12:02:42,479 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-15 12:02:42,479 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54294.7, 'new_value': 59652.14}, {'field': 'offline_amount', 'old_value': 115852.82, 'new_value': 122181.75}, {'field': 'total_amount', 'old_value': 170147.52, 'new_value': 181833.89}, {'field': 'order_count', 'old_value': 5290, 'new_value': 5721}]
2025-05-15 12:02:42,479 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-15 12:02:42,979 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-15 12:02:42,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53678.0, 'new_value': 57276.0}, {'field': 'total_amount', 'old_value': 53678.0, 'new_value': 57276.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 247}]
2025-05-15 12:02:42,979 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-15 12:02:43,416 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-15 12:02:43,416 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86765.64, 'new_value': 93960.6}, {'field': 'offline_amount', 'old_value': 29878.45, 'new_value': 30648.28}, {'field': 'total_amount', 'old_value': 116644.09, 'new_value': 124608.88}, {'field': 'order_count', 'old_value': 6520, 'new_value': 6987}]
2025-05-15 12:02:43,416 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-15 12:02:43,838 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-15 12:02:43,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147809.92, 'new_value': 152932.58}, {'field': 'total_amount', 'old_value': 170297.32, 'new_value': 175419.98}, {'field': 'order_count', 'old_value': 917, 'new_value': 956}]
2025-05-15 12:02:43,838 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-15 12:02:44,338 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-15 12:02:44,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131852.87, 'new_value': 136077.61}, {'field': 'offline_amount', 'old_value': 237000.0, 'new_value': 241000.0}, {'field': 'total_amount', 'old_value': 368852.87, 'new_value': 377077.61}, {'field': 'order_count', 'old_value': 797, 'new_value': 831}]
2025-05-15 12:02:44,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-15 12:02:44,745 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-15 12:02:44,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98894.8, 'new_value': 103683.53}, {'field': 'total_amount', 'old_value': 98894.8, 'new_value': 103683.53}, {'field': 'order_count', 'old_value': 5019, 'new_value': 5219}]
2025-05-15 12:02:44,745 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-15 12:02:45,198 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-15 12:02:45,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87443.8, 'new_value': 92001.1}, {'field': 'total_amount', 'old_value': 87443.8, 'new_value': 92001.1}, {'field': 'order_count', 'old_value': 404, 'new_value': 428}]
2025-05-15 12:02:45,198 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-15 12:02:45,651 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-15 12:02:45,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91049.2, 'new_value': 92791.3}, {'field': 'total_amount', 'old_value': 91049.2, 'new_value': 92791.3}, {'field': 'order_count', 'old_value': 2508, 'new_value': 2556}]
2025-05-15 12:02:45,651 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-15 12:02:46,104 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-15 12:02:46,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4500.0, 'new_value': 5199.0}, {'field': 'total_amount', 'old_value': 12187.0, 'new_value': 12886.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 66}]
2025-05-15 12:02:46,104 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-15 12:02:46,651 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-15 12:02:46,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82873.57, 'new_value': 89155.2}, {'field': 'offline_amount', 'old_value': 163255.75, 'new_value': 170293.63}, {'field': 'total_amount', 'old_value': 246129.32, 'new_value': 259448.83}, {'field': 'order_count', 'old_value': 1953, 'new_value': 2084}]
2025-05-15 12:02:46,666 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-15 12:02:47,088 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-15 12:02:47,088 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60005.6, 'new_value': 63404.3}, {'field': 'total_amount', 'old_value': 60005.6, 'new_value': 63404.3}, {'field': 'order_count', 'old_value': 283, 'new_value': 298}]
2025-05-15 12:02:47,088 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-15 12:02:47,494 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-15 12:02:47,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22015.88, 'new_value': 23857.43}, {'field': 'offline_amount', 'old_value': 15628.45, 'new_value': 16615.17}, {'field': 'total_amount', 'old_value': 37644.33, 'new_value': 40472.6}, {'field': 'order_count', 'old_value': 1628, 'new_value': 1749}]
2025-05-15 12:02:47,494 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-15 12:02:47,932 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-15 12:02:47,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8923.66, 'new_value': 9301.05}, {'field': 'offline_amount', 'old_value': 21488.6, 'new_value': 21986.0}, {'field': 'total_amount', 'old_value': 30412.26, 'new_value': 31287.05}, {'field': 'order_count', 'old_value': 1201, 'new_value': 1246}]
2025-05-15 12:02:47,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-15 12:02:48,510 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-15 12:02:48,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12200.0, 'new_value': 18300.0}, {'field': 'total_amount', 'old_value': 12200.0, 'new_value': 18300.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-15 12:02:48,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-15 12:02:48,932 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-15 12:02:48,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42399.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 42399.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 1683, 'new_value': 1684}]
2025-05-15 12:02:48,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-15 12:02:49,369 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-15 12:02:49,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51315.33, 'new_value': 53223.62}, {'field': 'total_amount', 'old_value': 52575.76, 'new_value': 54484.05}, {'field': 'order_count', 'old_value': 242, 'new_value': 251}]
2025-05-15 12:02:49,369 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-15 12:02:49,807 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-15 12:02:49,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 461166.94, 'new_value': 476835.92}, {'field': 'total_amount', 'old_value': 461166.94, 'new_value': 476835.92}, {'field': 'order_count', 'old_value': 3498, 'new_value': 3674}]
2025-05-15 12:02:49,807 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-15 12:02:50,213 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-15 12:02:50,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10460.0, 'new_value': 11013.1}, {'field': 'offline_amount', 'old_value': 100821.4, 'new_value': 106234.2}, {'field': 'total_amount', 'old_value': 111281.4, 'new_value': 117247.3}, {'field': 'order_count', 'old_value': 3385, 'new_value': 3613}]
2025-05-15 12:02:50,213 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-15 12:02:50,713 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-15 12:02:50,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244234.0, 'new_value': 377286.0}, {'field': 'total_amount', 'old_value': 244234.0, 'new_value': 377286.0}, {'field': 'order_count', 'old_value': 2087, 'new_value': 2179}]
2025-05-15 12:02:50,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-15 12:02:51,198 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-15 12:02:51,198 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30403.86, 'new_value': 32207.48}, {'field': 'offline_amount', 'old_value': 24565.76, 'new_value': 27112.43}, {'field': 'total_amount', 'old_value': 54969.62, 'new_value': 59319.91}, {'field': 'order_count', 'old_value': 2884, 'new_value': 3084}]
2025-05-15 12:02:51,198 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-15 12:02:51,588 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-15 12:02:51,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2025.0, 'new_value': 2113.0}, {'field': 'offline_amount', 'old_value': 18232.6, 'new_value': 18678.6}, {'field': 'total_amount', 'old_value': 20257.6, 'new_value': 20791.6}, {'field': 'order_count', 'old_value': 740, 'new_value': 761}]
2025-05-15 12:02:51,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-15 12:02:52,088 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-15 12:02:52,088 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51903.36, 'new_value': 56993.71}, {'field': 'offline_amount', 'old_value': 59768.51, 'new_value': 64377.16}, {'field': 'total_amount', 'old_value': 111671.87, 'new_value': 121370.87}, {'field': 'order_count', 'old_value': 2790, 'new_value': 3038}]
2025-05-15 12:02:52,088 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-15 12:02:52,526 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-15 12:02:52,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52890.0, 'new_value': 62130.0}, {'field': 'total_amount', 'old_value': 52890.0, 'new_value': 62130.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-15 12:02:52,526 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-15 12:02:52,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-15 12:02:52,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 531556.0, 'new_value': 565860.0}, {'field': 'total_amount', 'old_value': 531556.0, 'new_value': 565860.0}, {'field': 'order_count', 'old_value': 622, 'new_value': 662}]
2025-05-15 12:02:52,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-15 12:02:53,338 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-15 12:02:53,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108388.8, 'new_value': 111042.6}, {'field': 'total_amount', 'old_value': 114339.1, 'new_value': 116992.9}, {'field': 'order_count', 'old_value': 224, 'new_value': 231}]
2025-05-15 12:02:53,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-15 12:02:53,744 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-15 12:02:53,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23559.15, 'new_value': 24527.75}, {'field': 'offline_amount', 'old_value': 61835.0, 'new_value': 65035.0}, {'field': 'total_amount', 'old_value': 85394.15, 'new_value': 89562.75}, {'field': 'order_count', 'old_value': 907, 'new_value': 952}]
2025-05-15 12:02:53,744 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-15 12:02:54,151 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-15 12:02:54,151 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72883.0, 'new_value': 76742.0}, {'field': 'offline_amount', 'old_value': 53892.0, 'new_value': 56111.0}, {'field': 'total_amount', 'old_value': 126775.0, 'new_value': 132853.0}, {'field': 'order_count', 'old_value': 1555, 'new_value': 1639}]
2025-05-15 12:02:54,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-15 12:02:54,573 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-15 12:02:54,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5729.4, 'new_value': 5818.3}, {'field': 'offline_amount', 'old_value': 11908.1, 'new_value': 12246.45}, {'field': 'total_amount', 'old_value': 17637.5, 'new_value': 18064.75}, {'field': 'order_count', 'old_value': 183, 'new_value': 188}]
2025-05-15 12:02:54,573 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-15 12:02:55,119 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-15 12:02:55,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66073.0, 'new_value': 66137.0}, {'field': 'total_amount', 'old_value': 72275.5, 'new_value': 72339.5}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-15 12:02:55,119 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-15 12:02:55,526 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-15 12:02:55,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49017.13, 'new_value': 56070.2}, {'field': 'total_amount', 'old_value': 56246.2, 'new_value': 63299.27}, {'field': 'order_count', 'old_value': 320, 'new_value': 354}]
2025-05-15 12:02:55,526 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-15 12:02:56,041 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-15 12:02:56,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15987.62, 'new_value': 17333.52}, {'field': 'offline_amount', 'old_value': 20532.6, 'new_value': 21490.6}, {'field': 'total_amount', 'old_value': 36520.22, 'new_value': 38824.12}, {'field': 'order_count', 'old_value': 145, 'new_value': 157}]
2025-05-15 12:02:56,041 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-15 12:02:56,526 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-15 12:02:56,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1165.0, 'new_value': 1223.0}, {'field': 'offline_amount', 'old_value': 34339.0, 'new_value': 35811.0}, {'field': 'total_amount', 'old_value': 35504.0, 'new_value': 37034.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 292}]
2025-05-15 12:02:56,526 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-15 12:02:57,010 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-15 12:02:57,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122857.5, 'new_value': 128773.5}, {'field': 'total_amount', 'old_value': 122857.5, 'new_value': 128773.5}, {'field': 'order_count', 'old_value': 614, 'new_value': 641}]
2025-05-15 12:02:57,010 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-15 12:02:57,510 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-15 12:02:57,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3177.0, 'new_value': 3305.0}, {'field': 'offline_amount', 'old_value': 11334.0, 'new_value': 12224.0}, {'field': 'total_amount', 'old_value': 14511.0, 'new_value': 15529.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 140}]
2025-05-15 12:02:57,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-15 12:02:57,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-15 12:02:57,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3247.0, 'new_value': 3687.0}, {'field': 'offline_amount', 'old_value': 24579.09, 'new_value': 26845.09}, {'field': 'total_amount', 'old_value': 27826.09, 'new_value': 30532.09}, {'field': 'order_count', 'old_value': 272, 'new_value': 293}]
2025-05-15 12:02:57,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-15 12:02:58,463 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-15 12:02:58,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92936.88, 'new_value': 98212.99}, {'field': 'total_amount', 'old_value': 92936.88, 'new_value': 98212.99}, {'field': 'order_count', 'old_value': 313, 'new_value': 344}]
2025-05-15 12:02:58,463 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-15 12:02:58,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-15 12:02:58,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10459.0, 'new_value': 12159.0}, {'field': 'total_amount', 'old_value': 10459.0, 'new_value': 12159.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-15 12:02:58,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-15 12:02:59,354 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-15 12:02:59,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40676.0, 'new_value': 42176.0}, {'field': 'offline_amount', 'old_value': 139906.0, 'new_value': 152038.0}, {'field': 'total_amount', 'old_value': 180582.0, 'new_value': 194214.0}, {'field': 'order_count', 'old_value': 808, 'new_value': 858}]
2025-05-15 12:02:59,354 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-15 12:02:59,776 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-15 12:02:59,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 650117.0, 'new_value': 670704.0}, {'field': 'total_amount', 'old_value': 650117.0, 'new_value': 670704.0}, {'field': 'order_count', 'old_value': 2778, 'new_value': 2861}]
2025-05-15 12:02:59,776 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-15 12:03:00,198 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-15 12:03:00,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8396794.0, 'new_value': 8688481.0}, {'field': 'total_amount', 'old_value': 8396794.0, 'new_value': 8688481.0}, {'field': 'order_count', 'old_value': 24938, 'new_value': 25990}]
2025-05-15 12:03:00,213 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-15 12:03:00,666 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-15 12:03:00,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2148144.99, 'new_value': 2260884.89}, {'field': 'total_amount', 'old_value': 2148144.99, 'new_value': 2260884.89}, {'field': 'order_count', 'old_value': 3586, 'new_value': 3800}]
2025-05-15 12:03:00,666 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-15 12:03:01,088 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-15 12:03:01,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81139.79, 'new_value': 88881.53}, {'field': 'total_amount', 'old_value': 88579.43, 'new_value': 96321.17}, {'field': 'order_count', 'old_value': 6124, 'new_value': 6714}]
2025-05-15 12:03:01,088 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-15 12:03:01,541 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-15 12:03:01,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161518.0, 'new_value': 178890.0}, {'field': 'total_amount', 'old_value': 161518.0, 'new_value': 178890.0}, {'field': 'order_count', 'old_value': 3402, 'new_value': 3797}]
2025-05-15 12:03:01,541 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-15 12:03:02,057 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-15 12:03:02,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137415.0, 'new_value': 140514.0}, {'field': 'total_amount', 'old_value': 137415.0, 'new_value': 140514.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 289}]
2025-05-15 12:03:02,057 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-15 12:03:02,510 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-15 12:03:02,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25045.69, 'new_value': 28581.18}, {'field': 'offline_amount', 'old_value': 39122.97, 'new_value': 38781.94}, {'field': 'total_amount', 'old_value': 64168.66, 'new_value': 67363.12}, {'field': 'order_count', 'old_value': 1343, 'new_value': 1532}]
2025-05-15 12:03:02,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-15 12:03:02,916 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-15 12:03:02,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22100.0, 'new_value': 25080.0}, {'field': 'total_amount', 'old_value': 22100.0, 'new_value': 25080.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-15 12:03:02,916 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-15 12:03:03,432 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-15 12:03:03,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96722.05, 'new_value': 105854.05}, {'field': 'total_amount', 'old_value': 173712.75, 'new_value': 182844.75}, {'field': 'order_count', 'old_value': 172, 'new_value': 188}]
2025-05-15 12:03:03,432 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-15 12:03:03,838 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-15 12:03:03,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214845.0, 'new_value': 218664.6}, {'field': 'total_amount', 'old_value': 214845.0, 'new_value': 218664.6}, {'field': 'order_count', 'old_value': 4643, 'new_value': 4722}]
2025-05-15 12:03:03,838 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-15 12:03:04,369 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-15 12:03:04,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29957.0, 'new_value': 32016.0}, {'field': 'total_amount', 'old_value': 29957.0, 'new_value': 32016.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 163}]
2025-05-15 12:03:04,369 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-15 12:03:04,823 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-15 12:03:04,823 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92962.0, 'new_value': 101907.0}, {'field': 'offline_amount', 'old_value': 71878.0, 'new_value': 85034.0}, {'field': 'total_amount', 'old_value': 164840.0, 'new_value': 186941.0}, {'field': 'order_count', 'old_value': 533, 'new_value': 561}]
2025-05-15 12:03:04,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-15 12:03:05,338 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-15 12:03:05,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68916.0, 'new_value': 76712.0}, {'field': 'total_amount', 'old_value': 68916.0, 'new_value': 76712.0}, {'field': 'order_count', 'old_value': 5041, 'new_value': 5609}]
2025-05-15 12:03:05,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-15 12:03:05,744 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-15 12:03:05,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25670.0, 'new_value': 25739.0}, {'field': 'total_amount', 'old_value': 25670.0, 'new_value': 25739.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-15 12:03:05,744 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-15 12:03:06,197 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-15 12:03:06,197 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166.6, 'new_value': 226.2}, {'field': 'offline_amount', 'old_value': 48618.2, 'new_value': 49584.2}, {'field': 'total_amount', 'old_value': 48784.8, 'new_value': 49810.4}, {'field': 'order_count', 'old_value': 750, 'new_value': 764}]
2025-05-15 12:03:06,197 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-15 12:03:06,682 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-15 12:03:06,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11178.1, 'new_value': 12636.3}, {'field': 'offline_amount', 'old_value': 31351.0, 'new_value': 33275.4}, {'field': 'total_amount', 'old_value': 42529.1, 'new_value': 45911.7}, {'field': 'order_count', 'old_value': 1576, 'new_value': 1698}]
2025-05-15 12:03:06,682 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-15 12:03:07,088 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-15 12:03:07,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19926.36, 'new_value': 21161.58}, {'field': 'total_amount', 'old_value': 19926.36, 'new_value': 21161.58}, {'field': 'order_count', 'old_value': 921, 'new_value': 986}]
2025-05-15 12:03:07,088 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-15 12:03:07,526 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-15 12:03:07,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38483.94, 'new_value': 41068.94}, {'field': 'total_amount', 'old_value': 38483.94, 'new_value': 41068.94}, {'field': 'order_count', 'old_value': 567, 'new_value': 667}]
2025-05-15 12:03:07,526 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-15 12:03:07,994 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-15 12:03:07,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2628495.39, 'new_value': 2800118.39}, {'field': 'total_amount', 'old_value': 2628495.39, 'new_value': 2800118.39}, {'field': 'order_count', 'old_value': 54545, 'new_value': 58107}]
2025-05-15 12:03:07,994 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-15 12:03:08,510 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-15 12:03:08,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14456.29, 'new_value': 15028.29}, {'field': 'total_amount', 'old_value': 14456.29, 'new_value': 15028.29}, {'field': 'order_count', 'old_value': 58, 'new_value': 61}]
2025-05-15 12:03:08,510 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-15 12:03:08,979 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-15 12:03:08,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332626.47, 'new_value': 343573.47}, {'field': 'total_amount', 'old_value': 332626.47, 'new_value': 343573.47}, {'field': 'order_count', 'old_value': 4231, 'new_value': 4338}]
2025-05-15 12:03:08,979 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-15 12:03:09,432 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-15 12:03:09,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95574.12, 'new_value': 104057.78}, {'field': 'total_amount', 'old_value': 95574.12, 'new_value': 104057.78}, {'field': 'order_count', 'old_value': 1890, 'new_value': 2045}]
2025-05-15 12:03:09,432 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-15 12:03:09,854 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-15 12:03:09,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164605.0, 'new_value': 179010.0}, {'field': 'total_amount', 'old_value': 164605.0, 'new_value': 179010.0}, {'field': 'order_count', 'old_value': 3574, 'new_value': 3898}]
2025-05-15 12:03:09,854 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-15 12:03:10,276 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-15 12:03:10,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20637.0, 'new_value': 22336.0}, {'field': 'total_amount', 'old_value': 20664.9, 'new_value': 22363.9}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-15 12:03:10,276 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-15 12:03:10,729 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-15 12:03:10,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172807.0, 'new_value': 240025.0}, {'field': 'total_amount', 'old_value': 172807.0, 'new_value': 240025.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 52}]
2025-05-15 12:03:10,729 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-15 12:03:11,260 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-15 12:03:11,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70968.0, 'new_value': 74710.4}, {'field': 'total_amount', 'old_value': 70968.0, 'new_value': 74710.4}, {'field': 'order_count', 'old_value': 1802, 'new_value': 1910}]
2025-05-15 12:03:11,260 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-15 12:03:11,666 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-15 12:03:11,666 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49257.23, 'new_value': 53611.93}, {'field': 'offline_amount', 'old_value': 257222.8, 'new_value': 262990.4}, {'field': 'total_amount', 'old_value': 306480.03, 'new_value': 316602.33}, {'field': 'order_count', 'old_value': 1936, 'new_value': 2052}]
2025-05-15 12:03:11,666 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-15 12:03:12,260 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-15 12:03:12,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28116.93, 'new_value': 31950.28}, {'field': 'total_amount', 'old_value': 46994.4, 'new_value': 50827.75}, {'field': 'order_count', 'old_value': 3037, 'new_value': 3290}]
2025-05-15 12:03:12,260 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-15 12:03:12,666 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-15 12:03:12,666 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45935.37, 'new_value': 53034.01}, {'field': 'total_amount', 'old_value': 76343.51, 'new_value': 83442.15}, {'field': 'order_count', 'old_value': 4989, 'new_value': 5444}]
2025-05-15 12:03:12,666 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-15 12:03:13,119 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-15 12:03:13,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 631030.38, 'new_value': 668368.99}, {'field': 'total_amount', 'old_value': 631030.38, 'new_value': 668368.99}, {'field': 'order_count', 'old_value': 1887, 'new_value': 2001}]
2025-05-15 12:03:13,119 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-15 12:03:13,557 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-15 12:03:13,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85884.0, 'new_value': 95024.0}, {'field': 'total_amount', 'old_value': 85884.0, 'new_value': 95024.0}, {'field': 'order_count', 'old_value': 2997, 'new_value': 3304}]
2025-05-15 12:03:13,557 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-15 12:03:13,994 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-15 12:03:13,994 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 0, 'new_value': 4}]
2025-05-15 12:03:13,994 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-15 12:03:14,447 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-15 12:03:14,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385086.01, 'new_value': 406563.65}, {'field': 'total_amount', 'old_value': 385086.01, 'new_value': 406563.65}, {'field': 'order_count', 'old_value': 1789, 'new_value': 1925}]
2025-05-15 12:03:14,447 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-15 12:03:14,854 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-15 12:03:14,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 592367.2, 'new_value': 632082.7}, {'field': 'total_amount', 'old_value': 592367.2, 'new_value': 632082.7}, {'field': 'order_count', 'old_value': 2076, 'new_value': 2203}]
2025-05-15 12:03:14,854 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-15 12:03:15,291 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-15 12:03:15,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 428343.26, 'new_value': 466025.06}, {'field': 'total_amount', 'old_value': 428343.26, 'new_value': 466025.06}, {'field': 'order_count', 'old_value': 1367, 'new_value': 1504}]
2025-05-15 12:03:15,291 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-15 12:03:15,713 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-15 12:03:15,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23247.02, 'new_value': 24416.02}, {'field': 'total_amount', 'old_value': 24257.92, 'new_value': 25426.92}, {'field': 'order_count', 'old_value': 180, 'new_value': 192}]
2025-05-15 12:03:15,713 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBG
2025-05-15 12:03:16,197 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBG
2025-05-15 12:03:16,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1288.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1288.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-15 12:03:16,197 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-15 12:03:16,666 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-15 12:03:16,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 197444.0, 'new_value': 214512.0}, {'field': 'total_amount', 'old_value': 198440.0, 'new_value': 215508.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-05-15 12:03:16,666 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-15 12:03:17,135 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-15 12:03:17,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177658.32, 'new_value': 184957.49}, {'field': 'total_amount', 'old_value': 177658.32, 'new_value': 184957.49}, {'field': 'order_count', 'old_value': 496, 'new_value': 515}]
2025-05-15 12:03:17,135 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-15 12:03:17,619 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-15 12:03:17,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30521.0, 'new_value': 32766.0}, {'field': 'total_amount', 'old_value': 39575.0, 'new_value': 41820.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-05-15 12:03:17,619 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-15 12:03:18,072 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-15 12:03:18,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63008.0, 'new_value': 69493.0}, {'field': 'offline_amount', 'old_value': 41088.0, 'new_value': 47081.0}, {'field': 'total_amount', 'old_value': 104096.0, 'new_value': 116574.0}, {'field': 'order_count', 'old_value': 4399, 'new_value': 4953}]
2025-05-15 12:03:18,088 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-15 12:03:18,526 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-15 12:03:18,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57469.0, 'new_value': 61138.0}, {'field': 'total_amount', 'old_value': 57469.0, 'new_value': 61138.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 294}]
2025-05-15 12:03:18,526 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-15 12:03:18,932 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-15 12:03:18,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72032.0, 'new_value': 81996.0}, {'field': 'total_amount', 'old_value': 72032.0, 'new_value': 81996.0}, {'field': 'order_count', 'old_value': 286, 'new_value': 320}]
2025-05-15 12:03:18,932 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-15 12:03:19,369 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-15 12:03:19,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115785.0, 'new_value': 126685.0}, {'field': 'total_amount', 'old_value': 115785.0, 'new_value': 126685.0}, {'field': 'order_count', 'old_value': 278, 'new_value': 306}]
2025-05-15 12:03:19,369 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-15 12:03:19,776 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-15 12:03:19,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3288.0}, {'field': 'total_amount', 'old_value': 15588.0, 'new_value': 18876.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-15 12:03:19,776 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-15 12:03:20,213 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-15 12:03:20,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24848.0, 'new_value': 27561.0}, {'field': 'total_amount', 'old_value': 24848.0, 'new_value': 27561.0}, {'field': 'order_count', 'old_value': 476, 'new_value': 526}]
2025-05-15 12:03:20,213 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-15 12:03:20,651 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-15 12:03:20,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89807.0, 'new_value': 100065.0}, {'field': 'total_amount', 'old_value': 89807.0, 'new_value': 100065.0}, {'field': 'order_count', 'old_value': 9397, 'new_value': 10550}]
2025-05-15 12:03:20,651 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-15 12:03:21,088 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-15 12:03:21,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72024.0, 'new_value': 77928.0}, {'field': 'total_amount', 'old_value': 72024.0, 'new_value': 77928.0}, {'field': 'order_count', 'old_value': 631, 'new_value': 667}]
2025-05-15 12:03:21,088 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-15 12:03:21,541 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-15 12:03:21,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90130.24, 'new_value': 92130.24}, {'field': 'total_amount', 'old_value': 90130.24, 'new_value': 92130.24}, {'field': 'order_count', 'old_value': 732, 'new_value': 733}]
2025-05-15 12:03:21,541 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-15 12:03:21,979 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-15 12:03:21,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2420.0, 'new_value': 2798.0}, {'field': 'total_amount', 'old_value': 2420.0, 'new_value': 2798.0}, {'field': 'order_count', 'old_value': 584, 'new_value': 585}]
2025-05-15 12:03:21,979 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-15 12:03:22,416 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-15 12:03:22,416 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12511.0, 'new_value': 15471.0}, {'field': 'offline_amount', 'old_value': 35293.0, 'new_value': 35549.0}, {'field': 'total_amount', 'old_value': 47804.0, 'new_value': 51020.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 72}]
2025-05-15 12:03:22,416 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-15 12:03:22,916 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-15 12:03:22,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29064.46, 'new_value': 30427.46}, {'field': 'total_amount', 'old_value': 29064.46, 'new_value': 30427.46}, {'field': 'order_count', 'old_value': 462, 'new_value': 493}]
2025-05-15 12:03:22,916 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-15 12:03:23,307 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-15 12:03:23,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74625.83, 'new_value': 77821.69}, {'field': 'offline_amount', 'old_value': 360991.38, 'new_value': 389790.95}, {'field': 'total_amount', 'old_value': 435617.21, 'new_value': 467612.64}, {'field': 'order_count', 'old_value': 985, 'new_value': 1054}]
2025-05-15 12:03:23,307 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-15 12:03:23,807 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-15 12:03:23,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35555.6, 'new_value': 41912.6}, {'field': 'total_amount', 'old_value': 35555.6, 'new_value': 41912.6}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-15 12:03:23,807 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-15 12:03:24,307 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-15 12:03:24,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43730.62, 'new_value': 48098.73}, {'field': 'offline_amount', 'old_value': 423366.49, 'new_value': 451882.86}, {'field': 'total_amount', 'old_value': 465222.78, 'new_value': 498107.26}, {'field': 'order_count', 'old_value': 2230, 'new_value': 2433}]
2025-05-15 12:03:24,307 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-15 12:03:24,744 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-15 12:03:24,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53771.0, 'new_value': 59309.0}, {'field': 'total_amount', 'old_value': 53771.0, 'new_value': 59309.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 200}]
2025-05-15 12:03:24,744 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-15 12:03:25,229 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-15 12:03:25,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1557.95, 'new_value': 1780.95}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3000.0}, {'field': 'total_amount', 'old_value': 1557.95, 'new_value': 4780.95}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-15 12:03:25,229 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-15 12:03:25,650 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-15 12:03:25,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23481.0, 'new_value': 47182.0}, {'field': 'total_amount', 'old_value': 28799.0, 'new_value': 52500.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 25}]
2025-05-15 12:03:25,650 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-15 12:03:26,182 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-15 12:03:26,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94807.17, 'new_value': 101173.47}, {'field': 'total_amount', 'old_value': 94807.17, 'new_value': 101173.47}, {'field': 'order_count', 'old_value': 9787, 'new_value': 10536}]
2025-05-15 12:03:26,182 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-15 12:03:26,682 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-15 12:03:26,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12717.0, 'new_value': 13427.0}, {'field': 'total_amount', 'old_value': 12717.0, 'new_value': 13427.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 79}]
2025-05-15 12:03:26,682 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-15 12:03:27,150 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-15 12:03:27,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13006.96, 'new_value': 16474.08}, {'field': 'total_amount', 'old_value': 13006.96, 'new_value': 16474.08}, {'field': 'order_count', 'old_value': 77, 'new_value': 103}]
2025-05-15 12:03:27,150 - INFO - 日期 2025-05 处理完成 - 更新: 389 条，插入: 0 条，错误: 0 条
2025-05-15 12:03:27,150 - INFO - 数据同步完成！更新: 389 条，插入: 0 条，错误: 0 条
2025-05-15 12:03:27,150 - INFO - =================同步完成====================
2025-05-15 15:00:01,886 - INFO - =================使用默认全量同步=============
2025-05-15 15:00:03,245 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 15:00:03,245 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 15:00:03,277 - INFO - 开始处理日期: 2025-01
2025-05-15 15:00:03,277 - INFO - Request Parameters - Page 1:
2025-05-15 15:00:03,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:03,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:04,324 - INFO - Response - Page 1:
2025-05-15 15:00:04,527 - INFO - 第 1 页获取到 100 条记录
2025-05-15 15:00:04,527 - INFO - Request Parameters - Page 2:
2025-05-15 15:00:04,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:04,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:05,292 - INFO - Response - Page 2:
2025-05-15 15:00:05,495 - INFO - 第 2 页获取到 100 条记录
2025-05-15 15:00:05,495 - INFO - Request Parameters - Page 3:
2025-05-15 15:00:05,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:05,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:06,011 - INFO - Response - Page 3:
2025-05-15 15:00:06,214 - INFO - 第 3 页获取到 100 条记录
2025-05-15 15:00:06,214 - INFO - Request Parameters - Page 4:
2025-05-15 15:00:06,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:06,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:06,745 - INFO - Response - Page 4:
2025-05-15 15:00:06,949 - INFO - 第 4 页获取到 100 条记录
2025-05-15 15:00:06,949 - INFO - Request Parameters - Page 5:
2025-05-15 15:00:06,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:06,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:07,386 - INFO - Response - Page 5:
2025-05-15 15:00:07,589 - INFO - 第 5 页获取到 100 条记录
2025-05-15 15:00:07,589 - INFO - Request Parameters - Page 6:
2025-05-15 15:00:07,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:07,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:08,073 - INFO - Response - Page 6:
2025-05-15 15:00:08,277 - INFO - 第 6 页获取到 100 条记录
2025-05-15 15:00:08,277 - INFO - Request Parameters - Page 7:
2025-05-15 15:00:08,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:08,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:08,698 - INFO - Response - Page 7:
2025-05-15 15:00:08,902 - INFO - 第 7 页获取到 82 条记录
2025-05-15 15:00:08,902 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 15:00:08,902 - INFO - 获取到 682 条表单数据
2025-05-15 15:00:08,902 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 15:00:08,917 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 15:00:08,917 - INFO - 开始处理日期: 2025-02
2025-05-15 15:00:08,917 - INFO - Request Parameters - Page 1:
2025-05-15 15:00:08,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:08,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:09,448 - INFO - Response - Page 1:
2025-05-15 15:00:09,652 - INFO - 第 1 页获取到 100 条记录
2025-05-15 15:00:09,652 - INFO - Request Parameters - Page 2:
2025-05-15 15:00:09,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:09,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:10,120 - INFO - Response - Page 2:
2025-05-15 15:00:10,323 - INFO - 第 2 页获取到 100 条记录
2025-05-15 15:00:10,323 - INFO - Request Parameters - Page 3:
2025-05-15 15:00:10,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:10,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:10,808 - INFO - Response - Page 3:
2025-05-15 15:00:11,011 - INFO - 第 3 页获取到 100 条记录
2025-05-15 15:00:11,011 - INFO - Request Parameters - Page 4:
2025-05-15 15:00:11,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:11,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:11,495 - INFO - Response - Page 4:
2025-05-15 15:00:11,698 - INFO - 第 4 页获取到 100 条记录
2025-05-15 15:00:11,698 - INFO - Request Parameters - Page 5:
2025-05-15 15:00:11,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:11,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:12,167 - INFO - Response - Page 5:
2025-05-15 15:00:12,370 - INFO - 第 5 页获取到 100 条记录
2025-05-15 15:00:12,370 - INFO - Request Parameters - Page 6:
2025-05-15 15:00:12,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:12,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:12,792 - INFO - Response - Page 6:
2025-05-15 15:00:12,995 - INFO - 第 6 页获取到 100 条记录
2025-05-15 15:00:12,995 - INFO - Request Parameters - Page 7:
2025-05-15 15:00:12,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:12,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:13,402 - INFO - Response - Page 7:
2025-05-15 15:00:13,605 - INFO - 第 7 页获取到 70 条记录
2025-05-15 15:00:13,605 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 15:00:13,605 - INFO - 获取到 670 条表单数据
2025-05-15 15:00:13,605 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 15:00:13,620 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 15:00:13,620 - INFO - 开始处理日期: 2025-03
2025-05-15 15:00:13,620 - INFO - Request Parameters - Page 1:
2025-05-15 15:00:13,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:13,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:14,120 - INFO - Response - Page 1:
2025-05-15 15:00:14,323 - INFO - 第 1 页获取到 100 条记录
2025-05-15 15:00:14,323 - INFO - Request Parameters - Page 2:
2025-05-15 15:00:14,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:14,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:14,761 - INFO - Response - Page 2:
2025-05-15 15:00:14,964 - INFO - 第 2 页获取到 100 条记录
2025-05-15 15:00:14,964 - INFO - Request Parameters - Page 3:
2025-05-15 15:00:14,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:14,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:15,448 - INFO - Response - Page 3:
2025-05-15 15:00:15,652 - INFO - 第 3 页获取到 100 条记录
2025-05-15 15:00:15,652 - INFO - Request Parameters - Page 4:
2025-05-15 15:00:15,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:15,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:16,136 - INFO - Response - Page 4:
2025-05-15 15:00:16,339 - INFO - 第 4 页获取到 100 条记录
2025-05-15 15:00:16,339 - INFO - Request Parameters - Page 5:
2025-05-15 15:00:16,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:16,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:16,823 - INFO - Response - Page 5:
2025-05-15 15:00:17,027 - INFO - 第 5 页获取到 100 条记录
2025-05-15 15:00:17,027 - INFO - Request Parameters - Page 6:
2025-05-15 15:00:17,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:17,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:17,448 - INFO - Response - Page 6:
2025-05-15 15:00:17,652 - INFO - 第 6 页获取到 100 条记录
2025-05-15 15:00:17,652 - INFO - Request Parameters - Page 7:
2025-05-15 15:00:17,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:17,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:18,105 - INFO - Response - Page 7:
2025-05-15 15:00:18,323 - INFO - 第 7 页获取到 61 条记录
2025-05-15 15:00:18,323 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 15:00:18,323 - INFO - 获取到 661 条表单数据
2025-05-15 15:00:18,323 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 15:00:18,339 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 15:00:18,339 - INFO - 开始处理日期: 2025-04
2025-05-15 15:00:18,339 - INFO - Request Parameters - Page 1:
2025-05-15 15:00:18,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:18,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:18,870 - INFO - Response - Page 1:
2025-05-15 15:00:19,073 - INFO - 第 1 页获取到 100 条记录
2025-05-15 15:00:19,073 - INFO - Request Parameters - Page 2:
2025-05-15 15:00:19,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:19,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:19,620 - INFO - Response - Page 2:
2025-05-15 15:00:19,823 - INFO - 第 2 页获取到 100 条记录
2025-05-15 15:00:19,823 - INFO - Request Parameters - Page 3:
2025-05-15 15:00:19,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:19,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:20,308 - INFO - Response - Page 3:
2025-05-15 15:00:20,511 - INFO - 第 3 页获取到 100 条记录
2025-05-15 15:00:20,511 - INFO - Request Parameters - Page 4:
2025-05-15 15:00:20,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:20,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:20,980 - INFO - Response - Page 4:
2025-05-15 15:00:21,183 - INFO - 第 4 页获取到 100 条记录
2025-05-15 15:00:21,183 - INFO - Request Parameters - Page 5:
2025-05-15 15:00:21,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:21,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:21,792 - INFO - Response - Page 5:
2025-05-15 15:00:21,995 - INFO - 第 5 页获取到 100 条记录
2025-05-15 15:00:21,995 - INFO - Request Parameters - Page 6:
2025-05-15 15:00:21,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:21,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:22,495 - INFO - Response - Page 6:
2025-05-15 15:00:22,698 - INFO - 第 6 页获取到 100 条记录
2025-05-15 15:00:22,698 - INFO - Request Parameters - Page 7:
2025-05-15 15:00:22,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:22,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:23,073 - INFO - Response - Page 7:
2025-05-15 15:00:23,277 - INFO - 第 7 页获取到 54 条记录
2025-05-15 15:00:23,277 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 15:00:23,277 - INFO - 获取到 654 条表单数据
2025-05-15 15:00:23,277 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 15:00:23,277 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-05-15 15:00:23,917 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-05-15 15:00:23,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2850000.0, 'new_value': 2852008.85}, {'field': 'total_amount', 'old_value': 2850000.0, 'new_value': 2852008.85}]
2025-05-15 15:00:23,917 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-05-15 15:00:24,417 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-05-15 15:00:24,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355646.0, 'new_value': 255646.0}, {'field': 'total_amount', 'old_value': 355646.0, 'new_value': 255646.0}]
2025-05-15 15:00:24,433 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-15 15:00:24,433 - INFO - 开始处理日期: 2025-05
2025-05-15 15:00:24,433 - INFO - Request Parameters - Page 1:
2025-05-15 15:00:24,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:24,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:24,948 - INFO - Response - Page 1:
2025-05-15 15:00:25,152 - INFO - 第 1 页获取到 100 条记录
2025-05-15 15:00:25,152 - INFO - Request Parameters - Page 2:
2025-05-15 15:00:25,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:25,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:25,636 - INFO - Response - Page 2:
2025-05-15 15:00:25,839 - INFO - 第 2 页获取到 100 条记录
2025-05-15 15:00:25,839 - INFO - Request Parameters - Page 3:
2025-05-15 15:00:25,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:25,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:26,308 - INFO - Response - Page 3:
2025-05-15 15:00:26,511 - INFO - 第 3 页获取到 100 条记录
2025-05-15 15:00:26,511 - INFO - Request Parameters - Page 4:
2025-05-15 15:00:26,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:26,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:26,980 - INFO - Response - Page 4:
2025-05-15 15:00:27,183 - INFO - 第 4 页获取到 100 条记录
2025-05-15 15:00:27,183 - INFO - Request Parameters - Page 5:
2025-05-15 15:00:27,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:27,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:27,651 - INFO - Response - Page 5:
2025-05-15 15:00:27,855 - INFO - 第 5 页获取到 100 条记录
2025-05-15 15:00:27,855 - INFO - Request Parameters - Page 6:
2025-05-15 15:00:27,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:27,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:28,261 - INFO - Response - Page 6:
2025-05-15 15:00:28,464 - INFO - 第 6 页获取到 100 条记录
2025-05-15 15:00:28,464 - INFO - Request Parameters - Page 7:
2025-05-15 15:00:28,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 15:00:28,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 15:00:28,823 - INFO - Response - Page 7:
2025-05-15 15:00:29,026 - INFO - 第 7 页获取到 25 条记录
2025-05-15 15:00:29,026 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 15:00:29,026 - INFO - 获取到 625 条表单数据
2025-05-15 15:00:29,026 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 15:00:29,026 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-15 15:00:29,448 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-15 15:00:29,448 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3084.61, 'new_value': 3475.03}, {'field': 'offline_amount', 'old_value': 30674.96, 'new_value': 34891.7}, {'field': 'total_amount', 'old_value': 33759.57, 'new_value': 38366.73}, {'field': 'order_count', 'old_value': 1193, 'new_value': 1344}]
2025-05-15 15:00:29,448 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-15 15:00:29,870 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-15 15:00:29,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67354.54, 'new_value': 151615.04}, {'field': 'offline_amount', 'old_value': 10099.15, 'new_value': 10792.15}, {'field': 'total_amount', 'old_value': 77453.69, 'new_value': 162407.19}, {'field': 'order_count', 'old_value': 2776, 'new_value': 3026}]
2025-05-15 15:00:29,886 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-15 15:00:30,323 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-15 15:00:30,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52127.91, 'new_value': 58281.41}, {'field': 'offline_amount', 'old_value': 47023.66, 'new_value': 51228.99}, {'field': 'total_amount', 'old_value': 99151.57, 'new_value': 109510.4}, {'field': 'order_count', 'old_value': 3385, 'new_value': 3842}]
2025-05-15 15:00:30,323 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-15 15:00:30,823 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-15 15:00:30,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105598.58, 'new_value': 116006.23}, {'field': 'total_amount', 'old_value': 112514.24, 'new_value': 122921.89}, {'field': 'order_count', 'old_value': 2231, 'new_value': 2483}]
2025-05-15 15:00:30,823 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-15 15:00:31,245 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-15 15:00:31,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253595.75, 'new_value': 268630.79}, {'field': 'total_amount', 'old_value': 253595.75, 'new_value': 268630.79}, {'field': 'order_count', 'old_value': 2515, 'new_value': 2686}]
2025-05-15 15:00:31,261 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-15 15:00:31,636 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-15 15:00:31,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23597.07, 'new_value': 24840.58}, {'field': 'offline_amount', 'old_value': 132915.14, 'new_value': 141167.29}, {'field': 'total_amount', 'old_value': 156512.21, 'new_value': 166007.87}, {'field': 'order_count', 'old_value': 4751, 'new_value': 5097}]
2025-05-15 15:00:31,636 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-15 15:00:32,011 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-15 15:00:32,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54187.79, 'new_value': 58645.4}, {'field': 'total_amount', 'old_value': 54187.79, 'new_value': 58645.4}, {'field': 'order_count', 'old_value': 1505, 'new_value': 1628}]
2025-05-15 15:00:32,011 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-15 15:00:32,542 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-15 15:00:32,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95951.91, 'new_value': 106102.91}, {'field': 'total_amount', 'old_value': 95951.91, 'new_value': 106102.91}, {'field': 'order_count', 'old_value': 3971, 'new_value': 4433}]
2025-05-15 15:00:32,542 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-15 15:00:32,980 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-15 15:00:32,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16108.79, 'new_value': 17494.55}, {'field': 'offline_amount', 'old_value': 32934.18, 'new_value': 35607.36}, {'field': 'total_amount', 'old_value': 49042.97, 'new_value': 53101.91}, {'field': 'order_count', 'old_value': 416, 'new_value': 448}]
2025-05-15 15:00:32,980 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-15 15:00:33,417 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-15 15:00:33,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47204.0, 'new_value': 52755.0}, {'field': 'total_amount', 'old_value': 47204.0, 'new_value': 52755.0}, {'field': 'order_count', 'old_value': 1091, 'new_value': 1244}]
2025-05-15 15:00:33,417 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-15 15:00:33,901 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-15 15:00:33,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65182.36, 'new_value': 72611.56}, {'field': 'total_amount', 'old_value': 65190.36, 'new_value': 72619.56}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-15 15:00:33,917 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-15 15:00:34,339 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-15 15:00:34,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59705.0, 'new_value': 67055.0}, {'field': 'total_amount', 'old_value': 59785.0, 'new_value': 67135.0}, {'field': 'order_count', 'old_value': 6170, 'new_value': 7010}]
2025-05-15 15:00:34,339 - INFO - 日期 2025-05 处理完成 - 更新: 12 条，插入: 0 条，错误: 0 条
2025-05-15 15:00:34,339 - INFO - 数据同步完成！更新: 14 条，插入: 0 条，错误: 0 条
2025-05-15 15:00:34,339 - INFO - =================同步完成====================
2025-05-15 18:00:01,972 - INFO - =================使用默认全量同步=============
2025-05-15 18:00:03,316 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 18:00:03,316 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 18:00:03,347 - INFO - 开始处理日期: 2025-01
2025-05-15 18:00:03,347 - INFO - Request Parameters - Page 1:
2025-05-15 18:00:03,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:03,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:04,566 - INFO - Response - Page 1:
2025-05-15 18:00:04,769 - INFO - 第 1 页获取到 100 条记录
2025-05-15 18:00:04,769 - INFO - Request Parameters - Page 2:
2025-05-15 18:00:04,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:04,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:05,254 - INFO - Response - Page 2:
2025-05-15 18:00:05,457 - INFO - 第 2 页获取到 100 条记录
2025-05-15 18:00:05,457 - INFO - Request Parameters - Page 3:
2025-05-15 18:00:05,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:05,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:06,004 - INFO - Response - Page 3:
2025-05-15 18:00:06,207 - INFO - 第 3 页获取到 100 条记录
2025-05-15 18:00:06,207 - INFO - Request Parameters - Page 4:
2025-05-15 18:00:06,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:06,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:06,738 - INFO - Response - Page 4:
2025-05-15 18:00:06,941 - INFO - 第 4 页获取到 100 条记录
2025-05-15 18:00:06,941 - INFO - Request Parameters - Page 5:
2025-05-15 18:00:06,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:06,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:07,504 - INFO - Response - Page 5:
2025-05-15 18:00:07,707 - INFO - 第 5 页获取到 100 条记录
2025-05-15 18:00:07,707 - INFO - Request Parameters - Page 6:
2025-05-15 18:00:07,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:07,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:08,316 - INFO - Response - Page 6:
2025-05-15 18:00:08,519 - INFO - 第 6 页获取到 100 条记录
2025-05-15 18:00:08,519 - INFO - Request Parameters - Page 7:
2025-05-15 18:00:08,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:08,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:09,035 - INFO - Response - Page 7:
2025-05-15 18:00:09,238 - INFO - 第 7 页获取到 82 条记录
2025-05-15 18:00:09,238 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 18:00:09,238 - INFO - 获取到 682 条表单数据
2025-05-15 18:00:09,238 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 18:00:09,254 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 18:00:09,254 - INFO - 开始处理日期: 2025-02
2025-05-15 18:00:09,254 - INFO - Request Parameters - Page 1:
2025-05-15 18:00:09,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:09,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:09,785 - INFO - Response - Page 1:
2025-05-15 18:00:09,988 - INFO - 第 1 页获取到 100 条记录
2025-05-15 18:00:09,988 - INFO - Request Parameters - Page 2:
2025-05-15 18:00:09,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:09,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:10,519 - INFO - Response - Page 2:
2025-05-15 18:00:10,722 - INFO - 第 2 页获取到 100 条记录
2025-05-15 18:00:10,722 - INFO - Request Parameters - Page 3:
2025-05-15 18:00:10,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:10,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:11,254 - INFO - Response - Page 3:
2025-05-15 18:00:11,457 - INFO - 第 3 页获取到 100 条记录
2025-05-15 18:00:11,457 - INFO - Request Parameters - Page 4:
2025-05-15 18:00:11,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:11,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:11,972 - INFO - Response - Page 4:
2025-05-15 18:00:12,176 - INFO - 第 4 页获取到 100 条记录
2025-05-15 18:00:12,176 - INFO - Request Parameters - Page 5:
2025-05-15 18:00:12,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:12,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:12,675 - INFO - Response - Page 5:
2025-05-15 18:00:12,879 - INFO - 第 5 页获取到 100 条记录
2025-05-15 18:00:12,879 - INFO - Request Parameters - Page 6:
2025-05-15 18:00:12,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:12,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:13,394 - INFO - Response - Page 6:
2025-05-15 18:00:13,597 - INFO - 第 6 页获取到 100 条记录
2025-05-15 18:00:13,597 - INFO - Request Parameters - Page 7:
2025-05-15 18:00:13,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:13,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:14,035 - INFO - Response - Page 7:
2025-05-15 18:00:14,238 - INFO - 第 7 页获取到 70 条记录
2025-05-15 18:00:14,238 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 18:00:14,238 - INFO - 获取到 670 条表单数据
2025-05-15 18:00:14,238 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 18:00:14,254 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 18:00:14,254 - INFO - 开始处理日期: 2025-03
2025-05-15 18:00:14,254 - INFO - Request Parameters - Page 1:
2025-05-15 18:00:14,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:14,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:14,785 - INFO - Response - Page 1:
2025-05-15 18:00:14,988 - INFO - 第 1 页获取到 100 条记录
2025-05-15 18:00:14,988 - INFO - Request Parameters - Page 2:
2025-05-15 18:00:14,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:14,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:15,441 - INFO - Response - Page 2:
2025-05-15 18:00:15,644 - INFO - 第 2 页获取到 100 条记录
2025-05-15 18:00:15,644 - INFO - Request Parameters - Page 3:
2025-05-15 18:00:15,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:15,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:16,129 - INFO - Response - Page 3:
2025-05-15 18:00:16,332 - INFO - 第 3 页获取到 100 条记录
2025-05-15 18:00:16,332 - INFO - Request Parameters - Page 4:
2025-05-15 18:00:16,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:16,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:16,847 - INFO - Response - Page 4:
2025-05-15 18:00:17,050 - INFO - 第 4 页获取到 100 条记录
2025-05-15 18:00:17,050 - INFO - Request Parameters - Page 5:
2025-05-15 18:00:17,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:17,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:17,675 - INFO - Response - Page 5:
2025-05-15 18:00:17,879 - INFO - 第 5 页获取到 100 条记录
2025-05-15 18:00:17,879 - INFO - Request Parameters - Page 6:
2025-05-15 18:00:17,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:17,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:18,504 - INFO - Response - Page 6:
2025-05-15 18:00:18,707 - INFO - 第 6 页获取到 100 条记录
2025-05-15 18:00:18,707 - INFO - Request Parameters - Page 7:
2025-05-15 18:00:18,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:18,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:19,097 - INFO - Response - Page 7:
2025-05-15 18:00:19,300 - INFO - 第 7 页获取到 61 条记录
2025-05-15 18:00:19,300 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 18:00:19,300 - INFO - 获取到 661 条表单数据
2025-05-15 18:00:19,300 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 18:00:19,316 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 18:00:19,316 - INFO - 开始处理日期: 2025-04
2025-05-15 18:00:19,316 - INFO - Request Parameters - Page 1:
2025-05-15 18:00:19,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:19,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:19,863 - INFO - Response - Page 1:
2025-05-15 18:00:20,066 - INFO - 第 1 页获取到 100 条记录
2025-05-15 18:00:20,066 - INFO - Request Parameters - Page 2:
2025-05-15 18:00:20,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:20,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:20,550 - INFO - Response - Page 2:
2025-05-15 18:00:20,754 - INFO - 第 2 页获取到 100 条记录
2025-05-15 18:00:20,754 - INFO - Request Parameters - Page 3:
2025-05-15 18:00:20,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:20,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:21,222 - INFO - Response - Page 3:
2025-05-15 18:00:21,425 - INFO - 第 3 页获取到 100 条记录
2025-05-15 18:00:21,425 - INFO - Request Parameters - Page 4:
2025-05-15 18:00:21,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:21,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:21,879 - INFO - Response - Page 4:
2025-05-15 18:00:22,082 - INFO - 第 4 页获取到 100 条记录
2025-05-15 18:00:22,082 - INFO - Request Parameters - Page 5:
2025-05-15 18:00:22,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:22,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:22,566 - INFO - Response - Page 5:
2025-05-15 18:00:22,769 - INFO - 第 5 页获取到 100 条记录
2025-05-15 18:00:22,769 - INFO - Request Parameters - Page 6:
2025-05-15 18:00:22,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:22,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:23,363 - INFO - Response - Page 6:
2025-05-15 18:00:23,566 - INFO - 第 6 页获取到 100 条记录
2025-05-15 18:00:23,566 - INFO - Request Parameters - Page 7:
2025-05-15 18:00:23,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:23,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:24,097 - INFO - Response - Page 7:
2025-05-15 18:00:24,300 - INFO - 第 7 页获取到 54 条记录
2025-05-15 18:00:24,300 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 18:00:24,300 - INFO - 获取到 654 条表单数据
2025-05-15 18:00:24,300 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 18:00:24,300 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-05-15 18:00:24,816 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-05-15 18:00:24,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228632.5, 'new_value': 489321.27}, {'field': 'total_amount', 'old_value': 228632.5, 'new_value': 489321.27}]
2025-05-15 18:00:24,832 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-05-15 18:00:25,410 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-05-15 18:00:25,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31069.0, 'new_value': 79635.0}, {'field': 'total_amount', 'old_value': 31069.0, 'new_value': 79635.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 179}]
2025-05-15 18:00:25,425 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-15 18:00:25,425 - INFO - 开始处理日期: 2025-05
2025-05-15 18:00:25,425 - INFO - Request Parameters - Page 1:
2025-05-15 18:00:25,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:25,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:26,004 - INFO - Response - Page 1:
2025-05-15 18:00:26,207 - INFO - 第 1 页获取到 100 条记录
2025-05-15 18:00:26,207 - INFO - Request Parameters - Page 2:
2025-05-15 18:00:26,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:26,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:26,769 - INFO - Response - Page 2:
2025-05-15 18:00:26,972 - INFO - 第 2 页获取到 100 条记录
2025-05-15 18:00:26,972 - INFO - Request Parameters - Page 3:
2025-05-15 18:00:26,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:26,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:27,472 - INFO - Response - Page 3:
2025-05-15 18:00:27,675 - INFO - 第 3 页获取到 100 条记录
2025-05-15 18:00:27,675 - INFO - Request Parameters - Page 4:
2025-05-15 18:00:27,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:27,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:28,129 - INFO - Response - Page 4:
2025-05-15 18:00:28,332 - INFO - 第 4 页获取到 100 条记录
2025-05-15 18:00:28,332 - INFO - Request Parameters - Page 5:
2025-05-15 18:00:28,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:28,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:28,816 - INFO - Response - Page 5:
2025-05-15 18:00:29,019 - INFO - 第 5 页获取到 100 条记录
2025-05-15 18:00:29,019 - INFO - Request Parameters - Page 6:
2025-05-15 18:00:29,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:29,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:29,550 - INFO - Response - Page 6:
2025-05-15 18:00:29,754 - INFO - 第 6 页获取到 100 条记录
2025-05-15 18:00:29,754 - INFO - Request Parameters - Page 7:
2025-05-15 18:00:29,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 18:00:29,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 18:00:30,082 - INFO - Response - Page 7:
2025-05-15 18:00:30,285 - INFO - 第 7 页获取到 25 条记录
2025-05-15 18:00:30,285 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 18:00:30,285 - INFO - 获取到 625 条表单数据
2025-05-15 18:00:30,285 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 18:00:30,285 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-15 18:00:30,707 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-15 18:00:30,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401603.98, 'new_value': 427618.98}, {'field': 'total_amount', 'old_value': 401603.98, 'new_value': 427618.98}, {'field': 'order_count', 'old_value': 1195, 'new_value': 1264}]
2025-05-15 18:00:30,707 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-15 18:00:31,207 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-15 18:00:31,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19932.43, 'new_value': 22205.88}, {'field': 'total_amount', 'old_value': 19932.43, 'new_value': 22205.88}, {'field': 'order_count', 'old_value': 3785, 'new_value': 4241}]
2025-05-15 18:00:31,207 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-15 18:00:31,707 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-15 18:00:31,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32580.0, 'new_value': 36561.0}, {'field': 'total_amount', 'old_value': 81975.0, 'new_value': 85956.0}, {'field': 'order_count', 'old_value': 1006, 'new_value': 1072}]
2025-05-15 18:00:31,707 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-15 18:00:32,269 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-15 18:00:32,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28426.63, 'new_value': 32483.06}, {'field': 'total_amount', 'old_value': 83580.86, 'new_value': 87637.29}, {'field': 'order_count', 'old_value': 4699, 'new_value': 5028}]
2025-05-15 18:00:32,269 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-15 18:00:32,691 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-15 18:00:32,691 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3213.44, 'new_value': 4808.95}, {'field': 'offline_amount', 'old_value': 47770.67, 'new_value': 48112.15}, {'field': 'total_amount', 'old_value': 50984.11, 'new_value': 52921.1}, {'field': 'order_count', 'old_value': 2865, 'new_value': 2990}]
2025-05-15 18:00:32,691 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-15 18:00:33,128 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-15 18:00:33,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8411.0, 'new_value': 9601.0}, {'field': 'total_amount', 'old_value': 12583.0, 'new_value': 13773.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 83}]
2025-05-15 18:00:33,128 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-15 18:00:33,660 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-15 18:00:33,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102192.0, 'new_value': 109073.0}, {'field': 'total_amount', 'old_value': 102192.0, 'new_value': 109073.0}, {'field': 'order_count', 'old_value': 9323, 'new_value': 9466}]
2025-05-15 18:00:33,660 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-15 18:00:34,128 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-15 18:00:34,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6654.02, 'new_value': 7050.96}, {'field': 'offline_amount', 'old_value': 66551.84, 'new_value': 71109.23}, {'field': 'total_amount', 'old_value': 73205.86, 'new_value': 78160.19}, {'field': 'order_count', 'old_value': 1810, 'new_value': 1960}]
2025-05-15 18:00:34,128 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-15 18:00:34,550 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-15 18:00:34,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1656.7, 'new_value': 1776.2}, {'field': 'total_amount', 'old_value': 1656.7, 'new_value': 1776.2}, {'field': 'order_count', 'old_value': 154, 'new_value': 163}]
2025-05-15 18:00:34,550 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-15 18:00:35,035 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-15 18:00:35,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76659.3, 'new_value': 78017.3}, {'field': 'total_amount', 'old_value': 76659.3, 'new_value': 78017.3}, {'field': 'order_count', 'old_value': 790, 'new_value': 806}]
2025-05-15 18:00:35,035 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-15 18:00:35,519 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-15 18:00:35,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3507813.0, 'new_value': 3547856.0}, {'field': 'total_amount', 'old_value': 3507813.0, 'new_value': 3547856.0}, {'field': 'order_count', 'old_value': 55593, 'new_value': 59695}]
2025-05-15 18:00:35,519 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-15 18:00:36,003 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-15 18:00:36,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48711.74, 'new_value': 50530.74}, {'field': 'total_amount', 'old_value': 48711.74, 'new_value': 50530.74}, {'field': 'order_count', 'old_value': 1609, 'new_value': 1650}]
2025-05-15 18:00:36,003 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-15 18:00:36,457 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-15 18:00:36,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70721.0, 'new_value': 69336.0}, {'field': 'total_amount', 'old_value': 70721.0, 'new_value': 69336.0}, {'field': 'order_count', 'old_value': 2566, 'new_value': 2683}]
2025-05-15 18:00:36,457 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-15 18:00:36,957 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-15 18:00:36,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17376.07, 'new_value': 17524.07}, {'field': 'total_amount', 'old_value': 17376.07, 'new_value': 17524.07}, {'field': 'order_count', 'old_value': 1624, 'new_value': 1732}]
2025-05-15 18:00:36,957 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-15 18:00:37,472 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-15 18:00:37,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4610700.0, 'new_value': 4647200.0}, {'field': 'total_amount', 'old_value': 4610700.0, 'new_value': 4647200.0}]
2025-05-15 18:00:37,472 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-15 18:00:37,894 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-15 18:00:37,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185545.0, 'new_value': 188660.0}, {'field': 'total_amount', 'old_value': 194363.99, 'new_value': 197478.99}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-15 18:00:37,894 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-15 18:00:38,410 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-15 18:00:38,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216382.0, 'new_value': 226427.0}, {'field': 'total_amount', 'old_value': 216382.0, 'new_value': 226427.0}, {'field': 'order_count', 'old_value': 5769, 'new_value': 6134}]
2025-05-15 18:00:38,425 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-15 18:00:38,816 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-15 18:00:38,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251339.45, 'new_value': 257666.2}, {'field': 'total_amount', 'old_value': 251339.45, 'new_value': 257666.2}, {'field': 'order_count', 'old_value': 478, 'new_value': 499}]
2025-05-15 18:00:38,816 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-15 18:00:39,285 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-15 18:00:39,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45574.0, 'new_value': 45674.0}, {'field': 'total_amount', 'old_value': 45574.0, 'new_value': 45674.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-15 18:00:39,285 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-15 18:00:39,722 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-15 18:00:39,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17732.0, 'new_value': 17650.0}, {'field': 'total_amount', 'old_value': 19108.0, 'new_value': 19026.0}, {'field': 'order_count', 'old_value': 1965, 'new_value': 2079}]
2025-05-15 18:00:39,722 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-15 18:00:40,144 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-15 18:00:40,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62053.0, 'new_value': 59528.0}, {'field': 'total_amount', 'old_value': 62053.0, 'new_value': 59528.0}, {'field': 'order_count', 'old_value': 376, 'new_value': 395}]
2025-05-15 18:00:40,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-15 18:00:40,644 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-15 18:00:40,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4773.67, 'new_value': 5384.67}, {'field': 'total_amount', 'old_value': 4773.67, 'new_value': 5384.67}, {'field': 'order_count', 'old_value': 156, 'new_value': 169}]
2025-05-15 18:00:40,644 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-15 18:00:41,097 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-15 18:00:41,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57740.4, 'new_value': 61699.0}, {'field': 'offline_amount', 'old_value': 20256.3, 'new_value': 21237.3}, {'field': 'total_amount', 'old_value': 77996.7, 'new_value': 82936.3}, {'field': 'order_count', 'old_value': 6470, 'new_value': 6869}]
2025-05-15 18:00:41,113 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-15 18:00:41,582 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-15 18:00:41,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136077.61, 'new_value': 122928.61}, {'field': 'offline_amount', 'old_value': 241000.0, 'new_value': 249946.92}, {'field': 'total_amount', 'old_value': 377077.61, 'new_value': 372875.53}]
2025-05-15 18:00:41,582 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-15 18:00:42,050 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-15 18:00:42,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1298.0, 'new_value': 1697.0}, {'field': 'total_amount', 'old_value': 6896.0, 'new_value': 7295.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-15 18:00:42,050 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-15 18:00:42,503 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-15 18:00:42,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13973.24, 'new_value': 14637.54}, {'field': 'offline_amount', 'old_value': 15613.05, 'new_value': 16659.85}, {'field': 'total_amount', 'old_value': 29586.29, 'new_value': 31297.39}, {'field': 'order_count', 'old_value': 1393, 'new_value': 1479}]
2025-05-15 18:00:42,503 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-15 18:00:42,941 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-15 18:00:42,941 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2389.0, 'new_value': 2440.6}, {'field': 'offline_amount', 'old_value': 11308.0, 'new_value': 12646.0}, {'field': 'total_amount', 'old_value': 13697.0, 'new_value': 15086.6}, {'field': 'order_count', 'old_value': 334, 'new_value': 368}]
2025-05-15 18:00:42,941 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-15 18:00:43,363 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-15 18:00:43,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101263.31, 'new_value': 108905.73}, {'field': 'total_amount', 'old_value': 101263.31, 'new_value': 108905.73}, {'field': 'order_count', 'old_value': 7081, 'new_value': 7650}]
2025-05-15 18:00:43,363 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-15 18:00:43,816 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-15 18:00:43,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43184.74, 'new_value': 46200.02}, {'field': 'total_amount', 'old_value': 43184.74, 'new_value': 46200.02}, {'field': 'order_count', 'old_value': 4159, 'new_value': 4515}]
2025-05-15 18:00:43,816 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-15 18:00:44,457 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-15 18:00:44,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23990.0, 'new_value': 24990.0}, {'field': 'total_amount', 'old_value': 23990.0, 'new_value': 24990.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-15 18:00:44,457 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-15 18:00:44,894 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-15 18:00:44,894 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9709.0, 'new_value': 10375.0}, {'field': 'offline_amount', 'old_value': 12109.0, 'new_value': 12775.0}, {'field': 'total_amount', 'old_value': 21818.0, 'new_value': 23150.0}, {'field': 'order_count', 'old_value': 9747, 'new_value': 10413}]
2025-05-15 18:00:44,894 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-15 18:00:45,347 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-15 18:00:45,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61138.0, 'new_value': 61476.0}, {'field': 'total_amount', 'old_value': 61138.0, 'new_value': 61476.0}, {'field': 'order_count', 'old_value': 294, 'new_value': 325}]
2025-05-15 18:00:45,347 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-15 18:00:45,800 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-15 18:00:45,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92130.24, 'new_value': 94905.17}, {'field': 'total_amount', 'old_value': 92130.24, 'new_value': 94905.17}, {'field': 'order_count', 'old_value': 733, 'new_value': 794}]
2025-05-15 18:00:45,800 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-15 18:00:46,238 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-15 18:00:46,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26744.36, 'new_value': 27846.36}, {'field': 'total_amount', 'old_value': 30743.36, 'new_value': 31845.36}, {'field': 'order_count', 'old_value': 2044, 'new_value': 2121}]
2025-05-15 18:00:46,238 - INFO - 日期 2025-05 处理完成 - 更新: 34 条，插入: 0 条，错误: 0 条
2025-05-15 18:00:46,238 - INFO - 数据同步完成！更新: 36 条，插入: 0 条，错误: 0 条
2025-05-15 18:00:46,238 - INFO - =================同步完成====================
2025-05-15 21:00:01,945 - INFO - =================使用默认全量同步=============
2025-05-15 21:00:03,320 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-15 21:00:03,320 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-15 21:00:03,336 - INFO - 开始处理日期: 2025-01
2025-05-15 21:00:03,352 - INFO - Request Parameters - Page 1:
2025-05-15 21:00:03,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:03,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:04,383 - INFO - Response - Page 1:
2025-05-15 21:00:04,586 - INFO - 第 1 页获取到 100 条记录
2025-05-15 21:00:04,586 - INFO - Request Parameters - Page 2:
2025-05-15 21:00:04,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:04,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:05,320 - INFO - Response - Page 2:
2025-05-15 21:00:05,523 - INFO - 第 2 页获取到 100 条记录
2025-05-15 21:00:05,523 - INFO - Request Parameters - Page 3:
2025-05-15 21:00:05,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:05,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:06,102 - INFO - Response - Page 3:
2025-05-15 21:00:06,305 - INFO - 第 3 页获取到 100 条记录
2025-05-15 21:00:06,305 - INFO - Request Parameters - Page 4:
2025-05-15 21:00:06,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:06,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:06,820 - INFO - Response - Page 4:
2025-05-15 21:00:07,023 - INFO - 第 4 页获取到 100 条记录
2025-05-15 21:00:07,023 - INFO - Request Parameters - Page 5:
2025-05-15 21:00:07,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:07,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:07,523 - INFO - Response - Page 5:
2025-05-15 21:00:07,727 - INFO - 第 5 页获取到 100 条记录
2025-05-15 21:00:07,727 - INFO - Request Parameters - Page 6:
2025-05-15 21:00:07,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:07,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:08,211 - INFO - Response - Page 6:
2025-05-15 21:00:08,414 - INFO - 第 6 页获取到 100 条记录
2025-05-15 21:00:08,414 - INFO - Request Parameters - Page 7:
2025-05-15 21:00:08,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:08,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:08,898 - INFO - Response - Page 7:
2025-05-15 21:00:09,102 - INFO - 第 7 页获取到 82 条记录
2025-05-15 21:00:09,102 - INFO - 查询完成，共获取到 682 条记录
2025-05-15 21:00:09,102 - INFO - 获取到 682 条表单数据
2025-05-15 21:00:09,102 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-15 21:00:09,117 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 21:00:09,117 - INFO - 开始处理日期: 2025-02
2025-05-15 21:00:09,117 - INFO - Request Parameters - Page 1:
2025-05-15 21:00:09,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:09,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:09,711 - INFO - Response - Page 1:
2025-05-15 21:00:09,914 - INFO - 第 1 页获取到 100 条记录
2025-05-15 21:00:09,914 - INFO - Request Parameters - Page 2:
2025-05-15 21:00:09,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:09,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:10,445 - INFO - Response - Page 2:
2025-05-15 21:00:10,648 - INFO - 第 2 页获取到 100 条记录
2025-05-15 21:00:10,648 - INFO - Request Parameters - Page 3:
2025-05-15 21:00:10,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:10,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:11,180 - INFO - Response - Page 3:
2025-05-15 21:00:11,383 - INFO - 第 3 页获取到 100 条记录
2025-05-15 21:00:11,383 - INFO - Request Parameters - Page 4:
2025-05-15 21:00:11,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:11,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:11,977 - INFO - Response - Page 4:
2025-05-15 21:00:12,180 - INFO - 第 4 页获取到 100 条记录
2025-05-15 21:00:12,180 - INFO - Request Parameters - Page 5:
2025-05-15 21:00:12,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:12,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:12,648 - INFO - Response - Page 5:
2025-05-15 21:00:12,852 - INFO - 第 5 页获取到 100 条记录
2025-05-15 21:00:12,852 - INFO - Request Parameters - Page 6:
2025-05-15 21:00:12,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:12,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:13,352 - INFO - Response - Page 6:
2025-05-15 21:00:13,555 - INFO - 第 6 页获取到 100 条记录
2025-05-15 21:00:13,555 - INFO - Request Parameters - Page 7:
2025-05-15 21:00:13,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:13,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:14,070 - INFO - Response - Page 7:
2025-05-15 21:00:14,273 - INFO - 第 7 页获取到 70 条记录
2025-05-15 21:00:14,273 - INFO - 查询完成，共获取到 670 条记录
2025-05-15 21:00:14,273 - INFO - 获取到 670 条表单数据
2025-05-15 21:00:14,273 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-15 21:00:14,289 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 21:00:14,289 - INFO - 开始处理日期: 2025-03
2025-05-15 21:00:14,289 - INFO - Request Parameters - Page 1:
2025-05-15 21:00:14,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:14,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:14,820 - INFO - Response - Page 1:
2025-05-15 21:00:15,023 - INFO - 第 1 页获取到 100 条记录
2025-05-15 21:00:15,023 - INFO - Request Parameters - Page 2:
2025-05-15 21:00:15,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:15,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:15,555 - INFO - Response - Page 2:
2025-05-15 21:00:15,758 - INFO - 第 2 页获取到 100 条记录
2025-05-15 21:00:15,758 - INFO - Request Parameters - Page 3:
2025-05-15 21:00:15,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:15,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:16,289 - INFO - Response - Page 3:
2025-05-15 21:00:16,492 - INFO - 第 3 页获取到 100 条记录
2025-05-15 21:00:16,492 - INFO - Request Parameters - Page 4:
2025-05-15 21:00:16,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:16,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:17,023 - INFO - Response - Page 4:
2025-05-15 21:00:17,226 - INFO - 第 4 页获取到 100 条记录
2025-05-15 21:00:17,226 - INFO - Request Parameters - Page 5:
2025-05-15 21:00:17,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:17,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:17,695 - INFO - Response - Page 5:
2025-05-15 21:00:17,898 - INFO - 第 5 页获取到 100 条记录
2025-05-15 21:00:17,898 - INFO - Request Parameters - Page 6:
2025-05-15 21:00:17,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:17,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:18,398 - INFO - Response - Page 6:
2025-05-15 21:00:18,601 - INFO - 第 6 页获取到 100 条记录
2025-05-15 21:00:18,601 - INFO - Request Parameters - Page 7:
2025-05-15 21:00:18,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:18,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:19,023 - INFO - Response - Page 7:
2025-05-15 21:00:19,226 - INFO - 第 7 页获取到 61 条记录
2025-05-15 21:00:19,226 - INFO - 查询完成，共获取到 661 条记录
2025-05-15 21:00:19,226 - INFO - 获取到 661 条表单数据
2025-05-15 21:00:19,226 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-15 21:00:19,242 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-15 21:00:19,242 - INFO - 开始处理日期: 2025-04
2025-05-15 21:00:19,242 - INFO - Request Parameters - Page 1:
2025-05-15 21:00:19,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:19,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:19,758 - INFO - Response - Page 1:
2025-05-15 21:00:19,961 - INFO - 第 1 页获取到 100 条记录
2025-05-15 21:00:19,961 - INFO - Request Parameters - Page 2:
2025-05-15 21:00:19,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:19,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:20,430 - INFO - Response - Page 2:
2025-05-15 21:00:20,633 - INFO - 第 2 页获取到 100 条记录
2025-05-15 21:00:20,633 - INFO - Request Parameters - Page 3:
2025-05-15 21:00:20,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:20,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:21,086 - INFO - Response - Page 3:
2025-05-15 21:00:21,289 - INFO - 第 3 页获取到 100 条记录
2025-05-15 21:00:21,289 - INFO - Request Parameters - Page 4:
2025-05-15 21:00:21,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:21,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:21,789 - INFO - Response - Page 4:
2025-05-15 21:00:21,992 - INFO - 第 4 页获取到 100 条记录
2025-05-15 21:00:21,992 - INFO - Request Parameters - Page 5:
2025-05-15 21:00:21,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:21,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:22,586 - INFO - Response - Page 5:
2025-05-15 21:00:22,789 - INFO - 第 5 页获取到 100 条记录
2025-05-15 21:00:22,789 - INFO - Request Parameters - Page 6:
2025-05-15 21:00:22,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:22,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:23,289 - INFO - Response - Page 6:
2025-05-15 21:00:23,492 - INFO - 第 6 页获取到 100 条记录
2025-05-15 21:00:23,492 - INFO - Request Parameters - Page 7:
2025-05-15 21:00:23,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:23,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:23,898 - INFO - Response - Page 7:
2025-05-15 21:00:24,101 - INFO - 第 7 页获取到 54 条记录
2025-05-15 21:00:24,101 - INFO - 查询完成，共获取到 654 条记录
2025-05-15 21:00:24,101 - INFO - 获取到 654 条表单数据
2025-05-15 21:00:24,101 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-15 21:00:24,101 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M54
2025-05-15 21:00:24,555 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M54
2025-05-15 21:00:24,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23897.0, 'new_value': 15498.0}, {'field': 'total_amount', 'old_value': 23897.0, 'new_value': 15498.0}]
2025-05-15 21:00:24,555 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-05-15 21:00:25,023 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-05-15 21:00:25,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14690.0, 'new_value': 15089.0}, {'field': 'total_amount', 'old_value': 14690.0, 'new_value': 15089.0}]
2025-05-15 21:00:25,023 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-05-15 21:00:25,555 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-05-15 21:00:25,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138821.33, 'new_value': 145060.32}, {'field': 'total_amount', 'old_value': 139154.13, 'new_value': 145393.12}]
2025-05-15 21:00:25,555 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-05-15 21:00:26,070 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-05-15 21:00:26,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79635.0, 'new_value': 85347.0}, {'field': 'total_amount', 'old_value': 79635.0, 'new_value': 85347.0}]
2025-05-15 21:00:26,070 - INFO - 日期 2025-04 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-05-15 21:00:26,070 - INFO - 开始处理日期: 2025-05
2025-05-15 21:00:26,070 - INFO - Request Parameters - Page 1:
2025-05-15 21:00:26,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:26,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:26,570 - INFO - Response - Page 1:
2025-05-15 21:00:26,773 - INFO - 第 1 页获取到 100 条记录
2025-05-15 21:00:26,773 - INFO - Request Parameters - Page 2:
2025-05-15 21:00:26,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:26,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:27,289 - INFO - Response - Page 2:
2025-05-15 21:00:27,492 - INFO - 第 2 页获取到 100 条记录
2025-05-15 21:00:27,492 - INFO - Request Parameters - Page 3:
2025-05-15 21:00:27,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:27,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:28,008 - INFO - Response - Page 3:
2025-05-15 21:00:28,226 - INFO - 第 3 页获取到 100 条记录
2025-05-15 21:00:28,226 - INFO - Request Parameters - Page 4:
2025-05-15 21:00:28,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:28,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:28,695 - INFO - Response - Page 4:
2025-05-15 21:00:28,898 - INFO - 第 4 页获取到 100 条记录
2025-05-15 21:00:28,898 - INFO - Request Parameters - Page 5:
2025-05-15 21:00:28,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:28,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:29,461 - INFO - Response - Page 5:
2025-05-15 21:00:29,664 - INFO - 第 5 页获取到 100 条记录
2025-05-15 21:00:29,664 - INFO - Request Parameters - Page 6:
2025-05-15 21:00:29,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:29,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:30,148 - INFO - Response - Page 6:
2025-05-15 21:00:30,351 - INFO - 第 6 页获取到 100 条记录
2025-05-15 21:00:30,351 - INFO - Request Parameters - Page 7:
2025-05-15 21:00:30,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-15 21:00:30,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-15 21:00:30,758 - INFO - Response - Page 7:
2025-05-15 21:00:30,961 - INFO - 第 7 页获取到 25 条记录
2025-05-15 21:00:30,961 - INFO - 查询完成，共获取到 625 条记录
2025-05-15 21:00:30,961 - INFO - 获取到 625 条表单数据
2025-05-15 21:00:30,961 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-15 21:00:30,961 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-15 21:00:31,461 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-15 21:00:31,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47335.0, 'new_value': 49664.0}, {'field': 'offline_amount', 'old_value': 60104.0, 'new_value': 64216.0}, {'field': 'total_amount', 'old_value': 107439.0, 'new_value': 113880.0}, {'field': 'order_count', 'old_value': 2549, 'new_value': 2726}]
2025-05-15 21:00:31,476 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-15 21:00:31,476 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-05-15 21:00:31,476 - INFO - =================同步完成====================
