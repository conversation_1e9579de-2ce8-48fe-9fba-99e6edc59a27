# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_dingtalk.yida_2_0.client import Client as dingtalkyida_2_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> dingtalkyida_2_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkyida_2_0Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        get_form_data_by_idheaders = dingtalkyida__2__0_models.GetFormDataByIDHeaders()
        get_form_data_by_idheaders.x_acs_dingtalk_access_token = 'f6c7f093cdd03d5698ece5d126411f71'
        get_form_data_by_idrequest = dingtalkyida__2__0_models.GetFormDataByIDRequest(
            app_type='APP_D7E6ZB94ZUL5Q1GUAOLD',
            system_token='BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
            user_id='hexuepeng',
            language='zh_CN',
            use_alias=True,
            form_uuid='FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P'
        )
        try:
           response = client.get_form_data_by_idwith_options('FINST-2PF66KD1KWJW2F02FZALU5V5HFHH22KP2S9CM7E', get_form_data_by_idrequest, get_form_data_by_idheaders, util_models.RuntimeOptions())
           print(response)
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass


if __name__ == '__main__':
    Sample.main(sys.argv[1:])