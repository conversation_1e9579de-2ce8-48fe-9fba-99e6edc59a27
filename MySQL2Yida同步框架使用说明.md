# MySQL与宜搭数据同步通用框架使用说明

## 概述

这是一个通用的MySQL与宜搭数据同步框架，支持新增、更新场景的数据同步。通过配置文件驱动，可以快速配置不同的数据同步需求。

## 文件结构

```
mysql2yida_sync_framework.py    # 主框架文件
sync_config_example.json        # 销售数据同步配置示例
sync_config_devices.json        # 设备数据同步配置示例
test_sync_framework.py          # 框架测试脚本
example_usage.py                # 交互式使用示例
quick_start.py                  # 快速启动脚本
MySQL2Yida同步框架使用说明.md   # 使用说明文档
logs/                           # 日志文件目录
```

## 核心功能

1. **配置驱动**: 通过JSON配置文件定义数据库连接、宜搭表单、字段映射等
2. **数据对比**: 基于键字段自动识别新增和更新数据
3. **批量处理**: 支持批量新增和逐条更新
4. **日志记录**: 详细的同步过程日志
5. **错误处理**: 完善的异常处理机制

## 配置文件说明

### 1. 数据库配置 (db_config)
```json
{
  "db_config": {
    "host": "数据库主机地址",
    "port": 3306,
    "user": "数据库用户名",
    "password": "数据库密码",
    "database": "数据库名",
    "charset": "utf8mb4",
    "cursorclass": "pymysql.cursors.DictCursor"
  }
}
```

### 2. 宜搭配置 (yida_config)
```json
{
  "yida_config": {
    "APP_TYPE": "宜搭应用类型",
    "SYSTEM_TOKEN": "系统令牌",
    "USER_ID": "用户ID",
    "LANGUAGE": "zh_CN",
    "FORM_UUID": "表单UUID"
  }
}
```

### 3. SQL查询语句 (sql_query)
定义从MySQL获取数据的SQL语句，支持复杂的JOIN查询和聚合函数。

### 4. 字段映射 (field_mapping)
定义MySQL字段名与宜搭表单字段ID的映射关系：
```json
{
  "field_mapping": {
    "mysql_field_name": "yida_field_id",
    "project_code": "textField_m9tojheo",
    "sales_date": "dateField_m9tojheu"
  }
}
```

### 5. 键字段 (key_fields)
用于数据对比的唯一标识字段，确定是否为同一条记录：
```json
{
  "key_fields": ["project_code", "store_code", "sales_date"]
}
```

### 6. 比较字段 (compare_fields)
需要比较更新的字段列表：
```json
{
  "compare_fields": ["online_amount", "offline_amount", "total_amount"]
}
```

### 7. 日志配置 (log_config)
```json
{
  "log_config": {
    "level": 20,
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "encoding": "utf-8",
    "filename_prefix": "sync_log"
  }
}
```

### 8. 批处理配置 (batch_config)
```json
{
  "batch_config": {
    "batch_size": 50,
    "delay_seconds": 1
  }
}
```

## 使用方法

### 方法一：使用主框架文件
```bash
# 创建示例配置文件
python mysql2yida_sync_framework.py --create-config my_sync_config.json

# 使用指定配置文件执行同步
python mysql2yida_sync_framework.py --config my_sync_config.json

# 使用默认配置执行同步
python mysql2yida_sync_framework.py
```

### 方法二：使用快速启动脚本
```bash
# 创建配置文件模板
python quick_start.py --create-config my_config.json

# 使用预设的销售数据配置同步
python quick_start.py --type sales

# 使用预设的设备数据配置同步
python quick_start.py --type device

# 使用自定义配置文件同步
python quick_start.py --config my_config.json
```

### 方法三：使用交互式示例
```bash
# 运行交互式示例程序
python example_usage.py
```

### 方法四：测试框架功能
```bash
# 运行测试脚本验证框架功能
python test_sync_framework.py
```

## 数据同步流程

1. **加载配置**: 读取配置文件或使用默认配置
2. **连接数据库**: 建立MySQL和宜搭的连接
3. **获取数据**: 从MySQL和宜搭分别获取数据
4. **数据对比**: 基于键字段生成唯一标识，比较数据差异
5. **分类处理**: 
   - 新增数据：MySQL中存在但宜搭中不存在的记录
   - 更新数据：两边都存在但比较字段有差异的记录
6. **执行同步**: 
   - 批量新增数据到宜搭
   - 逐条更新已存在的数据
7. **记录日志**: 详细记录同步过程和结果

## 注意事项

1. **字段类型处理**: 
   - 日期字段会自动转换为时间戳格式
   - 数值字段支持小数点误差比较

2. **批量处理**: 
   - 新增操作使用批量接口提高效率
   - 更新操作逐条执行确保准确性

3. **错误处理**: 
   - 网络异常会自动重试
   - 数据格式错误会记录详细日志

4. **性能优化**: 
   - 支持分页获取大量数据
   - 批次间有延时避免请求过频

## 扩展使用

### 自定义数据处理
可以继承 `MySQL2YidaSyncClient` 类，重写相关方法来实现自定义的数据处理逻辑。

### 多表单同步
可以创建多个配置文件，分别对应不同的表单同步需求。

### 定时同步
结合系统定时任务（如crontab），可以实现定时自动同步。

## 常见问题

1. **连接失败**: 检查数据库配置和网络连接
2. **字段映射错误**: 确认宜搭表单字段ID的正确性
3. **数据格式问题**: 检查SQL查询结果的数据类型
4. **权限问题**: 确认宜搭用户权限和系统令牌有效性

## 示例配置

框架提供了两个示例配置：
- `sync_config_example.json`: 销售数据同步配置
- `sync_config_devices.json`: 设备数据同步配置

可以基于这些示例快速创建自己的同步配置。
