# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys
from typing import List
from alibabacloud_dingtalk.yida_2_0.client import Client as dingtalkyida_2_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient

class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> dingtalkyida_2_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkyida_2_0Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        update_form_data_headers = dingtalkyida__2__0_models.UpdateFormDataHeaders()
        update_form_data_headers.x_acs_dingtalk_access_token = '<your access token>'
        update_form_data_request = dingtalkyida__2__0_models.UpdateFormDataRequest(
            app_type='APP_PBKTxxx',
            system_token='hexxxx',
            user_id='manager123',
            language='zh_CN',
            form_instance_id='FORM_INxxx',
            use_latest_version=False,
            update_form_data_json='{}',
            use_alias=True,
            form_uuid='FORM-AA285xxx'
        )
        try:
            client.update_form_data_with_options(update_form_data_request, update_form_data_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        update_form_data_headers = dingtalkyida__2__0_models.UpdateFormDataHeaders()
        update_form_data_headers.x_acs_dingtalk_access_token = '<your access token>'
        update_form_data_request = dingtalkyida__2__0_models.UpdateFormDataRequest(
            app_type='APP_PBKTxxx',
            system_token='hexxxx',
            user_id='manager123',
            language='zh_CN',
            form_instance_id='FORM_INxxx',
            use_latest_version=False,
            update_form_data_json='{}',
            use_alias=True,
            form_uuid='FORM-AA285xxx'
        )
        try:
            await client.update_form_data_with_options_async(update_form_data_request, update_form_data_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass


if __name__ == '__main__':
    Sample.main(sys.argv[1:])