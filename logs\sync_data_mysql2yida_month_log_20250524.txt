2025-05-24 00:00:02,106 - INFO - =================使用默认全量同步=============
2025-05-24 00:00:03,637 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-24 00:00:03,637 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 00:00:03,653 - INFO - 开始处理日期: 2025-01
2025-05-24 00:00:03,669 - INFO - Request Parameters - Page 1:
2025-05-24 00:00:03,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:03,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:04,778 - INFO - Response - Page 1:
2025-05-24 00:00:04,981 - INFO - 第 1 页获取到 100 条记录
2025-05-24 00:00:04,981 - INFO - Request Parameters - Page 2:
2025-05-24 00:00:04,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:04,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:05,841 - INFO - Response - Page 2:
2025-05-24 00:00:06,044 - INFO - 第 2 页获取到 100 条记录
2025-05-24 00:00:06,044 - INFO - Request Parameters - Page 3:
2025-05-24 00:00:06,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:06,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:06,559 - INFO - Response - Page 3:
2025-05-24 00:00:06,763 - INFO - 第 3 页获取到 100 条记录
2025-05-24 00:00:06,763 - INFO - Request Parameters - Page 4:
2025-05-24 00:00:06,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:06,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:07,434 - INFO - Response - Page 4:
2025-05-24 00:00:07,638 - INFO - 第 4 页获取到 100 条记录
2025-05-24 00:00:07,638 - INFO - Request Parameters - Page 5:
2025-05-24 00:00:07,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:07,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:08,169 - INFO - Response - Page 5:
2025-05-24 00:00:08,372 - INFO - 第 5 页获取到 100 条记录
2025-05-24 00:00:08,372 - INFO - Request Parameters - Page 6:
2025-05-24 00:00:08,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:08,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:08,872 - INFO - Response - Page 6:
2025-05-24 00:00:09,075 - INFO - 第 6 页获取到 100 条记录
2025-05-24 00:00:09,075 - INFO - Request Parameters - Page 7:
2025-05-24 00:00:09,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:09,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:09,513 - INFO - Response - Page 7:
2025-05-24 00:00:09,716 - INFO - 第 7 页获取到 82 条记录
2025-05-24 00:00:09,716 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 00:00:09,716 - INFO - 获取到 682 条表单数据
2025-05-24 00:00:09,716 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 00:00:09,731 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 00:00:09,731 - INFO - 开始处理日期: 2025-02
2025-05-24 00:00:09,731 - INFO - Request Parameters - Page 1:
2025-05-24 00:00:09,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:09,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:10,231 - INFO - Response - Page 1:
2025-05-24 00:00:10,435 - INFO - 第 1 页获取到 100 条记录
2025-05-24 00:00:10,435 - INFO - Request Parameters - Page 2:
2025-05-24 00:00:10,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:10,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:10,966 - INFO - Response - Page 2:
2025-05-24 00:00:11,169 - INFO - 第 2 页获取到 100 条记录
2025-05-24 00:00:11,169 - INFO - Request Parameters - Page 3:
2025-05-24 00:00:11,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:11,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:11,747 - INFO - Response - Page 3:
2025-05-24 00:00:11,950 - INFO - 第 3 页获取到 100 条记录
2025-05-24 00:00:11,950 - INFO - Request Parameters - Page 4:
2025-05-24 00:00:11,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:11,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:12,419 - INFO - Response - Page 4:
2025-05-24 00:00:12,622 - INFO - 第 4 页获取到 100 条记录
2025-05-24 00:00:12,622 - INFO - Request Parameters - Page 5:
2025-05-24 00:00:12,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:12,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:13,200 - INFO - Response - Page 5:
2025-05-24 00:00:13,404 - INFO - 第 5 页获取到 100 条记录
2025-05-24 00:00:13,404 - INFO - Request Parameters - Page 6:
2025-05-24 00:00:13,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:13,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:13,888 - INFO - Response - Page 6:
2025-05-24 00:00:14,091 - INFO - 第 6 页获取到 100 条记录
2025-05-24 00:00:14,091 - INFO - Request Parameters - Page 7:
2025-05-24 00:00:14,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:14,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:14,575 - INFO - Response - Page 7:
2025-05-24 00:00:14,779 - INFO - 第 7 页获取到 70 条记录
2025-05-24 00:00:14,779 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 00:00:14,779 - INFO - 获取到 670 条表单数据
2025-05-24 00:00:14,779 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 00:00:14,794 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 00:00:14,794 - INFO - 开始处理日期: 2025-03
2025-05-24 00:00:14,794 - INFO - Request Parameters - Page 1:
2025-05-24 00:00:14,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:14,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:15,294 - INFO - Response - Page 1:
2025-05-24 00:00:15,497 - INFO - 第 1 页获取到 100 条记录
2025-05-24 00:00:15,497 - INFO - Request Parameters - Page 2:
2025-05-24 00:00:15,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:15,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:16,029 - INFO - Response - Page 2:
2025-05-24 00:00:16,232 - INFO - 第 2 页获取到 100 条记录
2025-05-24 00:00:16,232 - INFO - Request Parameters - Page 3:
2025-05-24 00:00:16,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:16,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:16,779 - INFO - Response - Page 3:
2025-05-24 00:00:16,982 - INFO - 第 3 页获取到 100 条记录
2025-05-24 00:00:16,982 - INFO - Request Parameters - Page 4:
2025-05-24 00:00:16,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:16,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:17,451 - INFO - Response - Page 4:
2025-05-24 00:00:17,654 - INFO - 第 4 页获取到 100 条记录
2025-05-24 00:00:17,654 - INFO - Request Parameters - Page 5:
2025-05-24 00:00:17,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:17,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:18,216 - INFO - Response - Page 5:
2025-05-24 00:00:18,419 - INFO - 第 5 页获取到 100 条记录
2025-05-24 00:00:18,419 - INFO - Request Parameters - Page 6:
2025-05-24 00:00:18,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:18,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:19,185 - INFO - Response - Page 6:
2025-05-24 00:00:19,388 - INFO - 第 6 页获取到 100 条记录
2025-05-24 00:00:19,388 - INFO - Request Parameters - Page 7:
2025-05-24 00:00:19,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:19,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:19,857 - INFO - Response - Page 7:
2025-05-24 00:00:20,060 - INFO - 第 7 页获取到 61 条记录
2025-05-24 00:00:20,060 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 00:00:20,060 - INFO - 获取到 661 条表单数据
2025-05-24 00:00:20,060 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 00:00:20,076 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 00:00:20,076 - INFO - 开始处理日期: 2025-04
2025-05-24 00:00:20,076 - INFO - Request Parameters - Page 1:
2025-05-24 00:00:20,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:20,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:20,607 - INFO - Response - Page 1:
2025-05-24 00:00:20,810 - INFO - 第 1 页获取到 100 条记录
2025-05-24 00:00:20,810 - INFO - Request Parameters - Page 2:
2025-05-24 00:00:20,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:20,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:21,295 - INFO - Response - Page 2:
2025-05-24 00:00:21,498 - INFO - 第 2 页获取到 100 条记录
2025-05-24 00:00:21,498 - INFO - Request Parameters - Page 3:
2025-05-24 00:00:21,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:21,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:21,935 - INFO - Response - Page 3:
2025-05-24 00:00:22,138 - INFO - 第 3 页获取到 100 条记录
2025-05-24 00:00:22,138 - INFO - Request Parameters - Page 4:
2025-05-24 00:00:22,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:22,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:22,732 - INFO - Response - Page 4:
2025-05-24 00:00:22,935 - INFO - 第 4 页获取到 100 条记录
2025-05-24 00:00:22,935 - INFO - Request Parameters - Page 5:
2025-05-24 00:00:22,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:22,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:23,420 - INFO - Response - Page 5:
2025-05-24 00:00:23,623 - INFO - 第 5 页获取到 100 条记录
2025-05-24 00:00:23,623 - INFO - Request Parameters - Page 6:
2025-05-24 00:00:23,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:23,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:24,201 - INFO - Response - Page 6:
2025-05-24 00:00:24,404 - INFO - 第 6 页获取到 100 条记录
2025-05-24 00:00:24,404 - INFO - Request Parameters - Page 7:
2025-05-24 00:00:24,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:24,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:24,779 - INFO - Response - Page 7:
2025-05-24 00:00:24,982 - INFO - 第 7 页获取到 56 条记录
2025-05-24 00:00:24,982 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 00:00:24,982 - INFO - 获取到 656 条表单数据
2025-05-24 00:00:24,982 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 00:00:24,998 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 00:00:24,998 - INFO - 开始处理日期: 2025-05
2025-05-24 00:00:24,998 - INFO - Request Parameters - Page 1:
2025-05-24 00:00:24,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:24,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:25,482 - INFO - Response - Page 1:
2025-05-24 00:00:25,685 - INFO - 第 1 页获取到 100 条记录
2025-05-24 00:00:25,685 - INFO - Request Parameters - Page 2:
2025-05-24 00:00:25,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:25,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:26,154 - INFO - Response - Page 2:
2025-05-24 00:00:26,357 - INFO - 第 2 页获取到 100 条记录
2025-05-24 00:00:26,357 - INFO - Request Parameters - Page 3:
2025-05-24 00:00:26,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:26,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:26,810 - INFO - Response - Page 3:
2025-05-24 00:00:27,014 - INFO - 第 3 页获取到 100 条记录
2025-05-24 00:00:27,014 - INFO - Request Parameters - Page 4:
2025-05-24 00:00:27,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:27,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:27,451 - INFO - Response - Page 4:
2025-05-24 00:00:27,654 - INFO - 第 4 页获取到 100 条记录
2025-05-24 00:00:27,654 - INFO - Request Parameters - Page 5:
2025-05-24 00:00:27,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:27,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:28,154 - INFO - Response - Page 5:
2025-05-24 00:00:28,357 - INFO - 第 5 页获取到 100 条记录
2025-05-24 00:00:28,357 - INFO - Request Parameters - Page 6:
2025-05-24 00:00:28,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:28,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:28,889 - INFO - Response - Page 6:
2025-05-24 00:00:29,092 - INFO - 第 6 页获取到 100 条记录
2025-05-24 00:00:29,092 - INFO - Request Parameters - Page 7:
2025-05-24 00:00:29,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:00:29,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:00:29,451 - INFO - Response - Page 7:
2025-05-24 00:00:29,654 - INFO - 第 7 页获取到 28 条记录
2025-05-24 00:00:29,654 - INFO - 查询完成，共获取到 628 条记录
2025-05-24 00:00:29,654 - INFO - 获取到 628 条表单数据
2025-05-24 00:00:29,654 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-24 00:00:29,654 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-24 00:00:30,154 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-24 00:00:30,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105208.0, 'new_value': 109927.0}, {'field': 'offline_amount', 'old_value': 118729.28, 'new_value': 124788.28}, {'field': 'total_amount', 'old_value': 223937.28, 'new_value': 234715.28}, {'field': 'order_count', 'old_value': 4803, 'new_value': 5041}]
2025-05-24 00:00:30,154 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-24 00:00:30,639 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-24 00:00:30,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72691.0, 'new_value': 75702.0}, {'field': 'offline_amount', 'old_value': 93514.0, 'new_value': 97637.0}, {'field': 'total_amount', 'old_value': 166205.0, 'new_value': 173339.0}, {'field': 'order_count', 'old_value': 3834, 'new_value': 3976}]
2025-05-24 00:00:30,639 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-24 00:00:31,170 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-24 00:00:31,170 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24743.2, 'new_value': 29668.2}, {'field': 'offline_amount', 'old_value': 145493.38, 'new_value': 152516.04}, {'field': 'total_amount', 'old_value': 170236.58, 'new_value': 182184.24}, {'field': 'order_count', 'old_value': 231, 'new_value': 248}]
2025-05-24 00:00:31,170 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVT
2025-05-24 00:00:31,654 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVT
2025-05-24 00:00:31,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4450.0, 'new_value': 7240.0}, {'field': 'total_amount', 'old_value': 4450.0, 'new_value': 7240.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-24 00:00:31,654 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-24 00:00:32,014 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-24 00:00:32,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46112.96, 'new_value': 48874.64}, {'field': 'offline_amount', 'old_value': 622486.53, 'new_value': 653021.81}, {'field': 'total_amount', 'old_value': 668599.49, 'new_value': 701896.45}, {'field': 'order_count', 'old_value': 2801, 'new_value': 2920}]
2025-05-24 00:00:32,029 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-24 00:00:32,436 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-24 00:00:32,436 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53905.15, 'new_value': 56292.88}, {'field': 'offline_amount', 'old_value': 86006.01, 'new_value': 89060.7}, {'field': 'total_amount', 'old_value': 139911.16, 'new_value': 145353.58}, {'field': 'order_count', 'old_value': 4864, 'new_value': 5059}]
2025-05-24 00:00:32,436 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-24 00:00:32,920 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-24 00:00:32,920 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286462.0, 'new_value': 290295.0}, {'field': 'total_amount', 'old_value': 286462.0, 'new_value': 290295.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 176}]
2025-05-24 00:00:32,920 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-24 00:00:33,342 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-24 00:00:33,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151095.0, 'new_value': 151383.0}, {'field': 'total_amount', 'old_value': 184841.15, 'new_value': 185129.15}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-24 00:00:33,342 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-24 00:00:33,780 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-24 00:00:33,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46745.0, 'new_value': 48779.0}, {'field': 'total_amount', 'old_value': 47093.0, 'new_value': 49127.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 103}]
2025-05-24 00:00:33,780 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-24 00:00:34,217 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-24 00:00:34,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 749653.0, 'new_value': 781341.0}, {'field': 'total_amount', 'old_value': 749653.0, 'new_value': 781341.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 138}]
2025-05-24 00:00:34,217 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-24 00:00:34,827 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-24 00:00:34,827 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19947.69, 'new_value': 20440.21}, {'field': 'offline_amount', 'old_value': 268147.36, 'new_value': 272803.15}, {'field': 'total_amount', 'old_value': 288095.05, 'new_value': 293243.36}, {'field': 'order_count', 'old_value': 1349, 'new_value': 1374}]
2025-05-24 00:00:34,827 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-24 00:00:35,217 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-24 00:00:35,217 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32157.48, 'new_value': 33521.9}, {'field': 'offline_amount', 'old_value': 481631.24, 'new_value': 500897.29}, {'field': 'total_amount', 'old_value': 513788.72, 'new_value': 534419.19}, {'field': 'order_count', 'old_value': 2769, 'new_value': 2935}]
2025-05-24 00:00:35,217 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-24 00:00:35,733 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-24 00:00:35,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1572635.56, 'new_value': 1638680.3}, {'field': 'total_amount', 'old_value': 1572635.56, 'new_value': 1638680.3}, {'field': 'order_count', 'old_value': 12983, 'new_value': 13628}]
2025-05-24 00:00:35,733 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-24 00:00:36,186 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-24 00:00:36,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172966.0, 'new_value': 173464.0}, {'field': 'total_amount', 'old_value': 173099.0, 'new_value': 173597.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 122}]
2025-05-24 00:00:36,186 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-24 00:00:36,577 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-24 00:00:36,577 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65765.28, 'new_value': 69060.84}, {'field': 'offline_amount', 'old_value': 222089.77, 'new_value': 228546.33}, {'field': 'total_amount', 'old_value': 287855.05, 'new_value': 297607.17}, {'field': 'order_count', 'old_value': 3507, 'new_value': 3630}]
2025-05-24 00:00:36,577 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-24 00:00:36,983 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-24 00:00:36,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60459.5, 'new_value': 61502.5}, {'field': 'total_amount', 'old_value': 60459.5, 'new_value': 61502.5}, {'field': 'order_count', 'old_value': 1615, 'new_value': 1619}]
2025-05-24 00:00:36,983 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-24 00:00:37,467 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-24 00:00:37,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 563771.0, 'new_value': 567065.0}, {'field': 'total_amount', 'old_value': 565367.0, 'new_value': 568661.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 249}]
2025-05-24 00:00:37,467 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-24 00:00:37,874 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-24 00:00:37,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266841.0, 'new_value': 275194.0}, {'field': 'total_amount', 'old_value': 266841.0, 'new_value': 275194.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-05-24 00:00:37,874 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-24 00:00:38,389 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-24 00:00:38,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 639401.96, 'new_value': 662319.52}, {'field': 'total_amount', 'old_value': 639401.96, 'new_value': 662319.52}, {'field': 'order_count', 'old_value': 4451, 'new_value': 4618}]
2025-05-24 00:00:38,389 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-24 00:00:38,874 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-24 00:00:38,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99514.7, 'new_value': 101835.7}, {'field': 'offline_amount', 'old_value': 67372.68, 'new_value': 68235.18}, {'field': 'total_amount', 'old_value': 166887.38, 'new_value': 170070.88}, {'field': 'order_count', 'old_value': 1125, 'new_value': 1148}]
2025-05-24 00:00:38,874 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-24 00:00:39,280 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-24 00:00:39,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222391.03, 'new_value': 227663.03}, {'field': 'total_amount', 'old_value': 222391.03, 'new_value': 227663.03}, {'field': 'order_count', 'old_value': 1365, 'new_value': 1400}]
2025-05-24 00:00:39,280 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-24 00:00:39,671 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-24 00:00:39,671 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29375.43, 'new_value': 30350.26}, {'field': 'offline_amount', 'old_value': 306256.35, 'new_value': 314698.25}, {'field': 'total_amount', 'old_value': 335631.78, 'new_value': 345048.51}, {'field': 'order_count', 'old_value': 9542, 'new_value': 9608}]
2025-05-24 00:00:39,671 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-24 00:00:40,233 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-24 00:00:40,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 538179.0, 'new_value': 549628.0}, {'field': 'total_amount', 'old_value': 538179.0, 'new_value': 549628.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 87}]
2025-05-24 00:00:40,233 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-24 00:00:40,671 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-24 00:00:40,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 583291.0, 'new_value': 590487.0}, {'field': 'total_amount', 'old_value': 583291.0, 'new_value': 590487.0}, {'field': 'order_count', 'old_value': 422, 'new_value': 443}]
2025-05-24 00:00:40,671 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-24 00:00:41,092 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-24 00:00:41,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273112.0, 'new_value': 356054.0}, {'field': 'total_amount', 'old_value': 277836.0, 'new_value': 360778.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 86}]
2025-05-24 00:00:41,108 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-24 00:00:41,499 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-24 00:00:41,499 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4326.83, 'new_value': 4652.93}, {'field': 'offline_amount', 'old_value': 41152.64, 'new_value': 44693.74}, {'field': 'total_amount', 'old_value': 45479.47, 'new_value': 49346.67}, {'field': 'order_count', 'old_value': 1793, 'new_value': 1947}]
2025-05-24 00:00:41,499 - INFO - 日期 2025-05 处理完成 - 更新: 26 条，插入: 0 条，错误: 0 条
2025-05-24 00:00:41,499 - INFO - 数据同步完成！更新: 26 条，插入: 0 条，错误: 0 条
2025-05-24 00:00:41,499 - INFO - =================同步完成====================
2025-05-24 03:00:02,063 - INFO - =================使用默认全量同步=============
2025-05-24 03:00:03,485 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-24 03:00:03,485 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 03:00:03,516 - INFO - 开始处理日期: 2025-01
2025-05-24 03:00:03,516 - INFO - Request Parameters - Page 1:
2025-05-24 03:00:03,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:03,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:04,626 - INFO - Response - Page 1:
2025-05-24 03:00:04,829 - INFO - 第 1 页获取到 100 条记录
2025-05-24 03:00:04,829 - INFO - Request Parameters - Page 2:
2025-05-24 03:00:04,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:04,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:05,423 - INFO - Response - Page 2:
2025-05-24 03:00:05,626 - INFO - 第 2 页获取到 100 条记录
2025-05-24 03:00:05,626 - INFO - Request Parameters - Page 3:
2025-05-24 03:00:05,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:05,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:06,391 - INFO - Response - Page 3:
2025-05-24 03:00:06,594 - INFO - 第 3 页获取到 100 条记录
2025-05-24 03:00:06,594 - INFO - Request Parameters - Page 4:
2025-05-24 03:00:06,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:06,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:07,141 - INFO - Response - Page 4:
2025-05-24 03:00:07,345 - INFO - 第 4 页获取到 100 条记录
2025-05-24 03:00:07,345 - INFO - Request Parameters - Page 5:
2025-05-24 03:00:07,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:07,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:07,891 - INFO - Response - Page 5:
2025-05-24 03:00:08,095 - INFO - 第 5 页获取到 100 条记录
2025-05-24 03:00:08,095 - INFO - Request Parameters - Page 6:
2025-05-24 03:00:08,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:08,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:08,595 - INFO - Response - Page 6:
2025-05-24 03:00:08,798 - INFO - 第 6 页获取到 100 条记录
2025-05-24 03:00:08,798 - INFO - Request Parameters - Page 7:
2025-05-24 03:00:08,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:08,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:09,282 - INFO - Response - Page 7:
2025-05-24 03:00:09,485 - INFO - 第 7 页获取到 82 条记录
2025-05-24 03:00:09,485 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 03:00:09,485 - INFO - 获取到 682 条表单数据
2025-05-24 03:00:09,485 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 03:00:09,501 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 03:00:09,501 - INFO - 开始处理日期: 2025-02
2025-05-24 03:00:09,501 - INFO - Request Parameters - Page 1:
2025-05-24 03:00:09,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:09,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:09,985 - INFO - Response - Page 1:
2025-05-24 03:00:10,188 - INFO - 第 1 页获取到 100 条记录
2025-05-24 03:00:10,188 - INFO - Request Parameters - Page 2:
2025-05-24 03:00:10,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:10,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:10,642 - INFO - Response - Page 2:
2025-05-24 03:00:10,845 - INFO - 第 2 页获取到 100 条记录
2025-05-24 03:00:10,845 - INFO - Request Parameters - Page 3:
2025-05-24 03:00:10,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:10,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:11,345 - INFO - Response - Page 3:
2025-05-24 03:00:11,548 - INFO - 第 3 页获取到 100 条记录
2025-05-24 03:00:11,548 - INFO - Request Parameters - Page 4:
2025-05-24 03:00:11,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:11,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:12,079 - INFO - Response - Page 4:
2025-05-24 03:00:12,282 - INFO - 第 4 页获取到 100 条记录
2025-05-24 03:00:12,282 - INFO - Request Parameters - Page 5:
2025-05-24 03:00:12,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:12,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:12,814 - INFO - Response - Page 5:
2025-05-24 03:00:13,017 - INFO - 第 5 页获取到 100 条记录
2025-05-24 03:00:13,017 - INFO - Request Parameters - Page 6:
2025-05-24 03:00:13,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:13,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:13,532 - INFO - Response - Page 6:
2025-05-24 03:00:13,735 - INFO - 第 6 页获取到 100 条记录
2025-05-24 03:00:13,735 - INFO - Request Parameters - Page 7:
2025-05-24 03:00:13,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:13,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:14,204 - INFO - Response - Page 7:
2025-05-24 03:00:14,407 - INFO - 第 7 页获取到 70 条记录
2025-05-24 03:00:14,407 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 03:00:14,407 - INFO - 获取到 670 条表单数据
2025-05-24 03:00:14,407 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 03:00:14,423 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 03:00:14,423 - INFO - 开始处理日期: 2025-03
2025-05-24 03:00:14,423 - INFO - Request Parameters - Page 1:
2025-05-24 03:00:14,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:14,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:14,986 - INFO - Response - Page 1:
2025-05-24 03:00:15,189 - INFO - 第 1 页获取到 100 条记录
2025-05-24 03:00:15,189 - INFO - Request Parameters - Page 2:
2025-05-24 03:00:15,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:15,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:15,689 - INFO - Response - Page 2:
2025-05-24 03:00:15,892 - INFO - 第 2 页获取到 100 条记录
2025-05-24 03:00:15,892 - INFO - Request Parameters - Page 3:
2025-05-24 03:00:15,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:15,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:16,376 - INFO - Response - Page 3:
2025-05-24 03:00:16,579 - INFO - 第 3 页获取到 100 条记录
2025-05-24 03:00:16,579 - INFO - Request Parameters - Page 4:
2025-05-24 03:00:16,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:16,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:17,048 - INFO - Response - Page 4:
2025-05-24 03:00:17,251 - INFO - 第 4 页获取到 100 条记录
2025-05-24 03:00:17,251 - INFO - Request Parameters - Page 5:
2025-05-24 03:00:17,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:17,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:17,861 - INFO - Response - Page 5:
2025-05-24 03:00:18,064 - INFO - 第 5 页获取到 100 条记录
2025-05-24 03:00:18,064 - INFO - Request Parameters - Page 6:
2025-05-24 03:00:18,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:18,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:18,579 - INFO - Response - Page 6:
2025-05-24 03:00:18,783 - INFO - 第 6 页获取到 100 条记录
2025-05-24 03:00:18,783 - INFO - Request Parameters - Page 7:
2025-05-24 03:00:18,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:18,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:19,189 - INFO - Response - Page 7:
2025-05-24 03:00:19,392 - INFO - 第 7 页获取到 61 条记录
2025-05-24 03:00:19,392 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 03:00:19,392 - INFO - 获取到 661 条表单数据
2025-05-24 03:00:19,392 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 03:00:19,408 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 03:00:19,408 - INFO - 开始处理日期: 2025-04
2025-05-24 03:00:19,408 - INFO - Request Parameters - Page 1:
2025-05-24 03:00:19,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:19,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:19,986 - INFO - Response - Page 1:
2025-05-24 03:00:20,189 - INFO - 第 1 页获取到 100 条记录
2025-05-24 03:00:20,189 - INFO - Request Parameters - Page 2:
2025-05-24 03:00:20,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:20,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:20,689 - INFO - Response - Page 2:
2025-05-24 03:00:20,892 - INFO - 第 2 页获取到 100 条记录
2025-05-24 03:00:20,892 - INFO - Request Parameters - Page 3:
2025-05-24 03:00:20,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:20,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:21,361 - INFO - Response - Page 3:
2025-05-24 03:00:21,564 - INFO - 第 3 页获取到 100 条记录
2025-05-24 03:00:21,564 - INFO - Request Parameters - Page 4:
2025-05-24 03:00:21,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:21,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:22,048 - INFO - Response - Page 4:
2025-05-24 03:00:22,252 - INFO - 第 4 页获取到 100 条记录
2025-05-24 03:00:22,252 - INFO - Request Parameters - Page 5:
2025-05-24 03:00:22,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:22,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:22,767 - INFO - Response - Page 5:
2025-05-24 03:00:22,970 - INFO - 第 5 页获取到 100 条记录
2025-05-24 03:00:22,970 - INFO - Request Parameters - Page 6:
2025-05-24 03:00:22,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:22,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:23,486 - INFO - Response - Page 6:
2025-05-24 03:00:23,689 - INFO - 第 6 页获取到 100 条记录
2025-05-24 03:00:23,689 - INFO - Request Parameters - Page 7:
2025-05-24 03:00:23,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:23,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:24,095 - INFO - Response - Page 7:
2025-05-24 03:00:24,298 - INFO - 第 7 页获取到 56 条记录
2025-05-24 03:00:24,298 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 03:00:24,298 - INFO - 获取到 656 条表单数据
2025-05-24 03:00:24,314 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 03:00:24,330 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 03:00:24,330 - INFO - 开始处理日期: 2025-05
2025-05-24 03:00:24,330 - INFO - Request Parameters - Page 1:
2025-05-24 03:00:24,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:24,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:24,908 - INFO - Response - Page 1:
2025-05-24 03:00:25,111 - INFO - 第 1 页获取到 100 条记录
2025-05-24 03:00:25,111 - INFO - Request Parameters - Page 2:
2025-05-24 03:00:25,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:25,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:25,580 - INFO - Response - Page 2:
2025-05-24 03:00:25,783 - INFO - 第 2 页获取到 100 条记录
2025-05-24 03:00:25,783 - INFO - Request Parameters - Page 3:
2025-05-24 03:00:25,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:25,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:26,299 - INFO - Response - Page 3:
2025-05-24 03:00:26,502 - INFO - 第 3 页获取到 100 条记录
2025-05-24 03:00:26,502 - INFO - Request Parameters - Page 4:
2025-05-24 03:00:26,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:26,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:27,096 - INFO - Response - Page 4:
2025-05-24 03:00:27,299 - INFO - 第 4 页获取到 100 条记录
2025-05-24 03:00:27,299 - INFO - Request Parameters - Page 5:
2025-05-24 03:00:27,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:27,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:27,783 - INFO - Response - Page 5:
2025-05-24 03:00:27,986 - INFO - 第 5 页获取到 100 条记录
2025-05-24 03:00:27,986 - INFO - Request Parameters - Page 6:
2025-05-24 03:00:27,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:27,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:28,502 - INFO - Response - Page 6:
2025-05-24 03:00:28,705 - INFO - 第 6 页获取到 100 条记录
2025-05-24 03:00:28,705 - INFO - Request Parameters - Page 7:
2025-05-24 03:00:28,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:00:28,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:00:29,080 - INFO - Response - Page 7:
2025-05-24 03:00:29,283 - INFO - 第 7 页获取到 28 条记录
2025-05-24 03:00:29,283 - INFO - 查询完成，共获取到 628 条记录
2025-05-24 03:00:29,283 - INFO - 获取到 628 条表单数据
2025-05-24 03:00:29,283 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-24 03:00:29,299 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-24 03:00:29,814 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-24 03:00:29,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9570.0, 'new_value': 11370.0}, {'field': 'total_amount', 'old_value': 9570.0, 'new_value': 11370.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-24 03:00:29,814 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-24 03:00:29,814 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-24 03:00:29,814 - INFO - =================同步完成====================
2025-05-24 06:00:01,953 - INFO - =================使用默认全量同步=============
2025-05-24 06:00:03,406 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-24 06:00:03,406 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 06:00:03,437 - INFO - 开始处理日期: 2025-01
2025-05-24 06:00:03,437 - INFO - Request Parameters - Page 1:
2025-05-24 06:00:03,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:03,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:04,344 - INFO - Response - Page 1:
2025-05-24 06:00:04,547 - INFO - 第 1 页获取到 100 条记录
2025-05-24 06:00:04,547 - INFO - Request Parameters - Page 2:
2025-05-24 06:00:04,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:04,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:05,469 - INFO - Response - Page 2:
2025-05-24 06:00:05,672 - INFO - 第 2 页获取到 100 条记录
2025-05-24 06:00:05,672 - INFO - Request Parameters - Page 3:
2025-05-24 06:00:05,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:05,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:06,250 - INFO - Response - Page 3:
2025-05-24 06:00:06,453 - INFO - 第 3 页获取到 100 条记录
2025-05-24 06:00:06,453 - INFO - Request Parameters - Page 4:
2025-05-24 06:00:06,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:06,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:06,937 - INFO - Response - Page 4:
2025-05-24 06:00:07,141 - INFO - 第 4 页获取到 100 条记录
2025-05-24 06:00:07,141 - INFO - Request Parameters - Page 5:
2025-05-24 06:00:07,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:07,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:07,734 - INFO - Response - Page 5:
2025-05-24 06:00:07,937 - INFO - 第 5 页获取到 100 条记录
2025-05-24 06:00:07,937 - INFO - Request Parameters - Page 6:
2025-05-24 06:00:07,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:07,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:08,422 - INFO - Response - Page 6:
2025-05-24 06:00:08,625 - INFO - 第 6 页获取到 100 条记录
2025-05-24 06:00:08,625 - INFO - Request Parameters - Page 7:
2025-05-24 06:00:08,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:08,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:09,047 - INFO - Response - Page 7:
2025-05-24 06:00:09,250 - INFO - 第 7 页获取到 82 条记录
2025-05-24 06:00:09,250 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 06:00:09,250 - INFO - 获取到 682 条表单数据
2025-05-24 06:00:09,250 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 06:00:09,266 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 06:00:09,266 - INFO - 开始处理日期: 2025-02
2025-05-24 06:00:09,266 - INFO - Request Parameters - Page 1:
2025-05-24 06:00:09,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:09,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:09,766 - INFO - Response - Page 1:
2025-05-24 06:00:09,969 - INFO - 第 1 页获取到 100 条记录
2025-05-24 06:00:09,969 - INFO - Request Parameters - Page 2:
2025-05-24 06:00:09,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:09,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:10,547 - INFO - Response - Page 2:
2025-05-24 06:00:10,750 - INFO - 第 2 页获取到 100 条记录
2025-05-24 06:00:10,750 - INFO - Request Parameters - Page 3:
2025-05-24 06:00:10,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:10,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:11,266 - INFO - Response - Page 3:
2025-05-24 06:00:11,469 - INFO - 第 3 页获取到 100 条记录
2025-05-24 06:00:11,469 - INFO - Request Parameters - Page 4:
2025-05-24 06:00:11,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:11,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:11,937 - INFO - Response - Page 4:
2025-05-24 06:00:12,141 - INFO - 第 4 页获取到 100 条记录
2025-05-24 06:00:12,141 - INFO - Request Parameters - Page 5:
2025-05-24 06:00:12,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:12,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:12,609 - INFO - Response - Page 5:
2025-05-24 06:00:12,812 - INFO - 第 5 页获取到 100 条记录
2025-05-24 06:00:12,812 - INFO - Request Parameters - Page 6:
2025-05-24 06:00:12,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:12,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:13,359 - INFO - Response - Page 6:
2025-05-24 06:00:13,562 - INFO - 第 6 页获取到 100 条记录
2025-05-24 06:00:13,562 - INFO - Request Parameters - Page 7:
2025-05-24 06:00:13,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:13,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:14,016 - INFO - Response - Page 7:
2025-05-24 06:00:14,219 - INFO - 第 7 页获取到 70 条记录
2025-05-24 06:00:14,219 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 06:00:14,219 - INFO - 获取到 670 条表单数据
2025-05-24 06:00:14,219 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 06:00:14,234 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 06:00:14,234 - INFO - 开始处理日期: 2025-03
2025-05-24 06:00:14,234 - INFO - Request Parameters - Page 1:
2025-05-24 06:00:14,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:14,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:14,750 - INFO - Response - Page 1:
2025-05-24 06:00:14,953 - INFO - 第 1 页获取到 100 条记录
2025-05-24 06:00:14,953 - INFO - Request Parameters - Page 2:
2025-05-24 06:00:14,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:14,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:15,437 - INFO - Response - Page 2:
2025-05-24 06:00:15,641 - INFO - 第 2 页获取到 100 条记录
2025-05-24 06:00:15,641 - INFO - Request Parameters - Page 3:
2025-05-24 06:00:15,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:15,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:16,125 - INFO - Response - Page 3:
2025-05-24 06:00:16,328 - INFO - 第 3 页获取到 100 条记录
2025-05-24 06:00:16,328 - INFO - Request Parameters - Page 4:
2025-05-24 06:00:16,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:16,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:16,828 - INFO - Response - Page 4:
2025-05-24 06:00:17,031 - INFO - 第 4 页获取到 100 条记录
2025-05-24 06:00:17,031 - INFO - Request Parameters - Page 5:
2025-05-24 06:00:17,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:17,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:17,516 - INFO - Response - Page 5:
2025-05-24 06:00:17,719 - INFO - 第 5 页获取到 100 条记录
2025-05-24 06:00:17,719 - INFO - Request Parameters - Page 6:
2025-05-24 06:00:17,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:17,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:18,219 - INFO - Response - Page 6:
2025-05-24 06:00:18,422 - INFO - 第 6 页获取到 100 条记录
2025-05-24 06:00:18,422 - INFO - Request Parameters - Page 7:
2025-05-24 06:00:18,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:18,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:18,875 - INFO - Response - Page 7:
2025-05-24 06:00:19,078 - INFO - 第 7 页获取到 61 条记录
2025-05-24 06:00:19,078 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 06:00:19,078 - INFO - 获取到 661 条表单数据
2025-05-24 06:00:19,078 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 06:00:19,094 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 06:00:19,094 - INFO - 开始处理日期: 2025-04
2025-05-24 06:00:19,094 - INFO - Request Parameters - Page 1:
2025-05-24 06:00:19,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:19,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:19,672 - INFO - Response - Page 1:
2025-05-24 06:00:19,875 - INFO - 第 1 页获取到 100 条记录
2025-05-24 06:00:19,875 - INFO - Request Parameters - Page 2:
2025-05-24 06:00:19,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:19,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:20,328 - INFO - Response - Page 2:
2025-05-24 06:00:20,531 - INFO - 第 2 页获取到 100 条记录
2025-05-24 06:00:20,531 - INFO - Request Parameters - Page 3:
2025-05-24 06:00:20,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:20,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:21,031 - INFO - Response - Page 3:
2025-05-24 06:00:21,234 - INFO - 第 3 页获取到 100 条记录
2025-05-24 06:00:21,234 - INFO - Request Parameters - Page 4:
2025-05-24 06:00:21,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:21,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:21,812 - INFO - Response - Page 4:
2025-05-24 06:00:22,015 - INFO - 第 4 页获取到 100 条记录
2025-05-24 06:00:22,015 - INFO - Request Parameters - Page 5:
2025-05-24 06:00:22,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:22,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:22,531 - INFO - Response - Page 5:
2025-05-24 06:00:22,734 - INFO - 第 5 页获取到 100 条记录
2025-05-24 06:00:22,734 - INFO - Request Parameters - Page 6:
2025-05-24 06:00:22,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:22,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:23,281 - INFO - Response - Page 6:
2025-05-24 06:00:23,484 - INFO - 第 6 页获取到 100 条记录
2025-05-24 06:00:23,484 - INFO - Request Parameters - Page 7:
2025-05-24 06:00:23,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:23,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:23,984 - INFO - Response - Page 7:
2025-05-24 06:00:24,187 - INFO - 第 7 页获取到 56 条记录
2025-05-24 06:00:24,187 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 06:00:24,187 - INFO - 获取到 656 条表单数据
2025-05-24 06:00:24,187 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 06:00:24,203 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 06:00:24,203 - INFO - 开始处理日期: 2025-05
2025-05-24 06:00:24,203 - INFO - Request Parameters - Page 1:
2025-05-24 06:00:24,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:24,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:24,781 - INFO - Response - Page 1:
2025-05-24 06:00:24,984 - INFO - 第 1 页获取到 100 条记录
2025-05-24 06:00:24,984 - INFO - Request Parameters - Page 2:
2025-05-24 06:00:24,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:24,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:25,484 - INFO - Response - Page 2:
2025-05-24 06:00:25,687 - INFO - 第 2 页获取到 100 条记录
2025-05-24 06:00:25,687 - INFO - Request Parameters - Page 3:
2025-05-24 06:00:25,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:25,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:26,125 - INFO - Response - Page 3:
2025-05-24 06:00:26,328 - INFO - 第 3 页获取到 100 条记录
2025-05-24 06:00:26,328 - INFO - Request Parameters - Page 4:
2025-05-24 06:00:26,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:26,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:26,797 - INFO - Response - Page 4:
2025-05-24 06:00:27,000 - INFO - 第 4 页获取到 100 条记录
2025-05-24 06:00:27,000 - INFO - Request Parameters - Page 5:
2025-05-24 06:00:27,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:27,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:27,469 - INFO - Response - Page 5:
2025-05-24 06:00:27,672 - INFO - 第 5 页获取到 100 条记录
2025-05-24 06:00:27,672 - INFO - Request Parameters - Page 6:
2025-05-24 06:00:27,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:27,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:28,187 - INFO - Response - Page 6:
2025-05-24 06:00:28,390 - INFO - 第 6 页获取到 100 条记录
2025-05-24 06:00:28,390 - INFO - Request Parameters - Page 7:
2025-05-24 06:00:28,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:00:28,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:00:28,750 - INFO - Response - Page 7:
2025-05-24 06:00:28,953 - INFO - 第 7 页获取到 28 条记录
2025-05-24 06:00:28,953 - INFO - 查询完成，共获取到 628 条记录
2025-05-24 06:00:28,953 - INFO - 获取到 628 条表单数据
2025-05-24 06:00:28,953 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-24 06:00:28,969 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-24 06:00:29,375 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-24 06:00:29,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70168.0, 'new_value': 73544.0}, {'field': 'total_amount', 'old_value': 72018.0, 'new_value': 75394.0}, {'field': 'order_count', 'old_value': 418, 'new_value': 435}]
2025-05-24 06:00:29,375 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-24 06:00:29,375 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-24 06:00:29,375 - INFO - =================同步完成====================
2025-05-24 09:00:01,946 - INFO - =================使用默认全量同步=============
2025-05-24 09:00:03,367 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-24 09:00:03,367 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 09:00:03,399 - INFO - 开始处理日期: 2025-01
2025-05-24 09:00:03,399 - INFO - Request Parameters - Page 1:
2025-05-24 09:00:03,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:03,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:04,367 - INFO - Response - Page 1:
2025-05-24 09:00:04,571 - INFO - 第 1 页获取到 100 条记录
2025-05-24 09:00:04,571 - INFO - Request Parameters - Page 2:
2025-05-24 09:00:04,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:04,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:05,492 - INFO - Response - Page 2:
2025-05-24 09:00:05,695 - INFO - 第 2 页获取到 100 条记录
2025-05-24 09:00:05,695 - INFO - Request Parameters - Page 3:
2025-05-24 09:00:05,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:05,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:06,227 - INFO - Response - Page 3:
2025-05-24 09:00:06,430 - INFO - 第 3 页获取到 100 条记录
2025-05-24 09:00:06,430 - INFO - Request Parameters - Page 4:
2025-05-24 09:00:06,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:06,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:06,977 - INFO - Response - Page 4:
2025-05-24 09:00:07,180 - INFO - 第 4 页获取到 100 条记录
2025-05-24 09:00:07,180 - INFO - Request Parameters - Page 5:
2025-05-24 09:00:07,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:07,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:07,664 - INFO - Response - Page 5:
2025-05-24 09:00:07,883 - INFO - 第 5 页获取到 100 条记录
2025-05-24 09:00:07,883 - INFO - Request Parameters - Page 6:
2025-05-24 09:00:07,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:07,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:08,352 - INFO - Response - Page 6:
2025-05-24 09:00:08,555 - INFO - 第 6 页获取到 100 条记录
2025-05-24 09:00:08,555 - INFO - Request Parameters - Page 7:
2025-05-24 09:00:08,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:08,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:09,117 - INFO - Response - Page 7:
2025-05-24 09:00:09,320 - INFO - 第 7 页获取到 82 条记录
2025-05-24 09:00:09,320 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 09:00:09,320 - INFO - 获取到 682 条表单数据
2025-05-24 09:00:09,320 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 09:00:09,336 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 09:00:09,336 - INFO - 开始处理日期: 2025-02
2025-05-24 09:00:09,336 - INFO - Request Parameters - Page 1:
2025-05-24 09:00:09,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:09,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:09,805 - INFO - Response - Page 1:
2025-05-24 09:00:10,008 - INFO - 第 1 页获取到 100 条记录
2025-05-24 09:00:10,008 - INFO - Request Parameters - Page 2:
2025-05-24 09:00:10,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:10,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:10,539 - INFO - Response - Page 2:
2025-05-24 09:00:10,742 - INFO - 第 2 页获取到 100 条记录
2025-05-24 09:00:10,742 - INFO - Request Parameters - Page 3:
2025-05-24 09:00:10,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:10,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:11,305 - INFO - Response - Page 3:
2025-05-24 09:00:11,508 - INFO - 第 3 页获取到 100 条记录
2025-05-24 09:00:11,508 - INFO - Request Parameters - Page 4:
2025-05-24 09:00:11,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:11,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:12,086 - INFO - Response - Page 4:
2025-05-24 09:00:12,289 - INFO - 第 4 页获取到 100 条记录
2025-05-24 09:00:12,289 - INFO - Request Parameters - Page 5:
2025-05-24 09:00:12,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:12,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:12,789 - INFO - Response - Page 5:
2025-05-24 09:00:12,992 - INFO - 第 5 页获取到 100 条记录
2025-05-24 09:00:12,992 - INFO - Request Parameters - Page 6:
2025-05-24 09:00:12,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:12,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:13,492 - INFO - Response - Page 6:
2025-05-24 09:00:13,695 - INFO - 第 6 页获取到 100 条记录
2025-05-24 09:00:13,695 - INFO - Request Parameters - Page 7:
2025-05-24 09:00:13,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:13,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:14,211 - INFO - Response - Page 7:
2025-05-24 09:00:14,414 - INFO - 第 7 页获取到 70 条记录
2025-05-24 09:00:14,414 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 09:00:14,414 - INFO - 获取到 670 条表单数据
2025-05-24 09:00:14,414 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 09:00:14,430 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 09:00:14,430 - INFO - 开始处理日期: 2025-03
2025-05-24 09:00:14,430 - INFO - Request Parameters - Page 1:
2025-05-24 09:00:14,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:14,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:14,914 - INFO - Response - Page 1:
2025-05-24 09:00:15,117 - INFO - 第 1 页获取到 100 条记录
2025-05-24 09:00:15,117 - INFO - Request Parameters - Page 2:
2025-05-24 09:00:15,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:15,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:15,617 - INFO - Response - Page 2:
2025-05-24 09:00:15,820 - INFO - 第 2 页获取到 100 条记录
2025-05-24 09:00:15,820 - INFO - Request Parameters - Page 3:
2025-05-24 09:00:15,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:15,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:16,383 - INFO - Response - Page 3:
2025-05-24 09:00:16,586 - INFO - 第 3 页获取到 100 条记录
2025-05-24 09:00:16,586 - INFO - Request Parameters - Page 4:
2025-05-24 09:00:16,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:16,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:17,102 - INFO - Response - Page 4:
2025-05-24 09:00:17,305 - INFO - 第 4 页获取到 100 条记录
2025-05-24 09:00:17,305 - INFO - Request Parameters - Page 5:
2025-05-24 09:00:17,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:17,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:17,805 - INFO - Response - Page 5:
2025-05-24 09:00:18,008 - INFO - 第 5 页获取到 100 条记录
2025-05-24 09:00:18,008 - INFO - Request Parameters - Page 6:
2025-05-24 09:00:18,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:18,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:18,508 - INFO - Response - Page 6:
2025-05-24 09:00:18,711 - INFO - 第 6 页获取到 100 条记录
2025-05-24 09:00:18,711 - INFO - Request Parameters - Page 7:
2025-05-24 09:00:18,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:18,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:19,164 - INFO - Response - Page 7:
2025-05-24 09:00:19,367 - INFO - 第 7 页获取到 61 条记录
2025-05-24 09:00:19,367 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 09:00:19,367 - INFO - 获取到 661 条表单数据
2025-05-24 09:00:19,367 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 09:00:19,383 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 09:00:19,383 - INFO - 开始处理日期: 2025-04
2025-05-24 09:00:19,383 - INFO - Request Parameters - Page 1:
2025-05-24 09:00:19,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:19,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:19,930 - INFO - Response - Page 1:
2025-05-24 09:00:20,133 - INFO - 第 1 页获取到 100 条记录
2025-05-24 09:00:20,133 - INFO - Request Parameters - Page 2:
2025-05-24 09:00:20,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:20,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:20,586 - INFO - Response - Page 2:
2025-05-24 09:00:20,789 - INFO - 第 2 页获取到 100 条记录
2025-05-24 09:00:20,789 - INFO - Request Parameters - Page 3:
2025-05-24 09:00:20,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:20,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:21,352 - INFO - Response - Page 3:
2025-05-24 09:00:21,555 - INFO - 第 3 页获取到 100 条记录
2025-05-24 09:00:21,555 - INFO - Request Parameters - Page 4:
2025-05-24 09:00:21,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:21,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:22,055 - INFO - Response - Page 4:
2025-05-24 09:00:22,258 - INFO - 第 4 页获取到 100 条记录
2025-05-24 09:00:22,258 - INFO - Request Parameters - Page 5:
2025-05-24 09:00:22,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:22,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:22,758 - INFO - Response - Page 5:
2025-05-24 09:00:22,961 - INFO - 第 5 页获取到 100 条记录
2025-05-24 09:00:22,961 - INFO - Request Parameters - Page 6:
2025-05-24 09:00:22,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:22,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:23,492 - INFO - Response - Page 6:
2025-05-24 09:00:23,695 - INFO - 第 6 页获取到 100 条记录
2025-05-24 09:00:23,695 - INFO - Request Parameters - Page 7:
2025-05-24 09:00:23,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:23,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:24,180 - INFO - Response - Page 7:
2025-05-24 09:00:24,383 - INFO - 第 7 页获取到 56 条记录
2025-05-24 09:00:24,383 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 09:00:24,383 - INFO - 获取到 656 条表单数据
2025-05-24 09:00:24,383 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 09:00:24,399 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 09:00:24,399 - INFO - 开始处理日期: 2025-05
2025-05-24 09:00:24,399 - INFO - Request Parameters - Page 1:
2025-05-24 09:00:24,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:24,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:24,930 - INFO - Response - Page 1:
2025-05-24 09:00:25,133 - INFO - 第 1 页获取到 100 条记录
2025-05-24 09:00:25,133 - INFO - Request Parameters - Page 2:
2025-05-24 09:00:25,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:25,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:25,648 - INFO - Response - Page 2:
2025-05-24 09:00:25,852 - INFO - 第 2 页获取到 100 条记录
2025-05-24 09:00:25,852 - INFO - Request Parameters - Page 3:
2025-05-24 09:00:25,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:25,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:26,352 - INFO - Response - Page 3:
2025-05-24 09:00:26,555 - INFO - 第 3 页获取到 100 条记录
2025-05-24 09:00:26,555 - INFO - Request Parameters - Page 4:
2025-05-24 09:00:26,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:26,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:26,992 - INFO - Response - Page 4:
2025-05-24 09:00:27,195 - INFO - 第 4 页获取到 100 条记录
2025-05-24 09:00:27,195 - INFO - Request Parameters - Page 5:
2025-05-24 09:00:27,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:27,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:27,695 - INFO - Response - Page 5:
2025-05-24 09:00:27,898 - INFO - 第 5 页获取到 100 条记录
2025-05-24 09:00:27,898 - INFO - Request Parameters - Page 6:
2025-05-24 09:00:27,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:27,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:28,398 - INFO - Response - Page 6:
2025-05-24 09:00:28,602 - INFO - 第 6 页获取到 100 条记录
2025-05-24 09:00:28,602 - INFO - Request Parameters - Page 7:
2025-05-24 09:00:28,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:00:28,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:00:28,945 - INFO - Response - Page 7:
2025-05-24 09:00:29,148 - INFO - 第 7 页获取到 28 条记录
2025-05-24 09:00:29,148 - INFO - 查询完成，共获取到 628 条记录
2025-05-24 09:00:29,148 - INFO - 获取到 628 条表单数据
2025-05-24 09:00:29,148 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-24 09:00:29,148 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-24 09:00:29,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-24 09:00:29,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8700000.0, 'new_value': 9100080.0}, {'field': 'total_amount', 'old_value': 8800000.0, 'new_value': 9200080.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-24 09:00:29,602 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-24 09:00:30,086 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-24 09:00:30,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68019.0, 'new_value': 72003.0}, {'field': 'total_amount', 'old_value': 68019.0, 'new_value': 72003.0}, {'field': 'order_count', 'old_value': 595, 'new_value': 626}]
2025-05-24 09:00:30,086 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-24 09:00:30,570 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-24 09:00:30,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26365.2, 'new_value': 26379.79}, {'field': 'total_amount', 'old_value': 59933.42, 'new_value': 59948.01}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-05-24 09:00:30,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-24 09:00:31,055 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-24 09:00:31,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5849.32, 'new_value': 6354.16}, {'field': 'offline_amount', 'old_value': 180762.42, 'new_value': 191773.42}, {'field': 'total_amount', 'old_value': 186611.74, 'new_value': 198127.58}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1219}]
2025-05-24 09:00:31,055 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-24 09:00:31,555 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-24 09:00:31,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22613.4, 'new_value': 23162.8}, {'field': 'total_amount', 'old_value': 22678.95, 'new_value': 23228.35}, {'field': 'order_count', 'old_value': 200, 'new_value': 202}]
2025-05-24 09:00:31,555 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-24 09:00:32,117 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-24 09:00:32,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 204537.78, 'new_value': 214642.03}, {'field': 'offline_amount', 'old_value': 99735.85, 'new_value': 101272.85}, {'field': 'total_amount', 'old_value': 304273.63, 'new_value': 315914.88}, {'field': 'order_count', 'old_value': 1143, 'new_value': 1204}]
2025-05-24 09:00:32,117 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-24 09:00:32,570 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-24 09:00:32,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 282545.0, 'new_value': 286476.0}, {'field': 'total_amount', 'old_value': 282545.0, 'new_value': 286476.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 236}]
2025-05-24 09:00:32,570 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-24 09:00:32,977 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-24 09:00:32,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265579.0, 'new_value': 273354.5}, {'field': 'total_amount', 'old_value': 265579.0, 'new_value': 273354.5}, {'field': 'order_count', 'old_value': 2904, 'new_value': 2977}]
2025-05-24 09:00:32,977 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-24 09:00:33,445 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-24 09:00:33,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211251.42, 'new_value': 217304.3}, {'field': 'total_amount', 'old_value': 211251.42, 'new_value': 217304.3}, {'field': 'order_count', 'old_value': 375, 'new_value': 386}]
2025-05-24 09:00:33,445 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-24 09:00:33,883 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-24 09:00:33,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 429306.25, 'new_value': 451183.96}, {'field': 'total_amount', 'old_value': 538050.93, 'new_value': 559928.64}, {'field': 'order_count', 'old_value': 2237, 'new_value': 2297}]
2025-05-24 09:00:33,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-24 09:00:34,320 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-24 09:00:34,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93644.38, 'new_value': 96750.66}, {'field': 'total_amount', 'old_value': 99255.9, 'new_value': 102362.18}, {'field': 'order_count', 'old_value': 8934, 'new_value': 9264}]
2025-05-24 09:00:34,320 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-24 09:00:34,805 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-24 09:00:34,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65843.0, 'new_value': 68547.0}, {'field': 'total_amount', 'old_value': 65843.0, 'new_value': 68547.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 92}]
2025-05-24 09:00:34,805 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-24 09:00:35,273 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-24 09:00:35,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 333874.46, 'new_value': 348144.81}, {'field': 'total_amount', 'old_value': 333874.46, 'new_value': 348144.81}, {'field': 'order_count', 'old_value': 1649, 'new_value': 1713}]
2025-05-24 09:00:35,273 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-24 09:00:35,773 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-24 09:00:35,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 614128.15, 'new_value': 627485.15}, {'field': 'total_amount', 'old_value': 614128.15, 'new_value': 627485.15}, {'field': 'order_count', 'old_value': 1639, 'new_value': 1693}]
2025-05-24 09:00:35,773 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-24 09:00:36,242 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-24 09:00:36,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 640075.0, 'new_value': 656060.0}, {'field': 'total_amount', 'old_value': 640075.0, 'new_value': 656060.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 100}]
2025-05-24 09:00:36,242 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-24 09:00:36,680 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-24 09:00:36,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20026.0, 'new_value': 20758.0}, {'field': 'total_amount', 'old_value': 20026.0, 'new_value': 20758.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-24 09:00:36,680 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-24 09:00:37,148 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-24 09:00:37,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173563.0, 'new_value': 176631.0}, {'field': 'total_amount', 'old_value': 173563.0, 'new_value': 176631.0}, {'field': 'order_count', 'old_value': 260, 'new_value': 3328}]
2025-05-24 09:00:37,148 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-24 09:00:37,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-24 09:00:37,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 665260.0, 'new_value': 686389.0}, {'field': 'total_amount', 'old_value': 665260.0, 'new_value': 686389.0}, {'field': 'order_count', 'old_value': 158, 'new_value': 165}]
2025-05-24 09:00:37,602 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-24 09:00:38,023 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-24 09:00:38,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69106.0, 'new_value': 69836.0}, {'field': 'total_amount', 'old_value': 69106.0, 'new_value': 69836.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 176}]
2025-05-24 09:00:38,039 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-24 09:00:38,445 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-24 09:00:38,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44507.13, 'new_value': 46355.93}, {'field': 'offline_amount', 'old_value': 301696.36, 'new_value': 314109.52}, {'field': 'total_amount', 'old_value': 346203.49, 'new_value': 360465.45}, {'field': 'order_count', 'old_value': 2189, 'new_value': 2283}]
2025-05-24 09:00:38,445 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-24 09:00:38,914 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-24 09:00:38,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67614.0, 'new_value': 69269.0}, {'field': 'total_amount', 'old_value': 67614.0, 'new_value': 69269.0}, {'field': 'order_count', 'old_value': 1995, 'new_value': 2045}]
2025-05-24 09:00:38,914 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-24 09:00:39,352 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-24 09:00:39,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8956.61, 'new_value': 9133.41}, {'field': 'total_amount', 'old_value': 24256.61, 'new_value': 24433.41}, {'field': 'order_count', 'old_value': 140, 'new_value': 142}]
2025-05-24 09:00:39,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-24 09:00:39,789 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-24 09:00:39,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112831.5, 'new_value': 115893.6}, {'field': 'total_amount', 'old_value': 112831.5, 'new_value': 115893.6}, {'field': 'order_count', 'old_value': 219, 'new_value': 226}]
2025-05-24 09:00:39,789 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-24 09:00:40,352 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-24 09:00:40,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315197.5, 'new_value': 322625.3}, {'field': 'total_amount', 'old_value': 315197.5, 'new_value': 322625.3}, {'field': 'order_count', 'old_value': 388, 'new_value': 400}]
2025-05-24 09:00:40,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-24 09:00:40,836 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-24 09:00:40,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 797081.0, 'new_value': 815080.0}, {'field': 'total_amount', 'old_value': 797081.0, 'new_value': 815080.0}, {'field': 'order_count', 'old_value': 50914, 'new_value': 50934}]
2025-05-24 09:00:40,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-24 09:00:41,367 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-24 09:00:41,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24115.0, 'new_value': 25323.0}, {'field': 'total_amount', 'old_value': 24115.0, 'new_value': 25323.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 142}]
2025-05-24 09:00:41,367 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-24 09:00:41,852 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-24 09:00:41,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 575896.0, 'new_value': 610990.0}, {'field': 'total_amount', 'old_value': 575896.0, 'new_value': 610990.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 70}]
2025-05-24 09:00:41,852 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-24 09:00:42,352 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-24 09:00:42,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 289274.12, 'new_value': 297847.97}, {'field': 'offline_amount', 'old_value': 1107617.98, 'new_value': 1147259.49}, {'field': 'total_amount', 'old_value': 1396892.1, 'new_value': 1445107.46}, {'field': 'order_count', 'old_value': 7021, 'new_value': 7255}]
2025-05-24 09:00:42,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-24 09:00:42,836 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-24 09:00:42,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119823.1, 'new_value': 124251.1}, {'field': 'offline_amount', 'old_value': 97094.1, 'new_value': 102378.1}, {'field': 'total_amount', 'old_value': 216917.2, 'new_value': 226629.2}, {'field': 'order_count', 'old_value': 5121, 'new_value': 5295}]
2025-05-24 09:00:42,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-24 09:00:43,258 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-24 09:00:43,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1980000.0, 'new_value': 2030000.0}, {'field': 'total_amount', 'old_value': 1980000.0, 'new_value': 2030000.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 280}]
2025-05-24 09:00:43,258 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-24 09:00:43,727 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-24 09:00:43,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36001.94, 'new_value': 38187.45}, {'field': 'offline_amount', 'old_value': 966122.88, 'new_value': 1008513.89}, {'field': 'total_amount', 'old_value': 1002124.82, 'new_value': 1046701.34}, {'field': 'order_count', 'old_value': 4908, 'new_value': 5125}]
2025-05-24 09:00:43,727 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-24 09:00:44,461 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-24 09:00:44,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211506.21, 'new_value': 221233.11}, {'field': 'offline_amount', 'old_value': 653712.43, 'new_value': 671752.42}, {'field': 'total_amount', 'old_value': 865218.64, 'new_value': 892985.53}, {'field': 'order_count', 'old_value': 5158, 'new_value': 5352}]
2025-05-24 09:00:44,461 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-24 09:00:44,898 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-24 09:00:44,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 495000.0, 'new_value': 500000.0}, {'field': 'total_amount', 'old_value': 495000.0, 'new_value': 500000.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 147}]
2025-05-24 09:00:44,898 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-24 09:00:45,367 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-24 09:00:45,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475000.0, 'new_value': 480000.0}, {'field': 'total_amount', 'old_value': 475000.0, 'new_value': 480000.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 146}]
2025-05-24 09:00:45,367 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-24 09:00:45,883 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-24 09:00:45,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3098674.0, 'new_value': 3148674.0}, {'field': 'total_amount', 'old_value': 3098674.0, 'new_value': 3148674.0}, {'field': 'order_count', 'old_value': 299, 'new_value': 300}]
2025-05-24 09:00:45,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-24 09:00:46,320 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-24 09:00:46,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62301.1, 'new_value': 63469.1}, {'field': 'total_amount', 'old_value': 63106.1, 'new_value': 64274.1}, {'field': 'order_count', 'old_value': 16294, 'new_value': 16297}]
2025-05-24 09:00:46,320 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-24 09:00:46,883 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-24 09:00:46,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105641.0, 'new_value': 108731.0}, {'field': 'total_amount', 'old_value': 105641.0, 'new_value': 108731.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 21}]
2025-05-24 09:00:46,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-24 09:00:47,414 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-24 09:00:47,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254581.36, 'new_value': 258862.9}, {'field': 'total_amount', 'old_value': 254581.36, 'new_value': 258862.9}, {'field': 'order_count', 'old_value': 1426, 'new_value': 1458}]
2025-05-24 09:00:47,414 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-24 09:00:47,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-24 09:00:47,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52669.92, 'new_value': 53828.1}, {'field': 'offline_amount', 'old_value': 39957.55, 'new_value': 41443.49}, {'field': 'total_amount', 'old_value': 92627.47, 'new_value': 95271.59}, {'field': 'order_count', 'old_value': 7784, 'new_value': 7989}]
2025-05-24 09:00:47,930 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-24 09:00:48,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-24 09:00:48,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321252.9, 'new_value': 330153.9}, {'field': 'total_amount', 'old_value': 321252.9, 'new_value': 330153.9}]
2025-05-24 09:00:48,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-24 09:00:48,805 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-24 09:00:48,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85413.56, 'new_value': 87953.56}, {'field': 'total_amount', 'old_value': 85413.56, 'new_value': 87953.56}, {'field': 'order_count', 'old_value': 4398, 'new_value': 4522}]
2025-05-24 09:00:48,805 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-24 09:00:49,289 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-24 09:00:49,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3704.0, 'new_value': 4512.0}, {'field': 'offline_amount', 'old_value': 49443.0, 'new_value': 51311.0}, {'field': 'total_amount', 'old_value': 53147.0, 'new_value': 55823.0}, {'field': 'order_count', 'old_value': 418, 'new_value': 434}]
2025-05-24 09:00:49,289 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-24 09:00:49,742 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-24 09:00:49,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 394178.0, 'new_value': 404747.0}, {'field': 'total_amount', 'old_value': 394178.0, 'new_value': 404747.0}, {'field': 'order_count', 'old_value': 8923, 'new_value': 9169}]
2025-05-24 09:00:49,742 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-24 09:00:50,180 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-24 09:00:50,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56143.18, 'new_value': 59813.18}, {'field': 'total_amount', 'old_value': 61503.15, 'new_value': 65173.15}, {'field': 'order_count', 'old_value': 965, 'new_value': 1065}]
2025-05-24 09:00:50,180 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-24 09:00:50,617 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-24 09:00:50,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4411277.51, 'new_value': 4607573.51}, {'field': 'total_amount', 'old_value': 4411277.51, 'new_value': 4607573.51}, {'field': 'order_count', 'old_value': 91220, 'new_value': 94958}]
2025-05-24 09:00:50,617 - INFO - 日期 2025-05 处理完成 - 更新: 45 条，插入: 0 条，错误: 0 条
2025-05-24 09:00:50,617 - INFO - 数据同步完成！更新: 45 条，插入: 0 条，错误: 0 条
2025-05-24 09:00:50,617 - INFO - =================同步完成====================
2025-05-24 12:00:01,954 - INFO - =================使用默认全量同步=============
2025-05-24 12:00:03,422 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-24 12:00:03,422 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 12:00:03,469 - INFO - 开始处理日期: 2025-01
2025-05-24 12:00:03,469 - INFO - Request Parameters - Page 1:
2025-05-24 12:00:03,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:03,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:04,532 - INFO - Response - Page 1:
2025-05-24 12:00:04,735 - INFO - 第 1 页获取到 100 条记录
2025-05-24 12:00:04,735 - INFO - Request Parameters - Page 2:
2025-05-24 12:00:04,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:04,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:05,375 - INFO - Response - Page 2:
2025-05-24 12:00:05,579 - INFO - 第 2 页获取到 100 条记录
2025-05-24 12:00:05,579 - INFO - Request Parameters - Page 3:
2025-05-24 12:00:05,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:05,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:06,329 - INFO - Response - Page 3:
2025-05-24 12:00:06,532 - INFO - 第 3 页获取到 100 条记录
2025-05-24 12:00:06,532 - INFO - Request Parameters - Page 4:
2025-05-24 12:00:06,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:06,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:07,094 - INFO - Response - Page 4:
2025-05-24 12:00:07,297 - INFO - 第 4 页获取到 100 条记录
2025-05-24 12:00:07,297 - INFO - Request Parameters - Page 5:
2025-05-24 12:00:07,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:07,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:07,782 - INFO - Response - Page 5:
2025-05-24 12:00:07,985 - INFO - 第 5 页获取到 100 条记录
2025-05-24 12:00:07,985 - INFO - Request Parameters - Page 6:
2025-05-24 12:00:07,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:07,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:08,563 - INFO - Response - Page 6:
2025-05-24 12:00:08,766 - INFO - 第 6 页获取到 100 条记录
2025-05-24 12:00:08,766 - INFO - Request Parameters - Page 7:
2025-05-24 12:00:08,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:08,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:09,250 - INFO - Response - Page 7:
2025-05-24 12:00:09,453 - INFO - 第 7 页获取到 82 条记录
2025-05-24 12:00:09,453 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 12:00:09,453 - INFO - 获取到 682 条表单数据
2025-05-24 12:00:09,453 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 12:00:09,469 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 12:00:09,469 - INFO - 开始处理日期: 2025-02
2025-05-24 12:00:09,469 - INFO - Request Parameters - Page 1:
2025-05-24 12:00:09,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:09,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:09,938 - INFO - Response - Page 1:
2025-05-24 12:00:10,141 - INFO - 第 1 页获取到 100 条记录
2025-05-24 12:00:10,141 - INFO - Request Parameters - Page 2:
2025-05-24 12:00:10,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:10,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:10,672 - INFO - Response - Page 2:
2025-05-24 12:00:10,875 - INFO - 第 2 页获取到 100 条记录
2025-05-24 12:00:10,875 - INFO - Request Parameters - Page 3:
2025-05-24 12:00:10,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:10,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:11,328 - INFO - Response - Page 3:
2025-05-24 12:00:11,532 - INFO - 第 3 页获取到 100 条记录
2025-05-24 12:00:11,532 - INFO - Request Parameters - Page 4:
2025-05-24 12:00:11,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:11,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:12,047 - INFO - Response - Page 4:
2025-05-24 12:00:12,250 - INFO - 第 4 页获取到 100 条记录
2025-05-24 12:00:12,250 - INFO - Request Parameters - Page 5:
2025-05-24 12:00:12,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:12,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:12,735 - INFO - Response - Page 5:
2025-05-24 12:00:12,938 - INFO - 第 5 页获取到 100 条记录
2025-05-24 12:00:12,938 - INFO - Request Parameters - Page 6:
2025-05-24 12:00:12,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:12,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:13,391 - INFO - Response - Page 6:
2025-05-24 12:00:13,594 - INFO - 第 6 页获取到 100 条记录
2025-05-24 12:00:13,594 - INFO - Request Parameters - Page 7:
2025-05-24 12:00:13,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:13,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:14,063 - INFO - Response - Page 7:
2025-05-24 12:00:14,282 - INFO - 第 7 页获取到 70 条记录
2025-05-24 12:00:14,282 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 12:00:14,282 - INFO - 获取到 670 条表单数据
2025-05-24 12:00:14,282 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 12:00:14,297 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 12:00:14,297 - INFO - 开始处理日期: 2025-03
2025-05-24 12:00:14,297 - INFO - Request Parameters - Page 1:
2025-05-24 12:00:14,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:14,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:14,828 - INFO - Response - Page 1:
2025-05-24 12:00:15,032 - INFO - 第 1 页获取到 100 条记录
2025-05-24 12:00:15,032 - INFO - Request Parameters - Page 2:
2025-05-24 12:00:15,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:15,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:15,594 - INFO - Response - Page 2:
2025-05-24 12:00:15,797 - INFO - 第 2 页获取到 100 条记录
2025-05-24 12:00:15,797 - INFO - Request Parameters - Page 3:
2025-05-24 12:00:15,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:15,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:16,266 - INFO - Response - Page 3:
2025-05-24 12:00:16,469 - INFO - 第 3 页获取到 100 条记录
2025-05-24 12:00:16,469 - INFO - Request Parameters - Page 4:
2025-05-24 12:00:16,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:16,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:17,172 - INFO - Response - Page 4:
2025-05-24 12:00:17,375 - INFO - 第 4 页获取到 100 条记录
2025-05-24 12:00:17,375 - INFO - Request Parameters - Page 5:
2025-05-24 12:00:17,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:17,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:17,828 - INFO - Response - Page 5:
2025-05-24 12:00:18,032 - INFO - 第 5 页获取到 100 条记录
2025-05-24 12:00:18,032 - INFO - Request Parameters - Page 6:
2025-05-24 12:00:18,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:18,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:18,594 - INFO - Response - Page 6:
2025-05-24 12:00:18,797 - INFO - 第 6 页获取到 100 条记录
2025-05-24 12:00:18,797 - INFO - Request Parameters - Page 7:
2025-05-24 12:00:18,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:18,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:19,282 - INFO - Response - Page 7:
2025-05-24 12:00:19,485 - INFO - 第 7 页获取到 61 条记录
2025-05-24 12:00:19,485 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 12:00:19,485 - INFO - 获取到 661 条表单数据
2025-05-24 12:00:19,485 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 12:00:19,500 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 12:00:19,500 - INFO - 开始处理日期: 2025-04
2025-05-24 12:00:19,500 - INFO - Request Parameters - Page 1:
2025-05-24 12:00:19,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:19,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:20,047 - INFO - Response - Page 1:
2025-05-24 12:00:20,250 - INFO - 第 1 页获取到 100 条记录
2025-05-24 12:00:20,250 - INFO - Request Parameters - Page 2:
2025-05-24 12:00:20,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:20,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:20,797 - INFO - Response - Page 2:
2025-05-24 12:00:21,000 - INFO - 第 2 页获取到 100 条记录
2025-05-24 12:00:21,000 - INFO - Request Parameters - Page 3:
2025-05-24 12:00:21,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:21,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:21,625 - INFO - Response - Page 3:
2025-05-24 12:00:21,828 - INFO - 第 3 页获取到 100 条记录
2025-05-24 12:00:21,828 - INFO - Request Parameters - Page 4:
2025-05-24 12:00:21,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:21,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:22,578 - INFO - Response - Page 4:
2025-05-24 12:00:22,782 - INFO - 第 4 页获取到 100 条记录
2025-05-24 12:00:22,782 - INFO - Request Parameters - Page 5:
2025-05-24 12:00:22,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:22,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:23,266 - INFO - Response - Page 5:
2025-05-24 12:00:23,469 - INFO - 第 5 页获取到 100 条记录
2025-05-24 12:00:23,469 - INFO - Request Parameters - Page 6:
2025-05-24 12:00:23,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:23,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:24,032 - INFO - Response - Page 6:
2025-05-24 12:00:24,235 - INFO - 第 6 页获取到 100 条记录
2025-05-24 12:00:24,235 - INFO - Request Parameters - Page 7:
2025-05-24 12:00:24,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:24,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:24,703 - INFO - Response - Page 7:
2025-05-24 12:00:24,907 - INFO - 第 7 页获取到 56 条记录
2025-05-24 12:00:24,907 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 12:00:24,907 - INFO - 获取到 656 条表单数据
2025-05-24 12:00:24,907 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 12:00:24,922 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 12:00:24,922 - INFO - 开始处理日期: 2025-05
2025-05-24 12:00:24,922 - INFO - Request Parameters - Page 1:
2025-05-24 12:00:24,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:24,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:25,438 - INFO - Response - Page 1:
2025-05-24 12:00:25,641 - INFO - 第 1 页获取到 100 条记录
2025-05-24 12:00:25,641 - INFO - Request Parameters - Page 2:
2025-05-24 12:00:25,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:25,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:26,219 - INFO - Response - Page 2:
2025-05-24 12:00:26,422 - INFO - 第 2 页获取到 100 条记录
2025-05-24 12:00:26,422 - INFO - Request Parameters - Page 3:
2025-05-24 12:00:26,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:26,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:26,860 - INFO - Response - Page 3:
2025-05-24 12:00:27,063 - INFO - 第 3 页获取到 100 条记录
2025-05-24 12:00:27,063 - INFO - Request Parameters - Page 4:
2025-05-24 12:00:27,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:27,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:27,594 - INFO - Response - Page 4:
2025-05-24 12:00:27,797 - INFO - 第 4 页获取到 100 条记录
2025-05-24 12:00:27,797 - INFO - Request Parameters - Page 5:
2025-05-24 12:00:27,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:27,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:28,485 - INFO - Response - Page 5:
2025-05-24 12:00:28,688 - INFO - 第 5 页获取到 100 条记录
2025-05-24 12:00:28,688 - INFO - Request Parameters - Page 6:
2025-05-24 12:00:28,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:28,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:29,188 - INFO - Response - Page 6:
2025-05-24 12:00:29,391 - INFO - 第 6 页获取到 100 条记录
2025-05-24 12:00:29,391 - INFO - Request Parameters - Page 7:
2025-05-24 12:00:29,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:00:29,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:00:29,813 - INFO - Response - Page 7:
2025-05-24 12:00:30,016 - INFO - 第 7 页获取到 28 条记录
2025-05-24 12:00:30,016 - INFO - 查询完成，共获取到 628 条记录
2025-05-24 12:00:30,016 - INFO - 获取到 628 条表单数据
2025-05-24 12:00:30,016 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-24 12:00:30,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-24 12:00:30,500 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-24 12:00:30,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242000.0, 'new_value': 255200.0}, {'field': 'total_amount', 'old_value': 242000.0, 'new_value': 255200.0}]
2025-05-24 12:00:30,500 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-24 12:00:30,953 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-24 12:00:30,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1867.0, 'new_value': 1892.0}, {'field': 'offline_amount', 'old_value': 35178.0, 'new_value': 37168.0}, {'field': 'total_amount', 'old_value': 37045.0, 'new_value': 39060.0}, {'field': 'order_count', 'old_value': 503, 'new_value': 538}]
2025-05-24 12:00:30,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-24 12:00:31,406 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-24 12:00:31,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342884.0, 'new_value': 352588.0}, {'field': 'total_amount', 'old_value': 342884.0, 'new_value': 352588.0}, {'field': 'order_count', 'old_value': 256, 'new_value': 264}]
2025-05-24 12:00:31,406 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-24 12:00:31,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-24 12:00:31,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43990.0, 'new_value': 45845.0}, {'field': 'total_amount', 'old_value': 45580.0, 'new_value': 47435.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 179}]
2025-05-24 12:00:31,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-24 12:00:32,422 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-24 12:00:32,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 640303.98, 'new_value': 671733.98}, {'field': 'total_amount', 'old_value': 640303.98, 'new_value': 671733.98}, {'field': 'order_count', 'old_value': 1956, 'new_value': 2063}]
2025-05-24 12:00:32,422 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-24 12:00:32,875 - INFO - 更新表单数据成功: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-24 12:00:32,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39591.0, 'new_value': 43002.0}, {'field': 'total_amount', 'old_value': 39591.0, 'new_value': 43002.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-24 12:00:32,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-24 12:00:33,375 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-24 12:00:33,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55122.0, 'new_value': 59010.0}, {'field': 'total_amount', 'old_value': 58019.0, 'new_value': 61907.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-24 12:00:33,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-24 12:00:33,828 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-24 12:00:33,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29244.61, 'new_value': 30667.87}, {'field': 'offline_amount', 'old_value': 14274.17, 'new_value': 15170.57}, {'field': 'total_amount', 'old_value': 43518.78, 'new_value': 45838.44}, {'field': 'order_count', 'old_value': 2237, 'new_value': 2326}]
2025-05-24 12:00:33,828 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-24 12:00:34,281 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-24 12:00:34,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49129.78, 'new_value': 50094.78}, {'field': 'total_amount', 'old_value': 49129.78, 'new_value': 50094.78}, {'field': 'order_count', 'old_value': 107, 'new_value': 112}]
2025-05-24 12:00:34,281 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-24 12:00:34,750 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-24 12:00:34,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317120.0, 'new_value': 331980.0}, {'field': 'total_amount', 'old_value': 317120.0, 'new_value': 331980.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 199}]
2025-05-24 12:00:34,750 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-24 12:00:35,266 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-24 12:00:35,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84001.0, 'new_value': 85001.0}, {'field': 'total_amount', 'old_value': 92200.0, 'new_value': 93200.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-24 12:00:35,266 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-24 12:00:35,703 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-24 12:00:35,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90154.0, 'new_value': 101309.0}, {'field': 'total_amount', 'old_value': 90154.0, 'new_value': 101309.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 116}]
2025-05-24 12:00:35,703 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-24 12:00:36,125 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-24 12:00:36,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45500.0, 'new_value': 47280.0}, {'field': 'total_amount', 'old_value': 49620.0, 'new_value': 51400.0}, {'field': 'order_count', 'old_value': 474, 'new_value': 492}]
2025-05-24 12:00:36,125 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-24 12:00:36,578 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-24 12:00:36,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2436.6, 'new_value': 2508.6}, {'field': 'total_amount', 'old_value': 33790.4, 'new_value': 33862.4}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-24 12:00:36,578 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-24 12:00:37,031 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-24 12:00:37,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34102.0, 'new_value': 44992.0}, {'field': 'total_amount', 'old_value': 34102.0, 'new_value': 44992.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-24 12:00:37,031 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-24 12:00:37,485 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-24 12:00:37,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46724.3, 'new_value': 46764.2}, {'field': 'total_amount', 'old_value': 50684.3, 'new_value': 50724.2}, {'field': 'order_count', 'old_value': 350, 'new_value': 359}]
2025-05-24 12:00:37,500 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-24 12:00:37,922 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-24 12:00:37,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51800.34, 'new_value': 53098.46}, {'field': 'offline_amount', 'old_value': 100030.25, 'new_value': 103424.85}, {'field': 'total_amount', 'old_value': 151830.59, 'new_value': 156523.31}, {'field': 'order_count', 'old_value': 1757, 'new_value': 1807}]
2025-05-24 12:00:37,922 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-24 12:00:38,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-24 12:00:38,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19091.17, 'new_value': 19768.0}, {'field': 'offline_amount', 'old_value': 23503.21, 'new_value': 24373.81}, {'field': 'total_amount', 'old_value': 42594.38, 'new_value': 44141.81}, {'field': 'order_count', 'old_value': 2066, 'new_value': 2148}]
2025-05-24 12:00:38,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-24 12:00:38,891 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-24 12:00:38,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272476.5, 'new_value': 279242.3}, {'field': 'total_amount', 'old_value': 387496.2, 'new_value': 394262.0}, {'field': 'order_count', 'old_value': 3039, 'new_value': 3126}]
2025-05-24 12:00:38,891 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-24 12:00:39,344 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-24 12:00:39,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92366.0, 'new_value': 96353.0}, {'field': 'total_amount', 'old_value': 92366.0, 'new_value': 96353.0}, {'field': 'order_count', 'old_value': 5031, 'new_value': 5271}]
2025-05-24 12:00:39,344 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-24 12:00:39,781 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-24 12:00:39,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58710.0, 'new_value': 61108.0}, {'field': 'total_amount', 'old_value': 58710.0, 'new_value': 61108.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 103}]
2025-05-24 12:00:39,781 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-24 12:00:40,235 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-24 12:00:40,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57719.5, 'new_value': 61319.5}, {'field': 'total_amount', 'old_value': 62219.5, 'new_value': 65819.5}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-24 12:00:40,235 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-24 12:00:40,610 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-24 12:00:40,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 397745.71, 'new_value': 416695.71}, {'field': 'total_amount', 'old_value': 397745.71, 'new_value': 416695.71}, {'field': 'order_count', 'old_value': 389, 'new_value': 400}]
2025-05-24 12:00:40,610 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-24 12:00:41,047 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-24 12:00:41,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127929.65, 'new_value': 132477.88}, {'field': 'total_amount', 'old_value': 127929.65, 'new_value': 132477.88}, {'field': 'order_count', 'old_value': 1492, 'new_value': 1555}]
2025-05-24 12:00:41,047 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-24 12:00:41,485 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-24 12:00:41,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 991785.0, 'new_value': 1032563.0}, {'field': 'offline_amount', 'old_value': 294219.0, 'new_value': 301896.0}, {'field': 'total_amount', 'old_value': 1286004.0, 'new_value': 1334459.0}, {'field': 'order_count', 'old_value': 1494, 'new_value': 1551}]
2025-05-24 12:00:41,485 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-24 12:00:42,016 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-24 12:00:42,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11923.23, 'new_value': 12630.19}, {'field': 'offline_amount', 'old_value': 33077.69, 'new_value': 34304.69}, {'field': 'total_amount', 'old_value': 45000.92, 'new_value': 46934.88}, {'field': 'order_count', 'old_value': 816, 'new_value': 848}]
2025-05-24 12:00:42,016 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-24 12:00:42,453 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-24 12:00:42,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47476.0, 'new_value': 50036.0}, {'field': 'total_amount', 'old_value': 85076.0, 'new_value': 87636.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-24 12:00:42,453 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-24 12:00:42,922 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-24 12:00:42,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6558.75, 'new_value': 6678.55}, {'field': 'offline_amount', 'old_value': 75263.7, 'new_value': 75915.7}, {'field': 'total_amount', 'old_value': 81822.45, 'new_value': 82594.25}, {'field': 'order_count', 'old_value': 1792, 'new_value': 1812}]
2025-05-24 12:00:42,922 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-24 12:00:43,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-24 12:00:43,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238825.0, 'new_value': 246905.0}, {'field': 'total_amount', 'old_value': 268825.0, 'new_value': 276905.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-24 12:00:43,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-24 12:00:43,844 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-24 12:00:43,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308167.89, 'new_value': 312721.89}, {'field': 'total_amount', 'old_value': 308167.89, 'new_value': 312721.89}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-05-24 12:00:43,844 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-24 12:00:44,266 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-24 12:00:44,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274580.13, 'new_value': 279380.13}, {'field': 'total_amount', 'old_value': 313940.13, 'new_value': 318740.13}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-24 12:00:44,266 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-24 12:00:44,766 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-24 12:00:44,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 699990.5, 'new_value': 816389.6}, {'field': 'total_amount', 'old_value': 742626.7, 'new_value': 859025.8}, {'field': 'order_count', 'old_value': 83, 'new_value': 88}]
2025-05-24 12:00:44,766 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-24 12:00:45,234 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-24 12:00:45,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45255.6, 'new_value': 45873.6}, {'field': 'total_amount', 'old_value': 60650.3, 'new_value': 61268.3}, {'field': 'order_count', 'old_value': 644, 'new_value': 646}]
2025-05-24 12:00:45,234 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-24 12:00:45,688 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-24 12:00:45,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76807.0, 'new_value': 82211.0}, {'field': 'total_amount', 'old_value': 135852.0, 'new_value': 141256.0}, {'field': 'order_count', 'old_value': 1825, 'new_value': 1909}]
2025-05-24 12:00:45,688 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-24 12:00:46,281 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-24 12:00:46,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5801.0, 'new_value': 6233.0}, {'field': 'offline_amount', 'old_value': 19153.0, 'new_value': 19291.0}, {'field': 'total_amount', 'old_value': 24954.0, 'new_value': 25524.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 69}]
2025-05-24 12:00:46,281 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-24 12:00:46,781 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-24 12:00:46,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161942.0, 'new_value': 165604.0}, {'field': 'total_amount', 'old_value': 161942.0, 'new_value': 165604.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 327}]
2025-05-24 12:00:46,781 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-24 12:00:47,203 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-24 12:00:47,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43903.5, 'new_value': 55879.5}, {'field': 'total_amount', 'old_value': 43903.5, 'new_value': 55879.5}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-24 12:00:47,203 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-24 12:00:47,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-24 12:00:47,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149545.79, 'new_value': 160385.9}, {'field': 'total_amount', 'old_value': 149545.79, 'new_value': 160385.9}, {'field': 'order_count', 'old_value': 223, 'new_value': 253}]
2025-05-24 12:00:47,672 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-24 12:00:48,141 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-24 12:00:48,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12019.0, 'new_value': 12757.0}, {'field': 'total_amount', 'old_value': 12019.0, 'new_value': 12757.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-05-24 12:00:48,141 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-24 12:00:48,594 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-24 12:00:48,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40190.33, 'new_value': 40796.97}, {'field': 'total_amount', 'old_value': 40190.33, 'new_value': 40796.97}, {'field': 'order_count', 'old_value': 2922, 'new_value': 2980}]
2025-05-24 12:00:48,594 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-24 12:00:49,063 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-24 12:00:49,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158329.31, 'new_value': 162378.02}, {'field': 'offline_amount', 'old_value': 27760.64, 'new_value': 28179.54}, {'field': 'total_amount', 'old_value': 186089.95, 'new_value': 190557.56}, {'field': 'order_count', 'old_value': 678, 'new_value': 695}]
2025-05-24 12:00:49,063 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-24 12:00:49,453 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-24 12:00:49,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146250.0, 'new_value': 151544.0}, {'field': 'offline_amount', 'old_value': 54653.3, 'new_value': 58678.01}, {'field': 'total_amount', 'old_value': 200903.3, 'new_value': 210222.01}, {'field': 'order_count', 'old_value': 1287, 'new_value': 1354}]
2025-05-24 12:00:49,453 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-24 12:00:50,063 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-24 12:00:50,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38418.0, 'new_value': 39974.0}, {'field': 'total_amount', 'old_value': 43794.0, 'new_value': 45350.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 204}]
2025-05-24 12:00:50,063 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-24 12:00:50,547 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-24 12:00:50,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8213.0, 'new_value': 8312.0}, {'field': 'total_amount', 'old_value': 9706.0, 'new_value': 9805.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 187}]
2025-05-24 12:00:50,547 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-24 12:00:51,094 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-24 12:00:51,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80356.48, 'new_value': 84182.43}, {'field': 'total_amount', 'old_value': 80356.48, 'new_value': 84182.43}, {'field': 'order_count', 'old_value': 2158, 'new_value': 2270}]
2025-05-24 12:00:51,094 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-24 12:00:51,656 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-24 12:00:51,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68140.42, 'new_value': 69263.94}, {'field': 'total_amount', 'old_value': 68140.42, 'new_value': 69263.94}, {'field': 'order_count', 'old_value': 117, 'new_value': 121}]
2025-05-24 12:00:51,656 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-24 12:00:52,047 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-24 12:00:52,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195812.1, 'new_value': 197196.1}, {'field': 'total_amount', 'old_value': 195812.1, 'new_value': 197196.1}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-24 12:00:52,047 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-24 12:00:52,500 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-24 12:00:52,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137533.0, 'new_value': 145162.0}, {'field': 'offline_amount', 'old_value': 60165.72, 'new_value': 62533.26}, {'field': 'total_amount', 'old_value': 197698.72, 'new_value': 207695.26}, {'field': 'order_count', 'old_value': 1403, 'new_value': 1469}]
2025-05-24 12:00:52,500 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-24 12:00:52,953 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-24 12:00:52,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47608.42, 'new_value': 49592.48}, {'field': 'offline_amount', 'old_value': 474231.46, 'new_value': 497743.0}, {'field': 'total_amount', 'old_value': 521839.88, 'new_value': 547335.48}, {'field': 'order_count', 'old_value': 1675, 'new_value': 1770}]
2025-05-24 12:00:52,953 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-24 12:00:53,406 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-24 12:00:53,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9570.81, 'new_value': 10022.47}, {'field': 'offline_amount', 'old_value': 156777.79, 'new_value': 162709.49}, {'field': 'total_amount', 'old_value': 166348.6, 'new_value': 172731.96}, {'field': 'order_count', 'old_value': 1814, 'new_value': 1890}]
2025-05-24 12:00:53,406 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-24 12:00:53,969 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-24 12:00:53,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48096.1, 'new_value': 49688.0}, {'field': 'total_amount', 'old_value': 48108.0, 'new_value': 49699.9}, {'field': 'order_count', 'old_value': 273, 'new_value': 287}]
2025-05-24 12:00:53,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-24 12:00:54,422 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-24 12:00:54,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119196.0, 'new_value': 122723.0}, {'field': 'offline_amount', 'old_value': 70731.0, 'new_value': 73143.0}, {'field': 'total_amount', 'old_value': 189927.0, 'new_value': 195866.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-05-24 12:00:54,422 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-24 12:00:55,203 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-24 12:00:55,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 185583.0, 'new_value': 192754.0}, {'field': 'total_amount', 'old_value': 185583.0, 'new_value': 192754.0}, {'field': 'order_count', 'old_value': 965, 'new_value': 1021}]
2025-05-24 12:00:55,203 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-24 12:00:55,609 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-24 12:00:55,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180620.51, 'new_value': 190644.21}, {'field': 'total_amount', 'old_value': 180620.51, 'new_value': 190644.21}, {'field': 'order_count', 'old_value': 607, 'new_value': 640}]
2025-05-24 12:00:55,609 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-24 12:00:56,141 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-24 12:00:56,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75291.36, 'new_value': 79149.31}, {'field': 'offline_amount', 'old_value': 37271.21, 'new_value': 38648.01}, {'field': 'total_amount', 'old_value': 112562.57, 'new_value': 117797.32}, {'field': 'order_count', 'old_value': 3888, 'new_value': 4080}]
2025-05-24 12:00:56,141 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-24 12:00:56,594 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-24 12:00:56,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128102.0, 'new_value': 134417.0}, {'field': 'total_amount', 'old_value': 128102.0, 'new_value': 134417.0}, {'field': 'order_count', 'old_value': 3211, 'new_value': 3365}]
2025-05-24 12:00:56,594 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-24 12:00:57,047 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-24 12:00:57,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20688.94, 'new_value': 21109.34}, {'field': 'total_amount', 'old_value': 20688.94, 'new_value': 21109.34}, {'field': 'order_count', 'old_value': 125, 'new_value': 131}]
2025-05-24 12:00:57,047 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-24 12:00:57,531 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-24 12:00:57,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149172.0, 'new_value': 155966.0}, {'field': 'total_amount', 'old_value': 149172.0, 'new_value': 155966.0}, {'field': 'order_count', 'old_value': 5595, 'new_value': 5870}]
2025-05-24 12:00:57,531 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-24 12:00:58,047 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-24 12:00:58,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 618745.41, 'new_value': 645450.7}, {'field': 'total_amount', 'old_value': 618745.41, 'new_value': 645450.7}]
2025-05-24 12:00:58,047 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-24 12:00:58,688 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-24 12:00:58,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21736.0, 'new_value': 31612.0}, {'field': 'offline_amount', 'old_value': 437027.0, 'new_value': 461049.2}, {'field': 'total_amount', 'old_value': 458763.0, 'new_value': 492661.2}, {'field': 'order_count', 'old_value': 80, 'new_value': 85}]
2025-05-24 12:00:58,688 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-24 12:00:59,141 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-24 12:00:59,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122412.94, 'new_value': 128044.31}, {'field': 'total_amount', 'old_value': 122412.94, 'new_value': 128044.31}, {'field': 'order_count', 'old_value': 4455, 'new_value': 4641}]
2025-05-24 12:00:59,141 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-24 12:00:59,563 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-24 12:00:59,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106763.0, 'new_value': 121659.0}, {'field': 'total_amount', 'old_value': 169435.0, 'new_value': 184331.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 69}]
2025-05-24 12:00:59,563 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-24 12:01:00,000 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-24 12:01:00,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 276640.7, 'new_value': 291335.36}, {'field': 'total_amount', 'old_value': 277449.7, 'new_value': 292144.36}, {'field': 'order_count', 'old_value': 3155, 'new_value': 3333}]
2025-05-24 12:01:00,000 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-24 12:01:00,484 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-24 12:01:00,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3934.69, 'new_value': 4205.93}, {'field': 'offline_amount', 'old_value': 121880.2, 'new_value': 125427.96}, {'field': 'total_amount', 'old_value': 125814.89, 'new_value': 129633.89}, {'field': 'order_count', 'old_value': 586, 'new_value': 606}]
2025-05-24 12:01:00,484 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-24 12:01:00,938 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-24 12:01:00,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51028.0, 'new_value': 51355.0}, {'field': 'total_amount', 'old_value': 51028.0, 'new_value': 51355.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 103}]
2025-05-24 12:01:00,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-24 12:01:01,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-24 12:01:01,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66825.89, 'new_value': 70590.67}, {'field': 'offline_amount', 'old_value': 464588.3, 'new_value': 478617.3}, {'field': 'total_amount', 'old_value': 531414.19, 'new_value': 549207.97}, {'field': 'order_count', 'old_value': 739, 'new_value': 768}]
2025-05-24 12:01:01,391 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-24 12:01:02,078 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-24 12:01:02,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156906.98, 'new_value': 165637.73}, {'field': 'total_amount', 'old_value': 156906.98, 'new_value': 165637.73}, {'field': 'order_count', 'old_value': 897, 'new_value': 952}]
2025-05-24 12:01:02,078 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-24 12:01:02,594 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-24 12:01:02,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 169252.65, 'new_value': 177137.65}, {'field': 'offline_amount', 'old_value': 382584.93, 'new_value': 392006.86}, {'field': 'total_amount', 'old_value': 551837.58, 'new_value': 569144.51}, {'field': 'order_count', 'old_value': 4078, 'new_value': 4193}]
2025-05-24 12:01:02,594 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-24 12:01:03,047 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-24 12:01:03,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25975.57, 'new_value': 26883.57}, {'field': 'total_amount', 'old_value': 25975.57, 'new_value': 26883.57}, {'field': 'order_count', 'old_value': 148, 'new_value': 154}]
2025-05-24 12:01:03,047 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-24 12:01:03,516 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-24 12:01:03,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141325.0, 'new_value': 163109.0}, {'field': 'total_amount', 'old_value': 141325.0, 'new_value': 163109.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 40}]
2025-05-24 12:01:03,516 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-24 12:01:04,000 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-24 12:01:04,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1511705.88, 'new_value': 1567621.68}, {'field': 'total_amount', 'old_value': 1565150.98, 'new_value': 1621066.78}, {'field': 'order_count', 'old_value': 2814, 'new_value': 2919}]
2025-05-24 12:01:04,000 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-24 12:01:04,453 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-24 12:01:04,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184950.65, 'new_value': 195222.65}, {'field': 'offline_amount', 'old_value': 105947.0, 'new_value': 111493.0}, {'field': 'total_amount', 'old_value': 290897.65, 'new_value': 306715.65}, {'field': 'order_count', 'old_value': 1638, 'new_value': 1725}]
2025-05-24 12:01:04,453 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-24 12:01:04,906 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-24 12:01:04,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34375.9, 'new_value': 36109.9}, {'field': 'total_amount', 'old_value': 34375.9, 'new_value': 36109.9}, {'field': 'order_count', 'old_value': 151, 'new_value': 158}]
2025-05-24 12:01:04,906 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-24 12:01:05,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-24 12:01:05,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49467.0, 'new_value': 53162.0}, {'field': 'total_amount', 'old_value': 78113.0, 'new_value': 81808.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-24 12:01:05,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-24 12:01:05,828 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-24 12:01:05,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17525.0, 'new_value': 17815.0}, {'field': 'total_amount', 'old_value': 17525.0, 'new_value': 17815.0}, {'field': 'order_count', 'old_value': 302, 'new_value': 307}]
2025-05-24 12:01:05,828 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-24 12:01:06,297 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-24 12:01:06,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112018.0, 'new_value': 113238.0}, {'field': 'total_amount', 'old_value': 112021.0, 'new_value': 113241.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-24 12:01:06,297 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-24 12:01:06,766 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-24 12:01:06,766 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6574.91, 'new_value': 6816.71}, {'field': 'offline_amount', 'old_value': 14568.25, 'new_value': 15189.03}, {'field': 'total_amount', 'old_value': 21143.16, 'new_value': 22005.74}, {'field': 'order_count', 'old_value': 713, 'new_value': 744}]
2025-05-24 12:01:06,766 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-24 12:01:07,234 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-24 12:01:07,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159679.99, 'new_value': 166626.54}, {'field': 'offline_amount', 'old_value': 131386.14, 'new_value': 136248.86}, {'field': 'total_amount', 'old_value': 291066.13, 'new_value': 302875.4}, {'field': 'order_count', 'old_value': 2588, 'new_value': 2714}]
2025-05-24 12:01:07,234 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-24 12:01:07,625 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-24 12:01:07,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79045.01, 'new_value': 80916.93}, {'field': 'total_amount', 'old_value': 79048.31, 'new_value': 80920.23}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-05-24 12:01:07,641 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-24 12:01:08,094 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-24 12:01:08,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 325802.9, 'new_value': 339088.2}, {'field': 'offline_amount', 'old_value': 87197.2, 'new_value': 88649.2}, {'field': 'total_amount', 'old_value': 413000.1, 'new_value': 427737.4}, {'field': 'order_count', 'old_value': 522, 'new_value': 543}]
2025-05-24 12:01:08,094 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-24 12:01:08,547 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-24 12:01:08,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24236.0, 'new_value': 25419.0}, {'field': 'total_amount', 'old_value': 24236.0, 'new_value': 25419.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 72}]
2025-05-24 12:01:08,547 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-24 12:01:09,156 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-24 12:01:09,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 749.0}, {'field': 'total_amount', 'old_value': 64710.0, 'new_value': 65459.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-05-24 12:01:09,156 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-24 12:01:09,687 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-24 12:01:09,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151107.4, 'new_value': 154418.4}, {'field': 'total_amount', 'old_value': 158683.2, 'new_value': 161994.2}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-24 12:01:09,687 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-24 12:01:10,125 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-24 12:01:10,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39829.43, 'new_value': 41811.01}, {'field': 'offline_amount', 'old_value': 81359.49, 'new_value': 83678.65}, {'field': 'total_amount', 'old_value': 121188.92, 'new_value': 125489.66}, {'field': 'order_count', 'old_value': 4434, 'new_value': 4608}]
2025-05-24 12:01:10,125 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-24 12:01:10,594 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-24 12:01:10,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72252.19, 'new_value': 72282.09}, {'field': 'total_amount', 'old_value': 76021.29, 'new_value': 76051.19}, {'field': 'order_count', 'old_value': 388, 'new_value': 389}]
2025-05-24 12:01:10,594 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-24 12:01:11,047 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-24 12:01:11,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152072.0, 'new_value': 160950.0}, {'field': 'total_amount', 'old_value': 204080.0, 'new_value': 212958.0}, {'field': 'order_count', 'old_value': 4482, 'new_value': 4704}]
2025-05-24 12:01:11,047 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-24 12:01:11,516 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-24 12:01:11,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12086.77, 'new_value': 13271.43}, {'field': 'offline_amount', 'old_value': 248239.04, 'new_value': 267369.44}, {'field': 'total_amount', 'old_value': 260325.81, 'new_value': 280640.87}, {'field': 'order_count', 'old_value': 1798, 'new_value': 1950}]
2025-05-24 12:01:11,516 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-24 12:01:11,969 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-24 12:01:11,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91098.64, 'new_value': 91683.64}, {'field': 'total_amount', 'old_value': 96438.64, 'new_value': 97023.64}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-24 12:01:11,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-24 12:01:12,406 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-24 12:01:12,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131381.98, 'new_value': 137990.13}, {'field': 'offline_amount', 'old_value': 101216.45, 'new_value': 107252.45}, {'field': 'total_amount', 'old_value': 232598.43, 'new_value': 245242.58}, {'field': 'order_count', 'old_value': 2340, 'new_value': 2447}]
2025-05-24 12:01:12,406 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-24 12:01:12,953 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-24 12:01:12,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 487786.04, 'new_value': 491137.04}, {'field': 'offline_amount', 'old_value': 208963.1, 'new_value': 209038.1}, {'field': 'total_amount', 'old_value': 696749.14, 'new_value': 700175.14}, {'field': 'order_count', 'old_value': 6252, 'new_value': 6281}]
2025-05-24 12:01:12,953 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-24 12:01:13,469 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-24 12:01:13,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36205.6, 'new_value': 36306.6}, {'field': 'offline_amount', 'old_value': 544.0, 'new_value': 545.0}, {'field': 'total_amount', 'old_value': 36749.6, 'new_value': 36851.6}, {'field': 'order_count', 'old_value': 158, 'new_value': 160}]
2025-05-24 12:01:13,469 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-24 12:01:14,016 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-24 12:01:14,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70197.5, 'new_value': 75197.5}, {'field': 'offline_amount', 'old_value': 5684.85, 'new_value': 5971.15}, {'field': 'total_amount', 'old_value': 75882.35, 'new_value': 81168.65}, {'field': 'order_count', 'old_value': 227, 'new_value': 243}]
2025-05-24 12:01:14,016 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-24 12:01:14,516 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-24 12:01:14,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62641.86, 'new_value': 65045.66}, {'field': 'total_amount', 'old_value': 62641.86, 'new_value': 65045.66}, {'field': 'order_count', 'old_value': 1776, 'new_value': 1844}]
2025-05-24 12:01:14,516 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-24 12:01:15,000 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-24 12:01:15,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7856.4, 'new_value': 7955.4}, {'field': 'offline_amount', 'old_value': 41711.0, 'new_value': 43909.0}, {'field': 'total_amount', 'old_value': 49567.4, 'new_value': 51864.4}, {'field': 'order_count', 'old_value': 65, 'new_value': 67}]
2025-05-24 12:01:15,000 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-24 12:01:15,594 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-24 12:01:15,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 534481.0, 'new_value': 559481.0}, {'field': 'total_amount', 'old_value': 534481.0, 'new_value': 559481.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 63}]
2025-05-24 12:01:15,594 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-24 12:01:16,062 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-24 12:01:16,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113864.08, 'new_value': 120671.58}, {'field': 'offline_amount', 'old_value': 384587.75, 'new_value': 402119.54}, {'field': 'total_amount', 'old_value': 498451.83, 'new_value': 522791.12}, {'field': 'order_count', 'old_value': 2662, 'new_value': 2732}]
2025-05-24 12:01:16,062 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-24 12:01:16,562 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-24 12:01:16,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376046.57, 'new_value': 390457.97}, {'field': 'total_amount', 'old_value': 376046.57, 'new_value': 390457.97}, {'field': 'order_count', 'old_value': 495, 'new_value': 509}]
2025-05-24 12:01:16,562 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-24 12:01:17,016 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-24 12:01:17,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13959.0, 'new_value': 14652.17}, {'field': 'offline_amount', 'old_value': 362596.77, 'new_value': 374951.86}, {'field': 'total_amount', 'old_value': 376555.77, 'new_value': 389604.03}, {'field': 'order_count', 'old_value': 1584, 'new_value': 1650}]
2025-05-24 12:01:17,016 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-24 12:01:17,453 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-24 12:01:17,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76849.0, 'new_value': 77322.0}, {'field': 'offline_amount', 'old_value': 71483.29, 'new_value': 73480.09}, {'field': 'total_amount', 'old_value': 148332.29, 'new_value': 150802.09}, {'field': 'order_count', 'old_value': 178, 'new_value': 183}]
2025-05-24 12:01:17,469 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-24 12:01:17,828 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-24 12:01:17,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142114.5, 'new_value': 145019.2}, {'field': 'total_amount', 'old_value': 142114.5, 'new_value': 145019.2}, {'field': 'order_count', 'old_value': 325, 'new_value': 333}]
2025-05-24 12:01:17,828 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-24 12:01:18,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-24 12:01:18,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197619.36, 'new_value': 198496.57}, {'field': 'offline_amount', 'old_value': 76265.29, 'new_value': 81401.99}, {'field': 'total_amount', 'old_value': 273884.65, 'new_value': 279898.56}, {'field': 'order_count', 'old_value': 2944, 'new_value': 2999}]
2025-05-24 12:01:18,344 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-24 12:01:18,844 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-24 12:01:18,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9731.0, 'new_value': 10557.0}, {'field': 'total_amount', 'old_value': 9731.0, 'new_value': 10557.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-24 12:01:18,844 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-24 12:01:19,328 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-24 12:01:19,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83485.57, 'new_value': 86381.79}, {'field': 'total_amount', 'old_value': 83485.57, 'new_value': 86381.79}, {'field': 'order_count', 'old_value': 3211, 'new_value': 3315}]
2025-05-24 12:01:19,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-24 12:01:19,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-24 12:01:19,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89860.54, 'new_value': 95922.54}, {'field': 'total_amount', 'old_value': 89860.54, 'new_value': 95922.54}, {'field': 'order_count', 'old_value': 76, 'new_value': 81}]
2025-05-24 12:01:19,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-24 12:01:20,297 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-24 12:01:20,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12596.88, 'new_value': 12890.48}, {'field': 'total_amount', 'old_value': 21836.01, 'new_value': 22129.61}, {'field': 'order_count', 'old_value': 93, 'new_value': 96}]
2025-05-24 12:01:20,297 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-24 12:01:20,781 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-24 12:01:20,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37576.0, 'new_value': 38327.0}, {'field': 'total_amount', 'old_value': 37576.0, 'new_value': 38327.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-24 12:01:20,781 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-24 12:01:21,281 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-24 12:01:21,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53625.0, 'new_value': 53904.0}, {'field': 'total_amount', 'old_value': 53625.0, 'new_value': 53904.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-24 12:01:21,281 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-24 12:01:21,750 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-24 12:01:21,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6489.0, 'new_value': 6589.0}, {'field': 'total_amount', 'old_value': 6489.0, 'new_value': 6589.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-24 12:01:21,750 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-24 12:01:22,234 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-24 12:01:22,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89689.5, 'new_value': 92667.1}, {'field': 'total_amount', 'old_value': 89689.5, 'new_value': 92667.1}, {'field': 'order_count', 'old_value': 276, 'new_value': 283}]
2025-05-24 12:01:22,234 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-24 12:01:22,672 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-24 12:01:22,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168780.29, 'new_value': 177292.47}, {'field': 'offline_amount', 'old_value': 304782.84, 'new_value': 316156.35}, {'field': 'total_amount', 'old_value': 473563.13, 'new_value': 493448.82}, {'field': 'order_count', 'old_value': 13351, 'new_value': 13978}]
2025-05-24 12:01:22,672 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-24 12:01:23,156 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-24 12:01:23,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40084.0, 'new_value': 40999.0}, {'field': 'total_amount', 'old_value': 40084.0, 'new_value': 40999.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 109}]
2025-05-24 12:01:23,156 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-24 12:01:23,578 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-24 12:01:23,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7235.85, 'new_value': 7275.65}, {'field': 'offline_amount', 'old_value': 25774.0, 'new_value': 27062.0}, {'field': 'total_amount', 'old_value': 33009.85, 'new_value': 34337.65}, {'field': 'order_count', 'old_value': 187, 'new_value': 190}]
2025-05-24 12:01:23,578 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-24 12:01:24,172 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-24 12:01:24,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31987.0, 'new_value': 32542.0}, {'field': 'total_amount', 'old_value': 31987.0, 'new_value': 32542.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 102}]
2025-05-24 12:01:24,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-24 12:01:24,609 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-24 12:01:24,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 403.2, 'new_value': 521.6}, {'field': 'offline_amount', 'old_value': 41095.41, 'new_value': 41611.87}, {'field': 'total_amount', 'old_value': 41498.61, 'new_value': 42133.47}, {'field': 'order_count', 'old_value': 350, 'new_value': 357}]
2025-05-24 12:01:24,609 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-24 12:01:25,219 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-24 12:01:25,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4918.0, 'new_value': 5327.0}, {'field': 'offline_amount', 'old_value': 21585.4, 'new_value': 23196.5}, {'field': 'total_amount', 'old_value': 26503.4, 'new_value': 28523.5}, {'field': 'order_count', 'old_value': 1060, 'new_value': 1128}]
2025-05-24 12:01:25,219 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-24 12:01:25,797 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-24 12:01:25,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79176.26, 'new_value': 83253.86}, {'field': 'total_amount', 'old_value': 79176.26, 'new_value': 83253.86}, {'field': 'order_count', 'old_value': 291, 'new_value': 309}]
2025-05-24 12:01:25,797 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-24 12:01:26,281 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-24 12:01:26,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17168.63, 'new_value': 18264.8}, {'field': 'offline_amount', 'old_value': 28510.91, 'new_value': 29857.2}, {'field': 'total_amount', 'old_value': 45679.54, 'new_value': 48122.0}, {'field': 'order_count', 'old_value': 2049, 'new_value': 2165}]
2025-05-24 12:01:26,281 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-24 12:01:26,781 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-24 12:01:26,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175849.5, 'new_value': 179696.3}, {'field': 'total_amount', 'old_value': 175849.5, 'new_value': 179696.3}, {'field': 'order_count', 'old_value': 653, 'new_value': 668}]
2025-05-24 12:01:26,781 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-24 12:01:27,297 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-24 12:01:27,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2044.0, 'new_value': 2593.0}, {'field': 'offline_amount', 'old_value': 43310.0, 'new_value': 44736.0}, {'field': 'total_amount', 'old_value': 45354.0, 'new_value': 47329.0}, {'field': 'order_count', 'old_value': 183, 'new_value': 188}]
2025-05-24 12:01:27,297 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-24 12:01:27,750 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-24 12:01:27,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260250.85, 'new_value': 272164.7}, {'field': 'total_amount', 'old_value': 260250.85, 'new_value': 272164.7}, {'field': 'order_count', 'old_value': 7127, 'new_value': 7490}]
2025-05-24 12:01:27,750 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-24 12:01:28,172 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-24 12:01:28,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131377.8, 'new_value': 135072.17}, {'field': 'total_amount', 'old_value': 131377.8, 'new_value': 135072.17}, {'field': 'order_count', 'old_value': 651, 'new_value': 669}]
2025-05-24 12:01:28,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-24 12:01:28,640 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-24 12:01:28,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20547.05, 'new_value': 21456.94}, {'field': 'offline_amount', 'old_value': 37626.26, 'new_value': 39248.16}, {'field': 'total_amount', 'old_value': 58173.31, 'new_value': 60705.1}, {'field': 'order_count', 'old_value': 2089, 'new_value': 2180}]
2025-05-24 12:01:28,640 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-24 12:01:29,109 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-24 12:01:29,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63876.0, 'new_value': 66226.0}, {'field': 'total_amount', 'old_value': 66284.0, 'new_value': 68634.0}, {'field': 'order_count', 'old_value': 270, 'new_value': 283}]
2025-05-24 12:01:29,109 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-24 12:01:29,547 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-24 12:01:29,547 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18990.7, 'new_value': 19367.2}, {'field': 'offline_amount', 'old_value': 49994.97, 'new_value': 50654.97}, {'field': 'total_amount', 'old_value': 68985.67, 'new_value': 70022.17}, {'field': 'order_count', 'old_value': 792, 'new_value': 808}]
2025-05-24 12:01:29,547 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-24 12:01:30,047 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-24 12:01:30,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87645.6, 'new_value': 90458.8}, {'field': 'offline_amount', 'old_value': 125940.0, 'new_value': 128834.0}, {'field': 'total_amount', 'old_value': 213585.6, 'new_value': 219292.8}, {'field': 'order_count', 'old_value': 1406, 'new_value': 1433}]
2025-05-24 12:01:30,047 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-24 12:01:30,594 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-24 12:01:30,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87664.57, 'new_value': 91024.29}, {'field': 'offline_amount', 'old_value': 64116.84, 'new_value': 67030.53}, {'field': 'total_amount', 'old_value': 151781.41, 'new_value': 158054.82}, {'field': 'order_count', 'old_value': 6421, 'new_value': 6727}]
2025-05-24 12:01:30,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-24 12:01:31,047 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-24 12:01:31,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8424.31, 'new_value': 9257.06}, {'field': 'offline_amount', 'old_value': 76935.22, 'new_value': 79914.65}, {'field': 'total_amount', 'old_value': 85359.53, 'new_value': 89171.71}, {'field': 'order_count', 'old_value': 2376, 'new_value': 2473}]
2025-05-24 12:01:31,047 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-24 12:01:31,515 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-24 12:01:31,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19575.5, 'new_value': 20787.58}, {'field': 'offline_amount', 'old_value': 36466.19, 'new_value': 39062.45}, {'field': 'total_amount', 'old_value': 56041.69, 'new_value': 59850.03}, {'field': 'order_count', 'old_value': 2963, 'new_value': 3161}]
2025-05-24 12:01:31,515 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-24 12:01:31,953 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-24 12:01:31,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100660.0, 'new_value': 103870.0}, {'field': 'total_amount', 'old_value': 100660.0, 'new_value': 103870.0}, {'field': 'order_count', 'old_value': 4956, 'new_value': 5171}]
2025-05-24 12:01:31,953 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-24 12:01:32,484 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-24 12:01:32,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27538.77, 'new_value': 28296.77}, {'field': 'total_amount', 'old_value': 29325.67, 'new_value': 30083.67}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-24 12:01:32,484 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-24 12:01:32,969 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-24 12:01:32,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9248.85, 'new_value': 9755.4}, {'field': 'offline_amount', 'old_value': 10894.48, 'new_value': 11176.58}, {'field': 'total_amount', 'old_value': 20143.33, 'new_value': 20931.98}, {'field': 'order_count', 'old_value': 1558, 'new_value': 1644}]
2025-05-24 12:01:32,969 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-24 12:01:33,531 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-24 12:01:33,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44492.0, 'new_value': 47155.0}, {'field': 'total_amount', 'old_value': 44841.0, 'new_value': 47504.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 87}]
2025-05-24 12:01:33,531 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-24 12:01:33,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-24 12:01:33,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45960.0, 'new_value': 47359.0}, {'field': 'total_amount', 'old_value': 45960.0, 'new_value': 47359.0}, {'field': 'order_count', 'old_value': 328, 'new_value': 338}]
2025-05-24 12:01:33,969 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-24 12:01:34,406 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-24 12:01:34,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45531.44, 'new_value': 49948.26}, {'field': 'offline_amount', 'old_value': 344479.16, 'new_value': 352878.03}, {'field': 'total_amount', 'old_value': 390010.6, 'new_value': 402826.29}, {'field': 'order_count', 'old_value': 3278, 'new_value': 3380}]
2025-05-24 12:01:34,422 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-24 12:01:34,875 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-24 12:01:34,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45869.98, 'new_value': 46204.98}, {'field': 'offline_amount', 'old_value': 413466.5, 'new_value': 428466.5}, {'field': 'total_amount', 'old_value': 459336.48, 'new_value': 474671.48}, {'field': 'order_count', 'old_value': 3565, 'new_value': 3704}]
2025-05-24 12:01:34,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-24 12:01:35,375 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-24 12:01:35,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104604.53, 'new_value': 107797.35}, {'field': 'total_amount', 'old_value': 104604.53, 'new_value': 107797.35}, {'field': 'order_count', 'old_value': 3081, 'new_value': 3190}]
2025-05-24 12:01:35,375 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-24 12:01:35,844 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-24 12:01:35,844 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 194.0}, {'field': 'total_amount', 'old_value': 30430.2, 'new_value': 30624.2}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-24 12:01:35,844 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-24 12:01:36,234 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-24 12:01:36,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116383.38, 'new_value': 121659.78}, {'field': 'offline_amount', 'old_value': 235994.51, 'new_value': 242769.87}, {'field': 'total_amount', 'old_value': 352377.89, 'new_value': 364429.65}, {'field': 'order_count', 'old_value': 4141, 'new_value': 4281}]
2025-05-24 12:01:36,234 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-24 12:01:36,703 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-24 12:01:36,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78158.25, 'new_value': 82359.93}, {'field': 'offline_amount', 'old_value': 35527.79, 'new_value': 36774.89}, {'field': 'total_amount', 'old_value': 113686.04, 'new_value': 119134.82}, {'field': 'order_count', 'old_value': 7070, 'new_value': 7345}]
2025-05-24 12:01:36,703 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-24 12:01:37,187 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-24 12:01:37,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147994.43, 'new_value': 157429.42}, {'field': 'total_amount', 'old_value': 147994.43, 'new_value': 157429.42}, {'field': 'order_count', 'old_value': 250, 'new_value': 268}]
2025-05-24 12:01:37,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-24 12:01:37,640 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-24 12:01:37,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 198552.0, 'new_value': 207912.0}, {'field': 'total_amount', 'old_value': 198552.0, 'new_value': 207912.0}, {'field': 'order_count', 'old_value': 16546, 'new_value': 17326}]
2025-05-24 12:01:37,640 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-24 12:01:38,109 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-24 12:01:38,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97079.98, 'new_value': 99694.98}, {'field': 'total_amount', 'old_value': 97079.98, 'new_value': 99694.98}, {'field': 'order_count', 'old_value': 850, 'new_value': 873}]
2025-05-24 12:01:38,109 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-24 12:01:38,625 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-24 12:01:38,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84927.9, 'new_value': 88709.5}, {'field': 'offline_amount', 'old_value': 219763.35, 'new_value': 227756.66}, {'field': 'total_amount', 'old_value': 304691.25, 'new_value': 316466.16}, {'field': 'order_count', 'old_value': 10157, 'new_value': 10637}]
2025-05-24 12:01:38,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-24 12:01:39,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-24 12:01:39,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356839.0, 'new_value': 386839.0}, {'field': 'total_amount', 'old_value': 356839.0, 'new_value': 386839.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-24 12:01:39,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-24 12:01:39,531 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-24 12:01:39,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39580.3, 'new_value': 41004.9}, {'field': 'total_amount', 'old_value': 39580.3, 'new_value': 41004.9}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-05-24 12:01:39,531 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-24 12:01:39,937 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-24 12:01:39,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6924.0, 'new_value': 7350.0}, {'field': 'total_amount', 'old_value': 76764.7, 'new_value': 77190.7}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-24 12:01:39,937 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-24 12:01:40,437 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-24 12:01:40,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41391.6, 'new_value': 43189.8}, {'field': 'total_amount', 'old_value': 43691.9, 'new_value': 45490.1}, {'field': 'order_count', 'old_value': 137, 'new_value': 141}]
2025-05-24 12:01:40,437 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-24 12:01:40,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-24 12:01:40,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 272021.45, 'new_value': 282965.56}, {'field': 'offline_amount', 'old_value': 17379.42, 'new_value': 17701.62}, {'field': 'total_amount', 'old_value': 289400.87, 'new_value': 300667.18}, {'field': 'order_count', 'old_value': 11591, 'new_value': 11959}]
2025-05-24 12:01:40,875 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-24 12:01:41,359 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-24 12:01:41,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18127.9, 'new_value': 18326.9}, {'field': 'offline_amount', 'old_value': 101721.4, 'new_value': 102235.4}, {'field': 'total_amount', 'old_value': 119849.3, 'new_value': 120562.3}, {'field': 'order_count', 'old_value': 167, 'new_value': 170}]
2025-05-24 12:01:41,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-24 12:01:41,844 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-24 12:01:41,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39833.25, 'new_value': 41550.55}, {'field': 'total_amount', 'old_value': 39833.25, 'new_value': 41550.55}, {'field': 'order_count', 'old_value': 1766, 'new_value': 1851}]
2025-05-24 12:01:41,844 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-24 12:01:42,390 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-24 12:01:42,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112263.38, 'new_value': 117404.2}, {'field': 'offline_amount', 'old_value': 233266.82, 'new_value': 242173.49}, {'field': 'total_amount', 'old_value': 345530.2, 'new_value': 359577.69}, {'field': 'order_count', 'old_value': 4371, 'new_value': 4557}]
2025-05-24 12:01:42,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-24 12:01:42,843 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-24 12:01:42,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93157.8, 'new_value': 100923.3}, {'field': 'total_amount', 'old_value': 224622.15, 'new_value': 232387.65}, {'field': 'order_count', 'old_value': 5869, 'new_value': 6115}]
2025-05-24 12:01:42,843 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-24 12:01:43,312 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-24 12:01:43,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230517.0, 'new_value': 237112.0}, {'field': 'total_amount', 'old_value': 230517.0, 'new_value': 237112.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 114}]
2025-05-24 12:01:43,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-24 12:01:43,828 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-24 12:01:43,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71048.3, 'new_value': 72443.7}, {'field': 'total_amount', 'old_value': 71048.3, 'new_value': 72443.7}, {'field': 'order_count', 'old_value': 517, 'new_value': 532}]
2025-05-24 12:01:43,828 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-24 12:01:44,281 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-24 12:01:44,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39505.69, 'new_value': 40410.51}, {'field': 'total_amount', 'old_value': 39505.69, 'new_value': 40410.51}, {'field': 'order_count', 'old_value': 161, 'new_value': 165}]
2025-05-24 12:01:44,281 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-24 12:01:44,625 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-24 12:01:44,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49928.59, 'new_value': 52000.44}, {'field': 'offline_amount', 'old_value': 30147.59, 'new_value': 31245.43}, {'field': 'total_amount', 'old_value': 80076.18, 'new_value': 83245.87}, {'field': 'order_count', 'old_value': 4355, 'new_value': 4543}]
2025-05-24 12:01:44,640 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-24 12:01:45,078 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-24 12:01:45,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67712.0, 'new_value': 75632.0}, {'field': 'total_amount', 'old_value': 72922.0, 'new_value': 80842.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 48}]
2025-05-24 12:01:45,078 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-24 12:01:45,468 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-24 12:01:45,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24417.02, 'new_value': 26683.9}, {'field': 'offline_amount', 'old_value': 86323.0, 'new_value': 86363.0}, {'field': 'total_amount', 'old_value': 110740.02, 'new_value': 113046.9}, {'field': 'order_count', 'old_value': 91, 'new_value': 93}]
2025-05-24 12:01:45,468 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-24 12:01:45,937 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-24 12:01:45,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26251.0, 'new_value': 26389.0}, {'field': 'total_amount', 'old_value': 26251.0, 'new_value': 26389.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 255}]
2025-05-24 12:01:45,937 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-24 12:01:46,437 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-24 12:01:46,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19097.18, 'new_value': 19366.58}, {'field': 'total_amount', 'old_value': 19097.18, 'new_value': 19366.58}, {'field': 'order_count', 'old_value': 159, 'new_value': 163}]
2025-05-24 12:01:46,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-24 12:01:46,922 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-24 12:01:46,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 371996.84, 'new_value': 385436.03}, {'field': 'total_amount', 'old_value': 371996.84, 'new_value': 385436.03}, {'field': 'order_count', 'old_value': 1332, 'new_value': 1374}]
2025-05-24 12:01:46,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-24 12:01:47,390 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-24 12:01:47,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270672.4, 'new_value': 285897.4}, {'field': 'total_amount', 'old_value': 270672.4, 'new_value': 285897.4}, {'field': 'order_count', 'old_value': 6872, 'new_value': 7237}]
2025-05-24 12:01:47,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-24 12:01:47,812 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-24 12:01:47,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34273.32, 'new_value': 36007.47}, {'field': 'total_amount', 'old_value': 34273.32, 'new_value': 36007.47}, {'field': 'order_count', 'old_value': 4429, 'new_value': 4657}]
2025-05-24 12:01:47,812 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-24 12:01:48,312 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-24 12:01:48,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22280.23, 'new_value': 23494.28}, {'field': 'offline_amount', 'old_value': 27980.06, 'new_value': 29544.78}, {'field': 'total_amount', 'old_value': 50260.29, 'new_value': 53039.06}, {'field': 'order_count', 'old_value': 2268, 'new_value': 2411}]
2025-05-24 12:01:48,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-24 12:01:48,703 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-24 12:01:48,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71156.54, 'new_value': 73378.66}, {'field': 'offline_amount', 'old_value': 107133.84, 'new_value': 108649.64}, {'field': 'total_amount', 'old_value': 178290.38, 'new_value': 182028.3}, {'field': 'order_count', 'old_value': 1827, 'new_value': 1872}]
2025-05-24 12:01:48,703 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-24 12:01:49,140 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-24 12:01:49,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74494.0, 'new_value': 79262.0}, {'field': 'total_amount', 'old_value': 79695.0, 'new_value': 84463.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 240}]
2025-05-24 12:01:49,140 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-24 12:01:49,609 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-24 12:01:49,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1514134.59, 'new_value': 1587513.05}, {'field': 'total_amount', 'old_value': 1668304.89, 'new_value': 1741683.35}, {'field': 'order_count', 'old_value': 5699, 'new_value': 5987}]
2025-05-24 12:01:49,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-24 12:01:50,047 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-24 12:01:50,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248364.8, 'new_value': 249021.8}, {'field': 'total_amount', 'old_value': 248364.8, 'new_value': 249021.8}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-05-24 12:01:50,047 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-24 12:01:50,562 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-24 12:01:50,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11047.0, 'new_value': 11785.0}, {'field': 'total_amount', 'old_value': 11047.0, 'new_value': 11785.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 83}]
2025-05-24 12:01:50,562 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-24 12:01:51,172 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-24 12:01:51,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99004.0, 'new_value': 106996.0}, {'field': 'total_amount', 'old_value': 99004.0, 'new_value': 106996.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-24 12:01:51,172 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-24 12:01:51,609 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-24 12:01:51,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392314.88, 'new_value': 405355.08}, {'field': 'total_amount', 'old_value': 392314.88, 'new_value': 405355.08}, {'field': 'order_count', 'old_value': 1965, 'new_value': 2040}]
2025-05-24 12:01:51,609 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-24 12:01:52,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-24 12:01:52,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9597.0, 'new_value': 9695.0}, {'field': 'total_amount', 'old_value': 11603.0, 'new_value': 11701.0}, {'field': 'order_count', 'old_value': 112, 'new_value': 113}]
2025-05-24 12:01:52,062 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-24 12:01:52,547 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-24 12:01:52,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25553.0, 'new_value': 26344.0}, {'field': 'total_amount', 'old_value': 25553.0, 'new_value': 26344.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 118}]
2025-05-24 12:01:52,547 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-24 12:01:53,031 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-24 12:01:53,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131580.0, 'new_value': 135979.0}, {'field': 'total_amount', 'old_value': 131580.0, 'new_value': 135979.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-24 12:01:53,031 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-24 12:01:53,437 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-24 12:01:53,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82091.7, 'new_value': 84577.4}, {'field': 'offline_amount', 'old_value': 121152.1, 'new_value': 124942.7}, {'field': 'total_amount', 'old_value': 203243.8, 'new_value': 209520.1}, {'field': 'order_count', 'old_value': 4113, 'new_value': 4239}]
2025-05-24 12:01:53,437 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-24 12:01:53,890 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-24 12:01:53,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 414221.35, 'new_value': 430176.58}, {'field': 'total_amount', 'old_value': 414221.35, 'new_value': 430176.58}, {'field': 'order_count', 'old_value': 5699, 'new_value': 5930}]
2025-05-24 12:01:53,890 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-24 12:01:54,343 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-24 12:01:54,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25488.9, 'new_value': 25767.9}, {'field': 'total_amount', 'old_value': 35056.2, 'new_value': 35335.2}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-24 12:01:54,343 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-24 12:01:54,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-24 12:01:54,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 536228.26, 'new_value': 557635.19}, {'field': 'total_amount', 'old_value': 538141.31, 'new_value': 559548.24}, {'field': 'order_count', 'old_value': 1277, 'new_value': 1338}]
2025-05-24 12:01:54,797 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-24 12:01:55,359 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-24 12:01:55,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155050.0, 'new_value': 162630.0}, {'field': 'total_amount', 'old_value': 155051.0, 'new_value': 162631.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-24 12:01:55,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-24 12:01:55,797 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-24 12:01:55,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77771.07, 'new_value': 80564.57}, {'field': 'total_amount', 'old_value': 77771.07, 'new_value': 80564.57}, {'field': 'order_count', 'old_value': 2404, 'new_value': 2480}]
2025-05-24 12:01:55,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-24 12:01:56,437 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-24 12:01:56,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8374.16, 'new_value': 8815.46}, {'field': 'offline_amount', 'old_value': 26522.09, 'new_value': 27353.09}, {'field': 'total_amount', 'old_value': 34896.25, 'new_value': 36168.55}, {'field': 'order_count', 'old_value': 1212, 'new_value': 1253}]
2025-05-24 12:01:56,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-24 12:01:56,875 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-24 12:01:56,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 983084.0, 'new_value': 1020162.0}, {'field': 'total_amount', 'old_value': 983084.0, 'new_value': 1020162.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 123}]
2025-05-24 12:01:56,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-24 12:01:57,375 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-24 12:01:57,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41689.46, 'new_value': 41755.17}, {'field': 'offline_amount', 'old_value': 47105.15, 'new_value': 50161.2}, {'field': 'total_amount', 'old_value': 88794.61, 'new_value': 91916.37}, {'field': 'order_count', 'old_value': 303, 'new_value': 311}]
2025-05-24 12:01:57,375 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-24 12:01:57,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-24 12:01:57,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129770.0, 'new_value': 133295.47}, {'field': 'total_amount', 'old_value': 129770.0, 'new_value': 133295.47}, {'field': 'order_count', 'old_value': 3346, 'new_value': 3458}]
2025-05-24 12:01:57,875 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-24 12:01:58,359 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-24 12:01:58,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30265.58, 'new_value': 33535.74}, {'field': 'offline_amount', 'old_value': 274092.54, 'new_value': 290558.72}, {'field': 'total_amount', 'old_value': 304358.12, 'new_value': 324094.46}, {'field': 'order_count', 'old_value': 7076, 'new_value': 7492}]
2025-05-24 12:01:58,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-24 12:01:58,828 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-24 12:01:58,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293778.56, 'new_value': 302833.25}, {'field': 'total_amount', 'old_value': 293778.56, 'new_value': 302833.25}, {'field': 'order_count', 'old_value': 2761, 'new_value': 2879}]
2025-05-24 12:01:58,828 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-24 12:01:59,328 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-24 12:01:59,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61884.0, 'new_value': 62754.0}, {'field': 'total_amount', 'old_value': 61884.0, 'new_value': 62754.0}, {'field': 'order_count', 'old_value': 303, 'new_value': 308}]
2025-05-24 12:01:59,328 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-24 12:01:59,718 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-24 12:01:59,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 283038.92, 'new_value': 292971.92}, {'field': 'offline_amount', 'old_value': 9193.5, 'new_value': 9273.5}, {'field': 'total_amount', 'old_value': 292232.42, 'new_value': 302245.42}, {'field': 'order_count', 'old_value': 2511, 'new_value': 2617}]
2025-05-24 12:01:59,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-24 12:02:00,297 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-24 12:02:00,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18538.0, 'new_value': 18807.0}, {'field': 'total_amount', 'old_value': 18538.0, 'new_value': 18807.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 100}]
2025-05-24 12:02:00,297 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-24 12:02:00,718 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-24 12:02:00,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372930.2, 'new_value': 380282.4}, {'field': 'total_amount', 'old_value': 372930.2, 'new_value': 380282.4}, {'field': 'order_count', 'old_value': 1857, 'new_value': 1903}]
2025-05-24 12:02:00,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-24 12:02:01,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-24 12:02:01,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24117.2, 'new_value': 25538.0}, {'field': 'total_amount', 'old_value': 24117.2, 'new_value': 25538.0}, {'field': 'order_count', 'old_value': 657, 'new_value': 693}]
2025-05-24 12:02:01,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-24 12:02:01,828 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-24 12:02:01,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121051.71, 'new_value': 125104.03}, {'field': 'offline_amount', 'old_value': 47575.46, 'new_value': 49059.26}, {'field': 'total_amount', 'old_value': 168627.17, 'new_value': 174163.29}, {'field': 'order_count', 'old_value': 10340, 'new_value': 10666}]
2025-05-24 12:02:01,828 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-24 12:02:02,328 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-24 12:02:02,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11137.4, 'new_value': 11337.4}, {'field': 'total_amount', 'old_value': 11566.4, 'new_value': 11766.4}, {'field': 'order_count', 'old_value': 151, 'new_value': 152}]
2025-05-24 12:02:02,328 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-24 12:02:02,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-24 12:02:02,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6507.1, 'new_value': 6866.1}, {'field': 'offline_amount', 'old_value': 33375.75, 'new_value': 34966.65}, {'field': 'total_amount', 'old_value': 39882.85, 'new_value': 41832.75}, {'field': 'order_count', 'old_value': 459, 'new_value': 479}]
2025-05-24 12:02:02,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-24 12:02:03,156 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-24 12:02:03,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6261.0, 'new_value': 6594.0}, {'field': 'offline_amount', 'old_value': 29192.0, 'new_value': 30625.0}, {'field': 'total_amount', 'old_value': 35453.0, 'new_value': 37219.0}, {'field': 'order_count', 'old_value': 276, 'new_value': 289}]
2025-05-24 12:02:03,156 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-24 12:02:03,687 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-24 12:02:03,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13830.0, 'new_value': 14199.0}, {'field': 'offline_amount', 'old_value': 26832.8, 'new_value': 34006.8}, {'field': 'total_amount', 'old_value': 40662.8, 'new_value': 48205.8}, {'field': 'order_count', 'old_value': 82, 'new_value': 89}]
2025-05-24 12:02:03,687 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-24 12:02:04,187 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-24 12:02:04,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182124.0, 'new_value': 186630.0}, {'field': 'total_amount', 'old_value': 182124.0, 'new_value': 186630.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 219}]
2025-05-24 12:02:04,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-24 12:02:04,609 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-24 12:02:04,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 618408.26, 'new_value': 635320.89}, {'field': 'total_amount', 'old_value': 618408.26, 'new_value': 635320.89}, {'field': 'order_count', 'old_value': 11717, 'new_value': 12020}]
2025-05-24 12:02:04,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-24 12:02:05,062 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-24 12:02:05,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74829.97, 'new_value': 83342.32}, {'field': 'offline_amount', 'old_value': 197907.12, 'new_value': 214818.34}, {'field': 'total_amount', 'old_value': 272737.09, 'new_value': 298160.66}, {'field': 'order_count', 'old_value': 13109, 'new_value': 14671}]
2025-05-24 12:02:05,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-24 12:02:05,562 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-24 12:02:05,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236117.9, 'new_value': 245784.98}, {'field': 'total_amount', 'old_value': 248168.87, 'new_value': 257835.95}, {'field': 'order_count', 'old_value': 10559, 'new_value': 10971}]
2025-05-24 12:02:05,562 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-24 12:02:06,031 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-24 12:02:06,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28915.37, 'new_value': 30017.78}, {'field': 'total_amount', 'old_value': 28915.37, 'new_value': 30017.78}, {'field': 'order_count', 'old_value': 1068, 'new_value': 1118}]
2025-05-24 12:02:06,031 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-24 12:02:06,468 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-24 12:02:06,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 647689.51, 'new_value': 674095.94}, {'field': 'total_amount', 'old_value': 647689.51, 'new_value': 674095.94}, {'field': 'order_count', 'old_value': 4837, 'new_value': 5041}]
2025-05-24 12:02:06,484 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-24 12:02:06,968 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-24 12:02:06,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187864.0, 'new_value': 193027.0}, {'field': 'total_amount', 'old_value': 187864.0, 'new_value': 193027.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 593}]
2025-05-24 12:02:06,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-24 12:02:07,468 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-24 12:02:07,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53692.83, 'new_value': 56956.72}, {'field': 'offline_amount', 'old_value': 394848.49, 'new_value': 408243.11}, {'field': 'total_amount', 'old_value': 448541.32, 'new_value': 465199.83}, {'field': 'order_count', 'old_value': 2142, 'new_value': 2230}]
2025-05-24 12:02:07,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-24 12:02:07,921 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-24 12:02:07,921 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1588.66, 'new_value': 1615.28}, {'field': 'offline_amount', 'old_value': 20213.94, 'new_value': 20472.43}, {'field': 'total_amount', 'old_value': 21802.6, 'new_value': 22087.71}, {'field': 'order_count', 'old_value': 776, 'new_value': 787}]
2025-05-24 12:02:07,921 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-24 12:02:08,421 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-24 12:02:08,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 745874.0, 'new_value': 777367.0}, {'field': 'total_amount', 'old_value': 745874.0, 'new_value': 777367.0}, {'field': 'order_count', 'old_value': 3360, 'new_value': 3503}]
2025-05-24 12:02:08,421 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-24 12:02:08,859 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-24 12:02:08,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30061.0, 'new_value': 31141.0}, {'field': 'total_amount', 'old_value': 30061.0, 'new_value': 31141.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-24 12:02:08,859 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-24 12:02:09,265 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-24 12:02:09,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6046.42, 'new_value': 6367.56}, {'field': 'offline_amount', 'old_value': 316624.14, 'new_value': 325104.14}, {'field': 'total_amount', 'old_value': 322670.56, 'new_value': 331471.7}, {'field': 'order_count', 'old_value': 15667, 'new_value': 16194}]
2025-05-24 12:02:09,265 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-24 12:02:09,734 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-24 12:02:09,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185376.9, 'new_value': 195692.7}, {'field': 'total_amount', 'old_value': 185376.9, 'new_value': 195692.7}, {'field': 'order_count', 'old_value': 1022, 'new_value': 1082}]
2025-05-24 12:02:09,734 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-24 12:02:10,203 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-24 12:02:10,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56498.21, 'new_value': 59224.06}, {'field': 'offline_amount', 'old_value': 77284.58, 'new_value': 79099.96}, {'field': 'total_amount', 'old_value': 133782.79, 'new_value': 138324.02}, {'field': 'order_count', 'old_value': 6152, 'new_value': 6369}]
2025-05-24 12:02:10,203 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-24 12:02:10,640 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-24 12:02:10,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320445.17, 'new_value': 333397.69}, {'field': 'total_amount', 'old_value': 342608.29, 'new_value': 355560.81}, {'field': 'order_count', 'old_value': 14542, 'new_value': 15105}]
2025-05-24 12:02:10,640 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-24 12:02:11,078 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-24 12:02:11,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26113.24, 'new_value': 27775.59}, {'field': 'offline_amount', 'old_value': 203683.54, 'new_value': 209219.94}, {'field': 'total_amount', 'old_value': 229796.78, 'new_value': 236995.53}, {'field': 'order_count', 'old_value': 7235, 'new_value': 7456}]
2025-05-24 12:02:11,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-24 12:02:11,546 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-24 12:02:11,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121947.0, 'new_value': 123116.0}, {'field': 'total_amount', 'old_value': 121947.0, 'new_value': 123116.0}, {'field': 'order_count', 'old_value': 3895, 'new_value': 3930}]
2025-05-24 12:02:11,546 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-24 12:02:12,078 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-24 12:02:12,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114791.29, 'new_value': 123472.79}, {'field': 'offline_amount', 'old_value': 307586.35, 'new_value': 312220.95}, {'field': 'total_amount', 'old_value': 422377.64, 'new_value': 435693.74}, {'field': 'order_count', 'old_value': 3765, 'new_value': 3952}]
2025-05-24 12:02:12,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-24 12:02:12,531 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-24 12:02:12,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83077.1, 'new_value': 87187.12}, {'field': 'offline_amount', 'old_value': 96474.43, 'new_value': 100067.82}, {'field': 'total_amount', 'old_value': 179551.53, 'new_value': 187254.94}, {'field': 'order_count', 'old_value': 7260, 'new_value': 7577}]
2025-05-24 12:02:12,531 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-24 12:02:12,984 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-24 12:02:12,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 212068.92, 'new_value': 217779.92}, {'field': 'offline_amount', 'old_value': 86167.68, 'new_value': 86267.68}, {'field': 'total_amount', 'old_value': 298236.6, 'new_value': 304047.6}, {'field': 'order_count', 'old_value': 537, 'new_value': 550}]
2025-05-24 12:02:12,984 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-24 12:02:13,437 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-24 12:02:13,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204111.95, 'new_value': 210033.09}, {'field': 'total_amount', 'old_value': 223285.38, 'new_value': 229206.52}, {'field': 'order_count', 'old_value': 4611, 'new_value': 4723}]
2025-05-24 12:02:13,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-24 12:02:13,843 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-24 12:02:13,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49227.0, 'new_value': 51298.0}, {'field': 'total_amount', 'old_value': 49227.0, 'new_value': 51298.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 111}]
2025-05-24 12:02:13,843 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-24 12:02:14,312 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-24 12:02:14,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 605520.72, 'new_value': 632572.72}, {'field': 'total_amount', 'old_value': 605520.72, 'new_value': 632572.72}, {'field': 'order_count', 'old_value': 4649, 'new_value': 4847}]
2025-05-24 12:02:14,312 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-24 12:02:14,734 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-24 12:02:14,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69533.82, 'new_value': 71627.29}, {'field': 'total_amount', 'old_value': 69533.82, 'new_value': 71627.29}, {'field': 'order_count', 'old_value': 3959, 'new_value': 4095}]
2025-05-24 12:02:14,734 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-24 12:02:15,187 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-24 12:02:15,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50632.77, 'new_value': 53312.55}, {'field': 'offline_amount', 'old_value': 39183.5, 'new_value': 40326.4}, {'field': 'total_amount', 'old_value': 89816.27, 'new_value': 93638.95}, {'field': 'order_count', 'old_value': 1788, 'new_value': 1868}]
2025-05-24 12:02:15,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-24 12:02:15,609 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-24 12:02:15,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64000.0, 'new_value': 83800.0}, {'field': 'total_amount', 'old_value': 64000.0, 'new_value': 83800.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-24 12:02:15,609 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-24 12:02:16,078 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-24 12:02:16,093 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-24 12:02:16,093 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-24 12:02:16,562 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-24 12:02:16,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30827.7, 'new_value': 31549.7}, {'field': 'total_amount', 'old_value': 30827.7, 'new_value': 31549.7}, {'field': 'order_count', 'old_value': 180, 'new_value': 184}]
2025-05-24 12:02:16,562 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-24 12:02:17,062 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-24 12:02:17,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89746.83, 'new_value': 91801.53}, {'field': 'total_amount', 'old_value': 89746.83, 'new_value': 91801.53}, {'field': 'order_count', 'old_value': 115, 'new_value': 120}]
2025-05-24 12:02:17,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-24 12:02:17,515 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-24 12:02:17,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73248.83, 'new_value': 75252.62}, {'field': 'offline_amount', 'old_value': 166187.07, 'new_value': 171014.47}, {'field': 'total_amount', 'old_value': 239435.9, 'new_value': 246267.09}, {'field': 'order_count', 'old_value': 4541, 'new_value': 4722}]
2025-05-24 12:02:17,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-24 12:02:17,953 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-24 12:02:17,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4100.0, 'new_value': 4300.0}, {'field': 'offline_amount', 'old_value': 274795.0, 'new_value': 287425.0}, {'field': 'total_amount', 'old_value': 278895.0, 'new_value': 291725.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 202}]
2025-05-24 12:02:17,953 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-24 12:02:18,406 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-24 12:02:18,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 628168.32, 'new_value': 644636.12}, {'field': 'total_amount', 'old_value': 628168.32, 'new_value': 644636.12}, {'field': 'order_count', 'old_value': 7394, 'new_value': 7623}]
2025-05-24 12:02:18,406 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-24 12:02:18,812 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-24 12:02:18,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 410913.2, 'new_value': 419677.0}, {'field': 'total_amount', 'old_value': 591111.2, 'new_value': 599875.0}, {'field': 'order_count', 'old_value': 3978, 'new_value': 3986}]
2025-05-24 12:02:18,812 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-24 12:02:19,265 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-24 12:02:19,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159180.0, 'new_value': 161439.0}, {'field': 'total_amount', 'old_value': 159180.0, 'new_value': 161439.0}, {'field': 'order_count', 'old_value': 2669, 'new_value': 2723}]
2025-05-24 12:02:19,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-24 12:02:19,718 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-24 12:02:19,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160119.64, 'new_value': 164414.64}, {'field': 'total_amount', 'old_value': 160119.64, 'new_value': 164414.64}, {'field': 'order_count', 'old_value': 6768, 'new_value': 6964}]
2025-05-24 12:02:19,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-24 12:02:20,187 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-24 12:02:20,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 497976.0, 'new_value': 524627.0}, {'field': 'total_amount', 'old_value': 497976.0, 'new_value': 524627.0}, {'field': 'order_count', 'old_value': 446, 'new_value': 471}]
2025-05-24 12:02:20,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-24 12:02:20,625 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-24 12:02:20,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 230300.02, 'new_value': 243770.96}, {'field': 'offline_amount', 'old_value': 132150.09, 'new_value': 137591.59}, {'field': 'total_amount', 'old_value': 362450.11, 'new_value': 381362.55}, {'field': 'order_count', 'old_value': 3046, 'new_value': 3102}]
2025-05-24 12:02:20,625 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-24 12:02:21,109 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-24 12:02:21,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203846.82, 'new_value': 211414.94}, {'field': 'total_amount', 'old_value': 203846.82, 'new_value': 211414.94}, {'field': 'order_count', 'old_value': 1568, 'new_value': 1635}]
2025-05-24 12:02:21,109 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-24 12:02:21,578 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-24 12:02:21,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76651.2, 'new_value': 78325.8}, {'field': 'total_amount', 'old_value': 78618.0, 'new_value': 80292.6}, {'field': 'order_count', 'old_value': 488, 'new_value': 503}]
2025-05-24 12:02:21,578 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-24 12:02:21,999 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-24 12:02:21,999 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57501.6, 'new_value': 61501.6}, {'field': 'offline_amount', 'old_value': 84234.79, 'new_value': 84619.63}, {'field': 'total_amount', 'old_value': 141736.39, 'new_value': 146121.23}, {'field': 'order_count', 'old_value': 3929, 'new_value': 4034}]
2025-05-24 12:02:21,999 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-24 12:02:22,421 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-24 12:02:22,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45644.0, 'new_value': 46424.0}, {'field': 'total_amount', 'old_value': 45644.0, 'new_value': 46424.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 108}]
2025-05-24 12:02:22,421 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-24 12:02:22,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-24 12:02:22,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184736.71, 'new_value': 192328.71}, {'field': 'offline_amount', 'old_value': 22340.5, 'new_value': 22774.5}, {'field': 'total_amount', 'old_value': 207077.21, 'new_value': 215103.21}, {'field': 'order_count', 'old_value': 9475, 'new_value': 9972}]
2025-05-24 12:02:22,906 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-24 12:02:23,359 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-24 12:02:23,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82629.0, 'new_value': 89839.0}, {'field': 'offline_amount', 'old_value': 959768.0, 'new_value': 1006558.0}, {'field': 'total_amount', 'old_value': 1042397.0, 'new_value': 1096397.0}, {'field': 'order_count', 'old_value': 26400, 'new_value': 27785}]
2025-05-24 12:02:23,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-24 12:02:23,828 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-24 12:02:23,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340150.88, 'new_value': 353876.3}, {'field': 'total_amount', 'old_value': 353661.36, 'new_value': 367386.78}, {'field': 'order_count', 'old_value': 1154, 'new_value': 1191}]
2025-05-24 12:02:23,828 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-24 12:02:24,359 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-24 12:02:24,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46995.0, 'new_value': 47159.0}, {'field': 'offline_amount', 'old_value': 238772.0, 'new_value': 239832.0}, {'field': 'total_amount', 'old_value': 285767.0, 'new_value': 286991.0}, {'field': 'order_count', 'old_value': 251, 'new_value': 254}]
2025-05-24 12:02:24,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-24 12:02:24,796 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-24 12:02:24,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19220.0, 'new_value': 25000.0}, {'field': 'total_amount', 'old_value': 19220.0, 'new_value': 25000.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 8}]
2025-05-24 12:02:24,796 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-24 12:02:25,296 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-24 12:02:25,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32256.67, 'new_value': 34003.68}, {'field': 'offline_amount', 'old_value': 42134.82, 'new_value': 43134.82}, {'field': 'total_amount', 'old_value': 74391.49, 'new_value': 77138.5}, {'field': 'order_count', 'old_value': 3628, 'new_value': 3792}]
2025-05-24 12:02:25,296 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-24 12:02:25,718 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-24 12:02:25,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20009.48, 'new_value': 21092.68}, {'field': 'offline_amount', 'old_value': 14240.96, 'new_value': 14667.86}, {'field': 'total_amount', 'old_value': 34250.44, 'new_value': 35760.54}, {'field': 'order_count', 'old_value': 1464, 'new_value': 1527}]
2025-05-24 12:02:25,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-24 12:02:26,203 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-24 12:02:26,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272378.0, 'new_value': 279438.0}, {'field': 'total_amount', 'old_value': 272378.0, 'new_value': 279438.0}, {'field': 'order_count', 'old_value': 403, 'new_value': 426}]
2025-05-24 12:02:26,203 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-24 12:02:26,640 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-24 12:02:26,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34504.0, 'new_value': 35654.0}, {'field': 'total_amount', 'old_value': 34504.0, 'new_value': 35654.0}, {'field': 'order_count', 'old_value': 156, 'new_value': 165}]
2025-05-24 12:02:26,640 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-24 12:02:27,062 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-24 12:02:27,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14975.9, 'new_value': 15154.8}, {'field': 'offline_amount', 'old_value': 37110.6, 'new_value': 37149.6}, {'field': 'total_amount', 'old_value': 52086.5, 'new_value': 52304.4}, {'field': 'order_count', 'old_value': 537, 'new_value': 541}]
2025-05-24 12:02:27,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-24 12:02:27,484 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-24 12:02:27,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103670.1, 'new_value': 109427.69}, {'field': 'offline_amount', 'old_value': 183927.85, 'new_value': 191398.74}, {'field': 'total_amount', 'old_value': 287597.95, 'new_value': 300826.43}, {'field': 'order_count', 'old_value': 8863, 'new_value': 9321}]
2025-05-24 12:02:27,484 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-24 12:02:27,937 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-24 12:02:27,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92328.0, 'new_value': 96788.0}, {'field': 'total_amount', 'old_value': 92328.0, 'new_value': 96788.0}, {'field': 'order_count', 'old_value': 398, 'new_value': 413}]
2025-05-24 12:02:27,937 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-24 12:02:28,406 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-24 12:02:28,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154944.91, 'new_value': 162889.93}, {'field': 'offline_amount', 'old_value': 41227.33, 'new_value': 42145.89}, {'field': 'total_amount', 'old_value': 196172.24, 'new_value': 205035.82}, {'field': 'order_count', 'old_value': 11073, 'new_value': 11551}]
2025-05-24 12:02:28,406 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-24 12:02:28,843 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-24 12:02:28,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236465.12, 'new_value': 244874.93}, {'field': 'total_amount', 'old_value': 258952.52, 'new_value': 267362.33}, {'field': 'order_count', 'old_value': 1420, 'new_value': 1477}]
2025-05-24 12:02:28,843 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-24 12:02:29,328 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-24 12:02:29,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 180866.5, 'new_value': 190343.57}, {'field': 'offline_amount', 'old_value': 334946.92, 'new_value': 344946.92}, {'field': 'total_amount', 'old_value': 515813.42, 'new_value': 535290.49}, {'field': 'order_count', 'old_value': 1272, 'new_value': 1336}]
2025-05-24 12:02:29,328 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-24 12:02:29,734 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-24 12:02:29,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150944.99, 'new_value': 157002.39}, {'field': 'total_amount', 'old_value': 150944.99, 'new_value': 157002.39}, {'field': 'order_count', 'old_value': 7730, 'new_value': 7930}]
2025-05-24 12:02:29,734 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-24 12:02:30,187 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-24 12:02:30,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142892.0, 'new_value': 148353.6}, {'field': 'total_amount', 'old_value': 142892.0, 'new_value': 148353.6}, {'field': 'order_count', 'old_value': 634, 'new_value': 660}]
2025-05-24 12:02:30,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-24 12:02:30,593 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-24 12:02:30,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120505.0, 'new_value': 122835.3}, {'field': 'total_amount', 'old_value': 120505.0, 'new_value': 122835.3}, {'field': 'order_count', 'old_value': 3322, 'new_value': 3389}]
2025-05-24 12:02:30,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-24 12:02:31,046 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-24 12:02:31,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8631.0, 'new_value': 8885.0}, {'field': 'total_amount', 'old_value': 19416.0, 'new_value': 19670.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 110}]
2025-05-24 12:02:31,046 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-24 12:02:31,515 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-24 12:02:31,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140659.65, 'new_value': 145456.45}, {'field': 'offline_amount', 'old_value': 257371.45, 'new_value': 268690.35}, {'field': 'total_amount', 'old_value': 398031.1, 'new_value': 414146.8}, {'field': 'order_count', 'old_value': 3327, 'new_value': 3455}]
2025-05-24 12:02:31,515 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-24 12:02:32,015 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-24 12:02:32,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1706881.0, 'new_value': 1752190.0}, {'field': 'total_amount', 'old_value': 1706881.0, 'new_value': 1752190.0}, {'field': 'order_count', 'old_value': 6719, 'new_value': 6906}]
2025-05-24 12:02:32,015 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-24 12:02:32,499 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-24 12:02:32,499 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91093.5, 'new_value': 94356.3}, {'field': 'total_amount', 'old_value': 91093.5, 'new_value': 94356.3}, {'field': 'order_count', 'old_value': 437, 'new_value': 453}]
2025-05-24 12:02:32,499 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-24 12:02:33,124 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-24 12:02:33,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37992.05, 'new_value': 39435.2}, {'field': 'offline_amount', 'old_value': 22196.32, 'new_value': 22705.57}, {'field': 'total_amount', 'old_value': 60188.37, 'new_value': 62140.77}, {'field': 'order_count', 'old_value': 2614, 'new_value': 2715}]
2025-05-24 12:02:33,124 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-24 12:02:33,578 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-24 12:02:33,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13574.49, 'new_value': 14107.57}, {'field': 'offline_amount', 'old_value': 29742.2, 'new_value': 30393.2}, {'field': 'total_amount', 'old_value': 43316.69, 'new_value': 44500.77}, {'field': 'order_count', 'old_value': 1723, 'new_value': 1782}]
2025-05-24 12:02:33,578 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-24 12:02:33,984 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-24 12:02:33,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75542.0, 'new_value': 96069.0}, {'field': 'total_amount', 'old_value': 75542.0, 'new_value': 96069.0}, {'field': 'order_count', 'old_value': 1692, 'new_value': 1693}]
2025-05-24 12:02:33,984 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-24 12:02:34,406 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-24 12:02:34,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75798.15, 'new_value': 78235.26}, {'field': 'total_amount', 'old_value': 78079.26, 'new_value': 80516.37}, {'field': 'order_count', 'old_value': 371, 'new_value': 378}]
2025-05-24 12:02:34,406 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-24 12:02:34,984 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-24 12:02:34,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 678842.17, 'new_value': 701630.77}, {'field': 'total_amount', 'old_value': 678842.17, 'new_value': 701630.77}, {'field': 'order_count', 'old_value': 5445, 'new_value': 5668}]
2025-05-24 12:02:34,984 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-24 12:02:35,437 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-24 12:02:35,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21365.5, 'new_value': 22194.0}, {'field': 'offline_amount', 'old_value': 167002.7, 'new_value': 174558.4}, {'field': 'total_amount', 'old_value': 188368.2, 'new_value': 196752.4}, {'field': 'order_count', 'old_value': 5815, 'new_value': 6120}]
2025-05-24 12:02:35,437 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-24 12:02:35,921 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-24 12:02:35,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 472201.0, 'new_value': 483504.0}, {'field': 'total_amount', 'old_value': 472201.0, 'new_value': 483504.0}, {'field': 'order_count', 'old_value': 3166, 'new_value': 3282}]
2025-05-24 12:02:35,921 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-24 12:02:36,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-24 12:02:36,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4475.0, 'new_value': 4605.0}, {'field': 'total_amount', 'old_value': 10903.0, 'new_value': 11033.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 108}]
2025-05-24 12:02:36,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-24 12:02:36,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-24 12:02:36,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48176.53, 'new_value': 50513.96}, {'field': 'offline_amount', 'old_value': 47544.18, 'new_value': 50953.76}, {'field': 'total_amount', 'old_value': 95720.71, 'new_value': 101467.72}, {'field': 'order_count', 'old_value': 4845, 'new_value': 5069}]
2025-05-24 12:02:36,765 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-24 12:02:37,249 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-24 12:02:37,249 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3066.0, 'new_value': 3298.0}, {'field': 'offline_amount', 'old_value': 25409.0, 'new_value': 25639.0}, {'field': 'total_amount', 'old_value': 28475.0, 'new_value': 28937.0}, {'field': 'order_count', 'old_value': 1043, 'new_value': 1053}]
2025-05-24 12:02:37,249 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-24 12:02:37,718 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-24 12:02:37,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92102.98, 'new_value': 96496.44}, {'field': 'offline_amount', 'old_value': 102357.19, 'new_value': 106912.05}, {'field': 'total_amount', 'old_value': 194460.17, 'new_value': 203408.49}, {'field': 'order_count', 'old_value': 4912, 'new_value': 5152}]
2025-05-24 12:02:37,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-24 12:02:38,218 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-24 12:02:38,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71069.0, 'new_value': 79069.0}, {'field': 'total_amount', 'old_value': 71069.0, 'new_value': 79069.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-24 12:02:38,218 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-24 12:02:38,624 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-24 12:02:38,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55440.0, 'new_value': 70180.0}, {'field': 'total_amount', 'old_value': 55440.0, 'new_value': 70180.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-05-24 12:02:38,624 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-24 12:02:39,046 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-24 12:02:39,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 850821.0, 'new_value': 903651.0}, {'field': 'total_amount', 'old_value': 850821.0, 'new_value': 903651.0}, {'field': 'order_count', 'old_value': 998, 'new_value': 1037}]
2025-05-24 12:02:39,046 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-24 12:02:39,531 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-24 12:02:39,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180097.3, 'new_value': 189219.1}, {'field': 'total_amount', 'old_value': 186047.6, 'new_value': 195169.4}, {'field': 'order_count', 'old_value': 344, 'new_value': 362}]
2025-05-24 12:02:39,531 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-24 12:02:39,999 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-24 12:02:39,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87850.0, 'new_value': 96650.0}, {'field': 'total_amount', 'old_value': 87850.0, 'new_value': 96650.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-05-24 12:02:39,999 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-24 12:02:40,468 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-24 12:02:40,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38209.85, 'new_value': 39987.85}, {'field': 'offline_amount', 'old_value': 104026.0, 'new_value': 107730.0}, {'field': 'total_amount', 'old_value': 142235.85, 'new_value': 147717.85}, {'field': 'order_count', 'old_value': 1579, 'new_value': 1652}]
2025-05-24 12:02:40,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-24 12:02:40,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-24 12:02:40,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121226.0, 'new_value': 126906.0}, {'field': 'offline_amount', 'old_value': 82724.0, 'new_value': 85966.0}, {'field': 'total_amount', 'old_value': 203950.0, 'new_value': 212872.0}, {'field': 'order_count', 'old_value': 2661, 'new_value': 2804}]
2025-05-24 12:02:40,906 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-24 12:02:41,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-24 12:02:41,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7566.4, 'new_value': 7645.4}, {'field': 'offline_amount', 'old_value': 20702.67, 'new_value': 22758.67}, {'field': 'total_amount', 'old_value': 28269.07, 'new_value': 30404.07}, {'field': 'order_count', 'old_value': 286, 'new_value': 299}]
2025-05-24 12:02:41,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-24 12:02:41,796 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-24 12:02:41,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9268.5, 'new_value': 9863.5}, {'field': 'offline_amount', 'old_value': 136436.0, 'new_value': 142447.0}, {'field': 'total_amount', 'old_value': 145704.5, 'new_value': 152310.5}, {'field': 'order_count', 'old_value': 66, 'new_value': 71}]
2025-05-24 12:02:41,796 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-24 12:02:42,265 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-24 12:02:42,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101634.11, 'new_value': 105998.87}, {'field': 'total_amount', 'old_value': 108863.18, 'new_value': 113227.94}, {'field': 'order_count', 'old_value': 574, 'new_value': 608}]
2025-05-24 12:02:42,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-24 12:02:42,749 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-24 12:02:42,749 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28166.62, 'new_value': 29332.66}, {'field': 'offline_amount', 'old_value': 30339.82, 'new_value': 30934.82}, {'field': 'total_amount', 'old_value': 58506.44, 'new_value': 60267.48}, {'field': 'order_count', 'old_value': 255, 'new_value': 267}]
2025-05-24 12:02:42,749 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-24 12:02:43,218 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-24 12:02:43,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191466.5, 'new_value': 200558.5}, {'field': 'total_amount', 'old_value': 191466.5, 'new_value': 200558.5}, {'field': 'order_count', 'old_value': 947, 'new_value': 992}]
2025-05-24 12:02:43,218 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-24 12:02:43,640 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-24 12:02:43,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4618.0, 'new_value': 4718.0}, {'field': 'offline_amount', 'old_value': 19184.0, 'new_value': 19954.0}, {'field': 'total_amount', 'old_value': 23802.0, 'new_value': 24672.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 193}]
2025-05-24 12:02:43,640 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-24 12:02:44,109 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-24 12:02:44,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4164.0, 'new_value': 6666.04}, {'field': 'offline_amount', 'old_value': 46596.14, 'new_value': 46612.14}, {'field': 'total_amount', 'old_value': 50760.14, 'new_value': 53278.18}, {'field': 'order_count', 'old_value': 447, 'new_value': 480}]
2025-05-24 12:02:44,109 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-24 12:02:44,624 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-24 12:02:44,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171084.3, 'new_value': 178482.5}, {'field': 'total_amount', 'old_value': 171084.3, 'new_value': 178482.5}, {'field': 'order_count', 'old_value': 641, 'new_value': 674}]
2025-05-24 12:02:44,624 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-24 12:02:45,077 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-24 12:02:45,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58962.0, 'new_value': 61096.0}, {'field': 'offline_amount', 'old_value': 272166.0, 'new_value': 283122.0}, {'field': 'total_amount', 'old_value': 331128.0, 'new_value': 344218.0}, {'field': 'order_count', 'old_value': 1316, 'new_value': 1376}]
2025-05-24 12:02:45,077 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-24 12:02:45,577 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-24 12:02:45,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 915416.0, 'new_value': 951716.0}, {'field': 'total_amount', 'old_value': 915416.0, 'new_value': 951716.0}, {'field': 'order_count', 'old_value': 4009, 'new_value': 4159}]
2025-05-24 12:02:45,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-24 12:02:46,077 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-24 12:02:46,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190291.0, 'new_value': 196148.0}, {'field': 'total_amount', 'old_value': 190291.0, 'new_value': 196148.0}, {'field': 'order_count', 'old_value': 429, 'new_value': 445}]
2025-05-24 12:02:46,077 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-24 12:02:46,546 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-24 12:02:46,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 263090.84, 'new_value': 273756.96}, {'field': 'offline_amount', 'old_value': 182633.86, 'new_value': 189062.24}, {'field': 'total_amount', 'old_value': 445724.7, 'new_value': 462819.2}, {'field': 'order_count', 'old_value': 17869, 'new_value': 18582}]
2025-05-24 12:02:46,546 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-24 12:02:46,968 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-24 12:02:46,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40709.89, 'new_value': 43339.76}, {'field': 'offline_amount', 'old_value': 61815.41, 'new_value': 64054.16}, {'field': 'total_amount', 'old_value': 102525.3, 'new_value': 107393.92}, {'field': 'order_count', 'old_value': 2150, 'new_value': 2276}]
2025-05-24 12:02:46,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-24 12:02:47,406 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-24 12:02:47,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256367.0, 'new_value': 268857.0}, {'field': 'total_amount', 'old_value': 256367.0, 'new_value': 268857.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 330}]
2025-05-24 12:02:47,406 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-24 12:02:47,890 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-24 12:02:47,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30130.0, 'new_value': 34602.0}, {'field': 'total_amount', 'old_value': 30130.0, 'new_value': 34602.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-24 12:02:47,890 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-24 12:02:48,359 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-24 12:02:48,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140563.7, 'new_value': 156197.6}, {'field': 'total_amount', 'old_value': 284218.48, 'new_value': 299852.38}]
2025-05-24 12:02:48,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-24 12:02:48,843 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-24 12:02:48,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281911.5, 'new_value': 287832.3}, {'field': 'total_amount', 'old_value': 281911.5, 'new_value': 287832.3}, {'field': 'order_count', 'old_value': 6134, 'new_value': 6264}]
2025-05-24 12:02:48,843 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-24 12:02:49,281 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-24 12:02:49,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48093.4, 'new_value': 49506.4}, {'field': 'total_amount', 'old_value': 48093.4, 'new_value': 49506.4}, {'field': 'order_count', 'old_value': 253, 'new_value': 268}]
2025-05-24 12:02:49,281 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-24 12:02:49,734 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-24 12:02:49,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171003.0, 'new_value': 177788.0}, {'field': 'offline_amount', 'old_value': 159055.0, 'new_value': 167419.0}, {'field': 'total_amount', 'old_value': 330058.0, 'new_value': 345207.0}, {'field': 'order_count', 'old_value': 887, 'new_value': 929}]
2025-05-24 12:02:49,734 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-24 12:02:50,171 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-24 12:02:50,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 644327.26, 'new_value': 679573.05}, {'field': 'total_amount', 'old_value': 644327.26, 'new_value': 679573.05}, {'field': 'order_count', 'old_value': 3630, 'new_value': 3730}]
2025-05-24 12:02:50,171 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-24 12:02:50,718 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-24 12:02:50,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123937.08, 'new_value': 127922.84}, {'field': 'total_amount', 'old_value': 123937.08, 'new_value': 127922.84}, {'field': 'order_count', 'old_value': 8548, 'new_value': 8851}]
2025-05-24 12:02:50,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-24 12:02:51,077 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-24 12:02:51,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87795.0, 'new_value': 91208.0}, {'field': 'total_amount', 'old_value': 87795.0, 'new_value': 91208.0}, {'field': 'order_count', 'old_value': 5946, 'new_value': 6141}]
2025-05-24 12:02:51,077 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-24 12:02:51,499 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-24 12:02:51,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128895.0, 'new_value': 136012.0}, {'field': 'total_amount', 'old_value': 128895.0, 'new_value': 136012.0}, {'field': 'order_count', 'old_value': 9540, 'new_value': 10116}]
2025-05-24 12:02:51,499 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-24 12:02:51,937 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-24 12:02:51,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30745.0, 'new_value': 32032.0}, {'field': 'total_amount', 'old_value': 30745.0, 'new_value': 32032.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-24 12:02:51,937 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-24 12:02:52,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-24 12:02:52,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68160.2, 'new_value': 68896.2}, {'field': 'total_amount', 'old_value': 68440.0, 'new_value': 69176.0}, {'field': 'order_count', 'old_value': 1006, 'new_value': 1021}]
2025-05-24 12:02:52,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-24 12:02:52,843 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-24 12:02:52,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24404.9, 'new_value': 25509.9}, {'field': 'offline_amount', 'old_value': 53466.0, 'new_value': 55426.0}, {'field': 'total_amount', 'old_value': 77870.9, 'new_value': 80935.9}, {'field': 'order_count', 'old_value': 2909, 'new_value': 3033}]
2025-05-24 12:02:52,843 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-24 12:02:53,281 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-24 12:02:53,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22988.21, 'new_value': 23581.21}, {'field': 'total_amount', 'old_value': 22988.21, 'new_value': 23581.21}, {'field': 'order_count', 'old_value': 101, 'new_value': 104}]
2025-05-24 12:02:53,281 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-24 12:02:53,687 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-24 12:02:53,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 480623.39, 'new_value': 495479.39}, {'field': 'total_amount', 'old_value': 486169.75, 'new_value': 501025.75}, {'field': 'order_count', 'old_value': 5239, 'new_value': 5364}]
2025-05-24 12:02:53,687 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-24 12:02:54,140 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-24 12:02:54,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178298.5, 'new_value': 188153.6}, {'field': 'total_amount', 'old_value': 178298.5, 'new_value': 188153.6}, {'field': 'order_count', 'old_value': 3298, 'new_value': 3449}]
2025-05-24 12:02:54,140 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-24 12:02:54,577 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-24 12:02:54,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289125.0, 'new_value': 302459.0}, {'field': 'total_amount', 'old_value': 289125.0, 'new_value': 302459.0}, {'field': 'order_count', 'old_value': 6295, 'new_value': 6605}]
2025-05-24 12:02:54,577 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-24 12:02:55,343 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-24 12:02:55,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27970.0, 'new_value': 32451.0}, {'field': 'total_amount', 'old_value': 27997.9, 'new_value': 32478.9}, {'field': 'order_count', 'old_value': 26, 'new_value': 30}]
2025-05-24 12:02:55,343 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-24 12:02:55,765 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-24 12:02:55,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4968.2}, {'field': 'total_amount', 'old_value': 116176.13, 'new_value': 121144.33}, {'field': 'order_count', 'old_value': 2959, 'new_value': 3088}]
2025-05-24 12:02:55,765 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-24 12:02:56,234 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-24 12:02:56,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28608.0, 'new_value': 30317.0}, {'field': 'total_amount', 'old_value': 28608.0, 'new_value': 30317.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 96}]
2025-05-24 12:02:56,234 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-24 12:02:56,656 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-24 12:02:56,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89906.25, 'new_value': 94584.25}, {'field': 'offline_amount', 'old_value': 362446.5, 'new_value': 367420.5}, {'field': 'total_amount', 'old_value': 452352.75, 'new_value': 462004.75}, {'field': 'order_count', 'old_value': 3242, 'new_value': 3351}]
2025-05-24 12:02:56,656 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-24 12:02:57,171 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-24 12:02:57,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46421.57, 'new_value': 50219.7}, {'field': 'total_amount', 'old_value': 80774.07, 'new_value': 84572.2}, {'field': 'order_count', 'old_value': 5283, 'new_value': 5529}]
2025-05-24 12:02:57,171 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-24 12:02:57,656 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-24 12:02:57,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81382.0, 'new_value': 88435.35}, {'field': 'total_amount', 'old_value': 141236.39, 'new_value': 148289.74}, {'field': 'order_count', 'old_value': 9281, 'new_value': 9773}]
2025-05-24 12:02:57,656 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-24 12:02:58,062 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-24 12:02:58,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1044062.06, 'new_value': 1089658.87}, {'field': 'total_amount', 'old_value': 1044062.06, 'new_value': 1089658.87}, {'field': 'order_count', 'old_value': 3048, 'new_value': 3189}]
2025-05-24 12:02:58,062 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-24 12:02:58,546 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-24 12:02:58,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161508.8, 'new_value': 170244.8}, {'field': 'total_amount', 'old_value': 161508.8, 'new_value': 170244.8}, {'field': 'order_count', 'old_value': 5671, 'new_value': 5973}]
2025-05-24 12:02:58,546 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-24 12:02:59,015 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-24 12:02:59,015 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 13, 'new_value': 17}]
2025-05-24 12:02:59,015 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-24 12:02:59,421 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-24 12:02:59,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 635088.67, 'new_value': 669749.69}, {'field': 'total_amount', 'old_value': 635088.67, 'new_value': 669749.69}, {'field': 'order_count', 'old_value': 3303, 'new_value': 3507}]
2025-05-24 12:02:59,421 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-24 12:02:59,874 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-24 12:02:59,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 950549.52, 'new_value': 991823.52}, {'field': 'total_amount', 'old_value': 950549.52, 'new_value': 991823.52}, {'field': 'order_count', 'old_value': 3352, 'new_value': 3498}]
2025-05-24 12:02:59,874 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-24 12:03:00,359 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-24 12:03:00,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42798.22, 'new_value': 44320.22}, {'field': 'total_amount', 'old_value': 45642.22, 'new_value': 47164.22}, {'field': 'order_count', 'old_value': 333, 'new_value': 343}]
2025-05-24 12:03:00,374 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-24 12:03:00,843 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-24 12:03:00,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272998.99, 'new_value': 283976.71}, {'field': 'total_amount', 'old_value': 272998.99, 'new_value': 283976.71}, {'field': 'order_count', 'old_value': 754, 'new_value': 781}]
2025-05-24 12:03:00,843 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-24 12:03:01,280 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-24 12:03:01,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116915.0, 'new_value': 124035.0}, {'field': 'total_amount', 'old_value': 116995.0, 'new_value': 124115.0}, {'field': 'order_count', 'old_value': 11600, 'new_value': 12300}]
2025-05-24 12:03:01,280 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-24 12:03:01,718 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-24 12:03:01,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59158.0, 'new_value': 62370.0}, {'field': 'total_amount', 'old_value': 74663.0, 'new_value': 77875.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 122}]
2025-05-24 12:03:01,718 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-24 12:03:02,249 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-24 12:03:02,249 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122636.0, 'new_value': 128149.0}, {'field': 'offline_amount', 'old_value': 83136.0, 'new_value': 87959.0}, {'field': 'total_amount', 'old_value': 205772.0, 'new_value': 216108.0}, {'field': 'order_count', 'old_value': 8436, 'new_value': 8884}]
2025-05-24 12:03:02,249 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-24 12:03:02,749 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-24 12:03:02,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103933.0, 'new_value': 109356.0}, {'field': 'total_amount', 'old_value': 103933.0, 'new_value': 109356.0}, {'field': 'order_count', 'old_value': 544, 'new_value': 569}]
2025-05-24 12:03:02,749 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-24 12:03:03,202 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-24 12:03:03,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116559.0, 'new_value': 122772.0}, {'field': 'total_amount', 'old_value': 116559.0, 'new_value': 122772.0}, {'field': 'order_count', 'old_value': 474, 'new_value': 498}]
2025-05-24 12:03:03,202 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-24 12:03:03,718 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-24 12:03:03,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45749.0, 'new_value': 48182.0}, {'field': 'total_amount', 'old_value': 45749.0, 'new_value': 48182.0}, {'field': 'order_count', 'old_value': 892, 'new_value': 940}]
2025-05-24 12:03:03,718 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-24 12:03:04,171 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-24 12:03:04,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176169.0, 'new_value': 186960.0}, {'field': 'total_amount', 'old_value': 176169.0, 'new_value': 186960.0}, {'field': 'order_count', 'old_value': 18566, 'new_value': 19768}]
2025-05-24 12:03:04,171 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-24 12:03:04,655 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-24 12:03:04,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106464.0, 'new_value': 109111.0}, {'field': 'total_amount', 'old_value': 106464.0, 'new_value': 109111.0}, {'field': 'order_count', 'old_value': 1005, 'new_value': 1045}]
2025-05-24 12:03:04,655 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-24 12:03:05,187 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-24 12:03:05,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34187.0, 'new_value': 34585.0}, {'field': 'total_amount', 'old_value': 73552.4, 'new_value': 73950.4}, {'field': 'order_count', 'old_value': 104, 'new_value': 106}]
2025-05-24 12:03:05,187 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-24 12:03:05,624 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-24 12:03:05,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43841.36, 'new_value': 45422.04}, {'field': 'total_amount', 'old_value': 43841.36, 'new_value': 45422.04}, {'field': 'order_count', 'old_value': 744, 'new_value': 778}]
2025-05-24 12:03:05,640 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-24 12:03:06,124 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-24 12:03:06,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114139.09, 'new_value': 118970.79}, {'field': 'offline_amount', 'old_value': 630673.71, 'new_value': 654421.82}, {'field': 'total_amount', 'old_value': 744812.8, 'new_value': 773392.61}, {'field': 'order_count', 'old_value': 1671, 'new_value': 1752}]
2025-05-24 12:03:06,124 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-24 12:03:06,609 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-24 12:03:06,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69102.6, 'new_value': 70770.6}, {'field': 'total_amount', 'old_value': 69102.6, 'new_value': 70770.6}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-24 12:03:06,609 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-24 12:03:07,155 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-24 12:03:07,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99769.0, 'new_value': 106277.0}, {'field': 'total_amount', 'old_value': 99769.0, 'new_value': 106277.0}, {'field': 'order_count', 'old_value': 331, 'new_value': 348}]
2025-05-24 12:03:07,155 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-24 12:03:07,671 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-24 12:03:07,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9600.0, 'new_value': 9900.0}, {'field': 'total_amount', 'old_value': 11538.95, 'new_value': 11838.95}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-24 12:03:07,671 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-24 12:03:08,124 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-24 12:03:08,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15423514.48, 'new_value': 16088662.34}, {'field': 'total_amount', 'old_value': 15423514.48, 'new_value': 16088662.34}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-24 12:03:08,124 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-24 12:03:08,593 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-24 12:03:08,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42922.04, 'new_value': 48045.73}, {'field': 'total_amount', 'old_value': 42922.04, 'new_value': 48045.73}, {'field': 'order_count', 'old_value': 2336, 'new_value': 2756}]
2025-05-24 12:03:08,593 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-24 12:03:09,030 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-24 12:03:09,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26035.99, 'new_value': 30510.96}, {'field': 'offline_amount', 'old_value': 49019.33, 'new_value': 50734.33}, {'field': 'total_amount', 'old_value': 75055.32, 'new_value': 81245.29}, {'field': 'order_count', 'old_value': 339, 'new_value': 370}]
2025-05-24 12:03:09,030 - INFO - 开始批量插入 1 条新记录
2025-05-24 12:03:09,171 - INFO - 批量插入响应状态码: 200
2025-05-24 12:03:09,171 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:03:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7153EC06-9829-7333-B92F-DB43625F86A3', 'x-acs-trace-id': 'e6f8bb7bc6ac159933bdf129336ed6e3', 'etag': '6iWir3XfV2K/BHfPqTIrR4Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:03:09,171 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44']}
2025-05-24 12:03:09,171 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-24 12:03:09,171 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44']
2025-05-24 12:03:12,187 - INFO - 批量插入完成，共 1 条记录
2025-05-24 12:03:12,187 - INFO - 日期 2025-05 处理完成 - 更新: 337 条，插入: 1 条，错误: 0 条
2025-05-24 12:03:12,187 - INFO - 数据同步完成！更新: 337 条，插入: 1 条，错误: 0 条
2025-05-24 12:03:12,187 - INFO - =================同步完成====================
2025-05-24 15:00:01,915 - INFO - =================使用默认全量同步=============
2025-05-24 15:00:03,399 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-24 15:00:03,399 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 15:00:03,430 - INFO - 开始处理日期: 2025-01
2025-05-24 15:00:03,430 - INFO - Request Parameters - Page 1:
2025-05-24 15:00:03,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:03,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:04,821 - INFO - Response - Page 1:
2025-05-24 15:00:05,024 - INFO - 第 1 页获取到 100 条记录
2025-05-24 15:00:05,024 - INFO - Request Parameters - Page 2:
2025-05-24 15:00:05,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:05,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:05,540 - INFO - Response - Page 2:
2025-05-24 15:00:05,743 - INFO - 第 2 页获取到 100 条记录
2025-05-24 15:00:05,743 - INFO - Request Parameters - Page 3:
2025-05-24 15:00:05,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:05,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:06,243 - INFO - Response - Page 3:
2025-05-24 15:00:06,446 - INFO - 第 3 页获取到 100 条记录
2025-05-24 15:00:06,446 - INFO - Request Parameters - Page 4:
2025-05-24 15:00:06,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:06,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:07,087 - INFO - Response - Page 4:
2025-05-24 15:00:07,290 - INFO - 第 4 页获取到 100 条记录
2025-05-24 15:00:07,290 - INFO - Request Parameters - Page 5:
2025-05-24 15:00:07,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:07,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:07,759 - INFO - Response - Page 5:
2025-05-24 15:00:07,962 - INFO - 第 5 页获取到 100 条记录
2025-05-24 15:00:07,962 - INFO - Request Parameters - Page 6:
2025-05-24 15:00:07,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:07,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:08,493 - INFO - Response - Page 6:
2025-05-24 15:00:08,696 - INFO - 第 6 页获取到 100 条记录
2025-05-24 15:00:08,696 - INFO - Request Parameters - Page 7:
2025-05-24 15:00:08,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:08,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:09,165 - INFO - Response - Page 7:
2025-05-24 15:00:09,368 - INFO - 第 7 页获取到 82 条记录
2025-05-24 15:00:09,368 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 15:00:09,368 - INFO - 获取到 682 条表单数据
2025-05-24 15:00:09,368 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 15:00:09,384 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 15:00:09,384 - INFO - 开始处理日期: 2025-02
2025-05-24 15:00:09,384 - INFO - Request Parameters - Page 1:
2025-05-24 15:00:09,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:09,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:09,884 - INFO - Response - Page 1:
2025-05-24 15:00:10,087 - INFO - 第 1 页获取到 100 条记录
2025-05-24 15:00:10,087 - INFO - Request Parameters - Page 2:
2025-05-24 15:00:10,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:10,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:10,665 - INFO - Response - Page 2:
2025-05-24 15:00:10,868 - INFO - 第 2 页获取到 100 条记录
2025-05-24 15:00:10,868 - INFO - Request Parameters - Page 3:
2025-05-24 15:00:10,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:10,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:11,337 - INFO - Response - Page 3:
2025-05-24 15:00:11,540 - INFO - 第 3 页获取到 100 条记录
2025-05-24 15:00:11,540 - INFO - Request Parameters - Page 4:
2025-05-24 15:00:11,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:11,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:12,024 - INFO - Response - Page 4:
2025-05-24 15:00:12,227 - INFO - 第 4 页获取到 100 条记录
2025-05-24 15:00:12,227 - INFO - Request Parameters - Page 5:
2025-05-24 15:00:12,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:12,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:12,727 - INFO - Response - Page 5:
2025-05-24 15:00:12,930 - INFO - 第 5 页获取到 100 条记录
2025-05-24 15:00:12,930 - INFO - Request Parameters - Page 6:
2025-05-24 15:00:12,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:12,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:13,446 - INFO - Response - Page 6:
2025-05-24 15:00:13,649 - INFO - 第 6 页获取到 100 条记录
2025-05-24 15:00:13,649 - INFO - Request Parameters - Page 7:
2025-05-24 15:00:13,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:13,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:14,133 - INFO - Response - Page 7:
2025-05-24 15:00:14,337 - INFO - 第 7 页获取到 70 条记录
2025-05-24 15:00:14,337 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 15:00:14,337 - INFO - 获取到 670 条表单数据
2025-05-24 15:00:14,337 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 15:00:14,352 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 15:00:14,352 - INFO - 开始处理日期: 2025-03
2025-05-24 15:00:14,352 - INFO - Request Parameters - Page 1:
2025-05-24 15:00:14,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:14,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:15,008 - INFO - Response - Page 1:
2025-05-24 15:00:15,212 - INFO - 第 1 页获取到 100 条记录
2025-05-24 15:00:15,212 - INFO - Request Parameters - Page 2:
2025-05-24 15:00:15,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:15,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:15,852 - INFO - Response - Page 2:
2025-05-24 15:00:16,055 - INFO - 第 2 页获取到 100 条记录
2025-05-24 15:00:16,055 - INFO - Request Parameters - Page 3:
2025-05-24 15:00:16,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:16,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:16,587 - INFO - Response - Page 3:
2025-05-24 15:00:16,790 - INFO - 第 3 页获取到 100 条记录
2025-05-24 15:00:16,790 - INFO - Request Parameters - Page 4:
2025-05-24 15:00:16,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:16,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:17,477 - INFO - Response - Page 4:
2025-05-24 15:00:17,680 - INFO - 第 4 页获取到 100 条记录
2025-05-24 15:00:17,680 - INFO - Request Parameters - Page 5:
2025-05-24 15:00:17,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:17,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:18,133 - INFO - Response - Page 5:
2025-05-24 15:00:18,337 - INFO - 第 5 页获取到 100 条记录
2025-05-24 15:00:18,337 - INFO - Request Parameters - Page 6:
2025-05-24 15:00:18,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:18,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:18,899 - INFO - Response - Page 6:
2025-05-24 15:00:19,102 - INFO - 第 6 页获取到 100 条记录
2025-05-24 15:00:19,102 - INFO - Request Parameters - Page 7:
2025-05-24 15:00:19,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:19,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:19,571 - INFO - Response - Page 7:
2025-05-24 15:00:19,774 - INFO - 第 7 页获取到 61 条记录
2025-05-24 15:00:19,774 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 15:00:19,774 - INFO - 获取到 661 条表单数据
2025-05-24 15:00:19,774 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 15:00:19,790 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 15:00:19,790 - INFO - 开始处理日期: 2025-04
2025-05-24 15:00:19,790 - INFO - Request Parameters - Page 1:
2025-05-24 15:00:19,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:19,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:20,352 - INFO - Response - Page 1:
2025-05-24 15:00:20,555 - INFO - 第 1 页获取到 100 条记录
2025-05-24 15:00:20,555 - INFO - Request Parameters - Page 2:
2025-05-24 15:00:20,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:20,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:21,133 - INFO - Response - Page 2:
2025-05-24 15:00:21,337 - INFO - 第 2 页获取到 100 条记录
2025-05-24 15:00:21,337 - INFO - Request Parameters - Page 3:
2025-05-24 15:00:21,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:21,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:21,805 - INFO - Response - Page 3:
2025-05-24 15:00:22,008 - INFO - 第 3 页获取到 100 条记录
2025-05-24 15:00:22,008 - INFO - Request Parameters - Page 4:
2025-05-24 15:00:22,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:22,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:22,508 - INFO - Response - Page 4:
2025-05-24 15:00:22,712 - INFO - 第 4 页获取到 100 条记录
2025-05-24 15:00:22,712 - INFO - Request Parameters - Page 5:
2025-05-24 15:00:22,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:22,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:23,212 - INFO - Response - Page 5:
2025-05-24 15:00:23,415 - INFO - 第 5 页获取到 100 条记录
2025-05-24 15:00:23,415 - INFO - Request Parameters - Page 6:
2025-05-24 15:00:23,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:23,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:23,962 - INFO - Response - Page 6:
2025-05-24 15:00:24,165 - INFO - 第 6 页获取到 100 条记录
2025-05-24 15:00:24,165 - INFO - Request Parameters - Page 7:
2025-05-24 15:00:24,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:24,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:24,524 - INFO - Response - Page 7:
2025-05-24 15:00:24,727 - INFO - 第 7 页获取到 56 条记录
2025-05-24 15:00:24,727 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 15:00:24,727 - INFO - 获取到 656 条表单数据
2025-05-24 15:00:24,727 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 15:00:24,743 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 15:00:24,743 - INFO - 开始处理日期: 2025-05
2025-05-24 15:00:24,743 - INFO - Request Parameters - Page 1:
2025-05-24 15:00:24,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:24,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:25,274 - INFO - Response - Page 1:
2025-05-24 15:00:25,477 - INFO - 第 1 页获取到 100 条记录
2025-05-24 15:00:25,477 - INFO - Request Parameters - Page 2:
2025-05-24 15:00:25,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:25,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:25,993 - INFO - Response - Page 2:
2025-05-24 15:00:26,196 - INFO - 第 2 页获取到 100 条记录
2025-05-24 15:00:26,196 - INFO - Request Parameters - Page 3:
2025-05-24 15:00:26,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:26,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:26,727 - INFO - Response - Page 3:
2025-05-24 15:00:26,930 - INFO - 第 3 页获取到 100 条记录
2025-05-24 15:00:26,930 - INFO - Request Parameters - Page 4:
2025-05-24 15:00:26,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:26,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:27,415 - INFO - Response - Page 4:
2025-05-24 15:00:27,618 - INFO - 第 4 页获取到 100 条记录
2025-05-24 15:00:27,618 - INFO - Request Parameters - Page 5:
2025-05-24 15:00:27,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:27,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:28,165 - INFO - Response - Page 5:
2025-05-24 15:00:28,368 - INFO - 第 5 页获取到 100 条记录
2025-05-24 15:00:28,368 - INFO - Request Parameters - Page 6:
2025-05-24 15:00:28,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:28,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:28,805 - INFO - Response - Page 6:
2025-05-24 15:00:29,008 - INFO - 第 6 页获取到 100 条记录
2025-05-24 15:00:29,008 - INFO - Request Parameters - Page 7:
2025-05-24 15:00:29,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:00:29,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:00:29,415 - INFO - Response - Page 7:
2025-05-24 15:00:29,618 - INFO - 第 7 页获取到 29 条记录
2025-05-24 15:00:29,618 - INFO - 查询完成，共获取到 629 条记录
2025-05-24 15:00:29,618 - INFO - 获取到 629 条表单数据
2025-05-24 15:00:29,618 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-24 15:00:29,618 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-24 15:00:30,024 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-24 15:00:30,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165047.3, 'new_value': 173990.27}, {'field': 'total_amount', 'old_value': 165047.3, 'new_value': 173990.27}, {'field': 'order_count', 'old_value': 6282, 'new_value': 6559}]
2025-05-24 15:00:30,024 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-24 15:00:30,446 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-24 15:00:30,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 366612.0, 'new_value': 380208.0}, {'field': 'offline_amount', 'old_value': 296720.0, 'new_value': 304944.0}, {'field': 'total_amount', 'old_value': 663332.0, 'new_value': 685152.0}, {'field': 'order_count', 'old_value': 698, 'new_value': 728}]
2025-05-24 15:00:30,446 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-24 15:00:30,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-24 15:00:30,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76653.02, 'new_value': 80684.02}, {'field': 'offline_amount', 'old_value': 786382.98, 'new_value': 833271.66}, {'field': 'total_amount', 'old_value': 863036.0, 'new_value': 913955.68}, {'field': 'order_count', 'old_value': 2772, 'new_value': 2906}]
2025-05-24 15:00:30,930 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-24 15:00:31,383 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-24 15:00:31,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26273.0, 'new_value': 30473.0}, {'field': 'total_amount', 'old_value': 26273.0, 'new_value': 30473.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-05-24 15:00:31,383 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-24 15:00:31,883 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-24 15:00:31,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98966.05, 'new_value': 101694.55}, {'field': 'total_amount', 'old_value': 98966.05, 'new_value': 101694.55}, {'field': 'order_count', 'old_value': 678, 'new_value': 707}]
2025-05-24 15:00:31,883 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-24 15:00:32,321 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-24 15:00:32,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 905247.94, 'new_value': 908647.94}, {'field': 'total_amount', 'old_value': 905247.94, 'new_value': 908647.94}, {'field': 'order_count', 'old_value': 548, 'new_value': 550}]
2025-05-24 15:00:32,321 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-24 15:00:32,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-24 15:00:32,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42110.0, 'new_value': 45760.0}, {'field': 'total_amount', 'old_value': 42110.0, 'new_value': 45760.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-24 15:00:32,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-24 15:00:33,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-24 15:00:33,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5533450.0, 'new_value': 5786365.0}, {'field': 'total_amount', 'old_value': 5533450.0, 'new_value': 5786365.0}, {'field': 'order_count', 'old_value': 94162, 'new_value': 98315}]
2025-05-24 15:00:33,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-24 15:00:33,696 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-24 15:00:33,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54556.0, 'new_value': 57318.0}, {'field': 'total_amount', 'old_value': 54556.0, 'new_value': 57318.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-24 15:00:33,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-24 15:00:34,149 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-24 15:00:34,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27151.0, 'new_value': 27678.0}, {'field': 'total_amount', 'old_value': 27151.0, 'new_value': 27678.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 268}]
2025-05-24 15:00:34,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-24 15:00:34,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-24 15:00:34,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15410.5, 'new_value': 17240.4}, {'field': 'offline_amount', 'old_value': 41974.9, 'new_value': 42650.9}, {'field': 'total_amount', 'old_value': 57385.4, 'new_value': 59891.3}, {'field': 'order_count', 'old_value': 165, 'new_value': 175}]
2025-05-24 15:00:34,602 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-24 15:00:35,024 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-24 15:00:35,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86772.0, 'new_value': 91876.0}, {'field': 'total_amount', 'old_value': 86772.0, 'new_value': 91876.0}, {'field': 'order_count', 'old_value': 596, 'new_value': 623}]
2025-05-24 15:00:35,024 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-24 15:00:35,415 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-24 15:00:35,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21846.0, 'new_value': 22966.0}, {'field': 'total_amount', 'old_value': 21846.0, 'new_value': 22966.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-24 15:00:35,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-24 15:00:35,836 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-24 15:00:35,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20416.0, 'new_value': 22515.0}, {'field': 'total_amount', 'old_value': 20416.0, 'new_value': 22515.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-24 15:00:35,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-24 15:00:36,258 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-24 15:00:36,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 740300.0, 'new_value': 823900.0}, {'field': 'total_amount', 'old_value': 740300.0, 'new_value': 823900.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 61}]
2025-05-24 15:00:36,258 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-24 15:00:36,727 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-24 15:00:36,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13019.45, 'new_value': 13536.45}, {'field': 'offline_amount', 'old_value': 243706.0, 'new_value': 258487.0}, {'field': 'total_amount', 'old_value': 256725.45, 'new_value': 272023.45}, {'field': 'order_count', 'old_value': 1373, 'new_value': 1450}]
2025-05-24 15:00:36,743 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-24 15:00:37,290 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-24 15:00:37,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11498800.0, 'new_value': 11794373.0}, {'field': 'total_amount', 'old_value': 11498800.0, 'new_value': 11794373.0}, {'field': 'order_count', 'old_value': 35572, 'new_value': 36664}]
2025-05-24 15:00:37,290 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-24 15:00:37,696 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-24 15:00:37,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3321938.45, 'new_value': 3462651.59}, {'field': 'total_amount', 'old_value': 3321938.45, 'new_value': 3462651.59}, {'field': 'order_count', 'old_value': 5677, 'new_value': 5918}]
2025-05-24 15:00:37,696 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-24 15:00:38,305 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-24 15:00:38,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142897.42, 'new_value': 150479.97}, {'field': 'total_amount', 'old_value': 150337.06, 'new_value': 157919.61}, {'field': 'order_count', 'old_value': 10505, 'new_value': 11076}]
2025-05-24 15:00:38,305 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-24 15:00:38,727 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-24 15:00:38,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296572.0, 'new_value': 320664.0}, {'field': 'total_amount', 'old_value': 296572.0, 'new_value': 320664.0}]
2025-05-24 15:00:38,727 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-24 15:00:39,243 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-24 15:00:39,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 436386.0, 'new_value': 441866.0}, {'field': 'total_amount', 'old_value': 436386.0, 'new_value': 441866.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-05-24 15:00:39,243 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-24 15:00:39,665 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-24 15:00:39,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1.0, 'new_value': 2.0}, {'field': 'offline_amount', 'old_value': 96480.0, 'new_value': 99280.0}, {'field': 'total_amount', 'old_value': 96481.0, 'new_value': 99282.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-24 15:00:39,665 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-24 15:00:40,086 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-24 15:00:40,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160339.79, 'new_value': 167675.5}, {'field': 'total_amount', 'old_value': 160339.79, 'new_value': 167675.5}, {'field': 'order_count', 'old_value': 16792, 'new_value': 17580}]
2025-05-24 15:00:40,086 - INFO - 日期 2025-05 处理完成 - 更新: 23 条，插入: 0 条，错误: 0 条
2025-05-24 15:00:40,086 - INFO - 数据同步完成！更新: 23 条，插入: 0 条，错误: 0 条
2025-05-24 15:00:40,086 - INFO - =================同步完成====================
2025-05-24 18:00:01,861 - INFO - =================使用默认全量同步=============
2025-05-24 18:00:03,282 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-24 18:00:03,282 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 18:00:03,314 - INFO - 开始处理日期: 2025-01
2025-05-24 18:00:03,314 - INFO - Request Parameters - Page 1:
2025-05-24 18:00:03,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:03,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:04,267 - INFO - Response - Page 1:
2025-05-24 18:00:04,470 - INFO - 第 1 页获取到 100 条记录
2025-05-24 18:00:04,470 - INFO - Request Parameters - Page 2:
2025-05-24 18:00:04,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:04,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:05,439 - INFO - Response - Page 2:
2025-05-24 18:00:05,642 - INFO - 第 2 页获取到 100 条记录
2025-05-24 18:00:05,642 - INFO - Request Parameters - Page 3:
2025-05-24 18:00:05,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:05,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:06,157 - INFO - Response - Page 3:
2025-05-24 18:00:06,361 - INFO - 第 3 页获取到 100 条记录
2025-05-24 18:00:06,361 - INFO - Request Parameters - Page 4:
2025-05-24 18:00:06,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:06,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:06,876 - INFO - Response - Page 4:
2025-05-24 18:00:07,079 - INFO - 第 4 页获取到 100 条记录
2025-05-24 18:00:07,079 - INFO - Request Parameters - Page 5:
2025-05-24 18:00:07,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:07,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:07,626 - INFO - Response - Page 5:
2025-05-24 18:00:07,829 - INFO - 第 5 页获取到 100 条记录
2025-05-24 18:00:07,829 - INFO - Request Parameters - Page 6:
2025-05-24 18:00:07,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:07,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:08,470 - INFO - Response - Page 6:
2025-05-24 18:00:08,673 - INFO - 第 6 页获取到 100 条记录
2025-05-24 18:00:08,673 - INFO - Request Parameters - Page 7:
2025-05-24 18:00:08,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:08,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:09,142 - INFO - Response - Page 7:
2025-05-24 18:00:09,345 - INFO - 第 7 页获取到 82 条记录
2025-05-24 18:00:09,345 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 18:00:09,345 - INFO - 获取到 682 条表单数据
2025-05-24 18:00:09,361 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 18:00:09,376 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 18:00:09,376 - INFO - 开始处理日期: 2025-02
2025-05-24 18:00:09,376 - INFO - Request Parameters - Page 1:
2025-05-24 18:00:09,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:09,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:09,845 - INFO - Response - Page 1:
2025-05-24 18:00:10,064 - INFO - 第 1 页获取到 100 条记录
2025-05-24 18:00:10,064 - INFO - Request Parameters - Page 2:
2025-05-24 18:00:10,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:10,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:10,595 - INFO - Response - Page 2:
2025-05-24 18:00:10,798 - INFO - 第 2 页获取到 100 条记录
2025-05-24 18:00:10,798 - INFO - Request Parameters - Page 3:
2025-05-24 18:00:10,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:10,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:11,361 - INFO - Response - Page 3:
2025-05-24 18:00:11,564 - INFO - 第 3 页获取到 100 条记录
2025-05-24 18:00:11,564 - INFO - Request Parameters - Page 4:
2025-05-24 18:00:11,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:11,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:12,111 - INFO - Response - Page 4:
2025-05-24 18:00:12,314 - INFO - 第 4 页获取到 100 条记录
2025-05-24 18:00:12,314 - INFO - Request Parameters - Page 5:
2025-05-24 18:00:12,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:12,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:12,767 - INFO - Response - Page 5:
2025-05-24 18:00:12,970 - INFO - 第 5 页获取到 100 条记录
2025-05-24 18:00:12,970 - INFO - Request Parameters - Page 6:
2025-05-24 18:00:12,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:12,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:13,454 - INFO - Response - Page 6:
2025-05-24 18:00:13,657 - INFO - 第 6 页获取到 100 条记录
2025-05-24 18:00:13,657 - INFO - Request Parameters - Page 7:
2025-05-24 18:00:13,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:13,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:14,220 - INFO - Response - Page 7:
2025-05-24 18:00:14,423 - INFO - 第 7 页获取到 70 条记录
2025-05-24 18:00:14,423 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 18:00:14,423 - INFO - 获取到 670 条表单数据
2025-05-24 18:00:14,423 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 18:00:14,439 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 18:00:14,439 - INFO - 开始处理日期: 2025-03
2025-05-24 18:00:14,439 - INFO - Request Parameters - Page 1:
2025-05-24 18:00:14,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:14,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:15,017 - INFO - Response - Page 1:
2025-05-24 18:00:15,220 - INFO - 第 1 页获取到 100 条记录
2025-05-24 18:00:15,220 - INFO - Request Parameters - Page 2:
2025-05-24 18:00:15,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:15,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:15,704 - INFO - Response - Page 2:
2025-05-24 18:00:15,907 - INFO - 第 2 页获取到 100 条记录
2025-05-24 18:00:15,907 - INFO - Request Parameters - Page 3:
2025-05-24 18:00:15,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:15,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:16,361 - INFO - Response - Page 3:
2025-05-24 18:00:16,564 - INFO - 第 3 页获取到 100 条记录
2025-05-24 18:00:16,564 - INFO - Request Parameters - Page 4:
2025-05-24 18:00:16,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:16,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:17,079 - INFO - Response - Page 4:
2025-05-24 18:00:17,282 - INFO - 第 4 页获取到 100 条记录
2025-05-24 18:00:17,282 - INFO - Request Parameters - Page 5:
2025-05-24 18:00:17,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:17,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:17,735 - INFO - Response - Page 5:
2025-05-24 18:00:17,939 - INFO - 第 5 页获取到 100 条记录
2025-05-24 18:00:17,939 - INFO - Request Parameters - Page 6:
2025-05-24 18:00:17,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:17,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:18,485 - INFO - Response - Page 6:
2025-05-24 18:00:18,689 - INFO - 第 6 页获取到 100 条记录
2025-05-24 18:00:18,689 - INFO - Request Parameters - Page 7:
2025-05-24 18:00:18,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:18,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:19,204 - INFO - Response - Page 7:
2025-05-24 18:00:19,407 - INFO - 第 7 页获取到 61 条记录
2025-05-24 18:00:19,407 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 18:00:19,407 - INFO - 获取到 661 条表单数据
2025-05-24 18:00:19,407 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 18:00:19,423 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 18:00:19,423 - INFO - 开始处理日期: 2025-04
2025-05-24 18:00:19,423 - INFO - Request Parameters - Page 1:
2025-05-24 18:00:19,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:19,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:19,939 - INFO - Response - Page 1:
2025-05-24 18:00:20,142 - INFO - 第 1 页获取到 100 条记录
2025-05-24 18:00:20,142 - INFO - Request Parameters - Page 2:
2025-05-24 18:00:20,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:20,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:20,595 - INFO - Response - Page 2:
2025-05-24 18:00:20,798 - INFO - 第 2 页获取到 100 条记录
2025-05-24 18:00:20,798 - INFO - Request Parameters - Page 3:
2025-05-24 18:00:20,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:20,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:21,360 - INFO - Response - Page 3:
2025-05-24 18:00:21,564 - INFO - 第 3 页获取到 100 条记录
2025-05-24 18:00:21,564 - INFO - Request Parameters - Page 4:
2025-05-24 18:00:21,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:21,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:22,064 - INFO - Response - Page 4:
2025-05-24 18:00:22,267 - INFO - 第 4 页获取到 100 条记录
2025-05-24 18:00:22,267 - INFO - Request Parameters - Page 5:
2025-05-24 18:00:22,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:22,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:22,829 - INFO - Response - Page 5:
2025-05-24 18:00:23,032 - INFO - 第 5 页获取到 100 条记录
2025-05-24 18:00:23,032 - INFO - Request Parameters - Page 6:
2025-05-24 18:00:23,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:23,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:23,564 - INFO - Response - Page 6:
2025-05-24 18:00:23,767 - INFO - 第 6 页获取到 100 条记录
2025-05-24 18:00:23,767 - INFO - Request Parameters - Page 7:
2025-05-24 18:00:23,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:23,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:24,267 - INFO - Response - Page 7:
2025-05-24 18:00:24,470 - INFO - 第 7 页获取到 56 条记录
2025-05-24 18:00:24,470 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 18:00:24,470 - INFO - 获取到 656 条表单数据
2025-05-24 18:00:24,470 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 18:00:24,485 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 18:00:24,485 - INFO - 开始处理日期: 2025-05
2025-05-24 18:00:24,485 - INFO - Request Parameters - Page 1:
2025-05-24 18:00:24,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:24,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:24,970 - INFO - Response - Page 1:
2025-05-24 18:00:25,173 - INFO - 第 1 页获取到 100 条记录
2025-05-24 18:00:25,173 - INFO - Request Parameters - Page 2:
2025-05-24 18:00:25,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:25,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:25,657 - INFO - Response - Page 2:
2025-05-24 18:00:25,860 - INFO - 第 2 页获取到 100 条记录
2025-05-24 18:00:25,860 - INFO - Request Parameters - Page 3:
2025-05-24 18:00:25,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:25,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:26,407 - INFO - Response - Page 3:
2025-05-24 18:00:26,610 - INFO - 第 3 页获取到 100 条记录
2025-05-24 18:00:26,610 - INFO - Request Parameters - Page 4:
2025-05-24 18:00:26,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:26,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:27,064 - INFO - Response - Page 4:
2025-05-24 18:00:27,267 - INFO - 第 4 页获取到 100 条记录
2025-05-24 18:00:27,267 - INFO - Request Parameters - Page 5:
2025-05-24 18:00:27,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:27,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:27,720 - INFO - Response - Page 5:
2025-05-24 18:00:27,923 - INFO - 第 5 页获取到 100 条记录
2025-05-24 18:00:27,923 - INFO - Request Parameters - Page 6:
2025-05-24 18:00:27,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:27,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:28,392 - INFO - Response - Page 6:
2025-05-24 18:00:28,595 - INFO - 第 6 页获取到 100 条记录
2025-05-24 18:00:28,595 - INFO - Request Parameters - Page 7:
2025-05-24 18:00:28,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:00:28,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:00:29,095 - INFO - Response - Page 7:
2025-05-24 18:00:29,298 - INFO - 第 7 页获取到 29 条记录
2025-05-24 18:00:29,298 - INFO - 查询完成，共获取到 629 条记录
2025-05-24 18:00:29,298 - INFO - 获取到 629 条表单数据
2025-05-24 18:00:29,298 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-24 18:00:29,314 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-24 18:00:29,782 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-24 18:00:29,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1106000.0, 'new_value': 1166000.0}, {'field': 'total_amount', 'old_value': 1106000.0, 'new_value': 1166000.0}, {'field': 'order_count', 'old_value': 341, 'new_value': 342}]
2025-05-24 18:00:29,782 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-24 18:00:30,204 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-24 18:00:30,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80693.0, 'new_value': 85493.0}, {'field': 'total_amount', 'old_value': 80693.0, 'new_value': 85493.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-24 18:00:30,204 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-24 18:00:30,626 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-24 18:00:30,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34585.8, 'new_value': 37405.8}, {'field': 'total_amount', 'old_value': 34585.8, 'new_value': 37405.8}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-24 18:00:30,626 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-24 18:00:31,048 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-24 18:00:31,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123919.08, 'new_value': 130000.84}, {'field': 'total_amount', 'old_value': 123919.08, 'new_value': 130000.84}, {'field': 'order_count', 'old_value': 11212, 'new_value': 11805}]
2025-05-24 18:00:31,048 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-24 18:00:31,548 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-24 18:00:31,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102937.1, 'new_value': 104749.1}, {'field': 'total_amount', 'old_value': 102937.1, 'new_value': 104749.1}, {'field': 'order_count', 'old_value': 1030, 'new_value': 1045}]
2025-05-24 18:00:31,548 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-24 18:00:32,095 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-24 18:00:32,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102115.0, 'new_value': 107329.0}, {'field': 'total_amount', 'old_value': 102115.0, 'new_value': 107329.0}, {'field': 'order_count', 'old_value': 3831, 'new_value': 3966}]
2025-05-24 18:00:32,095 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-24 18:00:32,579 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-24 18:00:32,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26891.07, 'new_value': 28041.07}, {'field': 'total_amount', 'old_value': 26891.07, 'new_value': 28041.07}, {'field': 'order_count', 'old_value': 2619, 'new_value': 2721}]
2025-05-24 18:00:32,579 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-24 18:00:33,001 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-24 18:00:33,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47640.0, 'new_value': 52640.0}, {'field': 'total_amount', 'old_value': 47640.0, 'new_value': 52640.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-24 18:00:33,001 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-24 18:00:33,423 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-24 18:00:33,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5213400.0, 'new_value': 5288200.0}, {'field': 'total_amount', 'old_value': 5213400.0, 'new_value': 5288200.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 66}]
2025-05-24 18:00:33,423 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-24 18:00:33,923 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-24 18:00:33,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368793.75, 'new_value': 381697.64}, {'field': 'total_amount', 'old_value': 368793.75, 'new_value': 381697.64}, {'field': 'order_count', 'old_value': 695, 'new_value': 729}]
2025-05-24 18:00:33,923 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-24 18:00:34,329 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-24 18:00:34,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65586.0, 'new_value': 80512.0}, {'field': 'total_amount', 'old_value': 65586.0, 'new_value': 80512.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-05-24 18:00:34,329 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-24 18:00:34,907 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-24 18:00:34,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28408.0, 'new_value': 29749.0}, {'field': 'total_amount', 'old_value': 29784.0, 'new_value': 31125.0}, {'field': 'order_count', 'old_value': 3073, 'new_value': 3198}]
2025-05-24 18:00:34,923 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-24 18:00:35,329 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-24 18:00:35,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77534.0, 'new_value': 83414.0}, {'field': 'total_amount', 'old_value': 77534.0, 'new_value': 83414.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-24 18:00:35,329 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-24 18:00:35,751 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-24 18:00:35,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82350.0, 'new_value': 84252.0}, {'field': 'total_amount', 'old_value': 97554.0, 'new_value': 99456.0}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2269}]
2025-05-24 18:00:35,767 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-24 18:00:36,173 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-24 18:00:36,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13775.0, 'new_value': 27090.0}, {'field': 'total_amount', 'old_value': 303291.0, 'new_value': 316606.0}, {'field': 'order_count', 'old_value': 6558, 'new_value': 6683}]
2025-05-24 18:00:36,189 - INFO - 日期 2025-05 处理完成 - 更新: 15 条，插入: 0 条，错误: 0 条
2025-05-24 18:00:36,189 - INFO - 数据同步完成！更新: 15 条，插入: 0 条，错误: 0 条
2025-05-24 18:00:36,189 - INFO - =================同步完成====================
2025-05-24 21:00:01,644 - INFO - =================使用默认全量同步=============
2025-05-24 21:00:03,097 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-24 21:00:03,097 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-24 21:00:03,128 - INFO - 开始处理日期: 2025-01
2025-05-24 21:00:03,128 - INFO - Request Parameters - Page 1:
2025-05-24 21:00:03,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:03,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:04,284 - INFO - Response - Page 1:
2025-05-24 21:00:04,487 - INFO - 第 1 页获取到 100 条记录
2025-05-24 21:00:04,487 - INFO - Request Parameters - Page 2:
2025-05-24 21:00:04,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:04,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:05,065 - INFO - Response - Page 2:
2025-05-24 21:00:05,268 - INFO - 第 2 页获取到 100 条记录
2025-05-24 21:00:05,268 - INFO - Request Parameters - Page 3:
2025-05-24 21:00:05,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:05,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:05,784 - INFO - Response - Page 3:
2025-05-24 21:00:05,987 - INFO - 第 3 页获取到 100 条记录
2025-05-24 21:00:05,987 - INFO - Request Parameters - Page 4:
2025-05-24 21:00:05,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:05,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:06,409 - INFO - Response - Page 4:
2025-05-24 21:00:06,612 - INFO - 第 4 页获取到 100 条记录
2025-05-24 21:00:06,612 - INFO - Request Parameters - Page 5:
2025-05-24 21:00:06,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:06,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:07,096 - INFO - Response - Page 5:
2025-05-24 21:00:07,299 - INFO - 第 5 页获取到 100 条记录
2025-05-24 21:00:07,299 - INFO - Request Parameters - Page 6:
2025-05-24 21:00:07,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:07,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:07,830 - INFO - Response - Page 6:
2025-05-24 21:00:08,033 - INFO - 第 6 页获取到 100 条记录
2025-05-24 21:00:08,033 - INFO - Request Parameters - Page 7:
2025-05-24 21:00:08,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:08,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:08,486 - INFO - Response - Page 7:
2025-05-24 21:00:08,689 - INFO - 第 7 页获取到 82 条记录
2025-05-24 21:00:08,689 - INFO - 查询完成，共获取到 682 条记录
2025-05-24 21:00:08,689 - INFO - 获取到 682 条表单数据
2025-05-24 21:00:08,689 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-24 21:00:08,705 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 21:00:08,705 - INFO - 开始处理日期: 2025-02
2025-05-24 21:00:08,705 - INFO - Request Parameters - Page 1:
2025-05-24 21:00:08,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:08,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:09,346 - INFO - Response - Page 1:
2025-05-24 21:00:09,549 - INFO - 第 1 页获取到 100 条记录
2025-05-24 21:00:09,549 - INFO - Request Parameters - Page 2:
2025-05-24 21:00:09,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:09,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:10,127 - INFO - Response - Page 2:
2025-05-24 21:00:10,330 - INFO - 第 2 页获取到 100 条记录
2025-05-24 21:00:10,330 - INFO - Request Parameters - Page 3:
2025-05-24 21:00:10,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:10,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:10,830 - INFO - Response - Page 3:
2025-05-24 21:00:11,033 - INFO - 第 3 页获取到 100 条记录
2025-05-24 21:00:11,033 - INFO - Request Parameters - Page 4:
2025-05-24 21:00:11,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:11,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:11,533 - INFO - Response - Page 4:
2025-05-24 21:00:11,736 - INFO - 第 4 页获取到 100 条记录
2025-05-24 21:00:11,736 - INFO - Request Parameters - Page 5:
2025-05-24 21:00:11,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:11,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:12,189 - INFO - Response - Page 5:
2025-05-24 21:00:12,392 - INFO - 第 5 页获取到 100 条记录
2025-05-24 21:00:12,392 - INFO - Request Parameters - Page 6:
2025-05-24 21:00:12,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:12,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:12,907 - INFO - Response - Page 6:
2025-05-24 21:00:13,110 - INFO - 第 6 页获取到 100 条记录
2025-05-24 21:00:13,110 - INFO - Request Parameters - Page 7:
2025-05-24 21:00:13,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:13,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:13,595 - INFO - Response - Page 7:
2025-05-24 21:00:13,798 - INFO - 第 7 页获取到 70 条记录
2025-05-24 21:00:13,798 - INFO - 查询完成，共获取到 670 条记录
2025-05-24 21:00:13,798 - INFO - 获取到 670 条表单数据
2025-05-24 21:00:13,798 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-24 21:00:13,813 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 21:00:13,813 - INFO - 开始处理日期: 2025-03
2025-05-24 21:00:13,813 - INFO - Request Parameters - Page 1:
2025-05-24 21:00:13,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:13,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:14,282 - INFO - Response - Page 1:
2025-05-24 21:00:14,485 - INFO - 第 1 页获取到 100 条记录
2025-05-24 21:00:14,485 - INFO - Request Parameters - Page 2:
2025-05-24 21:00:14,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:14,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:15,001 - INFO - Response - Page 2:
2025-05-24 21:00:15,204 - INFO - 第 2 页获取到 100 条记录
2025-05-24 21:00:15,204 - INFO - Request Parameters - Page 3:
2025-05-24 21:00:15,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:15,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:15,828 - INFO - Response - Page 3:
2025-05-24 21:00:16,032 - INFO - 第 3 页获取到 100 条记录
2025-05-24 21:00:16,032 - INFO - Request Parameters - Page 4:
2025-05-24 21:00:16,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:16,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:16,453 - INFO - Response - Page 4:
2025-05-24 21:00:16,656 - INFO - 第 4 页获取到 100 条记录
2025-05-24 21:00:16,656 - INFO - Request Parameters - Page 5:
2025-05-24 21:00:16,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:16,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:17,141 - INFO - Response - Page 5:
2025-05-24 21:00:17,344 - INFO - 第 5 页获取到 100 条记录
2025-05-24 21:00:17,344 - INFO - Request Parameters - Page 6:
2025-05-24 21:00:17,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:17,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:17,906 - INFO - Response - Page 6:
2025-05-24 21:00:18,109 - INFO - 第 6 页获取到 100 条记录
2025-05-24 21:00:18,109 - INFO - Request Parameters - Page 7:
2025-05-24 21:00:18,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:18,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:18,547 - INFO - Response - Page 7:
2025-05-24 21:00:18,750 - INFO - 第 7 页获取到 61 条记录
2025-05-24 21:00:18,750 - INFO - 查询完成，共获取到 661 条记录
2025-05-24 21:00:18,750 - INFO - 获取到 661 条表单数据
2025-05-24 21:00:18,750 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-24 21:00:18,765 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 21:00:18,765 - INFO - 开始处理日期: 2025-04
2025-05-24 21:00:18,765 - INFO - Request Parameters - Page 1:
2025-05-24 21:00:18,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:18,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:19,437 - INFO - Response - Page 1:
2025-05-24 21:00:19,640 - INFO - 第 1 页获取到 100 条记录
2025-05-24 21:00:19,640 - INFO - Request Parameters - Page 2:
2025-05-24 21:00:19,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:19,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:20,093 - INFO - Response - Page 2:
2025-05-24 21:00:20,296 - INFO - 第 2 页获取到 100 条记录
2025-05-24 21:00:20,296 - INFO - Request Parameters - Page 3:
2025-05-24 21:00:20,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:20,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:20,827 - INFO - Response - Page 3:
2025-05-24 21:00:21,030 - INFO - 第 3 页获取到 100 条记录
2025-05-24 21:00:21,030 - INFO - Request Parameters - Page 4:
2025-05-24 21:00:21,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:21,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:21,530 - INFO - Response - Page 4:
2025-05-24 21:00:21,733 - INFO - 第 4 页获取到 100 条记录
2025-05-24 21:00:21,733 - INFO - Request Parameters - Page 5:
2025-05-24 21:00:21,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:21,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:22,218 - INFO - Response - Page 5:
2025-05-24 21:00:22,421 - INFO - 第 5 页获取到 100 条记录
2025-05-24 21:00:22,421 - INFO - Request Parameters - Page 6:
2025-05-24 21:00:22,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:22,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:22,889 - INFO - Response - Page 6:
2025-05-24 21:00:23,093 - INFO - 第 6 页获取到 100 条记录
2025-05-24 21:00:23,093 - INFO - Request Parameters - Page 7:
2025-05-24 21:00:23,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:23,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:23,514 - INFO - Response - Page 7:
2025-05-24 21:00:23,717 - INFO - 第 7 页获取到 56 条记录
2025-05-24 21:00:23,717 - INFO - 查询完成，共获取到 656 条记录
2025-05-24 21:00:23,717 - INFO - 获取到 656 条表单数据
2025-05-24 21:00:23,717 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-24 21:00:23,733 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 21:00:23,733 - INFO - 开始处理日期: 2025-05
2025-05-24 21:00:23,733 - INFO - Request Parameters - Page 1:
2025-05-24 21:00:23,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:23,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:24,295 - INFO - Response - Page 1:
2025-05-24 21:00:24,498 - INFO - 第 1 页获取到 100 条记录
2025-05-24 21:00:24,498 - INFO - Request Parameters - Page 2:
2025-05-24 21:00:24,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:24,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:24,998 - INFO - Response - Page 2:
2025-05-24 21:00:25,201 - INFO - 第 2 页获取到 100 条记录
2025-05-24 21:00:25,201 - INFO - Request Parameters - Page 3:
2025-05-24 21:00:25,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:25,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:25,654 - INFO - Response - Page 3:
2025-05-24 21:00:25,858 - INFO - 第 3 页获取到 100 条记录
2025-05-24 21:00:25,858 - INFO - Request Parameters - Page 4:
2025-05-24 21:00:25,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:25,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:26,482 - INFO - Response - Page 4:
2025-05-24 21:00:26,685 - INFO - 第 4 页获取到 100 条记录
2025-05-24 21:00:26,685 - INFO - Request Parameters - Page 5:
2025-05-24 21:00:26,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:26,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:27,170 - INFO - Response - Page 5:
2025-05-24 21:00:27,373 - INFO - 第 5 页获取到 100 条记录
2025-05-24 21:00:27,373 - INFO - Request Parameters - Page 6:
2025-05-24 21:00:27,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:27,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:27,857 - INFO - Response - Page 6:
2025-05-24 21:00:28,060 - INFO - 第 6 页获取到 100 条记录
2025-05-24 21:00:28,060 - INFO - Request Parameters - Page 7:
2025-05-24 21:00:28,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:00:28,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:00:28,435 - INFO - Response - Page 7:
2025-05-24 21:00:28,638 - INFO - 第 7 页获取到 29 条记录
2025-05-24 21:00:28,638 - INFO - 查询完成，共获取到 629 条记录
2025-05-24 21:00:28,638 - INFO - 获取到 629 条表单数据
2025-05-24 21:00:28,638 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-24 21:00:28,638 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-24 21:00:29,091 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-24 21:00:29,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75702.0, 'new_value': 78679.0}, {'field': 'offline_amount', 'old_value': 97637.0, 'new_value': 101759.0}, {'field': 'total_amount', 'old_value': 173339.0, 'new_value': 180438.0}, {'field': 'order_count', 'old_value': 3976, 'new_value': 4128}]
2025-05-24 21:00:29,107 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-24 21:00:29,107 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-24 21:00:29,107 - INFO - =================同步完成====================
