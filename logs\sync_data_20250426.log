2025-04-26 08:00:06,512 - INFO - ==================================================
2025-04-26 08:00:06,512 - INFO - 程序启动 - 版本 v1.0.0
2025-04-26 08:00:06,512 - INFO - 日志文件: logs\sync_data_20250426.log
2025-04-26 08:00:06,512 - INFO - ==================================================
2025-04-26 08:00:06,512 - INFO - 程序入口点: __main__
2025-04-26 08:00:06,512 - INFO - ==================================================
2025-04-26 08:00:06,512 - INFO - 程序启动 - 版本 v1.0.1
2025-04-26 08:00:06,512 - INFO - 日志文件: logs\sync_data_20250426.log
2025-04-26 08:00:06,512 - INFO - ==================================================
2025-04-26 08:00:06,825 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-26 08:00:06,825 - INFO - sales_data表已存在，无需创建
2025-04-26 08:00:06,825 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-26 08:00:06,825 - INFO - DataSyncManager初始化完成
2025-04-26 08:00:06,825 - INFO - 未提供日期参数，使用默认值
2025-04-26 08:00:06,825 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-04-26 08:00:06,825 - INFO - 开始综合数据同步流程...
2025-04-26 08:00:06,825 - INFO - 正在获取数衍平台日销售数据...
2025-04-26 08:00:06,825 - INFO - 查询数衍平台数据，时间段为: 2025-02-24 08:00:06.825392, 2025-04-25 08:00:06.825392
2025-04-26 08:00:06,825 - INFO - 正在获取********至********的数据
2025-04-26 08:00:06,825 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:00:06,825 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6802D96636D1238AF05B9430CE6962C5'}
2025-04-26 08:00:21,214 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:00:21,229 - INFO - 过滤后保留 1548 条记录
2025-04-26 08:00:23,247 - INFO - 正在获取********至********的数据
2025-04-26 08:00:23,247 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:00:23,247 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A0BEC87AEAADD4D6980364DBE05EFD50'}
2025-04-26 08:00:26,359 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:00:26,375 - INFO - 过滤后保留 1563 条记录
2025-04-26 08:00:28,377 - INFO - 正在获取********至********的数据
2025-04-26 08:00:28,377 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:00:28,377 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B4FCE8BC01B51F2E6BE4FD5BA5ED4D83'}
2025-04-26 08:00:38,698 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:00:38,730 - INFO - 过滤后保留 1555 条记录
2025-04-26 08:00:40,747 - INFO - 正在获取********至********的数据
2025-04-26 08:00:40,747 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:00:40,747 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '324BC7776C435099DBA92A0A3C1ECD97'}
2025-04-26 08:00:48,879 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:00:48,895 - INFO - 过滤后保留 1561 条记录
2025-04-26 08:00:50,912 - INFO - 正在获取********至********的数据
2025-04-26 08:00:50,912 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:00:50,912 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D0E8103498B821EC4965E864155DAA97'}
2025-04-26 08:00:57,684 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:00:57,700 - INFO - 过滤后保留 1559 条记录
2025-04-26 08:00:59,717 - INFO - 正在获取********至********的数据
2025-04-26 08:00:59,717 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:00:59,717 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F63E0444A14C38A4C23BA89C79EB8CD8'}
2025-04-26 08:01:02,235 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:01:02,251 - INFO - 过滤后保留 1509 条记录
2025-04-26 08:01:04,268 - INFO - 正在获取********至********的数据
2025-04-26 08:01:04,268 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:01:04,268 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DC077470B84EA95F7C712BA91A01F8C6'}
2025-04-26 08:01:06,426 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:01:06,426 - INFO - 过滤后保留 1490 条记录
2025-04-26 08:01:08,444 - INFO - 正在获取********至********的数据
2025-04-26 08:01:08,444 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:01:08,444 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8B6545264731898D94142CA7BBE7ED72'}
2025-04-26 08:01:11,775 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:01:11,775 - INFO - 过滤后保留 1491 条记录
2025-04-26 08:01:13,792 - INFO - 正在获取********至********的数据
2025-04-26 08:01:13,792 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 08:01:13,792 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CC86BB964EB704484619CEED71535B6E'}
2025-04-26 08:01:15,450 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 08:01:15,466 - INFO - 过滤后保留 1031 条记录
2025-04-26 08:01:17,483 - INFO - 开始保存数据到SQLite数据库，共 13307 条记录待处理
2025-04-26 08:01:18,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-04-16
2025-04-26 08:01:18,297 - INFO - 变更字段: amount: 9553 -> 9673, count: 95 -> 96, instore_amount: 1554.6 -> 1674.6, instore_count: 8 -> 9
2025-04-26 08:01:18,343 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-04-17
2025-04-26 08:01:18,343 - INFO - 变更字段: amount: 19486 -> 19920, count: 117 -> 118, instore_amount: 17810.44 -> 18244.44, instore_count: 82 -> 83
2025-04-26 08:01:18,343 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-04-24
2025-04-26 08:01:18,343 - INFO - 变更字段: recommend_amount: 0.0 -> 6654.2, daily_bill_amount: 0.0 -> 6654.2
2025-04-26 08:01:18,359 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-04-24
2025-04-26 08:01:18,359 - INFO - 变更字段: recommend_amount: 9895.1 -> 9745.1, amount: 9895 -> 9745
2025-04-26 08:01:18,359 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTA575PTKJ7Q2OVBN4IS72001D34, sale_time=2025-04-24
2025-04-26 08:01:18,359 - INFO - 变更字段: recommend_amount: 3134.07 -> 4680.74, daily_bill_amount: 0.0 -> 4477.59, amount: 3134 -> 4680, count: 130 -> 195, instore_amount: 2775.56 -> 4269.72, instore_count: 121 -> 184, online_amount: 358.51 -> 411.02, online_count: 9 -> 11
2025-04-26 08:01:18,359 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-04-24
2025-04-26 08:01:18,359 - INFO - 变更字段: recommend_amount: 0.0 -> 11417.4, daily_bill_amount: 0.0 -> 11417.4
2025-04-26 08:01:18,375 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-04-22
2025-04-26 08:01:18,375 - INFO - 变更字段: amount: 1598 -> 1801, count: 10 -> 11, instore_amount: 1353.0 -> 1555.9, instore_count: 9 -> 10
2025-04-26 08:01:18,375 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-04-24
2025-04-26 08:01:18,375 - INFO - 变更字段: recommend_amount: 0.0 -> 24377.4, daily_bill_amount: 0.0 -> 24377.4
2025-04-26 08:01:18,375 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-04-23
2025-04-26 08:01:18,375 - INFO - 变更字段: recommend_amount: 0.0 -> 7326.3, daily_bill_amount: 0.0 -> 7326.3
2025-04-26 08:01:18,375 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-04-24
2025-04-26 08:01:18,375 - INFO - 变更字段: amount: 4239 -> 4331, count: 152 -> 154, online_amount: 4231.6 -> 4322.9, online_count: 150 -> 152
2025-04-26 08:01:18,390 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-04-24
2025-04-26 08:01:18,390 - INFO - 变更字段: amount: 3765 -> 3541
2025-04-26 08:01:18,390 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-04-24
2025-04-26 08:01:18,390 - INFO - 变更字段: amount: 3959 -> 3990, count: 256 -> 259, online_amount: 3653.73 -> 3684.43, online_count: 238 -> 241
2025-04-26 08:01:18,406 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-04-24
2025-04-26 08:01:18,406 - INFO - 变更字段: recommend_amount: 11484.08 -> 11522.08, amount: 11484 -> 11522, count: 261 -> 263, instore_amount: 8717.19 -> 8755.19, instore_count: 170 -> 172
2025-04-26 08:01:18,594 - INFO - SQLite数据保存完成，统计信息：
2025-04-26 08:01:18,594 - INFO - - 总记录数: 13307
2025-04-26 08:01:18,594 - INFO - - 成功插入: 203
2025-04-26 08:01:18,594 - INFO - - 成功更新: 13
2025-04-26 08:01:18,594 - INFO - - 无需更新: 13091
2025-04-26 08:01:18,594 - INFO - - 处理失败: 0
2025-04-26 08:01:24,319 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250426.xlsx
2025-04-26 08:01:24,334 - INFO - 成功获取数衍平台数据，共 13307 条记录
2025-04-26 08:01:24,334 - INFO - 正在更新SQLite月度汇总数据...
2025-04-26 08:01:24,334 - INFO - 开始更新月度汇总数据，时间范围: 2024-05-01 至 2025-04-30
2025-04-26 08:01:24,569 - INFO - 月度汇总数据更新完成，处理了 963 条汇总记录
2025-04-26 08:01:24,569 - INFO - 成功更新月度汇总数据，共 963 条记录
2025-04-26 08:01:24,569 - INFO - 正在获取宜搭日销售表单数据...
2025-04-26 08:01:24,569 - INFO - Request Parameters - Page 1:
2025-04-26 08:01:24,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:24,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:29,119 - INFO - Response - Page 1:
2025-04-26 08:01:29,134 - INFO - 第 1 页获取到 100 条记录
2025-04-26 08:01:29,134 - INFO - Request Parameters - Page 2:
2025-04-26 08:01:29,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:29,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:29,729 - INFO - Response - Page 2:
2025-04-26 08:01:29,729 - INFO - 第 2 页获取到 100 条记录
2025-04-26 08:01:29,729 - INFO - Request Parameters - Page 3:
2025-04-26 08:01:29,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:29,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:31,668 - INFO - Response - Page 3:
2025-04-26 08:01:31,668 - INFO - 第 3 页获取到 100 条记录
2025-04-26 08:01:31,668 - INFO - Request Parameters - Page 4:
2025-04-26 08:01:31,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:31,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:32,340 - INFO - Response - Page 4:
2025-04-26 08:01:32,340 - INFO - 第 4 页获取到 100 条记录
2025-04-26 08:01:32,340 - INFO - Request Parameters - Page 5:
2025-04-26 08:01:32,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:32,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:32,966 - INFO - Response - Page 5:
2025-04-26 08:01:32,966 - INFO - 第 5 页获取到 100 条记录
2025-04-26 08:01:32,966 - INFO - Request Parameters - Page 6:
2025-04-26 08:01:32,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:32,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:33,779 - INFO - Response - Page 6:
2025-04-26 08:01:33,779 - INFO - 第 6 页获取到 100 条记录
2025-04-26 08:01:33,779 - INFO - Request Parameters - Page 7:
2025-04-26 08:01:33,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:33,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:34,389 - INFO - Response - Page 7:
2025-04-26 08:01:34,389 - INFO - 第 7 页获取到 100 条记录
2025-04-26 08:01:34,389 - INFO - Request Parameters - Page 8:
2025-04-26 08:01:34,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:34,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:35,046 - INFO - Response - Page 8:
2025-04-26 08:01:35,046 - INFO - 第 8 页获取到 100 条记录
2025-04-26 08:01:35,046 - INFO - Request Parameters - Page 9:
2025-04-26 08:01:35,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:35,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:35,703 - INFO - Response - Page 9:
2025-04-26 08:01:35,703 - INFO - 第 9 页获取到 100 条记录
2025-04-26 08:01:35,703 - INFO - Request Parameters - Page 10:
2025-04-26 08:01:35,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:35,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:36,313 - INFO - Response - Page 10:
2025-04-26 08:01:36,313 - INFO - 第 10 页获取到 100 条记录
2025-04-26 08:01:36,328 - INFO - Request Parameters - Page 11:
2025-04-26 08:01:36,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:36,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:37,017 - INFO - Response - Page 11:
2025-04-26 08:01:37,017 - INFO - 第 11 页获取到 100 条记录
2025-04-26 08:01:37,017 - INFO - Request Parameters - Page 12:
2025-04-26 08:01:37,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:37,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:37,658 - INFO - Response - Page 12:
2025-04-26 08:01:37,658 - INFO - 第 12 页获取到 100 条记录
2025-04-26 08:01:37,658 - INFO - Request Parameters - Page 13:
2025-04-26 08:01:37,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:37,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:38,236 - INFO - Response - Page 13:
2025-04-26 08:01:38,236 - INFO - 第 13 页获取到 100 条记录
2025-04-26 08:01:38,236 - INFO - Request Parameters - Page 14:
2025-04-26 08:01:38,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:38,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:38,909 - INFO - Response - Page 14:
2025-04-26 08:01:38,909 - INFO - 第 14 页获取到 100 条记录
2025-04-26 08:01:38,909 - INFO - Request Parameters - Page 15:
2025-04-26 08:01:38,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:38,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:39,534 - INFO - Response - Page 15:
2025-04-26 08:01:39,534 - INFO - 第 15 页获取到 100 条记录
2025-04-26 08:01:39,534 - INFO - Request Parameters - Page 16:
2025-04-26 08:01:39,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:39,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:40,223 - INFO - Response - Page 16:
2025-04-26 08:01:40,223 - INFO - 第 16 页获取到 100 条记录
2025-04-26 08:01:40,223 - INFO - Request Parameters - Page 17:
2025-04-26 08:01:40,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:40,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:40,895 - INFO - Response - Page 17:
2025-04-26 08:01:40,895 - INFO - 第 17 页获取到 100 条记录
2025-04-26 08:01:40,895 - INFO - Request Parameters - Page 18:
2025-04-26 08:01:40,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:40,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:41,552 - INFO - Response - Page 18:
2025-04-26 08:01:41,552 - INFO - 第 18 页获取到 100 条记录
2025-04-26 08:01:41,552 - INFO - Request Parameters - Page 19:
2025-04-26 08:01:41,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:41,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:42,193 - INFO - Response - Page 19:
2025-04-26 08:01:42,193 - INFO - 第 19 页获取到 100 条记录
2025-04-26 08:01:42,193 - INFO - Request Parameters - Page 20:
2025-04-26 08:01:42,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:42,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:42,850 - INFO - Response - Page 20:
2025-04-26 08:01:42,866 - INFO - 第 20 页获取到 100 条记录
2025-04-26 08:01:42,866 - INFO - Request Parameters - Page 21:
2025-04-26 08:01:42,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:42,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:43,444 - INFO - Response - Page 21:
2025-04-26 08:01:43,444 - INFO - 第 21 页获取到 100 条记录
2025-04-26 08:01:43,444 - INFO - Request Parameters - Page 22:
2025-04-26 08:01:43,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:43,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:44,085 - INFO - Response - Page 22:
2025-04-26 08:01:44,085 - INFO - 第 22 页获取到 100 条记录
2025-04-26 08:01:44,085 - INFO - Request Parameters - Page 23:
2025-04-26 08:01:44,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:44,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:44,758 - INFO - Response - Page 23:
2025-04-26 08:01:44,758 - INFO - 第 23 页获取到 100 条记录
2025-04-26 08:01:44,758 - INFO - Request Parameters - Page 24:
2025-04-26 08:01:44,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:44,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:45,493 - INFO - Response - Page 24:
2025-04-26 08:01:45,493 - INFO - 第 24 页获取到 100 条记录
2025-04-26 08:01:45,493 - INFO - Request Parameters - Page 25:
2025-04-26 08:01:45,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:45,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:46,040 - INFO - Response - Page 25:
2025-04-26 08:01:46,040 - INFO - 第 25 页获取到 100 条记录
2025-04-26 08:01:46,040 - INFO - Request Parameters - Page 26:
2025-04-26 08:01:46,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:46,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:46,744 - INFO - Response - Page 26:
2025-04-26 08:01:46,744 - INFO - 第 26 页获取到 100 条记录
2025-04-26 08:01:46,744 - INFO - Request Parameters - Page 27:
2025-04-26 08:01:46,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:46,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:47,307 - INFO - Response - Page 27:
2025-04-26 08:01:47,307 - INFO - 第 27 页获取到 100 条记录
2025-04-26 08:01:47,307 - INFO - Request Parameters - Page 28:
2025-04-26 08:01:47,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:47,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:48,011 - INFO - Response - Page 28:
2025-04-26 08:01:48,011 - INFO - 第 28 页获取到 100 条记录
2025-04-26 08:01:48,011 - INFO - Request Parameters - Page 29:
2025-04-26 08:01:48,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:48,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:48,636 - INFO - Response - Page 29:
2025-04-26 08:01:48,636 - INFO - 第 29 页获取到 100 条记录
2025-04-26 08:01:48,636 - INFO - Request Parameters - Page 30:
2025-04-26 08:01:48,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:48,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:49,246 - INFO - Response - Page 30:
2025-04-26 08:01:49,246 - INFO - 第 30 页获取到 100 条记录
2025-04-26 08:01:49,246 - INFO - Request Parameters - Page 31:
2025-04-26 08:01:49,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:49,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:49,825 - INFO - Response - Page 31:
2025-04-26 08:01:49,825 - INFO - 第 31 页获取到 100 条记录
2025-04-26 08:01:49,825 - INFO - Request Parameters - Page 32:
2025-04-26 08:01:49,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:49,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:50,451 - INFO - Response - Page 32:
2025-04-26 08:01:50,451 - INFO - 第 32 页获取到 100 条记录
2025-04-26 08:01:50,451 - INFO - Request Parameters - Page 33:
2025-04-26 08:01:50,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:50,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:51,154 - INFO - Response - Page 33:
2025-04-26 08:01:51,154 - INFO - 第 33 页获取到 100 条记录
2025-04-26 08:01:51,154 - INFO - Request Parameters - Page 34:
2025-04-26 08:01:51,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:51,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:51,796 - INFO - Response - Page 34:
2025-04-26 08:01:51,796 - INFO - 第 34 页获取到 100 条记录
2025-04-26 08:01:51,796 - INFO - Request Parameters - Page 35:
2025-04-26 08:01:51,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:51,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:52,437 - INFO - Response - Page 35:
2025-04-26 08:01:52,437 - INFO - 第 35 页获取到 100 条记录
2025-04-26 08:01:52,437 - INFO - Request Parameters - Page 36:
2025-04-26 08:01:52,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:52,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:53,078 - INFO - Response - Page 36:
2025-04-26 08:01:53,078 - INFO - 第 36 页获取到 100 条记录
2025-04-26 08:01:53,078 - INFO - Request Parameters - Page 37:
2025-04-26 08:01:53,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:53,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:53,938 - INFO - Response - Page 37:
2025-04-26 08:01:53,938 - INFO - 第 37 页获取到 100 条记录
2025-04-26 08:01:53,938 - INFO - Request Parameters - Page 38:
2025-04-26 08:01:53,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:53,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:54,611 - INFO - Response - Page 38:
2025-04-26 08:01:54,611 - INFO - 第 38 页获取到 100 条记录
2025-04-26 08:01:54,611 - INFO - Request Parameters - Page 39:
2025-04-26 08:01:54,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:54,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:55,330 - INFO - Response - Page 39:
2025-04-26 08:01:55,330 - INFO - 第 39 页获取到 100 条记录
2025-04-26 08:01:55,330 - INFO - Request Parameters - Page 40:
2025-04-26 08:01:55,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:55,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:56,002 - INFO - Response - Page 40:
2025-04-26 08:01:56,002 - INFO - 第 40 页获取到 100 条记录
2025-04-26 08:01:56,002 - INFO - Request Parameters - Page 41:
2025-04-26 08:01:56,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:56,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:56,612 - INFO - Response - Page 41:
2025-04-26 08:01:56,612 - INFO - 第 41 页获取到 100 条记录
2025-04-26 08:01:56,612 - INFO - Request Parameters - Page 42:
2025-04-26 08:01:56,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:56,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:57,394 - INFO - Response - Page 42:
2025-04-26 08:01:57,394 - INFO - 第 42 页获取到 100 条记录
2025-04-26 08:01:57,394 - INFO - Request Parameters - Page 43:
2025-04-26 08:01:57,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:57,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:58,036 - INFO - Response - Page 43:
2025-04-26 08:01:58,036 - INFO - 第 43 页获取到 100 条记录
2025-04-26 08:01:58,036 - INFO - Request Parameters - Page 44:
2025-04-26 08:01:58,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:58,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:58,849 - INFO - Response - Page 44:
2025-04-26 08:01:58,849 - INFO - 第 44 页获取到 100 条记录
2025-04-26 08:01:58,849 - INFO - Request Parameters - Page 45:
2025-04-26 08:01:58,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:58,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:01:59,506 - INFO - Response - Page 45:
2025-04-26 08:01:59,506 - INFO - 第 45 页获取到 100 条记录
2025-04-26 08:01:59,506 - INFO - Request Parameters - Page 46:
2025-04-26 08:01:59,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:01:59,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:00,116 - INFO - Response - Page 46:
2025-04-26 08:02:00,116 - INFO - 第 46 页获取到 100 条记录
2025-04-26 08:02:00,116 - INFO - Request Parameters - Page 47:
2025-04-26 08:02:00,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:00,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:00,741 - INFO - Response - Page 47:
2025-04-26 08:02:00,741 - INFO - 第 47 页获取到 100 条记录
2025-04-26 08:02:00,741 - INFO - Request Parameters - Page 48:
2025-04-26 08:02:00,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:00,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:01,351 - INFO - Response - Page 48:
2025-04-26 08:02:01,351 - INFO - 第 48 页获取到 100 条记录
2025-04-26 08:02:01,351 - INFO - Request Parameters - Page 49:
2025-04-26 08:02:01,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:01,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:02,039 - INFO - Response - Page 49:
2025-04-26 08:02:02,039 - INFO - 第 49 页获取到 100 条记录
2025-04-26 08:02:02,039 - INFO - Request Parameters - Page 50:
2025-04-26 08:02:02,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:02,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 50, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:02,649 - INFO - Response - Page 50:
2025-04-26 08:02:02,649 - INFO - 第 50 页获取到 100 条记录
2025-04-26 08:02:02,649 - INFO - Request Parameters - Page 51:
2025-04-26 08:02:02,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:02,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 51, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:03,337 - INFO - Response - Page 51:
2025-04-26 08:02:03,337 - INFO - 第 51 页获取到 100 条记录
2025-04-26 08:02:03,337 - INFO - Request Parameters - Page 52:
2025-04-26 08:02:03,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:03,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 52, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:03,963 - INFO - Response - Page 52:
2025-04-26 08:02:03,963 - INFO - 第 52 页获取到 100 条记录
2025-04-26 08:02:03,963 - INFO - Request Parameters - Page 53:
2025-04-26 08:02:03,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:03,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 53, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:04,526 - INFO - Response - Page 53:
2025-04-26 08:02:04,526 - INFO - 第 53 页获取到 100 条记录
2025-04-26 08:02:04,526 - INFO - Request Parameters - Page 54:
2025-04-26 08:02:04,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:04,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 54, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:05,167 - INFO - Response - Page 54:
2025-04-26 08:02:05,167 - INFO - 第 54 页获取到 100 条记录
2025-04-26 08:02:05,167 - INFO - Request Parameters - Page 55:
2025-04-26 08:02:05,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:05,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 55, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:05,855 - INFO - Response - Page 55:
2025-04-26 08:02:05,855 - INFO - 第 55 页获取到 100 条记录
2025-04-26 08:02:05,855 - INFO - Request Parameters - Page 56:
2025-04-26 08:02:05,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:05,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 56, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:06,543 - INFO - Response - Page 56:
2025-04-26 08:02:06,543 - INFO - 第 56 页获取到 100 条记录
2025-04-26 08:02:06,543 - INFO - Request Parameters - Page 57:
2025-04-26 08:02:06,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:06,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 57, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:07,231 - INFO - Response - Page 57:
2025-04-26 08:02:07,231 - INFO - 第 57 页获取到 100 条记录
2025-04-26 08:02:07,231 - INFO - Request Parameters - Page 58:
2025-04-26 08:02:07,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:07,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 58, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:07,841 - INFO - Response - Page 58:
2025-04-26 08:02:07,841 - INFO - 第 58 页获取到 100 条记录
2025-04-26 08:02:07,841 - INFO - Request Parameters - Page 59:
2025-04-26 08:02:07,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:07,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 59, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:08,514 - INFO - Response - Page 59:
2025-04-26 08:02:08,514 - INFO - 第 59 页获取到 100 条记录
2025-04-26 08:02:08,514 - INFO - Request Parameters - Page 60:
2025-04-26 08:02:08,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:08,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 60, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:09,171 - INFO - Response - Page 60:
2025-04-26 08:02:09,171 - INFO - 第 60 页获取到 100 条记录
2025-04-26 08:02:09,171 - INFO - Request Parameters - Page 61:
2025-04-26 08:02:09,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:09,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 61, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:09,843 - INFO - Response - Page 61:
2025-04-26 08:02:09,843 - INFO - 第 61 页获取到 100 条记录
2025-04-26 08:02:09,843 - INFO - Request Parameters - Page 62:
2025-04-26 08:02:09,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:09,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 62, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:10,500 - INFO - Response - Page 62:
2025-04-26 08:02:10,500 - INFO - 第 62 页获取到 100 条记录
2025-04-26 08:02:10,500 - INFO - Request Parameters - Page 63:
2025-04-26 08:02:10,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:10,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 63, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:11,110 - INFO - Response - Page 63:
2025-04-26 08:02:11,110 - INFO - 第 63 页获取到 100 条记录
2025-04-26 08:02:11,110 - INFO - Request Parameters - Page 64:
2025-04-26 08:02:11,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:11,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 64, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:11,751 - INFO - Response - Page 64:
2025-04-26 08:02:11,751 - INFO - 第 64 页获取到 100 条记录
2025-04-26 08:02:11,751 - INFO - Request Parameters - Page 65:
2025-04-26 08:02:11,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:11,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 65, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:12,345 - INFO - Response - Page 65:
2025-04-26 08:02:12,345 - INFO - 第 65 页获取到 100 条记录
2025-04-26 08:02:12,345 - INFO - Request Parameters - Page 66:
2025-04-26 08:02:12,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:12,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 66, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:13,018 - INFO - Response - Page 66:
2025-04-26 08:02:13,018 - INFO - 第 66 页获取到 100 条记录
2025-04-26 08:02:13,018 - INFO - Request Parameters - Page 67:
2025-04-26 08:02:13,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:13,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 67, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:13,628 - INFO - Response - Page 67:
2025-04-26 08:02:13,643 - INFO - 第 67 页获取到 100 条记录
2025-04-26 08:02:13,643 - INFO - Request Parameters - Page 68:
2025-04-26 08:02:13,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:13,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 68, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:14,316 - INFO - Response - Page 68:
2025-04-26 08:02:14,316 - INFO - 第 68 页获取到 100 条记录
2025-04-26 08:02:14,331 - INFO - Request Parameters - Page 69:
2025-04-26 08:02:14,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:14,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 69, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:15,004 - INFO - Response - Page 69:
2025-04-26 08:02:15,004 - INFO - 第 69 页获取到 100 条记录
2025-04-26 08:02:15,004 - INFO - Request Parameters - Page 70:
2025-04-26 08:02:15,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:15,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 70, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:15,676 - INFO - Response - Page 70:
2025-04-26 08:02:15,676 - INFO - 第 70 页获取到 100 条记录
2025-04-26 08:02:15,676 - INFO - Request Parameters - Page 71:
2025-04-26 08:02:15,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:15,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 71, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:16,333 - INFO - Response - Page 71:
2025-04-26 08:02:16,333 - INFO - 第 71 页获取到 100 条记录
2025-04-26 08:02:16,349 - INFO - Request Parameters - Page 72:
2025-04-26 08:02:16,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:16,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 72, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:17,006 - INFO - Response - Page 72:
2025-04-26 08:02:17,006 - INFO - 第 72 页获取到 100 条记录
2025-04-26 08:02:17,006 - INFO - Request Parameters - Page 73:
2025-04-26 08:02:17,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:17,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 73, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:17,647 - INFO - Response - Page 73:
2025-04-26 08:02:17,647 - INFO - 第 73 页获取到 100 条记录
2025-04-26 08:02:17,647 - INFO - Request Parameters - Page 74:
2025-04-26 08:02:17,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:17,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 74, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:18,257 - INFO - Response - Page 74:
2025-04-26 08:02:18,257 - INFO - 第 74 页获取到 100 条记录
2025-04-26 08:02:18,257 - INFO - Request Parameters - Page 75:
2025-04-26 08:02:18,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:18,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 75, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:18,898 - INFO - Response - Page 75:
2025-04-26 08:02:18,914 - INFO - 第 75 页获取到 100 条记录
2025-04-26 08:02:18,914 - INFO - Request Parameters - Page 76:
2025-04-26 08:02:18,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:18,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 76, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:19,524 - INFO - Response - Page 76:
2025-04-26 08:02:19,524 - INFO - 第 76 页获取到 100 条记录
2025-04-26 08:02:19,524 - INFO - Request Parameters - Page 77:
2025-04-26 08:02:19,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:19,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 77, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:20,165 - INFO - Response - Page 77:
2025-04-26 08:02:20,165 - INFO - 第 77 页获取到 100 条记录
2025-04-26 08:02:20,165 - INFO - Request Parameters - Page 78:
2025-04-26 08:02:20,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:20,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 78, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:21,025 - INFO - Response - Page 78:
2025-04-26 08:02:21,025 - INFO - 第 78 页获取到 100 条记录
2025-04-26 08:02:21,025 - INFO - Request Parameters - Page 79:
2025-04-26 08:02:21,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:21,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 79, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:21,682 - INFO - Response - Page 79:
2025-04-26 08:02:21,682 - INFO - 第 79 页获取到 100 条记录
2025-04-26 08:02:21,682 - INFO - Request Parameters - Page 80:
2025-04-26 08:02:21,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:21,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 80, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:22,276 - INFO - Response - Page 80:
2025-04-26 08:02:22,276 - INFO - 第 80 页获取到 100 条记录
2025-04-26 08:02:22,276 - INFO - Request Parameters - Page 81:
2025-04-26 08:02:22,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:22,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 81, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:22,902 - INFO - Response - Page 81:
2025-04-26 08:02:22,902 - INFO - 第 81 页获取到 100 条记录
2025-04-26 08:02:22,902 - INFO - Request Parameters - Page 82:
2025-04-26 08:02:22,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:22,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 82, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:23,527 - INFO - Response - Page 82:
2025-04-26 08:02:23,527 - INFO - 第 82 页获取到 100 条记录
2025-04-26 08:02:23,527 - INFO - Request Parameters - Page 83:
2025-04-26 08:02:23,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:23,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 83, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:24,137 - INFO - Response - Page 83:
2025-04-26 08:02:24,137 - INFO - 第 83 页获取到 100 条记录
2025-04-26 08:02:24,137 - INFO - Request Parameters - Page 84:
2025-04-26 08:02:24,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:24,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 84, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:24,810 - INFO - Response - Page 84:
2025-04-26 08:02:24,810 - INFO - 第 84 页获取到 100 条记录
2025-04-26 08:02:24,810 - INFO - Request Parameters - Page 85:
2025-04-26 08:02:24,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:24,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 85, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:25,592 - INFO - Response - Page 85:
2025-04-26 08:02:25,592 - INFO - 第 85 页获取到 100 条记录
2025-04-26 08:02:25,592 - INFO - Request Parameters - Page 86:
2025-04-26 08:02:25,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:25,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 86, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:26,248 - INFO - Response - Page 86:
2025-04-26 08:02:26,248 - INFO - 第 86 页获取到 100 条记录
2025-04-26 08:02:26,248 - INFO - Request Parameters - Page 87:
2025-04-26 08:02:26,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:26,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 87, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:26,905 - INFO - Response - Page 87:
2025-04-26 08:02:26,921 - INFO - 第 87 页获取到 100 条记录
2025-04-26 08:02:26,952 - INFO - Request Parameters - Page 88:
2025-04-26 08:02:26,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:26,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 88, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:27,578 - INFO - Response - Page 88:
2025-04-26 08:02:27,578 - INFO - 第 88 页获取到 100 条记录
2025-04-26 08:02:27,578 - INFO - Request Parameters - Page 89:
2025-04-26 08:02:27,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:27,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 89, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:28,188 - INFO - Response - Page 89:
2025-04-26 08:02:28,188 - INFO - 第 89 页获取到 100 条记录
2025-04-26 08:02:28,188 - INFO - Request Parameters - Page 90:
2025-04-26 08:02:28,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:28,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 90, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:28,860 - INFO - Response - Page 90:
2025-04-26 08:02:28,860 - INFO - 第 90 页获取到 100 条记录
2025-04-26 08:02:28,860 - INFO - Request Parameters - Page 91:
2025-04-26 08:02:28,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:28,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 91, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:29,486 - INFO - Response - Page 91:
2025-04-26 08:02:29,486 - INFO - 第 91 页获取到 100 条记录
2025-04-26 08:02:29,486 - INFO - Request Parameters - Page 92:
2025-04-26 08:02:29,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:29,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 92, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:30,143 - INFO - Response - Page 92:
2025-04-26 08:02:30,143 - INFO - 第 92 页获取到 100 条记录
2025-04-26 08:02:30,143 - INFO - Request Parameters - Page 93:
2025-04-26 08:02:30,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:30,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 93, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:30,831 - INFO - Response - Page 93:
2025-04-26 08:02:30,831 - INFO - 第 93 页获取到 100 条记录
2025-04-26 08:02:30,831 - INFO - Request Parameters - Page 94:
2025-04-26 08:02:30,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:30,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 94, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:31,456 - INFO - Response - Page 94:
2025-04-26 08:02:31,456 - INFO - 第 94 页获取到 100 条记录
2025-04-26 08:02:31,456 - INFO - Request Parameters - Page 95:
2025-04-26 08:02:31,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:31,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 95, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:32,082 - INFO - Response - Page 95:
2025-04-26 08:02:32,082 - INFO - 第 95 页获取到 100 条记录
2025-04-26 08:02:32,082 - INFO - Request Parameters - Page 96:
2025-04-26 08:02:32,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:32,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 96, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:32,801 - INFO - Response - Page 96:
2025-04-26 08:02:32,801 - INFO - 第 96 页获取到 100 条记录
2025-04-26 08:02:32,801 - INFO - Request Parameters - Page 97:
2025-04-26 08:02:32,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:32,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 97, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:33,396 - INFO - Response - Page 97:
2025-04-26 08:02:33,396 - INFO - 第 97 页获取到 100 条记录
2025-04-26 08:02:33,396 - INFO - Request Parameters - Page 98:
2025-04-26 08:02:33,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:33,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 98, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:34,021 - INFO - Response - Page 98:
2025-04-26 08:02:34,021 - INFO - 第 98 页获取到 100 条记录
2025-04-26 08:02:34,021 - INFO - Request Parameters - Page 99:
2025-04-26 08:02:34,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:34,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 99, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:34,709 - INFO - Response - Page 99:
2025-04-26 08:02:34,709 - INFO - 第 99 页获取到 100 条记录
2025-04-26 08:02:34,709 - INFO - Request Parameters - Page 100:
2025-04-26 08:02:34,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:34,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 100, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:35,413 - INFO - Response - Page 100:
2025-04-26 08:02:35,413 - INFO - 第 100 页获取到 100 条记录
2025-04-26 08:02:35,413 - INFO - Request Parameters - Page 101:
2025-04-26 08:02:35,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:35,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 101, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:36,023 - INFO - Response - Page 101:
2025-04-26 08:02:36,023 - INFO - 第 101 页获取到 100 条记录
2025-04-26 08:02:36,023 - INFO - Request Parameters - Page 102:
2025-04-26 08:02:36,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:36,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 102, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:36,695 - INFO - Response - Page 102:
2025-04-26 08:02:36,695 - INFO - 第 102 页获取到 100 条记录
2025-04-26 08:02:36,695 - INFO - Request Parameters - Page 103:
2025-04-26 08:02:36,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:36,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 103, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:37,384 - INFO - Response - Page 103:
2025-04-26 08:02:37,384 - INFO - 第 103 页获取到 100 条记录
2025-04-26 08:02:37,384 - INFO - Request Parameters - Page 104:
2025-04-26 08:02:37,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:37,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 104, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:38,009 - INFO - Response - Page 104:
2025-04-26 08:02:38,009 - INFO - 第 104 页获取到 100 条记录
2025-04-26 08:02:38,009 - INFO - Request Parameters - Page 105:
2025-04-26 08:02:38,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:38,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 105, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:38,603 - INFO - Response - Page 105:
2025-04-26 08:02:38,603 - INFO - 第 105 页获取到 100 条记录
2025-04-26 08:02:38,603 - INFO - Request Parameters - Page 106:
2025-04-26 08:02:38,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:38,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 106, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:39,276 - INFO - Response - Page 106:
2025-04-26 08:02:39,276 - INFO - 第 106 页获取到 100 条记录
2025-04-26 08:02:39,276 - INFO - Request Parameters - Page 107:
2025-04-26 08:02:39,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:39,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 107, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:40,105 - INFO - Response - Page 107:
2025-04-26 08:02:40,105 - INFO - 第 107 页获取到 100 条记录
2025-04-26 08:02:40,105 - INFO - Request Parameters - Page 108:
2025-04-26 08:02:40,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:40,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 108, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:40,730 - INFO - Response - Page 108:
2025-04-26 08:02:40,746 - INFO - 第 108 页获取到 100 条记录
2025-04-26 08:02:40,746 - INFO - Request Parameters - Page 109:
2025-04-26 08:02:40,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:40,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 109, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:41,465 - INFO - Response - Page 109:
2025-04-26 08:02:41,465 - INFO - 第 109 页获取到 100 条记录
2025-04-26 08:02:41,481 - INFO - Request Parameters - Page 110:
2025-04-26 08:02:41,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:41,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 110, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:42,200 - INFO - Response - Page 110:
2025-04-26 08:02:42,200 - INFO - 第 110 页获取到 100 条记录
2025-04-26 08:02:42,200 - INFO - Request Parameters - Page 111:
2025-04-26 08:02:42,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:42,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 111, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:42,873 - INFO - Response - Page 111:
2025-04-26 08:02:42,873 - INFO - 第 111 页获取到 100 条记录
2025-04-26 08:02:42,873 - INFO - Request Parameters - Page 112:
2025-04-26 08:02:42,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:42,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 112, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:43,498 - INFO - Response - Page 112:
2025-04-26 08:02:43,498 - INFO - 第 112 页获取到 100 条记录
2025-04-26 08:02:43,498 - INFO - Request Parameters - Page 113:
2025-04-26 08:02:43,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:43,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 113, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:44,187 - INFO - Response - Page 113:
2025-04-26 08:02:44,187 - INFO - 第 113 页获取到 100 条记录
2025-04-26 08:02:44,187 - INFO - Request Parameters - Page 114:
2025-04-26 08:02:44,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:44,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 114, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:44,828 - INFO - Response - Page 114:
2025-04-26 08:02:44,828 - INFO - 第 114 页获取到 100 条记录
2025-04-26 08:02:44,828 - INFO - Request Parameters - Page 115:
2025-04-26 08:02:44,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:44,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 115, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:45,485 - INFO - Response - Page 115:
2025-04-26 08:02:45,485 - INFO - 第 115 页获取到 100 条记录
2025-04-26 08:02:45,485 - INFO - Request Parameters - Page 116:
2025-04-26 08:02:45,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:45,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 116, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:46,220 - INFO - Response - Page 116:
2025-04-26 08:02:46,220 - INFO - 第 116 页获取到 100 条记录
2025-04-26 08:02:46,220 - INFO - Request Parameters - Page 117:
2025-04-26 08:02:46,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:46,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 117, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:46,892 - INFO - Response - Page 117:
2025-04-26 08:02:46,892 - INFO - 第 117 页获取到 100 条记录
2025-04-26 08:02:46,892 - INFO - Request Parameters - Page 118:
2025-04-26 08:02:46,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:46,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 118, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:47,549 - INFO - Response - Page 118:
2025-04-26 08:02:47,549 - INFO - 第 118 页获取到 100 条记录
2025-04-26 08:02:47,549 - INFO - Request Parameters - Page 119:
2025-04-26 08:02:47,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:47,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 119, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:48,190 - INFO - Response - Page 119:
2025-04-26 08:02:48,190 - INFO - 第 119 页获取到 100 条记录
2025-04-26 08:02:48,190 - INFO - Request Parameters - Page 120:
2025-04-26 08:02:48,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:48,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 120, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:48,769 - INFO - Response - Page 120:
2025-04-26 08:02:48,769 - INFO - 第 120 页获取到 100 条记录
2025-04-26 08:02:48,769 - INFO - Request Parameters - Page 121:
2025-04-26 08:02:48,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:48,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 121, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:49,410 - INFO - Response - Page 121:
2025-04-26 08:02:49,410 - INFO - 第 121 页获取到 100 条记录
2025-04-26 08:02:49,410 - INFO - Request Parameters - Page 122:
2025-04-26 08:02:49,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:49,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 122, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:50,067 - INFO - Response - Page 122:
2025-04-26 08:02:50,067 - INFO - 第 122 页获取到 100 条记录
2025-04-26 08:02:50,067 - INFO - Request Parameters - Page 123:
2025-04-26 08:02:50,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:50,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 123, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:50,692 - INFO - Response - Page 123:
2025-04-26 08:02:50,692 - INFO - 第 123 页获取到 100 条记录
2025-04-26 08:02:50,692 - INFO - Request Parameters - Page 124:
2025-04-26 08:02:50,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:50,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 124, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:51,302 - INFO - Response - Page 124:
2025-04-26 08:02:51,302 - INFO - 第 124 页获取到 100 条记录
2025-04-26 08:02:51,302 - INFO - Request Parameters - Page 125:
2025-04-26 08:02:51,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:51,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 125, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:52,006 - INFO - Response - Page 125:
2025-04-26 08:02:52,006 - INFO - 第 125 页获取到 100 条记录
2025-04-26 08:02:52,006 - INFO - Request Parameters - Page 126:
2025-04-26 08:02:52,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:52,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 126, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:52,694 - INFO - Response - Page 126:
2025-04-26 08:02:52,694 - INFO - 第 126 页获取到 100 条记录
2025-04-26 08:02:52,694 - INFO - Request Parameters - Page 127:
2025-04-26 08:02:52,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:52,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 127, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:53,288 - INFO - Response - Page 127:
2025-04-26 08:02:53,288 - INFO - 第 127 页获取到 100 条记录
2025-04-26 08:02:53,288 - INFO - Request Parameters - Page 128:
2025-04-26 08:02:53,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:53,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 128, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:53,945 - INFO - Response - Page 128:
2025-04-26 08:02:53,945 - INFO - 第 128 页获取到 100 条记录
2025-04-26 08:02:53,945 - INFO - Request Parameters - Page 129:
2025-04-26 08:02:53,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:02:53,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 129, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740355284569, 1745539284569], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:02:54,508 - INFO - Response - Page 129:
2025-04-26 08:02:54,508 - INFO - 第 129 页获取到 52 条记录
2025-04-26 08:02:54,508 - INFO - 查询完成，共获取到 12852 条记录
2025-04-26 08:02:54,508 - INFO - 成功获取宜搭日销售表单数据，共 12852 条记录
2025-04-26 08:02:54,508 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-04-26 08:02:54,508 - INFO - 开始对比和同步日销售数据...
2025-04-26 08:02:54,884 - INFO - 成功创建宜搭日销售数据索引，共 12852 条记录
2025-04-26 08:02:54,884 - INFO - 开始处理数衍数据，共 13307 条记录
2025-04-26 08:02:55,462 - INFO - 更新表单数据成功: FINST-S0E660A195VUB4XR6VREK986UN6I3W1L21W9MG1
2025-04-26 08:02:55,462 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_********, 变更字段: [{'field': 'amount', 'old_value': 9553.4, 'new_value': 9673.4}, {'field': 'count', 'old_value': 95, 'new_value': 96}, {'field': 'instoreAmount', 'old_value': 1554.6, 'new_value': 1674.6}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-04-26 08:02:55,963 - INFO - 更新表单数据成功: FINST-N3G66S81J7VUXZI47CRRB9S60R1P2YN241W9MJ9
2025-04-26 08:02:55,963 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250417, 变更字段: [{'field': 'amount', 'old_value': 19486.94, 'new_value': 19920.94}, {'field': 'count', 'old_value': 117, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 17810.44, 'new_value': 18244.44}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 83}]
2025-04-26 08:02:56,401 - INFO - 更新表单数据成功: FINST-R1A66H91H5VUYX5N6L7YI5B9F1KZ2FS641W9MWC
2025-04-26 08:02:56,401 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250424, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6654.2}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6654.2}]
2025-04-26 08:02:56,839 - INFO - 更新表单数据成功: FINST-HJ966H81R4VUPEQ7CF7CGAW7ZPCZ233F41W9MG4
2025-04-26 08:02:56,839 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_20250424, 变更字段: [{'field': 'recommendAmount', 'old_value': 9895.1, 'new_value': 9745.1}, {'field': 'amount', 'old_value': 9895.1, 'new_value': 9745.1}]
2025-04-26 08:02:57,276 - INFO - 更新表单数据成功: FINST-HJ966H81R4VUPEQ7CF7CGAW7ZPCZ233F41W9MK5
2025-04-26 08:02:57,276 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_20250424, 变更字段: [{'field': 'recommendAmount', 'old_value': 3134.07, 'new_value': 4680.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4477.59}, {'field': 'amount', 'old_value': 3134.07, 'new_value': 4680.74}, {'field': 'count', 'old_value': 130, 'new_value': 195}, {'field': 'instoreAmount', 'old_value': 2775.56, 'new_value': 4269.72}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 184}, {'field': 'onlineAmount', 'old_value': 358.51, 'new_value': 411.02}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 11}]
2025-04-26 08:02:57,777 - INFO - 更新表单数据成功: FINST-W3B66L71S6VU45VJF0TI27S3SG3P388J41W9MI8
2025-04-26 08:02:57,777 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250424, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11417.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11417.4}]
2025-04-26 08:02:58,246 - INFO - 更新表单数据成功: FINST-6IF66PC12KVU0NMNB2L26DJ8BWW32MIR41W9MC
2025-04-26 08:02:58,246 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_********, 变更字段: [{'field': 'amount', 'old_value': 1598.4, 'new_value': 1801.3}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 1353.0, 'new_value': 1555.9}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-04-26 08:02:58,715 - INFO - 更新表单数据成功: FINST-6IF66PC12KVU0NMNB2L26DJ8BWW32MIR41W9MF
2025-04-26 08:02:58,715 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250424, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 24377.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24377.4}]
2025-04-26 08:02:59,184 - INFO - 更新表单数据成功: FINST-6IF66PC12KVU0NMNB2L26DJ8BWW32MIR41W9M01
2025-04-26 08:02:59,184 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7326.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7326.3}]
2025-04-26 08:02:59,669 - INFO - 批量插入日度响应状态码: 200
2025-04-26 08:02:59,669 - INFO - 批量插入日度表单数据成功，批次 1，共 100 条记录
2025-04-26 08:02:59,669 - INFO - 成功插入的数据ID: ['FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M3C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M4C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M5C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M6C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M7C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M8C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9M9C', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MAC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MBC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MCC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MDC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MEC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MFC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MGC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MHC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MIC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MJC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MKC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MLC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MMC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MNC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MOC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MPC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MQC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MRC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MSC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MTC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MUC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MVC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MWC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MXC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MYC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2GL1IGX9MZC', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M0D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M1D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M2D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M3D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M4D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M5D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M6D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M7D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M8D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M9D', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MAD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MBD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MCD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MDD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MED', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MFD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MGD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MHD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MID', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MJD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MKD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MLD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MMD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MND', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MOD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MPD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MQD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MRD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MSD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MTD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MUD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MVD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MWD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MXD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MYD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MZD', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M0E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M1E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M2E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M3E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M4E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M5E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M6E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M7E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M8E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9M9E', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MAE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MBE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MCE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MDE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MEE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MFE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MGE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MHE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MIE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MJE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MKE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MLE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MME', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MNE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MOE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MPE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MQE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MRE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MSE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MTE', 'FINST-A17661C1ROVU5VS2D1T5Z8DGX2WU2HL1IGX9MUE']
2025-04-26 08:03:03,094 - INFO - 批量插入日度响应状态码: 200
2025-04-26 08:03:03,094 - INFO - 批量插入日度表单数据成功，批次 2，共 100 条记录
2025-04-26 08:03:03,094 - INFO - 成功插入的数据ID: ['FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M46', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M56', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M66', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M76', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M86', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M96', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MA6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MB6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MC6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MD6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9ME6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MF6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MG6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MH6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MI6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MJ6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MK6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9ML6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MM6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MN6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MO6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MP6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MQ6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MR6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MS6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MT6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MU6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MV6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MW6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MX6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MY6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MZ6', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M07', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M17', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M27', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M37', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M47', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M57', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M67', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M77', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M87', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M97', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MA7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MB7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MC7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MD7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9ME7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MF7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MG7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MH7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MI7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MJ7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MK7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9ML7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MM7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MN7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MO7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MP7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MQ7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MR7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MS7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MT7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MU7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MV7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MW7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MX7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MY7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9MZ7', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M08', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M18', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M28', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M38', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M48', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2L84IGX9M58', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9M68', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9M78', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9M88', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9M98', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MA8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MB8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MC8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MD8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9ME8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MF8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MG8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MH8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MI8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MJ8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MK8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9ML8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MM8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MN8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MO8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MP8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MQ8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MR8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MS8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MT8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MU8', 'FINST-KLF66WC191WU5OWBEVLEO5LHDR2B2M84IGX9MV8']
2025-04-26 08:03:06,644 - INFO - 批量插入日度响应状态码: 200
2025-04-26 08:03:06,644 - INFO - 批量插入日度表单数据成功，批次 3，共 100 条记录
2025-04-26 08:03:06,644 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MN8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MO8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MP8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MQ8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MR8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MS8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MT8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MU8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MV8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MW8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MX8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MY8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MZ8', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M09', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M19', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M29', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M39', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M49', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M59', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M69', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M79', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M89', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9M99', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MA9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MB9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MC9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MD9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9ME9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MF9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MG9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MH9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MI9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MJ9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MK9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9ML9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MM9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MN9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MO9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MP9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MQ9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MR9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MS9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MT9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MU9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MV9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MW9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1TY6IGX9MX9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MY9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MZ9', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M0A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M1A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M2A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M3A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M4A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M5A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M6A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M7A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M8A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M9A', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MAA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MBA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MCA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MDA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MEA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MFA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MGA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MHA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MIA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MJA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MKA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MLA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MMA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MNA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MOA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MPA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MQA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MRA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MSA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MTA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MUA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MVA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MWA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MXA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MYA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MZA', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M0B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M1B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M2B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M3B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M4B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M5B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M6B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M7B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M8B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9M9B', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MAB', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MBB', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MCB', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MDB', 'FINST-KLF66WC1OWVUICCYA7KK4DMUR2UZ1UY6IGX9MEB']
2025-04-26 08:03:10,210 - INFO - 批量插入日度响应状态码: 200
2025-04-26 08:03:10,210 - INFO - 批量插入日度表单数据成功，批次 4，共 100 条记录
2025-04-26 08:03:10,210 - INFO - 成功插入的数据ID: ['FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MZ8', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M09', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M19', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M29', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M39', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M49', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M59', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M69', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M79', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M89', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M99', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MA9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MB9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MC9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MD9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9ME9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MF9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MG9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MH9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MI9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MJ9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MK9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9ML9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MM9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MN9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MO9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MP9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MQ9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MR9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MS9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MT9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MU9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MV9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MW9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MX9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MY9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MZ9', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M0A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M1A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M2A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M3A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M4A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M5A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M6A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M7A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M8A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9M9A', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MAA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MBA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MCA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MDA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MEA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MFA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MGA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MHA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MIA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MJA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MKA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MLA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MMA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MNA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MOA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MPA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3VP9IGX9MQA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MRA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MSA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MTA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MUA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MVA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MWA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MXA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MYA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MZA', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M0B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M1B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M2B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M3B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M4B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M5B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M6B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M7B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M8B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9M9B', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MAB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MBB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MCB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MDB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MEB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MFB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MGB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MHB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MIB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MJB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MKB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MLB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MMB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MNB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MOB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MPB', 'FINST-VME66K81HBWUHSNVBB9JJDYDCT2A3WP9IGX9MQB']
2025-04-26 08:03:13,604 - INFO - 批量插入日度响应状态码: 200
2025-04-26 08:03:13,604 - INFO - 批量插入日度表单数据成功，批次 5，共 55 条记录
2025-04-26 08:03:13,604 - INFO - 成功插入的数据ID: ['FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9MK9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9ML9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9MM9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9MN9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9MO9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9MP9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2UBCIGX9MQ9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MR9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MS9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MT9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MU9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MV9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MW9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MX9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MY9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MZ9', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M0A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M1A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M2A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M3A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M4A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M5A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M6A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M7A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M8A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9M9A', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MAA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MBA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MCA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MDA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MEA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MFA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MGA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MHA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MIA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MJA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MKA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MLA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MMA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MNA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MOA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MPA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MQA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MRA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MSA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MTA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MUA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2VBCIGX9MVA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9MWA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9MXA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9MYA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9MZA', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9M0B', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9M1B', 'FINST-8LC66GC1OLVUC1VW6KA2RAWJRH2W2WBCIGX9M2B']
2025-04-26 08:03:16,622 - INFO - 批量插入日销售数据完成，共 455 条记录
2025-04-26 08:03:16,622 - INFO - 日销售数据同步完成！更新: 9 条，插入: 455 条，错误: 0 条，跳过: 12843 条
2025-04-26 08:03:16,622 - INFO - 正在获取宜搭月销售表单数据...
2025-04-26 08:03:16,622 - INFO - Request Parameters - Page 1:
2025-04-26 08:03:16,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:16,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:17,279 - INFO - Response - Page 1:
2025-04-26 08:03:17,279 - INFO - 第 1 页获取到 100 条记录
2025-04-26 08:03:17,279 - INFO - Request Parameters - Page 2:
2025-04-26 08:03:17,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:17,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:17,936 - INFO - Response - Page 2:
2025-04-26 08:03:17,936 - INFO - 第 2 页获取到 100 条记录
2025-04-26 08:03:17,936 - INFO - Request Parameters - Page 3:
2025-04-26 08:03:17,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:17,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:18,452 - INFO - Response - Page 3:
2025-04-26 08:03:18,452 - INFO - 第 3 页获取到 100 条记录
2025-04-26 08:03:18,452 - INFO - Request Parameters - Page 4:
2025-04-26 08:03:18,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:18,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:18,999 - INFO - Response - Page 4:
2025-04-26 08:03:18,999 - INFO - 第 4 页获取到 100 条记录
2025-04-26 08:03:18,999 - INFO - Request Parameters - Page 5:
2025-04-26 08:03:18,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:18,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:19,484 - INFO - Response - Page 5:
2025-04-26 08:03:19,484 - INFO - 第 5 页获取到 100 条记录
2025-04-26 08:03:19,484 - INFO - Request Parameters - Page 6:
2025-04-26 08:03:19,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:19,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:20,063 - INFO - Response - Page 6:
2025-04-26 08:03:20,063 - INFO - 第 6 页获取到 100 条记录
2025-04-26 08:03:20,063 - INFO - Request Parameters - Page 7:
2025-04-26 08:03:20,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:20,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:20,563 - INFO - Response - Page 7:
2025-04-26 08:03:20,563 - INFO - 第 7 页获取到 100 条记录
2025-04-26 08:03:20,563 - INFO - Request Parameters - Page 8:
2025-04-26 08:03:20,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:20,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:21,079 - INFO - Response - Page 8:
2025-04-26 08:03:21,079 - INFO - 第 8 页获取到 100 条记录
2025-04-26 08:03:21,079 - INFO - Request Parameters - Page 9:
2025-04-26 08:03:21,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:21,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:21,611 - INFO - Response - Page 9:
2025-04-26 08:03:21,627 - INFO - 第 9 页获取到 100 条记录
2025-04-26 08:03:21,627 - INFO - Request Parameters - Page 10:
2025-04-26 08:03:21,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 08:03:21,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1711900800000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 08:03:22,018 - INFO - Response - Page 10:
2025-04-26 08:03:22,018 - INFO - 第 10 页获取到 63 条记录
2025-04-26 08:03:22,018 - INFO - 查询完成，共获取到 963 条记录
2025-04-26 08:03:22,018 - INFO - 成功获取宜搭月销售表单数据，共 963 条记录
2025-04-26 08:03:22,018 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-04-26 08:03:22,018 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-26 08:03:22,033 - INFO - 成功获取SQLite月度汇总数据，共 963 条记录
2025-04-26 08:03:22,080 - INFO - 成功创建宜搭月销售数据索引，共 963 条记录
2025-04-26 08:03:22,565 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M3B
2025-04-26 08:03:22,565 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 192130.49, 'new_value': 202467.86000000002}, {'field': 'dailyBillAmount', 'old_value': 192130.49, 'new_value': 202467.86000000002}, {'field': 'amount', 'old_value': 7420.62, 'new_value': 7943.5199999999995}, {'field': 'count', 'old_value': 96, 'new_value': 101}, {'field': 'onlineAmount', 'old_value': 7631.0, 'new_value': 8153.9}, {'field': 'onlineCount', 'old_value': 96, 'new_value': 101}]
2025-04-26 08:03:22,987 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M4B
2025-04-26 08:03:22,987 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 292982.76, 'new_value': 308919.0}, {'field': 'dailyBillAmount', 'old_value': 292982.76, 'new_value': 308919.0}, {'field': 'amount', 'old_value': 136277.8, 'new_value': 143895.9}, {'field': 'count', 'old_value': 1016, 'new_value': 1078}, {'field': 'instoreAmount', 'old_value': 75088.8, 'new_value': 79456.6}, {'field': 'instoreCount', 'old_value': 467, 'new_value': 501}, {'field': 'onlineAmount', 'old_value': 61285.0, 'new_value': 64535.3}, {'field': 'onlineCount', 'old_value': 549, 'new_value': 577}]
2025-04-26 08:03:23,472 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M5B
2025-04-26 08:03:23,472 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 333506.66000000003, 'new_value': 343505.97000000003}, {'field': 'dailyBillAmount', 'old_value': 333506.66000000003, 'new_value': 343505.97000000003}, {'field': 'amount', 'old_value': 338875.07, 'new_value': 349098.5}, {'field': 'count', 'old_value': 2275, 'new_value': 2375}, {'field': 'instoreAmount', 'old_value': 316699.3, 'new_value': 325449.3}, {'field': 'instoreCount', 'old_value': 1975, 'new_value': 2048}, {'field': 'onlineAmount', 'old_value': 22431.63, 'new_value': 23905.06}, {'field': 'onlineCount', 'old_value': 300, 'new_value': 327}]
2025-04-26 08:03:23,910 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M6B
2025-04-26 08:03:23,910 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 452469.83999999997, 'new_value': 480785.77999999997}, {'field': 'dailyBillAmount', 'old_value': 452469.83999999997, 'new_value': 480785.77999999997}, {'field': 'amount', 'old_value': 461005.39999999997, 'new_value': 481268.39999999997}, {'field': 'count', 'old_value': 2189, 'new_value': 2288}, {'field': 'instoreAmount', 'old_value': 461005.39999999997, 'new_value': 481268.39999999997}, {'field': 'instoreCount', 'old_value': 2189, 'new_value': 2288}]
2025-04-26 08:03:24,426 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M7B
2025-04-26 08:03:24,426 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 78460.97, 'new_value': 89102.27}, {'field': 'dailyBillAmount', 'old_value': 78460.97, 'new_value': 89102.27}, {'field': 'amount', 'old_value': 101026.7, 'new_value': 122928.7}, {'field': 'count', 'old_value': 383, 'new_value': 446}, {'field': 'instoreAmount', 'old_value': 101382.7, 'new_value': 123284.7}, {'field': 'instoreCount', 'old_value': 383, 'new_value': 446}]
2025-04-26 08:03:24,879 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M8B
2025-04-26 08:03:24,879 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 74081.8, 'new_value': 77772.3}, {'field': 'dailyBillAmount', 'old_value': 74081.8, 'new_value': 77772.3}, {'field': 'amount', 'old_value': 91863.69, 'new_value': 98134.99}, {'field': 'count', 'old_value': 323, 'new_value': 334}, {'field': 'instoreAmount', 'old_value': 51390.3, 'new_value': 56308.3}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 43317.09, 'new_value': 44670.39}, {'field': 'onlineCount', 'old_value': 280, 'new_value': 289}]
2025-04-26 08:03:25,317 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M9B
2025-04-26 08:03:25,317 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 43922.2, 'new_value': 60462.2}, {'field': 'amount', 'old_value': 43922.2, 'new_value': 60462.2}, {'field': 'count', 'old_value': 98, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 43922.2, 'new_value': 60462.2}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 104}]
2025-04-26 08:03:25,802 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MAB
2025-04-26 08:03:25,802 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 640264.48, 'new_value': 669147.98}, {'field': 'dailyBillAmount', 'old_value': 640264.48, 'new_value': 669147.98}, {'field': 'amount', 'old_value': 660113.54, 'new_value': 691011.11}, {'field': 'count', 'old_value': 5646, 'new_value': 5924}, {'field': 'instoreAmount', 'old_value': 490954.89, 'new_value': 511523.64}, {'field': 'instoreCount', 'old_value': 2175, 'new_value': 2266}, {'field': 'onlineAmount', 'old_value': 172995.64, 'new_value': 184043.76}, {'field': 'onlineCount', 'old_value': 3471, 'new_value': 3658}]
2025-04-26 08:03:26,256 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MBB
2025-04-26 08:03:26,256 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1HRQIR9VACRH722I1UUTD5AEGF001EGK_2025-04, 变更字段: [{'field': 'amount', 'old_value': 97610.96, 'new_value': 101667.54}, {'field': 'count', 'old_value': 3008, 'new_value': 3116}, {'field': 'instoreAmount', 'old_value': 99271.86, 'new_value': 103328.44}, {'field': 'instoreCount', 'old_value': 3008, 'new_value': 3116}]
2025-04-26 08:03:26,741 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MCB
2025-04-26 08:03:26,741 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 52539.39, 'new_value': 67649.87}, {'field': 'amount', 'old_value': 52539.39, 'new_value': 67649.87}, {'field': 'count', 'old_value': 2023, 'new_value': 2510}, {'field': 'onlineAmount', 'old_value': 54023.71, 'new_value': 69360.28}, {'field': 'onlineCount', 'old_value': 2023, 'new_value': 2510}]
2025-04-26 08:03:27,257 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MDB
2025-04-26 08:03:27,257 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 718614.71, 'new_value': 748387.8099999999}, {'field': 'dailyBillAmount', 'old_value': 718614.71, 'new_value': 748387.8099999999}, {'field': 'amount', 'old_value': 305765.09, 'new_value': 314493.09}, {'field': 'count', 'old_value': 1477, 'new_value': 1524}, {'field': 'instoreAmount', 'old_value': 305587.57, 'new_value': 314315.57}, {'field': 'instoreCount', 'old_value': 1475, 'new_value': 1522}]
2025-04-26 08:03:27,679 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MEB
2025-04-26 08:03:27,679 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 696275.6, 'new_value': 719175.87}, {'field': 'dailyBillAmount', 'old_value': 696275.6, 'new_value': 719175.87}, {'field': 'amount', 'old_value': 40482.37, 'new_value': 40934.37}, {'field': 'count', 'old_value': 24, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 40482.37, 'new_value': 40934.37}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 25}]
2025-04-26 08:03:28,085 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MFB
2025-04-26 08:03:28,085 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-04, 变更字段: [{'field': 'amount', 'old_value': 69763.0, 'new_value': 76289.0}, {'field': 'count', 'old_value': 232, 'new_value': 258}, {'field': 'instoreAmount', 'old_value': 69763.0, 'new_value': 76289.0}, {'field': 'instoreCount', 'old_value': 232, 'new_value': 258}]
2025-04-26 08:03:28,555 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MHB
2025-04-26 08:03:28,555 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 1311915.84, 'new_value': 1329201.58}, {'field': 'dailyBillAmount', 'old_value': 1176066.26, 'new_value': 1193352.0}, {'field': 'amount', 'old_value': 1138783.83, 'new_value': 1133158.21}, {'field': 'count', 'old_value': 2151, 'new_value': 2184}, {'field': 'instoreAmount', 'old_value': 1168955.45, 'new_value': 1185934.33}, {'field': 'instoreCount', 'old_value': 2148, 'new_value': 2181}]
2025-04-26 08:03:29,008 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MIB
2025-04-26 08:03:29,008 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 331560.0, 'new_value': 353808.0}, {'field': 'amount', 'old_value': 331560.0, 'new_value': 353808.0}, {'field': 'count', 'old_value': 1192, 'new_value': 1277}, {'field': 'instoreAmount', 'old_value': 331560.0, 'new_value': 353808.0}, {'field': 'instoreCount', 'old_value': 1192, 'new_value': 1277}]
2025-04-26 08:03:29,493 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MJB
2025-04-26 08:03:29,493 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 377454.48, 'new_value': 393449.9}, {'field': 'dailyBillAmount', 'old_value': 239818.18, 'new_value': 254837.8}, {'field': 'amount', 'old_value': 377454.48, 'new_value': 393449.9}, {'field': 'count', 'old_value': 1619, 'new_value': 1687}, {'field': 'instoreAmount', 'old_value': 377454.48, 'new_value': 393449.9}, {'field': 'instoreCount', 'old_value': 1619, 'new_value': 1687}]
2025-04-26 08:03:29,947 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MKB
2025-04-26 08:03:29,947 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 212068.28, 'new_value': 223485.68}, {'field': 'dailyBillAmount', 'old_value': 212068.28, 'new_value': 223485.68}, {'field': 'amount', 'old_value': 15867.56, 'new_value': 16316.56}, {'field': 'count', 'old_value': 69, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 17094.36, 'new_value': 17543.36}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 73}]
2025-04-26 08:03:30,478 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MLB
2025-04-26 08:03:30,478 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 92156.23, 'new_value': 95400.58}, {'field': 'dailyBillAmount', 'old_value': 90791.73, 'new_value': 94036.08}, {'field': 'amount', 'old_value': 71283.26, 'new_value': 73539.61}, {'field': 'count', 'old_value': 1050, 'new_value': 1082}, {'field': 'instoreAmount', 'old_value': 73062.86, 'new_value': 75319.21}, {'field': 'instoreCount', 'old_value': 1050, 'new_value': 1082}]
2025-04-26 08:03:30,932 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MMB
2025-04-26 08:03:30,932 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 145819.74, 'new_value': 150879.23}, {'field': 'dailyBillAmount', 'old_value': 103664.55, 'new_value': 108142.14}, {'field': 'amount', 'old_value': 145819.74, 'new_value': 150878.49}, {'field': 'count', 'old_value': 4837, 'new_value': 5016}, {'field': 'instoreAmount', 'old_value': 130030.85, 'new_value': 134469.71}, {'field': 'instoreCount', 'old_value': 4495, 'new_value': 4657}, {'field': 'onlineAmount', 'old_value': 15788.89, 'new_value': 16409.52}, {'field': 'onlineCount', 'old_value': 342, 'new_value': 359}]
2025-04-26 08:03:31,385 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MNB
2025-04-26 08:03:31,385 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 273588.0, 'new_value': 280795.0}, {'field': 'dailyBillAmount', 'old_value': 267017.0, 'new_value': 273755.0}, {'field': 'amount', 'old_value': 206853.33, 'new_value': 214060.33}, {'field': 'count', 'old_value': 265, 'new_value': 272}, {'field': 'instoreAmount', 'old_value': 205831.0, 'new_value': 213038.0}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 271}]
2025-04-26 08:03:31,823 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MOB
2025-04-26 08:03:31,823 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 520577.49, 'new_value': 532269.39}, {'field': 'dailyBillAmount', 'old_value': 180447.06, 'new_value': 192138.96}, {'field': 'amount', 'old_value': 513765.51, 'new_value': 525457.41}, {'field': 'count', 'old_value': 397, 'new_value': 409}, {'field': 'instoreAmount', 'old_value': 513765.51, 'new_value': 525457.41}, {'field': 'instoreCount', 'old_value': 397, 'new_value': 409}]
2025-04-26 08:03:32,308 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MPB
2025-04-26 08:03:32,308 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-04, 变更字段: [{'field': 'amount', 'old_value': 38517.0, 'new_value': 41579.0}, {'field': 'count', 'old_value': 49, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 38517.0, 'new_value': 41579.0}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 51}]
2025-04-26 08:03:32,824 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MQB
2025-04-26 08:03:32,824 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 130576.1, 'new_value': 139501.3}, {'field': 'dailyBillAmount', 'old_value': 130576.1, 'new_value': 139501.3}, {'field': 'amount', 'old_value': 109841.3, 'new_value': 115429.8}, {'field': 'count', 'old_value': 286, 'new_value': 300}, {'field': 'instoreAmount', 'old_value': 109841.3, 'new_value': 115429.8}, {'field': 'instoreCount', 'old_value': 286, 'new_value': 300}]
2025-04-26 08:03:33,262 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MRB
2025-04-26 08:03:33,262 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 164828.66, 'new_value': 173512.66}, {'field': 'amount', 'old_value': 164828.66, 'new_value': 173512.66}, {'field': 'count', 'old_value': 224, 'new_value': 233}, {'field': 'instoreAmount', 'old_value': 168368.66, 'new_value': 177052.66}, {'field': 'instoreCount', 'old_value': 224, 'new_value': 233}]
2025-04-26 08:03:33,731 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MSB
2025-04-26 08:03:33,731 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 228411.0, 'new_value': 240656.3}, {'field': 'dailyBillAmount', 'old_value': 228411.0, 'new_value': 240656.3}, {'field': 'amount', 'old_value': 252794.3, 'new_value': 265681.3}, {'field': 'count', 'old_value': 1636, 'new_value': 1725}, {'field': 'instoreAmount', 'old_value': 254830.3, 'new_value': 268012.3}, {'field': 'instoreCount', 'old_value': 1636, 'new_value': 1725}]
2025-04-26 08:03:34,138 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MTB
2025-04-26 08:03:34,138 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 165431.79, 'new_value': 173299.53}, {'field': 'dailyBillAmount', 'old_value': 165431.79, 'new_value': 173299.53}, {'field': 'amount', 'old_value': 45231.58, 'new_value': 46102.41}, {'field': 'count', 'old_value': 3414, 'new_value': 3473}, {'field': 'instoreAmount', 'old_value': 48562.14, 'new_value': 49441.38}, {'field': 'instoreCount', 'old_value': 3414, 'new_value': 3473}]
2025-04-26 08:03:34,576 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MUB
2025-04-26 08:03:34,576 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 216576.57, 'new_value': 226251.47}, {'field': 'amount', 'old_value': 216576.57, 'new_value': 226251.37}, {'field': 'count', 'old_value': 6104, 'new_value': 6393}, {'field': 'instoreAmount', 'old_value': 213776.11, 'new_value': 223136.01}, {'field': 'instoreCount', 'old_value': 5949, 'new_value': 6230}, {'field': 'onlineAmount', 'old_value': 6841.0, 'new_value': 7543.3}, {'field': 'onlineCount', 'old_value': 155, 'new_value': 163}]
2025-04-26 08:03:35,045 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MVB
2025-04-26 08:03:35,045 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 206466.22, 'new_value': 219831.92}, {'field': 'dailyBillAmount', 'old_value': 202318.72, 'new_value': 214870.42}, {'field': 'amount', 'old_value': 205501.22, 'new_value': 218866.92}, {'field': 'count', 'old_value': 604, 'new_value': 643}, {'field': 'instoreAmount', 'old_value': 205501.22, 'new_value': 218866.92}, {'field': 'instoreCount', 'old_value': 604, 'new_value': 643}]
2025-04-26 08:03:35,483 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MWB
2025-04-26 08:03:35,483 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 180782.6, 'new_value': 188476.7}, {'field': 'dailyBillAmount', 'old_value': 180782.6, 'new_value': 188476.7}, {'field': 'amount', 'old_value': 43887.6, 'new_value': 46155.6}, {'field': 'count', 'old_value': 113, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 43887.6, 'new_value': 46155.6}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 119}]
2025-04-26 08:03:35,921 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MXB
2025-04-26 08:03:35,921 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 465062.85, 'new_value': 487752.03}, {'field': 'dailyBillAmount', 'old_value': 465062.85, 'new_value': 487752.03}, {'field': 'amount', 'old_value': 233674.55, 'new_value': 241401.25}, {'field': 'count', 'old_value': 1020, 'new_value': 1051}, {'field': 'instoreAmount', 'old_value': 233674.55, 'new_value': 241401.25}, {'field': 'instoreCount', 'old_value': 1020, 'new_value': 1051}]
2025-04-26 08:03:36,437 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MYB
2025-04-26 08:03:36,437 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 17414.0, 'new_value': 17709.0}, {'field': 'amount', 'old_value': 17414.0, 'new_value': 17709.0}, {'field': 'count', 'old_value': 17, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 17414.0, 'new_value': 17709.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 18}]
2025-04-26 08:03:36,859 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MZB
2025-04-26 08:03:36,859 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 92389.46, 'new_value': 96704.63}, {'field': 'dailyBillAmount', 'old_value': 92389.46, 'new_value': 96704.63}, {'field': 'amount', 'old_value': 27392.23, 'new_value': 28701.67}, {'field': 'count', 'old_value': 1101, 'new_value': 1156}, {'field': 'instoreAmount', 'old_value': 6214.8, 'new_value': 6443.1}, {'field': 'instoreCount', 'old_value': 165, 'new_value': 171}, {'field': 'onlineAmount', 'old_value': 21458.75, 'new_value': 22539.9}, {'field': 'onlineCount', 'old_value': 936, 'new_value': 985}]
2025-04-26 08:03:37,328 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M0C
2025-04-26 08:03:37,328 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 135154.33000000002, 'new_value': 140857.43}, {'field': 'dailyBillAmount', 'old_value': 135154.33000000002, 'new_value': 140857.43}, {'field': 'amount', 'old_value': 25130.989999999998, 'new_value': 25948.67}, {'field': 'count', 'old_value': 566, 'new_value': 584}, {'field': 'instoreAmount', 'old_value': 20883.67, 'new_value': 21593.58}, {'field': 'instoreCount', 'old_value': 483, 'new_value': 499}, {'field': 'onlineAmount', 'old_value': 4249.62, 'new_value': 4357.39}, {'field': 'onlineCount', 'old_value': 83, 'new_value': 85}]
2025-04-26 08:03:37,782 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M1C
2025-04-26 08:03:37,782 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 15010.6, 'new_value': 15728.6}, {'field': 'dailyBillAmount', 'old_value': 15010.6, 'new_value': 15728.6}, {'field': 'amount', 'old_value': 11643.3, 'new_value': 12361.3}, {'field': 'count', 'old_value': 423, 'new_value': 438}, {'field': 'instoreAmount', 'old_value': 12207.5, 'new_value': 12925.5}, {'field': 'instoreCount', 'old_value': 423, 'new_value': 438}]
2025-04-26 08:03:38,220 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M2C
2025-04-26 08:03:38,220 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-04, 变更字段: [{'field': 'amount', 'old_value': 28797.45, 'new_value': 30040.68}, {'field': 'count', 'old_value': 1307, 'new_value': 1369}, {'field': 'instoreAmount', 'old_value': 13447.39, 'new_value': 14152.66}, {'field': 'instoreCount', 'old_value': 625, 'new_value': 649}, {'field': 'onlineAmount', 'old_value': 16109.41, 'new_value': 16647.37}, {'field': 'onlineCount', 'old_value': 682, 'new_value': 720}]
2025-04-26 08:03:38,720 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M4C
2025-04-26 08:03:38,720 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 302860.56, 'new_value': 317181.5}, {'field': 'dailyBillAmount', 'old_value': 302860.56, 'new_value': 317181.5}, {'field': 'amount', 'old_value': 127583.79, 'new_value': 135121.29}, {'field': 'count', 'old_value': 530, 'new_value': 559}, {'field': 'instoreAmount', 'old_value': 129869.36, 'new_value': 137406.86}, {'field': 'instoreCount', 'old_value': 530, 'new_value': 559}]
2025-04-26 08:03:39,189 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M5C
2025-04-26 08:03:39,189 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 15677.6, 'new_value': 16158.1}, {'field': 'amount', 'old_value': 15677.6, 'new_value': 16158.1}, {'field': 'count', 'old_value': 155, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 16366.34, 'new_value': 16846.84}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 160}]
2025-04-26 08:03:39,721 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M6C
2025-04-26 08:03:39,737 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 178213.9, 'new_value': 187996.04}, {'field': 'dailyBillAmount', 'old_value': 178213.9, 'new_value': 187996.04}, {'field': 'amount', 'old_value': 82734.59, 'new_value': 87120.96}, {'field': 'count', 'old_value': 3226, 'new_value': 3398}, {'field': 'instoreAmount', 'old_value': 84439.89, 'new_value': 88914.16}, {'field': 'instoreCount', 'old_value': 3226, 'new_value': 3398}]
2025-04-26 08:03:40,206 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M7C
2025-04-26 08:03:40,206 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 535902.2, 'new_value': 552399.2}, {'field': 'dailyBillAmount', 'old_value': 535902.2, 'new_value': 552399.2}, {'field': 'amount', 'old_value': 512748.2, 'new_value': 529245.2}, {'field': 'count', 'old_value': 684, 'new_value': 706}, {'field': 'instoreAmount', 'old_value': 512748.2, 'new_value': 529245.2}, {'field': 'instoreCount', 'old_value': 684, 'new_value': 706}]
2025-04-26 08:03:40,581 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M8C
2025-04-26 08:03:40,581 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 150098.94, 'new_value': 156800.34}, {'field': 'dailyBillAmount', 'old_value': 150098.94, 'new_value': 156800.34}, {'field': 'amount', 'old_value': 95414.31999999999, 'new_value': 99196.31999999999}, {'field': 'count', 'old_value': 278, 'new_value': 286}, {'field': 'instoreAmount', 'old_value': 95992.31999999999, 'new_value': 99774.31999999999}, {'field': 'instoreCount', 'old_value': 278, 'new_value': 286}]
2025-04-26 08:03:41,066 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9M9C
2025-04-26 08:03:41,066 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 59418.0, 'new_value': 63452.0}, {'field': 'dailyBillAmount', 'old_value': 59171.0, 'new_value': 63205.0}, {'field': 'amount', 'old_value': 58437.0, 'new_value': 62471.0}, {'field': 'count', 'old_value': 1109, 'new_value': 1177}, {'field': 'instoreAmount', 'old_value': 58501.0, 'new_value': 62535.0}, {'field': 'instoreCount', 'old_value': 1109, 'new_value': 1177}]
2025-04-26 08:03:41,426 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MAC
2025-04-26 08:03:41,426 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 84366.87, 'new_value': 88428.98}, {'field': 'dailyBillAmount', 'old_value': 84366.87, 'new_value': 88428.98}, {'field': 'amount', 'old_value': 89523.58, 'new_value': 93754.53}, {'field': 'count', 'old_value': 4465, 'new_value': 4670}, {'field': 'instoreAmount', 'old_value': 51411.6, 'new_value': 53809.95}, {'field': 'instoreCount', 'old_value': 2806, 'new_value': 2940}, {'field': 'onlineAmount', 'old_value': 39460.36, 'new_value': 41349.56}, {'field': 'onlineCount', 'old_value': 1659, 'new_value': 1730}]
2025-04-26 08:03:41,910 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MBC
2025-04-26 08:03:41,910 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 34724.1, 'new_value': 36381.7}, {'field': 'dailyBillAmount', 'old_value': 34724.1, 'new_value': 36381.7}, {'field': 'amount', 'old_value': 49786.48, 'new_value': 51925.64}, {'field': 'count', 'old_value': 1401, 'new_value': 1463}, {'field': 'instoreAmount', 'old_value': 44802.1, 'new_value': 46706.1}, {'field': 'instoreCount', 'old_value': 1221, 'new_value': 1274}, {'field': 'onlineAmount', 'old_value': 5182.25, 'new_value': 5417.41}, {'field': 'onlineCount', 'old_value': 180, 'new_value': 189}]
2025-04-26 08:03:42,380 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MCC
2025-04-26 08:03:42,380 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 70920.74, 'new_value': 74418.57}, {'field': 'dailyBillAmount', 'old_value': 70920.74, 'new_value': 74418.57}, {'field': 'amount', 'old_value': 70971.8, 'new_value': 74484.25}, {'field': 'count', 'old_value': 2815, 'new_value': 2946}, {'field': 'instoreAmount', 'old_value': 46634.08, 'new_value': 48808.08}, {'field': 'instoreCount', 'old_value': 1709, 'new_value': 1781}, {'field': 'onlineAmount', 'old_value': 24940.86, 'new_value': 26279.31}, {'field': 'onlineCount', 'old_value': 1106, 'new_value': 1165}]
2025-04-26 08:03:42,739 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MDC
2025-04-26 08:03:42,739 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-04, 变更字段: [{'field': 'amount', 'old_value': 68544.94, 'new_value': 70900.25}, {'field': 'count', 'old_value': 775, 'new_value': 809}, {'field': 'instoreAmount', 'old_value': 61225.63, 'new_value': 63580.94}, {'field': 'instoreCount', 'old_value': 699, 'new_value': 733}]
2025-04-26 08:03:43,334 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F24M0EKW9MEC
2025-04-26 08:03:43,334 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 59885.8, 'new_value': 63094.7}, {'field': 'amount', 'old_value': 59885.8, 'new_value': 63094.7}, {'field': 'count', 'old_value': 1563, 'new_value': 1624}, {'field': 'instoreAmount', 'old_value': 60346.8, 'new_value': 63972.7}, {'field': 'instoreCount', 'old_value': 1563, 'new_value': 1624}]
2025-04-26 08:03:43,803 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MFC
2025-04-26 08:03:43,803 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 322636.03, 'new_value': 340853.28}, {'field': 'dailyBillAmount', 'old_value': 322636.03, 'new_value': 340853.28}, {'field': 'amount', 'old_value': 121051.3, 'new_value': 123823.3}, {'field': 'count', 'old_value': 430, 'new_value': 445}, {'field': 'instoreAmount', 'old_value': 121051.3, 'new_value': 123823.3}, {'field': 'instoreCount', 'old_value': 430, 'new_value': 445}]
2025-04-26 08:03:44,225 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MGC
2025-04-26 08:03:44,225 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 80334.31, 'new_value': 85951.41}, {'field': 'dailyBillAmount', 'old_value': 80334.31, 'new_value': 85951.41}, {'field': 'amount', 'old_value': 78078.31, 'new_value': 83043.41}, {'field': 'count', 'old_value': 325, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 78078.31, 'new_value': 83043.41}, {'field': 'instoreCount', 'old_value': 325, 'new_value': 339}]
2025-04-26 08:03:44,647 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MHC
2025-04-26 08:03:44,647 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 44406.0, 'new_value': 48505.0}, {'field': 'dailyBillAmount', 'old_value': 44406.0, 'new_value': 48505.0}, {'field': 'amount', 'old_value': 53588.0, 'new_value': 57687.0}, {'field': 'count', 'old_value': 112, 'new_value': 124}, {'field': 'instoreAmount', 'old_value': 57054.0, 'new_value': 61442.0}, {'field': 'instoreCount', 'old_value': 112, 'new_value': 124}]
2025-04-26 08:03:45,148 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MIC
2025-04-26 08:03:45,148 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 116631.59999999999, 'new_value': 120046.2}, {'field': 'dailyBillAmount', 'old_value': 113850.5, 'new_value': 117265.09999999999}, {'field': 'amount', 'old_value': 116631.6, 'new_value': 120046.2}, {'field': 'count', 'old_value': 331, 'new_value': 336}, {'field': 'instoreAmount', 'old_value': 134792.35, 'new_value': 138206.95}, {'field': 'instoreCount', 'old_value': 331, 'new_value': 336}]
2025-04-26 08:03:45,586 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MJC
2025-04-26 08:03:45,586 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 109137.88, 'new_value': 114011.85}, {'field': 'dailyBillAmount', 'old_value': 109137.88, 'new_value': 114011.85}, {'field': 'amount', 'old_value': 62538.65, 'new_value': 65059.020000000004}, {'field': 'count', 'old_value': 1602, 'new_value': 1662}, {'field': 'instoreAmount', 'old_value': 52801.26, 'new_value': 55016.56}, {'field': 'instoreCount', 'old_value': 1353, 'new_value': 1406}, {'field': 'onlineAmount', 'old_value': 10881.49, 'new_value': 11186.56}, {'field': 'onlineCount', 'old_value': 249, 'new_value': 256}]
2025-04-26 08:03:46,086 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MKC
2025-04-26 08:03:46,086 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 177933.48, 'new_value': 185237.13}, {'field': 'dailyBillAmount', 'old_value': 177236.99, 'new_value': 184425.13}, {'field': 'amount', 'old_value': 177933.48, 'new_value': 185237.13}, {'field': 'count', 'old_value': 2215, 'new_value': 2295}, {'field': 'instoreAmount', 'old_value': 171834.57, 'new_value': 178754.57}, {'field': 'instoreCount', 'old_value': 2145, 'new_value': 2222}, {'field': 'onlineAmount', 'old_value': 6139.5599999999995, 'new_value': 6523.21}, {'field': 'onlineCount', 'old_value': 70, 'new_value': 73}]
2025-04-26 08:03:46,524 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MLC
2025-04-26 08:03:46,524 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-04, 变更字段: [{'field': 'amount', 'old_value': 106525.89, 'new_value': 111939.6}, {'field': 'count', 'old_value': 495, 'new_value': 519}, {'field': 'instoreAmount', 'old_value': 103293.05, 'new_value': 108343.76}, {'field': 'instoreCount', 'old_value': 450, 'new_value': 471}, {'field': 'onlineAmount', 'old_value': 3877.84, 'new_value': 4240.84}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 48}]
2025-04-26 08:03:46,962 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MMC
2025-04-26 08:03:46,962 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 241431.55, 'new_value': 249941.05}, {'field': 'dailyBillAmount', 'old_value': 241431.55, 'new_value': 249941.05}, {'field': 'amount', 'old_value': 248260.05, 'new_value': 256526.65}, {'field': 'count', 'old_value': 945, 'new_value': 981}, {'field': 'instoreAmount', 'old_value': 255026.55, 'new_value': 263293.15}, {'field': 'instoreCount', 'old_value': 945, 'new_value': 981}]
2025-04-26 08:03:47,415 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MNC
2025-04-26 08:03:47,415 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 12099.0, 'new_value': 12568.0}, {'field': 'dailyBillAmount', 'old_value': 9707.0, 'new_value': 10176.0}, {'field': 'amount', 'old_value': 11900.0, 'new_value': 12369.0}, {'field': 'count', 'old_value': 44, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 12599.0, 'new_value': 13068.0}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 46}]
2025-04-26 08:03:47,869 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MOC
2025-04-26 08:03:47,869 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-04, 变更字段: [{'field': 'amount', 'old_value': 22993.7, 'new_value': 23123.2}, {'field': 'count', 'old_value': 37, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 23520.5, 'new_value': 23650.0}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}]
2025-04-26 08:03:48,385 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MPC
2025-04-26 08:03:48,385 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 29115.55, 'new_value': 31471.77}, {'field': 'dailyBillAmount', 'old_value': 29115.55, 'new_value': 31471.77}, {'field': 'amount', 'old_value': 36461.01, 'new_value': 39097.95}, {'field': 'count', 'old_value': 258, 'new_value': 275}, {'field': 'instoreAmount', 'old_value': 30323.31, 'new_value': 32682.11}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 167}, {'field': 'onlineAmount', 'old_value': 6177.76, 'new_value': 6455.900000000001}, {'field': 'onlineCount', 'old_value': 104, 'new_value': 108}]
2025-04-26 08:03:48,932 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MQC
2025-04-26 08:03:48,932 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 25995.1, 'new_value': 26745.0}, {'field': 'dailyBillAmount', 'old_value': 25995.1, 'new_value': 26745.0}, {'field': 'amount', 'old_value': 20120.94, 'new_value': 20795.74}, {'field': 'count', 'old_value': 890, 'new_value': 919}, {'field': 'instoreAmount', 'old_value': 20299.1, 'new_value': 20973.9}, {'field': 'instoreCount', 'old_value': 890, 'new_value': 919}]
2025-04-26 08:03:49,355 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MRC
2025-04-26 08:03:49,355 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 58618.96, 'new_value': 61064.24}, {'field': 'amount', 'old_value': 58618.96, 'new_value': 61064.24}, {'field': 'count', 'old_value': 2838, 'new_value': 2954}, {'field': 'instoreAmount', 'old_value': 61355.66, 'new_value': 63930.520000000004}, {'field': 'instoreCount', 'old_value': 2838, 'new_value': 2954}]
2025-04-26 08:03:49,793 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MTC
2025-04-26 08:03:49,793 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 340736.84, 'new_value': 350988.71}, {'field': 'dailyBillAmount', 'old_value': 340736.84, 'new_value': 350988.71}, {'field': 'amount', 'old_value': 277319.67, 'new_value': 286178.28}, {'field': 'count', 'old_value': 3037, 'new_value': 3142}, {'field': 'instoreAmount', 'old_value': 60201.92, 'new_value': 62456.13}, {'field': 'instoreCount', 'old_value': 302, 'new_value': 322}, {'field': 'onlineAmount', 'old_value': 217117.75, 'new_value': 223722.55}, {'field': 'onlineCount', 'old_value': 2735, 'new_value': 2820}]
2025-04-26 08:03:50,246 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MUC
2025-04-26 08:03:50,246 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 151396.7, 'new_value': 161453.6}, {'field': 'dailyBillAmount', 'old_value': 151396.7, 'new_value': 161453.6}, {'field': 'amount', 'old_value': 176479.0, 'new_value': 186737.9}, {'field': 'count', 'old_value': 1118, 'new_value': 1183}, {'field': 'instoreAmount', 'old_value': 176921.0, 'new_value': 187179.9}, {'field': 'instoreCount', 'old_value': 1118, 'new_value': 1183}]
2025-04-26 08:03:50,778 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MWC
2025-04-26 08:03:50,778 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 175672.08, 'new_value': 182881.6}, {'field': 'dailyBillAmount', 'old_value': 175672.08, 'new_value': 182881.6}, {'field': 'amount', 'old_value': 107216.68, 'new_value': 110532.7}, {'field': 'count', 'old_value': 1263, 'new_value': 1298}, {'field': 'instoreAmount', 'old_value': 94603.66, 'new_value': 97726.66}, {'field': 'instoreCount', 'old_value': 813, 'new_value': 838}, {'field': 'onlineAmount', 'old_value': 14735.22, 'new_value': 14928.24}, {'field': 'onlineCount', 'old_value': 450, 'new_value': 460}]
2025-04-26 08:03:51,216 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MXC
2025-04-26 08:03:51,216 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-04, 变更字段: [{'field': 'amount', 'old_value': 4442.05, 'new_value': 4605.95}, {'field': 'count', 'old_value': 38, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 4442.05, 'new_value': 4605.95}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 41}]
2025-04-26 08:03:51,669 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MZC
2025-04-26 08:03:51,669 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 159411.84, 'new_value': 167345.54}, {'field': 'dailyBillAmount', 'old_value': 134611.46, 'new_value': 142545.16}, {'field': 'amount', 'old_value': 159411.84, 'new_value': 167345.54}, {'field': 'count', 'old_value': 664, 'new_value': 699}, {'field': 'instoreAmount', 'old_value': 159411.84, 'new_value': 167345.54}, {'field': 'instoreCount', 'old_value': 664, 'new_value': 699}]
2025-04-26 08:03:52,123 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M1D
2025-04-26 08:03:52,123 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 24555.85, 'new_value': 25442.05}, {'field': 'dailyBillAmount', 'old_value': 24555.85, 'new_value': 25442.05}, {'field': 'amount', 'old_value': 27049.75, 'new_value': 28037.75}, {'field': 'count', 'old_value': 787, 'new_value': 820}, {'field': 'instoreAmount', 'old_value': 27073.75, 'new_value': 28061.75}, {'field': 'instoreCount', 'old_value': 787, 'new_value': 820}]
2025-04-26 08:03:52,639 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M2D
2025-04-26 08:03:52,639 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 311870.3, 'new_value': 322402.3}, {'field': 'amount', 'old_value': 311870.3, 'new_value': 322402.3}, {'field': 'count', 'old_value': 492, 'new_value': 509}, {'field': 'instoreAmount', 'old_value': 311870.3, 'new_value': 322402.3}, {'field': 'instoreCount', 'old_value': 492, 'new_value': 509}]
2025-04-26 08:03:53,124 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M3D
2025-04-26 08:03:53,124 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 35117.57, 'new_value': 36101.87}, {'field': 'amount', 'old_value': 35117.57, 'new_value': 36101.87}, {'field': 'count', 'old_value': 350, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 35117.57, 'new_value': 36101.87}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 360}]
2025-04-26 08:03:53,593 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M4D
2025-04-26 08:03:53,593 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 330162.0, 'new_value': 347688.0}, {'field': 'amount', 'old_value': 330162.0, 'new_value': 347688.0}, {'field': 'count', 'old_value': 66, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 330162.0, 'new_value': 347688.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 69}]
2025-04-26 08:03:54,015 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M6D
2025-04-26 08:03:54,015 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-04, 变更字段: [{'field': 'amount', 'old_value': 42658.6, 'new_value': 43144.3}, {'field': 'count', 'old_value': 476, 'new_value': 485}, {'field': 'instoreAmount', 'old_value': 42658.6, 'new_value': 43144.3}, {'field': 'instoreCount', 'old_value': 476, 'new_value': 485}]
2025-04-26 08:03:54,437 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M7D
2025-04-26 08:03:54,437 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 44970.0, 'new_value': 45449.4}, {'field': 'dailyBillAmount', 'old_value': 44970.0, 'new_value': 45449.4}, {'field': 'amount', 'old_value': 44970.0, 'new_value': 45449.4}, {'field': 'count', 'old_value': 54, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 45758.3, 'new_value': 46237.700000000004}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 55}]
2025-04-26 08:03:54,907 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M8D
2025-04-26 08:03:54,907 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 395480.71, 'new_value': 418450.71}, {'field': 'dailyBillAmount', 'old_value': 395480.71, 'new_value': 418450.71}, {'field': 'amount', 'old_value': 415899.75, 'new_value': 438869.75}, {'field': 'count', 'old_value': 1360, 'new_value': 1432}, {'field': 'instoreAmount', 'old_value': 416525.75, 'new_value': 439495.75}, {'field': 'instoreCount', 'old_value': 1360, 'new_value': 1432}]
2025-04-26 08:03:55,360 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9M9D
2025-04-26 08:03:55,360 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 461094.89, 'new_value': 513798.89}, {'field': 'dailyBillAmount', 'old_value': 461094.89, 'new_value': 513798.89}, {'field': 'amount', 'old_value': 984920.4500000001, 'new_value': 1032069.8200000001}, {'field': 'count', 'old_value': 1233, 'new_value': 1287}, {'field': 'instoreAmount', 'old_value': 984920.4500000001, 'new_value': 1032069.8200000001}, {'field': 'instoreCount', 'old_value': 1233, 'new_value': 1287}]
2025-04-26 08:03:55,876 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MBD
2025-04-26 08:03:55,876 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 156629.1, 'new_value': 163283.3}, {'field': 'dailyBillAmount', 'old_value': 156629.1, 'new_value': 163283.3}, {'field': 'amount', 'old_value': 27855.600000000002, 'new_value': 29835.4}, {'field': 'count', 'old_value': 103, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 27855.600000000002, 'new_value': 29835.4}, {'field': 'instoreCount', 'old_value': 103, 'new_value': 110}]
2025-04-26 08:03:56,345 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MCD
2025-04-26 08:03:56,345 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 124067.95, 'new_value': 129540.12}, {'field': 'amount', 'old_value': 124067.95, 'new_value': 129540.12}, {'field': 'count', 'old_value': 1337, 'new_value': 1401}, {'field': 'instoreAmount', 'old_value': 71866.86, 'new_value': 74379.96}, {'field': 'instoreCount', 'old_value': 708, 'new_value': 736}, {'field': 'onlineAmount', 'old_value': 54687.69, 'new_value': 57675.659999999996}, {'field': 'onlineCount', 'old_value': 629, 'new_value': 665}]
2025-04-26 08:03:56,815 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MED
2025-04-26 08:03:56,815 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 261266.13, 'new_value': 271461.5}, {'field': 'dailyBillAmount', 'old_value': 261266.13, 'new_value': 271461.5}, {'field': 'amount', 'old_value': 34594.31, 'new_value': 36137.07}, {'field': 'count', 'old_value': 950, 'new_value': 992}, {'field': 'instoreAmount', 'old_value': 36934.51, 'new_value': 38510.17}, {'field': 'instoreCount', 'old_value': 950, 'new_value': 992}]
2025-04-26 08:03:57,315 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MFD
2025-04-26 08:03:57,315 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 222980.73, 'new_value': 229886.58000000002}, {'field': 'dailyBillAmount', 'old_value': 222980.73, 'new_value': 229886.58000000002}, {'field': 'amount', 'old_value': 113759.95, 'new_value': 116957.89}, {'field': 'count', 'old_value': 2821, 'new_value': 2913}, {'field': 'instoreAmount', 'old_value': 91009.27, 'new_value': 93380.57}, {'field': 'instoreCount', 'old_value': 2316, 'new_value': 2389}, {'field': 'onlineAmount', 'old_value': 23277.08, 'new_value': 24174.02}, {'field': 'onlineCount', 'old_value': 505, 'new_value': 524}]
2025-04-26 08:03:57,769 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MGD
2025-04-26 08:03:57,769 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 263353.2, 'new_value': 273464.5}, {'field': 'amount', 'old_value': 263353.2, 'new_value': 273464.5}, {'field': 'count', 'old_value': 992, 'new_value': 1024}, {'field': 'instoreAmount', 'old_value': 265384.4, 'new_value': 276760.7}, {'field': 'instoreCount', 'old_value': 992, 'new_value': 1024}]
2025-04-26 08:03:58,222 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MHD
2025-04-26 08:03:58,222 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 438285.67, 'new_value': 455676.37}, {'field': 'dailyBillAmount', 'old_value': 438285.67, 'new_value': 455676.37}, {'field': 'amount', 'old_value': 253496.36, 'new_value': 265545.31}, {'field': 'count', 'old_value': 4771, 'new_value': 4973}, {'field': 'instoreAmount', 'old_value': 235269.79, 'new_value': 246315.45}, {'field': 'instoreCount', 'old_value': 4428, 'new_value': 4613}, {'field': 'onlineAmount', 'old_value': 19418.57, 'new_value': 20441.76}, {'field': 'onlineCount', 'old_value': 343, 'new_value': 360}]
2025-04-26 08:03:58,613 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MID
2025-04-26 08:03:58,613 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 183493.26, 'new_value': 196334.15}, {'field': 'dailyBillAmount', 'old_value': 92255.18, 'new_value': 105096.06999999999}, {'field': 'amount', 'old_value': 114758.15, 'new_value': 118939.75}, {'field': 'count', 'old_value': 3217, 'new_value': 3331}, {'field': 'instoreAmount', 'old_value': 56465.43, 'new_value': 58979.73}, {'field': 'instoreCount', 'old_value': 1533, 'new_value': 1600}, {'field': 'onlineAmount', 'old_value': 58292.72, 'new_value': 59960.020000000004}, {'field': 'onlineCount', 'old_value': 1684, 'new_value': 1731}]
2025-04-26 08:03:59,020 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MJD
2025-04-26 08:03:59,020 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-04, 变更字段: [{'field': 'amount', 'old_value': 360919.75, 'new_value': 363617.85}, {'field': 'count', 'old_value': 5593, 'new_value': 5644}, {'field': 'instoreAmount', 'old_value': 291824.23, 'new_value': 292114.23}, {'field': 'instoreCount', 'old_value': 4244, 'new_value': 4248}, {'field': 'onlineAmount', 'old_value': 80371.82, 'new_value': 83145.92}, {'field': 'onlineCount', 'old_value': 1349, 'new_value': 1396}]
2025-04-26 08:03:59,426 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MKD
2025-04-26 08:03:59,426 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-04, 变更字段: [{'field': 'amount', 'old_value': 174602.02, 'new_value': 182636.04}, {'field': 'count', 'old_value': 11108, 'new_value': 11681}, {'field': 'instoreAmount', 'old_value': 53777.72, 'new_value': 57280.62}, {'field': 'instoreCount', 'old_value': 4819, 'new_value': 4995}, {'field': 'onlineAmount', 'old_value': 125959.92, 'new_value': 131824.25}, {'field': 'onlineCount', 'old_value': 6289, 'new_value': 6686}]
2025-04-26 08:03:59,817 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MLD
2025-04-26 08:03:59,817 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 197428.13999999998, 'new_value': 206085.03}, {'field': 'dailyBillAmount', 'old_value': 197428.13999999998, 'new_value': 206085.03}, {'field': 'amount', 'old_value': 189804.48, 'new_value': 197795.48}, {'field': 'count', 'old_value': 5892, 'new_value': 6116}, {'field': 'instoreAmount', 'old_value': 191221.75, 'new_value': 199358.75}, {'field': 'instoreCount', 'old_value': 5892, 'new_value': 6116}]
2025-04-26 08:04:00,286 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MMD
2025-04-26 08:04:00,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 17018.35, 'new_value': 18529.26}, {'field': 'amount', 'old_value': 43356.18, 'new_value': 44867.09}, {'field': 'count', 'old_value': 2376, 'new_value': 2452}, {'field': 'instoreAmount', 'old_value': 26559.260000000002, 'new_value': 27559.87}, {'field': 'instoreCount', 'old_value': 1759, 'new_value': 1816}, {'field': 'onlineAmount', 'old_value': 16796.920000000002, 'new_value': 17307.22}, {'field': 'onlineCount', 'old_value': 617, 'new_value': 636}]
2025-04-26 08:04:00,724 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MND
2025-04-26 08:04:00,724 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 113811.23, 'new_value': 118143.42}, {'field': 'dailyBillAmount', 'old_value': 113811.23, 'new_value': 118143.42}, {'field': 'amount', 'old_value': 24261.73, 'new_value': 25507.63}, {'field': 'count', 'old_value': 888, 'new_value': 921}, {'field': 'instoreAmount', 'old_value': 25236.96, 'new_value': 26532.06}, {'field': 'instoreCount', 'old_value': 888, 'new_value': 921}]
2025-04-26 08:04:01,178 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MOD
2025-04-26 08:04:01,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 102219.11, 'new_value': 106083.72}, {'field': 'dailyBillAmount', 'old_value': 102219.11, 'new_value': 106083.72}, {'field': 'amount', 'old_value': 78241.6, 'new_value': 81413.69}, {'field': 'count', 'old_value': 3059, 'new_value': 3189}, {'field': 'instoreAmount', 'old_value': 11273.42, 'new_value': 11842.46}, {'field': 'instoreCount', 'old_value': 675, 'new_value': 704}, {'field': 'onlineAmount', 'old_value': 68048.81, 'new_value': 70659.86}, {'field': 'onlineCount', 'old_value': 2384, 'new_value': 2485}]
2025-04-26 08:04:01,600 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MPD
2025-04-26 08:04:01,600 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 88368.16, 'new_value': 91776.56}, {'field': 'amount', 'old_value': 88368.16, 'new_value': 91776.56}, {'field': 'count', 'old_value': 2212, 'new_value': 2301}, {'field': 'instoreAmount', 'old_value': 83411.96, 'new_value': 86328.76}, {'field': 'instoreCount', 'old_value': 2140, 'new_value': 2221}, {'field': 'onlineAmount', 'old_value': 5249.99, 'new_value': 5741.59}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 80}]
2025-04-26 08:04:02,038 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MQD
2025-04-26 08:04:02,038 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-04, 变更字段: [{'field': 'amount', 'old_value': 150222.85, 'new_value': 154924.74}, {'field': 'count', 'old_value': 4025, 'new_value': 4165}, {'field': 'instoreAmount', 'old_value': 155902.8, 'new_value': 160760.59}, {'field': 'instoreCount', 'old_value': 4024, 'new_value': 4164}]
2025-04-26 08:04:02,507 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MRD
2025-04-26 08:04:02,507 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 117259.39, 'new_value': 122016.18}, {'field': 'count', 'old_value': 5514, 'new_value': 5775}, {'field': 'instoreAmount', 'old_value': 10946.75, 'new_value': 11451.75}, {'field': 'instoreCount', 'old_value': 548, 'new_value': 569}, {'field': 'onlineAmount', 'old_value': 109795.38, 'new_value': 114089.6}, {'field': 'onlineCount', 'old_value': 4966, 'new_value': 5206}]
2025-04-26 08:04:02,976 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MSD
2025-04-26 08:04:02,976 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 123267.6, 'new_value': 128434.15}, {'field': 'dailyBillAmount', 'old_value': 123267.6, 'new_value': 128434.15}, {'field': 'amount', 'old_value': 113278.05, 'new_value': 116969.53}, {'field': 'count', 'old_value': 3580, 'new_value': 3715}, {'field': 'instoreAmount', 'old_value': 63825.47, 'new_value': 66521.09}, {'field': 'instoreCount', 'old_value': 2555, 'new_value': 2654}, {'field': 'onlineAmount', 'old_value': 58260.9, 'new_value': 60209.3}, {'field': 'onlineCount', 'old_value': 1025, 'new_value': 1061}]
2025-04-26 08:04:03,461 - INFO - 更新表单数据成功: FINST-GX9663D193VUTBRPBSHWW4JO8U2F25M0EKW9MUD
2025-04-26 08:04:03,461 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-04, 变更字段: [{'field': 'amount', 'old_value': 86772.8, 'new_value': 89492.28}, {'field': 'count', 'old_value': 2937, 'new_value': 3045}, {'field': 'instoreAmount', 'old_value': 19288.02, 'new_value': 19795.4}, {'field': 'instoreCount', 'old_value': 784, 'new_value': 816}, {'field': 'onlineAmount', 'old_value': 68766.55, 'new_value': 71029.15}, {'field': 'onlineCount', 'old_value': 2153, 'new_value': 2229}]
2025-04-26 08:04:04,009 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9MR
2025-04-26 08:04:04,009 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 67861.48, 'new_value': 70073.21}, {'field': 'dailyBillAmount', 'old_value': 67861.48, 'new_value': 70073.21}, {'field': 'amount', 'old_value': 70003.85, 'new_value': 72302.21}, {'field': 'count', 'old_value': 2604, 'new_value': 2701}, {'field': 'instoreAmount', 'old_value': 70003.85, 'new_value': 72302.21}, {'field': 'instoreCount', 'old_value': 2604, 'new_value': 2701}]
2025-04-26 08:04:04,493 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9MS
2025-04-26 08:04:04,493 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 242993.0, 'new_value': 251251.0}, {'field': 'dailyBillAmount', 'old_value': 242993.0, 'new_value': 251251.0}, {'field': 'amount', 'old_value': 254013.0, 'new_value': 262481.0}, {'field': 'count', 'old_value': 195, 'new_value': 204}, {'field': 'instoreAmount', 'old_value': 268000.0, 'new_value': 277020.0}, {'field': 'instoreCount', 'old_value': 195, 'new_value': 204}]
2025-04-26 08:04:04,978 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9MT
2025-04-26 08:04:04,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-04, 变更字段: [{'field': 'amount', 'old_value': 47167.4, 'new_value': 48539.4}, {'field': 'count', 'old_value': 92, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 49770.8, 'new_value': 51142.8}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 95}]
2025-04-26 08:04:05,447 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9MV
2025-04-26 08:04:05,447 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-04, 变更字段: [{'field': 'amount', 'old_value': 36817.0, 'new_value': 37222.0}, {'field': 'count', 'old_value': 87, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 36817.0, 'new_value': 37222.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 89}]
2025-04-26 08:04:06,026 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9MW
2025-04-26 08:04:06,026 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 35902.0, 'new_value': 36730.0}, {'field': 'dailyBillAmount', 'old_value': 30699.0, 'new_value': 31527.0}]
2025-04-26 08:04:06,433 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9MY
2025-04-26 08:04:06,433 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 618764.0, 'new_value': 641786.0}, {'field': 'dailyBillAmount', 'old_value': 618764.0, 'new_value': 641786.0}, {'field': 'amount', 'old_value': 510841.01, 'new_value': 533863.01}, {'field': 'count', 'old_value': 62, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 510841.01, 'new_value': 533863.01}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 64}]
2025-04-26 08:04:06,902 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9M01
2025-04-26 08:04:06,902 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 23264.0, 'new_value': 23662.0}, {'field': 'amount', 'old_value': 23264.0, 'new_value': 23662.0}, {'field': 'count', 'old_value': 37, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 23264.0, 'new_value': 23662.0}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}]
2025-04-26 08:04:07,324 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2QB3EKW9M21
2025-04-26 08:04:07,324 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDAAQ1NK8J0I86N3H2U1EL001ENR_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 43341.82, 'new_value': 43588.82}, {'field': 'dailyBillAmount', 'old_value': 43410.82, 'new_value': 43657.82}, {'field': 'amount', 'old_value': 43341.82, 'new_value': 43588.82}, {'field': 'count', 'old_value': 194, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 43700.92, 'new_value': 43947.92}, {'field': 'instoreCount', 'old_value': 194, 'new_value': 198}]
2025-04-26 08:04:07,746 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M41
2025-04-26 08:04:07,746 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 86343.1, 'new_value': 91502.7}, {'field': 'count', 'old_value': 105, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 90043.5, 'new_value': 95203.1}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 112}]
2025-04-26 08:04:08,184 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M51
2025-04-26 08:04:08,184 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 91163.0, 'new_value': 98065.2}, {'field': 'dailyBillAmount', 'old_value': 91163.0, 'new_value': 98065.2}, {'field': 'amount', 'old_value': 100906.55, 'new_value': 107827.85}, {'field': 'count', 'old_value': 901, 'new_value': 942}, {'field': 'instoreAmount', 'old_value': 97084.81, 'new_value': 104405.11}, {'field': 'instoreCount', 'old_value': 841, 'new_value': 882}]
2025-04-26 08:04:08,638 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M61
2025-04-26 08:04:08,638 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 7803.0, 'new_value': 8161.0}, {'field': 'amount', 'old_value': 7803.0, 'new_value': 8161.0}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 7803.0, 'new_value': 8161.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-04-26 08:04:09,091 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M71
2025-04-26 08:04:09,091 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 31313.0, 'new_value': 33246.0}, {'field': 'dailyBillAmount', 'old_value': 31313.0, 'new_value': 33246.0}, {'field': 'amount', 'old_value': 34581.0, 'new_value': 36155.0}, {'field': 'count', 'old_value': 117, 'new_value': 122}, {'field': 'instoreAmount', 'old_value': 34772.0, 'new_value': 36346.0}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 122}]
2025-04-26 08:04:09,639 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M81
2025-04-26 08:04:09,639 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 23712.9, 'new_value': 25432.8}, {'field': 'amount', 'old_value': 23712.9, 'new_value': 25432.8}, {'field': 'count', 'old_value': 153, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 23506.0, 'new_value': 25146.0}, {'field': 'instoreCount', 'old_value': 152, 'new_value': 160}, {'field': 'onlineAmount', 'old_value': 206.9, 'new_value': 286.8}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-04-26 08:04:10,092 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M91
2025-04-26 08:04:10,092 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 8521.0, 'new_value': 8673.0}, {'field': 'dailyBillAmount', 'old_value': 8521.0, 'new_value': 8673.0}, {'field': 'amount', 'old_value': 31252.0, 'new_value': 31707.0}, {'field': 'count', 'old_value': 100, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 31252.0, 'new_value': 31707.0}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 103}]
2025-04-26 08:04:10,577 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MA1
2025-04-26 08:04:10,577 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 287760.88, 'new_value': 309286.69}, {'field': 'dailyBillAmount', 'old_value': 287760.88, 'new_value': 309286.69}, {'field': 'amount', 'old_value': 38432.86, 'new_value': 39476.7}, {'field': 'count', 'old_value': 441, 'new_value': 453}, {'field': 'instoreAmount', 'old_value': 30210.55, 'new_value': 31183.829999999998}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 311}, {'field': 'onlineAmount', 'old_value': 9593.36, 'new_value': 9663.92}, {'field': 'onlineCount', 'old_value': 140, 'new_value': 142}]
2025-04-26 08:04:10,968 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MB1
2025-04-26 08:04:10,968 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 45606.0, 'new_value': 48086.0}, {'field': 'amount', 'old_value': 45606.0, 'new_value': 48086.0}, {'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 45706.0, 'new_value': 48186.0}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 74}]
2025-04-26 08:04:11,406 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MC1
2025-04-26 08:04:11,406 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 11473.0, 'new_value': 11872.0}, {'field': 'amount', 'old_value': 11473.0, 'new_value': 11872.0}, {'field': 'count', 'old_value': 25, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 11722.0, 'new_value': 12619.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 27}]
2025-04-26 08:04:11,844 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MD1
2025-04-26 08:04:11,844 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 17193.66, 'new_value': 20362.06}, {'field': 'amount', 'old_value': 17193.66, 'new_value': 20362.06}, {'field': 'count', 'old_value': 53, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 17193.66, 'new_value': 20362.06}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 61}]
2025-04-26 08:04:12,282 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9ME1
2025-04-26 08:04:12,282 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 37973.0, 'new_value': 38203.0}, {'field': 'dailyBillAmount', 'old_value': 37973.0, 'new_value': 38203.0}, {'field': 'amount', 'old_value': 39724.0, 'new_value': 39954.0}, {'field': 'count', 'old_value': 118, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 42972.0, 'new_value': 43202.0}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 120}]
2025-04-26 08:04:12,673 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MF1
2025-04-26 08:04:12,673 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 292910.32, 'new_value': 301324.08}, {'field': 'dailyBillAmount', 'old_value': 263360.27, 'new_value': 270307.27}, {'field': 'amount', 'old_value': 286649.34, 'new_value': 295063.1}, {'field': 'count', 'old_value': 616, 'new_value': 635}, {'field': 'instoreAmount', 'old_value': 288734.34, 'new_value': 297647.1}, {'field': 'instoreCount', 'old_value': 616, 'new_value': 635}]
2025-04-26 08:04:13,126 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MG1
2025-04-26 08:04:13,126 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 48818.0, 'new_value': 49070.0}, {'field': 'amount', 'old_value': 48818.0, 'new_value': 49070.0}, {'field': 'count', 'old_value': 187, 'new_value': 191}, {'field': 'instoreAmount', 'old_value': 48934.0, 'new_value': 49186.0}, {'field': 'instoreCount', 'old_value': 187, 'new_value': 191}]
2025-04-26 08:04:13,611 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MJ1
2025-04-26 08:04:13,611 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 68949.81, 'new_value': 71737.96}, {'field': 'dailyBillAmount', 'old_value': 68949.81, 'new_value': 71737.96}, {'field': 'amount', 'old_value': 29223.51, 'new_value': 30070.79}, {'field': 'count', 'old_value': 2812, 'new_value': 2895}, {'field': 'instoreAmount', 'old_value': 30273.87, 'new_value': 31238.02}, {'field': 'instoreCount', 'old_value': 2812, 'new_value': 2895}]
2025-04-26 08:04:14,080 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MK1
2025-04-26 08:04:14,080 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 517254.93, 'new_value': 534689.96}, {'field': 'dailyBillAmount', 'old_value': 517254.93, 'new_value': 534689.96}, {'field': 'amount', 'old_value': 549490.38, 'new_value': 567849.11}, {'field': 'count', 'old_value': 4406, 'new_value': 4599}, {'field': 'instoreAmount', 'old_value': 412514.02, 'new_value': 424265.52}, {'field': 'instoreCount', 'old_value': 1901, 'new_value': 1959}, {'field': 'onlineAmount', 'old_value': 140924.31, 'new_value': 147787.9}, {'field': 'onlineCount', 'old_value': 2505, 'new_value': 2640}]
2025-04-26 08:04:14,534 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9ML1
2025-04-26 08:04:14,534 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 129168.44, 'new_value': 133880.74}, {'field': 'amount', 'old_value': 129168.44, 'new_value': 133880.74}, {'field': 'count', 'old_value': 849, 'new_value': 880}, {'field': 'instoreAmount', 'old_value': 129168.44, 'new_value': 133880.74}, {'field': 'instoreCount', 'old_value': 849, 'new_value': 880}]
2025-04-26 08:04:15,034 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MM1
2025-04-26 08:04:15,034 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-04, 变更字段: [{'field': 'amount', 'old_value': 110505.34, 'new_value': 117001.82}, {'field': 'count', 'old_value': 3880, 'new_value': 4075}, {'field': 'instoreAmount', 'old_value': 8701.14, 'new_value': 9264.51}, {'field': 'instoreCount', 'old_value': 144, 'new_value': 152}, {'field': 'onlineAmount', 'old_value': 104242.13, 'new_value': 110339.98}, {'field': 'onlineCount', 'old_value': 3736, 'new_value': 3923}]
2025-04-26 08:04:15,503 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MN1
2025-04-26 08:04:15,503 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 61422.58, 'new_value': 64280.8}, {'field': 'dailyBillAmount', 'old_value': 61422.58, 'new_value': 64280.8}, {'field': 'amount', 'old_value': 79081.76, 'new_value': 82727.68}, {'field': 'count', 'old_value': 2300, 'new_value': 2409}, {'field': 'instoreAmount', 'old_value': 24459.92, 'new_value': 25869.04}, {'field': 'instoreCount', 'old_value': 957, 'new_value': 1021}, {'field': 'onlineAmount', 'old_value': 54889.74, 'new_value': 57146.54}, {'field': 'onlineCount', 'old_value': 1343, 'new_value': 1388}]
2025-04-26 08:04:15,957 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MQ1
2025-04-26 08:04:15,957 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 126868.51, 'new_value': 132710.78}, {'field': 'dailyBillAmount', 'old_value': 126868.51, 'new_value': 132710.78}, {'field': 'amount', 'old_value': 79716.79, 'new_value': 83540.19}, {'field': 'count', 'old_value': 6442, 'new_value': 6836}, {'field': 'instoreAmount', 'old_value': 4662.2, 'new_value': 5039.1}, {'field': 'instoreCount', 'old_value': 349, 'new_value': 359}, {'field': 'onlineAmount', 'old_value': 75060.49, 'new_value': 78506.99}, {'field': 'onlineCount', 'old_value': 6093, 'new_value': 6477}]
2025-04-26 08:04:16,348 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MR1
2025-04-26 08:04:16,348 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 254815.21, 'new_value': 264509.07}, {'field': 'dailyBillAmount', 'old_value': 254815.21, 'new_value': 264509.07}, {'field': 'amount', 'old_value': 257442.79, 'new_value': 267760.09}, {'field': 'count', 'old_value': 2105, 'new_value': 2189}, {'field': 'instoreAmount', 'old_value': 177869.79, 'new_value': 185651.79}, {'field': 'instoreCount', 'old_value': 866, 'new_value': 908}, {'field': 'onlineAmount', 'old_value': 79573.0, 'new_value': 82108.3}, {'field': 'onlineCount', 'old_value': 1239, 'new_value': 1281}]
2025-04-26 08:04:16,833 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MS1
2025-04-26 08:04:16,833 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 297072.06, 'new_value': 307102.53}, {'field': 'dailyBillAmount', 'old_value': 297072.06, 'new_value': 307102.53}, {'field': 'amount', 'old_value': 311936.43, 'new_value': 322908.43}, {'field': 'count', 'old_value': 2192, 'new_value': 2268}, {'field': 'instoreAmount', 'old_value': 274072.13, 'new_value': 282626.43}, {'field': 'instoreCount', 'old_value': 1730, 'new_value': 1783}, {'field': 'onlineAmount', 'old_value': 44391.6, 'new_value': 46809.3}, {'field': 'onlineCount', 'old_value': 462, 'new_value': 485}]
2025-04-26 08:04:17,208 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MT1
2025-04-26 08:04:17,208 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 370807.67000000004, 'new_value': 398336.72000000003}, {'field': 'dailyBillAmount', 'old_value': 370807.67000000004, 'new_value': 398336.72000000003}, {'field': 'amount', 'old_value': 696438.81, 'new_value': 726042.41}, {'field': 'count', 'old_value': 3705, 'new_value': 3884}, {'field': 'instoreAmount', 'old_value': 531722.28, 'new_value': 551415.78}, {'field': 'instoreCount', 'old_value': 2209, 'new_value': 2286}, {'field': 'onlineAmount', 'old_value': 166461.94, 'new_value': 176372.44}, {'field': 'onlineCount', 'old_value': 1496, 'new_value': 1598}]
2025-04-26 08:04:17,693 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MU1
2025-04-26 08:04:17,693 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 218608.30000000002, 'new_value': 229271.11000000002}, {'field': 'dailyBillAmount', 'old_value': 218608.30000000002, 'new_value': 229271.11000000002}, {'field': 'amount', 'old_value': 329632.35, 'new_value': 345757.96}, {'field': 'count', 'old_value': 1709, 'new_value': 1798}, {'field': 'instoreAmount', 'old_value': 305152.49, 'new_value': 319938.49}, {'field': 'instoreCount', 'old_value': 1302, 'new_value': 1372}, {'field': 'onlineAmount', 'old_value': 24653.85, 'new_value': 25993.46}, {'field': 'onlineCount', 'old_value': 407, 'new_value': 426}]
2025-04-26 08:04:18,146 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MV1
2025-04-26 08:04:18,146 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 273857.72, 'new_value': 284470.89}, {'field': 'dailyBillAmount', 'old_value': 273857.72, 'new_value': 284470.89}, {'field': 'amount', 'old_value': 255924.3, 'new_value': 265486.3}, {'field': 'count', 'old_value': 1170, 'new_value': 1211}, {'field': 'instoreAmount', 'old_value': 259082.2, 'new_value': 268644.2}, {'field': 'instoreCount', 'old_value': 1170, 'new_value': 1211}]
2025-04-26 08:04:18,553 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MW1
2025-04-26 08:04:18,569 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 659829.14, 'new_value': 684968.74}, {'field': 'amount', 'old_value': 659829.14, 'new_value': 684968.74}, {'field': 'count', 'old_value': 5422, 'new_value': 5647}, {'field': 'instoreAmount', 'old_value': 659829.14, 'new_value': 684968.74}, {'field': 'instoreCount', 'old_value': 5422, 'new_value': 5647}]
2025-04-26 08:04:19,038 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MX1
2025-04-26 08:04:19,038 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 514930.54, 'new_value': 535830.44}, {'field': 'dailyBillAmount', 'old_value': 514930.54, 'new_value': 535830.44}, {'field': 'amount', 'old_value': 583157.91, 'new_value': 606781.81}, {'field': 'count', 'old_value': 4142, 'new_value': 4314}, {'field': 'instoreAmount', 'old_value': 316533.5, 'new_value': 327848.0}, {'field': 'instoreCount', 'old_value': 1677, 'new_value': 1738}, {'field': 'onlineAmount', 'old_value': 271890.0, 'new_value': 284358.4}, {'field': 'onlineCount', 'old_value': 2465, 'new_value': 2576}]
2025-04-26 08:04:19,476 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MY1
2025-04-26 08:04:19,476 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 289346.7, 'new_value': 309037.22}, {'field': 'dailyBillAmount', 'old_value': 289346.7, 'new_value': 309037.22}, {'field': 'amount', 'old_value': 403339.76, 'new_value': 414933.33}, {'field': 'count', 'old_value': 5016, 'new_value': 5167}, {'field': 'instoreAmount', 'old_value': 271198.66000000003, 'new_value': 277578.56}, {'field': 'instoreCount', 'old_value': 2503, 'new_value': 2551}, {'field': 'onlineAmount', 'old_value': 132805.3, 'new_value': 138018.97}, {'field': 'onlineCount', 'old_value': 2513, 'new_value': 2616}]
2025-04-26 08:04:19,976 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MZ1
2025-04-26 08:04:19,976 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 434238.98, 'new_value': 452657.83}, {'field': 'dailyBillAmount', 'old_value': 434238.98, 'new_value': 452657.83}, {'field': 'amount', 'old_value': 438774.89, 'new_value': 457535.05}, {'field': 'count', 'old_value': 3550, 'new_value': 3703}, {'field': 'instoreAmount', 'old_value': 391774.79, 'new_value': 408222.22}, {'field': 'instoreCount', 'old_value': 2076, 'new_value': 2159}, {'field': 'onlineAmount', 'old_value': 47330.25, 'new_value': 49714.56}, {'field': 'onlineCount', 'old_value': 1474, 'new_value': 1544}]
2025-04-26 08:04:20,430 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M02
2025-04-26 08:04:20,430 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 128094.8, 'new_value': 133501.6}, {'field': 'amount', 'old_value': 128094.8, 'new_value': 133501.6}, {'field': 'count', 'old_value': 515, 'new_value': 533}, {'field': 'instoreAmount', 'old_value': 128094.8, 'new_value': 133501.6}, {'field': 'instoreCount', 'old_value': 515, 'new_value': 533}]
2025-04-26 08:04:20,883 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M12
2025-04-26 08:04:20,883 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 262698.99, 'new_value': 264799.86}, {'field': 'dailyBillAmount', 'old_value': 262698.99, 'new_value': 264799.86}, {'field': 'amount', 'old_value': -213764.69, 'new_value': -221354.59}, {'field': 'count', 'old_value': 1045, 'new_value': 1087}, {'field': 'instoreAmount', 'old_value': 8701.1, 'new_value': 8814.1}, {'field': 'instoreCount', 'old_value': 415, 'new_value': 425}, {'field': 'onlineAmount', 'old_value': 20205.61, 'new_value': 21146.61}, {'field': 'onlineCount', 'old_value': 630, 'new_value': 662}]
2025-04-26 08:04:21,321 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M22
2025-04-26 08:04:21,321 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 542778.63, 'new_value': 567156.03}, {'field': 'dailyBillAmount', 'old_value': 542778.63, 'new_value': 567156.03}, {'field': 'amount', 'old_value': 344724.46, 'new_value': 355471.18}, {'field': 'count', 'old_value': 1530, 'new_value': 1579}, {'field': 'instoreAmount', 'old_value': 344724.46, 'new_value': 355471.18}, {'field': 'instoreCount', 'old_value': 1530, 'new_value': 1579}]
2025-04-26 08:04:21,728 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M32
2025-04-26 08:04:21,728 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 411415.6, 'new_value': 430256.58}, {'field': 'dailyBillAmount', 'old_value': 411415.6, 'new_value': 430256.58}, {'field': 'amount', 'old_value': 94423.8, 'new_value': 99812.9}, {'field': 'count', 'old_value': 420, 'new_value': 440}, {'field': 'instoreAmount', 'old_value': 98078.9, 'new_value': 103365.8}, {'field': 'instoreCount', 'old_value': 403, 'new_value': 421}, {'field': 'onlineAmount', 'old_value': 2160.0, 'new_value': 2262.5}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 19}]
2025-04-26 08:04:22,181 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M42
2025-04-26 08:04:22,181 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 167178.5, 'new_value': 178140.55}, {'field': 'dailyBillAmount', 'old_value': 167178.5, 'new_value': 178140.55}, {'field': 'amount', 'old_value': 153862.94, 'new_value': 164766.66}, {'field': 'count', 'old_value': 1154, 'new_value': 1239}, {'field': 'instoreAmount', 'old_value': 137545.45, 'new_value': 147568.59}, {'field': 'instoreCount', 'old_value': 744, 'new_value': 806}, {'field': 'onlineAmount', 'old_value': 16317.49, 'new_value': 17198.07}, {'field': 'onlineCount', 'old_value': 410, 'new_value': 433}]
2025-04-26 08:04:22,666 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M52
2025-04-26 08:04:22,666 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 225655.96, 'new_value': 235845.87}, {'field': 'dailyBillAmount', 'old_value': 225655.96, 'new_value': 235845.87}, {'field': 'amount', 'old_value': 104488.06, 'new_value': 110508.07}, {'field': 'count', 'old_value': 1159, 'new_value': 1214}, {'field': 'instoreAmount', 'old_value': 60533.21, 'new_value': 64002.31}, {'field': 'instoreCount', 'old_value': 271, 'new_value': 290}, {'field': 'onlineAmount', 'old_value': 43954.85, 'new_value': 46505.76}, {'field': 'onlineCount', 'old_value': 888, 'new_value': 924}]
2025-04-26 08:04:23,120 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M62
2025-04-26 08:04:23,120 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-04, 变更字段: [{'field': 'amount', 'old_value': 65014.0, 'new_value': 72645.0}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 65014.0, 'new_value': 72645.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-04-26 08:04:23,620 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M72
2025-04-26 08:04:23,620 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 142990.32, 'new_value': 149379.4}, {'field': 'amount', 'old_value': 142990.32, 'new_value': 149379.4}, {'field': 'count', 'old_value': 6401, 'new_value': 6647}, {'field': 'instoreAmount', 'old_value': 55486.22, 'new_value': 58273.65}, {'field': 'instoreCount', 'old_value': 2608, 'new_value': 2708}, {'field': 'onlineAmount', 'old_value': 92111.54, 'new_value': 96078.6}, {'field': 'onlineCount', 'old_value': 3793, 'new_value': 3939}]
2025-04-26 08:04:24,089 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M82
2025-04-26 08:04:24,089 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 35409.2, 'new_value': 37467.2}, {'field': 'amount', 'old_value': 35409.2, 'new_value': 37467.2}, {'field': 'count', 'old_value': 143, 'new_value': 152}, {'field': 'instoreAmount', 'old_value': 35409.2, 'new_value': 37467.2}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 152}]
2025-04-26 08:04:24,511 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M92
2025-04-26 08:04:24,511 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 249701.86, 'new_value': 262070.38}, {'field': 'dailyBillAmount', 'old_value': 345989.83, 'new_value': 358358.35}, {'field': 'amount', 'old_value': 174266.94, 'new_value': 178972.84}, {'field': 'count', 'old_value': 3254, 'new_value': 3356}, {'field': 'instoreAmount', 'old_value': 175158.76, 'new_value': 179865.66}, {'field': 'instoreCount', 'old_value': 3254, 'new_value': 3356}]
2025-04-26 08:04:24,996 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MD2
2025-04-26 08:04:24,996 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 30810.2, 'new_value': 31443.7}, {'field': 'amount', 'old_value': 30810.2, 'new_value': 31443.7}, {'field': 'count', 'old_value': 85, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 30810.2, 'new_value': 31443.7}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 88}]
2025-04-26 08:04:25,481 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9ME2
2025-04-26 08:04:25,481 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_EC9758A692DF47FBA8F7C97344079C9E_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 64771.38, 'new_value': 67270.09}, {'field': 'dailyBillAmount', 'old_value': 64771.38, 'new_value': 67270.09}, {'field': 'amount', 'old_value': 83304.51000000001, 'new_value': 86688.97}, {'field': 'count', 'old_value': 3045, 'new_value': 3158}, {'field': 'instoreAmount', 'old_value': 74484.45, 'new_value': 77295.62}, {'field': 'instoreCount', 'old_value': 2782, 'new_value': 2882}, {'field': 'onlineAmount', 'old_value': 8820.06, 'new_value': 9393.35}, {'field': 'onlineCount', 'old_value': 263, 'new_value': 276}]
2025-04-26 08:04:25,935 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MF2
2025-04-26 08:04:25,935 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 130029.3, 'new_value': 136138.87}, {'field': 'dailyBillAmount', 'old_value': 130029.3, 'new_value': 136138.87}, {'field': 'amount', 'old_value': 127357.64, 'new_value': 133267.21}, {'field': 'count', 'old_value': 5001, 'new_value': 5222}, {'field': 'instoreAmount', 'old_value': 128222.64, 'new_value': 134150.21}, {'field': 'instoreCount', 'old_value': 5001, 'new_value': 5222}]
2025-04-26 08:04:26,388 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MG2
2025-04-26 08:04:26,388 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 141755.27, 'new_value': 146490.17}, {'field': 'dailyBillAmount', 'old_value': 106221.53, 'new_value': 109584.23}, {'field': 'amount', 'old_value': 141755.27, 'new_value': 146490.17}, {'field': 'count', 'old_value': 2541, 'new_value': 2624}, {'field': 'instoreAmount', 'old_value': 133085.53, 'new_value': 137102.23}, {'field': 'instoreCount', 'old_value': 2191, 'new_value': 2250}, {'field': 'onlineAmount', 'old_value': 8954.33, 'new_value': 9672.53}, {'field': 'onlineCount', 'old_value': 350, 'new_value': 374}]
2025-04-26 08:04:26,810 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MI2
2025-04-26 08:04:26,810 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 18765.62, 'new_value': 19452.38}, {'field': 'amount', 'old_value': 18765.62, 'new_value': 19452.38}, {'field': 'count', 'old_value': 863, 'new_value': 897}, {'field': 'instoreAmount', 'old_value': 18618.62, 'new_value': 19305.38}, {'field': 'instoreCount', 'old_value': 857, 'new_value': 891}]
2025-04-26 08:04:27,264 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MJ2
2025-04-26 08:04:27,264 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 226875.04, 'new_value': 239132.45}, {'field': 'dailyBillAmount', 'old_value': 226875.04, 'new_value': 239132.45}, {'field': 'amount', 'old_value': 313434.47, 'new_value': 329279.26}, {'field': 'count', 'old_value': 3382, 'new_value': 3566}, {'field': 'instoreAmount', 'old_value': 278564.91000000003, 'new_value': 293755.66000000003}, {'field': 'instoreCount', 'old_value': 1800, 'new_value': 1926}, {'field': 'onlineAmount', 'old_value': 40509.26, 'new_value': 41911.0}, {'field': 'onlineCount', 'old_value': 1582, 'new_value': 1640}]
2025-04-26 08:04:27,686 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MK2
2025-04-26 08:04:27,686 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0J21PQQ9M3IKSIOCDI7P2001G03_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 23803.1, 'new_value': 23972.7}, {'field': 'amount', 'old_value': 23803.1, 'new_value': 23972.7}, {'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 23803.1, 'new_value': 23972.7}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 74}]
2025-04-26 08:04:28,108 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9ML2
2025-04-26 08:04:28,108 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 42347.7, 'new_value': 44940.3}, {'field': 'dailyBillAmount', 'old_value': 41671.6, 'new_value': 44264.2}, {'field': 'amount', 'old_value': 40840.700000000004, 'new_value': 43030.5}, {'field': 'count', 'old_value': 211, 'new_value': 221}, {'field': 'instoreAmount', 'old_value': 40840.700000000004, 'new_value': 43030.5}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 221}]
2025-04-26 08:04:28,515 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MM2
2025-04-26 08:04:28,515 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 117984.6, 'new_value': 122326.09}, {'field': 'dailyBillAmount', 'old_value': 117984.6, 'new_value': 122326.09}, {'field': 'amount', 'old_value': 30439.78, 'new_value': 31161.829999999998}, {'field': 'count', 'old_value': 382, 'new_value': 394}, {'field': 'instoreAmount', 'old_value': 11282.0, 'new_value': 11568.68}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 109}, {'field': 'onlineAmount', 'old_value': 20017.55, 'new_value': 20482.91}, {'field': 'onlineCount', 'old_value': 277, 'new_value': 285}]
2025-04-26 08:04:28,937 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MN2
2025-04-26 08:04:28,937 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 93011.4, 'new_value': 99395.23}, {'field': 'dailyBillAmount', 'old_value': 83111.7, 'new_value': 89451.93}, {'field': 'amount', 'old_value': 93011.4, 'new_value': 99395.23}, {'field': 'count', 'old_value': 4826, 'new_value': 5193}, {'field': 'instoreAmount', 'old_value': 33411.3, 'new_value': 36514.43}, {'field': 'instoreCount', 'old_value': 1536, 'new_value': 1695}, {'field': 'onlineAmount', 'old_value': 59984.9, 'new_value': 63300.6}, {'field': 'onlineCount', 'old_value': 3290, 'new_value': 3498}]
2025-04-26 08:04:29,406 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MO2
2025-04-26 08:04:29,406 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 58097.73, 'new_value': 60685.0}, {'field': 'amount', 'old_value': 58097.73, 'new_value': 60685.0}, {'field': 'count', 'old_value': 3394, 'new_value': 3584}, {'field': 'instoreAmount', 'old_value': 31446.73, 'new_value': 32889.69}, {'field': 'instoreCount', 'old_value': 1836, 'new_value': 1944}, {'field': 'onlineAmount', 'old_value': 27189.05, 'new_value': 28456.74}, {'field': 'onlineCount', 'old_value': 1558, 'new_value': 1640}]
2025-04-26 08:04:29,891 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MP2
2025-04-26 08:04:29,891 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-04, 变更字段: [{'field': 'amount', 'old_value': 124158.61, 'new_value': 128086.01}, {'field': 'count', 'old_value': 1410, 'new_value': 1469}, {'field': 'instoreAmount', 'old_value': 124725.06, 'new_value': 128652.45999999999}, {'field': 'instoreCount', 'old_value': 1410, 'new_value': 1469}]
2025-04-26 08:04:30,345 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MQ2
2025-04-26 08:04:30,345 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 84490.13, 'new_value': 89599.96}, {'field': 'dailyBillAmount', 'old_value': 82898.36, 'new_value': 87889.46}, {'field': 'amount', 'old_value': 84490.13, 'new_value': 89599.96}, {'field': 'count', 'old_value': 2431, 'new_value': 2563}, {'field': 'instoreAmount', 'old_value': 74248.75, 'new_value': 78938.73}, {'field': 'instoreCount', 'old_value': 1838, 'new_value': 1948}, {'field': 'onlineAmount', 'old_value': 10258.960000000001, 'new_value': 10678.81}, {'field': 'onlineCount', 'old_value': 593, 'new_value': 615}]
2025-04-26 08:04:30,751 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MS2
2025-04-26 08:04:30,751 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 161038.72, 'new_value': 170776.27}, {'field': 'dailyBillAmount', 'old_value': 161038.72, 'new_value': 170776.27}, {'field': 'amount', 'old_value': 36162.22, 'new_value': 39854.59}, {'field': 'count', 'old_value': 1186, 'new_value': 1249}, {'field': 'instoreAmount', 'old_value': 37907.83, 'new_value': 41644.2}, {'field': 'instoreCount', 'old_value': 1186, 'new_value': 1249}]
2025-04-26 08:04:31,205 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MT2
2025-04-26 08:04:31,205 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 493529.05, 'new_value': 516969.97000000003}, {'field': 'dailyBillAmount', 'old_value': 493529.05, 'new_value': 516969.97000000003}, {'field': 'amount', 'old_value': 49389.2, 'new_value': 52155.8}, {'field': 'count', 'old_value': 235, 'new_value': 246}, {'field': 'instoreAmount', 'old_value': 49389.2, 'new_value': 52155.8}, {'field': 'instoreCount', 'old_value': 235, 'new_value': 246}]
2025-04-26 08:04:31,643 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MU2
2025-04-26 08:04:31,643 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 16503.78, 'new_value': 17511.9}, {'field': 'count', 'old_value': 851, 'new_value': 914}, {'field': 'onlineAmount', 'old_value': 16503.78, 'new_value': 17511.9}, {'field': 'onlineCount', 'old_value': 851, 'new_value': 914}]
2025-04-26 08:04:32,112 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MV2
2025-04-26 08:04:32,112 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 334997.14, 'new_value': 353104.41000000003}, {'field': 'dailyBillAmount', 'old_value': 334997.14, 'new_value': 353104.41000000003}, {'field': 'amount', 'old_value': 284654.13, 'new_value': 300495.28}, {'field': 'count', 'old_value': 4028, 'new_value': 4256}, {'field': 'instoreAmount', 'old_value': 264609.56, 'new_value': 279457.06}, {'field': 'instoreCount', 'old_value': 3559, 'new_value': 3766}, {'field': 'onlineAmount', 'old_value': 27928.99, 'new_value': 29354.2}, {'field': 'onlineCount', 'old_value': 469, 'new_value': 490}]
2025-04-26 08:04:32,566 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MW2
2025-04-26 08:04:32,566 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 223143.9, 'new_value': 233723.5}, {'field': 'dailyBillAmount', 'old_value': 174008.6, 'new_value': 184550.2}, {'field': 'amount', 'old_value': 223143.9, 'new_value': 231638.84}, {'field': 'count', 'old_value': 5728, 'new_value': 6007}, {'field': 'instoreAmount', 'old_value': 140283.18, 'new_value': 145973.02}, {'field': 'instoreCount', 'old_value': 3287, 'new_value': 3456}, {'field': 'onlineAmount', 'old_value': 83479.3, 'new_value': 86302.38}, {'field': 'onlineCount', 'old_value': 2441, 'new_value': 2551}]
2025-04-26 08:04:33,019 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MX2
2025-04-26 08:04:33,019 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 114188.86, 'new_value': 121735.67}, {'field': 'dailyBillAmount', 'old_value': 114188.86, 'new_value': 121735.67}, {'field': 'amount', 'old_value': 928.18, 'new_value': 1157.57}, {'field': 'count', 'old_value': 29, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 928.18, 'new_value': 1157.57}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 36}]
2025-04-26 08:04:33,441 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MY2
2025-04-26 08:04:33,441 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 90976.25, 'new_value': 94663.03}, {'field': 'dailyBillAmount', 'old_value': 38905.2, 'new_value': 40736.4}, {'field': 'amount', 'old_value': 90976.25, 'new_value': 94663.03}, {'field': 'count', 'old_value': 2294, 'new_value': 2390}, {'field': 'instoreAmount', 'old_value': 42005.49, 'new_value': 43823.49}, {'field': 'instoreCount', 'old_value': 1115, 'new_value': 1160}, {'field': 'onlineAmount', 'old_value': 50348.68, 'new_value': 52256.46}, {'field': 'onlineCount', 'old_value': 1179, 'new_value': 1230}]
2025-04-26 08:04:33,942 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M03
2025-04-26 08:04:33,942 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 52559.15, 'new_value': 55294.23}, {'field': 'dailyBillAmount', 'old_value': 52559.15, 'new_value': 55294.23}, {'field': 'amount', 'old_value': 57645.17, 'new_value': 60568.55}, {'field': 'count', 'old_value': 2270, 'new_value': 2391}, {'field': 'instoreAmount', 'old_value': 37134.65, 'new_value': 38634.61}, {'field': 'instoreCount', 'old_value': 1354, 'new_value': 1409}, {'field': 'onlineAmount', 'old_value': 20510.52, 'new_value': 21933.940000000002}, {'field': 'onlineCount', 'old_value': 916, 'new_value': 982}]
2025-04-26 08:04:34,458 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M13
2025-04-26 08:04:34,458 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 66364.53, 'new_value': 70160.70999999999}, {'field': 'amount', 'old_value': 66364.53, 'new_value': 70160.70999999999}, {'field': 'count', 'old_value': 1894, 'new_value': 1985}, {'field': 'instoreAmount', 'old_value': 36001.48, 'new_value': 37729.07}, {'field': 'instoreCount', 'old_value': 1229, 'new_value': 1276}, {'field': 'onlineAmount', 'old_value': 30363.05, 'new_value': 32431.64}, {'field': 'onlineCount', 'old_value': 665, 'new_value': 709}]
2025-04-26 08:04:34,943 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M23
2025-04-26 08:04:34,943 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 63711.76, 'new_value': 66582.58}, {'field': 'amount', 'old_value': 63711.76, 'new_value': 66582.58}, {'field': 'count', 'old_value': 1186, 'new_value': 1236}, {'field': 'instoreAmount', 'old_value': 52584.35, 'new_value': 55314.35}, {'field': 'instoreCount', 'old_value': 956, 'new_value': 1001}, {'field': 'onlineAmount', 'old_value': 12084.81, 'new_value': 12225.63}, {'field': 'onlineCount', 'old_value': 230, 'new_value': 235}]
2025-04-26 08:04:35,459 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M33
2025-04-26 08:04:35,459 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 259500.49, 'new_value': 270876.61}, {'field': 'dailyBillAmount', 'old_value': 259500.49, 'new_value': 270876.61}, {'field': 'amount', 'old_value': 206847.52, 'new_value': 214572.37}, {'field': 'count', 'old_value': 5383, 'new_value': 5574}, {'field': 'instoreAmount', 'old_value': 142080.34, 'new_value': 145926.74}, {'field': 'instoreCount', 'old_value': 3599, 'new_value': 3680}, {'field': 'onlineAmount', 'old_value': 67360.48, 'new_value': 71442.83}, {'field': 'onlineCount', 'old_value': 1784, 'new_value': 1894}]
2025-04-26 08:04:35,928 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M43
2025-04-26 08:04:35,928 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-04, 变更字段: [{'field': 'amount', 'old_value': 460303.7, 'new_value': 495944.6}, {'field': 'count', 'old_value': 2856, 'new_value': 3062}, {'field': 'instoreAmount', 'old_value': 294554.4, 'new_value': 323071.2}, {'field': 'instoreCount', 'old_value': 2073, 'new_value': 2246}, {'field': 'onlineAmount', 'old_value': 165749.3, 'new_value': 172873.4}, {'field': 'onlineCount', 'old_value': 783, 'new_value': 816}]
2025-04-26 08:04:36,381 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M53
2025-04-26 08:04:36,381 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 980830.27, 'new_value': 1021822.0700000001}, {'field': 'amount', 'old_value': 980830.27, 'new_value': 1021822.0700000001}, {'field': 'count', 'old_value': 3365, 'new_value': 3498}, {'field': 'instoreAmount', 'old_value': 979457.27, 'new_value': 1020449.0700000001}, {'field': 'instoreCount', 'old_value': 3363, 'new_value': 3496}]
2025-04-26 08:04:36,772 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M63
2025-04-26 08:04:36,772 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 611509.89, 'new_value': 645099.18}, {'field': 'dailyBillAmount', 'old_value': 542164.43, 'new_value': 571605.36}, {'field': 'amount', 'old_value': 611509.89, 'new_value': 645099.18}, {'field': 'count', 'old_value': 3971, 'new_value': 4164}, {'field': 'instoreAmount', 'old_value': 556533.59, 'new_value': 587393.64}, {'field': 'instoreCount', 'old_value': 2447, 'new_value': 2568}, {'field': 'onlineAmount', 'old_value': 54976.3, 'new_value': 57705.54}, {'field': 'onlineCount', 'old_value': 1524, 'new_value': 1596}]
2025-04-26 08:04:37,195 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M73
2025-04-26 08:04:37,195 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 615809.99, 'new_value': 674374.78}, {'field': 'dailyBillAmount', 'old_value': 586584.67, 'new_value': 644465.46}, {'field': 'amount', 'old_value': 615809.99, 'new_value': 674374.78}, {'field': 'count', 'old_value': 1447, 'new_value': 1535}, {'field': 'instoreAmount', 'old_value': 579676.8, 'new_value': 636172.8}, {'field': 'instoreCount', 'old_value': 1148, 'new_value': 1221}, {'field': 'onlineAmount', 'old_value': 36843.19, 'new_value': 38911.98}, {'field': 'onlineCount', 'old_value': 299, 'new_value': 314}]
2025-04-26 08:04:37,633 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M83
2025-04-26 08:04:37,633 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 659532.1900000001, 'new_value': 692222.53}, {'field': 'amount', 'old_value': 659532.1900000001, 'new_value': 692222.53}, {'field': 'count', 'old_value': 3177, 'new_value': 3336}, {'field': 'instoreAmount', 'old_value': 623098.13, 'new_value': 653239.23}, {'field': 'instoreCount', 'old_value': 2118, 'new_value': 2217}, {'field': 'onlineAmount', 'old_value': 36434.06, 'new_value': 38983.3}, {'field': 'onlineCount', 'old_value': 1059, 'new_value': 1119}]
2025-04-26 08:04:38,055 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9M93
2025-04-26 08:04:38,055 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 711089.5, 'new_value': 752997.6900000001}, {'field': 'dailyBillAmount', 'old_value': 711089.5, 'new_value': 752997.6900000001}, {'field': 'amount', 'old_value': 664719.4, 'new_value': 708063.29}, {'field': 'count', 'old_value': 3540, 'new_value': 3739}, {'field': 'instoreAmount', 'old_value': 588536.83, 'new_value': 626962.34}, {'field': 'instoreCount', 'old_value': 2668, 'new_value': 2821}, {'field': 'onlineAmount', 'old_value': 76219.36, 'new_value': 81336.72}, {'field': 'onlineCount', 'old_value': 872, 'new_value': 918}]
2025-04-26 08:04:38,587 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MA3
2025-04-26 08:04:38,587 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 352457.97, 'new_value': 361796.52}, {'field': 'dailyBillAmount', 'old_value': 351879.12, 'new_value': 361217.67}, {'field': 'amount', 'old_value': 342604.07, 'new_value': 351942.62}, {'field': 'count', 'old_value': 640, 'new_value': 657}, {'field': 'instoreAmount', 'old_value': 342604.07, 'new_value': 351942.62}, {'field': 'instoreCount', 'old_value': 640, 'new_value': 657}]
2025-04-26 08:04:39,040 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MB3
2025-04-26 08:04:39,040 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 175067.91, 'new_value': 180464.71}, {'field': 'dailyBillAmount', 'old_value': 175067.91, 'new_value': 180464.71}, {'field': 'amount', 'old_value': 202475.98, 'new_value': 204302.08}, {'field': 'count', 'old_value': 265, 'new_value': 269}, {'field': 'instoreAmount', 'old_value': 204892.7, 'new_value': 206718.8}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 244}]
2025-04-26 08:04:39,447 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MC3
2025-04-26 08:04:39,447 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 19659.77, 'new_value': 19906.37}, {'field': 'amount', 'old_value': 19659.77, 'new_value': 19906.37}, {'field': 'count', 'old_value': 416, 'new_value': 425}, {'field': 'instoreAmount', 'old_value': 19677.84, 'new_value': 19939.44}, {'field': 'instoreCount', 'old_value': 416, 'new_value': 425}]
2025-04-26 08:04:39,947 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MD3
2025-04-26 08:04:39,947 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 71270.13, 'new_value': 76278.59}, {'field': 'amount', 'old_value': 71270.13, 'new_value': 76278.59}, {'field': 'count', 'old_value': 604, 'new_value': 645}, {'field': 'instoreAmount', 'old_value': 71419.47, 'new_value': 76427.93}, {'field': 'instoreCount', 'old_value': 604, 'new_value': 645}]
2025-04-26 08:04:40,354 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9ME3
2025-04-26 08:04:40,354 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 297631.74, 'new_value': 311614.07}, {'field': 'dailyBillAmount', 'old_value': 297631.74, 'new_value': 311614.07}, {'field': 'amount', 'old_value': 357107.84, 'new_value': 374668.24}, {'field': 'count', 'old_value': 9067, 'new_value': 9495}, {'field': 'instoreAmount', 'old_value': 317090.2, 'new_value': 336285.6}, {'field': 'instoreCount', 'old_value': 7943, 'new_value': 8304}, {'field': 'onlineAmount', 'old_value': 45787.24, 'new_value': 48026.14}, {'field': 'onlineCount', 'old_value': 1124, 'new_value': 1191}]
2025-04-26 08:04:40,792 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MF3
2025-04-26 08:04:40,792 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 126312.08, 'new_value': 127590.92}, {'field': 'dailyBillAmount', 'old_value': 126312.08, 'new_value': 127590.92}, {'field': 'amount', 'old_value': 131542.68, 'new_value': 132821.52}, {'field': 'count', 'old_value': 112, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 131542.68, 'new_value': 132821.52}, {'field': 'instoreCount', 'old_value': 112, 'new_value': 115}]
2025-04-26 08:04:41,370 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MG3
2025-04-26 08:04:41,370 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 756115.25, 'new_value': 791024.38}, {'field': 'dailyBillAmount', 'old_value': 756115.25, 'new_value': 791024.38}, {'field': 'amount', 'old_value': 717985.52, 'new_value': 750209.1}, {'field': 'count', 'old_value': 1835, 'new_value': 1938}, {'field': 'instoreAmount', 'old_value': 732390.11, 'new_value': 765703.34}, {'field': 'instoreCount', 'old_value': 1468, 'new_value': 1546}, {'field': 'onlineAmount', 'old_value': 8781.75, 'new_value': 9288.0}, {'field': 'onlineCount', 'old_value': 367, 'new_value': 392}]
2025-04-26 08:04:41,824 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MH3
2025-04-26 08:04:41,824 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 1069958.55, 'new_value': 1128650.15}, {'field': 'amount', 'old_value': 1069958.55, 'new_value': 1128650.15}, {'field': 'count', 'old_value': 3480, 'new_value': 3625}, {'field': 'instoreAmount', 'old_value': 1069958.55, 'new_value': 1129788.15}, {'field': 'instoreCount', 'old_value': 3480, 'new_value': 3625}]
2025-04-26 08:04:42,293 - INFO - 更新表单数据成功: FINST-LLF66F71DKVUP905E9P2E830HB7M2RB3EKW9MI3
2025-04-26 08:04:42,293 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 902652.39, 'new_value': 948527.66}, {'field': 'dailyBillAmount', 'old_value': 902652.39, 'new_value': 948527.66}, {'field': 'amount', 'old_value': 712820.91, 'new_value': 753228.26}, {'field': 'count', 'old_value': 2001, 'new_value': 2117}, {'field': 'instoreAmount', 'old_value': 701746.24, 'new_value': 742458.29}, {'field': 'instoreCount', 'old_value': 1404, 'new_value': 1486}, {'field': 'onlineAmount', 'old_value': 19285.14, 'new_value': 20252.44}, {'field': 'onlineCount', 'old_value': 597, 'new_value': 631}]
2025-04-26 08:04:42,793 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9M32
2025-04-26 08:04:42,793 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 1739324.85, 'new_value': 1817138.89}, {'field': 'dailyBillAmount', 'old_value': 1739324.85, 'new_value': 1817138.89}, {'field': 'amount', 'old_value': 1630025.0, 'new_value': 1708814.0}, {'field': 'count', 'old_value': 4510, 'new_value': 4745}, {'field': 'instoreAmount', 'old_value': 1630025.0, 'new_value': 1708814.0}, {'field': 'instoreCount', 'old_value': 4510, 'new_value': 4745}]
2025-04-26 08:04:43,247 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9M42
2025-04-26 08:04:43,247 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 235533.1, 'new_value': 254291.97}, {'field': 'dailyBillAmount', 'old_value': 235533.1, 'new_value': 254291.97}, {'field': 'amount', 'old_value': 250150.74, 'new_value': 268909.61}, {'field': 'count', 'old_value': 1287, 'new_value': 1369}, {'field': 'instoreAmount', 'old_value': 241476.2, 'new_value': 259944.2}, {'field': 'instoreCount', 'old_value': 1030, 'new_value': 1105}, {'field': 'onlineAmount', 'old_value': 15891.74, 'new_value': 16182.61}, {'field': 'onlineCount', 'old_value': 257, 'new_value': 264}]
2025-04-26 08:04:43,638 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9M52
2025-04-26 08:04:43,638 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 825301.0, 'new_value': 868275.78}, {'field': 'dailyBillAmount', 'old_value': 825301.0, 'new_value': 868275.78}, {'field': 'amount', 'old_value': 825459.07, 'new_value': 868732.4299999999}, {'field': 'count', 'old_value': 3761, 'new_value': 3971}, {'field': 'instoreAmount', 'old_value': 825464.94, 'new_value': 868738.3}, {'field': 'instoreCount', 'old_value': 3761, 'new_value': 3971}]
2025-04-26 08:04:44,154 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9M62
2025-04-26 08:04:44,154 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 334242.93, 'new_value': 355169.41}, {'field': 'dailyBillAmount', 'old_value': 334242.93, 'new_value': 355169.41}, {'field': 'amount', 'old_value': 533158.75, 'new_value': 563553.45}, {'field': 'count', 'old_value': 851, 'new_value': 919}, {'field': 'instoreAmount', 'old_value': 528227.12, 'new_value': 558031.12}, {'field': 'instoreCount', 'old_value': 827, 'new_value': 891}, {'field': 'onlineAmount', 'old_value': 4951.8, 'new_value': 5542.5}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 28}]
2025-04-26 08:04:44,608 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9M82
2025-04-26 08:04:44,608 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 225495.13, 'new_value': 237413.13}, {'field': 'dailyBillAmount', 'old_value': 225495.13, 'new_value': 237413.13}, {'field': 'amount', 'old_value': 235209.0, 'new_value': 247815.0}, {'field': 'count', 'old_value': 1665, 'new_value': 1761}, {'field': 'instoreAmount', 'old_value': 238226.0, 'new_value': 250832.0}, {'field': 'instoreCount', 'old_value': 1665, 'new_value': 1761}]
2025-04-26 08:04:45,046 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9M92
2025-04-26 08:04:45,046 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 228716.92, 'new_value': 237257.37}, {'field': 'dailyBillAmount', 'old_value': 228716.92, 'new_value': 237257.37}, {'field': 'amount', 'old_value': 205339.44, 'new_value': 211469.6}, {'field': 'count', 'old_value': 1422, 'new_value': 1465}, {'field': 'instoreAmount', 'old_value': 200801.0, 'new_value': 206690.0}, {'field': 'instoreCount', 'old_value': 1246, 'new_value': 1281}, {'field': 'onlineAmount', 'old_value': 8245.44, 'new_value': 8486.6}, {'field': 'onlineCount', 'old_value': 176, 'new_value': 184}]
2025-04-26 08:04:45,421 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MC2
2025-04-26 08:04:45,421 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 193341.68, 'new_value': 198443.93}, {'field': 'amount', 'old_value': 193341.68, 'new_value': 198443.93}, {'field': 'count', 'old_value': 3857, 'new_value': 4004}, {'field': 'instoreAmount', 'old_value': 186953.88999999998, 'new_value': 191859.84}, {'field': 'instoreCount', 'old_value': 3746, 'new_value': 3888}, {'field': 'onlineAmount', 'old_value': 6387.79, 'new_value': 6584.09}, {'field': 'onlineCount', 'old_value': 111, 'new_value': 116}]
2025-04-26 08:04:45,937 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MD2
2025-04-26 08:04:45,937 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 42883.2, 'new_value': 43838.6}, {'field': 'amount', 'old_value': 42883.2, 'new_value': 43838.6}, {'field': 'count', 'old_value': 249, 'new_value': 257}, {'field': 'instoreAmount', 'old_value': 42883.2, 'new_value': 43838.6}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 257}]
2025-04-26 08:04:46,406 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9ME2
2025-04-26 08:04:46,406 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 94924.3, 'new_value': 96634.3}, {'field': 'dailyBillAmount', 'old_value': 83488.8, 'new_value': 85198.8}, {'field': 'amount', 'old_value': 115677.7, 'new_value': 118242.2}, {'field': 'count', 'old_value': 832, 'new_value': 858}, {'field': 'instoreAmount', 'old_value': 115929.7, 'new_value': 118494.2}, {'field': 'instoreCount', 'old_value': 832, 'new_value': 858}]
2025-04-26 08:04:46,860 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MG2
2025-04-26 08:04:46,860 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 269193.2, 'new_value': 277408.0}, {'field': 'dailyBillAmount', 'old_value': 269193.2, 'new_value': 277408.0}, {'field': 'amount', 'old_value': 152621.39, 'new_value': 158270.69}, {'field': 'count', 'old_value': 4992, 'new_value': 5188}, {'field': 'instoreAmount', 'old_value': 147226.22, 'new_value': 152972.12}, {'field': 'instoreCount', 'old_value': 4837, 'new_value': 5030}, {'field': 'onlineAmount', 'old_value': 6886.38, 'new_value': 6972.98}, {'field': 'onlineCount', 'old_value': 155, 'new_value': 158}]
2025-04-26 08:04:47,298 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MH2
2025-04-26 08:04:47,298 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 41050.9, 'new_value': 42146.6}, {'field': 'dailyBillAmount', 'old_value': 41050.9, 'new_value': 42146.6}, {'field': 'amount', 'old_value': 41711.7, 'new_value': 42807.4}, {'field': 'count', 'old_value': 240, 'new_value': 245}, {'field': 'instoreAmount', 'old_value': 43970.7, 'new_value': 45066.4}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 241}]
2025-04-26 08:04:47,720 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MI2
2025-04-26 08:04:47,720 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 70013.24, 'new_value': 72764.98}, {'field': 'dailyBillAmount', 'old_value': 70013.24, 'new_value': 72764.98}]
2025-04-26 08:04:48,142 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MJ2
2025-04-26 08:04:48,142 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 50530.52, 'new_value': 52635.44}, {'field': 'amount', 'old_value': 50530.52, 'new_value': 52635.44}, {'field': 'count', 'old_value': 2812, 'new_value': 2931}, {'field': 'instoreAmount', 'old_value': 51427.66, 'new_value': 53570.25}, {'field': 'instoreCount', 'old_value': 2812, 'new_value': 2931}]
2025-04-26 08:04:48,564 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MK2
2025-04-26 08:04:48,564 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 77164.44, 'new_value': 80551.23}, {'field': 'dailyBillAmount', 'old_value': 77164.44, 'new_value': 80551.23}, {'field': 'amount', 'old_value': 81798.05, 'new_value': 85345.91}, {'field': 'count', 'old_value': 4020, 'new_value': 4206}, {'field': 'instoreAmount', 'old_value': 73654.8, 'new_value': 77153.7}, {'field': 'instoreCount', 'old_value': 3686, 'new_value': 3870}, {'field': 'onlineAmount', 'old_value': 8420.8, 'new_value': 8469.76}, {'field': 'onlineCount', 'old_value': 334, 'new_value': 336}]
2025-04-26 08:04:49,002 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9ML2
2025-04-26 08:04:49,002 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 46250.76, 'new_value': 48542.83}, {'field': 'amount', 'old_value': 46250.76, 'new_value': 48542.83}, {'field': 'count', 'old_value': 2269, 'new_value': 2395}, {'field': 'instoreAmount', 'old_value': 26848.48, 'new_value': 28283.6}, {'field': 'instoreCount', 'old_value': 1419, 'new_value': 1498}, {'field': 'onlineAmount', 'old_value': 19402.28, 'new_value': 20259.23}, {'field': 'onlineCount', 'old_value': 850, 'new_value': 897}]
2025-04-26 08:04:49,503 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MM2
2025-04-26 08:04:49,503 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 37965.66, 'new_value': 39611.39}, {'field': 'dailyBillAmount', 'old_value': 37965.66, 'new_value': 39611.39}, {'field': 'amount', 'old_value': 27469.36, 'new_value': 28741.94}, {'field': 'count', 'old_value': 1098, 'new_value': 1147}, {'field': 'instoreAmount', 'old_value': 27731.26, 'new_value': 29003.84}, {'field': 'instoreCount', 'old_value': 1098, 'new_value': 1147}]
2025-04-26 08:04:49,956 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MN2
2025-04-26 08:04:49,956 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 60729.25, 'new_value': 63443.49}, {'field': 'amount', 'old_value': 60729.25, 'new_value': 63443.49}, {'field': 'count', 'old_value': 3174, 'new_value': 3321}, {'field': 'instoreAmount', 'old_value': 17930.55, 'new_value': 18857.14}, {'field': 'instoreCount', 'old_value': 1165, 'new_value': 1216}, {'field': 'onlineAmount', 'old_value': 43622.5, 'new_value': 45450.25}, {'field': 'onlineCount', 'old_value': 2009, 'new_value': 2105}]
2025-04-26 08:04:50,363 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MO2
2025-04-26 08:04:50,363 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 140502.15, 'new_value': 147211.86}, {'field': 'dailyBillAmount', 'old_value': 140502.15, 'new_value': 147211.86}, {'field': 'amount', 'old_value': 110919.91, 'new_value': 116271.91}, {'field': 'count', 'old_value': 1092, 'new_value': 1145}, {'field': 'instoreAmount', 'old_value': 110919.91, 'new_value': 116271.91}, {'field': 'instoreCount', 'old_value': 1092, 'new_value': 1145}]
2025-04-26 08:04:50,754 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MP2
2025-04-26 08:04:50,754 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 107884.9, 'new_value': 112064.7}, {'field': 'dailyBillAmount', 'old_value': 107884.9, 'new_value': 112064.7}, {'field': 'amount', 'old_value': 131542.0, 'new_value': 136120.0}, {'field': 'count', 'old_value': 529, 'new_value': 549}, {'field': 'instoreAmount', 'old_value': 131542.0, 'new_value': 136120.0}, {'field': 'instoreCount', 'old_value': 529, 'new_value': 549}]
2025-04-26 08:04:51,223 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MQ2
2025-04-26 08:04:51,223 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 62979.7, 'new_value': 66849.7}, {'field': 'dailyBillAmount', 'old_value': 62979.7, 'new_value': 66849.7}, {'field': 'amount', 'old_value': 81628.1, 'new_value': 85397.1}, {'field': 'count', 'old_value': 412, 'new_value': 431}, {'field': 'instoreAmount', 'old_value': 81768.93000000001, 'new_value': 85537.93000000001}, {'field': 'instoreCount', 'old_value': 412, 'new_value': 431}]
2025-04-26 08:04:51,723 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MR2
2025-04-26 08:04:51,723 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 135003.0, 'new_value': 140774.0}, {'field': 'amount', 'old_value': 135003.0, 'new_value': 140774.0}, {'field': 'count', 'old_value': 1413, 'new_value': 1481}, {'field': 'instoreAmount', 'old_value': 135003.0, 'new_value': 140774.0}, {'field': 'instoreCount', 'old_value': 1413, 'new_value': 1481}]
2025-04-26 08:04:52,224 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MS2
2025-04-26 08:04:52,224 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 40210.4, 'new_value': 42027.65}, {'field': 'dailyBillAmount', 'old_value': 40210.4, 'new_value': 42027.65}, {'field': 'amount', 'old_value': 6368.36, 'new_value': 6628.35}, {'field': 'count', 'old_value': 296, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 7458.0599999999995, 'new_value': 7785.07}, {'field': 'instoreCount', 'old_value': 296, 'new_value': 314}]
2025-04-26 08:04:52,724 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3CT5EKW9MT2
2025-04-26 08:04:52,724 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 23825.24, 'new_value': 24738.21}, {'field': 'count', 'old_value': 694, 'new_value': 721}, {'field': 'instoreAmount', 'old_value': 23745.03, 'new_value': 24632.72}, {'field': 'instoreCount', 'old_value': 691, 'new_value': 717}, {'field': 'onlineAmount', 'old_value': 108.53999999999999, 'new_value': 133.82}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-04-26 08:04:53,147 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3DT5EKW9MU2
2025-04-26 08:04:53,147 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 36973.64, 'new_value': 38607.14}, {'field': 'dailyBillAmount', 'old_value': 36973.64, 'new_value': 38607.14}, {'field': 'amount', 'old_value': 58117.2, 'new_value': 60478.2}, {'field': 'count', 'old_value': 212, 'new_value': 221}, {'field': 'instoreAmount', 'old_value': 58695.7, 'new_value': 61056.7}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 220}]
2025-04-26 08:04:53,569 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3DT5EKW9MV2
2025-04-26 08:04:53,569 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 41448.0, 'new_value': 43491.0}, {'field': 'dailyBillAmount', 'old_value': 41448.0, 'new_value': 43491.0}, {'field': 'amount', 'old_value': 41588.0, 'new_value': 43631.0}, {'field': 'count', 'old_value': 204, 'new_value': 210}, {'field': 'instoreAmount', 'old_value': 41758.0, 'new_value': 43801.0}, {'field': 'instoreCount', 'old_value': 204, 'new_value': 210}]
2025-04-26 08:04:54,038 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3DT5EKW9MW2
2025-04-26 08:04:54,038 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 74715.18, 'new_value': 77979.92}, {'field': 'dailyBillAmount', 'old_value': 74715.18, 'new_value': 77979.92}, {'field': 'amount', 'old_value': 70333.97, 'new_value': 73469.06999999999}, {'field': 'count', 'old_value': 2276, 'new_value': 2377}, {'field': 'instoreAmount', 'old_value': 66245.17, 'new_value': 69234.67}, {'field': 'instoreCount', 'old_value': 2106, 'new_value': 2201}, {'field': 'onlineAmount', 'old_value': 4088.7999999999997, 'new_value': 4234.4}, {'field': 'onlineCount', 'old_value': 170, 'new_value': 176}]
2025-04-26 08:04:54,476 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3DT5EKW9MX2
2025-04-26 08:04:54,476 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 46039.41, 'new_value': 47065.14}, {'field': 'dailyBillAmount', 'old_value': 46039.41, 'new_value': 47065.14}, {'field': 'amount', 'old_value': 42861.89, 'new_value': 43831.04}, {'field': 'count', 'old_value': 491, 'new_value': 510}, {'field': 'instoreAmount', 'old_value': 41506.159999999996, 'new_value': 42400.159999999996}, {'field': 'instoreCount', 'old_value': 291, 'new_value': 302}, {'field': 'onlineAmount', 'old_value': 1393.74, 'new_value': 1468.89}, {'field': 'onlineCount', 'old_value': 200, 'new_value': 208}]
2025-04-26 08:04:54,867 - INFO - 更新表单数据成功: FINST-VOC66Y91B9VU0PKQDBVIU8GA7TZU3DT5EKW9MY2
2025-04-26 08:04:54,867 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 200451.98, 'new_value': 210751.6}, {'field': 'dailyBillAmount', 'old_value': 200451.98, 'new_value': 210751.6}, {'field': 'amount', 'old_value': 204359.9, 'new_value': 215266.9}, {'field': 'count', 'old_value': 1330, 'new_value': 1385}, {'field': 'instoreAmount', 'old_value': 199064.0, 'new_value': 209395.0}, {'field': 'instoreCount', 'old_value': 1144, 'new_value': 1190}, {'field': 'onlineAmount', 'old_value': 12575.9, 'new_value': 13151.9}, {'field': 'onlineCount', 'old_value': 186, 'new_value': 195}]
2025-04-26 08:04:54,867 - INFO - 月销售数据同步完成！更新: 203 条，插入: 0 条，错误: 0 条，跳过: 760 条
2025-04-26 08:04:54,867 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-5 至 2025-4
2025-04-26 08:04:55,430 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250426.xlsx
2025-04-26 08:04:55,430 - INFO - 综合数据同步流程完成！
2025-04-26 08:04:55,461 - INFO - 综合数据同步完成
2025-04-26 08:04:55,461 - INFO - ==================================================
2025-04-26 08:04:55,461 - INFO - 程序退出
2025-04-26 08:04:55,461 - INFO - ==================================================
2025-04-26 15:30:07,511 - INFO - ==================================================
2025-04-26 15:30:07,511 - INFO - 程序启动 - 版本 v1.0.0
2025-04-26 15:30:07,511 - INFO - 日志文件: logs\sync_data_20250426.log
2025-04-26 15:30:07,511 - INFO - ==================================================
2025-04-26 15:30:07,511 - INFO - 程序入口点: __main__
2025-04-26 15:30:07,511 - INFO - ==================================================
2025-04-26 15:30:07,511 - INFO - 程序启动 - 版本 v1.0.1
2025-04-26 15:30:07,511 - INFO - 日志文件: logs\sync_data_20250426.log
2025-04-26 15:30:07,511 - INFO - ==================================================
2025-04-26 15:30:07,998 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-26 15:30:07,999 - INFO - sales_data表已存在，无需创建
2025-04-26 15:30:08,000 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-26 15:30:08,000 - INFO - DataSyncManager初始化完成
2025-04-26 15:30:08,000 - INFO - 接收到命令行参数: ['********', '********']
2025-04-26 15:30:08,000 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-26 15:30:08,001 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-26 15:30:08,001 - INFO - 开始综合数据同步流程...
2025-04-26 15:30:08,001 - INFO - 正在获取数衍平台日销售数据...
2025-04-26 15:30:08,002 - INFO - 查询数衍平台数据，时间段为: 2025-01-01, 2025-04-25
2025-04-26 15:30:08,002 - INFO - 正在获取********至********的数据
2025-04-26 15:30:08,002 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:08,002 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8045C8E7B67B643BE0365808E86F2755'}
2025-04-26 15:30:13,854 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:13,870 - INFO - 过滤后保留 1609 条记录
2025-04-26 15:30:15,872 - INFO - 正在获取********至********的数据
2025-04-26 15:30:15,872 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:15,873 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4894DD91CE29218468489628C5A27200'}
2025-04-26 15:30:19,512 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:19,528 - INFO - 过滤后保留 1589 条记录
2025-04-26 15:30:21,544 - INFO - 正在获取********至********的数据
2025-04-26 15:30:21,544 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:21,544 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '41C7DCA644E1A5D4BF293CC8666FF065'}
2025-04-26 15:30:24,546 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:24,562 - INFO - 过滤后保留 1566 条记录
2025-04-26 15:30:26,575 - INFO - 正在获取********至********的数据
2025-04-26 15:30:26,575 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:26,575 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EB908B7E4BF6534B85E149CB0C3E6334'}
2025-04-26 15:30:29,662 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:29,678 - INFO - 过滤后保留 1450 条记录
2025-04-26 15:30:31,686 - INFO - 正在获取********至********的数据
2025-04-26 15:30:31,686 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:31,686 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1369BC40B72F926CAEDB33E25AABE45C'}
2025-04-26 15:30:34,327 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:34,342 - INFO - 过滤后保留 1261 条记录
2025-04-26 15:30:36,359 - INFO - 正在获取********至********的数据
2025-04-26 15:30:36,359 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:36,359 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D6A22568DCD2947562964F7A0F0A6C3E'}
2025-04-26 15:30:39,110 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:39,126 - INFO - 过滤后保留 1567 条记录
2025-04-26 15:30:41,141 - INFO - 正在获取********至********的数据
2025-04-26 15:30:41,141 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:41,141 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E53375699C0CF1EC5EC182A3E7F4ADA1'}
2025-04-26 15:30:43,892 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:43,892 - INFO - 过滤后保留 1577 条记录
2025-04-26 15:30:45,907 - INFO - 正在获取********至********的数据
2025-04-26 15:30:45,907 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:45,907 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CC333639285F1D91F1975627B3451E8A'}
2025-04-26 15:30:48,258 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:48,267 - INFO - 过滤后保留 1549 条记录
2025-04-26 15:30:50,283 - INFO - 正在获取********至********的数据
2025-04-26 15:30:50,283 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:50,283 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '814FB5458328DD51D00DA476009B6758'}
2025-04-26 15:30:52,794 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:30:52,809 - INFO - 过滤后保留 1554 条记录
2025-04-26 15:30:54,812 - INFO - 正在获取********至********的数据
2025-04-26 15:30:54,812 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:30:54,812 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E196CA82AD28822668EBFC7C2A939468'}
2025-04-26 15:31:00,030 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:00,046 - INFO - 过滤后保留 1569 条记录
2025-04-26 15:31:02,049 - INFO - 正在获取********至********的数据
2025-04-26 15:31:02,049 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:02,050 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3EF6786FD82DE28C7A402E61C11EB020'}
2025-04-26 15:31:04,371 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:04,385 - INFO - 过滤后保留 1553 条记录
2025-04-26 15:31:06,389 - INFO - 正在获取********至********的数据
2025-04-26 15:31:06,389 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:06,389 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '400FED43EDFF8286216E0B8678ABF9CB'}
2025-04-26 15:31:08,788 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:08,801 - INFO - 过滤后保留 1562 条记录
2025-04-26 15:31:10,810 - INFO - 正在获取********至********的数据
2025-04-26 15:31:10,810 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:10,810 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A1F31E0A7F2CDFE77422D4AB350791B2'}
2025-04-26 15:31:13,151 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:13,166 - INFO - 过滤后保留 1555 条记录
2025-04-26 15:31:15,169 - INFO - 正在获取********至********的数据
2025-04-26 15:31:15,169 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:15,169 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '035E28BF428505D295AEC8426CDD327A'}
2025-04-26 15:31:17,577 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:17,591 - INFO - 过滤后保留 1496 条记录
2025-04-26 15:31:19,602 - INFO - 正在获取********至********的数据
2025-04-26 15:31:19,602 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:19,602 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '929DC81A33A56FDAA9D3FC84E82E7A33'}
2025-04-26 15:31:22,337 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:22,352 - INFO - 过滤后保留 1490 条记录
2025-04-26 15:31:24,366 - INFO - 正在获取********至********的数据
2025-04-26 15:31:24,366 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:24,366 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7A0595E1D5BDCE943A784FB14F651B35'}
2025-04-26 15:31:26,430 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:26,430 - INFO - 过滤后保留 1486 条记录
2025-04-26 15:31:28,463 - INFO - 正在获取********至********的数据
2025-04-26 15:31:28,463 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-26 15:31:28,463 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1A7C6DF2C33326792047F508C57E96F0'}
2025-04-26 15:31:29,553 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-26 15:31:29,553 - INFO - 过滤后保留 614 条记录
2025-04-26 15:31:31,567 - INFO - 开始保存数据到SQLite数据库，共 25047 条记录待处理
2025-04-26 15:31:33,265 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-04-21
2025-04-26 15:31:33,265 - INFO - 变更字段: amount: 3667 -> 4016, count: 4 -> 5, instore_amount: 3667.6 -> 4016.6, instore_count: 4 -> 5
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: recommend_amount: 10532.0 -> 11770.0, amount: 10532 -> 11770, count: 17 -> 18, instore_amount: 10532.0 -> 11770.0, instore_count: 17 -> 18
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: amount: 163 -> 249, count: 3 -> 5, instore_amount: 163.9 -> 249.65, instore_count: 3 -> 5
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HHDS9O4DUBH2L6U1G19QP11V40018CI, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: amount: 2636 -> 3714, count: 17 -> 19, instore_amount: 2358.8 -> 3436.8, instore_count: 13 -> 15
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: amount: 2520 -> 2762, count: 60 -> 61, instore_amount: 2215.3 -> 2457.13, instore_count: 53 -> 54
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDVG061A0H7Q2OV4FVC7DO0014A9, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: recommend_amount: 3208.9 -> 6208.9, amount: 3208 -> 6208, count: 61 -> 62, instore_amount: 3625.9 -> 6625.9, instore_count: 61 -> 62
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: amount: 4386 -> 4397, count: 172 -> 173, instore_amount: 4474.27 -> 4485.37, instore_count: 172 -> 173
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: recommend_amount: 0.0 -> 2156.81, daily_bill_amount: 0.0 -> 2156.81
2025-04-26 15:31:33,297 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-04-25
2025-04-26 15:31:33,297 - INFO - 变更字段: amount: 5588 -> 6047, count: 14 -> 15, instore_amount: 5588.5 -> 6047.5, instore_count: 14 -> 15
2025-04-26 15:31:33,314 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-04-25
2025-04-26 15:31:33,314 - INFO - 变更字段: amount: 6526 -> 7402, count: 26 -> 28, instore_amount: 6526.0 -> 7402.0, instore_count: 26 -> 28
2025-04-26 15:31:33,315 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16HSQFKC90AM6QNN0HOT12RK0013M7, sale_time=2025-04-25
2025-04-26 15:31:33,315 - INFO - 变更字段: recommend_amount: 0.0 -> 887.69, daily_bill_amount: 0.0 -> 887.69
2025-04-26 15:31:33,316 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEPVAL5TUK42F6DB81RHA2001P6A, sale_time=2025-04-25
2025-04-26 15:31:33,317 - INFO - 变更字段: recommend_amount: 2292.07 -> 2279.15, amount: 2292 -> 2279
2025-04-26 15:31:33,318 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-04-25
2025-04-26 15:31:33,318 - INFO - 变更字段: recommend_amount: 1710.0 -> 1873.0, daily_bill_amount: 1710.0 -> 1873.0
2025-04-26 15:31:33,318 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-04-25
2025-04-26 15:31:33,319 - INFO - 变更字段: recommend_amount: 5102.25 -> 5142.85, amount: 5102 -> 5142, count: 147 -> 148, online_amount: 196.3 -> 236.9, online_count: 5 -> 6
2025-04-26 15:31:33,320 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-04-25
2025-04-26 15:31:33,321 - INFO - 变更字段: recommend_amount: 6389.08 -> 6400.08, amount: 6389 -> 6400, count: 246 -> 247, online_amount: 3967.06 -> 3978.06, online_count: 146 -> 147
2025-04-26 15:31:33,321 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-04-25
2025-04-26 15:31:33,321 - INFO - 变更字段: recommend_amount: 0.0 -> 131.0, daily_bill_amount: 0.0 -> 131.0
2025-04-26 15:31:33,324 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-04-24
2025-04-26 15:31:33,324 - INFO - 变更字段: recommend_amount: 17606.08 -> 27529.0, daily_bill_amount: 17606.08 -> 27529.0
2025-04-26 15:31:33,324 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-04-25
2025-04-26 15:31:33,325 - INFO - 变更字段: amount: 10317 -> 10502, count: 84 -> 85, online_amount: 2535.3 -> 2720.4, online_count: 42 -> 43
2025-04-26 15:31:33,325 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-04-25
2025-04-26 15:31:33,326 - INFO - 变更字段: amount: 6405 -> 6449, count: 193 -> 194, online_amount: 6006.55 -> 6051.15, online_count: 185 -> 186
2025-04-26 15:31:33,327 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-04-25
2025-04-26 15:31:33,327 - INFO - 变更字段: amount: 847 -> 808
2025-04-26 15:31:33,327 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDF8HFHI690I86N3H2U1H9001EQF, sale_time=2025-04-25
2025-04-26 15:31:33,327 - INFO - 变更字段: amount: 1574 -> 2283, count: 5 -> 6, instore_amount: 1574.0 -> 2283.0, instore_count: 5 -> 6
2025-04-26 15:31:33,327 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-04-25
2025-04-26 15:31:33,327 - INFO - 变更字段: recommend_amount: 0.0 -> 1874.0, daily_bill_amount: 0.0 -> 1874.0
2025-04-26 15:31:33,327 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-04-25
2025-04-26 15:31:33,327 - INFO - 变更字段: amount: 4726 -> 4815, count: 258 -> 271, online_amount: 4263.52 -> 4353.03, online_count: 237 -> 250
2025-04-26 15:31:33,327 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSK489TE20I86N3H2U114001EAA, sale_time=2025-04-25
2025-04-26 15:31:33,327 - INFO - 变更字段: recommend_amount: 3408.4 -> 3521.83, amount: 3408 -> 3521, count: 89 -> 90, instore_amount: 2916.8 -> 3030.23, instore_count: 81 -> 82
2025-04-26 15:31:33,333 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-04-25
2025-04-26 15:31:33,333 - INFO - 变更字段: count: 130 -> 131, instore_amount: 569.04 -> 581.54, instore_count: 29 -> 30
2025-04-26 15:31:33,334 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQQG9THS10I86N3H2U108001E9E, sale_time=2025-04-25
2025-04-26 15:31:33,334 - INFO - 变更字段: amount: 7991 -> 8139, count: 224 -> 225, instore_amount: 8137.0 -> 8285.0, instore_count: 224 -> 225
2025-04-26 15:31:33,335 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-04-25
2025-04-26 15:31:33,335 - INFO - 变更字段: amount: 8034 -> 8020, count: 573 -> 576, instore_amount: 3502.9 -> 3508.79, instore_count: 176 -> 177, online_amount: 5864.33 -> 5871.33, online_count: 397 -> 399
2025-04-26 15:31:33,336 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-04-25
2025-04-26 15:31:33,336 - INFO - 变更字段: amount: 12048 -> 12455, count: 202 -> 207, online_amount: 1023.19 -> 1430.19, online_count: 17 -> 22
2025-04-26 15:31:33,337 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-04-25
2025-04-26 15:31:33,338 - INFO - 变更字段: recommend_amount: 15110.48 -> 15400.2, amount: 15110 -> 15400, count: 487 -> 504, online_amount: 15336.57 -> 15665.19, online_count: 487 -> 504
2025-04-26 15:31:33,338 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-04-24
2025-04-26 15:31:33,338 - INFO - 变更字段: recommend_amount: 13028.62 -> 13049.62, amount: 13028 -> 13049, count: 519 -> 520, online_amount: 13341.3 -> 13362.3, online_count: 519 -> 520
2025-04-26 15:31:33,339 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-04-23
2025-04-26 15:31:33,339 - INFO - 变更字段: recommend_amount: 13695.28 -> 13708.28, amount: 13695 -> 13708, count: 497 -> 498, online_amount: 14139.28 -> 14152.28, online_count: 497 -> 498
2025-04-26 15:31:33,340 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FHI5VTHC3RHRI7Q2OVAE57DT4001C39, sale_time=2025-04-25
2025-04-26 15:31:33,340 - INFO - 变更字段: amount: 21902 -> 21598
2025-04-26 15:31:33,340 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MRVUM0P77G7Q2OV78BKOG4001PUK, sale_time=2025-04-25
2025-04-26 15:31:33,341 - INFO - 变更字段: count: 100 -> 101, instore_count: 73 -> 74
2025-04-26 15:31:33,343 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-04-25
2025-04-26 15:31:33,343 - INFO - 变更字段: amount: 15844 -> 16505, count: 184 -> 186, instore_amount: 15190.75 -> 15851.65, instore_count: 126 -> 128
2025-04-26 15:31:33,344 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8, sale_time=2025-04-25
2025-04-26 15:31:33,344 - INFO - 变更字段: amount: 12606 -> 13018, count: 96 -> 97, instore_amount: 12606.0 -> 13018.0, instore_count: 96 -> 97
2025-04-26 15:31:33,347 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRBVE2CQT7AV8LHQQGID9001EJI, sale_time=2025-04-25
2025-04-26 15:31:33,347 - INFO - 变更字段: recommend_amount: 0.0 -> 24922.04, daily_bill_amount: 0.0 -> 24922.04, amount: 35640 -> 36864, count: 206 -> 209, instore_amount: 28516.8 -> 29740.7, instore_count: 173 -> 176
2025-04-26 15:31:33,348 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-04-25
2025-04-26 15:31:33,349 - INFO - 变更字段: amount: 8457 -> 8581, count: 277 -> 284, instore_amount: 5651.84 -> 5776.74, instore_count: 167 -> 174
2025-04-26 15:31:33,349 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-04-24
2025-04-26 15:31:33,349 - INFO - 变更字段: recommend_amount: 11522.08 -> 11540.98, amount: 11522 -> 11540, count: 263 -> 264, instore_amount: 8755.19 -> 8774.09, instore_count: 172 -> 173
2025-04-26 15:31:33,374 - INFO - SQLite数据保存完成，统计信息：
2025-04-26 15:31:33,375 - INFO - - 总记录数: 25047
2025-04-26 15:31:33,375 - INFO - - 成功插入: 3
2025-04-26 15:31:33,375 - INFO - - 成功更新: 38
2025-04-26 15:31:33,375 - INFO - - 无需更新: 25006
2025-04-26 15:31:33,376 - INFO - - 处理失败: 0
2025-04-26 15:31:43,886 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250426.xlsx
2025-04-26 15:31:43,902 - INFO - 成功获取数衍平台数据，共 25047 条记录
2025-04-26 15:31:43,902 - INFO - 正在更新SQLite月度汇总数据...
2025-04-26 15:31:43,902 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-04-25
2025-04-26 15:31:44,102 - INFO - 月度汇总数据更新完成，处理了 963 条汇总记录
2025-04-26 15:31:44,102 - INFO - 成功更新月度汇总数据，共 963 条记录
2025-04-26 15:31:44,102 - INFO - 正在获取宜搭日销售表单数据...
2025-04-26 15:31:44,102 - INFO - 开始获取宜搭每日表单数据，时间范围: 2025-01-01 00:00:00 至 2025-04-25 00:00:00
2025-04-26 15:31:44,102 - INFO - Request Parameters - Page 1:
2025-04-26 15:31:44,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:44,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:46,582 - INFO - Response - Page 1:
2025-04-26 15:31:46,582 - INFO - 第 1 页获取到 100 条记录
2025-04-26 15:31:46,582 - INFO - Request Parameters - Page 2:
2025-04-26 15:31:46,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:46,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:48,072 - INFO - Response - Page 2:
2025-04-26 15:31:48,072 - INFO - 第 2 页获取到 100 条记录
2025-04-26 15:31:48,073 - INFO - Request Parameters - Page 3:
2025-04-26 15:31:48,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:48,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:48,636 - INFO - Response - Page 3:
2025-04-26 15:31:48,636 - INFO - 第 3 页获取到 100 条记录
2025-04-26 15:31:48,636 - INFO - Request Parameters - Page 4:
2025-04-26 15:31:48,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:48,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:49,306 - INFO - Response - Page 4:
2025-04-26 15:31:49,306 - INFO - 第 4 页获取到 100 条记录
2025-04-26 15:31:49,306 - INFO - Request Parameters - Page 5:
2025-04-26 15:31:49,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:49,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:49,930 - INFO - Response - Page 5:
2025-04-26 15:31:49,930 - INFO - 第 5 页获取到 100 条记录
2025-04-26 15:31:49,930 - INFO - Request Parameters - Page 6:
2025-04-26 15:31:49,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:49,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:50,601 - INFO - Response - Page 6:
2025-04-26 15:31:50,601 - INFO - 第 6 页获取到 100 条记录
2025-04-26 15:31:50,601 - INFO - Request Parameters - Page 7:
2025-04-26 15:31:50,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:50,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:51,227 - INFO - Response - Page 7:
2025-04-26 15:31:51,227 - INFO - 第 7 页获取到 100 条记录
2025-04-26 15:31:51,228 - INFO - Request Parameters - Page 8:
2025-04-26 15:31:51,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:51,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:51,815 - INFO - Response - Page 8:
2025-04-26 15:31:51,815 - INFO - 第 8 页获取到 100 条记录
2025-04-26 15:31:51,815 - INFO - Request Parameters - Page 9:
2025-04-26 15:31:51,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:51,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:52,454 - INFO - Response - Page 9:
2025-04-26 15:31:52,454 - INFO - 第 9 页获取到 100 条记录
2025-04-26 15:31:52,454 - INFO - Request Parameters - Page 10:
2025-04-26 15:31:52,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:52,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:53,046 - INFO - Response - Page 10:
2025-04-26 15:31:53,046 - INFO - 第 10 页获取到 100 条记录
2025-04-26 15:31:53,046 - INFO - Request Parameters - Page 11:
2025-04-26 15:31:53,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:53,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:53,679 - INFO - Response - Page 11:
2025-04-26 15:31:53,679 - INFO - 第 11 页获取到 100 条记录
2025-04-26 15:31:53,680 - INFO - Request Parameters - Page 12:
2025-04-26 15:31:53,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:53,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:54,303 - INFO - Response - Page 12:
2025-04-26 15:31:54,303 - INFO - 第 12 页获取到 100 条记录
2025-04-26 15:31:54,304 - INFO - Request Parameters - Page 13:
2025-04-26 15:31:54,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:54,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:54,916 - INFO - Response - Page 13:
2025-04-26 15:31:54,916 - INFO - 第 13 页获取到 100 条记录
2025-04-26 15:31:54,916 - INFO - Request Parameters - Page 14:
2025-04-26 15:31:54,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:54,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:55,479 - INFO - Response - Page 14:
2025-04-26 15:31:55,480 - INFO - 第 14 页获取到 100 条记录
2025-04-26 15:31:55,480 - INFO - Request Parameters - Page 15:
2025-04-26 15:31:55,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:55,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:56,134 - INFO - Response - Page 15:
2025-04-26 15:31:56,135 - INFO - 第 15 页获取到 100 条记录
2025-04-26 15:31:56,135 - INFO - Request Parameters - Page 16:
2025-04-26 15:31:56,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:56,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:56,805 - INFO - Response - Page 16:
2025-04-26 15:31:56,805 - INFO - 第 16 页获取到 100 条记录
2025-04-26 15:31:56,805 - INFO - Request Parameters - Page 17:
2025-04-26 15:31:56,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:56,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:57,351 - INFO - Response - Page 17:
2025-04-26 15:31:57,351 - INFO - 第 17 页获取到 100 条记录
2025-04-26 15:31:57,351 - INFO - Request Parameters - Page 18:
2025-04-26 15:31:57,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:57,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:57,943 - INFO - Response - Page 18:
2025-04-26 15:31:57,943 - INFO - 第 18 页获取到 100 条记录
2025-04-26 15:31:57,943 - INFO - Request Parameters - Page 19:
2025-04-26 15:31:57,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:57,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:58,568 - INFO - Response - Page 19:
2025-04-26 15:31:58,568 - INFO - 第 19 页获取到 100 条记录
2025-04-26 15:31:58,568 - INFO - Request Parameters - Page 20:
2025-04-26 15:31:58,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:58,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:59,127 - INFO - Response - Page 20:
2025-04-26 15:31:59,127 - INFO - 第 20 页获取到 100 条记录
2025-04-26 15:31:59,127 - INFO - Request Parameters - Page 21:
2025-04-26 15:31:59,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:59,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:31:59,744 - INFO - Response - Page 21:
2025-04-26 15:31:59,745 - INFO - 第 21 页获取到 100 条记录
2025-04-26 15:31:59,745 - INFO - Request Parameters - Page 22:
2025-04-26 15:31:59,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:31:59,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:00,315 - INFO - Response - Page 22:
2025-04-26 15:32:00,315 - INFO - 第 22 页获取到 100 条记录
2025-04-26 15:32:00,315 - INFO - Request Parameters - Page 23:
2025-04-26 15:32:00,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:00,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:00,910 - INFO - Response - Page 23:
2025-04-26 15:32:00,910 - INFO - 第 23 页获取到 100 条记录
2025-04-26 15:32:00,910 - INFO - Request Parameters - Page 24:
2025-04-26 15:32:00,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:00,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:01,446 - INFO - Response - Page 24:
2025-04-26 15:32:01,447 - INFO - 第 24 页获取到 100 条记录
2025-04-26 15:32:01,447 - INFO - Request Parameters - Page 25:
2025-04-26 15:32:01,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:01,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:02,113 - INFO - Response - Page 25:
2025-04-26 15:32:02,113 - INFO - 第 25 页获取到 100 条记录
2025-04-26 15:32:02,113 - INFO - Request Parameters - Page 26:
2025-04-26 15:32:02,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:02,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:02,734 - INFO - Response - Page 26:
2025-04-26 15:32:02,734 - INFO - 第 26 页获取到 100 条记录
2025-04-26 15:32:02,734 - INFO - Request Parameters - Page 27:
2025-04-26 15:32:02,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:02,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:03,407 - INFO - Response - Page 27:
2025-04-26 15:32:03,407 - INFO - 第 27 页获取到 100 条记录
2025-04-26 15:32:03,408 - INFO - Request Parameters - Page 28:
2025-04-26 15:32:03,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:03,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:04,025 - INFO - Response - Page 28:
2025-04-26 15:32:04,025 - INFO - 第 28 页获取到 100 条记录
2025-04-26 15:32:04,025 - INFO - Request Parameters - Page 29:
2025-04-26 15:32:04,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:04,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:04,665 - INFO - Response - Page 29:
2025-04-26 15:32:04,665 - INFO - 第 29 页获取到 100 条记录
2025-04-26 15:32:04,665 - INFO - Request Parameters - Page 30:
2025-04-26 15:32:04,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:04,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:05,370 - INFO - Response - Page 30:
2025-04-26 15:32:05,370 - INFO - 第 30 页获取到 100 条记录
2025-04-26 15:32:05,371 - INFO - Request Parameters - Page 31:
2025-04-26 15:32:05,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:05,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:06,042 - INFO - Response - Page 31:
2025-04-26 15:32:06,042 - INFO - 第 31 页获取到 100 条记录
2025-04-26 15:32:06,042 - INFO - Request Parameters - Page 32:
2025-04-26 15:32:06,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:06,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:06,637 - INFO - Response - Page 32:
2025-04-26 15:32:06,637 - INFO - 第 32 页获取到 100 条记录
2025-04-26 15:32:06,637 - INFO - Request Parameters - Page 33:
2025-04-26 15:32:06,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:06,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:07,191 - INFO - Response - Page 33:
2025-04-26 15:32:07,192 - INFO - 第 33 页获取到 100 条记录
2025-04-26 15:32:07,192 - INFO - Request Parameters - Page 34:
2025-04-26 15:32:07,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:07,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:07,799 - INFO - Response - Page 34:
2025-04-26 15:32:07,799 - INFO - 第 34 页获取到 100 条记录
2025-04-26 15:32:07,799 - INFO - Request Parameters - Page 35:
2025-04-26 15:32:07,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:07,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:08,476 - INFO - Response - Page 35:
2025-04-26 15:32:08,476 - INFO - 第 35 页获取到 100 条记录
2025-04-26 15:32:08,476 - INFO - Request Parameters - Page 36:
2025-04-26 15:32:08,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:08,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:09,067 - INFO - Response - Page 36:
2025-04-26 15:32:09,067 - INFO - 第 36 页获取到 100 条记录
2025-04-26 15:32:09,068 - INFO - Request Parameters - Page 37:
2025-04-26 15:32:09,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:09,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:09,761 - INFO - Response - Page 37:
2025-04-26 15:32:09,761 - INFO - 第 37 页获取到 100 条记录
2025-04-26 15:32:09,762 - INFO - Request Parameters - Page 38:
2025-04-26 15:32:09,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:09,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:10,356 - INFO - Response - Page 38:
2025-04-26 15:32:10,356 - INFO - 第 38 页获取到 100 条记录
2025-04-26 15:32:10,356 - INFO - Request Parameters - Page 39:
2025-04-26 15:32:10,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:10,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:11,017 - INFO - Response - Page 39:
2025-04-26 15:32:11,017 - INFO - 第 39 页获取到 100 条记录
2025-04-26 15:32:11,018 - INFO - Request Parameters - Page 40:
2025-04-26 15:32:11,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:11,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:11,660 - INFO - Response - Page 40:
2025-04-26 15:32:11,661 - INFO - 第 40 页获取到 100 条记录
2025-04-26 15:32:11,661 - INFO - Request Parameters - Page 41:
2025-04-26 15:32:11,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:11,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:12,259 - INFO - Response - Page 41:
2025-04-26 15:32:12,259 - INFO - 第 41 页获取到 100 条记录
2025-04-26 15:32:12,259 - INFO - Request Parameters - Page 42:
2025-04-26 15:32:12,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:12,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:12,881 - INFO - Response - Page 42:
2025-04-26 15:32:12,881 - INFO - 第 42 页获取到 100 条记录
2025-04-26 15:32:12,881 - INFO - Request Parameters - Page 43:
2025-04-26 15:32:12,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:12,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:13,506 - INFO - Response - Page 43:
2025-04-26 15:32:13,507 - INFO - 第 43 页获取到 100 条记录
2025-04-26 15:32:13,507 - INFO - Request Parameters - Page 44:
2025-04-26 15:32:13,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:13,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:14,079 - INFO - Response - Page 44:
2025-04-26 15:32:14,079 - INFO - 第 44 页获取到 100 条记录
2025-04-26 15:32:14,079 - INFO - Request Parameters - Page 45:
2025-04-26 15:32:14,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:14,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:14,707 - INFO - Response - Page 45:
2025-04-26 15:32:14,707 - INFO - 第 45 页获取到 100 条记录
2025-04-26 15:32:14,708 - INFO - Request Parameters - Page 46:
2025-04-26 15:32:14,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:14,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:15,321 - INFO - Response - Page 46:
2025-04-26 15:32:15,321 - INFO - 第 46 页获取到 100 条记录
2025-04-26 15:32:15,321 - INFO - Request Parameters - Page 47:
2025-04-26 15:32:15,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:15,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:15,981 - INFO - Response - Page 47:
2025-04-26 15:32:15,982 - INFO - 第 47 页获取到 100 条记录
2025-04-26 15:32:15,982 - INFO - Request Parameters - Page 48:
2025-04-26 15:32:15,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:15,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:16,571 - INFO - Response - Page 48:
2025-04-26 15:32:16,572 - INFO - 第 48 页获取到 100 条记录
2025-04-26 15:32:16,572 - INFO - Request Parameters - Page 49:
2025-04-26 15:32:16,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:16,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:17,250 - INFO - Response - Page 49:
2025-04-26 15:32:17,250 - INFO - 第 49 页获取到 100 条记录
2025-04-26 15:32:17,251 - INFO - Request Parameters - Page 50:
2025-04-26 15:32:17,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:17,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 50, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:17,858 - INFO - Response - Page 50:
2025-04-26 15:32:17,859 - INFO - 第 50 页获取到 100 条记录
2025-04-26 15:32:17,859 - INFO - Request Parameters - Page 51:
2025-04-26 15:32:17,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:17,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 51, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:18,481 - INFO - Response - Page 51:
2025-04-26 15:32:18,481 - INFO - 第 51 页获取到 100 条记录
2025-04-26 15:32:18,481 - INFO - Request Parameters - Page 52:
2025-04-26 15:32:18,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:18,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 52, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:19,037 - INFO - Response - Page 52:
2025-04-26 15:32:19,038 - INFO - 第 52 页获取到 100 条记录
2025-04-26 15:32:19,038 - INFO - Request Parameters - Page 53:
2025-04-26 15:32:19,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:19,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 53, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:19,700 - INFO - Response - Page 53:
2025-04-26 15:32:19,716 - INFO - 第 53 页获取到 100 条记录
2025-04-26 15:32:19,716 - INFO - Request Parameters - Page 54:
2025-04-26 15:32:19,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:19,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 54, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:20,261 - INFO - Response - Page 54:
2025-04-26 15:32:20,261 - INFO - 第 54 页获取到 100 条记录
2025-04-26 15:32:20,261 - INFO - Request Parameters - Page 55:
2025-04-26 15:32:20,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:20,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 55, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:20,938 - INFO - Response - Page 55:
2025-04-26 15:32:20,938 - INFO - 第 55 页获取到 100 条记录
2025-04-26 15:32:20,939 - INFO - Request Parameters - Page 56:
2025-04-26 15:32:20,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:20,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 56, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:21,536 - INFO - Response - Page 56:
2025-04-26 15:32:21,536 - INFO - 第 56 页获取到 100 条记录
2025-04-26 15:32:21,537 - INFO - Request Parameters - Page 57:
2025-04-26 15:32:21,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:21,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 57, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:22,168 - INFO - Response - Page 57:
2025-04-26 15:32:22,168 - INFO - 第 57 页获取到 100 条记录
2025-04-26 15:32:22,168 - INFO - Request Parameters - Page 58:
2025-04-26 15:32:22,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:22,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 58, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:22,807 - INFO - Response - Page 58:
2025-04-26 15:32:22,807 - INFO - 第 58 页获取到 100 条记录
2025-04-26 15:32:22,808 - INFO - Request Parameters - Page 59:
2025-04-26 15:32:22,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:22,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 59, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:23,396 - INFO - Response - Page 59:
2025-04-26 15:32:23,396 - INFO - 第 59 页获取到 100 条记录
2025-04-26 15:32:23,397 - INFO - Request Parameters - Page 60:
2025-04-26 15:32:23,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 15:32:23,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 60, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1745510400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 15:32:23,816 - INFO - ==================================================
2025-04-26 15:32:23,816 - INFO - 程序退出
2025-04-26 15:32:23,816 - INFO - ==================================================
