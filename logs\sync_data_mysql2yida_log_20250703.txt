2025-07-03 01:30:34,092 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 01:30:34,092 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 01:30:34,092 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 01:30:34,170 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 0 条记录
2025-07-03 01:30:34,170 - ERROR - 未获取到MySQL数据
2025-07-03 01:31:34,185 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 01:31:34,185 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 01:31:34,185 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 01:31:34,310 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 77 条记录
2025-07-03 01:31:34,310 - INFO - 获取到 1 个日期需要处理: ['2025-07-02']
2025-07-03 01:31:34,310 - INFO - 开始处理日期: 2025-07-02
2025-07-03 01:31:34,310 - INFO - Request Parameters - Page 1:
2025-07-03 01:31:34,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 01:31:34,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 01:31:42,435 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5D469EA0-4601-7F3C-A600-B3931EBE50FD Response: {'code': 'ServiceUnavailable', 'requestid': '5D469EA0-4601-7F3C-A600-B3931EBE50FD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5D469EA0-4601-7F3C-A600-B3931EBE50FD)
2025-07-03 01:31:42,435 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-03 01:31:42,435 - INFO - 同步完成
2025-07-03 04:30:34,085 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 04:30:34,085 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 04:30:34,085 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 04:30:34,163 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 0 条记录
2025-07-03 04:30:34,163 - ERROR - 未获取到MySQL数据
2025-07-03 04:31:34,178 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 04:31:34,178 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 04:31:34,178 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 04:31:34,303 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 77 条记录
2025-07-03 04:31:34,303 - INFO - 获取到 1 个日期需要处理: ['2025-07-02']
2025-07-03 04:31:34,303 - INFO - 开始处理日期: 2025-07-02
2025-07-03 04:31:34,319 - INFO - Request Parameters - Page 1:
2025-07-03 04:31:34,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 04:31:34,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 04:31:42,444 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 45A69080-8973-76E7-B0E7-A9F037109264 Response: {'code': 'ServiceUnavailable', 'requestid': '45A69080-8973-76E7-B0E7-A9F037109264', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 45A69080-8973-76E7-B0E7-A9F037109264)
2025-07-03 04:31:42,444 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-03 04:31:42,444 - INFO - 同步完成
2025-07-03 07:30:34,233 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 07:30:34,233 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 07:30:34,233 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 07:30:34,311 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 0 条记录
2025-07-03 07:30:34,327 - ERROR - 未获取到MySQL数据
2025-07-03 07:31:34,342 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 07:31:34,342 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 07:31:34,342 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 07:31:34,467 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 77 条记录
2025-07-03 07:31:34,467 - INFO - 获取到 1 个日期需要处理: ['2025-07-02']
2025-07-03 07:31:34,467 - INFO - 开始处理日期: 2025-07-02
2025-07-03 07:31:34,467 - INFO - Request Parameters - Page 1:
2025-07-03 07:31:34,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 07:31:34,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 07:31:40,764 - INFO - Response - Page 1:
2025-07-03 07:31:40,764 - INFO - 第 1 页获取到 50 条记录
2025-07-03 07:31:41,280 - INFO - Request Parameters - Page 2:
2025-07-03 07:31:41,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 07:31:41,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 07:31:49,389 - INFO - Response - Page 2:
2025-07-03 07:31:49,389 - INFO - 第 2 页获取到 1 条记录
2025-07-03 07:31:49,905 - INFO - 查询完成，共获取到 51 条记录
2025-07-03 07:31:49,905 - INFO - 获取到 51 条表单数据
2025-07-03 07:31:49,905 - INFO - 当前日期 2025-07-02 有 73 条MySQL数据需要处理
2025-07-03 07:31:49,905 - INFO - 开始更新记录 - 表单实例ID: FINST-B1D66U616QRWM4HE9TF9O9ZJ51W62I7WBCLCMZ
2025-07-03 07:31:50,405 - INFO - 更新表单数据成功: FINST-B1D66U616QRWM4HE9TF9O9ZJ51W62I7WBCLCMZ
2025-07-03 07:31:50,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 6962.92, 'new_value': 11347.7}, {'field': 'total_amount', 'old_value': 7062.92, 'new_value': 11347.7}, {'field': 'order_count', 'old_value': 13, 'new_value': 28}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/498fbb27c35f48cdb0787a85667b6767.jpg?Expires=2066720553&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=HkJvHZYHSKKJm6RQketqFiNd%2B9w%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d686bf478a1d4f89bbe4926ea957c5fb.jpg?Expires=2066720548&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=9JmhlCXNSmVEOUwWyOjMaWMZok8%3D'}]
2025-07-03 07:31:50,405 - INFO - 开始批量插入 22 条新记录
2025-07-03 07:31:50,608 - INFO - 批量插入响应状态码: 200
2025-07-03 07:31:50,608 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 02 Jul 2025 23:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1068', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D8F5D616-4D98-736C-952B-761F80CD8C23', 'x-acs-trace-id': '4b2d521b0f29f9930af1eab3a7125882', 'etag': '1hHT4tf2uyDjdCongIO6YaQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 07:31:50,608 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM1J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM2J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM3J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM4J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM5J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM6J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM7J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM8J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM9J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMAJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMBJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMCJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMDJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMEJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMFJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMGJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMHJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMIJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMJJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMKJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMLJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMMJ']}
2025-07-03 07:31:50,608 - INFO - 批量插入表单数据成功，批次 1，共 22 条记录
2025-07-03 07:31:50,608 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM1J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM2J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM3J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM4J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM5J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM6J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM7J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM8J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCM9J', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMAJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMBJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMCJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMDJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMEJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMFJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMGJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMHJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMIJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMJJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMKJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMLJ', 'FINST-7PF66CC1D5SWHPW07NW0U7PGLV8P3BYICLMCMMJ']
2025-07-03 07:31:55,623 - INFO - 批量插入完成，共 22 条记录
2025-07-03 07:31:55,623 - INFO - 日期 2025-07-02 处理完成 - 更新: 1 条，插入: 22 条，错误: 0 条
2025-07-03 07:31:55,623 - INFO - 数据同步完成！更新: 1 条，插入: 22 条，错误: 0 条
2025-07-03 07:31:55,623 - INFO - 同步完成
2025-07-03 10:30:33,996 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 10:30:33,996 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 10:30:33,996 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 10:30:34,137 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 173 条记录
2025-07-03 10:30:34,137 - INFO - 获取到 3 个日期需要处理: ['2025-06-20', '2025-07-02', '2025-07-03']
2025-07-03 10:30:34,137 - INFO - 开始处理日期: 2025-06-20
2025-07-03 10:30:34,137 - INFO - Request Parameters - Page 1:
2025-07-03 10:30:34,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 10:30:34,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 10:30:42,262 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B892B1E5-CFA0-765C-B963-9A3BE6EF1604 Response: {'code': 'ServiceUnavailable', 'requestid': 'B892B1E5-CFA0-765C-B963-9A3BE6EF1604', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B892B1E5-CFA0-765C-B963-9A3BE6EF1604)
2025-07-03 10:30:42,262 - INFO - 开始处理日期: 2025-07-02
2025-07-03 10:30:42,262 - INFO - Request Parameters - Page 1:
2025-07-03 10:30:42,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 10:30:42,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 10:30:50,387 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE54AE62-39E0-7AD4-B3FA-BCDB954E27B8 Response: {'code': 'ServiceUnavailable', 'requestid': 'BE54AE62-39E0-7AD4-B3FA-BCDB954E27B8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE54AE62-39E0-7AD4-B3FA-BCDB954E27B8)
2025-07-03 10:30:50,387 - INFO - 开始处理日期: 2025-07-03
2025-07-03 10:30:50,387 - INFO - Request Parameters - Page 1:
2025-07-03 10:30:50,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 10:30:50,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 10:30:51,293 - INFO - Response - Page 1:
2025-07-03 10:30:51,293 - INFO - 查询完成，共获取到 0 条记录
2025-07-03 10:30:51,293 - INFO - 获取到 0 条表单数据
2025-07-03 10:30:51,293 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 10:30:51,293 - INFO - 开始批量插入 1 条新记录
2025-07-03 10:30:51,465 - INFO - 批量插入响应状态码: 200
2025-07-03 10:30:51,465 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F2EBED34-2BB0-7D7D-AA74-591E63A237F8', 'x-acs-trace-id': 'ec84e3058b45edcc29174c0c3e17a62d', 'etag': '6iqbDfT6L+X8jlAo7NqK/kQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:30:51,465 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91DZSWESCHF7PV080NOY0O3BPQQRMCMD1']}
2025-07-03 10:30:51,465 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-03 10:30:51,465 - INFO - 成功插入的数据ID: ['FINST-XMC66R91DZSWESCHF7PV080NOY0O3BPQQRMCMD1']
2025-07-03 10:30:56,481 - INFO - 批量插入完成，共 1 条记录
2025-07-03 10:30:56,481 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-03 10:30:56,481 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-07-03 10:31:56,496 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 10:31:56,496 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 10:31:56,496 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 10:31:56,637 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 522 条记录
2025-07-03 10:31:56,637 - INFO - 获取到 2 个日期需要处理: ['2025-07-02', '2025-07-03']
2025-07-03 10:31:56,652 - INFO - 开始处理日期: 2025-07-02
2025-07-03 10:31:56,652 - INFO - Request Parameters - Page 1:
2025-07-03 10:31:56,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 10:31:56,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 10:32:03,058 - INFO - Response - Page 1:
2025-07-03 10:32:03,058 - INFO - 第 1 页获取到 50 条记录
2025-07-03 10:32:03,574 - INFO - Request Parameters - Page 2:
2025-07-03 10:32:03,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 10:32:03,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 10:32:04,246 - INFO - Response - Page 2:
2025-07-03 10:32:04,246 - INFO - 第 2 页获取到 23 条记录
2025-07-03 10:32:04,746 - INFO - 查询完成，共获取到 73 条记录
2025-07-03 10:32:04,746 - INFO - 获取到 73 条表单数据
2025-07-03 10:32:04,746 - INFO - 当前日期 2025-07-02 有 508 条MySQL数据需要处理
2025-07-03 10:32:04,746 - INFO - 开始更新记录 - 表单实例ID: FINST-B1D66U616QRWM4HE9TF9O9ZJ51W62I7WBCLCM11
2025-07-03 10:32:05,449 - INFO - 更新表单数据成功: FINST-B1D66U616QRWM4HE9TF9O9ZJ51W62I7WBCLCM11
2025-07-03 10:32:05,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35484.0, 'new_value': 21628.0}, {'field': 'total_amount', 'old_value': 35484.0, 'new_value': 21628.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/42f10e113c1a4a8297a8bc43335738e4.jpg?Expires=2066720553&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=7Ccv%2By5EGLd6DFFMgS%2Bhcg%2FhkHs%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/4a86414db680483293a2b15ef60f7ce1.jpg?Expires=2066720553&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=90VjJ0Fylnuicot7rKubXvfbXVw%3D'}]
2025-07-03 10:32:05,449 - INFO - 开始批量插入 435 条新记录
2025-07-03 10:32:05,730 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:05,730 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BA35C052-E148-7CFF-861F-5DC1AC1C67BB', 'x-acs-trace-id': '928b04d08f154f6425b735688bc32139', 'etag': '25lfzzm9uCUi+CBGhkgFuvw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:05,730 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM01', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM11', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM21', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM31', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM41', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM51', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM61', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM71', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM81', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM91', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMA1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMB1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMC1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMD1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCME1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMF1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMG1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMH1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMI1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMJ1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMK1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCML1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMM1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMN1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMO1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMP1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMQ1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMR1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMS1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMT1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMU1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMV1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMW1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMX1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMY1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMZ1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM02', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM12', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM22', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM32', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM42', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM52', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM62', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM72', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM82', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM92', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMA2', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMB2', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2I0CSRMCMC2', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2I0CSRMCMD2']}
2025-07-03 10:32:05,730 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-03 10:32:05,730 - INFO - 成功插入的数据ID: ['FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM01', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM11', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM21', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM31', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM41', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM51', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM61', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM71', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM81', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM91', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMA1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMB1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMC1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMD1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCME1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMF1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMG1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMH1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMI1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMJ1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMK1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCML1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMM1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMN1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMO1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMP1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMQ1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMR1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMS1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMT1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMU1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMV1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMW1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMX1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMY1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMZ1', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM02', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM12', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM22', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM32', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM42', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM52', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM62', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM72', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM82', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCM92', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMA2', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMB2', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2I0CSRMCMC2', 'FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2I0CSRMCMD2']
2025-07-03 10:32:10,996 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:10,996 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2886F25-31DA-7D49-A084-FEA8FE8AF1AE', 'x-acs-trace-id': '68946ad7468544084a41e1b158ea60ed', 'etag': '2z9gnITeKUtlhbFMeYbuDnQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:10,996 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM28', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM38', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM48', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM58', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM68', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM78', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM88', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM98', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMA8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMB8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMC8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMD8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCME8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMF8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMG8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMH8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMI8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMJ8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMK8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCML8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMM8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMN8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMO8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMP8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMQ8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMR8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMS8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMT8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMU8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMV8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMW8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMX8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMY8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMZ8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM09', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM19', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM29', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM39', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM49', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM59', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM69', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM79', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM89', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM99', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMA9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMB9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMC9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMD9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCME9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMF9']}
2025-07-03 10:32:10,996 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-03 10:32:10,996 - INFO - 成功插入的数据ID: ['FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM28', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM38', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM48', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM58', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM68', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM78', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM88', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM98', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMA8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMB8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMC8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMD8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCME8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMF8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMG8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMH8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMI8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMJ8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMK8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCML8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMM8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMN8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMO8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMP8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMQ8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMR8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMS8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMT8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMU8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMV8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMW8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMX8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMY8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMZ8', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM09', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM19', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM29', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM39', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM49', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM59', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM69', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM79', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM89', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCM99', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMA9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMB9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMC9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMD9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCME9', 'FINST-XL866HB114TWUMW8C3GA37DXMD513K2GSRMCMF9']
2025-07-03 10:32:16,246 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:16,246 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2377', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '098F7BA5-BCCB-72E0-BA86-24863A37893C', 'x-acs-trace-id': '02463f0318e6fbf00ff0c9bd64be2cc1', 'etag': '2eDwSlBDqRL91Hc3NWu9nzg7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:16,246 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM2', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM3', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM4', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM5', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM6', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM7', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM8', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM9', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMA', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMB', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMC', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMD', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCME', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMF', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMG', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMH', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMI', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMJ', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMK', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCML', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMM', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMN', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMO', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMP', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMQ', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMR', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMS', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMT', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMU', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMV', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMW', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMX', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMY', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMZ', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM01', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM11', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM21', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM31', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM41', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM51', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM61', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM71', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM81', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM91', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMA1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMB1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMC1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMD1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCME1']}
2025-07-03 10:32:16,246 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-03 10:32:16,246 - INFO - 成功插入的数据ID: ['FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM2', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM3', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM4', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM5', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM6', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM7', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM8', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM9', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMA', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMB', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMC', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMD', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCME', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMF', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMG', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMH', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMI', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMJ', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMK', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCML', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMM', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMN', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMO', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMP', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMQ', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMR', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMS', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMT', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMU', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMV', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMW', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMX', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMY', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMZ', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM01', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM11', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM21', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM31', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM41', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM51', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM61', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM71', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM81', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCM91', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMA1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMB1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMC1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCMD1', 'FINST-QUA66S71IUTW9GXQF4R625H0TIY62B4KSRMCME1']
2025-07-03 10:32:21,496 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:21,496 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8F5C0689-9330-74B8-AEBF-35F9E04BF9BE', 'x-acs-trace-id': 'b0f534e8cae2ba30c6f1e08bd6dd1bb2', 'etag': '2J7I2I4DXjVBax137aG2Wcw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:21,496 - INFO - 批量插入响应体: {'result': ['FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMPA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMQA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMRA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMSA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMTA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMUA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMVA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMWA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMXA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMYA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMZA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM0B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM1B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM2B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM3B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM4B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM5B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM6B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM7B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM8B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM9B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMAB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMBB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMCB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMDB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMEB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMFB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMGB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMHB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMIB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMJB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMKB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMLB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMMB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMNB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMOB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMPB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMQB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMRB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMSB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMTB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMUB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMVB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMWB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMXB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMYB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMZB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM0C', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM1C', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM2C']}
2025-07-03 10:32:21,496 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-03 10:32:21,496 - INFO - 成功插入的数据ID: ['FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMPA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMQA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMRA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMSA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMTA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMUA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMVA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMWA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMXA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMYA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMZA', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM0B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM1B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM2B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM3B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM4B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM5B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM6B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM7B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM8B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM9B', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMAB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMBB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMCB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMDB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMEB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMFB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMGB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMHB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMIB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMJB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMKB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMLB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMMB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMNB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMOB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMPB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMQB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMRB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMSB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMTB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMUB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMVB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMWB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMXB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMYB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCMZB', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM0C', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM1C', 'FINST-K8C66U61Y3SW1O588SYHXBJYVD9D3A6OSRMCM2C']
2025-07-03 10:32:26,746 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:26,746 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '82B7B1CF-75E7-778B-A194-AE9CE950D5B7', 'x-acs-trace-id': '232b6c1fd373d9fe9a0209e703f398e1', 'etag': '29pFYMmr4enCrJF1lvYs/DQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:26,746 - INFO - 批量插入响应体: {'result': ['FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMAA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMBA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMCA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMDA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMEA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMFA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMGA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMHA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMIA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMJA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMKA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMLA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMMA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMNA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMOA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMPA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMQA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMRA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMSA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMTA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMUA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMVA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMWA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMXA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMYA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMZA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM0B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM1B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM2B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM3B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM4B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM5B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM6B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM7B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM8B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM9B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMAB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMBB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMCB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMDB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMEB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMFB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMGB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMHB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMIB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMJB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMKB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMLB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMMB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMNB']}
2025-07-03 10:32:26,746 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-03 10:32:26,746 - INFO - 成功插入的数据ID: ['FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMAA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMBA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMCA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMDA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMEA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMFA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMGA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMHA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMIA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMJA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMKA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMLA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMMA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMNA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMOA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMPA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMQA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMRA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMSA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMTA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMUA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMVA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6238SSRMCMWA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMXA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMYA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMZA', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM0B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM1B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM2B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM3B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM4B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM5B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM6B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM7B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM8B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCM9B', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMAB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMBB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMCB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMDB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMEB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMFB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMGB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMHB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMIB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMJB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMKB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMLB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMMB', 'FINST-QVA66B81B3TWQ1YCFQXU971I4XM6248SSRMCMNB']
2025-07-03 10:32:32,027 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:32,027 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2378', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AFD6A27E-D248-7EA7-BE00-310274D68D48', 'x-acs-trace-id': '16bfb6db2425aedabd74637229184f02', 'etag': '247yGxfBeB8ku1e+z1/cFlg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:32,027 - INFO - 批量插入响应体: {'result': ['FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM2', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM3', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM4', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM5', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM6', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM7', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM8', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM9', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMA', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMB', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMC', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMD', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCME', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMF', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMG', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMH', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMI', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMJ', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMK', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCML', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMM', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMN', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMO', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMP', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMQ', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMR', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMS', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMT', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMU', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMV', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMW', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMX', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMY', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMZ', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM01', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM11', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM21', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM31', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM41', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM51', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM61', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM71', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM81', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM91', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMA1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMB1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMC1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMD1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCME1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMF1']}
2025-07-03 10:32:32,027 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-07-03 10:32:32,027 - INFO - 成功插入的数据ID: ['FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM2', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM3', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM4', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM5', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM6', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM7', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM8', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCM9', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMA', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMB', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMC', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMD', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCME', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMF', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMG', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMH', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMI', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMJ', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMK', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCML', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMM', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMN', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMO', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMP', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMQ', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMR', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMS', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMT', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMU', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMV', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2DAWSRMCMW', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMX', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMY', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMZ', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM01', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM11', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM21', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM31', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM41', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM51', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM61', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM71', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM81', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCM91', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMA1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMB1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMC1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMD1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCME1', 'FINST-NU966I81HUTWN3KKA48T29CZKFNI2EAWSRMCMF1']
2025-07-03 10:32:37,277 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:37,277 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3971F436-AD0B-7F43-A764-F70C4766EB52', 'x-acs-trace-id': '82118bf3f4feae7061b0ac13dd01e2ab', 'etag': '29EdxNMaA8wTa11gKCmHmIQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:37,277 - INFO - 批量插入响应体: {'result': ['FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMD8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCME8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMF8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMG8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMH8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMI8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMJ8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMK8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCML8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMM8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMN8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMO8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMP8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMQ8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMR8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMS8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMT8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMU8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMV8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMW8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMX8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMY8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMZ8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM09', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM19', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM29', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM39', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM49', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM59', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM69', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM79', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM89', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM99', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMA9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMB9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMC9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMD9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCME9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMF9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMG9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMH9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMI9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMJ9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMK9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCML9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMM9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMN9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMO9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMP9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMQ9']}
2025-07-03 10:32:37,277 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-07-03 10:32:37,277 - INFO - 成功插入的数据ID: ['FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMD8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCME8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMF8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMG8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMH8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMI8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMJ8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMK8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCML8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMM8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMN8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMO8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMP8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMQ8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMR8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMS8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMT8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMU8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMV8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMW8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMX8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMY8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMZ8', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM09', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM19', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM29', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM39', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM49', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM59', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM69', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM79', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM89', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCM99', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMA9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMB9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMC9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMD9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCME9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMF9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMG9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMH9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMI9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMJ9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMK9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCML9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMM9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMN9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMO9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMP9', 'FINST-B9C660C1DRRW7EK07KVPRD7B5V6Z2MC0TRMCMQ9']
2025-07-03 10:32:42,511 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:42,511 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '66D392E2-4B17-712D-9F3B-C31C14169EDC', 'x-acs-trace-id': 'dfe3d7146d65517d36f4b9d6b1bfdb24', 'etag': '2LbxxUl4znMLgwxSYthPJ2Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:42,511 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM32', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM42', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM52', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM62', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM72', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM82', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM92', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMA2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMB2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMC2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMD2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCME2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMF2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMG2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMH2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMI2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMJ2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMK2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCML2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMM2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMN2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMO2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMP2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMQ2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMR2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMS2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMT2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMU2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMV2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMW2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMX2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMY2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMZ2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM03', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM13', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM23', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM33', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM43', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM53', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM63', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM73', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM83', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM93', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMA3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMB3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMC3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMD3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCME3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMF3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMG3']}
2025-07-03 10:32:42,511 - INFO - 批量插入表单数据成功，批次 8，共 50 条记录
2025-07-03 10:32:42,511 - INFO - 成功插入的数据ID: ['FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM32', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM42', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM52', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM62', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM72', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM82', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM92', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMA2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMB2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMC2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMD2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCME2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMF2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMG2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMH2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMI2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMJ2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMK2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCML2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMM2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMN2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMO2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMP2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMQ2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMR2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMS2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMT2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMU2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMV2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMW2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMX2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMY2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMZ2', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM03', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM13', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM23', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM33', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM43', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM53', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM63', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM73', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM83', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM93', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMA3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMB3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMC3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMD3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCME3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMF3', 'FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMG3']
2025-07-03 10:32:47,746 - INFO - 批量插入响应状态码: 200
2025-07-03 10:32:47,746 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 02:32:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4F77445C-5CF2-7C24-9198-00A21AE75223', 'x-acs-trace-id': '7566bfa3a5ba0d4f59eb38af15c34b15', 'etag': '1zTRIaFzQrNpR759fmLdnhQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 10:32:47,746 - INFO - 批量插入响应体: {'result': ['FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM3A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM4A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM5A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM6A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM7A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM8A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM9A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMAA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMBA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMCA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMDA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMEA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMFA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMGA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMHA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMIA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMJA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMKA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMLA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMMA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMNA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMOA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMPA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMQA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMRA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMSA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMTA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMUA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMVA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMWA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMXA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMYA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMZA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM0B', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM1B']}
2025-07-03 10:32:47,746 - INFO - 批量插入表单数据成功，批次 9，共 35 条记录
2025-07-03 10:32:47,746 - INFO - 成功插入的数据ID: ['FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM3A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM4A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM5A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM6A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM7A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM8A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM9A', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMAA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMBA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMCA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMDA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMEA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMFA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMGA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMHA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMIA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMJA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMKA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMLA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMMA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMNA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMOA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMPA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMQA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMRA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMSA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMTA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMUA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMVA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMWA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMXA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMYA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMZA', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM0B', 'FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM1B']
2025-07-03 10:32:52,761 - INFO - 批量插入完成，共 435 条记录
2025-07-03 10:32:52,761 - INFO - 日期 2025-07-02 处理完成 - 更新: 1 条，插入: 435 条，错误: 0 条
2025-07-03 10:32:52,761 - INFO - 开始处理日期: 2025-07-03
2025-07-03 10:32:52,761 - INFO - Request Parameters - Page 1:
2025-07-03 10:32:52,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 10:32:52,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 10:32:53,214 - INFO - Response - Page 1:
2025-07-03 10:32:53,214 - INFO - 第 1 页获取到 1 条记录
2025-07-03 10:32:53,730 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 10:32:53,730 - INFO - 获取到 1 条表单数据
2025-07-03 10:32:53,730 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 10:32:53,730 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 10:32:53,730 - INFO - 数据同步完成！更新: 1 条，插入: 435 条，错误: 0 条
2025-07-03 10:32:53,730 - INFO - 同步完成
2025-07-03 13:30:34,036 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 13:30:34,036 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 13:30:34,036 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 13:30:34,192 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 180 条记录
2025-07-03 13:30:34,192 - INFO - 获取到 4 个日期需要处理: ['2025-06-20', '2025-06-29', '2025-07-02', '2025-07-03']
2025-07-03 13:30:34,192 - INFO - 开始处理日期: 2025-06-20
2025-07-03 13:30:34,192 - INFO - Request Parameters - Page 1:
2025-07-03 13:30:34,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:34,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:42,317 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A81C9408-696E-735F-84D3-B39DE8122174 Response: {'code': 'ServiceUnavailable', 'requestid': 'A81C9408-696E-735F-84D3-B39DE8122174', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A81C9408-696E-735F-84D3-B39DE8122174)
2025-07-03 13:30:42,317 - INFO - 开始处理日期: 2025-06-29
2025-07-03 13:30:42,317 - INFO - Request Parameters - Page 1:
2025-07-03 13:30:42,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:42,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:50,192 - INFO - Response - Page 1:
2025-07-03 13:30:50,192 - INFO - 第 1 页获取到 50 条记录
2025-07-03 13:30:50,692 - INFO - Request Parameters - Page 2:
2025-07-03 13:30:50,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:50,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:51,411 - INFO - Response - Page 2:
2025-07-03 13:30:51,411 - INFO - 第 2 页获取到 50 条记录
2025-07-03 13:30:51,926 - INFO - Request Parameters - Page 3:
2025-07-03 13:30:51,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:51,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:52,567 - INFO - Response - Page 3:
2025-07-03 13:30:52,567 - INFO - 第 3 页获取到 50 条记录
2025-07-03 13:30:53,067 - INFO - Request Parameters - Page 4:
2025-07-03 13:30:53,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:53,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:53,708 - INFO - Response - Page 4:
2025-07-03 13:30:53,723 - INFO - 第 4 页获取到 50 条记录
2025-07-03 13:30:54,223 - INFO - Request Parameters - Page 5:
2025-07-03 13:30:54,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:54,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:54,895 - INFO - Response - Page 5:
2025-07-03 13:30:54,895 - INFO - 第 5 页获取到 50 条记录
2025-07-03 13:30:55,411 - INFO - Request Parameters - Page 6:
2025-07-03 13:30:55,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:55,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:56,082 - INFO - Response - Page 6:
2025-07-03 13:30:56,082 - INFO - 第 6 页获取到 50 条记录
2025-07-03 13:30:56,582 - INFO - Request Parameters - Page 7:
2025-07-03 13:30:56,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:56,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:57,254 - INFO - Response - Page 7:
2025-07-03 13:30:57,254 - INFO - 第 7 页获取到 50 条记录
2025-07-03 13:30:57,770 - INFO - Request Parameters - Page 8:
2025-07-03 13:30:57,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:57,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:58,411 - INFO - Response - Page 8:
2025-07-03 13:30:58,411 - INFO - 第 8 页获取到 50 条记录
2025-07-03 13:30:58,911 - INFO - Request Parameters - Page 9:
2025-07-03 13:30:58,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:30:58,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:30:59,582 - INFO - Response - Page 9:
2025-07-03 13:30:59,582 - INFO - 第 9 页获取到 50 条记录
2025-07-03 13:31:00,082 - INFO - Request Parameters - Page 10:
2025-07-03 13:31:00,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:00,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:00,786 - INFO - Response - Page 10:
2025-07-03 13:31:00,786 - INFO - 第 10 页获取到 50 条记录
2025-07-03 13:31:01,301 - INFO - Request Parameters - Page 11:
2025-07-03 13:31:01,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:01,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:01,957 - INFO - Response - Page 11:
2025-07-03 13:31:01,957 - INFO - 第 11 页获取到 39 条记录
2025-07-03 13:31:02,473 - INFO - 查询完成，共获取到 539 条记录
2025-07-03 13:31:02,473 - INFO - 获取到 539 条表单数据
2025-07-03 13:31:02,473 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-07-03 13:31:02,473 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI9
2025-07-03 13:31:03,036 - INFO - 更新表单数据成功: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI9
2025-07-03 13:31:03,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1712.5, 'new_value': 7000.0}, {'field': 'offline_amount', 'old_value': 8.0, 'new_value': 10000.0}, {'field': 'total_amount', 'old_value': 1720.5, 'new_value': 17000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 18}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d60d80cb09b24fffa6e172318c4947f2.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=i%2BVjDb%2BjsSxJXMXG16XzHKUM80g%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/16be88f2c0434d3d87c3777892a8e918.jpg?Expires=2066720548&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=FGaxNZ1QTyG3MYgMCdEf5nclwgA%3D'}]
2025-07-03 13:31:03,036 - INFO - 日期 2025-06-29 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-03 13:31:03,036 - INFO - 开始处理日期: 2025-07-02
2025-07-03 13:31:03,036 - INFO - Request Parameters - Page 1:
2025-07-03 13:31:03,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:03,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:03,661 - INFO - Response - Page 1:
2025-07-03 13:31:03,661 - INFO - 第 1 页获取到 50 条记录
2025-07-03 13:31:04,176 - INFO - Request Parameters - Page 2:
2025-07-03 13:31:04,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:04,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:04,879 - INFO - Response - Page 2:
2025-07-03 13:31:04,879 - INFO - 第 2 页获取到 50 条记录
2025-07-03 13:31:05,379 - INFO - Request Parameters - Page 3:
2025-07-03 13:31:05,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:05,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:06,082 - INFO - Response - Page 3:
2025-07-03 13:31:06,082 - INFO - 第 3 页获取到 50 条记录
2025-07-03 13:31:06,582 - INFO - Request Parameters - Page 4:
2025-07-03 13:31:06,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:06,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:07,254 - INFO - Response - Page 4:
2025-07-03 13:31:07,254 - INFO - 第 4 页获取到 50 条记录
2025-07-03 13:31:07,770 - INFO - Request Parameters - Page 5:
2025-07-03 13:31:07,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:07,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:08,411 - INFO - Response - Page 5:
2025-07-03 13:31:08,411 - INFO - 第 5 页获取到 50 条记录
2025-07-03 13:31:08,911 - INFO - Request Parameters - Page 6:
2025-07-03 13:31:08,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:08,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:09,661 - INFO - Response - Page 6:
2025-07-03 13:31:09,661 - INFO - 第 6 页获取到 50 条记录
2025-07-03 13:31:10,176 - INFO - Request Parameters - Page 7:
2025-07-03 13:31:10,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:10,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:10,832 - INFO - Response - Page 7:
2025-07-03 13:31:10,832 - INFO - 第 7 页获取到 50 条记录
2025-07-03 13:31:11,348 - INFO - Request Parameters - Page 8:
2025-07-03 13:31:11,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:11,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:12,067 - INFO - Response - Page 8:
2025-07-03 13:31:12,067 - INFO - 第 8 页获取到 50 条记录
2025-07-03 13:31:12,582 - INFO - Request Parameters - Page 9:
2025-07-03 13:31:12,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:12,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:13,223 - INFO - Response - Page 9:
2025-07-03 13:31:13,223 - INFO - 第 9 页获取到 50 条记录
2025-07-03 13:31:13,723 - INFO - Request Parameters - Page 10:
2025-07-03 13:31:13,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:13,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:14,410 - INFO - Response - Page 10:
2025-07-03 13:31:14,410 - INFO - 第 10 页获取到 50 条记录
2025-07-03 13:31:14,911 - INFO - Request Parameters - Page 11:
2025-07-03 13:31:14,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:14,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:15,395 - INFO - Response - Page 11:
2025-07-03 13:31:15,395 - INFO - 第 11 页获取到 8 条记录
2025-07-03 13:31:15,895 - INFO - 查询完成，共获取到 508 条记录
2025-07-03 13:31:15,895 - INFO - 获取到 508 条表单数据
2025-07-03 13:31:15,895 - INFO - 当前日期 2025-07-02 有 173 条MySQL数据需要处理
2025-07-03 13:31:15,911 - INFO - 开始批量插入 5 条新记录
2025-07-03 13:31:16,067 - INFO - 批量插入响应状态码: 200
2025-07-03 13:31:16,067 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 05:31:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BE3F7EE0-55D5-7604-A774-962315ACA689', 'x-acs-trace-id': '6c68cbc438f130cbc989a51396e5efc7', 'etag': '2v9zIsuxBM+vnVJsFx5RVJA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 13:31:16,067 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM37', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM47', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM57', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM67', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM77']}
2025-07-03 13:31:16,067 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-07-03 13:31:16,067 - INFO - 成功插入的数据ID: ['FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM37', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM47', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM57', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM67', 'FINST-07E66I91O4TWGML9FTGPB7FKS4EP3M2R6YMCM77']
2025-07-03 13:31:21,082 - INFO - 批量插入完成，共 5 条记录
2025-07-03 13:31:21,082 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-07-03 13:31:21,082 - INFO - 开始处理日期: 2025-07-03
2025-07-03 13:31:21,082 - INFO - Request Parameters - Page 1:
2025-07-03 13:31:21,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:31:21,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:31:21,535 - INFO - Response - Page 1:
2025-07-03 13:31:21,535 - INFO - 第 1 页获取到 1 条记录
2025-07-03 13:31:22,035 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 13:31:22,035 - INFO - 获取到 1 条表单数据
2025-07-03 13:31:22,035 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 13:31:22,035 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 13:31:22,035 - INFO - 数据同步完成！更新: 1 条，插入: 5 条，错误: 1 条
2025-07-03 13:32:22,051 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 13:32:22,051 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 13:32:22,051 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 13:32:22,191 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 542 条记录
2025-07-03 13:32:22,191 - INFO - 获取到 2 个日期需要处理: ['2025-07-02', '2025-07-03']
2025-07-03 13:32:22,191 - INFO - 开始处理日期: 2025-07-02
2025-07-03 13:32:22,207 - INFO - Request Parameters - Page 1:
2025-07-03 13:32:22,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:22,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:23,019 - INFO - Response - Page 1:
2025-07-03 13:32:23,019 - INFO - 第 1 页获取到 50 条记录
2025-07-03 13:32:23,519 - INFO - Request Parameters - Page 2:
2025-07-03 13:32:23,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:23,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:24,223 - INFO - Response - Page 2:
2025-07-03 13:32:24,223 - INFO - 第 2 页获取到 50 条记录
2025-07-03 13:32:24,738 - INFO - Request Parameters - Page 3:
2025-07-03 13:32:24,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:24,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:25,379 - INFO - Response - Page 3:
2025-07-03 13:32:25,379 - INFO - 第 3 页获取到 50 条记录
2025-07-03 13:32:25,879 - INFO - Request Parameters - Page 4:
2025-07-03 13:32:25,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:25,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:26,566 - INFO - Response - Page 4:
2025-07-03 13:32:26,566 - INFO - 第 4 页获取到 50 条记录
2025-07-03 13:32:27,066 - INFO - Request Parameters - Page 5:
2025-07-03 13:32:27,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:27,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:27,707 - INFO - Response - Page 5:
2025-07-03 13:32:27,707 - INFO - 第 5 页获取到 50 条记录
2025-07-03 13:32:28,223 - INFO - Request Parameters - Page 6:
2025-07-03 13:32:28,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:28,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:28,910 - INFO - Response - Page 6:
2025-07-03 13:32:28,910 - INFO - 第 6 页获取到 50 条记录
2025-07-03 13:32:29,426 - INFO - Request Parameters - Page 7:
2025-07-03 13:32:29,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:29,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:30,066 - INFO - Response - Page 7:
2025-07-03 13:32:30,066 - INFO - 第 7 页获取到 50 条记录
2025-07-03 13:32:30,582 - INFO - Request Parameters - Page 8:
2025-07-03 13:32:30,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:30,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:31,301 - INFO - Response - Page 8:
2025-07-03 13:32:31,301 - INFO - 第 8 页获取到 50 条记录
2025-07-03 13:32:31,816 - INFO - Request Parameters - Page 9:
2025-07-03 13:32:31,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:31,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:32,488 - INFO - Response - Page 9:
2025-07-03 13:32:32,488 - INFO - 第 9 页获取到 50 条记录
2025-07-03 13:32:33,004 - INFO - Request Parameters - Page 10:
2025-07-03 13:32:33,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:33,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:33,676 - INFO - Response - Page 10:
2025-07-03 13:32:33,676 - INFO - 第 10 页获取到 50 条记录
2025-07-03 13:32:34,191 - INFO - Request Parameters - Page 11:
2025-07-03 13:32:34,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:34,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:34,769 - INFO - Response - Page 11:
2025-07-03 13:32:34,769 - INFO - 第 11 页获取到 13 条记录
2025-07-03 13:32:35,269 - INFO - 查询完成，共获取到 513 条记录
2025-07-03 13:32:35,269 - INFO - 获取到 513 条表单数据
2025-07-03 13:32:35,269 - INFO - 当前日期 2025-07-02 有 526 条MySQL数据需要处理
2025-07-03 13:32:35,285 - INFO - 开始批量插入 13 条新记录
2025-07-03 13:32:35,457 - INFO - 批量插入响应状态码: 200
2025-07-03 13:32:35,457 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 05:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FD879B7F-BC2E-7027-9540-BBB4DC4273B7', 'x-acs-trace-id': '220ea65df480a15ffba0f803ea2a8cf2', 'etag': '6iBFCthjukiyzHDQlujbWbA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 13:32:35,457 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMR2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMS2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMT2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMU2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMV2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMW2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMX2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMY2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMZ2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM03', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM13', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM23', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM33']}
2025-07-03 13:32:35,457 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-07-03 13:32:35,457 - INFO - 成功插入的数据ID: ['FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMR2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMS2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMT2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMU2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMV2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMW2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMX2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMY2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCMZ2', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM03', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM13', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM23', 'FINST-EZD66RB1USRWHR2HD8WE299W60Z136CG8YMCM33']
2025-07-03 13:32:40,472 - INFO - 批量插入完成，共 13 条记录
2025-07-03 13:32:40,472 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-07-03 13:32:40,472 - INFO - 开始处理日期: 2025-07-03
2025-07-03 13:32:40,472 - INFO - Request Parameters - Page 1:
2025-07-03 13:32:40,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 13:32:40,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 13:32:40,941 - INFO - Response - Page 1:
2025-07-03 13:32:40,941 - INFO - 第 1 页获取到 1 条记录
2025-07-03 13:32:41,457 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 13:32:41,457 - INFO - 获取到 1 条表单数据
2025-07-03 13:32:41,457 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 13:32:41,457 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 13:32:41,457 - INFO - 数据同步完成！更新: 0 条，插入: 13 条，错误: 0 条
2025-07-03 13:32:41,457 - INFO - 同步完成
2025-07-03 16:30:34,090 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 16:30:34,090 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 16:30:34,090 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 16:30:34,231 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 196 条记录
2025-07-03 16:30:34,231 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-29', '2025-06-30', '2025-07-02', '2025-07-03']
2025-07-03 16:30:34,231 - INFO - 开始处理日期: 2025-06-20
2025-07-03 16:30:34,231 - INFO - Request Parameters - Page 1:
2025-07-03 16:30:34,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:34,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:42,372 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3B486CC1-6B8D-7AEA-A148-553BC3C79B9D Response: {'code': 'ServiceUnavailable', 'requestid': '3B486CC1-6B8D-7AEA-A148-553BC3C79B9D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3B486CC1-6B8D-7AEA-A148-553BC3C79B9D)
2025-07-03 16:30:42,372 - INFO - 开始处理日期: 2025-06-29
2025-07-03 16:30:42,372 - INFO - Request Parameters - Page 1:
2025-07-03 16:30:42,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:42,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:44,934 - INFO - Response - Page 1:
2025-07-03 16:30:44,934 - INFO - 第 1 页获取到 50 条记录
2025-07-03 16:30:45,450 - INFO - Request Parameters - Page 2:
2025-07-03 16:30:45,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:45,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:50,606 - INFO - Response - Page 2:
2025-07-03 16:30:50,606 - INFO - 第 2 页获取到 50 条记录
2025-07-03 16:30:51,122 - INFO - Request Parameters - Page 3:
2025-07-03 16:30:51,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:51,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:51,793 - INFO - Response - Page 3:
2025-07-03 16:30:51,793 - INFO - 第 3 页获取到 50 条记录
2025-07-03 16:30:52,293 - INFO - Request Parameters - Page 4:
2025-07-03 16:30:52,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:52,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:52,903 - INFO - Response - Page 4:
2025-07-03 16:30:52,903 - INFO - 第 4 页获取到 50 条记录
2025-07-03 16:30:53,403 - INFO - Request Parameters - Page 5:
2025-07-03 16:30:53,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:53,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:54,215 - INFO - Response - Page 5:
2025-07-03 16:30:54,215 - INFO - 第 5 页获取到 50 条记录
2025-07-03 16:30:54,731 - INFO - Request Parameters - Page 6:
2025-07-03 16:30:54,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:54,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:55,434 - INFO - Response - Page 6:
2025-07-03 16:30:55,434 - INFO - 第 6 页获取到 50 条记录
2025-07-03 16:30:55,950 - INFO - Request Parameters - Page 7:
2025-07-03 16:30:55,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:55,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:56,622 - INFO - Response - Page 7:
2025-07-03 16:30:56,622 - INFO - 第 7 页获取到 50 条记录
2025-07-03 16:30:57,137 - INFO - Request Parameters - Page 8:
2025-07-03 16:30:57,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:57,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:57,840 - INFO - Response - Page 8:
2025-07-03 16:30:57,840 - INFO - 第 8 页获取到 50 条记录
2025-07-03 16:30:58,356 - INFO - Request Parameters - Page 9:
2025-07-03 16:30:58,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:58,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:30:59,168 - INFO - Response - Page 9:
2025-07-03 16:30:59,168 - INFO - 第 9 页获取到 50 条记录
2025-07-03 16:30:59,684 - INFO - Request Parameters - Page 10:
2025-07-03 16:30:59,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:30:59,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:00,356 - INFO - Response - Page 10:
2025-07-03 16:31:00,356 - INFO - 第 10 页获取到 50 条记录
2025-07-03 16:31:00,871 - INFO - Request Parameters - Page 11:
2025-07-03 16:31:00,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:00,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:01,496 - INFO - Response - Page 11:
2025-07-03 16:31:01,512 - INFO - 第 11 页获取到 39 条记录
2025-07-03 16:31:02,012 - INFO - 查询完成，共获取到 539 条记录
2025-07-03 16:31:02,012 - INFO - 获取到 539 条表单数据
2025-07-03 16:31:02,012 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-07-03 16:31:02,012 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 16:31:02,012 - INFO - 开始处理日期: 2025-06-30
2025-07-03 16:31:02,012 - INFO - Request Parameters - Page 1:
2025-07-03 16:31:02,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:02,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:02,746 - INFO - Response - Page 1:
2025-07-03 16:31:02,746 - INFO - 第 1 页获取到 50 条记录
2025-07-03 16:31:03,262 - INFO - Request Parameters - Page 2:
2025-07-03 16:31:03,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:03,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:03,934 - INFO - Response - Page 2:
2025-07-03 16:31:03,934 - INFO - 第 2 页获取到 50 条记录
2025-07-03 16:31:04,450 - INFO - Request Parameters - Page 3:
2025-07-03 16:31:04,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:04,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:05,121 - INFO - Response - Page 3:
2025-07-03 16:31:05,121 - INFO - 第 3 页获取到 50 条记录
2025-07-03 16:31:05,637 - INFO - Request Parameters - Page 4:
2025-07-03 16:31:05,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:05,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:06,450 - INFO - Response - Page 4:
2025-07-03 16:31:06,450 - INFO - 第 4 页获取到 50 条记录
2025-07-03 16:31:06,950 - INFO - Request Parameters - Page 5:
2025-07-03 16:31:06,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:06,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:07,621 - INFO - Response - Page 5:
2025-07-03 16:31:07,621 - INFO - 第 5 页获取到 50 条记录
2025-07-03 16:31:08,137 - INFO - Request Parameters - Page 6:
2025-07-03 16:31:08,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:08,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:08,778 - INFO - Response - Page 6:
2025-07-03 16:31:08,778 - INFO - 第 6 页获取到 50 条记录
2025-07-03 16:31:09,293 - INFO - Request Parameters - Page 7:
2025-07-03 16:31:09,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:09,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:09,965 - INFO - Response - Page 7:
2025-07-03 16:31:09,981 - INFO - 第 7 页获取到 50 条记录
2025-07-03 16:31:10,496 - INFO - Request Parameters - Page 8:
2025-07-03 16:31:10,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:10,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:11,153 - INFO - Response - Page 8:
2025-07-03 16:31:11,153 - INFO - 第 8 页获取到 50 条记录
2025-07-03 16:31:11,668 - INFO - Request Parameters - Page 9:
2025-07-03 16:31:11,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:11,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:12,356 - INFO - Response - Page 9:
2025-07-03 16:31:12,356 - INFO - 第 9 页获取到 50 条记录
2025-07-03 16:31:12,871 - INFO - Request Parameters - Page 10:
2025-07-03 16:31:12,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:12,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:13,543 - INFO - Response - Page 10:
2025-07-03 16:31:13,543 - INFO - 第 10 页获取到 50 条记录
2025-07-03 16:31:14,059 - INFO - Request Parameters - Page 11:
2025-07-03 16:31:14,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:14,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:14,668 - INFO - Response - Page 11:
2025-07-03 16:31:14,668 - INFO - 第 11 页获取到 27 条记录
2025-07-03 16:31:15,184 - INFO - 查询完成，共获取到 527 条记录
2025-07-03 16:31:15,184 - INFO - 获取到 527 条表单数据
2025-07-03 16:31:15,184 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-07-03 16:31:15,184 - INFO - 开始更新记录 - 表单实例ID: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMJL
2025-07-03 16:31:15,824 - INFO - 更新表单数据成功: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMJL
2025-07-03 16:31:15,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19000.0, 'new_value': 18731.0}, {'field': 'total_amount', 'old_value': 19000.0, 'new_value': 18731.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-03 16:31:15,824 - INFO - 日期 2025-06-30 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-03 16:31:15,824 - INFO - 开始处理日期: 2025-07-02
2025-07-03 16:31:15,824 - INFO - Request Parameters - Page 1:
2025-07-03 16:31:15,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:15,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:16,512 - INFO - Response - Page 1:
2025-07-03 16:31:16,512 - INFO - 第 1 页获取到 50 条记录
2025-07-03 16:31:17,012 - INFO - Request Parameters - Page 2:
2025-07-03 16:31:17,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:17,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:17,746 - INFO - Response - Page 2:
2025-07-03 16:31:17,746 - INFO - 第 2 页获取到 50 条记录
2025-07-03 16:31:18,262 - INFO - Request Parameters - Page 3:
2025-07-03 16:31:18,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:18,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:18,918 - INFO - Response - Page 3:
2025-07-03 16:31:18,918 - INFO - 第 3 页获取到 50 条记录
2025-07-03 16:31:19,434 - INFO - Request Parameters - Page 4:
2025-07-03 16:31:19,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:19,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:20,168 - INFO - Response - Page 4:
2025-07-03 16:31:20,168 - INFO - 第 4 页获取到 50 条记录
2025-07-03 16:31:20,684 - INFO - Request Parameters - Page 5:
2025-07-03 16:31:20,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:20,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:21,387 - INFO - Response - Page 5:
2025-07-03 16:31:21,387 - INFO - 第 5 页获取到 50 条记录
2025-07-03 16:31:21,903 - INFO - Request Parameters - Page 6:
2025-07-03 16:31:21,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:21,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:22,653 - INFO - Response - Page 6:
2025-07-03 16:31:22,653 - INFO - 第 6 页获取到 50 条记录
2025-07-03 16:31:23,153 - INFO - Request Parameters - Page 7:
2025-07-03 16:31:23,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:23,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:23,809 - INFO - Response - Page 7:
2025-07-03 16:31:23,809 - INFO - 第 7 页获取到 50 条记录
2025-07-03 16:31:24,324 - INFO - Request Parameters - Page 8:
2025-07-03 16:31:24,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:24,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:25,043 - INFO - Response - Page 8:
2025-07-03 16:31:25,043 - INFO - 第 8 页获取到 50 条记录
2025-07-03 16:31:25,559 - INFO - Request Parameters - Page 9:
2025-07-03 16:31:25,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:25,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:26,340 - INFO - Response - Page 9:
2025-07-03 16:31:26,340 - INFO - 第 9 页获取到 50 条记录
2025-07-03 16:31:26,856 - INFO - Request Parameters - Page 10:
2025-07-03 16:31:26,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:26,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:27,543 - INFO - Response - Page 10:
2025-07-03 16:31:27,543 - INFO - 第 10 页获取到 50 条记录
2025-07-03 16:31:28,043 - INFO - Request Parameters - Page 11:
2025-07-03 16:31:28,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:28,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:28,637 - INFO - Response - Page 11:
2025-07-03 16:31:28,637 - INFO - 第 11 页获取到 26 条记录
2025-07-03 16:31:29,153 - INFO - 查询完成，共获取到 526 条记录
2025-07-03 16:31:29,153 - INFO - 获取到 526 条表单数据
2025-07-03 16:31:29,153 - INFO - 当前日期 2025-07-02 有 186 条MySQL数据需要处理
2025-07-03 16:31:29,153 - INFO - 开始批量插入 13 条新记录
2025-07-03 16:31:29,324 - INFO - 批量插入响应状态码: 200
2025-07-03 16:31:29,324 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 08:31:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '623', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CEE00C50-4F46-78E7-8945-4C0B34307F6D', 'x-acs-trace-id': 'f8a2db31f75bfbc40211a5ed07f02827', 'etag': '6Pp6xu9lEZIzzGScL7qyK5A3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 16:31:29,324 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM2', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM3', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM4', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM5', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM6', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM7', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM8', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM9', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMA', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMB', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMC', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMD', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCME']}
2025-07-03 16:31:29,324 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-07-03 16:31:29,324 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM2', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM3', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM4', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM5', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM6', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM7', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM8', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCM9', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMA', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMB', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMC', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCMD', 'FINST-FQD66YB1P2UWSUH0AR7NND0QXAXY2ZOIM4NCME']
2025-07-03 16:31:34,340 - INFO - 批量插入完成，共 13 条记录
2025-07-03 16:31:34,340 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-07-03 16:31:34,340 - INFO - 开始处理日期: 2025-07-03
2025-07-03 16:31:34,340 - INFO - Request Parameters - Page 1:
2025-07-03 16:31:34,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:31:34,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:31:34,777 - INFO - Response - Page 1:
2025-07-03 16:31:34,777 - INFO - 第 1 页获取到 1 条记录
2025-07-03 16:31:35,293 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 16:31:35,293 - INFO - 获取到 1 条表单数据
2025-07-03 16:31:35,293 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 16:31:35,293 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 16:31:35,293 - INFO - 数据同步完成！更新: 1 条，插入: 13 条，错误: 1 条
2025-07-03 16:32:35,308 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 16:32:35,308 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 16:32:35,308 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 16:32:35,449 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 579 条记录
2025-07-03 16:32:35,449 - INFO - 获取到 2 个日期需要处理: ['2025-07-02', '2025-07-03']
2025-07-03 16:32:35,465 - INFO - 开始处理日期: 2025-07-02
2025-07-03 16:32:35,465 - INFO - Request Parameters - Page 1:
2025-07-03 16:32:35,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:35,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:36,215 - INFO - Response - Page 1:
2025-07-03 16:32:36,215 - INFO - 第 1 页获取到 50 条记录
2025-07-03 16:32:36,715 - INFO - Request Parameters - Page 2:
2025-07-03 16:32:36,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:36,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:37,371 - INFO - Response - Page 2:
2025-07-03 16:32:37,371 - INFO - 第 2 页获取到 50 条记录
2025-07-03 16:32:37,871 - INFO - Request Parameters - Page 3:
2025-07-03 16:32:37,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:37,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:38,527 - INFO - Response - Page 3:
2025-07-03 16:32:38,527 - INFO - 第 3 页获取到 50 条记录
2025-07-03 16:32:39,027 - INFO - Request Parameters - Page 4:
2025-07-03 16:32:39,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:39,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:39,855 - INFO - Response - Page 4:
2025-07-03 16:32:39,855 - INFO - 第 4 页获取到 50 条记录
2025-07-03 16:32:40,355 - INFO - Request Parameters - Page 5:
2025-07-03 16:32:40,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:40,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:40,996 - INFO - Response - Page 5:
2025-07-03 16:32:40,996 - INFO - 第 5 页获取到 50 条记录
2025-07-03 16:32:41,511 - INFO - Request Parameters - Page 6:
2025-07-03 16:32:41,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:41,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:42,230 - INFO - Response - Page 6:
2025-07-03 16:32:42,230 - INFO - 第 6 页获取到 50 条记录
2025-07-03 16:32:42,746 - INFO - Request Parameters - Page 7:
2025-07-03 16:32:42,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:42,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:43,433 - INFO - Response - Page 7:
2025-07-03 16:32:43,433 - INFO - 第 7 页获取到 50 条记录
2025-07-03 16:32:43,933 - INFO - Request Parameters - Page 8:
2025-07-03 16:32:43,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:43,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:44,574 - INFO - Response - Page 8:
2025-07-03 16:32:44,574 - INFO - 第 8 页获取到 50 条记录
2025-07-03 16:32:45,074 - INFO - Request Parameters - Page 9:
2025-07-03 16:32:45,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:45,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:45,746 - INFO - Response - Page 9:
2025-07-03 16:32:45,746 - INFO - 第 9 页获取到 50 条记录
2025-07-03 16:32:46,261 - INFO - Request Parameters - Page 10:
2025-07-03 16:32:46,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:46,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:46,933 - INFO - Response - Page 10:
2025-07-03 16:32:46,933 - INFO - 第 10 页获取到 50 条记录
2025-07-03 16:32:47,433 - INFO - Request Parameters - Page 11:
2025-07-03 16:32:47,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:47,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:48,043 - INFO - Response - Page 11:
2025-07-03 16:32:48,043 - INFO - 第 11 页获取到 39 条记录
2025-07-03 16:32:48,543 - INFO - 查询完成，共获取到 539 条记录
2025-07-03 16:32:48,543 - INFO - 获取到 539 条表单数据
2025-07-03 16:32:48,543 - INFO - 当前日期 2025-07-02 有 559 条MySQL数据需要处理
2025-07-03 16:32:48,558 - INFO - 开始批量插入 20 条新记录
2025-07-03 16:32:48,793 - INFO - 批量插入响应状态码: 200
2025-07-03 16:32:48,793 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 08:32:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '952', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B5DE38F6-1954-7925-96CA-AC876759848C', 'x-acs-trace-id': '0fa0b402d2c79745ffdd94acb5a7f8eb', 'etag': '9CvFJ/yocmJ5U/sJWdka3Qw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 16:32:48,793 - INFO - 批量插入响应体: {'result': ['FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCM7', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCM8', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCM9', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMA', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMB', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMC', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMD', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCME', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMF', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMG', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMH', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMI', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMJ', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMK', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCML', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMM', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMN', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMO', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMP', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMQ']}
2025-07-03 16:32:48,793 - INFO - 批量插入表单数据成功，批次 1，共 20 条记录
2025-07-03 16:32:48,793 - INFO - 成功插入的数据ID: ['FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCM7', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCM8', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCM9', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMA', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMB', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMC', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMD', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCME', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMF', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMG', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMH', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMI', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMJ', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMK', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCML', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMM', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMN', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMO', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMP', 'FINST-49866E71O0UWKVYPCYGFRB6ZX31I2B08O4NCMQ']
2025-07-03 16:32:53,808 - INFO - 批量插入完成，共 20 条记录
2025-07-03 16:32:53,808 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 20 条，错误: 0 条
2025-07-03 16:32:53,808 - INFO - 开始处理日期: 2025-07-03
2025-07-03 16:32:53,808 - INFO - Request Parameters - Page 1:
2025-07-03 16:32:53,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 16:32:53,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 16:32:54,246 - INFO - Response - Page 1:
2025-07-03 16:32:54,246 - INFO - 第 1 页获取到 1 条记录
2025-07-03 16:32:54,761 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 16:32:54,761 - INFO - 获取到 1 条表单数据
2025-07-03 16:32:54,761 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 16:32:54,761 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 16:32:54,761 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 0 条
2025-07-03 16:32:54,761 - INFO - 同步完成
2025-07-03 19:30:34,410 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 19:30:34,410 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 19:30:34,410 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 19:30:34,551 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 197 条记录
2025-07-03 19:30:34,551 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-29', '2025-06-30', '2025-07-02', '2025-07-03']
2025-07-03 19:30:34,567 - INFO - 开始处理日期: 2025-06-20
2025-07-03 19:30:34,567 - INFO - Request Parameters - Page 1:
2025-07-03 19:30:34,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:34,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:42,679 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D84B7907-4A1A-7376-BB3A-7629A7D3D400 Response: {'code': 'ServiceUnavailable', 'requestid': 'D84B7907-4A1A-7376-BB3A-7629A7D3D400', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D84B7907-4A1A-7376-BB3A-7629A7D3D400)
2025-07-03 19:30:42,679 - INFO - 开始处理日期: 2025-06-29
2025-07-03 19:30:42,679 - INFO - Request Parameters - Page 1:
2025-07-03 19:30:42,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:42,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:48,869 - INFO - Response - Page 1:
2025-07-03 19:30:48,869 - INFO - 第 1 页获取到 50 条记录
2025-07-03 19:30:49,385 - INFO - Request Parameters - Page 2:
2025-07-03 19:30:49,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:49,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:50,073 - INFO - Response - Page 2:
2025-07-03 19:30:50,073 - INFO - 第 2 页获取到 50 条记录
2025-07-03 19:30:50,589 - INFO - Request Parameters - Page 3:
2025-07-03 19:30:50,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:50,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:51,324 - INFO - Response - Page 3:
2025-07-03 19:30:51,324 - INFO - 第 3 页获取到 50 条记录
2025-07-03 19:30:51,839 - INFO - Request Parameters - Page 4:
2025-07-03 19:30:51,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:51,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:52,480 - INFO - Response - Page 4:
2025-07-03 19:30:52,480 - INFO - 第 4 页获取到 50 条记录
2025-07-03 19:30:52,996 - INFO - Request Parameters - Page 5:
2025-07-03 19:30:52,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:52,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:53,699 - INFO - Response - Page 5:
2025-07-03 19:30:53,699 - INFO - 第 5 页获取到 50 条记录
2025-07-03 19:30:54,215 - INFO - Request Parameters - Page 6:
2025-07-03 19:30:54,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:54,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:54,934 - INFO - Response - Page 6:
2025-07-03 19:30:54,934 - INFO - 第 6 页获取到 50 条记录
2025-07-03 19:30:55,435 - INFO - Request Parameters - Page 7:
2025-07-03 19:30:55,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:55,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:56,122 - INFO - Response - Page 7:
2025-07-03 19:30:56,122 - INFO - 第 7 页获取到 50 条记录
2025-07-03 19:30:56,622 - INFO - Request Parameters - Page 8:
2025-07-03 19:30:56,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:56,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:57,279 - INFO - Response - Page 8:
2025-07-03 19:30:57,279 - INFO - 第 8 页获取到 50 条记录
2025-07-03 19:30:57,795 - INFO - Request Parameters - Page 9:
2025-07-03 19:30:57,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:57,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:58,420 - INFO - Response - Page 9:
2025-07-03 19:30:58,420 - INFO - 第 9 页获取到 50 条记录
2025-07-03 19:30:58,936 - INFO - Request Parameters - Page 10:
2025-07-03 19:30:58,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:30:58,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:30:59,577 - INFO - Response - Page 10:
2025-07-03 19:30:59,577 - INFO - 第 10 页获取到 50 条记录
2025-07-03 19:31:00,077 - INFO - Request Parameters - Page 11:
2025-07-03 19:31:00,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:00,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:00,765 - INFO - Response - Page 11:
2025-07-03 19:31:00,765 - INFO - 第 11 页获取到 39 条记录
2025-07-03 19:31:01,265 - INFO - 查询完成，共获取到 539 条记录
2025-07-03 19:31:01,265 - INFO - 获取到 539 条表单数据
2025-07-03 19:31:01,265 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-07-03 19:31:01,265 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 19:31:01,265 - INFO - 开始处理日期: 2025-06-30
2025-07-03 19:31:01,265 - INFO - Request Parameters - Page 1:
2025-07-03 19:31:01,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:01,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:02,093 - INFO - Response - Page 1:
2025-07-03 19:31:02,093 - INFO - 第 1 页获取到 50 条记录
2025-07-03 19:31:02,609 - INFO - Request Parameters - Page 2:
2025-07-03 19:31:02,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:02,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:03,328 - INFO - Response - Page 2:
2025-07-03 19:31:03,328 - INFO - 第 2 页获取到 50 条记录
2025-07-03 19:31:03,828 - INFO - Request Parameters - Page 3:
2025-07-03 19:31:03,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:03,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:04,516 - INFO - Response - Page 3:
2025-07-03 19:31:04,532 - INFO - 第 3 页获取到 50 条记录
2025-07-03 19:31:05,048 - INFO - Request Parameters - Page 4:
2025-07-03 19:31:05,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:05,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:05,751 - INFO - Response - Page 4:
2025-07-03 19:31:05,751 - INFO - 第 4 页获取到 50 条记录
2025-07-03 19:31:06,251 - INFO - Request Parameters - Page 5:
2025-07-03 19:31:06,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:06,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:06,939 - INFO - Response - Page 5:
2025-07-03 19:31:06,939 - INFO - 第 5 页获取到 50 条记录
2025-07-03 19:31:07,439 - INFO - Request Parameters - Page 6:
2025-07-03 19:31:07,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:07,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:08,080 - INFO - Response - Page 6:
2025-07-03 19:31:08,080 - INFO - 第 6 页获取到 50 条记录
2025-07-03 19:31:08,580 - INFO - Request Parameters - Page 7:
2025-07-03 19:31:08,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:08,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:09,221 - INFO - Response - Page 7:
2025-07-03 19:31:09,221 - INFO - 第 7 页获取到 50 条记录
2025-07-03 19:31:09,737 - INFO - Request Parameters - Page 8:
2025-07-03 19:31:09,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:09,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:10,456 - INFO - Response - Page 8:
2025-07-03 19:31:10,456 - INFO - 第 8 页获取到 50 条记录
2025-07-03 19:31:10,972 - INFO - Request Parameters - Page 9:
2025-07-03 19:31:10,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:10,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:11,628 - INFO - Response - Page 9:
2025-07-03 19:31:11,628 - INFO - 第 9 页获取到 50 条记录
2025-07-03 19:31:12,144 - INFO - Request Parameters - Page 10:
2025-07-03 19:31:12,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:12,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:12,832 - INFO - Response - Page 10:
2025-07-03 19:31:12,832 - INFO - 第 10 页获取到 50 条记录
2025-07-03 19:31:13,348 - INFO - Request Parameters - Page 11:
2025-07-03 19:31:13,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:13,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:13,911 - INFO - Response - Page 11:
2025-07-03 19:31:13,911 - INFO - 第 11 页获取到 27 条记录
2025-07-03 19:31:14,426 - INFO - 查询完成，共获取到 527 条记录
2025-07-03 19:31:14,426 - INFO - 获取到 527 条表单数据
2025-07-03 19:31:14,426 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-07-03 19:31:14,426 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 19:31:14,426 - INFO - 开始处理日期: 2025-07-02
2025-07-03 19:31:14,426 - INFO - Request Parameters - Page 1:
2025-07-03 19:31:14,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:14,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:15,192 - INFO - Response - Page 1:
2025-07-03 19:31:15,192 - INFO - 第 1 页获取到 50 条记录
2025-07-03 19:31:15,693 - INFO - Request Parameters - Page 2:
2025-07-03 19:31:15,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:15,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:16,318 - INFO - Response - Page 2:
2025-07-03 19:31:16,318 - INFO - 第 2 页获取到 50 条记录
2025-07-03 19:31:16,818 - INFO - Request Parameters - Page 3:
2025-07-03 19:31:16,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:16,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:17,553 - INFO - Response - Page 3:
2025-07-03 19:31:17,553 - INFO - 第 3 页获取到 50 条记录
2025-07-03 19:31:18,069 - INFO - Request Parameters - Page 4:
2025-07-03 19:31:18,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:18,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:18,709 - INFO - Response - Page 4:
2025-07-03 19:31:18,709 - INFO - 第 4 页获取到 50 条记录
2025-07-03 19:31:19,210 - INFO - Request Parameters - Page 5:
2025-07-03 19:31:19,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:19,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:19,866 - INFO - Response - Page 5:
2025-07-03 19:31:19,866 - INFO - 第 5 页获取到 50 条记录
2025-07-03 19:31:20,382 - INFO - Request Parameters - Page 6:
2025-07-03 19:31:20,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:20,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:21,163 - INFO - Response - Page 6:
2025-07-03 19:31:21,163 - INFO - 第 6 页获取到 50 条记录
2025-07-03 19:31:21,679 - INFO - Request Parameters - Page 7:
2025-07-03 19:31:21,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:21,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:22,383 - INFO - Response - Page 7:
2025-07-03 19:31:22,383 - INFO - 第 7 页获取到 50 条记录
2025-07-03 19:31:22,883 - INFO - Request Parameters - Page 8:
2025-07-03 19:31:22,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:22,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:23,571 - INFO - Response - Page 8:
2025-07-03 19:31:23,571 - INFO - 第 8 页获取到 50 条记录
2025-07-03 19:31:24,087 - INFO - Request Parameters - Page 9:
2025-07-03 19:31:24,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:24,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:24,759 - INFO - Response - Page 9:
2025-07-03 19:31:24,759 - INFO - 第 9 页获取到 50 条记录
2025-07-03 19:31:25,259 - INFO - Request Parameters - Page 10:
2025-07-03 19:31:25,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:25,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:25,947 - INFO - Response - Page 10:
2025-07-03 19:31:25,947 - INFO - 第 10 页获取到 50 条记录
2025-07-03 19:31:26,462 - INFO - Request Parameters - Page 11:
2025-07-03 19:31:26,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:26,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:27,135 - INFO - Response - Page 11:
2025-07-03 19:31:27,135 - INFO - 第 11 页获取到 50 条记录
2025-07-03 19:31:27,635 - INFO - Request Parameters - Page 12:
2025-07-03 19:31:27,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:27,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:28,104 - INFO - Response - Page 12:
2025-07-03 19:31:28,104 - INFO - 第 12 页获取到 9 条记录
2025-07-03 19:31:28,604 - INFO - 查询完成，共获取到 559 条记录
2025-07-03 19:31:28,604 - INFO - 获取到 559 条表单数据
2025-07-03 19:31:28,604 - INFO - 当前日期 2025-07-02 有 187 条MySQL数据需要处理
2025-07-03 19:31:28,604 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMRA
2025-07-03 19:31:29,135 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMRA
2025-07-03 19:31:29,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6690.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6690.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-03 19:31:29,135 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMF3
2025-07-03 19:31:29,729 - INFO - 更新表单数据成功: FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCMF3
2025-07-03 19:31:29,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10741.59}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10741.59}, {'field': 'order_count', 'old_value': 0, 'new_value': 456}]
2025-07-03 19:31:29,729 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM7A
2025-07-03 19:31:30,277 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM7A
2025-07-03 19:31:30,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4540.0, 'new_value': 2782.0}, {'field': 'total_amount', 'old_value': 4540.0, 'new_value': 2782.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 29}]
2025-07-03 19:31:30,277 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMUA
2025-07-03 19:31:30,839 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMUA
2025-07-03 19:31:30,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 980.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 980.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-03 19:31:30,839 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMGA
2025-07-03 19:31:31,355 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMGA
2025-07-03 19:31:31,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1742.91}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1742.91}, {'field': 'order_count', 'old_value': 0, 'new_value': 82}]
2025-07-03 19:31:31,355 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM3A
2025-07-03 19:31:31,871 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM3A
2025-07-03 19:31:31,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15425.0, 'new_value': 13020.0}, {'field': 'total_amount', 'old_value': 15425.0, 'new_value': 13020.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 460}]
2025-07-03 19:31:31,871 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM8A
2025-07-03 19:31:32,418 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM8A
2025-07-03 19:31:32,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12450.0, 'new_value': 3600.0}, {'field': 'total_amount', 'old_value': 12450.0, 'new_value': 3600.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 60}]
2025-07-03 19:31:32,418 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMPA
2025-07-03 19:31:33,012 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMPA
2025-07-03 19:31:33,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 40000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 40000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 20}]
2025-07-03 19:31:33,012 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMBA
2025-07-03 19:31:33,637 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMBA
2025-07-03 19:31:33,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2300.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2300.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 200}]
2025-07-03 19:31:33,637 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM5A
2025-07-03 19:31:34,169 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM5A
2025-07-03 19:31:34,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1458.0, 'new_value': 1870.69}, {'field': 'total_amount', 'old_value': 1458.0, 'new_value': 1870.69}, {'field': 'order_count', 'old_value': 45, 'new_value': 161}]
2025-07-03 19:31:34,169 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMFA
2025-07-03 19:31:34,794 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMFA
2025-07-03 19:31:34,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4900.72}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 788.5}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5689.22}, {'field': 'order_count', 'old_value': 0, 'new_value': 366}]
2025-07-03 19:31:34,794 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM6A
2025-07-03 19:31:35,341 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM6A
2025-07-03 19:31:35,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9540.0, 'new_value': 15786.93}, {'field': 'total_amount', 'old_value': 9540.0, 'new_value': 15786.93}, {'field': 'order_count', 'old_value': 2, 'new_value': 32}]
2025-07-03 19:31:35,341 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMHA
2025-07-03 19:31:35,919 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMHA
2025-07-03 19:31:35,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 20}]
2025-07-03 19:31:35,919 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMMA
2025-07-03 19:31:36,435 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMMA
2025-07-03 19:31:36,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 477.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 477.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-03 19:31:36,435 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM4A
2025-07-03 19:31:36,967 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCM4A
2025-07-03 19:31:36,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13000.0, 'new_value': 15208.0}, {'field': 'total_amount', 'old_value': 13000.0, 'new_value': 15208.0}]
2025-07-03 19:31:36,967 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMOA
2025-07-03 19:31:37,545 - INFO - 更新表单数据成功: FINST-R8666Q713URWKO7QACISY743OG3I3LF8TRMCMOA
2025-07-03 19:31:37,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1492.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1492.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-03 19:31:37,545 - INFO - 开始批量插入 1 条新记录
2025-07-03 19:31:37,701 - INFO - 批量插入响应状态码: 200
2025-07-03 19:31:37,701 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 11:31:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C5079858-349E-7E3E-B7C1-7A457B849332', 'x-acs-trace-id': 'c291a0d2a56efe04cbc42af66b7a7526', 'etag': '6NjfJ+y2E1Xu018j5UxM6aQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 19:31:37,701 - INFO - 批量插入响应体: {'result': ['FINST-HXD667B1J1UWID3QBR8DSAKFPMRA39A62BNCMP1']}
2025-07-03 19:31:37,701 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-03 19:31:37,701 - INFO - 成功插入的数据ID: ['FINST-HXD667B1J1UWID3QBR8DSAKFPMRA39A62BNCMP1']
2025-07-03 19:31:42,719 - INFO - 批量插入完成，共 1 条记录
2025-07-03 19:31:42,719 - INFO - 日期 2025-07-02 处理完成 - 更新: 16 条，插入: 1 条，错误: 0 条
2025-07-03 19:31:42,719 - INFO - 开始处理日期: 2025-07-03
2025-07-03 19:31:42,719 - INFO - Request Parameters - Page 1:
2025-07-03 19:31:42,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:31:42,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:31:43,172 - INFO - Response - Page 1:
2025-07-03 19:31:43,172 - INFO - 第 1 页获取到 1 条记录
2025-07-03 19:31:43,688 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 19:31:43,688 - INFO - 获取到 1 条表单数据
2025-07-03 19:31:43,688 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 19:31:43,688 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 19:31:43,688 - INFO - 数据同步完成！更新: 16 条，插入: 1 条，错误: 1 条
2025-07-03 19:32:43,728 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 19:32:43,728 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 19:32:43,728 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 19:32:43,884 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 580 条记录
2025-07-03 19:32:43,884 - INFO - 获取到 2 个日期需要处理: ['2025-07-02', '2025-07-03']
2025-07-03 19:32:43,884 - INFO - 开始处理日期: 2025-07-02
2025-07-03 19:32:43,884 - INFO - Request Parameters - Page 1:
2025-07-03 19:32:43,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:43,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:44,572 - INFO - Response - Page 1:
2025-07-03 19:32:44,572 - INFO - 第 1 页获取到 50 条记录
2025-07-03 19:32:45,103 - INFO - Request Parameters - Page 2:
2025-07-03 19:32:45,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:45,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:45,713 - INFO - Response - Page 2:
2025-07-03 19:32:45,713 - INFO - 第 2 页获取到 50 条记录
2025-07-03 19:32:46,213 - INFO - Request Parameters - Page 3:
2025-07-03 19:32:46,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:46,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:46,838 - INFO - Response - Page 3:
2025-07-03 19:32:46,838 - INFO - 第 3 页获取到 50 条记录
2025-07-03 19:32:47,354 - INFO - Request Parameters - Page 4:
2025-07-03 19:32:47,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:47,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:47,979 - INFO - Response - Page 4:
2025-07-03 19:32:47,979 - INFO - 第 4 页获取到 50 条记录
2025-07-03 19:32:48,480 - INFO - Request Parameters - Page 5:
2025-07-03 19:32:48,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:48,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:49,136 - INFO - Response - Page 5:
2025-07-03 19:32:49,136 - INFO - 第 5 页获取到 50 条记录
2025-07-03 19:32:49,652 - INFO - Request Parameters - Page 6:
2025-07-03 19:32:49,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:49,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:50,418 - INFO - Response - Page 6:
2025-07-03 19:32:50,418 - INFO - 第 6 页获取到 50 条记录
2025-07-03 19:32:50,934 - INFO - Request Parameters - Page 7:
2025-07-03 19:32:50,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:50,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:51,621 - INFO - Response - Page 7:
2025-07-03 19:32:51,621 - INFO - 第 7 页获取到 50 条记录
2025-07-03 19:32:52,137 - INFO - Request Parameters - Page 8:
2025-07-03 19:32:52,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:52,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:52,841 - INFO - Response - Page 8:
2025-07-03 19:32:52,841 - INFO - 第 8 页获取到 50 条记录
2025-07-03 19:32:53,356 - INFO - Request Parameters - Page 9:
2025-07-03 19:32:53,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:53,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:53,997 - INFO - Response - Page 9:
2025-07-03 19:32:54,013 - INFO - 第 9 页获取到 50 条记录
2025-07-03 19:32:54,513 - INFO - Request Parameters - Page 10:
2025-07-03 19:32:54,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:54,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:55,217 - INFO - Response - Page 10:
2025-07-03 19:32:55,217 - INFO - 第 10 页获取到 50 条记录
2025-07-03 19:32:55,717 - INFO - Request Parameters - Page 11:
2025-07-03 19:32:55,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:55,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:56,326 - INFO - Response - Page 11:
2025-07-03 19:32:56,326 - INFO - 第 11 页获取到 50 条记录
2025-07-03 19:32:56,842 - INFO - Request Parameters - Page 12:
2025-07-03 19:32:56,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:56,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:57,374 - INFO - Response - Page 12:
2025-07-03 19:32:57,374 - INFO - 第 12 页获取到 10 条记录
2025-07-03 19:32:57,874 - INFO - 查询完成，共获取到 560 条记录
2025-07-03 19:32:57,874 - INFO - 获取到 560 条表单数据
2025-07-03 19:32:57,874 - INFO - 当前日期 2025-07-02 有 560 条MySQL数据需要处理
2025-07-03 19:32:57,890 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 19:32:57,890 - INFO - 开始处理日期: 2025-07-03
2025-07-03 19:32:57,890 - INFO - Request Parameters - Page 1:
2025-07-03 19:32:57,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 19:32:57,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 19:32:58,358 - INFO - Response - Page 1:
2025-07-03 19:32:58,358 - INFO - 第 1 页获取到 1 条记录
2025-07-03 19:32:58,874 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 19:32:58,874 - INFO - 获取到 1 条表单数据
2025-07-03 19:32:58,874 - INFO - 当前日期 2025-07-03 有 1 条MySQL数据需要处理
2025-07-03 19:32:58,874 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 19:32:58,874 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 19:32:58,874 - INFO - 同步完成
2025-07-03 22:30:35,364 - INFO - 使用默认增量同步（当天更新数据）
2025-07-03 22:30:35,364 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-03 22:30:35,364 - INFO - 查询参数: ('2025-07-03',)
2025-07-03 22:30:35,521 - INFO - MySQL查询成功，增量数据（日期: 2025-07-03），共获取 241 条记录
2025-07-03 22:30:35,521 - INFO - 获取到 7 个日期需要处理: ['2025-06-10', '2025-06-16', '2025-06-20', '2025-06-29', '2025-06-30', '2025-07-02', '2025-07-03']
2025-07-03 22:30:35,536 - INFO - 开始处理日期: 2025-06-10
2025-07-03 22:30:35,536 - INFO - Request Parameters - Page 1:
2025-07-03 22:30:35,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:35,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749484800000, 1749571199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:43,665 - ERROR - 处理日期 2025-06-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 07ED33B4-C2F6-7545-9F9F-4180DA8F9A0D Response: {'code': 'ServiceUnavailable', 'requestid': '07ED33B4-C2F6-7545-9F9F-4180DA8F9A0D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 07ED33B4-C2F6-7545-9F9F-4180DA8F9A0D)
2025-07-03 22:30:43,665 - INFO - 开始处理日期: 2025-06-16
2025-07-03 22:30:43,665 - INFO - Request Parameters - Page 1:
2025-07-03 22:30:43,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:43,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:49,855 - INFO - Response - Page 1:
2025-07-03 22:30:49,855 - INFO - 第 1 页获取到 50 条记录
2025-07-03 22:30:50,355 - INFO - Request Parameters - Page 2:
2025-07-03 22:30:50,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:50,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:51,043 - INFO - Response - Page 2:
2025-07-03 22:30:51,043 - INFO - 第 2 页获取到 50 条记录
2025-07-03 22:30:51,558 - INFO - Request Parameters - Page 3:
2025-07-03 22:30:51,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:51,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:52,231 - INFO - Response - Page 3:
2025-07-03 22:30:52,231 - INFO - 第 3 页获取到 50 条记录
2025-07-03 22:30:52,746 - INFO - Request Parameters - Page 4:
2025-07-03 22:30:52,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:52,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:53,450 - INFO - Response - Page 4:
2025-07-03 22:30:53,450 - INFO - 第 4 页获取到 50 条记录
2025-07-03 22:30:53,966 - INFO - Request Parameters - Page 5:
2025-07-03 22:30:53,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:53,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:54,716 - INFO - Response - Page 5:
2025-07-03 22:30:54,716 - INFO - 第 5 页获取到 50 条记录
2025-07-03 22:30:55,216 - INFO - Request Parameters - Page 6:
2025-07-03 22:30:55,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:55,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:55,904 - INFO - Response - Page 6:
2025-07-03 22:30:55,904 - INFO - 第 6 页获取到 50 条记录
2025-07-03 22:30:56,420 - INFO - Request Parameters - Page 7:
2025-07-03 22:30:56,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:56,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:57,092 - INFO - Response - Page 7:
2025-07-03 22:30:57,092 - INFO - 第 7 页获取到 50 条记录
2025-07-03 22:30:57,608 - INFO - Request Parameters - Page 8:
2025-07-03 22:30:57,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:57,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:58,311 - INFO - Response - Page 8:
2025-07-03 22:30:58,311 - INFO - 第 8 页获取到 50 条记录
2025-07-03 22:30:58,827 - INFO - Request Parameters - Page 9:
2025-07-03 22:30:58,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:58,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:30:59,499 - INFO - Response - Page 9:
2025-07-03 22:30:59,499 - INFO - 第 9 页获取到 50 条记录
2025-07-03 22:30:59,999 - INFO - Request Parameters - Page 10:
2025-07-03 22:30:59,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:30:59,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:00,687 - INFO - Response - Page 10:
2025-07-03 22:31:00,687 - INFO - 第 10 页获取到 50 条记录
2025-07-03 22:31:01,203 - INFO - Request Parameters - Page 11:
2025-07-03 22:31:01,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:01,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:01,859 - INFO - Response - Page 11:
2025-07-03 22:31:01,859 - INFO - 第 11 页获取到 37 条记录
2025-07-03 22:31:02,360 - INFO - 查询完成，共获取到 537 条记录
2025-07-03 22:31:02,360 - INFO - 获取到 537 条表单数据
2025-07-03 22:31:02,360 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-07-03 22:31:02,360 - INFO - 开始批量插入 1 条新记录
2025-07-03 22:31:02,531 - INFO - 批量插入响应状态码: 200
2025-07-03 22:31:02,531 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 14:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '022659E2-0E9A-730F-84FA-B0714255B1F2', 'x-acs-trace-id': '6ef0544412f8f3a26bfda322050cc060', 'etag': '6zzk48MZeg4ASNRGXfOaD1Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 22:31:02,531 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B19VSWS0MM9TQF06KPTJ822L6TGHNCMRJ']}
2025-07-03 22:31:02,531 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-03 22:31:02,531 - INFO - 成功插入的数据ID: ['FINST-1MD668B19VSWS0MM9TQF06KPTJ822L6TGHNCMRJ']
2025-07-03 22:31:07,549 - INFO - 批量插入完成，共 1 条记录
2025-07-03 22:31:07,549 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-03 22:31:07,549 - INFO - 开始处理日期: 2025-06-20
2025-07-03 22:31:07,549 - INFO - Request Parameters - Page 1:
2025-07-03 22:31:07,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:07,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:08,206 - INFO - Response - Page 1:
2025-07-03 22:31:08,206 - INFO - 第 1 页获取到 50 条记录
2025-07-03 22:31:08,721 - INFO - Request Parameters - Page 2:
2025-07-03 22:31:08,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:08,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:09,440 - INFO - Response - Page 2:
2025-07-03 22:31:09,440 - INFO - 第 2 页获取到 50 条记录
2025-07-03 22:31:09,941 - INFO - Request Parameters - Page 3:
2025-07-03 22:31:09,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:09,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:10,675 - INFO - Response - Page 3:
2025-07-03 22:31:10,675 - INFO - 第 3 页获取到 50 条记录
2025-07-03 22:31:11,176 - INFO - Request Parameters - Page 4:
2025-07-03 22:31:11,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:11,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:11,816 - INFO - Response - Page 4:
2025-07-03 22:31:11,816 - INFO - 第 4 页获取到 50 条记录
2025-07-03 22:31:12,332 - INFO - Request Parameters - Page 5:
2025-07-03 22:31:12,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:12,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:13,067 - INFO - Response - Page 5:
2025-07-03 22:31:13,067 - INFO - 第 5 页获取到 50 条记录
2025-07-03 22:31:13,567 - INFO - Request Parameters - Page 6:
2025-07-03 22:31:13,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:13,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:14,255 - INFO - Response - Page 6:
2025-07-03 22:31:14,255 - INFO - 第 6 页获取到 50 条记录
2025-07-03 22:31:14,771 - INFO - Request Parameters - Page 7:
2025-07-03 22:31:14,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:14,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:15,474 - INFO - Response - Page 7:
2025-07-03 22:31:15,474 - INFO - 第 7 页获取到 50 条记录
2025-07-03 22:31:15,990 - INFO - Request Parameters - Page 8:
2025-07-03 22:31:15,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:15,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:16,662 - INFO - Response - Page 8:
2025-07-03 22:31:16,662 - INFO - 第 8 页获取到 50 条记录
2025-07-03 22:31:17,162 - INFO - Request Parameters - Page 9:
2025-07-03 22:31:17,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:17,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:17,866 - INFO - Response - Page 9:
2025-07-03 22:31:17,866 - INFO - 第 9 页获取到 50 条记录
2025-07-03 22:31:18,366 - INFO - Request Parameters - Page 10:
2025-07-03 22:31:18,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:18,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:18,944 - INFO - Response - Page 10:
2025-07-03 22:31:18,944 - INFO - 第 10 页获取到 17 条记录
2025-07-03 22:31:19,460 - INFO - 查询完成，共获取到 467 条记录
2025-07-03 22:31:19,460 - INFO - 获取到 467 条表单数据
2025-07-03 22:31:19,460 - INFO - 当前日期 2025-06-20 有 1 条MySQL数据需要处理
2025-07-03 22:31:19,460 - INFO - 开始批量插入 1 条新记录
2025-07-03 22:31:19,616 - INFO - 批量插入响应状态码: 200
2025-07-03 22:31:19,616 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 14:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2A5CBB55-6CD7-76F3-B773-51FB5EE16A0C', 'x-acs-trace-id': '260e016d0f15396e8c47f9d56b26e566', 'etag': '630ngxJQu9H7VB7eUG1POvg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 22:31:19,616 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC1XVTWX622DOCPI5OK7FBV2ZC6HHNCMS2']}
2025-07-03 22:31:19,616 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-03 22:31:19,616 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC1XVTWX622DOCPI5OK7FBV2ZC6HHNCMS2']
2025-07-03 22:31:24,634 - INFO - 批量插入完成，共 1 条记录
2025-07-03 22:31:24,634 - INFO - 日期 2025-06-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-03 22:31:24,634 - INFO - 开始处理日期: 2025-06-29
2025-07-03 22:31:24,634 - INFO - Request Parameters - Page 1:
2025-07-03 22:31:24,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:24,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:25,306 - INFO - Response - Page 1:
2025-07-03 22:31:25,306 - INFO - 第 1 页获取到 50 条记录
2025-07-03 22:31:25,806 - INFO - Request Parameters - Page 2:
2025-07-03 22:31:25,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:25,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:26,588 - INFO - Response - Page 2:
2025-07-03 22:31:26,588 - INFO - 第 2 页获取到 50 条记录
2025-07-03 22:31:27,104 - INFO - Request Parameters - Page 3:
2025-07-03 22:31:27,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:27,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:27,791 - INFO - Response - Page 3:
2025-07-03 22:31:27,791 - INFO - 第 3 页获取到 50 条记录
2025-07-03 22:31:28,307 - INFO - Request Parameters - Page 4:
2025-07-03 22:31:28,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:28,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:29,073 - INFO - Response - Page 4:
2025-07-03 22:31:29,073 - INFO - 第 4 页获取到 50 条记录
2025-07-03 22:31:29,589 - INFO - Request Parameters - Page 5:
2025-07-03 22:31:29,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:29,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:30,199 - INFO - Response - Page 5:
2025-07-03 22:31:30,199 - INFO - 第 5 页获取到 50 条记录
2025-07-03 22:31:30,699 - INFO - Request Parameters - Page 6:
2025-07-03 22:31:30,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:30,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:31,418 - INFO - Response - Page 6:
2025-07-03 22:31:31,418 - INFO - 第 6 页获取到 50 条记录
2025-07-03 22:31:31,918 - INFO - Request Parameters - Page 7:
2025-07-03 22:31:31,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:31,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:32,606 - INFO - Response - Page 7:
2025-07-03 22:31:32,606 - INFO - 第 7 页获取到 50 条记录
2025-07-03 22:31:33,106 - INFO - Request Parameters - Page 8:
2025-07-03 22:31:33,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:33,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:33,747 - INFO - Response - Page 8:
2025-07-03 22:31:33,763 - INFO - 第 8 页获取到 50 条记录
2025-07-03 22:31:34,263 - INFO - Request Parameters - Page 9:
2025-07-03 22:31:34,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:34,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:34,904 - INFO - Response - Page 9:
2025-07-03 22:31:34,904 - INFO - 第 9 页获取到 50 条记录
2025-07-03 22:31:35,420 - INFO - Request Parameters - Page 10:
2025-07-03 22:31:35,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:35,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:36,139 - INFO - Response - Page 10:
2025-07-03 22:31:36,139 - INFO - 第 10 页获取到 50 条记录
2025-07-03 22:31:36,654 - INFO - Request Parameters - Page 11:
2025-07-03 22:31:36,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:36,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:37,436 - INFO - Response - Page 11:
2025-07-03 22:31:37,436 - INFO - 第 11 页获取到 39 条记录
2025-07-03 22:31:37,936 - INFO - 查询完成，共获取到 539 条记录
2025-07-03 22:31:37,936 - INFO - 获取到 539 条表单数据
2025-07-03 22:31:37,936 - INFO - 当前日期 2025-06-29 有 1 条MySQL数据需要处理
2025-07-03 22:31:37,936 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 22:31:37,936 - INFO - 开始处理日期: 2025-06-30
2025-07-03 22:31:37,936 - INFO - Request Parameters - Page 1:
2025-07-03 22:31:37,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:37,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:38,561 - INFO - Response - Page 1:
2025-07-03 22:31:38,561 - INFO - 第 1 页获取到 50 条记录
2025-07-03 22:31:39,062 - INFO - Request Parameters - Page 2:
2025-07-03 22:31:39,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:39,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:39,718 - INFO - Response - Page 2:
2025-07-03 22:31:39,718 - INFO - 第 2 页获取到 50 条记录
2025-07-03 22:31:40,234 - INFO - Request Parameters - Page 3:
2025-07-03 22:31:40,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:40,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:40,875 - INFO - Response - Page 3:
2025-07-03 22:31:40,875 - INFO - 第 3 页获取到 50 条记录
2025-07-03 22:31:41,391 - INFO - Request Parameters - Page 4:
2025-07-03 22:31:41,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:41,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:42,063 - INFO - Response - Page 4:
2025-07-03 22:31:42,063 - INFO - 第 4 页获取到 50 条记录
2025-07-03 22:31:42,579 - INFO - Request Parameters - Page 5:
2025-07-03 22:31:42,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:42,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:43,219 - INFO - Response - Page 5:
2025-07-03 22:31:43,219 - INFO - 第 5 页获取到 50 条记录
2025-07-03 22:31:43,720 - INFO - Request Parameters - Page 6:
2025-07-03 22:31:43,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:43,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:44,345 - INFO - Response - Page 6:
2025-07-03 22:31:44,345 - INFO - 第 6 页获取到 50 条记录
2025-07-03 22:31:44,845 - INFO - Request Parameters - Page 7:
2025-07-03 22:31:44,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:44,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:45,517 - INFO - Response - Page 7:
2025-07-03 22:31:45,517 - INFO - 第 7 页获取到 50 条记录
2025-07-03 22:31:46,033 - INFO - Request Parameters - Page 8:
2025-07-03 22:31:46,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:46,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:46,721 - INFO - Response - Page 8:
2025-07-03 22:31:46,721 - INFO - 第 8 页获取到 50 条记录
2025-07-03 22:31:47,237 - INFO - Request Parameters - Page 9:
2025-07-03 22:31:47,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:47,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:47,940 - INFO - Response - Page 9:
2025-07-03 22:31:47,940 - INFO - 第 9 页获取到 50 条记录
2025-07-03 22:31:48,456 - INFO - Request Parameters - Page 10:
2025-07-03 22:31:48,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:48,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:49,159 - INFO - Response - Page 10:
2025-07-03 22:31:49,159 - INFO - 第 10 页获取到 50 条记录
2025-07-03 22:31:49,660 - INFO - Request Parameters - Page 11:
2025-07-03 22:31:49,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:49,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:50,285 - INFO - Response - Page 11:
2025-07-03 22:31:50,285 - INFO - 第 11 页获取到 27 条记录
2025-07-03 22:31:50,785 - INFO - 查询完成，共获取到 527 条记录
2025-07-03 22:31:50,785 - INFO - 获取到 527 条表单数据
2025-07-03 22:31:50,785 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-07-03 22:31:50,785 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 22:31:50,785 - INFO - 开始处理日期: 2025-07-02
2025-07-03 22:31:50,785 - INFO - Request Parameters - Page 1:
2025-07-03 22:31:50,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:50,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:51,629 - INFO - Response - Page 1:
2025-07-03 22:31:51,629 - INFO - 第 1 页获取到 50 条记录
2025-07-03 22:31:52,129 - INFO - Request Parameters - Page 2:
2025-07-03 22:31:52,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:52,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:52,833 - INFO - Response - Page 2:
2025-07-03 22:31:52,833 - INFO - 第 2 页获取到 50 条记录
2025-07-03 22:31:53,348 - INFO - Request Parameters - Page 3:
2025-07-03 22:31:53,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:53,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:54,005 - INFO - Response - Page 3:
2025-07-03 22:31:54,005 - INFO - 第 3 页获取到 50 条记录
2025-07-03 22:31:54,521 - INFO - Request Parameters - Page 4:
2025-07-03 22:31:54,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:54,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:55,224 - INFO - Response - Page 4:
2025-07-03 22:31:55,224 - INFO - 第 4 页获取到 50 条记录
2025-07-03 22:31:55,740 - INFO - Request Parameters - Page 5:
2025-07-03 22:31:55,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:55,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:56,397 - INFO - Response - Page 5:
2025-07-03 22:31:56,397 - INFO - 第 5 页获取到 50 条记录
2025-07-03 22:31:56,912 - INFO - Request Parameters - Page 6:
2025-07-03 22:31:56,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:56,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:57,616 - INFO - Response - Page 6:
2025-07-03 22:31:57,616 - INFO - 第 6 页获取到 50 条记录
2025-07-03 22:31:58,116 - INFO - Request Parameters - Page 7:
2025-07-03 22:31:58,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:58,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:58,773 - INFO - Response - Page 7:
2025-07-03 22:31:58,773 - INFO - 第 7 页获取到 50 条记录
2025-07-03 22:31:59,273 - INFO - Request Parameters - Page 8:
2025-07-03 22:31:59,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:31:59,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:31:59,945 - INFO - Response - Page 8:
2025-07-03 22:31:59,945 - INFO - 第 8 页获取到 50 条记录
2025-07-03 22:32:00,445 - INFO - Request Parameters - Page 9:
2025-07-03 22:32:00,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:32:00,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:32:01,117 - INFO - Response - Page 9:
2025-07-03 22:32:01,117 - INFO - 第 9 页获取到 50 条记录
2025-07-03 22:32:01,633 - INFO - Request Parameters - Page 10:
2025-07-03 22:32:01,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:32:01,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:32:02,305 - INFO - Response - Page 10:
2025-07-03 22:32:02,305 - INFO - 第 10 页获取到 50 条记录
2025-07-03 22:32:02,821 - INFO - Request Parameters - Page 11:
2025-07-03 22:32:02,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:32:02,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:32:03,493 - INFO - Response - Page 11:
2025-07-03 22:32:03,493 - INFO - 第 11 页获取到 50 条记录
2025-07-03 22:32:04,009 - INFO - Request Parameters - Page 12:
2025-07-03 22:32:04,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:32:04,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:32:04,556 - INFO - Response - Page 12:
2025-07-03 22:32:04,556 - INFO - 第 12 页获取到 10 条记录
2025-07-03 22:32:05,072 - INFO - 查询完成，共获取到 560 条记录
2025-07-03 22:32:05,072 - INFO - 获取到 560 条表单数据
2025-07-03 22:32:05,072 - INFO - 当前日期 2025-07-02 有 187 条MySQL数据需要处理
2025-07-03 22:32:05,088 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 22:32:05,088 - INFO - 开始处理日期: 2025-07-03
2025-07-03 22:32:05,088 - INFO - Request Parameters - Page 1:
2025-07-03 22:32:05,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:32:05,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:32:05,525 - INFO - Response - Page 1:
2025-07-03 22:32:05,525 - INFO - 第 1 页获取到 1 条记录
2025-07-03 22:32:06,025 - INFO - 查询完成，共获取到 1 条记录
2025-07-03 22:32:06,025 - INFO - 获取到 1 条表单数据
2025-07-03 22:32:06,025 - INFO - 当前日期 2025-07-03 有 40 条MySQL数据需要处理
2025-07-03 22:32:06,025 - INFO - 开始批量插入 39 条新记录
2025-07-03 22:32:06,260 - INFO - 批量插入响应状态码: 200
2025-07-03 22:32:06,260 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 03 Jul 2025 14:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1884', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D7901D3C-0FDF-70C2-8553-765F3CB5A8C0', 'x-acs-trace-id': '28113912e8e7804f6bee379766a9fe7f', 'etag': '1gwKfxFgGY6DcaKQbBqndvQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-03 22:32:06,260 - INFO - 批量插入响应体: {'result': ['FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMMM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMNM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMOM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMPM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMQM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMRM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMSM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMTM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMUM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMVM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMWM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMXM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMYM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMZM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM0N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM1N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM2N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM3N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM4N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM5N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM6N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM7N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM8N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM9N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMAN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMBN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMCN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMDN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMEN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMFN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMGN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMHN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMIN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMJN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMKN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMLN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMMN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMNN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMON']}
2025-07-03 22:32:06,260 - INFO - 批量插入表单数据成功，批次 1，共 39 条记录
2025-07-03 22:32:06,260 - INFO - 成功插入的数据ID: ['FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMMM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMNM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMOM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMPM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMQM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMRM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMSM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMTM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMUM', 'FINST-74766M712QRWG03688AZR8E73VAT35C6IHNCMVM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMWM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMXM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMYM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMZM', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM0N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM1N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM2N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM3N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM4N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM5N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM6N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM7N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM8N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCM9N', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMAN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMBN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMCN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMDN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMEN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMFN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMGN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMHN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMIN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMJN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMKN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMLN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMMN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMNN', 'FINST-74766M712QRWG03688AZR8E73VAT36C6IHNCMON']
2025-07-03 22:32:11,277 - INFO - 批量插入完成，共 39 条记录
2025-07-03 22:32:11,277 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 39 条，错误: 0 条
2025-07-03 22:32:11,277 - INFO - 数据同步完成！更新: 0 条，插入: 41 条，错误: 1 条
2025-07-03 22:33:11,317 - INFO - 开始同步昨天与今天的销售数据: 2025-07-02 至 2025-07-03
2025-07-03 22:33:11,317 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-03 22:33:11,317 - INFO - 查询参数: ('2025-07-02', '2025-07-03')
2025-07-03 22:33:11,473 - INFO - MySQL查询成功，时间段: 2025-07-02 至 2025-07-03，共获取 622 条记录
2025-07-03 22:33:11,473 - INFO - 获取到 2 个日期需要处理: ['2025-07-02', '2025-07-03']
2025-07-03 22:33:11,473 - INFO - 开始处理日期: 2025-07-02
2025-07-03 22:33:11,473 - INFO - Request Parameters - Page 1:
2025-07-03 22:33:11,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:11,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:12,224 - INFO - Response - Page 1:
2025-07-03 22:33:12,224 - INFO - 第 1 页获取到 50 条记录
2025-07-03 22:33:12,739 - INFO - Request Parameters - Page 2:
2025-07-03 22:33:12,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:12,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:13,349 - INFO - Response - Page 2:
2025-07-03 22:33:13,349 - INFO - 第 2 页获取到 50 条记录
2025-07-03 22:33:13,849 - INFO - Request Parameters - Page 3:
2025-07-03 22:33:13,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:13,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:14,615 - INFO - Response - Page 3:
2025-07-03 22:33:14,615 - INFO - 第 3 页获取到 50 条记录
2025-07-03 22:33:15,131 - INFO - Request Parameters - Page 4:
2025-07-03 22:33:15,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:15,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:15,850 - INFO - Response - Page 4:
2025-07-03 22:33:15,850 - INFO - 第 4 页获取到 50 条记录
2025-07-03 22:33:16,366 - INFO - Request Parameters - Page 5:
2025-07-03 22:33:16,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:16,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:17,038 - INFO - Response - Page 5:
2025-07-03 22:33:17,038 - INFO - 第 5 页获取到 50 条记录
2025-07-03 22:33:17,538 - INFO - Request Parameters - Page 6:
2025-07-03 22:33:17,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:17,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:18,257 - INFO - Response - Page 6:
2025-07-03 22:33:18,257 - INFO - 第 6 页获取到 50 条记录
2025-07-03 22:33:18,757 - INFO - Request Parameters - Page 7:
2025-07-03 22:33:18,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:18,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:19,445 - INFO - Response - Page 7:
2025-07-03 22:33:19,445 - INFO - 第 7 页获取到 50 条记录
2025-07-03 22:33:19,945 - INFO - Request Parameters - Page 8:
2025-07-03 22:33:19,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:19,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:20,617 - INFO - Response - Page 8:
2025-07-03 22:33:20,617 - INFO - 第 8 页获取到 50 条记录
2025-07-03 22:33:21,133 - INFO - Request Parameters - Page 9:
2025-07-03 22:33:21,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:21,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:21,868 - INFO - Response - Page 9:
2025-07-03 22:33:21,868 - INFO - 第 9 页获取到 50 条记录
2025-07-03 22:33:22,384 - INFO - Request Parameters - Page 10:
2025-07-03 22:33:22,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:22,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:23,150 - INFO - Response - Page 10:
2025-07-03 22:33:23,150 - INFO - 第 10 页获取到 50 条记录
2025-07-03 22:33:23,650 - INFO - Request Parameters - Page 11:
2025-07-03 22:33:23,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:23,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:24,306 - INFO - Response - Page 11:
2025-07-03 22:33:24,306 - INFO - 第 11 页获取到 50 条记录
2025-07-03 22:33:24,807 - INFO - Request Parameters - Page 12:
2025-07-03 22:33:24,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:24,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:25,338 - INFO - Response - Page 12:
2025-07-03 22:33:25,338 - INFO - 第 12 页获取到 10 条记录
2025-07-03 22:33:25,854 - INFO - 查询完成，共获取到 560 条记录
2025-07-03 22:33:25,854 - INFO - 获取到 560 条表单数据
2025-07-03 22:33:25,854 - INFO - 当前日期 2025-07-02 有 560 条MySQL数据需要处理
2025-07-03 22:33:25,870 - INFO - 日期 2025-07-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 22:33:25,870 - INFO - 开始处理日期: 2025-07-03
2025-07-03 22:33:25,870 - INFO - Request Parameters - Page 1:
2025-07-03 22:33:25,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-03 22:33:25,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-03 22:33:26,495 - INFO - Response - Page 1:
2025-07-03 22:33:26,495 - INFO - 第 1 页获取到 40 条记录
2025-07-03 22:33:27,011 - INFO - 查询完成，共获取到 40 条记录
2025-07-03 22:33:27,011 - INFO - 获取到 40 条表单数据
2025-07-03 22:33:27,011 - INFO - 当前日期 2025-07-03 有 40 条MySQL数据需要处理
2025-07-03 22:33:27,011 - INFO - 日期 2025-07-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 22:33:27,011 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-03 22:33:27,011 - INFO - 同步完成
