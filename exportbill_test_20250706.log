2025-07-06 18:40:38,679 - INFO - ============================================================
2025-07-06 18:40:38,679 - INFO - 开始账单导入接口测试
2025-07-06 18:40:38,680 - INFO - ============================================================
2025-07-06 18:40:38,680 - INFO - 🧪 使用测试环境配置
2025-07-06 18:40:38,680 - INFO - 接口名称: com.gooagoo.exportbill
2025-07-06 18:40:38,680 - INFO - 请求地址: http://api.test.goago.cn/oapi/rest
2025-07-06 18:40:38,681 - INFO - 账单类型: 1 - 结账单
2025-07-06 18:40:38,681 - INFO - 细分类型: 10102 - 美团外卖单
2025-07-06 18:40:38,681 - INFO - 请求URL: http://api.test.goago.cn/oapi/rest
2025-07-06 18:40:38,681 - INFO - 请求参数: {'appId': 'd1667ebbaa3e4935a7e09be1a50f0af5', 'appKey': '2c968875814106ca0181adf9657d0005', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.exportbill', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"exactBillType": "10102", "billSerialNumber": "TEST1751798438", "terminalNumber": "6A53BB2D7CDE", "saleTime": "2025-07-06 18:40:38", "thirdPartyOrderNo": "ORDER1751798438", "receivableAmount": 55.22, "totalNum": 10.0, "totalFee": 60.0, "paidAmount": 55.22, "billType": "1"}', 'sign': '58B0368A6F16E0A0E6DC10A29BD8DFD7'}
2025-07-06 18:40:39,191 - INFO - 响应状态码: 200
2025-07-06 18:40:39,191 - INFO - 响应结果: {'rescode': 'OPEN_SUCCESS', 'resmsg': '账单导入成功', 'data': '成功', 'sign': '43D9D9E681E2143B8E356719CADD8CD6'}
2025-07-06 18:40:39,192 - INFO - ✅ 账单导入成功!
2025-07-06 18:40:39,192 - INFO - 📄 响应消息: 账单导入成功
2025-07-06 18:40:39,192 - INFO - 📊 业务数据: 成功
2025-07-06 18:40:39,193 - INFO - 📊 测试时间: 2025-07-06 18:40:39
2025-07-06 18:40:39,193 - INFO - � 请求参数: {
  "exactBillType": "10102",
  "billSerialNumber": "TEST1751798438",
  "terminalNumber": "6A53BB2D7CDE",
  "saleTime": "2025-07-06 18:40:38",
  "thirdPartyOrderNo": "ORDER1751798438",
  "receivableAmount": 55.22,
  "totalNum": 10.0,
  "totalFee": 60.0,
  "paidAmount": 55.22,
  "billType": "1"
}
2025-07-06 18:40:39,193 - INFO - ============================================================
2025-07-06 18:40:39,193 - INFO - 账单导入测试完成
2025-07-06 18:40:39,194 - INFO - ============================================================
2025-07-06 18:41:19,148 - INFO - ============================================================
2025-07-06 18:41:19,148 - INFO - 开始测试不同类型的账单
2025-07-06 18:41:19,149 - INFO - ============================================================
2025-07-06 18:41:19,149 - INFO - 
🧪 测试场景 1: 美团外卖结账单
2025-07-06 18:41:19,149 - INFO - 请求URL: http://api.test.goago.cn/oapi/rest
2025-07-06 18:41:19,149 - INFO - 请求参数: {'appId': 'd1667ebbaa3e4935a7e09be1a50f0af5', 'appKey': '2c968875814106ca0181adf9657d0005', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.exportbill', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"exactBillType": "10102", "billSerialNumber": "TEST101021751798479", "terminalNumber": "6A53BB2D7CDE", "saleTime": "2025-07-06 18:41:19", "thirdPartyOrderNo": "ORDER101021751798479", "receivableAmount": 88.88, "totalNum": 5.0, "totalFee": 90.0, "paidAmount": 88.88, "billType": "1"}', 'sign': '81A92385348AF319C787F19672D09D64'}
2025-07-06 18:41:19,329 - INFO - 响应状态码: 200
2025-07-06 18:41:19,329 - INFO - 响应结果: {'rescode': 'OPEN_SUCCESS', 'resmsg': '账单导入成功', 'data': '成功', 'sign': '43D9D9E681E2143B8E356719CADD8CD6'}
2025-07-06 18:41:19,330 - INFO -    ✅ 美团外卖结账单 测试成功
2025-07-06 18:41:20,332 - INFO - 
🧪 测试场景 2: 饿了么外卖退款单
2025-07-06 18:41:20,332 - INFO - 请求URL: http://api.test.goago.cn/oapi/rest
2025-07-06 18:41:20,333 - INFO - 请求参数: {'appId': 'd1667ebbaa3e4935a7e09be1a50f0af5', 'appKey': '2c968875814106ca0181adf9657d0005', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.exportbill', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"exactBillType": "10603", "billSerialNumber": "TEST106031751798480", "terminalNumber": "6A53BB2D7CDE", "saleTime": "2025-07-06 18:41:20", "thirdPartyOrderNo": "ORDER106031751798480", "receivableAmount": -25.5, "totalNum": 5.0, "totalFee": -25.5, "paidAmount": -25.5, "billType": "6"}', 'sign': 'D22C1459BBC001BE7454ED80C980520D'}
2025-07-06 18:41:20,463 - INFO - 响应状态码: 200
2025-07-06 18:41:20,463 - INFO - 响应结果: {'rescode': 'OPEN_SUCCESS', 'resmsg': '账单导入成功', 'data': '成功', 'sign': '43D9D9E681E2143B8E356719CADD8CD6'}
2025-07-06 18:41:20,463 - INFO -    ✅ 饿了么外卖退款单 测试成功
2025-07-06 18:41:21,465 - INFO - 
🧪 测试场景 3: 抖音外卖结账单
2025-07-06 18:41:21,465 - INFO - 请求URL: http://api.test.goago.cn/oapi/rest
2025-07-06 18:41:21,465 - INFO - 请求参数: {'appId': 'd1667ebbaa3e4935a7e09be1a50f0af5', 'appKey': '2c968875814106ca0181adf9657d0005', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.exportbill', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"exactBillType": "10104", "billSerialNumber": "TEST101041751798481", "terminalNumber": "6A53BB2D7CDE", "saleTime": "2025-07-06 18:41:21", "thirdPartyOrderNo": "ORDER101041751798481", "receivableAmount": 66.66, "totalNum": 5.0, "totalFee": 70.0, "paidAmount": 66.66, "billType": "1"}', 'sign': '4B871A2A621539B769C3E80D97DB06FE'}
2025-07-06 18:41:21,606 - INFO - 响应状态码: 200
2025-07-06 18:41:21,607 - INFO - 响应结果: {'rescode': 'OPEN_SUCCESS', 'resmsg': '账单导入成功', 'data': '成功', 'sign': '43D9D9E681E2143B8E356719CADD8CD6'}
2025-07-06 18:41:21,608 - INFO -    ✅ 抖音外卖结账单 测试成功
2025-07-06 18:41:22,609 - INFO - 
============================================================
2025-07-06 18:41:22,609 - INFO - 不同类型账单测试完成
2025-07-06 18:41:22,610 - INFO - ============================================================
2025-07-06 20:12:59,785 - INFO - ============================================================
2025-07-06 20:12:59,785 - INFO - 开始账单导入接口测试
2025-07-06 20:12:59,785 - INFO - ============================================================
2025-07-06 20:12:59,785 - INFO - 🧪 使用测试环境配置
2025-07-06 20:12:59,785 - INFO - 接口名称: com.gooagoo.exportbill
2025-07-06 20:12:59,785 - INFO - 请求地址: http://api.test.goago.cn/oapi/rest
2025-07-06 20:12:59,785 - INFO - 账单类型: 1 - 结账单
2025-07-06 20:12:59,785 - INFO - 细分类型: 10102 - 美团外卖单
2025-07-06 20:12:59,785 - INFO - 请求URL: http://api.test.goago.cn/oapi/rest
2025-07-06 20:12:59,785 - INFO - 请求参数: {'appId': 'd1667ebbaa3e4935a7e09be1a50f0af5', 'appKey': '2c968875814106ca0181adf9657d0005', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.exportbill', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"exactBillType": "10102", "billSerialNumber": "TEST1751803979", "terminalNumber": "6A53BB2D7CDE", "saleTime": "2025-07-06 20:12:59", "thirdPartyOrderNo": "ORDER1751803979", "receivableAmount": 55.22, "totalNum": 10.0, "totalFee": 60.0, "paidAmount": 55.22, "billType": "1"}', 'sign': '6488C3F74BF73A901B9B19F426F5E374'}
2025-07-06 20:13:00,193 - INFO - 响应状态码: 200
2025-07-06 20:13:00,193 - INFO - 响应结果: {'rescode': 'OPEN_SUCCESS', 'resmsg': '账单导入成功', 'data': '成功', 'sign': '43D9D9E681E2143B8E356719CADD8CD6'}
2025-07-06 20:13:00,194 - INFO - ✅ 账单导入成功!
2025-07-06 20:13:00,194 - INFO - 📄 响应消息: 账单导入成功
2025-07-06 20:13:00,194 - INFO - 📊 业务数据: 成功
2025-07-06 20:13:00,195 - INFO - 📊 测试时间: 2025-07-06 20:13:00
2025-07-06 20:13:00,195 - INFO - � 请求参数: {
  "exactBillType": "10102",
  "billSerialNumber": "TEST1751803979",
  "terminalNumber": "6A53BB2D7CDE",
  "saleTime": "2025-07-06 20:12:59",
  "thirdPartyOrderNo": "ORDER1751803979",
  "receivableAmount": 55.22,
  "totalNum": 10.0,
  "totalFee": 60.0,
  "paidAmount": 55.22,
  "billType": "1"
}
2025-07-06 20:13:00,195 - INFO - ============================================================
2025-07-06 20:13:00,195 - INFO - 账单导入测试完成
2025-07-06 20:13:00,196 - INFO - ============================================================
2025-07-06 20:14:42,135 - INFO - ============================================================
2025-07-06 20:14:42,135 - INFO - 开始账单导入接口测试
2025-07-06 20:14:42,135 - INFO - ============================================================
2025-07-06 20:14:42,135 - INFO - 🚀 使用生产环境配置
2025-07-06 20:14:42,135 - INFO - 接口名称: com.gooagoo.exportbill
2025-07-06 20:14:42,151 - INFO - 请求地址: http://api.gooagoo.com/oapi/rest
2025-07-06 20:14:42,151 - INFO - 账单类型: 1 - 结账单
2025-07-06 20:14:42,151 - INFO - 细分类型: 10102 - 美团外卖单
2025-07-06 20:14:42,151 - INFO - 请求URL: http://api.gooagoo.com/oapi/rest
2025-07-06 20:14:42,151 - INFO - 请求参数: {'appId': '0df229fdf08a45b1a61893db35d94bd6', 'appKey': '2c9a4b5d97d35a000197df5707610001', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.exportbill', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"exactBillType": "10102", "billSerialNumber": "TEST1751804082", "terminalNumber": "BBBB00000271", "saleTime": "2025-07-06 20:14:42", "thirdPartyOrderNo": "ORDER1751804082", "receivableAmount": 55.22, "totalNum": 10.0, "totalFee": 60.0, "paidAmount": 55.22, "billType": "1"}', 'sign': 'AE08FC951B3DCE41DD93638381B0C7AF'}
2025-07-06 20:14:42,414 - INFO - 响应状态码: 200
2025-07-06 20:14:42,414 - INFO - 响应结果: {'rescode': 'OPEN_SUCCESS', 'resmsg': '账单导入成功', 'data': '成功', 'sign': 'B8872B6EDA8E0F9432E06C0D4F093932'}
2025-07-06 20:14:42,430 - INFO - ✅ 账单导入成功!
2025-07-06 20:14:42,430 - INFO - 📄 响应消息: 账单导入成功
2025-07-06 20:14:42,430 - INFO - 📊 业务数据: 成功
2025-07-06 20:14:42,430 - INFO - 📊 测试时间: 2025-07-06 20:14:42
2025-07-06 20:14:42,430 - INFO - � 请求参数: {
  "exactBillType": "10102",
  "billSerialNumber": "TEST1751804082",
  "terminalNumber": "BBBB00000271",
  "saleTime": "2025-07-06 20:14:42",
  "thirdPartyOrderNo": "ORDER1751804082",
  "receivableAmount": 55.22,
  "totalNum": 10.0,
  "totalFee": 60.0,
  "paidAmount": 55.22,
  "billType": "1"
}
2025-07-06 20:14:42,430 - INFO - ============================================================
2025-07-06 20:14:42,430 - INFO - 账单导入测试完成
2025-07-06 20:14:42,430 - INFO - ============================================================
