import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import json
import time
import pymysql
from pathlib import Path
import os
import sys
import pandas as pd
from decimal import Decimal
from get_token import token
from get_shuyandata import call_sale_query_api
from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from get_token import token
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client

# 全局错误日期列表 - 用于存储API查询失败的日期
ERROR_DATES = set()

# MySQL数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Hxp@1987!@#',
    'database': 'mydatabase',
    'port': 43306,
    'charset': 'utf8mb4'
}

# 数据目录配置
DATA_DIR = Path('data')
DATA_DIR.mkdir(exist_ok=True)

# 配置日志
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)
today = datetime.now().strftime('%Y%m%d')
log_file = log_dir / f'sync_data_shuyan2yida_{today}.log'

# 检查日志目录权限和文件可写状态
try:
    # 测试日志目录写入权限
    test_file = log_dir / "test_permission.tmp"
    with open(test_file, 'w') as f:
        f.write('test')
    # 如果成功写入，则删除测试文件
    if test_file.exists():
        os.remove(test_file)
    print(f"日志目录 {log_dir} 可写入")
    
    # 如果日志文件已存在，检查是否可写
    if log_file.exists():
        # 尝试以追加方式打开
        with open(log_file, 'a') as f:
            pass
        print(f"日志文件 {log_file} 可写入")
except PermissionError as e:
    print(f"权限错误: {e}")
    # 降级到当前目录
    log_dir = Path('.')
    log_file = log_dir / f'sync_data_shuyan2yida_{today}.log'
    print(f"改用当前目录下的日志文件: {log_file}")
except Exception as e:
    print(f"日志文件设置错误: {e}")
    # 降级到当前目录
    log_dir = Path('.')
    log_file = log_dir / f'sync_data_shuyan2yida_{today}.log'
    print(f"改用当前目录下的日志文件: {log_file}")

# 配置根日志记录器
try:
    # 确保没有重复配置
    root_logger = logging.getLogger()
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='a')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 添加处理器到根日志记录器
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # 记录初始日志
    logging.info("=" * 50)
    logging.info(f"程序启动 - 版本 v1.0.0")
    logging.info(f"日志文件: {log_file}")
    logging.info("=" * 50)
    
except Exception as e:
    print(f"配置日志记录器时出错: {str(e)}")
    # 使用基本配置作为后备
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    logging.warning(f"无法配置文件日志，仅使用控制台日志: {str(e)}")

# 宜搭配置
YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P'  # 日销售表单
}

# 宜搭月销售表配置
YIDA_MONTHLY_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE'  # 月销售表单
}

# 字段映射
FIELD_MAPPING = {
    'project_code': 'textField_m9dkdkpg',
    'project_name': 'textField_m9dkdkox', 
    'store_code': 'textField_m9dkdkph',
    'store_name': 'textField_m9dkdkpi',
    'sale_date': 'dateField_m9dkdkoz',
    'online_amount': 'numberField_m9dkdkpe',
    'offline_amount': 'numberField_m9dkdkpc', 
    'total_amount': 'numberField_m9dkdkpa',
    'order_count': 'numberField_m9dkdkpb'
}

# 需要比较的字段
COMPARE_FIELDS = [
    'online_amount',    # 线上销售额
    'offline_amount',   # 线下销售额
    'total_amount',     # 总销售额
    'order_count'       # 订单数量
]

# 常量定义
NUMERIC_FIELDS = [
    'numberField_m9dkdkp8',  # 推荐金额
    'numberField_m9dkdkp9',  # 日结金额
    'numberField_m9dkdkpa',  # 净销售额
    'numberField_m9dkdkpb',  # 总销售笔数
    'numberField_m9dkdkpc',  # 店内净销售额
    'numberField_m9dkdkpd',  # 店内销售笔数
    'numberField_m9dkdkpe',  # 线上净销售额
    'numberField_m9dkdkpf'   # 线上销售笔数
]

class YidaFormDataClient:
    def __init__(self, yida_config: Dict, is_monthly: bool = False):
        self.access_token = token.get_token()
        self.client = self._create_client()
        self.config = yida_config
        self.is_monthly = is_monthly
        self.table_type = "月度" if is_monthly else "日度"

    def _create_client(self):
        """
        初始化宜搭客户端
        
        Returns:
            dingtalkyida_1_0Client: 宜搭客户端实例
        """
        try:
            config = open_api_models.Config(
                protocol='https',
                region_id='central'
            )
            return dingtalkyida_1_0Client(config)
        except Exception as e:
            logging.error(f"创建宜搭API客户端失败: {str(e)}", exc_info=True)
            raise
    def _create_client_2(self):
        """
        初始化宜搭客户端
        
        Returns:
            dingtalkyida_2_0Client: 宜搭客户端实例
        """
        try:
            config = open_api_models.Config(
                protocol='https',
                region_id='central'
            )
            return DingTalkYidaClient(config)
        except Exception as e:
            logging.error(f"创建宜搭API客户端失败: {str(e)}", exc_info=True)
            raise

    def get_form_data(self, page_size: int = 100, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据
        
        Args:
            page_size: 每页数据条数，默认100
            search_condition: 查询条件，可选
            
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        max_retries = 3
        retry_count = 0
        last_error = None
        
        # 记录查询条件，便于调试
        try:
            if search_condition:
                # 从查询条件中安全提取日期范围，用于日志
                for cond in search_condition:
                    if isinstance(cond, dict) and cond.get("key") == FIELD_MAPPING['sale_date'] and cond.get("operator") == "between":
                        values = cond.get("value", [])
                        if len(values) == 2:
                            try:
                                start_time = datetime.fromtimestamp(values[0]/1000).strftime('%Y-%m-%d')
                                end_time = datetime.fromtimestamp(values[1]/1000).strftime('%Y-%m-%d')
                                logging.info(f"查询日期范围: {start_time} 至 {end_time}，使用分页查询，每页 {page_size} 条记录")
                            except Exception as e:
                                logging.error(f"转换查询条件时间戳失败: {values}, {str(e)}")
        except Exception as e:
            logging.error(f"解析查询条件失败: {str(e)}")
        
        while retry_count < max_retries:
            try:
                all_data = []
                current_page = 1
                
                while True:
                    try:
                        headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                        headers.x_acs_dingtalk_access_token = self.access_token
                        
                        request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                            page_number=current_page,
                            form_uuid=self.config['FORM_UUID'],
                            search_condition=json.dumps(search_condition) if search_condition else None,
                            system_token=self.config['SYSTEM_TOKEN'],
                            page_size=page_size,
                            user_id=self.config['USER_ID'],
                            app_type=self.config['APP_TYPE']
                        )
                        
                        # 记录请求参数
                        logging.info(f"Request Parameters - Page {current_page}:")
                        logging.info(f"Headers: {headers}")
                        logging.info(f"Request: {request}")
                        
                        # 添加超时参数到RuntimeOptions，增加超时时间更提高稳定性
                        runtime_options = util_models.RuntimeOptions(
                            read_timeout=60000,  # 读取超时毫秒，增加到60秒
                            connect_timeout=20000  # 连接超时毫秒，增加到20秒
                        )
                        
                        # 记录开始请求时间
                        start_time = time.time()
                        
                        # 发送请求
                        try:
                            result = self.client.search_form_data_second_generation_no_table_field_with_options(
                                request, 
                                headers, 
                                runtime_options
                            )
                            # 记录请求耗时
                            elapsed = round((time.time() - start_time) * 1000)
                            logging.info(f"API请求耗时: {elapsed}ms")
                        except Exception as request_error:
                            # 详细记录请求错误
                            error_msg = str(request_error)
                            logging.error(f"API请求失败: {error_msg}")
                            
                            # 保存失败日期到全局错误日期列表
                            if search_condition:
                                for cond in search_condition:
                                    if isinstance(cond, dict) and cond.get("key") == FIELD_MAPPING['sale_date'] and cond.get("operator") == "between":
                                        values = cond.get("value", [])
                                        if len(values) == 2:
                                            try:
                                                # 遍历时间范围内的每一天
                                                start_dt = datetime.fromtimestamp(values[0]/1000)
                                                end_dt = datetime.fromtimestamp(values[1]/1000)
                                                current_dt = start_dt
                                                while current_dt <= end_dt:
                                                    ERROR_DATES.add(current_dt.strftime('%Y%m%d'))
                                                    current_dt += timedelta(days=1)
                                                logging.warning(f"已将错误日期范围添加到排除列表: {start_dt.strftime('%Y-%m-%d')} 至 {end_dt.strftime('%Y-%m-%d')}")
                                            except Exception as dt_error:
                                                logging.error(f"处理错误日期时出错: {dt_error}")
                            
                            # 根据错误类型处理
                            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                                logging.error("请求超时，将进行重试")
                                raise Exception(f"请求超时: {error_msg}")
                            elif "service unavailable" in error_msg.lower() or "503" in error_msg:
                                logging.error("服务不可用，将等待后重试")
                                raise Exception(f"服务不可用: {error_msg}")
                            else:
                                raise
                        
                        # 记录响应结果
                        if result and result.body:
                            logging.info(f"Response - Page {current_page}")
                            
                            # 检查是否有错误信息
                            has_error = False
                            error_msg = ""
                            error_code = ""
                            
                            # 提取错误代码和消息的通用函数
                            def extract_error_info(response_body):
                                code = ""
                                message = ""
                                # 尝试多种可能的属性名称
                                if hasattr(response_body, 'code'):
                                    code = response_body.code
                                elif hasattr(response_body, 'errorCode'):
                                    code = response_body.errorCode
                                
                                if hasattr(response_body, 'message'):
                                    message = response_body.message
                                elif hasattr(response_body, 'errorMessage'):
                                    message = response_body.errorMessage
                                elif hasattr(response_body, 'msg'):
                                    message = response_body.msg
                                
                                return code, message
                            
                            # 根据响应对象可能有的不同属性进行检查
                            if hasattr(result.body, 'success') and result.body.success == False:
                                has_error = True
                                error_code, error_msg = extract_error_info(result.body)
                                logging.warning(f"API返回失败状态: {error_code}, {error_msg}")
                            elif hasattr(result.body, 'code') and result.body.code not in ["OK", "ok", "200", 200]:
                                has_error = True
                                error_code, error_msg = extract_error_info(result.body)
                                logging.warning(f"API返回错误码: {error_code}, {error_msg}")
                            elif hasattr(result.body, 'errcode') and result.body.errcode != 0:
                                has_error = True
                                error_code = result.body.errcode
                                error_msg = getattr(result.body, 'errmsg', '未知错误')
                                logging.warning(f"API返回错误码: {error_code}, {error_msg}")

                            # 根据状态码和响应体属性检查错误
                            # 注意：API请求成功时状态码为200，不依赖success字段
                            
                            # 首先检查状态码，如果状态码不是200，则认为有错误
                            if hasattr(result, 'status_code') and result.status_code != 200:
                                has_error = True
                                error_code, error_msg = extract_error_info(result.body)
                                logging.warning(f"API返回非200状态码: {result.status_code}, {error_code}, {error_msg}")
                            # 然后检查响应体中的code字段
                            elif hasattr(result.body, 'code') and result.body.code not in ["OK", "ok", "200", 200]:
                                has_error = True
                                error_code, error_msg = extract_error_info(result.body)
                                logging.warning(f"API返回错误码: {error_code}, {error_msg}")
                            # 最后检查errcode字段
                            elif hasattr(result.body, 'errcode') and result.body.errcode != 0:
                                has_error = True
                                error_code = result.body.errcode
                                error_msg = getattr(result.body, 'errmsg', '未知错误')
                                logging.warning(f"API返回错误码: {error_code}, {error_msg}")
                            
                            # 特殊处理token相关错误
                            if has_error and (
                                "token" in str(error_msg).lower() or 
                                "权限" in str(error_msg) or 
                                "认证" in str(error_msg).lower() or
                                error_code in ["InvalidAuthentication", "40014", "40001"]
                            ):
                                # Token可能过期，重新获取
                                logging.info("Token可能已过期，尝试重新获取...")
                                self.access_token = token.get_token()
                                raise Exception("需要刷新Token，将重试请求")
                            
                            # 特殊处理服务不可用错误
                            if has_error and (
                                "ServiceUnavailable" in str(error_code) or 
                                "503" in str(error_code) or
                                "服务不可用" in str(error_msg)
                            ):
                                raise Exception(f"服务暂时不可用: {error_msg}")
                            
                            # 检查数据是否存在
                            if not hasattr(result.body, 'data') or not result.body.data:
                                logging.info(f"第 {current_page} 页没有数据，已到达最后一页")
                                break
                            
                            # 处理正常数据
                            if hasattr(result.body, 'data') and result.body.data:
                                # 提取指定字段
                                count = len(result.body.data)
                                for item in result.body.data:
                                    filtered_data = {
                                        'formInstanceId': item.form_instance_id,
                                        'formData': item.form_data
                                    }
                                    all_data.append(filtered_data)
                                
                                logging.info(f"第 {current_page} 页获取到 {count} 条记录")
                                
                                # 如果获取的数据少于页大小，说明已经是最后一页
                                if count < page_size:
                                    break
                            else:
                                logging.warning(f"第 {current_page} 页数据解析异常，未找到数据字段")
                                break
                        else:
                            logging.warning(f"第 {current_page} 页返回空结果")
                            break
                            
                        current_page += 1
                        time.sleep(0.5)  # 添加较长的延时，避免过于频繁请求
                    except Exception as page_error:
                        page_error_msg = str(page_error)
                        logging.error(f"获取第 {current_page} 页数据时出错: {page_error_msg}")
                        
                        if "token" in page_error_msg.lower() and retry_count < max_retries:
                            # Token可能过期，重新获取
                            self.access_token = token.get_token()
                            logging.info(f"已刷新Token，将重试...")
                            break  # 跳出内层循环，使用新Token重试整个请求
                        elif "ServiceUnavailable" in page_error_msg or "503" in page_error_msg:
                            # 服务不可用，计算更长的等待时间
                            wait_time = min(30, (current_page + 1) * 3)
                            logging.warning(f"服务暂时不可用，等待 {wait_time} 秒后重试...")
                            time.sleep(wait_time)
                            
                            # 如果已经重试多次，考虑返回已获取的部分数据
                            if current_page > 2 and len(all_data) > 0:
                                logging.warning(f"由于服务不稳定，返回已获取的 {len(all_data)} 条记录")
                                return all_data
                            else:
                                # 否则重新抛出异常，交给外层重试
                                raise
                        elif current_page > 1 and len(all_data) > 0:
                            # 如果已经获取了一些数据，就返回已获取的部分，避免完全失败
                            logging.warning(f"由于错误停止获取更多页，已获取 {len(all_data)} 条记录")
                            return all_data
                        else:
                            # 重新抛出异常，交给外层重试机制处理
                            raise
                
                logging.info(f"查询完成，共获取到 {len(all_data)} 条记录")
                return all_data
                
            except Exception as e:
                retry_count += 1
                last_error = e
                err_msg = str(e)
                
                if retry_count < max_retries:
                    # 根据错误类型计算退避时间
                    if "ServiceUnavailable" in err_msg or "503" in err_msg or "服务不可用" in err_msg:
                        wait_time = min(60, (2 ** retry_count) * 5)  # 更长的退避时间，最长60秒
                        logging.warning(f"服务暂时不可用，将等待更长时间: {wait_time}秒")
                    elif "timeout" in err_msg.lower() or "timed out" in err_msg.lower():
                        wait_time = min(30, (2 ** retry_count) * 3)  # 超时错误的退避时间
                        logging.warning(f"请求超时，将等待: {wait_time}秒")
                    else:
                        wait_time = 2 ** retry_count  # 标准指数退避
                    
                    error_msg = f"获取表单数据失败 (尝试 {retry_count}/{max_retries}): {err_msg}"
                    if hasattr(e, 'code') and hasattr(e, 'message'):
                        error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
                    
                    logging.warning(f"{error_msg}，将在 {wait_time} 秒后重试...")
                    
                    # 尝试刷新 token
                    if "token" in err_msg.lower() or "认证" in err_msg.lower() or "权限" in err_msg:
                        try:
                            self.access_token = token.get_token()
                            logging.info("已刷新access_token")
                        except Exception as token_error:
                            logging.error(f"刷新token时出错: {str(token_error)}")
                    
                    time.sleep(wait_time)
                else:
                    logging.error(f"{error_msg}，已达到最大重试次数，不再重试")
        
        # 所有重试都失败
        error_msg = f"在 {max_retries} 次尝试后获取表单数据仍然失败: {str(last_error)}"
        if hasattr(last_error, 'code') and hasattr(last_error, 'message'):
            error_msg += f" (错误码: {last_error.code}, 错误信息: {last_error.message})"
        logging.error(error_msg)
        return []  # 返回空列表而不是抛出异常，这样可以继续处理后续逻辑

    def update_form_data(self, form_instance_id: str, new_data: Dict):
        """更新宜搭表单数据"""
        try:
            client = DingTalkYidaClient(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = yida_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 处理日期字段
            processed_data = new_data.copy()
            if FIELD_MAPPING['sale_date'] in processed_data:
                try:
                    value = processed_data[FIELD_MAPPING['sale_date']]
                    # 检查是否是时间戳格式
                    if isinstance(value, (int, float)) or (isinstance(value, str) and value.isdigit()):
                        # 如果是时间戳，直接使用
                        processed_data[FIELD_MAPPING['sale_date']] = int(value)
                    else:
                        # 根据是否为月表处理日期
                        if self.is_monthly:
                            # 将年月格式转换为该月第一天
                            if len(str(value)) == 8:  # YYYYMMDD格式
                                dt = datetime.strptime(str(value), '%Y%m%d')
                            elif len(str(value)) == 7:  # YYYY-MM格式
                                dt = datetime.strptime(str(value), '%Y-%m')
                            else:
                                raise ValueError(f"不支持的月度日期格式: {value}")
                            # 设置为当月第一天
                            dt = dt.replace(day=1)
                            processed_data[FIELD_MAPPING['sale_date']] = int(dt.timestamp() * 1000)  # 秒转毫秒
                        else:
                            # 日表日期处理
                            if len(str(value)) == 8:  # YYYYMMDD格式
                                dt = datetime.strptime(str(value), '%Y%m%d')
                            elif len(str(value)) == 10:  # YYYY-MM-DD格式
                                dt = datetime.strptime(str(value), '%Y-%m-%d')
                            else:
                                raise ValueError(f"不支持的日度日期格式: {value}")
                            processed_data[FIELD_MAPPING['sale_date']] = int(dt.timestamp() * 1000)  # 秒转毫秒
                    
                    logging.debug(f"更新{self.table_type}数据日期字段转换: {new_data[FIELD_MAPPING['sale_date']]} -> {processed_data[FIELD_MAPPING['sale_date']]}")
                except ValueError as e:
                    logging.error(f"更新{self.table_type}数据日期格式转换错误: {processed_data[FIELD_MAPPING['sale_date']]}, {str(e)}")
                    raise
            
            # 确保所有数值字段都有正确的格式
            for field in NUMERIC_FIELDS:
                if field in processed_data:
                    value = processed_data[field]
                    if value is None or value == '':
                        processed_data[field] = 0
                    elif isinstance(value, (int, float)):
                        processed_data[field] = value
                    else:
                        try:
                            processed_data[field] = float(value)
                        except ValueError:
                            processed_data[field] = 0
            
            request = yida_models.UpdateFormDataRequest()
            request.app_type = self.config['APP_TYPE']
            request.system_token = self.config['SYSTEM_TOKEN']
            request.user_id = self.config['USER_ID']
            request.language = self.config['LANGUAGE']
            request.form_instance_id = form_instance_id
            request.form_uuid = self.config['FORM_UUID']
            request.update_form_data_json = json.dumps(processed_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新{self.table_type}表单数据失败: {str(e)}", exc_info=True)
            raise

    def batch_create_form_data(self, data_list: List[Dict], batch_size: int = 100):
        """批量插入宜搭表单数据"""
        try:
            client = dingtalkyida_1_0Client(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 将数据列表分成多个批次
            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                processed_data_list = []
                
                for item in batch_data:
                    processed_item = {}
                    for key, value in item.items():
                        # 处理日期字段
                        if key == FIELD_MAPPING['sale_date']:
                            try:
                                # 根据是否为月表处理日期
                                if self.is_monthly:
                                    # 将年月格式转换为该月第一天
                                    if isinstance(value, (int, float)) or (isinstance(value, str) and value.isdigit()):
                                        # 如果是时间戳，直接使用
                                        processed_item[key] = int(value)
                                    else:
                                        if len(str(value)) == 8:  # YYYYMMDD格式
                                            dt = datetime.strptime(str(value), '%Y%m%d')
                                        elif len(str(value)) == 7:  # YYYY-MM格式
                                            dt = datetime.strptime(str(value), '%Y-%m')
                                        else:
                                            raise ValueError(f"不支持的月度日期格式: {value}")
                                        # 设置为当月第一天
                                        dt = dt.replace(day=1)
                                        processed_item[key] = int(dt.timestamp() * 1000)  # 秒转毫秒
                                else:
                                    # 日表日期处理
                                    if isinstance(value, (int, float)) or (isinstance(value, str) and value.isdigit()):
                                        # 如果是时间戳，直接使用
                                        processed_item[key] = int(value)
                                    else:
                                        if len(str(value)) == 8:  # YYYYMMDD格式
                                            dt = datetime.strptime(str(value), '%Y%m%d')
                                        elif len(str(value)) == 10:  # YYYY-MM-DD格式
                                            dt = datetime.strptime(str(value), '%Y-%m-%d')
                                        else:
                                            raise ValueError(f"不支持的日度日期格式: {value}")
                                        processed_item[key] = int(dt.timestamp() * 1000)  # 秒转毫秒
                            
                                logging.debug(f"批量插入{self.table_type}日期字段转换: {value} -> {processed_item[key]}")
                            except ValueError as e:
                                logging.error(f"批量插入{self.table_type}日期格式转换错误: {value}, {str(e)}")
                            continue
                        # 处理数值字段
                        elif key.startswith('numberField_'):
                            try:
                                if value is None or value == '':
                                    value = 0
                                # 对于笔数字段，确保是整数
                                if key == FIELD_MAPPING['order_count']:
                                    value = int(float(value)) if value else 0
                                # 对于金额字段，保持原始类型
                                elif key in [FIELD_MAPPING['online_amount'], FIELD_MAPPING['offline_amount'], FIELD_MAPPING['total_amount']]:
                                    if value == 0:
                                        value = 0  # 保持为整数0
                                    elif isinstance(value, (int, float)):
                                        value = value  # 保持原始数值类型
                                # processed_item[f"{key}_value"] = str(value)  # 添加字符串版本
                                processed_item[key] = value  # 保持原始值
                            except ValueError as e:
                                logging.error(f"数值转换错误: {value}, {str(e)}")
                                continue
                        else:
                            processed_item[key] = value
                    processed_data_list.append(processed_item)
                
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in processed_data_list]
                
                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=self.config['FORM_UUID'],
                    app_type=self.config['APP_TYPE'],
                    asynchronous_execution=True,
                    system_token=self.config['SYSTEM_TOKEN'],
                    keep_running_after_exception=True,
                    user_id=self.config['USER_ID'],
                    form_data_json_list=form_data_json_list
                )
                
                try:
                    response = client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())
                    
                    # 记录详细的响应信息
                    logging.info(f"批量插入{self.table_type}响应状态码: {response.status_code}")
                    # logging.info(f"批量插入{self.table_type}响应头: {response.headers}")
                    # logging.info(f"批量插入{self.table_type}响应体: {response.body}")
                    
                    # 检查响应是否成功
                    if response.status_code == 200:
                        if hasattr(response.body, 'result') and response.body.result:
                            logging.info(f"批量插入{self.table_type}表单数据成功，批次 {i//batch_size + 1}，共 {len(batch_data)} 条记录")
                            logging.info(f"成功插入的数据ID: {response.body.result}")
                        else:
                            logging.warning(f"批量插入{self.table_type}响应成功但未返回结果，批次 {i//batch_size + 1}")
                            logging.warning(f"请求数据: {form_data_json_list}")
                    else:
                        logging.error(f"批量插入{self.table_type}表单数据失败，批次 {i//batch_size + 1}: {response.status_code}")
                        logging.error(f"错误响应: {response.body}")
                        logging.error(f"失败数据: {form_data_json_list}")
                        raise Exception(f"批量插入失败，状态码: {response.status_code}")
                        
                except Exception as e:
                    logging.error(f"批量插入{self.table_type}表单数据失败，批次 {i//batch_size + 1}: {str(e)}")
                    logging.error(f"错误数据: {form_data_json_list}")
                    raise
                
                # 添加延时避免请求过于频繁
                time.sleep(1)
        except Exception as e:
            logging.error(f"批量插入{self.table_type}表单数据失败: {str(e)}", exc_info=True)
            raise
        finally:
            pass

class DataSyncManager:
    def __init__(self):
        # 初始化宜搭客户端
        self.yida_client = YidaFormDataClient(YIDA_CONFIG, is_monthly=False)
        self.yida_monthly_client = YidaFormDataClient(YIDA_MONTHLY_CONFIG, is_monthly=True)
        
        # 初始化数据库连接
        try:
            self.conn = pymysql.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor()
            logging.info("MySQL数据库连接成功")
        except pymysql.Error as e:
            logging.error(f"MySQL数据库连接失败: {str(e)}")
            raise
        
        # 初始化数据库表
        self._init_db()
        self._init_monthly_table()
        
        # 数衍接口店铺和项目映射
        self.shop_mapping = {
            "1ETDLFB9DIMQME7Q2OVD93ISAI00189O": "广州VT101维多利广场",
            "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE": "武汉国金天地",
            "1HFLOR99TBR11L6UBHOUTGCK1C001A3F": "广州悦汇城",
            "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV": "悦汇广场·南海",
            "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU": "广州IFC国金天地",
            "1HRIS7255PESAA7AV8LHQQGIH8001KNH": "广州ICC环贸天地",
            "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D": "武汉星汇维港"
        }
        
        # 数衍平台配置
        self.shuyan_config = {
            'appId': 'a5274b7e5d9a41939346c33c2c3443db',
            'appKey': '2c9a5a628e7dab16018f5b055f3d0002',
            'apiSecret': '07F77244AD915AC2BB3EECE8EF7AE4DB',
            'method': 'gogo.open.auto.routing',
            'lowerMethod': 'com.gooagoo.open.api.salequery',
            'url': 'http://api.gooagoo.com/oapi/rest'
        }

    def _init_db(self):
        """初始化MySQL数据库 - 日销售数据表"""
        try:
            # 检查表是否存在
            self.cursor.execute("SHOW TABLES LIKE 'sales_data'")
            if self.cursor.fetchone():
                logging.info("sales_data表已存在，无需创建")
                return
            
            # 创建销售数据表
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    shop_id VARCHAR(255) NOT NULL,
                    shop_entity_id VARCHAR(255) NOT NULL,
                    shop_entity_name VARCHAR(255),
                    sale_time DATE NOT NULL,
                    recommend_amount DECIMAL(15,2) DEFAULT 0.00,
                    daily_bill_amount DECIMAL(15,2) DEFAULT 0.00,
                    amount DECIMAL(15,2) DEFAULT 0.00,
                    count INT DEFAULT 0,
                    instore_amount DECIMAL(15,2) DEFAULT 0.00,
                    instore_count INT DEFAULT 0,
                    online_amount DECIMAL(15,2) DEFAULT 0.00,
                    online_count INT DEFAULT 0,
                    project_name VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_sale_record (shop_id, shop_entity_id, sale_time),
                    INDEX idx_sale_time (sale_time),
                    INDEX idx_shop_entity (shop_entity_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
            ''')
            
            self.conn.commit()
            logging.info("MySQL数据库sales_data表初始化成功")
        except pymysql.Error as e:
            logging.error(f"MySQL数据库初始化失败: {str(e)}")
            raise

    def _init_monthly_table(self):
        """
        初始化月度汇总表
        """
        try:
            # 检查表是否已存在
            self.cursor.execute("SHOW TABLES LIKE 'sales_data_month'")
            if self.cursor.fetchone():
                logging.info("月度汇总表 'sales_data_month' 已存在")
            else:
                # 创建月度汇总表
                logging.info("创建月度汇总表 'sales_data_month'...")
                self.cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sales_data_month (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        shop_id VARCHAR(50) NOT NULL COMMENT '机构ID',
                        shop_entity_id VARCHAR(50) NOT NULL COMMENT '店铺ID',
                        shop_entity_name VARCHAR(100) COMMENT '店铺名称',
                        year SMALLINT NOT NULL COMMENT '年份',
                        month TINYINT NOT NULL COMMENT '月份',
                        recommend_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '推荐金额',
                        daily_bill_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '日结金额',
                        amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '净销售额',
                        count INT DEFAULT 0 COMMENT '总销售笔数',
                        instore_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '店内净销售额',
                        instore_count INT DEFAULT 0 COMMENT '店内销售笔数',
                        online_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '线上净销售额',
                        online_count INT DEFAULT 0 COMMENT '线上销售笔数',
                        project_name VARCHAR(100) COMMENT '项目名称',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                        UNIQUE KEY uniq_monthly_sale (shop_id, shop_entity_id, year, month),
                        INDEX idx_year_month (year, month),
                        INDEX idx_shop (shop_id),
                        INDEX idx_entity (shop_entity_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月度销售汇总数据表';
                ''')
                logging.info("月度汇总表创建成功")
            
            self.conn.commit()
            return True
            
        except pymysql.Error as e:
            logging.error(f"初始化月度汇总表失败: {str(e)}")
            return False
            
    def get_shop_entity_name(self, shop_id: str, shop_entity_id: str) -> str:
        """
        从映射表获取店铺名称
        
        Args:
            shop_id: 数衍平台机构ID
            shop_entity_id: 数衍平台店铺ID
            
        Returns:
            str: 店铺名称，如果不存在则返回空字符串
        """
        try:
            # 确保数据库连接有效
            if not self.conn.open:
                self.conn = pymysql.connect(**DB_CONFIG)
                self.cursor = self.conn.cursor()
                
            # 查询店铺名称
            self.cursor.execute('''
                SELECT shop_entity_name FROM shop_entity_mapping
                WHERE shop_id = %s AND shop_entity_id = %s
                LIMIT 1
            ''', (shop_id, shop_entity_id))
            
            result = self.cursor.fetchone()
            if result and result[0]:
                return result[0]
            return ""
            
        except pymysql.Error as e:
            logging.error(f"查询店铺名称失败: {str(e)}")
            return ""
            
    def update_shop_entity_mapping(self) -> bool:
        """
        获取最近7天的数据衍接口数据，更新店铺映射表
        不过滤全零记录，用于构建完整的shopId、shopEntityId和shopEntityName映射
        
        Returns:
            bool: 是否更新成功
        """
        try:
            # 设置时间范围为最近7天
            end_date = datetime.now() - timedelta(days=1)
            start_date = end_date - timedelta(days=6)
            
            logging.info(f"开始获取最近7天的店铺信息，时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
            
            # 按天查询，避免返回内容过大
            shop_entities = {}
            current_date = start_date
            
            while current_date <= end_date:
                day_str = current_date.strftime("%Y%m%d")
                logging.info(f"查询日期 {day_str} 的店铺信息")
                
                # 构建请求参数 - 仅查询单天数据
                business_data = {
                    "fromDate": day_str,
                    "toDate": day_str,
                    "shopIds": list(self.shop_mapping.keys())
                }
                
                # 调用数衍API
                result = call_sale_query_api(
                    self.shuyan_config['appId'],
                    self.shuyan_config['appKey'],
                    self.shuyan_config['apiSecret'],
                    self.shuyan_config['method'],
                    self.shuyan_config['lowerMethod'],
                    self.shuyan_config['url'],
                    business_data
                )
                
                # 检查响应
                if  result['rescode'] == '0':
                    logging.info(f"获取 {day_str} 店铺信息成功: {{'rescode': '{result['rescode']}', 'resmsg': '{result['resmsg']}'}}")
                    
                    # 处理数据
                    if 'data' in result and isinstance(result['data'], list) and result['data']:
                        logging.info(f"日期 {day_str} 获取到 {len(result['data'])} 条店铺记录")
                        
                        for item in result['data']:
                            shop_id = item.get('shopId', '')
                            shop_entity_id = item.get('shopEntityId', '')
                            shop_entity_name = item.get('shopEntityName', '')
                            
                            if not shop_id or not shop_entity_id:
                                continue
                                
                            # 按照时间排序，每个shop_id+shop_entity_id组合只保留最新的记录
                            key = f"{shop_id}_{shop_entity_id}"
                            sale_time = item.get('saleTime', '')
                            
                            # 如果键不存在或者新的销售日期更大，则更新
                            if key not in shop_entities or sale_time > shop_entities[key]['sale_time']:
                                shop_entities[key] = {
                                    'shop_id': shop_id,
                                    'shop_entity_id': shop_entity_id,
                                    'shop_entity_name': shop_entity_name,
                                    'sale_time': sale_time
                                }
                    else:
                        logging.warning(f"日期 {day_str} 未获取到店铺数据")
                else:
                    logging.error(f"获取 {day_str} 店铺信息失败: {result}")
                
                # 增加0.5秒的延时
                time.sleep(0.5)
                
                # 前进到下一天
                current_date += timedelta(days=1)
            
            if not shop_entities:
                logging.warning("7天内未获取到任何店铺信息数据")
                return False
                
            # 更新映射表
            success_count = 0
            update_count = 0
            insert_count = 0
            
            # 确保数据库连接有效
            if not self.conn.open:
                self.conn = pymysql.connect(**DB_CONFIG)
                self.cursor = self.conn.cursor()
                
            for entity_data in shop_entities.values():
                try:
                    shop_id = entity_data['shop_id']
                    shop_entity_id = entity_data['shop_entity_id']
                    shop_entity_name = entity_data['shop_entity_name']
                    
                    # 检查记录是否存在
                    self.cursor.execute('''
                        SELECT id FROM shop_entity_mapping
                        WHERE shop_id = %s AND shop_entity_id = %s
                    ''', (shop_id, shop_entity_id))
                    
                    existing_record = self.cursor.fetchone()
                    
                    if existing_record:
                        # 更新记录
                        self.cursor.execute('''
                            UPDATE shop_entity_mapping
                            SET shop_entity_name = %s, updated_at = NOW()
                            WHERE shop_id = %s AND shop_entity_id = %s
                        ''', (shop_entity_name, shop_id, shop_entity_id))
                        update_count += 1
                    else:
                        # 插入新记录
                        self.cursor.execute('''
                            INSERT INTO shop_entity_mapping
                            (shop_id, shop_entity_id, shop_entity_name)
                            VALUES (%s, %s, %s)
                        ''', (shop_id, shop_entity_id, shop_entity_name))
                        insert_count += 1
                        
                    success_count += 1
                except Exception as e:
                    logging.error(f"更新店铺映射失败: {str(e)}, 数据: {entity_data}")
                    continue
                    
            # 提交事务
            self.conn.commit()
            
            logging.info(f"店铺映射表更新完成，总计: {len(shop_entities)}条，成功: {success_count}条 (更新: {update_count}条, 插入: {insert_count}条)")
            return True
            
        except Exception as e:
            logging.error(f"更新店铺映射表失败: {str(e)}")
            return False

    def _save_to_mysql(self, data_list: List[Dict]):
        """将日销售数据保存到MySQL数据库"""
        try:
            # 确保数据库连接有效
            if not self.conn.open:
                self.conn = pymysql.connect(**DB_CONFIG)
                self.cursor = self.conn.cursor()
            
            # 初始化统计计数器
            total_records = len(data_list)
            insert_count = 0
            update_count = 0
            skip_count = 0
            error_count = 0
            
            logging.info(f"开始保存数据到MySQL数据库，共 {total_records} 条记录待处理")
            
            # 定义需要比较的金额和笔数字段及其映射关系
            field_mapping = {
                'recommendAmount': 'recommend_amount',
                'dailyBillAmount': 'daily_bill_amount',
                'amount': 'amount',
                'count': 'count',
                'instoreAmount': 'instore_amount',
                'instoreCount': 'instore_count',
                'onlineAmount': 'online_amount',
                'onlineCount': 'online_count'
            }
            
            for item in data_list:
                try:
                    # 将日期字符串转换为MySQL的DATE格式 (YYYY-MM-DD)
                    sale_time = item.get('saleTime', '')
                    if sale_time:
                        try:
                            dt = datetime.strptime(sale_time, '%Y%m%d')
                            sale_time = dt.strftime('%Y-%m-%d')
                        except ValueError as e:
                            logging.error(f"日期格式转换错误1: {sale_time}, {str(e)}")
                            error_count += 1
                            continue
                    
                    # 优先使用映射表中的店铺名称
                    shop_id = item.get('shopId', '')
                    shop_entity_id = item.get('shopEntityId', '')
                    shop_entity_name = self.get_shop_entity_name(shop_id, shop_entity_id)
                    
                    # 如果映射表中没有，则使用数衍接口返回的名称
                    if not shop_entity_name:
                        shop_entity_name = item.get('shopEntityName', '')
                    
                    # 检查记录是否存在
                    self.cursor.execute('''
                        SELECT id, recommend_amount, daily_bill_amount, amount, count,
                               instore_amount, instore_count, online_amount, online_count,
                               shop_entity_name
                        FROM sales_data
                        WHERE shop_id = %s AND shop_entity_id = %s AND sale_time = %s
                    ''', (
                        item.get('shopId', ''),
                        item.get('shopEntityId', ''),
                        sale_time
                    ))
                    
                    existing_record = self.cursor.fetchone()
                    
                    if existing_record:
                        # 记录存在，检查是否需要更新
                        record_id = existing_record[0]
                        need_update = False
                        update_fields = []
                        update_values = []
                        changed_fields = []
                        
                        # 检查所有字段
                        for i, (source_field, db_field) in enumerate(field_mapping.items(), 1):
                            new_value = item.get(source_field, 0)
                            old_value = existing_record[i]
                            
                            # 根据字段类型进行转换和比较
                            if source_field.endswith('Amount'):
                                new_value = float(new_value)
                                old_value = float(old_value)
                                if abs(new_value - old_value) > 0.01:  # 考虑浮点数精度
                                    need_update = True
                                    update_fields.append(f"{db_field} = %s")
                                    update_values.append(new_value)
                                    changed_fields.append(f"{db_field}: {old_value} -> {new_value}")
                            else:  # 笔数字段
                                new_value = int(float(new_value))
                                old_value = int(old_value)
                                if new_value != old_value:
                                    need_update = True
                                    update_fields.append(f"{db_field} = %s")
                                    update_values.append(new_value)
                                    changed_fields.append(f"{db_field}: {old_value} -> {new_value}")
                        
                        # 检查店铺名称是否需要更新
                        old_shop_entity_name = existing_record[9]  # shop_entity_name是查询的第10个字段
                        if shop_entity_name != old_shop_entity_name and shop_entity_name:
                            need_update = True
                            update_fields.append("shop_entity_name = %s")
                            update_values.append(shop_entity_name)
                            changed_fields.append(f"shop_entity_name: {old_shop_entity_name} -> {shop_entity_name}")
                        
                        if need_update:
                            # 更新记录
                            update_sql = f'''
                                UPDATE sales_data
                                SET {', '.join(update_fields)}
                                WHERE id = %s
                            '''
                            update_values.append(record_id)
                            self.cursor.execute(update_sql, update_values)
                            update_count += 1
                            logging.info(f"更新记录成功: shop_id={item.get('shopId')}, shop_entity_id={item.get('shopEntityId')}, sale_time={sale_time}")
                            logging.info(f"变更字段: {', '.join(changed_fields)}")
                        else:
                            skip_count += 1
                            logging.debug(f"记录无需更新: shop_id={item.get('shopId')}, shop_entity_id={item.get('shopEntityId')}, sale_time={sale_time}")
                    else:
                        # 记录不存在，插入新记录
                        self.cursor.execute('''
                            INSERT INTO sales_data (
                                shop_id, shop_entity_id, shop_entity_name, sale_time,
                                recommend_amount, daily_bill_amount, amount, count,
                                instore_amount, instore_count, online_amount, online_count,
                                project_name
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ''', (
                            item.get('shopId', ''),
                            item.get('shopEntityId', ''),
                            shop_entity_name,
                            sale_time,
                            float(item.get('recommendAmount', 0)),
                            float(item.get('dailyBillAmount', 0)),
                            float(item.get('amount', 0)),
                            int(float(item.get('count', 0))),
                            float(item.get('instoreAmount', 0)),
                            int(float(item.get('instoreCount', 0))),
                            float(item.get('onlineAmount', 0)),
                            int(float(item.get('onlineCount', 0))),
                            self.shop_mapping.get(item.get('shopId', ''), '')
                        ))
                        insert_count += 1
                        # logging.info(f"插入新记录成功: shop_id={item.get('shopId')}, shop_entity_id={item.get('shopEntityId')}, sale_time={sale_time}")
                
                except Exception as e:
                    error_count += 1
                    logging.error(f"处理数据项失败: {item}, {str(e)}")
                    continue
            
            self.conn.commit()
            
            # 输出统计信息
            logging.info(f"MySQL数据保存完成，统计信息：")
            logging.info(f"- 总记录数: {total_records}")
            logging.info(f"- 成功插入: {insert_count}")
            logging.info(f"- 成功更新: {update_count}")
            logging.info(f"- 无需更新: {skip_count}")
            logging.info(f"- 处理失败: {error_count}")
            
        except pymysql.Error as e:
            logging.error(f"保存数据到MySQL数据库失败: {str(e)}")
            raise

    def get_shuyan_data(self, start_date=None, end_date=None) -> List[Dict]:
        """
        获取数衍平台数据并保存到MySQL数据库
        :param start_date: 开始日期，格式：YYYYMMDD，默认为30天前
        :param end_date: 结束日期，格式：YYYYMMDD，默认为昨天
        """
        try:
            # 首先更新店铺映射表
            self.update_shop_entity_mapping()
            
            if end_date is None:
                end_date = datetime.now() - timedelta(days=1)
            else:
                try:
                    end_date = datetime.strptime(end_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"结束日期格式错误: {end_date}, {str(e)}")
                    return []
            
            if start_date is None:
                start_date = end_date - timedelta(days=60)  # 默认获取60天数据
            else:
                try:
                    start_date = datetime.strptime(start_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"开始日期格式错误: {start_date}, {str(e)}")
                    return []
            
            all_data = []
            current_date = start_date
            logging.info(f"查询数衍平台数据，时间段为: {start_date.strftime('%Y-%m-%d')}, {end_date.strftime('%Y-%m-%d')}")
            # 将时间段分成多个1天的批次
            while current_date <= end_date:
                batch_end_date = min(current_date + timedelta(days=1), end_date)
                
                business_data = {
                    "fromDate": current_date.strftime("%Y%m%d"),
                    "toDate": batch_end_date.strftime("%Y%m%d"),
                    "shopIds": list(self.shop_mapping.keys())
                }
                
                logging.info(f"正在获取{business_data['fromDate']}至{business_data['toDate']}的数据")
                
                try:
                    result = call_sale_query_api(
                        self.shuyan_config['appId'],
                        self.shuyan_config['appKey'],
                        self.shuyan_config['apiSecret'],
                        self.shuyan_config['method'],
                        self.shuyan_config['lowerMethod'],
                        self.shuyan_config['url'],
                        business_data
                    )
                    
                    # 优化日志输出，避免显示过大的数据内容
                    if 'rescode' in result and 'resmsg' in result:
                        logging.info(f"Response: {{'rescode': '{result['rescode']}', 'resmsg': '{result['resmsg']}'}}")
                    else:
                        logging.error(f"获取{business_data['fromDate']}至{business_data['toDate']}的数据失败: 响应格式错误")
                        continue
                    
                    if 'data' in result and isinstance(result['data'], list):
                        # 过滤掉所有金额和笔数都为0的记录
                        filtered_data = []
                        for item in result['data']:
                            # 检查所有金额和笔数字段
                            amount_fields = ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']
                            count_fields = ['count', 'instoreCount', 'onlineCount']
                            
                            # 检查是否有任何金额或笔数不为0
                            has_non_zero_amount = any(float(item.get(field, 0)) > 0 for field in amount_fields)
                            has_non_zero_count = any(float(item.get(field, 0)) > 0 for field in count_fields)
                            
                            if has_non_zero_amount or has_non_zero_count:
                                filtered_data.append(item)
                            else:
                                logging.debug(f"过滤掉全零记录: {item}")
                        
                        all_data.extend(filtered_data)
                        logging.info(f"过滤后保留 {len(filtered_data)} 条记录")
                    else:
                        logging.error(f"获取{business_data['fromDate']}至{business_data['toDate']}的数据失败: {result}")
                    
                    # 添加延时避免请求过于频繁
                    time.sleep(2)
                    current_date = batch_end_date + timedelta(days=1)
                except Exception as e:
                    logging.error(f"调用数衍平台API失败: {str(e)}")
                    continue
            
            # 保存数据到MySQL数据库
            if all_data:
                try:
                    self._save_to_mysql(all_data)
                except Exception as e:
                    logging.error(f"保存数据到MySQL数据库失败: {str(e)}")
            
            # # 生成Excel文件
            # if all_data:
            #     try:
            #         # 准备数据
            #         excel_data = []
            #         for item in all_data:
            #             try:
            #                 shop_id = item.get('shopId', '')
            #                 shop_entity_id = item.get('shopEntityId', '')
            #                 project_name = self.shop_mapping.get(shop_id, '')
                            
            #                 # 优先使用映射表中的店铺名称
            #                 shop_entity_name = self.get_shop_entity_name(shop_id, shop_entity_id)
            #                 # 如果映射表中没有，则使用数衍接口返回的名称
            #                 if not shop_entity_name:
            #                     shop_entity_name = item.get('shopEntityName', '')
                            
            #                 if not project_name:
            #                     logging.warning(f"警告：机构ID {shop_id} 未找到对应的项目名称")
                            
            #                 excel_data.append({
            #                     '项目名称': project_name,
            #                     '数衍平台机构ID': shop_id,
            #                     '数衍平台店铺ID': shop_entity_id,
            #                     '店铺名称': shop_entity_name,
            #                     '销售日期': item.get('saleTime', ''),
            #                     '推荐金额': round(float(item.get('recommendAmount', 0)), 2),
            #                     '日结金额': round(float(item.get('dailyBillAmount', 0)), 2),
            #                     '净销售额': round(float(item.get('amount', 0)), 2),
            #                     '总销售笔数': int(float(item.get('count', 0))),
            #                     '店内净销售额': round(float(item.get('instoreAmount', 0)), 2),
            #                     '店内销售笔数': int(float(item.get('instoreCount', 0))),
            #                     '线上净销售额': round(float(item.get('onlineAmount', 0)), 2),
            #                     '线上销售笔数': int(float(item.get('onlineCount', 0)))
            #                 })
            #             except Exception as e:
            #                 logging.error(f"处理数据项失败: {item}, {str(e)}")
            #                 continue
                    
            #         # 创建DataFrame
            #         df = pd.DataFrame(excel_data)
                    
            #         # 生成文件名
            #         today = datetime.now().strftime('%Y%m%d')
            #         file_path = Path(f'logs/数衍平台数据导出_{today}.xlsx')
                    
            #         # 保存Excel文件
            #         df.to_excel(file_path, index=False, engine='openpyxl')
            #         logging.info(f"数据已保存到Excel文件: {file_path}")
            #     except Exception as e:
            #         logging.error(f"生成Excel文件失败: {str(e)}")
            
            return all_data
        except Exception as e:
            logging.error(f"获取数衍平台数据失败: {str(e)}")
            return []
            
    def update_monthly_summary(self, start_date=None, end_date=None):
        """更新月度汇总数据
        
        Args:
            start_date: 开始日期，格式：YYYYMMDD，若不提供则默认为12个月前的第一天
            end_date: 结束日期，格式：YYYYMMDD，若不提供则默认为当月最后一天
        """
        try:           
            # 确保数据库连接有效
            if not self.conn.open:
                self.conn = pymysql.connect(**DB_CONFIG)
                self.cursor = self.conn.cursor()
            
            # 清空指定日期范围内的汇总数据
            self.cursor.execute('''
                DELETE FROM sales_data_month 
                WHERE 1=1
            ''')
            logging.info(f"月度数据表清空完成")
            
            # 执行月度汇总查询
            self.cursor.execute('''
                INSERT INTO sales_data_month 
                    (shop_id, shop_entity_id, shop_entity_name, year, month, 
                     recommend_amount, daily_bill_amount, amount, count, 
                     instore_amount, instore_count, online_amount, online_count, 
                     project_name, created_at)
                SELECT 
                    shop_id, 
                    shop_entity_id, 
                    MAX(shop_entity_name) as shop_entity_name, 
                    YEAR(sale_time) AS year, 
                    MONTH(sale_time) AS month, 
                    SUM(recommend_amount) AS recommend_amount, 
                    SUM(daily_bill_amount) AS daily_bill_amount, 
                    SUM(amount) AS amount, 
                    SUM(count) AS count, 
                    SUM(instore_amount) AS instore_amount, 
                    SUM(instore_count) AS instore_count, 
                    SUM(online_amount) AS online_amount, 
                    SUM(online_count) AS online_count, 
                    MAX(project_name) as project_name, 
                    NOW()
                FROM sales_data
                GROUP BY shop_id, shop_entity_id, YEAR(sale_time), MONTH(sale_time)
                ORDER BY shop_id, year, month
            ''')
            
            # 获取处理的记录数
            rows_affected = self.cursor.rowcount
            
            self.conn.commit()
            
            logging.info(f"月度汇总数据更新完成，处理了 {rows_affected} 条汇总记录")
            return rows_affected
        
        except pymysql.Error as e:
            logging.error(f"更新月度汇总数据失败: {str(e)}")
            raise
            
    def get_daily_form_data(self, start_date=None, end_date=None, segment_days=7):
        """获取宜搭每日表单数据
        
        Args:
            start_date: 开始日期，格式：YYYYMMDD，默认为60天前
            end_date: 结束日期，格式：YYYYMMDD，默认为昨天
            segment_days: 每个分段的天数，默认为7天
            
        Returns:
            List[Dict]: 表单数据列表
        """
        try:
            if end_date is None:
                # 设置结束时间为昨天23:59:59
                end_date = datetime.now() - timedelta(days=1)
                end_date = end_date.replace(hour=23, minute=59, second=59)
            elif isinstance(end_date, str):
                # 只有当end_date是字符串时才进行转换
                try:
                    end_date = datetime.strptime(end_date, '%Y%m%d')
                    end_date = end_date.replace(hour=23, minute=59, second=59)
                except ValueError as e:
                    logging.error(f"结束日期格式错误: {end_date}, {str(e)}")
                    return []
            
            if start_date is None:
                # 设置开始时间为60天前的00:00:00
                start_date = end_date - timedelta(days=60)
                start_date = start_date.replace(hour=0, minute=0, second=0)
            elif isinstance(start_date, str):
                # 只有当start_date是字符串时才进行转换
                try:
                    start_date = datetime.strptime(start_date, '%Y%m%d')
                    start_date = start_date.replace(hour=0, minute=0, second=0)
                except ValueError as e:
                    logging.error(f"开始日期格式错误: {start_date}, {str(e)}")
                    return []

            logging.info(f"开始获取宜搭每日表单数据，总时间范围: {start_date.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 分段查询实现
            all_data = []
            current_start = start_date
            segment_count = 0
            error_count = 0
            max_errors = 3  # 最大连续错误次数
            consecutive_errors = 0
            
            while current_start <= end_date:
                # 计算当前分段结束日期
                current_end = min(current_start + timedelta(days=1), end_date)
                # 强制归零时分秒微秒，保证毫秒整点
                current_start_zero = current_start.replace(hour=0, minute=0, second=0, microsecond=0)
                current_end_zero = current_end.replace(hour=0, minute=0, second=0, microsecond=0)
                # 结束时间为次日0点减1毫秒
                end_timestamp = int((current_end_zero + timedelta(days=1)).timestamp() * 1000) - 1
                start_timestamp = int(current_start_zero.timestamp() * 1000)
                segment_count += 1
                logging.info(f"查询分段 {segment_count}: {current_start_zero.strftime('%Y-%m-%d')} 至 {current_end_zero.strftime('%Y-%m-%d')}")
                # 构建日期筛选条件
                search_condition = [{
                    "key": FIELD_MAPPING['sale_date'],
                    "value": [start_timestamp, end_timestamp],
                    "type": "DOUBLE",
                    "operator": "between",
                    "componentName": "DateField"
                }]
                try:
                    # 查询当前分段数据
                    segment_data = self.yida_client.get_form_data(search_condition=search_condition)
                    if segment_data:
                        logging.info(f"分段 {segment_count} 查询成功，获取到 {len(segment_data)} 条记录")
                        all_data.extend(segment_data)
                        consecutive_errors = 0  # 重置连续错误计数
                    else:
                        logging.warning(f"分段 {segment_count} 查询返回空数据")
                        # 尝试进一步细分时间段
                        if segment_days > 1 and (current_end - current_start).days > 1:
                            logging.info(f"尝试将分段 {segment_count} 再细分为单日查询")
                            sub_start = current_start_zero
                            sub_end = current_end_zero
                            while sub_start <= sub_end:
                                # 单日查询，强制归零
                                day_start = sub_start.replace(hour=0, minute=0, second=0, microsecond=0)
                                day_end = (day_start + timedelta(days=1)) - timedelta(milliseconds=1)
                                sub_condition = [{
                                    "key": FIELD_MAPPING['sale_date'],
                                    "value": [int(day_start.timestamp() * 1000), int(day_end.timestamp() * 1000)],
                                    "type": "DOUBLE",
                                    "operator": "between",
                                    "componentName": "DateField"
                                }]
                                try:
                                    sub_data = self.yida_client.get_form_data(search_condition=sub_condition)
                                    if sub_data:
                                        logging.info(f"单日查询成功 {day_start.strftime('%Y-%m-%d')}，获取到 {len(sub_data)} 条记录")
                                        all_data.extend(sub_data)
                                    else:
                                        logging.warning(f"单日查询返回空数据: {day_start.strftime('%Y-%m-%d')}")
                                except Exception as e:
                                    logging.error(f"单日查询失败 {day_start.strftime('%Y-%m-%d')}: {str(e)}")
                                    ERROR_DATES.add(day_start.strftime('%Y%m%d'))
                                    logging.warning(f"已将失败日期添加到排除列表: {day_start.strftime('%Y-%m-%d')}")
                                sub_start = day_start + timedelta(days=1)
                                time.sleep(0.5)  # 单日查询添加较短的延时
                except Exception as e:
                    error_count += 1
                    consecutive_errors += 1
                    logging.error(f"分段 {segment_count} 查询失败 ({current_start_zero.strftime('%Y-%m-%d')} 至 {current_end_zero.strftime('%Y-%m-%d')}): {str(e)}")
                    
                    # 记录失败的日期范围到全局错误日期列表
                    try:
                        # 遍历时间范围内的每一天
                        dt = current_start_zero
                        while dt <= current_end_zero:
                            ERROR_DATES.add(dt.strftime('%Y%m%d'))
                            dt += timedelta(days=1)
                        logging.warning(f"已将失败日期范围添加到排除列表: {current_start_zero.strftime('%Y-%m-%d')} 至 {current_end_zero.strftime('%Y-%m-%d')}")
                    except Exception as dt_error:
                        logging.error(f"处理失败日期时出错: {dt_error}")
                    
                    # 如果连续多次错误，尝试减小分段大小
                    if consecutive_errors >= max_errors and segment_days > 1:
                        old_segment_days = segment_days
                        segment_days = max(1, segment_days // 2)
                        logging.warning(f"连续 {consecutive_errors} 次查询失败，减小分段大小: {old_segment_days} -> {segment_days} 天")
                        consecutive_errors = 0  # 重置连续错误计数
                    
                # 更新开始日期为下一个分段
                current_start = current_end + timedelta(days=1)
                
                # 添加延时避免请求过于频繁
                time.sleep(1)
            
            logging.info(f"宜搭每日表单数据查询完成，共 {segment_count} 个分段，成功获取 {len(all_data)} 条记录，失败 {error_count} 次")
            return all_data
            
        except Exception as e:
            logging.error(f"获取宜搭每日表单数据失败: {str(e)}")
            return []

    def get_monthly_form_data(self, start_date=None, end_date=None, segment_months=2):
        """获取宜搭月度表单数据
        
        Args:
            start_date: 开始日期，格式：YYYYMMDD，默认为1年前
            end_date: 结束日期，格式：YYYYMMDD，默认为昨天
            segment_months: 每个分段的月数，默认为2个月
            
        Returns:
            List[Dict]: 表单数据列表
        """
        try:
            if end_date is None:
                end_date = datetime.now() - timedelta(days=1)
            else:
                try:
                    end_date = datetime.strptime(end_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"结束日期格式错误: {end_date}, {str(e)}")
                    return []
            
            if start_date is None:
                start_date = end_date - timedelta(days=365)  # 默认获取一年的数据
            else:
                try:
                    start_date = datetime.strptime(start_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"开始日期格式错误: {start_date}, {str(e)}")
                    return []
            
            # 转换为月首日
            start_date = datetime(start_date.year, start_date.month, 1)
            # 转换为月末日
            if end_date.month == 12:
                end_date = datetime(end_date.year, 12, 31)
            else:
                end_date = datetime(end_date.year, end_date.month + 1, 1) - timedelta(days=1)
            
            logging.info(f"开始获取宜搭月度表单数据，总时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
            
            # 分段查询实现
            all_data = []
            current_start = start_date
            segment_count = 0
            error_count = 0
            max_errors = 3  # 最大连续错误次数
            consecutive_errors = 0
            
            while current_start <= end_date:
                # 计算当前分段结束日期 (前进segment_months个月)
                segment_count += 1
                month = current_start.month - 1 + segment_months  # 减1是因为月份从0开始计算更方便
                year = current_start.year + month // 12  # 整除，获取年份增量
                month = month % 12 + 1  # 取余，获取新的月份值 (1-12)
                
                if month == 12:
                    current_end = datetime(year, month, 31)
                else:
                    current_end = datetime(year, month + 1, 1) - timedelta(days=1)
                
                # 确保不超过总结束日期
                current_end = min(current_end, end_date)
                
                logging.info(f"查询月度分段 {segment_count}: {current_start.strftime('%Y-%m-%d')} 至 {current_end.strftime('%Y-%m-%d')}")
                
                # 构建日期筛选条件
                search_condition = [{
                    "key": FIELD_MAPPING['sale_date'],
                    "value": [int(current_start.timestamp() * 1000), int(current_end.timestamp() * 1000)],
                    "type": "DOUBLE",
                    "operator": "between",
                    "componentName": "DateField"
                }]
                
                try:
                    # 查询当前分段数据
                    segment_data = self.yida_monthly_client.get_form_data(search_condition=search_condition)
                    if segment_data:
                        logging.info(f"月度分段 {segment_count} 查询成功，获取到 {len(segment_data)} 条记录")
                        all_data.extend(segment_data)
                        consecutive_errors = 0  # 重置连续错误计数
                    else:
                        logging.warning(f"月度分段 {segment_count} 查询返回空数据")
                        
                        # 尝试按单月查询
                        if segment_months > 1 and (current_end.year > current_start.year or current_end.month > current_start.month):
                            logging.info(f"尝试将月度分段 {segment_count} 再细分为单月查询")
                            sub_start = current_start
                            while sub_start <= current_end:
                                # 计算当月月末
                                if sub_start.month == 12:
                                    sub_end = datetime(sub_start.year, 12, 31)
                                else:
                                    sub_end = datetime(sub_start.year, sub_start.month + 1, 1) - timedelta(days=1)
                                
                                # 确保不超过当前分段结束日期
                                sub_end = min(sub_end, current_end)
                                
                                try:
                                    sub_condition = [{
                                        "key": FIELD_MAPPING['sale_date'],
                                        "value": [int(sub_start.timestamp() * 1000), int(sub_end.timestamp() * 1000)],
                                        "type": "DOUBLE",
                                        "operator": "between",
                                        "componentName": "DateField"
                                    }]
                                    sub_data = self.yida_monthly_client.get_form_data(search_condition=sub_condition)
                                    if sub_data:
                                        logging.info(f"单月查询成功 {sub_start.strftime('%Y-%m')}，获取到 {len(sub_data)} 条记录")
                                        all_data.extend(sub_data)
                                    else:
                                        logging.warning(f"单月查询返回空数据: {sub_start.strftime('%Y-%m')}")
                                except Exception as e:
                                    logging.error(f"单月查询失败 {sub_start.strftime('%Y-%m')}: {str(e)}")
                                    # 记录失败的月份到全局错误日期列表（记录当月第一天）
                                    ERROR_DATES.add(sub_start.replace(day=1).strftime('%Y%m%d'))
                                    logging.warning(f"已将失败月份添加到排除列表: {sub_start.strftime('%Y-%m')}")
                                
                                # 前进到下个月第一天
                                if sub_start.month == 12:
                                    sub_start = datetime(sub_start.year + 1, 1, 1)
                                else:
                                    sub_start = datetime(sub_start.year, sub_start.month + 1, 1)
                                
                                time.sleep(0.5)  # 单月查询添加较短的延时
                except Exception as e:
                    error_count += 1
                    consecutive_errors += 1
                    logging.error(f"月度分段 {segment_count} 查询失败 ({current_start.strftime('%Y-%m-%d')} 至 {current_end.strftime('%Y-%m-%d')}): {str(e)}")
                    
                    # 记录失败的月份到全局错误日期列表
                    try:
                        # 对于月度数据，我们只记录每个月的第一天
                        dt = current_start
                        while dt <= current_end:
                            # 记录当月第一天
                            first_day = dt.replace(day=1)
                            ERROR_DATES.add(first_day.strftime('%Y%m%d'))
                            # 前进到下个月
                            if dt.month == 12:
                                dt = datetime(dt.year + 1, 1, 1)
                            else:
                                dt = datetime(dt.year, dt.month + 1, 1)
                        logging.warning(f"已将失败月份添加到排除列表: {current_start.strftime('%Y-%m')} 至 {current_end.strftime('%Y-%m')}")
                    except Exception as dt_error:
                        logging.error(f"处理失败月份时出错: {dt_error}")
                    
                    # 如果连续多次错误，尝试减小分段大小
                    if consecutive_errors >= max_errors and segment_months > 1:
                        old_segment_months = segment_months
                        segment_months = 1  # 减小到每次查询1个月
                        logging.warning(f"连续 {consecutive_errors} 次查询失败，减小分段大小: {old_segment_months} -> {segment_months} 个月")
                        consecutive_errors = 0  # 重置连续错误计数
                
                # 更新开始日期为下一个分段
                if current_end.month == 12:
                    current_start = datetime(current_end.year + 1, 1, 1)
                else:
                    current_start = datetime(current_end.year, current_end.month + 1, 1)
                
                # 添加延时避免请求过于频繁
                time.sleep(1)
            
            logging.info(f"宜搭月度表单数据查询完成，共 {segment_count} 个分段，成功获取 {len(all_data)} 条记录，失败 {error_count} 次")
            return all_data
            
        except Exception as e:
            logging.error(f"获取宜搭月度表单数据失败: {str(e)}")
            return []

    def update_daily_form_data(self, instance_id: str, update_data: Dict):
        """更新宜搭每日表单数据"""
        try:
            if not instance_id or not update_data:
                logging.warning("表单实例ID或更新数据为空")
                return
                
            return self.yida_client.update_form_data(instance_id, update_data)
        except Exception as e:
            logging.error(f"更新宜搭每日表单数据失败: {str(e)}")
            return None

    def update_monthly_form_data(self, instance_id: str, update_data: Dict):
        """更新宜搭月度表单数据"""
        try:
            if not instance_id or not update_data:
                logging.warning("月度表单实例ID或更新数据为空")
                return
            
            # 处理日期字段 - 使用月度客户端方法
            return self.yida_monthly_client.update_form_data(instance_id, update_data)
        except Exception as e:
            logging.error(f"更新宜搭月度表单数据失败: {str(e)}")
            return None

    def batch_insert_daily_form_data(self, data_list: List[Dict], batch_size: int = 100):
        """批量插入宜搭每日表单数据
        
        Args:
            data_list: 待插入的数据列表
            batch_size: 每批次数据条数，默认50条
        
        Returns:
            bool: 是否插入成功
        """
        try:
            if not data_list:
                logging.info("没有需要插入的数据")
                return True
            
            total_count = len(data_list)
            success_count = 0
            error_count = 0
            current_batch_size = batch_size
            min_batch_size = 10  # 最小批量大小
            
            # 处理数据
            processed_data_list = []
            for item in data_list:
                # 首先转换Decimal类型
                item = convert_decimal_to_float(item)

                processed_item = {}
                for key, value in item.items():
                    if key == FIELD_MAPPING['sale_date']:
                        try:
                            # 首先检查是否是时间戳格式
                            if isinstance(value, (int, float)) or (isinstance(value, str) and value.isdigit()):
                                # 如果是时间戳，直接使用
                                processed_item[key] = int(value)
                            else:
                                # 如果不是时间戳，尝试解析为日期字符串
                                dt = datetime.strptime(str(value), '%Y%m%d')
                                processed_item[key] = int(dt.timestamp() * 1000)
                        except ValueError as e:
                            logging.error(f"日期格式转换错误2: {value}, {str(e)}")
                            continue
                    elif key.startswith('numberField_'):
                        if value is None or value == '':
                            value = 0
                        if key == FIELD_MAPPING['order_count']:
                            value = int(value) if value else 0
                        # processed_item[f"{key}_value"] = str(value)
                        processed_item[key] = value
                    else:
                        processed_item[key] = value
                processed_data_list.append(processed_item)
            
            # 分批次插入数据
            for i in range(0, len(processed_data_list), current_batch_size):
                batch_data = processed_data_list[i:i + current_batch_size]
                batch_insert_success = False
                retry_count = 0
                max_retries = 3
                
                while not batch_insert_success and retry_count < max_retries:
                    try:
                        # 转换为JSON格式
                        form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in batch_data]
                        
                        logging.info(f"正在批量插入每日数据，批次 {i//current_batch_size + 1}/{(len(processed_data_list)-1)//current_batch_size + 1}，共 {len(batch_data)} 条记录")
                        
                        client = dingtalkyida_1_0Client(open_api_models.Config(
                            protocol='https',
                            region_id='central'
                        ))
                        
                        headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
                        headers.x_acs_dingtalk_access_token = self.yida_client.access_token
                        
                        request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                            no_execute_expression=True,
                            form_uuid=self.yida_client.config['FORM_UUID'],
                            app_type=self.yida_client.config['APP_TYPE'],
                            asynchronous_execution=True,
                            system_token=self.yida_client.config['SYSTEM_TOKEN'],
                            keep_running_after_exception=True,
                            user_id=self.yida_client.config['USER_ID'],
                            form_data_json_list=form_data_json_list
                        )
                        
                        # 添加超时参数
                        runtime_options = util_models.RuntimeOptions(
                            read_timeout=60000,  # 读取超时毫秒
                            connect_timeout=20000  # 连接超时毫秒
                        )
                        
                        response = client.batch_save_form_data_with_options(request, headers, runtime_options)
                        
                        # 检查响应
                        if response and response.status_code == 200 and hasattr(response.body, 'result') and response.body.result:
                            logging.info(f"批量插入每日数据成功，批次 {i//current_batch_size + 1}，{len(batch_data)} 条记录")
                            success_count += len(batch_data)
                            batch_insert_success = True
                        else:
                            error_message = getattr(response.body, 'message', '未知错误') if hasattr(response, 'body') else '响应无body'
                            raise Exception(f"批量插入响应异常: {error_message}")
                            
                    except Exception as e:
                        retry_count += 1
                        err_msg = str(e)
                        
                        if retry_count < max_retries:
                            # 计算退避时间 - 对于服务不可用错误增加更长的等待时间
                            if "ServiceUnavailable" in err_msg or "503" in err_msg:
                                wait_time = min(30, (2 ** retry_count) * 2)  # 更长的退避时间，最长30秒
                                logging.warning(f"服务暂时不可用，将等待更长时间: {wait_time}秒")
                            else:
                                wait_time = 2 ** retry_count  # 标准指数退避
                            
                            # 如果是Token错误，尝试刷新Token
                            if "token" in err_msg.lower() or "auth" in err_msg.lower() or "认证" in err_msg.lower() or "权限" in err_msg.lower():
                                try:
                                    self.yida_client.access_token = token.get_token()  # 移除 force_refresh 参数
                                    logging.info("已刷新Token，准备重试")
                                except Exception as token_error:
                                    logging.error(f"刷新Token失败: {str(token_error)}")
                            
                            # 如果批量太大，尝试减小批量
                            if "too large" in err_msg.lower() or "超过" in err_msg or "超时" in err_msg or "timeout" in err_msg:
                                old_batch_size = current_batch_size
                                current_batch_size = max(current_batch_size // 2, min_batch_size)
                                if current_batch_size < old_batch_size:
                                    logging.warning(f"批量过大，减小批量大小: {old_batch_size} -> {current_batch_size}")
                                    # 重新计算当前批次数据
                                    batch_data = processed_data_list[i:i + current_batch_size]
                                    logging.info(f"使用新的批量大小 {current_batch_size}，当前批次包含 {len(batch_data)} 条记录")
                            
                            logging.warning(f"批量插入失败，将在 {retry_count * 2} 秒后重试 (尝试 {retry_count}/{max_retries}): {err_msg}")
                            time.sleep(retry_count * 2)  # 指数退避
                        else:
                            logging.error(f"批量插入失败达到最大重试次数，跳过当前批次: {err_msg}")
                            error_count += len(batch_data)
                
                # 添加延时避免请求过于频繁
                time.sleep(3)
            
            # 输出统计信息
            logging.info(f"批量插入每日数据完成: 总计 {total_count} 条，成功 {success_count} 条，失败 {error_count} 条")
            return error_count == 0
        
        except Exception as e:
            logging.error(f"批量插入宜搭每日表单数据过程中发生异常: {str(e)}", exc_info=True)
            return False

    def batch_insert_monthly_form_data(self, data_list: List[Dict], batch_size: int = 100):
        """批量插入宜搭月度表单数据
        
        Args:
            data_list: 待插入的数据列表
            batch_size: 每批次数据条数，默认50条
        
        Returns:
            bool: 是否插入成功
        """
        try:
            if not data_list:
                logging.info("没有需要插入的月度数据")
                return True
            
            total_count = len(data_list)
            success_count = 0
            error_count = 0
            current_batch_size = batch_size
            min_batch_size = 10  # 最小批量大小
            
            # 处理数据
            processed_data_list = []
            for item in data_list:
                # 首先转换Decimal类型
                item = convert_decimal_to_float(item)

                processed_item = {}
                for key, value in item.items():
                    if key == FIELD_MAPPING['sale_date']:
                        try:
                             # 如果已经是时间戳格式（13位整数），直接使用
                            if isinstance(value, int) and len(str(value)) == 13:
                                processed_item[key] = value
                                continue
                            # 确保月度数据的日期是月首日
                            if isinstance(value, str):
                                if len(value) == 7:  # YYYY-MM 格式
                                    dt = datetime.strptime(value, '%Y-%m')
                                elif len(value) == 8:  # YYYYMMDD 格式
                                    dt = datetime.strptime(value, '%Y%m%d')
                                else:
                                    raise ValueError(f"不支持的月度日期格式: {value}")
                            elif isinstance(value, datetime):
                                dt = value
                            else:
                                raise ValueError(f"不支持的月度日期类型: {type(value)}")

                            # 设置为当月的第一天
                            dt = dt.replace(day=1)
                            processed_item[key] = int(dt.timestamp() * 1000)
                        except ValueError as e:
                            logging.error(f"月度日期格式转换错误: {value}, {str(e)}")
                            continue
                    elif key.startswith('numberField_'):
                        if value is None or value == '':
                            value = 0
                        if key == FIELD_MAPPING['order_count']:
                            value = int(value) if value else 0
                        # processed_item[f"{key}_value"] = str(value)
                        processed_item[key] = value
                    else:
                        processed_item[key] = value
                processed_data_list.append(processed_item)
            
            # 分批次插入数据
            for i in range(0, len(processed_data_list), current_batch_size):
                batch_data = processed_data_list[i:i + current_batch_size]
                batch_insert_success = False
                retry_count = 0
                max_retries = 3
                logging.info(f"开始批量插入月度数据，批次数据: {len(batch_data)} 条记录")
                while not batch_insert_success and retry_count < max_retries:
                    try:
                        # 转换为JSON格式
                        form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in batch_data]
                        logging.debug(f"计划插入的数据: {form_data_json_list[:2]}...")  # 只显示前2条数据避免日志过长
                        logging.info(f"正在批量插入月度数据，批次 {i//current_batch_size + 1}/{(len(processed_data_list)-1)//current_batch_size + 1}，共 {len(batch_data)} 条记录")
                        
                        client = dingtalkyida_1_0Client(open_api_models.Config(
                            protocol='https',
                            region_id='central'
                        ))
                        
                        headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
                        headers.x_acs_dingtalk_access_token = self.yida_monthly_client.access_token
                        
                        request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                            no_execute_expression=True,
                            form_uuid=self.yida_monthly_client.config['FORM_UUID'],
                            app_type=self.yida_monthly_client.config['APP_TYPE'],
                            asynchronous_execution=True,
                            system_token=self.yida_monthly_client.config['SYSTEM_TOKEN'],
                            keep_running_after_exception=True,
                            user_id=self.yida_monthly_client.config['USER_ID'],
                            form_data_json_list=form_data_json_list
                        )
                        
                        # 添加超时参数
                        runtime_options = util_models.RuntimeOptions(
                            read_timeout=60000,  # 读取超时毫秒
                            connect_timeout=20000  # 连接超时毫秒
                        )
                        
                        response = client.batch_save_form_data_with_options(request, headers, runtime_options)
                        
                        # 检查响应
                        if response and response.status_code == 200 and hasattr(response.body, 'result') and response.body.result:
                            logging.info(f"批量插入月度数据成功，批次 {i//current_batch_size + 1}，{len(batch_data)} 条记录")
                            success_count += len(batch_data)
                            batch_insert_success = True
                        else:
                            error_message = getattr(response.body, 'message', '未知错误') if hasattr(response, 'body') else '响应无body'
                            raise Exception(f"批量插入响应异常: {error_message}")
                            
                    except Exception as e:
                        retry_count += 1
                        err_msg = str(e)
                        
                        if retry_count < max_retries:
                            # 计算退避时间 - 对于服务不可用错误增加更长的等待时间
                            if "ServiceUnavailable" in err_msg or "503" in err_msg:
                                wait_time = min(30, (2 ** retry_count) * 2)  # 更长的退避时间，最长30秒
                                logging.warning(f"服务暂时不可用，将等待更长时间: {wait_time}秒")
                            else:
                                wait_time = 2 ** retry_count  # 标准指数退避
                            
                            # 如果是Token错误，尝试刷新Token
                            if "token" in err_msg.lower() or "auth" in err_msg.lower() or "认证" in err_msg.lower() or "权限" in err_msg.lower():
                                try:
                                    self.yida_monthly_client.access_token = token.get_token()  # 移除 force_refresh 参数
                                    logging.info("已刷新Token，准备重试")
                                except Exception as token_error:
                                    logging.error(f"刷新Token失败: {str(token_error)}")
                            
                            # 如果批量太大，尝试减小批量
                            if "too large" in err_msg.lower() or "超过" in err_msg or "超时" in err_msg or "timeout" in err_msg:
                                old_batch_size = current_batch_size
                                current_batch_size = max(current_batch_size // 2, min_batch_size)
                                if current_batch_size < old_batch_size:
                                    logging.warning(f"批量过大，减小批量大小: {old_batch_size} -> {current_batch_size}")
                                    # 重新计算当前批次数据
                                    batch_data = processed_data_list[i:i + current_batch_size]
                                    logging.info(f"使用新的批量大小 {current_batch_size}，当前批次包含 {len(batch_data)} 条记录")
                            
                            logging.warning(f"批量插入月度数据失败，将在 {retry_count * 2} 秒后重试 (尝试 {retry_count}/{max_retries}): {err_msg}")
                            time.sleep(retry_count * 2)  # 指数退避
                        else:
                            logging.error(f"批量插入月度数据失败达到最大重试次数，跳过当前批次: {err_msg}")
                            error_count += len(batch_data)
                
                # 添加延时避免请求过于频繁
                time.sleep(3)
            
            # 输出统计信息
            logging.info(f"批量插入月度数据完成: 总计 {total_count} 条，成功 {success_count} 条，失败 {error_count} 条")
            return error_count == 0
            
        except Exception as e:
            logging.error(f"批量插入宜搭月度表单数据失败: {str(e)}", exc_info=True)
            return False

    def create_key(self, data: Dict) -> str:
        shop_id = data.get('shopId')
        shop_entity_id = data.get('shopEntityId')
        sale_time = data.get('saleTime')
        
        if not all([shop_id, shop_entity_id, sale_time]):
            raise KeyError(f"缺少创建键值所需的必要字段: {data}")
        
        return f"{shop_id}_{shop_entity_id}_{sale_time}"
        
    def create_monthly_key(self, data: Dict) -> str:
        # 从日期中获取年月
        sale_time = data.get('saleTime', '')
        if not sale_time:
            return f"{data.get('shopId', '')}_{data.get('shopEntityId', '')}_{data.get('year', '')}-{data.get('month', '')}"
            
        try:
            dt = datetime.strptime(str(sale_time), '%Y%m%d')
            year_month = dt.strftime('%Y-%m')
            return f"{data.get('shopId')}_{data.get('shopEntityId')}_{year_month}"
        except ValueError:
            # 如果日期格式异常，使用原始数据
            return f"{data.get('shopId')}_{data.get('shopEntityId')}_{sale_time}"
            
    def sync_data(self, start_date=None, end_date=None):
        """
        数据同步主流程
        :param start_date: 开始日期，格式：YYYYMMDD，默认为60天前
        :param end_date: 结束日期，格式：YYYYMMDD，默认为昨天
        """
        try:
            logging.info("开始综合数据同步流程...")
            
            # 记录错误日期列表状态
            if ERROR_DATES:
                logging.info(f"当前错误日期列表中有 {len(ERROR_DATES)} 个日期，这些日期的数据对比将被跳过")
                logging.debug(f"错误日期列表: {sorted(list(ERROR_DATES))}")
            else:
                logging.info("当前错误日期列表为空")
                
            # 1. 从数衍平台获取日销售数据并保存到MySQL
            logging.info("正在获取数衍平台日销售数据...")
            shuyan_data = self.get_shuyan_data(start_date, end_date)
            if not shuyan_data:
                logging.error("未获取到数衍平台数据，同步流程终止")
                return
            logging.info(f"成功获取数衍平台数据，共 {len(shuyan_data)} 条记录")
            
            # 2. 更新MySQL的月度汇总表
            logging.info("正在更新MySQL月度汇总数据...")
            try:
                monthly_rows = self.update_monthly_summary(start_date, end_date)
                logging.info(f"成功更新月度汇总数据，共 {monthly_rows} 条记录")
            except Exception as e:
                logging.error(f"更新月度汇总数据失败: {str(e)}", exc_info=True)
                logging.warning("将继续执行其他步骤...")
            
            # 3. 获取宜搭日销售表单数据
            logging.info("正在获取宜搭日销售表单数据...")
            yida_daily_data = []  # 初始化为空列表，确保即使后续获取失败也有默认值
            try:
                yida_daily_data = self.get_daily_form_data(start_date, end_date)
                logging.info(f"成功获取宜搭日销售表单数据，共 {len(yida_daily_data)} 条记录")
                # 检查获取的数据结构
                if yida_daily_data and len(yida_daily_data) > 0:
                    sample_item = yida_daily_data[0]
                    logging.info(f"宜搭日销售数据项结构示例: {list(sample_item.keys())}")
                    if 'form_data' in sample_item:
                        logging.info(f"form_data字段结构示例: {list(sample_item['form_data'].keys())}")
            except Exception as e:
                logging.error(f"获取宜搭日销售表单数据失败: {str(e)}", exc_info=True)
                logging.warning("尝试继续处理其他数据...")
                # 确保yida_daily_data是空列表而不是None
                yida_daily_data = []
            
            # 4. 对比并同步日销售数据到宜搭表单
            logging.info("开始对比和同步日销售数据...")
            
            # 创建宜搭日销售数据索引
            yida_daily_dict = {}
            for item in yida_daily_data:
                standard_form_data = convert_yida_to_standard(item['formData'])
                key = self.create_key(standard_form_data)
                yida_daily_dict[key] = {
                    'form_instance_id': item['formInstanceId'],
                    'form_data': standard_form_data
                }
                logging.info(f"成功创建宜搭日销售数据索引: {key}, 表单实例ID: {item['formInstanceId']}")   
                # try:
                #     # 检查item是否包含必要的字段
                #     if 'formData' not in item or 'formInstanceId' not in item:
                #         logging.error(f"宜搭数据项缺少必要字段: {item}")
                #         continue
                    
                #     # 确保formData是字典类型
                #     if not isinstance(item['formData'], dict):
                #         logging.error(f"宜搭数据项的formData不是字典类型: {type(item['formData'])}")
                #         # 尝试转换JSON字符串为字典
                #         if isinstance(item['formData'], str):
                #             try:
                #                 item['formData'] = json.loads(item['formData'])
                #             except json.JSONDecodeError:
                #                 logging.error(f"无法将formData解析为JSON: {item['formData']}")
                #                 continue
                #         else:
                #             continue
                    
                #     # 转换为标准格式
                #     try:
                #         standard_form_data = convert_yida_to_standard(item['formData'])
                #         key = self.create_key(standard_form_data)
                #         yida_daily_dict[key] = {
                #             'form_instance_id': item['formInstanceId'],
                #             'form_data': standard_form_data
                #         }
                #     except KeyError as ke:
                #         logging.error(f"生成键值时缺少必要字段: {ke}, 数据项: {item['formData']}")
                #         continue
                #     except Exception as e:
                #         logging.error(f"处理宜搭日销售数据项失败: {e}, 数据项: {item}")
                #         continue
                # except Exception as e:
                #     logging.error(f"处理宜搭日销售数据项时发生异常: {str(e)}, 数据项: {item}")
                #     continue
            
            logging.info(f"成功创建宜搭日销售数据索引，共 {len(yida_daily_dict)} 条记录")
            
            # 对比并同步日销售数据
            daily_update_count = 0
            daily_insert_count = 0
            daily_error_count = 0
            daily_skip_count = 0
            daily_insert_list = []
            
            # 定义需要比较的字段
            compare_fields = [
                'recommendAmount',    # 推荐金额
                'dailyBillAmount',    # 日结金额
                'amount',             # 净销售额
                'count',              # 总销售笔数
                'instoreAmount',      # 店内净销售额
                'instoreCount',       # 店内销售笔数
                'onlineAmount',       # 线上净销售额
                'onlineCount',        # 线上销售笔数
                'shopEntityName'      # 数衍平台店铺名称
            ]
            
            logging.info(f"开始处理数衍数据，共 {len(shuyan_data)} 条记录")
            
            for idx, shuyan_item in enumerate(shuyan_data):
                try:
                    # 优先从MySQL获取店铺名称，如果为空再使用shuyan_data中的值
                    shop_id = shuyan_item.get('shopId', '')
                    shop_entity_id = shuyan_item.get('shopEntityId', '')
                    shop_entity_name = self.get_shop_entity_name(shop_id, shop_entity_id)
                    
                    # 如果MySQL中没有，则使用数衍接口返回的名称
                    if not shop_entity_name:
                        shop_entity_name = shuyan_item.get('shopEntityName', '')
                    
                    # 更新shuyan_item中的shopEntityName
                    shuyan_item['shopEntityName'] = shop_entity_name
                    
                    # 检查该记录的日期是否在错误日期列表中
                    sale_time = shuyan_item.get('saleTime', '')
                    if sale_time in ERROR_DATES:
                        logging.info(f"跳过错误日期数据: {sale_time}, shopId: {shop_id}, shopEntityId: {shop_entity_id}")
                        daily_skip_count += 1
                        continue
                    
                    # 生成键值
                    key = self.create_key(shuyan_item)
                    logging.info(f"生成键值: {key}")
                    if key in yida_daily_dict:
                        logging.info(f"找到宜搭日销售数据索引: {key}")
                        yida_item = yida_daily_dict[key]['form_data']
                        # 逐个字段比较
                        need_update = False
                        changed_fields = []
                        
                        for field in compare_fields:
                            shuyan_value = shuyan_item.get(field, 0)
                            yida_value = yida_item.get(field, 0)
                            
                            # 对于笔数字段，确保比较整数
                            if field in ['count', 'instoreCount', 'onlineCount']:
                                shuyan_value = int(float(shuyan_value)) if shuyan_value else 0
                                yida_value = int(float(yida_value)) if yida_value else 0
                            
                            if shuyan_value != yida_value:
                                need_update = True
                                changed_fields.append({
                                    'field': field,
                                    'old_value': yida_value,
                                    'new_value': shuyan_value
                                })
                        
                        if need_update:
                            try:
                                yida_format_data = convert_standard_to_yida(shuyan_item, self.shop_mapping)
                                form_instance_id = yida_daily_dict[key]['form_instance_id']
                                
                                if not form_instance_id or not form_instance_id.startswith('FINST-'):
                                    logging.error(f"无效的表单实例ID: {form_instance_id}")
                                    daily_error_count += 1
                                    continue
                                
                                if not all(key in yida_format_data for key in ['textField_m9dkdkpg', 'textField_m9dkdkph', 'dateField_m9dkdkoz']):
                                    logging.error(f"转换后的数据缺少必要字段: {yida_format_data}")
                                    daily_error_count += 1
                                    continue
                                
                                self.update_daily_form_data(form_instance_id, yida_format_data)
                                daily_update_count += 1
                                logging.info(f"更新日销售记录成功: {key}, 变更字段: {changed_fields}")
                            except Exception as e:
                                logging.error(f"更新日销售记录时发生错误: {str(e)}")
                                daily_error_count += 1
                        else:
                            daily_skip_count += 1
                            if idx < 5:  # 只记录前5条的详细数据
                                logging.debug(f"跳过无需更新的记录: {key}")
                    else:
                        logging.info(f"未找到宜搭日销售数据索引: {key}")
                        try:
                            yida_format_data = convert_standard_to_yida(shuyan_item, self.shop_mapping)
                            
                            # 验证转换后的数据是否包含必要字段
                            if not all(key in yida_format_data for key in ['textField_m9dkdkpg', 'textField_m9dkdkph', 'dateField_m9dkdkoz']):
                                logging.error(f"转换后的新数据缺少必要字段: {yida_format_data}")
                                daily_error_count += 1
                                continue
                            
                            daily_insert_list.append(yida_format_data)
                            daily_insert_count += 1
                            
                            if idx < 3:  # 只记录前3条的详细数据
                                logging.debug(f"准备插入新记录: {yida_format_data}")
                        except Exception as e:
                            logging.error(f"转换日销售数据格式失败: {str(e)}")
                            daily_error_count += 1
                except Exception as e:
                    logging.error(f"处理日销售数据项失败: {str(e)}, 数据项: {shuyan_item}")
                    daily_error_count += 1
                    continue
            
            # 批量插入新的日销售数据
            if daily_insert_list:
                try:
                    self.batch_insert_daily_form_data(daily_insert_list)
                    logging.info(f"批量插入日销售数据完成，共 {len(daily_insert_list)} 条记录")
                except Exception as e:
                    logging.error(f"批量插入日销售数据失败: {str(e)}", exc_info=True)
                    daily_error_count += len(daily_insert_list)
            
            logging.info(f"日销售数据同步完成！更新: {daily_update_count} 条，插入: {daily_insert_count} 条，错误: {daily_error_count} 条，跳过: {daily_skip_count} 条")
            
            # 5. 获取宜搭月销售表单数据
            logging.info("正在获取宜搭月销售表单数据...")
            yida_monthly_data = []  # 初始化为空列表，确保即使后续获取失败也有默认值
            try:
                yida_monthly_data = self.get_monthly_form_data(start_date, end_date)
                logging.info(f"成功获取宜搭月销售表单数据，共 {len(yida_monthly_data)} 条记录")
                # 检查月度数据结构
                if yida_monthly_data and len(yida_monthly_data) > 0:
                    sample_item = yida_monthly_data[0]
                    logging.info(f"宜搭月销售数据项结构示例: {list(sample_item.keys())}")
                    if 'form_data' in sample_item:
                        logging.info(f"月度form_data字段结构示例: {list(sample_item['form_data'].keys())}")
            except Exception as e:
                logging.error(f"获取宜搭月销售表单数据失败: {str(e)}", exc_info=True)
                logging.warning("将尝试继续处理其他数据...")
                yida_monthly_data = []  # 确保是空列表而不是None
            
            # 6. 从MySQL获取最新的月度汇总数据
            logging.info("正在从MySQL获取月度汇总数据...")
            try:
                # 确保数据库连接有效
                if not self.conn.open:
                    self.conn = pymysql.connect(**DB_CONFIG)
                    self.cursor = self.conn.cursor()
                
                # 解析日期范围
                if start_date:
                    start_dt = datetime.strptime(start_date, '%Y%m%d')
                else:
                    # 默认处理过去12个月的数据
                    today = datetime.now()
                    # 获取当月最后一天
                    if today.month == 12:
                        end_dt = datetime(today.year, 12, 31)
                    else:
                        next_month = today.month + 1
                        next_month_first_day = datetime(today.year, next_month, 1)
                        end_dt = next_month_first_day - timedelta(days=1)
                    
                    # 默认处理前12个月数据
                    start_month = end_dt.month - 11 if end_dt.month > 11 else end_dt.month + 1
                    start_year = end_dt.year if end_dt.month > 11 else end_dt.year - 1
                    start_dt = datetime(start_year, start_month, 1)
                
                if end_date:
                    end_dt = datetime.strptime(end_date, '%Y%m%d')
                else:
                    # 默认处理到当月
                    today = datetime.now()
                    # 获取当月最后一天
                    if today.month == 12:
                        end_dt = datetime(today.year, 12, 31)
                    else:
                        next_month = today.month + 1
                        next_month_first_day = datetime(today.year, next_month, 1)
                        end_dt = next_month_first_day - timedelta(days=1)
                
                # 查询月度汇总数据
                self.cursor.execute('''
                    SELECT 
                        shop_id, shop_entity_id, shop_entity_name, year, month,
                        recommend_amount, daily_bill_amount, amount, count,
                        instore_amount, instore_count, online_amount, online_count,
                        project_name
                    FROM sales_data_month
                    WHERE (year > %s OR (year = %s AND month >= %s)) 
                      AND (year < %s OR (year = %s AND month <= %s))
                ''', (
                    start_dt.year, start_dt.year, start_dt.month,
                    end_dt.year, end_dt.year, end_dt.month
                ))
                
                sqlite_monthly_data = self.cursor.fetchall()
                
                if not sqlite_monthly_data:
                    logging.warning("未获取到MySQL月度汇总数据")
                else:
                    logging.info(f"成功获取MySQL月度汇总数据，共 {len(sqlite_monthly_data)} 条记录")
                    
                    # 可以在这里将月度数据导出到Excel（修改为不使用pandas.read_sql_query）
                    try:
                        today = datetime.now().strftime('%Y%m%d')
                        file_path = f'logs/数衍平台月度数据_{today}.xlsx'
                        
                        # 创建数据框架
                        headers = ['项目名称', '数衍平台机构ID', '数衍平台店铺ID', '店铺名称', '销售月份', 
                                  '推荐金额', '日结金额', '净销售额', '总销售笔数', '店内净销售额', 
                                  '店内销售笔数', '线上净销售额', '线上销售笔数']
                        
                        # 处理数据
                        excel_data = []
                        for row in sqlite_monthly_data:
                            year = row[3]
                            month = row[4]
                            month_str = f"{year}-{month:02d}"
                            
                            excel_data.append([
                                row[13] or self.shop_mapping.get(row[0], ''),  # 项目名称
                                row[0],  # 数衍平台机构ID
                                row[1],  # 数衍平台店铺ID
                                row[2],  # 店铺名称
                                month_str,  # 销售月份
                                row[5],  # 推荐金额
                                row[6],  # 日结金额
                                row[7],  # 净销售额
                                row[8],  # 总销售笔数
                                row[9],  # 店内净销售额
                                row[10],  # 店内销售笔数
                                row[11],  # 线上净销售额
                                row[12]   # 线上销售笔数
                            ])
                        
                        # 创建DataFrame
                        df = pd.DataFrame(excel_data, columns=headers)
                        
                        # 导出到Excel
                        df.to_excel(file_path, index=False, engine='openpyxl')
                        logging.info(f"月度汇总数据已导出到Excel文件: {file_path}")
                    except Exception as e:
                        logging.error(f"导出月度汇总数据到Excel失败: {str(e)}")
                    
                    # 7. 将MySQL月度汇总数据转换为标准格式
                    sqlite_monthly_list = []
                    for row in sqlite_monthly_data:
                        # 转换为标准格式
                        year = row[3]
                        month = row[4]
                        # 生成月份字符串 YYYY-MM
                        month_str = f"{year}-{month:02d}"
                        
                        # 创建数据项并转换Decimal类型
                        data_item = {
                            'shopId': row[0],
                            'shopEntityId': row[1],
                            'shopEntityName': row[2],
                            'saleTime': month_str,  # 使用年月格式
                            'recommendAmount': row[5],
                            'dailyBillAmount': row[6],
                            'amount': row[7],
                            'count': row[8],
                            'instoreAmount': row[9],
                            'instoreCount': row[10],
                            'onlineAmount': row[11],
                            'onlineCount': row[12],
                            'projectName': row[13] or self.shop_mapping.get(row[0], '')
                        }
                        # 转换Decimal类型为float
                        data_item = convert_decimal_to_float(data_item)
                        sqlite_monthly_list.append(data_item)
                    
                    # 8. 创建宜搭月销售数据索引
                    yida_monthly_dict = {}
                    for item in yida_monthly_data:
                        try:
                            # 检查item是否包含必要的字段
                            if 'formData' not in item or 'formInstanceId' not in item:
                                logging.error(f"宜搭月度数据项缺少必要字段: {item}")
                                continue
                            
                            # 确保formData是字典类型
                            if not isinstance(item['formData'], dict):
                                logging.error(f"宜搭月度数据项的formData不是字典类型: {type(item['formData'])}")
                                # 尝试转换JSON字符串为字典
                                if isinstance(item['formData'], str):
                                    try:
                                        item['formData'] = json.loads(item['formData'])
                                    except json.JSONDecodeError:
                                        logging.error(f"无法将formData解析为JSON: {item['formData']}")
                                        continue
                                else:
                                    continue
                            
                            # 转换为标准格式
                            try:
                                standard_form_data = convert_yida_to_standard(item['formData'])
                                key = self.create_monthly_key(standard_form_data)
                                yida_monthly_dict[key] = {
                                    'form_instance_id': item['formInstanceId'],
                                    'form_data': standard_form_data
                                }
                            except KeyError as ke:
                                logging.error(f"生成键值时缺少必要字段: {ke}, 数据项: {item['formData']}")
                                continue
                            except Exception as e:
                                logging.error(f"处理宜搭月销售数据项失败: {e}, 数据项: {item}")
                                continue
                        except Exception as e:
                            logging.error(f"处理宜搭月销售数据项时发生异常: {str(e)}, 数据项: {item}")
                            continue
                    
                    logging.info(f"成功创建宜搭月销售数据索引，共 {len(yida_monthly_dict)} 条记录")
                    
                    # 9. 对比并同步月销售数据
                    monthly_update_count = 0
                    monthly_insert_count = 0
                    monthly_error_count = 0
                    monthly_skip_count = 0
                    monthly_insert_list = []
                    
                    for idx, sqlite_item in enumerate(sqlite_monthly_list):                           
                        try:    
                            # 优先从MySQL获取店铺名称，如果为空再使用sqlite_item中的值
                            shop_id = sqlite_item.get('shopId', '')
                            shop_entity_id = sqlite_item.get('shopEntityId', '')
                            shop_entity_name = self.get_shop_entity_name(shop_id, shop_entity_id)
                            
                            # 如果MySQL中没有，则使用原始的名称
                            if not shop_entity_name:
                                shop_entity_name = sqlite_item.get('shopEntityName', '')
                            
                            # 更新sqlite_item中的shopEntityName
                            sqlite_item['shopEntityName'] = shop_entity_name
                            
                            # 检查该记录的日期是否在错误日期列表中（对于月度数据，我们只检查月的第一天）
                            sale_time = sqlite_item.get('saleTime', '')
                            try:
                                if len(sale_time) >= 7:  # 确保日期格式正确
                                    # 将月度数据转换为当月第一天的格式，以便与错误日期列表比较
                                    if '-' in sale_time:  # YYYY-MM 或 YYYY-MM-DD 格式
                                        dt = datetime.strptime(sale_time.split('-')[0] + '-' + sale_time.split('-')[1], '%Y-%m')
                                    else:  # YYYYMM 或 YYYYMMDD 格式
                                        dt = datetime.strptime(sale_time[:6], '%Y%m')
                                    first_day = dt.replace(day=1).strftime('%Y%m%d')
                                    if first_day in ERROR_DATES:
                                        logging.info(f"跳过错误月份数据: {sale_time}, shopId: {shop_id}, shopEntityId: {shop_entity_id}")
                                        monthly_skip_count += 1
                                        continue
                            except Exception as e:
                                logging.error(f"检查月度错误日期时出错: {e}, 日期: {sale_time}")
                            
                            key = self.create_monthly_key(sqlite_item)
                            
                            if key in yida_monthly_dict:
                                yida_item = yida_monthly_dict[key]['form_data']
                                # 逐个字段比较
                                need_update = False
                                changed_fields = []
                                
                                for field in compare_fields:
                                    sqlite_value = sqlite_item.get(field, 0)
                                    yida_value = yida_item.get(field, 0)
                                    
                                    # 对于笔数字段，确保比较整数
                                    if field in ['count', 'instoreCount', 'onlineCount']:
                                        sqlite_value = int(float(sqlite_value)) if sqlite_value else 0
                                        yida_value = int(float(yida_value)) if yida_value else 0
                                    # 对于金额字段，确保比较浮点数
                                    elif field in ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']:
                                        sqlite_value = float(sqlite_value) if sqlite_value else 0.0
                                        yida_value = float(yida_value) if yida_value else 0.0
                                        # 考虑浮点数精度问题，使用近似比较
                                        if abs(sqlite_value - yida_value) < 0.01:
                                            continue
                                    
                                    if sqlite_value != yida_value:
                                        need_update = True
                                        changed_fields.append({
                                            'field': field,
                                            'old_value': yida_value,
                                            'new_value': sqlite_value
                                        })
                                
                                if need_update:
                                    try:
                                        # 转换为宜搭月销售表单格式，设置is_monthly=True
                                        yida_format_data = convert_standard_to_yida(sqlite_item, self.shop_mapping, is_monthly=True)
                                        form_instance_id = yida_monthly_dict[key]['form_instance_id']
                                        
                                        if not form_instance_id or not form_instance_id.startswith('FINST-'):
                                            logging.error(f"无效的月销售表单实例ID: {form_instance_id}")
                                            monthly_error_count += 1
                                            continue
                                        
                                        if not all(key in yida_format_data for key in ['textField_m9dkdkpg', 'textField_m9dkdkph', 'dateField_m9dkdkoz']):
                                            logging.error(f"转换后的月销售数据缺少必要字段: {yida_format_data}")
                                            monthly_error_count += 1
                                            continue
                                        
                                        self.update_monthly_form_data(form_instance_id, yida_format_data)
                                        monthly_update_count += 1
                                        logging.info(f"更新月销售记录成功: {key}, 变更字段: {changed_fields}")
                                    except Exception as e:
                                        logging.error(f"更新月销售记录时发生错误: {str(e)}, 数据: {key}")
                                        monthly_error_count += 1
                                else:
                                    monthly_skip_count += 1
                                    if idx < 5:  # 只记录前5条的详细数据
                                        logging.debug(f"跳过无需更新的月度记录: {key}")
                            else:
                                try:
                                    # 转换为宜搭月销售表单格式，设置is_monthly=True
                                    yida_format_data = convert_standard_to_yida(sqlite_item, self.shop_mapping, is_monthly=True)
                                    
                                    # 验证转换后的数据是否包含必要字段
                                    if not all(key in yida_format_data for key in ['textField_m9dkdkpg', 'textField_m9dkdkph', 'dateField_m9dkdkoz']):
                                        logging.error(f"转换后的新月度数据缺少必要字段: {yida_format_data}")
                                        monthly_error_count += 1
                                        continue
                                    
                                    monthly_insert_list.append(yida_format_data)
                                    monthly_insert_count += 1
                                    
                                    if idx < 3:  # 只记录前3条的详细数据
                                        logging.debug(f"准备插入新的月度记录: {yida_format_data}")
                                except Exception as e:
                                    logging.error(f"转换月销售数据格式失败: {str(e)}, 数据: {sqlite_item}")
                                    monthly_error_count += 1
                        except Exception as e:
                            logging.error(f"处理月销售数据项失败: {str(e)}, 数据: {sqlite_item}")
                            monthly_error_count += 1
                            continue
                    
                    # 批量插入新的月销售数据
                    if monthly_insert_list:
                        try:
                            self.batch_insert_monthly_form_data(monthly_insert_list)
                            logging.info(f"批量插入月销售数据完成，共 {len(monthly_insert_list)} 条记录")
                        except Exception as e:
                            logging.error(f"批量插入月销售数据失败: {str(e)}", exc_info=True)
                            monthly_error_count += len(monthly_insert_list)
                    
                    logging.info(f"月销售数据同步完成！更新: {monthly_update_count} 条，插入: {monthly_insert_count} 条，错误: {monthly_error_count} 条，跳过: {monthly_skip_count} 条")
                
            except Exception as e:
                logging.error(f"同步月销售数据失败: {str(e)}", exc_info=True)
            
            logging.info("综合数据同步流程完成！")
        except Exception as e:
            logging.error(f"综合数据同步流程失败: {str(e)}", exc_info=True)
            raise

def convert_decimal_to_float(value):
    """将Decimal类型转换为float类型，用于JSON序列化

    Args:
        value: 可能包含Decimal类型的值

    Returns:
        转换后的值
    """
    if isinstance(value, Decimal):
        return float(value)
    elif isinstance(value, dict):
        return {k: convert_decimal_to_float(v) for k, v in value.items()}
    elif isinstance(value, list):
        return [convert_decimal_to_float(item) for item in value]
    else:
        return value

def convert_yida_to_standard(yida_data: Dict) -> Dict:
    """将宜搭数据格式转换为标准格式"""
    logging.debug(f"开始转换宜搭数据到标准格式: {yida_data}")
    mapping = {
        'numberField_m9dkdkp8': 'recommendAmount',    # 推荐金额
        'numberField_m9dkdkp9': 'dailyBillAmount',    # 日结金额
        'numberField_m9dkdkpa': 'amount',             # 净销售额
        'numberField_m9dkdkpb': 'count',              # 总销售笔数
        'numberField_m9dkdkpc': 'instoreAmount',      # 店内净销售额
        'numberField_m9dkdkpd': 'instoreCount',       # 店内销售笔数
        'numberField_m9dkdkpe': 'onlineAmount',       # 线上净销售额
        'numberField_m9dkdkpf': 'onlineCount',        # 线上销售笔数
        'textField_m9dkdkpg': 'shopId',              # 数衍平台机构ID
        'textField_m9dkdkph': 'shopEntityId',        # 数衍平台店铺ID
        'textField_m9dkdkpi': 'shopEntityName',      # 数衍平台店铺名称
        'textField_m9dkdkox': 'projectName',         # 项目名称
        'dateField_m9dkdkoz': 'saleTime'             # 销售日期
    }
    
    result = {}
    for yida_key, standard_key in mapping.items():
        if yida_key in yida_data:
            value = yida_data[yida_key]
            # 处理日期字段
            if yida_key == 'dateField_m9dkdkoz':
                timestamp = int(value) / 1000  # 毫秒转秒
                value = datetime.fromtimestamp(timestamp).strftime('%Y%m%d')
            # 处理数值字段（使用不带_value后缀的数值版本）
            elif yida_key.startswith('numberField_'):
                value = float(value) if value else 0.0
            result[standard_key] = value
            
    return result

def _validate_and_convert_timestamp(value: Union[int, float, str]) -> Optional[int]:
    """验证并转换时间戳
    
    Args:
        value: 时间戳值（可能是整数、浮点数或字符串）
        
    Returns:
        Optional[int]: 转换后的毫秒级时间戳，如果转换失败则返回None
    """
    try:
        # 记录原始数据信息
        logging.debug(f"开始处理时间戳: 原始值={value}, 类型={type(value)}")
        
        # 转换为整数
        if isinstance(value, str):
            if not value.isdigit():
                logging.warning(f"时间戳字符串包含非数字字符: {value}")
                return None
            value = int(value)
            logging.debug(f"字符串转换为整数: {value}")
        
        # 根据位数判断时间戳类型
        timestamp_str = str(int(value))
        logging.debug(f"时间戳字符串: {timestamp_str}, 长度: {len(timestamp_str)}")
        
        if len(timestamp_str) == 13:  # 毫秒级时间戳
            logging.debug(f"识别为毫秒级时间戳: {value}")
            return int(value)
        elif len(timestamp_str) == 10:  # 秒级时间戳
            logging.debug(f"识别为秒级时间戳: {value}, 转换为毫秒: {int(value * 1000)}")
            return int(value * 1000)  # 转换为毫秒级
        elif len(timestamp_str) == 8 and timestamp_str.isdigit():  # 可能是YYYYMMDD格式
            try:
                dt = datetime.strptime(timestamp_str, '%Y%m%d')
                ms_timestamp = int(dt.timestamp() * 1000)
                logging.debug(f"识别为YYYYMMDD格式: {timestamp_str}, 转换为时间戳: {ms_timestamp}")
                return ms_timestamp
            except ValueError:
                logging.warning(f"8位数字不是有效的YYYYMMDD格式: {timestamp_str}")
        else:
            logging.warning(f"不支持的时间戳格式: {value}, 长度: {len(timestamp_str)}, 类型: {type(value)}")
            return None
    except (ValueError, TypeError) as e:
        logging.error(f"时间戳转换错误: 原始值={value}, 类型={type(value)}, 错误: {str(e)}")
        return None

def _validate_and_convert_date(value: str, is_monthly: bool = False) -> Optional[datetime]:
    """验证并转换日期字符串
    
    Args:
        value: 日期字符串
        is_monthly: 是否为月度数据
        
    Returns:
        Optional[datetime]: 转换后的datetime对象，如果转换失败则返回None
    """
    try:
        # 记录原始数据信息
        logging.debug(f"开始处理日期: 原始值={value}, 类型={type(value)}, 是否月度={is_monthly}")
        
        value = str(value).strip()
        
        # 尝试不同的日期格式
        date_formats = []
        if is_monthly:
            # 月度数据格式
            date_formats = [
                ('%Y%m', 6),      # YYYYMM
                ('%Y-%m', 7),     # YYYY-MM
                ('%Y/%m', 7),     # YYYY/MM
                ('%Y%m%d', 8),    # YYYYMMDD
            ]
        else:
            # 日度数据格式
            date_formats = [
                ('%Y%m%d', 8),    # YYYYMMDD
                ('%Y-%m-%d', 10), # YYYY-MM-DD
                ('%Y/%m/%d', 10), # YYYY/MM/DD
                ('%Y.%m.%d', 10), # YYYY.MM.DD
            ]
        
        # 记录支持的格式
        supported_formats = [fmt for fmt, _ in date_formats]
        logging.debug(f"支持的日期格式: {', '.join(supported_formats)}")
        
        # 根据长度匹配格式
        for fmt, length in date_formats:
            if len(value) == length:
                try:
                    dt = datetime.strptime(value, fmt)
                    # 验证日期有效性
                    if is_monthly:
                        # 月度数据设置为当月第一天
                        dt = dt.replace(day=1)
                        logging.debug(f"月度日期转换成功: {value} -> {dt.strftime('%Y-%m-%d')}")
                    else:
                        # 验证日期的有效性（如2月30日）
                        if dt.day != datetime.strptime(dt.strftime('%Y-%m-%d'), '%Y-%m-%d').day:
                            raise ValueError(f"无效的日期: {value}")
                        logging.debug(f"日度日期转换成功: {value} -> {dt.strftime('%Y-%m-%d')}")
                    return dt
                except ValueError as e:
                    logging.debug(f"尝试格式 {fmt} 失败: {str(e)}")
                    continue
        
        # 如果没有匹配的格式，记录错误
        logging.error(f"不支持的日期格式: 原始值={value}, 类型={type(value)}, 长度={len(value)}, 支持的格式: {', '.join(supported_formats)}")
        return None
        
    except Exception as e:
        logging.error(f"日期格式转换错误: 原始值={value}, 类型={type(value)}, 错误: {str(e)}")
        return None

def convert_standard_to_yida(standard_data: Dict, shop_mapping: Dict, is_monthly: bool = False) -> Dict:
    """将标准格式转换为宜搭数据格式

    Args:
        standard_data: 标准格式数据
        shop_mapping: 店铺与项目映射
        is_monthly: 是否为月度数据，影响日期格式处理

    Returns:
        Dict: 宜搭表单格式数据
    """
    # 首先转换Decimal类型
    standard_data = convert_decimal_to_float(standard_data)

    logging.debug(f"开始转换标准数据到宜搭格式: {standard_data}")
    mapping = {
        'recommendAmount': 'numberField_m9dkdkp8',    # 推荐金额 - 商户推荐的销售金额
        'dailyBillAmount': 'numberField_m9dkdkp9',    # 日结金额 - 每日结算的销售总额
        'amount': 'numberField_m9dkdkpa',             # 净销售额 - 扣除退款后的实际销售金额
        'count': 'numberField_m9dkdkpb',              # 总销售笔数 - 包含所有销售交易的笔数
        'instoreAmount': 'numberField_m9dkdkpc',      # 店内净销售额 - 实体店铺内的销售金额
        'instoreCount': 'numberField_m9dkdkpd',       # 店内销售笔数 - 实体店铺内的销售交易笔数
        'onlineAmount': 'numberField_m9dkdkpe',       # 线上净销售额 - 线上渠道的销售金额
        'onlineCount': 'numberField_m9dkdkpf',        # 线上销售笔数 - 线上渠道的销售交易笔数
        'shopId': 'textField_m9dkdkpg',              # 数衍平台机构ID - 商户在数衍平台的唯一标识
        'shopEntityId': 'textField_m9dkdkph',        # 数衍平台店铺ID - 店铺在数衍平台的唯一标识
        'shopEntityName': 'textField_m9dkdkpi',      # 数衍平台店铺名称 - 店铺的显示名称
        'projectName': 'textField_m9dkdkox',         # 项目名称 - 所属项目的名称
        'saleTime': 'dateField_m9dkdkoz'             # 销售日期 - 销售数据统计的日期
    }
    result = {}
    
    # 获取项目名称
    shop_id = standard_data.get('shopId', '')
    project_name = shop_mapping.get(shop_id, '')
    if not project_name:
        logging.warning(f"警告：机构ID {shop_id} 未找到对应的项目名称")
    standard_data['projectName'] = project_name
    
    for standard_key, value in standard_data.items():
        if standard_key in mapping:
            yida_key = mapping[standard_key]
            # 处理日期字段
            if yida_key == 'dateField_m9dkdkoz':
                try:
                    # 首先尝试作为时间戳处理
                    timestamp = _validate_and_convert_timestamp(value)
                    if timestamp is not None:
                        value = timestamp
                    else:
                        # 如果不是时间戳，尝试作为日期字符串处理
                        dt = _validate_and_convert_date(value, is_monthly)
                        if dt is not None:
                            value = int(dt.timestamp() * 1000)  # 转换为毫秒级时间戳
                        else:
                            # 记录原始数据，便于后续排查
                            logging.error(f"无法处理的日期值: {value}, 原始数据: {standard_data}")
                            continue
                except Exception as e:
                    logging.error(f"日期处理异常: {value}, {str(e)}, 原始数据: {standard_data}")
                    continue
            # 处理数值字段
            elif yida_key.startswith('numberField_'):
                try:
                    # 保持原始值，不进行类型转换
                    if value is None or value == '':
                        value = 0
                    # 对于笔数字段，确保是整数
                    if standard_key in ['count', 'instoreCount', 'onlineCount']:
                        value = int(float(value)) if value else 0
                    # 对于金额字段，保持原始类型
                    elif standard_key in ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']:
                        if value == 0:
                            value = 0  # 保持为整数0
                        elif isinstance(value, (int, float)):
                            value = value  # 保持原始数值类型
                    # result[f"{yida_key}_value"] = str(value)  # 添加字符串版本
                except ValueError as e:
                    logging.error(f"数值转换错误: {value}, {str(e)}, 原始数据: {standard_data}")
                    continue
            result[yida_key] = value
            
    return result

def main():
    try:
        logging.info("="*50)
        logging.info("程序启动 - 版本 v1.0.1")
        logging.info(f"日志文件: {log_file}")
        logging.info("="*50)
        
        # 验证MySQL连接
        try:
            conn = pymysql.connect(**DB_CONFIG)
            conn.close()
            logging.info("MySQL数据库连接成功")
        except pymysql.Error as e:
            logging.error(f"MySQL数据库连接失败: {str(e)}")
            print(f"MySQL数据库连接失败: {str(e)}")
            return
        
        sync_manager = DataSyncManager()
        logging.info("DataSyncManager初始化完成")
        
        # 首先更新店铺映射表
        try:
            logging.info("开始更新店铺映射表...")
            sync_manager.update_shop_entity_mapping()
            logging.info("店铺映射表更新完成")
        except Exception as e:
            logging.error(f"更新店铺映射表失败: {str(e)}")
            logging.warning("继续执行其他任务...")
        
        # 处理命令行参数
        if len(sys.argv) > 1:
            logging.info(f"接收到命令行参数: {sys.argv[1:]}")
            # 处理参数逻辑
            if sys.argv[1] == 'monthly':
                logging.info("启动月度汇总数据模式")
                sync_manager.update_monthly_summary()
                logging.info("月度汇总数据处理完成")
            # 如果只有一个参数，则作为开始日期
            if len(sys.argv) == 2:
                start_date = sys.argv[1]
                end_date = None
                logging.info(f"使用指定的开始日期: {start_date}, 结束日期使用默认值")
            # 如果有两个参数，则分别作为开始日期和结束日期
            elif len(sys.argv) == 3:
                start_date = sys.argv[1]
                end_date = sys.argv[2]
                logging.info(f"使用指定的日期范围: {start_date} 至 {end_date}")
            else:
                logging.error("参数格式错误，请使用以下格式：python sync_data_shuyan2yida_new1.py [开始日期] [结束日期]")
                print("参数格式错误，请使用以下格式：python sync_data_shuyan2yida_new1.py [开始日期] [结束日期]")
                return
        else:
            start_date = None
            end_date = None
            logging.info("未提供日期参数，使用默认值")
        
        # 执行综合数据同步
        logging.info(f"开始执行综合数据同步，参数: start_date={start_date}, end_date={end_date}")
        try:
            sync_manager.sync_data(start_date, end_date)
            logging.info("综合数据同步完成")
        except Exception as e:
            logging.error(f"综合数据同步失败: {str(e)}", exc_info=True)
            print(f"综合数据同步失败: {str(e)}")
        
        # 关闭数据库连接
        if hasattr(sync_manager, 'conn') and sync_manager.conn.open:
            sync_manager.conn.close()
            logging.info("MySQL数据库连接已关闭")
        
    except Exception as e:
        logging.error(f"程序执行失败: {str(e)}", exc_info=True)
        print(f"程序执行失败: {str(e)}")
    finally:
        logging.info("="*50)
        logging.info("程序退出")
        logging.info("="*50)

if __name__ == '__main__':
    logging.info("程序入口点: __main__")
    main() 