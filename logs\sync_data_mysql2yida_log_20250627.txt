2025-06-27 01:30:34,499 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 01:30:34,499 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 01:30:34,499 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 01:30:34,593 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 0 条记录
2025-06-27 01:30:34,593 - ERROR - 未获取到MySQL数据
2025-06-27 01:31:34,598 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 01:31:34,598 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 01:31:34,598 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 01:31:34,723 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 56 条记录
2025-06-27 01:31:34,723 - INFO - 获取到 1 个日期需要处理: ['2025-06-26']
2025-06-27 01:31:34,723 - INFO - 开始处理日期: 2025-06-26
2025-06-27 01:31:34,723 - INFO - Request Parameters - Page 1:
2025-06-27 01:31:34,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 01:31:34,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 01:31:40,771 - INFO - Response - Page 1:
2025-06-27 01:31:40,771 - INFO - 第 1 页获取到 33 条记录
2025-06-27 01:31:41,287 - INFO - 查询完成，共获取到 33 条记录
2025-06-27 01:31:41,287 - INFO - 获取到 33 条表单数据
2025-06-27 01:31:41,287 - INFO - 当前日期 2025-06-26 有 55 条MySQL数据需要处理
2025-06-27 01:31:41,287 - INFO - 开始批量插入 22 条新记录
2025-06-27 01:31:41,521 - INFO - 批量插入响应状态码: 200
2025-06-27 01:31:41,521 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 17:31:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1068', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0F501038-3F42-7BCB-B12A-74D2FC64A14E', 'x-acs-trace-id': '131c643e4ce4a266b80ac61b80d5f341', 'etag': '1k2qr5cbxHq/1fHEh5JZYJg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 01:31:41,521 - INFO - 批量插入响应体: {'result': ['FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMFM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMGM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMHM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMIM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMJM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMKM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMLM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMMM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMNM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMOM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMPM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMQM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMRM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMSM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMTM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMUM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMVM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMWM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMXM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMYM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMZM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCM0N']}
2025-06-27 01:31:41,521 - INFO - 批量插入表单数据成功，批次 1，共 22 条记录
2025-06-27 01:31:41,521 - INFO - 成功插入的数据ID: ['FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMFM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMGM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMHM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3VT8UNDCMIM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMJM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMKM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMLM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMMM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMNM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMOM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMPM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMQM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMRM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMSM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMTM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMUM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMVM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMWM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMXM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMYM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCMZM', 'FINST-LLF66F71C7LWR382FHZ6GAL6FOVI3WT8UNDCM0N']
2025-06-27 01:31:46,537 - INFO - 批量插入完成，共 22 条记录
2025-06-27 01:31:46,537 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 22 条，错误: 0 条
2025-06-27 01:31:46,537 - INFO - 数据同步完成！更新: 0 条，插入: 22 条，错误: 0 条
2025-06-27 01:31:46,537 - INFO - 同步完成
2025-06-27 04:30:34,213 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 04:30:34,213 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 04:30:34,213 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 04:30:34,338 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 1 条记录
2025-06-27 04:30:34,338 - INFO - 获取到 1 个日期需要处理: ['2025-06-26']
2025-06-27 04:30:34,338 - INFO - 开始处理日期: 2025-06-26
2025-06-27 04:30:34,338 - INFO - Request Parameters - Page 1:
2025-06-27 04:30:34,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 04:30:34,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 04:30:42,464 - ERROR - 处理日期 2025-06-26 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 09DA6DFA-0A9D-779C-B732-3DC6D8346F17 Response: {'code': 'ServiceUnavailable', 'requestid': '09DA6DFA-0A9D-779C-B732-3DC6D8346F17', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 09DA6DFA-0A9D-779C-B732-3DC6D8346F17)
2025-06-27 04:30:42,464 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-27 04:31:42,485 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 04:31:42,485 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 04:31:42,485 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 04:31:42,610 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 57 条记录
2025-06-27 04:31:42,610 - INFO - 获取到 1 个日期需要处理: ['2025-06-26']
2025-06-27 04:31:42,610 - INFO - 开始处理日期: 2025-06-26
2025-06-27 04:31:42,610 - INFO - Request Parameters - Page 1:
2025-06-27 04:31:42,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 04:31:42,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 04:31:48,610 - INFO - Response - Page 1:
2025-06-27 04:31:48,610 - INFO - 第 1 页获取到 50 条记录
2025-06-27 04:31:49,110 - INFO - Request Parameters - Page 2:
2025-06-27 04:31:49,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 04:31:49,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 04:31:49,563 - INFO - Response - Page 2:
2025-06-27 04:31:49,563 - INFO - 第 2 页获取到 5 条记录
2025-06-27 04:31:50,079 - INFO - 查询完成，共获取到 55 条记录
2025-06-27 04:31:50,079 - INFO - 获取到 55 条表单数据
2025-06-27 04:31:50,079 - INFO - 当前日期 2025-06-26 有 56 条MySQL数据需要处理
2025-06-27 04:31:50,079 - INFO - 开始批量插入 1 条新记录
2025-06-27 04:31:50,220 - INFO - 批量插入响应状态码: 200
2025-06-27 04:31:50,220 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 20:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5DC8F234-642A-7CAD-980F-A503F4BFDDEF', 'x-acs-trace-id': 'c10c9a8d0b73689e8e5b43b7d2d72763', 'etag': '6GAzdFXDdTjRw9w/iqpuAOg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 04:31:50,220 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91RYLWHDRS9E7Q4CKD39B72I6W9UDCMNJ']}
2025-06-27 04:31:50,220 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-27 04:31:50,220 - INFO - 成功插入的数据ID: ['FINST-SWC66P91RYLWHDRS9E7Q4CKD39B72I6W9UDCMNJ']
2025-06-27 04:31:55,236 - INFO - 批量插入完成，共 1 条记录
2025-06-27 04:31:55,236 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 04:31:55,236 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 04:31:55,236 - INFO - 同步完成
2025-06-27 07:30:33,766 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 07:30:33,766 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 07:30:33,766 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 07:30:33,906 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 1 条记录
2025-06-27 07:30:33,906 - INFO - 获取到 1 个日期需要处理: ['2025-06-26']
2025-06-27 07:30:33,906 - INFO - 开始处理日期: 2025-06-26
2025-06-27 07:30:33,906 - INFO - Request Parameters - Page 1:
2025-06-27 07:30:33,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 07:30:33,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 07:30:42,016 - ERROR - 处理日期 2025-06-26 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E3259D0E-2CAD-74BF-840C-13F31A370856 Response: {'code': 'ServiceUnavailable', 'requestid': 'E3259D0E-2CAD-74BF-840C-13F31A370856', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E3259D0E-2CAD-74BF-840C-13F31A370856)
2025-06-27 07:30:42,016 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-27 07:31:42,031 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 07:31:42,031 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 07:31:42,031 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 07:31:42,156 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 57 条记录
2025-06-27 07:31:42,156 - INFO - 获取到 1 个日期需要处理: ['2025-06-26']
2025-06-27 07:31:42,156 - INFO - 开始处理日期: 2025-06-26
2025-06-27 07:31:42,156 - INFO - Request Parameters - Page 1:
2025-06-27 07:31:42,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 07:31:42,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 07:31:48,218 - INFO - Response - Page 1:
2025-06-27 07:31:48,218 - INFO - 第 1 页获取到 50 条记录
2025-06-27 07:31:48,718 - INFO - Request Parameters - Page 2:
2025-06-27 07:31:48,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 07:31:48,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 07:31:49,203 - INFO - Response - Page 2:
2025-06-27 07:31:49,203 - INFO - 第 2 页获取到 6 条记录
2025-06-27 07:31:49,718 - INFO - 查询完成，共获取到 56 条记录
2025-06-27 07:31:49,718 - INFO - 获取到 56 条表单数据
2025-06-27 07:31:49,718 - INFO - 当前日期 2025-06-26 有 56 条MySQL数据需要处理
2025-06-27 07:31:49,718 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 07:31:49,718 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 07:31:49,718 - INFO - 同步完成
2025-06-27 10:30:33,742 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 10:30:33,742 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 10:30:33,742 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 10:30:33,883 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 90 条记录
2025-06-27 10:30:33,883 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-21', '2025-06-25', '2025-06-26', '2025-06-27']
2025-06-27 10:30:33,883 - INFO - 开始处理日期: 2025-06-20
2025-06-27 10:30:33,883 - INFO - Request Parameters - Page 1:
2025-06-27 10:30:33,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:33,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:42,024 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3FA85462-F2D1-7202-A69E-3645E15B562D Response: {'code': 'ServiceUnavailable', 'requestid': '3FA85462-F2D1-7202-A69E-3645E15B562D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3FA85462-F2D1-7202-A69E-3645E15B562D)
2025-06-27 10:30:42,024 - INFO - 开始处理日期: 2025-06-21
2025-06-27 10:30:42,024 - INFO - Request Parameters - Page 1:
2025-06-27 10:30:42,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:42,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:48,180 - INFO - Response - Page 1:
2025-06-27 10:30:48,180 - INFO - 第 1 页获取到 50 条记录
2025-06-27 10:30:48,695 - INFO - Request Parameters - Page 2:
2025-06-27 10:30:48,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:48,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:49,399 - INFO - Response - Page 2:
2025-06-27 10:30:49,399 - INFO - 第 2 页获取到 50 条记录
2025-06-27 10:30:49,914 - INFO - Request Parameters - Page 3:
2025-06-27 10:30:49,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:49,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:50,586 - INFO - Response - Page 3:
2025-06-27 10:30:50,586 - INFO - 第 3 页获取到 50 条记录
2025-06-27 10:30:51,102 - INFO - Request Parameters - Page 4:
2025-06-27 10:30:51,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:51,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:51,820 - INFO - Response - Page 4:
2025-06-27 10:30:51,820 - INFO - 第 4 页获取到 50 条记录
2025-06-27 10:30:52,320 - INFO - Request Parameters - Page 5:
2025-06-27 10:30:52,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:52,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:52,977 - INFO - Response - Page 5:
2025-06-27 10:30:52,977 - INFO - 第 5 页获取到 50 条记录
2025-06-27 10:30:53,492 - INFO - Request Parameters - Page 6:
2025-06-27 10:30:53,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:53,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:54,149 - INFO - Response - Page 6:
2025-06-27 10:30:54,149 - INFO - 第 6 页获取到 50 条记录
2025-06-27 10:30:54,664 - INFO - Request Parameters - Page 7:
2025-06-27 10:30:54,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:54,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:55,289 - INFO - Response - Page 7:
2025-06-27 10:30:55,289 - INFO - 第 7 页获取到 50 条记录
2025-06-27 10:30:55,789 - INFO - Request Parameters - Page 8:
2025-06-27 10:30:55,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:55,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:56,445 - INFO - Response - Page 8:
2025-06-27 10:30:56,445 - INFO - 第 8 页获取到 50 条记录
2025-06-27 10:30:56,945 - INFO - Request Parameters - Page 9:
2025-06-27 10:30:56,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:56,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:57,633 - INFO - Response - Page 9:
2025-06-27 10:30:57,633 - INFO - 第 9 页获取到 50 条记录
2025-06-27 10:30:58,148 - INFO - Request Parameters - Page 10:
2025-06-27 10:30:58,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:58,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:58,867 - INFO - Response - Page 10:
2025-06-27 10:30:58,867 - INFO - 第 10 页获取到 50 条记录
2025-06-27 10:30:59,367 - INFO - Request Parameters - Page 11:
2025-06-27 10:30:59,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:30:59,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:30:59,977 - INFO - Response - Page 11:
2025-06-27 10:30:59,977 - INFO - 第 11 页获取到 20 条记录
2025-06-27 10:31:00,477 - INFO - 查询完成，共获取到 520 条记录
2025-06-27 10:31:00,477 - INFO - 获取到 520 条表单数据
2025-06-27 10:31:00,477 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-27 10:31:00,477 - INFO - 开始更新记录 - 表单实例ID: FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMYC
2025-06-27 10:31:01,008 - INFO - 更新表单数据成功: FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMYC
2025-06-27 10:31:01,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 51458.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 51458.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 5}]
2025-06-27 10:31:01,008 - INFO - 日期 2025-06-21 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-27 10:31:01,008 - INFO - 开始处理日期: 2025-06-25
2025-06-27 10:31:01,008 - INFO - Request Parameters - Page 1:
2025-06-27 10:31:01,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:01,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:01,680 - INFO - Response - Page 1:
2025-06-27 10:31:01,680 - INFO - 第 1 页获取到 50 条记录
2025-06-27 10:31:02,195 - INFO - Request Parameters - Page 2:
2025-06-27 10:31:02,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:02,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:02,852 - INFO - Response - Page 2:
2025-06-27 10:31:02,852 - INFO - 第 2 页获取到 50 条记录
2025-06-27 10:31:03,352 - INFO - Request Parameters - Page 3:
2025-06-27 10:31:03,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:03,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:04,086 - INFO - Response - Page 3:
2025-06-27 10:31:04,102 - INFO - 第 3 页获取到 50 条记录
2025-06-27 10:31:04,602 - INFO - Request Parameters - Page 4:
2025-06-27 10:31:04,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:04,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:05,273 - INFO - Response - Page 4:
2025-06-27 10:31:05,273 - INFO - 第 4 页获取到 50 条记录
2025-06-27 10:31:05,789 - INFO - Request Parameters - Page 5:
2025-06-27 10:31:05,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:05,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:06,492 - INFO - Response - Page 5:
2025-06-27 10:31:06,492 - INFO - 第 5 页获取到 50 条记录
2025-06-27 10:31:07,008 - INFO - Request Parameters - Page 6:
2025-06-27 10:31:07,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:07,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:07,727 - INFO - Response - Page 6:
2025-06-27 10:31:07,727 - INFO - 第 6 页获取到 50 条记录
2025-06-27 10:31:08,242 - INFO - Request Parameters - Page 7:
2025-06-27 10:31:08,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:08,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:08,898 - INFO - Response - Page 7:
2025-06-27 10:31:08,898 - INFO - 第 7 页获取到 50 条记录
2025-06-27 10:31:09,398 - INFO - Request Parameters - Page 8:
2025-06-27 10:31:09,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:09,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:10,039 - INFO - Response - Page 8:
2025-06-27 10:31:10,039 - INFO - 第 8 页获取到 50 条记录
2025-06-27 10:31:10,539 - INFO - Request Parameters - Page 9:
2025-06-27 10:31:10,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:10,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:11,211 - INFO - Response - Page 9:
2025-06-27 10:31:11,211 - INFO - 第 9 页获取到 50 条记录
2025-06-27 10:31:11,711 - INFO - Request Parameters - Page 10:
2025-06-27 10:31:11,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:11,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:12,383 - INFO - Response - Page 10:
2025-06-27 10:31:12,383 - INFO - 第 10 页获取到 48 条记录
2025-06-27 10:31:12,898 - INFO - 查询完成，共获取到 498 条记录
2025-06-27 10:31:12,898 - INFO - 获取到 498 条表单数据
2025-06-27 10:31:12,898 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-27 10:31:12,898 - INFO - 开始批量插入 1 条新记录
2025-06-27 10:31:13,086 - INFO - 批量插入响应状态码: 200
2025-06-27 10:31:13,086 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '102835E2-288A-7311-A9EB-0F290E40AFA4', 'x-acs-trace-id': '23acca4fbee14bba65a39db377e89c5a', 'etag': '6u6v0Jg1Ml9TPtZIQwd6Teg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:31:13,086 - INFO - 批量插入响应体: {'result': ['FINST-FPB66VB1JVMWM7FBFPAL862IHPA023GV37ECMJ1']}
2025-06-27 10:31:13,086 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-27 10:31:13,086 - INFO - 成功插入的数据ID: ['FINST-FPB66VB1JVMWM7FBFPAL862IHPA023GV37ECMJ1']
2025-06-27 10:31:18,101 - INFO - 批量插入完成，共 1 条记录
2025-06-27 10:31:18,101 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 10:31:18,101 - INFO - 开始处理日期: 2025-06-26
2025-06-27 10:31:18,101 - INFO - Request Parameters - Page 1:
2025-06-27 10:31:18,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:18,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:18,789 - INFO - Response - Page 1:
2025-06-27 10:31:18,789 - INFO - 第 1 页获取到 50 条记录
2025-06-27 10:31:19,305 - INFO - Request Parameters - Page 2:
2025-06-27 10:31:19,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:19,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:19,805 - INFO - Response - Page 2:
2025-06-27 10:31:19,805 - INFO - 第 2 页获取到 6 条记录
2025-06-27 10:31:20,320 - INFO - 查询完成，共获取到 56 条记录
2025-06-27 10:31:20,320 - INFO - 获取到 56 条表单数据
2025-06-27 10:31:20,320 - INFO - 当前日期 2025-06-26 有 84 条MySQL数据需要处理
2025-06-27 10:31:20,320 - INFO - 开始批量插入 83 条新记录
2025-06-27 10:31:20,555 - INFO - 批量插入响应状态码: 200
2025-06-27 10:31:20,555 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2383', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3DCF422B-BB62-72CA-A8D0-EBC8A77060D1', 'x-acs-trace-id': '3956b27624d0027a0b50a4beb69685be', 'etag': '2H+geAiMNADfd3TmIFivuCA3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:31:20,555 - INFO - 批量插入响应体: {'result': ['FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM7', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM8', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM9', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMA', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMB', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMC', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMD', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECME', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMF', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMG', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMH', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMI', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMJ', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMK', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECML', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMM', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMN', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMO', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMP', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMQ', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMR', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMS', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMT', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMU', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMV', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMW', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMX', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMY', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMZ', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM01', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM11', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM21', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM31', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM41', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM51', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM61', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM71', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM81', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM91', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMA1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMB1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMC1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMD1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECME1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMF1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMG1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMH1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMI1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMJ1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMK1']}
2025-06-27 10:31:20,555 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-27 10:31:20,555 - INFO - 成功插入的数据ID: ['FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM7', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM8', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM9', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMA', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMB', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMC', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMD', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECME', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMF', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMG', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMH', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMI', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMJ', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMK', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECML', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMM', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMN', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMO', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMP', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMQ', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMR', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMS', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMT', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMU', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMV', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMW', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMX', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMY', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMZ', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM01', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM11', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM21', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM31', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM41', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM51', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM61', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM71', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM81', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECM91', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMA1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMB1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMC1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMD1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECME1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMF1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMG1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMH1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMI1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMJ1', 'FINST-CK766D71DPNW9IAUDKHA4DCCE86C2I7147ECMK1']
2025-06-27 10:31:25,758 - INFO - 批量插入响应状态码: 200
2025-06-27 10:31:25,758 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1563', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3AC0ACAF-1226-725D-A5EB-04575D51C6EB', 'x-acs-trace-id': '3bfe5f57c674664b326637588c1eb256', 'etag': '1DK15piu/Om4XsO6CPcaGZg3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:31:25,758 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM3', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM4', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM5', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM6', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM7', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM8', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM9', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMA', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMB', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMC', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMD', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECME', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMF', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMG', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMH', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMI', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMJ', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMK', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECML', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMM', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMN', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMO', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMP', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMQ', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMR', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMS', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMT', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMU', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMV', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMW', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMX', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMY', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMZ']}
2025-06-27 10:31:25,758 - INFO - 批量插入表单数据成功，批次 2，共 33 条记录
2025-06-27 10:31:25,758 - INFO - 成功插入的数据ID: ['FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM3', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM4', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM5', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM6', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM7', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM8', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECM9', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMA', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMB', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMC', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMD', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECME', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMF', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMG', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMH', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMI', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMJ', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMK', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECML', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMM', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMN', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMO', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMP', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMQ', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMR', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMS', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMT', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMU', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMV', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMW', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMX', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMY', 'FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMZ']
2025-06-27 10:31:30,773 - INFO - 批量插入完成，共 83 条记录
2025-06-27 10:31:30,773 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 83 条，错误: 0 条
2025-06-27 10:31:30,773 - INFO - 开始处理日期: 2025-06-27
2025-06-27 10:31:30,773 - INFO - Request Parameters - Page 1:
2025-06-27 10:31:30,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:31:30,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:31:31,195 - INFO - Response - Page 1:
2025-06-27 10:31:31,195 - INFO - 查询完成，共获取到 0 条记录
2025-06-27 10:31:31,195 - INFO - 获取到 0 条表单数据
2025-06-27 10:31:31,195 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-06-27 10:31:31,195 - INFO - 开始批量插入 1 条新记录
2025-06-27 10:31:31,414 - INFO - 批量插入响应状态码: 200
2025-06-27 10:31:31,414 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:31:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C21E570A-1349-79CB-A0F4-3A9DD0A4EE98', 'x-acs-trace-id': '0337dd8affcabbb949c9046199dd31fb', 'etag': '6zdrhFx6ujwF/O3KkzDQ73g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:31:31,414 - INFO - 批量插入响应体: {'result': ['FINST-EEC66XC1QZLW9FWI68JY3AIPACTW23L947ECMZA']}
2025-06-27 10:31:31,414 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-27 10:31:31,414 - INFO - 成功插入的数据ID: ['FINST-EEC66XC1QZLW9FWI68JY3AIPACTW23L947ECMZA']
2025-06-27 10:31:36,429 - INFO - 批量插入完成，共 1 条记录
2025-06-27 10:31:36,429 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 10:31:36,429 - INFO - 数据同步完成！更新: 1 条，插入: 85 条，错误: 1 条
2025-06-27 10:32:36,445 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 10:32:36,445 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 10:32:36,445 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 10:32:36,585 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 296 条记录
2025-06-27 10:32:36,585 - INFO - 获取到 2 个日期需要处理: ['2025-06-26', '2025-06-27']
2025-06-27 10:32:36,585 - INFO - 开始处理日期: 2025-06-26
2025-06-27 10:32:36,585 - INFO - Request Parameters - Page 1:
2025-06-27 10:32:36,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:32:36,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:32:37,288 - INFO - Response - Page 1:
2025-06-27 10:32:37,288 - INFO - 第 1 页获取到 50 条记录
2025-06-27 10:32:37,788 - INFO - Request Parameters - Page 2:
2025-06-27 10:32:37,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:32:37,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:32:38,445 - INFO - Response - Page 2:
2025-06-27 10:32:38,445 - INFO - 第 2 页获取到 50 条记录
2025-06-27 10:32:38,945 - INFO - Request Parameters - Page 3:
2025-06-27 10:32:38,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:32:38,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:32:39,617 - INFO - Response - Page 3:
2025-06-27 10:32:39,617 - INFO - 第 3 页获取到 39 条记录
2025-06-27 10:32:40,132 - INFO - 查询完成，共获取到 139 条记录
2025-06-27 10:32:40,132 - INFO - 获取到 139 条表单数据
2025-06-27 10:32:40,132 - INFO - 当前日期 2025-06-26 有 286 条MySQL数据需要处理
2025-06-27 10:32:40,132 - INFO - 开始批量插入 147 条新记录
2025-06-27 10:32:40,413 - INFO - 批量插入响应状态码: 200
2025-06-27 10:32:40,413 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2118BB5-337B-79C9-A8E7-587DC27B7736', 'x-acs-trace-id': 'c4cc83ddb54fd209752a71980716a186', 'etag': '2wUdpVAmgUnGzWOY3d/+Yiw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:32:40,413 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM18', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM28', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM38', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM48', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM58', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM68', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM78', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM88', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM98', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMA8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMB8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMC8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMD8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECME8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMF8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMG8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMH8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMI8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMJ8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMK8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECML8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMM8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMN8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMO8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMP8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMQ8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMR8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMS8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMT8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMU8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMV8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMW8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMX8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMY8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMZ8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM09', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM19', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM29', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM39', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM49', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM59', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM69', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM79', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM89', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM99', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMA9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMB9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMC9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMD9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECME9']}
2025-06-27 10:32:40,413 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-27 10:32:40,413 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM18', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM28', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM38', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM48', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM58', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM68', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM78', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM88', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM98', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMA8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMB8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMC8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMD8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECME8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMF8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMG8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMH8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMI8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMJ8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMK8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECML8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMM8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMN8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMO8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMP8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMQ8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMR8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMS8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMT8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMU8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMV8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMW8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMX8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMY8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMZ8', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM09', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM19', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM29', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM39', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM49', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM59', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM69', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM79', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM89', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECM99', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMA9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMB9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMC9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECMD9', 'FINST-IQE66ZC1FYMWORCJ65I5KBU7I8022VTQ57ECME9']
2025-06-27 10:32:45,648 - INFO - 批量插入响应状态码: 200
2025-06-27 10:32:45,648 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2383', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '38319F48-556C-748B-A6C3-1C3750CB2E13', 'x-acs-trace-id': '295595d62f7971de2d8f1508769be657', 'etag': '25AlnCPiSWMf80US2aLCc+w3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:32:45,648 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECM7', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECM8', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECM9', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMA', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMB', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMC', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMD', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECME', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMF', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMG', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMH', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMI', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMJ', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMK', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECML', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMM', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMN', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMO', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMP', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMQ', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMR', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMS', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMT', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMU', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMV', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMW', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMX', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMY', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMZ', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM01', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM11', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM21', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM31', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM41', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM51', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM61', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM71', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM81', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM91', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMA1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMB1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMC1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMD1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECME1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMF1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMG1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMH1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMI1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMJ1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMK1']}
2025-06-27 10:32:45,648 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-27 10:32:45,648 - INFO - 成功插入的数据ID: ['FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECM7', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECM8', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECM9', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMA', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMB', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMC', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMD', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECME', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMF', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03EVU57ECMG', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMH', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMI', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMJ', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMK', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECML', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMM', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMN', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMO', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMP', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMQ', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMR', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMS', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMT', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMU', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMV', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMW', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMX', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMY', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMZ', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM01', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM11', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM21', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM31', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM41', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM51', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM61', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM71', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM81', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECM91', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMA1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMB1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMC1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMD1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECME1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMF1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMG1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMH1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMI1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMJ1', 'FINST-AEF66BC1GPNWZ99B8LGQVALMN5W03FVU57ECMK1']
2025-06-27 10:32:50,882 - INFO - 批量插入响应状态码: 200
2025-06-27 10:32:50,882 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 02:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2237', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '104432D6-E208-72C6-AED9-DD374F35FD3D', 'x-acs-trace-id': 'c6d57a099645694c1e281228ff89f935', 'etag': '2sga3oP7wrnw7sdCDiCLNig7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 10:32:50,882 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM5', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM6', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM7', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM8', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM9', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMA', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMB', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMC', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMD', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECME', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMF', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMG', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMH', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMI', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMJ', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMK', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECML', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMM', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMN', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMO', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMP', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMQ', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMR', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMS', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMT', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMU', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMV', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMW', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMX', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMY', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMZ', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM01', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM11', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM21', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM31', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM41', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM51', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM61', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM71', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM81', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM91', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMA1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMB1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMC1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMD1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECME1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMF1']}
2025-06-27 10:32:50,882 - INFO - 批量插入表单数据成功，批次 3，共 47 条记录
2025-06-27 10:32:50,882 - INFO - 成功插入的数据ID: ['FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM5', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM6', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM7', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM8', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM9', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMA', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMB', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMC', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMD', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECME', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMF', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMG', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMH', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMI', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMJ', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMK', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECML', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMM', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMN', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMO', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMP', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMQ', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMR', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMS', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMT', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMU', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMV', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMW', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMX', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMY', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMZ', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM01', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM11', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM21', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM31', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM41', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM51', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM61', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM71', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM81', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM91', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMA1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMB1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMC1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMD1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECME1', 'FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECMF1']
2025-06-27 10:32:55,898 - INFO - 批量插入完成，共 147 条记录
2025-06-27 10:32:55,898 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 147 条，错误: 0 条
2025-06-27 10:32:55,898 - INFO - 开始处理日期: 2025-06-27
2025-06-27 10:32:55,898 - INFO - Request Parameters - Page 1:
2025-06-27 10:32:55,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 10:32:55,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 10:32:56,382 - INFO - Response - Page 1:
2025-06-27 10:32:56,382 - INFO - 第 1 页获取到 1 条记录
2025-06-27 10:32:56,898 - INFO - 查询完成，共获取到 1 条记录
2025-06-27 10:32:56,898 - INFO - 获取到 1 条表单数据
2025-06-27 10:32:56,898 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-06-27 10:32:56,898 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 10:32:56,898 - INFO - 数据同步完成！更新: 0 条，插入: 147 条，错误: 0 条
2025-06-27 10:32:56,898 - INFO - 同步完成
2025-06-27 13:30:34,232 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 13:30:34,232 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 13:30:34,232 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 13:30:34,372 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 137 条记录
2025-06-27 13:30:34,372 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-21', '2025-06-25', '2025-06-26', '2025-06-27']
2025-06-27 13:30:34,372 - INFO - 开始处理日期: 2025-06-20
2025-06-27 13:30:34,372 - INFO - Request Parameters - Page 1:
2025-06-27 13:30:34,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:34,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:42,482 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A3E4AB6B-4AFB-71FF-9A2F-D2CCA1049783 Response: {'code': 'ServiceUnavailable', 'requestid': 'A3E4AB6B-4AFB-71FF-9A2F-D2CCA1049783', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A3E4AB6B-4AFB-71FF-9A2F-D2CCA1049783)
2025-06-27 13:30:42,482 - INFO - 开始处理日期: 2025-06-21
2025-06-27 13:30:42,482 - INFO - Request Parameters - Page 1:
2025-06-27 13:30:42,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:42,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:49,013 - INFO - Response - Page 1:
2025-06-27 13:30:49,013 - INFO - 第 1 页获取到 50 条记录
2025-06-27 13:30:49,513 - INFO - Request Parameters - Page 2:
2025-06-27 13:30:49,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:49,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:50,153 - INFO - Response - Page 2:
2025-06-27 13:30:50,153 - INFO - 第 2 页获取到 50 条记录
2025-06-27 13:30:50,653 - INFO - Request Parameters - Page 3:
2025-06-27 13:30:50,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:50,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:51,388 - INFO - Response - Page 3:
2025-06-27 13:30:51,388 - INFO - 第 3 页获取到 50 条记录
2025-06-27 13:30:51,903 - INFO - Request Parameters - Page 4:
2025-06-27 13:30:51,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:51,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:52,591 - INFO - Response - Page 4:
2025-06-27 13:30:52,591 - INFO - 第 4 页获取到 50 条记录
2025-06-27 13:30:53,091 - INFO - Request Parameters - Page 5:
2025-06-27 13:30:53,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:53,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:53,794 - INFO - Response - Page 5:
2025-06-27 13:30:53,794 - INFO - 第 5 页获取到 50 条记录
2025-06-27 13:30:54,310 - INFO - Request Parameters - Page 6:
2025-06-27 13:30:54,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:54,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:54,966 - INFO - Response - Page 6:
2025-06-27 13:30:54,966 - INFO - 第 6 页获取到 50 条记录
2025-06-27 13:30:55,466 - INFO - Request Parameters - Page 7:
2025-06-27 13:30:55,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:55,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:56,138 - INFO - Response - Page 7:
2025-06-27 13:30:56,138 - INFO - 第 7 页获取到 50 条记录
2025-06-27 13:30:56,638 - INFO - Request Parameters - Page 8:
2025-06-27 13:30:56,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:56,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:57,247 - INFO - Response - Page 8:
2025-06-27 13:30:57,247 - INFO - 第 8 页获取到 50 条记录
2025-06-27 13:30:57,763 - INFO - Request Parameters - Page 9:
2025-06-27 13:30:57,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:57,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:58,513 - INFO - Response - Page 9:
2025-06-27 13:30:58,513 - INFO - 第 9 页获取到 50 条记录
2025-06-27 13:30:59,028 - INFO - Request Parameters - Page 10:
2025-06-27 13:30:59,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:30:59,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:30:59,763 - INFO - Response - Page 10:
2025-06-27 13:30:59,763 - INFO - 第 10 页获取到 50 条记录
2025-06-27 13:31:00,263 - INFO - Request Parameters - Page 11:
2025-06-27 13:31:00,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:00,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:00,872 - INFO - Response - Page 11:
2025-06-27 13:31:00,872 - INFO - 第 11 页获取到 20 条记录
2025-06-27 13:31:01,388 - INFO - 查询完成，共获取到 520 条记录
2025-06-27 13:31:01,388 - INFO - 获取到 520 条表单数据
2025-06-27 13:31:01,388 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-27 13:31:01,388 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 13:31:01,388 - INFO - 开始处理日期: 2025-06-25
2025-06-27 13:31:01,388 - INFO - Request Parameters - Page 1:
2025-06-27 13:31:01,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:01,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:02,075 - INFO - Response - Page 1:
2025-06-27 13:31:02,075 - INFO - 第 1 页获取到 50 条记录
2025-06-27 13:31:02,575 - INFO - Request Parameters - Page 2:
2025-06-27 13:31:02,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:02,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:03,278 - INFO - Response - Page 2:
2025-06-27 13:31:03,278 - INFO - 第 2 页获取到 50 条记录
2025-06-27 13:31:03,778 - INFO - Request Parameters - Page 3:
2025-06-27 13:31:03,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:03,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:04,716 - INFO - Response - Page 3:
2025-06-27 13:31:04,716 - INFO - 第 3 页获取到 50 条记录
2025-06-27 13:31:05,231 - INFO - Request Parameters - Page 4:
2025-06-27 13:31:05,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:05,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:05,888 - INFO - Response - Page 4:
2025-06-27 13:31:05,888 - INFO - 第 4 页获取到 50 条记录
2025-06-27 13:31:06,403 - INFO - Request Parameters - Page 5:
2025-06-27 13:31:06,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:06,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:07,075 - INFO - Response - Page 5:
2025-06-27 13:31:07,075 - INFO - 第 5 页获取到 50 条记录
2025-06-27 13:31:07,591 - INFO - Request Parameters - Page 6:
2025-06-27 13:31:07,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:07,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:08,310 - INFO - Response - Page 6:
2025-06-27 13:31:08,310 - INFO - 第 6 页获取到 50 条记录
2025-06-27 13:31:08,825 - INFO - Request Parameters - Page 7:
2025-06-27 13:31:08,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:08,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:09,497 - INFO - Response - Page 7:
2025-06-27 13:31:09,497 - INFO - 第 7 页获取到 50 条记录
2025-06-27 13:31:09,997 - INFO - Request Parameters - Page 8:
2025-06-27 13:31:09,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:09,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:10,638 - INFO - Response - Page 8:
2025-06-27 13:31:10,638 - INFO - 第 8 页获取到 50 条记录
2025-06-27 13:31:11,138 - INFO - Request Parameters - Page 9:
2025-06-27 13:31:11,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:11,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:11,856 - INFO - Response - Page 9:
2025-06-27 13:31:11,856 - INFO - 第 9 页获取到 50 条记录
2025-06-27 13:31:12,372 - INFO - Request Parameters - Page 10:
2025-06-27 13:31:12,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:12,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:13,059 - INFO - Response - Page 10:
2025-06-27 13:31:13,059 - INFO - 第 10 页获取到 49 条记录
2025-06-27 13:31:13,559 - INFO - 查询完成，共获取到 499 条记录
2025-06-27 13:31:13,559 - INFO - 获取到 499 条表单数据
2025-06-27 13:31:13,559 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-27 13:31:13,559 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 13:31:13,559 - INFO - 开始处理日期: 2025-06-26
2025-06-27 13:31:13,559 - INFO - Request Parameters - Page 1:
2025-06-27 13:31:13,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:13,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:14,263 - INFO - Response - Page 1:
2025-06-27 13:31:14,263 - INFO - 第 1 页获取到 50 条记录
2025-06-27 13:31:14,763 - INFO - Request Parameters - Page 2:
2025-06-27 13:31:14,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:14,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:15,372 - INFO - Response - Page 2:
2025-06-27 13:31:15,372 - INFO - 第 2 页获取到 50 条记录
2025-06-27 13:31:15,888 - INFO - Request Parameters - Page 3:
2025-06-27 13:31:15,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:15,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:16,544 - INFO - Response - Page 3:
2025-06-27 13:31:16,544 - INFO - 第 3 页获取到 50 条记录
2025-06-27 13:31:17,059 - INFO - Request Parameters - Page 4:
2025-06-27 13:31:17,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:17,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:17,716 - INFO - Response - Page 4:
2025-06-27 13:31:17,716 - INFO - 第 4 页获取到 50 条记录
2025-06-27 13:31:18,216 - INFO - Request Parameters - Page 5:
2025-06-27 13:31:18,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:18,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:18,966 - INFO - Response - Page 5:
2025-06-27 13:31:18,966 - INFO - 第 5 页获取到 50 条记录
2025-06-27 13:31:19,481 - INFO - Request Parameters - Page 6:
2025-06-27 13:31:19,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:19,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:20,075 - INFO - Response - Page 6:
2025-06-27 13:31:20,075 - INFO - 第 6 页获取到 36 条记录
2025-06-27 13:31:20,575 - INFO - 查询完成，共获取到 286 条记录
2025-06-27 13:31:20,575 - INFO - 获取到 286 条表单数据
2025-06-27 13:31:20,575 - INFO - 当前日期 2025-06-26 有 130 条MySQL数据需要处理
2025-06-27 13:31:20,575 - INFO - 开始批量插入 46 条新记录
2025-06-27 13:31:20,809 - INFO - 批量插入响应状态码: 200
2025-06-27 13:31:20,809 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 05:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2220', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '623FB7FB-8EA4-7EFD-8FE7-ADF4DD356A00', 'x-acs-trace-id': '03ada4fdea6e19f9b0ec0f509533b1f0', 'etag': '2/kBxVEjrNmttuTGIru8ZXQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 13:31:20,809 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMA1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMB1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMC1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMD1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECME1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMF1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMG1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMH1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMI1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMJ1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMK1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECML1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMM1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMN1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMO1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMP1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMQ1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMR1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMS1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMT1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMU1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMV1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMW1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMX1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMY1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMZ1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM02', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM12', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM22', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM32', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM42', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM52', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM62', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM72', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM82', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM92', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMA2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMB2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMC2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMD2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECME2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMF2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMG2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMH2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMI2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMJ2']}
2025-06-27 13:31:20,809 - INFO - 批量插入表单数据成功，批次 1，共 46 条记录
2025-06-27 13:31:20,809 - INFO - 成功插入的数据ID: ['FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMA1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMB1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMC1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMD1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECME1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMF1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMG1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMH1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMI1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMJ1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMK1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECML1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMM1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMN1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMO1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMP1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMQ1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMR1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMS1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMT1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMU1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMV1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMW1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMX1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMY1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECMZ1', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM02', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM12', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM22', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM32', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM42', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM52', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM62', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM72', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM82', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2DSIJDECM92', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMA2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMB2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMC2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMD2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECME2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMF2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMG2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMH2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMI2', 'FINST-07E66I91GRNWPIGY88CHP42ML62H2ESIJDECMJ2']
2025-06-27 13:31:25,825 - INFO - 批量插入完成，共 46 条记录
2025-06-27 13:31:25,825 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 46 条，错误: 0 条
2025-06-27 13:31:25,825 - INFO - 开始处理日期: 2025-06-27
2025-06-27 13:31:25,825 - INFO - Request Parameters - Page 1:
2025-06-27 13:31:25,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:31:25,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:31:26,263 - INFO - Response - Page 1:
2025-06-27 13:31:26,263 - INFO - 第 1 页获取到 1 条记录
2025-06-27 13:31:26,763 - INFO - 查询完成，共获取到 1 条记录
2025-06-27 13:31:26,763 - INFO - 获取到 1 条表单数据
2025-06-27 13:31:26,763 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-06-27 13:31:26,763 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 13:31:26,763 - INFO - 数据同步完成！更新: 0 条，插入: 46 条，错误: 1 条
2025-06-27 13:32:26,778 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 13:32:26,778 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 13:32:26,778 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 13:32:26,918 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 443 条记录
2025-06-27 13:32:26,918 - INFO - 获取到 2 个日期需要处理: ['2025-06-26', '2025-06-27']
2025-06-27 13:32:26,918 - INFO - 开始处理日期: 2025-06-26
2025-06-27 13:32:26,918 - INFO - Request Parameters - Page 1:
2025-06-27 13:32:26,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:26,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:27,684 - INFO - Response - Page 1:
2025-06-27 13:32:27,684 - INFO - 第 1 页获取到 50 条记录
2025-06-27 13:32:28,200 - INFO - Request Parameters - Page 2:
2025-06-27 13:32:28,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:28,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:28,840 - INFO - Response - Page 2:
2025-06-27 13:32:28,840 - INFO - 第 2 页获取到 50 条记录
2025-06-27 13:32:29,340 - INFO - Request Parameters - Page 3:
2025-06-27 13:32:29,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:29,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:29,965 - INFO - Response - Page 3:
2025-06-27 13:32:29,965 - INFO - 第 3 页获取到 50 条记录
2025-06-27 13:32:30,465 - INFO - Request Parameters - Page 4:
2025-06-27 13:32:30,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:30,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:31,153 - INFO - Response - Page 4:
2025-06-27 13:32:31,153 - INFO - 第 4 页获取到 50 条记录
2025-06-27 13:32:31,653 - INFO - Request Parameters - Page 5:
2025-06-27 13:32:31,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:31,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:32,653 - INFO - Response - Page 5:
2025-06-27 13:32:32,653 - INFO - 第 5 页获取到 50 条记录
2025-06-27 13:32:33,168 - INFO - Request Parameters - Page 6:
2025-06-27 13:32:33,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:33,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:33,965 - INFO - Response - Page 6:
2025-06-27 13:32:33,965 - INFO - 第 6 页获取到 50 条记录
2025-06-27 13:32:34,465 - INFO - Request Parameters - Page 7:
2025-06-27 13:32:34,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:34,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:35,059 - INFO - Response - Page 7:
2025-06-27 13:32:35,059 - INFO - 第 7 页获取到 32 条记录
2025-06-27 13:32:35,559 - INFO - 查询完成，共获取到 332 条记录
2025-06-27 13:32:35,559 - INFO - 获取到 332 条表单数据
2025-06-27 13:32:35,559 - INFO - 当前日期 2025-06-26 有 431 条MySQL数据需要处理
2025-06-27 13:32:35,575 - INFO - 开始批量插入 99 条新记录
2025-06-27 13:32:35,809 - INFO - 批量插入响应状态码: 200
2025-06-27 13:32:35,809 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 05:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2376', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E98CB896-A55B-7C81-A42B-3079AE3ED53D', 'x-acs-trace-id': 'e496a48ccb526652f49a68f23618feae', 'etag': '2vCSx+Y7fSZ4pV1g1lmHLtw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 13:32:35,809 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA1JVNWAC17AT2X0D5W42N43WN4LDECM0', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM2', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM3', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM4', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM5', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM6', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM7', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM8', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM9', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMA', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMB', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMC', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMD', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECME', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMF', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMG', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMH', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMI', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMJ', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMK', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECML', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMM', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMN', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMO', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMP', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMQ', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMR', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMS', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMT', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMU', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMV', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMW', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMX', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMY', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMZ', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM01', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM11', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM21', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM31', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM41', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM51', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM61', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM71', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM81', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM91', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMA1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMB1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMC1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMD1']}
2025-06-27 13:32:35,809 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-27 13:32:35,809 - INFO - 成功插入的数据ID: ['FINST-FD966QA1JVNWAC17AT2X0D5W42N43WN4LDECM0', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM2', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM3', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM4', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM5', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM6', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM7', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM8', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM9', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMA', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMB', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMC', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMD', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECME', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMF', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMG', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMH', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMI', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMJ', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMK', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECML', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMM', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMN', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMO', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMP', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMQ', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMR', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMS', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMT', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMU', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMV', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMW', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMX', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMY', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMZ', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM01', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM11', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM21', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM31', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM41', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM51', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM61', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM71', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM81', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECM91', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMA1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMB1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMC1', 'FINST-FD966QA1JVNWAC17AT2X0D5W42N43XN4LDECMD1']
2025-06-27 13:32:41,074 - INFO - 批量插入响应状态码: 200
2025-06-27 13:32:41,074 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 05:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2364', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '63470893-EFF2-7FB4-87EB-3C338E75100A', 'x-acs-trace-id': 'ea49af5b307de071f57a5620de77b521', 'etag': '2LkmEQTQkHav6CtaSnAwedQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 13:32:41,074 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMT1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMU1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMV1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMW1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMX1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMY1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMZ1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM02', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM12', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM22', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM32', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM42', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM52', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM62', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM72', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM82', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM92', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMA2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMB2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMC2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMD2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECME2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMF2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMG2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMH2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMI2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMJ2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMK2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECML2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMM2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMN2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMO2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMP2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMQ2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMR2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMS2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMT2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMU2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMV2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMW2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMX2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMY2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMZ2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM03', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM13', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM23', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM33', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM43', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM53']}
2025-06-27 13:32:41,074 - INFO - 批量插入表单数据成功，批次 2，共 49 条记录
2025-06-27 13:32:41,074 - INFO - 成功插入的数据ID: ['FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMT1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMU1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMV1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMW1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMX1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMY1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMZ1', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM02', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM12', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM22', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM32', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM42', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM52', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM62', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM72', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM82', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM92', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMA2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMB2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMC2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMD2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECME2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMF2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMG2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMH2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMI2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMJ2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMK2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECML2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMM2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMN2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMO2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMP2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMQ2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMR2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMS2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMT2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMU2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMV2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMW2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMX2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMY2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECMZ2', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM03', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM13', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM23', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM33', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM43', 'FINST-MUC66Q819QNWCKB2BR766BCN8IX339Q8LDECM53']
2025-06-27 13:32:46,090 - INFO - 批量插入完成，共 99 条记录
2025-06-27 13:32:46,090 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 99 条，错误: 0 条
2025-06-27 13:32:46,090 - INFO - 开始处理日期: 2025-06-27
2025-06-27 13:32:46,090 - INFO - Request Parameters - Page 1:
2025-06-27 13:32:46,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 13:32:46,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 13:32:46,512 - INFO - Response - Page 1:
2025-06-27 13:32:46,512 - INFO - 第 1 页获取到 1 条记录
2025-06-27 13:32:47,028 - INFO - 查询完成，共获取到 1 条记录
2025-06-27 13:32:47,028 - INFO - 获取到 1 条表单数据
2025-06-27 13:32:47,028 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-06-27 13:32:47,028 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 13:32:47,028 - INFO - 数据同步完成！更新: 0 条，插入: 99 条，错误: 0 条
2025-06-27 13:32:47,028 - INFO - 同步完成
2025-06-27 16:30:33,632 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 16:30:33,632 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 16:30:33,632 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 16:30:33,773 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 152 条记录
2025-06-27 16:30:33,773 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-21', '2025-06-25', '2025-06-26', '2025-06-27']
2025-06-27 16:30:33,773 - INFO - 开始处理日期: 2025-06-20
2025-06-27 16:30:33,788 - INFO - Request Parameters - Page 1:
2025-06-27 16:30:33,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:33,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:41,945 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1FB2ECB8-F2D3-7075-8076-7360B9873C2F Response: {'code': 'ServiceUnavailable', 'requestid': '1FB2ECB8-F2D3-7075-8076-7360B9873C2F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1FB2ECB8-F2D3-7075-8076-7360B9873C2F)
2025-06-27 16:30:41,945 - INFO - 开始处理日期: 2025-06-21
2025-06-27 16:30:41,945 - INFO - Request Parameters - Page 1:
2025-06-27 16:30:41,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:41,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:43,632 - INFO - Response - Page 1:
2025-06-27 16:30:43,632 - INFO - 第 1 页获取到 50 条记录
2025-06-27 16:30:44,148 - INFO - Request Parameters - Page 2:
2025-06-27 16:30:44,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:44,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:49,898 - INFO - Response - Page 2:
2025-06-27 16:30:49,898 - INFO - 第 2 页获取到 50 条记录
2025-06-27 16:30:50,413 - INFO - Request Parameters - Page 3:
2025-06-27 16:30:50,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:50,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:51,132 - INFO - Response - Page 3:
2025-06-27 16:30:51,132 - INFO - 第 3 页获取到 50 条记录
2025-06-27 16:30:51,648 - INFO - Request Parameters - Page 4:
2025-06-27 16:30:51,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:51,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:52,445 - INFO - Response - Page 4:
2025-06-27 16:30:52,460 - INFO - 第 4 页获取到 50 条记录
2025-06-27 16:30:52,976 - INFO - Request Parameters - Page 5:
2025-06-27 16:30:52,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:52,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:53,648 - INFO - Response - Page 5:
2025-06-27 16:30:53,648 - INFO - 第 5 页获取到 50 条记录
2025-06-27 16:30:54,163 - INFO - Request Parameters - Page 6:
2025-06-27 16:30:54,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:54,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:54,929 - INFO - Response - Page 6:
2025-06-27 16:30:54,929 - INFO - 第 6 页获取到 50 条记录
2025-06-27 16:30:55,429 - INFO - Request Parameters - Page 7:
2025-06-27 16:30:55,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:55,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:56,101 - INFO - Response - Page 7:
2025-06-27 16:30:56,101 - INFO - 第 7 页获取到 50 条记录
2025-06-27 16:30:56,616 - INFO - Request Parameters - Page 8:
2025-06-27 16:30:56,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:56,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:57,288 - INFO - Response - Page 8:
2025-06-27 16:30:57,288 - INFO - 第 8 页获取到 50 条记录
2025-06-27 16:30:57,804 - INFO - Request Parameters - Page 9:
2025-06-27 16:30:57,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:57,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:58,491 - INFO - Response - Page 9:
2025-06-27 16:30:58,491 - INFO - 第 9 页获取到 50 条记录
2025-06-27 16:30:59,007 - INFO - Request Parameters - Page 10:
2025-06-27 16:30:59,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:30:59,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:30:59,726 - INFO - Response - Page 10:
2025-06-27 16:30:59,726 - INFO - 第 10 页获取到 50 条记录
2025-06-27 16:31:00,241 - INFO - Request Parameters - Page 11:
2025-06-27 16:31:00,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:00,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:00,804 - INFO - Response - Page 11:
2025-06-27 16:31:00,804 - INFO - 第 11 页获取到 20 条记录
2025-06-27 16:31:01,319 - INFO - 查询完成，共获取到 520 条记录
2025-06-27 16:31:01,319 - INFO - 获取到 520 条表单数据
2025-06-27 16:31:01,319 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-27 16:31:01,319 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 16:31:01,319 - INFO - 开始处理日期: 2025-06-25
2025-06-27 16:31:01,319 - INFO - Request Parameters - Page 1:
2025-06-27 16:31:01,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:01,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:02,007 - INFO - Response - Page 1:
2025-06-27 16:31:02,007 - INFO - 第 1 页获取到 50 条记录
2025-06-27 16:31:02,507 - INFO - Request Parameters - Page 2:
2025-06-27 16:31:02,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:02,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:03,210 - INFO - Response - Page 2:
2025-06-27 16:31:03,210 - INFO - 第 2 页获取到 50 条记录
2025-06-27 16:31:03,710 - INFO - Request Parameters - Page 3:
2025-06-27 16:31:03,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:03,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:04,413 - INFO - Response - Page 3:
2025-06-27 16:31:04,413 - INFO - 第 3 页获取到 50 条记录
2025-06-27 16:31:04,929 - INFO - Request Parameters - Page 4:
2025-06-27 16:31:04,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:04,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:05,585 - INFO - Response - Page 4:
2025-06-27 16:31:05,585 - INFO - 第 4 页获取到 50 条记录
2025-06-27 16:31:06,101 - INFO - Request Parameters - Page 5:
2025-06-27 16:31:06,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:06,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:06,788 - INFO - Response - Page 5:
2025-06-27 16:31:06,788 - INFO - 第 5 页获取到 50 条记录
2025-06-27 16:31:07,304 - INFO - Request Parameters - Page 6:
2025-06-27 16:31:07,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:07,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:08,023 - INFO - Response - Page 6:
2025-06-27 16:31:08,023 - INFO - 第 6 页获取到 50 条记录
2025-06-27 16:31:08,538 - INFO - Request Parameters - Page 7:
2025-06-27 16:31:08,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:08,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:09,335 - INFO - Response - Page 7:
2025-06-27 16:31:09,335 - INFO - 第 7 页获取到 50 条记录
2025-06-27 16:31:09,851 - INFO - Request Parameters - Page 8:
2025-06-27 16:31:09,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:09,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:10,476 - INFO - Response - Page 8:
2025-06-27 16:31:10,476 - INFO - 第 8 页获取到 50 条记录
2025-06-27 16:31:10,991 - INFO - Request Parameters - Page 9:
2025-06-27 16:31:10,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:10,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:11,710 - INFO - Response - Page 9:
2025-06-27 16:31:11,710 - INFO - 第 9 页获取到 50 条记录
2025-06-27 16:31:12,226 - INFO - Request Parameters - Page 10:
2025-06-27 16:31:12,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:12,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:12,913 - INFO - Response - Page 10:
2025-06-27 16:31:12,913 - INFO - 第 10 页获取到 49 条记录
2025-06-27 16:31:13,429 - INFO - 查询完成，共获取到 499 条记录
2025-06-27 16:31:13,429 - INFO - 获取到 499 条表单数据
2025-06-27 16:31:13,429 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-27 16:31:13,429 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 16:31:13,429 - INFO - 开始处理日期: 2025-06-26
2025-06-27 16:31:13,429 - INFO - Request Parameters - Page 1:
2025-06-27 16:31:13,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:13,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:14,116 - INFO - Response - Page 1:
2025-06-27 16:31:14,116 - INFO - 第 1 页获取到 50 条记录
2025-06-27 16:31:14,632 - INFO - Request Parameters - Page 2:
2025-06-27 16:31:14,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:14,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:15,429 - INFO - Response - Page 2:
2025-06-27 16:31:15,429 - INFO - 第 2 页获取到 50 条记录
2025-06-27 16:31:15,944 - INFO - Request Parameters - Page 3:
2025-06-27 16:31:15,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:15,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:16,632 - INFO - Response - Page 3:
2025-06-27 16:31:16,632 - INFO - 第 3 页获取到 50 条记录
2025-06-27 16:31:17,147 - INFO - Request Parameters - Page 4:
2025-06-27 16:31:17,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:17,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:17,804 - INFO - Response - Page 4:
2025-06-27 16:31:17,804 - INFO - 第 4 页获取到 50 条记录
2025-06-27 16:31:18,304 - INFO - Request Parameters - Page 5:
2025-06-27 16:31:18,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:18,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:18,944 - INFO - Response - Page 5:
2025-06-27 16:31:18,944 - INFO - 第 5 页获取到 50 条记录
2025-06-27 16:31:19,444 - INFO - Request Parameters - Page 6:
2025-06-27 16:31:19,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:19,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:20,163 - INFO - Response - Page 6:
2025-06-27 16:31:20,163 - INFO - 第 6 页获取到 50 条记录
2025-06-27 16:31:20,663 - INFO - Request Parameters - Page 7:
2025-06-27 16:31:20,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:20,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:21,319 - INFO - Response - Page 7:
2025-06-27 16:31:21,319 - INFO - 第 7 页获取到 50 条记录
2025-06-27 16:31:21,835 - INFO - Request Parameters - Page 8:
2025-06-27 16:31:21,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:21,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:22,522 - INFO - Response - Page 8:
2025-06-27 16:31:22,522 - INFO - 第 8 页获取到 50 条记录
2025-06-27 16:31:23,022 - INFO - Request Parameters - Page 9:
2025-06-27 16:31:23,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:23,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:23,601 - INFO - Response - Page 9:
2025-06-27 16:31:23,601 - INFO - 第 9 页获取到 31 条记录
2025-06-27 16:31:24,101 - INFO - 查询完成，共获取到 431 条记录
2025-06-27 16:31:24,101 - INFO - 获取到 431 条表单数据
2025-06-27 16:31:24,101 - INFO - 当前日期 2025-06-26 有 144 条MySQL数据需要处理
2025-06-27 16:31:24,101 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMZ
2025-06-27 16:31:24,601 - INFO - 更新表单数据成功: FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMZ
2025-06-27 16:31:24,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9120.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9120.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/11207d17827e4acc9b16f6c9dc6b2332.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Ny38%2FvHzcm%2BxWKQbUczjTPpm8JM%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1e28d4cb01b24038a456bab87461ac1a.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=xhyANlvFAm6fzCNQhRvsDImSczk%3D'}]
2025-06-27 16:31:24,601 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMQ
2025-06-27 16:31:25,194 - INFO - 更新表单数据成功: FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMQ
2025-06-27 16:31:25,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114.0, 'new_value': 2000.0}, {'field': 'total_amount', 'old_value': 114.0, 'new_value': 2000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e723a0fb3ce74101bc3acfd93116068c.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=CLKx9nS7Qc6DyAEEzUNY%2BsMPfp0%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9656491d517846ed94c7aaa5e28454b2.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=rtghx3XiNE6uAV9bBeARbCR98ZQ%3D'}]
2025-06-27 16:31:25,194 - INFO - 开始批量插入 14 条新记录
2025-06-27 16:31:25,366 - INFO - 批量插入响应状态码: 200
2025-06-27 16:31:25,366 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 08:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '683', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3D9AFB0B-FF1A-7A66-9114-10611322608D', 'x-acs-trace-id': '9843d22b9e648d1409b5b4eb9b8b86e0', 'etag': '6ITgmd7C7hinBpKHqUXlMNQ3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 16:31:25,366 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMZ', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM01', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM11', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM21', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM31', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM41', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM51', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM61', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM71', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM81', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM91', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMA1', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMB1', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMC1']}
2025-06-27 16:31:25,366 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-06-27 16:31:25,366 - INFO - 成功插入的数据ID: ['FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMZ', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM01', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM11', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM21', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM31', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM41', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM51', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM61', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM71', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM81', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECM91', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMA1', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMB1', 'FINST-SWC66P91PWNW2Z1J9WJOKCQ66FFZ23P3ZJECMC1']
2025-06-27 16:31:30,382 - INFO - 批量插入完成，共 14 条记录
2025-06-27 16:31:30,382 - INFO - 日期 2025-06-26 处理完成 - 更新: 2 条，插入: 14 条，错误: 0 条
2025-06-27 16:31:30,382 - INFO - 开始处理日期: 2025-06-27
2025-06-27 16:31:30,382 - INFO - Request Parameters - Page 1:
2025-06-27 16:31:30,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:31:30,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:31:30,835 - INFO - Response - Page 1:
2025-06-27 16:31:30,835 - INFO - 第 1 页获取到 1 条记录
2025-06-27 16:31:31,335 - INFO - 查询完成，共获取到 1 条记录
2025-06-27 16:31:31,335 - INFO - 获取到 1 条表单数据
2025-06-27 16:31:31,335 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-06-27 16:31:31,335 - INFO - 开始批量插入 1 条新记录
2025-06-27 16:31:31,491 - INFO - 批量插入响应状态码: 200
2025-06-27 16:31:31,491 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 08:31:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5FD6C3E5-27E8-7E69-A6B1-18E9BAF155BC', 'x-acs-trace-id': 'cf1cb03f3b27c61644a0a51eea5915eb', 'etag': '67pjxhnnUoyl0UOdeGTqFoA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 16:31:31,491 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91LQNW8B64BLLAFDUG7VBE3FF8ZJECMJ4']}
2025-06-27 16:31:31,491 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-27 16:31:31,491 - INFO - 成功插入的数据ID: ['FINST-7PF66N91LQNW8B64BLLAFDUG7VBE3FF8ZJECMJ4']
2025-06-27 16:31:36,507 - INFO - 批量插入完成，共 1 条记录
2025-06-27 16:31:36,507 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 16:31:36,507 - INFO - 数据同步完成！更新: 2 条，插入: 15 条，错误: 1 条
2025-06-27 16:32:36,522 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 16:32:36,522 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 16:32:36,522 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 16:32:36,663 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 473 条记录
2025-06-27 16:32:36,663 - INFO - 获取到 2 个日期需要处理: ['2025-06-26', '2025-06-27']
2025-06-27 16:32:36,678 - INFO - 开始处理日期: 2025-06-26
2025-06-27 16:32:36,678 - INFO - Request Parameters - Page 1:
2025-06-27 16:32:36,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:36,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:37,397 - INFO - Response - Page 1:
2025-06-27 16:32:37,397 - INFO - 第 1 页获取到 50 条记录
2025-06-27 16:32:37,897 - INFO - Request Parameters - Page 2:
2025-06-27 16:32:37,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:37,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:38,538 - INFO - Response - Page 2:
2025-06-27 16:32:38,538 - INFO - 第 2 页获取到 50 条记录
2025-06-27 16:32:39,053 - INFO - Request Parameters - Page 3:
2025-06-27 16:32:39,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:39,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:39,694 - INFO - Response - Page 3:
2025-06-27 16:32:39,694 - INFO - 第 3 页获取到 50 条记录
2025-06-27 16:32:40,209 - INFO - Request Parameters - Page 4:
2025-06-27 16:32:40,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:40,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:41,006 - INFO - Response - Page 4:
2025-06-27 16:32:41,006 - INFO - 第 4 页获取到 50 条记录
2025-06-27 16:32:41,522 - INFO - Request Parameters - Page 5:
2025-06-27 16:32:41,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:41,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:42,194 - INFO - Response - Page 5:
2025-06-27 16:32:42,194 - INFO - 第 5 页获取到 50 条记录
2025-06-27 16:32:42,709 - INFO - Request Parameters - Page 6:
2025-06-27 16:32:42,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:42,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:43,475 - INFO - Response - Page 6:
2025-06-27 16:32:43,475 - INFO - 第 6 页获取到 50 条记录
2025-06-27 16:32:43,991 - INFO - Request Parameters - Page 7:
2025-06-27 16:32:43,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:43,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:44,756 - INFO - Response - Page 7:
2025-06-27 16:32:44,756 - INFO - 第 7 页获取到 50 条记录
2025-06-27 16:32:45,272 - INFO - Request Parameters - Page 8:
2025-06-27 16:32:45,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:45,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:45,881 - INFO - Response - Page 8:
2025-06-27 16:32:45,881 - INFO - 第 8 页获取到 50 条记录
2025-06-27 16:32:46,381 - INFO - Request Parameters - Page 9:
2025-06-27 16:32:46,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:46,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:47,053 - INFO - Response - Page 9:
2025-06-27 16:32:47,053 - INFO - 第 9 页获取到 45 条记录
2025-06-27 16:32:47,553 - INFO - 查询完成，共获取到 445 条记录
2025-06-27 16:32:47,553 - INFO - 获取到 445 条表单数据
2025-06-27 16:32:47,553 - INFO - 当前日期 2025-06-26 有 460 条MySQL数据需要处理
2025-06-27 16:32:47,569 - INFO - 开始批量插入 15 条新记录
2025-06-27 16:32:47,756 - INFO - 批量插入响应状态码: 200
2025-06-27 16:32:47,756 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 08:32:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '21F3C115-B6FF-75B2-8FA9-DFBEDEF0C5A0', 'x-acs-trace-id': '8d0eb4fa2a1b500265e581ed8d554cf9', 'etag': '7hgi7aKK/xzh+rlRlg1gnDg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 16:32:47,756 - INFO - 批量插入响应体: {'result': ['FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECM74', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECM84', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECM94', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMA4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMB4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMC4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMD4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECME4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMF4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMG4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMH4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMI4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMJ4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMK4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECML4']}
2025-06-27 16:32:47,756 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-06-27 16:32:47,756 - INFO - 成功插入的数据ID: ['FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECM74', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECM84', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECM94', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMA4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMB4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMC4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMD4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECME4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMF4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMG4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMH4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMI4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMJ4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECMK4', 'FINST-EWE66Z91VQNWTRAAFOWJ8BX3SJX63R9V0KECML4']
2025-06-27 16:32:52,772 - INFO - 批量插入完成，共 15 条记录
2025-06-27 16:32:52,772 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 15 条，错误: 0 条
2025-06-27 16:32:52,772 - INFO - 开始处理日期: 2025-06-27
2025-06-27 16:32:52,772 - INFO - Request Parameters - Page 1:
2025-06-27 16:32:52,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 16:32:52,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 16:32:53,209 - INFO - Response - Page 1:
2025-06-27 16:32:53,209 - INFO - 第 1 页获取到 2 条记录
2025-06-27 16:32:53,709 - INFO - 查询完成，共获取到 2 条记录
2025-06-27 16:32:53,709 - INFO - 获取到 2 条表单数据
2025-06-27 16:32:53,709 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-06-27 16:32:53,709 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 16:32:53,709 - INFO - 数据同步完成！更新: 0 条，插入: 15 条，错误: 0 条
2025-06-27 16:32:53,709 - INFO - 同步完成
2025-06-27 19:30:34,487 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 19:30:34,487 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 19:30:34,487 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 19:30:34,643 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 153 条记录
2025-06-27 19:30:34,643 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-21', '2025-06-25', '2025-06-26', '2025-06-27']
2025-06-27 19:30:34,643 - INFO - 开始处理日期: 2025-06-20
2025-06-27 19:30:34,643 - INFO - Request Parameters - Page 1:
2025-06-27 19:30:34,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:34,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:42,787 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 02A5345E-73DA-790F-A983-4263BAB55C6A Response: {'code': 'ServiceUnavailable', 'requestid': '02A5345E-73DA-790F-A983-4263BAB55C6A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 02A5345E-73DA-790F-A983-4263BAB55C6A)
2025-06-27 19:30:42,787 - INFO - 开始处理日期: 2025-06-21
2025-06-27 19:30:42,787 - INFO - Request Parameters - Page 1:
2025-06-27 19:30:42,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:42,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:50,399 - INFO - Response - Page 1:
2025-06-27 19:30:50,399 - INFO - 第 1 页获取到 50 条记录
2025-06-27 19:30:50,915 - INFO - Request Parameters - Page 2:
2025-06-27 19:30:50,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:50,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:51,603 - INFO - Response - Page 2:
2025-06-27 19:30:51,603 - INFO - 第 2 页获取到 50 条记录
2025-06-27 19:30:52,119 - INFO - Request Parameters - Page 3:
2025-06-27 19:30:52,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:52,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:52,838 - INFO - Response - Page 3:
2025-06-27 19:30:52,838 - INFO - 第 3 页获取到 50 条记录
2025-06-27 19:30:53,354 - INFO - Request Parameters - Page 4:
2025-06-27 19:30:53,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:53,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:54,041 - INFO - Response - Page 4:
2025-06-27 19:30:54,041 - INFO - 第 4 页获取到 50 条记录
2025-06-27 19:30:54,541 - INFO - Request Parameters - Page 5:
2025-06-27 19:30:54,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:54,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:55,245 - INFO - Response - Page 5:
2025-06-27 19:30:55,245 - INFO - 第 5 页获取到 50 条记录
2025-06-27 19:30:55,745 - INFO - Request Parameters - Page 6:
2025-06-27 19:30:55,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:55,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:56,449 - INFO - Response - Page 6:
2025-06-27 19:30:56,449 - INFO - 第 6 页获取到 50 条记录
2025-06-27 19:30:56,964 - INFO - Request Parameters - Page 7:
2025-06-27 19:30:56,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:56,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:57,636 - INFO - Response - Page 7:
2025-06-27 19:30:57,636 - INFO - 第 7 页获取到 50 条记录
2025-06-27 19:30:58,152 - INFO - Request Parameters - Page 8:
2025-06-27 19:30:58,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:58,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:58,809 - INFO - Response - Page 8:
2025-06-27 19:30:58,809 - INFO - 第 8 页获取到 50 条记录
2025-06-27 19:30:59,325 - INFO - Request Parameters - Page 9:
2025-06-27 19:30:59,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:30:59,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:30:59,950 - INFO - Response - Page 9:
2025-06-27 19:30:59,950 - INFO - 第 9 页获取到 50 条记录
2025-06-27 19:31:00,466 - INFO - Request Parameters - Page 10:
2025-06-27 19:31:00,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:00,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:01,169 - INFO - Response - Page 10:
2025-06-27 19:31:01,169 - INFO - 第 10 页获取到 50 条记录
2025-06-27 19:31:01,685 - INFO - Request Parameters - Page 11:
2025-06-27 19:31:01,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:01,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:02,263 - INFO - Response - Page 11:
2025-06-27 19:31:02,263 - INFO - 第 11 页获取到 20 条记录
2025-06-27 19:31:02,779 - INFO - 查询完成，共获取到 520 条记录
2025-06-27 19:31:02,779 - INFO - 获取到 520 条表单数据
2025-06-27 19:31:02,779 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-27 19:31:02,779 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 19:31:02,779 - INFO - 开始处理日期: 2025-06-25
2025-06-27 19:31:02,779 - INFO - Request Parameters - Page 1:
2025-06-27 19:31:02,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:02,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:03,498 - INFO - Response - Page 1:
2025-06-27 19:31:03,498 - INFO - 第 1 页获取到 50 条记录
2025-06-27 19:31:03,998 - INFO - Request Parameters - Page 2:
2025-06-27 19:31:03,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:03,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:04,655 - INFO - Response - Page 2:
2025-06-27 19:31:04,655 - INFO - 第 2 页获取到 50 条记录
2025-06-27 19:31:05,171 - INFO - Request Parameters - Page 3:
2025-06-27 19:31:05,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:05,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:05,796 - INFO - Response - Page 3:
2025-06-27 19:31:05,796 - INFO - 第 3 页获取到 50 条记录
2025-06-27 19:31:06,296 - INFO - Request Parameters - Page 4:
2025-06-27 19:31:06,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:06,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:06,984 - INFO - Response - Page 4:
2025-06-27 19:31:06,984 - INFO - 第 4 页获取到 50 条记录
2025-06-27 19:31:07,500 - INFO - Request Parameters - Page 5:
2025-06-27 19:31:07,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:07,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:08,219 - INFO - Response - Page 5:
2025-06-27 19:31:08,219 - INFO - 第 5 页获取到 50 条记录
2025-06-27 19:31:08,719 - INFO - Request Parameters - Page 6:
2025-06-27 19:31:08,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:08,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:09,376 - INFO - Response - Page 6:
2025-06-27 19:31:09,376 - INFO - 第 6 页获取到 50 条记录
2025-06-27 19:31:09,891 - INFO - Request Parameters - Page 7:
2025-06-27 19:31:09,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:09,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:10,517 - INFO - Response - Page 7:
2025-06-27 19:31:10,517 - INFO - 第 7 页获取到 50 条记录
2025-06-27 19:31:11,032 - INFO - Request Parameters - Page 8:
2025-06-27 19:31:11,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:11,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:11,705 - INFO - Response - Page 8:
2025-06-27 19:31:11,705 - INFO - 第 8 页获取到 50 条记录
2025-06-27 19:31:12,220 - INFO - Request Parameters - Page 9:
2025-06-27 19:31:12,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:12,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:12,893 - INFO - Response - Page 9:
2025-06-27 19:31:12,893 - INFO - 第 9 页获取到 50 条记录
2025-06-27 19:31:13,393 - INFO - Request Parameters - Page 10:
2025-06-27 19:31:13,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:13,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:14,096 - INFO - Response - Page 10:
2025-06-27 19:31:14,096 - INFO - 第 10 页获取到 49 条记录
2025-06-27 19:31:14,612 - INFO - 查询完成，共获取到 499 条记录
2025-06-27 19:31:14,612 - INFO - 获取到 499 条表单数据
2025-06-27 19:31:14,612 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-27 19:31:14,612 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 19:31:14,612 - INFO - 开始处理日期: 2025-06-26
2025-06-27 19:31:14,612 - INFO - Request Parameters - Page 1:
2025-06-27 19:31:14,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:14,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:15,456 - INFO - Response - Page 1:
2025-06-27 19:31:15,456 - INFO - 第 1 页获取到 50 条记录
2025-06-27 19:31:15,972 - INFO - Request Parameters - Page 2:
2025-06-27 19:31:15,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:15,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:16,753 - INFO - Response - Page 2:
2025-06-27 19:31:16,753 - INFO - 第 2 页获取到 50 条记录
2025-06-27 19:31:17,254 - INFO - Request Parameters - Page 3:
2025-06-27 19:31:17,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:17,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:17,957 - INFO - Response - Page 3:
2025-06-27 19:31:17,957 - INFO - 第 3 页获取到 50 条记录
2025-06-27 19:31:18,473 - INFO - Request Parameters - Page 4:
2025-06-27 19:31:18,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:18,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:19,129 - INFO - Response - Page 4:
2025-06-27 19:31:19,129 - INFO - 第 4 页获取到 50 条记录
2025-06-27 19:31:19,645 - INFO - Request Parameters - Page 5:
2025-06-27 19:31:19,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:19,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:20,317 - INFO - Response - Page 5:
2025-06-27 19:31:20,317 - INFO - 第 5 页获取到 50 条记录
2025-06-27 19:31:20,818 - INFO - Request Parameters - Page 6:
2025-06-27 19:31:20,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:20,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:21,474 - INFO - Response - Page 6:
2025-06-27 19:31:21,474 - INFO - 第 6 页获取到 50 条记录
2025-06-27 19:31:21,990 - INFO - Request Parameters - Page 7:
2025-06-27 19:31:21,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:21,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:22,771 - INFO - Response - Page 7:
2025-06-27 19:31:22,771 - INFO - 第 7 页获取到 50 条记录
2025-06-27 19:31:23,287 - INFO - Request Parameters - Page 8:
2025-06-27 19:31:23,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:23,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:23,959 - INFO - Response - Page 8:
2025-06-27 19:31:23,959 - INFO - 第 8 页获取到 50 条记录
2025-06-27 19:31:24,475 - INFO - Request Parameters - Page 9:
2025-06-27 19:31:24,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:24,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:25,132 - INFO - Response - Page 9:
2025-06-27 19:31:25,132 - INFO - 第 9 页获取到 50 条记录
2025-06-27 19:31:25,648 - INFO - Request Parameters - Page 10:
2025-06-27 19:31:25,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:25,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:26,179 - INFO - Response - Page 10:
2025-06-27 19:31:26,179 - INFO - 第 10 页获取到 10 条记录
2025-06-27 19:31:26,695 - INFO - 查询完成，共获取到 460 条记录
2025-06-27 19:31:26,695 - INFO - 获取到 460 条表单数据
2025-06-27 19:31:26,695 - INFO - 当前日期 2025-06-26 有 144 条MySQL数据需要处理
2025-06-27 19:31:26,695 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMY
2025-06-27 19:31:27,195 - INFO - 更新表单数据成功: FINST-N3G66S81MQNWIJL4C6SCXA0WZ4YD2A8547ECMY
2025-06-27 19:31:27,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 1521.11}, {'field': 'total_amount', 'old_value': 1000.0, 'new_value': 1521.11}, {'field': 'order_count', 'old_value': 10, 'new_value': 36}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9bc14a811ce441a6886894c451d51bd9.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=OWFJkqnOPFri0%2FEm%2FmW8i6r013k%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/5d6279a8e2a5404d8a35d71c2cf6993f.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=ilMR9bdX0n4bKYDTf6A9J70yubE%3D'}]
2025-06-27 19:31:27,195 - INFO - 日期 2025-06-26 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-27 19:31:27,195 - INFO - 开始处理日期: 2025-06-27
2025-06-27 19:31:27,195 - INFO - Request Parameters - Page 1:
2025-06-27 19:31:27,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:31:27,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:31:27,664 - INFO - Response - Page 1:
2025-06-27 19:31:27,664 - INFO - 第 1 页获取到 2 条记录
2025-06-27 19:31:28,180 - INFO - 查询完成，共获取到 2 条记录
2025-06-27 19:31:28,180 - INFO - 获取到 2 条表单数据
2025-06-27 19:31:28,180 - INFO - 当前日期 2025-06-27 有 3 条MySQL数据需要处理
2025-06-27 19:31:28,180 - INFO - 开始批量插入 1 条新记录
2025-06-27 19:31:28,336 - INFO - 批量插入响应状态码: 200
2025-06-27 19:31:28,352 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 11:31:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C737D139-FAB2-734E-8B54-8D2790FA15FB', 'x-acs-trace-id': '28087a6e3d44bb5c36f2916776ee72b1', 'etag': '5j03mpCjOFyrQT99fdvRn8A9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 19:31:28,352 - INFO - 批量插入响应体: {'result': ['FINST-DUF66091W1OWNELWBS1DX4X4YE4Y3G2VEQECMV']}
2025-06-27 19:31:28,352 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-27 19:31:28,352 - INFO - 成功插入的数据ID: ['FINST-DUF66091W1OWNELWBS1DX4X4YE4Y3G2VEQECMV']
2025-06-27 19:31:33,369 - INFO - 批量插入完成，共 1 条记录
2025-06-27 19:31:33,369 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 19:31:33,369 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 1 条
2025-06-27 19:32:33,409 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 19:32:33,409 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 19:32:33,409 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 19:32:33,550 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 474 条记录
2025-06-27 19:32:33,550 - INFO - 获取到 2 个日期需要处理: ['2025-06-26', '2025-06-27']
2025-06-27 19:32:33,565 - INFO - 开始处理日期: 2025-06-26
2025-06-27 19:32:33,565 - INFO - Request Parameters - Page 1:
2025-06-27 19:32:33,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:33,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:34,237 - INFO - Response - Page 1:
2025-06-27 19:32:34,237 - INFO - 第 1 页获取到 50 条记录
2025-06-27 19:32:34,737 - INFO - Request Parameters - Page 2:
2025-06-27 19:32:34,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:34,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:35,472 - INFO - Response - Page 2:
2025-06-27 19:32:35,472 - INFO - 第 2 页获取到 50 条记录
2025-06-27 19:32:35,972 - INFO - Request Parameters - Page 3:
2025-06-27 19:32:35,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:35,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:36,801 - INFO - Response - Page 3:
2025-06-27 19:32:36,801 - INFO - 第 3 页获取到 50 条记录
2025-06-27 19:32:37,317 - INFO - Request Parameters - Page 4:
2025-06-27 19:32:37,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:37,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:37,926 - INFO - Response - Page 4:
2025-06-27 19:32:37,926 - INFO - 第 4 页获取到 50 条记录
2025-06-27 19:32:38,442 - INFO - Request Parameters - Page 5:
2025-06-27 19:32:38,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:38,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:39,145 - INFO - Response - Page 5:
2025-06-27 19:32:39,145 - INFO - 第 5 页获取到 50 条记录
2025-06-27 19:32:39,661 - INFO - Request Parameters - Page 6:
2025-06-27 19:32:39,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:39,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:40,333 - INFO - Response - Page 6:
2025-06-27 19:32:40,333 - INFO - 第 6 页获取到 50 条记录
2025-06-27 19:32:40,849 - INFO - Request Parameters - Page 7:
2025-06-27 19:32:40,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:40,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:41,459 - INFO - Response - Page 7:
2025-06-27 19:32:41,459 - INFO - 第 7 页获取到 50 条记录
2025-06-27 19:32:41,959 - INFO - Request Parameters - Page 8:
2025-06-27 19:32:41,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:41,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:42,600 - INFO - Response - Page 8:
2025-06-27 19:32:42,600 - INFO - 第 8 页获取到 50 条记录
2025-06-27 19:32:43,116 - INFO - Request Parameters - Page 9:
2025-06-27 19:32:43,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:43,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:43,835 - INFO - Response - Page 9:
2025-06-27 19:32:43,835 - INFO - 第 9 页获取到 50 条记录
2025-06-27 19:32:44,335 - INFO - Request Parameters - Page 10:
2025-06-27 19:32:44,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:44,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:44,851 - INFO - Response - Page 10:
2025-06-27 19:32:44,851 - INFO - 第 10 页获取到 10 条记录
2025-06-27 19:32:45,367 - INFO - 查询完成，共获取到 460 条记录
2025-06-27 19:32:45,367 - INFO - 获取到 460 条表单数据
2025-06-27 19:32:45,367 - INFO - 当前日期 2025-06-26 有 460 条MySQL数据需要处理
2025-06-27 19:32:45,382 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 19:32:45,382 - INFO - 开始处理日期: 2025-06-27
2025-06-27 19:32:45,382 - INFO - Request Parameters - Page 1:
2025-06-27 19:32:45,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 19:32:45,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 19:32:45,836 - INFO - Response - Page 1:
2025-06-27 19:32:45,836 - INFO - 第 1 页获取到 3 条记录
2025-06-27 19:32:46,351 - INFO - 查询完成，共获取到 3 条记录
2025-06-27 19:32:46,351 - INFO - 获取到 3 条表单数据
2025-06-27 19:32:46,351 - INFO - 当前日期 2025-06-27 有 3 条MySQL数据需要处理
2025-06-27 19:32:46,351 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 19:32:46,351 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 19:32:46,351 - INFO - 同步完成
2025-06-27 22:30:35,505 - INFO - 使用默认增量同步（当天更新数据）
2025-06-27 22:30:35,505 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-27 22:30:35,505 - INFO - 查询参数: ('2025-06-27',)
2025-06-27 22:30:35,661 - INFO - MySQL查询成功，增量数据（日期: 2025-06-27），共获取 244 条记录
2025-06-27 22:30:35,661 - INFO - 获取到 5 个日期需要处理: ['2025-06-20', '2025-06-21', '2025-06-25', '2025-06-26', '2025-06-27']
2025-06-27 22:30:35,661 - INFO - 开始处理日期: 2025-06-20
2025-06-27 22:30:35,661 - INFO - Request Parameters - Page 1:
2025-06-27 22:30:35,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:35,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750348800000, 1750435199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:43,789 - ERROR - 处理日期 2025-06-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A12016E1-1E4E-7A51-8430-9C02CA5079BF Response: {'code': 'ServiceUnavailable', 'requestid': 'A12016E1-1E4E-7A51-8430-9C02CA5079BF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A12016E1-1E4E-7A51-8430-9C02CA5079BF)
2025-06-27 22:30:43,789 - INFO - 开始处理日期: 2025-06-21
2025-06-27 22:30:43,789 - INFO - Request Parameters - Page 1:
2025-06-27 22:30:43,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:43,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:49,401 - INFO - Response - Page 1:
2025-06-27 22:30:49,401 - INFO - 第 1 页获取到 50 条记录
2025-06-27 22:30:49,917 - INFO - Request Parameters - Page 2:
2025-06-27 22:30:49,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:49,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:50,605 - INFO - Response - Page 2:
2025-06-27 22:30:50,605 - INFO - 第 2 页获取到 50 条记录
2025-06-27 22:30:51,105 - INFO - Request Parameters - Page 3:
2025-06-27 22:30:51,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:51,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:51,730 - INFO - Response - Page 3:
2025-06-27 22:30:51,730 - INFO - 第 3 页获取到 50 条记录
2025-06-27 22:30:52,246 - INFO - Request Parameters - Page 4:
2025-06-27 22:30:52,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:52,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:52,887 - INFO - Response - Page 4:
2025-06-27 22:30:52,887 - INFO - 第 4 页获取到 50 条记录
2025-06-27 22:30:53,403 - INFO - Request Parameters - Page 5:
2025-06-27 22:30:53,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:53,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:54,012 - INFO - Response - Page 5:
2025-06-27 22:30:54,012 - INFO - 第 5 页获取到 50 条记录
2025-06-27 22:30:54,512 - INFO - Request Parameters - Page 6:
2025-06-27 22:30:54,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:54,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:55,247 - INFO - Response - Page 6:
2025-06-27 22:30:55,247 - INFO - 第 6 页获取到 50 条记录
2025-06-27 22:30:55,747 - INFO - Request Parameters - Page 7:
2025-06-27 22:30:55,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:55,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:56,435 - INFO - Response - Page 7:
2025-06-27 22:30:56,435 - INFO - 第 7 页获取到 50 条记录
2025-06-27 22:30:56,935 - INFO - Request Parameters - Page 8:
2025-06-27 22:30:56,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:56,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:57,576 - INFO - Response - Page 8:
2025-06-27 22:30:57,576 - INFO - 第 8 页获取到 50 条记录
2025-06-27 22:30:58,092 - INFO - Request Parameters - Page 9:
2025-06-27 22:30:58,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:58,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:58,702 - INFO - Response - Page 9:
2025-06-27 22:30:58,702 - INFO - 第 9 页获取到 50 条记录
2025-06-27 22:30:59,217 - INFO - Request Parameters - Page 10:
2025-06-27 22:30:59,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:30:59,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:30:59,843 - INFO - Response - Page 10:
2025-06-27 22:30:59,843 - INFO - 第 10 页获取到 50 条记录
2025-06-27 22:31:00,343 - INFO - Request Parameters - Page 11:
2025-06-27 22:31:00,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:00,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:00,890 - INFO - Response - Page 11:
2025-06-27 22:31:00,890 - INFO - 第 11 页获取到 20 条记录
2025-06-27 22:31:01,390 - INFO - 查询完成，共获取到 520 条记录
2025-06-27 22:31:01,390 - INFO - 获取到 520 条表单数据
2025-06-27 22:31:01,390 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-27 22:31:01,390 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 22:31:01,390 - INFO - 开始处理日期: 2025-06-25
2025-06-27 22:31:01,390 - INFO - Request Parameters - Page 1:
2025-06-27 22:31:01,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:01,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:02,062 - INFO - Response - Page 1:
2025-06-27 22:31:02,062 - INFO - 第 1 页获取到 50 条记录
2025-06-27 22:31:02,578 - INFO - Request Parameters - Page 2:
2025-06-27 22:31:02,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:02,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:03,250 - INFO - Response - Page 2:
2025-06-27 22:31:03,250 - INFO - 第 2 页获取到 50 条记录
2025-06-27 22:31:03,750 - INFO - Request Parameters - Page 3:
2025-06-27 22:31:03,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:03,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:04,407 - INFO - Response - Page 3:
2025-06-27 22:31:04,407 - INFO - 第 3 页获取到 50 条记录
2025-06-27 22:31:04,923 - INFO - Request Parameters - Page 4:
2025-06-27 22:31:04,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:04,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:05,626 - INFO - Response - Page 4:
2025-06-27 22:31:05,626 - INFO - 第 4 页获取到 50 条记录
2025-06-27 22:31:06,142 - INFO - Request Parameters - Page 5:
2025-06-27 22:31:06,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:06,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:06,845 - INFO - Response - Page 5:
2025-06-27 22:31:06,845 - INFO - 第 5 页获取到 50 条记录
2025-06-27 22:31:07,346 - INFO - Request Parameters - Page 6:
2025-06-27 22:31:07,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:07,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:08,018 - INFO - Response - Page 6:
2025-06-27 22:31:08,018 - INFO - 第 6 页获取到 50 条记录
2025-06-27 22:31:08,534 - INFO - Request Parameters - Page 7:
2025-06-27 22:31:08,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:08,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:09,237 - INFO - Response - Page 7:
2025-06-27 22:31:09,237 - INFO - 第 7 页获取到 50 条记录
2025-06-27 22:31:09,737 - INFO - Request Parameters - Page 8:
2025-06-27 22:31:09,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:09,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:10,362 - INFO - Response - Page 8:
2025-06-27 22:31:10,362 - INFO - 第 8 页获取到 50 条记录
2025-06-27 22:31:10,863 - INFO - Request Parameters - Page 9:
2025-06-27 22:31:10,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:10,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:11,472 - INFO - Response - Page 9:
2025-06-27 22:31:11,472 - INFO - 第 9 页获取到 50 条记录
2025-06-27 22:31:11,988 - INFO - Request Parameters - Page 10:
2025-06-27 22:31:11,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:11,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:12,645 - INFO - Response - Page 10:
2025-06-27 22:31:12,645 - INFO - 第 10 页获取到 49 条记录
2025-06-27 22:31:13,160 - INFO - 查询完成，共获取到 499 条记录
2025-06-27 22:31:13,160 - INFO - 获取到 499 条表单数据
2025-06-27 22:31:13,160 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-27 22:31:13,160 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 22:31:13,160 - INFO - 开始处理日期: 2025-06-26
2025-06-27 22:31:13,160 - INFO - Request Parameters - Page 1:
2025-06-27 22:31:13,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:13,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:13,848 - INFO - Response - Page 1:
2025-06-27 22:31:13,848 - INFO - 第 1 页获取到 50 条记录
2025-06-27 22:31:14,364 - INFO - Request Parameters - Page 2:
2025-06-27 22:31:14,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:14,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:15,005 - INFO - Response - Page 2:
2025-06-27 22:31:15,005 - INFO - 第 2 页获取到 50 条记录
2025-06-27 22:31:15,505 - INFO - Request Parameters - Page 3:
2025-06-27 22:31:15,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:15,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:16,162 - INFO - Response - Page 3:
2025-06-27 22:31:16,162 - INFO - 第 3 页获取到 50 条记录
2025-06-27 22:31:16,662 - INFO - Request Parameters - Page 4:
2025-06-27 22:31:16,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:16,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:17,287 - INFO - Response - Page 4:
2025-06-27 22:31:17,287 - INFO - 第 4 页获取到 50 条记录
2025-06-27 22:31:17,803 - INFO - Request Parameters - Page 5:
2025-06-27 22:31:17,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:17,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:18,475 - INFO - Response - Page 5:
2025-06-27 22:31:18,475 - INFO - 第 5 页获取到 50 条记录
2025-06-27 22:31:18,975 - INFO - Request Parameters - Page 6:
2025-06-27 22:31:18,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:18,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:19,569 - INFO - Response - Page 6:
2025-06-27 22:31:19,569 - INFO - 第 6 页获取到 50 条记录
2025-06-27 22:31:20,085 - INFO - Request Parameters - Page 7:
2025-06-27 22:31:20,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:20,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:20,695 - INFO - Response - Page 7:
2025-06-27 22:31:20,695 - INFO - 第 7 页获取到 50 条记录
2025-06-27 22:31:21,210 - INFO - Request Parameters - Page 8:
2025-06-27 22:31:21,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:21,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:21,945 - INFO - Response - Page 8:
2025-06-27 22:31:21,945 - INFO - 第 8 页获取到 50 条记录
2025-06-27 22:31:22,461 - INFO - Request Parameters - Page 9:
2025-06-27 22:31:22,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:22,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:23,117 - INFO - Response - Page 9:
2025-06-27 22:31:23,117 - INFO - 第 9 页获取到 50 条记录
2025-06-27 22:31:23,618 - INFO - Request Parameters - Page 10:
2025-06-27 22:31:23,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:23,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:24,149 - INFO - Response - Page 10:
2025-06-27 22:31:24,149 - INFO - 第 10 页获取到 10 条记录
2025-06-27 22:31:24,665 - INFO - 查询完成，共获取到 460 条记录
2025-06-27 22:31:24,665 - INFO - 获取到 460 条表单数据
2025-06-27 22:31:24,665 - INFO - 当前日期 2025-06-26 有 145 条MySQL数据需要处理
2025-06-27 22:31:24,665 - INFO - 开始批量插入 1 条新记录
2025-06-27 22:31:24,821 - INFO - 批量插入响应状态码: 200
2025-06-27 22:31:24,821 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 14:31:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DCE2D1CD-DF86-74F3-8A48-9BC80B50E43C', 'x-acs-trace-id': '23be2884dc6e55874bbcca19e4b68940', 'etag': '6KFJakd3lB1+1sdMdtevCbg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 22:31:24,821 - INFO - 批量插入响应体: {'result': ['FINST-LLF66O71Z4OW469AB66IXDKWVVW13VD6UWECMV3']}
2025-06-27 22:31:24,821 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-27 22:31:24,821 - INFO - 成功插入的数据ID: ['FINST-LLF66O71Z4OW469AB66IXDKWVVW13VD6UWECMV3']
2025-06-27 22:31:29,839 - INFO - 批量插入完成，共 1 条记录
2025-06-27 22:31:29,839 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-27 22:31:29,839 - INFO - 开始处理日期: 2025-06-27
2025-06-27 22:31:29,839 - INFO - Request Parameters - Page 1:
2025-06-27 22:31:29,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:31:29,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:31:30,277 - INFO - Response - Page 1:
2025-06-27 22:31:30,277 - INFO - 第 1 页获取到 3 条记录
2025-06-27 22:31:30,777 - INFO - 查询完成，共获取到 3 条记录
2025-06-27 22:31:30,777 - INFO - 获取到 3 条表单数据
2025-06-27 22:31:30,777 - INFO - 当前日期 2025-06-27 有 90 条MySQL数据需要处理
2025-06-27 22:31:30,777 - INFO - 开始批量插入 87 条新记录
2025-06-27 22:31:31,027 - INFO - 批量插入响应状态码: 200
2025-06-27 22:31:31,027 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 14:31:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2382', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3D2F8C55-7691-70B3-945D-7FB65AB16273', 'x-acs-trace-id': '1f01e59fb140f4805cdb7273ae98235b', 'etag': '2AxZYR0XOoP+zT4aHGh8A2A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 22:31:31,027 - INFO - 批量插入响应体: {'result': ['FINST-KLF66HD1L8OWP61D7E6AM98BU4C6306BUWECM6', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM7', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM8', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM9', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMA', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMB', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMC', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMD', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECME', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMF', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMG', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMH', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMI', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMJ', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMK', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECML', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMM', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMN', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMO', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMP', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMQ', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMR', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMS', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMT', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMU', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMV', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMW', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMX', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMY', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMZ', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM01', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM11', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM21', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM31', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM41', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM51', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM61', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM71', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM81', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM91', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMA1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMB1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMC1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMD1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECME1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMF1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMG1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMH1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMI1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMJ1']}
2025-06-27 22:31:31,027 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-27 22:31:31,027 - INFO - 成功插入的数据ID: ['FINST-KLF66HD1L8OWP61D7E6AM98BU4C6306BUWECM6', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM7', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM8', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM9', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMA', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMB', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMC', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMD', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECME', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMF', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMG', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMH', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMI', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMJ', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMK', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECML', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMM', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMN', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMO', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMP', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMQ', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMR', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMS', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMT', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMU', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMV', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMW', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMX', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMY', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMZ', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM01', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM11', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM21', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM31', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM41', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM51', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM61', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM71', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM81', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM91', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMA1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMB1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMC1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMD1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECME1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMF1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMG1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMH1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMI1', 'FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECMJ1']
2025-06-27 22:31:36,248 - INFO - 批量插入响应状态码: 200
2025-06-27 22:31:36,248 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 14:31:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1788', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FFC96E95-0787-7549-B027-1315BC0100AF', 'x-acs-trace-id': '62f86c4f1764438679e4151a146cd5c4', 'etag': '1rHQD3Dgzscr11xIc3khdzQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 22:31:36,248 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMC1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMD1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECME1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMF1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMG1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMH1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMI1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMJ1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMK1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECML1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMM1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMN1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMO1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMP1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMQ1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMR1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMS1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMT1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMU1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMV1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMW1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMX1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMY1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMZ1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM02', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM12', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM22', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM32', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM42', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM52', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM62', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM72', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM82', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM92', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMA2', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMB2', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMC2']}
2025-06-27 22:31:36,248 - INFO - 批量插入表单数据成功，批次 2，共 37 条记录
2025-06-27 22:31:36,248 - INFO - 成功插入的数据ID: ['FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMC1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMD1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECME1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMF1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMG1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMH1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMI1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMJ1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMK1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECML1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06247FUWECMM1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMN1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMO1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMP1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMQ1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMR1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMS1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMT1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMU1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMV1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMW1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMX1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMY1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMZ1', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM02', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM12', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM22', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM32', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM42', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM52', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM62', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM72', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM82', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECM92', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMA2', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMB2', 'FINST-HJ966H81M6OWC7CMDMJJABRT5Y06257FUWECMC2']
2025-06-27 22:31:41,265 - INFO - 批量插入完成，共 87 条记录
2025-06-27 22:31:41,265 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 87 条，错误: 0 条
2025-06-27 22:31:41,265 - INFO - 数据同步完成！更新: 0 条，插入: 88 条，错误: 1 条
2025-06-27 22:32:41,305 - INFO - 开始同步昨天与今天的销售数据: 2025-06-26 至 2025-06-27
2025-06-27 22:32:41,305 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-27 22:32:41,305 - INFO - 查询参数: ('2025-06-26', '2025-06-27')
2025-06-27 22:32:41,445 - INFO - MySQL查询成功，时间段: 2025-06-26 至 2025-06-27，共获取 565 条记录
2025-06-27 22:32:41,445 - INFO - 获取到 2 个日期需要处理: ['2025-06-26', '2025-06-27']
2025-06-27 22:32:41,445 - INFO - 开始处理日期: 2025-06-26
2025-06-27 22:32:41,461 - INFO - Request Parameters - Page 1:
2025-06-27 22:32:41,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:41,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:42,180 - INFO - Response - Page 1:
2025-06-27 22:32:42,180 - INFO - 第 1 页获取到 50 条记录
2025-06-27 22:32:42,696 - INFO - Request Parameters - Page 2:
2025-06-27 22:32:42,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:42,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:43,321 - INFO - Response - Page 2:
2025-06-27 22:32:43,321 - INFO - 第 2 页获取到 50 条记录
2025-06-27 22:32:43,821 - INFO - Request Parameters - Page 3:
2025-06-27 22:32:43,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:43,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:44,431 - INFO - Response - Page 3:
2025-06-27 22:32:44,431 - INFO - 第 3 页获取到 50 条记录
2025-06-27 22:32:44,947 - INFO - Request Parameters - Page 4:
2025-06-27 22:32:44,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:44,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:45,588 - INFO - Response - Page 4:
2025-06-27 22:32:45,588 - INFO - 第 4 页获取到 50 条记录
2025-06-27 22:32:46,103 - INFO - Request Parameters - Page 5:
2025-06-27 22:32:46,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:46,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:46,760 - INFO - Response - Page 5:
2025-06-27 22:32:46,760 - INFO - 第 5 页获取到 50 条记录
2025-06-27 22:32:47,260 - INFO - Request Parameters - Page 6:
2025-06-27 22:32:47,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:47,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:47,885 - INFO - Response - Page 6:
2025-06-27 22:32:47,885 - INFO - 第 6 页获取到 50 条记录
2025-06-27 22:32:48,401 - INFO - Request Parameters - Page 7:
2025-06-27 22:32:48,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:48,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:49,011 - INFO - Response - Page 7:
2025-06-27 22:32:49,011 - INFO - 第 7 页获取到 50 条记录
2025-06-27 22:32:49,527 - INFO - Request Parameters - Page 8:
2025-06-27 22:32:49,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:49,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:50,183 - INFO - Response - Page 8:
2025-06-27 22:32:50,183 - INFO - 第 8 页获取到 50 条记录
2025-06-27 22:32:50,699 - INFO - Request Parameters - Page 9:
2025-06-27 22:32:50,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:50,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:51,371 - INFO - Response - Page 9:
2025-06-27 22:32:51,371 - INFO - 第 9 页获取到 50 条记录
2025-06-27 22:32:51,871 - INFO - Request Parameters - Page 10:
2025-06-27 22:32:51,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:51,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:52,403 - INFO - Response - Page 10:
2025-06-27 22:32:52,403 - INFO - 第 10 页获取到 11 条记录
2025-06-27 22:32:52,919 - INFO - 查询完成，共获取到 461 条记录
2025-06-27 22:32:52,919 - INFO - 获取到 461 条表单数据
2025-06-27 22:32:52,919 - INFO - 当前日期 2025-06-26 有 461 条MySQL数据需要处理
2025-06-27 22:32:52,934 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 22:32:52,934 - INFO - 开始处理日期: 2025-06-27
2025-06-27 22:32:52,934 - INFO - Request Parameters - Page 1:
2025-06-27 22:32:52,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:52,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:53,544 - INFO - Response - Page 1:
2025-06-27 22:32:53,544 - INFO - 第 1 页获取到 50 条记录
2025-06-27 22:32:54,060 - INFO - Request Parameters - Page 2:
2025-06-27 22:32:54,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 22:32:54,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 22:32:54,669 - INFO - Response - Page 2:
2025-06-27 22:32:54,669 - INFO - 第 2 页获取到 40 条记录
2025-06-27 22:32:55,185 - INFO - 查询完成，共获取到 90 条记录
2025-06-27 22:32:55,185 - INFO - 获取到 90 条表单数据
2025-06-27 22:32:55,185 - INFO - 当前日期 2025-06-27 有 90 条MySQL数据需要处理
2025-06-27 22:32:55,185 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 22:32:55,185 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 22:32:55,185 - INFO - 同步完成
