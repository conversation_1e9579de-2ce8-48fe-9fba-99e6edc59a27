2025-06-03 08:00:03,183 - INFO - ==================================================
2025-06-03 08:00:03,183 - INFO - 程序启动 - 版本 v1.0.0
2025-06-03 08:00:03,183 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250603.log
2025-06-03 08:00:03,183 - INFO - ==================================================
2025-06-03 08:00:03,183 - INFO - 程序入口点: __main__
2025-06-03 08:00:03,183 - INFO - ==================================================
2025-06-03 08:00:03,183 - INFO - 程序启动 - 版本 v1.0.1
2025-06-03 08:00:03,183 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250603.log
2025-06-03 08:00:03,183 - INFO - ==================================================
2025-06-03 08:00:03,526 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-03 08:00:03,526 - INFO - sales_data表已存在，无需创建
2025-06-03 08:00:03,526 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-03 08:00:03,526 - INFO - DataSyncManager初始化完成
2025-06-03 08:00:03,526 - INFO - 未提供日期参数，使用默认值
2025-06-03 08:00:03,526 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-03 08:00:03,526 - INFO - 开始综合数据同步流程...
2025-06-03 08:00:03,526 - INFO - 正在获取数衍平台日销售数据...
2025-06-03 08:00:03,526 - INFO - 查询数衍平台数据，时间段为: 2025-04-03, 2025-06-02
2025-06-03 08:00:03,526 - INFO - 正在获取********至********的数据
2025-06-03 08:00:03,526 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:03,526 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6D3BE9798E6C67FF32658EC01D0FBF17'}
2025-06-03 08:00:05,308 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:05,308 - INFO - 过滤后保留 434 条记录
2025-06-03 08:00:07,323 - INFO - 正在获取********至********的数据
2025-06-03 08:00:07,323 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:07,323 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '89041C8DE2A04090E80620E207B62D80'}
2025-06-03 08:00:08,651 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:08,667 - INFO - 过滤后保留 431 条记录
2025-06-03 08:00:10,667 - INFO - 正在获取********至********的数据
2025-06-03 08:00:10,667 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:10,667 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CC7BE484AD308407FCDBCAE88295D736'}
2025-06-03 08:00:11,792 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:11,808 - INFO - 过滤后保留 426 条记录
2025-06-03 08:00:13,823 - INFO - 正在获取********至********的数据
2025-06-03 08:00:13,823 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:13,823 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '84F66782354E8F0D710AE105F372B7D6'}
2025-06-03 08:00:15,042 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:15,042 - INFO - 过滤后保留 423 条记录
2025-06-03 08:00:17,058 - INFO - 正在获取********至********的数据
2025-06-03 08:00:17,058 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:17,058 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C041DB3AD7759DB868A5F2A8E03BB745'}
2025-06-03 08:00:18,229 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:18,229 - INFO - 过滤后保留 432 条记录
2025-06-03 08:00:20,229 - INFO - 正在获取********至********的数据
2025-06-03 08:00:20,229 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:20,229 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '06FFA9F3D73EB0994680545E5CD2CF17'}
2025-06-03 08:00:21,214 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:21,214 - INFO - 过滤后保留 434 条记录
2025-06-03 08:00:23,229 - INFO - 正在获取********至********的数据
2025-06-03 08:00:23,229 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:23,229 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4B78FDD96BD4C3BBDDC76216B95B91CF'}
2025-06-03 08:00:24,120 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:24,136 - INFO - 过滤后保留 424 条记录
2025-06-03 08:00:26,151 - INFO - 正在获取********至********的数据
2025-06-03 08:00:26,151 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:26,151 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1B94B45290DBB8DEC68F0EF86BEB1E2D'}
2025-06-03 08:00:26,964 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:26,964 - INFO - 过滤后保留 436 条记录
2025-06-03 08:00:28,979 - INFO - 正在获取********至********的数据
2025-06-03 08:00:28,979 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:28,979 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CE06C78351CA8810248B1FD2120AE54B'}
2025-06-03 08:00:29,792 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:29,792 - INFO - 过滤后保留 431 条记录
2025-06-03 08:00:31,792 - INFO - 正在获取********至********的数据
2025-06-03 08:00:31,792 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:31,792 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '434A4DBF5B1982035AE673914B9A610D'}
2025-06-03 08:00:32,604 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:32,604 - INFO - 过滤后保留 425 条记录
2025-06-03 08:00:34,620 - INFO - 正在获取********至********的数据
2025-06-03 08:00:34,620 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:34,620 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8BA8D12C93EF99C5D364908FEE8353D5'}
2025-06-03 08:00:35,511 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:35,511 - INFO - 过滤后保留 414 条记录
2025-06-03 08:00:37,511 - INFO - 正在获取********至********的数据
2025-06-03 08:00:37,511 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:37,511 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5A2B80B4C7F0DA4B910D7D79BF9B3B65'}
2025-06-03 08:00:38,307 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:38,307 - INFO - 过滤后保留 427 条记录
2025-06-03 08:00:40,323 - INFO - 正在获取********至********的数据
2025-06-03 08:00:40,323 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:40,323 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9D24AD45608C1DE8D68CCCF17E2B9010'}
2025-06-03 08:00:41,167 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:41,167 - INFO - 过滤后保留 428 条记录
2025-06-03 08:00:43,167 - INFO - 正在获取********至********的数据
2025-06-03 08:00:43,167 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:43,167 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2BB4BB9B492CF51FAEBEC6820EE76FF2'}
2025-06-03 08:00:44,011 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:44,011 - INFO - 过滤后保留 429 条记录
2025-06-03 08:00:46,026 - INFO - 正在获取********至********的数据
2025-06-03 08:00:46,026 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:46,026 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-03 08:00:46,854 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:46,870 - INFO - 过滤后保留 423 条记录
2025-06-03 08:00:48,870 - INFO - 正在获取********至********的数据
2025-06-03 08:00:48,870 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:48,870 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '93280739268269F59ABB3B0FFFB29E43'}
2025-06-03 08:00:49,636 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:49,651 - INFO - 过滤后保留 426 条记录
2025-06-03 08:00:51,667 - INFO - 正在获取********至********的数据
2025-06-03 08:00:51,667 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:51,667 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5B4C118455AC895BBAEE34CBA0A05B15'}
2025-06-03 08:00:52,464 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:52,479 - INFO - 过滤后保留 411 条记录
2025-06-03 08:00:54,479 - INFO - 正在获取********至********的数据
2025-06-03 08:00:54,479 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:54,479 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B4E65BB15C2953890675722A05D32A18'}
2025-06-03 08:00:55,245 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:55,261 - INFO - 过滤后保留 413 条记录
2025-06-03 08:00:57,276 - INFO - 正在获取********至********的数据
2025-06-03 08:00:57,276 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:00:57,276 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C49B0350764651025E0B36B9983B2AB8'}
2025-06-03 08:00:58,120 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:00:58,120 - INFO - 过滤后保留 432 条记录
2025-06-03 08:01:00,135 - INFO - 正在获取********至********的数据
2025-06-03 08:01:00,135 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:00,135 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5016709616586F528A6CAB29A863D51B'}
2025-06-03 08:01:01,010 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:01,010 - INFO - 过滤后保留 431 条记录
2025-06-03 08:01:03,026 - INFO - 正在获取********至********的数据
2025-06-03 08:01:03,026 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:03,026 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3C0396B541E23D6AB8BCAF4943D9ABD2'}
2025-06-03 08:01:03,792 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:03,792 - INFO - 过滤后保留 417 条记录
2025-06-03 08:01:05,792 - INFO - 正在获取********至********的数据
2025-06-03 08:01:05,792 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:05,792 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9B448E877970ADE6D4BD97D9A4B35899'}
2025-06-03 08:01:06,557 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:06,573 - INFO - 过滤后保留 420 条记录
2025-06-03 08:01:08,589 - INFO - 正在获取********至********的数据
2025-06-03 08:01:08,589 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:08,589 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '79316DB0A9AD37AB7DB5E360382E07EB'}
2025-06-03 08:01:09,307 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:09,307 - INFO - 过滤后保留 431 条记录
2025-06-03 08:01:11,323 - INFO - 正在获取********至********的数据
2025-06-03 08:01:11,323 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:11,323 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0102A78A2FB72CAEAF00A0525CE1875B'}
2025-06-03 08:01:12,104 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:12,104 - INFO - 过滤后保留 423 条记录
2025-06-03 08:01:14,120 - INFO - 正在获取********至********的数据
2025-06-03 08:01:14,120 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:14,120 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9D7DB168D58ECAD95D043EADF9A0F46A'}
2025-06-03 08:01:14,901 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:14,901 - INFO - 过滤后保留 416 条记录
2025-06-03 08:01:16,917 - INFO - 正在获取********至********的数据
2025-06-03 08:01:16,917 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:16,917 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0C539509A4B23930376291E685461C46'}
2025-06-03 08:01:17,635 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:17,651 - INFO - 过滤后保留 423 条记录
2025-06-03 08:01:19,667 - INFO - 正在获取********至********的数据
2025-06-03 08:01:19,667 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:19,667 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '662F0A24FDBF925B1A96DF34DC1B66FC'}
2025-06-03 08:01:20,417 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:20,417 - INFO - 过滤后保留 414 条记录
2025-06-03 08:01:22,417 - INFO - 正在获取********至********的数据
2025-06-03 08:01:22,417 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:22,417 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '14C69486C5650D4F14D528A04590893B'}
2025-06-03 08:01:23,213 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:23,213 - INFO - 过滤后保留 413 条记录
2025-06-03 08:01:25,229 - INFO - 正在获取********至********的数据
2025-06-03 08:01:25,229 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:25,229 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A7278CAF0B445519FB8307046603EE85'}
2025-06-03 08:01:25,932 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:25,948 - INFO - 过滤后保留 414 条记录
2025-06-03 08:01:27,963 - INFO - 正在获取********至********的数据
2025-06-03 08:01:27,963 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:27,963 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '502AD03A86FF341C7360E899B664ED70'}
2025-06-03 08:01:28,713 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:28,729 - INFO - 过滤后保留 413 条记录
2025-06-03 08:01:30,745 - INFO - 正在获取********至********的数据
2025-06-03 08:01:30,745 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-03 08:01:30,745 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '15F49305E9F4CC20D7951A91E44E5C12'}
2025-06-03 08:01:31,260 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-03 08:01:31,260 - INFO - 过滤后保留 199 条记录
2025-06-03 08:01:33,276 - INFO - 开始保存数据到SQLite数据库，共 12913 条记录待处理
2025-06-03 08:01:34,151 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-01
2025-06-03 08:01:34,151 - INFO - 变更字段: recommend_amount: 0.0 -> 9435.4, daily_bill_amount: 0.0 -> 9435.4
2025-06-03 08:01:34,151 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-06-01
2025-06-03 08:01:34,151 - INFO - 变更字段: amount: 8324 -> 8383, count: 169 -> 170, instore_amount: 7948.95 -> 8007.95, instore_count: 147 -> 148
2025-06-03 08:01:34,151 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-06-01
2025-06-03 08:01:34,151 - INFO - 变更字段: recommend_amount: 0.0 -> 4175.69, daily_bill_amount: 0.0 -> 4175.69
2025-06-03 08:01:34,151 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDN6L4LMS87Q2OV4FVC79K001465, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: amount: 1483 -> 1472
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: recommend_amount: 19630.82 -> 19601.82, amount: 19630 -> 19601, instore_amount: 19630.82 -> 19601.82
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-31
2025-06-03 08:01:34,166 - INFO - 变更字段: recommend_amount: 6898.33 -> 6903.33, amount: 6898 -> 6903, count: 322 -> 323, online_amount: 4583.7 -> 4588.7, online_count: 222 -> 223
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: amount: 18140 -> 18461, count: 81 -> 83, instore_amount: 19030.7 -> 19351.4, instore_count: 80 -> 82
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: recommend_amount: 0.0 -> 24879.33, daily_bill_amount: 0.0 -> 24879.33
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: amount: 61656 -> 61862, count: 371 -> 373, instore_amount: 45021.3 -> 45227.3, instore_count: 222 -> 224
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: recommend_amount: 0.0 -> 6194.67, daily_bill_amount: 0.0 -> 6194.67, amount: 3646 -> 5142, count: 258 -> 365, instore_amount: 3753.18 -> 5302.78, instore_count: 258 -> 365
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: amount: 5557 -> 5575, count: 436 -> 440, online_amount: 5353.42 -> 5371.82, online_count: 398 -> 402
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-31
2025-06-03 08:01:34,166 - INFO - 变更字段: amount: 5407 -> 5415, count: 459 -> 462, online_amount: 5577.94 -> 5585.44, online_count: 428 -> 431
2025-06-03 08:01:34,166 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-06-01
2025-06-03 08:01:34,166 - INFO - 变更字段: recommend_amount: 15998.8 -> 16548.3, amount: 15998 -> 16548, count: 65 -> 66, instore_amount: 16732.8 -> 17282.3, instore_count: 65 -> 66
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-01
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 10254.03 -> 10796.93, daily_bill_amount: 10254.03 -> 10796.93
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=AC07B70DB49845A8A52846E099EBC515, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 16632.72, daily_bill_amount: 0.0 -> 16632.72, amount: 564 -> 16632, count: 2 -> 51, instore_amount: 564.0 -> 16632.72, instore_count: 2 -> 51
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=A93C60005A8F41B092F6C5A8C21577CB, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: amount: 396 -> 4342, count: 3 -> 28, instore_amount: 396.8 -> 4342.8, instore_count: 3 -> 28
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=78655ECA4A32471AB7842F8DE2018120, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 7999.0 -> 14385.0, amount: 7999 -> 14385, count: 1 -> 4, instore_amount: 7999.0 -> 14385.0, instore_count: 1 -> 4
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6B7571A27AF84C73B4FC04CCBDB83D9B, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 10.8 -> 2712.94, amount: 10 -> 2712, count: 1 -> 24, instore_amount: 10.8 -> 2712.94, instore_count: 1 -> 24
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=3BB9A16AE8544997965802FAA3B83381, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 1909.57, daily_bill_amount: 0.0 -> 1909.57, amount: 164 -> 2012, count: 6 -> 58, instore_amount: 164.2 -> 2012.97, instore_count: 6 -> 58
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: amount: 256 -> 498, count: 1 -> 6, instore_amount: 256.0 -> 498.7, instore_count: 1 -> 6
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HVORJ88U7D2IL1AIB692RTFU8001185, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 6017.18, daily_bill_amount: 0.0 -> 6017.18, amount: 106 -> 2594, count: 2 -> 26, instore_amount: 106.0 -> 2528.6, instore_count: 2 -> 23, online_amount: 0.0 -> 131.5, online_count: 0 -> 3
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HV6P9SGUVGG9S36QDA69ST70J0015SA, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 14446.38, daily_bill_amount: 0.0 -> 14446.38, amount: 3027 -> 16659, count: 9 -> 85, instore_amount: 3027.0 -> 16659.8, instore_count: 9 -> 85
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 16016.4, daily_bill_amount: 0.0 -> 16016.4, amount: 1590 -> 6702, count: 17 -> 59, instore_amount: 431.0 -> 3455.67, instore_count: 4 -> 20, online_amount: 1159.4 -> 3247.0, online_count: 13 -> 39
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 6653.58, daily_bill_amount: 0.0 -> 6653.58, amount: 179 -> 2820, count: 1 -> 10, instore_amount: 179.0 -> 2820.9, instore_count: 1 -> 10
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HINIR4GO8E5NM5U25UDHUFEGO001L3K, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 375.18 -> 1431.21, amount: 375 -> 1431, count: 18 -> 75, instore_amount: 408.7 -> 1599.5, instore_count: 18 -> 75
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9UCRQKEOIF52ASKKUBQUNH0018FA, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 10901.0, daily_bill_amount: 0.0 -> 10901.0, amount: 158 -> 12902, count: 1 -> 46, instore_amount: 158.0 -> 12902.0, instore_count: 1 -> 46
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9S64J8E8R652ASKKUBQUMU0018EN, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 6138.96, daily_bill_amount: 0.0 -> 6138.96, amount: 198 -> 6289, count: 1 -> 28, instore_amount: 198.0 -> 5798.18, instore_count: 1 -> 25, online_amount: 0.0 -> 491.53, online_count: 0 -> 3
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9PGR3703D752ASKKUBQUM50018DU, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 1561.0 -> 6706.99, daily_bill_amount: 0.0 -> 6683.68, amount: 1561 -> 6706, count: 16 -> 81, instore_amount: 1561.0 -> 6597.62, instore_count: 16 -> 80, online_amount: 0.0 -> 109.37, online_count: 0 -> 1
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 465.28 -> 5544.18, daily_bill_amount: 0.0 -> 5544.18, amount: 465 -> 3773, count: 14 -> 99, instore_amount: 506.18 -> 3735.11, instore_count: 14 -> 92, online_amount: 0.0 -> 208.65, online_count: 0 -> 7
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE29HIJ7QK7Q2OV4FVC7F40014BL, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 300.8 -> 6262.1, daily_bill_amount: 0.0 -> 6262.1, amount: 300 -> 6262, count: 2 -> 6, instore_amount: 538.8 -> 6500.1, instore_count: 2 -> 6
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE0PKSKM647Q2OV4FVC7EC0014AT, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 5811.16, daily_bill_amount: 0.0 -> 5811.16, amount: 268 -> 5507, count: 3 -> 21, instore_amount: 268.0 -> 5507.16, instore_count: 3 -> 21
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 16762.0, daily_bill_amount: 0.0 -> 16762.0, amount: 1152 -> 7146, count: 3 -> 20, instore_amount: 1152.0 -> 7146.0, instore_count: 3 -> 20
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDVG061A0H7Q2OV4FVC7DO0014A9, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 635.2 -> 5280.8, amount: 635 -> 5280, count: 18 -> 90, instore_amount: 635.2 -> 5280.8, instore_count: 18 -> 90
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDV84M589R7Q2OV4FVC7DK0014A5, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: amount: 196 -> 1305, count: 2 -> 23, instore_amount: 196.0 -> 1305.89, instore_count: 2 -> 23
2025-06-03 08:01:34,182 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDV0D9P6J27Q2OV4FVC7DG0014A1, sale_time=2025-06-02
2025-06-03 08:01:34,182 - INFO - 变更字段: recommend_amount: 0.0 -> 2390.5, daily_bill_amount: 0.0 -> 2390.5, amount: 351 -> 2410, count: 15 -> 92, instore_amount: 212.0 -> 1620.7, instore_count: 8 -> 55, online_amount: 139.21 -> 822.3, online_count: 7 -> 37
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 3562.0, daily_bill_amount: 0.0 -> 3562.0, amount: 518 -> 3572, count: 36 -> 218, instore_amount: 184.77 -> 1881.64, instore_count: 13 -> 106, online_amount: 341.81 -> 1731.82, online_count: 23 -> 112
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDT8J32K6E7Q2OV4FVC7CK001495, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 169.0 -> 2229.0, daily_bill_amount: 0.0 -> 2229.0, amount: 169 -> 2229, count: 5 -> 49, instore_amount: 169.0 -> 2229.0, instore_count: 5 -> 49
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 7969.3, daily_bill_amount: 0.0 -> 7969.3, amount: 486 -> 3715, count: 34 -> 169, instore_amount: 502.76 -> 3768.34, instore_count: 34 -> 169
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDRLHCKFK97Q2OV4FVC7BS00148D, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 10835.32, daily_bill_amount: 0.0 -> 10835.32, amount: 864 -> 6738, count: 5 -> 31, instore_amount: 864.0 -> 6838.0, instore_count: 5 -> 31
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 2115.88, daily_bill_amount: 0.0 -> 2115.88, amount: 189 -> 996, count: 12 -> 58, instore_amount: 46.0 -> 294.59, instore_count: 3 -> 17, online_amount: 143.6 -> 742.0, online_count: 9 -> 41
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDOE3O1J9R7Q2OV4FVC7A800146P, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 1169.0, daily_bill_amount: 0.0 -> 1169.0, amount: 300 -> 708, count: 9 -> 29, instore_amount: 300.0 -> 708.0, instore_count: 9 -> 29
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDN6L4LMS87Q2OV4FVC79K001465, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 5841.2, daily_bill_amount: 0.0 -> 5841.2, amount: 543 -> 1334, count: 11 -> 30, instore_amount: 515.69 -> 1141.59, instore_count: 10 -> 25, online_amount: 28.24 -> 192.42, online_count: 1 -> 5
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 180.42 -> 2831.74, daily_bill_amount: 0.0 -> 2831.74, amount: 180 -> 701, count: 9 -> 26, instore_amount: 50.0 -> 190.8, instore_count: 1 -> 4, online_amount: 130.42 -> 510.55, online_count: 8 -> 22
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 2300.1 -> 10608.2, amount: 2300 -> 10608, count: 64 -> 289, instore_amount: 1931.2 -> 10023.3, instore_count: 57 -> 275, online_amount: 368.9 -> 584.9, online_count: 7 -> 14
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTE3QTM66G7Q2OVBN4IS8M001D4O, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 6644.4, daily_bill_amount: 0.0 -> 6644.4, amount: 239 -> 443, count: 12 -> 24, instore_amount: 254.19 -> 458.69, instore_count: 12 -> 24
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 17274.62, daily_bill_amount: 0.0 -> 17274.62, amount: 7946 -> 23353, count: 23 -> 101, instore_amount: 7946.0 -> 23353.3, instore_count: 23 -> 101
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: amount: 190 -> 836, count: 1 -> 3, instore_amount: 190.0 -> 836.0, instore_count: 1 -> 3
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTA575PTKJ7Q2OVBN4IS72001D34, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 2043.12 -> 5617.36, daily_bill_amount: 0.0 -> 5186.04, amount: 2043 -> 5617, count: 70 -> 183, instore_amount: 1731.62 -> 5096.36, instore_count: 60 -> 167, online_amount: 311.5 -> 521.0, online_count: 10 -> 16
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 397.3 -> 6198.7, daily_bill_amount: 0.0 -> 6198.7, amount: 397 -> 3898, count: 7 -> 56, instore_amount: 397.3 -> 4144.25, instore_count: 7 -> 56
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 78.0 -> 63543.37, daily_bill_amount: 0.0 -> 63543.37, amount: 78 -> -23541, count: 2 -> 67, instore_amount: 621.0 -> 38728.77, instore_count: 2 -> 67
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=A48FB8F8F66644F59454F3E73DFCEB92, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 7337.95, daily_bill_amount: 0.0 -> 7337.95, amount: 1687 -> 8003, count: 11 -> 59, instore_amount: 1671.0 -> 7880.0, instore_count: 10 -> 56, online_amount: 16.0 -> 123.0, online_count: 1 -> 3
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=7987833E6DE549FCBAC0AAF7A1D27E61, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 2260.48, daily_bill_amount: 0.0 -> 2260.48, amount: 52 -> 2142, count: 1 -> 16, instore_amount: 0.0 -> 2090.1, instore_count: 0 -> 15
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6FEB527E4B354363BD1420A3FF0FB3E3, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 3200.83, daily_bill_amount: 0.0 -> 3200.83, amount: 218 -> 2532, count: 8 -> 76, instore_amount: 218.1 -> 2456.63, instore_count: 8 -> 74, online_amount: 0.0 -> 75.8, online_count: 0 -> 2
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 2890.0, daily_bill_amount: 0.0 -> 2890.0, amount: 800 -> 2800, count: 2 -> 16, instore_amount: 800.0 -> 2800.0, instore_count: 2 -> 16
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 100.0 -> 3332.0, amount: 100 -> 3332, count: 2 -> 41, instore_amount: 100.0 -> 3332.0, instore_count: 2 -> 41
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERF6AAOTL42F6DB81RHAU001P76, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 3172.92, daily_bill_amount: 0.0 -> 3172.92, amount: 272 -> 2313, count: 3 -> 22, instore_amount: 272.0 -> 2313.5, instore_count: 3 -> 22
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 611.9 -> 2339.71, amount: 611 -> 2339, count: 34 -> 142, instore_amount: 86.0 -> 560.0, instore_count: 5 -> 26, online_amount: 525.9 -> 1813.71, online_count: 29 -> 116
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQ5682UCT42F6DB81RHA6001P6E, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 1374.07, daily_bill_amount: 0.0 -> 1374.07, amount: 82 -> 877, count: 5 -> 32, instore_amount: 82.6 -> 877.39, instore_count: 5 -> 32
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEPVAL5TUK42F6DB81RHA2001P6A, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 185.84 -> 2189.15, amount: 185 -> 2189, count: 9 -> 98, instore_amount: 89.51 -> 1169.65, instore_count: 5 -> 52, online_amount: 96.33 -> 1027.22, online_count: 4 -> 46
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEOTOKMGDD42F6DB81RH9E001P5M, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 2651.65, daily_bill_amount: 0.0 -> 2651.65, amount: 984 -> 2563, count: 44 -> 121, instore_amount: 761.0 -> 2288.5, instore_count: 36 -> 110, online_amount: 223.69 -> 275.19, online_count: 8 -> 11
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 314.85 -> 1905.6, amount: 314 -> 1905, count: 25 -> 125, instore_amount: 314.85 -> 1913.63, instore_count: 25 -> 125
2025-06-03 08:01:34,198 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMMCI75GD42F6DB81RH81001P49, sale_time=2025-06-02
2025-06-03 08:01:34,198 - INFO - 变更字段: recommend_amount: 0.0 -> 4105.9, daily_bill_amount: 0.0 -> 4105.9, amount: 829 -> 4105, count: 3 -> 24, instore_amount: 829.0 -> 4343.7, instore_count: 3 -> 24
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 10007.1, daily_bill_amount: 0.0 -> 10007.1, amount: 3018 -> 8962, count: 68 -> 249, instore_amount: 2766.8 -> 8499.39, instore_count: 65 -> 239, online_amount: 251.7 -> 555.4, online_count: 3 -> 10
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 2500.0, daily_bill_amount: 0.0 -> 2500.0, amount: 1064 -> 2383, count: 6 -> 18, instore_amount: 1064.6 -> 2383.3, instore_count: 6 -> 18
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKLLRRBCQ42F6DB81RH73001P3B, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 1142.6 -> 3497.4, daily_bill_amount: 0.0 -> 104.8, amount: 1142 -> 3497, count: 4 -> 15, instore_amount: 1142.6 -> 3497.4, instore_count: 4 -> 15
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 3178.12 -> 6653.24, amount: 3178 -> 6653, count: 35 -> 126, instore_amount: 2867.83 -> 5801.95, instore_count: 29 -> 106, online_amount: 310.29 -> 851.29, online_count: 6 -> 20
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: amount: 658 -> 3140, count: 42 -> 197, instore_amount: 27.0 -> 307.86, instore_count: 7 -> 39, online_amount: 631.7 -> 2946.0, online_count: 35 -> 158
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=101F34500A0D43DF833463DEFB95F423, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 13147.85, daily_bill_amount: 0.0 -> 13147.85, amount: 327 -> 11038, count: 4 -> 56, instore_amount: 327.0 -> 12065.0, instore_count: 4 -> 56
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 294.48 -> 2004.89, amount: 294 -> 2004, count: 18 -> 121, instore_amount: 110.0 -> 1094.3, instore_count: 6 -> 57, online_amount: 184.48 -> 973.23, online_count: 12 -> 64
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 446.6 -> 9801.56, amount: 446 -> 9801, count: 10 -> 231, instore_amount: 446.6 -> 9801.56, instore_count: 10 -> 231
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1IFRR231GUB4QN7QBECDAL3H28001J69, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 172.1 -> 20464.74, daily_bill_amount: 0.0 -> 20464.74, amount: 172 -> 8645, count: 6 -> 157, instore_amount: 172.1 -> 8667.4, instore_count: 6 -> 157
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 1559.0 -> 5717.81, amount: 1559 -> 5717, count: 90 -> 289, instore_amount: 219.9 -> 1506.89, instore_count: 8 -> 65, online_amount: 1362.1 -> 4282.82, online_count: 82 -> 224
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 16301.53, daily_bill_amount: 0.0 -> 16301.53, amount: 727 -> 7431, count: 26 -> 115, instore_amount: 308.97 -> 5731.38, instore_count: 7 -> 40, online_amount: 418.99 -> 1700.56, online_count: 19 -> 75
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBP5Q03GB0I86N3H2U106001F9C, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 16343.41, daily_bill_amount: 0.0 -> 16343.41, amount: 358 -> 16013, count: 3 -> 107, instore_amount: 282.73 -> 15059.25, instore_count: 1 -> 81, online_amount: 75.29 -> 954.03, online_count: 2 -> 26
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 23307.34, daily_bill_amount: 0.0 -> 23307.34, amount: 246 -> 6314, count: 2 -> 28, instore_amount: 246.2 -> 6229.1, instore_count: 2 -> 27, online_amount: 0.0 -> 85.6, online_count: 0 -> 1
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: amount: 437 -> 17939, count: 2 -> 89, instore_amount: 437.0 -> 17939.76, instore_count: 2 -> 89
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 16637.86, daily_bill_amount: 0.0 -> 16637.86, amount: 12 -> -15992, count: 1 -> 40, instore_amount: 0.0 -> 285.0, instore_count: 0 -> 15, online_amount: 12.0 -> 609.06, online_count: 1 -> 25
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAB2RFI0N0I86N3H2U1VD001F8J, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 694.0 -> 6769.0, amount: 694 -> 6769, count: 2 -> 38, instore_amount: 694.0 -> 6769.0, instore_count: 2 -> 38
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 26724.46, daily_bill_amount: 0.0 -> 26724.46, amount: 457 -> 26528, count: 15 -> 209, instore_amount: 150.0 -> 24424.25, instore_count: 1 -> 125, online_amount: 307.85 -> 2130.21, online_count: 14 -> 84
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 27573.18, daily_bill_amount: 0.0 -> 27573.18, amount: 2082 -> 27717, count: 33 -> 262, instore_amount: 799.3 -> 20490.86, instore_count: 10 -> 134, online_amount: 1283.17 -> 7294.21, online_count: 23 -> 128
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 42005.77, daily_bill_amount: 0.0 -> 42005.77, amount: 4095 -> 49101, count: 36 -> 337, instore_amount: 487.7 -> 29126.5, instore_count: 3 -> 147, online_amount: 3607.7 -> 20596.5, online_count: 33 -> 190
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE4GES1U770I86N3H2U1SF001F5L, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 1727.6 -> 45243.2, amount: 1727 -> 45243, count: 13 -> 314, instore_amount: 1727.6 -> 45243.2, instore_count: 13 -> 314
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE3PJ2TE9A0I86N3H2U1S3001F59, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 14233.53, daily_bill_amount: 0.0 -> 14233.53, amount: 1031 -> 19244, count: 7 -> 91, instore_amount: 714.0 -> 18072.8, instore_count: 2 -> 70, online_amount: 317.61 -> 1377.31, online_count: 5 -> 21
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 49697.99, daily_bill_amount: 0.0 -> 49697.99, amount: 2914 -> 55273, count: 33 -> 327, instore_amount: 803.6 -> 42465.32, instore_count: 3 -> 185, online_amount: 2111.2 -> 12829.9, online_count: 30 -> 142
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 21229.9, daily_bill_amount: 0.0 -> 21229.9, amount: 191 -> 19231, count: 2 -> 108, instore_amount: 191.9 -> 19472.1, instore_count: 2 -> 108
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 18378.14, daily_bill_amount: 0.0 -> 18378.14, amount: 1571 -> 18027, count: 17 -> 146, instore_amount: 0.0 -> 13763.21, instore_count: 0 -> 75, online_amount: 1571.7 -> 4264.4, online_count: 17 -> 71
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE1T4E94FK0I86N3H2U1R9001F4F, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 5266.27, daily_bill_amount: 0.0 -> 5266.27, amount: 261 -> 2474, count: 24 -> 158, instore_amount: 14.5 -> 675.9, instore_count: 1 -> 22, online_amount: 247.3 -> 1798.7, online_count: 23 -> 136
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0VODGC6J0I86N3H2U1QP001F3V, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: count: 2 -> 7, instore_count: 2 -> 7
2025-06-03 08:01:34,213 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-06-02
2025-06-03 08:01:34,213 - INFO - 变更字段: recommend_amount: 0.0 -> 4065.19, daily_bill_amount: 0.0 -> 4065.19, amount: 919 -> 5971, count: 36 -> 277, instore_amount: 264.5 -> 3185.87, instore_count: 17 -> 164, online_amount: 655.0 -> 2811.6, online_count: 19 -> 113
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 34937.62, daily_bill_amount: 0.0 -> 34937.62, amount: 1688 -> 34766, count: 58 -> 326, instore_amount: 651.45 -> 30955.18, instore_count: 6 -> 149, online_amount: 1069.32 -> 4023.37, online_count: 52 -> 177
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 4393.43, daily_bill_amount: 0.0 -> 4393.43, amount: 164 -> 2048, count: 25 -> 197, instore_amount: 184.8 -> 2113.13, instore_count: 25 -> 197
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDPJQDDVRC0I86N3H2U1MO001EVU, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 1058.0 -> 7842.0, amount: 1058 -> 7842, count: 4 -> 56, instore_amount: 1058.0 -> 8190.0, instore_count: 4 -> 56
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 1009.92 -> 18477.64, daily_bill_amount: 0.0 -> 16677.8, amount: 1009 -> 18477, count: 10 -> 87, instore_amount: 1009.92 -> 18477.64, instore_count: 10 -> 87
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 1734.0 -> 2692.0, amount: 1734 -> 2692, count: 1 -> 5, instore_amount: 1734.0 -> 2692.0, instore_count: 1 -> 5
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 54567.57, daily_bill_amount: 0.0 -> 54567.57, amount: 517 -> 5381, count: 5 -> 42, instore_amount: 282.7 -> 4633.17, instore_count: 3 -> 34, online_amount: 234.84 -> 748.78, online_count: 2 -> 8
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 464.0 -> 2761.8, amount: 464 -> 2761, count: 3 -> 12, instore_amount: 464.0 -> 2869.8, instore_count: 3 -> 12
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEQ2M9E710I86N3H2U1H1001EQ7, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: amount: 236 -> 414, count: 1 -> 3, online_amount: 236.7 -> 414.4, online_count: 1 -> 3
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 11336.6, daily_bill_amount: 0.0 -> 11336.6, amount: 669 -> 10503, count: 2 -> 16, instore_amount: 1048.6 -> 10882.4, instore_count: 2 -> 16
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDCE3748SO0I86N3H2U1FP001EOV, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 1008.0 -> 4496.0, amount: 1008 -> 4496, count: 1 -> 5, instore_amount: 1008.0 -> 4496.0, instore_count: 1 -> 5
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 1350.0 -> 4162.0, amount: 1350 -> 4162, count: 1 -> 4, instore_amount: 1350.0 -> 4162.0, instore_count: 1 -> 4
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 9472.2, daily_bill_amount: 0.0 -> 9472.2, amount: 498 -> 9221, count: 2 -> 22, instore_amount: 498.5 -> 9655.3, instore_count: 2 -> 22
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 25341.0, daily_bill_amount: 0.0 -> 25341.0, amount: 1411 -> 26071, count: 1 -> 14, instore_amount: 1411.0 -> 26071.0, instore_count: 1 -> 14
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE0JS3BF7R0I86N3H2U135001ECB, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 5267.26, daily_bill_amount: 0.0 -> 5267.26, amount: 291 -> 5480, count: 11 -> 188, instore_amount: 291.44 -> 5442.54, instore_count: 11 -> 187, online_amount: 0.0 -> 37.9, online_count: 0 -> 1
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 2942.57, daily_bill_amount: 0.0 -> 2942.57, amount: 588 -> 3329, count: 22 -> 126, instore_amount: 106.0 -> 1484.57, instore_count: 6 -> 55, online_amount: 482.24 -> 1932.7, online_count: 16 -> 71
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 10851.64, daily_bill_amount: 0.0 -> 10851.64, amount: 991 -> 9376, count: 18 -> 275, instore_amount: 737.6 -> 6057.12, instore_count: 12 -> 219, online_amount: 253.8 -> 3634.8, online_count: 6 -> 56
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: amount: 734 -> 4706, count: 56 -> 333, instore_amount: 32.0 -> 355.0, instore_count: 1 -> 27, online_amount: 728.12 -> 4486.84, online_count: 55 -> 306
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: amount: 869 -> 7261, count: 65 -> 394, instore_amount: 869.18 -> 7560.25, instore_count: 65 -> 390, online_amount: 0.0 -> 87.5, online_count: 0 -> 4
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSK489TE20I86N3H2U114001EAA, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 591.21 -> 4324.55, amount: 591 -> 4324, count: 12 -> 159, instore_amount: 555.11 -> 4059.45, instore_count: 11 -> 155, online_amount: 36.1 -> 265.1, online_count: 1 -> 4
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 8462.72, daily_bill_amount: 0.0 -> 8462.72, amount: 533 -> 4890, count: 24 -> 206, instore_amount: 56.0 -> 907.88, instore_count: 3 -> 53, online_amount: 492.6 -> 4018.0, online_count: 21 -> 153
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDS341MMSU0I86N3H2U10P001E9V, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 7583.34, daily_bill_amount: 0.0 -> 7583.34, amount: 487 -> 1444, count: 11 -> 43, instore_amount: 487.3 -> 1444.1, instore_count: 11 -> 43
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDRR6FJ7A60I86N3H2U10L001E9R, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 587.59 -> 4810.04, amount: 587 -> 4810, count: 33 -> 279, instore_amount: 305.87 -> 2311.7, instore_count: 19 -> 160, online_amount: 281.72 -> 2498.34, online_count: 14 -> 119
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQQG9THS10I86N3H2U108001E9E, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 13057.64, daily_bill_amount: 0.0 -> 13057.64, amount: 1315 -> 12989, count: 40 -> 398, instore_amount: 1315.9 -> 13023.0, instore_count: 40 -> 398
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: amount: 917 -> 6615, count: 62 -> 453, instore_amount: 636.79 -> 4938.4, instore_count: 37 -> 316, online_amount: 323.84 -> 1826.79, online_count: 25 -> 137
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQ4GA7M630I86N3H2U1VS001E92, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: amount: -29 -> -1300, count: 1 -> 6, instore_amount: 0.0 -> 280.0, instore_count: 0 -> 2, online_amount: 18.0 -> 87.0, online_count: 1 -> 4
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 5321.6 -> 23247.86, amount: 5321 -> 23247, count: 139 -> 510, instore_amount: 3846.5 -> 17791.36, instore_count: 94 -> 361, online_amount: 1475.1 -> 5456.5, online_count: 45 -> 149
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 37343.06, daily_bill_amount: 0.0 -> 37343.06, amount: 654 -> 24565, count: 20 -> 453, instore_amount: 582.6 -> 23381.37, instore_count: 14 -> 423, online_amount: 241.18 -> 1382.72, online_count: 6 -> 30
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 1121.8 -> 15191.8, daily_bill_amount: 0.0 -> 24627.9, amount: 1121 -> 15191, count: 4 -> 55, instore_amount: 1121.8 -> 15579.7, instore_count: 4 -> 55
2025-06-03 08:01:34,229 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-02
2025-06-03 08:01:34,229 - INFO - 变更字段: recommend_amount: 0.0 -> 17753.51, daily_bill_amount: 0.0 -> 17753.51, amount: 728 -> 7102, count: 15 -> 169, instore_amount: 662.52 -> 7135.26, instore_count: 14 -> 165, online_amount: 65.85 -> 141.3, online_count: 1 -> 4
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 15144.77, daily_bill_amount: 0.0 -> 15144.77, amount: 73 -> 1166, count: 1 -> 34, instore_amount: 73.5 -> 1314.48, instore_count: 1 -> 34
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDMFCJQF4F0I86N3H2U1UC001E7I, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 1520.62 -> 8449.93, amount: 1520 -> 8449, count: 16 -> 99, instore_amount: 597.7 -> 5805.76, instore_count: 7 -> 62, online_amount: 922.92 -> 2706.27, online_count: 9 -> 37
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 25005.46, daily_bill_amount: 0.0 -> 25005.46, amount: 751 -> 17776, count: 18 -> 114, instore_amount: 210.0 -> 16246.1, instore_count: 1 -> 71, online_amount: 541.6 -> 1553.4, online_count: 17 -> 43
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 795.6, daily_bill_amount: 0.0 -> 795.6, amount: 32 -> 828, count: 1 -> 9, instore_amount: 0.0 -> 198.0, instore_count: 0 -> 1, online_amount: 32.7 -> 630.3, online_count: 1 -> 8
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FHI5VTHC3RHRI7Q2OVAE57DT4001C39, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 21243.71, daily_bill_amount: 0.0 -> 21243.71, amount: 297 -> 30959, count: 3 -> 107, instore_amount: 160.0 -> 30822.0, instore_count: 2 -> 106
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 25640.49, daily_bill_amount: 0.0 -> 25640.49, amount: 651 -> 19643, count: 3 -> 95, instore_amount: 651.9 -> 19643.1, instore_count: 3 -> 95
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MRVUM0P77G7Q2OV78BKOG4001PUK, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 12209.63, daily_bill_amount: 0.0 -> 12209.63, amount: 199 -> 12447, count: 1 -> 74, instore_amount: 199.0 -> 11359.78, instore_count: 1 -> 62, online_amount: 0.0 -> 1146.2, online_count: 0 -> 12
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 22070.76, daily_bill_amount: 0.0 -> 22070.76, amount: 3213 -> 13585, count: 29 -> 112, instore_amount: 1045.5 -> 6476.8, instore_count: 7 -> 38, online_amount: 2167.8 -> 7109.0, online_count: 22 -> 74
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TQNGGEI53IKSIOCDI7U6001G57, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 476.38 -> 4114.55, daily_bill_amount: 0.0 -> 4249.67, amount: 476 -> 4114, count: 15 -> 116, instore_amount: 401.3 -> 3681.66, instore_count: 8 -> 80, online_amount: 75.08 -> 432.89, online_count: 7 -> 36
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: amount: 258 -> 10928, count: 2 -> 92, instore_amount: 258.0 -> 10928.48, instore_count: 2 -> 92
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 640.13 -> 3077.21, amount: 640 -> 3077, count: 33 -> 186, instore_amount: 364.54 -> 1940.24, instore_count: 13 -> 104, online_amount: 275.59 -> 1281.44, online_count: 20 -> 82
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 816.76 -> 5086.0, daily_bill_amount: 0.0 -> 5076.15, amount: 816 -> 5086, count: 47 -> 257, instore_amount: 287.2 -> 2414.77, instore_count: 14 -> 104, online_amount: 547.36 -> 2692.03, online_count: 33 -> 153
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: amount: 143 -> 1970, count: 2 -> 19, instore_amount: 0.0 -> 1327.3, instore_count: 0 -> 7, online_amount: 143.22 -> 666.62, online_count: 2 -> 12
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 18996.61, daily_bill_amount: 0.0 -> 18996.61, amount: 707 -> 21943, count: 27 -> 193, instore_amount: 435.59 -> 21508.31, instore_count: 14 -> 128, online_amount: 272.24 -> 1531.32, online_count: 13 -> 65
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 97.0 -> 1781.34, amount: 97 -> 1781, count: 5 -> 72, instore_amount: 97.0 -> 1532.84, instore_count: 5 -> 66, online_amount: 0.0 -> 248.5, online_count: 0 -> 6
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 1334.59 -> 7941.37, daily_bill_amount: 0.0 -> 6773.2, amount: 1334 -> 7941, count: 15 -> 102, instore_amount: 1218.0 -> 7631.2, instore_count: 9 -> 86, online_amount: 116.59 -> 310.17, online_count: 6 -> 16
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 13299.03, daily_bill_amount: 0.0 -> 13299.03, amount: 1800 -> 14837, count: 8 -> 104, instore_amount: 1800.0 -> 14837.0, instore_count: 8 -> 104
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2PLB45826AJB6QM8HA7Q0011QO, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 36333.05, daily_bill_amount: 0.0 -> 36333.05, amount: 481 -> 36333, count: 4 -> 163, instore_amount: 481.18 -> 36333.05, instore_count: 4 -> 163
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 12266.79, daily_bill_amount: 0.0 -> 12266.79, amount: 581 -> 12242, count: 5 -> 63, instore_amount: 338.0 -> 11542.0, instore_count: 2 -> 53, online_amount: 243.13 -> 736.79, online_count: 3 -> 10
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE29O5UH0D6AJB6QM8HA7I0011QG, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 61191.73, daily_bill_amount: 0.0 -> 61191.73, amount: 349 -> 55951, count: 1 -> 126, instore_amount: 349.0 -> 57291.0, instore_count: 1 -> 126
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 24590.31, daily_bill_amount: 0.0 -> 24590.31, amount: 553 -> 17754, count: 12 -> 65, instore_amount: 31.0 -> 18111.95, instore_count: 1 -> 46, online_amount: 522.2 -> 735.3, online_count: 11 -> 19
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRD2F2MCTBD6AJB6QM8HA650011P3, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 223.0 -> 40410.05, amount: 223 -> 40410, count: 2 -> 133, instore_amount: 223.0 -> 40410.05, instore_count: 2 -> 133
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRD0GVFB5C86AJB6QM8HA590011O7, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 28073.28, daily_bill_amount: 0.0 -> 28073.28, amount: 726 -> 27845, count: 9 -> 64, instore_amount: 454.0 -> 27720.74, instore_count: 5 -> 55, online_amount: 272.42 -> 477.69, online_count: 4 -> 9
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=E79F261889C1492982227C207062C267, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 0.0 -> 15375.95, daily_bill_amount: 0.0 -> 15375.95, amount: 934 -> 17528, count: 33 -> 435, instore_amount: 762.0 -> 16231.0, instore_count: 25 -> 378, online_amount: 214.7 -> 1409.97, online_count: 8 -> 57
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=C207460918D74AAAB2E154B47B74F863, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 255.31 -> 2441.99, amount: 255 -> 2441, count: 3 -> 25, instore_amount: 255.31 -> 2441.99, instore_count: 3 -> 25
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=41D3E4ED4CEA49C09C36DE504B997534, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 52.98 -> 598.24, amount: 52 -> 598, count: 2 -> 16, instore_amount: 52.98 -> 598.24, instore_count: 2 -> 16
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: amount: 747 -> 16586, count: 4 -> 96, instore_amount: 0.0 -> 13954.2, instore_count: 0 -> 72, online_amount: 747.41 -> 2890.17, online_count: 4 -> 24
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS34G4B697AV8LHQQGIDL001EJU, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 505.42 -> 26035.95, amount: 505 -> 26035, count: 9 -> 145, instore_amount: 318.0 -> 23854.0, instore_count: 1 -> 86, online_amount: 187.42 -> 2181.95, online_count: 8 -> 59
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRL782OM57AV8LHQQGIDF001EJO, sale_time=2025-06-02
2025-06-03 08:01:34,245 - INFO - 变更字段: recommend_amount: 296.27 -> 17629.74, daily_bill_amount: 0.0 -> 15638.03, amount: 296 -> 17629, count: 9 -> 102, instore_amount: 72.0 -> 15636.31, instore_count: 3 -> 68, online_amount: 224.27 -> 1993.43, online_count: 6 -> 34
2025-06-03 08:01:34,245 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRGKS2JA27AV8LHQQGIDC001EJL, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 1546.6 -> 36711.3, amount: 1546 -> 36711, count: 5 -> 144, instore_amount: 1546.6 -> 36711.3, instore_count: 5 -> 144
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRBVE2CQT7AV8LHQQGID9001EJI, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 0.0 -> 35335.47, daily_bill_amount: 0.0 -> 35335.47, amount: 2195 -> 28795, count: 11 -> 198, instore_amount: 258.8 -> 22427.9, instore_count: 2 -> 167, online_amount: 1936.9 -> 6367.9, online_count: 9 -> 31
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIR7B0BL957AV8LHQQGID6001EJF, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 0.0 -> 6468.3, daily_bill_amount: 0.0 -> 6468.3, amount: 2603 -> 3926, count: 58 -> 100, instore_amount: 1138.9 -> 2260.9, instore_count: 19 -> 43, online_amount: 1514.71 -> 2089.14, online_count: 39 -> 57
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIR2ETOHPO6E7AERKQ83K0001UO2, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 320.61 -> 1592.84, amount: 320 -> 1592, count: 11 -> 37, instore_amount: 262.5 -> 1386.5, instore_count: 9 -> 32, online_amount: 58.11 -> 266.34, online_count: 2 -> 5
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQTRHD5AN6E7AERKQ83JT001UNV, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 263.96 -> 2655.02, amount: 263 -> 2655, count: 5 -> 90, instore_amount: 84.16 -> 1255.97, instore_count: 2 -> 59, online_amount: 179.8 -> 1399.05, online_count: 3 -> 31
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQP8527T06E7AERKQ83JQ001UNS, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 0.0 -> 1338.42, daily_bill_amount: 0.0 -> 1338.42, amount: 20 -> 661, count: 3 -> 31, instore_amount: 0.0 -> 334.9, instore_count: 0 -> 11, online_amount: 20.58 -> 327.05, online_count: 3 -> 20
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQG1BD1C36E7AERKQ83JN001UNP, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 414.2 -> 2590.27, amount: 414 -> 2590, count: 30 -> 131, instore_amount: 414.2 -> 2621.1, instore_count: 30 -> 131
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQ716B84L7AV8LHQQGID0001EJ9, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 306.65 -> 3681.89, daily_bill_amount: 0.0 -> 1814.7, amount: 306 -> 3681, count: 10 -> 110, instore_amount: 203.0 -> 2117.87, instore_count: 5 -> 63, online_amount: 103.65 -> 1628.02, online_count: 5 -> 47
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: amount: 45 -> 102, count: 2 -> 4, instore_amount: 45.7 -> 102.9, instore_count: 2 -> 4
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 941.53 -> 3617.2, amount: 941 -> 3617, count: 30 -> 141, instore_amount: 108.9 -> 654.78, instore_count: 1 -> 17, online_amount: 832.63 -> 2976.38, online_count: 29 -> 124
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPGJC2SUF6E7AERKQ83J8001UNA, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 1730.57 -> 8944.91, amount: 1730 -> 8944, count: 23 -> 81, instore_amount: 1369.0 -> 7869.0, instore_count: 13 -> 61, online_amount: 361.57 -> 1099.81, online_count: 10 -> 20
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPC0VTE5M6E7AERKQ83J5001UN7, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: amount: 66 -> 486, count: 5 -> 21, online_amount: 66.34 -> 509.68, online_count: 5 -> 21
2025-06-03 08:01:34,260 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-02
2025-06-03 08:01:34,260 - INFO - 变更字段: recommend_amount: 0.0 -> 4748.04, daily_bill_amount: 0.0 -> 4748.04, amount: 88 -> 677, count: 4 -> 20, instore_amount: 88.3 -> 688.17, instore_count: 4 -> 20
2025-06-03 08:01:34,401 - INFO - SQLite数据保存完成，统计信息：
2025-06-03 08:01:34,401 - INFO - - 总记录数: 12913
2025-06-03 08:01:34,401 - INFO - - 成功插入: 54
2025-06-03 08:01:34,401 - INFO - - 成功更新: 160
2025-06-03 08:01:34,401 - INFO - - 无需更新: 12699
2025-06-03 08:01:34,401 - INFO - - 处理失败: 0
2025-06-03 08:01:39,807 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250603.xlsx
2025-06-03 08:01:39,823 - INFO - 成功获取数衍平台数据，共 12913 条记录
2025-06-03 08:01:39,823 - INFO - 正在更新SQLite月度汇总数据...
2025-06-03 08:01:39,823 - INFO - 月度数据sqllite清空完成
2025-06-03 08:01:40,104 - INFO - 月度汇总数据更新完成，处理了 1401 条汇总记录
2025-06-03 08:01:40,104 - INFO - 成功更新月度汇总数据，共 1401 条记录
2025-06-03 08:01:40,104 - INFO - 正在获取宜搭日销售表单数据...
2025-06-03 08:01:40,104 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-03 00:00:00 至 2025-06-02 23:59:59
2025-06-03 08:01:40,104 - INFO - 查询分段 1: 2025-04-03 至 2025-04-04
2025-06-03 08:01:40,104 - INFO - 查询日期范围: 2025-04-03 至 2025-04-04，使用分页查询，每页 100 条记录
2025-06-03 08:01:40,104 - INFO - Request Parameters - Page 1:
2025-06-03 08:01:40,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:01:40,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600104, 1743696000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:01:48,291 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 856C7D16-5A08-783D-8B7F-64071AE84890 Response: {'code': 'ServiceUnavailable', 'requestid': '856C7D16-5A08-783D-8B7F-64071AE84890', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-03 08:01:48,291 - ERROR - 服务不可用，将等待后重试
2025-06-03 08:01:48,291 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 856C7D16-5A08-783D-8B7F-64071AE84890 Response: {'code': 'ServiceUnavailable', 'requestid': '856C7D16-5A08-783D-8B7F-64071AE84890', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-03 08:01:48,291 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-06-03 08:01:54,323 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-06-03 08:01:54,323 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 856C7D16-5A08-783D-8B7F-64071AE84890 Response: {'code': 'ServiceUnavailable', 'requestid': '856C7D16-5A08-783D-8B7F-64071AE84890', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-06-03 08:02:04,338 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:04,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:04,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600104, 1743696000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:05,135 - INFO - API请求耗时: 797ms
2025-06-03 08:02:05,135 - INFO - Response - Page 1
2025-06-03 08:02:05,135 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:05,635 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:05,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:05,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600104, 1743696000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:06,291 - INFO - API请求耗时: 656ms
2025-06-03 08:02:06,291 - INFO - Response - Page 2
2025-06-03 08:02:06,291 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:06,791 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:06,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:06,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600104, 1743696000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:07,244 - INFO - API请求耗时: 453ms
2025-06-03 08:02:07,244 - INFO - Response - Page 3
2025-06-03 08:02:07,244 - INFO - 第 3 页获取到 17 条记录
2025-06-03 08:02:07,244 - INFO - 查询完成，共获取到 217 条记录
2025-06-03 08:02:07,244 - INFO - 分段 1 查询成功，获取到 217 条记录
2025-06-03 08:02:08,244 - INFO - 查询分段 2: 2025-04-05 至 2025-04-06
2025-06-03 08:02:08,244 - INFO - 查询日期范围: 2025-04-05 至 2025-04-06，使用分页查询，每页 100 条记录
2025-06-03 08:02:08,244 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:08,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:08,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400104, 1743868800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:08,963 - INFO - API请求耗时: 719ms
2025-06-03 08:02:08,963 - INFO - Response - Page 1
2025-06-03 08:02:08,963 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:09,479 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:09,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:09,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400104, 1743868800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:13,541 - INFO - API请求耗时: 4062ms
2025-06-03 08:02:13,541 - INFO - Response - Page 2
2025-06-03 08:02:13,541 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:14,041 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:14,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:14,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400104, 1743868800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:14,479 - INFO - API请求耗时: 437ms
2025-06-03 08:02:14,479 - INFO - Response - Page 3
2025-06-03 08:02:14,479 - INFO - 第 3 页获取到 15 条记录
2025-06-03 08:02:14,479 - INFO - 查询完成，共获取到 215 条记录
2025-06-03 08:02:14,479 - INFO - 分段 2 查询成功，获取到 215 条记录
2025-06-03 08:02:15,494 - INFO - 查询分段 3: 2025-04-07 至 2025-04-08
2025-06-03 08:02:15,494 - INFO - 查询日期范围: 2025-04-07 至 2025-04-08，使用分页查询，每页 100 条记录
2025-06-03 08:02:15,494 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:15,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:15,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200104, 1744041600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:16,229 - INFO - API请求耗时: 734ms
2025-06-03 08:02:16,229 - INFO - Response - Page 1
2025-06-03 08:02:16,229 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:16,729 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:16,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:16,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200104, 1744041600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:17,432 - INFO - API请求耗时: 703ms
2025-06-03 08:02:17,432 - INFO - Response - Page 2
2025-06-03 08:02:17,432 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:17,947 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:17,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:17,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200104, 1744041600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:18,416 - INFO - API请求耗时: 469ms
2025-06-03 08:02:18,416 - INFO - Response - Page 3
2025-06-03 08:02:18,416 - INFO - 第 3 页获取到 14 条记录
2025-06-03 08:02:18,416 - INFO - 查询完成，共获取到 214 条记录
2025-06-03 08:02:18,416 - INFO - 分段 3 查询成功，获取到 214 条记录
2025-06-03 08:02:19,432 - INFO - 查询分段 4: 2025-04-09 至 2025-04-10
2025-06-03 08:02:19,432 - INFO - 查询日期范围: 2025-04-09 至 2025-04-10，使用分页查询，每页 100 条记录
2025-06-03 08:02:19,432 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:19,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:19,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000104, 1744214400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:20,166 - INFO - API请求耗时: 734ms
2025-06-03 08:02:20,166 - INFO - Response - Page 1
2025-06-03 08:02:20,166 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:20,666 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:20,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:20,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000104, 1744214400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:21,338 - INFO - API请求耗时: 672ms
2025-06-03 08:02:21,338 - INFO - Response - Page 2
2025-06-03 08:02:21,338 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:21,854 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:21,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:21,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000104, 1744214400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:22,276 - INFO - API请求耗时: 422ms
2025-06-03 08:02:22,276 - INFO - Response - Page 3
2025-06-03 08:02:22,276 - INFO - 第 3 页获取到 11 条记录
2025-06-03 08:02:22,276 - INFO - 查询完成，共获取到 211 条记录
2025-06-03 08:02:22,276 - INFO - 分段 4 查询成功，获取到 211 条记录
2025-06-03 08:02:23,291 - INFO - 查询分段 5: 2025-04-11 至 2025-04-12
2025-06-03 08:02:23,291 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-03 08:02:23,291 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:23,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:23,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800104, 1744387200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:24,072 - INFO - API请求耗时: 781ms
2025-06-03 08:02:24,072 - INFO - Response - Page 1
2025-06-03 08:02:24,072 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:24,572 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:24,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:24,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800104, 1744387200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:25,369 - INFO - API请求耗时: 797ms
2025-06-03 08:02:25,369 - INFO - Response - Page 2
2025-06-03 08:02:25,369 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:25,885 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:25,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:25,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800104, 1744387200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:26,338 - INFO - API请求耗时: 453ms
2025-06-03 08:02:26,338 - INFO - Response - Page 3
2025-06-03 08:02:26,338 - INFO - 第 3 页获取到 21 条记录
2025-06-03 08:02:26,338 - INFO - 查询完成，共获取到 221 条记录
2025-06-03 08:02:26,338 - INFO - 分段 5 查询成功，获取到 221 条记录
2025-06-03 08:02:27,354 - INFO - 查询分段 6: 2025-04-13 至 2025-04-14
2025-06-03 08:02:27,354 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-03 08:02:27,354 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:27,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:27,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600104, 1744560000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:28,041 - INFO - API请求耗时: 687ms
2025-06-03 08:02:28,041 - INFO - Response - Page 1
2025-06-03 08:02:28,041 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:28,541 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:28,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:28,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600104, 1744560000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:29,322 - INFO - API请求耗时: 781ms
2025-06-03 08:02:29,322 - INFO - Response - Page 2
2025-06-03 08:02:29,338 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:29,854 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:29,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:29,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600104, 1744560000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:30,354 - INFO - API请求耗时: 500ms
2025-06-03 08:02:30,354 - INFO - Response - Page 3
2025-06-03 08:02:30,354 - INFO - 第 3 页获取到 15 条记录
2025-06-03 08:02:30,354 - INFO - 查询完成，共获取到 215 条记录
2025-06-03 08:02:30,354 - INFO - 分段 6 查询成功，获取到 215 条记录
2025-06-03 08:02:31,369 - INFO - 查询分段 7: 2025-04-15 至 2025-04-16
2025-06-03 08:02:31,369 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-03 08:02:31,369 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:31,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:31,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400104, 1744732800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:32,072 - INFO - API请求耗时: 703ms
2025-06-03 08:02:32,072 - INFO - Response - Page 1
2025-06-03 08:02:32,072 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:32,588 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:32,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:32,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400104, 1744732800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:33,447 - INFO - API请求耗时: 859ms
2025-06-03 08:02:33,447 - INFO - Response - Page 2
2025-06-03 08:02:33,447 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:33,947 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:33,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:33,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400104, 1744732800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:34,416 - INFO - API请求耗时: 469ms
2025-06-03 08:02:34,416 - INFO - Response - Page 3
2025-06-03 08:02:34,416 - INFO - 第 3 页获取到 10 条记录
2025-06-03 08:02:34,416 - INFO - 查询完成，共获取到 210 条记录
2025-06-03 08:02:34,416 - INFO - 分段 7 查询成功，获取到 210 条记录
2025-06-03 08:02:35,432 - INFO - 查询分段 8: 2025-04-17 至 2025-04-18
2025-06-03 08:02:35,432 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-03 08:02:35,432 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:35,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:35,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200104, 1744905600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:36,119 - INFO - API请求耗时: 687ms
2025-06-03 08:02:36,119 - INFO - Response - Page 1
2025-06-03 08:02:36,119 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:36,619 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:36,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:36,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200104, 1744905600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:37,416 - INFO - API请求耗时: 797ms
2025-06-03 08:02:37,416 - INFO - Response - Page 2
2025-06-03 08:02:37,416 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:37,916 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:37,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:37,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200104, 1744905600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:38,447 - INFO - API请求耗时: 531ms
2025-06-03 08:02:38,447 - INFO - Response - Page 3
2025-06-03 08:02:38,447 - INFO - 第 3 页获取到 17 条记录
2025-06-03 08:02:38,447 - INFO - 查询完成，共获取到 217 条记录
2025-06-03 08:02:38,447 - INFO - 分段 8 查询成功，获取到 217 条记录
2025-06-03 08:02:39,463 - INFO - 查询分段 9: 2025-04-19 至 2025-04-20
2025-06-03 08:02:39,463 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-03 08:02:39,463 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:39,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:39,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000104, 1745078400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:40,166 - INFO - API请求耗时: 703ms
2025-06-03 08:02:40,166 - INFO - Response - Page 1
2025-06-03 08:02:40,166 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:40,682 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:40,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:40,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000104, 1745078400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:41,494 - INFO - API请求耗时: 812ms
2025-06-03 08:02:41,494 - INFO - Response - Page 2
2025-06-03 08:02:41,494 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:41,994 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:41,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:41,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000104, 1745078400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:42,525 - INFO - API请求耗时: 531ms
2025-06-03 08:02:42,525 - INFO - Response - Page 3
2025-06-03 08:02:42,525 - INFO - 第 3 页获取到 16 条记录
2025-06-03 08:02:42,525 - INFO - 查询完成，共获取到 216 条记录
2025-06-03 08:02:42,525 - INFO - 分段 9 查询成功，获取到 216 条记录
2025-06-03 08:02:43,541 - INFO - 查询分段 10: 2025-04-21 至 2025-04-22
2025-06-03 08:02:43,541 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-03 08:02:43,541 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:43,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:43,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800104, 1745251200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:44,307 - INFO - API请求耗时: 766ms
2025-06-03 08:02:44,307 - INFO - Response - Page 1
2025-06-03 08:02:44,307 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:44,807 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:44,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:44,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800104, 1745251200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:45,557 - INFO - API请求耗时: 750ms
2025-06-03 08:02:45,557 - INFO - Response - Page 2
2025-06-03 08:02:45,557 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:46,072 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:46,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:46,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800104, 1745251200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:46,572 - INFO - API请求耗时: 500ms
2025-06-03 08:02:46,572 - INFO - Response - Page 3
2025-06-03 08:02:46,572 - INFO - 第 3 页获取到 12 条记录
2025-06-03 08:02:46,572 - INFO - 查询完成，共获取到 212 条记录
2025-06-03 08:02:46,572 - INFO - 分段 10 查询成功，获取到 212 条记录
2025-06-03 08:02:47,572 - INFO - 查询分段 11: 2025-04-23 至 2025-04-24
2025-06-03 08:02:47,572 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-03 08:02:47,572 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:47,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:47,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600104, 1745424000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:48,229 - INFO - API请求耗时: 656ms
2025-06-03 08:02:48,229 - INFO - Response - Page 1
2025-06-03 08:02:48,229 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:48,729 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:48,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:48,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600104, 1745424000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:49,432 - INFO - API请求耗时: 703ms
2025-06-03 08:02:49,432 - INFO - Response - Page 2
2025-06-03 08:02:49,432 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:49,932 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:49,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:49,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600104, 1745424000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:50,322 - INFO - API请求耗时: 391ms
2025-06-03 08:02:50,322 - INFO - Response - Page 3
2025-06-03 08:02:50,322 - INFO - 第 3 页获取到 7 条记录
2025-06-03 08:02:50,322 - INFO - 查询完成，共获取到 207 条记录
2025-06-03 08:02:50,322 - INFO - 分段 11 查询成功，获取到 207 条记录
2025-06-03 08:02:51,338 - INFO - 查询分段 12: 2025-04-25 至 2025-04-26
2025-06-03 08:02:51,338 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-03 08:02:51,338 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:51,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:51,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400104, 1745596800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:52,010 - INFO - API请求耗时: 672ms
2025-06-03 08:02:52,010 - INFO - Response - Page 1
2025-06-03 08:02:52,010 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:52,525 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:52,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:52,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400104, 1745596800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:53,369 - INFO - API请求耗时: 844ms
2025-06-03 08:02:53,369 - INFO - Response - Page 2
2025-06-03 08:02:53,369 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:53,885 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:53,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:53,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400104, 1745596800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:54,432 - INFO - API请求耗时: 547ms
2025-06-03 08:02:54,432 - INFO - Response - Page 3
2025-06-03 08:02:54,432 - INFO - 第 3 页获取到 18 条记录
2025-06-03 08:02:54,432 - INFO - 查询完成，共获取到 218 条记录
2025-06-03 08:02:54,432 - INFO - 分段 12 查询成功，获取到 218 条记录
2025-06-03 08:02:55,447 - INFO - 查询分段 13: 2025-04-27 至 2025-04-28
2025-06-03 08:02:55,447 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-03 08:02:55,447 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:55,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:55,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200104, 1745769600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:56,150 - INFO - API请求耗时: 703ms
2025-06-03 08:02:56,150 - INFO - Response - Page 1
2025-06-03 08:02:56,150 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:02:56,650 - INFO - Request Parameters - Page 2:
2025-06-03 08:02:56,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:56,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200104, 1745769600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:57,463 - INFO - API请求耗时: 812ms
2025-06-03 08:02:57,463 - INFO - Response - Page 2
2025-06-03 08:02:57,463 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:02:57,963 - INFO - Request Parameters - Page 3:
2025-06-03 08:02:57,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:57,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200104, 1745769600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:02:58,432 - INFO - API请求耗时: 469ms
2025-06-03 08:02:58,432 - INFO - Response - Page 3
2025-06-03 08:02:58,432 - INFO - 第 3 页获取到 12 条记录
2025-06-03 08:02:58,432 - INFO - 查询完成，共获取到 212 条记录
2025-06-03 08:02:58,432 - INFO - 分段 13 查询成功，获取到 212 条记录
2025-06-03 08:02:59,432 - INFO - 查询分段 14: 2025-04-29 至 2025-04-30
2025-06-03 08:02:59,432 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-03 08:02:59,432 - INFO - Request Parameters - Page 1:
2025-06-03 08:02:59,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:02:59,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000104, 1745942400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:00,119 - INFO - API请求耗时: 687ms
2025-06-03 08:03:00,119 - INFO - Response - Page 1
2025-06-03 08:03:00,119 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:00,635 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:00,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:00,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000104, 1745942400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:01,322 - INFO - API请求耗时: 687ms
2025-06-03 08:03:01,322 - INFO - Response - Page 2
2025-06-03 08:03:01,322 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:01,822 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:01,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:01,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000104, 1745942400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:02,307 - INFO - API请求耗时: 484ms
2025-06-03 08:03:02,307 - INFO - Response - Page 3
2025-06-03 08:03:02,307 - INFO - 第 3 页获取到 17 条记录
2025-06-03 08:03:02,307 - INFO - 查询完成，共获取到 217 条记录
2025-06-03 08:03:02,307 - INFO - 分段 14 查询成功，获取到 217 条记录
2025-06-03 08:03:03,307 - INFO - 查询分段 15: 2025-05-01 至 2025-05-02
2025-06-03 08:03:03,307 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-03 08:03:03,307 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:03,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:03,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800104, 1746115200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:04,025 - INFO - API请求耗时: 719ms
2025-06-03 08:03:04,025 - INFO - Response - Page 1
2025-06-03 08:03:04,025 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:04,541 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:04,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:04,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800104, 1746115200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:05,213 - INFO - API请求耗时: 672ms
2025-06-03 08:03:05,213 - INFO - Response - Page 2
2025-06-03 08:03:05,213 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:05,713 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:05,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:05,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800104, 1746115200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:06,150 - INFO - API请求耗时: 437ms
2025-06-03 08:03:06,150 - INFO - Response - Page 3
2025-06-03 08:03:06,150 - INFO - 第 3 页获取到 9 条记录
2025-06-03 08:03:06,150 - INFO - 查询完成，共获取到 209 条记录
2025-06-03 08:03:06,150 - INFO - 分段 15 查询成功，获取到 209 条记录
2025-06-03 08:03:07,166 - INFO - 查询分段 16: 2025-05-03 至 2025-05-04
2025-06-03 08:03:07,166 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-03 08:03:07,166 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:07,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:07,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600104, 1746288000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:07,838 - INFO - API请求耗时: 672ms
2025-06-03 08:03:07,838 - INFO - Response - Page 1
2025-06-03 08:03:07,838 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:08,338 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:08,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:08,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600104, 1746288000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:08,963 - INFO - API请求耗时: 625ms
2025-06-03 08:03:08,963 - INFO - Response - Page 2
2025-06-03 08:03:08,963 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:09,478 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:09,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:09,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600104, 1746288000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:09,947 - INFO - API请求耗时: 469ms
2025-06-03 08:03:09,947 - INFO - Response - Page 3
2025-06-03 08:03:09,947 - INFO - 第 3 页获取到 14 条记录
2025-06-03 08:03:09,947 - INFO - 查询完成，共获取到 214 条记录
2025-06-03 08:03:09,947 - INFO - 分段 16 查询成功，获取到 214 条记录
2025-06-03 08:03:10,963 - INFO - 查询分段 17: 2025-05-05 至 2025-05-06
2025-06-03 08:03:10,963 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-03 08:03:10,963 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:10,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:10,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400104, 1746460800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:11,713 - INFO - API请求耗时: 750ms
2025-06-03 08:03:11,713 - INFO - Response - Page 1
2025-06-03 08:03:11,713 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:12,213 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:12,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:12,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400104, 1746460800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:12,900 - INFO - API请求耗时: 688ms
2025-06-03 08:03:12,900 - INFO - Response - Page 2
2025-06-03 08:03:12,900 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:13,416 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:13,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:13,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400104, 1746460800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:13,806 - INFO - API请求耗时: 391ms
2025-06-03 08:03:13,806 - INFO - Response - Page 3
2025-06-03 08:03:13,806 - INFO - 第 3 页获取到 1 条记录
2025-06-03 08:03:13,806 - INFO - 查询完成，共获取到 201 条记录
2025-06-03 08:03:13,806 - INFO - 分段 17 查询成功，获取到 201 条记录
2025-06-03 08:03:14,822 - INFO - 查询分段 18: 2025-05-07 至 2025-05-08
2025-06-03 08:03:14,822 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-03 08:03:14,822 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:14,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:14,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200104, 1746633600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:15,697 - INFO - API请求耗时: 875ms
2025-06-03 08:03:15,697 - INFO - Response - Page 1
2025-06-03 08:03:15,697 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:16,197 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:16,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:16,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200104, 1746633600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:16,916 - INFO - API请求耗时: 719ms
2025-06-03 08:03:16,916 - INFO - Response - Page 2
2025-06-03 08:03:16,916 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:17,431 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:17,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:17,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200104, 1746633600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:17,822 - INFO - API请求耗时: 391ms
2025-06-03 08:03:17,822 - INFO - Response - Page 3
2025-06-03 08:03:17,822 - INFO - 第 3 页获取到 7 条记录
2025-06-03 08:03:17,822 - INFO - 查询完成，共获取到 207 条记录
2025-06-03 08:03:17,822 - INFO - 分段 18 查询成功，获取到 207 条记录
2025-06-03 08:03:18,838 - INFO - 查询分段 19: 2025-05-09 至 2025-05-10
2025-06-03 08:03:18,838 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-03 08:03:18,838 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:18,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:18,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000104, 1746806400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:19,556 - INFO - API请求耗时: 719ms
2025-06-03 08:03:19,556 - INFO - Response - Page 1
2025-06-03 08:03:19,556 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:20,056 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:20,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:20,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000104, 1746806400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:20,838 - INFO - API请求耗时: 781ms
2025-06-03 08:03:20,838 - INFO - Response - Page 2
2025-06-03 08:03:20,838 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:21,353 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:21,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:21,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000104, 1746806400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:21,853 - INFO - API请求耗时: 500ms
2025-06-03 08:03:21,853 - INFO - Response - Page 3
2025-06-03 08:03:21,853 - INFO - 第 3 页获取到 18 条记录
2025-06-03 08:03:21,853 - INFO - 查询完成，共获取到 218 条记录
2025-06-03 08:03:21,853 - INFO - 分段 19 查询成功，获取到 218 条记录
2025-06-03 08:03:22,869 - INFO - 查询分段 20: 2025-05-11 至 2025-05-12
2025-06-03 08:03:22,869 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-03 08:03:22,869 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:22,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:22,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800104, 1746979200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:23,510 - INFO - API请求耗时: 641ms
2025-06-03 08:03:23,510 - INFO - Response - Page 1
2025-06-03 08:03:23,510 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:24,010 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:24,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:24,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800104, 1746979200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:24,775 - INFO - API请求耗时: 766ms
2025-06-03 08:03:24,775 - INFO - Response - Page 2
2025-06-03 08:03:24,775 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:25,275 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:25,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:25,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800104, 1746979200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:25,713 - INFO - API请求耗时: 437ms
2025-06-03 08:03:25,713 - INFO - Response - Page 3
2025-06-03 08:03:25,713 - INFO - 第 3 页获取到 14 条记录
2025-06-03 08:03:25,713 - INFO - 查询完成，共获取到 214 条记录
2025-06-03 08:03:25,713 - INFO - 分段 20 查询成功，获取到 214 条记录
2025-06-03 08:03:26,728 - INFO - 查询分段 21: 2025-05-13 至 2025-05-14
2025-06-03 08:03:26,728 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-03 08:03:26,728 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:26,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:26,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600104, 1747152000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:27,384 - INFO - API请求耗时: 656ms
2025-06-03 08:03:27,384 - INFO - Response - Page 1
2025-06-03 08:03:27,384 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:27,885 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:27,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:27,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600104, 1747152000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:28,572 - INFO - API请求耗时: 687ms
2025-06-03 08:03:28,572 - INFO - Response - Page 2
2025-06-03 08:03:28,572 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:29,072 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:29,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:29,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600104, 1747152000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:29,572 - INFO - API请求耗时: 500ms
2025-06-03 08:03:29,572 - INFO - Response - Page 3
2025-06-03 08:03:29,572 - INFO - 第 3 页获取到 7 条记录
2025-06-03 08:03:29,572 - INFO - 查询完成，共获取到 207 条记录
2025-06-03 08:03:29,572 - INFO - 分段 21 查询成功，获取到 207 条记录
2025-06-03 08:03:30,572 - INFO - 查询分段 22: 2025-05-15 至 2025-05-16
2025-06-03 08:03:30,572 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-03 08:03:30,572 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:30,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:30,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400104, 1747324800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:31,306 - INFO - API请求耗时: 734ms
2025-06-03 08:03:31,306 - INFO - Response - Page 1
2025-06-03 08:03:31,306 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:31,806 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:31,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:31,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400104, 1747324800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:32,463 - INFO - API请求耗时: 656ms
2025-06-03 08:03:32,463 - INFO - Response - Page 2
2025-06-03 08:03:32,463 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:32,978 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:32,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:32,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400104, 1747324800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:33,478 - INFO - API请求耗时: 500ms
2025-06-03 08:03:33,494 - INFO - Response - Page 3
2025-06-03 08:03:33,494 - INFO - 第 3 页获取到 10 条记录
2025-06-03 08:03:33,494 - INFO - 查询完成，共获取到 210 条记录
2025-06-03 08:03:33,494 - INFO - 分段 22 查询成功，获取到 210 条记录
2025-06-03 08:03:34,494 - INFO - 查询分段 23: 2025-05-17 至 2025-05-18
2025-06-03 08:03:34,494 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-03 08:03:34,494 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:34,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:34,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200104, 1747497600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:35,166 - INFO - API请求耗时: 672ms
2025-06-03 08:03:35,166 - INFO - Response - Page 1
2025-06-03 08:03:35,166 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:35,666 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:35,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:35,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200104, 1747497600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:36,353 - INFO - API请求耗时: 688ms
2025-06-03 08:03:36,353 - INFO - Response - Page 2
2025-06-03 08:03:36,353 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:36,869 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:36,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:36,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200104, 1747497600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:37,384 - INFO - API请求耗时: 516ms
2025-06-03 08:03:37,384 - INFO - Response - Page 3
2025-06-03 08:03:37,384 - INFO - 第 3 页获取到 16 条记录
2025-06-03 08:03:37,384 - INFO - 查询完成，共获取到 216 条记录
2025-06-03 08:03:37,384 - INFO - 分段 23 查询成功，获取到 216 条记录
2025-06-03 08:03:38,400 - INFO - 查询分段 24: 2025-05-19 至 2025-05-20
2025-06-03 08:03:38,400 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-03 08:03:38,400 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:38,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:38,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000104, 1747670400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:39,056 - INFO - API请求耗时: 656ms
2025-06-03 08:03:39,056 - INFO - Response - Page 1
2025-06-03 08:03:39,056 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:39,556 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:39,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:39,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000104, 1747670400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:40,181 - INFO - API请求耗时: 625ms
2025-06-03 08:03:40,181 - INFO - Response - Page 2
2025-06-03 08:03:40,181 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:40,681 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:40,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:40,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000104, 1747670400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:41,150 - INFO - API请求耗时: 469ms
2025-06-03 08:03:41,150 - INFO - Response - Page 3
2025-06-03 08:03:41,150 - INFO - 第 3 页获取到 13 条记录
2025-06-03 08:03:41,150 - INFO - 查询完成，共获取到 213 条记录
2025-06-03 08:03:41,150 - INFO - 分段 24 查询成功，获取到 213 条记录
2025-06-03 08:03:42,166 - INFO - 查询分段 25: 2025-05-21 至 2025-05-22
2025-06-03 08:03:42,166 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-03 08:03:42,166 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:42,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:42,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800104, 1747843200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:42,838 - INFO - API请求耗时: 672ms
2025-06-03 08:03:42,838 - INFO - Response - Page 1
2025-06-03 08:03:42,838 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:43,353 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:43,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:43,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800104, 1747843200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:43,994 - INFO - API请求耗时: 641ms
2025-06-03 08:03:43,994 - INFO - Response - Page 2
2025-06-03 08:03:43,994 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:44,494 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:44,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:44,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800104, 1747843200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:44,931 - INFO - API请求耗时: 437ms
2025-06-03 08:03:44,931 - INFO - Response - Page 3
2025-06-03 08:03:44,931 - INFO - 第 3 页获取到 8 条记录
2025-06-03 08:03:44,931 - INFO - 查询完成，共获取到 208 条记录
2025-06-03 08:03:44,931 - INFO - 分段 25 查询成功，获取到 208 条记录
2025-06-03 08:03:45,931 - INFO - 查询分段 26: 2025-05-23 至 2025-05-24
2025-06-03 08:03:45,931 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-03 08:03:45,931 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:45,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:45,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600104, 1748016000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:46,681 - INFO - API请求耗时: 750ms
2025-06-03 08:03:46,681 - INFO - Response - Page 1
2025-06-03 08:03:46,681 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:47,197 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:47,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:47,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600104, 1748016000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:47,994 - INFO - API请求耗时: 797ms
2025-06-03 08:03:47,994 - INFO - Response - Page 2
2025-06-03 08:03:47,994 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:48,494 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:48,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:48,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600104, 1748016000104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:48,962 - INFO - API请求耗时: 469ms
2025-06-03 08:03:48,962 - INFO - Response - Page 3
2025-06-03 08:03:48,978 - INFO - 第 3 页获取到 15 条记录
2025-06-03 08:03:48,978 - INFO - 查询完成，共获取到 215 条记录
2025-06-03 08:03:48,978 - INFO - 分段 26 查询成功，获取到 215 条记录
2025-06-03 08:03:49,978 - INFO - 查询分段 27: 2025-05-25 至 2025-05-26
2025-06-03 08:03:49,978 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-03 08:03:49,978 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:49,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:49,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400104, 1748188800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:50,884 - INFO - API请求耗时: 906ms
2025-06-03 08:03:50,884 - INFO - Response - Page 1
2025-06-03 08:03:50,884 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:51,384 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:51,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:51,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400104, 1748188800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:52,150 - INFO - API请求耗时: 766ms
2025-06-03 08:03:52,166 - INFO - Response - Page 2
2025-06-03 08:03:52,166 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:52,666 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:52,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:52,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400104, 1748188800104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:53,025 - INFO - API请求耗时: 359ms
2025-06-03 08:03:53,025 - INFO - Response - Page 3
2025-06-03 08:03:53,025 - INFO - 第 3 页获取到 5 条记录
2025-06-03 08:03:53,025 - INFO - 查询完成，共获取到 205 条记录
2025-06-03 08:03:53,025 - INFO - 分段 27 查询成功，获取到 205 条记录
2025-06-03 08:03:54,041 - INFO - 查询分段 28: 2025-05-27 至 2025-05-28
2025-06-03 08:03:54,041 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-03 08:03:54,041 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:54,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:54,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200104, 1748361600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:54,697 - INFO - API请求耗时: 656ms
2025-06-03 08:03:54,697 - INFO - Response - Page 1
2025-06-03 08:03:54,697 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:55,212 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:55,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:55,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200104, 1748361600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:55,869 - INFO - API请求耗时: 656ms
2025-06-03 08:03:55,869 - INFO - Response - Page 2
2025-06-03 08:03:55,869 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:03:56,369 - INFO - Request Parameters - Page 3:
2025-06-03 08:03:56,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:56,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200104, 1748361600104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:56,728 - INFO - API请求耗时: 359ms
2025-06-03 08:03:56,728 - INFO - Response - Page 3
2025-06-03 08:03:56,728 - INFO - 第 3 页获取到 8 条记录
2025-06-03 08:03:56,728 - INFO - 查询完成，共获取到 208 条记录
2025-06-03 08:03:56,728 - INFO - 分段 28 查询成功，获取到 208 条记录
2025-06-03 08:03:57,744 - INFO - 查询分段 29: 2025-05-29 至 2025-05-30
2025-06-03 08:03:57,744 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-03 08:03:57,744 - INFO - Request Parameters - Page 1:
2025-06-03 08:03:57,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:57,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000104, 1748534400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:58,306 - INFO - API请求耗时: 547ms
2025-06-03 08:03:58,306 - INFO - Response - Page 1
2025-06-03 08:03:58,306 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:03:58,806 - INFO - Request Parameters - Page 2:
2025-06-03 08:03:58,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:03:58,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000104, 1748534400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:03:59,509 - INFO - API请求耗时: 703ms
2025-06-03 08:03:59,509 - INFO - Response - Page 2
2025-06-03 08:03:59,525 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:04:00,025 - INFO - Request Parameters - Page 3:
2025-06-03 08:04:00,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:04:00,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000104, 1748534400104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:04:00,416 - INFO - API请求耗时: 391ms
2025-06-03 08:04:00,416 - INFO - Response - Page 3
2025-06-03 08:04:00,416 - INFO - 第 3 页获取到 8 条记录
2025-06-03 08:04:00,416 - INFO - 查询完成，共获取到 208 条记录
2025-06-03 08:04:00,416 - INFO - 分段 29 查询成功，获取到 208 条记录
2025-06-03 08:04:01,431 - INFO - 查询分段 30: 2025-05-31 至 2025-06-01
2025-06-03 08:04:01,431 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-03 08:04:01,431 - INFO - Request Parameters - Page 1:
2025-06-03 08:04:01,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:04:01,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800104, 1748707200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:04:02,087 - INFO - API请求耗时: 656ms
2025-06-03 08:04:02,087 - INFO - Response - Page 1
2025-06-03 08:04:02,087 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:04:02,603 - INFO - Request Parameters - Page 2:
2025-06-03 08:04:02,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:04:02,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800104, 1748707200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:04:03,244 - INFO - API请求耗时: 641ms
2025-06-03 08:04:03,244 - INFO - Response - Page 2
2025-06-03 08:04:03,244 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:04:03,759 - INFO - Request Parameters - Page 3:
2025-06-03 08:04:03,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:04:03,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800104, 1748707200104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:04:04,166 - INFO - API请求耗时: 406ms
2025-06-03 08:04:04,166 - INFO - Response - Page 3
2025-06-03 08:04:04,166 - INFO - 第 3 页获取到 4 条记录
2025-06-03 08:04:04,166 - INFO - 查询完成，共获取到 204 条记录
2025-06-03 08:04:04,166 - INFO - 分段 30 查询成功，获取到 204 条记录
2025-06-03 08:04:05,181 - INFO - 查询分段 31: 2025-06-02 至 2025-06-02
2025-06-03 08:04:05,181 - INFO - 查询日期范围: 2025-06-02 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-03 08:04:05,181 - INFO - Request Parameters - Page 1:
2025-06-03 08:04:05,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:04:05,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600104, 1748879999104], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:04:05,509 - INFO - API请求耗时: 328ms
2025-06-03 08:04:05,509 - INFO - Response - Page 1
2025-06-03 08:04:05,509 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:04:05,509 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:04:05,509 - WARNING - 分段 31 查询返回空数据
2025-06-03 08:04:06,525 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 6359 条记录，失败 0 次
2025-06-03 08:04:06,525 - INFO - 成功获取宜搭日销售表单数据，共 6359 条记录
2025-06-03 08:04:06,525 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-03 08:04:06,525 - INFO - 开始对比和同步日销售数据...
2025-06-03 08:04:06,853 - INFO - 成功创建宜搭日销售数据索引，共 6359 条记录
2025-06-03 08:04:06,853 - INFO - 开始处理数衍数据，共 12913 条记录
2025-06-03 08:04:07,775 - INFO - 更新表单数据成功: FINST-PAB66N710QOV8VW1C4GQBC58Z2GM33OU2LEBM5O1
2025-06-03 08:04:07,775 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9435.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9435.4}]
2025-06-03 08:04:08,244 - INFO - 更新表单数据成功: FINST-3Z966E918CYVZYFMF58ZX8GZL5QK3HBX2LEBMS
2025-06-03 08:04:08,244 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_********, 变更字段: [{'field': 'amount', 'old_value': 8324.710000000001, 'new_value': 8383.710000000001}, {'field': 'count', 'old_value': 169, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 7948.95, 'new_value': 8007.95}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 148}]
2025-06-03 08:04:08,744 - INFO - 更新表单数据成功: FINST-3Z966E918CYVZYFMF58ZX8GZL5QK3IBX2LEBML1
2025-06-03 08:04:08,744 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4175.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4175.69}]
2025-06-03 08:04:09,244 - INFO - 更新表单数据成功: FINST-3Z966E918CYVZYFMF58ZX8GZL5QK3IBX2LEBMP1
2025-06-03 08:04:09,244 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_********, 变更字段: [{'field': 'amount', 'old_value': 1483.06, 'new_value': 1472.56}]
2025-06-03 08:04:09,728 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBME6
2025-06-03 08:04:09,728 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 19630.82, 'new_value': 19601.82}, {'field': 'amount', 'old_value': 19630.82, 'new_value': 19601.82}, {'field': 'instoreAmount', 'old_value': 19630.82, 'new_value': 19601.82}]
2025-06-03 08:04:10,369 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBMR6
2025-06-03 08:04:10,369 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_********, 变更字段: [{'field': 'amount', 'old_value': 18140.6, 'new_value': 18461.3}, {'field': 'count', 'old_value': 81, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 19030.7, 'new_value': 19351.4}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 82}]
2025-06-03 08:04:10,837 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBMT6
2025-06-03 08:04:10,837 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 24879.33}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24879.33}]
2025-06-03 08:04:11,306 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBM37
2025-06-03 08:04:11,306 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 61656.659999999996, 'new_value': 61862.659999999996}, {'field': 'count', 'old_value': 371, 'new_value': 373}, {'field': 'instoreAmount', 'old_value': 45021.3, 'new_value': 45227.3}, {'field': 'instoreCount', 'old_value': 222, 'new_value': 224}]
2025-06-03 08:04:11,806 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBM19
2025-06-03 08:04:11,806 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6194.67}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6194.67}, {'field': 'amount', 'old_value': 3646.58, 'new_value': 5142.179999999999}, {'field': 'count', 'old_value': 258, 'new_value': 365}, {'field': 'instoreAmount', 'old_value': 3753.18, 'new_value': 5302.78}, {'field': 'instoreCount', 'old_value': 258, 'new_value': 365}]
2025-06-03 08:04:12,212 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMNA
2025-06-03 08:04:12,212 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 5557.1900000000005, 'new_value': 5575.59}, {'field': 'count', 'old_value': 436, 'new_value': 440}, {'field': 'onlineAmount', 'old_value': 5353.42, 'new_value': 5371.82}, {'field': 'onlineCount', 'old_value': 398, 'new_value': 402}]
2025-06-03 08:04:12,650 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBM9B
2025-06-03 08:04:12,650 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 15998.8, 'new_value': 16548.3}, {'field': 'amount', 'old_value': 15998.8, 'new_value': 16548.3}, {'field': 'count', 'old_value': 65, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 16732.8, 'new_value': 17282.3}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 66}]
2025-06-03 08:04:13,103 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2GB53LEBM7C
2025-06-03 08:04:13,103 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10254.03, 'new_value': 10796.93}, {'field': 'dailyBillAmount', 'old_value': 10254.03, 'new_value': 10796.93}]
2025-06-03 08:04:13,244 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-03 08:04:13,744 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-03 08:04:16,759 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-03 08:04:17,212 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-03 08:04:20,228 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-03 08:04:20,650 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-03 08:04:23,665 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-03 08:04:24,212 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-03 08:04:27,228 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-03 08:04:27,618 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-03 08:04:30,634 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-03 08:04:31,040 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-03 08:04:34,056 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-03 08:04:34,509 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-03 08:04:37,525 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-03 08:04:38,025 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-03 08:04:41,040 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-03 08:04:41,384 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-03 08:04:44,400 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-03 08:04:44,806 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-03 08:04:47,821 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-03 08:04:48,212 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-03 08:04:51,228 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-03 08:04:51,665 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-03 08:04:54,681 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-03 08:04:55,118 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-03 08:04:58,134 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-03 08:04:58,603 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-03 08:05:01,618 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-03 08:05:02,103 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-03 08:05:05,118 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-03 08:05:05,509 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-03 08:05:08,524 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-03 08:05:08,962 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-03 08:05:11,978 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-03 08:05:12,556 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-03 08:05:15,571 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-03 08:05:15,978 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-03 08:05:18,993 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-03 08:05:19,415 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-03 08:05:22,431 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-03 08:05:22,853 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-03 08:05:25,868 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-03 08:05:26,368 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-03 08:05:29,384 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-03 08:05:29,931 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-03 08:05:32,962 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-03 08:05:33,368 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-03 08:05:36,384 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-03 08:05:36,774 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-03 08:05:39,790 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-03 08:05:40,290 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-03 08:05:43,305 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-03 08:05:43,712 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-03 08:05:46,712 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-03 08:05:47,149 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-03 08:05:50,165 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-03 08:05:50,665 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-03 08:05:53,680 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-03 08:05:54,118 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-03 08:05:57,134 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-03 08:05:57,665 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-03 08:06:00,680 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-03 08:06:01,149 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-03 08:06:04,165 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-03 08:06:04,571 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-03 08:06:07,587 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-03 08:06:07,977 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-03 08:06:10,993 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-03 08:06:11,430 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-03 08:06:14,446 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-03 08:06:14,883 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-03 08:06:17,899 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-03 08:06:18,321 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-03 08:06:21,336 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-03 08:06:21,868 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-03 08:06:24,883 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-03 08:06:25,368 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-03 08:06:28,383 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-03 08:06:28,774 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-03 08:06:31,790 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-03 08:06:32,180 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-03 08:06:35,196 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-03 08:06:35,633 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-03 08:06:38,649 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-03 08:06:38,993 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-03 08:06:41,993 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-03 08:06:42,430 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-03 08:06:45,446 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-03 08:06:45,852 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-03 08:06:48,868 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-03 08:06:49,336 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-03 08:06:52,352 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-03 08:06:52,805 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-03 08:06:55,821 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-03 08:06:56,243 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-03 08:06:59,258 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-03 08:06:59,758 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-03 08:07:02,774 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-03 08:07:03,227 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-03 08:07:06,242 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-03 08:07:06,696 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-03 08:07:09,711 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-03 08:07:10,227 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-03 08:07:13,242 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-03 08:07:13,696 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-03 08:07:16,711 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-03 08:07:17,133 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-03 08:07:20,149 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-03 08:07:20,617 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-03 08:07:23,633 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-03 08:07:24,149 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-03 08:07:27,164 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-03 08:07:27,633 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-03 08:07:30,649 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-03 08:07:31,086 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-03 08:07:34,102 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-03 08:07:34,555 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-03 08:07:37,570 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-03 08:07:37,992 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-03 08:07:41,008 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-03 08:07:41,461 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-03 08:07:44,477 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-03 08:07:44,852 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-03 08:07:47,867 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-03 08:07:48,289 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-03 08:07:51,305 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-03 08:07:51,695 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-03 08:07:54,695 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-03 08:07:55,180 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-03 08:07:58,195 - INFO - 正在批量插入每日数据，批次 66/66，共 54 条记录
2025-06-03 08:07:58,555 - INFO - 批量插入每日数据成功，批次 66，54 条记录
2025-06-03 08:08:01,570 - INFO - 批量插入每日数据完成: 总计 6554 条，成功 6554 条，失败 0 条
2025-06-03 08:08:01,570 - INFO - 批量插入日销售数据完成，共 6554 条记录
2025-06-03 08:08:01,570 - INFO - 日销售数据同步完成！更新: 12 条，插入: 6554 条，错误: 0 条，跳过: 6347 条
2025-06-03 08:08:01,570 - INFO - 正在获取宜搭月销售表单数据...
2025-06-03 08:08:01,570 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-03 08:08:01,570 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-03 08:08:01,570 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-03 08:08:01,570 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:01,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:01,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:02,336 - INFO - API请求耗时: 766ms
2025-06-03 08:08:02,336 - INFO - Response - Page 1
2025-06-03 08:08:02,336 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:02,336 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:02,336 - WARNING - 月度分段 1 查询返回空数据
2025-06-03 08:08:02,336 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-03 08:08:02,336 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-03 08:08:02,336 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:02,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:02,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:02,570 - INFO - API请求耗时: 234ms
2025-06-03 08:08:02,570 - INFO - Response - Page 1
2025-06-03 08:08:02,570 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:02,570 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:02,570 - WARNING - 单月查询返回空数据: 2024-06
2025-06-03 08:08:03,070 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-03 08:08:03,070 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:03,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:03,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:03,289 - INFO - API请求耗时: 219ms
2025-06-03 08:08:03,289 - INFO - Response - Page 1
2025-06-03 08:08:03,289 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:03,289 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:03,289 - WARNING - 单月查询返回空数据: 2024-07
2025-06-03 08:08:03,789 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-03 08:08:03,789 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:03,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:03,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:04,008 - INFO - API请求耗时: 219ms
2025-06-03 08:08:04,008 - INFO - Response - Page 1
2025-06-03 08:08:04,008 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:04,008 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:04,008 - WARNING - 单月查询返回空数据: 2024-08
2025-06-03 08:08:05,539 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-03 08:08:05,539 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-03 08:08:05,539 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:05,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:05,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:05,758 - INFO - API请求耗时: 219ms
2025-06-03 08:08:05,758 - INFO - Response - Page 1
2025-06-03 08:08:05,758 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:05,758 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:05,758 - WARNING - 月度分段 2 查询返回空数据
2025-06-03 08:08:05,758 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-03 08:08:05,758 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-03 08:08:05,758 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:05,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:05,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:05,976 - INFO - API请求耗时: 219ms
2025-06-03 08:08:05,976 - INFO - Response - Page 1
2025-06-03 08:08:05,976 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:05,976 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:05,976 - WARNING - 单月查询返回空数据: 2024-09
2025-06-03 08:08:06,492 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-03 08:08:06,492 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:06,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:06,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:06,726 - INFO - API请求耗时: 234ms
2025-06-03 08:08:06,726 - INFO - Response - Page 1
2025-06-03 08:08:06,726 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:06,726 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:06,726 - WARNING - 单月查询返回空数据: 2024-10
2025-06-03 08:08:07,226 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-03 08:08:07,226 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:07,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:07,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:07,445 - INFO - API请求耗时: 219ms
2025-06-03 08:08:07,445 - INFO - Response - Page 1
2025-06-03 08:08:07,445 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-03 08:08:07,445 - INFO - 查询完成，共获取到 0 条记录
2025-06-03 08:08:07,445 - WARNING - 单月查询返回空数据: 2024-11
2025-06-03 08:08:08,976 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-03 08:08:08,976 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-03 08:08:08,976 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:08,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:08,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:09,539 - INFO - API请求耗时: 562ms
2025-06-03 08:08:09,539 - INFO - Response - Page 1
2025-06-03 08:08:09,539 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:08:10,055 - INFO - Request Parameters - Page 2:
2025-06-03 08:08:10,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:10,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:10,742 - INFO - API请求耗时: 687ms
2025-06-03 08:08:10,742 - INFO - Response - Page 2
2025-06-03 08:08:10,742 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:08:11,242 - INFO - Request Parameters - Page 3:
2025-06-03 08:08:11,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:11,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:11,820 - INFO - API请求耗时: 578ms
2025-06-03 08:08:11,820 - INFO - Response - Page 3
2025-06-03 08:08:11,820 - INFO - 第 3 页获取到 100 条记录
2025-06-03 08:08:12,320 - INFO - Request Parameters - Page 4:
2025-06-03 08:08:12,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:12,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:12,883 - INFO - API请求耗时: 562ms
2025-06-03 08:08:12,883 - INFO - Response - Page 4
2025-06-03 08:08:12,883 - INFO - 第 4 页获取到 100 条记录
2025-06-03 08:08:13,383 - INFO - Request Parameters - Page 5:
2025-06-03 08:08:13,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:13,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:14,008 - INFO - API请求耗时: 625ms
2025-06-03 08:08:14,008 - INFO - Response - Page 5
2025-06-03 08:08:14,008 - INFO - 第 5 页获取到 94 条记录
2025-06-03 08:08:14,008 - INFO - 查询完成，共获取到 494 条记录
2025-06-03 08:08:14,008 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-03 08:08:15,023 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-03 08:08:15,023 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-03 08:08:15,023 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:15,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:15,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:15,570 - INFO - API请求耗时: 547ms
2025-06-03 08:08:15,570 - INFO - Response - Page 1
2025-06-03 08:08:15,570 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:08:16,086 - INFO - Request Parameters - Page 2:
2025-06-03 08:08:16,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:16,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:16,695 - INFO - API请求耗时: 609ms
2025-06-03 08:08:16,695 - INFO - Response - Page 2
2025-06-03 08:08:16,695 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:08:17,195 - INFO - Request Parameters - Page 3:
2025-06-03 08:08:17,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:17,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:17,726 - INFO - API请求耗时: 531ms
2025-06-03 08:08:17,726 - INFO - Response - Page 3
2025-06-03 08:08:17,726 - INFO - 第 3 页获取到 100 条记录
2025-06-03 08:08:18,226 - INFO - Request Parameters - Page 4:
2025-06-03 08:08:18,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:18,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:18,789 - INFO - API请求耗时: 562ms
2025-06-03 08:08:18,789 - INFO - Response - Page 4
2025-06-03 08:08:18,789 - INFO - 第 4 页获取到 100 条记录
2025-06-03 08:08:19,289 - INFO - Request Parameters - Page 5:
2025-06-03 08:08:19,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:19,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:19,883 - INFO - API请求耗时: 594ms
2025-06-03 08:08:19,883 - INFO - Response - Page 5
2025-06-03 08:08:19,883 - INFO - 第 5 页获取到 100 条记录
2025-06-03 08:08:20,398 - INFO - Request Parameters - Page 6:
2025-06-03 08:08:20,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:20,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:21,086 - INFO - API请求耗时: 688ms
2025-06-03 08:08:21,086 - INFO - Response - Page 6
2025-06-03 08:08:21,086 - INFO - 第 6 页获取到 100 条记录
2025-06-03 08:08:21,601 - INFO - Request Parameters - Page 7:
2025-06-03 08:08:21,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:21,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:22,211 - INFO - API请求耗时: 609ms
2025-06-03 08:08:22,211 - INFO - Response - Page 7
2025-06-03 08:08:22,211 - INFO - 第 7 页获取到 98 条记录
2025-06-03 08:08:22,211 - INFO - 查询完成，共获取到 698 条记录
2025-06-03 08:08:22,211 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-03 08:08:23,211 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-03 08:08:23,211 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-03 08:08:23,211 - INFO - Request Parameters - Page 1:
2025-06-03 08:08:23,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:23,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:23,695 - INFO - API请求耗时: 484ms
2025-06-03 08:08:23,695 - INFO - Response - Page 1
2025-06-03 08:08:23,695 - INFO - 第 1 页获取到 100 条记录
2025-06-03 08:08:24,195 - INFO - Request Parameters - Page 2:
2025-06-03 08:08:24,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:24,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:24,804 - INFO - API请求耗时: 609ms
2025-06-03 08:08:24,804 - INFO - Response - Page 2
2025-06-03 08:08:24,804 - INFO - 第 2 页获取到 100 条记录
2025-06-03 08:08:25,304 - INFO - Request Parameters - Page 3:
2025-06-03 08:08:25,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-03 08:08:25,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-03 08:08:25,601 - INFO - API请求耗时: 297ms
2025-06-03 08:08:25,601 - INFO - Response - Page 3
2025-06-03 08:08:25,601 - INFO - 第 3 页获取到 4 条记录
2025-06-03 08:08:25,601 - INFO - 查询完成，共获取到 204 条记录
2025-06-03 08:08:25,601 - INFO - 月度分段 5 查询成功，获取到 204 条记录
2025-06-03 08:08:26,617 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1396 条记录，失败 0 次
2025-06-03 08:08:26,617 - INFO - 成功获取宜搭月销售表单数据，共 1396 条记录
2025-06-03 08:08:26,617 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-03 08:08:26,617 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-03 08:08:26,617 - INFO - 成功获取SQLite月度汇总数据，共 1401 条记录
2025-06-03 08:08:26,695 - INFO - 成功创建宜搭月销售数据索引，共 1396 条记录
2025-06-03 08:08:26,695 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:27,195 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-03 08:08:27,195 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11549.18, 'new_value': 21957.940000000002}, {'field': 'dailyBillAmount', 'old_value': 11549.18, 'new_value': 21957.940000000002}, {'field': 'amount', 'old_value': 0.0, 'new_value': 336.0}, {'field': 'count', 'old_value': 0, 'new_value': 4}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 336.0}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 4}]
2025-06-03 08:08:27,195 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:27,664 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-03 08:08:27,664 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34373.91, 'new_value': 56444.67}, {'field': 'dailyBillAmount', 'old_value': 34373.91, 'new_value': 56444.67}, {'field': 'amount', 'old_value': 18911.0, 'new_value': 29282.7}, {'field': 'count', 'old_value': 159, 'new_value': 242}, {'field': 'instoreAmount', 'old_value': 8167.8, 'new_value': 13599.1}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 89}, {'field': 'onlineAmount', 'old_value': 10743.2, 'new_value': 15684.4}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 153}]
2025-06-03 08:08:27,664 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:28,226 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-03 08:08:28,226 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21844.33, 'new_value': 34053.96}, {'field': 'dailyBillAmount', 'old_value': 21844.33, 'new_value': 34053.96}, {'field': 'amount', 'old_value': 22085.96, 'new_value': 34333.96}, {'field': 'count', 'old_value': 132, 'new_value': 205}, {'field': 'instoreAmount', 'old_value': 21371.15, 'new_value': 32531.93}, {'field': 'instoreCount', 'old_value': 122, 'new_value': 183}, {'field': 'onlineAmount', 'old_value': 714.81, 'new_value': 1861.01}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 22}]
2025-06-03 08:08:28,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:28,789 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-03 08:08:28,789 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37658.09, 'new_value': 63298.58}, {'field': 'dailyBillAmount', 'old_value': 37658.09, 'new_value': 63298.58}, {'field': 'amount', 'old_value': 32406.2, 'new_value': 51397.3}, {'field': 'count', 'old_value': 159, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 32406.2, 'new_value': 51397.399999999994}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 251}]
2025-06-03 08:08:28,789 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:29,258 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-03 08:08:29,258 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46076.98, 'new_value': 67320.69}, {'field': 'dailyBillAmount', 'old_value': 46076.98, 'new_value': 67320.69}, {'field': 'amount', 'old_value': 50546.2, 'new_value': 81208.0}, {'field': 'count', 'old_value': 159, 'new_value': 263}, {'field': 'instoreAmount', 'old_value': 50248.0, 'new_value': 80910.0}, {'field': 'instoreCount', 'old_value': 157, 'new_value': 261}]
2025-06-03 08:08:29,258 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:29,711 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-03 08:08:29,711 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2750.6, 'new_value': 3546.2}, {'field': 'dailyBillAmount', 'old_value': 2750.6, 'new_value': 3546.2}, {'field': 'amount', 'old_value': 2984.7999999999997, 'new_value': 3780.1}, {'field': 'count', 'old_value': 11, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 2190.0, 'new_value': 2388.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}, {'field': 'onlineAmount', 'old_value': 794.8000000000001, 'new_value': 1392.4}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 17}]
2025-06-03 08:08:29,711 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:30,273 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-03 08:08:30,273 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'count', 'old_value': 3, 'new_value': 6}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 6}]
2025-06-03 08:08:30,273 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:30,820 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-03 08:08:30,836 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39300.98, 'new_value': 64306.44}, {'field': 'dailyBillAmount', 'old_value': 39300.98, 'new_value': 64306.44}, {'field': 'amount', 'old_value': 26168.0, 'new_value': 43192.4}, {'field': 'count', 'old_value': 148, 'new_value': 244}, {'field': 'instoreAmount', 'old_value': 25330.5, 'new_value': 41366.6}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 172}, {'field': 'onlineAmount', 'old_value': 1658.1, 'new_value': 2669.9}, {'field': 'onlineCount', 'old_value': 46, 'new_value': 72}]
2025-06-03 08:08:30,836 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:31,258 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-03 08:08:31,258 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35637.5, 'new_value': 59484.68}, {'field': 'dailyBillAmount', 'old_value': 35637.5, 'new_value': 59484.68}, {'field': 'amount', 'old_value': 7208.0, 'new_value': 11485.0}, {'field': 'count', 'old_value': 33, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 7208.0, 'new_value': 11485.0}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 53}]
2025-06-03 08:08:31,258 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:31,726 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-03 08:08:31,726 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2282.0, 'new_value': 2782.0}, {'field': 'count', 'old_value': 4, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 2282.0, 'new_value': 2782.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 7}]
2025-06-03 08:08:31,726 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:32,226 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-03 08:08:32,226 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 74213.14, 'new_value': 137678.51}, {'field': 'dailyBillAmount', 'old_value': 74135.14, 'new_value': 137678.51}, {'field': 'amount', 'old_value': -21646.770000000004, 'new_value': -45265.770000000004}, {'field': 'count', 'old_value': 80, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 55719.61, 'new_value': 93827.38}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 145}]
2025-06-03 08:08:32,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:32,695 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-03 08:08:32,695 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22717.0, 'new_value': 29094.0}, {'field': 'amount', 'old_value': 22717.0, 'new_value': 29094.0}, {'field': 'count', 'old_value': 88, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 22717.0, 'new_value': 29094.0}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 128}]
2025-06-03 08:08:32,695 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:33,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-03 08:08:33,164 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24289.85, 'new_value': 46602.74}, {'field': 'dailyBillAmount', 'old_value': 20642.25, 'new_value': 39865.54}, {'field': 'amount', 'old_value': 24289.85, 'new_value': 46602.74}, {'field': 'count', 'old_value': 79, 'new_value': 149}, {'field': 'instoreAmount', 'old_value': 24289.85, 'new_value': 46602.74}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 149}]
2025-06-03 08:08:33,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:33,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:33,617 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFI
2025-06-03 08:08:33,617 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5310.650000000001, 'new_value': 11112.05}, {'field': 'dailyBillAmount', 'old_value': 4913.35, 'new_value': 11112.05}, {'field': 'amount', 'old_value': 2410.62, 'new_value': 5911.32}, {'field': 'count', 'old_value': 60, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 2436.62, 'new_value': 6183.57}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 109}]
2025-06-03 08:08:33,617 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:34,007 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-03 08:08:34,007 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6655.03, 'new_value': 10229.27}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5186.04}, {'field': 'amount', 'old_value': 6655.03, 'new_value': 10228.91}, {'field': 'count', 'old_value': 204, 'new_value': 317}, {'field': 'instoreAmount', 'old_value': 6088.72, 'new_value': 9453.46}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 293}, {'field': 'onlineAmount', 'old_value': 566.31, 'new_value': 775.81}, {'field': 'onlineCount', 'old_value': 18, 'new_value': 24}]
2025-06-03 08:08:34,007 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:34,539 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-03 08:08:34,539 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11240.0, 'new_value': 19255.0}, {'field': 'dailyBillAmount', 'old_value': 11240.0, 'new_value': 19255.0}, {'field': 'amount', 'old_value': 8349.0, 'new_value': 13248.0}, {'field': 'count', 'old_value': 12, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 8349.0, 'new_value': 13248.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 26}]
2025-06-03 08:08:34,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:35,023 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-03 08:08:35,023 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20919.87, 'new_value': 31596.769999999997}, {'field': 'dailyBillAmount', 'old_value': 20919.87, 'new_value': 31596.769999999997}, {'field': 'amount', 'old_value': 20919.87, 'new_value': 31596.769999999997}, {'field': 'count', 'old_value': 23, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 20919.87, 'new_value': 31596.769999999997}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 36}]
2025-06-03 08:08:35,023 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:35,476 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJI
2025-06-03 08:08:35,476 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-06, 变更字段: [{'field': 'amount', 'old_value': 824.0, 'new_value': 1470.0}, {'field': 'count', 'old_value': 2, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 824.0, 'new_value': 1470.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 4}]
2025-06-03 08:08:35,476 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:35,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-03 08:08:35,914 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6190.4, 'new_value': 10005.4}, {'field': 'dailyBillAmount', 'old_value': 6190.4, 'new_value': 10005.4}, {'field': 'amount', 'old_value': 6985.4, 'new_value': 10800.4}, {'field': 'count', 'old_value': 17, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 6985.4, 'new_value': 10800.4}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 28}]
2025-06-03 08:08:35,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:36,414 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-03 08:08:36,414 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1080.0, 'new_value': 3159.0}, {'field': 'amount', 'old_value': 1080.0, 'new_value': 3159.0}, {'field': 'count', 'old_value': 6, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 1080.0, 'new_value': 3159.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 9}]
2025-06-03 08:08:36,414 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:36,867 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-03 08:08:36,867 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12076.0, 'new_value': 29350.62}, {'field': 'dailyBillAmount', 'old_value': 12076.0, 'new_value': 29350.62}, {'field': 'amount', 'old_value': 24546.0, 'new_value': 39953.0}, {'field': 'count', 'old_value': 105, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 24546.0, 'new_value': 39953.3}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 183}]
2025-06-03 08:08:36,867 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:37,367 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-03 08:08:37,367 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4959.06, 'new_value': 11603.46}, {'field': 'dailyBillAmount', 'old_value': 4959.06, 'new_value': 11603.46}, {'field': 'amount', 'old_value': 137.98000000000002, 'new_value': 341.49}, {'field': 'count', 'old_value': 35, 'new_value': 47}, {'field': 'instoreAmount', 'old_value': 655.98, 'new_value': 860.48}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 47}]
2025-06-03 08:08:37,367 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:37,804 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOI
2025-06-03 08:08:37,804 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14339.720000000001, 'new_value': 22647.82}, {'field': 'amount', 'old_value': 14339.1, 'new_value': 22647.0}, {'field': 'count', 'old_value': 311, 'new_value': 536}, {'field': 'instoreAmount', 'old_value': 13478.36, 'new_value': 21570.46}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 498}, {'field': 'onlineAmount', 'old_value': 1425.62, 'new_value': 1641.62}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 38}]
2025-06-03 08:08:37,804 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:38,226 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-03 08:08:38,226 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22605.06, 'new_value': 36345.8}, {'field': 'dailyBillAmount', 'old_value': 22605.06, 'new_value': 36345.8}, {'field': 'amount', 'old_value': 22661.06, 'new_value': 36401.8}, {'field': 'count', 'old_value': 56, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 22661.06, 'new_value': 36401.8}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 90}]
2025-06-03 08:08:38,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:38,726 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-03 08:08:38,726 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18278.13, 'new_value': 26782.800000000003}, {'field': 'dailyBillAmount', 'old_value': 18278.13, 'new_value': 26782.800000000003}, {'field': 'amount', 'old_value': 8449.4, 'new_value': 10458.4}, {'field': 'count', 'old_value': 19, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 8449.4, 'new_value': 10458.4}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 25}]
2025-06-03 08:08:38,726 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:39,148 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-03 08:08:39,148 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21928.81, 'new_value': 37610.83}, {'field': 'dailyBillAmount', 'old_value': 21928.81, 'new_value': 37610.83}, {'field': 'amount', 'old_value': 8364.6, 'new_value': 14963.6}, {'field': 'count', 'old_value': 32, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 8364.6, 'new_value': 14963.6}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 60}]
2025-06-03 08:08:39,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:39,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:39,554 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-03 08:08:39,554 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4828.62, 'new_value': 7479.94}, {'field': 'dailyBillAmount', 'old_value': 4648.2, 'new_value': 7479.94}, {'field': 'amount', 'old_value': 1093.26, 'new_value': 1613.8400000000001}, {'field': 'count', 'old_value': 42, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 361.7, 'new_value': 502.5}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 11}, {'field': 'onlineAmount', 'old_value': 731.56, 'new_value': 1111.69}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 48}]
2025-06-03 08:08:39,554 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:40,054 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-03 08:08:40,054 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8082.91, 'new_value': 13924.11}, {'field': 'dailyBillAmount', 'old_value': 8082.91, 'new_value': 13924.11}, {'field': 'amount', 'old_value': 2026.9899999999998, 'new_value': 2806.0}, {'field': 'count', 'old_value': 46, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 1921.0700000000002, 'new_value': 2546.9700000000003}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 58}, {'field': 'onlineAmount', 'old_value': 105.92, 'new_value': 270.1}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 7}]
2025-06-03 08:08:40,054 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:40,539 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-03 08:08:40,539 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5721.6, 'new_value': 6890.6}, {'field': 'dailyBillAmount', 'old_value': 5721.6, 'new_value': 6890.6}, {'field': 'amount', 'old_value': 6075.6, 'new_value': 6483.6}, {'field': 'count', 'old_value': 121, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 6075.6, 'new_value': 6483.6}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 141}]
2025-06-03 08:08:40,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:40,992 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-03 08:08:40,992 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6291.57}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6291.57}, {'field': 'amount', 'old_value': 1823.84, 'new_value': 2630.24}, {'field': 'count', 'old_value': 104, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 678.93, 'new_value': 927.52}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 46}, {'field': 'onlineAmount', 'old_value': 1148.9099999999999, 'new_value': 1747.31}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 104}]
2025-06-03 08:08:40,992 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:41,461 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-03 08:08:41,461 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15728.04, 'new_value': 26563.36}, {'field': 'dailyBillAmount', 'old_value': 15728.04, 'new_value': 26563.36}, {'field': 'amount', 'old_value': 9517.52, 'new_value': 15391.52}, {'field': 'count', 'old_value': 43, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 10214.12, 'new_value': 16188.12}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 69}]
2025-06-03 08:08:41,461 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:41,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-03 08:08:41,914 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9304.07, 'new_value': 17273.37}, {'field': 'dailyBillAmount', 'old_value': 9304.07, 'new_value': 17273.37}, {'field': 'amount', 'old_value': 4945.24, 'new_value': 8173.38}, {'field': 'count', 'old_value': 251, 'new_value': 386}, {'field': 'instoreAmount', 'old_value': 5186.34, 'new_value': 8451.92}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 386}]
2025-06-03 08:08:41,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:42,382 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-03 08:08:42,382 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41377.0, 'new_value': 64372.3}, {'field': 'dailyBillAmount', 'old_value': 41377.0, 'new_value': 64372.3}, {'field': 'amount', 'old_value': 41377.0, 'new_value': 64372.3}, {'field': 'count', 'old_value': 50, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 41377.0, 'new_value': 64372.3}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 80}]
2025-06-03 08:08:42,382 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:42,804 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-03 08:08:42,804 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28951.7, 'new_value': 47334.0}, {'field': 'dailyBillAmount', 'old_value': 28951.7, 'new_value': 47334.0}, {'field': 'amount', 'old_value': 23023.1, 'new_value': 34990.6}, {'field': 'count', 'old_value': 59, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 23023.1, 'new_value': 34990.6}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 85}]
2025-06-03 08:08:42,804 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:43,226 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-03 08:08:43,226 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1841.0, 'new_value': 3901.0}, {'field': 'dailyBillAmount', 'old_value': 1672.0, 'new_value': 3901.0}, {'field': 'amount', 'old_value': 1841.0, 'new_value': 3901.0}, {'field': 'count', 'old_value': 35, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 1841.0, 'new_value': 3901.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 79}]
2025-06-03 08:08:43,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:43,757 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-03 08:08:43,757 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5025.05, 'new_value': 8587.05}, {'field': 'dailyBillAmount', 'old_value': 5025.05, 'new_value': 8587.05}, {'field': 'amount', 'old_value': 5561.040000000001, 'new_value': 8614.39}, {'field': 'count', 'old_value': 285, 'new_value': 467}, {'field': 'instoreAmount', 'old_value': 3167.07, 'new_value': 4863.9400000000005}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 251}, {'field': 'onlineAmount', 'old_value': 2512.64, 'new_value': 3902.6499999999996}, {'field': 'onlineCount', 'old_value': 127, 'new_value': 216}]
2025-06-03 08:08:43,757 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:44,148 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-03 08:08:44,148 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2819.45, 'new_value': 5209.95}, {'field': 'dailyBillAmount', 'old_value': 2819.45, 'new_value': 5209.95}, {'field': 'amount', 'old_value': 3132.0699999999997, 'new_value': 5190.86}, {'field': 'count', 'old_value': 108, 'new_value': 185}, {'field': 'instoreAmount', 'old_value': 2254.8, 'new_value': 3663.5}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 110}, {'field': 'onlineAmount', 'old_value': 947.07, 'new_value': 1630.1599999999999}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 75}]
2025-06-03 08:08:44,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:44,648 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-03 08:08:44,648 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1081.1, 'new_value': 2190.1}, {'field': 'count', 'old_value': 20, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 1081.1, 'new_value': 2190.9900000000002}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 41}]
2025-06-03 08:08:44,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:45,132 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-03 08:08:45,132 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6747.4, 'new_value': 11393.0}, {'field': 'amount', 'old_value': 6747.4, 'new_value': 11392.2}, {'field': 'count', 'old_value': 151, 'new_value': 223}, {'field': 'instoreAmount', 'old_value': 6747.4, 'new_value': 11393.0}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 223}]
2025-06-03 08:08:45,132 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:45,648 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-03 08:08:45,648 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31102.0, 'new_value': 47864.0}, {'field': 'dailyBillAmount', 'old_value': 31102.0, 'new_value': 47864.0}, {'field': 'amount', 'old_value': 12353.0, 'new_value': 18347.0}, {'field': 'count', 'old_value': 32, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 12353.0, 'new_value': 18347.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 49}]
2025-06-03 08:08:45,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:46,086 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-03 08:08:46,086 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3488.0, 'new_value': 9299.16}, {'field': 'dailyBillAmount', 'old_value': 3488.0, 'new_value': 9299.16}, {'field': 'amount', 'old_value': 3608.0, 'new_value': 8847.0}, {'field': 'count', 'old_value': 21, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 3847.0, 'new_value': 9086.16}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 39}]
2025-06-03 08:08:46,086 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:46,570 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-03 08:08:46,570 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7650.0, 'new_value': 8275.0}, {'field': 'dailyBillAmount', 'old_value': 7650.0, 'new_value': 8275.0}, {'field': 'amount', 'old_value': 7801.0, 'new_value': 10097.0}, {'field': 'count', 'old_value': 10, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 7801.0, 'new_value': 10097.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 12}]
2025-06-03 08:08:46,570 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:47,054 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-03 08:08:47,054 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1892.5, 'new_value': 7853.8}, {'field': 'dailyBillAmount', 'old_value': 1591.7, 'new_value': 7853.8}, {'field': 'amount', 'old_value': 1892.5, 'new_value': 7853.7}, {'field': 'count', 'old_value': 13, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 2130.5, 'new_value': 8091.8}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 17}]
2025-06-03 08:08:47,054 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:47,507 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-03 08:08:47,507 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14742.1, 'new_value': 19821.0}, {'field': 'dailyBillAmount', 'old_value': 14276.82, 'new_value': 19821.0}, {'field': 'amount', 'old_value': 8789.990000000002, 'new_value': 12156.0}, {'field': 'count', 'old_value': 183, 'new_value': 269}, {'field': 'instoreAmount', 'old_value': 8455.13, 'new_value': 11743.06}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 240}, {'field': 'onlineAmount', 'old_value': 1024.06, 'new_value': 1232.71}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 29}]
2025-06-03 08:08:47,507 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:47,961 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-03 08:08:47,961 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5765.38, 'new_value': 10911.369999999999}, {'field': 'dailyBillAmount', 'old_value': 4185.77, 'new_value': 10869.45}, {'field': 'amount', 'old_value': 5765.38, 'new_value': 10910.380000000001}, {'field': 'count', 'old_value': 64, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 5681.0, 'new_value': 10717.619999999999}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 127}, {'field': 'onlineAmount', 'old_value': 84.38, 'new_value': 193.75}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-06-03 08:08:47,961 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:48,414 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-03 08:08:48,414 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4202.5, 'new_value': 10341.46}, {'field': 'dailyBillAmount', 'old_value': 4202.5, 'new_value': 10341.46}, {'field': 'amount', 'old_value': 4624.0, 'new_value': 10715.0}, {'field': 'count', 'old_value': 30, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 3960.7, 'new_value': 9560.880000000001}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 48}, {'field': 'onlineAmount', 'old_value': 663.92, 'new_value': 1155.4499999999998}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 9}]
2025-06-03 08:08:48,414 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:48,851 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-03 08:08:48,851 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8414.0, 'new_value': 19315.0}, {'field': 'dailyBillAmount', 'old_value': 8414.0, 'new_value': 19315.0}, {'field': 'amount', 'old_value': 10680.0, 'new_value': 23424.0}, {'field': 'count', 'old_value': 41, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 10892.0, 'new_value': 23636.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 86}]
2025-06-03 08:08:48,851 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:49,382 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-03 08:08:49,382 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3030.0, 'new_value': 4194.0}, {'field': 'dailyBillAmount', 'old_value': 3030.0, 'new_value': 4194.0}, {'field': 'amount', 'old_value': 3030.0, 'new_value': 4194.0}, {'field': 'count', 'old_value': 5, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 3030.0, 'new_value': 4273.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 10}]
2025-06-03 08:08:49,382 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:49,820 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-03 08:08:49,820 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2758.1099999999997, 'new_value': 3814.14}, {'field': 'amount', 'old_value': 2758.11, 'new_value': 3813.9300000000003}, {'field': 'count', 'old_value': 127, 'new_value': 184}, {'field': 'instoreAmount', 'old_value': 2925.75, 'new_value': 4116.55}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 184}]
2025-06-03 08:08:49,820 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:50,257 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-03 08:08:50,257 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10028.63, 'new_value': 16682.21}, {'field': 'dailyBillAmount', 'old_value': 10028.63, 'new_value': 16682.21}, {'field': 'amount', 'old_value': 7724.8, 'new_value': 10365.8}, {'field': 'count', 'old_value': 33, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 7605.8, 'new_value': 10247.7}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 41}]
2025-06-03 08:08:50,257 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:50,742 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-03 08:08:50,742 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21562.77, 'new_value': 37579.17}, {'field': 'dailyBillAmount', 'old_value': 21562.77, 'new_value': 37579.17}, {'field': 'amount', 'old_value': 9277.9, 'new_value': 14389.5}, {'field': 'count', 'old_value': 84, 'new_value': 126}, {'field': 'instoreAmount', 'old_value': 5873.4, 'new_value': 8898.07}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 58}, {'field': 'onlineAmount', 'old_value': 3404.5, 'new_value': 5492.1}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 68}]
2025-06-03 08:08:50,742 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:51,132 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-03 08:08:51,132 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15883.0, 'new_value': 30329.379999999997}, {'field': 'dailyBillAmount', 'old_value': 15883.0, 'new_value': 30329.379999999997}, {'field': 'amount', 'old_value': 20492.0, 'new_value': 34124.0}, {'field': 'count', 'old_value': 101, 'new_value': 177}, {'field': 'instoreAmount', 'old_value': 20492.0, 'new_value': 34124.8}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 177}]
2025-06-03 08:08:51,132 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:51,601 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMIJ
2025-06-03 08:08:51,601 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29840.1, 'new_value': 29939.1}, {'field': 'amount', 'old_value': 29840.1, 'new_value': 29939.1}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 29840.1, 'new_value': 29939.1}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-03 08:08:51,601 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:52,117 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-03 08:08:52,117 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6780.7, 'new_value': 12797.880000000001}, {'field': 'dailyBillAmount', 'old_value': 6780.7, 'new_value': 12797.880000000001}, {'field': 'amount', 'old_value': 3399.19, 'new_value': 5887.1900000000005}, {'field': 'count', 'old_value': 37, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 3277.29, 'new_value': 5699.889999999999}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 56}, {'field': 'onlineAmount', 'old_value': 121.9, 'new_value': 253.4}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 5}]
2025-06-03 08:08:52,117 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:52,632 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-03 08:08:52,632 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 381.85, 'new_value': 623.85}, {'field': 'count', 'old_value': 4, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 381.85, 'new_value': 624.55}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 9}]
2025-06-03 08:08:52,632 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:53,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-03 08:08:53,164 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8244.5, 'new_value': 20421.27}, {'field': 'dailyBillAmount', 'old_value': 8244.5, 'new_value': 20421.27}, {'field': 'amount', 'old_value': 8244.5, 'new_value': 20421.27}, {'field': 'count', 'old_value': 32, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 8244.5, 'new_value': 20421.27}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 73}]
2025-06-03 08:08:53,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:53,617 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-03 08:08:53,617 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2095.39, 'new_value': 4004.96}, {'field': 'dailyBillAmount', 'old_value': 2095.39, 'new_value': 4004.96}, {'field': 'amount', 'old_value': 2524.49, 'new_value': 4372.29}, {'field': 'count', 'old_value': 67, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 2524.49, 'new_value': 4373.26}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 119}]
2025-06-03 08:08:53,617 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:54,117 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-03 08:08:54,117 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13546.0, 'new_value': 25986.0}, {'field': 'amount', 'old_value': 13546.0, 'new_value': 25986.0}, {'field': 'count', 'old_value': 22, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 13546.0, 'new_value': 25986.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 45}]
2025-06-03 08:08:54,117 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:54,570 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-03 08:08:54,570 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 963.1999999999999, 'new_value': 3665.34}, {'field': 'amount', 'old_value': 963.1999999999999, 'new_value': 3664.4}, {'field': 'count', 'old_value': 13, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 963.1999999999999, 'new_value': 3665.34}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 36}]
2025-06-03 08:08:54,570 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:55,039 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-03 08:08:55,039 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32038.0, 'new_value': 38424.0}, {'field': 'amount', 'old_value': 32038.0, 'new_value': 38424.0}, {'field': 'count', 'old_value': 9, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 32038.0, 'new_value': 38424.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 12}]
2025-06-03 08:08:55,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:55,492 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-03 08:08:55,492 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10360.0, 'new_value': 21653.0}, {'field': 'dailyBillAmount', 'old_value': 10360.0, 'new_value': 21653.0}, {'field': 'amount', 'old_value': 10360.0, 'new_value': 21653.0}, {'field': 'count', 'old_value': 42, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 10360.0, 'new_value': 21653.0}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 104}]
2025-06-03 08:08:55,492 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:55,492 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:59,023 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-03 08:08:59,023 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2921.8, 'new_value': 6867.0}, {'field': 'count', 'old_value': 29, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 2921.8, 'new_value': 6867.8}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 54}]
2025-06-03 08:08:59,023 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:59,476 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-03 08:08:59,476 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19017.0, 'new_value': 35649.72}, {'field': 'dailyBillAmount', 'old_value': 19017.0, 'new_value': 35649.72}, {'field': 'amount', 'old_value': 19581.0, 'new_value': 35649.0}, {'field': 'count', 'old_value': 60, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 19581.0, 'new_value': 35649.72}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 109}]
2025-06-03 08:08:59,476 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:59,960 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-03 08:08:59,960 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 45706.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 45706.0}, {'field': 'amount', 'old_value': 48505.64, 'new_value': 87463.55}, {'field': 'count', 'old_value': 61, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 48505.64, 'new_value': 87463.55}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 117}]
2025-06-03 08:08:59,960 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:08:59,960 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:00,429 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-03 08:09:00,429 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9435.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9435.4}, {'field': 'amount', 'old_value': 2456.6, 'new_value': 4247.5}, {'field': 'count', 'old_value': 10, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 2456.6, 'new_value': 4247.5}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 17}]
2025-06-03 08:09:00,429 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-03 08:09:00,867 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-06-03 08:09:00,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 146935.84, 'new_value': 146943.84}, {'field': 'count', 'old_value': 11533, 'new_value': 11536}, {'field': 'onlineAmount', 'old_value': 141947.87, 'new_value': 141955.37}, {'field': 'onlineCount', 'old_value': 10883, 'new_value': 10886}]
2025-06-03 08:09:00,867 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-03 08:09:01,398 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-06-03 08:09:01,398 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172555.28, 'new_value': 172560.28}, {'field': 'amount', 'old_value': 172539.97, 'new_value': 172544.97}, {'field': 'count', 'old_value': 8027, 'new_value': 8028}, {'field': 'onlineAmount', 'old_value': 120881.7, 'new_value': 120886.7}, {'field': 'onlineCount', 'old_value': 5657, 'new_value': 5658}]
2025-06-03 08:09:01,398 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:01,773 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-03 08:09:01,773 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28311.37, 'new_value': 35240.68}, {'field': 'amount', 'old_value': 28311.37, 'new_value': 35239.75}, {'field': 'count', 'old_value': 256, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 22920.77, 'new_value': 28128.83}, {'field': 'instoreCount', 'old_value': 194, 'new_value': 249}, {'field': 'onlineAmount', 'old_value': 6043.7, 'new_value': 7827.049999999999}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 90}]
2025-06-03 08:09:01,773 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:02,273 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-03 08:09:02,273 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19292.18, 'new_value': 34436.95}, {'field': 'dailyBillAmount', 'old_value': 19292.18, 'new_value': 34436.95}, {'field': 'amount', 'old_value': 2422.02, 'new_value': 3514.52}, {'field': 'count', 'old_value': 36, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 2477.22, 'new_value': 3718.2}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 69}]
2025-06-03 08:09:02,273 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:02,867 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-03 08:09:02,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38759.49, 'new_value': 56513.0}, {'field': 'dailyBillAmount', 'old_value': 38759.49, 'new_value': 56513.0}, {'field': 'amount', 'old_value': 14884.77, 'new_value': 21258.4}, {'field': 'count', 'old_value': 306, 'new_value': 460}, {'field': 'instoreAmount', 'old_value': 15114.82, 'new_value': 21587.559999999998}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 455}, {'field': 'onlineAmount', 'old_value': 118.55, 'new_value': 194.0}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 5}]
2025-06-03 08:09:02,867 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:03,398 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-03 08:09:03,398 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17120.6, 'new_value': 31740.1}, {'field': 'dailyBillAmount', 'old_value': 27206.9, 'new_value': 51834.8}, {'field': 'amount', 'old_value': 17120.6, 'new_value': 31739.0}, {'field': 'count', 'old_value': 69, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 17854.6, 'new_value': 32862.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 121}]
2025-06-03 08:09:03,398 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:03,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-03 08:09:03,914 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 102541.97, 'new_value': 139885.03}, {'field': 'dailyBillAmount', 'old_value': 102541.97, 'new_value': 139885.03}, {'field': 'amount', 'old_value': 87960.78, 'new_value': 111871.0}, {'field': 'count', 'old_value': 1477, 'new_value': 1910}, {'field': 'instoreAmount', 'old_value': 84104.12000000001, 'new_value': 106902.89}, {'field': 'instoreCount', 'old_value': 1405, 'new_value': 1814}, {'field': 'onlineAmount', 'old_value': 4255.27, 'new_value': 5396.81}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 96}]
2025-06-03 08:09:03,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:04,382 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-03 08:09:04,382 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29174.300000000003, 'new_value': 47100.56}, {'field': 'amount', 'old_value': 29174.300000000003, 'new_value': 47099.7}, {'field': 'count', 'old_value': 648, 'new_value': 1019}, {'field': 'instoreAmount', 'old_value': 21906.2, 'new_value': 35851.06}, {'field': 'instoreCount', 'old_value': 438, 'new_value': 705}, {'field': 'onlineAmount', 'old_value': 7268.1, 'new_value': 11249.5}, {'field': 'onlineCount', 'old_value': 210, 'new_value': 314}]
2025-06-03 08:09:04,382 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:04,867 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-03 08:09:04,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -872.46, 'new_value': -2143.46}, {'field': 'count', 'old_value': 5, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 269.0, 'new_value': 549.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 4}, {'field': 'onlineAmount', 'old_value': 67.0, 'new_value': 136.0}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 6}]
2025-06-03 08:09:04,867 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:05,226 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-03 08:09:05,226 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8766.64, 'new_value': 14464.0}, {'field': 'count', 'old_value': 563, 'new_value': 954}, {'field': 'instoreAmount', 'old_value': 6823.64, 'new_value': 11125.25}, {'field': 'instoreCount', 'old_value': 369, 'new_value': 648}, {'field': 'onlineAmount', 'old_value': 2232.4900000000002, 'new_value': 3735.44}, {'field': 'onlineCount', 'old_value': 194, 'new_value': 306}]
2025-06-03 08:09:05,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:05,632 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-03 08:09:05,632 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26199.16, 'new_value': 39256.8}, {'field': 'dailyBillAmount', 'old_value': 26199.16, 'new_value': 39256.8}, {'field': 'amount', 'old_value': 27604.76, 'new_value': 39277.86}, {'field': 'count', 'old_value': 763, 'new_value': 1121}, {'field': 'instoreAmount', 'old_value': 27678.16, 'new_value': 39385.259999999995}, {'field': 'instoreCount', 'old_value': 763, 'new_value': 1121}]
2025-06-03 08:09:05,632 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:06,039 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-03 08:09:06,039 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9726.880000000001, 'new_value': 13949.330000000002}, {'field': 'amount', 'old_value': 9726.880000000001, 'new_value': 13949.29}, {'field': 'count', 'old_value': 524, 'new_value': 770}, {'field': 'instoreAmount', 'old_value': 6400.5599999999995, 'new_value': 8406.39}, {'field': 'instoreCount', 'old_value': 383, 'new_value': 524}, {'field': 'onlineAmount', 'old_value': 3326.3199999999997, 'new_value': 5542.9400000000005}, {'field': 'onlineCount', 'old_value': 141, 'new_value': 246}]
2025-06-03 08:09:06,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:06,460 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-03 08:09:06,460 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12348.32, 'new_value': 19931.66}, {'field': 'dailyBillAmount', 'old_value': 12348.32, 'new_value': 19931.66}, {'field': 'amount', 'old_value': 2728.6000000000004, 'new_value': 3685.3}, {'field': 'count', 'old_value': 74, 'new_value': 106}, {'field': 'instoreAmount', 'old_value': 2728.6000000000004, 'new_value': 3685.4}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 106}]
2025-06-03 08:09:06,460 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:06,976 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-03 08:09:06,976 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11892.75, 'new_value': 20355.47}, {'field': 'dailyBillAmount', 'old_value': 11892.75, 'new_value': 20355.47}, {'field': 'amount', 'old_value': 6165.08, 'new_value': 10521.18}, {'field': 'count', 'old_value': 329, 'new_value': 511}, {'field': 'instoreAmount', 'old_value': 1478.18, 'new_value': 2330.06}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 156}, {'field': 'onlineAmount', 'old_value': 4928.3, 'new_value': 8453.7}, {'field': 'onlineCount', 'old_value': 223, 'new_value': 355}]
2025-06-03 08:09:06,976 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:07,382 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-03 08:09:07,382 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11834.150000000001, 'new_value': 15567.490000000002}, {'field': 'amount', 'old_value': 11834.150000000001, 'new_value': 15566.94}, {'field': 'count', 'old_value': 332, 'new_value': 479}, {'field': 'instoreAmount', 'old_value': 11241.480000000001, 'new_value': 14745.82}, {'field': 'instoreCount', 'old_value': 324, 'new_value': 468}, {'field': 'onlineAmount', 'old_value': 599.97, 'new_value': 828.97}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 11}]
2025-06-03 08:09:07,382 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:07,820 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-03 08:09:07,835 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8743.9, 'new_value': 15135.720000000001}, {'field': 'count', 'old_value': 528, 'new_value': 857}, {'field': 'instoreAmount', 'old_value': 9487.550000000001, 'new_value': 16178.62}, {'field': 'instoreCount', 'old_value': 519, 'new_value': 844}, {'field': 'onlineAmount', 'old_value': 232.9, 'new_value': 320.4}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 13}]
2025-06-03 08:09:07,835 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:08,335 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-03 08:09:08,335 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6291.12, 'new_value': 10281.0}, {'field': 'count', 'old_value': 492, 'new_value': 773}, {'field': 'instoreAmount', 'old_value': 625.0, 'new_value': 948.0}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 65}, {'field': 'onlineAmount', 'old_value': 6081.54, 'new_value': 9858.66}, {'field': 'onlineCount', 'old_value': 453, 'new_value': 708}]
2025-06-03 08:09:08,335 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:08,851 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-03 08:09:08,851 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19485.93, 'new_value': 30337.57}, {'field': 'dailyBillAmount', 'old_value': 19485.93, 'new_value': 30337.57}, {'field': 'amount', 'old_value': 14666.4, 'new_value': 23051.0}, {'field': 'count', 'old_value': 468, 'new_value': 725}, {'field': 'instoreAmount', 'old_value': 8972.210000000001, 'new_value': 14291.73}, {'field': 'instoreCount', 'old_value': 379, 'new_value': 586}, {'field': 'onlineAmount', 'old_value': 6066.2, 'new_value': 9447.2}, {'field': 'onlineCount', 'old_value': 89, 'new_value': 139}]
2025-06-03 08:09:08,851 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:09,273 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-03 08:09:09,273 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4223.48, 'new_value': 7166.049999999999}, {'field': 'dailyBillAmount', 'old_value': 4223.48, 'new_value': 7166.049999999999}, {'field': 'amount', 'old_value': 5187.5, 'new_value': 7928.26}, {'field': 'count', 'old_value': 162, 'new_value': 266}, {'field': 'instoreAmount', 'old_value': 2336.32, 'new_value': 3714.8900000000003}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 132}, {'field': 'onlineAmount', 'old_value': 2851.1800000000003, 'new_value': 4301.64}, {'field': 'onlineCount', 'old_value': 79, 'new_value': 134}]
2025-06-03 08:09:09,289 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:09,789 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-03 08:09:09,789 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13322.32, 'new_value': 18589.58}, {'field': 'dailyBillAmount', 'old_value': 13322.32, 'new_value': 18589.58}, {'field': 'amount', 'old_value': 13933.95, 'new_value': 19122.510000000002}, {'field': 'count', 'old_value': 406, 'new_value': 583}, {'field': 'instoreAmount', 'old_value': 13993.85, 'new_value': 19144.95}, {'field': 'instoreCount', 'old_value': 406, 'new_value': 582}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 37.9}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-06-03 08:09:09,789 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:10,257 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-03 08:09:10,257 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20256.0, 'new_value': 45597.0}, {'field': 'dailyBillAmount', 'old_value': 20256.0, 'new_value': 45597.0}, {'field': 'amount', 'old_value': 24344.0, 'new_value': 49004.0}, {'field': 'count', 'old_value': 22, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 26446.0, 'new_value': 51106.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 35}]
2025-06-03 08:09:10,257 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:10,726 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-03 08:09:10,726 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16137.6, 'new_value': 25609.800000000003}, {'field': 'dailyBillAmount', 'old_value': 16137.6, 'new_value': 25609.800000000003}, {'field': 'amount', 'old_value': 17470.5, 'new_value': 26193.0}, {'field': 'count', 'old_value': 31, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 17470.61, 'new_value': 26627.41}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 51}]
2025-06-03 08:09:10,726 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:11,148 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMHK
2025-06-03 08:09:11,148 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3208.0, 'new_value': 4678.0}, {'field': 'amount', 'old_value': 3208.0, 'new_value': 4678.0}, {'field': 'count', 'old_value': 8, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 3208.0, 'new_value': 4678.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 15}]
2025-06-03 08:09:11,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:11,632 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-03 08:09:11,632 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3143.0, 'new_value': 6800.0}, {'field': 'dailyBillAmount', 'old_value': 3143.0, 'new_value': 6800.0}, {'field': 'amount', 'old_value': 89.0, 'new_value': 1535.0}, {'field': 'count', 'old_value': 1, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 89.0, 'new_value': 1535.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 5}]
2025-06-03 08:09:11,632 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:12,054 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-03 08:09:12,054 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4438.0, 'new_value': 7250.0}, {'field': 'amount', 'old_value': 4438.0, 'new_value': 7250.0}, {'field': 'count', 'old_value': 5, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 4438.0, 'new_value': 7250.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 8}]
2025-06-03 08:09:12,054 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:12,539 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-03 08:09:12,539 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7310.7, 'new_value': 10454.5}, {'field': 'amount', 'old_value': 7310.7, 'new_value': 10454.5}, {'field': 'count', 'old_value': 18, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 7310.7, 'new_value': 10454.5}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 25}]
2025-06-03 08:09:12,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:13,007 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-03 08:09:13,007 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32196.0, 'new_value': 53915.0}, {'field': 'dailyBillAmount', 'old_value': 32196.0, 'new_value': 53915.0}, {'field': 'amount', 'old_value': 32196.0, 'new_value': 53915.0}, {'field': 'count', 'old_value': 4, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 32196.0, 'new_value': 53915.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 6}]
2025-06-03 08:09:13,007 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:13,445 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMMK
2025-06-03 08:09:13,445 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4285.0, 'new_value': 18947.0}, {'field': 'amount', 'old_value': 4285.0, 'new_value': 18947.0}, {'field': 'count', 'old_value': 1, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 4285.0, 'new_value': 18947.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 6}]
2025-06-03 08:09:13,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:13,960 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-03 08:09:13,960 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1796.0, 'new_value': 2514.0}, {'field': 'amount', 'old_value': 1796.0, 'new_value': 2514.0}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1796.0, 'new_value': 2514.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-06-03 08:09:13,960 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:14,460 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMOK
2025-06-03 08:09:14,460 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3626.0, 'new_value': 7114.0}, {'field': 'amount', 'old_value': 3626.0, 'new_value': 7114.0}, {'field': 'count', 'old_value': 4, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 3626.0, 'new_value': 7114.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 8}]
2025-06-03 08:09:14,460 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:14,913 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-03 08:09:14,913 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21867.2, 'new_value': 33203.8}, {'field': 'dailyBillAmount', 'old_value': 21867.2, 'new_value': 33203.8}, {'field': 'amount', 'old_value': 22536.8, 'new_value': 32370.2}, {'field': 'count', 'old_value': 30, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 22915.8, 'new_value': 32749.6}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 44}]
2025-06-03 08:09:14,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:15,351 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-03 08:09:15,351 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 505.8, 'new_value': 683.1}, {'field': 'count', 'old_value': 7, 'new_value': 9}, {'field': 'onlineAmount', 'old_value': 505.8, 'new_value': 683.5}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 9}]
2025-06-03 08:09:15,351 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:15,913 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-03 08:09:15,913 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1110.0, 'new_value': 2089.0}, {'field': 'dailyBillAmount', 'old_value': 1110.0, 'new_value': 2089.0}, {'field': 'amount', 'old_value': 1110.0, 'new_value': 2084.0}, {'field': 'count', 'old_value': 6, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 1301.0, 'new_value': 2275.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 9}]
2025-06-03 08:09:15,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:16,320 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-03 08:09:16,320 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3338.9, 'new_value': 5636.700000000001}, {'field': 'amount', 'old_value': 3338.9, 'new_value': 5635.9}, {'field': 'count', 'old_value': 21, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 3338.9, 'new_value': 5744.700000000001}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 30}]
2025-06-03 08:09:16,320 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:16,773 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-03 08:09:16,773 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 389.0, 'new_value': 1079.0}, {'field': 'dailyBillAmount', 'old_value': 389.0, 'new_value': 1079.0}, {'field': 'amount', 'old_value': 4280.0, 'new_value': 9799.0}, {'field': 'count', 'old_value': 11, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 4280.0, 'new_value': 9799.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 19}]
2025-06-03 08:09:16,773 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:17,273 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-03 08:09:17,273 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 152646.33, 'new_value': 207213.9}, {'field': 'dailyBillAmount', 'old_value': 152646.33, 'new_value': 207213.9}, {'field': 'amount', 'old_value': 7378.54, 'new_value': 12242.0}, {'field': 'count', 'old_value': 80, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 5255.9, 'new_value': 9606.369999999999}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 86}, {'field': 'onlineAmount', 'old_value': 2122.86, 'new_value': 2636.8}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 31}]
2025-06-03 08:09:17,273 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:17,804 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-03 08:09:17,804 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4433.0, 'new_value': 5391.0}, {'field': 'amount', 'old_value': 4433.0, 'new_value': 5391.0}, {'field': 'count', 'old_value': 6, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 4832.0, 'new_value': 5790.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 10}]
2025-06-03 08:09:17,804 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:18,304 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-03 08:09:18,304 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2665.2, 'new_value': 5222.799999999999}, {'field': 'amount', 'old_value': 2665.2, 'new_value': 5222.799999999999}, {'field': 'count', 'old_value': 6, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 2665.2, 'new_value': 5222.799999999999}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 15}]
2025-06-03 08:09:18,304 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:18,851 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-03 08:09:18,851 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1521.0, 'new_value': 2789.0}, {'field': 'dailyBillAmount', 'old_value': 1521.0, 'new_value': 2789.0}, {'field': 'amount', 'old_value': 1521.0, 'new_value': 2842.0}, {'field': 'count', 'old_value': 9, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 2889.0, 'new_value': 4477.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 12}]
2025-06-03 08:09:18,851 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:19,429 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-03 08:09:19,429 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21087.359999999997, 'new_value': 38555.08}, {'field': 'dailyBillAmount', 'old_value': 20077.44, 'new_value': 36755.24}, {'field': 'amount', 'old_value': 20949.379999999997, 'new_value': 38416.46}, {'field': 'count', 'old_value': 145, 'new_value': 222}, {'field': 'instoreAmount', 'old_value': 20949.379999999997, 'new_value': 38417.1}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 222}]
2025-06-03 08:09:19,429 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:19,867 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-03 08:09:19,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21240.0, 'new_value': 28024.0}, {'field': 'amount', 'old_value': 21240.0, 'new_value': 28024.0}, {'field': 'count', 'old_value': 94, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 21936.0, 'new_value': 29068.0}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 146}]
2025-06-03 08:09:19,867 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:20,351 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-03 08:09:20,351 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23569.0, 'new_value': 35078.5}, {'field': 'dailyBillAmount', 'old_value': 23569.0, 'new_value': 35078.5}, {'field': 'amount', 'old_value': 23569.0, 'new_value': 34782.5}, {'field': 'count', 'old_value': 117, 'new_value': 184}, {'field': 'instoreAmount', 'old_value': 23569.0, 'new_value': 34782.5}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 184}]
2025-06-03 08:09:20,351 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:20,820 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-03 08:09:20,820 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10588.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10588.1}, {'field': 'amount', 'old_value': 3810.5, 'new_value': 7190.0}, {'field': 'count', 'old_value': 283, 'new_value': 562}, {'field': 'instoreAmount', 'old_value': 3937.98, 'new_value': 7415.91}, {'field': 'instoreCount', 'old_value': 283, 'new_value': 562}]
2025-06-03 08:09:20,820 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:21,304 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-03 08:09:21,304 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44339.14, 'new_value': 79276.76000000001}, {'field': 'dailyBillAmount', 'old_value': 44339.14, 'new_value': 79276.76000000001}, {'field': 'amount', 'old_value': 47349.67, 'new_value': 80427.0}, {'field': 'count', 'old_value': 393, 'new_value': 661}, {'field': 'instoreAmount', 'old_value': 42151.92, 'new_value': 72455.65}, {'field': 'instoreCount', 'old_value': 199, 'new_value': 342}, {'field': 'onlineAmount', 'old_value': 5295.86, 'new_value': 8249.91}, {'field': 'onlineCount', 'old_value': 194, 'new_value': 319}]
2025-06-03 08:09:21,304 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:21,742 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-03 08:09:21,742 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23668.5, 'new_value': 34914.5}, {'field': 'amount', 'old_value': 23668.5, 'new_value': 34914.5}, {'field': 'count', 'old_value': 134, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 23668.5, 'new_value': 34914.5}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 201}]
2025-06-03 08:09:21,742 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:22,179 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-03 08:09:22,179 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5708.47, 'new_value': 9773.66}, {'field': 'dailyBillAmount', 'old_value': 5708.47, 'new_value': 9773.66}, {'field': 'amount', 'old_value': 8410.5, 'new_value': 13462.0}, {'field': 'count', 'old_value': 425, 'new_value': 666}, {'field': 'instoreAmount', 'old_value': 5961.92, 'new_value': 8883.29}, {'field': 'instoreCount', 'old_value': 300, 'new_value': 447}, {'field': 'onlineAmount', 'old_value': 2563.12, 'new_value': 4719.719999999999}, {'field': 'onlineCount', 'old_value': 125, 'new_value': 219}]
2025-06-03 08:09:22,179 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:22,601 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7S
2025-06-03 08:09:22,601 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-06, 变更字段: [{'field': 'count', 'old_value': 34, 'new_value': 39}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 39}]
2025-06-03 08:09:22,601 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:23,101 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-03 08:09:23,101 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8598.0, 'new_value': 9479.0}, {'field': 'amount', 'old_value': 8598.0, 'new_value': 9479.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 8598.0, 'new_value': 9479.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-03 08:09:23,101 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:23,570 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-03 08:09:23,570 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10050.98, 'new_value': 15317.25}, {'field': 'dailyBillAmount', 'old_value': 10050.98, 'new_value': 15317.25}, {'field': 'amount', 'old_value': 3672.9100000000003, 'new_value': 5885.110000000001}, {'field': 'count', 'old_value': 211, 'new_value': 345}, {'field': 'instoreAmount', 'old_value': 1035.3, 'new_value': 1696.6999999999998}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 76}, {'field': 'onlineAmount', 'old_value': 2637.61, 'new_value': 4189.01}, {'field': 'onlineCount', 'old_value': 156, 'new_value': 269}]
2025-06-03 08:09:23,570 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:24,085 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-03 08:09:24,085 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28328.99, 'new_value': 46707.130000000005}, {'field': 'dailyBillAmount', 'old_value': 28328.99, 'new_value': 46707.130000000005}, {'field': 'amount', 'old_value': 27932.7, 'new_value': 44388.0}, {'field': 'count', 'old_value': 168, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 24366.5, 'new_value': 38129.71}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 198}, {'field': 'onlineAmount', 'old_value': 3566.7, 'new_value': 6259.4}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 99}]
2025-06-03 08:09:24,085 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:24,601 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-03 08:09:24,601 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35766.54, 'new_value': 56996.44}, {'field': 'dailyBillAmount', 'old_value': 35766.54, 'new_value': 56996.44}, {'field': 'amount', 'old_value': 34767.9, 'new_value': 53807.0}, {'field': 'count', 'old_value': 178, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 34936.3, 'new_value': 54216.5}, {'field': 'instoreCount', 'old_value': 178, 'new_value': 284}]
2025-06-03 08:09:24,601 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:25,038 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-03 08:09:25,038 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72230.92, 'new_value': 121928.91}, {'field': 'dailyBillAmount', 'old_value': 72230.92, 'new_value': 121928.91}, {'field': 'amount', 'old_value': 82482.8, 'new_value': 134841.0}, {'field': 'count', 'old_value': 373, 'new_value': 667}, {'field': 'instoreAmount', 'old_value': 70005.64, 'new_value': 111667.35999999999}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 431}, {'field': 'onlineAmount', 'old_value': 12510.900000000001, 'new_value': 23229.6}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 236}]
2025-06-03 08:09:25,038 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:25,492 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-03 08:09:25,492 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35107.76, 'new_value': 49341.29}, {'field': 'dailyBillAmount', 'old_value': 35107.76, 'new_value': 49341.29}, {'field': 'amount', 'old_value': 47507.71, 'new_value': 65720.1}, {'field': 'count', 'old_value': 197, 'new_value': 281}, {'field': 'instoreAmount', 'old_value': 45596.8, 'new_value': 62955.600000000006}, {'field': 'instoreCount', 'old_value': 172, 'new_value': 240}, {'field': 'onlineAmount', 'old_value': 1910.9099999999999, 'new_value': 2970.6099999999997}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 41}]
2025-06-03 08:09:25,492 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:25,976 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-03 08:09:25,976 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37145.66, 'new_value': 53586.04000000001}, {'field': 'dailyBillAmount', 'old_value': 37145.66, 'new_value': 53586.04000000001}, {'field': 'amount', 'old_value': 35904.0, 'new_value': 51169.1}, {'field': 'count', 'old_value': 141, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 36566.9, 'new_value': 52129.0}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 209}]
2025-06-03 08:09:25,976 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:26,445 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-03 08:09:26,445 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70327.01000000001, 'new_value': 113842.61}, {'field': 'amount', 'old_value': 70327.01000000001, 'new_value': 113842.41}, {'field': 'count', 'old_value': 409, 'new_value': 710}, {'field': 'instoreAmount', 'old_value': 70327.01000000001, 'new_value': 113842.61}, {'field': 'instoreCount', 'old_value': 409, 'new_value': 710}]
2025-06-03 08:09:26,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:27,038 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-03 08:09:27,038 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 54727.34, 'new_value': 96733.10999999999}, {'field': 'dailyBillAmount', 'old_value': 54727.34, 'new_value': 96733.10999999999}, {'field': 'amount', 'old_value': 65751.4, 'new_value': 110963.0}, {'field': 'count', 'old_value': 407, 'new_value': 710}, {'field': 'instoreAmount', 'old_value': 45509.0, 'new_value': 74353.8}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 371}, {'field': 'onlineAmount', 'old_value': 21060.0, 'new_value': 38048.8}, {'field': 'onlineCount', 'old_value': 182, 'new_value': 339}]
2025-06-03 08:09:27,038 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:27,429 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-03 08:09:27,429 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46491.8, 'new_value': 74064.98000000001}, {'field': 'dailyBillAmount', 'old_value': 46491.8, 'new_value': 74064.98000000001}, {'field': 'amount', 'old_value': 49359.43, 'new_value': 74993.95999999999}, {'field': 'count', 'old_value': 358, 'new_value': 587}, {'field': 'instoreAmount', 'old_value': 42863.5, 'new_value': 62555.06}, {'field': 'instoreCount', 'old_value': 244, 'new_value': 368}, {'field': 'onlineAmount', 'old_value': 6546.89, 'new_value': 12557.93}, {'field': 'onlineCount', 'old_value': 114, 'new_value': 219}]
2025-06-03 08:09:27,429 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:27,945 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-03 08:09:27,945 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42357.63, 'new_value': 69082.09}, {'field': 'dailyBillAmount', 'old_value': 42357.63, 'new_value': 69082.09}, {'field': 'amount', 'old_value': 42865.38, 'new_value': 68935.53}, {'field': 'count', 'old_value': 283, 'new_value': 477}, {'field': 'instoreAmount', 'old_value': 40344.34, 'new_value': 64618.59}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 314}, {'field': 'onlineAmount', 'old_value': 2521.04, 'new_value': 4343.4}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 163}]
2025-06-03 08:09:27,945 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:28,429 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-03 08:09:28,429 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5687.0, 'new_value': 11762.0}, {'field': 'amount', 'old_value': 5687.0, 'new_value': 11762.0}, {'field': 'count', 'old_value': 34, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 5687.0, 'new_value': 11762.0}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 70}]
2025-06-03 08:09:28,429 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:28,882 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-03 08:09:28,882 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58705.26, 'new_value': 75343.12}, {'field': 'dailyBillAmount', 'old_value': 58705.26, 'new_value': 75343.12}, {'field': 'amount', 'old_value': -32270.0, 'new_value': -48274.0}, {'field': 'count', 'old_value': 71, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 993.5, 'new_value': 1278.5}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 55}, {'field': 'onlineAmount', 'old_value': 949.06, 'new_value': 1546.12}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 55}]
2025-06-03 08:09:28,882 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:29,351 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-03 08:09:29,351 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 24879.33}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24879.33}, {'field': 'amount', 'old_value': 41830.06, 'new_value': 59332.06}, {'field': 'count', 'old_value': 164, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 41830.06, 'new_value': 59332.81999999999}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 251}]
2025-06-03 08:09:29,351 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:29,757 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-03 08:09:29,757 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46849.16, 'new_value': 70156.5}, {'field': 'dailyBillAmount', 'old_value': 46849.16, 'new_value': 70156.5}, {'field': 'amount', 'old_value': 18386.8, 'new_value': 24775.0}, {'field': 'count', 'old_value': 83, 'new_value': 111}, {'field': 'instoreAmount', 'old_value': 19276.9, 'new_value': 25580.5}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 109}, {'field': 'onlineAmount', 'old_value': 148.9, 'new_value': 234.5}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-06-03 08:09:29,757 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:30,148 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-03 08:09:30,148 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34540.23, 'new_value': 50883.64}, {'field': 'dailyBillAmount', 'old_value': 34540.23, 'new_value': 50883.64}, {'field': 'amount', 'old_value': 34615.829999999994, 'new_value': 50270.81}, {'field': 'count', 'old_value': 158, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 34290.47, 'new_value': 49066.99}, {'field': 'instoreCount', 'old_value': 148, 'new_value': 228}, {'field': 'onlineAmount', 'old_value': 325.36, 'new_value': 1204.1}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 34}]
2025-06-03 08:09:30,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:30,648 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-03 08:09:30,648 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31918.26, 'new_value': 48219.79}, {'field': 'dailyBillAmount', 'old_value': 31918.26, 'new_value': 48219.79}, {'field': 'amount', 'old_value': 15004.720000000001, 'new_value': 21707.760000000002}, {'field': 'count', 'old_value': 162, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 12265.84, 'new_value': 17688.25}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 107}, {'field': 'onlineAmount', 'old_value': 2738.88, 'new_value': 4020.45}, {'field': 'onlineCount', 'old_value': 88, 'new_value': 144}]
2025-06-03 08:09:30,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:30,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:31,054 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-03 08:09:31,054 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8890.01, 'new_value': 13048.82}, {'field': 'amount', 'old_value': 8890.01, 'new_value': 13048.01}, {'field': 'count', 'old_value': 452, 'new_value': 651}, {'field': 'instoreAmount', 'old_value': 1744.6000000000001, 'new_value': 3031.59}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 150}, {'field': 'onlineAmount', 'old_value': 7358.610000000001, 'new_value': 10279.33}, {'field': 'onlineCount', 'old_value': 359, 'new_value': 501}]
2025-06-03 08:09:31,054 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:31,492 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-03 08:09:31,507 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35739.689999999995, 'new_value': 56032.33}, {'field': 'dailyBillAmount', 'old_value': 35567.59, 'new_value': 56032.33}, {'field': 'amount', 'old_value': 16444.1, 'new_value': 24917.0}, {'field': 'count', 'old_value': 270, 'new_value': 421}, {'field': 'instoreAmount', 'old_value': 16487.3, 'new_value': 24982.6}, {'field': 'instoreCount', 'old_value': 270, 'new_value': 421}]
2025-06-03 08:09:31,507 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:31,976 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSS
2025-06-03 08:09:31,976 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20077.42, 'new_value': 29403.379999999997}, {'field': 'amount', 'old_value': 20077.42, 'new_value': 29402.0}, {'field': 'count', 'old_value': 413, 'new_value': 634}, {'field': 'instoreAmount', 'old_value': 20077.42, 'new_value': 29403.379999999997}, {'field': 'instoreCount', 'old_value': 413, 'new_value': 634}]
2025-06-03 08:09:31,976 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:32,351 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-03 08:09:32,351 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3347.79, 'new_value': 5058.2}, {'field': 'amount', 'old_value': 3347.48, 'new_value': 5057.0}, {'field': 'count', 'old_value': 173, 'new_value': 276}, {'field': 'instoreAmount', 'old_value': 1961.6, 'new_value': 2945.8999999999996}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 140}, {'field': 'onlineAmount', 'old_value': 1433.19, 'new_value': 2221.94}, {'field': 'onlineCount', 'old_value': 84, 'new_value': 136}]
2025-06-03 08:09:32,351 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:32,866 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-03 08:09:32,866 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8226.0, 'new_value': 11866.9}, {'field': 'amount', 'old_value': 8226.0, 'new_value': 11866.9}, {'field': 'count', 'old_value': 20, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 8226.0, 'new_value': 11866.9}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 29}]
2025-06-03 08:09:32,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:33,366 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-03 08:09:33,366 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15803.85, 'new_value': 22410.63}, {'field': 'dailyBillAmount', 'old_value': 12620.9, 'new_value': 19394.1}, {'field': 'amount', 'old_value': 15803.85, 'new_value': 22410.260000000002}, {'field': 'count', 'old_value': 177, 'new_value': 264}, {'field': 'instoreAmount', 'old_value': 15577.5, 'new_value': 21990.7}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 243}, {'field': 'onlineAmount', 'old_value': 255.35, 'new_value': 448.93}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 21}]
2025-06-03 08:09:33,366 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:33,929 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-03 08:09:33,929 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2290.66, 'new_value': 3975.0}, {'field': 'amount', 'old_value': 2290.66, 'new_value': 3974.66}, {'field': 'count', 'old_value': 97, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 2105.66, 'new_value': 3541.5}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 152}, {'field': 'onlineAmount', 'old_value': 219.4, 'new_value': 467.9}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 12}]
2025-06-03 08:09:33,929 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:34,351 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-03 08:09:34,351 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42176.12, 'new_value': 61172.73}, {'field': 'dailyBillAmount', 'old_value': 42176.12, 'new_value': 61172.73}, {'field': 'amount', 'old_value': 55649.83, 'new_value': 76885.0}, {'field': 'count', 'old_value': 288, 'new_value': 454}, {'field': 'instoreAmount', 'old_value': 54655.24, 'new_value': 75727.96}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 341}, {'field': 'onlineAmount', 'old_value': 1499.38, 'new_value': 2758.46}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 113}]
2025-06-03 08:09:34,351 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:34,913 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-03 08:09:34,913 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10254.03, 'new_value': 10796.93}, {'field': 'dailyBillAmount', 'old_value': 10254.03, 'new_value': 10796.93}, {'field': 'amount', 'old_value': 3754.52, 'new_value': 5581.3}, {'field': 'count', 'old_value': 29, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 2547.52, 'new_value': 3874.8199999999997}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 23}, {'field': 'onlineAmount', 'old_value': 1207.0, 'new_value': 1730.4}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 23}]
2025-06-03 08:09:34,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:35,413 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-03 08:09:35,413 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5075.530000000001, 'new_value': 9344.77}, {'field': 'dailyBillAmount', 'old_value': 4240.86, 'new_value': 9317.009999999998}, {'field': 'amount', 'old_value': 5075.530000000001, 'new_value': 9344.77}, {'field': 'count', 'old_value': 272, 'new_value': 482}, {'field': 'instoreAmount', 'old_value': 2251.56, 'new_value': 4379.13}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 197}, {'field': 'onlineAmount', 'old_value': 2852.7200000000003, 'new_value': 4997.39}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 285}]
2025-06-03 08:09:35,413 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:35,866 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-03 08:09:35,866 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4714.59, 'new_value': 7151.67}, {'field': 'amount', 'old_value': 4714.59, 'new_value': 7151.46}, {'field': 'count', 'old_value': 255, 'new_value': 408}, {'field': 'instoreAmount', 'old_value': 3193.74, 'new_value': 4769.44}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 238}, {'field': 'onlineAmount', 'old_value': 1674.06, 'new_value': 2679.91}, {'field': 'onlineCount', 'old_value': 108, 'new_value': 170}]
2025-06-03 08:09:35,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:36,413 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-03 08:09:36,413 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 17061.62, 'new_value': 27731.62}, {'field': 'count', 'old_value': 125, 'new_value': 215}, {'field': 'instoreAmount', 'old_value': 17074.62, 'new_value': 27745.1}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 215}]
2025-06-03 08:09:36,413 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:36,866 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-03 08:09:36,866 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8172.89, 'new_value': 11811.060000000001}, {'field': 'dailyBillAmount', 'old_value': 7841.91, 'new_value': 12091.58}, {'field': 'amount', 'old_value': 8172.89, 'new_value': 11810.51}, {'field': 'count', 'old_value': 178, 'new_value': 279}, {'field': 'instoreAmount', 'old_value': 7772.1900000000005, 'new_value': 11052.55}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 217}, {'field': 'onlineAmount', 'old_value': 400.7, 'new_value': 758.51}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 62}]
2025-06-03 08:09:36,882 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:37,288 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-03 08:09:37,288 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5566.94, 'new_value': 10314.98}, {'field': 'dailyBillAmount', 'old_value': 5566.94, 'new_value': 10314.98}, {'field': 'amount', 'old_value': 1144.47, 'new_value': 1733.17}, {'field': 'count', 'old_value': 34, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 1179.77, 'new_value': 1779.6399999999999}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 50}]
2025-06-03 08:09:37,288 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:37,804 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-03 08:09:37,804 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42829.46, 'new_value': 62265.46}, {'field': 'dailyBillAmount', 'old_value': 42829.46, 'new_value': 62265.46}, {'field': 'amount', 'old_value': 2613.3, 'new_value': 4427.200000000001}, {'field': 'count', 'old_value': 13, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 2613.3, 'new_value': 4427.200000000001}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 23}]
2025-06-03 08:09:37,804 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:38,304 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-03 08:09:38,304 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 615.7, 'new_value': 1035.3600000000001}, {'field': 'count', 'old_value': 28, 'new_value': 44}, {'field': 'onlineAmount', 'old_value': 615.7, 'new_value': 1059.04}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 44}]
2025-06-03 08:09:38,304 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:38,773 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-03 08:09:38,773 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17971.420000000002, 'new_value': 25185.760000000002}, {'field': 'amount', 'old_value': 17971.420000000002, 'new_value': 25184.85}, {'field': 'count', 'old_value': 154, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 16940.2, 'new_value': 23440.2}, {'field': 'instoreCount', 'old_value': 122, 'new_value': 170}, {'field': 'onlineAmount', 'old_value': 1267.03, 'new_value': 2005.27}, {'field': 'onlineCount', 'old_value': 32, 'new_value': 42}]
2025-06-03 08:09:38,788 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:39,226 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-03 08:09:39,226 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9873.400000000001, 'new_value': 12549.07}, {'field': 'amount', 'old_value': 6556.86, 'new_value': 9232.33}, {'field': 'count', 'old_value': 205, 'new_value': 316}, {'field': 'instoreAmount', 'old_value': 1747.3000000000002, 'new_value': 2293.1800000000003}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 34}, {'field': 'onlineAmount', 'old_value': 4876.58, 'new_value': 7020.33}, {'field': 'onlineCount', 'old_value': 187, 'new_value': 282}]
2025-06-03 08:09:39,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:39,648 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-03 08:09:39,648 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'amount', 'old_value': 181.44, 'new_value': 237.74}, {'field': 'count', 'old_value': 7, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 181.44, 'new_value': 238.64000000000001}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 9}]
2025-06-03 08:09:39,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:40,023 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-03 08:09:40,023 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 194.88, 'new_value': 506.98}, {'field': 'count', 'old_value': 11, 'new_value': 24}, {'field': 'onlineAmount', 'old_value': 194.88, 'new_value': 526.06}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 24}]
2025-06-03 08:09:40,023 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:40,445 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-03 08:09:40,445 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6527.25, 'new_value': 9902.49}, {'field': 'dailyBillAmount', 'old_value': 4222.52, 'new_value': 6037.22}, {'field': 'amount', 'old_value': 6527.25, 'new_value': 9901.6}, {'field': 'count', 'old_value': 157, 'new_value': 257}, {'field': 'instoreAmount', 'old_value': 4727.72, 'new_value': 6642.59}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 167}, {'field': 'onlineAmount', 'old_value': 2006.77, 'new_value': 3531.14}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 90}]
2025-06-03 08:09:40,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:40,882 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-03 08:09:40,882 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6551.79, 'new_value': 8727.86}, {'field': 'amount', 'old_value': 6551.789999999999, 'new_value': 8727.59}, {'field': 'count', 'old_value': 313, 'new_value': 414}, {'field': 'instoreAmount', 'old_value': 6620.5599999999995, 'new_value': 8827.46}, {'field': 'instoreCount', 'old_value': 313, 'new_value': 414}]
2025-06-03 08:09:40,882 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:41,366 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-03 08:09:41,366 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2841.41, 'new_value': 4179.83}, {'field': 'dailyBillAmount', 'old_value': 2841.41, 'new_value': 4179.83}, {'field': 'amount', 'old_value': 1290.59, 'new_value': 1931.01}, {'field': 'count', 'old_value': 73, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 595.9, 'new_value': 930.8}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 36}, {'field': 'onlineAmount', 'old_value': 694.69, 'new_value': 1001.1600000000001}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 65}]
2025-06-03 08:09:41,366 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:41,835 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-03 08:09:41,835 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3651.2, 'new_value': 6042.26}, {'field': 'amount', 'old_value': 3651.2, 'new_value': 6042.24}, {'field': 'count', 'old_value': 112, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 1925.3500000000001, 'new_value': 3097.16}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 125}, {'field': 'onlineAmount', 'old_value': 1725.85, 'new_value': 2945.1}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 72}]
2025-06-03 08:09:41,835 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:42,398 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-03 08:09:42,398 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2491.2200000000003, 'new_value': 3763.45}, {'field': 'amount', 'old_value': 2491.2200000000003, 'new_value': 3762.61}, {'field': 'count', 'old_value': 65, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 2127.9, 'new_value': 3251.9}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 77}, {'field': 'onlineAmount', 'old_value': 363.32, 'new_value': 571.55}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 14}]
2025-06-03 08:09:42,398 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:42,835 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-03 08:09:42,835 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9354.8, 'new_value': 15823.099999999999}, {'field': 'dailyBillAmount', 'old_value': 9354.8, 'new_value': 15823.099999999999}, {'field': 'amount', 'old_value': 8910.98, 'new_value': 10233.369999999999}, {'field': 'count', 'old_value': 204, 'new_value': 246}, {'field': 'instoreAmount', 'old_value': 5104.700000000001, 'new_value': 6226.700000000001}, {'field': 'instoreCount', 'old_value': 96, 'new_value': 120}, {'field': 'onlineAmount', 'old_value': 4000.28, 'new_value': 4574.71}, {'field': 'onlineCount', 'old_value': 108, 'new_value': 126}]
2025-06-03 08:09:42,835 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:43,257 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-03 08:09:43,257 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61386.34, 'new_value': 96721.81}, {'field': 'dailyBillAmount', 'old_value': 61386.34, 'new_value': 96721.81}, {'field': 'amount', 'old_value': 50655.399999999994, 'new_value': 77254.7}, {'field': 'count', 'old_value': 318, 'new_value': 505}, {'field': 'instoreAmount', 'old_value': 41139.100000000006, 'new_value': 63308.200000000004}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 438}, {'field': 'onlineAmount', 'old_value': 9516.3, 'new_value': 13947.3}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 67}]
2025-06-03 08:09:43,257 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:43,804 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-03 08:09:43,804 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64655.06, 'new_value': 99819.76000000001}, {'field': 'amount', 'old_value': 64655.06, 'new_value': 99819.45999999999}, {'field': 'count', 'old_value': 213, 'new_value': 352}, {'field': 'instoreAmount', 'old_value': 64655.06, 'new_value': 99819.76000000001}, {'field': 'instoreCount', 'old_value': 213, 'new_value': 352}]
2025-06-03 08:09:43,804 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:44,241 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-03 08:09:44,241 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32878.67, 'new_value': 50212.14}, {'field': 'dailyBillAmount', 'old_value': 31073.91, 'new_value': 46711.94}, {'field': 'amount', 'old_value': 32878.67, 'new_value': 50211.4}, {'field': 'count', 'old_value': 169, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 31189.93, 'new_value': 46754.24}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 200}, {'field': 'onlineAmount', 'old_value': 1688.74, 'new_value': 3457.9}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 62}]
2025-06-03 08:09:44,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:44,710 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-03 08:09:44,710 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23031.28, 'new_value': 36698.7}, {'field': 'amount', 'old_value': 23031.28, 'new_value': 36698.7}, {'field': 'count', 'old_value': 77, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 20443.0, 'new_value': 32019.0}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 87}, {'field': 'onlineAmount', 'old_value': 2588.28, 'new_value': 4846.530000000001}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 38}]
2025-06-03 08:09:44,710 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:45,163 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-03 08:09:45,163 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47920.7, 'new_value': 73451.23}, {'field': 'amount', 'old_value': 47920.7, 'new_value': 73450.28}, {'field': 'count', 'old_value': 230, 'new_value': 366}, {'field': 'instoreAmount', 'old_value': 45018.0, 'new_value': 68554.0}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 246}, {'field': 'onlineAmount', 'old_value': 2902.7000000000003, 'new_value': 4897.23}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 120}]
2025-06-03 08:09:45,163 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:45,663 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-03 08:09:45,663 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 39765.41, 'new_value': 55604.0}, {'field': 'count', 'old_value': 206, 'new_value': 298}, {'field': 'instoreAmount', 'old_value': 36382.39, 'new_value': 50336.59}, {'field': 'instoreCount', 'old_value': 179, 'new_value': 251}, {'field': 'onlineAmount', 'old_value': 3383.25, 'new_value': 5526.01}, {'field': 'onlineCount', 'old_value': 27, 'new_value': 47}]
2025-06-03 08:09:45,679 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:46,116 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-03 08:09:46,116 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11938.0, 'new_value': 15640.0}, {'field': 'dailyBillAmount', 'old_value': 11938.0, 'new_value': 15640.0}, {'field': 'amount', 'old_value': 11642.0, 'new_value': 15171.0}, {'field': 'count', 'old_value': 11, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 11642.0, 'new_value': 15171.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 22}]
2025-06-03 08:09:46,116 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:46,585 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-03 08:09:46,585 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9276.06, 'new_value': 13770.32}, {'field': 'dailyBillAmount', 'old_value': 9276.06, 'new_value': 13770.32}, {'field': 'amount', 'old_value': 7880.0, 'new_value': 13421.0}, {'field': 'count', 'old_value': 7, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 7620.09, 'new_value': 14297.09}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 16}, {'field': 'onlineAmount', 'old_value': 260.8, 'new_value': 322.8}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-06-03 08:09:46,585 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:46,991 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-03 08:09:46,991 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1293.55, 'new_value': 1838.81}, {'field': 'amount', 'old_value': 1293.55, 'new_value': 1838.57}, {'field': 'count', 'old_value': 27, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 1293.55, 'new_value': 1838.81}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 41}]
2025-06-03 08:09:46,991 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:47,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-03 08:09:47,523 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3910.81, 'new_value': 6097.49}, {'field': 'amount', 'old_value': 3910.81, 'new_value': 6096.5}, {'field': 'count', 'old_value': 36, 'new_value': 58}, {'field': 'instoreAmount', 'old_value': 3910.81, 'new_value': 6097.49}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 58}]
2025-06-03 08:09:47,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:47,960 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-03 08:09:47,960 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17474.39, 'new_value': 32850.34}, {'field': 'dailyBillAmount', 'old_value': 17474.39, 'new_value': 32850.34}, {'field': 'amount', 'old_value': 18890.58, 'new_value': 35483.880000000005}, {'field': 'count', 'old_value': 524, 'new_value': 926}, {'field': 'instoreAmount', 'old_value': 18240.8, 'new_value': 33709.8}, {'field': 'instoreCount', 'old_value': 472, 'new_value': 825}, {'field': 'onlineAmount', 'old_value': 999.8299999999999, 'new_value': 2195.1}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 101}]
2025-06-03 08:09:47,976 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:48,445 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-03 08:09:48,445 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23460.64, 'new_value': 51533.92}, {'field': 'dailyBillAmount', 'old_value': 23460.64, 'new_value': 51533.92}, {'field': 'amount', 'old_value': 24286.579999999998, 'new_value': 51405.16}, {'field': 'count', 'old_value': 75, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 23914.64, 'new_value': 51181.380000000005}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 116}, {'field': 'onlineAmount', 'old_value': 371.94, 'new_value': 577.21}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 14}]
2025-06-03 08:09:48,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:48,913 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-03 08:09:48,913 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58059.31, 'new_value': 98246.36}, {'field': 'amount', 'old_value': 58059.31, 'new_value': 98246.31}, {'field': 'count', 'old_value': 214, 'new_value': 345}, {'field': 'instoreAmount', 'old_value': 58059.31, 'new_value': 98246.36}, {'field': 'instoreCount', 'old_value': 214, 'new_value': 345}]
2025-06-03 08:09:48,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:49,366 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-03 08:09:49,366 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33890.84, 'new_value': 58481.149999999994}, {'field': 'dailyBillAmount', 'old_value': 33890.84, 'new_value': 58481.149999999994}, {'field': 'amount', 'old_value': 24230.89, 'new_value': 41431.69}, {'field': 'count', 'old_value': 102, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 23325.19, 'new_value': 41406.14}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 105}, {'field': 'onlineAmount', 'old_value': 1586.6000000000001, 'new_value': 1799.7}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 50}]
2025-06-03 08:09:49,366 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:49,757 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-03 08:09:49,757 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84357.01, 'new_value': 145548.74}, {'field': 'dailyBillAmount', 'old_value': 84357.01, 'new_value': 145548.74}, {'field': 'amount', 'old_value': 63216.0, 'new_value': 118818.0}, {'field': 'count', 'old_value': 161, 'new_value': 286}, {'field': 'instoreAmount', 'old_value': 68512.0, 'new_value': 125454.0}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 286}]
2025-06-03 08:09:49,757 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:50,226 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-03 08:09:50,226 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15748.74, 'new_value': 28015.53}, {'field': 'dailyBillAmount', 'old_value': 15748.74, 'new_value': 28015.53}, {'field': 'amount', 'old_value': 16313.869999999999, 'new_value': 27974.739999999998}, {'field': 'count', 'old_value': 74, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 15188.0, 'new_value': 26392.0}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 113}, {'field': 'onlineAmount', 'old_value': 1235.49, 'new_value': 1729.15}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 19}]
2025-06-03 08:09:50,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:50,788 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-03 08:09:50,788 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18502.25, 'new_value': 54835.3}, {'field': 'dailyBillAmount', 'old_value': 18502.25, 'new_value': 54835.3}, {'field': 'amount', 'old_value': 55245.35, 'new_value': 91097.17}, {'field': 'count', 'old_value': 212, 'new_value': 371}, {'field': 'instoreAmount', 'old_value': 55245.35, 'new_value': 91097.22}, {'field': 'instoreCount', 'old_value': 212, 'new_value': 371}]
2025-06-03 08:09:50,788 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:51,273 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-03 08:09:51,273 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22872.89, 'new_value': 32048.18}, {'field': 'dailyBillAmount', 'old_value': 22872.89, 'new_value': 32048.18}, {'field': 'amount', 'old_value': 36342.0, 'new_value': 55047.8}, {'field': 'count', 'old_value': 49, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 35977.0, 'new_value': 54170.0}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 74}, {'field': 'onlineAmount', 'old_value': 365.2, 'new_value': 878.0}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 4}]
2025-06-03 08:09:51,273 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:51,616 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-03 08:09:51,616 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28386.39, 'new_value': 41685.42}, {'field': 'dailyBillAmount', 'old_value': 28386.39, 'new_value': 41685.42}, {'field': 'amount', 'old_value': 33385.3, 'new_value': 46422.3}, {'field': 'count', 'old_value': 197, 'new_value': 293}, {'field': 'instoreAmount', 'old_value': 33385.3, 'new_value': 46422.3}, {'field': 'instoreCount', 'old_value': 197, 'new_value': 293}]
2025-06-03 08:09:51,632 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:52,069 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-03 08:09:52,069 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14767.1, 'new_value': 27914.95}, {'field': 'dailyBillAmount', 'old_value': 14767.1, 'new_value': 27914.95}, {'field': 'amount', 'old_value': 8601.2, 'new_value': 19312.2}, {'field': 'count', 'old_value': 65, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 8440.0, 'new_value': 20178.0}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 114}]
2025-06-03 08:09:52,069 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:52,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-03 08:09:52,523 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6073.889999999999, 'new_value': 8555.189999999999}, {'field': 'count', 'old_value': 328, 'new_value': 483}, {'field': 'instoreAmount', 'old_value': 795.39, 'new_value': 1076.25}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 88}, {'field': 'onlineAmount', 'old_value': 5695.5, 'new_value': 8009.8}, {'field': 'onlineCount', 'old_value': 272, 'new_value': 395}]
2025-06-03 08:09:52,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:53,070 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-03 08:09:53,070 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8484.33, 'new_value': 11959.45}, {'field': 'amount', 'old_value': 8484.33, 'new_value': 11959.21}, {'field': 'count', 'old_value': 150, 'new_value': 241}, {'field': 'instoreAmount', 'old_value': 6591.13, 'new_value': 9525.25}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 187}, {'field': 'onlineAmount', 'old_value': 1893.2, 'new_value': 2434.2}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 54}]
2025-06-03 08:09:53,070 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:53,460 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-03 08:09:53,460 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1757.6, 'new_value': 4112.4}, {'field': 'dailyBillAmount', 'old_value': 49.0, 'new_value': 153.8}, {'field': 'amount', 'old_value': 1757.6, 'new_value': 4112.0}, {'field': 'count', 'old_value': 10, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 1757.6, 'new_value': 4112.4}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 21}]
2025-06-03 08:09:53,460 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:53,929 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-03 08:09:53,929 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 216.0, 'new_value': 2716.0}, {'field': 'dailyBillAmount', 'old_value': 216.0, 'new_value': 2716.0}, {'field': 'amount', 'old_value': 1712.3999999999999, 'new_value': 3030.8}, {'field': 'count', 'old_value': 12, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 1712.3999999999999, 'new_value': 3031.1000000000004}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 24}]
2025-06-03 08:09:53,929 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:54,648 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-03 08:09:54,648 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5649.0, 'new_value': 5938.0}, {'field': 'dailyBillAmount', 'old_value': 5649.0, 'new_value': 5938.0}]
2025-06-03 08:09:54,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:55,101 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-03 08:09:55,101 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12901.3, 'new_value': 22908.4}, {'field': 'dailyBillAmount', 'old_value': 12901.3, 'new_value': 22908.4}, {'field': 'amount', 'old_value': 13375.7, 'new_value': 19319.2}, {'field': 'count', 'old_value': 328, 'new_value': 509}, {'field': 'instoreAmount', 'old_value': 12258.82, 'new_value': 17991.41}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 478}, {'field': 'onlineAmount', 'old_value': 1116.8799999999999, 'new_value': 1420.58}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 31}]
2025-06-03 08:09:55,116 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:55,632 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-03 08:09:55,632 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1704.5, 'new_value': 5810.4}, {'field': 'dailyBillAmount', 'old_value': 1704.5, 'new_value': 5810.4}, {'field': 'amount', 'old_value': 2533.5, 'new_value': 5809.5}, {'field': 'count', 'old_value': 13, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 2533.5, 'new_value': 6048.2}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 34}]
2025-06-03 08:09:55,632 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:56,069 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-03 08:09:56,069 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2061.17, 'new_value': 3651.92}, {'field': 'amount', 'old_value': 2061.17, 'new_value': 3651.32}, {'field': 'count', 'old_value': 135, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 2096.27, 'new_value': 3695.05}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 235}]
2025-06-03 08:09:56,069 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:56,554 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-03 08:09:56,554 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2775.91, 'new_value': 5427.5599999999995}, {'field': 'dailyBillAmount', 'old_value': 2775.91, 'new_value': 5427.5599999999995}, {'field': 'amount', 'old_value': 3616.21, 'new_value': 5194.52}, {'field': 'count', 'old_value': 172, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 3109.5, 'new_value': 4637.0}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 221}, {'field': 'onlineAmount', 'old_value': 506.71, 'new_value': 558.21}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 28}]
2025-06-03 08:09:56,554 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:57,038 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-03 08:09:57,038 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3779.8500000000004, 'new_value': 5783.16}, {'field': 'amount', 'old_value': 3779.8500000000004, 'new_value': 5783.01}, {'field': 'count', 'old_value': 143, 'new_value': 232}, {'field': 'instoreAmount', 'old_value': 1998.18, 'new_value': 3078.32}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 127}, {'field': 'onlineAmount', 'old_value': 1811.77, 'new_value': 2742.66}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 105}]
2025-06-03 08:09:57,038 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:57,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-03 08:09:57,523 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2060.4, 'new_value': 3434.4700000000003}, {'field': 'dailyBillAmount', 'old_value': 2060.4, 'new_value': 3434.4700000000003}, {'field': 'amount', 'old_value': 1339.07, 'new_value': 2133.4700000000003}, {'field': 'count', 'old_value': 50, 'new_value': 77}, {'field': 'instoreAmount', 'old_value': 1410.6599999999999, 'new_value': 2205.45}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 77}]
2025-06-03 08:09:57,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:57,929 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-03 08:09:57,929 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4117.92, 'new_value': 5845.73}, {'field': 'amount', 'old_value': 4117.9, 'new_value': 5845.0}, {'field': 'count', 'old_value': 252, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 719.59, 'new_value': 1193.5900000000001}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 62}, {'field': 'onlineAmount', 'old_value': 3450.1, 'new_value': 4737.91}, {'field': 'onlineCount', 'old_value': 211, 'new_value': 298}]
2025-06-03 08:09:57,929 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:58,366 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-03 08:09:58,366 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3554.76, 'new_value': 6727.68}, {'field': 'dailyBillAmount', 'old_value': 3554.76, 'new_value': 6727.68}, {'field': 'amount', 'old_value': 2440.2, 'new_value': 4481.2}, {'field': 'count', 'old_value': 24, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 2440.2, 'new_value': 4481.7}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 43}]
2025-06-03 08:09:58,366 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:58,866 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-03 08:09:58,866 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9499.0, 'new_value': 17809.0}, {'field': 'dailyBillAmount', 'old_value': 9499.0, 'new_value': 17809.0}, {'field': 'amount', 'old_value': 11043.0, 'new_value': 21064.0}, {'field': 'count', 'old_value': 44, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 11043.0, 'new_value': 21064.0}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 81}]
2025-06-03 08:09:58,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:59,304 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-03 08:09:59,304 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 5143.0, 'new_value': 7005.0}, {'field': 'count', 'old_value': 25, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 5143.0, 'new_value': 7005.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 34}]
2025-06-03 08:09:59,304 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:09:59,788 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-03 08:09:59,788 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5176.0, 'new_value': 8408.0}, {'field': 'amount', 'old_value': 5176.0, 'new_value': 8408.0}, {'field': 'count', 'old_value': 67, 'new_value': 106}, {'field': 'instoreAmount', 'old_value': 5176.0, 'new_value': 8408.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 106}]
2025-06-03 08:09:59,788 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:10:00,226 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-03 08:10:00,226 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 531.7, 'new_value': 1066.8000000000002}, {'field': 'dailyBillAmount', 'old_value': 531.7, 'new_value': 1066.8000000000002}, {'field': 'amount', 'old_value': 34.6, 'new_value': 219.59999999999997}, {'field': 'count', 'old_value': 2, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 40.6, 'new_value': 330.5}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 10}]
2025-06-03 08:10:00,226 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:10:00,648 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-03 08:10:00,648 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1984.0, 'new_value': 4874.0}, {'field': 'dailyBillAmount', 'old_value': 1984.0, 'new_value': 4874.0}, {'field': 'amount', 'old_value': 2785.0, 'new_value': 4785.0}, {'field': 'count', 'old_value': 11, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 2785.0, 'new_value': 4785.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 25}]
2025-06-03 08:10:00,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:10:01,148 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-03 08:10:01,148 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2464.5, 'new_value': 5665.33}, {'field': 'dailyBillAmount', 'old_value': 2464.5, 'new_value': 5665.33}, {'field': 'amount', 'old_value': 1795.6, 'new_value': 4109.5}, {'field': 'count', 'old_value': 59, 'new_value': 127}, {'field': 'instoreAmount', 'old_value': 1680.3, 'new_value': 3918.83}, {'field': 'instoreCount', 'old_value': 52, 'new_value': 118}, {'field': 'onlineAmount', 'old_value': 115.3, 'new_value': 191.1}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 9}]
2025-06-03 08:10:01,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:10:01,585 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-03 08:10:01,585 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4684.08, 'new_value': 6944.5599999999995}, {'field': 'dailyBillAmount', 'old_value': 4684.08, 'new_value': 6944.5599999999995}, {'field': 'amount', 'old_value': 4563.3, 'new_value': 6652.6}, {'field': 'count', 'old_value': 24, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 4561.3, 'new_value': 6651.4}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 37}]
2025-06-03 08:10:01,585 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-03 08:10:02,038 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-03 08:10:02,038 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12367.83, 'new_value': 19705.78}, {'field': 'dailyBillAmount', 'old_value': 12367.83, 'new_value': 19705.78}, {'field': 'amount', 'old_value': 14771.6, 'new_value': 21087.6}, {'field': 'count', 'old_value': 92, 'new_value': 140}, {'field': 'instoreAmount', 'old_value': 13882.0, 'new_value': 20091.0}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 119}, {'field': 'onlineAmount', 'old_value': 921.6, 'new_value': 1028.6}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 21}]
2025-06-03 08:10:02,038 - INFO - 正在批量插入月度数据，批次 1/1，共 5 条记录
2025-06-03 08:10:02,194 - INFO - 批量插入月度数据成功，批次 1，5 条记录
2025-06-03 08:10:05,210 - INFO - 批量插入月度数据完成: 总计 5 条，成功 5 条，失败 0 条
2025-06-03 08:10:05,210 - INFO - 批量插入月销售数据完成，共 5 条记录
2025-06-03 08:10:05,210 - INFO - 月销售数据同步完成！更新: 196 条，插入: 5 条，错误: 0 条，跳过: 1200 条
2025-06-03 08:10:05,210 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-7 至 2025-6
2025-06-03 08:10:05,835 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250603.xlsx
2025-06-03 08:10:05,835 - INFO - 综合数据同步流程完成！
2025-06-03 08:10:05,866 - INFO - 综合数据同步完成
2025-06-03 08:10:05,866 - INFO - ==================================================
2025-06-03 08:10:05,866 - INFO - 程序退出
2025-06-03 08:10:05,866 - INFO - ==================================================
