2025-06-18 09:00:02,994 - INFO - 数据库连接成功
2025-06-18 09:00:03,166 - INFO - 获取钉钉access_token成功
2025-06-18 09:00:03,166 - INFO - 宜搭客户端初始化成功
2025-06-18 09:00:03,166 - INFO - 正在获取数据库店铺信息...
2025-06-18 09:00:03,197 - INFO - 获取店铺信息成功，共 1261 条记录
2025-06-18 09:00:03,197 - INFO - 成功获取数据库店铺信息，共 1261 条记录
2025-06-18 09:00:03,197 - INFO - 正在获取宜搭店铺信息...
2025-06-18 09:00:04,462 - INFO - 店铺 100101292 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100845 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100858 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100846 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100855 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100843 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100856 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100844 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100853 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100841 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100854 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100842 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100851 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100839 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100852 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100840 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100849 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100850 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100860 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100861 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100859 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100099118 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100834 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100838 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100847 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100832 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100848 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100833 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101243 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101241 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100084 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101242 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101256 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100100918 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101154 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101156 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101132 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101155 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101283 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101284 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101282 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101296 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101295 的userid值为空
2025-06-18 09:00:04,462 - INFO - 店铺 100101275 的userid值为空
2025-06-18 09:00:04,478 - INFO - 店铺 100101312 的userid值为空
2025-06-18 09:00:04,478 - INFO - 店铺 100101313 的userid值为空
2025-06-18 09:00:07,103 - INFO - 店铺 100101303 的userid值为空
2025-06-18 09:00:07,103 - INFO - 店铺 100101288 的userid值为空
2025-06-18 09:00:07,869 - INFO - 店铺 100100236 的userid值为空
2025-06-18 09:00:08,697 - INFO - 店铺 100098428 的userid值为空
2025-06-18 09:00:08,697 - INFO - 店铺 100099293 的userid值为空
2025-06-18 09:00:09,478 - INFO - 店铺 100100926 的userid值为空
2025-06-18 09:00:09,478 - INFO - 店铺 100101311 的userid值为空
2025-06-18 09:00:09,478 - INFO - 店铺 100100865 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100101310 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100101308 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100101309 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100886 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100098583 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100670 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100646 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100279 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100099988 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100098589 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100882 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100006 的userid值为空
2025-06-18 09:00:10,087 - INFO - 店铺 100100004 的userid值为空
2025-06-18 09:00:10,103 - INFO - 店铺 100098572 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100893 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099980 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100233 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100101181 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099978 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099985 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099870 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099983 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100894 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099928 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099984 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100964 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099930 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099824 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100875 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100447 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099936 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099829 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100880 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100456 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100881 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100101304 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100101265 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099305 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100278 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099189 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100328 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099216 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100369 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099270 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100376 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099275 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099291 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100101124 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100269 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100327 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100368 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100466 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100101123 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099274 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099290 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099307 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099338 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100238 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099171 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100291 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099191 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100335 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099218 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100373 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099249 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100378 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099190 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099217 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099271 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100377 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100101125 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099277 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099292 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099313 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099193 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099226 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100375 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100380 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100664 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099281 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099295 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099192 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100339 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100100374 的userid值为空
2025-06-18 09:00:10,697 - INFO - 店铺 100099237 的userid值为空
2025-06-18 09:00:10,712 - INFO - 店铺 100099256 的userid值为空
2025-06-18 09:00:10,712 - INFO - 店铺 100100663 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100379 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100101129 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099280 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099294 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100249 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099177 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100314 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099198 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100434 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099283 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099176 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099197 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100665 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100433 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099282 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100437 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099322 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099345 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099185 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100321 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100436 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099317 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100262 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099199 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100435 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099261 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100101118 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099298 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099796 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099148 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100265 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099187 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099259 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099297 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099323 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099346 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100217 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099147 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100100264 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099186 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099264 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100101120 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099153 的userid值为空
2025-06-18 09:00:11,400 - INFO - 店铺 100099263 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101119 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099284 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099299 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099332 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100224 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099150 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099188 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099212 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099266 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100438 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099289 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101122 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099304 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099931 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100361 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099231 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099243 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099265 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101121 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099272 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100900 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099303 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101302 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101297 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101287 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100101224 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099101 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100082 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100060 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098405 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100042 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100229 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098358 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100199 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100453 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098436 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100061 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100044 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098391 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100293 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100024 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100099903 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100200 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100097 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098448 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098253 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100076 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098433 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100055 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100040 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098268 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100098449 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100081 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100056 的userid值为空
2025-06-18 09:00:11,416 - INFO - 店铺 100100041 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100020 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100047 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098396 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098247 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100320 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100028 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099906 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098362 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100207 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100185 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099119 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100160 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100661 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098397 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100029 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099913 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100210 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099816 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100187 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099793 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100162 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098258 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098423 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100045 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100300 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100026 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098377 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098360 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100201 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100182 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098246 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098424 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100046 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100027 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099905 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100183 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100215 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100194 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099806 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100169 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100144 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098369 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100216 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099847 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100195 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100172 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100146 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100030 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100260 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100212 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100189 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099794 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100142 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100261 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098367 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100214 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100192 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099797 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100166 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100143 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099811 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100178 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100150 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098348 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100180 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100152 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098317 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100196 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100175 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100148 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100904 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100198 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100177 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100870 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098598 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100128 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100101180 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100154 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098350 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098488 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100156 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098351 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098597 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098342 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100099109 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098297 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100133 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100106 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098284 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098343 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100134 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100108 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100083 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098295 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098477 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100129 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098355 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100130 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100098296 的userid值为空
2025-06-18 09:00:12,041 - INFO - 店铺 100100101 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098486 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098273 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100112 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100089 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100070 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098260 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098289 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100140 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098303 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100113 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100091 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098440 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098274 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100071 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098427 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100048 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098300 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100135 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100110 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100085 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098437 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099117 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098485 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098287 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100136 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100111 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100088 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100067 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100121 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100095 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098445 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100074 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100052 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100035 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098384 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098461 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100096 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098447 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100075 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100053 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100371 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100037 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098385 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100141 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098275 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100114 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100092 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098249 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100049 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098398 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100829 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098474 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100117 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100094 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098276 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100073 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098237 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100050 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098250 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100033 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100101278 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100101285 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100101299 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100101301 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099585 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099650 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099629 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099636 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100810 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099587 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100266 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100251 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099992 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100016 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099522 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099588 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099503 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099525 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100303 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099499 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099681 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099500 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099615 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099675 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099613 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100341 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100887 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099956 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099539 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100098588 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100366 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100461 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100460 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100258 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100099541 的userid值为空
2025-06-18 09:00:12,650 - INFO - 店铺 100100459 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099555 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100465 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100462 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100098559 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099831 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101200 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100481 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100342 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100883 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099835 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101246 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100480 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100365 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101190 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101236 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101272 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100478 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101306 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099507 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099910 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101254 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099506 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099553 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100922 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099861 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100885 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099104 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100098596 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100917 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100098594 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099995 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099899 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100255 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099987 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100098561 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100280 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100890 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100888 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100884 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100889 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099911 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100944 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101162 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100891 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099803 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100818 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100098579 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100347 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099853 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099863 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101153 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100359 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100101173 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100099804 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100667 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100344 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 100100241 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 ********* 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 ********* 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 ********* 的userid值为空
2025-06-18 09:00:13,181 - INFO - 店铺 ********* 的userid值为空
2025-06-18 09:00:13,181 - INFO - 获取宜搭店铺信息成功，共 1261 条记录
2025-06-18 09:00:13,181 - INFO - 成功获取宜搭店铺信息，共 1261 条记录
2025-06-18 09:00:13,181 - INFO - 正在获取用户信息并处理oa_account...
2025-06-18 09:00:13,197 - INFO - 获取用户信息成功，共 70 条记录
2025-06-18 09:00:13,494 - INFO - 需要查询的手机号数量: 41
2025-06-18 09:00:16,572 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:17,275 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:17,431 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:17,572 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:17,712 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:17,853 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,056 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,212 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,353 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,509 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,666 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,806 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:18,947 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:19,072 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:19,228 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:19,384 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:19,525 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:19,744 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:19,900 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,056 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,212 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,353 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,509 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,665 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,837 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:20,962 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:21,103 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:21,259 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:21,400 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:21,540 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:21,775 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,009 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,165 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,306 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,447 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,587 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,744 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:22,900 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,041 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,181 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,322 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,462 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,603 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,759 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:23,931 - WARNING - 获取userid失败: ***********, 错误信息: 找不到该用户
2025-06-18 09:00:26,509 - INFO - 处理oa_account完成，缓存大小: 40
2025-06-18 09:00:26,509 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid']
2025-06-18 09:00:26,509 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'form_instance_id']
2025-06-18 09:00:26,509 - INFO - 开始对比数据库和宜搭数据...
2025-06-18 09:00:26,509 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid']
2025-06-18 09:00:26,509 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'form_instance_id']
2025-06-18 09:00:26,525 - INFO - 仅在数据库存在的记录数: 0
2025-06-18 09:00:26,525 - INFO - 仅在宜搭存在的记录数: 0
2025-06-18 09:00:26,572 - INFO - 店铺 ********* 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,572 - INFO - 店铺 ********* userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '195cbfaa191990374b814d747ed92fe3', '1823db918644667677cfbe44476b7b9d', '16340e95afb25e94fcd338840d78edb8'}, 宜搭: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d', '16340e95afb25e94fcd338840d78edb8'}
2025-06-18 09:00:26,572 - INFO - 店铺 ********* - 仅在数据库存在的userid: {'195cbfaa191990374b814d747ed92fe3'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099396 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099396 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099396 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099395 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099395 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099395 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099389 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099389 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099389 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099694 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099694 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099694 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099432 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099432 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099432 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099388 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099388 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099388 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099393 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099393 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099393 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099392 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099392 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099392 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099426 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099426 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099426 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099425 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099425 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099425 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099429 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099429 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099429 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099387 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099387 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099387 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099385 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099385 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099385 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099427 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099427 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099427 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099422 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099422 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099422 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099421 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099421 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099421 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101201 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100101201 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101201 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099424 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099424 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099424 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099423 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099423 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099423 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101144 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100101144 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101144 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101141 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100101141 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101141 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101197 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100101197 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101197 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101145 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100101145 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100101145 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099415 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,665 - INFO - 店铺 100099415 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,665 - INFO - 店铺 100099415 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099414 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099414 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099414 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099420 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099420 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099420 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099418 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099418 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099418 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099406 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099406 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099406 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100961 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100961 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100961 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099403 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099403 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099403 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100960 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100960 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100960 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100963 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100963 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100963 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099410 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099410 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099410 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100962 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100962 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100962 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099408 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099408 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099408 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100862 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100862 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100862 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100348 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100348 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100348 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100959 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100959 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100959 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099401 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099401 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099401 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100898 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100100898 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100100898 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099399 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099399 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099399 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099944 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099944 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099944 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099943 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100099943 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100099943 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100101247 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100101247 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100101247 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,681 - INFO - 店铺 100101289 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,681 - INFO - 店铺 100101289 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:26,681 - INFO - 店铺 100101289 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:26,790 - INFO - 店铺 100101272 存在字段差异: ['userid_diff']
2025-06-18 09:00:26,806 - INFO - 店铺 100101272 userid差异 - 数据库: {'18acf80e2a353b81652ebdc4329bdcc4', '16d2416ce06396ed3f55a964f9fa0c71', '1835384a550d8f5bfad8b704c9e963f7', '1847a0e687fd986b4b870904dea8430d', '189fd3054a18e03a44b872b495795896', '18353820b37845b95ed69864fd58b885', '18b6ef624129a1e2c9b51774b6e8ce8c'}, 宜搭: set()
2025-06-18 09:00:26,806 - INFO - 店铺 100101272 - 数据库有userid但宜搭为空
2025-06-18 09:00:26,822 - INFO - 数据对比完成：
2025-06-18 09:00:26,822 - INFO - - 需要插入的记录数: 0
2025-06-18 09:00:26,822 - INFO - - 需要更新状态为禁用的记录数: 0
2025-06-18 09:00:26,822 - INFO - - 需要更新的记录数: 47
2025-06-18 09:00:26,822 - INFO - - 店铺名称变更数: 0
2025-06-18 09:00:26,822 - INFO - 生成差异报告...
2025-06-18 09:00:27,212 - INFO - 差异报告已保存到文件: data/sync_store/store_info_diff_report_20250618.xlsx
2025-06-18 09:00:27,212 - INFO - 开始更新宜搭表单...
2025-06-18 09:00:27,212 - INFO - 开始更新宜搭表单数据...
2025-06-18 09:00:27,212 - INFO - 数据库记录数: 1261
2025-06-18 09:00:27,212 - INFO - 宜搭记录数: 1261
2025-06-18 09:00:27,212 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid']
2025-06-18 09:00:27,212 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'form_instance_id']
2025-06-18 09:00:27,212 - INFO - 仅在数据库存在的记录数: 0
2025-06-18 09:00:27,212 - INFO - 仅在宜搭存在的记录数: 0
2025-06-18 09:00:27,259 - INFO - 店铺 ********* 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,259 - INFO - 店铺 ********* userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '195cbfaa191990374b814d747ed92fe3', '1823db918644667677cfbe44476b7b9d', '16340e95afb25e94fcd338840d78edb8'}, 宜搭: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d', '16340e95afb25e94fcd338840d78edb8'}
2025-06-18 09:00:27,259 - INFO - 店铺 ********* - 仅在数据库存在的userid: {'195cbfaa191990374b814d747ed92fe3'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099396 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099396 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099396 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099395 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099395 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099395 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099389 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099389 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099389 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099694 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099694 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099694 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099432 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099432 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099432 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099388 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099388 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099388 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099393 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099393 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099393 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099392 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099392 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099392 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099426 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099426 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099426 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099425 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099425 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099425 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099429 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099429 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099429 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099387 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099387 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099387 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099385 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099385 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099385 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099427 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099427 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099427 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099422 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099422 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099422 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099421 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099421 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099421 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101201 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100101201 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101201 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099424 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099424 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099424 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099423 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099423 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099423 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101144 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100101144 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101144 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101141 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100101141 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101141 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101197 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100101197 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101197 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101145 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100101145 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100101145 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099415 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,353 - INFO - 店铺 100099415 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,353 - INFO - 店铺 100099415 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099414 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099414 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099414 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099420 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099420 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099420 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099418 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099418 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099418 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099406 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099406 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099406 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100961 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100961 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100961 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099403 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099403 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099403 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100960 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100960 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100960 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100963 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100963 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100963 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099410 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099410 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099410 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100962 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100962 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100962 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099408 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099408 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099408 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100862 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100862 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100862 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100348 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100348 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100348 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100959 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100959 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100959 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099401 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099401 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099401 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100898 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100100898 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100100898 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099399 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099399 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099399 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099944 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099944 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099944 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099943 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100099943 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100099943 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100101247 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100101247 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '18f99e6481178d51b06ff0b4410ad323', '16d2416d58bbecd312653de411ab0b7b', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100101247 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,369 - INFO - 店铺 100101289 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,369 - INFO - 店铺 100101289 userid差异 - 数据库: {'18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8', '18ea2e5066661f3b4e114024bd290c2d'}, 宜搭: {'18ea2e5066661f3b4e114024bd290c2d', '16d2416d58bbecd312653de411ab0b7b', '18f99e6481178d51b06ff0b4410ad323', '16d2416db03efb5beff93cb4f59aab6e', '181d652cd41d8f441bfe3bd4596a79e8'}
2025-06-18 09:00:27,369 - INFO - 店铺 100101289 - 仅在宜搭存在的userid: {'16d2416d58bbecd312653de411ab0b7b'}
2025-06-18 09:00:27,478 - INFO - 店铺 100101272 存在字段差异: ['userid_diff']
2025-06-18 09:00:27,478 - INFO - 店铺 100101272 userid差异 - 数据库: {'18acf80e2a353b81652ebdc4329bdcc4', '16d2416ce06396ed3f55a964f9fa0c71', '1835384a550d8f5bfad8b704c9e963f7', '1847a0e687fd986b4b870904dea8430d', '189fd3054a18e03a44b872b495795896', '18353820b37845b95ed69864fd58b885', '18b6ef624129a1e2c9b51774b6e8ce8c'}, 宜搭: set()
2025-06-18 09:00:27,478 - INFO - 店铺 100101272 - 数据库有userid但宜搭为空
2025-06-18 09:00:27,509 - INFO - 数据对比完成：
2025-06-18 09:00:27,509 - INFO - - 需要插入的记录数: 0
2025-06-18 09:00:27,509 - INFO - - 需要更新状态为禁用的记录数: 0
2025-06-18 09:00:27,509 - INFO - - 需要更新的记录数: 47
2025-06-18 09:00:27,509 - INFO - - 店铺名称变更数: 0
2025-06-18 09:00:27,509 - INFO - 开始处理需要更新的记录，共 47 条
2025-06-18 09:00:27,509 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-06-18 09:00:27,509 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "美特斯邦威", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "195cbfaa191990374b814d747ed92fe3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0344"}
2025-06-18 09:00:27,509 - INFO - 正在处理第 2 条更新记录 - store_code: 100099396
2025-06-18 09:00:27,509 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099396", "textField_m8e8g3lu": "星巴克", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101190", "textField_mb7rs39i": "广东星巴克咖啡有限公司", "textField_mbc1lbzm": "10311L20CO0061"}
2025-06-18 09:00:27,509 - INFO - 正在处理第 3 条更新记录 - store_code: 100099395
2025-06-18 09:00:27,509 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099395", "textField_m8e8g3lu": "爱睫物语", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101456", "textField_mb7rs39i": "黄祖贤", "textField_mbc1lbzm": "10311L20CO0076"}
2025-06-18 09:00:27,509 - INFO - 正在处理第 4 条更新记录 - store_code: 100099389
2025-06-18 09:00:27,509 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099389", "textField_m8e8g3lu": "樱花国际日语", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101188", "textField_mb7rs39i": "广州新英东教育咨询有限公司", "textField_mbc1lbzm": "10311L20CO0062"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 5 条更新记录 - store_code: 100099694
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099694", "textField_m8e8g3lu": "多经-MIKA", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0023"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 6 条更新记录 - store_code: 100099432
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099432", "textField_m8e8g3lu": "广州VT101维多利广场", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L20CO0002"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 7 条更新记录 - store_code: 100099388
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099388", "textField_m8e8g3lu": "GU", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104333", "textField_mb7rs39i": "极优（上海）商贸有限公司广州体育西路店", "textField_mbc1lbzm": "10311L20CO0051"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 8 条更新记录 - store_code: 100099393
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099393", "textField_m8e8g3lu": "诚寿司", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L20CO0082"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 9 条更新记录 - store_code: 100099392
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099392", "textField_m8e8g3lu": "丝域养发馆", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106621", "textField_mb7rs39i": "陈文娟", "textField_mbc1lbzm": "10311L22CO0005"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 10 条更新记录 - store_code: 100099426
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099426", "textField_m8e8g3lu": "简悦厨", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103632", "textField_mb7rs39i": "广州简悦厨餐饮管理有限公司天河维多利分店", "textField_mbc1lbzm": "10311L24CO0009"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 11 条更新记录 - store_code: 100099425
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099425", "textField_m8e8g3lu": "麦当劳", "employeeField_m8e8g3lw": ["18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101403", "textField_mb7rs39i": "广东三元麦当劳食品有限公司", "textField_mbc1lbzm": "10311L20CO0066"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 12 条更新记录 - store_code: 100099429
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099429", "textField_m8e8g3lu": "中国工商银行", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101018", "textField_mb7rs39i": "中国工商银行股份有限公司广州德政中路支行", "textField_mbc1lbzm": "10311L20CO0055"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 13 条更新记录 - store_code: 100099387
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099387", "textField_m8e8g3lu": "侨美食家", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101035", "textField_mb7rs39i": "广州市天河无国界美食沙龙（普通合伙）", "textField_mbc1lbzm": "10311L24CO0003"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 14 条更新记录 - store_code: 100099385
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099385", "textField_m8e8g3lu": "乐凯撒", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101489", "textField_mb7rs39i": "广州市乐凯撒比萨餐饮管理有限公司", "textField_mbc1lbzm": "10311L20CO0056"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 15 条更新记录 - store_code: 100099427
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099427", "textField_m8e8g3lu": "小荔园", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106761", "textField_mb7rs39i": "广州市小荔圆餐饮有限公司", "textField_mbc1lbzm": "10311L22CO0006"}
2025-06-18 09:00:27,525 - INFO - 正在处理第 16 条更新记录 - store_code: 100099422
2025-06-18 09:00:27,525 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099422", "textField_m8e8g3lu": "圣安娜饼屋", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "100910", "textField_mb7rs39i": "广州市圣安娜饼屋有限公司", "textField_mbc1lbzm": "10311L22CO0007"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 17 条更新记录 - store_code: 100099421
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099421", "textField_m8e8g3lu": "博士眼镜", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "105344", "textField_mb7rs39i": "广州诗琪眼镜有限公司", "textField_mbc1lbzm": "10311L21CO0038"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 18 条更新记录 - store_code: 100101201
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101201", "textField_m8e8g3lu": "餐车", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0048"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 19 条更新记录 - store_code: 100099424
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099424", "textField_m8e8g3lu": "招商银行", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101017", "textField_mb7rs39i": "招商银行股份有限公司广州分行", "textField_mbc1lbzm": "10311L24CO0002"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 20 条更新记录 - store_code: 100099423
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099423", "textField_m8e8g3lu": "Seven-Eleven", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104560", "textField_mb7rs39i": "广东赛壹便利店有限公司", "textField_mbc1lbzm": "10311L21CO0022"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 21 条更新记录 - store_code: 100101144
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101144", "textField_m8e8g3lu": "多经-黄祥兴", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0027"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 22 条更新记录 - store_code: 100101141
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101141", "textField_m8e8g3lu": "德图", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": ""}
2025-06-18 09:00:27,540 - INFO - 正在处理第 23 条更新记录 - store_code: 100101197
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101197", "textField_m8e8g3lu": "唐机豆豆", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0047"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 24 条更新记录 - store_code: 100101145
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101145", "textField_m8e8g3lu": "关活华", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0039"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 25 条更新记录 - store_code: 100099415
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099415", "textField_m8e8g3lu": "华为客户服务中心", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103872", "textField_mb7rs39i": "深圳市天音科技发展有限公司广州体育西路分公司", "textField_mbc1lbzm": "10311L24CO0008"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 26 条更新记录 - store_code: 100099414
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099414", "textField_m8e8g3lu": "优衣库", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101203", "textField_mb7rs39i": "迅销（中国）商贸有限公司广州维多利广场店", "textField_mbc1lbzm": "10311L24CO0001"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 27 条更新记录 - store_code: 100099420
2025-06-18 09:00:27,540 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099420", "textField_m8e8g3lu": "喜茶", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106408", "textField_mb7rs39i": "广州灵感之茶餐饮管理有限公司", "textField_mbc1lbzm": "10311L20CO0090"}
2025-06-18 09:00:27,540 - INFO - 正在处理第 28 条更新记录 - store_code: 100099418
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099418", "textField_m8e8g3lu": "SUPA FAMA", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L25CO0003"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 29 条更新记录 - store_code: 100099406
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099406", "textField_m8e8g3lu": "MLB", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L21CO0036"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 30 条更新记录 - store_code: 100100961
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100961", "textField_m8e8g3lu": "MIKA饰物", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0023"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 31 条更新记录 - store_code: 100099403
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099403", "textField_m8e8g3lu": "椰客椰子鸡", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106242", "textField_mb7rs39i": "广州椰客餐饮管理有限公司维多利分公司", "textField_mbc1lbzm": "10311L20CO0115"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 32 条更新记录 - store_code: 100100960
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100960", "textField_m8e8g3lu": "多经-肌肉蚂蚁", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "105154", "textField_mb7rs39i": "李晓爽", "textField_mbc1lbzm": "10311L24CO0020"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 33 条更新记录 - store_code: 100100963
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100963", "textField_m8e8g3lu": "多经-金刚手机维修", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0021"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 34 条更新记录 - store_code: 100099410
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099410", "textField_m8e8g3lu": "西贝莜面村", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101909", "textField_mb7rs39i": "深圳西贝喜悦餐饮有限公司广州维多利广场分公司", "textField_mbc1lbzm": "10311L24CO0006"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 35 条更新记录 - store_code: 100100962
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100962", "textField_m8e8g3lu": "ELLE active", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0022"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 36 条更新记录 - store_code: 100099408
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099408", "textField_m8e8g3lu": "客语", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101257", "textField_mb7rs39i": "广东客语餐饮管理有限公司广州天河第一分公司", "textField_mbc1lbzm": "10311L21CO0027"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 37 条更新记录 - store_code: 100100862
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100862", "textField_m8e8g3lu": "未来医美", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108428", "textField_mb7rs39i": "广州未来医疗美容诊所有限公司", "textField_mbc1lbzm": "10311L24CO0005"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 38 条更新记录 - store_code: 100100348
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100348", "textField_m8e8g3lu": "吴系粤菜馆", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108356", "textField_mb7rs39i": "广州市吴粤龙威餐饮有限公司", "textField_mbc1lbzm": "10311L24CO0004"}
2025-06-18 09:00:27,556 - INFO - 正在处理第 39 条更新记录 - store_code: 100100959
2025-06-18 09:00:27,556 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100959", "textField_m8e8g3lu": "多经-蒸汽狮电子烟专柜", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L24CO0024"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 40 条更新记录 - store_code: 100099401
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099401", "textField_m8e8g3lu": "太二酸菜鱼", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101145", "textField_mb7rs39i": "广州太二餐饮连锁有限公司", "textField_mbc1lbzm": "10311L20CO0122"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 41 条更新记录 - store_code: 100100898
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100100898", "textField_m8e8g3lu": "蜜卡美", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108427", "textField_mb7rs39i": "上海汇慧健康管理咨询有限公司广州第三分公司", "textField_mbc1lbzm": "10311L24CO0007"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 42 条更新记录 - store_code: 100099399
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099399", "textField_m8e8g3lu": "老湘村", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104099", "textField_mb7rs39i": "陈波", "textField_mbc1lbzm": "10311L20CO0121"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 43 条更新记录 - store_code: 100099944
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099944", "textField_m8e8g3lu": "霸王茶姬", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "107447", "textField_mb7rs39i": "广州市茶季芳香茶饮有限公司", "textField_mbc1lbzm": "10311L23CO0005"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 44 条更新记录 - store_code: 100099943
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099943", "textField_m8e8g3lu": "皮氏咖啡", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106722", "textField_mb7rs39i": "皮氏咖啡（深圳）有限公司", "textField_mbc1lbzm": "10311L23CO0006"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 45 条更新记录 - store_code: 100101247
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101247", "textField_m8e8g3lu": "瑞幸咖啡", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L25CO0001"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 46 条更新记录 - store_code: 100101289
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101289", "textField_m8e8g3lu": "虾宴", "employeeField_m8e8g3lw": ["181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10311L25CO0002"}
2025-06-18 09:00:27,572 - INFO - 正在处理第 47 条更新记录 - store_code: 100101272
2025-06-18 09:00:27,572 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100101272", "textField_m8e8g3lu": "满江红教育", "employeeField_m8e8g3lw": ["16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c", "1847a0e687fd986b4b870904dea8430d", "18353820b37845b95ed69864fd58b885", "1835384a550d8f5bfad8b704c9e963f7", "18acf80e2a353b81652ebdc4329bdcc4", "189fd3054a18e03a44b872b495795896"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0003L25CO0013"}
2025-06-18 09:00:27,572 - INFO - 处理剩余 47 条更新记录
2025-06-18 09:00:28,087 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:28,540 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:29,025 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:29,556 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:30,056 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:30,665 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:31,134 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:31,759 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:32,306 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:32,806 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:33,275 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:33,900 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:34,556 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:34,915 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:35,400 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:36,619 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:37,087 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:37,587 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:38,072 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:38,556 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:39,134 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:39,634 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:40,087 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:40,462 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:40,962 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:41,493 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:41,931 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:42,447 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:43,072 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:43,650 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:44,072 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:44,634 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:45,290 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:45,915 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:46,478 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:46,993 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:47,493 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:47,978 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:48,572 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:49,040 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:49,540 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:50,009 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:50,509 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:51,118 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:51,665 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:52,165 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:52,587 - INFO - 批量更新表单数据成功: 
2025-06-18 09:00:52,587 - INFO - 批量更新成功，form_instance_ids: ['FINST-DIC66I91VDVVFVB58ZJ7V4EKM7G63FRHADCBM85', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM22', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM32', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM42', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM52', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM62', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM72', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM82', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM92', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMA2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMB2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMC2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMD2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBME2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMF2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMG2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMH2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMI2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMJ2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMK2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBML2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMM2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMN2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMO2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMP2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMQ2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMR2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMS2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMT2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMU2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMV2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMW2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMX2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMY2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMZ2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM03', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM13', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM23', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM33', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM43', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM53', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM63', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM73', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM83', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM93', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMA3', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573G0LADCBM44']
2025-06-18 09:00:52,587 - INFO - 宜搭表单更新完成
2025-06-18 09:00:52,587 - INFO - 数据处理完成
2025-06-18 09:00:52,587 - INFO - 数据库连接已关闭
