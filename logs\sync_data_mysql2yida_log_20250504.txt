2025-05-04 00:30:34,604 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 00:30:34,604 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 00:30:34,604 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 00:30:34,651 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 2 条记录
2025-05-04 00:30:34,651 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 00:30:34,651 - INFO - 开始处理日期: 2025-05-03
2025-05-04 00:30:34,667 - INFO - Request Parameters - Page 1:
2025-05-04 00:30:34,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:30:34,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:30:42,769 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 73A95BAD-2976-789E-8803-8449839EF06C Response: {'code': 'ServiceUnavailable', 'requestid': '73A95BAD-2976-789E-8803-8449839EF06C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 73A95BAD-2976-789E-8803-8449839EF06C)
2025-05-04 00:30:42,769 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 00:31:42,849 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 00:31:42,849 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 00:31:42,849 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 00:31:42,896 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 00:31:42,896 - ERROR - 未获取到MySQL数据
2025-05-04 00:31:42,896 - INFO - 同步完成
2025-05-04 01:30:34,457 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 01:30:34,457 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 01:30:34,457 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 01:30:34,503 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 2 条记录
2025-05-04 01:30:34,503 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 01:30:34,503 - INFO - 开始处理日期: 2025-05-03
2025-05-04 01:30:34,503 - INFO - Request Parameters - Page 1:
2025-05-04 01:30:34,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 01:30:34,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 01:30:42,621 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D7C2CF60-930B-7B59-BE4D-E7D8CC767C79 Response: {'code': 'ServiceUnavailable', 'requestid': 'D7C2CF60-930B-7B59-BE4D-E7D8CC767C79', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D7C2CF60-930B-7B59-BE4D-E7D8CC767C79)
2025-05-04 01:30:42,621 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 01:31:42,701 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 01:31:42,701 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 01:31:42,701 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 01:31:42,748 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 01:31:42,748 - ERROR - 未获取到MySQL数据
2025-05-04 01:31:42,748 - INFO - 同步完成
2025-05-04 02:30:34,419 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 02:30:34,419 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 02:30:34,419 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 02:30:34,481 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 2 条记录
2025-05-04 02:30:34,481 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 02:30:34,481 - INFO - 开始处理日期: 2025-05-03
2025-05-04 02:30:34,481 - INFO - Request Parameters - Page 1:
2025-05-04 02:30:34,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 02:30:34,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 02:30:42,615 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7214711C-6A28-76F4-A54D-A4AD1F0EC532 Response: {'code': 'ServiceUnavailable', 'requestid': '7214711C-6A28-76F4-A54D-A4AD1F0EC532', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7214711C-6A28-76F4-A54D-A4AD1F0EC532)
2025-05-04 02:30:42,615 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 02:31:42,694 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 02:31:42,694 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 02:31:42,694 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 02:31:42,741 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 02:31:42,741 - ERROR - 未获取到MySQL数据
2025-05-04 02:31:42,741 - INFO - 同步完成
2025-05-04 03:30:34,600 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 03:30:34,600 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 03:30:34,600 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 03:30:34,662 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 3 条记录
2025-05-04 03:30:34,662 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 03:30:34,662 - INFO - 开始处理日期: 2025-05-03
2025-05-04 03:30:34,662 - INFO - Request Parameters - Page 1:
2025-05-04 03:30:34,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:30:34,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:30:42,796 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 996A411F-E4B3-793B-8A42-E41B6A40FC69 Response: {'code': 'ServiceUnavailable', 'requestid': '996A411F-E4B3-793B-8A42-E41B6A40FC69', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 996A411F-E4B3-793B-8A42-E41B6A40FC69)
2025-05-04 03:30:42,796 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 03:31:42,875 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 03:31:42,875 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 03:31:42,875 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 03:31:42,922 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 03:31:42,922 - ERROR - 未获取到MySQL数据
2025-05-04 03:31:42,922 - INFO - 同步完成
2025-05-04 04:30:34,592 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 04:30:34,592 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 04:30:34,592 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 04:30:34,654 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 3 条记录
2025-05-04 04:30:34,654 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 04:30:34,654 - INFO - 开始处理日期: 2025-05-03
2025-05-04 04:30:34,654 - INFO - Request Parameters - Page 1:
2025-05-04 04:30:34,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 04:30:34,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 04:30:42,772 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D8694DD0-B2C7-7F0A-88D8-726F11FA89AD Response: {'code': 'ServiceUnavailable', 'requestid': 'D8694DD0-B2C7-7F0A-88D8-726F11FA89AD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D8694DD0-B2C7-7F0A-88D8-726F11FA89AD)
2025-05-04 04:30:42,772 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 04:31:42,852 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 04:31:42,852 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 04:31:42,852 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 04:31:42,898 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 04:31:42,898 - ERROR - 未获取到MySQL数据
2025-05-04 04:31:42,898 - INFO - 同步完成
2025-05-04 05:30:33,900 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 05:30:33,900 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 05:30:33,900 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 05:30:33,947 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 3 条记录
2025-05-04 05:30:33,947 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 05:30:33,947 - INFO - 开始处理日期: 2025-05-03
2025-05-04 05:30:33,947 - INFO - Request Parameters - Page 1:
2025-05-04 05:30:33,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 05:30:33,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 05:30:42,056 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8D848E48-CC51-778B-86BD-F50A85C988E4 Response: {'code': 'ServiceUnavailable', 'requestid': '8D848E48-CC51-778B-86BD-F50A85C988E4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8D848E48-CC51-778B-86BD-F50A85C988E4)
2025-05-04 05:30:42,056 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 05:31:42,072 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 05:31:42,072 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 05:31:42,072 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 05:31:42,119 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 05:31:42,119 - ERROR - 未获取到MySQL数据
2025-05-04 05:31:42,119 - INFO - 同步完成
2025-05-04 06:30:33,893 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 06:30:33,893 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 06:30:33,893 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 06:30:33,956 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 8 条记录
2025-05-04 06:30:33,956 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 06:30:33,956 - INFO - 开始处理日期: 2025-05-03
2025-05-04 06:30:33,956 - INFO - Request Parameters - Page 1:
2025-05-04 06:30:33,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:30:33,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:30:42,065 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 174CF79A-5297-7944-A0A7-F8189BD1EAD8 Response: {'code': 'ServiceUnavailable', 'requestid': '174CF79A-5297-7944-A0A7-F8189BD1EAD8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 174CF79A-5297-7944-A0A7-F8189BD1EAD8)
2025-05-04 06:30:42,065 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 06:31:42,080 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 06:31:42,080 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 06:31:42,080 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 06:31:42,127 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 06:31:42,127 - ERROR - 未获取到MySQL数据
2025-05-04 06:31:42,127 - INFO - 同步完成
2025-05-04 07:30:33,918 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 07:30:33,918 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 07:30:33,918 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 07:30:33,980 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 15 条记录
2025-05-04 07:30:33,980 - INFO - 获取到 1 个日期需要处理: ['2025-05-03']
2025-05-04 07:30:33,980 - INFO - 开始处理日期: 2025-05-03
2025-05-04 07:30:33,980 - INFO - Request Parameters - Page 1:
2025-05-04 07:30:33,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 07:30:33,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 07:30:42,105 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2AB598F8-E74B-788D-9928-C0F4BA0A37AD Response: {'code': 'ServiceUnavailable', 'requestid': '2AB598F8-E74B-788D-9928-C0F4BA0A37AD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2AB598F8-E74B-788D-9928-C0F4BA0A37AD)
2025-05-04 07:30:42,105 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 07:31:42,120 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 07:31:42,120 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 07:31:42,120 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 07:31:42,167 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 07:31:42,167 - ERROR - 未获取到MySQL数据
2025-05-04 07:31:42,167 - INFO - 同步完成
2025-05-04 08:30:33,864 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 08:30:33,864 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 08:30:33,880 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 08:30:33,927 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 21 条记录
2025-05-04 08:30:33,927 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 08:30:33,927 - INFO - 开始处理日期: 2025-05-02
2025-05-04 08:30:33,927 - INFO - Request Parameters - Page 1:
2025-05-04 08:30:33,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 08:30:33,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 08:30:42,036 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E2FEEDB9-68AF-728A-A986-63C33C6C77CC Response: {'code': 'ServiceUnavailable', 'requestid': 'E2FEEDB9-68AF-728A-A986-63C33C6C77CC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E2FEEDB9-68AF-728A-A986-63C33C6C77CC)
2025-05-04 08:30:42,036 - INFO - 开始处理日期: 2025-05-03
2025-05-04 08:30:42,036 - INFO - Request Parameters - Page 1:
2025-05-04 08:30:42,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 08:30:42,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 08:30:50,161 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 987EF5F1-25C0-78A7-8F91-AF17072C8441 Response: {'code': 'ServiceUnavailable', 'requestid': '987EF5F1-25C0-78A7-8F91-AF17072C8441', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 987EF5F1-25C0-78A7-8F91-AF17072C8441)
2025-05-04 08:30:50,161 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 08:31:50,176 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 08:31:50,176 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 08:31:50,176 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 08:31:50,223 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 08:31:50,223 - ERROR - 未获取到MySQL数据
2025-05-04 08:31:50,223 - INFO - 同步完成
2025-05-04 09:30:33,920 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 09:30:33,920 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 09:30:33,920 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 09:30:33,982 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 99 条记录
2025-05-04 09:30:33,982 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 09:30:33,982 - INFO - 开始处理日期: 2025-05-02
2025-05-04 09:30:33,982 - INFO - Request Parameters - Page 1:
2025-05-04 09:30:33,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:30:33,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:30:42,107 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 421D7F67-E90A-78D6-994B-51CBCDB2A764 Response: {'code': 'ServiceUnavailable', 'requestid': '421D7F67-E90A-78D6-994B-51CBCDB2A764', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 421D7F67-E90A-78D6-994B-51CBCDB2A764)
2025-05-04 09:30:42,107 - INFO - 开始处理日期: 2025-05-03
2025-05-04 09:30:42,107 - INFO - Request Parameters - Page 1:
2025-05-04 09:30:42,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:30:42,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:30:42,263 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: B7896C50-291A-7587-83CC-9305481279F6 Response: {'requestid': 'B7896C50-291A-7587-83CC-9305481279F6', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: B7896C50-291A-7587-83CC-9305481279F6)
2025-05-04 09:30:42,263 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 09:31:42,279 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 09:31:42,279 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 09:31:42,279 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 09:31:42,326 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 09:31:42,326 - ERROR - 未获取到MySQL数据
2025-05-04 09:31:42,326 - INFO - 同步完成
2025-05-04 10:30:33,960 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 10:30:33,960 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 10:30:33,960 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 10:30:34,022 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 125 条记录
2025-05-04 10:30:34,022 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 10:30:34,022 - INFO - 开始处理日期: 2025-05-02
2025-05-04 10:30:34,038 - INFO - Request Parameters - Page 1:
2025-05-04 10:30:34,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 10:30:34,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 10:30:42,147 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0063A32F-14AD-77D1-ADEB-665778C55A78 Response: {'code': 'ServiceUnavailable', 'requestid': '0063A32F-14AD-77D1-ADEB-665778C55A78', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0063A32F-14AD-77D1-ADEB-665778C55A78)
2025-05-04 10:30:42,147 - INFO - 开始处理日期: 2025-05-03
2025-05-04 10:30:42,147 - INFO - Request Parameters - Page 1:
2025-05-04 10:30:42,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 10:30:42,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 10:30:42,303 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: C39F643E-1C3B-7FAB-A9E0-A930B5BBC30C Response: {'requestid': 'C39F643E-1C3B-7FAB-A9E0-A930B5BBC30C', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: C39F643E-1C3B-7FAB-A9E0-A930B5BBC30C)
2025-05-04 10:30:42,303 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 10:31:42,319 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 10:31:42,319 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 10:31:42,319 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 10:31:42,366 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 10:31:42,366 - ERROR - 未获取到MySQL数据
2025-05-04 10:31:42,366 - INFO - 同步完成
2025-05-04 11:30:33,890 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 11:30:33,890 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 11:30:33,890 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 11:30:33,953 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 130 条记录
2025-05-04 11:30:33,953 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 11:30:33,953 - INFO - 开始处理日期: 2025-05-02
2025-05-04 11:30:33,953 - INFO - Request Parameters - Page 1:
2025-05-04 11:30:33,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 11:30:33,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 11:30:42,078 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 04BDC1D7-5FAD-7EA5-B0AC-5ADB14001F9A Response: {'code': 'ServiceUnavailable', 'requestid': '04BDC1D7-5FAD-7EA5-B0AC-5ADB14001F9A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 04BDC1D7-5FAD-7EA5-B0AC-5ADB14001F9A)
2025-05-04 11:30:42,078 - INFO - 开始处理日期: 2025-05-03
2025-05-04 11:30:42,078 - INFO - Request Parameters - Page 1:
2025-05-04 11:30:42,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 11:30:42,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 11:30:46,062 - INFO - Response - Page 1:
2025-05-04 11:30:46,062 - INFO - 第 1 页获取到 100 条记录
2025-05-04 11:30:46,265 - INFO - Request Parameters - Page 2:
2025-05-04 11:30:46,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 11:30:46,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 11:30:54,375 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 30A20DE4-1376-76D7-8E28-B265EC85571A Response: {'code': 'ServiceUnavailable', 'requestid': '30A20DE4-1376-76D7-8E28-B265EC85571A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 30A20DE4-1376-76D7-8E28-B265EC85571A)
2025-05-04 11:30:54,375 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 11:31:54,390 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 11:31:54,390 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 11:31:54,390 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 11:31:54,437 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 11:31:54,437 - ERROR - 未获取到MySQL数据
2025-05-04 11:31:54,437 - INFO - 同步完成
2025-05-04 12:30:33,727 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 12:30:33,727 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 12:30:33,727 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 12:30:33,790 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 167 条记录
2025-05-04 12:30:33,790 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 12:30:33,790 - INFO - 开始处理日期: 2025-05-02
2025-05-04 12:30:33,790 - INFO - Request Parameters - Page 1:
2025-05-04 12:30:33,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:30:33,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:30:41,915 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B15094A7-E3D2-7ACA-A93A-CEF0CC72F8F8 Response: {'code': 'ServiceUnavailable', 'requestid': 'B15094A7-E3D2-7ACA-A93A-CEF0CC72F8F8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B15094A7-E3D2-7ACA-A93A-CEF0CC72F8F8)
2025-05-04 12:30:41,915 - INFO - 开始处理日期: 2025-05-03
2025-05-04 12:30:41,915 - INFO - Request Parameters - Page 1:
2025-05-04 12:30:41,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:30:41,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:30:42,087 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: F403E0FE-718D-7576-BE23-B7168062AAC8 Response: {'requestid': 'F403E0FE-718D-7576-BE23-B7168062AAC8', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: F403E0FE-718D-7576-BE23-B7168062AAC8)
2025-05-04 12:30:42,087 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 12:31:42,102 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 12:31:42,102 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 12:31:42,102 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 12:31:42,149 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 12:31:42,149 - ERROR - 未获取到MySQL数据
2025-05-04 12:31:42,149 - INFO - 同步完成
2025-05-04 13:30:33,908 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 13:30:33,908 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 13:30:33,908 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 13:30:33,970 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 167 条记录
2025-05-04 13:30:33,970 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 13:30:33,970 - INFO - 开始处理日期: 2025-05-02
2025-05-04 13:30:33,970 - INFO - Request Parameters - Page 1:
2025-05-04 13:30:33,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 13:30:33,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 13:30:42,111 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 189573C6-2FF4-7961-958A-A56DF08EC10A Response: {'code': 'ServiceUnavailable', 'requestid': '189573C6-2FF4-7961-958A-A56DF08EC10A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 189573C6-2FF4-7961-958A-A56DF08EC10A)
2025-05-04 13:30:42,111 - INFO - 开始处理日期: 2025-05-03
2025-05-04 13:30:42,111 - INFO - Request Parameters - Page 1:
2025-05-04 13:30:42,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 13:30:42,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 13:30:42,830 - INFO - Response - Page 1:
2025-05-04 13:30:42,830 - INFO - 第 1 页获取到 100 条记录
2025-05-04 13:30:43,033 - INFO - Request Parameters - Page 2:
2025-05-04 13:30:43,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 13:30:43,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 13:30:51,158 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D527B217-3A32-796A-9328-BB50B8723167 Response: {'code': 'ServiceUnavailable', 'requestid': 'D527B217-3A32-796A-9328-BB50B8723167', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D527B217-3A32-796A-9328-BB50B8723167)
2025-05-04 13:30:51,158 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 13:31:51,173 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 13:31:51,173 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 13:31:51,173 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 13:31:51,220 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 13:31:51,220 - ERROR - 未获取到MySQL数据
2025-05-04 13:31:51,220 - INFO - 同步完成
2025-05-04 14:30:33,901 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 14:30:33,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 14:30:33,901 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 14:30:33,964 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 167 条记录
2025-05-04 14:30:33,964 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 14:30:33,964 - INFO - 开始处理日期: 2025-05-02
2025-05-04 14:30:33,964 - INFO - Request Parameters - Page 1:
2025-05-04 14:30:33,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 14:30:33,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 14:30:42,089 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1EE63262-0449-79A8-8F04-01B2B19FB95A Response: {'code': 'ServiceUnavailable', 'requestid': '1EE63262-0449-79A8-8F04-01B2B19FB95A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1EE63262-0449-79A8-8F04-01B2B19FB95A)
2025-05-04 14:30:42,089 - INFO - 开始处理日期: 2025-05-03
2025-05-04 14:30:42,089 - INFO - Request Parameters - Page 1:
2025-05-04 14:30:42,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 14:30:42,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 14:30:42,948 - INFO - Response - Page 1:
2025-05-04 14:30:42,948 - INFO - 第 1 页获取到 100 条记录
2025-05-04 14:30:43,151 - INFO - Request Parameters - Page 2:
2025-05-04 14:30:43,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 14:30:43,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 14:30:43,635 - INFO - Response - Page 2:
2025-05-04 14:30:43,635 - INFO - 第 2 页获取到 12 条记录
2025-05-04 14:30:43,839 - INFO - 查询完成，共获取到 112 条记录
2025-05-04 14:30:43,839 - INFO - 获取到 112 条表单数据
2025-05-04 14:30:43,839 - INFO - 当前日期 2025-05-03 有 163 条MySQL数据需要处理
2025-05-04 14:30:43,839 - INFO - 开始批量插入 163 条新记录
2025-05-04 14:30:44,135 - INFO - 批量插入响应状态码: 200
2025-05-04 14:30:44,135 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 06:28:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D5E81D0-5AAA-77E1-9F15-06D0A4DC54C2', 'x-acs-trace-id': '01c07e78e14e963bbfddf41afeea0387', 'etag': '4UJUCr1aHQ5qygKw5Ot8QIg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 14:30:44,135 - INFO - 批量插入响应体: {'result': ['FINST-3PF66V71YOZU4YVKE7C749S3H7QP39C5T99AMKD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMLD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMMD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMND', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMOD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMPD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMQD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMRD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMSD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMTD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMUD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMVD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMWD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMXD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMYD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMZD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM0E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM1E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM2E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM3E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM4E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM5E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM6E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM7E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM8E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM9E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMAE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMBE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMCE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMDE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMEE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMFE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMGE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMHE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMIE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMJE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMKE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMLE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMME', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMNE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMOE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMPE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMQE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMRE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMSE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMTE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMUE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMVE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMWE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMXE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMYE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMZE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM0F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM1F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM2F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM3F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM4F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM5F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM6F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM7F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM8F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM9F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMAF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMBF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMCF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMDF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMEF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMFF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMGF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMHF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMIF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMJF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMKF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMLF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMMF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMNF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMOF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMPF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMQF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMRF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMSF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMTF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMUF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMVF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMWF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMXF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMYF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMZF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM0G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM1G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM2G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM3G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM4G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM5G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM6G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM7G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM8G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM9G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMAG', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMBG']}
2025-05-04 14:30:44,135 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-04 14:30:44,135 - INFO - 成功插入的数据ID: ['FINST-3PF66V71YOZU4YVKE7C749S3H7QP39C5T99AMKD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMLD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMMD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMND', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMOD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMPD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMQD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMRD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMSD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMTD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMUD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMVD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMWD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMXD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMYD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMZD', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM0E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM1E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM2E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM3E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM4E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM5E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM6E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM7E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM8E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM9E', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMAE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMBE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMCE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMDE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMEE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMFE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMGE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMHE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMIE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMJE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMKE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMLE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMME', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMNE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMOE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMPE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMQE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMRE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMSE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMTE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMUE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMVE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMWE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMXE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMYE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMZE', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM0F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM1F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM2F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM3F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM4F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM5F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM6F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM7F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM8F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM9F', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMAF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMBF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMCF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMDF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMEF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMFF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMGF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMHF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMIF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMJF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMKF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMLF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMMF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMNF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMOF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMPF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMQF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMRF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMSF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMTF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMUF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMVF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMWF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMXF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMYF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMZF', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM0G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM1G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM2G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM3G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM4G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM5G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM6G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM7G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM8G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AM9G', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMAG', 'FINST-3PF66V71YOZU4YVKE7C749S3H7QP3AC5T99AMBG']
2025-05-04 14:30:49,370 - INFO - 批量插入响应状态码: 200
2025-05-04 14:30:49,370 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 06:28:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3099', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FA01D7A9-EF24-7A98-95B2-2CEE49A03033', 'x-acs-trace-id': '7e74531d32689804dc6584248aab4abb', 'etag': '3AM0R4iiKBW/BTxJaS+2cBw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 14:30:49,370 - INFO - 批量插入响应体: {'result': ['FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMJC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMKC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMLC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMMC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMNC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMOC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMPC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMQC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMRC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMSC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMTC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMUC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMVC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMWC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMXC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMYC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMZC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM0D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM1D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM2D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM3D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM4D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM5D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM6D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM7D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM8D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM9D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMAD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMBD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMCD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMDD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMED1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMFD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMGD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMHD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMID1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMJD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMKD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMLD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMMD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMND1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMOD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMPD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMQD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMRD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMSD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMTD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMUD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMVD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMWD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMXD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMYD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMZD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM0E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM1E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM2E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM3E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM4E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM5E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM6E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM7E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM8E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM9E1']}
2025-05-04 14:30:49,370 - INFO - 批量插入表单数据成功，批次 2，共 63 条记录
2025-05-04 14:30:49,385 - INFO - 成功插入的数据ID: ['FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMJC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMKC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMLC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMMC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMNC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMOC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMPC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMQC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMRC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMSC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMTC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMUC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMVC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMWC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMXC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMYC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMZC1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM0D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM1D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM2D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM3D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM4D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM5D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM6D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM7D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM8D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM9D1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMAD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMBD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMCD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMDD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMED1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMFD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMGD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMHD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMID1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMJD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMKD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMLD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMMD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMND1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMOD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMPD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMQD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMRD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMSD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMTD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMUD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMVD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMWD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMXD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMYD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AMZD1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM0E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM1E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM2E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM3E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM4E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM5E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM6E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM7E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM8E1', 'FINST-2PF66JD1O20VHR4J85BX46IAMCXO23E9T99AM9E1']
2025-05-04 14:30:54,401 - INFO - 批量插入完成，共 163 条记录
2025-05-04 14:30:54,401 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 163 条，错误: 0 条
2025-05-04 14:30:54,401 - INFO - 数据同步完成！更新: 0 条，插入: 163 条，错误: 1 条
2025-05-04 14:31:54,416 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 14:31:54,416 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 14:31:54,416 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 14:31:54,463 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 14:31:54,463 - ERROR - 未获取到MySQL数据
2025-05-04 14:31:54,463 - INFO - 同步完成
2025-05-04 15:30:33,707 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 15:30:33,707 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 15:30:33,707 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 15:30:33,769 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 168 条记录
2025-05-04 15:30:33,769 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 15:30:33,769 - INFO - 开始处理日期: 2025-05-02
2025-05-04 15:30:33,785 - INFO - Request Parameters - Page 1:
2025-05-04 15:30:33,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:30:33,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:30:41,910 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A384C3EF-EE94-70DA-9ED3-5585E529B2AE Response: {'code': 'ServiceUnavailable', 'requestid': 'A384C3EF-EE94-70DA-9ED3-5585E529B2AE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A384C3EF-EE94-70DA-9ED3-5585E529B2AE)
2025-05-04 15:30:41,910 - INFO - 开始处理日期: 2025-05-03
2025-05-04 15:30:41,910 - INFO - Request Parameters - Page 1:
2025-05-04 15:30:41,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:30:41,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:30:46,504 - INFO - Response - Page 1:
2025-05-04 15:30:46,504 - INFO - 第 1 页获取到 100 条记录
2025-05-04 15:30:46,707 - INFO - Request Parameters - Page 2:
2025-05-04 15:30:46,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:30:46,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:30:47,472 - INFO - Response - Page 2:
2025-05-04 15:30:47,488 - INFO - 第 2 页获取到 100 条记录
2025-05-04 15:30:47,691 - INFO - Request Parameters - Page 3:
2025-05-04 15:30:47,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:30:47,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:30:48,394 - INFO - Response - Page 3:
2025-05-04 15:30:48,394 - INFO - 第 3 页获取到 75 条记录
2025-05-04 15:30:48,597 - INFO - 查询完成，共获取到 275 条记录
2025-05-04 15:30:48,597 - INFO - 获取到 275 条表单数据
2025-05-04 15:30:48,597 - INFO - 当前日期 2025-05-03 有 164 条MySQL数据需要处理
2025-05-04 15:30:48,597 - INFO - 开始批量插入 1 条新记录
2025-05-04 15:30:48,754 - INFO - 批量插入响应状态码: 200
2025-05-04 15:30:48,754 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 07:28:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '42269FBA-3439-7C44-98DD-2162DA656168', 'x-acs-trace-id': 'a731131ca6d1efd647292c4c9f245359', 'etag': '66R/adYPNgs0ys/CYJr8Z3g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 15:30:48,754 - INFO - 批量插入响应体: {'result': ['FINST-3PF66O71220VCK7V8E44I5HS355O3CPEYB9AM99']}
2025-05-04 15:30:48,754 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-04 15:30:48,754 - INFO - 成功插入的数据ID: ['FINST-3PF66O71220VCK7V8E44I5HS355O3CPEYB9AM99']
2025-05-04 15:30:53,769 - INFO - 批量插入完成，共 1 条记录
2025-05-04 15:30:53,769 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-04 15:30:53,769 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-05-04 15:31:53,784 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 15:31:53,784 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 15:31:53,784 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 15:31:53,831 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 15:31:53,831 - ERROR - 未获取到MySQL数据
2025-05-04 15:31:53,831 - INFO - 同步完成
2025-05-04 16:30:33,731 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 16:30:33,731 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 16:30:33,731 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 16:30:33,794 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 168 条记录
2025-05-04 16:30:33,794 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 16:30:33,794 - INFO - 开始处理日期: 2025-05-02
2025-05-04 16:30:33,794 - INFO - Request Parameters - Page 1:
2025-05-04 16:30:33,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 16:30:33,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 16:30:41,903 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D586E3B9-162A-7040-8D1F-2F677D43077E Response: {'code': 'ServiceUnavailable', 'requestid': 'D586E3B9-162A-7040-8D1F-2F677D43077E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D586E3B9-162A-7040-8D1F-2F677D43077E)
2025-05-04 16:30:41,903 - INFO - 开始处理日期: 2025-05-03
2025-05-04 16:30:41,903 - INFO - Request Parameters - Page 1:
2025-05-04 16:30:41,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 16:30:41,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 16:30:42,684 - INFO - Response - Page 1:
2025-05-04 16:30:42,684 - INFO - 第 1 页获取到 100 条记录
2025-05-04 16:30:42,887 - INFO - Request Parameters - Page 2:
2025-05-04 16:30:42,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 16:30:42,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 16:30:50,997 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6F9451E4-0456-7491-AF93-781CA41C0FD3 Response: {'code': 'ServiceUnavailable', 'requestid': '6F9451E4-0456-7491-AF93-781CA41C0FD3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6F9451E4-0456-7491-AF93-781CA41C0FD3)
2025-05-04 16:30:50,997 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 16:31:51,012 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 16:31:51,012 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 16:31:51,012 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 16:31:51,059 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 16:31:51,059 - ERROR - 未获取到MySQL数据
2025-05-04 16:31:51,059 - INFO - 同步完成
2025-05-04 17:30:33,865 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 17:30:33,865 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 17:30:33,865 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 17:30:33,928 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 168 条记录
2025-05-04 17:30:33,928 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 17:30:33,943 - INFO - 开始处理日期: 2025-05-02
2025-05-04 17:30:33,943 - INFO - Request Parameters - Page 1:
2025-05-04 17:30:33,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 17:30:33,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 17:30:42,068 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 83093CE6-EEF2-7782-AD4C-2187025235B4 Response: {'code': 'ServiceUnavailable', 'requestid': '83093CE6-EEF2-7782-AD4C-2187025235B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 83093CE6-EEF2-7782-AD4C-2187025235B4)
2025-05-04 17:30:42,068 - INFO - 开始处理日期: 2025-05-03
2025-05-04 17:30:42,068 - INFO - Request Parameters - Page 1:
2025-05-04 17:30:42,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 17:30:42,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 17:30:50,177 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 749F20BB-18DD-7BD9-A361-9DBF5EBF5285 Response: {'code': 'ServiceUnavailable', 'requestid': '749F20BB-18DD-7BD9-A361-9DBF5EBF5285', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 749F20BB-18DD-7BD9-A361-9DBF5EBF5285)
2025-05-04 17:30:50,177 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 17:31:50,193 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 17:31:50,193 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 17:31:50,193 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 17:31:50,240 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 17:31:50,240 - ERROR - 未获取到MySQL数据
2025-05-04 17:31:50,240 - INFO - 同步完成
2025-05-04 18:30:34,030 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 18:30:34,030 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 18:30:34,030 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 18:30:34,093 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 168 条记录
2025-05-04 18:30:34,093 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 18:30:34,093 - INFO - 开始处理日期: 2025-05-02
2025-05-04 18:30:34,093 - INFO - Request Parameters - Page 1:
2025-05-04 18:30:34,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:30:34,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:30:42,233 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E06F2BAA-A55D-7450-A192-7F2804EBE962 Response: {'code': 'ServiceUnavailable', 'requestid': 'E06F2BAA-A55D-7450-A192-7F2804EBE962', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E06F2BAA-A55D-7450-A192-7F2804EBE962)
2025-05-04 18:30:42,233 - INFO - 开始处理日期: 2025-05-03
2025-05-04 18:30:42,233 - INFO - Request Parameters - Page 1:
2025-05-04 18:30:42,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:30:42,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:30:46,155 - INFO - Response - Page 1:
2025-05-04 18:30:46,155 - INFO - 第 1 页获取到 100 条记录
2025-05-04 18:30:46,358 - INFO - Request Parameters - Page 2:
2025-05-04 18:30:46,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:30:46,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:30:47,030 - INFO - Response - Page 2:
2025-05-04 18:30:47,030 - INFO - 第 2 页获取到 100 条记录
2025-05-04 18:30:47,233 - INFO - Request Parameters - Page 3:
2025-05-04 18:30:47,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:30:47,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:30:47,843 - INFO - Response - Page 3:
2025-05-04 18:30:47,843 - INFO - 第 3 页获取到 76 条记录
2025-05-04 18:30:48,046 - INFO - 查询完成，共获取到 276 条记录
2025-05-04 18:30:48,046 - INFO - 获取到 276 条表单数据
2025-05-04 18:30:48,046 - INFO - 当前日期 2025-05-03 有 164 条MySQL数据需要处理
2025-05-04 18:30:48,046 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 18:30:48,046 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-04 18:31:48,061 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 18:31:48,061 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 18:31:48,061 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 18:31:48,108 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 18:31:48,108 - ERROR - 未获取到MySQL数据
2025-05-04 18:31:48,108 - INFO - 同步完成
2025-05-04 19:30:33,745 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 19:30:33,745 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 19:30:33,745 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 19:30:33,823 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 168 条记录
2025-05-04 19:30:33,823 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 19:30:33,823 - INFO - 开始处理日期: 2025-05-02
2025-05-04 19:30:33,823 - INFO - Request Parameters - Page 1:
2025-05-04 19:30:33,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 19:30:33,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 19:30:41,948 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 66E90AE7-842A-74CC-BBF5-750D8948A980 Response: {'code': 'ServiceUnavailable', 'requestid': '66E90AE7-842A-74CC-BBF5-750D8948A980', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 66E90AE7-842A-74CC-BBF5-750D8948A980)
2025-05-04 19:30:41,948 - INFO - 开始处理日期: 2025-05-03
2025-05-04 19:30:41,948 - INFO - Request Parameters - Page 1:
2025-05-04 19:30:41,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 19:30:41,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 19:30:50,042 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F0F6B65A-0A0F-7355-A7AF-FEF53F51218E Response: {'code': 'ServiceUnavailable', 'requestid': 'F0F6B65A-0A0F-7355-A7AF-FEF53F51218E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F0F6B65A-0A0F-7355-A7AF-FEF53F51218E)
2025-05-04 19:30:50,042 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 19:31:50,057 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 19:31:50,057 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 19:31:50,057 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 19:31:50,104 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 19:31:50,104 - ERROR - 未获取到MySQL数据
2025-05-04 19:31:50,104 - INFO - 同步完成
2025-05-04 20:30:33,879 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 20:30:33,879 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 20:30:33,879 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 20:30:33,941 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 168 条记录
2025-05-04 20:30:33,941 - INFO - 获取到 2 个日期需要处理: ['2025-05-02', '2025-05-03']
2025-05-04 20:30:33,941 - INFO - 开始处理日期: 2025-05-02
2025-05-04 20:30:33,941 - INFO - Request Parameters - Page 1:
2025-05-04 20:30:33,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 20:30:33,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 20:30:42,066 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 10E0B7C0-1304-7A50-B5B7-D0F3F1CF0CEE Response: {'code': 'ServiceUnavailable', 'requestid': '10E0B7C0-1304-7A50-B5B7-D0F3F1CF0CEE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 10E0B7C0-1304-7A50-B5B7-D0F3F1CF0CEE)
2025-05-04 20:30:42,066 - INFO - 开始处理日期: 2025-05-03
2025-05-04 20:30:42,066 - INFO - Request Parameters - Page 1:
2025-05-04 20:30:42,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 20:30:42,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 20:30:50,285 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 36F402AB-5C13-7696-B492-743D00E1A750 Response: {'code': 'ServiceUnavailable', 'requestid': '36F402AB-5C13-7696-B492-743D00E1A750', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 36F402AB-5C13-7696-B492-743D00E1A750)
2025-05-04 20:30:50,285 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-04 20:31:50,300 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 20:31:50,300 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 20:31:50,300 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 20:31:50,347 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 20:31:50,347 - ERROR - 未获取到MySQL数据
2025-05-04 20:31:50,347 - INFO - 同步完成
2025-05-04 21:30:33,731 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 21:30:33,731 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 21:30:33,731 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 21:30:33,794 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 175 条记录
2025-05-04 21:30:33,794 - INFO - 获取到 3 个日期需要处理: ['2025-05-02', '2025-05-03', '2025-05-04']
2025-05-04 21:30:33,794 - INFO - 开始处理日期: 2025-05-02
2025-05-04 21:30:33,794 - INFO - Request Parameters - Page 1:
2025-05-04 21:30:33,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:30:33,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:30:41,919 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B1658EC9-6754-7A88-901B-BFC7AFC82BAF Response: {'code': 'ServiceUnavailable', 'requestid': 'B1658EC9-6754-7A88-901B-BFC7AFC82BAF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B1658EC9-6754-7A88-901B-BFC7AFC82BAF)
2025-05-04 21:30:41,919 - INFO - 开始处理日期: 2025-05-03
2025-05-04 21:30:41,919 - INFO - Request Parameters - Page 1:
2025-05-04 21:30:41,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:30:41,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:30:50,028 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 35BA256C-09CB-7568-8490-437B380E7309 Response: {'code': 'ServiceUnavailable', 'requestid': '35BA256C-09CB-7568-8490-437B380E7309', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 35BA256C-09CB-7568-8490-437B380E7309)
2025-05-04 21:30:50,028 - INFO - 开始处理日期: 2025-05-04
2025-05-04 21:30:50,028 - INFO - Request Parameters - Page 1:
2025-05-04 21:30:50,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:30:50,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:30:50,450 - INFO - Response - Page 1:
2025-05-04 21:30:50,450 - INFO - 查询完成，共获取到 0 条记录
2025-05-04 21:30:50,450 - INFO - 获取到 0 条表单数据
2025-05-04 21:30:50,450 - INFO - 当前日期 2025-05-04 有 7 条MySQL数据需要处理
2025-05-04 21:30:50,450 - INFO - 开始批量插入 7 条新记录
2025-05-04 21:30:50,622 - INFO - 批量插入响应状态码: 200
2025-05-04 21:30:50,622 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 13:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '355', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '03CC6EC9-4373-7328-AA29-5CBD6E2FA413', 'x-acs-trace-id': 'a58b5adca47e1742a15e259691aec31b', 'etag': '3hLDDA/7I5gJzJFTPQcjgtg5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 21:30:50,622 - INFO - 批量插入响应体: {'result': ['FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AM8C1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AM9C1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMAC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMBC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMCC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMDC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMEC1']}
2025-05-04 21:30:50,622 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-04 21:30:50,622 - INFO - 成功插入的数据ID: ['FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AM8C1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AM9C1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMAC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMBC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMCC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMDC1', 'FINST-9EA669D1F41VKLFCDDMOQBJHFSV721NDWO9AMEC1']
2025-05-04 21:30:55,637 - INFO - 批量插入完成，共 7 条记录
2025-05-04 21:30:55,637 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-05-04 21:30:55,637 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 2 条
2025-05-04 21:31:55,653 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 21:31:55,653 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 21:31:55,653 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 21:31:55,699 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 21:31:55,699 - ERROR - 未获取到MySQL数据
2025-05-04 21:31:55,699 - INFO - 同步完成
2025-05-04 22:30:33,927 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 22:30:33,927 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 22:30:33,943 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 22:30:34,005 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 302 条记录
2025-05-04 22:30:34,005 - INFO - 获取到 3 个日期需要处理: ['2025-05-02', '2025-05-03', '2025-05-04']
2025-05-04 22:30:34,005 - INFO - 开始处理日期: 2025-05-02
2025-05-04 22:30:34,005 - INFO - Request Parameters - Page 1:
2025-05-04 22:30:34,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 22:30:34,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 22:30:42,130 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 97F6B7B8-257A-78BC-9EFC-377F35A283F8 Response: {'code': 'ServiceUnavailable', 'requestid': '97F6B7B8-257A-78BC-9EFC-377F35A283F8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 97F6B7B8-257A-78BC-9EFC-377F35A283F8)
2025-05-04 22:30:42,130 - INFO - 开始处理日期: 2025-05-03
2025-05-04 22:30:42,130 - INFO - Request Parameters - Page 1:
2025-05-04 22:30:42,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 22:30:42,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 22:30:46,365 - INFO - Response - Page 1:
2025-05-04 22:30:46,365 - INFO - 第 1 页获取到 100 条记录
2025-05-04 22:30:46,568 - INFO - Request Parameters - Page 2:
2025-05-04 22:30:46,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 22:30:46,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 22:30:54,693 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1DF81EAD-C8D6-70B1-8362-BA07A697AE77 Response: {'code': 'ServiceUnavailable', 'requestid': '1DF81EAD-C8D6-70B1-8362-BA07A697AE77', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1DF81EAD-C8D6-70B1-8362-BA07A697AE77)
2025-05-04 22:30:54,693 - INFO - 开始处理日期: 2025-05-04
2025-05-04 22:30:54,693 - INFO - Request Parameters - Page 1:
2025-05-04 22:30:54,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 22:30:54,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 22:30:55,208 - INFO - Response - Page 1:
2025-05-04 22:30:55,208 - INFO - 第 1 页获取到 7 条记录
2025-05-04 22:30:55,412 - INFO - 查询完成，共获取到 7 条记录
2025-05-04 22:30:55,412 - INFO - 获取到 7 条表单数据
2025-05-04 22:30:55,412 - INFO - 当前日期 2025-05-04 有 134 条MySQL数据需要处理
2025-05-04 22:30:55,412 - INFO - 开始批量插入 127 条新记录
2025-05-04 22:30:55,693 - INFO - 批量插入响应状态码: 200
2025-05-04 22:30:55,693 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 14:30:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4912', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AD2F5667-9FA7-788B-9965-D089D9799A3E', 'x-acs-trace-id': 'd7197337c48ba9af9f970f9f9399eaae', 'etag': '4ji5spNTW72YbQHZ1SmlYCw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 22:30:55,693 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMXS1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMYS1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMZS1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM0T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM1T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM2T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM3T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM4T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM5T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM7T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM8T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM9T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMAT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMBT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMCT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMDT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMET1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMKT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMLT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMMT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMNT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMOT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMPT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMQT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMRT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMST1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMTT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMUT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMVT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMWT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMXT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMYT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMZT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM0U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM1U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM2U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM3U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM4U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM5U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM7U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM8U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM9U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMAU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMBU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMCU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMDU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMEU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMKU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMLU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMMU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMNU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMOU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMPU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMQU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMRU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMSU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMTU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMUU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMVU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMWU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMXU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMYU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMZU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM0V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM1V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM2V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM3V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM4V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM5V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM7V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM8V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM9V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMAV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMBV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMCV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMDV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMEV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMKV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMLV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMMV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMNV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMOV1']}
2025-05-04 22:30:55,693 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-04 22:30:55,693 - INFO - 成功插入的数据ID: ['FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMXS1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMYS1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMZS1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM0T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM1T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM2T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM3T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM4T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM5T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM7T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM8T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM9T1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMAT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMBT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMCT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMDT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMET1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMKT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMLT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMMT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMNT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMOT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMPT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMQT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMRT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMST1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMTT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMUT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMVT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMWT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMXT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMYT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMZT1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM0U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM1U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM2U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM3U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM4U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM5U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM7U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM8U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM9U1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMAU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMBU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMCU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMDU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMEU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMKU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMLU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMMU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMNU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMOU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMPU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMQU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMRU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMSU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMTU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMUU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMVU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMWU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMXU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMYU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMZU1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM0V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM1V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM2V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM3V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM4V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM5V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM6V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM7V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM8V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AM9V1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMAV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMBV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMCV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMDV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMEV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMHV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMKV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMLV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMMV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMNV1', 'FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMOV1']
2025-05-04 22:31:00,880 - INFO - 批量插入响应状态码: 200
2025-05-04 22:31:00,880 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 14:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1335', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '99B1CE05-D33C-7052-8486-E1FA430D8809', 'x-acs-trace-id': '65ad0a289b68994a7dcd92acdc0eec64', 'etag': '1WeqG28vMjypH7x/uvG0gqQ5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 22:31:00,880 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMU21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMV21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMW21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMX21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMY21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMZ21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM031', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM131', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM231', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM331', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM431', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM531', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM631', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM731', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM831', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM931', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMA31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMB31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMC31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMD31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AME31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMF31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMG31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMH31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMI31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMJ31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMK31']}
2025-05-04 22:31:00,880 - INFO - 批量插入表单数据成功，批次 2，共 27 条记录
2025-05-04 22:31:00,880 - INFO - 成功插入的数据ID: ['FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMU21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMV21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMW21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMX21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMY21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMZ21', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM031', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM131', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM231', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM331', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM431', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM531', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM631', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM731', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM831', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AM931', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMA31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMB31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMC31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMD31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AME31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMF31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMG31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMH31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMI31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMJ31', 'FINST-7PF66N91211VT75Q6E56RCRT9OWS3VCR1R9AMK31']
2025-05-04 22:31:05,896 - INFO - 批量插入完成，共 127 条记录
2025-05-04 22:31:05,896 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 127 条，错误: 0 条
2025-05-04 22:31:05,896 - INFO - 数据同步完成！更新: 0 条，插入: 127 条，错误: 2 条
2025-05-04 22:32:05,911 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 22:32:05,911 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 22:32:05,911 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 22:32:05,958 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 22:32:05,958 - ERROR - 未获取到MySQL数据
2025-05-04 22:32:05,958 - INFO - 同步完成
2025-05-04 23:30:33,400 - INFO - 使用默认增量同步（当天更新数据）
2025-05-04 23:30:33,400 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 23:30:33,400 - INFO - 查询参数: ('2025-05-04',)
2025-05-04 23:30:33,463 - INFO - MySQL查询成功，增量数据（日期: 2025-05-04），共获取 408 条记录
2025-05-04 23:30:33,463 - INFO - 获取到 3 个日期需要处理: ['2025-05-02', '2025-05-03', '2025-05-04']
2025-05-04 23:30:33,479 - INFO - 开始处理日期: 2025-05-02
2025-05-04 23:30:33,479 - INFO - Request Parameters - Page 1:
2025-05-04 23:30:33,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 23:30:33,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 23:30:41,614 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BF31972A-4DE4-7E2A-885C-8849BD098CB9 Response: {'code': 'ServiceUnavailable', 'requestid': 'BF31972A-4DE4-7E2A-885C-8849BD098CB9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BF31972A-4DE4-7E2A-885C-8849BD098CB9)
2025-05-04 23:30:41,614 - INFO - 开始处理日期: 2025-05-03
2025-05-04 23:30:41,614 - INFO - Request Parameters - Page 1:
2025-05-04 23:30:41,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 23:30:41,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 23:30:49,749 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0DEAE914-559E-75C7-BD9E-CB56C80F778C Response: {'code': 'ServiceUnavailable', 'requestid': '0DEAE914-559E-75C7-BD9E-CB56C80F778C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0DEAE914-559E-75C7-BD9E-CB56C80F778C)
2025-05-04 23:30:49,749 - INFO - 开始处理日期: 2025-05-04
2025-05-04 23:30:49,749 - INFO - Request Parameters - Page 1:
2025-05-04 23:30:49,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 23:30:49,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 23:30:53,958 - INFO - Response - Page 1:
2025-05-04 23:30:53,958 - INFO - 第 1 页获取到 100 条记录
2025-05-04 23:30:54,161 - INFO - Request Parameters - Page 2:
2025-05-04 23:30:54,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 23:30:54,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 23:30:54,771 - INFO - Response - Page 2:
2025-05-04 23:30:54,771 - INFO - 第 2 页获取到 34 条记录
2025-05-04 23:30:54,974 - INFO - 查询完成，共获取到 134 条记录
2025-05-04 23:30:54,974 - INFO - 获取到 134 条表单数据
2025-05-04 23:30:54,974 - INFO - 当前日期 2025-05-04 有 240 条MySQL数据需要处理
2025-05-04 23:30:54,974 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFT1
2025-05-04 23:30:55,413 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMFT1
2025-05-04 23:30:55,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 60000.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 60000.0}]
2025-05-04 23:30:55,413 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGT1
2025-05-04 23:30:55,757 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMGT1
2025-05-04 23:30:55,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20000.0, 'new_value': 80000.0}, {'field': 'total_amount', 'old_value': 20000.0, 'new_value': 80000.0}]
2025-05-04 23:30:55,772 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIT1
2025-05-04 23:30:56,195 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIT1
2025-05-04 23:30:56,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100000.0, 'new_value': 120000.0}, {'field': 'total_amount', 'old_value': 100000.0, 'new_value': 120000.0}]
2025-05-04 23:30:56,195 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJT1
2025-05-04 23:30:56,570 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJT1
2025-05-04 23:30:56,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150000.0, 'new_value': 400000.0}, {'field': 'total_amount', 'old_value': 150000.0, 'new_value': 400000.0}]
2025-05-04 23:30:56,570 - INFO - 开始批量插入 106 条新记录
2025-05-04 23:30:56,852 - INFO - 批量插入响应状态码: 200
2025-05-04 23:30:56,852 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 15:30:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4912', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D8BBD9D-02C5-7579-B659-796177D443A3', 'x-acs-trace-id': '36f946728a4e4d6cb1257ff81cfc5c1e', 'etag': '4Wcf1zzYAXLuz0m/6jGEfmA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 23:30:56,852 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM601', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM701', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM801', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM901', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMD01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMF01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMG01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMI01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMK01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AML01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMN01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMO01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMP01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMQ01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMR01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMT01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMU01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMW01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMX01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMY01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMZ01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM011', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM111', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM211', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM311', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM411', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM511', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM611', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM711', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM811', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM911', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMD11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMF11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMG11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMI11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMK11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AML11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMN11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMO11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMP11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMQ11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMR11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMT11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMU11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMW11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMX11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMY11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMZ11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM021', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM121', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM221', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM321', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM421', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM521', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM621', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM721', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM821', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM921', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMD21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMF21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMG21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMI21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMK21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AML21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMN21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMO21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMP21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMQ21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMR21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMT21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMU21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMW21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMX21']}
2025-05-04 23:30:56,852 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-04 23:30:56,852 - INFO - 成功插入的数据ID: ['FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM601', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM701', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM801', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM901', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMD01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMF01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMG01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMI01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMK01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AML01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMN01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMO01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMP01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMQ01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMR01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMT01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMU01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMW01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMX01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMY01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMZ01', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM011', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM111', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM211', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM311', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM411', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM511', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM611', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM711', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM811', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM911', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMD11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMF11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMG11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMI11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMK11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AML11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMN11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMO11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMP11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMQ11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMR11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMT11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMU11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMW11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMX11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMY11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMZ11', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM021', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM121', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM221', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM321', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM421', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM521', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM621', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM721', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM821', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM921', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMD21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMF21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMG21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMI21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMK21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AML21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMN21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMO21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMP21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMQ21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMR21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMT21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMU21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMV21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMW21', 'FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMX21']
2025-05-04 23:31:02,015 - INFO - 批量插入响应状态码: 200
2025-05-04 23:31:02,030 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 15:30:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EA52CC06-FC44-7F1A-A1F3-782073BBC015', 'x-acs-trace-id': 'dd77b58f70da4bb34b8175e88be6c886', 'etag': '39Yb/gnz33m9ntXLAZagTLQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 23:31:02,030 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMV9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMW9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMX9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMY9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMZ9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AM0A']}
2025-05-04 23:31:02,030 - INFO - 批量插入表单数据成功，批次 2，共 6 条记录
2025-05-04 23:31:02,030 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMV9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMW9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMX9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMY9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AMZ9', 'FINST-E3G66QA1UNZUJGXZEB27H5KVS20B3UAW6T9AM0A']
2025-05-04 23:31:07,052 - INFO - 批量插入完成，共 106 条记录
2025-05-04 23:31:07,052 - INFO - 日期 2025-05-04 处理完成 - 更新: 4 条，插入: 106 条，错误: 0 条
2025-05-04 23:31:07,052 - INFO - 数据同步完成！更新: 4 条，插入: 106 条，错误: 2 条
2025-05-04 23:32:07,144 - INFO - 开始同步昨天与今天的销售数据: 20250503 至 *************-05-04 23:32:07,144 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-04 23:32:07,144 - INFO - 查询参数: ('20250503', '20250504')
2025-05-04 23:32:07,191 - INFO - MySQL查询成功，时间段: 20250503 至 20250504，共获取 0 条记录
2025-05-04 23:32:07,191 - ERROR - 未获取到MySQL数据
2025-05-04 23:32:07,191 - INFO - 同步完成
