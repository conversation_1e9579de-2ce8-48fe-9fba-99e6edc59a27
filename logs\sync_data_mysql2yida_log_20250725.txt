2025-07-25 01:30:33,681 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 01:30:33,681 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 01:30:33,681 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 01:30:33,837 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 4 条记录
2025-07-25 01:30:33,837 - INFO - 获取到 1 个日期需要处理: ['2025-07-24']
2025-07-25 01:30:33,837 - INFO - 开始处理日期: 2025-07-24
2025-07-25 01:30:33,837 - INFO - Request Parameters - Page 1:
2025-07-25 01:30:33,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 01:30:33,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 01:30:41,946 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 128FF7BC-51DA-79B1-AC43-5F5A0A75EC44 Response: {'code': 'ServiceUnavailable', 'requestid': '128FF7BC-51DA-79B1-AC43-5F5A0A75EC44', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 128FF7BC-51DA-79B1-AC43-5F5A0A75EC44)
2025-07-25 01:30:41,946 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-25 01:31:41,961 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 01:31:41,961 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 01:31:41,961 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 01:31:42,118 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 127 条记录
2025-07-25 01:31:42,118 - INFO - 获取到 1 个日期需要处理: ['2025-07-24']
2025-07-25 01:31:42,118 - INFO - 开始处理日期: 2025-07-24
2025-07-25 01:31:42,118 - INFO - Request Parameters - Page 1:
2025-07-25 01:31:42,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 01:31:42,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 01:31:46,321 - INFO - Response - Page 1:
2025-07-25 01:31:46,321 - INFO - 第 1 页获取到 50 条记录
2025-07-25 01:31:46,836 - INFO - Request Parameters - Page 2:
2025-07-25 01:31:46,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 01:31:46,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 01:31:47,461 - INFO - Response - Page 2:
2025-07-25 01:31:47,461 - INFO - 第 2 页获取到 19 条记录
2025-07-25 01:31:47,977 - INFO - 查询完成，共获取到 69 条记录
2025-07-25 01:31:47,977 - INFO - 获取到 69 条表单数据
2025-07-25 01:31:47,977 - INFO - 当前日期 2025-07-24 有 122 条MySQL数据需要处理
2025-07-25 01:31:47,977 - INFO - 开始批量插入 53 条新记录
2025-07-25 01:31:48,243 - INFO - 批量插入响应状态码: 200
2025-07-25 01:31:48,243 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 17:31:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '121D211C-234F-7020-8143-708D43311272', 'x-acs-trace-id': 'd2649f53f8f791aec121b7a61782be2f', 'etag': '2206/Dn9psBhe6N2mirpL1g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 01:31:48,243 - INFO - 批量插入响应体: {'result': ['FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM77', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM87', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM97', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMA7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMB7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMC7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMD7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDME7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMF7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMG7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMH7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMI7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMJ7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMK7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDML7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMM7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMN7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMO7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMP7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMQ7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMR7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMS7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMT7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMU7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMV7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMW7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMX7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMY7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMZ7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM08', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM18', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM28', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM38', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM48', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM58', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM68', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM78', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDM88', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDM98', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMA8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMB8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMC8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMD8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDME8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMF8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMG8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMH8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMI8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMJ8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMK8']}
2025-07-25 01:31:48,243 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-25 01:31:48,243 - INFO - 成功插入的数据ID: ['FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM77', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM87', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM97', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMA7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMB7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMC7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMD7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDME7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMF7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMG7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMH7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMI7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMJ7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMK7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDML7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMM7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMN7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMO7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMP7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMQ7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMR7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMS7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMT7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMU7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMV7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMW7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMX7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMY7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDMZ7', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM08', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM18', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM28', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM38', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM48', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM58', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM68', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2H096OHDM78', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDM88', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDM98', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMA8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMB8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMC8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMD8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDME8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMF8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMG8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMH8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMI8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMJ8', 'FINST-RI766091RHFX15WY51TVH4SSEPGG2I096OHDMK8']
2025-07-25 01:31:53,415 - INFO - 批量插入响应状态码: 200
2025-07-25 01:31:53,415 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 17:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9D0455F3-7AC4-7B63-87BB-CDCAC6B2FDBE', 'x-acs-trace-id': '52708d65780358ae1b992679fcdbf6a2', 'etag': '1qGH9UVfvSSyDoiqO5puL3g6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 01:31:53,415 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1PHFX75P7E7WRJ7MUER3W340D6OHDMCC', 'FINST-XL866HB1PHFX75P7E7WRJ7MUER3W340D6OHDMDC', 'FINST-XL866HB1PHFX75P7E7WRJ7MUER3W340D6OHDMEC']}
2025-07-25 01:31:53,415 - INFO - 批量插入表单数据成功，批次 2，共 3 条记录
2025-07-25 01:31:53,415 - INFO - 成功插入的数据ID: ['FINST-XL866HB1PHFX75P7E7WRJ7MUER3W340D6OHDMCC', 'FINST-XL866HB1PHFX75P7E7WRJ7MUER3W340D6OHDMDC', 'FINST-XL866HB1PHFX75P7E7WRJ7MUER3W340D6OHDMEC']
2025-07-25 01:31:58,430 - INFO - 批量插入完成，共 53 条记录
2025-07-25 01:31:58,430 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 53 条，错误: 0 条
2025-07-25 01:31:58,430 - INFO - 数据同步完成！更新: 0 条，插入: 53 条，错误: 0 条
2025-07-25 01:31:58,430 - INFO - 同步完成
2025-07-25 04:30:33,670 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 04:30:33,670 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 04:30:33,670 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 04:30:33,827 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 5 条记录
2025-07-25 04:30:33,827 - INFO - 获取到 1 个日期需要处理: ['2025-07-24']
2025-07-25 04:30:33,827 - INFO - 开始处理日期: 2025-07-24
2025-07-25 04:30:33,827 - INFO - Request Parameters - Page 1:
2025-07-25 04:30:33,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 04:30:33,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 04:30:41,967 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 01F11F70-313A-79C5-8808-52D46D1343F1 Response: {'code': 'ServiceUnavailable', 'requestid': '01F11F70-313A-79C5-8808-52D46D1343F1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 01F11F70-313A-79C5-8808-52D46D1343F1)
2025-07-25 04:30:41,967 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-25 04:31:41,982 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 04:31:41,982 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 04:31:41,982 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 04:31:42,123 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 128 条记录
2025-07-25 04:31:42,123 - INFO - 获取到 1 个日期需要处理: ['2025-07-24']
2025-07-25 04:31:42,139 - INFO - 开始处理日期: 2025-07-24
2025-07-25 04:31:42,139 - INFO - Request Parameters - Page 1:
2025-07-25 04:31:42,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 04:31:42,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 04:31:42,982 - INFO - Response - Page 1:
2025-07-25 04:31:42,982 - INFO - 第 1 页获取到 50 条记录
2025-07-25 04:31:43,498 - INFO - Request Parameters - Page 2:
2025-07-25 04:31:43,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 04:31:43,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 04:31:44,232 - INFO - Response - Page 2:
2025-07-25 04:31:44,232 - INFO - 第 2 页获取到 50 条记录
2025-07-25 04:31:44,732 - INFO - Request Parameters - Page 3:
2025-07-25 04:31:44,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 04:31:44,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 04:31:45,373 - INFO - Response - Page 3:
2025-07-25 04:31:45,373 - INFO - 第 3 页获取到 22 条记录
2025-07-25 04:31:45,889 - INFO - 查询完成，共获取到 122 条记录
2025-07-25 04:31:45,889 - INFO - 获取到 122 条表单数据
2025-07-25 04:31:45,889 - INFO - 当前日期 2025-07-24 有 123 条MySQL数据需要处理
2025-07-25 04:31:45,889 - INFO - 开始批量插入 1 条新记录
2025-07-25 04:31:46,060 - INFO - 批量插入响应状态码: 200
2025-07-25 04:31:46,060 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 20:31:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '834BE87A-20E3-7926-A5C6-256957A7EAA7', 'x-acs-trace-id': '7458be9e46f41e75406fab9dd15d7925', 'etag': '6IiR8/tHsGCBRz4IaTQ2zew0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 04:31:46,060 - INFO - 批量插入响应体: {'result': ['FINST-07E66I917HFXWO1M7BSAO6Z342V33UPOLUHDMWA']}
2025-07-25 04:31:46,060 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-25 04:31:46,060 - INFO - 成功插入的数据ID: ['FINST-07E66I917HFXWO1M7BSAO6Z342V33UPOLUHDMWA']
2025-07-25 04:31:51,060 - INFO - 批量插入完成，共 1 条记录
2025-07-25 04:31:51,060 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-25 04:31:51,060 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-07-25 04:31:51,060 - INFO - 同步完成
2025-07-25 07:30:33,848 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 07:30:33,848 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 07:30:33,848 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 07:30:34,004 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 5 条记录
2025-07-25 07:30:34,004 - INFO - 获取到 1 个日期需要处理: ['2025-07-24']
2025-07-25 07:30:34,004 - INFO - 开始处理日期: 2025-07-24
2025-07-25 07:30:34,004 - INFO - Request Parameters - Page 1:
2025-07-25 07:30:34,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 07:30:34,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 07:30:42,129 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3B9D9C1A-DADF-7C81-B61C-ADB0D6A2E686 Response: {'code': 'ServiceUnavailable', 'requestid': '3B9D9C1A-DADF-7C81-B61C-ADB0D6A2E686', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3B9D9C1A-DADF-7C81-B61C-ADB0D6A2E686)
2025-07-25 07:30:42,129 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-25 07:31:42,144 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 07:31:42,144 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 07:31:42,144 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 07:31:42,300 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 128 条记录
2025-07-25 07:31:42,300 - INFO - 获取到 1 个日期需要处理: ['2025-07-24']
2025-07-25 07:31:42,300 - INFO - 开始处理日期: 2025-07-24
2025-07-25 07:31:42,300 - INFO - Request Parameters - Page 1:
2025-07-25 07:31:42,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 07:31:42,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 07:31:43,160 - INFO - Response - Page 1:
2025-07-25 07:31:43,160 - INFO - 第 1 页获取到 50 条记录
2025-07-25 07:31:43,660 - INFO - Request Parameters - Page 2:
2025-07-25 07:31:43,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 07:31:43,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 07:31:44,378 - INFO - Response - Page 2:
2025-07-25 07:31:44,378 - INFO - 第 2 页获取到 50 条记录
2025-07-25 07:31:44,878 - INFO - Request Parameters - Page 3:
2025-07-25 07:31:44,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 07:31:44,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 07:31:45,535 - INFO - Response - Page 3:
2025-07-25 07:31:45,535 - INFO - 第 3 页获取到 23 条记录
2025-07-25 07:31:46,050 - INFO - 查询完成，共获取到 123 条记录
2025-07-25 07:31:46,050 - INFO - 获取到 123 条表单数据
2025-07-25 07:31:46,050 - INFO - 当前日期 2025-07-24 有 123 条MySQL数据需要处理
2025-07-25 07:31:46,050 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 07:31:46,050 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 07:31:46,050 - INFO - 同步完成
2025-07-25 10:30:33,720 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 10:30:33,720 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 10:30:33,720 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 10:30:33,892 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 195 条记录
2025-07-25 10:30:33,892 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 10:30:33,892 - INFO - 开始处理日期: 2025-07-24
2025-07-25 10:30:33,892 - INFO - Request Parameters - Page 1:
2025-07-25 10:30:33,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:30:33,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:30:42,002 - INFO - Response - Page 1:
2025-07-25 10:30:42,002 - INFO - 第 1 页获取到 50 条记录
2025-07-25 10:30:42,517 - INFO - Request Parameters - Page 2:
2025-07-25 10:30:42,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:30:42,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:30:50,642 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1C02C6CD-7409-7BC8-BFAA-2D5E4FF226F1 Response: {'code': 'ServiceUnavailable', 'requestid': '1C02C6CD-7409-7BC8-BFAA-2D5E4FF226F1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1C02C6CD-7409-7BC8-BFAA-2D5E4FF226F1)
2025-07-25 10:30:50,642 - INFO - 开始处理日期: 2025-07-25
2025-07-25 10:30:50,642 - INFO - Request Parameters - Page 1:
2025-07-25 10:30:50,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:30:50,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:30:55,830 - INFO - Response - Page 1:
2025-07-25 10:30:55,830 - INFO - 查询完成，共获取到 0 条记录
2025-07-25 10:30:55,830 - INFO - 获取到 0 条表单数据
2025-07-25 10:30:55,830 - INFO - 当前日期 2025-07-25 有 2 条MySQL数据需要处理
2025-07-25 10:30:55,830 - INFO - 开始批量插入 2 条新记录
2025-07-25 10:30:55,986 - INFO - 批量插入响应状态码: 200
2025-07-25 10:30:55,986 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:30:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F503B1C-64BB-73FF-83E2-CA6F6FB22BE4', 'x-acs-trace-id': 'a818d243041972e6ade3ebf433f099c2', 'etag': '1ar5TRx2q3ed3H1s4xaTKbg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:30:55,986 - INFO - 批量插入响应体: {'result': ['FINST-68E66TC1VGFXZN19FW2KG4EB74GY24VKF7IDM6G', 'FINST-68E66TC1VGFXZN19FW2KG4EB74GY24VKF7IDM7G']}
2025-07-25 10:30:55,986 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-25 10:30:55,986 - INFO - 成功插入的数据ID: ['FINST-68E66TC1VGFXZN19FW2KG4EB74GY24VKF7IDM6G', 'FINST-68E66TC1VGFXZN19FW2KG4EB74GY24VKF7IDM7G']
2025-07-25 10:31:01,002 - INFO - 批量插入完成，共 2 条记录
2025-07-25 10:31:01,002 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-25 10:31:01,002 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 1 条
2025-07-25 10:32:01,017 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 10:32:01,017 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 10:32:01,017 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 10:32:01,189 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 530 条记录
2025-07-25 10:32:01,189 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 10:32:01,189 - INFO - 开始处理日期: 2025-07-24
2025-07-25 10:32:01,189 - INFO - Request Parameters - Page 1:
2025-07-25 10:32:01,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:32:01,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:32:02,001 - INFO - Response - Page 1:
2025-07-25 10:32:02,001 - INFO - 第 1 页获取到 50 条记录
2025-07-25 10:32:02,517 - INFO - Request Parameters - Page 2:
2025-07-25 10:32:02,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:32:02,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:32:09,970 - INFO - Response - Page 2:
2025-07-25 10:32:09,970 - INFO - 第 2 页获取到 50 条记录
2025-07-25 10:32:10,470 - INFO - Request Parameters - Page 3:
2025-07-25 10:32:10,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:32:10,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:32:11,110 - INFO - Response - Page 3:
2025-07-25 10:32:11,110 - INFO - 第 3 页获取到 23 条记录
2025-07-25 10:32:11,610 - INFO - 查询完成，共获取到 123 条记录
2025-07-25 10:32:11,610 - INFO - 获取到 123 条表单数据
2025-07-25 10:32:11,610 - INFO - 当前日期 2025-07-24 有 518 条MySQL数据需要处理
2025-07-25 10:32:11,610 - INFO - 开始批量插入 395 条新记录
2025-07-25 10:32:11,860 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:11,860 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2397', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3860A270-08BE-75D7-87E0-1B58C7B5768E', 'x-acs-trace-id': 'b01a5adc1cf70343e50acb8b97fde110', 'etag': '25vWusyNTRFxmw59o6xrmeg7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:11,860 - INFO - 批量插入响应体: {'result': ['FINST-737662B1FGFXIUCAAAUNXCNGBBJ13HE7H7IDML', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMM', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMN', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMO', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMP', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMQ', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMR', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMS', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMT', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMU', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMV', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMW', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMX', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMY', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMZ', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM01', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM11', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM21', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM31', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM41', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM51', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM61', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM71', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM81', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM91', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMA1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMB1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMC1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMD1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDME1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMF1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMG1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMH1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMI1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMJ1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMK1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDML1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMM1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMN1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMO1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMP1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMQ1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMR1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMS1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMT1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMU1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMV1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMW1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMX1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMY1']}
2025-07-25 10:32:11,860 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-25 10:32:11,860 - INFO - 成功插入的数据ID: ['FINST-737662B1FGFXIUCAAAUNXCNGBBJ13HE7H7IDML', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMM', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMN', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMO', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMP', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMQ', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMR', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMS', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMT', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMU', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMV', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMW', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMX', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMY', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMZ', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM01', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM11', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM21', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM31', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM41', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM51', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM61', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM71', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM81', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDM91', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMA1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMB1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMC1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMD1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDME1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMF1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMG1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMH1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMI1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMJ1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMK1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDML1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMM1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMN1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMO1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMP1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMQ1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMR1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMS1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMT1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMU1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMV1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMW1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMX1', 'FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMY1']
2025-07-25 10:32:17,095 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:17,095 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7B0A33FD-7B57-7B12-9A24-DC3AA83ED882', 'x-acs-trace-id': 'f58dbfdd814fa90082bfcb2bc9154c15', 'etag': '2TcvG5IV39Rr/zB5/wvqWug2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:17,095 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMGH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMHH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMIH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMJH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMKH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMLH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMMH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMNH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMOH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMPH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMQH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMRH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMSH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMTH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMUH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMVH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMWH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMXH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMYH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMZH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM0I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM1I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM2I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM3I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM4I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM5I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM6I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM7I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM8I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM9I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMAI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMBI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMCI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMDI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMEI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMFI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMGI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMHI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMII', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMJI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMKI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMLI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMMI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMNI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMOI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMPI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMQI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMRI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMSI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMTI']}
2025-07-25 10:32:17,095 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-25 10:32:17,095 - INFO - 成功插入的数据ID: ['FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMGH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMHH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMIH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMJH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMKH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMLH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMMH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMNH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMOH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMPH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMQH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMRH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMSH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMTH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMUH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMVH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMWH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMXH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMYH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMZH', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM0I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM1I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM2I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM3I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM4I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM5I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM6I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM7I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM8I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDM9I', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMAI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMBI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMCI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMDI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMEI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMFI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMGI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMHI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMII', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMJI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMKI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMLI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMMI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMNI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMOI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMPI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMQI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMRI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMSI', 'FINST-DIC66I91QGFXDHSHBZ3WRAUWRUQN29GBH7IDMTI']
2025-07-25 10:32:22,329 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:22,329 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2968346B-53B1-74F7-B95E-F66F74CBA606', 'x-acs-trace-id': '26753e0814362103ad689b6025ddd649', 'etag': '2pAzbOmXhFG+UuVpyYBptfg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:22,329 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMS7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMT7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMU7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMV7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMW7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMX7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMY7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMZ7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM08', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM18', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM28', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM38', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM48', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM58', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM68', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM78', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM88', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM98', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMA8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMB8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMC8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMD8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDME8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMF8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMG8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMH8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMI8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMJ8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMK8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDML8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMM8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMN8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMO8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMP8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMQ8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMR8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMS8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMT8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMU8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMV8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMW8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMX8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMY8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMZ8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM09', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM19', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM29', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM39', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM49', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM59']}
2025-07-25 10:32:22,329 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-25 10:32:22,329 - INFO - 成功插入的数据ID: ['FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMS7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMT7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMU7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMV7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMW7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMX7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMY7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMZ7', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM08', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM18', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM28', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM38', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM48', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM58', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM68', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM78', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM88', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM98', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMA8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMB8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMC8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMD8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDME8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMF8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMG8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMH8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMI8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMJ8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMK8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDML8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMM8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMN8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMO8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMP8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMQ8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMR8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMS8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMT8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMU8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMV8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMW8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMX8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMY8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDMZ8', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM09', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM19', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM29', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM39', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM49', 'FINST-90E66JD1EIEX8HE67LMFMD2WLX453LHFH7IDM59']
2025-07-25 10:32:27,642 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:27,642 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '43A2A1C4-88AA-7ACC-8E5E-191E47574098', 'x-acs-trace-id': 'e5cdaaea1e0ea79dace94f6820320552', 'etag': '2coHrrT93t9Tl3zpuT6eIRQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:27,642 - INFO - 批量插入响应体: {'result': ['FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMY4', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMZ4', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM05', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM15', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM25', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM35', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM45', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM55', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM65', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM75', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM85', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM95', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMA5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMB5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMC5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMD5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDME5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMF5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMG5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMH5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMI5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMJ5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMK5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDML5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMM5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMN5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMO5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMP5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMQ5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMR5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMS5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMT5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMU5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMV5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMW5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMX5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMY5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMZ5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM06', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM16', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM26', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM36', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM46', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM56', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM66', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM76', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM86', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM96', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMA6', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMB6']}
2025-07-25 10:32:27,642 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-25 10:32:27,642 - INFO - 成功插入的数据ID: ['FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMY4', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMZ4', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM05', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM15', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM25', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM35', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM45', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM55', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM65', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM75', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM85', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM95', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMA5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMB5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMC5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMD5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDME5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMF5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMG5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMH5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMI5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMJ5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMK5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDML5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMM5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMN5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMO5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMP5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMQ5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMR5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMS5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMT5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMU5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMV5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMW5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMX5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMY5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMZ5', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM06', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM16', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM26', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM36', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM46', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM56', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM66', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM76', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM86', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDM96', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMA6', 'FINST-737662B1VCFXAI2DBHIJFBEOMT1531LJH7IDMB6']
2025-07-25 10:32:32,892 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:32,892 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '11A2ECF8-89E4-7F0B-BF94-389A8A88BE1C', 'x-acs-trace-id': 'cd0b15ad5b98389d246601fb27d9e928', 'etag': '2ssXIaKQJYa2azTMF3MiOpg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:32,892 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMJX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMKX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMLX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMMX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMNX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMOX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMPX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMQX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMRX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMSX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMTX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMUX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMVX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMWX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMXX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMYX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMZX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM0Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM1Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM2Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM3Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM4Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM5Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM6Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM7Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM8Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM9Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMAY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMBY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMCY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMDY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMEY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMFY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMGY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMHY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMIY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMJY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMKY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMLY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMMY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMNY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMOY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMPY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMQY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMRY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMSY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMTY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMUY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMVY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMWY']}
2025-07-25 10:32:32,892 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-25 10:32:32,892 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMJX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMKX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMLX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMMX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMNX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMOX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMPX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMQX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMRX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMSX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMTX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMUX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMVX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMWX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMXX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMYX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMZX', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM0Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM1Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM2Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM3Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM4Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM5Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM6Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM7Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM8Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDM9Y', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMAY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMBY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMCY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMDY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMEY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMFY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMGY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMHY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMIY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMJY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMKY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMLY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMMY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMNY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMOY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMPY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMQY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMRY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMSY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMTY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMUY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMVY', 'FINST-7PF66CC1KGFXN0ZVATYL9DP5FBFP3ZMNH7IDMWY']
2025-07-25 10:32:38,220 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:38,220 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2399', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-A505-70C7-8500-E07FA50F01B1', 'x-acs-trace-id': '0cd8643bba7aafa66090f318a191a5aa', 'etag': '2YbR8VVYGmC0E8x1A7ZPYwA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:38,220 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMN', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMO', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMP', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMQ', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMR', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMS', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMT', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMU', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMV', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMW', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMX', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMY', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMZ', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM01', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM11', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM21', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM31', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM41', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM51', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM61', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM71', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM81', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM91', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMA1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMB1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMC1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMD1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDME1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMF1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMG1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMH1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMI1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMJ1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMK1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDML1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMM1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMN1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMO1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMP1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMQ1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMR1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMS1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMT1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMU1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMV1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMW1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMX1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMY1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMZ1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM02']}
2025-07-25 10:32:38,220 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-07-25 10:32:38,220 - INFO - 成功插入的数据ID: ['FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMN', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMO', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMP', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMQ', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMR', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMS', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMT', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMU', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMV', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMW', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMX', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMY', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMZ', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM01', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM11', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM21', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM31', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM41', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM51', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM61', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM71', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM81', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM91', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMA1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMB1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMC1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMD1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDME1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMF1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMG1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMH1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMI1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMJ1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMK1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDML1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMM1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMN1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMO1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMP1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMQ1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMR1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMS1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMT1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMU1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMV1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMW1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMX1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMY1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDMZ1', 'FINST-N3G66S81K6GXGXRL9GEEXDX8Q1ZW2UQRH7IDM02']
2025-07-25 10:32:43,485 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:43,485 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D6E13252-0025-7591-B4C0-45593B21E35E', 'x-acs-trace-id': '265cd26c6bc726398071f42050ad4c29', 'etag': '2jbtJYwzdHT+3zT8XLZWEJg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:43,485 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMVE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMWE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMXE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMYE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMZE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM0F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM1F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM2F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM3F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM4F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM5F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM6F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM7F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM8F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM9F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMAF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMBF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMCF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMDF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMEF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMFF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMGF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMHF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMIF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMJF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMKF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMLF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMMF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMNF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMOF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMPF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMQF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMRF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMSF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMTF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMUF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMVF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMWF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMXF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMYF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMZF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM0G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM1G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM2G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM3G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM4G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM5G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM6G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM7G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM8G']}
2025-07-25 10:32:43,485 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-07-25 10:32:43,485 - INFO - 成功插入的数据ID: ['FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMVE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMWE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMXE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMYE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMZE', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM0F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM1F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM2F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM3F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM4F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM5F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM6F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM7F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM8F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM9F', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMAF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMBF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMCF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMDF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMEF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMFF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMGF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMHF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMIF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMJF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMKF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMLF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMMF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMNF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMOF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMPF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMQF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMRF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMSF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMTF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMUF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMVF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMWF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMXF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMYF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMZF', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM0G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM1G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM2G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM3G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM4G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM5G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM6G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM7G', 'FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDM8G']
2025-07-25 10:32:48,720 - INFO - 批量插入响应状态码: 200
2025-07-25 10:32:48,720 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 02:32:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2172', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9A6C169E-7114-7B20-AFDB-2D77FDDC4EA7', 'x-acs-trace-id': '0c62fbfcd8335b6e85c2b642bfa64c8e', 'etag': '2y13sT2pydmrDkFVTtm6FHA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 10:32:48,720 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMHC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMIC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMKC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMLC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMMC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMNC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMOC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMPC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMQC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMRC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMSC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMTC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMUC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMVC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMWC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMXC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMYC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMZC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM0D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM1D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM2D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM3D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM4D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM5D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM6D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM7D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM8D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM9D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMAD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMBD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMCD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMDD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMED', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMFD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMGD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMHD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMID', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMKD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMLD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMMD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMND', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMOD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMPD']}
2025-07-25 10:32:48,720 - INFO - 批量插入表单数据成功，批次 8，共 45 条记录
2025-07-25 10:32:48,720 - INFO - 成功插入的数据ID: ['FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMHC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMIC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMKC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMLC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMMC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMNC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMOC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMPC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMQC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMRC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMSC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMTC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMUC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMVC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMWC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMXC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMYC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMZC', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM0D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM1D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM2D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM3D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM4D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM5D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM6D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM7D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM8D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDM9D', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMAD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMBD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMCD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMDD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMED', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMFD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMGD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMHD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMID', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMKD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMLD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMMD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMND', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMOD', 'FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMPD']
2025-07-25 10:32:53,735 - INFO - 批量插入完成，共 395 条记录
2025-07-25 10:32:53,735 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 395 条，错误: 0 条
2025-07-25 10:32:53,735 - INFO - 开始处理日期: 2025-07-25
2025-07-25 10:32:53,735 - INFO - Request Parameters - Page 1:
2025-07-25 10:32:53,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 10:32:53,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 10:32:54,298 - INFO - Response - Page 1:
2025-07-25 10:32:54,298 - INFO - 第 1 页获取到 2 条记录
2025-07-25 10:32:54,813 - INFO - 查询完成，共获取到 2 条记录
2025-07-25 10:32:54,813 - INFO - 获取到 2 条表单数据
2025-07-25 10:32:54,813 - INFO - 当前日期 2025-07-25 有 2 条MySQL数据需要处理
2025-07-25 10:32:54,813 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 10:32:54,813 - INFO - 数据同步完成！更新: 0 条，插入: 395 条，错误: 0 条
2025-07-25 10:32:54,813 - INFO - 同步完成
2025-07-25 13:30:33,804 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 13:30:33,804 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 13:30:33,804 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 13:30:33,976 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 199 条记录
2025-07-25 13:30:33,976 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 13:30:33,976 - INFO - 开始处理日期: 2025-07-24
2025-07-25 13:30:33,976 - INFO - Request Parameters - Page 1:
2025-07-25 13:30:33,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:30:33,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:30:42,101 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A5DBA874-B0F8-77FF-BD8D-ECBA29D67D33 Response: {'code': 'ServiceUnavailable', 'requestid': 'A5DBA874-B0F8-77FF-BD8D-ECBA29D67D33', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A5DBA874-B0F8-77FF-BD8D-ECBA29D67D33)
2025-07-25 13:30:42,101 - INFO - 开始处理日期: 2025-07-25
2025-07-25 13:30:42,101 - INFO - Request Parameters - Page 1:
2025-07-25 13:30:42,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:30:42,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:30:46,695 - INFO - Response - Page 1:
2025-07-25 13:30:46,695 - INFO - 第 1 页获取到 2 条记录
2025-07-25 13:30:47,211 - INFO - 查询完成，共获取到 2 条记录
2025-07-25 13:30:47,211 - INFO - 获取到 2 条表单数据
2025-07-25 13:30:47,211 - INFO - 当前日期 2025-07-25 有 2 条MySQL数据需要处理
2025-07-25 13:30:47,211 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 13:30:47,211 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-25 13:31:47,226 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 13:31:47,226 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 13:31:47,226 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 13:31:47,382 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 538 条记录
2025-07-25 13:31:47,398 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 13:31:47,398 - INFO - 开始处理日期: 2025-07-24
2025-07-25 13:31:47,398 - INFO - Request Parameters - Page 1:
2025-07-25 13:31:47,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:31:47,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:31:48,179 - INFO - Response - Page 1:
2025-07-25 13:31:48,179 - INFO - 第 1 页获取到 50 条记录
2025-07-25 13:31:48,695 - INFO - Request Parameters - Page 2:
2025-07-25 13:31:48,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:31:48,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:31:49,445 - INFO - Response - Page 2:
2025-07-25 13:31:49,445 - INFO - 第 2 页获取到 50 条记录
2025-07-25 13:31:49,960 - INFO - Request Parameters - Page 3:
2025-07-25 13:31:49,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:31:49,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:31:57,804 - INFO - Response - Page 3:
2025-07-25 13:31:57,804 - INFO - 第 3 页获取到 50 条记录
2025-07-25 13:31:58,304 - INFO - Request Parameters - Page 4:
2025-07-25 13:31:58,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:31:58,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:31:58,991 - INFO - Response - Page 4:
2025-07-25 13:31:58,991 - INFO - 第 4 页获取到 50 条记录
2025-07-25 13:31:59,507 - INFO - Request Parameters - Page 5:
2025-07-25 13:31:59,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:31:59,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:00,210 - INFO - Response - Page 5:
2025-07-25 13:32:00,210 - INFO - 第 5 页获取到 50 条记录
2025-07-25 13:32:00,726 - INFO - Request Parameters - Page 6:
2025-07-25 13:32:00,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:00,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:01,554 - INFO - Response - Page 6:
2025-07-25 13:32:01,554 - INFO - 第 6 页获取到 50 条记录
2025-07-25 13:32:02,054 - INFO - Request Parameters - Page 7:
2025-07-25 13:32:02,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:02,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:02,788 - INFO - Response - Page 7:
2025-07-25 13:32:02,788 - INFO - 第 7 页获取到 50 条记录
2025-07-25 13:32:03,304 - INFO - Request Parameters - Page 8:
2025-07-25 13:32:03,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:03,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:04,038 - INFO - Response - Page 8:
2025-07-25 13:32:04,038 - INFO - 第 8 页获取到 50 条记录
2025-07-25 13:32:04,538 - INFO - Request Parameters - Page 9:
2025-07-25 13:32:04,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:04,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:05,273 - INFO - Response - Page 9:
2025-07-25 13:32:05,273 - INFO - 第 9 页获取到 50 条记录
2025-07-25 13:32:05,788 - INFO - Request Parameters - Page 10:
2025-07-25 13:32:05,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:05,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:06,554 - INFO - Response - Page 10:
2025-07-25 13:32:06,554 - INFO - 第 10 页获取到 50 条记录
2025-07-25 13:32:07,069 - INFO - Request Parameters - Page 11:
2025-07-25 13:32:07,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:07,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:07,694 - INFO - Response - Page 11:
2025-07-25 13:32:07,694 - INFO - 第 11 页获取到 18 条记录
2025-07-25 13:32:08,210 - INFO - 查询完成，共获取到 518 条记录
2025-07-25 13:32:08,210 - INFO - 获取到 518 条表单数据
2025-07-25 13:32:08,210 - INFO - 当前日期 2025-07-24 有 526 条MySQL数据需要处理
2025-07-25 13:32:08,226 - INFO - 开始批量插入 8 条新记录
2025-07-25 13:32:08,398 - INFO - 批量插入响应状态码: 200
2025-07-25 13:32:08,398 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 05:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3EE5D6CD-A43B-76FB-B5F9-FB90AF41CB60', 'x-acs-trace-id': '7e0aaab7b4aceada3ac1524888e6c81c', 'etag': '3UQD1Pad/5KaGN8YWMDku2A6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 13:32:08,398 - INFO - 批量插入响应体: {'result': ['FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDMZK', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM0L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM1L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM2L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM3L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM4L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM5L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM6L']}
2025-07-25 13:32:08,398 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-07-25 13:32:08,398 - INFO - 成功插入的数据ID: ['FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDMZK', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM0L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM1L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM2L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM3L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM4L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM5L', 'FINST-2HF66O61UHEXCHK4ECKN34A82U302I4MWDIDM6L']
2025-07-25 13:32:13,413 - INFO - 批量插入完成，共 8 条记录
2025-07-25 13:32:13,413 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-07-25 13:32:13,413 - INFO - 开始处理日期: 2025-07-25
2025-07-25 13:32:13,413 - INFO - Request Parameters - Page 1:
2025-07-25 13:32:13,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 13:32:13,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 13:32:13,897 - INFO - Response - Page 1:
2025-07-25 13:32:13,897 - INFO - 第 1 页获取到 2 条记录
2025-07-25 13:32:14,398 - INFO - 查询完成，共获取到 2 条记录
2025-07-25 13:32:14,398 - INFO - 获取到 2 条表单数据
2025-07-25 13:32:14,398 - INFO - 当前日期 2025-07-25 有 2 条MySQL数据需要处理
2025-07-25 13:32:14,398 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 13:32:14,398 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 0 条
2025-07-25 13:32:14,398 - INFO - 同步完成
2025-07-25 16:30:34,170 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 16:30:34,170 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 16:30:34,170 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 16:30:34,326 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 205 条记录
2025-07-25 16:30:34,326 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 16:30:34,342 - INFO - 开始处理日期: 2025-07-24
2025-07-25 16:30:34,342 - INFO - Request Parameters - Page 1:
2025-07-25 16:30:34,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:30:34,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:30:42,451 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E4C62FF8-7302-7CDA-A619-7FDDEDDA607F Response: {'code': 'ServiceUnavailable', 'requestid': 'E4C62FF8-7302-7CDA-A619-7FDDEDDA607F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E4C62FF8-7302-7CDA-A619-7FDDEDDA607F)
2025-07-25 16:30:42,451 - INFO - 开始处理日期: 2025-07-25
2025-07-25 16:30:42,451 - INFO - Request Parameters - Page 1:
2025-07-25 16:30:42,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:30:42,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:30:50,576 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 989420A3-C385-7E40-AEF8-E18DBC60834E Response: {'code': 'ServiceUnavailable', 'requestid': '989420A3-C385-7E40-AEF8-E18DBC60834E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 989420A3-C385-7E40-AEF8-E18DBC60834E)
2025-07-25 16:30:50,576 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-25 16:31:50,591 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 16:31:50,591 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 16:31:50,591 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 16:31:50,747 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 566 条记录
2025-07-25 16:31:50,747 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 16:31:50,763 - INFO - 开始处理日期: 2025-07-24
2025-07-25 16:31:50,763 - INFO - Request Parameters - Page 1:
2025-07-25 16:31:50,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:31:50,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:31:58,513 - INFO - Response - Page 1:
2025-07-25 16:31:58,513 - INFO - 第 1 页获取到 50 条记录
2025-07-25 16:31:59,013 - INFO - Request Parameters - Page 2:
2025-07-25 16:31:59,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:31:59,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:31:59,794 - INFO - Response - Page 2:
2025-07-25 16:31:59,794 - INFO - 第 2 页获取到 50 条记录
2025-07-25 16:32:00,294 - INFO - Request Parameters - Page 3:
2025-07-25 16:32:00,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:00,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:01,029 - INFO - Response - Page 3:
2025-07-25 16:32:01,029 - INFO - 第 3 页获取到 50 条记录
2025-07-25 16:32:01,529 - INFO - Request Parameters - Page 4:
2025-07-25 16:32:01,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:01,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:02,341 - INFO - Response - Page 4:
2025-07-25 16:32:02,341 - INFO - 第 4 页获取到 50 条记录
2025-07-25 16:32:02,841 - INFO - Request Parameters - Page 5:
2025-07-25 16:32:02,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:02,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:03,544 - INFO - Response - Page 5:
2025-07-25 16:32:03,544 - INFO - 第 5 页获取到 50 条记录
2025-07-25 16:32:04,060 - INFO - Request Parameters - Page 6:
2025-07-25 16:32:04,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:04,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:04,810 - INFO - Response - Page 6:
2025-07-25 16:32:04,810 - INFO - 第 6 页获取到 50 条记录
2025-07-25 16:32:05,325 - INFO - Request Parameters - Page 7:
2025-07-25 16:32:05,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:05,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:06,028 - INFO - Response - Page 7:
2025-07-25 16:32:06,028 - INFO - 第 7 页获取到 50 条记录
2025-07-25 16:32:06,544 - INFO - Request Parameters - Page 8:
2025-07-25 16:32:06,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:06,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:07,357 - INFO - Response - Page 8:
2025-07-25 16:32:07,372 - INFO - 第 8 页获取到 50 条记录
2025-07-25 16:32:07,888 - INFO - Request Parameters - Page 9:
2025-07-25 16:32:07,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:07,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:08,653 - INFO - Response - Page 9:
2025-07-25 16:32:08,653 - INFO - 第 9 页获取到 50 条记录
2025-07-25 16:32:09,153 - INFO - Request Parameters - Page 10:
2025-07-25 16:32:09,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:09,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:09,810 - INFO - Response - Page 10:
2025-07-25 16:32:09,810 - INFO - 第 10 页获取到 50 条记录
2025-07-25 16:32:10,310 - INFO - Request Parameters - Page 11:
2025-07-25 16:32:10,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:10,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:11,013 - INFO - Response - Page 11:
2025-07-25 16:32:11,013 - INFO - 第 11 页获取到 26 条记录
2025-07-25 16:32:11,528 - INFO - 查询完成，共获取到 526 条记录
2025-07-25 16:32:11,528 - INFO - 获取到 526 条表单数据
2025-07-25 16:32:11,528 - INFO - 当前日期 2025-07-24 有 551 条MySQL数据需要处理
2025-07-25 16:32:11,544 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMMC
2025-07-25 16:32:12,200 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMMC
2025-07-25 16:32:12,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10097.56}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10097.56}, {'field': 'order_count', 'old_value': 0, 'new_value': 475}]
2025-07-25 16:32:12,200 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMYE
2025-07-25 16:32:12,685 - INFO - 更新表单数据成功: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMYE
2025-07-25 16:32:12,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1626.78}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1626.78}, {'field': 'order_count', 'old_value': 0, 'new_value': 198}]
2025-07-25 16:32:12,700 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMOC
2025-07-25 16:32:13,185 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMOC
2025-07-25 16:32:13,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 980.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 980.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-25 16:32:13,185 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMAD
2025-07-25 16:32:14,091 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMAD
2025-07-25 16:32:14,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 235259.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 235259.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 4439}]
2025-07-25 16:32:14,091 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMWC
2025-07-25 16:32:14,622 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMWC
2025-07-25 16:32:14,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 599.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 599.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-25 16:32:14,622 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMVF
2025-07-25 16:32:15,200 - INFO - 更新表单数据成功: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMVF
2025-07-25 16:32:15,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7000.0, 'new_value': 3500.0}, {'field': 'total_amount', 'old_value': 7000.0, 'new_value': 3500.0}]
2025-07-25 16:32:15,200 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMDF
2025-07-25 16:32:15,810 - INFO - 更新表单数据成功: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDMDF
2025-07-25 16:32:15,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 19500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 19500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-25 16:32:15,810 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMSC
2025-07-25 16:32:16,341 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMSC
2025-07-25 16:32:16,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3888.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3888.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/7b1782566f8d4331b80fcf5b9e8acc81.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=L8JYRxyEAbEUywc%2F1gCZBi2%2FPhg%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/bc1da713325948849dd4ecb488c14fd8.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=nvsovjOo%2F9goZ63Owqr9E6WV%2Boc%3D'}]
2025-07-25 16:32:16,341 - INFO - 开始批量插入 25 条新记录
2025-07-25 16:32:16,607 - INFO - 批量插入响应状态码: 200
2025-07-25 16:32:16,607 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 08:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1187', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E054F0BF-BCF6-7B40-B0A1-469DC72F1130', 'x-acs-trace-id': '94342734ec3f042f26855c3688922c13', 'etag': '1hXM97KE6kn3BIy78dnZGjA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 16:32:16,607 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMA', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMB', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMC', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMD', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDME', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMF', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMG', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMH', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMI', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMJ', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMK', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDML', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMM', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMN', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMO', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMP', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMQ', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMR', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMS', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMT', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMU', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMV', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMW', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMX', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMY']}
2025-07-25 16:32:16,607 - INFO - 批量插入表单数据成功，批次 1，共 25 条记录
2025-07-25 16:32:16,607 - INFO - 成功插入的数据ID: ['FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMA', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMB', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMC', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMD', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDME', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMF', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMG', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMH', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMI', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMJ', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMK', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDML', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMM', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMN', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMO', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMP', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMQ', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMR', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMS', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMT', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMU', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMV', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMW', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMX', 'FINST-07E66I91CGGXF6X4ARR9UBYAPTOD3KU9CKIDMY']
2025-07-25 16:32:21,622 - INFO - 批量插入完成，共 25 条记录
2025-07-25 16:32:21,622 - INFO - 日期 2025-07-24 处理完成 - 更新: 8 条，插入: 25 条，错误: 0 条
2025-07-25 16:32:21,622 - INFO - 开始处理日期: 2025-07-25
2025-07-25 16:32:21,622 - INFO - Request Parameters - Page 1:
2025-07-25 16:32:21,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 16:32:21,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 16:32:22,106 - INFO - Response - Page 1:
2025-07-25 16:32:22,106 - INFO - 第 1 页获取到 2 条记录
2025-07-25 16:32:22,606 - INFO - 查询完成，共获取到 2 条记录
2025-07-25 16:32:22,606 - INFO - 获取到 2 条表单数据
2025-07-25 16:32:22,606 - INFO - 当前日期 2025-07-25 有 2 条MySQL数据需要处理
2025-07-25 16:32:22,606 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 16:32:22,606 - INFO - 数据同步完成！更新: 8 条，插入: 25 条，错误: 0 条
2025-07-25 16:32:22,606 - INFO - 同步完成
2025-07-25 19:30:34,498 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 19:30:34,498 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 19:30:34,498 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 19:30:34,670 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 207 条记录
2025-07-25 19:30:34,670 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 19:30:34,670 - INFO - 开始处理日期: 2025-07-24
2025-07-25 19:30:34,670 - INFO - Request Parameters - Page 1:
2025-07-25 19:30:34,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:30:34,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:30:42,798 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4CA7A3B6-5072-713A-B91D-32CCD01230E1 Response: {'code': 'ServiceUnavailable', 'requestid': '4CA7A3B6-5072-713A-B91D-32CCD01230E1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4CA7A3B6-5072-713A-B91D-32CCD01230E1)
2025-07-25 19:30:42,798 - INFO - 开始处理日期: 2025-07-25
2025-07-25 19:30:42,798 - INFO - Request Parameters - Page 1:
2025-07-25 19:30:42,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:30:42,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:30:50,911 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 771A087A-3E74-7DC1-973D-648A75C6ABD5 Response: {'code': 'ServiceUnavailable', 'requestid': '771A087A-3E74-7DC1-973D-648A75C6ABD5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 771A087A-3E74-7DC1-973D-648A75C6ABD5)
2025-07-25 19:30:50,911 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-25 19:31:50,950 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 19:31:50,950 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 19:31:50,950 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 19:31:51,107 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 567 条记录
2025-07-25 19:31:51,107 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 19:31:51,122 - INFO - 开始处理日期: 2025-07-24
2025-07-25 19:31:51,122 - INFO - Request Parameters - Page 1:
2025-07-25 19:31:51,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:31:51,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:31:58,625 - INFO - Response - Page 1:
2025-07-25 19:31:58,625 - INFO - 第 1 页获取到 50 条记录
2025-07-25 19:31:59,141 - INFO - Request Parameters - Page 2:
2025-07-25 19:31:59,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:31:59,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:31:59,766 - INFO - Response - Page 2:
2025-07-25 19:31:59,766 - INFO - 第 2 页获取到 50 条记录
2025-07-25 19:32:00,282 - INFO - Request Parameters - Page 3:
2025-07-25 19:32:00,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:00,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:01,064 - INFO - Response - Page 3:
2025-07-25 19:32:01,064 - INFO - 第 3 页获取到 50 条记录
2025-07-25 19:32:01,564 - INFO - Request Parameters - Page 4:
2025-07-25 19:32:01,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:01,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:02,283 - INFO - Response - Page 4:
2025-07-25 19:32:02,283 - INFO - 第 4 页获取到 50 条记录
2025-07-25 19:32:02,799 - INFO - Request Parameters - Page 5:
2025-07-25 19:32:02,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:02,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:03,502 - INFO - Response - Page 5:
2025-07-25 19:32:03,502 - INFO - 第 5 页获取到 50 条记录
2025-07-25 19:32:04,002 - INFO - Request Parameters - Page 6:
2025-07-25 19:32:04,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:04,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:04,721 - INFO - Response - Page 6:
2025-07-25 19:32:04,721 - INFO - 第 6 页获取到 50 条记录
2025-07-25 19:32:05,237 - INFO - Request Parameters - Page 7:
2025-07-25 19:32:05,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:05,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:06,003 - INFO - Response - Page 7:
2025-07-25 19:32:06,003 - INFO - 第 7 页获取到 50 条记录
2025-07-25 19:32:06,519 - INFO - Request Parameters - Page 8:
2025-07-25 19:32:06,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:06,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:07,254 - INFO - Response - Page 8:
2025-07-25 19:32:07,254 - INFO - 第 8 页获取到 50 条记录
2025-07-25 19:32:07,769 - INFO - Request Parameters - Page 9:
2025-07-25 19:32:07,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:07,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:08,520 - INFO - Response - Page 9:
2025-07-25 19:32:08,520 - INFO - 第 9 页获取到 50 条记录
2025-07-25 19:32:09,020 - INFO - Request Parameters - Page 10:
2025-07-25 19:32:09,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:09,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:09,708 - INFO - Response - Page 10:
2025-07-25 19:32:09,708 - INFO - 第 10 页获取到 50 条记录
2025-07-25 19:32:10,208 - INFO - Request Parameters - Page 11:
2025-07-25 19:32:10,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:10,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:10,943 - INFO - Response - Page 11:
2025-07-25 19:32:10,943 - INFO - 第 11 页获取到 50 条记录
2025-07-25 19:32:11,443 - INFO - Request Parameters - Page 12:
2025-07-25 19:32:11,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:11,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:11,959 - INFO - Response - Page 12:
2025-07-25 19:32:11,959 - INFO - 第 12 页获取到 1 条记录
2025-07-25 19:32:12,459 - INFO - 查询完成，共获取到 551 条记录
2025-07-25 19:32:12,459 - INFO - 获取到 551 条表单数据
2025-07-25 19:32:12,459 - INFO - 当前日期 2025-07-24 有 551 条MySQL数据需要处理
2025-07-25 19:32:12,474 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMZC
2025-07-25 19:32:12,975 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMZC
2025-07-25 19:32:12,975 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 0, 'new_value': 682}]
2025-07-25 19:32:12,975 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM2F
2025-07-25 19:32:13,490 - INFO - 更新表单数据成功: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM21TVH7IDM2F
2025-07-25 19:32:13,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 56000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 56000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 96}]
2025-07-25 19:32:13,490 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJD
2025-07-25 19:32:14,116 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMJD
2025-07-25 19:32:14,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3220.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3220.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 307}]
2025-07-25 19:32:14,116 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMYC
2025-07-25 19:32:14,632 - INFO - 更新表单数据成功: FINST-3PF66X61UHEXUFDUDQMHH4R40PVM3OUZH7IDMYC
2025-07-25 19:32:14,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 17}]
2025-07-25 19:32:14,632 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMC1
2025-07-25 19:32:15,101 - INFO - 更新表单数据成功: FINST-737662B1FGFXIUCAAAUNXCNGBBJ13IE7H7IDMC1
2025-07-25 19:32:15,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10580.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10580.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-25 19:32:15,101 - INFO - 日期 2025-07-24 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-07-25 19:32:15,101 - INFO - 开始处理日期: 2025-07-25
2025-07-25 19:32:15,101 - INFO - Request Parameters - Page 1:
2025-07-25 19:32:15,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 19:32:15,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 19:32:15,616 - INFO - Response - Page 1:
2025-07-25 19:32:15,616 - INFO - 第 1 页获取到 2 条记录
2025-07-25 19:32:16,117 - INFO - 查询完成，共获取到 2 条记录
2025-07-25 19:32:16,117 - INFO - 获取到 2 条表单数据
2025-07-25 19:32:16,117 - INFO - 当前日期 2025-07-25 有 3 条MySQL数据需要处理
2025-07-25 19:32:16,117 - INFO - 开始批量插入 1 条新记录
2025-07-25 19:32:16,273 - INFO - 批量插入响应状态码: 200
2025-07-25 19:32:16,273 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 11:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '590D7246-4859-71BC-B2D2-AABFCB7324CF', 'x-acs-trace-id': 'cfe37b01970210df791b960d5f756fd1', 'etag': '6+2sBP9bmVPISJvFdD2FEHQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 19:32:16,273 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1TCFXS7QXAZF309R0F0QB35PQRQIDM0B1']}
2025-07-25 19:32:16,273 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-25 19:32:16,273 - INFO - 成功插入的数据ID: ['FINST-LR5668B1TCFXS7QXAZF309R0F0QB35PQRQIDM0B1']
2025-07-25 19:32:21,290 - INFO - 批量插入完成，共 1 条记录
2025-07-25 19:32:21,290 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-25 19:32:21,290 - INFO - 数据同步完成！更新: 5 条，插入: 1 条，错误: 0 条
2025-07-25 19:32:21,290 - INFO - 同步完成
2025-07-25 22:30:34,199 - INFO - 使用默认增量同步（当天更新数据）
2025-07-25 22:30:34,199 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-25 22:30:34,199 - INFO - 查询参数: ('2025-07-25',)
2025-07-25 22:30:34,355 - INFO - MySQL查询成功，增量数据（日期: 2025-07-25），共获取 294 条记录
2025-07-25 22:30:34,355 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 22:30:34,371 - INFO - 开始处理日期: 2025-07-24
2025-07-25 22:30:34,371 - INFO - Request Parameters - Page 1:
2025-07-25 22:30:34,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:30:34,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:30:42,515 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C4CBD6E4-8BA4-704C-9FE4-757A6666F020 Response: {'code': 'ServiceUnavailable', 'requestid': 'C4CBD6E4-8BA4-704C-9FE4-757A6666F020', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C4CBD6E4-8BA4-704C-9FE4-757A6666F020)
2025-07-25 22:30:42,530 - INFO - 开始处理日期: 2025-07-25
2025-07-25 22:30:42,530 - INFO - Request Parameters - Page 1:
2025-07-25 22:30:42,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:30:42,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:30:50,659 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 593962F9-05BD-7CC4-B4FE-20D98BE7B129 Response: {'code': 'ServiceUnavailable', 'requestid': '593962F9-05BD-7CC4-B4FE-20D98BE7B129', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 593962F9-05BD-7CC4-B4FE-20D98BE7B129)
2025-07-25 22:30:50,659 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-25 22:31:50,698 - INFO - 开始同步昨天与今天的销售数据: 2025-07-24 至 2025-07-25
2025-07-25 22:31:50,698 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-25 22:31:50,698 - INFO - 查询参数: ('2025-07-24', '2025-07-25')
2025-07-25 22:31:50,870 - INFO - MySQL查询成功，时间段: 2025-07-24 至 2025-07-25，共获取 654 条记录
2025-07-25 22:31:50,870 - INFO - 获取到 2 个日期需要处理: ['2025-07-24', '2025-07-25']
2025-07-25 22:31:50,870 - INFO - 开始处理日期: 2025-07-24
2025-07-25 22:31:50,870 - INFO - Request Parameters - Page 1:
2025-07-25 22:31:50,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:50,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:51,573 - INFO - Response - Page 1:
2025-07-25 22:31:51,573 - INFO - 第 1 页获取到 50 条记录
2025-07-25 22:31:52,089 - INFO - Request Parameters - Page 2:
2025-07-25 22:31:52,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:52,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:52,793 - INFO - Response - Page 2:
2025-07-25 22:31:52,793 - INFO - 第 2 页获取到 50 条记录
2025-07-25 22:31:53,308 - INFO - Request Parameters - Page 3:
2025-07-25 22:31:53,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:53,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:54,121 - INFO - Response - Page 3:
2025-07-25 22:31:54,121 - INFO - 第 3 页获取到 50 条记录
2025-07-25 22:31:54,621 - INFO - Request Parameters - Page 4:
2025-07-25 22:31:54,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:54,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:55,387 - INFO - Response - Page 4:
2025-07-25 22:31:55,387 - INFO - 第 4 页获取到 50 条记录
2025-07-25 22:31:55,888 - INFO - Request Parameters - Page 5:
2025-07-25 22:31:55,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:55,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:56,575 - INFO - Response - Page 5:
2025-07-25 22:31:56,575 - INFO - 第 5 页获取到 50 条记录
2025-07-25 22:31:57,076 - INFO - Request Parameters - Page 6:
2025-07-25 22:31:57,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:57,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:57,763 - INFO - Response - Page 6:
2025-07-25 22:31:57,763 - INFO - 第 6 页获取到 50 条记录
2025-07-25 22:31:58,279 - INFO - Request Parameters - Page 7:
2025-07-25 22:31:58,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:58,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:31:58,983 - INFO - Response - Page 7:
2025-07-25 22:31:58,983 - INFO - 第 7 页获取到 50 条记录
2025-07-25 22:31:59,498 - INFO - Request Parameters - Page 8:
2025-07-25 22:31:59,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:31:59,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:32:00,217 - INFO - Response - Page 8:
2025-07-25 22:32:00,217 - INFO - 第 8 页获取到 50 条记录
2025-07-25 22:32:00,718 - INFO - Request Parameters - Page 9:
2025-07-25 22:32:00,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:32:00,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:32:01,405 - INFO - Response - Page 9:
2025-07-25 22:32:01,405 - INFO - 第 9 页获取到 50 条记录
2025-07-25 22:32:01,921 - INFO - Request Parameters - Page 10:
2025-07-25 22:32:01,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:32:01,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:32:02,656 - INFO - Response - Page 10:
2025-07-25 22:32:02,656 - INFO - 第 10 页获取到 50 条记录
2025-07-25 22:32:03,156 - INFO - Request Parameters - Page 11:
2025-07-25 22:32:03,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:32:03,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:32:03,922 - INFO - Response - Page 11:
2025-07-25 22:32:03,922 - INFO - 第 11 页获取到 50 条记录
2025-07-25 22:32:04,438 - INFO - Request Parameters - Page 12:
2025-07-25 22:32:04,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:32:04,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:32:04,938 - INFO - Response - Page 12:
2025-07-25 22:32:04,938 - INFO - 第 12 页获取到 1 条记录
2025-07-25 22:32:05,454 - INFO - 查询完成，共获取到 551 条记录
2025-07-25 22:32:05,454 - INFO - 获取到 551 条表单数据
2025-07-25 22:32:05,454 - INFO - 当前日期 2025-07-24 有 551 条MySQL数据需要处理
2025-07-25 22:32:05,470 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-25 22:32:05,470 - INFO - 开始处理日期: 2025-07-25
2025-07-25 22:32:05,470 - INFO - Request Parameters - Page 1:
2025-07-25 22:32:05,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-25 22:32:05,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-25 22:32:05,985 - INFO - Response - Page 1:
2025-07-25 22:32:05,985 - INFO - 第 1 页获取到 3 条记录
2025-07-25 22:32:06,486 - INFO - 查询完成，共获取到 3 条记录
2025-07-25 22:32:06,486 - INFO - 获取到 3 条表单数据
2025-07-25 22:32:06,486 - INFO - 当前日期 2025-07-25 有 87 条MySQL数据需要处理
2025-07-25 22:32:06,486 - INFO - 开始批量插入 84 条新记录
2025-07-25 22:32:06,736 - INFO - 批量插入响应状态码: 200
2025-07-25 22:32:06,736 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 14:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '56DE986E-EF83-72D6-9587-0E181643401D', 'x-acs-trace-id': 'ba00dcb56bb0427bb348f90ed1070c11', 'etag': '2j4e0pvwPq7a4dQDLaudzfA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 22:32:06,736 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMAD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMBD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMCD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMDD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMED', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMFD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMGD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMHD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMID', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMJD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMKD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMLD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMMD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMND', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMOD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMPD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMQD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMRD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMSD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMTD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMUD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMVD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMWD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMXD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMYD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMZD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM0E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM1E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM2E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM3E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM4E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM5E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM6E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM7E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM8E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM9E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMAE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMBE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMCE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMDE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMEE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMFE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMGE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMHE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMIE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMJE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMKE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMLE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMME', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3IDX6XIDMNE']}
2025-07-25 22:32:06,736 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-25 22:32:06,736 - INFO - 成功插入的数据ID: ['FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMAD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMBD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMCD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMDD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMED', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMFD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMGD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMHD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMID', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMJD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMKD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMLD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMMD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMND', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMOD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMPD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMQD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMRD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMSD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMTD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMUD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMVD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMWD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMXD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMYD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMZD', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM0E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM1E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM2E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM3E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM4E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM5E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM6E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM7E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM8E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDM9E', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMAE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMBE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMCE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMDE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMEE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMFE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMGE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMHE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMIE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMJE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMKE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMLE', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3HDX6XIDMME', 'FINST-QZE668D1WGFX20TK9JWNF7CD6WFT3IDX6XIDMNE']
2025-07-25 22:32:11,988 - INFO - 批量插入响应状态码: 200
2025-07-25 22:32:11,988 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 14:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1644', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B74E6D2A-A875-7669-8E81-C61F6862AC7E', 'x-acs-trace-id': '0d09774219c06caf10db07bf36920a7c', 'etag': '19waOzVkPC4M0g2hV3RywFw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-25 22:32:11,988 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMII', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMJI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMKI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMLI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMMI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMNI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMOI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMPI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMQI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMRI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMSI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMTI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMUI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMVI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMWI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMXI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMYI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMZI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM0J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM1J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM2J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM3J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM4J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM5J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM6J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM7J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM8J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM9J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMAJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMBJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMCJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMDJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMEJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMFJ']}
2025-07-25 22:32:11,988 - INFO - 批量插入表单数据成功，批次 2，共 34 条记录
2025-07-25 22:32:11,988 - INFO - 成功插入的数据ID: ['FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMII', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMJI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMKI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMLI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMMI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMNI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMOI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMPI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMQI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMRI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMSI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMTI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMUI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMVI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMWI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMXI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMYI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMZI', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM0J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM1J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM2J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM3J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM4J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM5J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM6J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM7J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM8J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDM9J', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMAJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMBJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMCJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMDJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMEJ', 'FINST-5XA66LC1XHEX74I2ETH4ADIBPEDU27F17XIDMFJ']
2025-07-25 22:32:17,005 - INFO - 批量插入完成，共 84 条记录
2025-07-25 22:32:17,005 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 84 条，错误: 0 条
2025-07-25 22:32:17,005 - INFO - 数据同步完成！更新: 0 条，插入: 84 条，错误: 0 条
2025-07-25 22:32:17,005 - INFO - 同步完成
