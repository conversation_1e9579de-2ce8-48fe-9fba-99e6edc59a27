2025-05-18 08:00:03,607 - INFO - ==================================================
2025-05-18 08:00:03,608 - INFO - 程序启动 - 版本 v1.0.0
2025-05-18 08:00:03,608 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250518.log
2025-05-18 08:00:03,608 - INFO - ==================================================
2025-05-18 08:00:03,609 - INFO - 程序入口点: __main__
2025-05-18 08:00:03,609 - INFO - ==================================================
2025-05-18 08:00:03,609 - INFO - 程序启动 - 版本 v1.0.1
2025-05-18 08:00:03,609 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250518.log
2025-05-18 08:00:03,609 - INFO - ==================================================
2025-05-18 08:00:03,911 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-18 08:00:03,912 - INFO - sales_data表已存在，无需创建
2025-05-18 08:00:03,913 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-18 08:00:03,913 - INFO - DataSyncManager初始化完成
2025-05-18 08:00:03,913 - INFO - 未提供日期参数，使用默认值
2025-05-18 08:00:03,914 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-18 08:00:03,914 - INFO - 开始综合数据同步流程...
2025-05-18 08:00:03,914 - INFO - 正在获取数衍平台日销售数据...
2025-05-18 08:00:03,914 - INFO - 查询数衍平台数据，时间段为: 2025-03-18, 2025-05-17
2025-05-18 08:00:03,915 - INFO - 正在获取********至********的数据
2025-05-18 08:00:03,915 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:03,915 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E8D6D36345206C5F1789142F93005003'}
2025-05-18 08:00:08,484 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:08,498 - INFO - 过滤后保留 1560 条记录
2025-05-18 08:00:10,499 - INFO - 正在获取********至********的数据
2025-05-18 08:00:10,499 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:10,500 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CF1F3EF5E6E50A48C491F5806AFADEA3'}
2025-05-18 08:00:13,497 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:13,510 - INFO - 过滤后保留 1556 条记录
2025-05-18 08:00:15,511 - INFO - 正在获取********至********的数据
2025-05-18 08:00:15,511 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:15,512 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0223427C57E62A0A0D4DF28BF7D7500F'}
2025-05-18 08:00:18,642 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:18,656 - INFO - 过滤后保留 1508 条记录
2025-05-18 08:00:20,657 - INFO - 正在获取********至********的数据
2025-05-18 08:00:20,657 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:20,658 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '118310870944E0CC3148920002AABC88'}
2025-05-18 08:00:23,196 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:23,208 - INFO - 过滤后保留 1503 条记录
2025-05-18 08:00:25,208 - INFO - 正在获取********至********的数据
2025-05-18 08:00:25,209 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:25,209 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6969AA54D685FFC8274636BB016FE284'}
2025-05-18 08:00:27,687 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:27,700 - INFO - 过滤后保留 1504 条记录
2025-05-18 08:00:29,701 - INFO - 正在获取********至********的数据
2025-05-18 08:00:29,701 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:29,701 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9F426E2B782EA23AF919DE79C99E5386'}
2025-05-18 08:00:32,219 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:32,232 - INFO - 过滤后保留 1481 条记录
2025-05-18 08:00:34,233 - INFO - 正在获取********至********的数据
2025-05-18 08:00:34,233 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:34,234 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EF340EC1911D45960F0766AB7E16BF6C'}
2025-05-18 08:00:36,399 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:36,412 - INFO - 过滤后保留 1488 条记录
2025-05-18 08:00:38,413 - INFO - 正在获取********至********的数据
2025-05-18 08:00:38,413 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:38,414 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7C4D6E4933659C6E4D44F8683E822C31'}
2025-05-18 08:00:40,439 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:40,452 - INFO - 过滤后保留 1477 条记录
2025-05-18 08:00:42,454 - INFO - 正在获取********至********的数据
2025-05-18 08:00:42,454 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-18 08:00:42,455 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '71432C08BA840ADEFEEF220593F3C099'}
2025-05-18 08:00:43,955 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-18 08:00:43,965 - INFO - 过滤后保留 1047 条记录
2025-05-18 08:00:45,965 - INFO - 开始保存数据到SQLite数据库，共 13124 条记录待处理
2025-05-18 08:00:46,734 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=8711865ACD3B4C2AADD3843CA2A204D9, sale_time=2025-05-09
2025-05-18 08:00:46,735 - INFO - 变更字段: recommend_amount: 3088.0 -> 4776.0, amount: 3088 -> 4776, count: 1 -> 2, instore_amount: 3088.0 -> 4776.0, instore_count: 1 -> 2
2025-05-18 08:00:46,758 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-10
2025-05-18 08:00:46,758 - INFO - 变更字段: amount: 9617 -> 9872, count: 322 -> 323, instore_amount: 8922.66 -> 9177.98, instore_count: 306 -> 307
2025-05-18 08:00:46,799 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-16
2025-05-18 08:00:46,799 - INFO - 变更字段: recommend_amount: 0.0 -> 8527.8, daily_bill_amount: 0.0 -> 8527.8
2025-05-18 08:00:46,805 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9UCRQKEOIF52ASKKUBQUNH0018FA, sale_time=2025-05-16
2025-05-18 08:00:46,805 - INFO - 变更字段: amount: 10048 -> 10314, count: 35 -> 36, instore_amount: 10300.9 -> 10566.9, instore_count: 35 -> 36
2025-05-18 08:00:46,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-05-16
2025-05-18 08:00:46,806 - INFO - 变更字段: amount: 2160 -> 2211, count: 60 -> 61, instore_amount: 1760.52 -> 1811.22, instore_count: 47 -> 48
2025-05-18 08:00:46,807 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE29HIJ7QK7Q2OV4FVC7F40014BL, sale_time=2025-05-16
2025-05-18 08:00:46,807 - INFO - 变更字段: amount: 1548 -> 1896, count: 5 -> 6, instore_amount: 1548.8 -> 1896.5, instore_count: 5 -> 6
2025-05-18 08:00:46,809 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDV0D9P6J27Q2OV4FVC7DG0014A1, sale_time=2025-05-16
2025-05-18 08:00:46,810 - INFO - 变更字段: recommend_amount: 2310.4 -> 2289.4, daily_bill_amount: 2310.4 -> 2289.4
2025-05-18 08:00:46,810 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-05-16
2025-05-18 08:00:46,811 - INFO - 变更字段: amount: 3611 -> 3616, count: 200 -> 201, instore_amount: 2055.04 -> 2059.46, instore_count: 114 -> 115
2025-05-18 08:00:46,812 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-05-16
2025-05-18 08:00:46,812 - INFO - 变更字段: amount: 4431 -> 4480, count: 180 -> 183, instore_amount: 4575.49 -> 4624.49, instore_count: 180 -> 183
2025-05-18 08:00:46,814 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-05-16
2025-05-18 08:00:46,814 - INFO - 变更字段: amount: 922 -> 903
2025-05-18 08:00:46,816 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-05-16
2025-05-18 08:00:46,817 - INFO - 变更字段: recommend_amount: 12494.1 -> 12434.1, amount: 12494 -> 12434
2025-05-18 08:00:46,820 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-16
2025-05-18 08:00:46,820 - INFO - 变更字段: recommend_amount: 0.0 -> 11915.9, daily_bill_amount: 0.0 -> 11915.9
2025-05-18 08:00:46,821 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-05-16
2025-05-18 08:00:46,821 - INFO - 变更字段: recommend_amount: 16295.0 -> 22961.0, amount: 16295 -> 22961, count: 36 -> 64, instore_amount: 16295.0 -> 22961.0, instore_count: 36 -> 64
2025-05-18 08:00:46,821 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-05-15
2025-05-18 08:00:46,821 - INFO - 变更字段: recommend_amount: 11163.0 -> 11291.0, amount: 11163 -> 11291, count: 42 -> 43, instore_amount: 11163.0 -> 11291.0, instore_count: 42 -> 43
2025-05-18 08:00:46,821 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-15
2025-05-18 08:00:46,821 - INFO - 变更字段: amount: 4590 -> 5310, count: 8 -> 9, instore_amount: 4590.0 -> 5310.0, instore_count: 8 -> 9
2025-05-18 08:00:46,823 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=7987833E6DE549FCBAC0AAF7A1D27E61, sale_time=2025-05-16
2025-05-18 08:00:46,824 - INFO - 变更字段: recommend_amount: 0.0 -> 2798.6, daily_bill_amount: 0.0 -> 2798.6, amount: 3102 -> 3328, count: 18 -> 19, instore_amount: 3102.8 -> 3328.6, instore_count: 18 -> 19
2025-05-18 08:00:46,826 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-16
2025-05-18 08:00:46,826 - INFO - 变更字段: recommend_amount: 2893.74 -> 2933.04, amount: 2893 -> 2933, count: 215 -> 223, instore_amount: 961.44 -> 1000.52, instore_count: 109 -> 114, online_amount: 1940.9 -> 1941.12, online_count: 106 -> 109
2025-05-18 08:00:46,829 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-16
2025-05-18 08:00:46,830 - INFO - 变更字段: recommend_amount: 100.0 -> 1468.9, daily_bill_amount: 100.0 -> 1468.9
2025-05-18 08:00:46,830 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-13
2025-05-18 08:00:46,830 - INFO - 变更字段: amount: 1075 -> 1078, instore_amount: 1075.9 -> 1078.9
2025-05-18 08:00:46,831 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-15
2025-05-18 08:00:46,831 - INFO - 变更字段: recommend_amount: 7643.3 -> 7817.8, amount: 7643 -> 7817, count: 123 -> 125, instore_amount: 7473.01 -> 7647.51, instore_count: 121 -> 123
2025-05-18 08:00:46,831 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-14
2025-05-18 08:00:46,831 - INFO - 变更字段: recommend_amount: 6711.09 -> 6821.79, amount: 6711 -> 6821, count: 135 -> 136, instore_amount: 6564.19 -> 6674.89, instore_count: 131 -> 132
2025-05-18 08:00:46,832 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-16
2025-05-18 08:00:46,832 - INFO - 变更字段: amount: 8709 -> 8765, count: 428 -> 435, instore_amount: 4761.65 -> 4817.5, instore_count: 286 -> 293
2025-05-18 08:00:46,834 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-16
2025-05-18 08:00:46,834 - INFO - 变更字段: recommend_amount: 1233.72 -> 1224.77, amount: 1233 -> 1224, online_amount: 1085.77 -> 1076.82
2025-05-18 08:00:46,835 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-16
2025-05-18 08:00:46,835 - INFO - 变更字段: recommend_amount: 6430.88 -> 6516.28, amount: 6430 -> 6516, count: 291 -> 293, instore_amount: 2167.36 -> 2176.36, instore_count: 100 -> 101, online_amount: 4506.12 -> 4582.52, online_count: 191 -> 192
2025-05-18 08:00:46,837 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-16
2025-05-18 08:00:46,837 - INFO - 变更字段: recommend_amount: 0.0 -> 32559.58, daily_bill_amount: 0.0 -> 32559.58
2025-05-18 08:00:46,839 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-16
2025-05-18 08:00:46,839 - INFO - 变更字段: amount: 12323 -> 12289
2025-05-18 08:00:46,840 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-16
2025-05-18 08:00:46,840 - INFO - 变更字段: amount: 27540 -> 28796, count: 231 -> 235, instore_amount: 12010.9 -> 13266.9, instore_count: 80 -> 84
2025-05-18 08:00:46,841 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-16
2025-05-18 08:00:46,841 - INFO - 变更字段: recommend_amount: 31146.72 -> 36093.0, daily_bill_amount: 31146.72 -> 36093.0
2025-05-18 08:00:46,842 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-05-16
2025-05-18 08:00:46,842 - INFO - 变更字段: amount: 7707 -> 7725, count: 92 -> 94, online_amount: 2722.4 -> 2740.7, online_count: 67 -> 69
2025-05-18 08:00:46,843 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-16
2025-05-18 08:00:46,843 - INFO - 变更字段: amount: 4998 -> 5005, count: 352 -> 353, instore_amount: 2804.23 -> 2811.23, instore_count: 255 -> 256
2025-05-18 08:00:46,844 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-15
2025-05-18 08:00:46,844 - INFO - 变更字段: amount: 4654 -> 4639, instore_amount: 1921.79 -> 1907.29
2025-05-18 08:00:46,845 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-16
2025-05-18 08:00:46,845 - INFO - 变更字段: amount: 21239 -> 21256, count: 233 -> 234, instore_amount: 15443.02 -> 15460.82, instore_count: 82 -> 83
2025-05-18 08:00:46,845 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-16
2025-05-18 08:00:46,846 - INFO - 变更字段: recommend_amount: 0.0 -> 4440.61, daily_bill_amount: 0.0 -> 4440.61, amount: 426 -> 938, count: 48 -> 84, instore_amount: 465.5 -> 991.8, instore_count: 48 -> 84
2025-05-18 08:00:46,848 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-16
2025-05-18 08:00:46,849 - INFO - 变更字段: amount: 784 -> 827, count: 10 -> 11, instore_amount: 662.8 -> 706.0, instore_count: 7 -> 8
2025-05-18 08:00:46,851 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEQ2M9E710I86N3H2U1H1001EQ7, sale_time=2025-05-16
2025-05-18 08:00:46,851 - INFO - 变更字段: amount: 89 -> 345, count: 1 -> 4, instore_amount: 89.8 -> 327.8, instore_count: 1 -> 3, online_amount: 0.0 -> 17.4, online_count: 0 -> 1
2025-05-18 08:00:46,852 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-16
2025-05-18 08:00:46,852 - INFO - 变更字段: amount: 13019 -> 16683, count: 17 -> 19, instore_amount: 13859.1 -> 17522.4, instore_count: 17 -> 19
2025-05-18 08:00:46,854 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-05-16
2025-05-18 08:00:46,854 - INFO - 变更字段: recommend_amount: 0.0 -> 6155.5, daily_bill_amount: 0.0 -> 6155.5
2025-05-18 08:00:46,856 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVBEGSM760I86N3H2U12H001EBN, sale_time=2025-05-16
2025-05-18 08:00:46,856 - INFO - 变更字段: recommend_amount: 0.0 -> 1980.0, amount: 0 -> 1980, count: 3 -> 4, instore_amount: 0.0 -> 1980.0, instore_count: 3 -> 4
2025-05-18 08:00:46,857 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-05-16
2025-05-18 08:00:46,857 - INFO - 变更字段: amount: 3803 -> 3977, count: 154 -> 155, instore_amount: 2070.55 -> 2244.55, instore_count: 112 -> 113
2025-05-18 08:00:46,858 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-05-16
2025-05-18 08:00:46,859 - INFO - 变更字段: recommend_amount: 0.0 -> 10630.3, daily_bill_amount: 0.0 -> 10630.3
2025-05-18 08:00:46,860 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-05-16
2025-05-18 08:00:46,860 - INFO - 变更字段: recommend_amount: 5041.24 -> 6792.61, daily_bill_amount: 5041.24 -> 6792.61
2025-05-18 08:00:46,861 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDRR6FJ7A60I86N3H2U10L001E9R, sale_time=2025-05-16
2025-05-18 08:00:46,861 - INFO - 变更字段: recommend_amount: 3381.12 -> 3386.88, amount: 3381 -> 3386, count: 163 -> 164, instore_amount: 1193.27 -> 1199.03, instore_count: 76 -> 77
2025-05-18 08:00:46,862 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-16
2025-05-18 08:00:46,862 - INFO - 变更字段: amount: 7414 -> 7427, count: 670 -> 676, instore_amount: 7238.77 -> 7392.0, instore_count: 631 -> 663, online_amount: 302.49 -> 161.89, online_count: 39 -> 13
2025-05-18 08:00:46,863 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-15
2025-05-18 08:00:46,863 - INFO - 变更字段: instore_amount: 5272.35 -> 5313.15, instore_count: 305 -> 306, online_amount: 1548.43 -> 1507.63, online_count: 98 -> 97
2025-05-18 08:00:46,864 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-05-16
2025-05-18 08:00:46,864 - INFO - 变更字段: amount: 4541 -> 4580, count: 85 -> 86, online_amount: 693.58 -> 732.48, online_count: 14 -> 15
2025-05-18 08:00:46,865 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDMFCJQF4F0I86N3H2U1UC001E7I, sale_time=2025-05-15
2025-05-18 08:00:46,865 - INFO - 变更字段: recommend_amount: 2633.48 -> 3791.18, amount: 2633 -> 3791, instore_amount: 1072.19 -> 2229.89
2025-05-18 08:00:46,866 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-16
2025-05-18 08:00:46,866 - INFO - 变更字段: recommend_amount: 15454.1 -> 16842.75, amount: 15454 -> 16842, count: 729 -> 780, online_amount: 15889.59 -> 17278.24, online_count: 729 -> 780
2025-05-18 08:00:46,870 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-16
2025-05-18 08:00:46,870 - INFO - 变更字段: recommend_amount: 3239.23 -> 3240.22, amount: 3239 -> 3240, count: 184 -> 185, instore_amount: 1643.47 -> 1655.46, instore_count: 95 -> 96
2025-05-18 08:00:46,872 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-16
2025-05-18 08:00:46,872 - INFO - 变更字段: amount: 17026 -> 18545, count: 243 -> 251, instore_amount: 15767.3 -> 17286.96, instore_count: 180 -> 188
2025-05-18 08:00:46,878 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRBVE2CQT7AV8LHQQGID9001EJI, sale_time=2025-05-16
2025-05-18 08:00:46,879 - INFO - 变更字段: amount: 17524 -> 19126, count: 120 -> 128, instore_amount: 9574.0 -> 11175.5, instore_count: 84 -> 92
2025-05-18 08:00:46,881 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQ716B84L7AV8LHQQGID0001EJ9, sale_time=2025-05-16
2025-05-18 08:00:46,881 - INFO - 变更字段: recommend_amount: 4595.69 -> 4480.59, amount: 4595 -> 4480
2025-05-18 08:00:46,882 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-05-16
2025-05-18 08:00:46,882 - INFO - 变更字段: recommend_amount: 10577.44 -> 10593.92, daily_bill_amount: 10577.44 -> 10593.92, amount: 6747 -> 6730
2025-05-18 08:00:47,106 - INFO - SQLite数据保存完成，统计信息：
2025-05-18 08:00:47,106 - INFO - - 总记录数: 13124
2025-05-18 08:00:47,107 - INFO - - 成功插入: 214
2025-05-18 08:00:47,107 - INFO - - 成功更新: 52
2025-05-18 08:00:47,107 - INFO - - 无需更新: 12858
2025-05-18 08:00:47,107 - INFO - - 处理失败: 0
2025-05-18 08:00:52,594 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250518.xlsx
2025-05-18 08:00:52,603 - INFO - 成功获取数衍平台数据，共 13124 条记录
2025-05-18 08:00:52,603 - INFO - 正在更新SQLite月度汇总数据...
2025-05-18 08:00:52,611 - INFO - 月度数据sqllite清空完成
2025-05-18 08:00:52,874 - INFO - 月度汇总数据更新完成，处理了 1189 条汇总记录
2025-05-18 08:00:52,875 - INFO - 成功更新月度汇总数据，共 1189 条记录
2025-05-18 08:00:52,875 - INFO - 正在获取宜搭日销售表单数据...
2025-05-18 08:00:52,876 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-18 00:00:00 至 2025-05-17 23:59:59
2025-05-18 08:00:52,876 - INFO - 查询分段 1: 2025-03-18 至 2025-03-24
2025-05-18 08:00:52,876 - INFO - 查询日期范围: 2025-03-18 至 2025-03-24，使用分页查询，每页 100 条记录
2025-05-18 08:00:52,876 - INFO - Request Parameters - Page 1:
2025-05-18 08:00:52,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:00:52,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:00:57,235 - INFO - API请求耗时: 4357ms
2025-05-18 08:00:57,235 - INFO - Response - Page 1
2025-05-18 08:00:57,236 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:00:57,737 - INFO - Request Parameters - Page 2:
2025-05-18 08:00:57,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:00:57,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:00,519 - INFO - API请求耗时: 2780ms
2025-05-18 08:01:00,519 - INFO - Response - Page 2
2025-05-18 08:01:00,520 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:01:01,020 - INFO - Request Parameters - Page 3:
2025-05-18 08:01:01,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:01,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:01,875 - INFO - API请求耗时: 855ms
2025-05-18 08:01:01,876 - INFO - Response - Page 3
2025-05-18 08:01:01,877 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:01:02,378 - INFO - Request Parameters - Page 4:
2025-05-18 08:01:02,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:02,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:03,078 - INFO - API请求耗时: 699ms
2025-05-18 08:01:03,078 - INFO - Response - Page 4
2025-05-18 08:01:03,079 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:01:03,580 - INFO - Request Parameters - Page 5:
2025-05-18 08:01:03,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:03,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:04,297 - INFO - API请求耗时: 716ms
2025-05-18 08:01:04,297 - INFO - Response - Page 5
2025-05-18 08:01:04,298 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:01:04,799 - INFO - Request Parameters - Page 6:
2025-05-18 08:01:04,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:04,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:05,482 - INFO - API请求耗时: 682ms
2025-05-18 08:01:05,482 - INFO - Response - Page 6
2025-05-18 08:01:05,482 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:01:05,983 - INFO - Request Parameters - Page 7:
2025-05-18 08:01:05,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:05,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:06,658 - INFO - API请求耗时: 674ms
2025-05-18 08:01:06,658 - INFO - Response - Page 7
2025-05-18 08:01:06,659 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:01:07,159 - INFO - Request Parameters - Page 8:
2025-05-18 08:01:07,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:07,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:07,888 - INFO - API请求耗时: 727ms
2025-05-18 08:01:07,888 - INFO - Response - Page 8
2025-05-18 08:01:07,888 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:01:08,390 - INFO - Request Parameters - Page 9:
2025-05-18 08:01:08,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:08,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:09,053 - INFO - API请求耗时: 662ms
2025-05-18 08:01:09,054 - INFO - Response - Page 9
2025-05-18 08:01:09,054 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:01:09,555 - INFO - Request Parameters - Page 10:
2025-05-18 08:01:09,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:09,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:10,324 - INFO - API请求耗时: 769ms
2025-05-18 08:01:10,325 - INFO - Response - Page 10
2025-05-18 08:01:10,325 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:01:10,826 - INFO - Request Parameters - Page 11:
2025-05-18 08:01:10,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:10,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:11,514 - INFO - API请求耗时: 687ms
2025-05-18 08:01:11,515 - INFO - Response - Page 11
2025-05-18 08:01:11,515 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:01:12,016 - INFO - Request Parameters - Page 12:
2025-05-18 08:01:12,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:12,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:12,695 - INFO - API请求耗时: 679ms
2025-05-18 08:01:12,696 - INFO - Response - Page 12
2025-05-18 08:01:12,696 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:01:13,196 - INFO - Request Parameters - Page 13:
2025-05-18 08:01:13,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:13,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:13,865 - INFO - API请求耗时: 668ms
2025-05-18 08:01:13,866 - INFO - Response - Page 13
2025-05-18 08:01:13,867 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:01:14,368 - INFO - Request Parameters - Page 14:
2025-05-18 08:01:14,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:14,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:14,964 - INFO - API请求耗时: 595ms
2025-05-18 08:01:14,965 - INFO - Response - Page 14
2025-05-18 08:01:14,965 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:01:15,467 - INFO - Request Parameters - Page 15:
2025-05-18 08:01:15,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:15,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:16,104 - INFO - API请求耗时: 636ms
2025-05-18 08:01:16,104 - INFO - Response - Page 15
2025-05-18 08:01:16,105 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:01:16,605 - INFO - Request Parameters - Page 16:
2025-05-18 08:01:16,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:16,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:17,393 - INFO - API请求耗时: 786ms
2025-05-18 08:01:17,393 - INFO - Response - Page 16
2025-05-18 08:01:17,394 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:01:17,895 - INFO - Request Parameters - Page 17:
2025-05-18 08:01:17,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:17,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:18,606 - INFO - API请求耗时: 711ms
2025-05-18 08:01:18,607 - INFO - Response - Page 17
2025-05-18 08:01:18,607 - INFO - 第 17 页获取到 100 条记录
2025-05-18 08:01:19,109 - INFO - Request Parameters - Page 18:
2025-05-18 08:01:19,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:19,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200876, 1742745600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:19,825 - INFO - API请求耗时: 715ms
2025-05-18 08:01:19,825 - INFO - Response - Page 18
2025-05-18 08:01:19,826 - INFO - 第 18 页获取到 91 条记录
2025-05-18 08:01:19,827 - INFO - 查询完成，共获取到 1791 条记录
2025-05-18 08:01:19,827 - INFO - 分段 1 查询成功，获取到 1791 条记录
2025-05-18 08:01:20,828 - INFO - 查询分段 2: 2025-03-25 至 2025-03-31
2025-05-18 08:01:20,828 - INFO - 查询日期范围: 2025-03-25 至 2025-03-31，使用分页查询，每页 100 条记录
2025-05-18 08:01:20,829 - INFO - Request Parameters - Page 1:
2025-05-18 08:01:20,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:20,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:21,453 - INFO - API请求耗时: 623ms
2025-05-18 08:01:21,453 - INFO - Response - Page 1
2025-05-18 08:01:21,454 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:01:21,955 - INFO - Request Parameters - Page 2:
2025-05-18 08:01:21,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:21,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:22,582 - INFO - API请求耗时: 626ms
2025-05-18 08:01:22,582 - INFO - Response - Page 2
2025-05-18 08:01:22,583 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:01:23,084 - INFO - Request Parameters - Page 3:
2025-05-18 08:01:23,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:23,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:23,748 - INFO - API请求耗时: 663ms
2025-05-18 08:01:23,748 - INFO - Response - Page 3
2025-05-18 08:01:23,749 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:01:24,249 - INFO - Request Parameters - Page 4:
2025-05-18 08:01:24,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:24,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:24,859 - INFO - API请求耗时: 609ms
2025-05-18 08:01:24,860 - INFO - Response - Page 4
2025-05-18 08:01:24,861 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:01:25,362 - INFO - Request Parameters - Page 5:
2025-05-18 08:01:25,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:25,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:26,113 - INFO - API请求耗时: 750ms
2025-05-18 08:01:26,114 - INFO - Response - Page 5
2025-05-18 08:01:26,114 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:01:26,616 - INFO - Request Parameters - Page 6:
2025-05-18 08:01:26,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:26,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:27,227 - INFO - API请求耗时: 609ms
2025-05-18 08:01:27,227 - INFO - Response - Page 6
2025-05-18 08:01:27,227 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:01:27,727 - INFO - Request Parameters - Page 7:
2025-05-18 08:01:27,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:27,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:28,390 - INFO - API请求耗时: 662ms
2025-05-18 08:01:28,391 - INFO - Response - Page 7
2025-05-18 08:01:28,391 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:01:28,893 - INFO - Request Parameters - Page 8:
2025-05-18 08:01:28,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:28,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:29,514 - INFO - API请求耗时: 620ms
2025-05-18 08:01:29,515 - INFO - Response - Page 8
2025-05-18 08:01:29,515 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:01:30,015 - INFO - Request Parameters - Page 9:
2025-05-18 08:01:30,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:30,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:30,696 - INFO - API请求耗时: 681ms
2025-05-18 08:01:30,697 - INFO - Response - Page 9
2025-05-18 08:01:30,697 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:01:31,198 - INFO - Request Parameters - Page 10:
2025-05-18 08:01:31,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:31,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:31,857 - INFO - API请求耗时: 658ms
2025-05-18 08:01:31,857 - INFO - Response - Page 10
2025-05-18 08:01:31,858 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:01:32,359 - INFO - Request Parameters - Page 11:
2025-05-18 08:01:32,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:32,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:33,016 - INFO - API请求耗时: 655ms
2025-05-18 08:01:33,016 - INFO - Response - Page 11
2025-05-18 08:01:33,017 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:01:33,517 - INFO - Request Parameters - Page 12:
2025-05-18 08:01:33,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:33,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:34,201 - INFO - API请求耗时: 683ms
2025-05-18 08:01:34,202 - INFO - Response - Page 12
2025-05-18 08:01:34,203 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:01:34,703 - INFO - Request Parameters - Page 13:
2025-05-18 08:01:34,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:34,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:35,270 - INFO - API请求耗时: 566ms
2025-05-18 08:01:35,271 - INFO - Response - Page 13
2025-05-18 08:01:35,272 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:01:35,772 - INFO - Request Parameters - Page 14:
2025-05-18 08:01:35,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:35,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:36,396 - INFO - API请求耗时: 623ms
2025-05-18 08:01:36,396 - INFO - Response - Page 14
2025-05-18 08:01:36,397 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:01:36,898 - INFO - Request Parameters - Page 15:
2025-05-18 08:01:36,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:36,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:37,549 - INFO - API请求耗时: 649ms
2025-05-18 08:01:37,550 - INFO - Response - Page 15
2025-05-18 08:01:37,550 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:01:38,050 - INFO - Request Parameters - Page 16:
2025-05-18 08:01:38,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:38,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000876, 1743350400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:38,583 - INFO - API请求耗时: 532ms
2025-05-18 08:01:38,584 - INFO - Response - Page 16
2025-05-18 08:01:38,584 - INFO - 第 16 页获取到 55 条记录
2025-05-18 08:01:38,585 - INFO - 查询完成，共获取到 1555 条记录
2025-05-18 08:01:38,585 - INFO - 分段 2 查询成功，获取到 1555 条记录
2025-05-18 08:01:39,585 - INFO - 查询分段 3: 2025-04-01 至 2025-04-07
2025-05-18 08:01:39,585 - INFO - 查询日期范围: 2025-04-01 至 2025-04-07，使用分页查询，每页 100 条记录
2025-05-18 08:01:39,586 - INFO - Request Parameters - Page 1:
2025-05-18 08:01:39,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:39,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:40,246 - INFO - API请求耗时: 660ms
2025-05-18 08:01:40,246 - INFO - Response - Page 1
2025-05-18 08:01:40,247 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:01:40,748 - INFO - Request Parameters - Page 2:
2025-05-18 08:01:40,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:40,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:41,370 - INFO - API请求耗时: 621ms
2025-05-18 08:01:41,371 - INFO - Response - Page 2
2025-05-18 08:01:41,371 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:01:41,873 - INFO - Request Parameters - Page 3:
2025-05-18 08:01:41,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:41,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:42,481 - INFO - API请求耗时: 607ms
2025-05-18 08:01:42,481 - INFO - Response - Page 3
2025-05-18 08:01:42,482 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:01:42,982 - INFO - Request Parameters - Page 4:
2025-05-18 08:01:42,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:42,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:43,744 - INFO - API请求耗时: 760ms
2025-05-18 08:01:43,744 - INFO - Response - Page 4
2025-05-18 08:01:43,744 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:01:44,246 - INFO - Request Parameters - Page 5:
2025-05-18 08:01:44,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:44,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:44,979 - INFO - API请求耗时: 732ms
2025-05-18 08:01:44,979 - INFO - Response - Page 5
2025-05-18 08:01:44,980 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:01:45,481 - INFO - Request Parameters - Page 6:
2025-05-18 08:01:45,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:45,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:46,105 - INFO - API请求耗时: 623ms
2025-05-18 08:01:46,105 - INFO - Response - Page 6
2025-05-18 08:01:46,106 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:01:46,607 - INFO - Request Parameters - Page 7:
2025-05-18 08:01:46,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:46,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:47,312 - INFO - API请求耗时: 704ms
2025-05-18 08:01:47,312 - INFO - Response - Page 7
2025-05-18 08:01:47,313 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:01:47,814 - INFO - Request Parameters - Page 8:
2025-05-18 08:01:47,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:47,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:48,374 - INFO - API请求耗时: 559ms
2025-05-18 08:01:48,375 - INFO - Response - Page 8
2025-05-18 08:01:48,375 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:01:48,875 - INFO - Request Parameters - Page 9:
2025-05-18 08:01:48,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:48,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:49,512 - INFO - API请求耗时: 636ms
2025-05-18 08:01:49,512 - INFO - Response - Page 9
2025-05-18 08:01:49,513 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:01:50,013 - INFO - Request Parameters - Page 10:
2025-05-18 08:01:50,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:50,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:50,618 - INFO - API请求耗时: 604ms
2025-05-18 08:01:50,619 - INFO - Response - Page 10
2025-05-18 08:01:50,619 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:01:51,120 - INFO - Request Parameters - Page 11:
2025-05-18 08:01:51,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:51,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:51,723 - INFO - API请求耗时: 603ms
2025-05-18 08:01:51,723 - INFO - Response - Page 11
2025-05-18 08:01:51,724 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:01:52,224 - INFO - Request Parameters - Page 12:
2025-05-18 08:01:52,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:52,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:52,809 - INFO - API请求耗时: 584ms
2025-05-18 08:01:52,809 - INFO - Response - Page 12
2025-05-18 08:01:52,810 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:01:53,311 - INFO - Request Parameters - Page 13:
2025-05-18 08:01:53,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:53,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:53,960 - INFO - API请求耗时: 647ms
2025-05-18 08:01:53,960 - INFO - Response - Page 13
2025-05-18 08:01:53,961 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:01:54,462 - INFO - Request Parameters - Page 14:
2025-05-18 08:01:54,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:54,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:55,086 - INFO - API请求耗时: 623ms
2025-05-18 08:01:55,086 - INFO - Response - Page 14
2025-05-18 08:01:55,087 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:01:55,588 - INFO - Request Parameters - Page 15:
2025-05-18 08:01:55,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:55,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:56,407 - INFO - API请求耗时: 818ms
2025-05-18 08:01:56,408 - INFO - Response - Page 15
2025-05-18 08:01:56,408 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:01:56,908 - INFO - Request Parameters - Page 16:
2025-05-18 08:01:56,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:56,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:57,642 - INFO - API请求耗时: 733ms
2025-05-18 08:01:57,642 - INFO - Response - Page 16
2025-05-18 08:01:57,642 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:01:58,144 - INFO - Request Parameters - Page 17:
2025-05-18 08:01:58,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:58,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:58,771 - INFO - API请求耗时: 626ms
2025-05-18 08:01:58,772 - INFO - Response - Page 17
2025-05-18 08:01:58,772 - INFO - 第 17 页获取到 100 条记录
2025-05-18 08:01:59,274 - INFO - Request Parameters - Page 18:
2025-05-18 08:01:59,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:01:59,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800876, 1743955200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:01:59,730 - INFO - API请求耗时: 455ms
2025-05-18 08:01:59,731 - INFO - Response - Page 18
2025-05-18 08:01:59,732 - INFO - 第 18 页获取到 16 条记录
2025-05-18 08:01:59,732 - INFO - 查询完成，共获取到 1716 条记录
2025-05-18 08:01:59,732 - INFO - 分段 3 查询成功，获取到 1716 条记录
2025-05-18 08:02:00,733 - INFO - 查询分段 4: 2025-04-08 至 2025-04-14
2025-05-18 08:02:00,733 - INFO - 查询日期范围: 2025-04-08 至 2025-04-14，使用分页查询，每页 100 条记录
2025-05-18 08:02:00,734 - INFO - Request Parameters - Page 1:
2025-05-18 08:02:00,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:00,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:01,348 - INFO - API请求耗时: 614ms
2025-05-18 08:02:01,348 - INFO - Response - Page 1
2025-05-18 08:02:01,349 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:02:01,850 - INFO - Request Parameters - Page 2:
2025-05-18 08:02:01,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:01,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:02,525 - INFO - API请求耗时: 674ms
2025-05-18 08:02:02,525 - INFO - Response - Page 2
2025-05-18 08:02:02,526 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:02:03,026 - INFO - Request Parameters - Page 3:
2025-05-18 08:02:03,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:03,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:03,613 - INFO - API请求耗时: 586ms
2025-05-18 08:02:03,613 - INFO - Response - Page 3
2025-05-18 08:02:03,614 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:02:04,114 - INFO - Request Parameters - Page 4:
2025-05-18 08:02:04,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:04,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:04,768 - INFO - API请求耗时: 653ms
2025-05-18 08:02:04,768 - INFO - Response - Page 4
2025-05-18 08:02:04,769 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:02:05,269 - INFO - Request Parameters - Page 5:
2025-05-18 08:02:05,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:05,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:05,933 - INFO - API请求耗时: 663ms
2025-05-18 08:02:05,933 - INFO - Response - Page 5
2025-05-18 08:02:05,934 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:02:06,435 - INFO - Request Parameters - Page 6:
2025-05-18 08:02:06,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:06,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:07,074 - INFO - API请求耗时: 638ms
2025-05-18 08:02:07,075 - INFO - Response - Page 6
2025-05-18 08:02:07,075 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:02:07,577 - INFO - Request Parameters - Page 7:
2025-05-18 08:02:07,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:07,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:08,211 - INFO - API请求耗时: 633ms
2025-05-18 08:02:08,212 - INFO - Response - Page 7
2025-05-18 08:02:08,212 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:02:08,713 - INFO - Request Parameters - Page 8:
2025-05-18 08:02:08,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:08,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:09,318 - INFO - API请求耗时: 604ms
2025-05-18 08:02:09,319 - INFO - Response - Page 8
2025-05-18 08:02:09,319 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:02:09,821 - INFO - Request Parameters - Page 9:
2025-05-18 08:02:09,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:09,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:10,456 - INFO - API请求耗时: 634ms
2025-05-18 08:02:10,456 - INFO - Response - Page 9
2025-05-18 08:02:10,457 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:02:10,958 - INFO - Request Parameters - Page 10:
2025-05-18 08:02:10,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:10,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:11,572 - INFO - API请求耗时: 613ms
2025-05-18 08:02:11,572 - INFO - Response - Page 10
2025-05-18 08:02:11,573 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:02:12,073 - INFO - Request Parameters - Page 11:
2025-05-18 08:02:12,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:12,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:12,695 - INFO - API请求耗时: 621ms
2025-05-18 08:02:12,695 - INFO - Response - Page 11
2025-05-18 08:02:12,696 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:02:13,197 - INFO - Request Parameters - Page 12:
2025-05-18 08:02:13,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:13,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:14,111 - INFO - API请求耗时: 913ms
2025-05-18 08:02:14,112 - INFO - Response - Page 12
2025-05-18 08:02:14,113 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:02:14,613 - INFO - Request Parameters - Page 13:
2025-05-18 08:02:14,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:14,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:15,172 - INFO - API请求耗时: 558ms
2025-05-18 08:02:15,172 - INFO - Response - Page 13
2025-05-18 08:02:15,172 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:02:15,672 - INFO - Request Parameters - Page 14:
2025-05-18 08:02:15,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:15,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:16,449 - INFO - API请求耗时: 774ms
2025-05-18 08:02:16,449 - INFO - Response - Page 14
2025-05-18 08:02:16,450 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:02:16,950 - INFO - Request Parameters - Page 15:
2025-05-18 08:02:16,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:16,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:17,644 - INFO - API请求耗时: 693ms
2025-05-18 08:02:17,644 - INFO - Response - Page 15
2025-05-18 08:02:17,645 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:02:18,146 - INFO - Request Parameters - Page 16:
2025-05-18 08:02:18,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:18,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:18,830 - INFO - API请求耗时: 683ms
2025-05-18 08:02:18,831 - INFO - Response - Page 16
2025-05-18 08:02:18,831 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:02:19,333 - INFO - Request Parameters - Page 17:
2025-05-18 08:02:19,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:19,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:19,991 - INFO - API请求耗时: 657ms
2025-05-18 08:02:19,991 - INFO - Response - Page 17
2025-05-18 08:02:19,992 - INFO - 第 17 页获取到 100 条记录
2025-05-18 08:02:20,493 - INFO - Request Parameters - Page 18:
2025-05-18 08:02:20,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:20,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600876, 1744560000876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:20,946 - INFO - API请求耗时: 452ms
2025-05-18 08:02:20,947 - INFO - Response - Page 18
2025-05-18 08:02:20,947 - INFO - 第 18 页获取到 22 条记录
2025-05-18 08:02:20,948 - INFO - 查询完成，共获取到 1722 条记录
2025-05-18 08:02:20,948 - INFO - 分段 4 查询成功，获取到 1722 条记录
2025-05-18 08:02:21,949 - INFO - 查询分段 5: 2025-04-15 至 2025-04-21
2025-05-18 08:02:21,949 - INFO - 查询日期范围: 2025-04-15 至 2025-04-21，使用分页查询，每页 100 条记录
2025-05-18 08:02:21,950 - INFO - Request Parameters - Page 1:
2025-05-18 08:02:21,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:21,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:22,685 - INFO - API请求耗时: 734ms
2025-05-18 08:02:22,685 - INFO - Response - Page 1
2025-05-18 08:02:22,686 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:02:23,187 - INFO - Request Parameters - Page 2:
2025-05-18 08:02:23,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:23,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:23,985 - INFO - API请求耗时: 797ms
2025-05-18 08:02:23,985 - INFO - Response - Page 2
2025-05-18 08:02:23,986 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:02:24,487 - INFO - Request Parameters - Page 3:
2025-05-18 08:02:24,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:24,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:25,095 - INFO - API请求耗时: 607ms
2025-05-18 08:02:25,096 - INFO - Response - Page 3
2025-05-18 08:02:25,096 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:02:25,596 - INFO - Request Parameters - Page 4:
2025-05-18 08:02:25,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:25,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:26,276 - INFO - API请求耗时: 679ms
2025-05-18 08:02:26,277 - INFO - Response - Page 4
2025-05-18 08:02:26,277 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:02:26,779 - INFO - Request Parameters - Page 5:
2025-05-18 08:02:26,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:26,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:27,415 - INFO - API请求耗时: 635ms
2025-05-18 08:02:27,416 - INFO - Response - Page 5
2025-05-18 08:02:27,416 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:02:27,917 - INFO - Request Parameters - Page 6:
2025-05-18 08:02:27,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:27,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:28,559 - INFO - API请求耗时: 642ms
2025-05-18 08:02:28,559 - INFO - Response - Page 6
2025-05-18 08:02:28,560 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:02:29,061 - INFO - Request Parameters - Page 7:
2025-05-18 08:02:29,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:29,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:29,646 - INFO - API请求耗时: 584ms
2025-05-18 08:02:29,646 - INFO - Response - Page 7
2025-05-18 08:02:29,647 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:02:30,148 - INFO - Request Parameters - Page 8:
2025-05-18 08:02:30,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:30,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:30,752 - INFO - API请求耗时: 604ms
2025-05-18 08:02:30,752 - INFO - Response - Page 8
2025-05-18 08:02:30,753 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:02:31,254 - INFO - Request Parameters - Page 9:
2025-05-18 08:02:31,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:31,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:31,890 - INFO - API请求耗时: 635ms
2025-05-18 08:02:31,890 - INFO - Response - Page 9
2025-05-18 08:02:31,891 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:02:32,392 - INFO - Request Parameters - Page 10:
2025-05-18 08:02:32,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:32,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:33,399 - INFO - API请求耗时: 1006ms
2025-05-18 08:02:33,400 - INFO - Response - Page 10
2025-05-18 08:02:33,400 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:02:33,902 - INFO - Request Parameters - Page 11:
2025-05-18 08:02:33,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:33,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:34,596 - INFO - API请求耗时: 693ms
2025-05-18 08:02:34,597 - INFO - Response - Page 11
2025-05-18 08:02:34,597 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:02:35,098 - INFO - Request Parameters - Page 12:
2025-05-18 08:02:35,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:35,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:35,826 - INFO - API请求耗时: 727ms
2025-05-18 08:02:35,827 - INFO - Response - Page 12
2025-05-18 08:02:35,827 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:02:36,327 - INFO - Request Parameters - Page 13:
2025-05-18 08:02:36,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:36,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:36,974 - INFO - API请求耗时: 646ms
2025-05-18 08:02:36,975 - INFO - Response - Page 13
2025-05-18 08:02:36,975 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:02:37,475 - INFO - Request Parameters - Page 14:
2025-05-18 08:02:37,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:37,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:38,062 - INFO - API请求耗时: 586ms
2025-05-18 08:02:38,063 - INFO - Response - Page 14
2025-05-18 08:02:38,063 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:02:38,565 - INFO - Request Parameters - Page 15:
2025-05-18 08:02:38,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:38,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:39,281 - INFO - API请求耗时: 715ms
2025-05-18 08:02:39,282 - INFO - Response - Page 15
2025-05-18 08:02:39,282 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:02:39,783 - INFO - Request Parameters - Page 16:
2025-05-18 08:02:39,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:39,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:40,399 - INFO - API请求耗时: 615ms
2025-05-18 08:02:40,400 - INFO - Response - Page 16
2025-05-18 08:02:40,400 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:02:40,901 - INFO - Request Parameters - Page 17:
2025-05-18 08:02:40,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:40,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:41,586 - INFO - API请求耗时: 685ms
2025-05-18 08:02:41,586 - INFO - Response - Page 17
2025-05-18 08:02:41,587 - INFO - 第 17 页获取到 100 条记录
2025-05-18 08:02:42,088 - INFO - Request Parameters - Page 18:
2025-05-18 08:02:42,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:42,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400876, 1745164800876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:42,543 - INFO - API请求耗时: 454ms
2025-05-18 08:02:42,544 - INFO - Response - Page 18
2025-05-18 08:02:42,544 - INFO - 第 18 页获取到 19 条记录
2025-05-18 08:02:42,545 - INFO - 查询完成，共获取到 1719 条记录
2025-05-18 08:02:42,545 - INFO - 分段 5 查询成功，获取到 1719 条记录
2025-05-18 08:02:43,545 - INFO - 查询分段 6: 2025-04-22 至 2025-04-28
2025-05-18 08:02:43,545 - INFO - 查询日期范围: 2025-04-22 至 2025-04-28，使用分页查询，每页 100 条记录
2025-05-18 08:02:43,546 - INFO - Request Parameters - Page 1:
2025-05-18 08:02:43,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:43,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:44,241 - INFO - API请求耗时: 693ms
2025-05-18 08:02:44,241 - INFO - Response - Page 1
2025-05-18 08:02:44,242 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:02:44,743 - INFO - Request Parameters - Page 2:
2025-05-18 08:02:44,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:44,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:45,415 - INFO - API请求耗时: 672ms
2025-05-18 08:02:45,415 - INFO - Response - Page 2
2025-05-18 08:02:45,416 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:02:45,916 - INFO - Request Parameters - Page 3:
2025-05-18 08:02:45,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:45,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:46,516 - INFO - API请求耗时: 599ms
2025-05-18 08:02:46,517 - INFO - Response - Page 3
2025-05-18 08:02:46,517 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:02:47,019 - INFO - Request Parameters - Page 4:
2025-05-18 08:02:47,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:47,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:47,846 - INFO - API请求耗时: 826ms
2025-05-18 08:02:47,847 - INFO - Response - Page 4
2025-05-18 08:02:47,847 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:02:48,349 - INFO - Request Parameters - Page 5:
2025-05-18 08:02:48,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:48,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:49,133 - INFO - API请求耗时: 782ms
2025-05-18 08:02:49,133 - INFO - Response - Page 5
2025-05-18 08:02:49,134 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:02:49,635 - INFO - Request Parameters - Page 6:
2025-05-18 08:02:49,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:49,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:50,255 - INFO - API请求耗时: 619ms
2025-05-18 08:02:50,255 - INFO - Response - Page 6
2025-05-18 08:02:50,256 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:02:50,757 - INFO - Request Parameters - Page 7:
2025-05-18 08:02:50,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:50,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:51,391 - INFO - API请求耗时: 633ms
2025-05-18 08:02:51,391 - INFO - Response - Page 7
2025-05-18 08:02:51,392 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:02:51,893 - INFO - Request Parameters - Page 8:
2025-05-18 08:02:51,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:51,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:52,531 - INFO - API请求耗时: 637ms
2025-05-18 08:02:52,532 - INFO - Response - Page 8
2025-05-18 08:02:52,533 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:02:53,034 - INFO - Request Parameters - Page 9:
2025-05-18 08:02:53,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:53,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:53,631 - INFO - API请求耗时: 597ms
2025-05-18 08:02:53,631 - INFO - Response - Page 9
2025-05-18 08:02:53,632 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:02:54,133 - INFO - Request Parameters - Page 10:
2025-05-18 08:02:54,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:54,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:54,821 - INFO - API请求耗时: 687ms
2025-05-18 08:02:54,821 - INFO - Response - Page 10
2025-05-18 08:02:54,822 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:02:55,323 - INFO - Request Parameters - Page 11:
2025-05-18 08:02:55,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:55,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:56,053 - INFO - API请求耗时: 729ms
2025-05-18 08:02:56,054 - INFO - Response - Page 11
2025-05-18 08:02:56,054 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:02:56,555 - INFO - Request Parameters - Page 12:
2025-05-18 08:02:56,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:56,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:57,203 - INFO - API请求耗时: 646ms
2025-05-18 08:02:57,203 - INFO - Response - Page 12
2025-05-18 08:02:57,204 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:02:57,705 - INFO - Request Parameters - Page 13:
2025-05-18 08:02:57,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:57,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:58,364 - INFO - API请求耗时: 657ms
2025-05-18 08:02:58,364 - INFO - Response - Page 13
2025-05-18 08:02:58,365 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:02:58,866 - INFO - Request Parameters - Page 14:
2025-05-18 08:02:58,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:02:58,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:02:59,523 - INFO - API请求耗时: 656ms
2025-05-18 08:02:59,523 - INFO - Response - Page 14
2025-05-18 08:02:59,524 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:03:00,025 - INFO - Request Parameters - Page 15:
2025-05-18 08:03:00,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:00,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:00,602 - INFO - API请求耗时: 575ms
2025-05-18 08:03:00,602 - INFO - Response - Page 15
2025-05-18 08:03:00,603 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:03:01,104 - INFO - Request Parameters - Page 16:
2025-05-18 08:03:01,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:01,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:01,764 - INFO - API请求耗时: 659ms
2025-05-18 08:03:01,764 - INFO - Response - Page 16
2025-05-18 08:03:01,765 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:03:02,266 - INFO - Request Parameters - Page 17:
2025-05-18 08:03:02,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:02,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200876, 1745769600876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:02,834 - INFO - API请求耗时: 567ms
2025-05-18 08:03:02,834 - INFO - Response - Page 17
2025-05-18 08:03:02,835 - INFO - 第 17 页获取到 97 条记录
2025-05-18 08:03:02,835 - INFO - 查询完成，共获取到 1697 条记录
2025-05-18 08:03:02,835 - INFO - 分段 6 查询成功，获取到 1697 条记录
2025-05-18 08:03:03,837 - INFO - 查询分段 7: 2025-04-29 至 2025-05-05
2025-05-18 08:03:03,837 - INFO - 查询日期范围: 2025-04-29 至 2025-05-05，使用分页查询，每页 100 条记录
2025-05-18 08:03:03,838 - INFO - Request Parameters - Page 1:
2025-05-18 08:03:03,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:03,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:04,451 - INFO - API请求耗时: 613ms
2025-05-18 08:03:04,452 - INFO - Response - Page 1
2025-05-18 08:03:04,452 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:03:04,952 - INFO - Request Parameters - Page 2:
2025-05-18 08:03:04,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:04,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:05,543 - INFO - API请求耗时: 590ms
2025-05-18 08:03:05,544 - INFO - Response - Page 2
2025-05-18 08:03:05,545 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:03:06,045 - INFO - Request Parameters - Page 3:
2025-05-18 08:03:06,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:06,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:06,710 - INFO - API请求耗时: 663ms
2025-05-18 08:03:06,710 - INFO - Response - Page 3
2025-05-18 08:03:06,711 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:03:07,212 - INFO - Request Parameters - Page 4:
2025-05-18 08:03:07,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:07,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:07,862 - INFO - API请求耗时: 648ms
2025-05-18 08:03:07,863 - INFO - Response - Page 4
2025-05-18 08:03:07,863 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:03:08,365 - INFO - Request Parameters - Page 5:
2025-05-18 08:03:08,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:08,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:09,086 - INFO - API请求耗时: 721ms
2025-05-18 08:03:09,087 - INFO - Response - Page 5
2025-05-18 08:03:09,087 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:03:09,588 - INFO - Request Parameters - Page 6:
2025-05-18 08:03:09,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:09,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:10,311 - INFO - API请求耗时: 722ms
2025-05-18 08:03:10,311 - INFO - Response - Page 6
2025-05-18 08:03:10,312 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:03:10,813 - INFO - Request Parameters - Page 7:
2025-05-18 08:03:10,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:10,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:11,515 - INFO - API请求耗时: 701ms
2025-05-18 08:03:11,516 - INFO - Response - Page 7
2025-05-18 08:03:11,516 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:03:12,016 - INFO - Request Parameters - Page 8:
2025-05-18 08:03:12,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:12,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:12,629 - INFO - API请求耗时: 612ms
2025-05-18 08:03:12,629 - INFO - Response - Page 8
2025-05-18 08:03:12,630 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:03:13,131 - INFO - Request Parameters - Page 9:
2025-05-18 08:03:13,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:13,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:13,809 - INFO - API请求耗时: 677ms
2025-05-18 08:03:13,809 - INFO - Response - Page 9
2025-05-18 08:03:13,810 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:03:14,310 - INFO - Request Parameters - Page 10:
2025-05-18 08:03:14,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:14,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:14,908 - INFO - API请求耗时: 598ms
2025-05-18 08:03:14,909 - INFO - Response - Page 10
2025-05-18 08:03:14,909 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:03:15,411 - INFO - Request Parameters - Page 11:
2025-05-18 08:03:15,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:15,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:16,171 - INFO - API请求耗时: 759ms
2025-05-18 08:03:16,171 - INFO - Response - Page 11
2025-05-18 08:03:16,172 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:03:16,673 - INFO - Request Parameters - Page 12:
2025-05-18 08:03:16,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:16,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:17,300 - INFO - API请求耗时: 626ms
2025-05-18 08:03:17,300 - INFO - Response - Page 12
2025-05-18 08:03:17,301 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:03:17,802 - INFO - Request Parameters - Page 13:
2025-05-18 08:03:17,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:17,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:18,409 - INFO - API请求耗时: 606ms
2025-05-18 08:03:18,410 - INFO - Response - Page 13
2025-05-18 08:03:18,411 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:03:18,912 - INFO - Request Parameters - Page 14:
2025-05-18 08:03:18,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:18,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:19,617 - INFO - API请求耗时: 704ms
2025-05-18 08:03:19,618 - INFO - Response - Page 14
2025-05-18 08:03:19,618 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:03:20,118 - INFO - Request Parameters - Page 15:
2025-05-18 08:03:20,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:20,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:20,838 - INFO - API请求耗时: 718ms
2025-05-18 08:03:20,838 - INFO - Response - Page 15
2025-05-18 08:03:20,839 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:03:21,339 - INFO - Request Parameters - Page 16:
2025-05-18 08:03:21,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:21,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:21,926 - INFO - API请求耗时: 585ms
2025-05-18 08:03:21,926 - INFO - Response - Page 16
2025-05-18 08:03:21,926 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:03:22,426 - INFO - Request Parameters - Page 17:
2025-05-18 08:03:22,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:22,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:23,063 - INFO - API请求耗时: 636ms
2025-05-18 08:03:23,063 - INFO - Response - Page 17
2025-05-18 08:03:23,064 - INFO - 第 17 页获取到 100 条记录
2025-05-18 08:03:23,565 - INFO - Request Parameters - Page 18:
2025-05-18 08:03:23,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:23,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:24,244 - INFO - API请求耗时: 679ms
2025-05-18 08:03:24,244 - INFO - Response - Page 18
2025-05-18 08:03:24,245 - INFO - 第 18 页获取到 100 条记录
2025-05-18 08:03:24,746 - INFO - Request Parameters - Page 19:
2025-05-18 08:03:24,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:24,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:25,311 - INFO - API请求耗时: 564ms
2025-05-18 08:03:25,311 - INFO - Response - Page 19
2025-05-18 08:03:25,312 - INFO - 第 19 页获取到 100 条记录
2025-05-18 08:03:25,812 - INFO - Request Parameters - Page 20:
2025-05-18 08:03:25,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:25,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:26,454 - INFO - API请求耗时: 642ms
2025-05-18 08:03:26,455 - INFO - Response - Page 20
2025-05-18 08:03:26,455 - INFO - 第 20 页获取到 100 条记录
2025-05-18 08:03:26,956 - INFO - Request Parameters - Page 21:
2025-05-18 08:03:26,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:26,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:27,599 - INFO - API请求耗时: 642ms
2025-05-18 08:03:27,600 - INFO - Response - Page 21
2025-05-18 08:03:27,600 - INFO - 第 21 页获取到 100 条记录
2025-05-18 08:03:28,100 - INFO - Request Parameters - Page 22:
2025-05-18 08:03:28,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:28,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:28,677 - INFO - API请求耗时: 576ms
2025-05-18 08:03:28,677 - INFO - Response - Page 22
2025-05-18 08:03:28,678 - INFO - 第 22 页获取到 100 条记录
2025-05-18 08:03:29,179 - INFO - Request Parameters - Page 23:
2025-05-18 08:03:29,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:29,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:29,930 - INFO - API请求耗时: 750ms
2025-05-18 08:03:29,931 - INFO - Response - Page 23
2025-05-18 08:03:29,931 - INFO - 第 23 页获取到 100 条记录
2025-05-18 08:03:30,431 - INFO - Request Parameters - Page 24:
2025-05-18 08:03:30,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:30,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000876, 1746374400876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:30,813 - INFO - API请求耗时: 381ms
2025-05-18 08:03:30,813 - INFO - Response - Page 24
2025-05-18 08:03:30,814 - INFO - 第 24 页获取到 6 条记录
2025-05-18 08:03:30,814 - INFO - 查询完成，共获取到 2306 条记录
2025-05-18 08:03:30,814 - INFO - 分段 7 查询成功，获取到 2306 条记录
2025-05-18 08:03:31,815 - INFO - 查询分段 8: 2025-05-06 至 2025-05-12
2025-05-18 08:03:31,815 - INFO - 查询日期范围: 2025-05-06 至 2025-05-12，使用分页查询，每页 100 条记录
2025-05-18 08:03:31,816 - INFO - Request Parameters - Page 1:
2025-05-18 08:03:31,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:31,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:32,483 - INFO - API请求耗时: 667ms
2025-05-18 08:03:32,484 - INFO - Response - Page 1
2025-05-18 08:03:32,484 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:03:32,985 - INFO - Request Parameters - Page 2:
2025-05-18 08:03:32,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:32,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:33,628 - INFO - API请求耗时: 642ms
2025-05-18 08:03:33,629 - INFO - Response - Page 2
2025-05-18 08:03:33,629 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:03:34,131 - INFO - Request Parameters - Page 3:
2025-05-18 08:03:34,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:34,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:34,768 - INFO - API请求耗时: 636ms
2025-05-18 08:03:34,768 - INFO - Response - Page 3
2025-05-18 08:03:34,769 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:03:35,270 - INFO - Request Parameters - Page 4:
2025-05-18 08:03:35,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:35,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:35,920 - INFO - API请求耗时: 649ms
2025-05-18 08:03:35,921 - INFO - Response - Page 4
2025-05-18 08:03:35,921 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:03:36,422 - INFO - Request Parameters - Page 5:
2025-05-18 08:03:36,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:36,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:37,253 - INFO - API请求耗时: 830ms
2025-05-18 08:03:37,253 - INFO - Response - Page 5
2025-05-18 08:03:37,254 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:03:37,755 - INFO - Request Parameters - Page 6:
2025-05-18 08:03:37,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:37,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:38,570 - INFO - API请求耗时: 814ms
2025-05-18 08:03:38,571 - INFO - Response - Page 6
2025-05-18 08:03:38,571 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:03:39,071 - INFO - Request Parameters - Page 7:
2025-05-18 08:03:39,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:39,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:39,693 - INFO - API请求耗时: 621ms
2025-05-18 08:03:39,693 - INFO - Response - Page 7
2025-05-18 08:03:39,694 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:03:40,195 - INFO - Request Parameters - Page 8:
2025-05-18 08:03:40,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:40,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:40,871 - INFO - API请求耗时: 675ms
2025-05-18 08:03:40,871 - INFO - Response - Page 8
2025-05-18 08:03:40,873 - INFO - 第 8 页获取到 100 条记录
2025-05-18 08:03:41,374 - INFO - Request Parameters - Page 9:
2025-05-18 08:03:41,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:41,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:41,977 - INFO - API请求耗时: 602ms
2025-05-18 08:03:41,977 - INFO - Response - Page 9
2025-05-18 08:03:41,978 - INFO - 第 9 页获取到 100 条记录
2025-05-18 08:03:42,479 - INFO - Request Parameters - Page 10:
2025-05-18 08:03:42,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:42,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:43,163 - INFO - API请求耗时: 684ms
2025-05-18 08:03:43,164 - INFO - Response - Page 10
2025-05-18 08:03:43,165 - INFO - 第 10 页获取到 100 条记录
2025-05-18 08:03:43,666 - INFO - Request Parameters - Page 11:
2025-05-18 08:03:43,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:43,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:44,297 - INFO - API请求耗时: 630ms
2025-05-18 08:03:44,297 - INFO - Response - Page 11
2025-05-18 08:03:44,298 - INFO - 第 11 页获取到 100 条记录
2025-05-18 08:03:44,799 - INFO - Request Parameters - Page 12:
2025-05-18 08:03:44,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:44,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:45,437 - INFO - API请求耗时: 637ms
2025-05-18 08:03:45,438 - INFO - Response - Page 12
2025-05-18 08:03:45,438 - INFO - 第 12 页获取到 100 条记录
2025-05-18 08:03:45,939 - INFO - Request Parameters - Page 13:
2025-05-18 08:03:45,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:45,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:46,592 - INFO - API请求耗时: 651ms
2025-05-18 08:03:46,592 - INFO - Response - Page 13
2025-05-18 08:03:46,593 - INFO - 第 13 页获取到 100 条记录
2025-05-18 08:03:47,094 - INFO - Request Parameters - Page 14:
2025-05-18 08:03:47,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:47,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:47,754 - INFO - API请求耗时: 659ms
2025-05-18 08:03:47,755 - INFO - Response - Page 14
2025-05-18 08:03:47,755 - INFO - 第 14 页获取到 100 条记录
2025-05-18 08:03:48,256 - INFO - Request Parameters - Page 15:
2025-05-18 08:03:48,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:48,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:48,950 - INFO - API请求耗时: 692ms
2025-05-18 08:03:48,950 - INFO - Response - Page 15
2025-05-18 08:03:48,951 - INFO - 第 15 页获取到 100 条记录
2025-05-18 08:03:49,452 - INFO - Request Parameters - Page 16:
2025-05-18 08:03:49,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:49,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:50,118 - INFO - API请求耗时: 665ms
2025-05-18 08:03:50,119 - INFO - Response - Page 16
2025-05-18 08:03:50,119 - INFO - 第 16 页获取到 100 条记录
2025-05-18 08:03:50,620 - INFO - Request Parameters - Page 17:
2025-05-18 08:03:50,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:50,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:51,244 - INFO - API请求耗时: 623ms
2025-05-18 08:03:51,245 - INFO - Response - Page 17
2025-05-18 08:03:51,245 - INFO - 第 17 页获取到 100 条记录
2025-05-18 08:03:51,746 - INFO - Request Parameters - Page 18:
2025-05-18 08:03:51,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:51,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800876, 1746979200876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:52,091 - INFO - API请求耗时: 344ms
2025-05-18 08:03:52,091 - INFO - Response - Page 18
2025-05-18 08:03:52,092 - INFO - 第 18 页获取到 5 条记录
2025-05-18 08:03:52,092 - INFO - 查询完成，共获取到 1705 条记录
2025-05-18 08:03:52,093 - INFO - 分段 8 查询成功，获取到 1705 条记录
2025-05-18 08:03:53,093 - INFO - 查询分段 9: 2025-05-13 至 2025-05-17
2025-05-18 08:03:53,093 - INFO - 查询日期范围: 2025-05-13 至 2025-05-17，使用分页查询，每页 100 条记录
2025-05-18 08:03:53,094 - INFO - Request Parameters - Page 1:
2025-05-18 08:03:53,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:53,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600876, 1747497599876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:53,945 - INFO - API请求耗时: 852ms
2025-05-18 08:03:53,946 - INFO - Response - Page 1
2025-05-18 08:03:53,946 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:03:54,447 - INFO - Request Parameters - Page 2:
2025-05-18 08:03:54,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:54,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600876, 1747497599876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:55,067 - INFO - API请求耗时: 619ms
2025-05-18 08:03:55,067 - INFO - Response - Page 2
2025-05-18 08:03:55,068 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:03:55,568 - INFO - Request Parameters - Page 3:
2025-05-18 08:03:55,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:55,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600876, 1747497599876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:56,145 - INFO - API请求耗时: 575ms
2025-05-18 08:03:56,145 - INFO - Response - Page 3
2025-05-18 08:03:56,145 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:03:56,646 - INFO - Request Parameters - Page 4:
2025-05-18 08:03:56,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:56,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600876, 1747497599876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:57,273 - INFO - API请求耗时: 625ms
2025-05-18 08:03:57,273 - INFO - Response - Page 4
2025-05-18 08:03:57,274 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:03:57,775 - INFO - Request Parameters - Page 5:
2025-05-18 08:03:57,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:57,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600876, 1747497599876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:58,460 - INFO - API请求耗时: 683ms
2025-05-18 08:03:58,460 - INFO - Response - Page 5
2025-05-18 08:03:58,461 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:03:58,962 - INFO - Request Parameters - Page 6:
2025-05-18 08:03:58,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:03:58,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600876, 1747497599876], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:03:59,542 - INFO - API请求耗时: 579ms
2025-05-18 08:03:59,543 - INFO - Response - Page 6
2025-05-18 08:03:59,543 - INFO - 第 6 页获取到 92 条记录
2025-05-18 08:03:59,544 - INFO - 查询完成，共获取到 592 条记录
2025-05-18 08:03:59,544 - INFO - 分段 9 查询成功，获取到 592 条记录
2025-05-18 08:04:00,545 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 14803 条记录，失败 0 次
2025-05-18 08:04:00,545 - INFO - 成功获取宜搭日销售表单数据，共 14803 条记录
2025-05-18 08:04:00,546 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-18 08:04:00,546 - INFO - 开始对比和同步日销售数据...
2025-05-18 08:04:01,015 - INFO - 成功创建宜搭日销售数据索引，共 10957 条记录
2025-05-18 08:04:01,015 - INFO - 开始处理数衍数据，共 13124 条记录
2025-05-18 08:04:01,560 - INFO - 更新表单数据成功: FINST-EEC66XC12CGVGQZLCZH9D7FETG6R2KVO59PAMR
2025-05-18 08:04:01,560 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 21297.0, 'new_value': 44796.0}, {'field': 'amount', 'old_value': 21297.0, 'new_value': 44796.0}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 21297.0, 'new_value': 44796.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-05-18 08:04:02,029 - INFO - 更新表单数据成功: FINST-LLF66O7115ZUNP9G79CZ7B705FGY2BUMNBAAMPQ1
2025-05-18 08:04:02,029 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9626.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9626.0}]
2025-05-18 08:04:02,503 - INFO - 更新表单数据成功: FINST-LLF66O7115ZUNP9G79CZ7B705FGY2BUMNBAAMER1
2025-05-18 08:04:02,503 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250501, 变更字段: [{'field': 'amount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-18 08:04:02,910 - INFO - 更新表单数据成功: FINST-6AG66W81LPZUA4GNEGSWNA1OHMB32NJPNBAAMDR
2025-05-18 08:04:02,910 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250501, 变更字段: [{'field': 'amount', 'old_value': 6249.22, 'new_value': 6358.820000000001}, {'field': 'count', 'old_value': 167, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 6246.12, 'new_value': 6355.72}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 163}]
2025-05-18 08:04:03,335 - INFO - 更新表单数据成功: FINST-7PF66CC1E3ZU7QJQEW19T6YC2UT43Y8SNBAAMTA1
2025-05-18 08:04:03,335 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 12410.65, 'new_value': 12478.25}, {'field': 'amount', 'old_value': 12410.65, 'new_value': 12478.25}, {'field': 'count', 'old_value': 199, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 11748.71, 'new_value': 11816.31}, {'field': 'instoreCount', 'old_value': 187, 'new_value': 188}]
2025-05-18 08:04:03,833 - INFO - 更新表单数据成功: FINST-7PF66CC1E3ZU7QJQEW19T6YC2UT43Y8SNBAAMYA1
2025-05-18 08:04:03,834 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9242.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9242.69}]
2025-05-18 08:04:04,338 - INFO - 更新表单数据成功: FINST-OLC66Z61NMZUP7N7EV2BF72NA4CI2SD0OBAAMCA1
2025-05-18 08:04:04,338 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250501, 变更字段: [{'field': 'amount', 'old_value': 52072.530000000006, 'new_value': 52131.93}, {'field': 'count', 'old_value': 1166, 'new_value': 1168}, {'field': 'instoreAmount', 'old_value': 49767.94, 'new_value': 49827.34}, {'field': 'instoreCount', 'old_value': 1121, 'new_value': 1123}]
2025-05-18 08:04:04,778 - INFO - 更新表单数据成功: FINST-OLC66Z61NMZUP7N7EV2BF72NA4CI2SD0OBAAMHA1
2025-05-18 08:04:04,779 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 21282.2, 'new_value': 22246.2}, {'field': 'amount', 'old_value': 21282.199999999997, 'new_value': 22246.199999999997}, {'field': 'count', 'old_value': 82, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 21511.1, 'new_value': 22475.1}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 84}]
2025-05-18 08:04:05,222 - INFO - 更新表单数据成功: FINST-LLF66O7115ZUNP9G79CZ7B705FGY21T5OBAAMBX1
2025-05-18 08:04:05,222 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_20250501, 变更字段: [{'field': 'count', 'old_value': 52, 'new_value': 64}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 52}]
2025-05-18 08:04:05,696 - INFO - 更新表单数据成功: FINST-LLF66O7115ZUNP9G79CZ7B705FGY21T5OBAAMLX1
2025-05-18 08:04:05,696 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 1630.55, 'new_value': 1718.55}, {'field': 'dailyBillAmount', 'old_value': 1630.55, 'new_value': 1718.55}]
2025-05-18 08:04:06,126 - INFO - 更新表单数据成功: FINST-X3766I91NAGVB5PB7WK9W426W76D2UBO79PAMV2
2025-05-18 08:04:06,126 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_20250509, 变更字段: [{'field': 'recommendAmount', 'old_value': 3088.0, 'new_value': 4776.0}, {'field': 'amount', 'old_value': 3088.0, 'new_value': 4776.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 3088.0, 'new_value': 4776.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-05-18 08:04:06,550 - INFO - 更新表单数据成功: FINST-RI766091IBGVQL11CTY5S7UWTO6S2ZQ299PAMZ
2025-05-18 08:04:06,550 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250510, 变更字段: [{'field': 'amount', 'old_value': 9617.46, 'new_value': 9872.779999999999}, {'field': 'count', 'old_value': 322, 'new_value': 323}, {'field': 'instoreAmount', 'old_value': 8922.66, 'new_value': 9177.98}, {'field': 'instoreCount', 'old_value': 306, 'new_value': 307}]
2025-05-18 08:04:07,057 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMN5
2025-05-18 08:04:07,057 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 1881.0, 'new_value': 2320.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2320.0}]
2025-05-18 08:04:07,497 - INFO - 更新表单数据成功: FINST-7PF66CC18BHV6BAC9B6MD7YZ95M22IH2UGRAMXC
2025-05-18 08:04:07,498 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8527.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8527.8}]
2025-05-18 08:04:07,837 - INFO - 更新表单数据成功: FINST-7PF66CC18BHV6BAC9B6MD7YZ95M22IH2UGRAM5E
2025-05-18 08:04:07,837 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_20250516, 变更字段: [{'field': 'amount', 'old_value': 10048.9, 'new_value': 10314.9}, {'field': 'count', 'old_value': 35, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 10300.9, 'new_value': 10566.9}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 36}]
2025-05-18 08:04:08,352 - INFO - 更新表单数据成功: FINST-7PF66CC18BHV6BAC9B6MD7YZ95M22IH2UGRAMBE
2025-05-18 08:04:08,352 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_20250516, 变更字段: [{'field': 'amount', 'old_value': 2160.98, 'new_value': 2211.68}, {'field': 'count', 'old_value': 60, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 1760.52, 'new_value': 1811.22}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 48}]
2025-05-18 08:04:08,822 - INFO - 更新表单数据成功: FINST-7PF66CC18BHV6BAC9B6MD7YZ95M22IH2UGRAMDE
2025-05-18 08:04:08,823 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_20250516, 变更字段: [{'field': 'amount', 'old_value': 1548.8, 'new_value': 1896.5}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 1548.8, 'new_value': 1896.5}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-05-18 08:04:09,219 - INFO - 更新表单数据成功: FINST-7PF66CC18BHV6BAC9B6MD7YZ95M22JH2UGRAMPE
2025-05-18 08:04:09,220 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 2310.4, 'new_value': 2289.4}, {'field': 'dailyBillAmount', 'old_value': 2310.4, 'new_value': 2289.4}]
2025-05-18 08:04:09,649 - INFO - 更新表单数据成功: FINST-7PF66CC18BHV6BAC9B6MD7YZ95M22JH2UGRAMTE
2025-05-18 08:04:09,650 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_20250516, 变更字段: [{'field': 'amount', 'old_value': 3611.89, 'new_value': 3616.31}, {'field': 'count', 'old_value': 200, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 2055.04, 'new_value': 2059.46}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 115}]
2025-05-18 08:04:10,135 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMC6
2025-05-18 08:04:10,135 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_20250516, 变更字段: [{'field': 'amount', 'old_value': 4431.889999999999, 'new_value': 4480.889999999999}, {'field': 'count', 'old_value': 180, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 4575.49, 'new_value': 4624.49}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 183}]
2025-05-18 08:04:10,606 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMK6
2025-05-18 08:04:10,607 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_20250516, 变更字段: [{'field': 'amount', 'old_value': 922.87, 'new_value': 903.27}]
2025-05-18 08:04:11,052 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAM07
2025-05-18 08:04:11,052 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 12494.1, 'new_value': 12434.1}, {'field': 'amount', 'old_value': 12494.1, 'new_value': 12434.1}]
2025-05-18 08:04:11,526 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMK7
2025-05-18 08:04:11,526 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11915.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11915.9}]
2025-05-18 08:04:11,895 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMP7
2025-05-18 08:04:11,895 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 16295.0, 'new_value': 22961.0}, {'field': 'amount', 'old_value': 16295.0, 'new_value': 22961.0}, {'field': 'count', 'old_value': 36, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 16295.0, 'new_value': 22961.0}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 64}]
2025-05-18 08:04:12,357 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAM52
2025-05-18 08:04:12,357 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250515, 变更字段: [{'field': 'amount', 'old_value': 4590.0, 'new_value': 5310.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 4590.0, 'new_value': 5310.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-05-18 08:04:12,825 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMZ7
2025-05-18 08:04:12,826 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2798.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2798.6}, {'field': 'amount', 'old_value': 3102.8, 'new_value': 3328.6}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 3102.8, 'new_value': 3328.6}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-05-18 08:04:13,251 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162E65UGRAMK8
2025-05-18 08:04:13,252 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 2893.74, 'new_value': 2933.04}, {'field': 'amount', 'old_value': 2893.7400000000002, 'new_value': 2933.04}, {'field': 'count', 'old_value': 215, 'new_value': 223}, {'field': 'instoreAmount', 'old_value': 961.44, 'new_value': 1000.52}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 114}, {'field': 'onlineAmount', 'old_value': 1940.9, 'new_value': 1941.12}, {'field': 'onlineCount', 'old_value': 106, 'new_value': 109}]
2025-05-18 08:04:13,780 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAM9F
2025-05-18 08:04:13,780 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 100.0, 'new_value': 1468.9}, {'field': 'dailyBillAmount', 'old_value': 100.0, 'new_value': 1468.9}]
2025-05-18 08:04:14,255 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAMEF
2025-05-18 08:04:14,256 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 6711.09, 'new_value': 6821.79}, {'field': 'amount', 'old_value': 6711.09, 'new_value': 6821.79}, {'field': 'count', 'old_value': 135, 'new_value': 136}, {'field': 'instoreAmount', 'old_value': 6564.19, 'new_value': 6674.89}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 132}]
2025-05-18 08:04:14,669 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAMGF
2025-05-18 08:04:14,669 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250516, 变更字段: [{'field': 'amount', 'old_value': 8709.63, 'new_value': 8765.48}, {'field': 'count', 'old_value': 428, 'new_value': 435}, {'field': 'instoreAmount', 'old_value': 4761.65, 'new_value': 4817.5}, {'field': 'instoreCount', 'old_value': 286, 'new_value': 293}]
2025-05-18 08:04:15,105 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAMNF
2025-05-18 08:04:15,105 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 1233.72, 'new_value': 1224.77}, {'field': 'amount', 'old_value': 1233.72, 'new_value': 1224.77}, {'field': 'onlineAmount', 'old_value': 1085.77, 'new_value': 1076.82}]
2025-05-18 08:04:15,500 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAMVF
2025-05-18 08:04:15,501 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 6430.88, 'new_value': 6516.28}, {'field': 'amount', 'old_value': 6430.879999999999, 'new_value': 6516.28}, {'field': 'count', 'old_value': 291, 'new_value': 293}, {'field': 'instoreAmount', 'old_value': 2167.36, 'new_value': 2176.36}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 101}, {'field': 'onlineAmount', 'old_value': 4506.12, 'new_value': 4582.52}, {'field': 'onlineCount', 'old_value': 191, 'new_value': 192}]
2025-05-18 08:04:15,964 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAM5G
2025-05-18 08:04:15,964 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 32559.58}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 32559.58}]
2025-05-18 08:04:16,459 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAMEG
2025-05-18 08:04:16,459 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_20250516, 变更字段: [{'field': 'amount', 'old_value': 12323.91, 'new_value': 12289.91}]
2025-05-18 08:04:17,009 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3CW7UGRAMGG
2025-05-18 08:04:17,010 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250516, 变更字段: [{'field': 'amount', 'old_value': 27540.63, 'new_value': 28796.63}, {'field': 'count', 'old_value': 231, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 12010.9, 'new_value': 13266.9}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 84}]
2025-05-18 08:04:17,463 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMOG
2025-05-18 08:04:17,463 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 31146.72, 'new_value': 36093.0}, {'field': 'dailyBillAmount', 'old_value': 31146.72, 'new_value': 36093.0}]
2025-05-18 08:04:17,918 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMSG
2025-05-18 08:04:17,918 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_20250516, 变更字段: [{'field': 'amount', 'old_value': 7707.5, 'new_value': 7725.8}, {'field': 'count', 'old_value': 92, 'new_value': 94}, {'field': 'onlineAmount', 'old_value': 2722.4, 'new_value': 2740.7}, {'field': 'onlineCount', 'old_value': 67, 'new_value': 69}]
2025-05-18 08:04:18,324 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMYG
2025-05-18 08:04:18,324 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250516, 变更字段: [{'field': 'amount', 'old_value': 4998.41, 'new_value': 5005.41}, {'field': 'count', 'old_value': 352, 'new_value': 353}, {'field': 'instoreAmount', 'old_value': 2804.23, 'new_value': 2811.23}, {'field': 'instoreCount', 'old_value': 255, 'new_value': 256}]
2025-05-18 08:04:18,754 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAM3H
2025-05-18 08:04:18,755 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250516, 变更字段: [{'field': 'amount', 'old_value': 21239.03, 'new_value': 21256.829999999998}, {'field': 'count', 'old_value': 233, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 15443.02, 'new_value': 15460.82}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 83}]
2025-05-18 08:04:19,172 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAM5H
2025-05-18 08:04:19,172 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4440.61}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4440.61}, {'field': 'amount', 'old_value': 426.2, 'new_value': 938.0699999999999}, {'field': 'count', 'old_value': 48, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 465.5, 'new_value': 991.8}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 84}]
2025-05-18 08:04:19,638 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMJH
2025-05-18 08:04:19,638 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250516, 变更字段: [{'field': 'amount', 'old_value': 784.09, 'new_value': 827.29}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 662.8, 'new_value': 706.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-05-18 08:04:20,060 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMSH
2025-05-18 08:04:20,060 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_20250516, 变更字段: [{'field': 'amount', 'old_value': 89.8, 'new_value': 345.2}, {'field': 'count', 'old_value': 1, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 89.8, 'new_value': 327.8}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 3}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 17.4}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-05-18 08:04:20,586 - INFO - 更新表单数据成功: FINST-IQE66ZC1HBHVU5DJBUE6L6Y3YY5N3DW7UGRAMUH
2025-05-18 08:04:20,587 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_20250516, 变更字段: [{'field': 'amount', 'old_value': 13019.800000000001, 'new_value': 16683.100000000002}, {'field': 'count', 'old_value': 17, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 13859.1, 'new_value': 17522.4}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 19}]
2025-05-18 08:04:21,024 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733SJAUGRAMJF
2025-05-18 08:04:21,024 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6155.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6155.5}]
2025-05-18 08:04:21,420 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733SJAUGRAMRF
2025-05-18 08:04:21,420 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1980.0}, {'field': 'amount', 'old_value': 0.0, 'new_value': 1980.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 1980.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-05-18 08:04:22,039 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMTF
2025-05-18 08:04:22,040 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_20250516, 变更字段: [{'field': 'amount', 'old_value': 3803.75, 'new_value': 3977.75}, {'field': 'count', 'old_value': 154, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 2070.55, 'new_value': 2244.55}, {'field': 'instoreCount', 'old_value': 112, 'new_value': 113}]
2025-05-18 08:04:22,551 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMXF
2025-05-18 08:04:22,552 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10630.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10630.3}]
2025-05-18 08:04:23,057 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAM1G
2025-05-18 08:04:23,058 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 5041.24, 'new_value': 6792.61}, {'field': 'dailyBillAmount', 'old_value': 5041.24, 'new_value': 6792.61}]
2025-05-18 08:04:23,526 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAM5G
2025-05-18 08:04:23,526 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 3381.12, 'new_value': 3386.88}, {'field': 'amount', 'old_value': 3381.12, 'new_value': 3386.88}, {'field': 'count', 'old_value': 163, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 1193.27, 'new_value': 1199.03}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 77}]
2025-05-18 08:04:23,961 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAM9G
2025-05-18 08:04:23,962 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250516, 变更字段: [{'field': 'amount', 'old_value': 7414.7, 'new_value': 7427.33}, {'field': 'count', 'old_value': 670, 'new_value': 676}, {'field': 'instoreAmount', 'old_value': 7238.77, 'new_value': 7392.0}, {'field': 'instoreCount', 'old_value': 631, 'new_value': 663}, {'field': 'onlineAmount', 'old_value': 302.49, 'new_value': 161.89}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 13}]
2025-05-18 08:04:24,429 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMKG
2025-05-18 08:04:24,430 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_20250516, 变更字段: [{'field': 'amount', 'old_value': 4541.17, 'new_value': 4580.07}, {'field': 'count', 'old_value': 85, 'new_value': 86}, {'field': 'onlineAmount', 'old_value': 693.58, 'new_value': 732.48}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 15}]
2025-05-18 08:04:24,891 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMV6
2025-05-18 08:04:24,891 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 2633.48, 'new_value': 3791.18}, {'field': 'amount', 'old_value': 2633.48, 'new_value': 3791.18}, {'field': 'instoreAmount', 'old_value': 1072.19, 'new_value': 2229.89}]
2025-05-18 08:04:25,326 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMSG
2025-05-18 08:04:25,326 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 15454.1, 'new_value': 16842.75}, {'field': 'amount', 'old_value': 15454.1, 'new_value': 16842.75}, {'field': 'count', 'old_value': 729, 'new_value': 780}, {'field': 'onlineAmount', 'old_value': 15889.59, 'new_value': 17278.24}, {'field': 'onlineCount', 'old_value': 729, 'new_value': 780}]
2025-05-18 08:04:25,753 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMGH
2025-05-18 08:04:25,754 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 3239.23, 'new_value': 3240.22}, {'field': 'amount', 'old_value': 3239.23, 'new_value': 3240.2200000000003}, {'field': 'count', 'old_value': 184, 'new_value': 185}, {'field': 'instoreAmount', 'old_value': 1643.47, 'new_value': 1655.46}, {'field': 'instoreCount', 'old_value': 95, 'new_value': 96}]
2025-05-18 08:04:26,162 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733UJAUGRAMOH
2025-05-18 08:04:26,163 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250516, 变更字段: [{'field': 'amount', 'old_value': 17026.33, 'new_value': 18545.99}, {'field': 'count', 'old_value': 243, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 15767.3, 'new_value': 17286.96}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 188}]
2025-05-18 08:04:26,615 - INFO - 更新表单数据成功: FINST-A17661C1HFHVUTF77C3YSA9LPDJ02N3DUGRAMI5
2025-05-18 08:04:26,616 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_20250516, 变更字段: [{'field': 'amount', 'old_value': 17524.7, 'new_value': 19126.2}, {'field': 'count', 'old_value': 120, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 9574.0, 'new_value': 11175.5}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 92}]
2025-05-18 08:04:27,078 - INFO - 更新表单数据成功: FINST-A17661C1HFHVUTF77C3YSA9LPDJ02N3DUGRAMU5
2025-05-18 08:04:27,079 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 4595.69, 'new_value': 4480.59}, {'field': 'amount', 'old_value': 4595.69, 'new_value': 4480.589999999999}]
2025-05-18 08:04:27,560 - INFO - 更新表单数据成功: FINST-A17661C1HFHVUTF77C3YSA9LPDJ02N3DUGRAM16
2025-05-18 08:04:27,560 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 10577.44, 'new_value': 10593.92}, {'field': 'dailyBillAmount', 'old_value': 10577.44, 'new_value': 10593.92}, {'field': 'amount', 'old_value': 6747.87, 'new_value': 6730.87}]
2025-05-18 08:04:28,023 - INFO - 更新表单数据成功: FINST-A17661C1HFHVUTF77C3YSA9LPDJ02O3DUGRAM96
2025-05-18 08:04:28,023 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_20250516, 变更字段: [{'field': 'amount', 'old_value': 1336.72, 'new_value': 1336.71}]
2025-05-18 08:04:28,094 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-18 08:04:28,539 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-18 08:04:31,542 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-18 08:04:31,965 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-18 08:04:34,968 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-18 08:04:35,367 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-18 08:04:38,370 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-18 08:04:38,754 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-18 08:04:41,757 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-18 08:04:42,163 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-18 08:04:45,167 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-18 08:04:45,584 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-18 08:04:48,587 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-18 08:04:48,976 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-18 08:04:51,980 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-18 08:04:52,472 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-18 08:04:55,476 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-18 08:04:55,935 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-18 08:04:58,938 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-18 08:04:59,425 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-18 08:05:02,429 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-18 08:05:02,906 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-18 08:05:05,914 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-18 08:05:06,309 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-18 08:05:09,312 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-18 08:05:09,773 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-18 08:05:12,776 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-18 08:05:13,151 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-18 08:05:16,154 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-18 08:05:16,682 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-18 08:05:19,686 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-18 08:05:20,074 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-18 08:05:23,078 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-18 08:05:23,425 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-18 08:05:26,427 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-18 08:05:26,821 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-18 08:05:29,825 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-18 08:05:30,291 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-18 08:05:33,294 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-18 08:05:33,735 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-18 08:05:36,738 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-18 08:05:37,143 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-18 08:05:40,145 - INFO - 正在批量插入每日数据，批次 22/22，共 67 条记录
2025-05-18 08:05:40,566 - INFO - 批量插入每日数据成功，批次 22，67 条记录
2025-05-18 08:05:43,567 - INFO - 批量插入每日数据完成: 总计 2167 条，成功 2167 条，失败 0 条
2025-05-18 08:05:43,571 - INFO - 批量插入日销售数据完成，共 2167 条记录
2025-05-18 08:05:43,571 - INFO - 日销售数据同步完成！更新: 59 条，插入: 2167 条，错误: 0 条，跳过: 10898 条
2025-05-18 08:05:43,571 - INFO - 正在获取宜搭月销售表单数据...
2025-05-18 08:05:43,571 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-18 08:05:43,572 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-18 08:05:43,572 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:43,572 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:43,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:43,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:44,005 - INFO - API请求耗时: 434ms
2025-05-18 08:05:44,006 - INFO - Response - Page 1
2025-05-18 08:05:44,006 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:44,006 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:44,007 - WARNING - 月度分段 1 查询返回空数据
2025-05-18 08:05:44,007 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-18 08:05:44,007 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:44,007 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:44,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:44,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:44,476 - INFO - API请求耗时: 467ms
2025-05-18 08:05:44,476 - INFO - Response - Page 1
2025-05-18 08:05:44,477 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:44,477 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:44,477 - WARNING - 单月查询返回空数据: 2024-05
2025-05-18 08:05:44,979 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-18 08:05:44,979 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:44,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:44,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:45,202 - INFO - API请求耗时: 223ms
2025-05-18 08:05:45,202 - INFO - Response - Page 1
2025-05-18 08:05:45,203 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:45,203 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:45,203 - WARNING - 单月查询返回空数据: 2024-06
2025-05-18 08:05:45,704 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:45,704 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:45,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:45,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:45,907 - INFO - API请求耗时: 202ms
2025-05-18 08:05:45,907 - INFO - Response - Page 1
2025-05-18 08:05:45,908 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:45,908 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:45,908 - WARNING - 单月查询返回空数据: 2024-07
2025-05-18 08:05:47,409 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-18 08:05:47,409 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:47,410 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:47,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:47,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:47,647 - INFO - API请求耗时: 236ms
2025-05-18 08:05:47,648 - INFO - Response - Page 1
2025-05-18 08:05:47,648 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:47,649 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:47,649 - WARNING - 月度分段 2 查询返回空数据
2025-05-18 08:05:47,649 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-18 08:05:47,649 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:47,649 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:47,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:47,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:47,862 - INFO - API请求耗时: 212ms
2025-05-18 08:05:47,863 - INFO - Response - Page 1
2025-05-18 08:05:47,863 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:47,864 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:47,864 - WARNING - 单月查询返回空数据: 2024-08
2025-05-18 08:05:48,364 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-18 08:05:48,364 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:48,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:48,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:48,558 - INFO - API请求耗时: 193ms
2025-05-18 08:05:48,558 - INFO - Response - Page 1
2025-05-18 08:05:48,559 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:48,559 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:48,559 - WARNING - 单月查询返回空数据: 2024-09
2025-05-18 08:05:49,059 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:49,059 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:49,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:49,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:49,273 - INFO - API请求耗时: 212ms
2025-05-18 08:05:49,273 - INFO - Response - Page 1
2025-05-18 08:05:49,274 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-18 08:05:49,274 - INFO - 查询完成，共获取到 0 条记录
2025-05-18 08:05:49,274 - WARNING - 单月查询返回空数据: 2024-10
2025-05-18 08:05:50,776 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-18 08:05:50,776 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-18 08:05:50,777 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:50,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:50,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:51,366 - INFO - API请求耗时: 589ms
2025-05-18 08:05:51,366 - INFO - Response - Page 1
2025-05-18 08:05:51,366 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:05:51,867 - INFO - Request Parameters - Page 2:
2025-05-18 08:05:51,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:51,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:52,388 - INFO - API请求耗时: 520ms
2025-05-18 08:05:52,389 - INFO - Response - Page 2
2025-05-18 08:05:52,389 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:05:52,890 - INFO - Request Parameters - Page 3:
2025-05-18 08:05:52,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:52,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:53,296 - INFO - API请求耗时: 405ms
2025-05-18 08:05:53,297 - INFO - Response - Page 3
2025-05-18 08:05:53,297 - INFO - 第 3 页获取到 48 条记录
2025-05-18 08:05:53,297 - INFO - 查询完成，共获取到 248 条记录
2025-05-18 08:05:53,298 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-18 08:05:54,299 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-18 08:05:54,299 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-18 08:05:54,300 - INFO - Request Parameters - Page 1:
2025-05-18 08:05:54,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:54,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:54,931 - INFO - API请求耗时: 630ms
2025-05-18 08:05:54,931 - INFO - Response - Page 1
2025-05-18 08:05:54,933 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:05:55,434 - INFO - Request Parameters - Page 2:
2025-05-18 08:05:55,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:55,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:55,925 - INFO - API请求耗时: 490ms
2025-05-18 08:05:55,925 - INFO - Response - Page 2
2025-05-18 08:05:55,926 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:05:56,427 - INFO - Request Parameters - Page 3:
2025-05-18 08:05:56,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:56,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:56,968 - INFO - API请求耗时: 540ms
2025-05-18 08:05:56,968 - INFO - Response - Page 3
2025-05-18 08:05:56,969 - INFO - 第 3 页获取到 100 条记录
2025-05-18 08:05:57,470 - INFO - Request Parameters - Page 4:
2025-05-18 08:05:57,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:57,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:58,003 - INFO - API请求耗时: 532ms
2025-05-18 08:05:58,003 - INFO - Response - Page 4
2025-05-18 08:05:58,004 - INFO - 第 4 页获取到 100 条记录
2025-05-18 08:05:58,505 - INFO - Request Parameters - Page 5:
2025-05-18 08:05:58,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:58,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:05:59,180 - INFO - API请求耗时: 674ms
2025-05-18 08:05:59,181 - INFO - Response - Page 5
2025-05-18 08:05:59,181 - INFO - 第 5 页获取到 100 条记录
2025-05-18 08:05:59,683 - INFO - Request Parameters - Page 6:
2025-05-18 08:05:59,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:05:59,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:06:00,260 - INFO - API请求耗时: 576ms
2025-05-18 08:06:00,260 - INFO - Response - Page 6
2025-05-18 08:06:00,261 - INFO - 第 6 页获取到 100 条记录
2025-05-18 08:06:00,762 - INFO - Request Parameters - Page 7:
2025-05-18 08:06:00,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:06:00,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:06:01,269 - INFO - API请求耗时: 506ms
2025-05-18 08:06:01,269 - INFO - Response - Page 7
2025-05-18 08:06:01,270 - INFO - 第 7 页获取到 100 条记录
2025-05-18 08:06:01,771 - INFO - Request Parameters - Page 8:
2025-05-18 08:06:01,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:06:01,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:06:02,109 - INFO - API请求耗时: 337ms
2025-05-18 08:06:02,109 - INFO - Response - Page 8
2025-05-18 08:06:02,110 - INFO - 第 8 页获取到 16 条记录
2025-05-18 08:06:02,110 - INFO - 查询完成，共获取到 716 条记录
2025-05-18 08:06:02,110 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-18 08:06:03,111 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-18 08:06:03,111 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-18 08:06:03,112 - INFO - Request Parameters - Page 1:
2025-05-18 08:06:03,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:06:03,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:06:03,693 - INFO - API请求耗时: 580ms
2025-05-18 08:06:03,693 - INFO - Response - Page 1
2025-05-18 08:06:03,694 - INFO - 第 1 页获取到 100 条记录
2025-05-18 08:06:04,194 - INFO - Request Parameters - Page 2:
2025-05-18 08:06:04,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:06:04,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:06:04,757 - INFO - API请求耗时: 562ms
2025-05-18 08:06:04,758 - INFO - Response - Page 2
2025-05-18 08:06:04,758 - INFO - 第 2 页获取到 100 条记录
2025-05-18 08:06:05,259 - INFO - Request Parameters - Page 3:
2025-05-18 08:06:05,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 08:06:05,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 08:06:05,640 - INFO - API请求耗时: 380ms
2025-05-18 08:06:05,640 - INFO - Response - Page 3
2025-05-18 08:06:05,641 - INFO - 第 3 页获取到 24 条记录
2025-05-18 08:06:05,641 - INFO - 查询完成，共获取到 224 条记录
2025-05-18 08:06:05,641 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-18 08:06:06,642 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-18 08:06:06,642 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-18 08:06:06,643 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-18 08:06:06,643 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-18 08:06:06,652 - INFO - 成功获取SQLite月度汇总数据，共 1189 条记录
2025-05-18 08:06:06,712 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-18 08:06:07,234 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-18 08:06:07,235 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 106827.62, 'new_value': 118213.28}, {'field': 'dailyBillAmount', 'old_value': 106827.62, 'new_value': 118213.28}, {'field': 'amount', 'old_value': 3210.7, 'new_value': 3506.7}, {'field': 'count', 'old_value': 48, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 3286.7, 'new_value': 3582.7}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 49}]
2025-05-18 08:06:07,746 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-18 08:06:07,747 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287481.47, 'new_value': 313411.63}, {'field': 'dailyBillAmount', 'old_value': 287481.47, 'new_value': 313411.63}, {'field': 'amount', 'old_value': 157265.1, 'new_value': 169079.5}, {'field': 'count', 'old_value': 1453, 'new_value': 1546}, {'field': 'instoreAmount', 'old_value': 61638.2, 'new_value': 66842.9}, {'field': 'instoreCount', 'old_value': 449, 'new_value': 482}, {'field': 'onlineAmount', 'old_value': 95918.3, 'new_value': 102528.0}, {'field': 'onlineCount', 'old_value': 1004, 'new_value': 1064}]
2025-05-18 08:06:08,215 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-18 08:06:08,216 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 184641.61000000002, 'new_value': 204699.74}, {'field': 'dailyBillAmount', 'old_value': 184641.61000000002, 'new_value': 204699.74}, {'field': 'amount', 'old_value': 186518.5, 'new_value': 206608.11000000002}, {'field': 'count', 'old_value': 1251, 'new_value': 1370}, {'field': 'instoreAmount', 'old_value': 175235.9, 'new_value': 194756.1}, {'field': 'instoreCount', 'old_value': 1087, 'new_value': 1196}, {'field': 'onlineAmount', 'old_value': 11500.8, 'new_value': 12070.21}, {'field': 'onlineCount', 'old_value': 164, 'new_value': 174}]
2025-05-18 08:06:08,639 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-18 08:06:08,639 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 428892.39, 'new_value': 460566.97}, {'field': 'dailyBillAmount', 'old_value': 428892.39, 'new_value': 460566.97}, {'field': 'amount', 'old_value': 322418.65, 'new_value': 344045.9}, {'field': 'count', 'old_value': 1538, 'new_value': 1656}, {'field': 'instoreAmount', 'old_value': 322418.65, 'new_value': 344045.9}, {'field': 'instoreCount', 'old_value': 1538, 'new_value': 1656}]
2025-05-18 08:06:09,154 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-18 08:06:09,154 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 333504.84, 'new_value': 368705.74}, {'field': 'dailyBillAmount', 'old_value': 333504.84, 'new_value': 368705.74}, {'field': 'amount', 'old_value': 535946.0, 'new_value': 581957.0}, {'field': 'count', 'old_value': 1831, 'new_value': 1992}, {'field': 'instoreAmount', 'old_value': 537196.0, 'new_value': 583207.0}, {'field': 'instoreCount', 'old_value': 1831, 'new_value': 1992}]
2025-05-18 08:06:09,599 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-18 08:06:09,599 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47567.7, 'new_value': 47967.3}, {'field': 'dailyBillAmount', 'old_value': 47567.7, 'new_value': 47967.3}, {'field': 'amount', 'old_value': 62437.01, 'new_value': 63056.21}, {'field': 'count', 'old_value': 186, 'new_value': 196}, {'field': 'onlineAmount', 'old_value': 28124.82, 'new_value': 28744.02}, {'field': 'onlineCount', 'old_value': 153, 'new_value': 163}]
2025-05-18 08:06:10,078 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-18 08:06:10,079 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'count', 'old_value': 88, 'new_value': 91}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 91}]
2025-05-18 08:06:10,546 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-18 08:06:10,546 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 476528.19, 'new_value': 507217.5}, {'field': 'dailyBillAmount', 'old_value': 476528.19, 'new_value': 507217.5}, {'field': 'amount', 'old_value': 470993.96, 'new_value': 493632.76}, {'field': 'count', 'old_value': 3434, 'new_value': 3572}, {'field': 'instoreAmount', 'old_value': 373343.06, 'new_value': 394091.56}, {'field': 'instoreCount', 'old_value': 1575, 'new_value': 1669}, {'field': 'onlineAmount', 'old_value': 100683.17, 'new_value': 102815.87}, {'field': 'onlineCount', 'old_value': 1859, 'new_value': 1903}]
2025-05-18 08:06:11,016 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-05-18 08:06:11,017 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 175997.54, 'new_value': 177386.19}, {'field': 'amount', 'old_value': 175991.7, 'new_value': 177379.6}, {'field': 'count', 'old_value': 7380, 'new_value': 7431}, {'field': 'onlineAmount', 'old_value': 180013.38, 'new_value': 181402.03}, {'field': 'onlineCount', 'old_value': 7380, 'new_value': 7431}]
2025-05-18 08:06:11,470 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-18 08:06:11,470 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 486206.89, 'new_value': 518233.42}, {'field': 'dailyBillAmount', 'old_value': 486206.89, 'new_value': 518233.42}, {'field': 'amount', 'old_value': 104657.71, 'new_value': 115069.89}, {'field': 'count', 'old_value': 546, 'new_value': 589}, {'field': 'instoreAmount', 'old_value': 104657.71, 'new_value': 115069.89}, {'field': 'instoreCount', 'old_value': 546, 'new_value': 589}]
2025-05-18 08:06:11,877 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-18 08:06:11,877 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 410108.56, 'new_value': 444803.16}, {'field': 'dailyBillAmount', 'old_value': 410108.56, 'new_value': 444803.16}, {'field': 'amount', 'old_value': 15144.0, 'new_value': 16440.0}, {'field': 'count', 'old_value': 18, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 15144.0, 'new_value': 16440.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 22}]
2025-05-18 08:06:12,308 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-18 08:06:12,309 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 79106.3, 'new_value': 83999.3}, {'field': 'count', 'old_value': 229, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 79107.1, 'new_value': 84000.1}, {'field': 'instoreCount', 'old_value': 229, 'new_value': 235}]
2025-05-18 08:06:12,758 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-18 08:06:12,759 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 642216.78, 'new_value': 695790.5499999999}, {'field': 'dailyBillAmount', 'old_value': 642216.78, 'new_value': 695790.5499999999}, {'field': 'amount', 'old_value': -274842.98, 'new_value': -310320.01}, {'field': 'count', 'old_value': 706, 'new_value': 746}, {'field': 'instoreAmount', 'old_value': 423117.49, 'new_value': 437945.49}, {'field': 'instoreCount', 'old_value': 706, 'new_value': 746}]
2025-05-18 08:06:13,179 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-18 08:06:13,180 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 201837.0, 'new_value': 226305.0}, {'field': 'amount', 'old_value': 201837.0, 'new_value': 226305.0}, {'field': 'count', 'old_value': 817, 'new_value': 902}, {'field': 'instoreAmount', 'old_value': 201837.0, 'new_value': 226305.0}, {'field': 'instoreCount', 'old_value': 817, 'new_value': 902}]
2025-05-18 08:06:13,570 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-18 08:06:13,571 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 265401.94, 'new_value': 288127.27}, {'field': 'dailyBillAmount', 'old_value': 213211.17, 'new_value': 232379.82}, {'field': 'amount', 'old_value': 265401.94, 'new_value': 288127.27}, {'field': 'count', 'old_value': 925, 'new_value': 1005}, {'field': 'instoreAmount', 'old_value': 265401.94, 'new_value': 288127.27}, {'field': 'instoreCount', 'old_value': 925, 'new_value': 1005}]
2025-05-18 08:06:14,036 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-18 08:06:14,036 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122950.35, 'new_value': 134866.25}, {'field': 'dailyBillAmount', 'old_value': 122950.35, 'new_value': 134866.25}, {'field': 'amount', 'old_value': 9564.9, 'new_value': 9700.3}, {'field': 'count', 'old_value': 74, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 11175.7, 'new_value': 11594.2}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 76}]
2025-05-18 08:06:14,464 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-18 08:06:14,464 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68221.34, 'new_value': 73684.9}, {'field': 'dailyBillAmount', 'old_value': 68221.34, 'new_value': 73684.9}, {'field': 'amount', 'old_value': 44173.91, 'new_value': 47610.16}, {'field': 'count', 'old_value': 645, 'new_value': 698}, {'field': 'instoreAmount', 'old_value': 45772.01, 'new_value': 49230.26}, {'field': 'instoreCount', 'old_value': 645, 'new_value': 698}]
2025-05-18 08:06:14,911 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-18 08:06:14,912 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93885.56, 'new_value': 99571.95}, {'field': 'dailyBillAmount', 'old_value': 49691.62, 'new_value': 55012.57}, {'field': 'amount', 'old_value': 93884.7, 'new_value': 99571.09}, {'field': 'count', 'old_value': 3244, 'new_value': 3455}, {'field': 'instoreAmount', 'old_value': 82854.68, 'new_value': 87866.52}, {'field': 'instoreCount', 'old_value': 2944, 'new_value': 3135}, {'field': 'onlineAmount', 'old_value': 11030.88, 'new_value': 11705.43}, {'field': 'onlineCount', 'old_value': 300, 'new_value': 320}]
2025-05-18 08:06:15,327 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-18 08:06:15,327 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156971.22, 'new_value': 171982.22}, {'field': 'dailyBillAmount', 'old_value': 153513.0, 'new_value': 168524.0}, {'field': 'amount', 'old_value': 125545.22, 'new_value': 138685.22}, {'field': 'count', 'old_value': 131, 'new_value': 144}, {'field': 'instoreAmount', 'old_value': 125416.0, 'new_value': 138556.0}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 143}]
2025-05-18 08:06:15,823 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-18 08:06:15,823 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 340448.21, 'new_value': 351735.21}, {'field': 'dailyBillAmount', 'old_value': 339943.66, 'new_value': 351230.66}, {'field': 'amount', 'old_value': 340448.21, 'new_value': 351735.21}, {'field': 'count', 'old_value': 316, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 340449.21, 'new_value': 351736.21}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 330}]
2025-05-18 08:06:16,230 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-18 08:06:16,230 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 31607.0, 'new_value': 33395.0}, {'field': 'count', 'old_value': 49, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 31607.0, 'new_value': 33395.0}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 51}]
2025-05-18 08:06:16,679 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-18 08:06:16,679 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70966.4, 'new_value': 76251.8}, {'field': 'dailyBillAmount', 'old_value': 70966.4, 'new_value': 76251.8}, {'field': 'amount', 'old_value': 80721.2, 'new_value': 85775.8}, {'field': 'count', 'old_value': 206, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 80725.8, 'new_value': 85780.4}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 219}]
2025-05-18 08:06:17,154 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-18 08:06:17,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111545.87, 'new_value': 115452.87}, {'field': 'amount', 'old_value': 111545.87, 'new_value': 115452.87}, {'field': 'count', 'old_value': 132, 'new_value': 139}, {'field': 'instoreAmount', 'old_value': 111672.87, 'new_value': 115579.87}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 139}]
2025-05-18 08:06:17,566 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-18 08:06:17,567 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 142045.12, 'new_value': 157467.91999999998}, {'field': 'dailyBillAmount', 'old_value': 142045.12, 'new_value': 157467.91999999998}, {'field': 'amount', 'old_value': 149928.25, 'new_value': 165746.05}, {'field': 'count', 'old_value': 1005, 'new_value': 1106}, {'field': 'instoreAmount', 'old_value': 150948.25, 'new_value': 166955.05}, {'field': 'instoreCount', 'old_value': 1005, 'new_value': 1106}]
2025-05-18 08:06:17,980 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-18 08:06:17,980 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95905.1, 'new_value': 102036.81}, {'field': 'dailyBillAmount', 'old_value': 95905.1, 'new_value': 102036.81}, {'field': 'amount', 'old_value': 9321.05, 'new_value': 9437.64}, {'field': 'count', 'old_value': 859, 'new_value': 877}, {'field': 'instoreAmount', 'old_value': 12749.71, 'new_value': 12978.0}, {'field': 'instoreCount', 'old_value': 859, 'new_value': 877}]
2025-05-18 08:06:18,491 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-18 08:06:18,491 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165704.26, 'new_value': 177237.86}, {'field': 'amount', 'old_value': 165701.57, 'new_value': 177235.07}, {'field': 'count', 'old_value': 4193, 'new_value': 4463}, {'field': 'instoreAmount', 'old_value': 160826.61, 'new_value': 172259.31}, {'field': 'instoreCount', 'old_value': 4044, 'new_value': 4308}, {'field': 'onlineAmount', 'old_value': 7347.33, 'new_value': 7578.2300000000005}, {'field': 'onlineCount', 'old_value': 149, 'new_value': 155}]
2025-05-18 08:06:18,940 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-18 08:06:18,941 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 143716.65, 'new_value': 156279.47}, {'field': 'dailyBillAmount', 'old_value': 143716.65, 'new_value': 156279.47}, {'field': 'amount', 'old_value': 143716.65, 'new_value': 156279.47}, {'field': 'count', 'old_value': 453, 'new_value': 491}, {'field': 'instoreAmount', 'old_value': 143716.65, 'new_value': 156279.47}, {'field': 'instoreCount', 'old_value': 453, 'new_value': 491}]
2025-05-18 08:06:19,352 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-18 08:06:19,353 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126654.26, 'new_value': 139906.06}, {'field': 'dailyBillAmount', 'old_value': 126654.26, 'new_value': 139906.06}, {'field': 'amount', 'old_value': 37945.8, 'new_value': 44403.2}, {'field': 'count', 'old_value': 95, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 37945.8, 'new_value': 44403.2}, {'field': 'instoreCount', 'old_value': 95, 'new_value': 110}]
2025-05-18 08:06:19,820 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-18 08:06:19,820 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 260873.76, 'new_value': 286243.63}, {'field': 'dailyBillAmount', 'old_value': 260873.76, 'new_value': 286243.63}, {'field': 'amount', 'old_value': 106928.9, 'new_value': 117479.1}, {'field': 'count', 'old_value': 408, 'new_value': 448}, {'field': 'instoreAmount', 'old_value': 106929.16, 'new_value': 117479.36}, {'field': 'instoreCount', 'old_value': 408, 'new_value': 448}]
2025-05-18 08:06:20,269 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFK
2025-05-18 08:06:20,269 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15618.0, 'new_value': 17831.0}, {'field': 'amount', 'old_value': 15618.0, 'new_value': 17831.0}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 15618.0, 'new_value': 17831.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-05-18 08:06:20,706 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-18 08:06:20,707 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58501.38, 'new_value': 61629.76}, {'field': 'dailyBillAmount', 'old_value': 58501.38, 'new_value': 61629.76}, {'field': 'amount', 'old_value': 17641.0, 'new_value': 18719.05}, {'field': 'count', 'old_value': 646, 'new_value': 683}, {'field': 'instoreAmount', 'old_value': 4286.8099999999995, 'new_value': 4586.91}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 114}, {'field': 'onlineAmount', 'old_value': 13593.21, 'new_value': 14371.16}, {'field': 'onlineCount', 'old_value': 540, 'new_value': 569}]
2025-05-18 08:06:21,142 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-18 08:06:21,143 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 91962.41, 'new_value': 98243.26}, {'field': 'dailyBillAmount', 'old_value': 91962.41, 'new_value': 98243.26}, {'field': 'amount', 'old_value': 14611.64, 'new_value': 15422.15}, {'field': 'count', 'old_value': 360, 'new_value': 382}, {'field': 'instoreAmount', 'old_value': 12411.41, 'new_value': 13025.11}, {'field': 'instoreCount', 'old_value': 317, 'new_value': 336}, {'field': 'onlineAmount', 'old_value': 2200.92, 'new_value': 2397.73}, {'field': 'onlineCount', 'old_value': 43, 'new_value': 46}]
2025-05-18 08:06:21,591 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-18 08:06:21,591 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 10518.03, 'new_value': 14096.03}, {'field': 'dailyBillAmount', 'old_value': 10518.03, 'new_value': 14096.03}, {'field': 'amount', 'old_value': 10507.18, 'new_value': 11685.18}, {'field': 'count', 'old_value': 374, 'new_value': 420}, {'field': 'instoreAmount', 'old_value': 10874.78, 'new_value': 12052.78}, {'field': 'instoreCount', 'old_value': 374, 'new_value': 420}]
2025-05-18 08:06:22,084 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-18 08:06:22,085 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29914.77, 'new_value': 32294.56}, {'field': 'dailyBillAmount', 'old_value': 29914.77, 'new_value': 32294.56}, {'field': 'amount', 'old_value': 18868.64, 'new_value': 20141.97}, {'field': 'count', 'old_value': 1057, 'new_value': 1119}, {'field': 'instoreAmount', 'old_value': 9366.13, 'new_value': 10125.13}, {'field': 'instoreCount', 'old_value': 410, 'new_value': 440}, {'field': 'onlineAmount', 'old_value': 9914.119999999999, 'new_value': 10492.82}, {'field': 'onlineCount', 'old_value': 647, 'new_value': 679}]
2025-05-18 08:06:22,522 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-18 08:06:22,523 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 205473.21, 'new_value': 220108.19}, {'field': 'dailyBillAmount', 'old_value': 205473.21, 'new_value': 220108.19}, {'field': 'amount', 'old_value': 95716.27, 'new_value': 102400.27}, {'field': 'count', 'old_value': 388, 'new_value': 410}, {'field': 'instoreAmount', 'old_value': 98942.52, 'new_value': 106156.52}, {'field': 'instoreCount', 'old_value': 388, 'new_value': 410}]
2025-05-18 08:06:22,942 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-18 08:06:22,942 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 11394.68, 'new_value': 12005.09}, {'field': 'count', 'old_value': 88, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 11468.92, 'new_value': 12079.33}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 95}]
2025-05-18 08:06:23,418 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-18 08:06:23,418 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132258.66, 'new_value': 141803.51}, {'field': 'dailyBillAmount', 'old_value': 132258.66, 'new_value': 141803.51}, {'field': 'amount', 'old_value': 62962.5, 'new_value': 67551.95999999999}, {'field': 'count', 'old_value': 2702, 'new_value': 2865}, {'field': 'instoreAmount', 'old_value': 64334.229999999996, 'new_value': 68963.18}, {'field': 'instoreCount', 'old_value': 2702, 'new_value': 2865}]
2025-05-18 08:06:23,879 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-18 08:06:23,880 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 284578.4, 'new_value': 304364.4}, {'field': 'dailyBillAmount', 'old_value': 284578.4, 'new_value': 304364.4}, {'field': 'amount', 'old_value': 284578.4, 'new_value': 304364.4}, {'field': 'count', 'old_value': 351, 'new_value': 382}, {'field': 'instoreAmount', 'old_value': 284578.4, 'new_value': 304364.4}, {'field': 'instoreCount', 'old_value': 351, 'new_value': 382}]
2025-05-18 08:06:24,302 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-18 08:06:24,303 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 136751.77, 'new_value': 149322.74}, {'field': 'dailyBillAmount', 'old_value': 136751.77, 'new_value': 149322.74}, {'field': 'amount', 'old_value': 79953.01, 'new_value': 86605.81}, {'field': 'count', 'old_value': 206, 'new_value': 227}, {'field': 'instoreAmount', 'old_value': 81004.81, 'new_value': 88022.41}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 227}]
2025-05-18 08:06:24,734 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-18 08:06:24,734 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32863.0, 'new_value': 34662.0}, {'field': 'dailyBillAmount', 'old_value': 32863.0, 'new_value': 34662.0}, {'field': 'amount', 'old_value': 32863.0, 'new_value': 34662.0}, {'field': 'count', 'old_value': 647, 'new_value': 679}, {'field': 'instoreAmount', 'old_value': 32902.0, 'new_value': 34701.0}, {'field': 'instoreCount', 'old_value': 647, 'new_value': 679}]
2025-05-18 08:06:25,225 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-18 08:06:25,225 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56870.75, 'new_value': 60884.04}, {'field': 'dailyBillAmount', 'old_value': 56870.75, 'new_value': 60884.04}, {'field': 'amount', 'old_value': 59121.3, 'new_value': 63056.31}, {'field': 'count', 'old_value': 3105, 'new_value': 3323}, {'field': 'instoreAmount', 'old_value': 27951.43, 'new_value': 29876.57}, {'field': 'instoreCount', 'old_value': 1395, 'new_value': 1493}, {'field': 'onlineAmount', 'old_value': 31927.68, 'new_value': 34075.86}, {'field': 'onlineCount', 'old_value': 1710, 'new_value': 1830}]
2025-05-18 08:06:25,720 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-18 08:06:25,720 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19481.73, 'new_value': 21252.55}, {'field': 'dailyBillAmount', 'old_value': 19481.73, 'new_value': 21252.55}, {'field': 'amount', 'old_value': 27246.52, 'new_value': 29397.4}, {'field': 'count', 'old_value': 811, 'new_value': 865}, {'field': 'instoreAmount', 'old_value': 24395.73, 'new_value': 26463.85}, {'field': 'instoreCount', 'old_value': 696, 'new_value': 746}, {'field': 'onlineAmount', 'old_value': 2873.59, 'new_value': 2956.35}, {'field': 'onlineCount', 'old_value': 115, 'new_value': 119}]
2025-05-18 08:06:26,176 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-18 08:06:26,177 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42426.58, 'new_value': 44440.83}, {'field': 'dailyBillAmount', 'old_value': 42426.58, 'new_value': 44440.83}, {'field': 'amount', 'old_value': 42406.59, 'new_value': 44404.94}, {'field': 'count', 'old_value': 1632, 'new_value': 1716}, {'field': 'instoreAmount', 'old_value': 27519.02, 'new_value': 28755.02}, {'field': 'instoreCount', 'old_value': 969, 'new_value': 1019}, {'field': 'onlineAmount', 'old_value': 14975.73, 'new_value': 15774.98}, {'field': 'onlineCount', 'old_value': 663, 'new_value': 697}]
2025-05-18 08:06:26,565 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-18 08:06:26,565 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 43811.24, 'new_value': 47284.43}, {'field': 'count', 'old_value': 521, 'new_value': 561}, {'field': 'instoreAmount', 'old_value': 44238.14, 'new_value': 47711.33}, {'field': 'instoreCount', 'old_value': 521, 'new_value': 561}]
2025-05-18 08:06:27,029 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-18 08:06:27,030 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47669.8, 'new_value': 51293.3}, {'field': 'amount', 'old_value': 47669.3, 'new_value': 51292.8}, {'field': 'count', 'old_value': 1167, 'new_value': 1262}, {'field': 'instoreAmount', 'old_value': 48318.0, 'new_value': 51941.5}, {'field': 'instoreCount', 'old_value': 1167, 'new_value': 1262}]
2025-05-18 08:06:27,445 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-18 08:06:27,445 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 217919.82, 'new_value': 232141.02}, {'field': 'dailyBillAmount', 'old_value': 217919.82, 'new_value': 232141.02}, {'field': 'amount', 'old_value': 63077.32, 'new_value': 67626.02}, {'field': 'count', 'old_value': 234, 'new_value': 248}, {'field': 'instoreAmount', 'old_value': 63077.32, 'new_value': 67626.02}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 248}]
2025-05-18 08:06:27,819 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-18 08:06:27,819 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69244.62, 'new_value': 73817.92}, {'field': 'dailyBillAmount', 'old_value': 69244.62, 'new_value': 73817.92}, {'field': 'amount', 'old_value': 67116.82, 'new_value': 71396.12}, {'field': 'count', 'old_value': 209, 'new_value': 233}, {'field': 'instoreAmount', 'old_value': 69251.45, 'new_value': 73530.75}, {'field': 'instoreCount', 'old_value': 209, 'new_value': 233}]
2025-05-18 08:06:28,232 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-18 08:06:28,232 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39448.0, 'new_value': 39602.0}, {'field': 'dailyBillAmount', 'old_value': 39448.0, 'new_value': 39602.0}, {'field': 'amount', 'old_value': 46895.0, 'new_value': 49035.0}, {'field': 'count', 'old_value': 87, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 50993.0, 'new_value': 53133.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 90}]
2025-05-18 08:06:28,686 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-18 08:06:28,686 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56970.65, 'new_value': 60559.15}, {'field': 'dailyBillAmount', 'old_value': 54477.45, 'new_value': 57366.95}, {'field': 'amount', 'old_value': 56622.25, 'new_value': 60557.95}, {'field': 'count', 'old_value': 159, 'new_value': 177}, {'field': 'instoreAmount', 'old_value': 62450.25, 'new_value': 67796.95}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 177}]
2025-05-18 08:06:29,197 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-18 08:06:29,198 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77505.78, 'new_value': 84673.34}, {'field': 'dailyBillAmount', 'old_value': 77505.78, 'new_value': 84673.34}, {'field': 'amount', 'old_value': 42867.56, 'new_value': 46627.85}, {'field': 'count', 'old_value': 1185, 'new_value': 1289}, {'field': 'instoreAmount', 'old_value': 36461.44, 'new_value': 39937.909999999996}, {'field': 'instoreCount', 'old_value': 997, 'new_value': 1091}, {'field': 'onlineAmount', 'old_value': 6592.32, 'new_value': 6876.82}, {'field': 'onlineCount', 'old_value': 188, 'new_value': 198}]
2025-05-18 08:06:29,707 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-18 08:06:29,708 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111840.47, 'new_value': 117205.62}, {'field': 'dailyBillAmount', 'old_value': 107598.14, 'new_value': 112887.72}, {'field': 'amount', 'old_value': 111840.47, 'new_value': 117205.62}, {'field': 'count', 'old_value': 1374, 'new_value': 1436}, {'field': 'instoreAmount', 'old_value': 107169.45, 'new_value': 112165.45}, {'field': 'instoreCount', 'old_value': 1323, 'new_value': 1380}, {'field': 'onlineAmount', 'old_value': 4671.0199999999995, 'new_value': 5040.17}, {'field': 'onlineCount', 'old_value': 51, 'new_value': 56}]
2025-05-18 08:06:30,134 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-18 08:06:30,135 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47897.59, 'new_value': 53200.18}, {'field': 'dailyBillAmount', 'old_value': 47897.59, 'new_value': 53200.18}, {'field': 'amount', 'old_value': 67963.66, 'new_value': 73396.84}, {'field': 'count', 'old_value': 285, 'new_value': 302}, {'field': 'instoreAmount', 'old_value': 65869.02, 'new_value': 70816.01}, {'field': 'instoreCount', 'old_value': 257, 'new_value': 271}, {'field': 'onlineAmount', 'old_value': 2094.64, 'new_value': 2580.83}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 31}]
2025-05-18 08:06:30,565 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-18 08:06:30,566 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 135409.4, 'new_value': 149554.4}, {'field': 'dailyBillAmount', 'old_value': 135409.4, 'new_value': 149554.4}, {'field': 'amount', 'old_value': 140557.6, 'new_value': 150671.7}, {'field': 'count', 'old_value': 509, 'new_value': 550}, {'field': 'instoreAmount', 'old_value': 142737.6, 'new_value': 152852.6}, {'field': 'instoreCount', 'old_value': 509, 'new_value': 550}]
2025-05-18 08:06:30,963 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-18 08:06:30,963 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32512.0, 'new_value': 35059.0}, {'field': 'dailyBillAmount', 'old_value': 32512.0, 'new_value': 35059.0}, {'field': 'amount', 'old_value': 31417.0, 'new_value': 33964.0}, {'field': 'count', 'old_value': 70, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 31773.0, 'new_value': 34320.0}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 78}]
2025-05-18 08:06:31,373 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-18 08:06:31,373 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 14161.28, 'new_value': 14943.08}, {'field': 'count', 'old_value': 28, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 14442.68, 'new_value': 15224.48}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 30}]
2025-05-18 08:06:31,765 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-18 08:06:31,766 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15648.9, 'new_value': 17092.1}, {'field': 'dailyBillAmount', 'old_value': 15648.9, 'new_value': 17092.1}, {'field': 'amount', 'old_value': 12246.11, 'new_value': 13460.91}, {'field': 'count', 'old_value': 555, 'new_value': 606}, {'field': 'instoreAmount', 'old_value': 12414.56, 'new_value': 13629.36}, {'field': 'instoreCount', 'old_value': 555, 'new_value': 606}]
2025-05-18 08:06:32,236 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-18 08:06:32,237 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27294.42, 'new_value': 29038.07}, {'field': 'amount', 'old_value': 27292.95, 'new_value': 29036.600000000002}, {'field': 'count', 'old_value': 1481, 'new_value': 1557}, {'field': 'instoreAmount', 'old_value': 32698.55, 'new_value': 34534.3}, {'field': 'instoreCount', 'old_value': 1481, 'new_value': 1557}]
2025-05-18 08:06:32,840 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-18 08:06:32,841 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85714.86, 'new_value': 92884.24}, {'field': 'dailyBillAmount', 'old_value': 85714.86, 'new_value': 92884.24}, {'field': 'amount', 'old_value': 68233.0, 'new_value': 73023.6}, {'field': 'count', 'old_value': 273, 'new_value': 294}, {'field': 'instoreAmount', 'old_value': 68233.0, 'new_value': 73023.6}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 294}]
2025-05-18 08:06:33,357 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-18 08:06:33,357 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 244735.3, 'new_value': 263894.32}, {'field': 'dailyBillAmount', 'old_value': 244735.3, 'new_value': 263894.32}, {'field': 'amount', 'old_value': 153824.56, 'new_value': 163271.05}, {'field': 'count', 'old_value': 1805, 'new_value': 1898}, {'field': 'instoreAmount', 'old_value': 61328.02, 'new_value': 66150.21}, {'field': 'instoreCount', 'old_value': 723, 'new_value': 768}, {'field': 'onlineAmount', 'old_value': 92496.54, 'new_value': 97120.84}, {'field': 'onlineCount', 'old_value': 1082, 'new_value': 1130}]
2025-05-18 08:06:33,839 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-18 08:06:33,839 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 153501.91999999998, 'new_value': 167707.82}, {'field': 'dailyBillAmount', 'old_value': 153501.91999999998, 'new_value': 167707.82}, {'field': 'amount', 'old_value': 159585.69999999998, 'new_value': 173847.6}, {'field': 'count', 'old_value': 942, 'new_value': 1028}, {'field': 'instoreAmount', 'old_value': 160365.6, 'new_value': 174627.5}, {'field': 'instoreCount', 'old_value': 942, 'new_value': 1028}]
2025-05-18 08:06:34,269 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-18 08:06:34,269 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 107672.38, 'new_value': 116959.52}, {'field': 'dailyBillAmount', 'old_value': 107672.38, 'new_value': 116959.52}, {'field': 'amount', 'old_value': 70717.86, 'new_value': 76041.67}, {'field': 'count', 'old_value': 819, 'new_value': 867}, {'field': 'instoreAmount', 'old_value': 63064.08, 'new_value': 67668.62}, {'field': 'instoreCount', 'old_value': 561, 'new_value': 594}, {'field': 'onlineAmount', 'old_value': 8315.95, 'new_value': 9164.22}, {'field': 'onlineCount', 'old_value': 258, 'new_value': 273}]
2025-05-18 08:06:34,732 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-18 08:06:34,732 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114215.05, 'new_value': 130140.45}, {'field': 'dailyBillAmount', 'old_value': 109409.3, 'new_value': 125334.7}, {'field': 'amount', 'old_value': 114215.05, 'new_value': 130140.45}, {'field': 'count', 'old_value': 483, 'new_value': 543}, {'field': 'instoreAmount', 'old_value': 114215.05, 'new_value': 130140.45}, {'field': 'instoreCount', 'old_value': 483, 'new_value': 543}]
2025-05-18 08:06:35,154 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-18 08:06:35,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14607.18, 'new_value': 15711.07}, {'field': 'dailyBillAmount', 'old_value': 14607.18, 'new_value': 15711.07}, {'field': 'amount', 'old_value': 17313.18, 'new_value': 18518.57}, {'field': 'count', 'old_value': 515, 'new_value': 556}, {'field': 'instoreAmount', 'old_value': 17313.18, 'new_value': 18518.57}, {'field': 'instoreCount', 'old_value': 515, 'new_value': 556}]
2025-05-18 08:06:35,560 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-18 08:06:35,560 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 187069.2, 'new_value': 200700.2}, {'field': 'amount', 'old_value': 187069.2, 'new_value': 200700.2}, {'field': 'count', 'old_value': 278, 'new_value': 299}, {'field': 'instoreAmount', 'old_value': 187069.2, 'new_value': 200700.2}, {'field': 'instoreCount', 'old_value': 278, 'new_value': 299}]
2025-05-18 08:06:35,986 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-18 08:06:35,986 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31867.4, 'new_value': 34698.1}, {'field': 'amount', 'old_value': 31867.4, 'new_value': 34698.1}, {'field': 'count', 'old_value': 258, 'new_value': 277}, {'field': 'instoreAmount', 'old_value': 31867.4, 'new_value': 34698.1}, {'field': 'instoreCount', 'old_value': 258, 'new_value': 277}]
2025-05-18 08:06:36,415 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-18 08:06:36,416 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 229468.0, 'new_value': 239866.0}, {'field': 'amount', 'old_value': 229468.0, 'new_value': 239866.0}, {'field': 'count', 'old_value': 46, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 229468.0, 'new_value': 239866.0}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 48}]
2025-05-18 08:06:36,792 - INFO - 更新表单数据成功: FINST-LLF66FD1Y3AVWSRCCPLOLDIRPAOX3YH3YGHAMGB
2025-05-18 08:06:36,793 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_7930744A602B4DF1A0EB88515999F5E5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 6949.27, 'new_value': 8978.27}, {'field': 'amount', 'old_value': 6949.27, 'new_value': 8978.27}, {'field': 'count', 'old_value': 47, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 6949.27, 'new_value': 8978.27}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 53}]
2025-05-18 08:06:37,236 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMG01
2025-05-18 08:06:37,237 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31388.0, 'new_value': 38052.0}, {'field': 'amount', 'old_value': 31388.0, 'new_value': 38052.0}, {'field': 'count', 'old_value': 4, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 31388.0, 'new_value': 38052.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 7}]
2025-05-18 08:06:37,703 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-18 08:06:37,703 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 23989.8, 'new_value': 27654.4}, {'field': 'count', 'old_value': 315, 'new_value': 359}, {'field': 'instoreAmount', 'old_value': 23989.8, 'new_value': 27654.4}, {'field': 'instoreCount', 'old_value': 315, 'new_value': 359}]
2025-05-18 08:06:38,195 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-18 08:06:38,195 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31577.4, 'new_value': 33144.4}, {'field': 'dailyBillAmount', 'old_value': 31577.4, 'new_value': 33144.4}, {'field': 'amount', 'old_value': 31577.4, 'new_value': 33144.4}, {'field': 'count', 'old_value': 37, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 31577.4, 'new_value': 33144.4}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 39}]
2025-05-18 08:06:38,652 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-18 08:06:38,652 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 285505.88, 'new_value': 308039.2}, {'field': 'dailyBillAmount', 'old_value': 285505.88, 'new_value': 308039.2}, {'field': 'amount', 'old_value': 298136.88, 'new_value': 320670.2}, {'field': 'count', 'old_value': 939, 'new_value': 1013}, {'field': 'instoreAmount', 'old_value': 298136.88, 'new_value': 320670.2}, {'field': 'instoreCount', 'old_value': 939, 'new_value': 1013}]
2025-05-18 08:06:39,067 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-18 08:06:39,067 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 665272.43, 'new_value': 709556.3}, {'field': 'count', 'old_value': 855, 'new_value': 916}, {'field': 'instoreAmount', 'old_value': 665272.6, 'new_value': 709556.47}, {'field': 'instoreCount', 'old_value': 855, 'new_value': 916}]
2025-05-18 08:06:39,490 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-18 08:06:39,490 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93486.3, 'new_value': 102014.1}, {'field': 'dailyBillAmount', 'old_value': 93486.3, 'new_value': 102014.1}, {'field': 'amount', 'old_value': 20791.2, 'new_value': 23032.2}, {'field': 'count', 'old_value': 81, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 20792.7, 'new_value': 23033.7}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 89}]
2025-05-18 08:06:39,886 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-18 08:06:39,886 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 135828.04, 'new_value': 149538.88999999998}, {'field': 'amount', 'old_value': 135827.57, 'new_value': 149538.24}, {'field': 'count', 'old_value': 1355, 'new_value': 1480}, {'field': 'instoreAmount', 'old_value': 87816.70999999999, 'new_value': 99184.3}, {'field': 'instoreCount', 'old_value': 786, 'new_value': 865}, {'field': 'onlineAmount', 'old_value': 49770.55, 'new_value': 53784.52}, {'field': 'onlineCount', 'old_value': 569, 'new_value': 615}]
2025-05-18 08:06:40,300 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-18 08:06:40,300 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 206481.47, 'new_value': 224365.3}, {'field': 'dailyBillAmount', 'old_value': 206481.47, 'new_value': 224365.3}, {'field': 'amount', 'old_value': 18567.74, 'new_value': 21539.28}, {'field': 'count', 'old_value': 616, 'new_value': 677}, {'field': 'instoreAmount', 'old_value': 21294.53, 'new_value': 24771.88}, {'field': 'instoreCount', 'old_value': 616, 'new_value': 677}]
2025-05-18 08:06:40,755 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-18 08:06:40,756 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 213288.42, 'new_value': 231987.03}, {'field': 'dailyBillAmount', 'old_value': 213288.42, 'new_value': 231987.03}, {'field': 'amount', 'old_value': 105854.79000000001, 'new_value': 113955.46}, {'field': 'count', 'old_value': 2314, 'new_value': 2537}, {'field': 'instoreAmount', 'old_value': 89823.49, 'new_value': 97100.21}, {'field': 'instoreCount', 'old_value': 1963, 'new_value': 2162}, {'field': 'onlineAmount', 'old_value': 17721.65, 'new_value': 18697.07}, {'field': 'onlineCount', 'old_value': 351, 'new_value': 375}]
2025-05-18 08:06:41,188 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-18 08:06:41,189 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 203707.8, 'new_value': 216379.6}, {'field': 'amount', 'old_value': 203706.2, 'new_value': 216378.0}, {'field': 'count', 'old_value': 800, 'new_value': 853}, {'field': 'instoreAmount', 'old_value': 206435.1, 'new_value': 219106.9}, {'field': 'instoreCount', 'old_value': 800, 'new_value': 853}]
2025-05-18 08:06:41,621 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-18 08:06:41,622 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 328762.98, 'new_value': 354338.54}, {'field': 'count', 'old_value': 6247, 'new_value': 6723}, {'field': 'instoreAmount', 'old_value': 308609.72, 'new_value': 334185.28}, {'field': 'instoreCount', 'old_value': 5854, 'new_value': 6330}]
2025-05-18 08:06:42,095 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-18 08:06:42,096 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 175412.67, 'new_value': 192854.07}, {'field': 'dailyBillAmount', 'old_value': 53862.64, 'new_value': 68024.42}, {'field': 'amount', 'old_value': 143127.4, 'new_value': 160568.8}, {'field': 'count', 'old_value': 3454, 'new_value': 3873}, {'field': 'instoreAmount', 'old_value': 128641.3, 'new_value': 144947.8}, {'field': 'instoreCount', 'old_value': 2795, 'new_value': 3154}, {'field': 'onlineAmount', 'old_value': 14645.9, 'new_value': 15780.8}, {'field': 'onlineCount', 'old_value': 659, 'new_value': 719}]
2025-05-18 08:06:42,510 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-18 08:06:42,510 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 35680.77, 'new_value': 34062.04}, {'field': 'count', 'old_value': 817, 'new_value': 818}, {'field': 'onlineAmount', 'old_value': 44790.729999999996, 'new_value': 44812.729999999996}, {'field': 'onlineCount', 'old_value': 782, 'new_value': 783}]
2025-05-18 08:06:42,917 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-18 08:06:42,917 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 104175.17, 'new_value': 111263.19}, {'field': 'count', 'old_value': 7130, 'new_value': 7644}, {'field': 'instoreAmount', 'old_value': 86969.76, 'new_value': 92743.18000000001}, {'field': 'instoreCount', 'old_value': 5801, 'new_value': 6218}, {'field': 'onlineAmount', 'old_value': 19619.510000000002, 'new_value': 21103.81}, {'field': 'onlineCount', 'old_value': 1329, 'new_value': 1426}]
2025-05-18 08:06:43,350 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-18 08:06:43,350 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 178877.62, 'new_value': 194002.89}, {'field': 'dailyBillAmount', 'old_value': 178877.62, 'new_value': 194002.89}, {'field': 'amount', 'old_value': 173004.74, 'new_value': 187571.04}, {'field': 'count', 'old_value': 5030, 'new_value': 5474}, {'field': 'instoreAmount', 'old_value': 174194.85, 'new_value': 188822.35}, {'field': 'instoreCount', 'old_value': 5030, 'new_value': 5474}]
2025-05-18 08:06:43,727 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-18 08:06:43,728 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50627.46, 'new_value': 54488.97}, {'field': 'amount', 'old_value': 50627.46, 'new_value': 54488.090000000004}, {'field': 'count', 'old_value': 2767, 'new_value': 2971}, {'field': 'instoreAmount', 'old_value': 31915.95, 'new_value': 34044.81}, {'field': 'instoreCount', 'old_value': 1795, 'new_value': 1925}, {'field': 'onlineAmount', 'old_value': 18711.51, 'new_value': 20444.16}, {'field': 'onlineCount', 'old_value': 972, 'new_value': 1046}]
2025-05-18 08:06:44,187 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-18 08:06:44,188 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88455.2, 'new_value': 96589.19}, {'field': 'dailyBillAmount', 'old_value': 88455.2, 'new_value': 96589.19}, {'field': 'amount', 'old_value': 18316.43, 'new_value': 20490.43}, {'field': 'count', 'old_value': 657, 'new_value': 714}, {'field': 'instoreAmount', 'old_value': 18975.52, 'new_value': 21149.52}, {'field': 'instoreCount', 'old_value': 657, 'new_value': 714}]
2025-05-18 08:06:44,764 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-18 08:06:44,765 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 75511.70999999999, 'new_value': 77263.08}, {'field': 'dailyBillAmount', 'old_value': 75511.70999999999, 'new_value': 77263.08}, {'field': 'amount', 'old_value': 63006.340000000004, 'new_value': 66728.55}, {'field': 'count', 'old_value': 3143, 'new_value': 3355}, {'field': 'instoreAmount', 'old_value': 14413.04, 'new_value': 15545.74}, {'field': 'instoreCount', 'old_value': 1023, 'new_value': 1105}, {'field': 'onlineAmount', 'old_value': 49559.54, 'new_value': 52161.55}, {'field': 'onlineCount', 'old_value': 2120, 'new_value': 2250}]
2025-05-18 08:06:45,210 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-18 08:06:45,210 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64870.3, 'new_value': 73056.87}, {'field': 'amount', 'old_value': 64869.49, 'new_value': 73056.06}, {'field': 'count', 'old_value': 1789, 'new_value': 1943}, {'field': 'instoreAmount', 'old_value': 62654.65, 'new_value': 70401.6}, {'field': 'instoreCount', 'old_value': 1744, 'new_value': 1893}, {'field': 'onlineAmount', 'old_value': 2859.69, 'new_value': 3502.29}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 50}]
2025-05-18 08:06:45,653 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-18 08:06:45,653 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39044.28, 'new_value': 49674.58}, {'field': 'dailyBillAmount', 'old_value': 39044.28, 'new_value': 49674.58}, {'field': 'amount', 'old_value': 96017.87, 'new_value': 104014.98}, {'field': 'count', 'old_value': 3406, 'new_value': 3778}, {'field': 'instoreAmount', 'old_value': 96733.89, 'new_value': 105275.94}, {'field': 'instoreCount', 'old_value': 3377, 'new_value': 3746}, {'field': 'onlineAmount', 'old_value': 1470.91, 'new_value': 1522.31}, {'field': 'onlineCount', 'old_value': 29, 'new_value': 32}]
2025-05-18 08:06:46,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-18 08:06:46,116 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121580.1, 'new_value': 129932.12}, {'field': 'dailyBillAmount', 'old_value': 121580.1, 'new_value': 129932.12}, {'field': 'amount', 'old_value': 79365.84, 'new_value': 84541.35}, {'field': 'count', 'old_value': 6947, 'new_value': 7289}, {'field': 'instoreAmount', 'old_value': 5352.91, 'new_value': 5936.91}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 310}, {'field': 'onlineAmount', 'old_value': 78297.34, 'new_value': 82985.96}, {'field': 'onlineCount', 'old_value': 6680, 'new_value': 6979}]
2025-05-18 08:06:46,576 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-18 08:06:46,576 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 107261.9, 'new_value': 115050.83}, {'field': 'dailyBillAmount', 'old_value': 107261.9, 'new_value': 115050.83}, {'field': 'amount', 'old_value': 90394.5, 'new_value': 97388.19}, {'field': 'count', 'old_value': 3120, 'new_value': 3337}, {'field': 'instoreAmount', 'old_value': 51731.5, 'new_value': 55914.84}, {'field': 'instoreCount', 'old_value': 2285, 'new_value': 2451}, {'field': 'onlineAmount', 'old_value': 45402.61, 'new_value': 48348.71}, {'field': 'onlineCount', 'old_value': 835, 'new_value': 886}]
2025-05-18 08:06:47,099 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-18 08:06:47,100 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34441.53, 'new_value': 62269.53}, {'field': 'amount', 'old_value': 34441.0, 'new_value': 62269.0}, {'field': 'count', 'old_value': 30, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 34441.53, 'new_value': 62269.53}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 36}]
2025-05-18 08:06:47,475 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-18 08:06:47,476 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36369.59, 'new_value': 39249.92}, {'field': 'dailyBillAmount', 'old_value': 36369.59, 'new_value': 39249.92}, {'field': 'amount', 'old_value': 51423.12, 'new_value': 54605.94}, {'field': 'count', 'old_value': 2010, 'new_value': 2130}, {'field': 'instoreAmount', 'old_value': 16663.63, 'new_value': 18037.57}, {'field': 'instoreCount', 'old_value': 727, 'new_value': 780}, {'field': 'onlineAmount', 'old_value': 35458.1, 'new_value': 37266.98}, {'field': 'onlineCount', 'old_value': 1283, 'new_value': 1350}]
2025-05-18 08:06:47,900 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-18 08:06:47,900 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63258.43, 'new_value': 70204.1}, {'field': 'dailyBillAmount', 'old_value': 63258.43, 'new_value': 70204.1}, {'field': 'amount', 'old_value': 65091.06, 'new_value': 72230.38}, {'field': 'count', 'old_value': 2324, 'new_value': 2575}, {'field': 'instoreAmount', 'old_value': 65091.06, 'new_value': 72230.38}, {'field': 'instoreCount', 'old_value': 2324, 'new_value': 2575}]
2025-05-18 08:06:48,368 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-18 08:06:48,368 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 225983.0, 'new_value': 240694.0}, {'field': 'dailyBillAmount', 'old_value': 225983.0, 'new_value': 240694.0}, {'field': 'amount', 'old_value': 234189.0, 'new_value': 249000.0}, {'field': 'count', 'old_value': 196, 'new_value': 208}, {'field': 'instoreAmount', 'old_value': 254689.0, 'new_value': 269500.0}, {'field': 'instoreCount', 'old_value': 196, 'new_value': 208}]
2025-05-18 08:06:48,822 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-18 08:06:48,822 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 170031.2, 'new_value': 187356.0}, {'field': 'dailyBillAmount', 'old_value': 170031.2, 'new_value': 187356.0}, {'field': 'amount', 'old_value': 180301.66, 'new_value': 191621.96}, {'field': 'count', 'old_value': 332, 'new_value': 356}, {'field': 'instoreAmount', 'old_value': 182217.36, 'new_value': 193537.66}, {'field': 'instoreCount', 'old_value': 332, 'new_value': 356}]
2025-05-18 08:06:49,271 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-18 08:06:49,272 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26013.0, 'new_value': 29584.0}, {'field': 'amount', 'old_value': 26013.0, 'new_value': 29584.0}, {'field': 'count', 'old_value': 54, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 26013.0, 'new_value': 29584.0}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 59}]
2025-05-18 08:06:49,749 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-18 08:06:49,750 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54252.0, 'new_value': 57762.0}, {'field': 'dailyBillAmount', 'old_value': 54252.0, 'new_value': 57762.0}, {'field': 'amount', 'old_value': 26656.0, 'new_value': 28814.0}, {'field': 'count', 'old_value': 72, 'new_value': 77}, {'field': 'instoreAmount', 'old_value': 27819.0, 'new_value': 29977.0}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 77}]
2025-05-18 08:06:50,264 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-18 08:06:50,264 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46993.0, 'new_value': 50311.0}, {'field': 'amount', 'old_value': 44738.0, 'new_value': 48056.0}, {'field': 'count', 'old_value': 56, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 44738.0, 'new_value': 48056.0}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 61}]
2025-05-18 08:06:50,718 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-18 08:06:50,718 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49133.7, 'new_value': 51589.5}, {'field': 'amount', 'old_value': 49131.5, 'new_value': 51587.3}, {'field': 'count', 'old_value': 133, 'new_value': 139}, {'field': 'instoreAmount', 'old_value': 49133.7, 'new_value': 51589.5}, {'field': 'instoreCount', 'old_value': 133, 'new_value': 139}]
2025-05-18 08:06:51,154 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-18 08:06:51,154 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 358627.0, 'new_value': 385124.0}, {'field': 'dailyBillAmount', 'old_value': 358627.0, 'new_value': 385124.0}, {'field': 'amount', 'old_value': 421056.0, 'new_value': 447752.0}, {'field': 'count', 'old_value': 53, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 421056.0, 'new_value': 447752.0}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 56}]
2025-05-18 08:06:51,608 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-18 08:06:51,608 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54699.0, 'new_value': 67208.0}, {'field': 'amount', 'old_value': 54699.0, 'new_value': 67208.0}, {'field': 'count', 'old_value': 14, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 54699.0, 'new_value': 67208.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 17}]
2025-05-18 08:06:52,065 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-18 08:06:52,066 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14862.0, 'new_value': 18958.0}, {'field': 'amount', 'old_value': 14862.0, 'new_value': 18958.0}, {'field': 'count', 'old_value': 23, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 14862.0, 'new_value': 18958.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 27}]
2025-05-18 08:06:52,426 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-18 08:06:52,427 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46182.0, 'new_value': 51474.0}, {'field': 'amount', 'old_value': 46182.0, 'new_value': 51474.0}, {'field': 'count', 'old_value': 55, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 46182.0, 'new_value': 51474.0}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 59}]
2025-05-18 08:06:52,861 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-18 08:06:52,861 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 142225.7, 'new_value': 158658.7}, {'field': 'dailyBillAmount', 'old_value': 142225.7, 'new_value': 158658.7}, {'field': 'amount', 'old_value': 230619.4, 'new_value': 250714.69999999998}, {'field': 'count', 'old_value': 293, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 237961.16, 'new_value': 258056.56}, {'field': 'instoreCount', 'old_value': 293, 'new_value': 314}]
2025-05-18 08:06:53,405 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-18 08:06:53,406 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74887.11, 'new_value': 78358.61}, {'field': 'dailyBillAmount', 'old_value': 74887.11, 'new_value': 78358.61}, {'field': 'amount', 'old_value': 9964.54, 'new_value': 13770.640000000001}, {'field': 'count', 'old_value': 95, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 9704.44, 'new_value': 13415.44}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 102}, {'field': 'onlineAmount', 'old_value': 2142.1, 'new_value': 2289.5}, {'field': 'onlineCount', 'old_value': 35, 'new_value': 39}]
2025-05-18 08:06:53,879 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-18 08:06:53,879 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9139.0, 'new_value': 9619.0}, {'field': 'amount', 'old_value': 9139.0, 'new_value': 9619.0}, {'field': 'count', 'old_value': 24, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 9139.0, 'new_value': 9619.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 25}]
2025-05-18 08:06:54,364 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-18 08:06:54,365 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23954.0, 'new_value': 25471.0}, {'field': 'dailyBillAmount', 'old_value': 23954.0, 'new_value': 25471.0}, {'field': 'amount', 'old_value': 27786.0, 'new_value': 29303.0}, {'field': 'count', 'old_value': 90, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 27786.0, 'new_value': 29303.0}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 95}]
2025-05-18 08:06:54,784 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-18 08:06:54,785 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23291.7, 'new_value': 24984.7}, {'field': 'amount', 'old_value': 23291.7, 'new_value': 24984.7}, {'field': 'count', 'old_value': 134, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 23629.7, 'new_value': 25322.7}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 142}]
2025-05-18 08:06:55,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-18 08:06:55,184 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 5230.0, 'new_value': 5779.0}, {'field': 'dailyBillAmount', 'old_value': 5230.0, 'new_value': 5779.0}, {'field': 'amount', 'old_value': 29615.0, 'new_value': 31109.0}, {'field': 'count', 'old_value': 89, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 30390.0, 'new_value': 31884.0}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 95}]
2025-05-18 08:06:55,594 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-18 08:06:55,595 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 540658.92, 'new_value': 588266.76}, {'field': 'dailyBillAmount', 'old_value': 540658.92, 'new_value': 588266.76}, {'field': 'amount', 'old_value': 35306.93, 'new_value': 37464.78}, {'field': 'count', 'old_value': 323, 'new_value': 351}, {'field': 'instoreAmount', 'old_value': 29304.329999999998, 'new_value': 30415.79}, {'field': 'instoreCount', 'old_value': 226, 'new_value': 243}, {'field': 'onlineAmount', 'old_value': 6815.13, 'new_value': 7941.41}, {'field': 'onlineCount', 'old_value': 97, 'new_value': 108}]
2025-05-18 08:06:56,068 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-18 08:06:56,068 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54715.0, 'new_value': 60083.0}, {'field': 'amount', 'old_value': 54515.0, 'new_value': 59883.0}, {'field': 'count', 'old_value': 66, 'new_value': 71}, {'field': 'instoreAmount', 'old_value': 55014.0, 'new_value': 60382.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 71}]
2025-05-18 08:06:56,534 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-18 08:06:56,534 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13102.0, 'new_value': 13585.0}, {'field': 'amount', 'old_value': 13102.0, 'new_value': 13585.0}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 13102.0, 'new_value': 13585.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-05-18 08:06:56,990 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-18 08:06:56,990 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15163.96, 'new_value': 15992.53}, {'field': 'amount', 'old_value': 15163.26, 'new_value': 15991.83}, {'field': 'count', 'old_value': 60, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 15163.96, 'new_value': 15992.53}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 65}]
2025-05-18 08:06:57,655 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-18 08:06:57,655 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30037.0, 'new_value': 34063.0}, {'field': 'dailyBillAmount', 'old_value': 30037.0, 'new_value': 34063.0}, {'field': 'amount', 'old_value': 30063.0, 'new_value': 34089.0}, {'field': 'count', 'old_value': 73, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 30513.0, 'new_value': 34539.0}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 79}]
2025-05-18 08:06:58,067 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-18 08:06:58,067 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 239799.41, 'new_value': 261244.53}, {'field': 'dailyBillAmount', 'old_value': 224018.52, 'new_value': 244791.82}, {'field': 'amount', 'old_value': 238022.88, 'new_value': 259468.0}, {'field': 'count', 'old_value': 462, 'new_value': 507}, {'field': 'instoreAmount', 'old_value': 240055.0, 'new_value': 261500.12}, {'field': 'instoreCount', 'old_value': 462, 'new_value': 507}]
2025-05-18 08:06:58,567 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-18 08:06:58,568 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48644.0, 'new_value': 52137.0}, {'field': 'amount', 'old_value': 48644.0, 'new_value': 52137.0}, {'field': 'count', 'old_value': 214, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 48702.0, 'new_value': 52891.0}, {'field': 'instoreCount', 'old_value': 214, 'new_value': 234}]
2025-05-18 08:06:59,018 - INFO - 更新表单数据成功: FINST-VRA66VA1RMZU72KJ6T3JJ8YBFM4G3QAM6RBAM032
2025-05-18 08:06:59,019 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13419.0, 'new_value': 19458.0}, {'field': 'amount', 'old_value': 13419.0, 'new_value': 19458.0}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 13419.0, 'new_value': 19458.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-05-18 08:06:59,502 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-18 08:06:59,502 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14144.5, 'new_value': 33062.6}, {'field': 'dailyBillAmount', 'old_value': 14144.5, 'new_value': 33062.6}, {'field': 'amount', 'old_value': 17407.02, 'new_value': 36325.119999999995}, {'field': 'count', 'old_value': 113, 'new_value': 214}, {'field': 'instoreAmount', 'old_value': 17407.02, 'new_value': 36325.119999999995}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 214}]
2025-05-18 08:06:59,927 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-18 08:06:59,927 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61502.97, 'new_value': 65943.58}, {'field': 'dailyBillAmount', 'old_value': 61502.97, 'new_value': 65943.58}, {'field': 'amount', 'old_value': 25340.87, 'new_value': 25947.46}, {'field': 'count', 'old_value': 2462, 'new_value': 2511}, {'field': 'instoreAmount', 'old_value': 27089.32, 'new_value': 27719.42}, {'field': 'instoreCount', 'old_value': 2462, 'new_value': 2511}]
2025-05-18 08:07:00,451 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-18 08:07:00,452 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 405896.0, 'new_value': 436567.91}, {'field': 'dailyBillAmount', 'old_value': 405896.0, 'new_value': 436567.91}, {'field': 'amount', 'old_value': 419195.08, 'new_value': 450928.35}, {'field': 'count', 'old_value': 3808, 'new_value': 4132}, {'field': 'instoreAmount', 'old_value': 322110.19, 'new_value': 347102.60000000003}, {'field': 'instoreCount', 'old_value': 1522, 'new_value': 1660}, {'field': 'onlineAmount', 'old_value': 100172.04, 'new_value': 107615.78}, {'field': 'onlineCount', 'old_value': 2286, 'new_value': 2472}]
2025-05-18 08:07:00,890 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-18 08:07:00,890 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124017.66, 'new_value': 135345.96}, {'field': 'amount', 'old_value': 124017.66, 'new_value': 135345.96}, {'field': 'count', 'old_value': 819, 'new_value': 899}, {'field': 'instoreAmount', 'old_value': 124126.66, 'new_value': 135454.96}, {'field': 'instoreCount', 'old_value': 819, 'new_value': 899}]
2025-05-18 08:07:01,359 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-18 08:07:01,359 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60086.27, 'new_value': 65703.72}, {'field': 'dailyBillAmount', 'old_value': 60086.27, 'new_value': 65703.72}, {'field': 'amount', 'old_value': 70771.64, 'new_value': 76913.83}, {'field': 'count', 'old_value': 3088, 'new_value': 3430}, {'field': 'instoreAmount', 'old_value': 36306.909999999996, 'new_value': 40046.19}, {'field': 'instoreCount', 'old_value': 1798, 'new_value': 2030}, {'field': 'onlineAmount', 'old_value': 35108.54, 'new_value': 37526.36}, {'field': 'onlineCount', 'old_value': 1290, 'new_value': 1400}]
2025-05-18 08:07:01,886 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-18 08:07:01,886 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1767.0, 'new_value': 1911.0}, {'field': 'amount', 'old_value': 1767.0, 'new_value': 1911.0}, {'field': 'count', 'old_value': 93, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 1767.0, 'new_value': 1911.0}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 100}]
2025-05-18 08:07:02,387 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-18 08:07:02,388 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28647.0, 'new_value': 37914.0}, {'field': 'amount', 'old_value': 28647.0, 'new_value': 37914.0}, {'field': 'count', 'old_value': 16, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 28647.0, 'new_value': 37914.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 19}]
2025-05-18 08:07:02,845 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-18 08:07:02,846 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85252.39, 'new_value': 90446.95999999999}, {'field': 'dailyBillAmount', 'old_value': 85252.39, 'new_value': 90446.95999999999}, {'field': 'amount', 'old_value': 39807.88, 'new_value': 42609.41}, {'field': 'count', 'old_value': 2541, 'new_value': 2702}, {'field': 'instoreAmount', 'old_value': 6077.8, 'new_value': 6576.4}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 268}, {'field': 'onlineAmount', 'old_value': 33730.08, 'new_value': 36033.01}, {'field': 'onlineCount', 'old_value': 2292, 'new_value': 2434}]
2025-05-18 08:07:03,253 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-18 08:07:03,254 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 220245.93, 'new_value': 238788.32}, {'field': 'dailyBillAmount', 'old_value': 220245.93, 'new_value': 238788.32}, {'field': 'amount', 'old_value': 207466.86, 'new_value': 223826.46}, {'field': 'count', 'old_value': 1725, 'new_value': 1892}, {'field': 'instoreAmount', 'old_value': 153550.25, 'new_value': 165583.95}, {'field': 'instoreCount', 'old_value': 740, 'new_value': 799}, {'field': 'onlineAmount', 'old_value': 53916.81, 'new_value': 58243.51}, {'field': 'onlineCount', 'old_value': 985, 'new_value': 1093}]
2025-05-18 08:07:03,702 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-18 08:07:03,702 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 253693.25, 'new_value': 276930.98}, {'field': 'dailyBillAmount', 'old_value': 253693.25, 'new_value': 276930.98}, {'field': 'amount', 'old_value': 260172.19999999998, 'new_value': 283919.21}, {'field': 'count', 'old_value': 1554, 'new_value': 1698}, {'field': 'instoreAmount', 'old_value': 236946.3, 'new_value': 258801.11000000002}, {'field': 'instoreCount', 'old_value': 1306, 'new_value': 1432}, {'field': 'onlineAmount', 'old_value': 27726.100000000002, 'new_value': 29942.3}, {'field': 'onlineCount', 'old_value': 248, 'new_value': 266}]
2025-05-18 08:07:04,099 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-18 08:07:04,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 651004.44, 'new_value': 709107.96}, {'field': 'dailyBillAmount', 'old_value': 651004.44, 'new_value': 709107.96}, {'field': 'amount', 'old_value': 719696.86, 'new_value': 780191.37}, {'field': 'count', 'old_value': 3745, 'new_value': 4093}, {'field': 'instoreAmount', 'old_value': 544420.58, 'new_value': 591540.49}, {'field': 'instoreCount', 'old_value': 2132, 'new_value': 2324}, {'field': 'onlineAmount', 'old_value': 180051.24, 'new_value': 194300.84}, {'field': 'onlineCount', 'old_value': 1613, 'new_value': 1769}]
2025-05-18 08:07:04,525 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-18 08:07:04,525 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194347.56, 'new_value': 214219.52}, {'field': 'dailyBillAmount', 'old_value': 194347.56, 'new_value': 214219.52}, {'field': 'amount', 'old_value': 272931.51, 'new_value': 302070.91}, {'field': 'count', 'old_value': 1318, 'new_value': 1447}, {'field': 'instoreAmount', 'old_value': 256276.32, 'new_value': 283759.12}, {'field': 'instoreCount', 'old_value': 1045, 'new_value': 1156}, {'field': 'onlineAmount', 'old_value': 16859.99, 'new_value': 18516.59}, {'field': 'onlineCount', 'old_value': 273, 'new_value': 291}]
2025-05-18 08:07:04,995 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-18 08:07:04,996 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 256886.66, 'new_value': 277380.53}, {'field': 'dailyBillAmount', 'old_value': 256886.66, 'new_value': 277380.53}, {'field': 'amount', 'old_value': 242901.3, 'new_value': 262311.2}, {'field': 'count', 'old_value': 1065, 'new_value': 1147}, {'field': 'instoreAmount', 'old_value': 246799.0, 'new_value': 266542.9}, {'field': 'instoreCount', 'old_value': 1065, 'new_value': 1147}]
2025-05-18 08:07:05,455 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-18 08:07:05,456 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 522978.84, 'new_value': 565455.44}, {'field': 'amount', 'old_value': 522978.84, 'new_value': 565455.44}, {'field': 'count', 'old_value': 4078, 'new_value': 4398}, {'field': 'instoreAmount', 'old_value': 522978.84, 'new_value': 565455.44}, {'field': 'instoreCount', 'old_value': 4078, 'new_value': 4398}]
2025-05-18 08:07:05,907 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-18 08:07:05,908 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 395710.58, 'new_value': 429109.37}, {'field': 'dailyBillAmount', 'old_value': 395710.58, 'new_value': 429109.37}, {'field': 'amount', 'old_value': 507854.37, 'new_value': 547067.29}, {'field': 'count', 'old_value': 3477, 'new_value': 3751}, {'field': 'instoreAmount', 'old_value': 279415.6, 'new_value': 302759.0}, {'field': 'instoreCount', 'old_value': 1438, 'new_value': 1567}, {'field': 'onlineAmount', 'old_value': 234810.9, 'new_value': 251708.3}, {'field': 'onlineCount', 'old_value': 2039, 'new_value': 2184}]
2025-05-18 08:07:06,485 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-18 08:07:06,485 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 249686.13, 'new_value': 278757.77}, {'field': 'dailyBillAmount', 'old_value': 249686.13, 'new_value': 278757.77}, {'field': 'amount', 'old_value': 321252.25, 'new_value': 349186.39}, {'field': 'count', 'old_value': 3406, 'new_value': 3695}, {'field': 'instoreAmount', 'old_value': 224693.82, 'new_value': 246187.62}, {'field': 'instoreCount', 'old_value': 1498, 'new_value': 1650}, {'field': 'onlineAmount', 'old_value': 97656.47, 'new_value': 104188.24}, {'field': 'onlineCount', 'old_value': 1908, 'new_value': 2045}]
2025-05-18 08:07:06,901 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-18 08:07:06,902 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 330579.52, 'new_value': 357277.44}, {'field': 'dailyBillAmount', 'old_value': 330579.52, 'new_value': 357277.44}, {'field': 'amount', 'old_value': 334940.68, 'new_value': 362341.68}, {'field': 'count', 'old_value': 3157, 'new_value': 3398}, {'field': 'instoreAmount', 'old_value': 291555.9, 'new_value': 316296.46}, {'field': 'instoreCount', 'old_value': 1587, 'new_value': 1739}, {'field': 'onlineAmount', 'old_value': 44250.34, 'new_value': 46931.68}, {'field': 'onlineCount', 'old_value': 1570, 'new_value': 1659}]
2025-05-18 08:07:07,312 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-18 08:07:07,312 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90790.8, 'new_value': 96473.8}, {'field': 'amount', 'old_value': 90790.3, 'new_value': 96473.3}, {'field': 'count', 'old_value': 397, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 90790.8, 'new_value': 96473.8}, {'field': 'instoreCount', 'old_value': 397, 'new_value': 427}]
2025-05-18 08:07:07,876 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-18 08:07:07,877 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 242525.79, 'new_value': 266858.09}, {'field': 'dailyBillAmount', 'old_value': 242525.79, 'new_value': 266858.09}, {'field': 'amount', 'old_value': -172493.98, 'new_value': -196654.08}, {'field': 'count', 'old_value': 679, 'new_value': 707}, {'field': 'instoreAmount', 'old_value': 4827.6, 'new_value': 5149.5}, {'field': 'instoreCount', 'old_value': 210, 'new_value': 225}, {'field': 'onlineAmount', 'old_value': 14379.42, 'new_value': 14917.42}, {'field': 'onlineCount', 'old_value': 469, 'new_value': 482}]
2025-05-18 08:07:08,296 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-18 08:07:08,296 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 401430.81, 'new_value': 433990.39}, {'field': 'dailyBillAmount', 'old_value': 401430.81, 'new_value': 433990.39}, {'field': 'amount', 'old_value': 319193.82, 'new_value': 343219.54}, {'field': 'count', 'old_value': 1299, 'new_value': 1406}, {'field': 'instoreAmount', 'old_value': 319193.82, 'new_value': 343219.54}, {'field': 'instoreCount', 'old_value': 1299, 'new_value': 1406}]
2025-05-18 08:07:08,719 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-18 08:07:08,719 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'amount', 'old_value': 110227.9, 'new_value': 119199.0}, {'field': 'count', 'old_value': 463, 'new_value': 500}, {'field': 'instoreAmount', 'old_value': 115316.6, 'new_value': 124287.7}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 484}]
2025-05-18 08:07:09,138 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-18 08:07:09,139 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204032.44, 'new_value': 223936.54}, {'field': 'dailyBillAmount', 'old_value': 204032.44, 'new_value': 223936.54}, {'field': 'amount', 'old_value': 198692.16, 'new_value': 217829.82}, {'field': 'count', 'old_value': 1289, 'new_value': 1414}, {'field': 'instoreAmount', 'old_value': 187935.74, 'new_value': 205797.73}, {'field': 'instoreCount', 'old_value': 1009, 'new_value': 1103}, {'field': 'onlineAmount', 'old_value': 10899.18, 'new_value': 12174.85}, {'field': 'onlineCount', 'old_value': 280, 'new_value': 311}]
2025-05-18 08:07:09,589 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-18 08:07:09,590 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221577.72, 'new_value': 237328.91999999998}, {'field': 'dailyBillAmount', 'old_value': 221577.72, 'new_value': 237328.91999999998}, {'field': 'amount', 'old_value': 97888.57, 'new_value': 104382.18}, {'field': 'count', 'old_value': 1471, 'new_value': 1612}, {'field': 'instoreAmount', 'old_value': 57760.42, 'new_value': 61672.15}, {'field': 'instoreCount', 'old_value': 412, 'new_value': 448}, {'field': 'onlineAmount', 'old_value': 40130.07, 'new_value': 42711.95}, {'field': 'onlineCount', 'old_value': 1059, 'new_value': 1164}]
2025-05-18 08:07:10,037 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-18 08:07:10,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 43024.0, 'new_value': 47154.0}, {'field': 'count', 'old_value': 24, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 43024.0, 'new_value': 47154.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 26}]
2025-05-18 08:07:10,511 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-18 08:07:10,511 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88634.07, 'new_value': 95307.12}, {'field': 'amount', 'old_value': 88627.85, 'new_value': 95300.62}, {'field': 'count', 'old_value': 3951, 'new_value': 4263}, {'field': 'instoreAmount', 'old_value': 33904.71, 'new_value': 36218.39}, {'field': 'instoreCount', 'old_value': 1319, 'new_value': 1423}, {'field': 'onlineAmount', 'old_value': 58844.520000000004, 'new_value': 63349.590000000004}, {'field': 'onlineCount', 'old_value': 2632, 'new_value': 2840}]
2025-05-18 08:07:10,997 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-18 08:07:10,998 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29765.9, 'new_value': 32657.9}, {'field': 'amount', 'old_value': 29765.9, 'new_value': 32657.9}, {'field': 'count', 'old_value': 133, 'new_value': 149}, {'field': 'instoreAmount', 'old_value': 29765.9, 'new_value': 32657.9}, {'field': 'instoreCount', 'old_value': 133, 'new_value': 149}]
2025-05-18 08:07:11,477 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-18 08:07:11,478 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 261257.53, 'new_value': 284150.24}, {'field': 'dailyBillAmount', 'old_value': 261257.53, 'new_value': 284150.24}, {'field': 'amount', 'old_value': 101926.0, 'new_value': 111537.0}, {'field': 'count', 'old_value': 1894, 'new_value': 2076}, {'field': 'instoreAmount', 'old_value': 102893.1, 'new_value': 112538.8}, {'field': 'instoreCount', 'old_value': 1894, 'new_value': 2076}]
2025-05-18 08:07:11,955 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-18 08:07:11,956 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104193.44, 'new_value': 116725.28}, {'field': 'amount', 'old_value': 104193.44, 'new_value': 116725.28}, {'field': 'count', 'old_value': 2480, 'new_value': 2762}, {'field': 'instoreAmount', 'old_value': 104193.44, 'new_value': 116725.28}, {'field': 'instoreCount', 'old_value': 2480, 'new_value': 2762}]
2025-05-18 08:07:12,377 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-18 08:07:12,378 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18073.69, 'new_value': 19620.95}, {'field': 'amount', 'old_value': 18072.15, 'new_value': 19618.64}, {'field': 'count', 'old_value': 1027, 'new_value': 1145}, {'field': 'instoreAmount', 'old_value': 10386.66, 'new_value': 11136.59}, {'field': 'instoreCount', 'old_value': 511, 'new_value': 563}, {'field': 'onlineAmount', 'old_value': 8106.98, 'new_value': 8918.11}, {'field': 'onlineCount', 'old_value': 516, 'new_value': 582}]
2025-05-18 08:07:12,780 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-18 08:07:12,781 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29821.5, 'new_value': 31882.7}, {'field': 'amount', 'old_value': 29821.5, 'new_value': 31882.7}, {'field': 'count', 'old_value': 74, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 29821.5, 'new_value': 31882.7}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 81}]
2025-05-18 08:07:13,234 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG81
2025-05-18 08:07:13,234 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48115.66, 'new_value': 53897.26}, {'field': 'dailyBillAmount', 'old_value': 48115.66, 'new_value': 53897.26}, {'field': 'amount', 'old_value': 55465.23, 'new_value': 61064.23}, {'field': 'count', 'old_value': 2141, 'new_value': 2360}, {'field': 'instoreAmount', 'old_value': 55781.13, 'new_value': 61393.13}, {'field': 'instoreCount', 'old_value': 2141, 'new_value': 2360}]
2025-05-18 08:07:13,613 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-18 08:07:13,613 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117090.59, 'new_value': 128782.67}, {'field': 'dailyBillAmount', 'old_value': 97970.5, 'new_value': 107919.1}, {'field': 'amount', 'old_value': 117089.91, 'new_value': 128781.99}, {'field': 'count', 'old_value': 1666, 'new_value': 1812}, {'field': 'instoreAmount', 'old_value': 112891.3, 'new_value': 124119.9}, {'field': 'instoreCount', 'old_value': 1466, 'new_value': 1592}, {'field': 'onlineAmount', 'old_value': 4386.49, 'new_value': 4871.89}, {'field': 'onlineCount', 'old_value': 200, 'new_value': 220}]
2025-05-18 08:07:14,104 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-18 08:07:14,105 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17967.6, 'new_value': 19847.32}, {'field': 'amount', 'old_value': 17966.8, 'new_value': 19846.52}, {'field': 'count', 'old_value': 763, 'new_value': 843}, {'field': 'instoreAmount', 'old_value': 15298.5, 'new_value': 16668.12}, {'field': 'instoreCount', 'old_value': 689, 'new_value': 756}, {'field': 'onlineAmount', 'old_value': 2709.3, 'new_value': 3219.4}, {'field': 'onlineCount', 'old_value': 74, 'new_value': 87}]
2025-05-18 08:07:14,578 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-18 08:07:14,578 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247139.73, 'new_value': 272399.36}, {'field': 'dailyBillAmount', 'old_value': 247139.73, 'new_value': 272399.36}, {'field': 'amount', 'old_value': 316867.45, 'new_value': 348266.7}, {'field': 'count', 'old_value': 3274, 'new_value': 3550}, {'field': 'instoreAmount', 'old_value': 299387.04, 'new_value': 328953.64}, {'field': 'instoreCount', 'old_value': 2260, 'new_value': 2467}, {'field': 'onlineAmount', 'old_value': 25301.38, 'new_value': 27201.08}, {'field': 'onlineCount', 'old_value': 1014, 'new_value': 1083}]
2025-05-18 08:07:15,037 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-18 08:07:15,037 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 100464.0, 'new_value': 109933.11}, {'field': 'dailyBillAmount', 'old_value': 100464.0, 'new_value': 109933.11}, {'field': 'amount', 'old_value': 24766.99, 'new_value': 26335.96}, {'field': 'count', 'old_value': 401, 'new_value': 428}, {'field': 'instoreAmount', 'old_value': 15189.53, 'new_value': 16363.73}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 221}, {'field': 'onlineAmount', 'old_value': 10436.55, 'new_value': 10835.56}, {'field': 'onlineCount', 'old_value': 195, 'new_value': 207}]
2025-05-18 08:07:15,490 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-18 08:07:15,490 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99816.68, 'new_value': 106095.1}, {'field': 'dailyBillAmount', 'old_value': 87288.19, 'new_value': 93504.01}, {'field': 'amount', 'old_value': 99815.22, 'new_value': 106093.64}, {'field': 'count', 'old_value': 5698, 'new_value': 6035}, {'field': 'instoreAmount', 'old_value': 61904.67, 'new_value': 66342.70999999999}, {'field': 'instoreCount', 'old_value': 3506, 'new_value': 3729}, {'field': 'onlineAmount', 'old_value': 39259.78, 'new_value': 41217.659999999996}, {'field': 'onlineCount', 'old_value': 2192, 'new_value': 2306}]
2025-05-18 08:07:15,889 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-18 08:07:15,889 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51941.35, 'new_value': 56522.409999999996}, {'field': 'amount', 'old_value': 51940.090000000004, 'new_value': 56520.93}, {'field': 'count', 'old_value': 3124, 'new_value': 3459}, {'field': 'instoreAmount', 'old_value': 25098.93, 'new_value': 27261.54}, {'field': 'instoreCount', 'old_value': 1393, 'new_value': 1549}, {'field': 'onlineAmount', 'old_value': 28011.31, 'new_value': 30553.85}, {'field': 'onlineCount', 'old_value': 1731, 'new_value': 1910}]
2025-05-18 08:07:16,292 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-18 08:07:16,293 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 8174.0, 'new_value': 13958.0}, {'field': 'dailyBillAmount', 'old_value': 8174.0, 'new_value': 13958.0}, {'field': 'amount', 'old_value': 97940.56, 'new_value': 107556.58}, {'field': 'count', 'old_value': 968, 'new_value': 1059}, {'field': 'instoreAmount', 'old_value': 98055.06, 'new_value': 107671.08}, {'field': 'instoreCount', 'old_value': 968, 'new_value': 1059}]
2025-05-18 08:07:16,750 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-18 08:07:16,750 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85547.47, 'new_value': 92051.56}, {'field': 'dailyBillAmount', 'old_value': 88285.71, 'new_value': 94911.01}, {'field': 'amount', 'old_value': 85546.59, 'new_value': 92050.68}, {'field': 'count', 'old_value': 1655, 'new_value': 1779}, {'field': 'instoreAmount', 'old_value': 82059.76, 'new_value': 88386.63}, {'field': 'instoreCount', 'old_value': 1400, 'new_value': 1507}, {'field': 'onlineAmount', 'old_value': 3560.07, 'new_value': 3737.29}, {'field': 'onlineCount', 'old_value': 255, 'new_value': 272}]
2025-05-18 08:07:17,180 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-18 08:07:17,181 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 125532.17, 'new_value': 131252.46}, {'field': 'dailyBillAmount', 'old_value': 125532.17, 'new_value': 131252.46}, {'field': 'amount', 'old_value': 16304.02, 'new_value': 16875.65}, {'field': 'count', 'old_value': 636, 'new_value': 662}, {'field': 'instoreAmount', 'old_value': 19033.8, 'new_value': 19684.83}, {'field': 'instoreCount', 'old_value': 636, 'new_value': 662}]
2025-05-18 08:07:17,622 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-18 08:07:17,622 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 313274.29, 'new_value': 344291.45}, {'field': 'dailyBillAmount', 'old_value': 313274.29, 'new_value': 344291.45}, {'field': 'amount', 'old_value': 29730.9, 'new_value': 34456.56}, {'field': 'count', 'old_value': 146, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 29911.7, 'new_value': 34682.36}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 168}]
2025-05-18 08:07:18,085 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-18 08:07:18,085 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 11575.02, 'new_value': 12047.88}, {'field': 'count', 'old_value': 594, 'new_value': 620}, {'field': 'onlineAmount', 'old_value': 11661.28, 'new_value': 12134.14}, {'field': 'onlineCount', 'old_value': 594, 'new_value': 620}]
2025-05-18 08:07:18,567 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-18 08:07:18,567 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 170438.02, 'new_value': 187514.56}, {'field': 'amount', 'old_value': 170284.24, 'new_value': 187360.78}, {'field': 'count', 'old_value': 1856, 'new_value': 1989}, {'field': 'instoreAmount', 'old_value': 160918.2, 'new_value': 177613.9}, {'field': 'instoreCount', 'old_value': 1560, 'new_value': 1681}, {'field': 'onlineAmount', 'old_value': 11669.26, 'new_value': 12216.1}, {'field': 'onlineCount', 'old_value': 296, 'new_value': 308}]
2025-05-18 08:07:18,981 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-18 08:07:18,982 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115858.97, 'new_value': 121853.68}, {'field': 'dailyBillAmount', 'old_value': 112058.5, 'new_value': 118053.20999999999}, {'field': 'amount', 'old_value': 93500.22, 'new_value': 97430.29}, {'field': 'count', 'old_value': 3419, 'new_value': 3549}, {'field': 'instoreAmount', 'old_value': 44362.97, 'new_value': 45576.78}, {'field': 'instoreCount', 'old_value': 1570, 'new_value': 1615}, {'field': 'onlineAmount', 'old_value': 50165.62, 'new_value': 52964.55}, {'field': 'onlineCount', 'old_value': 1849, 'new_value': 1934}]
2025-05-18 08:07:19,387 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-18 08:07:19,387 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33123.049999999996, 'new_value': 36745.0}, {'field': 'dailyBillAmount', 'old_value': 33123.049999999996, 'new_value': 36745.0}, {'field': 'amount', 'old_value': 1737.3, 'new_value': 2057.25}, {'field': 'count', 'old_value': 81, 'new_value': 94}, {'field': 'instoreAmount', 'old_value': 1737.3, 'new_value': 2057.25}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 94}]
2025-05-18 08:07:19,862 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-18 08:07:19,863 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 4100.64, 'new_value': 4257.09}, {'field': 'count', 'old_value': 177, 'new_value': 184}, {'field': 'onlineAmount', 'old_value': 4100.64, 'new_value': 4257.09}, {'field': 'onlineCount', 'old_value': 177, 'new_value': 184}]
2025-05-18 08:07:20,278 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-18 08:07:20,279 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68886.15, 'new_value': 74051.56}, {'field': 'dailyBillAmount', 'old_value': 35698.22, 'new_value': 38614.72}, {'field': 'amount', 'old_value': 68886.15, 'new_value': 74050.97}, {'field': 'count', 'old_value': 1691, 'new_value': 1815}, {'field': 'instoreAmount', 'old_value': 37899.95, 'new_value': 40891.75}, {'field': 'instoreCount', 'old_value': 916, 'new_value': 986}, {'field': 'onlineAmount', 'old_value': 32623.98, 'new_value': 35001.45}, {'field': 'onlineCount', 'old_value': 775, 'new_value': 829}]
2025-05-18 08:07:20,744 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-18 08:07:20,745 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24019.18, 'new_value': 26282.53}, {'field': 'amount', 'old_value': 24019.18, 'new_value': 26282.53}, {'field': 'count', 'old_value': 886, 'new_value': 977}, {'field': 'instoreAmount', 'old_value': 24271.18, 'new_value': 26647.98}, {'field': 'instoreCount', 'old_value': 886, 'new_value': 977}]
2025-05-18 08:07:21,195 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-18 08:07:21,196 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33547.0, 'new_value': 35296.3}, {'field': 'dailyBillAmount', 'old_value': 33547.0, 'new_value': 35296.3}, {'field': 'amount', 'old_value': 28705.31, 'new_value': 29667.68}, {'field': 'count', 'old_value': 1238, 'new_value': 1291}, {'field': 'instoreAmount', 'old_value': 17534.11, 'new_value': 17981.81}, {'field': 'instoreCount', 'old_value': 592, 'new_value': 610}, {'field': 'onlineAmount', 'old_value': 11196.31, 'new_value': 11710.98}, {'field': 'onlineCount', 'old_value': 646, 'new_value': 681}]
2025-05-18 08:07:21,660 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-18 08:07:21,661 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56856.55, 'new_value': 60367.05}, {'field': 'amount', 'old_value': 56856.55, 'new_value': 60367.05}, {'field': 'count', 'old_value': 1675, 'new_value': 1775}, {'field': 'instoreAmount', 'old_value': 23076.18, 'new_value': 23969.89}, {'field': 'instoreCount', 'old_value': 808, 'new_value': 849}, {'field': 'onlineAmount', 'old_value': 33813.37, 'new_value': 36430.16}, {'field': 'onlineCount', 'old_value': 867, 'new_value': 926}]
2025-05-18 08:07:22,162 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-18 08:07:22,163 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34009.39, 'new_value': 35780.26}, {'field': 'amount', 'old_value': 34008.49, 'new_value': 35779.36}, {'field': 'count', 'old_value': 788, 'new_value': 831}, {'field': 'instoreAmount', 'old_value': 27381.0, 'new_value': 28728.2}, {'field': 'instoreCount', 'old_value': 638, 'new_value': 673}, {'field': 'onlineAmount', 'old_value': 6960.92, 'new_value': 7384.59}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 158}]
2025-05-18 08:07:22,584 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-18 08:07:22,585 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 159179.31, 'new_value': 166698.13}, {'field': 'dailyBillAmount', 'old_value': 159179.31, 'new_value': 166698.13}, {'field': 'amount', 'old_value': 104876.81999999999, 'new_value': 109594.88}, {'field': 'count', 'old_value': 2609, 'new_value': 2722}, {'field': 'instoreAmount', 'old_value': 67359.32, 'new_value': 70167.42}, {'field': 'instoreCount', 'old_value': 1324, 'new_value': 1375}, {'field': 'onlineAmount', 'old_value': 45759.28, 'new_value': 48091.24}, {'field': 'onlineCount', 'old_value': 1285, 'new_value': 1347}]
2025-05-18 08:07:22,979 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-18 08:07:22,979 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 463860.85, 'new_value': 506987.32}, {'field': 'dailyBillAmount', 'old_value': 463860.85, 'new_value': 506987.32}, {'field': 'amount', 'old_value': 430852.8, 'new_value': 472309.7}, {'field': 'count', 'old_value': 2526, 'new_value': 2780}, {'field': 'instoreAmount', 'old_value': 316466.1, 'new_value': 344951.0}, {'field': 'instoreCount', 'old_value': 1996, 'new_value': 2189}, {'field': 'onlineAmount', 'old_value': 114388.6, 'new_value': 127360.8}, {'field': 'onlineCount', 'old_value': 530, 'new_value': 591}]
2025-05-18 08:07:23,347 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-18 08:07:23,347 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 689511.63, 'new_value': 740187.73}, {'field': 'amount', 'old_value': 689511.13, 'new_value': 740187.23}, {'field': 'count', 'old_value': 2383, 'new_value': 2565}, {'field': 'instoreAmount', 'old_value': 689511.63, 'new_value': 740187.73}, {'field': 'instoreCount', 'old_value': 2383, 'new_value': 2565}]
2025-05-18 08:07:23,775 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-18 08:07:23,775 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 404428.18, 'new_value': 430353.46}, {'field': 'dailyBillAmount', 'old_value': 361155.64, 'new_value': 383906.55}, {'field': 'amount', 'old_value': 404428.18, 'new_value': 430353.46}, {'field': 'count', 'old_value': 2454, 'new_value': 2597}, {'field': 'instoreAmount', 'old_value': 368479.65, 'new_value': 392691.39}, {'field': 'instoreCount', 'old_value': 1560, 'new_value': 1670}, {'field': 'onlineAmount', 'old_value': 36165.18, 'new_value': 37878.72}, {'field': 'onlineCount', 'old_value': 894, 'new_value': 927}]
2025-05-18 08:07:24,288 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-18 08:07:24,289 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 419447.18, 'new_value': 440372.85000000003}, {'field': 'dailyBillAmount', 'old_value': 400240.36, 'new_value': 419455.03}, {'field': 'amount', 'old_value': 419447.18, 'new_value': 440372.85000000003}, {'field': 'count', 'old_value': 936, 'new_value': 1007}, {'field': 'instoreAmount', 'old_value': 394988.7, 'new_value': 413946.6}, {'field': 'instoreCount', 'old_value': 734, 'new_value': 784}, {'field': 'onlineAmount', 'old_value': 24585.760000000002, 'new_value': 26553.53}, {'field': 'onlineCount', 'old_value': 202, 'new_value': 223}]
2025-05-18 08:07:24,700 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-18 08:07:24,701 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 460318.83999999997, 'new_value': 500290.26}, {'field': 'amount', 'old_value': 460318.84, 'new_value': 500290.26}, {'field': 'count', 'old_value': 2252, 'new_value': 2480}, {'field': 'instoreAmount', 'old_value': 436372.79, 'new_value': 474571.79}, {'field': 'instoreCount', 'old_value': 1575, 'new_value': 1746}, {'field': 'onlineAmount', 'old_value': 23991.62, 'new_value': 25764.04}, {'field': 'onlineCount', 'old_value': 677, 'new_value': 734}]
2025-05-18 08:07:25,118 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-18 08:07:25,118 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 548102.41, 'new_value': 588964.16}, {'field': 'dailyBillAmount', 'old_value': 548102.41, 'new_value': 588964.16}, {'field': 'amount', 'old_value': 495620.54, 'new_value': 532699.07}, {'field': 'count', 'old_value': 2406, 'new_value': 2612}, {'field': 'instoreAmount', 'old_value': 457215.88, 'new_value': 489218.45999999996}, {'field': 'instoreCount', 'old_value': 2016, 'new_value': 2179}, {'field': 'onlineAmount', 'old_value': 38721.94, 'new_value': 43797.89}, {'field': 'onlineCount', 'old_value': 390, 'new_value': 433}]
2025-05-18 08:07:25,518 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-18 08:07:25,518 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116686.44, 'new_value': 124672.24}, {'field': 'dailyBillAmount', 'old_value': 115279.89, 'new_value': 123265.69}, {'field': 'amount', 'old_value': 114526.86, 'new_value': 122395.66}, {'field': 'count', 'old_value': 188, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 114526.86, 'new_value': 122395.66}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 200}]
2025-05-18 08:07:25,949 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-18 08:07:25,949 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 103205.28, 'new_value': 112156.46}, {'field': 'dailyBillAmount', 'old_value': 103205.28, 'new_value': 112156.46}, {'field': 'amount', 'old_value': 86632.52, 'new_value': 96047.12}, {'field': 'count', 'old_value': 155, 'new_value': 167}, {'field': 'instoreAmount', 'old_value': 84711.2, 'new_value': 93792.0}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 154}, {'field': 'onlineAmount', 'old_value': 1922.03, 'new_value': 2255.83}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 13}]
2025-05-18 08:07:26,408 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-18 08:07:26,408 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14864.63, 'new_value': 16496.14}, {'field': 'amount', 'old_value': 14864.63, 'new_value': 16496.14}, {'field': 'count', 'old_value': 305, 'new_value': 328}, {'field': 'instoreAmount', 'old_value': 14864.63, 'new_value': 16496.14}, {'field': 'instoreCount', 'old_value': 305, 'new_value': 328}]
2025-05-18 08:07:26,840 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-18 08:07:26,841 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62205.95, 'new_value': 66787.63}, {'field': 'amount', 'old_value': 62205.95, 'new_value': 66787.63}, {'field': 'count', 'old_value': 518, 'new_value': 556}, {'field': 'instoreAmount', 'old_value': 62390.51, 'new_value': 66972.19}, {'field': 'instoreCount', 'old_value': 518, 'new_value': 556}]
2025-05-18 08:07:27,376 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-18 08:07:27,377 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 191796.65, 'new_value': 207356.06}, {'field': 'dailyBillAmount', 'old_value': 191796.65, 'new_value': 207356.06}, {'field': 'amount', 'old_value': 210659.19, 'new_value': 226494.97}, {'field': 'count', 'old_value': 5518, 'new_value': 5969}, {'field': 'instoreAmount', 'old_value': 199933.82, 'new_value': 215043.95}, {'field': 'instoreCount', 'old_value': 4988, 'new_value': 5405}, {'field': 'onlineAmount', 'old_value': 14213.91, 'new_value': 15059.31}, {'field': 'onlineCount', 'old_value': 530, 'new_value': 564}]
2025-05-18 08:07:27,796 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-18 08:07:27,796 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55950.86, 'new_value': 59089.38}, {'field': 'dailyBillAmount', 'old_value': 55950.86, 'new_value': 59089.38}, {'field': 'amount', 'old_value': 56549.86, 'new_value': 59688.38}, {'field': 'count', 'old_value': 52, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 56549.86, 'new_value': 59688.38}, {'field': 'instoreCount', 'old_value': 52, 'new_value': 55}]
2025-05-18 08:07:28,199 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-18 08:07:28,200 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 536742.82, 'new_value': 569009.49}, {'field': 'dailyBillAmount', 'old_value': 536742.82, 'new_value': 569009.49}, {'field': 'amount', 'old_value': 490204.55, 'new_value': 516679.35}, {'field': 'count', 'old_value': 1245, 'new_value': 1321}, {'field': 'instoreAmount', 'old_value': 509311.26, 'new_value': 538538.21}, {'field': 'instoreCount', 'old_value': 1034, 'new_value': 1100}, {'field': 'onlineAmount', 'old_value': 4637.82, 'new_value': 4942.69}, {'field': 'onlineCount', 'old_value': 211, 'new_value': 221}]
2025-05-18 08:07:28,654 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-18 08:07:28,654 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 796959.15, 'new_value': 855007.7}, {'field': 'amount', 'old_value': 796959.15, 'new_value': 855007.7}, {'field': 'count', 'old_value': 2566, 'new_value': 2747}, {'field': 'instoreAmount', 'old_value': 798170.15, 'new_value': 856218.7}, {'field': 'instoreCount', 'old_value': 2566, 'new_value': 2747}]
2025-05-18 08:07:29,144 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-18 08:07:29,145 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 595413.47, 'new_value': 631847.61}, {'field': 'dailyBillAmount', 'old_value': 595413.47, 'new_value': 631847.61}, {'field': 'amount', 'old_value': 479024.47, 'new_value': 503132.76}, {'field': 'count', 'old_value': 1779, 'new_value': 1869}, {'field': 'instoreAmount', 'old_value': 466615.42, 'new_value': 490958.02}, {'field': 'instoreCount', 'old_value': 1051, 'new_value': 1114}, {'field': 'onlineAmount', 'old_value': 21843.32, 'new_value': 22779.72}, {'field': 'onlineCount', 'old_value': 728, 'new_value': 755}]
2025-05-18 08:07:29,533 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-18 08:07:29,534 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1206442.29, 'new_value': 1278330.29}, {'field': 'dailyBillAmount', 'old_value': 1206442.29, 'new_value': 1278330.29}, {'field': 'amount', 'old_value': 1249110.0, 'new_value': 1322476.0}, {'field': 'count', 'old_value': 3567, 'new_value': 3734}, {'field': 'instoreAmount', 'old_value': 1249110.0, 'new_value': 1322476.0}, {'field': 'instoreCount', 'old_value': 3567, 'new_value': 3734}]
2025-05-18 08:07:29,966 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-18 08:07:29,967 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 186845.64, 'new_value': 200861.26}, {'field': 'dailyBillAmount', 'old_value': 186845.64, 'new_value': 200861.26}, {'field': 'amount', 'old_value': 184898.27, 'new_value': 198864.13}, {'field': 'count', 'old_value': 999, 'new_value': 1077}, {'field': 'instoreAmount', 'old_value': 178522.8, 'new_value': 191702.8}, {'field': 'instoreCount', 'old_value': 834, 'new_value': 901}, {'field': 'onlineAmount', 'old_value': 9883.84, 'new_value': 10736.21}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 176}]
2025-05-18 08:07:30,475 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-18 08:07:30,476 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 623236.66, 'new_value': 667285.03}, {'field': 'dailyBillAmount', 'old_value': 623236.66, 'new_value': 667285.03}, {'field': 'amount', 'old_value': 670158.39, 'new_value': 714215.56}, {'field': 'count', 'old_value': 2743, 'new_value': 2934}, {'field': 'instoreAmount', 'old_value': 670158.84, 'new_value': 714216.01}, {'field': 'instoreCount', 'old_value': 2743, 'new_value': 2934}]
2025-05-18 08:07:30,932 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-18 08:07:30,933 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 264552.14, 'new_value': 279810.04}, {'field': 'dailyBillAmount', 'old_value': 264552.14, 'new_value': 279810.04}, {'field': 'amount', 'old_value': 433989.3, 'new_value': 463334.3}, {'field': 'count', 'old_value': 728, 'new_value': 778}, {'field': 'instoreAmount', 'old_value': 430935.78, 'new_value': 460280.78}, {'field': 'instoreCount', 'old_value': 703, 'new_value': 753}]
2025-05-18 08:07:31,381 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-18 08:07:31,381 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 153164.5, 'new_value': 172550.84}, {'field': 'dailyBillAmount', 'old_value': 153164.5, 'new_value': 172550.84}, {'field': 'amount', 'old_value': 182388.3, 'new_value': 203485.3}, {'field': 'count', 'old_value': 1279, 'new_value': 1416}, {'field': 'instoreAmount', 'old_value': 185103.3, 'new_value': 206691.3}, {'field': 'instoreCount', 'old_value': 1279, 'new_value': 1416}]
2025-05-18 08:07:31,843 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-18 08:07:31,843 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88892.57, 'new_value': 100805.79}, {'field': 'dailyBillAmount', 'old_value': 88892.57, 'new_value': 100805.79}, {'field': 'amount', 'old_value': 66488.45999999999, 'new_value': 76414.45999999999}, {'field': 'count', 'old_value': 427, 'new_value': 492}, {'field': 'instoreAmount', 'old_value': 65777.0, 'new_value': 75561.0}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 448}, {'field': 'onlineAmount', 'old_value': 1894.46, 'new_value': 2036.46}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 44}]
2025-05-18 08:07:32,309 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-18 08:07:32,309 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26982.8, 'new_value': 28202.8}, {'field': 'dailyBillAmount', 'old_value': 26982.8, 'new_value': 28202.8}]
2025-05-18 08:07:32,794 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-18 08:07:32,794 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 100082.5, 'new_value': 107333.76}, {'field': 'count', 'old_value': 4738, 'new_value': 5099}, {'field': 'instoreAmount', 'old_value': 53663.74, 'new_value': 57653.31}, {'field': 'instoreCount', 'old_value': 2740, 'new_value': 2947}, {'field': 'onlineAmount', 'old_value': 49370.83, 'new_value': 52825.13}, {'field': 'onlineCount', 'old_value': 1998, 'new_value': 2152}]
2025-05-18 08:07:33,275 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-18 08:07:33,276 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 134784.27, 'new_value': 146413.15}, {'field': 'amount', 'old_value': 134780.16, 'new_value': 146407.84}, {'field': 'count', 'old_value': 2483, 'new_value': 2702}, {'field': 'instoreAmount', 'old_value': 128213.38, 'new_value': 139374.61}, {'field': 'instoreCount', 'old_value': 2356, 'new_value': 2563}, {'field': 'onlineAmount', 'old_value': 6570.89, 'new_value': 7038.54}, {'field': 'onlineCount', 'old_value': 127, 'new_value': 139}]
2025-05-18 08:07:33,785 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-18 08:07:33,786 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19474.0, 'new_value': 20602.2}, {'field': 'amount', 'old_value': 19474.0, 'new_value': 20602.2}, {'field': 'count', 'old_value': 136, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 19474.0, 'new_value': 20602.2}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 146}]
2025-05-18 08:07:34,212 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-18 08:07:34,212 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9385.3, 'new_value': 19304.2}, {'field': 'dailyBillAmount', 'old_value': 9385.3, 'new_value': 19304.2}, {'field': 'amount', 'old_value': 32846.1, 'new_value': 37154.6}, {'field': 'count', 'old_value': 297, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 33065.4, 'new_value': 37374.8}, {'field': 'instoreCount', 'old_value': 297, 'new_value': 331}]
2025-05-18 08:07:34,632 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-18 08:07:34,632 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31721.0, 'new_value': 38606.0}, {'field': 'dailyBillAmount', 'old_value': 31721.0, 'new_value': 38606.0}]
2025-05-18 08:07:35,140 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-18 08:07:35,141 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93934.5, 'new_value': 107105.3}, {'field': 'dailyBillAmount', 'old_value': 93934.5, 'new_value': 107105.3}, {'field': 'amount', 'old_value': 105316.87, 'new_value': 115069.93}, {'field': 'count', 'old_value': 3220, 'new_value': 3475}, {'field': 'instoreAmount', 'old_value': 102251.37, 'new_value': 111747.81}, {'field': 'instoreCount', 'old_value': 3090, 'new_value': 3337}, {'field': 'onlineAmount', 'old_value': 4883.82, 'new_value': 5166.22}, {'field': 'onlineCount', 'old_value': 130, 'new_value': 138}]
2025-05-18 08:07:35,652 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-18 08:07:35,653 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31496.8, 'new_value': 33312.5}, {'field': 'dailyBillAmount', 'old_value': 31496.8, 'new_value': 33312.5}, {'field': 'amount', 'old_value': 31254.4, 'new_value': 33070.1}, {'field': 'count', 'old_value': 178, 'new_value': 189}, {'field': 'instoreAmount', 'old_value': 33039.6, 'new_value': 34855.3}, {'field': 'instoreCount', 'old_value': 178, 'new_value': 189}]
2025-05-18 08:07:36,113 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-18 08:07:36,113 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44024.13, 'new_value': 46802.58}, {'field': 'dailyBillAmount', 'old_value': 44024.13, 'new_value': 46802.58}]
2025-05-18 08:07:36,589 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-18 08:07:36,589 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31797.54, 'new_value': 33527.57}, {'field': 'amount', 'old_value': 31797.54, 'new_value': 33527.57}, {'field': 'count', 'old_value': 1816, 'new_value': 1921}, {'field': 'instoreAmount', 'old_value': 32332.59, 'new_value': 34112.93}, {'field': 'instoreCount', 'old_value': 1816, 'new_value': 1921}]
2025-05-18 08:07:37,052 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-18 08:07:37,053 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50426.01, 'new_value': 53571.78}, {'field': 'dailyBillAmount', 'old_value': 50426.01, 'new_value': 53571.78}, {'field': 'amount', 'old_value': 51921.93, 'new_value': 55196.98}, {'field': 'count', 'old_value': 2526, 'new_value': 2678}, {'field': 'instoreAmount', 'old_value': 48415.4, 'new_value': 51392.6}, {'field': 'instoreCount', 'old_value': 2377, 'new_value': 2517}, {'field': 'onlineAmount', 'old_value': 3571.7799999999997, 'new_value': 3869.63}, {'field': 'onlineCount', 'old_value': 149, 'new_value': 161}]
2025-05-18 08:07:37,492 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-18 08:07:37,492 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35538.9, 'new_value': 37591.89}, {'field': 'amount', 'old_value': 35538.9, 'new_value': 37591.89}, {'field': 'count', 'old_value': 1720, 'new_value': 1811}, {'field': 'instoreAmount', 'old_value': 22558.19, 'new_value': 23737.89}, {'field': 'instoreCount', 'old_value': 1155, 'new_value': 1212}, {'field': 'onlineAmount', 'old_value': 13041.71, 'new_value': 13915.0}, {'field': 'onlineCount', 'old_value': 565, 'new_value': 599}]
2025-05-18 08:07:37,931 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-18 08:07:37,931 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25232.98, 'new_value': 26520.54}, {'field': 'dailyBillAmount', 'old_value': 25232.98, 'new_value': 26520.54}, {'field': 'amount', 'old_value': 17987.93, 'new_value': 18915.79}, {'field': 'count', 'old_value': 727, 'new_value': 763}, {'field': 'instoreAmount', 'old_value': 18177.33, 'new_value': 19105.19}, {'field': 'instoreCount', 'old_value': 727, 'new_value': 763}]
2025-05-18 08:07:38,397 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-18 08:07:38,397 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46459.05, 'new_value': 49557.43}, {'field': 'amount', 'old_value': 46454.08, 'new_value': 49552.42}, {'field': 'count', 'old_value': 2867, 'new_value': 3032}, {'field': 'instoreAmount', 'old_value': 11525.19, 'new_value': 12695.95}, {'field': 'instoreCount', 'old_value': 726, 'new_value': 788}, {'field': 'onlineAmount', 'old_value': 35934.86, 'new_value': 37903.479999999996}, {'field': 'onlineCount', 'old_value': 2141, 'new_value': 2244}]
2025-05-18 08:07:38,813 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-18 08:07:38,814 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83109.42, 'new_value': 88112.51}, {'field': 'dailyBillAmount', 'old_value': 83109.42, 'new_value': 88112.51}, {'field': 'amount', 'old_value': 69297.24, 'new_value': 73579.54}, {'field': 'count', 'old_value': 688, 'new_value': 730}, {'field': 'instoreAmount', 'old_value': 69297.24, 'new_value': 73579.54}, {'field': 'instoreCount', 'old_value': 688, 'new_value': 730}]
2025-05-18 08:07:39,282 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-18 08:07:39,283 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64452.89, 'new_value': 72634.49}, {'field': 'dailyBillAmount', 'old_value': 64452.89, 'new_value': 72634.49}, {'field': 'amount', 'old_value': 70556.8, 'new_value': 80624.8}, {'field': 'count', 'old_value': 298, 'new_value': 342}, {'field': 'instoreAmount', 'old_value': 70556.8, 'new_value': 80624.8}, {'field': 'instoreCount', 'old_value': 298, 'new_value': 342}]
2025-05-18 08:07:39,711 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-18 08:07:39,711 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 40232.31, 'new_value': 42941.65}, {'field': 'count', 'old_value': 212, 'new_value': 226}, {'field': 'instoreAmount', 'old_value': 41502.31, 'new_value': 44378.65}, {'field': 'instoreCount', 'old_value': 212, 'new_value': 226}]
2025-05-18 08:07:40,201 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-18 08:07:40,201 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87003.0, 'new_value': 92117.0}, {'field': 'amount', 'old_value': 87003.0, 'new_value': 92117.0}, {'field': 'count', 'old_value': 910, 'new_value': 961}, {'field': 'instoreAmount', 'old_value': 87003.0, 'new_value': 92117.0}, {'field': 'instoreCount', 'old_value': 910, 'new_value': 961}]
2025-05-18 08:07:40,698 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-18 08:07:40,699 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18989.33, 'new_value': 19497.33}, {'field': 'dailyBillAmount', 'old_value': 18989.33, 'new_value': 19497.33}, {'field': 'amount', 'old_value': 2005.6299999999999, 'new_value': 2166.13}, {'field': 'count', 'old_value': 115, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 2418.31, 'new_value': 2578.81}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 120}]
2025-05-18 08:07:41,265 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-18 08:07:41,266 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15294.76, 'new_value': 16085.59}, {'field': 'dailyBillAmount', 'old_value': 15294.76, 'new_value': 16085.59}, {'field': 'amount', 'old_value': 16044.69, 'new_value': 16721.59}, {'field': 'count', 'old_value': 426, 'new_value': 449}, {'field': 'instoreAmount', 'old_value': 16086.83, 'new_value': 16763.73}, {'field': 'instoreCount', 'old_value': 425, 'new_value': 448}]
2025-05-18 08:07:41,693 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-18 08:07:41,693 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33948.2, 'new_value': 35410.1}, {'field': 'dailyBillAmount', 'old_value': 33948.2, 'new_value': 35410.1}, {'field': 'amount', 'old_value': 45912.6, 'new_value': 48096.6}, {'field': 'count', 'old_value': 181, 'new_value': 190}, {'field': 'instoreAmount', 'old_value': 46164.6, 'new_value': 48348.6}, {'field': 'instoreCount', 'old_value': 181, 'new_value': 190}]
2025-05-18 08:07:42,151 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-18 08:07:42,151 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30943.0, 'new_value': 32134.0}, {'field': 'count', 'old_value': 157, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 30957.0, 'new_value': 32148.0}, {'field': 'instoreCount', 'old_value': 157, 'new_value': 164}]
2025-05-18 08:07:42,628 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-18 08:07:42,628 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49930.03, 'new_value': 53504.57}, {'field': 'dailyBillAmount', 'old_value': 49930.03, 'new_value': 53504.57}, {'field': 'amount', 'old_value': 44004.78, 'new_value': 47199.4}, {'field': 'count', 'old_value': 1495, 'new_value': 1595}, {'field': 'instoreAmount', 'old_value': 40319.92, 'new_value': 43293.54}, {'field': 'instoreCount', 'old_value': 1324, 'new_value': 1416}, {'field': 'onlineAmount', 'old_value': 3721.2999999999997, 'new_value': 3942.2999999999997}, {'field': 'onlineCount', 'old_value': 171, 'new_value': 179}]
2025-05-18 08:07:43,080 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-18 08:07:43,081 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15258.9, 'new_value': 20740.5}, {'field': 'dailyBillAmount', 'old_value': 15258.9, 'new_value': 20740.5}, {'field': 'amount', 'old_value': 21477.81, 'new_value': 24406.01}, {'field': 'count', 'old_value': 129, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 21542.41, 'new_value': 24471.21}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 142}]
2025-05-18 08:07:43,538 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-18 08:07:43,538 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132996.78, 'new_value': 141422.93}, {'field': 'dailyBillAmount', 'old_value': 132996.78, 'new_value': 141422.93}, {'field': 'amount', 'old_value': 139504.9, 'new_value': 148278.6}, {'field': 'count', 'old_value': 963, 'new_value': 1026}, {'field': 'instoreAmount', 'old_value': 133877.7, 'new_value': 142066.7}, {'field': 'instoreCount', 'old_value': 860, 'new_value': 914}, {'field': 'onlineAmount', 'old_value': 6602.2, 'new_value': 7202.9}, {'field': 'onlineCount', 'old_value': 103, 'new_value': 112}]
2025-05-18 08:07:43,539 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-18 08:07:43,539 - INFO - 正在批量插入月度数据，批次 1/1，共 1 条记录
2025-05-18 08:07:43,680 - INFO - 批量插入月度数据成功，批次 1，1 条记录
2025-05-18 08:07:46,681 - INFO - 批量插入月度数据完成: 总计 1 条，成功 1 条，失败 0 条
2025-05-18 08:07:46,682 - INFO - 批量插入月销售数据完成，共 1 条记录
2025-05-18 08:07:46,682 - INFO - 月销售数据同步完成！更新: 214 条，插入: 1 条，错误: 0 条，跳过: 974 条
2025-05-18 08:07:46,682 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-18 08:07:47,162 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250518.xlsx
2025-05-18 08:07:47,162 - INFO - 综合数据同步流程完成！
2025-05-18 08:07:47,214 - INFO - 综合数据同步完成
2025-05-18 08:07:47,214 - INFO - ==================================================
2025-05-18 08:07:47,215 - INFO - 程序退出
2025-05-18 08:07:47,215 - INFO - ==================================================
