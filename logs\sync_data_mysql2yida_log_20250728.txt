2025-07-28 01:30:33,813 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 01:30:33,813 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 01:30:33,813 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 01:30:33,969 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 2 条记录
2025-07-28 01:30:33,969 - INFO - 获取到 1 个日期需要处理: ['2025-07-27']
2025-07-28 01:30:33,969 - INFO - 开始处理日期: 2025-07-27
2025-07-28 01:30:33,969 - INFO - Request Parameters - Page 1:
2025-07-28 01:30:33,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 01:30:33,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 01:30:41,859 - INFO - Response - Page 1:
2025-07-28 01:30:41,859 - INFO - 第 1 页获取到 49 条记录
2025-07-28 01:30:42,375 - INFO - 查询完成，共获取到 49 条记录
2025-07-28 01:30:42,375 - INFO - 获取到 49 条表单数据
2025-07-28 01:30:42,375 - INFO - 当前日期 2025-07-27 有 2 条MySQL数据需要处理
2025-07-28 01:30:42,375 - INFO - 开始批量插入 2 条新记录
2025-07-28 01:30:42,516 - INFO - 批量插入响应状态码: 200
2025-07-28 01:30:42,516 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 17:30:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F95D5675-4651-7CB2-92EC-DF952EF376D7', 'x-acs-trace-id': '512e49c798ccd860a402b967df1dcda1', 'etag': '15kTx8c0PcviRzBea2H5b4w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 01:30:42,516 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D1GJHX3IBHAIW0RBOM23FB22BEGYLDM5O1', 'FINST-QZE668D1GJHX3IBHAIW0RBOM23FB22BEGYLDM6O1']}
2025-07-28 01:30:42,516 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-28 01:30:42,516 - INFO - 成功插入的数据ID: ['FINST-QZE668D1GJHX3IBHAIW0RBOM23FB22BEGYLDM5O1', 'FINST-QZE668D1GJHX3IBHAIW0RBOM23FB22BEGYLDM6O1']
2025-07-28 01:30:47,531 - INFO - 批量插入完成，共 2 条记录
2025-07-28 01:30:47,531 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-28 01:30:47,531 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-07-28 01:31:47,546 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 01:31:47,546 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 01:31:47,546 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 01:31:47,687 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 83 条记录
2025-07-28 01:31:47,687 - INFO - 获取到 1 个日期需要处理: ['2025-07-27']
2025-07-28 01:31:47,687 - INFO - 开始处理日期: 2025-07-27
2025-07-28 01:31:47,687 - INFO - Request Parameters - Page 1:
2025-07-28 01:31:47,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 01:31:47,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 01:31:48,500 - INFO - Response - Page 1:
2025-07-28 01:31:48,500 - INFO - 第 1 页获取到 50 条记录
2025-07-28 01:31:49,015 - INFO - Request Parameters - Page 2:
2025-07-28 01:31:49,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 01:31:49,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 01:31:57,124 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 44C67D71-AB50-7401-8055-C9C402EF438F Response: {'code': 'ServiceUnavailable', 'requestid': '44C67D71-AB50-7401-8055-C9C402EF438F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 44C67D71-AB50-7401-8055-C9C402EF438F)
2025-07-28 01:31:57,124 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-28 01:31:57,124 - INFO - 同步完成
2025-07-28 04:30:33,817 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 04:30:33,817 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 04:30:33,817 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 04:30:33,973 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 2 条记录
2025-07-28 04:30:33,973 - INFO - 获取到 1 个日期需要处理: ['2025-07-27']
2025-07-28 04:30:33,973 - INFO - 开始处理日期: 2025-07-27
2025-07-28 04:30:33,973 - INFO - Request Parameters - Page 1:
2025-07-28 04:30:33,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 04:30:33,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 04:30:42,083 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 424CAF15-2006-7B32-BB48-42D51ECC32A0 Response: {'code': 'ServiceUnavailable', 'requestid': '424CAF15-2006-7B32-BB48-42D51ECC32A0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 424CAF15-2006-7B32-BB48-42D51ECC32A0)
2025-07-28 04:30:42,083 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-28 04:31:42,098 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 04:31:42,098 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 04:31:42,098 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 04:31:42,238 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 83 条记录
2025-07-28 04:31:42,238 - INFO - 获取到 1 个日期需要处理: ['2025-07-27']
2025-07-28 04:31:42,254 - INFO - 开始处理日期: 2025-07-27
2025-07-28 04:31:42,254 - INFO - Request Parameters - Page 1:
2025-07-28 04:31:42,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 04:31:42,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 04:31:49,879 - INFO - Response - Page 1:
2025-07-28 04:31:49,879 - INFO - 第 1 页获取到 50 条记录
2025-07-28 04:31:50,395 - INFO - Request Parameters - Page 2:
2025-07-28 04:31:50,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 04:31:50,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 04:31:50,926 - INFO - Response - Page 2:
2025-07-28 04:31:50,926 - INFO - 第 2 页获取到 1 条记录
2025-07-28 04:31:51,441 - INFO - 查询完成，共获取到 51 条记录
2025-07-28 04:31:51,441 - INFO - 获取到 51 条表单数据
2025-07-28 04:31:51,441 - INFO - 当前日期 2025-07-27 有 83 条MySQL数据需要处理
2025-07-28 04:31:51,441 - INFO - 开始批量插入 32 条新记录
2025-07-28 04:31:51,691 - INFO - 批量插入响应状态码: 200
2025-07-28 04:31:51,691 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 20:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1580', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3FC3EF1B-C97B-7437-8FD5-ED7E0F808D02', 'x-acs-trace-id': 'd0cf82dfc598352e52961a34c2cf6b3d', 'etag': '1ELCC16sXkI24oFgPpboHRw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 04:31:51,691 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71FJHXNDSLABHBZAF0V8NF272DX4MDMKH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF272DX4MDMLH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF272DX4MDMMH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMNH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMOH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMPH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMQH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMRH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMSH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMTH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMUH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMVH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMWH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMXH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMYH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMZH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM0I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM1I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM2I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM3I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM4I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM5I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM6I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM7I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM8I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM9I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMAI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMBI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMCI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMDI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMEI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMFI1']}
2025-07-28 04:31:51,691 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-07-28 04:31:51,691 - INFO - 成功插入的数据ID: ['FINST-00D66K71FJHXNDSLABHBZAF0V8NF272DX4MDMKH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF272DX4MDMLH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF272DX4MDMMH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMNH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMOH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMPH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMQH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMRH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMSH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMTH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMUH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMVH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMWH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMXH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMYH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMZH1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM0I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM1I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM2I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM3I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM4I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM5I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM6I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM7I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM8I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM9I1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMAI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMBI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMCI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMDI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMEI1', 'FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDMFI1']
2025-07-28 04:31:56,707 - INFO - 批量插入完成，共 32 条记录
2025-07-28 04:31:56,707 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 32 条，错误: 0 条
2025-07-28 04:31:56,707 - INFO - 数据同步完成！更新: 0 条，插入: 32 条，错误: 0 条
2025-07-28 04:31:56,707 - INFO - 同步完成
2025-07-28 07:30:33,837 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 07:30:33,837 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 07:30:33,837 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 07:30:33,993 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 6 条记录
2025-07-28 07:30:33,993 - INFO - 获取到 1 个日期需要处理: ['2025-07-27']
2025-07-28 07:30:33,993 - INFO - 开始处理日期: 2025-07-27
2025-07-28 07:30:34,009 - INFO - Request Parameters - Page 1:
2025-07-28 07:30:34,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 07:30:34,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 07:30:42,118 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C627FC5F-1891-7E78-A45F-64D73F1FD66E Response: {'code': 'ServiceUnavailable', 'requestid': 'C627FC5F-1891-7E78-A45F-64D73F1FD66E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C627FC5F-1891-7E78-A45F-64D73F1FD66E)
2025-07-28 07:30:42,118 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-28 07:31:42,133 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 07:31:42,133 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 07:31:42,133 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 07:31:42,274 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 106 条记录
2025-07-28 07:31:42,274 - INFO - 获取到 1 个日期需要处理: ['2025-07-27']
2025-07-28 07:31:42,274 - INFO - 开始处理日期: 2025-07-27
2025-07-28 07:31:42,274 - INFO - Request Parameters - Page 1:
2025-07-28 07:31:42,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 07:31:42,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 07:31:43,008 - INFO - Response - Page 1:
2025-07-28 07:31:43,008 - INFO - 第 1 页获取到 50 条记录
2025-07-28 07:31:43,508 - INFO - Request Parameters - Page 2:
2025-07-28 07:31:43,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 07:31:43,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 07:31:44,211 - INFO - Response - Page 2:
2025-07-28 07:31:44,211 - INFO - 第 2 页获取到 33 条记录
2025-07-28 07:31:44,711 - INFO - 查询完成，共获取到 83 条记录
2025-07-28 07:31:44,711 - INFO - 获取到 83 条表单数据
2025-07-28 07:31:44,711 - INFO - 当前日期 2025-07-27 有 106 条MySQL数据需要处理
2025-07-28 07:31:44,711 - INFO - 开始批量插入 23 条新记录
2025-07-28 07:31:44,930 - INFO - 批量插入响应状态码: 200
2025-07-28 07:31:44,930 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 23:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1139', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D9B5E76C-D85B-7EB3-91B9-327C8F4CEFDD', 'x-acs-trace-id': '64735641a2740f3aa601662079922cfb', 'etag': '1NOl7RrSOpnwPZqV3Bp8JQg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 07:31:44,930 - INFO - 批量插入响应体: {'result': ['FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM142', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM242', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM342', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM442', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM542', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM642', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM742', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM842', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM942', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMA42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMB42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMC42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMD42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDME42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMF42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMG42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMH42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMI42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMJ42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDMK42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDML42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDMM42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDMN42']}
2025-07-28 07:31:44,930 - INFO - 批量插入表单数据成功，批次 1，共 23 条记录
2025-07-28 07:31:44,930 - INFO - 成功插入的数据ID: ['FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM142', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM242', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM342', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM442', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM542', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM642', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM742', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM842', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDM942', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMA42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMB42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMC42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMD42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDME42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMF42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMG42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMH42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMI42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62W8PCBMDMJ42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDMK42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDML42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDMM42', 'FINST-G1F66CA11NGXKBC8EQ9XUA4EZPH62X8PCBMDMN42']
2025-07-28 07:31:49,946 - INFO - 批量插入完成，共 23 条记录
2025-07-28 07:31:49,946 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 23 条，错误: 0 条
2025-07-28 07:31:49,946 - INFO - 数据同步完成！更新: 0 条，插入: 23 条，错误: 0 条
2025-07-28 07:31:49,946 - INFO - 同步完成
2025-07-28 10:30:34,126 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 10:30:34,126 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 10:30:34,126 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 10:30:34,298 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 133 条记录
2025-07-28 10:30:34,298 - INFO - 获取到 5 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28']
2025-07-28 10:30:34,298 - INFO - 开始处理日期: 2025-07-24
2025-07-28 10:30:34,298 - INFO - Request Parameters - Page 1:
2025-07-28 10:30:34,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:34,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:42,423 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6BC60F35-9B04-7B5A-A48F-D518E46F43C5 Response: {'code': 'ServiceUnavailable', 'requestid': '6BC60F35-9B04-7B5A-A48F-D518E46F43C5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6BC60F35-9B04-7B5A-A48F-D518E46F43C5)
2025-07-28 10:30:42,423 - INFO - 开始处理日期: 2025-07-25
2025-07-28 10:30:42,423 - INFO - Request Parameters - Page 1:
2025-07-28 10:30:42,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:42,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:50,532 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F60F1FEB-B188-7EEE-800B-2AC11731C152 Response: {'code': 'ServiceUnavailable', 'requestid': 'F60F1FEB-B188-7EEE-800B-2AC11731C152', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F60F1FEB-B188-7EEE-800B-2AC11731C152)
2025-07-28 10:30:50,532 - INFO - 开始处理日期: 2025-07-26
2025-07-28 10:30:50,532 - INFO - Request Parameters - Page 1:
2025-07-28 10:30:50,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:50,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:51,298 - INFO - Response - Page 1:
2025-07-28 10:30:51,298 - INFO - 第 1 页获取到 50 条记录
2025-07-28 10:30:51,814 - INFO - Request Parameters - Page 2:
2025-07-28 10:30:51,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:51,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:52,548 - INFO - Response - Page 2:
2025-07-28 10:30:52,548 - INFO - 第 2 页获取到 50 条记录
2025-07-28 10:30:53,064 - INFO - Request Parameters - Page 3:
2025-07-28 10:30:53,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:53,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:53,798 - INFO - Response - Page 3:
2025-07-28 10:30:53,798 - INFO - 第 3 页获取到 50 条记录
2025-07-28 10:30:54,314 - INFO - Request Parameters - Page 4:
2025-07-28 10:30:54,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:54,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:55,048 - INFO - Response - Page 4:
2025-07-28 10:30:55,048 - INFO - 第 4 页获取到 50 条记录
2025-07-28 10:30:55,548 - INFO - Request Parameters - Page 5:
2025-07-28 10:30:55,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:55,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:56,267 - INFO - Response - Page 5:
2025-07-28 10:30:56,267 - INFO - 第 5 页获取到 50 条记录
2025-07-28 10:30:56,767 - INFO - Request Parameters - Page 6:
2025-07-28 10:30:56,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:56,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:57,548 - INFO - Response - Page 6:
2025-07-28 10:30:57,548 - INFO - 第 6 页获取到 50 条记录
2025-07-28 10:30:58,063 - INFO - Request Parameters - Page 7:
2025-07-28 10:30:58,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:58,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:30:58,782 - INFO - Response - Page 7:
2025-07-28 10:30:58,782 - INFO - 第 7 页获取到 50 条记录
2025-07-28 10:30:59,282 - INFO - Request Parameters - Page 8:
2025-07-28 10:30:59,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:30:59,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:00,063 - INFO - Response - Page 8:
2025-07-28 10:31:00,063 - INFO - 第 8 页获取到 50 条记录
2025-07-28 10:31:00,563 - INFO - Request Parameters - Page 9:
2025-07-28 10:31:00,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:31:00,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:01,329 - INFO - Response - Page 9:
2025-07-28 10:31:01,329 - INFO - 第 9 页获取到 50 条记录
2025-07-28 10:31:01,829 - INFO - Request Parameters - Page 10:
2025-07-28 10:31:01,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:31:01,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:02,423 - INFO - Response - Page 10:
2025-07-28 10:31:02,423 - INFO - 第 10 页获取到 12 条记录
2025-07-28 10:31:02,923 - INFO - 查询完成，共获取到 462 条记录
2025-07-28 10:31:02,923 - INFO - 获取到 462 条表单数据
2025-07-28 10:31:02,923 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-28 10:31:02,923 - INFO - 开始批量插入 1 条新记录
2025-07-28 10:31:03,079 - INFO - 批量插入响应状态码: 200
2025-07-28 10:31:03,079 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C9A8A9B6-E4B3-7760-9940-11A0A6235EEF', 'x-acs-trace-id': '353e16984992e84ba8a64d9d2197a246', 'etag': '5uvMLznA6FYTGjOJCV9kBXw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:31:03,079 - INFO - 批量插入响应体: {'result': ['FINST-B1D66U6197JXBC98FCUV6ASZSKZ526CARHMDMO']}
2025-07-28 10:31:03,079 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-28 10:31:03,079 - INFO - 成功插入的数据ID: ['FINST-B1D66U6197JXBC98FCUV6ASZSKZ526CARHMDMO']
2025-07-28 10:31:08,095 - INFO - 批量插入完成，共 1 条记录
2025-07-28 10:31:08,095 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-28 10:31:08,095 - INFO - 开始处理日期: 2025-07-27
2025-07-28 10:31:08,095 - INFO - Request Parameters - Page 1:
2025-07-28 10:31:08,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:31:08,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:08,829 - INFO - Response - Page 1:
2025-07-28 10:31:08,829 - INFO - 第 1 页获取到 50 条记录
2025-07-28 10:31:09,345 - INFO - Request Parameters - Page 2:
2025-07-28 10:31:09,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:31:09,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:10,032 - INFO - Response - Page 2:
2025-07-28 10:31:10,032 - INFO - 第 2 页获取到 50 条记录
2025-07-28 10:31:10,532 - INFO - Request Parameters - Page 3:
2025-07-28 10:31:10,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:31:10,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:11,048 - INFO - Response - Page 3:
2025-07-28 10:31:11,048 - INFO - 第 3 页获取到 6 条记录
2025-07-28 10:31:11,563 - INFO - 查询完成，共获取到 106 条记录
2025-07-28 10:31:11,563 - INFO - 获取到 106 条表单数据
2025-07-28 10:31:11,563 - INFO - 当前日期 2025-07-27 有 124 条MySQL数据需要处理
2025-07-28 10:31:11,563 - INFO - 开始批量插入 118 条新记录
2025-07-28 10:31:11,845 - INFO - 批量插入响应状态码: 200
2025-07-28 10:31:11,845 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:31:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2395', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6FDBF6A2-B03F-7746-9EF6-1FFFA6531E9E', 'x-acs-trace-id': '2dd98006782524c2b20d32fe1060c646', 'etag': '2ka3iojda7/WDcKU4g/Zpvw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:31:11,845 - INFO - 批量插入响应体: {'result': ['FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMJ', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMK', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDML', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMM', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMN', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMO', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMP', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMQ', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMR', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMS', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMT', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMU', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMV', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMW', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMX', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMY', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMZ', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM01', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM11', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM21', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM31', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM41', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM51', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM61', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM71', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM81', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM91', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMA1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMB1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMC1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMD1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDME1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMF1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMG1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMH1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMI1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMJ1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMK1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDML1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMM1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMN1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMO1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMP1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMQ1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMR1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMS1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMT1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMU1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMV1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMW1']}
2025-07-28 10:31:11,845 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-28 10:31:11,845 - INFO - 成功插入的数据ID: ['FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMJ', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMK', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDML', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMM', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMN', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMO', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMP', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMQ', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMR', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMS', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMT', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMU', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMV', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMW', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMX', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMY', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMZ', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM01', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM11', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM21', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM31', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM41', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM51', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM61', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM71', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM81', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDM91', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMA1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2T3HRHMDMB1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMC1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMD1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDME1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMF1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMG1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMH1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMI1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMJ1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMK1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDML1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMM1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMN1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMO1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMP1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMQ1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMR1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMS1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMT1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMU1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMV1', 'FINST-VOC66Y9137JXLDSFE2O90CQFT5UY2U3HRHMDMW1']
2025-07-28 10:31:17,079 - INFO - 批量插入响应状态码: 200
2025-07-28 10:31:17,079 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:31:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15891291-0A92-7735-BACB-3FCABB6C8249', 'x-acs-trace-id': '4b7f020c710b8b4792202bedf21f9d46', 'etag': '2zKQD0RolzrrpYDIBVRu4Bg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:31:17,079 - INFO - 批量插入响应体: {'result': ['FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM2H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM3H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM4H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM5H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM6H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM7H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM8H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM9H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMAH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMBH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMCH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMDH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMEH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMFH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMGH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMHH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMIH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMJH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMKH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMLH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMMH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMNH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMOH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMPH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMQH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMRH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMSH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMTH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMUH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMVH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMWH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMXH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMYH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMZH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM0I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM1I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM2I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM3I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM4I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM5I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM6I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM7I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM8I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDM9I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMAI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMBI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMCI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMDI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMEI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMFI']}
2025-07-28 10:31:17,079 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-28 10:31:17,079 - INFO - 成功插入的数据ID: ['FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM2H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM3H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM4H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM5H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM6H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM7H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM8H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM9H', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMAH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMBH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMCH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMDH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMEH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMFH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMGH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMHH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMIH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMJH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMKH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMLH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMMH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMNH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMOH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMPH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMQH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMRH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMSH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMTH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMUH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMVH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMWH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMXH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMYH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMZH', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM0I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM1I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM2I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM3I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM4I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM5I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM6I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM7I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDM8I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDM9I', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMAI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMBI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMCI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMDI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMEI', 'FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2C5LRHMDMFI']
2025-07-28 10:31:22,266 - INFO - 批量插入响应状态码: 200
2025-07-28 10:31:22,266 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:31:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '894', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '710082A7-0E50-7117-94AA-4B9671504D19', 'x-acs-trace-id': 'dfb0934b86e854d7553ce57f6d86f76f', 'etag': '8BSEq58XtK+nDJnoh1pzSIA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:31:22,266 - INFO - 批量插入响应体: {'result': ['FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMT02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMU02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMV02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMW02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMX02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMY02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMZ02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM012', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM112', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM212', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM312', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM412', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM512', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM612', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM712', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM812', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM912', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMA12']}
2025-07-28 10:31:22,266 - INFO - 批量插入表单数据成功，批次 3，共 18 条记录
2025-07-28 10:31:22,266 - INFO - 成功插入的数据ID: ['FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMT02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMU02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMV02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMW02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMX02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMY02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMZ02', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM012', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM112', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM212', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM312', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM412', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM512', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM612', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM712', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM812', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM912', 'FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMA12']
2025-07-28 10:31:27,282 - INFO - 批量插入完成，共 118 条记录
2025-07-28 10:31:27,282 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 118 条，错误: 0 条
2025-07-28 10:31:27,282 - INFO - 开始处理日期: 2025-07-28
2025-07-28 10:31:27,282 - INFO - Request Parameters - Page 1:
2025-07-28 10:31:27,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:31:27,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:31:27,860 - INFO - Response - Page 1:
2025-07-28 10:31:27,860 - INFO - 查询完成，共获取到 0 条记录
2025-07-28 10:31:27,860 - INFO - 获取到 0 条表单数据
2025-07-28 10:31:27,860 - INFO - 当前日期 2025-07-28 有 2 条MySQL数据需要处理
2025-07-28 10:31:27,860 - INFO - 开始批量插入 2 条新记录
2025-07-28 10:31:28,001 - INFO - 批量插入响应状态码: 200
2025-07-28 10:31:28,001 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:31:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A697462F-90A6-70BF-BE92-8EE15D2894F8', 'x-acs-trace-id': '8c9029eca1efaa684fa70ee5f2cf7f6f', 'etag': '1m3RgcK8oDUwSnL7uvQ/8zA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:31:28,001 - INFO - 批量插入响应体: {'result': ['FINST-B1D66U610JHX9YC56GG4X4ML6JQ02NKTRHMDM0Z1', 'FINST-B1D66U610JHX9YC56GG4X4ML6JQ02OKTRHMDM1Z1']}
2025-07-28 10:31:28,001 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-28 10:31:28,001 - INFO - 成功插入的数据ID: ['FINST-B1D66U610JHX9YC56GG4X4ML6JQ02NKTRHMDM0Z1', 'FINST-B1D66U610JHX9YC56GG4X4ML6JQ02OKTRHMDM1Z1']
2025-07-28 10:31:33,016 - INFO - 批量插入完成，共 2 条记录
2025-07-28 10:31:33,016 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-28 10:31:33,016 - INFO - 数据同步完成！更新: 0 条，插入: 121 条，错误: 2 条
2025-07-28 10:32:33,032 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 10:32:33,032 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 10:32:33,032 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 10:32:33,188 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 464 条记录
2025-07-28 10:32:33,188 - INFO - 获取到 2 个日期需要处理: ['2025-07-27', '2025-07-28']
2025-07-28 10:32:33,203 - INFO - 开始处理日期: 2025-07-27
2025-07-28 10:32:33,203 - INFO - Request Parameters - Page 1:
2025-07-28 10:32:33,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:32:33,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:32:33,938 - INFO - Response - Page 1:
2025-07-28 10:32:33,938 - INFO - 第 1 页获取到 50 条记录
2025-07-28 10:32:34,453 - INFO - Request Parameters - Page 2:
2025-07-28 10:32:34,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:32:34,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:32:35,125 - INFO - Response - Page 2:
2025-07-28 10:32:35,125 - INFO - 第 2 页获取到 50 条记录
2025-07-28 10:32:35,641 - INFO - Request Parameters - Page 3:
2025-07-28 10:32:35,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:32:35,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:32:36,297 - INFO - Response - Page 3:
2025-07-28 10:32:36,297 - INFO - 第 3 页获取到 50 条记录
2025-07-28 10:32:36,813 - INFO - Request Parameters - Page 4:
2025-07-28 10:32:36,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:32:36,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:32:37,485 - INFO - Response - Page 4:
2025-07-28 10:32:37,485 - INFO - 第 4 页获取到 50 条记录
2025-07-28 10:32:38,000 - INFO - Request Parameters - Page 5:
2025-07-28 10:32:38,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:32:38,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:32:38,703 - INFO - Response - Page 5:
2025-07-28 10:32:38,703 - INFO - 第 5 页获取到 24 条记录
2025-07-28 10:32:39,219 - INFO - 查询完成，共获取到 224 条记录
2025-07-28 10:32:39,219 - INFO - 获取到 224 条表单数据
2025-07-28 10:32:39,219 - INFO - 当前日期 2025-07-27 有 445 条MySQL数据需要处理
2025-07-28 10:32:39,219 - INFO - 开始批量插入 221 条新记录
2025-07-28 10:32:39,438 - INFO - 批量插入响应状态码: 200
2025-07-28 10:32:39,438 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:32:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7A83A5EE-7052-7DD3-AAAA-F5DA12AFF1D3', 'x-acs-trace-id': 'f3c9722467c6dfbda6c980dd43371eee', 'etag': '2XKYecEQ6Cx2DHSOX+lzQbg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:32:39,438 - INFO - 批量插入响应体: {'result': ['FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMYB', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMZB', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM0C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM1C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM2C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM3C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM4C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM5C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM6C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM7C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM8C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM9C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMAC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMBC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMCC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMDC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMEC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMFC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMGC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMHC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMIC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMJC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMKC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMLC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMMC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMNC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMOC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMPC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMQC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMRC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMSC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMTC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMUC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMVC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMWC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMXC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMYC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMZC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM0D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM1D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM2D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM3D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM4D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM5D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM6D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM7D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM8D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM9D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMAD', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMBD']}
2025-07-28 10:32:39,438 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-28 10:32:39,438 - INFO - 成功插入的数据ID: ['FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMYB', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMZB', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM0C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM1C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM2C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM3C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM4C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM5C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM6C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM7C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM8C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM9C', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMAC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMBC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMCC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMDC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMEC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMFC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMGC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMHC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMIC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMJC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMKC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMLC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMMC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMNC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMOC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMPC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMQC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMRC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMSC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMTC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMUC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMVC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMWC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMXC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMYC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMZC', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM0D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM1D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM2D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM3D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM4D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM5D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM6D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM7D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM8D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDM9D', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMAD', 'FINST-2ZE66W71A5GXIB66EKHWIAX49ISO2XOCTHMDMBD']
2025-07-28 10:32:44,688 - INFO - 批量插入响应状态码: 200
2025-07-28 10:32:44,688 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:32:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2393', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1885828A-B5AE-7EB7-B393-C1CB3B591AC1', 'x-acs-trace-id': 'c9968edb6c4b9ea5543e0bdce5478e2d', 'etag': '2m1T/8+6+EtXeVsdPbWmG3Q3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:32:44,688 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMH', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMI', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMJ', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMK', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDML', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMM', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMN', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMO', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMP', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMQ', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMR', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMS', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMT', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMU', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMV', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMW', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMX', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMY', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMZ', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM01', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM11', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM21', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM31', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM41', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM51', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM61', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM71', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM81', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM91', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMA1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMB1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMC1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMD1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDME1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMF1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMG1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMH1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMI1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMJ1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMK1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDML1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMM1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMN1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMO1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMP1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMQ1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMR1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMS1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMT1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMU1']}
2025-07-28 10:32:44,688 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-28 10:32:44,688 - INFO - 成功插入的数据ID: ['FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMH', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMI', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMJ', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMK', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDML', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMM', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMN', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMO', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMP', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMQ', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMR', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMS', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMT', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMU', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMV', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMW', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMX', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMY', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMZ', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM01', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM11', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM21', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM31', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM41', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM51', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM61', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM71', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM81', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDM91', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMA1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMB1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMC1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMD1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDME1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMF1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMG1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMH1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMI1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMJ1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMK1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDML1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMM1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMN1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMO1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMP1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMQ1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMR1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMS1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMT1', 'FINST-7PF66N91J8JXF9Q5A8ZA49565OCL2QQGTHMDMU1']
2025-07-28 10:32:49,922 - INFO - 批量插入响应状态码: 200
2025-07-28 10:32:49,922 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:32:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '19EE3205-D1F0-7BFC-884F-CDEDCC6BF321', 'x-acs-trace-id': '057ecac8735d45d3df8711a17a40370b', 'etag': '2wnuGEMn8NlxzfxIi8ir/zg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:32:49,922 - INFO - 批量插入响应体: {'result': ['FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMXL1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMYL1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMZL1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM0M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM1M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM2M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM3M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM4M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM5M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM6M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM7M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM8M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM9M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMAM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMBM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMCM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMDM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMEM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMFM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMGM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMHM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMIM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMJM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMKM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMLM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMMM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMNM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMOM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMPM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMQM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMRM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMSM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMTM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMUM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMVM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMWM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMXM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMYM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMZM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM0N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM1N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM2N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM3N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM4N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM5N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM6N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM7N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM8N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM9N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMAN1']}
2025-07-28 10:32:49,922 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-28 10:32:49,922 - INFO - 成功插入的数据ID: ['FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMXL1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMYL1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMZL1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM0M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM1M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM2M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM3M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM4M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM5M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM6M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM7M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM8M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM9M1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMAM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMBM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMCM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMDM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMEM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMFM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMGM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMHM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMIM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMJM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMKM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMLM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMMM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMNM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMOM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMPM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMQM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMRM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMSM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMTM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMUM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMVM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMWM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMXM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMYM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMZM1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM0N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM1N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM2N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM3N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM4N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM5N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM6N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM7N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM8N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDM9N1', 'FINST-NYC66LB19PFXVYYH79SGCDT1I91H2ZRKTHMDMAN1']
2025-07-28 10:32:55,172 - INFO - 批量插入响应状态码: 200
2025-07-28 10:32:55,172 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:32:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F4BA03D6-CA2A-7C3D-835A-6C7F38F8F658', 'x-acs-trace-id': '7aa62e04d5f0719337e66040d4e019c5', 'etag': '2VbBv2aBipkB4+zuP2gK1mg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:32:55,172 - INFO - 批量插入响应体: {'result': ['FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMTI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMUI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMVI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMWI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMXI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMYI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMZI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM0J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM1J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM2J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM3J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM4J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM5J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM6J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM7J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM8J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM9J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMAJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMBJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMCJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMDJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMEJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMFJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMGJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMHJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMIJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMJJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMKJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMLJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMMJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMNJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMOJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMPJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMQJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMRJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMSJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMTJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMUJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMVJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMWJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMXJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMYJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMZJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM0K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM1K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM2K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM3K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM4K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM5K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM6K1']}
2025-07-28 10:32:55,172 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-28 10:32:55,172 - INFO - 成功插入的数据ID: ['FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMTI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMUI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMVI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMWI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMXI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMYI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMZI1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM0J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM1J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM2J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM3J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM4J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM5J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM6J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM7J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM8J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM9J1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMAJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMBJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMCJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMDJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMEJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMFJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMGJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMHJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMIJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMJJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMKJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMLJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMMJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMNJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMOJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMPJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMQJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMRJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMSJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMTJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMUJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMVJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMWJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMXJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMYJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDMZJ1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM0K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM1K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM2K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM3K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM4K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM5K1', 'FINST-49866E71HJHXJE7JAAFU65EML59R3WTOTHMDM6K1']
2025-07-28 10:33:00,406 - INFO - 批量插入响应状态码: 200
2025-07-28 10:33:00,406 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 02:33:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1041', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EE7A1947-D98E-7F18-BB65-EA76BD8A5FDF', 'x-acs-trace-id': 'c5969a9cdc8c6e571238a4b3cdf670d2', 'etag': '1vbb0aI1qZ5pvtCtpLkATrw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 10:33:00,406 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMUH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMVH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMWH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMXH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMYH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMZH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM0I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM1I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM2I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM3I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM4I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM5I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM6I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM7I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM8I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM9I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMAI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMBI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMCI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMDI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMEI1']}
2025-07-28 10:33:00,406 - INFO - 批量插入表单数据成功，批次 5，共 21 条记录
2025-07-28 10:33:00,406 - INFO - 成功插入的数据ID: ['FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMUH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMVH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMWH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMXH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMYH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMZH1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM0I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM1I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM2I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM3I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM4I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM5I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM6I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM7I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM8I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDM9I1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMAI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMBI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMCI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMDI1', 'FINST-8SG66JA1EJHX7LAJDHIS39YRS93I3BVSTHMDMEI1']
2025-07-28 10:33:05,422 - INFO - 批量插入完成，共 221 条记录
2025-07-28 10:33:05,422 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 221 条，错误: 0 条
2025-07-28 10:33:05,422 - INFO - 开始处理日期: 2025-07-28
2025-07-28 10:33:05,422 - INFO - Request Parameters - Page 1:
2025-07-28 10:33:05,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 10:33:05,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 10:33:05,875 - INFO - Response - Page 1:
2025-07-28 10:33:05,875 - INFO - 第 1 页获取到 2 条记录
2025-07-28 10:33:06,375 - INFO - 查询完成，共获取到 2 条记录
2025-07-28 10:33:06,375 - INFO - 获取到 2 条表单数据
2025-07-28 10:33:06,375 - INFO - 当前日期 2025-07-28 有 2 条MySQL数据需要处理
2025-07-28 10:33:06,375 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 10:33:06,375 - INFO - 数据同步完成！更新: 0 条，插入: 221 条，错误: 0 条
2025-07-28 10:33:06,375 - INFO - 同步完成
2025-07-28 13:30:33,819 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 13:30:33,819 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 13:30:33,819 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 13:30:33,975 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 135 条记录
2025-07-28 13:30:33,975 - INFO - 获取到 5 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28']
2025-07-28 13:30:33,975 - INFO - 开始处理日期: 2025-07-24
2025-07-28 13:30:33,991 - INFO - Request Parameters - Page 1:
2025-07-28 13:30:33,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:33,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:42,100 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 00072B13-0686-7335-A01D-7F546E39D71C Response: {'code': 'ServiceUnavailable', 'requestid': '00072B13-0686-7335-A01D-7F546E39D71C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 00072B13-0686-7335-A01D-7F546E39D71C)
2025-07-28 13:30:42,100 - INFO - 开始处理日期: 2025-07-25
2025-07-28 13:30:42,100 - INFO - Request Parameters - Page 1:
2025-07-28 13:30:42,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:42,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:50,210 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2E40469C-ED1B-79E8-A5A4-F40314263996 Response: {'code': 'ServiceUnavailable', 'requestid': '2E40469C-ED1B-79E8-A5A4-F40314263996', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2E40469C-ED1B-79E8-A5A4-F40314263996)
2025-07-28 13:30:50,210 - INFO - 开始处理日期: 2025-07-26
2025-07-28 13:30:50,210 - INFO - Request Parameters - Page 1:
2025-07-28 13:30:50,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:50,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:53,756 - INFO - Response - Page 1:
2025-07-28 13:30:53,756 - INFO - 第 1 页获取到 50 条记录
2025-07-28 13:30:54,272 - INFO - Request Parameters - Page 2:
2025-07-28 13:30:54,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:54,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:55,038 - INFO - Response - Page 2:
2025-07-28 13:30:55,038 - INFO - 第 2 页获取到 50 条记录
2025-07-28 13:30:55,553 - INFO - Request Parameters - Page 3:
2025-07-28 13:30:55,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:55,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:56,210 - INFO - Response - Page 3:
2025-07-28 13:30:56,210 - INFO - 第 3 页获取到 50 条记录
2025-07-28 13:30:56,725 - INFO - Request Parameters - Page 4:
2025-07-28 13:30:56,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:56,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:57,428 - INFO - Response - Page 4:
2025-07-28 13:30:57,428 - INFO - 第 4 页获取到 50 条记录
2025-07-28 13:30:57,944 - INFO - Request Parameters - Page 5:
2025-07-28 13:30:57,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:57,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:58,600 - INFO - Response - Page 5:
2025-07-28 13:30:58,600 - INFO - 第 5 页获取到 50 条记录
2025-07-28 13:30:59,116 - INFO - Request Parameters - Page 6:
2025-07-28 13:30:59,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:30:59,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:30:59,819 - INFO - Response - Page 6:
2025-07-28 13:30:59,819 - INFO - 第 6 页获取到 50 条记录
2025-07-28 13:31:00,335 - INFO - Request Parameters - Page 7:
2025-07-28 13:31:00,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:00,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:01,053 - INFO - Response - Page 7:
2025-07-28 13:31:01,053 - INFO - 第 7 页获取到 50 条记录
2025-07-28 13:31:01,553 - INFO - Request Parameters - Page 8:
2025-07-28 13:31:01,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:01,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:02,288 - INFO - Response - Page 8:
2025-07-28 13:31:02,288 - INFO - 第 8 页获取到 50 条记录
2025-07-28 13:31:02,788 - INFO - Request Parameters - Page 9:
2025-07-28 13:31:02,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:02,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:03,506 - INFO - Response - Page 9:
2025-07-28 13:31:03,506 - INFO - 第 9 页获取到 50 条记录
2025-07-28 13:31:04,022 - INFO - Request Parameters - Page 10:
2025-07-28 13:31:04,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:04,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:04,631 - INFO - Response - Page 10:
2025-07-28 13:31:04,631 - INFO - 第 10 页获取到 13 条记录
2025-07-28 13:31:05,147 - INFO - 查询完成，共获取到 463 条记录
2025-07-28 13:31:05,147 - INFO - 获取到 463 条表单数据
2025-07-28 13:31:05,147 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-28 13:31:05,147 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 13:31:05,147 - INFO - 开始处理日期: 2025-07-27
2025-07-28 13:31:05,147 - INFO - Request Parameters - Page 1:
2025-07-28 13:31:05,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:05,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:05,928 - INFO - Response - Page 1:
2025-07-28 13:31:05,928 - INFO - 第 1 页获取到 50 条记录
2025-07-28 13:31:06,428 - INFO - Request Parameters - Page 2:
2025-07-28 13:31:06,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:06,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:07,100 - INFO - Response - Page 2:
2025-07-28 13:31:07,100 - INFO - 第 2 页获取到 50 条记录
2025-07-28 13:31:07,600 - INFO - Request Parameters - Page 3:
2025-07-28 13:31:07,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:07,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:08,319 - INFO - Response - Page 3:
2025-07-28 13:31:08,319 - INFO - 第 3 页获取到 50 条记录
2025-07-28 13:31:08,834 - INFO - Request Parameters - Page 4:
2025-07-28 13:31:08,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:08,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:09,584 - INFO - Response - Page 4:
2025-07-28 13:31:09,584 - INFO - 第 4 页获取到 50 条记录
2025-07-28 13:31:10,100 - INFO - Request Parameters - Page 5:
2025-07-28 13:31:10,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:10,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:10,834 - INFO - Response - Page 5:
2025-07-28 13:31:10,834 - INFO - 第 5 页获取到 50 条记录
2025-07-28 13:31:11,350 - INFO - Request Parameters - Page 6:
2025-07-28 13:31:11,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:11,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:12,022 - INFO - Response - Page 6:
2025-07-28 13:31:12,022 - INFO - 第 6 页获取到 50 条记录
2025-07-28 13:31:12,522 - INFO - Request Parameters - Page 7:
2025-07-28 13:31:12,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:12,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:13,225 - INFO - Response - Page 7:
2025-07-28 13:31:13,225 - INFO - 第 7 页获取到 50 条记录
2025-07-28 13:31:13,725 - INFO - Request Parameters - Page 8:
2025-07-28 13:31:13,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:13,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:14,475 - INFO - Response - Page 8:
2025-07-28 13:31:14,475 - INFO - 第 8 页获取到 50 条记录
2025-07-28 13:31:14,991 - INFO - Request Parameters - Page 9:
2025-07-28 13:31:14,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:14,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:15,694 - INFO - Response - Page 9:
2025-07-28 13:31:15,694 - INFO - 第 9 页获取到 45 条记录
2025-07-28 13:31:16,209 - INFO - 查询完成，共获取到 445 条记录
2025-07-28 13:31:16,209 - INFO - 获取到 445 条表单数据
2025-07-28 13:31:16,209 - INFO - 当前日期 2025-07-27 有 125 条MySQL数据需要处理
2025-07-28 13:31:16,209 - INFO - 开始更新记录 - 表单实例ID: FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMHH
2025-07-28 13:31:16,709 - INFO - 更新表单数据成功: FINST-FIG66R81ARHXX2U46ZBPK6CR2UGY2B5LRHMDMHH
2025-07-28 13:31:16,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7523.0, 'new_value': 4763.74}, {'field': 'total_amount', 'old_value': 7523.0, 'new_value': 4763.74}, {'field': 'order_count', 'old_value': 23, 'new_value': 29}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/6b8d18d4b38a478b99653b856ca18804.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=YzsBMDWF9SaPHhQsFafzryGbl1c%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/6085891ad0e34b32b7a14b94a728205b.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=0%2FFCILwpHhm9%2BynAbP4JwFzok88%3D'}]
2025-07-28 13:31:16,709 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMX02
2025-07-28 13:31:17,319 - INFO - 更新表单数据成功: FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMX02
2025-07-28 13:31:17,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8900.0, 'new_value': 12370.88}, {'field': 'total_amount', 'old_value': 8900.0, 'new_value': 12370.88}, {'field': 'order_count', 'old_value': 320, 'new_value': 381}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/99ee4f2209504d34a38f306a4dfdd556.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=dzU2Jr1Pwg79zBGfoyHxsBkw5Bs%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/498d522c96f44a86a9a244f1bf4a41ec.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=ipivpjRZRxcme1icTZLXFuirMJ8%3D'}]
2025-07-28 13:31:17,319 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMY02
2025-07-28 13:31:17,928 - INFO - 更新表单数据成功: FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDMY02
2025-07-28 13:31:17,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9000.0, 'new_value': 9734.1}, {'field': 'total_amount', 'old_value': 9000.0, 'new_value': 9734.1}, {'field': 'order_count', 'old_value': 30, 'new_value': 46}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/bfc3c02fe7204d0782a3740161fe4f35.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=JoRuma1wXovUnI%2FBniBLQ990UN4%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/bebb8d889d3a4cd7b48b6a0d81e000c5.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=fDU5krvrTxPMQej1qJHuzmjTS%2BM%3D'}]
2025-07-28 13:31:17,928 - INFO - 开始批量插入 1 条新记录
2025-07-28 13:31:18,116 - INFO - 批量插入响应状态码: 200
2025-07-28 13:31:18,116 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 05:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C28A3761-470C-7BD6-9873-50E1D85FFE6D', 'x-acs-trace-id': 'be52d8b5a4312a899bc37b964c124316', 'etag': '6ZeUBtRhyJV6IzageUqXQ7g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 13:31:18,116 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1S8JXKJ6AEVB6S64GP1F535C37OMDMZ1']}
2025-07-28 13:31:18,116 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-28 13:31:18,116 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1S8JXKJ6AEVB6S64GP1F535C37OMDMZ1']
2025-07-28 13:31:23,131 - INFO - 批量插入完成，共 1 条记录
2025-07-28 13:31:23,131 - INFO - 日期 2025-07-27 处理完成 - 更新: 3 条，插入: 1 条，错误: 0 条
2025-07-28 13:31:23,131 - INFO - 开始处理日期: 2025-07-28
2025-07-28 13:31:23,131 - INFO - Request Parameters - Page 1:
2025-07-28 13:31:23,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:31:23,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:31:23,616 - INFO - Response - Page 1:
2025-07-28 13:31:23,616 - INFO - 第 1 页获取到 2 条记录
2025-07-28 13:31:24,131 - INFO - 查询完成，共获取到 2 条记录
2025-07-28 13:31:24,131 - INFO - 获取到 2 条表单数据
2025-07-28 13:31:24,131 - INFO - 当前日期 2025-07-28 有 3 条MySQL数据需要处理
2025-07-28 13:31:24,131 - INFO - 开始批量插入 1 条新记录
2025-07-28 13:31:24,287 - INFO - 批量插入响应状态码: 200
2025-07-28 13:31:24,287 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 05:31:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8F4CAB78-1849-7D7A-A4E2-B2A2FD8229B6', 'x-acs-trace-id': 'ab3ea91e23519afe9ca881d711675fb6', 'etag': '6LNfbHyc2Oh9qFllm21YnRQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 13:31:24,287 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA19EHXFERUC9BDXC6A26SU2B387OMDMUK1']}
2025-07-28 13:31:24,287 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-28 13:31:24,287 - INFO - 成功插入的数据ID: ['FINST-E3G66QA19EHXFERUC9BDXC6A26SU2B387OMDMUK1']
2025-07-28 13:31:29,303 - INFO - 批量插入完成，共 1 条记录
2025-07-28 13:31:29,303 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-28 13:31:29,303 - INFO - 数据同步完成！更新: 3 条，插入: 2 条，错误: 2 条
2025-07-28 13:32:29,318 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 13:32:29,318 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 13:32:29,318 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 13:32:29,490 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 466 条记录
2025-07-28 13:32:29,490 - INFO - 获取到 2 个日期需要处理: ['2025-07-27', '2025-07-28']
2025-07-28 13:32:29,490 - INFO - 开始处理日期: 2025-07-27
2025-07-28 13:32:29,490 - INFO - Request Parameters - Page 1:
2025-07-28 13:32:29,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:29,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:30,475 - INFO - Response - Page 1:
2025-07-28 13:32:30,475 - INFO - 第 1 页获取到 50 条记录
2025-07-28 13:32:30,990 - INFO - Request Parameters - Page 2:
2025-07-28 13:32:30,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:30,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:31,693 - INFO - Response - Page 2:
2025-07-28 13:32:31,693 - INFO - 第 2 页获取到 50 条记录
2025-07-28 13:32:32,209 - INFO - Request Parameters - Page 3:
2025-07-28 13:32:32,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:32,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:32,865 - INFO - Response - Page 3:
2025-07-28 13:32:32,865 - INFO - 第 3 页获取到 50 条记录
2025-07-28 13:32:33,381 - INFO - Request Parameters - Page 4:
2025-07-28 13:32:33,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:33,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:34,068 - INFO - Response - Page 4:
2025-07-28 13:32:34,068 - INFO - 第 4 页获取到 50 条记录
2025-07-28 13:32:34,568 - INFO - Request Parameters - Page 5:
2025-07-28 13:32:34,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:34,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:35,240 - INFO - Response - Page 5:
2025-07-28 13:32:35,240 - INFO - 第 5 页获取到 50 条记录
2025-07-28 13:32:35,756 - INFO - Request Parameters - Page 6:
2025-07-28 13:32:35,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:35,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:36,428 - INFO - Response - Page 6:
2025-07-28 13:32:36,428 - INFO - 第 6 页获取到 50 条记录
2025-07-28 13:32:36,943 - INFO - Request Parameters - Page 7:
2025-07-28 13:32:36,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:36,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:37,646 - INFO - Response - Page 7:
2025-07-28 13:32:37,646 - INFO - 第 7 页获取到 50 条记录
2025-07-28 13:32:38,162 - INFO - Request Parameters - Page 8:
2025-07-28 13:32:38,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:38,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:38,928 - INFO - Response - Page 8:
2025-07-28 13:32:38,928 - INFO - 第 8 页获取到 50 条记录
2025-07-28 13:32:39,443 - INFO - Request Parameters - Page 9:
2025-07-28 13:32:39,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:39,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:40,178 - INFO - Response - Page 9:
2025-07-28 13:32:40,178 - INFO - 第 9 页获取到 46 条记录
2025-07-28 13:32:40,693 - INFO - 查询完成，共获取到 446 条记录
2025-07-28 13:32:40,693 - INFO - 获取到 446 条表单数据
2025-07-28 13:32:40,693 - INFO - 当前日期 2025-07-27 有 446 条MySQL数据需要处理
2025-07-28 13:32:40,709 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 13:32:40,709 - INFO - 开始处理日期: 2025-07-28
2025-07-28 13:32:40,709 - INFO - Request Parameters - Page 1:
2025-07-28 13:32:40,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 13:32:40,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 13:32:41,240 - INFO - Response - Page 1:
2025-07-28 13:32:41,240 - INFO - 第 1 页获取到 3 条记录
2025-07-28 13:32:41,756 - INFO - 查询完成，共获取到 3 条记录
2025-07-28 13:32:41,756 - INFO - 获取到 3 条表单数据
2025-07-28 13:32:41,756 - INFO - 当前日期 2025-07-28 有 3 条MySQL数据需要处理
2025-07-28 13:32:41,756 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 13:32:41,756 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 13:32:41,756 - INFO - 同步完成
2025-07-28 16:30:33,685 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 16:30:33,685 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 16:30:33,685 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 16:30:33,856 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 155 条记录
2025-07-28 16:30:33,856 - INFO - 获取到 5 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28']
2025-07-28 16:30:33,856 - INFO - 开始处理日期: 2025-07-24
2025-07-28 16:30:33,856 - INFO - Request Parameters - Page 1:
2025-07-28 16:30:33,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:33,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:41,997 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 418B40B7-FFBA-77E5-8ED1-2519E511ACB0 Response: {'code': 'ServiceUnavailable', 'requestid': '418B40B7-FFBA-77E5-8ED1-2519E511ACB0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 418B40B7-FFBA-77E5-8ED1-2519E511ACB0)
2025-07-28 16:30:41,997 - INFO - 开始处理日期: 2025-07-25
2025-07-28 16:30:41,997 - INFO - Request Parameters - Page 1:
2025-07-28 16:30:41,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:41,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:50,059 - INFO - Response - Page 1:
2025-07-28 16:30:50,059 - INFO - 第 1 页获取到 50 条记录
2025-07-28 16:30:50,575 - INFO - Request Parameters - Page 2:
2025-07-28 16:30:50,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:50,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:51,263 - INFO - Response - Page 2:
2025-07-28 16:30:51,263 - INFO - 第 2 页获取到 50 条记录
2025-07-28 16:30:51,778 - INFO - Request Parameters - Page 3:
2025-07-28 16:30:51,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:51,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:52,528 - INFO - Response - Page 3:
2025-07-28 16:30:52,528 - INFO - 第 3 页获取到 50 条记录
2025-07-28 16:30:53,044 - INFO - Request Parameters - Page 4:
2025-07-28 16:30:53,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:53,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:53,747 - INFO - Response - Page 4:
2025-07-28 16:30:53,747 - INFO - 第 4 页获取到 50 条记录
2025-07-28 16:30:54,247 - INFO - Request Parameters - Page 5:
2025-07-28 16:30:54,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:54,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:54,966 - INFO - Response - Page 5:
2025-07-28 16:30:54,981 - INFO - 第 5 页获取到 50 条记录
2025-07-28 16:30:55,497 - INFO - Request Parameters - Page 6:
2025-07-28 16:30:55,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:55,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:56,200 - INFO - Response - Page 6:
2025-07-28 16:30:56,200 - INFO - 第 6 页获取到 50 条记录
2025-07-28 16:30:56,716 - INFO - Request Parameters - Page 7:
2025-07-28 16:30:56,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:56,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:57,466 - INFO - Response - Page 7:
2025-07-28 16:30:57,466 - INFO - 第 7 页获取到 50 条记录
2025-07-28 16:30:57,981 - INFO - Request Parameters - Page 8:
2025-07-28 16:30:57,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:57,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:58,653 - INFO - Response - Page 8:
2025-07-28 16:30:58,653 - INFO - 第 8 页获取到 50 条记录
2025-07-28 16:30:59,169 - INFO - Request Parameters - Page 9:
2025-07-28 16:30:59,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:30:59,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:30:59,872 - INFO - Response - Page 9:
2025-07-28 16:30:59,872 - INFO - 第 9 页获取到 46 条记录
2025-07-28 16:31:00,388 - INFO - 查询完成，共获取到 446 条记录
2025-07-28 16:31:00,388 - INFO - 获取到 446 条表单数据
2025-07-28 16:31:00,388 - INFO - 当前日期 2025-07-25 有 1 条MySQL数据需要处理
2025-07-28 16:31:00,388 - INFO - 开始批量插入 1 条新记录
2025-07-28 16:31:00,575 - INFO - 批量插入响应状态码: 200
2025-07-28 16:31:00,575 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 08:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B4C88C1B-BD2F-7DD0-8D25-8D9FFC74892D', 'x-acs-trace-id': '49fb5833489de0ecf4fdedcbe2c21f09', 'etag': '6UbX3WBSAee84F8tJcabEsA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 16:31:00,575 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S811EJXVMI3CLT46CRUCPP83K67MUMDMC2']}
2025-07-28 16:31:00,575 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-28 16:31:00,575 - INFO - 成功插入的数据ID: ['FINST-N3G66S811EJXVMI3CLT46CRUCPP83K67MUMDMC2']
2025-07-28 16:31:05,591 - INFO - 批量插入完成，共 1 条记录
2025-07-28 16:31:05,591 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-28 16:31:05,591 - INFO - 开始处理日期: 2025-07-26
2025-07-28 16:31:05,591 - INFO - Request Parameters - Page 1:
2025-07-28 16:31:05,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:05,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:06,309 - INFO - Response - Page 1:
2025-07-28 16:31:06,309 - INFO - 第 1 页获取到 50 条记录
2025-07-28 16:31:06,825 - INFO - Request Parameters - Page 2:
2025-07-28 16:31:06,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:06,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:07,544 - INFO - Response - Page 2:
2025-07-28 16:31:07,544 - INFO - 第 2 页获取到 50 条记录
2025-07-28 16:31:08,059 - INFO - Request Parameters - Page 3:
2025-07-28 16:31:08,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:08,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:08,778 - INFO - Response - Page 3:
2025-07-28 16:31:08,778 - INFO - 第 3 页获取到 50 条记录
2025-07-28 16:31:09,294 - INFO - Request Parameters - Page 4:
2025-07-28 16:31:09,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:09,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:10,106 - INFO - Response - Page 4:
2025-07-28 16:31:10,106 - INFO - 第 4 页获取到 50 条记录
2025-07-28 16:31:10,622 - INFO - Request Parameters - Page 5:
2025-07-28 16:31:10,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:10,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:11,341 - INFO - Response - Page 5:
2025-07-28 16:31:11,341 - INFO - 第 5 页获取到 50 条记录
2025-07-28 16:31:11,856 - INFO - Request Parameters - Page 6:
2025-07-28 16:31:11,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:11,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:12,637 - INFO - Response - Page 6:
2025-07-28 16:31:12,637 - INFO - 第 6 页获取到 50 条记录
2025-07-28 16:31:13,153 - INFO - Request Parameters - Page 7:
2025-07-28 16:31:13,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:13,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:13,903 - INFO - Response - Page 7:
2025-07-28 16:31:13,903 - INFO - 第 7 页获取到 50 条记录
2025-07-28 16:31:14,403 - INFO - Request Parameters - Page 8:
2025-07-28 16:31:14,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:14,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:15,091 - INFO - Response - Page 8:
2025-07-28 16:31:15,091 - INFO - 第 8 页获取到 50 条记录
2025-07-28 16:31:15,606 - INFO - Request Parameters - Page 9:
2025-07-28 16:31:15,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:15,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:16,262 - INFO - Response - Page 9:
2025-07-28 16:31:16,262 - INFO - 第 9 页获取到 50 条记录
2025-07-28 16:31:16,778 - INFO - Request Parameters - Page 10:
2025-07-28 16:31:16,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:16,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:17,419 - INFO - Response - Page 10:
2025-07-28 16:31:17,419 - INFO - 第 10 页获取到 13 条记录
2025-07-28 16:31:17,919 - INFO - 查询完成，共获取到 463 条记录
2025-07-28 16:31:17,919 - INFO - 获取到 463 条表单数据
2025-07-28 16:31:17,919 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-28 16:31:17,919 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 16:31:17,919 - INFO - 开始处理日期: 2025-07-27
2025-07-28 16:31:17,919 - INFO - Request Parameters - Page 1:
2025-07-28 16:31:17,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:17,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:18,747 - INFO - Response - Page 1:
2025-07-28 16:31:18,747 - INFO - 第 1 页获取到 50 条记录
2025-07-28 16:31:19,262 - INFO - Request Parameters - Page 2:
2025-07-28 16:31:19,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:19,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:20,028 - INFO - Response - Page 2:
2025-07-28 16:31:20,028 - INFO - 第 2 页获取到 50 条记录
2025-07-28 16:31:20,528 - INFO - Request Parameters - Page 3:
2025-07-28 16:31:20,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:20,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:21,200 - INFO - Response - Page 3:
2025-07-28 16:31:21,200 - INFO - 第 3 页获取到 50 条记录
2025-07-28 16:31:21,716 - INFO - Request Parameters - Page 4:
2025-07-28 16:31:21,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:21,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:22,403 - INFO - Response - Page 4:
2025-07-28 16:31:22,403 - INFO - 第 4 页获取到 50 条记录
2025-07-28 16:31:22,919 - INFO - Request Parameters - Page 5:
2025-07-28 16:31:22,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:22,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:23,606 - INFO - Response - Page 5:
2025-07-28 16:31:23,606 - INFO - 第 5 页获取到 50 条记录
2025-07-28 16:31:24,106 - INFO - Request Parameters - Page 6:
2025-07-28 16:31:24,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:24,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:24,778 - INFO - Response - Page 6:
2025-07-28 16:31:24,778 - INFO - 第 6 页获取到 50 条记录
2025-07-28 16:31:25,278 - INFO - Request Parameters - Page 7:
2025-07-28 16:31:25,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:25,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:26,028 - INFO - Response - Page 7:
2025-07-28 16:31:26,028 - INFO - 第 7 页获取到 50 条记录
2025-07-28 16:31:26,528 - INFO - Request Parameters - Page 8:
2025-07-28 16:31:26,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:26,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:27,200 - INFO - Response - Page 8:
2025-07-28 16:31:27,200 - INFO - 第 8 页获取到 50 条记录
2025-07-28 16:31:27,700 - INFO - Request Parameters - Page 9:
2025-07-28 16:31:27,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:27,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:28,403 - INFO - Response - Page 9:
2025-07-28 16:31:28,403 - INFO - 第 9 页获取到 46 条记录
2025-07-28 16:31:28,919 - INFO - 查询完成，共获取到 446 条记录
2025-07-28 16:31:28,919 - INFO - 获取到 446 条表单数据
2025-07-28 16:31:28,919 - INFO - 当前日期 2025-07-27 有 141 条MySQL数据需要处理
2025-07-28 16:31:28,919 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM012
2025-07-28 16:31:29,497 - INFO - 更新表单数据成功: FINST-UW966371AJHXGGH8F0TTADF2G9DK295PRHMDM012
2025-07-28 16:31:29,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2137.2}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2137.2}, {'field': 'order_count', 'old_value': 0, 'new_value': 241}]
2025-07-28 16:31:29,497 - INFO - 开始批量插入 16 条新记录
2025-07-28 16:31:29,731 - INFO - 批量插入响应状态码: 200
2025-07-28 16:31:29,731 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 08:31:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '796', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '845F7FA4-A489-73D7-A70E-36E544664CFF', 'x-acs-trace-id': '64197208679b49ba3a093625d4114a29', 'etag': '76Q78qWzGYydTeeMlAaAH5w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 16:31:29,731 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM5D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM6D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM7D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM8D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM9D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMAD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMBD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMCD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMDD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMED1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMFD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMGD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMHD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMID1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMJD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMKD1']}
2025-07-28 16:31:29,731 - INFO - 批量插入表单数据成功，批次 1，共 16 条记录
2025-07-28 16:31:29,731 - INFO - 成功插入的数据ID: ['FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM5D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM6D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM7D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM8D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDM9D1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMAD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMBD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMCD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMDD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMED1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMFD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMGD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMHD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMID1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMJD1', 'FINST-2FD66I717JHXO9W6CS6MV4MW5RQ43UOTMUMDMKD1']
2025-07-28 16:31:34,747 - INFO - 批量插入完成，共 16 条记录
2025-07-28 16:31:34,747 - INFO - 日期 2025-07-27 处理完成 - 更新: 1 条，插入: 16 条，错误: 0 条
2025-07-28 16:31:34,747 - INFO - 开始处理日期: 2025-07-28
2025-07-28 16:31:34,747 - INFO - Request Parameters - Page 1:
2025-07-28 16:31:34,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:31:34,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:31:35,231 - INFO - Response - Page 1:
2025-07-28 16:31:35,231 - INFO - 第 1 页获取到 3 条记录
2025-07-28 16:31:35,747 - INFO - 查询完成，共获取到 3 条记录
2025-07-28 16:31:35,747 - INFO - 获取到 3 条表单数据
2025-07-28 16:31:35,747 - INFO - 当前日期 2025-07-28 有 4 条MySQL数据需要处理
2025-07-28 16:31:35,747 - INFO - 开始批量插入 1 条新记录
2025-07-28 16:31:35,919 - INFO - 批量插入响应状态码: 200
2025-07-28 16:31:35,919 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 08:31:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3D800E1B-4588-79F2-987C-46C12637D2B8', 'x-acs-trace-id': '8b0889610c8447ffe87fc01a49a12566', 'etag': '6XTAvHVBdECfH/NfVLAXqiw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 16:31:35,919 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61CCJX09LM8JBCV7UZVW0F3OGYMUMDMJ1']}
2025-07-28 16:31:35,919 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-28 16:31:35,919 - INFO - 成功插入的数据ID: ['FINST-3PF66X61CCJX09LM8JBCV7UZVW0F3OGYMUMDMJ1']
2025-07-28 16:31:40,934 - INFO - 批量插入完成，共 1 条记录
2025-07-28 16:31:40,934 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-28 16:31:40,934 - INFO - 数据同步完成！更新: 1 条，插入: 18 条，错误: 1 条
2025-07-28 16:32:40,949 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 16:32:40,949 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 16:32:40,949 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 16:32:41,121 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 543 条记录
2025-07-28 16:32:41,121 - INFO - 获取到 2 个日期需要处理: ['2025-07-27', '2025-07-28']
2025-07-28 16:32:41,121 - INFO - 开始处理日期: 2025-07-27
2025-07-28 16:32:41,121 - INFO - Request Parameters - Page 1:
2025-07-28 16:32:41,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:41,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:41,887 - INFO - Response - Page 1:
2025-07-28 16:32:41,887 - INFO - 第 1 页获取到 50 条记录
2025-07-28 16:32:42,387 - INFO - Request Parameters - Page 2:
2025-07-28 16:32:42,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:42,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:43,121 - INFO - Response - Page 2:
2025-07-28 16:32:43,121 - INFO - 第 2 页获取到 50 条记录
2025-07-28 16:32:43,637 - INFO - Request Parameters - Page 3:
2025-07-28 16:32:43,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:43,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:44,356 - INFO - Response - Page 3:
2025-07-28 16:32:44,356 - INFO - 第 3 页获取到 50 条记录
2025-07-28 16:32:44,871 - INFO - Request Parameters - Page 4:
2025-07-28 16:32:44,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:44,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:45,606 - INFO - Response - Page 4:
2025-07-28 16:32:45,606 - INFO - 第 4 页获取到 50 条记录
2025-07-28 16:32:46,121 - INFO - Request Parameters - Page 5:
2025-07-28 16:32:46,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:46,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:46,887 - INFO - Response - Page 5:
2025-07-28 16:32:46,887 - INFO - 第 5 页获取到 50 条记录
2025-07-28 16:32:47,387 - INFO - Request Parameters - Page 6:
2025-07-28 16:32:47,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:47,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:48,090 - INFO - Response - Page 6:
2025-07-28 16:32:48,090 - INFO - 第 6 页获取到 50 条记录
2025-07-28 16:32:48,590 - INFO - Request Parameters - Page 7:
2025-07-28 16:32:48,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:48,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:49,324 - INFO - Response - Page 7:
2025-07-28 16:32:49,324 - INFO - 第 7 页获取到 50 条记录
2025-07-28 16:32:49,824 - INFO - Request Parameters - Page 8:
2025-07-28 16:32:49,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:49,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:50,590 - INFO - Response - Page 8:
2025-07-28 16:32:50,590 - INFO - 第 8 页获取到 50 条记录
2025-07-28 16:32:51,106 - INFO - Request Parameters - Page 9:
2025-07-28 16:32:51,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:51,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:51,777 - INFO - Response - Page 9:
2025-07-28 16:32:51,777 - INFO - 第 9 页获取到 50 条记录
2025-07-28 16:32:52,293 - INFO - Request Parameters - Page 10:
2025-07-28 16:32:52,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:32:52,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:32:52,902 - INFO - Response - Page 10:
2025-07-28 16:32:52,902 - INFO - 第 10 页获取到 12 条记录
2025-07-28 16:32:53,402 - INFO - 查询完成，共获取到 462 条记录
2025-07-28 16:32:53,402 - INFO - 获取到 462 条表单数据
2025-07-28 16:32:53,402 - INFO - 当前日期 2025-07-27 有 518 条MySQL数据需要处理
2025-07-28 16:32:53,418 - INFO - 开始批量插入 56 条新记录
2025-07-28 16:32:53,637 - INFO - 批量插入响应状态码: 200
2025-07-28 16:32:53,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 08:32:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2434', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F2D1032F-1807-70A9-B2B4-3C0299919920', 'x-acs-trace-id': 'e377b1643f95deb6d39612291a485076', 'etag': '2VBEzWCNmTz9Em1kUheP9xA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 16:32:53,637 - INFO - 批量插入响应体: {'result': ['FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM8Z', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM9Z', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMAZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMBZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMCZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMDZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMEZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMFZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMGZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMHZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMIZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMJZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMKZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMLZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMMZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMNZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMOZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMPZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMQZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMRZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMSZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMTZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMUZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMVZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMWZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMXZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMYZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMZZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM001', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM101', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM201', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM301', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM401', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM501', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM601', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM701', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM801', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM901', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMA01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMB01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMC01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMD01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDME01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMF01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMG01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMH01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMI01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMJ01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMK01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDML01']}
2025-07-28 16:32:53,637 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-28 16:32:53,637 - INFO - 成功插入的数据ID: ['FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM8Z', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM9Z', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMAZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMBZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMCZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMDZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMEZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMFZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMGZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMHZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMIZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMJZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMKZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMLZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMMZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMNZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMOZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMPZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMQZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMRZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMSZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMTZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMUZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMVZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMWZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMXZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMYZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMZZ', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM001', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM101', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM201', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM301', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM401', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM501', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM601', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM701', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM801', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM901', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMA01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMB01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMC01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMD01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDME01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMF01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMG01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMH01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMI01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMJ01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDMK01', 'FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDML01']
2025-07-28 16:32:58,809 - INFO - 批量插入响应状态码: 200
2025-07-28 16:32:58,809 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 08:32:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '306', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B8E28FF3-B9EB-7A17-AF84-7C425BEB35B4', 'x-acs-trace-id': '8c22fe9819a1e7cde041180c7607d23c', 'etag': '3eQ2x8xazj9EyCAB7C+FKEw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 16:32:58,809 - INFO - 批量插入响应体: {'result': ['FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDM9F1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMAF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMBF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMCF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMDF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMEF1']}
2025-07-28 16:32:58,809 - INFO - 批量插入表单数据成功，批次 2，共 6 条记录
2025-07-28 16:32:58,809 - INFO - 成功插入的数据ID: ['FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDM9F1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMAF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMBF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMCF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMDF1', 'FINST-UNG66081EDHXZ37TB90D2D1D2K5Z1ZEQOUMDMEF1']
2025-07-28 16:33:03,824 - INFO - 批量插入完成，共 56 条记录
2025-07-28 16:33:03,824 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 56 条，错误: 0 条
2025-07-28 16:33:03,824 - INFO - 开始处理日期: 2025-07-28
2025-07-28 16:33:03,824 - INFO - Request Parameters - Page 1:
2025-07-28 16:33:03,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 16:33:03,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 16:33:04,402 - INFO - Response - Page 1:
2025-07-28 16:33:04,402 - INFO - 第 1 页获取到 4 条记录
2025-07-28 16:33:04,918 - INFO - 查询完成，共获取到 4 条记录
2025-07-28 16:33:04,918 - INFO - 获取到 4 条表单数据
2025-07-28 16:33:04,918 - INFO - 当前日期 2025-07-28 有 4 条MySQL数据需要处理
2025-07-28 16:33:04,918 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 16:33:04,918 - INFO - 数据同步完成！更新: 0 条，插入: 56 条，错误: 0 条
2025-07-28 16:33:04,918 - INFO - 同步完成
2025-07-28 19:30:34,051 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 19:30:34,051 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 19:30:34,051 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 19:30:34,208 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 188 条记录
2025-07-28 19:30:34,208 - INFO - 获取到 6 个日期需要处理: ['2025-07-20', '2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28']
2025-07-28 19:30:34,208 - INFO - 开始处理日期: 2025-07-20
2025-07-28 19:30:34,223 - INFO - Request Parameters - Page 1:
2025-07-28 19:30:34,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:34,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:42,351 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2878983B-75AE-7807-A8B2-A259D33753FE Response: {'code': 'ServiceUnavailable', 'requestid': '2878983B-75AE-7807-A8B2-A259D33753FE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2878983B-75AE-7807-A8B2-A259D33753FE)
2025-07-28 19:30:42,351 - INFO - 开始处理日期: 2025-07-24
2025-07-28 19:30:42,351 - INFO - Request Parameters - Page 1:
2025-07-28 19:30:42,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:42,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:50,151 - INFO - Response - Page 1:
2025-07-28 19:30:50,151 - INFO - 第 1 页获取到 50 条记录
2025-07-28 19:30:50,652 - INFO - Request Parameters - Page 2:
2025-07-28 19:30:50,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:50,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:51,371 - INFO - Response - Page 2:
2025-07-28 19:30:51,371 - INFO - 第 2 页获取到 50 条记录
2025-07-28 19:30:51,886 - INFO - Request Parameters - Page 3:
2025-07-28 19:30:51,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:51,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:52,574 - INFO - Response - Page 3:
2025-07-28 19:30:52,574 - INFO - 第 3 页获取到 50 条记录
2025-07-28 19:30:53,074 - INFO - Request Parameters - Page 4:
2025-07-28 19:30:53,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:53,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:53,809 - INFO - Response - Page 4:
2025-07-28 19:30:53,809 - INFO - 第 4 页获取到 50 条记录
2025-07-28 19:30:54,309 - INFO - Request Parameters - Page 5:
2025-07-28 19:30:54,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:54,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:54,997 - INFO - Response - Page 5:
2025-07-28 19:30:54,997 - INFO - 第 5 页获取到 50 条记录
2025-07-28 19:30:55,497 - INFO - Request Parameters - Page 6:
2025-07-28 19:30:55,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:55,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:56,248 - INFO - Response - Page 6:
2025-07-28 19:30:56,248 - INFO - 第 6 页获取到 50 条记录
2025-07-28 19:30:56,763 - INFO - Request Parameters - Page 7:
2025-07-28 19:30:56,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:56,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:57,498 - INFO - Response - Page 7:
2025-07-28 19:30:57,498 - INFO - 第 7 页获取到 50 条记录
2025-07-28 19:30:58,014 - INFO - Request Parameters - Page 8:
2025-07-28 19:30:58,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:58,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:58,733 - INFO - Response - Page 8:
2025-07-28 19:30:58,733 - INFO - 第 8 页获取到 50 条记录
2025-07-28 19:30:59,249 - INFO - Request Parameters - Page 9:
2025-07-28 19:30:59,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:30:59,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:30:59,952 - INFO - Response - Page 9:
2025-07-28 19:30:59,952 - INFO - 第 9 页获取到 50 条记录
2025-07-28 19:31:00,452 - INFO - Request Parameters - Page 10:
2025-07-28 19:31:00,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:00,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:01,156 - INFO - Response - Page 10:
2025-07-28 19:31:01,156 - INFO - 第 10 页获取到 50 条记录
2025-07-28 19:31:01,672 - INFO - Request Parameters - Page 11:
2025-07-28 19:31:01,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:01,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:02,422 - INFO - Response - Page 11:
2025-07-28 19:31:02,422 - INFO - 第 11 页获取到 50 条记录
2025-07-28 19:31:02,938 - INFO - Request Parameters - Page 12:
2025-07-28 19:31:02,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:02,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:03,454 - INFO - Response - Page 12:
2025-07-28 19:31:03,454 - INFO - 第 12 页获取到 1 条记录
2025-07-28 19:31:03,969 - INFO - 查询完成，共获取到 551 条记录
2025-07-28 19:31:03,969 - INFO - 获取到 551 条表单数据
2025-07-28 19:31:03,969 - INFO - 当前日期 2025-07-24 有 1 条MySQL数据需要处理
2025-07-28 19:31:03,969 - INFO - 开始批量插入 1 条新记录
2025-07-28 19:31:04,126 - INFO - 批量插入响应状态码: 200
2025-07-28 19:31:04,126 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 11:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A0CD3B77-4E49-74AB-B66E-0C05D281D797', 'x-acs-trace-id': '6c20cd96f078b391c99576ed879d4845', 'etag': '63/xyS6PjH3VZIzDgTmbkQQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 19:31:04,126 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71PAJXS167CB91T4R3BNBX2W1R11NDMJ6']}
2025-07-28 19:31:04,126 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-28 19:31:04,126 - INFO - 成功插入的数据ID: ['FINST-RNA66D71PAJXS167CB91T4R3BNBX2W1R11NDMJ6']
2025-07-28 19:31:09,143 - INFO - 批量插入完成，共 1 条记录
2025-07-28 19:31:09,143 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-28 19:31:09,143 - INFO - 开始处理日期: 2025-07-25
2025-07-28 19:31:09,143 - INFO - Request Parameters - Page 1:
2025-07-28 19:31:09,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:09,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:09,878 - INFO - Response - Page 1:
2025-07-28 19:31:09,878 - INFO - 第 1 页获取到 50 条记录
2025-07-28 19:31:10,394 - INFO - Request Parameters - Page 2:
2025-07-28 19:31:10,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:10,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:11,160 - INFO - Response - Page 2:
2025-07-28 19:31:11,160 - INFO - 第 2 页获取到 50 条记录
2025-07-28 19:31:11,676 - INFO - Request Parameters - Page 3:
2025-07-28 19:31:11,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:11,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:12,379 - INFO - Response - Page 3:
2025-07-28 19:31:12,379 - INFO - 第 3 页获取到 50 条记录
2025-07-28 19:31:12,879 - INFO - Request Parameters - Page 4:
2025-07-28 19:31:12,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:12,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:13,645 - INFO - Response - Page 4:
2025-07-28 19:31:13,645 - INFO - 第 4 页获取到 50 条记录
2025-07-28 19:31:14,161 - INFO - Request Parameters - Page 5:
2025-07-28 19:31:14,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:14,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:14,864 - INFO - Response - Page 5:
2025-07-28 19:31:14,864 - INFO - 第 5 页获取到 50 条记录
2025-07-28 19:31:15,380 - INFO - Request Parameters - Page 6:
2025-07-28 19:31:15,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:15,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:16,052 - INFO - Response - Page 6:
2025-07-28 19:31:16,052 - INFO - 第 6 页获取到 50 条记录
2025-07-28 19:31:16,568 - INFO - Request Parameters - Page 7:
2025-07-28 19:31:16,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:16,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:17,318 - INFO - Response - Page 7:
2025-07-28 19:31:17,318 - INFO - 第 7 页获取到 50 条记录
2025-07-28 19:31:17,819 - INFO - Request Parameters - Page 8:
2025-07-28 19:31:17,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:17,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:18,553 - INFO - Response - Page 8:
2025-07-28 19:31:18,553 - INFO - 第 8 页获取到 50 条记录
2025-07-28 19:31:19,053 - INFO - Request Parameters - Page 9:
2025-07-28 19:31:19,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:19,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:19,788 - INFO - Response - Page 9:
2025-07-28 19:31:19,788 - INFO - 第 9 页获取到 47 条记录
2025-07-28 19:31:20,288 - INFO - 查询完成，共获取到 447 条记录
2025-07-28 19:31:20,288 - INFO - 获取到 447 条表单数据
2025-07-28 19:31:20,288 - INFO - 当前日期 2025-07-25 有 1 条MySQL数据需要处理
2025-07-28 19:31:20,288 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 19:31:20,288 - INFO - 开始处理日期: 2025-07-26
2025-07-28 19:31:20,288 - INFO - Request Parameters - Page 1:
2025-07-28 19:31:20,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:20,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:21,054 - INFO - Response - Page 1:
2025-07-28 19:31:21,054 - INFO - 第 1 页获取到 50 条记录
2025-07-28 19:31:21,570 - INFO - Request Parameters - Page 2:
2025-07-28 19:31:21,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:21,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:22,242 - INFO - Response - Page 2:
2025-07-28 19:31:22,242 - INFO - 第 2 页获取到 50 条记录
2025-07-28 19:31:22,758 - INFO - Request Parameters - Page 3:
2025-07-28 19:31:22,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:22,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:23,430 - INFO - Response - Page 3:
2025-07-28 19:31:23,430 - INFO - 第 3 页获取到 50 条记录
2025-07-28 19:31:23,930 - INFO - Request Parameters - Page 4:
2025-07-28 19:31:23,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:23,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:24,634 - INFO - Response - Page 4:
2025-07-28 19:31:24,634 - INFO - 第 4 页获取到 50 条记录
2025-07-28 19:31:25,150 - INFO - Request Parameters - Page 5:
2025-07-28 19:31:25,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:25,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:25,916 - INFO - Response - Page 5:
2025-07-28 19:31:25,916 - INFO - 第 5 页获取到 50 条记录
2025-07-28 19:31:26,431 - INFO - Request Parameters - Page 6:
2025-07-28 19:31:26,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:26,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:27,150 - INFO - Response - Page 6:
2025-07-28 19:31:27,150 - INFO - 第 6 页获取到 50 条记录
2025-07-28 19:31:27,651 - INFO - Request Parameters - Page 7:
2025-07-28 19:31:27,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:27,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:28,370 - INFO - Response - Page 7:
2025-07-28 19:31:28,370 - INFO - 第 7 页获取到 50 条记录
2025-07-28 19:31:28,885 - INFO - Request Parameters - Page 8:
2025-07-28 19:31:28,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:28,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:29,589 - INFO - Response - Page 8:
2025-07-28 19:31:29,589 - INFO - 第 8 页获取到 50 条记录
2025-07-28 19:31:30,105 - INFO - Request Parameters - Page 9:
2025-07-28 19:31:30,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:30,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:30,808 - INFO - Response - Page 9:
2025-07-28 19:31:30,808 - INFO - 第 9 页获取到 50 条记录
2025-07-28 19:31:31,308 - INFO - Request Parameters - Page 10:
2025-07-28 19:31:31,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:31,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:31,934 - INFO - Response - Page 10:
2025-07-28 19:31:31,934 - INFO - 第 10 页获取到 13 条记录
2025-07-28 19:31:32,434 - INFO - 查询完成，共获取到 463 条记录
2025-07-28 19:31:32,434 - INFO - 获取到 463 条表单数据
2025-07-28 19:31:32,434 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-28 19:31:32,434 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 19:31:32,434 - INFO - 开始处理日期: 2025-07-27
2025-07-28 19:31:32,434 - INFO - Request Parameters - Page 1:
2025-07-28 19:31:32,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:32,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:33,200 - INFO - Response - Page 1:
2025-07-28 19:31:33,200 - INFO - 第 1 页获取到 50 条记录
2025-07-28 19:31:33,716 - INFO - Request Parameters - Page 2:
2025-07-28 19:31:33,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:33,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:34,513 - INFO - Response - Page 2:
2025-07-28 19:31:34,513 - INFO - 第 2 页获取到 50 条记录
2025-07-28 19:31:35,013 - INFO - Request Parameters - Page 3:
2025-07-28 19:31:35,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:35,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:35,748 - INFO - Response - Page 3:
2025-07-28 19:31:35,748 - INFO - 第 3 页获取到 50 条记录
2025-07-28 19:31:36,248 - INFO - Request Parameters - Page 4:
2025-07-28 19:31:36,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:36,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:37,014 - INFO - Response - Page 4:
2025-07-28 19:31:37,014 - INFO - 第 4 页获取到 50 条记录
2025-07-28 19:31:37,530 - INFO - Request Parameters - Page 5:
2025-07-28 19:31:37,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:37,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:38,217 - INFO - Response - Page 5:
2025-07-28 19:31:38,217 - INFO - 第 5 页获取到 50 条记录
2025-07-28 19:31:38,718 - INFO - Request Parameters - Page 6:
2025-07-28 19:31:38,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:38,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:39,530 - INFO - Response - Page 6:
2025-07-28 19:31:39,530 - INFO - 第 6 页获取到 50 条记录
2025-07-28 19:31:40,046 - INFO - Request Parameters - Page 7:
2025-07-28 19:31:40,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:40,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:40,765 - INFO - Response - Page 7:
2025-07-28 19:31:40,765 - INFO - 第 7 页获取到 50 条记录
2025-07-28 19:31:41,265 - INFO - Request Parameters - Page 8:
2025-07-28 19:31:41,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:41,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:41,969 - INFO - Response - Page 8:
2025-07-28 19:31:41,969 - INFO - 第 8 页获取到 50 条记录
2025-07-28 19:31:42,485 - INFO - Request Parameters - Page 9:
2025-07-28 19:31:42,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:42,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:43,235 - INFO - Response - Page 9:
2025-07-28 19:31:43,235 - INFO - 第 9 页获取到 50 条记录
2025-07-28 19:31:43,735 - INFO - Request Parameters - Page 10:
2025-07-28 19:31:43,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:43,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:44,470 - INFO - Response - Page 10:
2025-07-28 19:31:44,470 - INFO - 第 10 页获取到 50 条记录
2025-07-28 19:31:44,970 - INFO - Request Parameters - Page 11:
2025-07-28 19:31:44,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:44,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:45,627 - INFO - Response - Page 11:
2025-07-28 19:31:45,627 - INFO - 第 11 页获取到 18 条记录
2025-07-28 19:31:46,142 - INFO - 查询完成，共获取到 518 条记录
2025-07-28 19:31:46,142 - INFO - 获取到 518 条表单数据
2025-07-28 19:31:46,142 - INFO - 当前日期 2025-07-27 有 165 条MySQL数据需要处理
2025-07-28 19:31:46,142 - INFO - 开始批量插入 24 条新记录
2025-07-28 19:31:46,361 - INFO - 批量插入响应状态码: 200
2025-07-28 19:31:46,361 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 11:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B74F81B0-D5E9-7DA2-A1B0-56869E842401', 'x-acs-trace-id': 'a9957b4ef9adafde6e46502b68a33611', 'etag': '1+kZCop/i+vE4J7GrxsC1fw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 19:31:46,361 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDM73', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDM83', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDM93', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMA3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMB3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMC3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMD3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDME3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMF3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMG3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMH3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMI3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMJ3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMK3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDML3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMM3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMN3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMO3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMP3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMQ3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMR3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMS3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMT3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMU3']}
2025-07-28 19:31:46,361 - INFO - 批量插入表单数据成功，批次 1，共 24 条记录
2025-07-28 19:31:46,361 - INFO - 成功插入的数据ID: ['FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDM73', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDM83', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDM93', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMA3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMB3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMC3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMD3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDME3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMF3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMG3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMH3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMI3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMJ3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMK3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDML3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMM3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMN3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMO3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMP3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMQ3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMR3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMS3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMT3', 'FINST-MUC66Q817EJX6E02E7WP5CQWKUQR2YMN21NDMU3']
2025-07-28 19:31:51,379 - INFO - 批量插入完成，共 24 条记录
2025-07-28 19:31:51,379 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 24 条，错误: 0 条
2025-07-28 19:31:51,379 - INFO - 开始处理日期: 2025-07-28
2025-07-28 19:31:51,379 - INFO - Request Parameters - Page 1:
2025-07-28 19:31:51,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:31:51,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:31:51,926 - INFO - Response - Page 1:
2025-07-28 19:31:51,926 - INFO - 第 1 页获取到 4 条记录
2025-07-28 19:31:52,426 - INFO - 查询完成，共获取到 4 条记录
2025-07-28 19:31:52,426 - INFO - 获取到 4 条表单数据
2025-07-28 19:31:52,426 - INFO - 当前日期 2025-07-28 有 4 条MySQL数据需要处理
2025-07-28 19:31:52,426 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 19:31:52,426 - INFO - 数据同步完成！更新: 0 条，插入: 25 条，错误: 1 条
2025-07-28 19:32:52,466 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 19:32:52,466 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 19:32:52,466 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 19:32:52,637 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 567 条记录
2025-07-28 19:32:52,637 - INFO - 获取到 2 个日期需要处理: ['2025-07-27', '2025-07-28']
2025-07-28 19:32:52,637 - INFO - 开始处理日期: 2025-07-27
2025-07-28 19:32:52,637 - INFO - Request Parameters - Page 1:
2025-07-28 19:32:52,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:32:52,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:32:53,356 - INFO - Response - Page 1:
2025-07-28 19:32:53,356 - INFO - 第 1 页获取到 50 条记录
2025-07-28 19:32:53,857 - INFO - Request Parameters - Page 2:
2025-07-28 19:32:53,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:32:53,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:32:54,560 - INFO - Response - Page 2:
2025-07-28 19:32:54,560 - INFO - 第 2 页获取到 50 条记录
2025-07-28 19:32:55,060 - INFO - Request Parameters - Page 3:
2025-07-28 19:32:55,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:32:55,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:32:55,779 - INFO - Response - Page 3:
2025-07-28 19:32:55,779 - INFO - 第 3 页获取到 50 条记录
2025-07-28 19:32:56,280 - INFO - Request Parameters - Page 4:
2025-07-28 19:32:56,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:32:56,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:32:57,030 - INFO - Response - Page 4:
2025-07-28 19:32:57,030 - INFO - 第 4 页获取到 50 条记录
2025-07-28 19:32:57,546 - INFO - Request Parameters - Page 5:
2025-07-28 19:32:57,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:32:57,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:32:58,296 - INFO - Response - Page 5:
2025-07-28 19:32:58,296 - INFO - 第 5 页获取到 50 条记录
2025-07-28 19:32:58,812 - INFO - Request Parameters - Page 6:
2025-07-28 19:32:58,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:32:58,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:32:59,546 - INFO - Response - Page 6:
2025-07-28 19:32:59,546 - INFO - 第 6 页获取到 50 条记录
2025-07-28 19:33:00,062 - INFO - Request Parameters - Page 7:
2025-07-28 19:33:00,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:33:00,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:33:00,688 - INFO - Response - Page 7:
2025-07-28 19:33:00,688 - INFO - 第 7 页获取到 50 条记录
2025-07-28 19:33:01,203 - INFO - Request Parameters - Page 8:
2025-07-28 19:33:01,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:33:01,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:33:01,922 - INFO - Response - Page 8:
2025-07-28 19:33:01,922 - INFO - 第 8 页获取到 50 条记录
2025-07-28 19:33:02,423 - INFO - Request Parameters - Page 9:
2025-07-28 19:33:02,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:33:02,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:33:03,110 - INFO - Response - Page 9:
2025-07-28 19:33:03,110 - INFO - 第 9 页获取到 50 条记录
2025-07-28 19:33:03,611 - INFO - Request Parameters - Page 10:
2025-07-28 19:33:03,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:33:03,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:33:04,361 - INFO - Response - Page 10:
2025-07-28 19:33:04,361 - INFO - 第 10 页获取到 50 条记录
2025-07-28 19:33:04,877 - INFO - Request Parameters - Page 11:
2025-07-28 19:33:04,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:33:04,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:33:05,533 - INFO - Response - Page 11:
2025-07-28 19:33:05,533 - INFO - 第 11 页获取到 42 条记录
2025-07-28 19:33:06,049 - INFO - 查询完成，共获取到 542 条记录
2025-07-28 19:33:06,049 - INFO - 获取到 542 条表单数据
2025-07-28 19:33:06,049 - INFO - 当前日期 2025-07-27 有 542 条MySQL数据需要处理
2025-07-28 19:33:06,065 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 19:33:06,065 - INFO - 开始处理日期: 2025-07-28
2025-07-28 19:33:06,065 - INFO - Request Parameters - Page 1:
2025-07-28 19:33:06,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 19:33:06,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 19:33:06,643 - INFO - Response - Page 1:
2025-07-28 19:33:06,643 - INFO - 第 1 页获取到 4 条记录
2025-07-28 19:33:07,159 - INFO - 查询完成，共获取到 4 条记录
2025-07-28 19:33:07,159 - INFO - 获取到 4 条表单数据
2025-07-28 19:33:07,159 - INFO - 当前日期 2025-07-28 有 4 条MySQL数据需要处理
2025-07-28 19:33:07,159 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 19:33:07,159 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 19:33:07,159 - INFO - 同步完成
2025-07-28 22:30:33,332 - INFO - 使用默认增量同步（当天更新数据）
2025-07-28 22:30:33,332 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-28 22:30:33,332 - INFO - 查询参数: ('2025-07-28',)
2025-07-28 22:30:33,504 - INFO - MySQL查询成功，增量数据（日期: 2025-07-28），共获取 225 条记录
2025-07-28 22:30:33,504 - INFO - 获取到 6 个日期需要处理: ['2025-07-20', '2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27', '2025-07-28']
2025-07-28 22:30:33,504 - INFO - 开始处理日期: 2025-07-20
2025-07-28 22:30:33,504 - INFO - Request Parameters - Page 1:
2025-07-28 22:30:33,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:33,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:41,632 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 914B3699-538B-7F18-8ED4-8635288BC798 Response: {'code': 'ServiceUnavailable', 'requestid': '914B3699-538B-7F18-8ED4-8635288BC798', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 914B3699-538B-7F18-8ED4-8635288BC798)
2025-07-28 22:30:41,632 - INFO - 开始处理日期: 2025-07-24
2025-07-28 22:30:41,632 - INFO - Request Parameters - Page 1:
2025-07-28 22:30:41,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:41,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:49,760 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6A968FB5-CFAA-7BD6-A87B-FA65C635782D Response: {'code': 'ServiceUnavailable', 'requestid': '6A968FB5-CFAA-7BD6-A87B-FA65C635782D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6A968FB5-CFAA-7BD6-A87B-FA65C635782D)
2025-07-28 22:30:49,760 - INFO - 开始处理日期: 2025-07-25
2025-07-28 22:30:49,760 - INFO - Request Parameters - Page 1:
2025-07-28 22:30:49,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:49,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:52,464 - INFO - Response - Page 1:
2025-07-28 22:30:52,464 - INFO - 第 1 页获取到 50 条记录
2025-07-28 22:30:52,980 - INFO - Request Parameters - Page 2:
2025-07-28 22:30:52,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:52,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:53,699 - INFO - Response - Page 2:
2025-07-28 22:30:53,699 - INFO - 第 2 页获取到 50 条记录
2025-07-28 22:30:54,215 - INFO - Request Parameters - Page 3:
2025-07-28 22:30:54,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:54,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:54,965 - INFO - Response - Page 3:
2025-07-28 22:30:54,965 - INFO - 第 3 页获取到 50 条记录
2025-07-28 22:30:55,481 - INFO - Request Parameters - Page 4:
2025-07-28 22:30:55,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:55,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:56,231 - INFO - Response - Page 4:
2025-07-28 22:30:56,231 - INFO - 第 4 页获取到 50 条记录
2025-07-28 22:30:56,732 - INFO - Request Parameters - Page 5:
2025-07-28 22:30:56,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:56,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:57,451 - INFO - Response - Page 5:
2025-07-28 22:30:57,451 - INFO - 第 5 页获取到 50 条记录
2025-07-28 22:30:57,951 - INFO - Request Parameters - Page 6:
2025-07-28 22:30:57,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:57,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:58,639 - INFO - Response - Page 6:
2025-07-28 22:30:58,639 - INFO - 第 6 页获取到 50 条记录
2025-07-28 22:30:59,154 - INFO - Request Parameters - Page 7:
2025-07-28 22:30:59,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:30:59,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:30:59,858 - INFO - Response - Page 7:
2025-07-28 22:30:59,858 - INFO - 第 7 页获取到 50 条记录
2025-07-28 22:31:00,358 - INFO - Request Parameters - Page 8:
2025-07-28 22:31:00,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:00,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:01,108 - INFO - Response - Page 8:
2025-07-28 22:31:01,108 - INFO - 第 8 页获取到 50 条记录
2025-07-28 22:31:01,624 - INFO - Request Parameters - Page 9:
2025-07-28 22:31:01,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:01,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:02,281 - INFO - Response - Page 9:
2025-07-28 22:31:02,281 - INFO - 第 9 页获取到 47 条记录
2025-07-28 22:31:02,781 - INFO - 查询完成，共获取到 447 条记录
2025-07-28 22:31:02,781 - INFO - 获取到 447 条表单数据
2025-07-28 22:31:02,781 - INFO - 当前日期 2025-07-25 有 1 条MySQL数据需要处理
2025-07-28 22:31:02,781 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 22:31:02,781 - INFO - 开始处理日期: 2025-07-26
2025-07-28 22:31:02,781 - INFO - Request Parameters - Page 1:
2025-07-28 22:31:02,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:02,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:03,453 - INFO - Response - Page 1:
2025-07-28 22:31:03,453 - INFO - 第 1 页获取到 50 条记录
2025-07-28 22:31:03,953 - INFO - Request Parameters - Page 2:
2025-07-28 22:31:03,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:03,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:04,610 - INFO - Response - Page 2:
2025-07-28 22:31:04,610 - INFO - 第 2 页获取到 50 条记录
2025-07-28 22:31:05,126 - INFO - Request Parameters - Page 3:
2025-07-28 22:31:05,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:05,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:05,766 - INFO - Response - Page 3:
2025-07-28 22:31:05,766 - INFO - 第 3 页获取到 50 条记录
2025-07-28 22:31:06,282 - INFO - Request Parameters - Page 4:
2025-07-28 22:31:06,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:06,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:07,080 - INFO - Response - Page 4:
2025-07-28 22:31:07,080 - INFO - 第 4 页获取到 50 条记录
2025-07-28 22:31:07,595 - INFO - Request Parameters - Page 5:
2025-07-28 22:31:07,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:07,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:08,346 - INFO - Response - Page 5:
2025-07-28 22:31:08,346 - INFO - 第 5 页获取到 50 条记录
2025-07-28 22:31:08,846 - INFO - Request Parameters - Page 6:
2025-07-28 22:31:08,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:08,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:09,581 - INFO - Response - Page 6:
2025-07-28 22:31:09,581 - INFO - 第 6 页获取到 50 条记录
2025-07-28 22:31:10,096 - INFO - Request Parameters - Page 7:
2025-07-28 22:31:10,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:10,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:10,800 - INFO - Response - Page 7:
2025-07-28 22:31:10,800 - INFO - 第 7 页获取到 50 条记录
2025-07-28 22:31:11,316 - INFO - Request Parameters - Page 8:
2025-07-28 22:31:11,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:11,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:12,097 - INFO - Response - Page 8:
2025-07-28 22:31:12,097 - INFO - 第 8 页获取到 50 条记录
2025-07-28 22:31:12,613 - INFO - Request Parameters - Page 9:
2025-07-28 22:31:12,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:12,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:13,332 - INFO - Response - Page 9:
2025-07-28 22:31:13,332 - INFO - 第 9 页获取到 50 条记录
2025-07-28 22:31:13,848 - INFO - Request Parameters - Page 10:
2025-07-28 22:31:13,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:13,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:14,411 - INFO - Response - Page 10:
2025-07-28 22:31:14,411 - INFO - 第 10 页获取到 13 条记录
2025-07-28 22:31:14,911 - INFO - 查询完成，共获取到 463 条记录
2025-07-28 22:31:14,911 - INFO - 获取到 463 条表单数据
2025-07-28 22:31:14,911 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-28 22:31:14,911 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 22:31:14,911 - INFO - 开始处理日期: 2025-07-27
2025-07-28 22:31:14,911 - INFO - Request Parameters - Page 1:
2025-07-28 22:31:14,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:14,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:15,614 - INFO - Response - Page 1:
2025-07-28 22:31:15,614 - INFO - 第 1 页获取到 50 条记录
2025-07-28 22:31:16,130 - INFO - Request Parameters - Page 2:
2025-07-28 22:31:16,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:16,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:16,849 - INFO - Response - Page 2:
2025-07-28 22:31:16,849 - INFO - 第 2 页获取到 50 条记录
2025-07-28 22:31:17,365 - INFO - Request Parameters - Page 3:
2025-07-28 22:31:17,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:17,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:18,131 - INFO - Response - Page 3:
2025-07-28 22:31:18,131 - INFO - 第 3 页获取到 50 条记录
2025-07-28 22:31:18,631 - INFO - Request Parameters - Page 4:
2025-07-28 22:31:18,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:18,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:19,475 - INFO - Response - Page 4:
2025-07-28 22:31:19,475 - INFO - 第 4 页获取到 50 条记录
2025-07-28 22:31:19,991 - INFO - Request Parameters - Page 5:
2025-07-28 22:31:19,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:19,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:20,679 - INFO - Response - Page 5:
2025-07-28 22:31:20,679 - INFO - 第 5 页获取到 50 条记录
2025-07-28 22:31:21,194 - INFO - Request Parameters - Page 6:
2025-07-28 22:31:21,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:21,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:21,929 - INFO - Response - Page 6:
2025-07-28 22:31:21,929 - INFO - 第 6 页获取到 50 条记录
2025-07-28 22:31:22,445 - INFO - Request Parameters - Page 7:
2025-07-28 22:31:22,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:22,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:23,117 - INFO - Response - Page 7:
2025-07-28 22:31:23,117 - INFO - 第 7 页获取到 50 条记录
2025-07-28 22:31:23,633 - INFO - Request Parameters - Page 8:
2025-07-28 22:31:23,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:23,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:24,368 - INFO - Response - Page 8:
2025-07-28 22:31:24,368 - INFO - 第 8 页获取到 50 条记录
2025-07-28 22:31:24,868 - INFO - Request Parameters - Page 9:
2025-07-28 22:31:24,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:24,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:25,556 - INFO - Response - Page 9:
2025-07-28 22:31:25,556 - INFO - 第 9 页获取到 50 条记录
2025-07-28 22:31:26,071 - INFO - Request Parameters - Page 10:
2025-07-28 22:31:26,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:26,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:26,728 - INFO - Response - Page 10:
2025-07-28 22:31:26,728 - INFO - 第 10 页获取到 50 条记录
2025-07-28 22:31:27,244 - INFO - Request Parameters - Page 11:
2025-07-28 22:31:27,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:27,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:27,885 - INFO - Response - Page 11:
2025-07-28 22:31:27,885 - INFO - 第 11 页获取到 42 条记录
2025-07-28 22:31:28,385 - INFO - 查询完成，共获取到 542 条记录
2025-07-28 22:31:28,385 - INFO - 获取到 542 条表单数据
2025-07-28 22:31:28,385 - INFO - 当前日期 2025-07-27 有 165 条MySQL数据需要处理
2025-07-28 22:31:28,400 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 22:31:28,400 - INFO - 开始处理日期: 2025-07-28
2025-07-28 22:31:28,400 - INFO - Request Parameters - Page 1:
2025-07-28 22:31:28,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:31:28,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:31:28,948 - INFO - Response - Page 1:
2025-07-28 22:31:28,948 - INFO - 第 1 页获取到 4 条记录
2025-07-28 22:31:29,463 - INFO - 查询完成，共获取到 4 条记录
2025-07-28 22:31:29,463 - INFO - 获取到 4 条表单数据
2025-07-28 22:31:29,463 - INFO - 当前日期 2025-07-28 有 40 条MySQL数据需要处理
2025-07-28 22:31:29,463 - INFO - 开始批量插入 36 条新记录
2025-07-28 22:31:29,713 - INFO - 批量插入响应状态码: 200
2025-07-28 22:31:29,713 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Jul 2025 14:31:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1776', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A714D64D-8D0C-772E-9BB9-48D380673874', 'x-acs-trace-id': 'b0f2dfa302db19299166f8251935f9d6', 'etag': '173ed9WWCsK8rlAcWzIvHNA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-28 22:31:29,713 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMDH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMEH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMFH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMGH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMHH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMIH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMJH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMKH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMLH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMMH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMNH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMOH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMPH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMQH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMRH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMSH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMTH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMUH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMVH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMWH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMXH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMYH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMZH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM0I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM1I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM2I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM3I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM4I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM5I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM6I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM7I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM8I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM9I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMAI1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMBI1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMCI1']}
2025-07-28 22:31:29,713 - INFO - 批量插入表单数据成功，批次 1，共 36 条记录
2025-07-28 22:31:29,713 - INFO - 成功插入的数据ID: ['FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMDH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMEH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMFH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMGH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMHH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMIH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMJH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMKH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMLH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMMH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMNH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMOH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMPH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMQH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMRH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMSH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMTH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMUH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMVH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMWH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMXH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMYH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMZH1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM0I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM1I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM2I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM3I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM4I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM5I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM6I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM7I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM8I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDM9I1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMAI1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMBI1', 'FINST-F3G66Q61VIHX06WHBYYYP55HLPS327TOH7NDMCI1']
2025-07-28 22:31:34,731 - INFO - 批量插入完成，共 36 条记录
2025-07-28 22:31:34,731 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 36 条，错误: 0 条
2025-07-28 22:31:34,731 - INFO - 数据同步完成！更新: 0 条，插入: 36 条，错误: 2 条
2025-07-28 22:32:34,771 - INFO - 开始同步昨天与今天的销售数据: 2025-07-27 至 2025-07-28
2025-07-28 22:32:34,771 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-28 22:32:34,771 - INFO - 查询参数: ('2025-07-27', '2025-07-28')
2025-07-28 22:32:34,942 - INFO - MySQL查询成功，时间段: 2025-07-27 至 2025-07-28，共获取 604 条记录
2025-07-28 22:32:34,942 - INFO - 获取到 2 个日期需要处理: ['2025-07-27', '2025-07-28']
2025-07-28 22:32:34,942 - INFO - 开始处理日期: 2025-07-27
2025-07-28 22:32:34,942 - INFO - Request Parameters - Page 1:
2025-07-28 22:32:34,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:34,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:35,708 - INFO - Response - Page 1:
2025-07-28 22:32:35,708 - INFO - 第 1 页获取到 50 条记录
2025-07-28 22:32:36,224 - INFO - Request Parameters - Page 2:
2025-07-28 22:32:36,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:36,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:36,928 - INFO - Response - Page 2:
2025-07-28 22:32:36,928 - INFO - 第 2 页获取到 50 条记录
2025-07-28 22:32:37,443 - INFO - Request Parameters - Page 3:
2025-07-28 22:32:37,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:37,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:38,147 - INFO - Response - Page 3:
2025-07-28 22:32:38,147 - INFO - 第 3 页获取到 50 条记录
2025-07-28 22:32:38,647 - INFO - Request Parameters - Page 4:
2025-07-28 22:32:38,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:38,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:39,366 - INFO - Response - Page 4:
2025-07-28 22:32:39,366 - INFO - 第 4 页获取到 50 条记录
2025-07-28 22:32:39,866 - INFO - Request Parameters - Page 5:
2025-07-28 22:32:39,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:39,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:40,585 - INFO - Response - Page 5:
2025-07-28 22:32:40,585 - INFO - 第 5 页获取到 50 条记录
2025-07-28 22:32:41,101 - INFO - Request Parameters - Page 6:
2025-07-28 22:32:41,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:41,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:41,805 - INFO - Response - Page 6:
2025-07-28 22:32:41,805 - INFO - 第 6 页获取到 50 条记录
2025-07-28 22:32:42,320 - INFO - Request Parameters - Page 7:
2025-07-28 22:32:42,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:42,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:43,039 - INFO - Response - Page 7:
2025-07-28 22:32:43,039 - INFO - 第 7 页获取到 50 条记录
2025-07-28 22:32:43,555 - INFO - Request Parameters - Page 8:
2025-07-28 22:32:43,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:43,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:44,274 - INFO - Response - Page 8:
2025-07-28 22:32:44,274 - INFO - 第 8 页获取到 50 条记录
2025-07-28 22:32:44,774 - INFO - Request Parameters - Page 9:
2025-07-28 22:32:44,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:44,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:45,494 - INFO - Response - Page 9:
2025-07-28 22:32:45,494 - INFO - 第 9 页获取到 50 条记录
2025-07-28 22:32:46,009 - INFO - Request Parameters - Page 10:
2025-07-28 22:32:46,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:46,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:46,728 - INFO - Response - Page 10:
2025-07-28 22:32:46,728 - INFO - 第 10 页获取到 50 条记录
2025-07-28 22:32:47,244 - INFO - Request Parameters - Page 11:
2025-07-28 22:32:47,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:47,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:47,979 - INFO - Response - Page 11:
2025-07-28 22:32:47,979 - INFO - 第 11 页获取到 42 条记录
2025-07-28 22:32:48,495 - INFO - 查询完成，共获取到 542 条记录
2025-07-28 22:32:48,495 - INFO - 获取到 542 条表单数据
2025-07-28 22:32:48,495 - INFO - 当前日期 2025-07-27 有 542 条MySQL数据需要处理
2025-07-28 22:32:48,510 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 22:32:48,510 - INFO - 开始处理日期: 2025-07-28
2025-07-28 22:32:48,510 - INFO - Request Parameters - Page 1:
2025-07-28 22:32:48,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-28 22:32:48,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-28 22:32:49,229 - INFO - Response - Page 1:
2025-07-28 22:32:49,229 - INFO - 第 1 页获取到 40 条记录
2025-07-28 22:32:49,730 - INFO - 查询完成，共获取到 40 条记录
2025-07-28 22:32:49,730 - INFO - 获取到 40 条表单数据
2025-07-28 22:32:49,730 - INFO - 当前日期 2025-07-28 有 40 条MySQL数据需要处理
2025-07-28 22:32:49,730 - INFO - 日期 2025-07-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 22:32:49,730 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-28 22:32:49,730 - INFO - 同步完成
