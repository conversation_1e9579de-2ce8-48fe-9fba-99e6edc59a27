2025-08-01 00:00:03,087 - INFO - =================使用默认全量同步=============
2025-08-01 00:00:05,321 - INFO - MySQL查询成功，共获取 4614 条记录
2025-08-01 00:00:05,321 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-08-01 00:00:05,353 - INFO - 开始处理日期: 2025-01
2025-08-01 00:00:05,353 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:05,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:05,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:07,134 - INFO - Response - Page 1:
2025-08-01 00:00:07,337 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:07,337 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:07,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:07,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:08,384 - INFO - Response - Page 2:
2025-08-01 00:00:08,587 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:08,587 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:08,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:08,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:09,134 - INFO - Response - Page 3:
2025-08-01 00:00:09,337 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:09,337 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:09,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:09,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:09,868 - INFO - Response - Page 4:
2025-08-01 00:00:10,071 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:10,071 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:10,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:10,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:10,618 - INFO - Response - Page 5:
2025-08-01 00:00:10,821 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:10,821 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:10,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:10,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:11,431 - INFO - Response - Page 6:
2025-08-01 00:00:11,634 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:11,634 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:11,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:11,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:12,196 - INFO - Response - Page 7:
2025-08-01 00:00:12,400 - INFO - 第 7 页获取到 82 条记录
2025-08-01 00:00:12,400 - INFO - 查询完成，共获取到 682 条记录
2025-08-01 00:00:12,400 - INFO - 获取到 682 条表单数据
2025-08-01 00:00:12,400 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-01 00:00:12,415 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 00:00:12,415 - INFO - 开始处理日期: 2025-02
2025-08-01 00:00:12,415 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:12,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:12,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:12,993 - INFO - Response - Page 1:
2025-08-01 00:00:13,196 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:13,196 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:13,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:13,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:13,759 - INFO - Response - Page 2:
2025-08-01 00:00:13,962 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:13,962 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:13,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:13,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:14,462 - INFO - Response - Page 3:
2025-08-01 00:00:14,665 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:14,665 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:14,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:14,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:15,196 - INFO - Response - Page 4:
2025-08-01 00:00:15,400 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:15,400 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:15,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:15,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:15,962 - INFO - Response - Page 5:
2025-08-01 00:00:16,165 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:16,165 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:16,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:16,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:16,665 - INFO - Response - Page 6:
2025-08-01 00:00:16,868 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:16,868 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:16,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:16,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:17,353 - INFO - Response - Page 7:
2025-08-01 00:00:17,556 - INFO - 第 7 页获取到 70 条记录
2025-08-01 00:00:17,556 - INFO - 查询完成，共获取到 670 条记录
2025-08-01 00:00:17,556 - INFO - 获取到 670 条表单数据
2025-08-01 00:00:17,556 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-01 00:00:17,571 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 00:00:17,571 - INFO - 开始处理日期: 2025-03
2025-08-01 00:00:17,571 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:17,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:17,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:18,274 - INFO - Response - Page 1:
2025-08-01 00:00:18,478 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:18,478 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:18,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:18,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:18,962 - INFO - Response - Page 2:
2025-08-01 00:00:19,165 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:19,165 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:19,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:19,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:19,696 - INFO - Response - Page 3:
2025-08-01 00:00:19,899 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:19,899 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:19,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:19,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:20,368 - INFO - Response - Page 4:
2025-08-01 00:00:20,571 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:20,571 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:20,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:20,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:21,103 - INFO - Response - Page 5:
2025-08-01 00:00:21,306 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:21,306 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:21,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:21,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:21,899 - INFO - Response - Page 6:
2025-08-01 00:00:22,103 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:22,103 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:22,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:22,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:22,556 - INFO - Response - Page 7:
2025-08-01 00:00:22,759 - INFO - 第 7 页获取到 61 条记录
2025-08-01 00:00:22,759 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 00:00:22,759 - INFO - 获取到 661 条表单数据
2025-08-01 00:00:22,759 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-01 00:00:22,774 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 00:00:22,774 - INFO - 开始处理日期: 2025-04
2025-08-01 00:00:22,774 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:22,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:22,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:23,259 - INFO - Response - Page 1:
2025-08-01 00:00:23,462 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:23,462 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:23,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:23,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:23,993 - INFO - Response - Page 2:
2025-08-01 00:00:24,196 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:24,196 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:24,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:24,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:24,618 - INFO - Response - Page 3:
2025-08-01 00:00:24,837 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:24,837 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:24,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:24,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:25,290 - INFO - Response - Page 4:
2025-08-01 00:00:25,493 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:25,493 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:25,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:25,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:26,024 - INFO - Response - Page 5:
2025-08-01 00:00:26,228 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:26,228 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:26,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:26,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:26,728 - INFO - Response - Page 6:
2025-08-01 00:00:26,931 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:26,931 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:26,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:26,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:27,446 - INFO - Response - Page 7:
2025-08-01 00:00:27,649 - INFO - 第 7 页获取到 56 条记录
2025-08-01 00:00:27,649 - INFO - 查询完成，共获取到 656 条记录
2025-08-01 00:00:27,649 - INFO - 获取到 656 条表单数据
2025-08-01 00:00:27,649 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-01 00:00:27,665 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 00:00:27,665 - INFO - 开始处理日期: 2025-05
2025-08-01 00:00:27,665 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:27,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:27,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:28,196 - INFO - Response - Page 1:
2025-08-01 00:00:28,399 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:28,399 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:28,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:28,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:28,899 - INFO - Response - Page 2:
2025-08-01 00:00:29,103 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:29,103 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:29,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:29,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:29,571 - INFO - Response - Page 3:
2025-08-01 00:00:29,774 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:29,774 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:29,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:29,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:30,399 - INFO - Response - Page 4:
2025-08-01 00:00:30,603 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:30,603 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:30,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:30,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:31,196 - INFO - Response - Page 5:
2025-08-01 00:00:31,399 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:31,399 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:31,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:31,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:31,962 - INFO - Response - Page 6:
2025-08-01 00:00:32,165 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:32,165 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:32,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:32,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:32,649 - INFO - Response - Page 7:
2025-08-01 00:00:32,852 - INFO - 第 7 页获取到 65 条记录
2025-08-01 00:00:32,852 - INFO - 查询完成，共获取到 665 条记录
2025-08-01 00:00:32,852 - INFO - 获取到 665 条表单数据
2025-08-01 00:00:32,852 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-01 00:00:32,868 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 00:00:32,868 - INFO - 开始处理日期: 2025-06
2025-08-01 00:00:32,868 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:32,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:32,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:33,431 - INFO - Response - Page 1:
2025-08-01 00:00:33,634 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:33,634 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:33,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:33,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:34,149 - INFO - Response - Page 2:
2025-08-01 00:00:34,352 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:34,352 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:34,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:34,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:34,868 - INFO - Response - Page 3:
2025-08-01 00:00:35,071 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:35,071 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:35,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:35,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:35,540 - INFO - Response - Page 4:
2025-08-01 00:00:35,743 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:35,743 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:35,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:35,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:36,274 - INFO - Response - Page 5:
2025-08-01 00:00:36,477 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:36,477 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:36,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:36,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:37,040 - INFO - Response - Page 6:
2025-08-01 00:00:37,243 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:37,243 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:37,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:37,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:37,790 - INFO - Response - Page 7:
2025-08-01 00:00:37,993 - INFO - 第 7 页获取到 61 条记录
2025-08-01 00:00:37,993 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 00:00:37,993 - INFO - 获取到 661 条表单数据
2025-08-01 00:00:37,993 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-01 00:00:38,009 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 00:00:38,009 - INFO - 开始处理日期: 2025-07
2025-08-01 00:00:38,009 - INFO - Request Parameters - Page 1:
2025-08-01 00:00:38,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:38,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:38,618 - INFO - Response - Page 1:
2025-08-01 00:00:38,821 - INFO - 第 1 页获取到 100 条记录
2025-08-01 00:00:38,821 - INFO - Request Parameters - Page 2:
2025-08-01 00:00:38,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:38,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:39,540 - INFO - Response - Page 2:
2025-08-01 00:00:39,743 - INFO - 第 2 页获取到 100 条记录
2025-08-01 00:00:39,743 - INFO - Request Parameters - Page 3:
2025-08-01 00:00:39,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:39,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:40,368 - INFO - Response - Page 3:
2025-08-01 00:00:40,571 - INFO - 第 3 页获取到 100 条记录
2025-08-01 00:00:40,571 - INFO - Request Parameters - Page 4:
2025-08-01 00:00:40,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:40,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:41,056 - INFO - Response - Page 4:
2025-08-01 00:00:41,259 - INFO - 第 4 页获取到 100 条记录
2025-08-01 00:00:41,259 - INFO - Request Parameters - Page 5:
2025-08-01 00:00:41,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:41,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:41,821 - INFO - Response - Page 5:
2025-08-01 00:00:42,024 - INFO - 第 5 页获取到 100 条记录
2025-08-01 00:00:42,024 - INFO - Request Parameters - Page 6:
2025-08-01 00:00:42,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:42,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:42,556 - INFO - Response - Page 6:
2025-08-01 00:00:42,759 - INFO - 第 6 页获取到 100 条记录
2025-08-01 00:00:42,759 - INFO - Request Parameters - Page 7:
2025-08-01 00:00:42,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 00:00:42,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 00:00:43,087 - INFO - Response - Page 7:
2025-08-01 00:00:43,290 - INFO - 第 7 页获取到 19 条记录
2025-08-01 00:00:43,290 - INFO - 查询完成，共获取到 619 条记录
2025-08-01 00:00:43,290 - INFO - 获取到 619 条表单数据
2025-08-01 00:00:43,290 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-01 00:00:43,290 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM85
2025-08-01 00:00:43,743 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM85
2025-08-01 00:00:43,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62440.0, 'new_value': 64680.0}, {'field': 'total_amount', 'old_value': 64700.0, 'new_value': 66940.0}, {'field': 'order_count', 'old_value': 724, 'new_value': 752}]
2025-08-01 00:00:43,743 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMD5
2025-08-01 00:00:44,243 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMD5
2025-08-01 00:00:44,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6778.9, 'new_value': 6981.45}, {'field': 'offline_amount', 'old_value': 95019.0, 'new_value': 94727.0}, {'field': 'total_amount', 'old_value': 101797.9, 'new_value': 101708.45}, {'field': 'order_count', 'old_value': 2318, 'new_value': 2341}]
2025-08-01 00:00:44,243 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS7
2025-08-01 00:00:44,806 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS7
2025-08-01 00:00:44,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39386.0, 'new_value': 43886.0}, {'field': 'total_amount', 'old_value': 39386.0, 'new_value': 43886.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-08-01 00:00:44,806 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV7
2025-08-01 00:00:45,227 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV7
2025-08-01 00:00:45,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90126.02, 'new_value': 92676.89}, {'field': 'offline_amount', 'old_value': 89871.2, 'new_value': 92608.62}, {'field': 'total_amount', 'old_value': 179997.22, 'new_value': 185285.51}, {'field': 'order_count', 'old_value': 6555, 'new_value': 6757}]
2025-08-01 00:00:45,227 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ5
2025-08-01 00:00:45,681 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ5
2025-08-01 00:00:45,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38680.0, 'new_value': 41660.0}, {'field': 'total_amount', 'old_value': 38680.0, 'new_value': 41660.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-08-01 00:00:45,681 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW7
2025-08-01 00:00:46,149 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW7
2025-08-01 00:00:46,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103894.47, 'new_value': 106426.11}, {'field': 'offline_amount', 'old_value': 554587.52, 'new_value': 572267.08}, {'field': 'total_amount', 'old_value': 658481.99, 'new_value': 678693.19}, {'field': 'order_count', 'old_value': 9017, 'new_value': 9276}]
2025-08-01 00:00:46,149 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMQ8
2025-08-01 00:00:46,587 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMQ8
2025-08-01 00:00:46,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56981.59, 'new_value': 58728.27}, {'field': 'offline_amount', 'old_value': 793820.75, 'new_value': 820988.12}, {'field': 'total_amount', 'old_value': 850802.34, 'new_value': 879716.39}, {'field': 'order_count', 'old_value': 3152, 'new_value': 3241}]
2025-08-01 00:00:46,587 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ7
2025-08-01 00:00:47,040 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ7
2025-08-01 00:00:47,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7663.88, 'new_value': 8071.88}, {'field': 'offline_amount', 'old_value': 84208.0, 'new_value': 85107.0}, {'field': 'total_amount', 'old_value': 91871.88, 'new_value': 93178.88}, {'field': 'order_count', 'old_value': 97, 'new_value': 100}]
2025-08-01 00:00:47,040 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM18
2025-08-01 00:00:47,602 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM18
2025-08-01 00:00:47,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126719.13, 'new_value': 133123.55}, {'field': 'offline_amount', 'old_value': 701383.03, 'new_value': 722383.03}, {'field': 'total_amount', 'old_value': 828102.16, 'new_value': 855506.58}, {'field': 'order_count', 'old_value': 2981, 'new_value': 3141}]
2025-08-01 00:00:47,602 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8
2025-08-01 00:00:48,040 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8
2025-08-01 00:00:48,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133071.7, 'new_value': 135424.7}, {'field': 'offline_amount', 'old_value': 269457.0, 'new_value': 274404.0}, {'field': 'total_amount', 'old_value': 402528.7, 'new_value': 409828.7}, {'field': 'order_count', 'old_value': 485, 'new_value': 500}]
2025-08-01 00:00:48,040 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMS8
2025-08-01 00:00:48,509 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMS8
2025-08-01 00:00:48,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1949854.61, 'new_value': 2006946.28}, {'field': 'total_amount', 'old_value': 1949854.61, 'new_value': 2006946.28}, {'field': 'order_count', 'old_value': 20955, 'new_value': 21758}]
2025-08-01 00:00:48,509 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM38
2025-08-01 00:00:48,946 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM38
2025-08-01 00:00:48,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150773.4, 'new_value': 156839.4}, {'field': 'offline_amount', 'old_value': 92869.4, 'new_value': 94504.0}, {'field': 'total_amount', 'old_value': 243642.8, 'new_value': 251343.4}, {'field': 'order_count', 'old_value': 1646, 'new_value': 1707}]
2025-08-01 00:00:48,946 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM48
2025-08-01 00:00:49,384 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM48
2025-08-01 00:00:49,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68846.59, 'new_value': 71021.67}, {'field': 'offline_amount', 'old_value': 443197.1, 'new_value': 453217.3}, {'field': 'total_amount', 'old_value': 512043.69, 'new_value': 524238.97}, {'field': 'order_count', 'old_value': 2605, 'new_value': 2683}]
2025-08-01 00:00:49,384 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMU8
2025-08-01 00:00:49,790 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMU8
2025-08-01 00:00:49,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112243.27, 'new_value': 115763.3}, {'field': 'offline_amount', 'old_value': 119442.55, 'new_value': 124185.86}, {'field': 'total_amount', 'old_value': 231685.82, 'new_value': 239949.16}, {'field': 'order_count', 'old_value': 12552, 'new_value': 12993}]
2025-08-01 00:00:49,790 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV6
2025-08-01 00:00:50,212 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV6
2025-08-01 00:00:50,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6244.0, 'new_value': 6245.0}, {'field': 'offline_amount', 'old_value': 76775.0, 'new_value': 77231.0}, {'field': 'total_amount', 'old_value': 83019.0, 'new_value': 83476.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-08-01 00:00:50,212 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ6
2025-08-01 00:00:50,634 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMZ6
2025-08-01 00:00:50,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157364.0, 'new_value': 163384.5}, {'field': 'total_amount', 'old_value': 157364.0, 'new_value': 163384.5}, {'field': 'order_count', 'old_value': 408, 'new_value': 421}]
2025-08-01 00:00:50,634 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM47
2025-08-01 00:00:51,087 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM47
2025-08-01 00:00:51,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46082.0, 'new_value': 46140.0}, {'field': 'total_amount', 'old_value': 46082.0, 'new_value': 46140.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 82}]
2025-08-01 00:00:51,087 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM88
2025-08-01 00:00:51,587 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM88
2025-08-01 00:00:51,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132444.8, 'new_value': 135976.4}, {'field': 'total_amount', 'old_value': 132444.8, 'new_value': 135976.4}, {'field': 'order_count', 'old_value': 217, 'new_value': 224}]
2025-08-01 00:00:51,587 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMA8
2025-08-01 00:00:52,149 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMA8
2025-08-01 00:00:52,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137815.0, 'new_value': 141077.0}, {'field': 'total_amount', 'old_value': 141885.0, 'new_value': 145147.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 76}]
2025-08-01 00:00:52,149 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMC8
2025-08-01 00:00:52,602 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMC8
2025-08-01 00:00:52,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 278691.99, 'new_value': 285223.21}, {'field': 'offline_amount', 'old_value': 856945.78, 'new_value': 880478.52}, {'field': 'total_amount', 'old_value': 1135637.77, 'new_value': 1165701.73}, {'field': 'order_count', 'old_value': 7169, 'new_value': 7352}]
2025-08-01 00:00:52,602 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCME8
2025-08-01 00:00:53,040 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCME8
2025-08-01 00:00:53,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 573619.0, 'new_value': 582884.0}, {'field': 'total_amount', 'old_value': 653619.0, 'new_value': 662884.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 90}]
2025-08-01 00:00:53,040 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMF8
2025-08-01 00:00:53,587 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMF8
2025-08-01 00:00:53,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41794.0, 'new_value': 41894.0}, {'field': 'total_amount', 'old_value': 41794.0, 'new_value': 41894.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 97}]
2025-08-01 00:00:53,587 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC
2025-08-01 00:00:53,993 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC
2025-08-01 00:00:53,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31115.0, 'new_value': 31582.0}, {'field': 'total_amount', 'old_value': 31115.0, 'new_value': 31582.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 177}]
2025-08-01 00:00:53,993 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP
2025-08-01 00:00:54,415 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP
2025-08-01 00:00:54,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132733.0, 'new_value': 135410.0}, {'field': 'total_amount', 'old_value': 132733.0, 'new_value': 135410.0}, {'field': 'order_count', 'old_value': 508, 'new_value': 522}]
2025-08-01 00:00:54,415 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS
2025-08-01 00:00:54,790 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS
2025-08-01 00:00:54,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98546.4, 'new_value': 99086.4}, {'field': 'total_amount', 'old_value': 98546.4, 'new_value': 99086.4}, {'field': 'order_count', 'old_value': 557, 'new_value': 567}]
2025-08-01 00:00:54,790 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMI8
2025-08-01 00:00:55,180 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMI8
2025-08-01 00:00:55,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263560.02, 'new_value': 267459.02}, {'field': 'total_amount', 'old_value': 304588.31, 'new_value': 308487.31}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-08-01 00:00:55,180 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMJ8
2025-08-01 00:00:55,618 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMJ8
2025-08-01 00:00:55,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103051.0, 'new_value': 128561.0}, {'field': 'total_amount', 'old_value': 103051.0, 'new_value': 128561.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 34}]
2025-08-01 00:00:55,618 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMK8
2025-08-01 00:00:56,071 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMK8
2025-08-01 00:00:56,071 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75785.0, 'new_value': 75813.0}, {'field': 'offline_amount', 'old_value': 368944.0, 'new_value': 375119.0}, {'field': 'total_amount', 'old_value': 444729.0, 'new_value': 450932.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 218}]
2025-08-01 00:00:56,071 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCML8
2025-08-01 00:00:56,540 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCML8
2025-08-01 00:00:56,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308343.0, 'new_value': 311034.0}, {'field': 'total_amount', 'old_value': 317411.0, 'new_value': 320102.0}, {'field': 'order_count', 'old_value': 276, 'new_value': 280}]
2025-08-01 00:00:56,540 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW
2025-08-01 00:00:57,009 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW
2025-08-01 00:00:57,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31793.5, 'new_value': 32110.4}, {'field': 'offline_amount', 'old_value': 69763.0, 'new_value': 70963.0}, {'field': 'total_amount', 'old_value': 101556.5, 'new_value': 103073.4}, {'field': 'order_count', 'old_value': 89, 'new_value': 91}]
2025-08-01 00:00:57,009 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY
2025-08-01 00:00:57,415 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY
2025-08-01 00:00:57,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203834.0, 'new_value': 220223.0}, {'field': 'total_amount', 'old_value': 230564.0, 'new_value': 246953.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 70}]
2025-08-01 00:00:57,415 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM11
2025-08-01 00:00:57,868 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM11
2025-08-01 00:00:57,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 560792.0, 'new_value': 573339.0}, {'field': 'total_amount', 'old_value': 560795.0, 'new_value': 573342.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-08-01 00:00:57,884 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM61
2025-08-01 00:00:58,274 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM61
2025-08-01 00:00:58,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141329.36, 'new_value': 144112.56}, {'field': 'total_amount', 'old_value': 141330.36, 'new_value': 144113.56}, {'field': 'order_count', 'old_value': 2570, 'new_value': 2642}]
2025-08-01 00:00:58,274 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG1
2025-08-01 00:00:58,665 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG1
2025-08-01 00:00:58,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52258.91, 'new_value': 53401.33}, {'field': 'total_amount', 'old_value': 52258.91, 'new_value': 53401.33}, {'field': 'order_count', 'old_value': 235, 'new_value': 241}]
2025-08-01 00:00:58,665 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH1
2025-08-01 00:00:59,040 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH1
2025-08-01 00:00:59,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174067.86, 'new_value': 180479.24}, {'field': 'offline_amount', 'old_value': 271500.32, 'new_value': 278122.1}, {'field': 'total_amount', 'old_value': 445568.18, 'new_value': 458601.34}, {'field': 'order_count', 'old_value': 7497, 'new_value': 7951}]
2025-08-01 00:00:59,040 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ1
2025-08-01 00:00:59,524 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ1
2025-08-01 00:00:59,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354130.2, 'new_value': 362896.0}, {'field': 'total_amount', 'old_value': 354130.2, 'new_value': 362896.0}, {'field': 'order_count', 'old_value': 3891, 'new_value': 3985}]
2025-08-01 00:00:59,524 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK1
2025-08-01 00:00:59,962 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK1
2025-08-01 00:00:59,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53893.0, 'new_value': 54008.0}, {'field': 'total_amount', 'old_value': 53893.0, 'new_value': 54008.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-08-01 00:00:59,962 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMR4
2025-08-01 00:01:00,430 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMR4
2025-08-01 00:01:00,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3373.17, 'new_value': 3428.17}, {'field': 'offline_amount', 'old_value': 24964.68, 'new_value': 25863.79}, {'field': 'total_amount', 'old_value': 28337.85, 'new_value': 29291.96}, {'field': 'order_count', 'old_value': 590, 'new_value': 609}]
2025-08-01 00:01:00,430 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU1
2025-08-01 00:01:00,884 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU1
2025-08-01 00:01:00,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 954793.0, 'new_value': 984835.0}, {'field': 'total_amount', 'old_value': 954793.0, 'new_value': 984835.0}, {'field': 'order_count', 'old_value': 1081, 'new_value': 1161}]
2025-08-01 00:01:00,884 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV1
2025-08-01 00:01:01,384 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV1
2025-08-01 00:01:01,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 354515.09, 'new_value': 364440.73}, {'field': 'offline_amount', 'old_value': 81419.15, 'new_value': 83355.65}, {'field': 'total_amount', 'old_value': 435934.24, 'new_value': 447796.38}, {'field': 'order_count', 'old_value': 2022, 'new_value': 2082}]
2025-08-01 00:01:01,384 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW1
2025-08-01 00:01:01,915 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMW1
2025-08-01 00:01:01,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160992.0, 'new_value': 161291.0}, {'field': 'total_amount', 'old_value': 161291.0, 'new_value': 161590.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-08-01 00:01:01,915 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP8
2025-08-01 00:01:02,305 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP8
2025-08-01 00:01:02,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236953.23, 'new_value': 239703.23}, {'field': 'total_amount', 'old_value': 246913.23, 'new_value': 249663.23}, {'field': 'order_count', 'old_value': 484, 'new_value': 491}]
2025-08-01 00:01:02,305 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ8
2025-08-01 00:01:02,759 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ8
2025-08-01 00:01:02,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 462372.94, 'new_value': 476053.47}, {'field': 'total_amount', 'old_value': 472056.58, 'new_value': 485737.11}, {'field': 'order_count', 'old_value': 2477, 'new_value': 2544}]
2025-08-01 00:01:02,759 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM32
2025-08-01 00:01:03,165 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM32
2025-08-01 00:01:03,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124823.0, 'new_value': 129359.0}, {'field': 'total_amount', 'old_value': 124823.0, 'new_value': 129359.0}, {'field': 'order_count', 'old_value': 3859, 'new_value': 3997}]
2025-08-01 00:01:03,165 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR8
2025-08-01 00:01:03,649 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR8
2025-08-01 00:01:03,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204072.0, 'new_value': 212237.0}, {'field': 'total_amount', 'old_value': 204072.0, 'new_value': 212237.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 327}]
2025-08-01 00:01:03,649 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS8
2025-08-01 00:01:04,165 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMS8
2025-08-01 00:01:04,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13362400.0, 'new_value': 13612300.0}, {'field': 'total_amount', 'old_value': 13362400.0, 'new_value': 13612300.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-08-01 00:01:04,165 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT8
2025-08-01 00:01:04,634 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT8
2025-08-01 00:01:04,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175007.39, 'new_value': 179525.39}, {'field': 'total_amount', 'old_value': 175007.39, 'new_value': 179525.39}, {'field': 'order_count', 'old_value': 18369, 'new_value': 18834}]
2025-08-01 00:01:04,634 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV8
2025-08-01 00:01:05,102 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV8
2025-08-01 00:01:05,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303321.0, 'new_value': 314422.0}, {'field': 'total_amount', 'old_value': 303321.0, 'new_value': 314422.0}, {'field': 'order_count', 'old_value': 198, 'new_value': 204}]
2025-08-01 00:01:05,102 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW8
2025-08-01 00:01:05,493 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW8
2025-08-01 00:01:05,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 714954.0, 'new_value': 738366.0}, {'field': 'total_amount', 'old_value': 714954.0, 'new_value': 738366.0}, {'field': 'order_count', 'old_value': 177545, 'new_value': 177548}]
2025-08-01 00:01:05,493 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMV8
2025-08-01 00:01:05,962 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMV8
2025-08-01 00:01:05,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158610.8, 'new_value': 165283.8}, {'field': 'total_amount', 'old_value': 158610.8, 'new_value': 165283.8}, {'field': 'order_count', 'old_value': 957, 'new_value': 995}]
2025-08-01 00:01:05,962 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY8
2025-08-01 00:01:06,509 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY8
2025-08-01 00:01:06,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 557013.0, 'new_value': 582867.0}, {'field': 'total_amount', 'old_value': 557013.0, 'new_value': 582867.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 132}]
2025-08-01 00:01:06,509 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMQ
2025-08-01 00:01:06,993 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMQ
2025-08-01 00:01:06,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65676.5, 'new_value': 67893.18}, {'field': 'offline_amount', 'old_value': 558981.95, 'new_value': 572589.55}, {'field': 'total_amount', 'old_value': 624658.45, 'new_value': 640482.73}, {'field': 'order_count', 'old_value': 2523, 'new_value': 2586}]
2025-08-01 00:01:06,993 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM59
2025-08-01 00:01:07,446 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM59
2025-08-01 00:01:07,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600854.11, 'new_value': 620451.11}, {'field': 'total_amount', 'old_value': 606153.11, 'new_value': 625750.11}, {'field': 'order_count', 'old_value': 477, 'new_value': 499}]
2025-08-01 00:01:07,446 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM81
2025-08-01 00:01:07,899 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM81
2025-08-01 00:01:07,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75.0, 'new_value': 925.1}, {'field': 'offline_amount', 'old_value': 42073.2, 'new_value': 43495.6}, {'field': 'total_amount', 'old_value': 42148.2, 'new_value': 44420.7}, {'field': 'order_count', 'old_value': 169, 'new_value': 179}]
2025-08-01 00:01:07,899 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMA9
2025-08-01 00:01:08,290 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMA9
2025-08-01 00:01:08,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4176.7, 'new_value': 4353.5}, {'field': 'total_amount', 'old_value': 34856.7, 'new_value': 35033.5}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-08-01 00:01:08,290 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMZ8
2025-08-01 00:01:08,696 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMZ8
2025-08-01 00:01:08,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 978363.37, 'new_value': 1004438.94}, {'field': 'total_amount', 'old_value': 978363.37, 'new_value': 1004438.94}, {'field': 'order_count', 'old_value': 7084, 'new_value': 7294}]
2025-08-01 00:01:08,712 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMF9
2025-08-01 00:01:09,227 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMF9
2025-08-01 00:01:09,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 656204.95, 'new_value': 670747.95}, {'field': 'total_amount', 'old_value': 656204.95, 'new_value': 670747.95}, {'field': 'order_count', 'old_value': 2431, 'new_value': 2512}]
2025-08-01 00:01:09,227 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME2
2025-08-01 00:01:09,774 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME2
2025-08-01 00:01:09,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97408.0, 'new_value': 101088.0}, {'field': 'total_amount', 'old_value': 97408.0, 'new_value': 101088.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-08-01 00:01:09,774 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR
2025-08-01 00:01:10,337 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR
2025-08-01 00:01:10,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181574.9, 'new_value': 185845.44}, {'field': 'total_amount', 'old_value': 181574.9, 'new_value': 185845.44}, {'field': 'order_count', 'old_value': 1063, 'new_value': 1086}]
2025-08-01 00:01:10,337 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM61
2025-08-01 00:01:10,758 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM61
2025-08-01 00:01:10,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152733.0, 'new_value': 157447.0}, {'field': 'total_amount', 'old_value': 152733.0, 'new_value': 157447.0}, {'field': 'order_count', 'old_value': 173, 'new_value': 174}]
2025-08-01 00:01:10,758 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM62
2025-08-01 00:01:11,212 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM62
2025-08-01 00:01:11,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 919179.0, 'new_value': 946170.0}, {'field': 'total_amount', 'old_value': 919179.0, 'new_value': 946170.0}, {'field': 'order_count', 'old_value': 210, 'new_value': 221}]
2025-08-01 00:01:11,212 - INFO - 开始更新记录 - 表单实例ID: FINST-LOG66Q61N7GX14H068KMH7GNQLJZ1GM2M5LDM5Q
2025-08-01 00:01:11,649 - INFO - 更新表单数据成功: FINST-LOG66Q61N7GX14H068KMH7GNQLJZ1GM2M5LDM5Q
2025-08-01 00:01:11,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65598.0, 'new_value': 73830.0}, {'field': 'total_amount', 'old_value': 65598.0, 'new_value': 73830.0}, {'field': 'order_count', 'old_value': 988, 'new_value': 1151}]
2025-08-01 00:01:11,649 - INFO - 日期 2025-07 处理完成 - 更新: 62 条，插入: 0 条，错误: 0 条
2025-08-01 00:01:11,649 - INFO - 数据同步完成！更新: 62 条，插入: 0 条，错误: 0 条
2025-08-01 00:01:11,649 - INFO - =================同步完成====================
2025-08-01 03:00:02,935 - INFO - =================使用默认全量同步=============
2025-08-01 03:00:05,044 - INFO - MySQL查询成功，共获取 4614 条记录
2025-08-01 03:00:05,044 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-08-01 03:00:05,076 - INFO - 开始处理日期: 2025-01
2025-08-01 03:00:05,091 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:05,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:05,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:06,966 - INFO - Response - Page 1:
2025-08-01 03:00:07,169 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:07,169 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:07,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:07,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:07,701 - INFO - Response - Page 2:
2025-08-01 03:00:07,904 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:07,904 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:07,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:07,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:08,576 - INFO - Response - Page 3:
2025-08-01 03:00:08,779 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:08,779 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:08,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:08,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:09,357 - INFO - Response - Page 4:
2025-08-01 03:00:09,560 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:09,560 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:09,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:09,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:10,201 - INFO - Response - Page 5:
2025-08-01 03:00:10,404 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:10,404 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:10,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:10,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:10,951 - INFO - Response - Page 6:
2025-08-01 03:00:11,154 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:11,154 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:11,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:11,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:11,654 - INFO - Response - Page 7:
2025-08-01 03:00:11,857 - INFO - 第 7 页获取到 82 条记录
2025-08-01 03:00:11,857 - INFO - 查询完成，共获取到 682 条记录
2025-08-01 03:00:11,857 - INFO - 获取到 682 条表单数据
2025-08-01 03:00:11,857 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-01 03:00:11,872 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:11,872 - INFO - 开始处理日期: 2025-02
2025-08-01 03:00:11,872 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:11,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:11,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:12,482 - INFO - Response - Page 1:
2025-08-01 03:00:12,685 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:12,685 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:12,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:12,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:13,216 - INFO - Response - Page 2:
2025-08-01 03:00:13,419 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:13,419 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:13,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:13,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:13,982 - INFO - Response - Page 3:
2025-08-01 03:00:14,185 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:14,185 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:14,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:14,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:14,685 - INFO - Response - Page 4:
2025-08-01 03:00:14,888 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:14,888 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:14,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:14,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:15,419 - INFO - Response - Page 5:
2025-08-01 03:00:15,622 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:15,622 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:15,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:15,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:16,107 - INFO - Response - Page 6:
2025-08-01 03:00:16,310 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:16,310 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:16,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:16,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:16,779 - INFO - Response - Page 7:
2025-08-01 03:00:16,982 - INFO - 第 7 页获取到 70 条记录
2025-08-01 03:00:16,982 - INFO - 查询完成，共获取到 670 条记录
2025-08-01 03:00:16,982 - INFO - 获取到 670 条表单数据
2025-08-01 03:00:16,982 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-01 03:00:16,997 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:16,997 - INFO - 开始处理日期: 2025-03
2025-08-01 03:00:16,997 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:16,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:16,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:17,497 - INFO - Response - Page 1:
2025-08-01 03:00:17,700 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:17,700 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:17,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:17,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:18,279 - INFO - Response - Page 2:
2025-08-01 03:00:18,482 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:18,482 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:18,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:18,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:18,982 - INFO - Response - Page 3:
2025-08-01 03:00:19,185 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:19,185 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:19,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:19,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:19,763 - INFO - Response - Page 4:
2025-08-01 03:00:19,966 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:19,966 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:19,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:19,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:20,482 - INFO - Response - Page 5:
2025-08-01 03:00:20,685 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:20,685 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:20,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:20,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:21,169 - INFO - Response - Page 6:
2025-08-01 03:00:21,372 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:21,372 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:21,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:21,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:21,841 - INFO - Response - Page 7:
2025-08-01 03:00:22,044 - INFO - 第 7 页获取到 61 条记录
2025-08-01 03:00:22,044 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 03:00:22,044 - INFO - 获取到 661 条表单数据
2025-08-01 03:00:22,044 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-01 03:00:22,060 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:22,060 - INFO - 开始处理日期: 2025-04
2025-08-01 03:00:22,060 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:22,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:22,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:22,622 - INFO - Response - Page 1:
2025-08-01 03:00:22,825 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:22,825 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:22,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:22,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:23,372 - INFO - Response - Page 2:
2025-08-01 03:00:23,575 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:23,575 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:23,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:23,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:24,122 - INFO - Response - Page 3:
2025-08-01 03:00:24,325 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:24,325 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:24,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:24,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:24,841 - INFO - Response - Page 4:
2025-08-01 03:00:25,044 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:25,044 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:25,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:25,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:25,497 - INFO - Response - Page 5:
2025-08-01 03:00:25,700 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:25,700 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:25,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:25,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:26,216 - INFO - Response - Page 6:
2025-08-01 03:00:26,419 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:26,419 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:26,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:26,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:26,872 - INFO - Response - Page 7:
2025-08-01 03:00:27,075 - INFO - 第 7 页获取到 56 条记录
2025-08-01 03:00:27,075 - INFO - 查询完成，共获取到 656 条记录
2025-08-01 03:00:27,075 - INFO - 获取到 656 条表单数据
2025-08-01 03:00:27,075 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-01 03:00:27,091 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:27,091 - INFO - 开始处理日期: 2025-05
2025-08-01 03:00:27,091 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:27,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:27,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:27,591 - INFO - Response - Page 1:
2025-08-01 03:00:27,794 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:27,794 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:27,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:27,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:28,357 - INFO - Response - Page 2:
2025-08-01 03:00:28,560 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:28,560 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:28,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:28,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:29,060 - INFO - Response - Page 3:
2025-08-01 03:00:29,263 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:29,263 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:29,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:29,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:29,732 - INFO - Response - Page 4:
2025-08-01 03:00:29,935 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:29,935 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:29,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:29,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:30,497 - INFO - Response - Page 5:
2025-08-01 03:00:30,700 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:30,700 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:30,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:30,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:31,232 - INFO - Response - Page 6:
2025-08-01 03:00:31,435 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:31,435 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:31,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:31,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:31,888 - INFO - Response - Page 7:
2025-08-01 03:00:32,091 - INFO - 第 7 页获取到 65 条记录
2025-08-01 03:00:32,091 - INFO - 查询完成，共获取到 665 条记录
2025-08-01 03:00:32,091 - INFO - 获取到 665 条表单数据
2025-08-01 03:00:32,091 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-01 03:00:32,107 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:32,107 - INFO - 开始处理日期: 2025-06
2025-08-01 03:00:32,107 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:32,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:32,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:32,685 - INFO - Response - Page 1:
2025-08-01 03:00:32,888 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:32,888 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:32,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:32,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:33,372 - INFO - Response - Page 2:
2025-08-01 03:00:33,575 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:33,575 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:33,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:33,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:34,091 - INFO - Response - Page 3:
2025-08-01 03:00:34,294 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:34,294 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:34,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:34,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:34,857 - INFO - Response - Page 4:
2025-08-01 03:00:35,060 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:35,060 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:35,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:35,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:35,575 - INFO - Response - Page 5:
2025-08-01 03:00:35,778 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:35,778 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:35,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:35,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:36,278 - INFO - Response - Page 6:
2025-08-01 03:00:36,482 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:36,482 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:36,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:36,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:36,982 - INFO - Response - Page 7:
2025-08-01 03:00:37,185 - INFO - 第 7 页获取到 61 条记录
2025-08-01 03:00:37,185 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 03:00:37,185 - INFO - 获取到 661 条表单数据
2025-08-01 03:00:37,185 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-01 03:00:37,200 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:37,200 - INFO - 开始处理日期: 2025-07
2025-08-01 03:00:37,200 - INFO - Request Parameters - Page 1:
2025-08-01 03:00:37,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:37,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:37,716 - INFO - Response - Page 1:
2025-08-01 03:00:37,919 - INFO - 第 1 页获取到 100 条记录
2025-08-01 03:00:37,919 - INFO - Request Parameters - Page 2:
2025-08-01 03:00:37,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:37,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:38,482 - INFO - Response - Page 2:
2025-08-01 03:00:38,685 - INFO - 第 2 页获取到 100 条记录
2025-08-01 03:00:38,685 - INFO - Request Parameters - Page 3:
2025-08-01 03:00:38,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:38,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:39,169 - INFO - Response - Page 3:
2025-08-01 03:00:39,372 - INFO - 第 3 页获取到 100 条记录
2025-08-01 03:00:39,372 - INFO - Request Parameters - Page 4:
2025-08-01 03:00:39,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:39,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:39,966 - INFO - Response - Page 4:
2025-08-01 03:00:40,169 - INFO - 第 4 页获取到 100 条记录
2025-08-01 03:00:40,169 - INFO - Request Parameters - Page 5:
2025-08-01 03:00:40,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:40,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:40,747 - INFO - Response - Page 5:
2025-08-01 03:00:40,950 - INFO - 第 5 页获取到 100 条记录
2025-08-01 03:00:40,950 - INFO - Request Parameters - Page 6:
2025-08-01 03:00:40,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:40,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:41,466 - INFO - Response - Page 6:
2025-08-01 03:00:41,669 - INFO - 第 6 页获取到 100 条记录
2025-08-01 03:00:41,669 - INFO - Request Parameters - Page 7:
2025-08-01 03:00:41,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 03:00:41,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 03:00:41,982 - INFO - Response - Page 7:
2025-08-01 03:00:42,185 - INFO - 第 7 页获取到 19 条记录
2025-08-01 03:00:42,185 - INFO - 查询完成，共获取到 619 条记录
2025-08-01 03:00:42,185 - INFO - 获取到 619 条表单数据
2025-08-01 03:00:42,185 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-01 03:00:42,200 - INFO - 开始更新记录 - 表单实例ID: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-08-01 03:00:42,669 - INFO - 更新表单数据成功: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-08-01 03:00:42,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 605764.4, 'new_value': 622997.4}, {'field': 'total_amount', 'old_value': 605764.4, 'new_value': 622997.4}, {'field': 'order_count', 'old_value': 223, 'new_value': 235}]
2025-08-01 03:00:42,669 - INFO - 日期 2025-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:42,669 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 03:00:42,669 - INFO - =================同步完成====================
2025-08-01 06:00:03,018 - INFO - =================使用默认全量同步=============
2025-08-01 06:00:05,174 - INFO - MySQL查询成功，共获取 4614 条记录
2025-08-01 06:00:05,174 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-08-01 06:00:05,221 - INFO - 开始处理日期: 2025-01
2025-08-01 06:00:05,221 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:05,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:05,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:06,893 - INFO - Response - Page 1:
2025-08-01 06:00:07,096 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:07,096 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:07,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:07,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:07,690 - INFO - Response - Page 2:
2025-08-01 06:00:07,893 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:07,893 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:07,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:07,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:08,487 - INFO - Response - Page 3:
2025-08-01 06:00:08,690 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:08,690 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:08,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:08,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:09,174 - INFO - Response - Page 4:
2025-08-01 06:00:09,377 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:09,377 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:09,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:09,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:09,908 - INFO - Response - Page 5:
2025-08-01 06:00:10,111 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:10,111 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:10,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:10,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:10,627 - INFO - Response - Page 6:
2025-08-01 06:00:10,830 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:10,830 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:10,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:10,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:11,377 - INFO - Response - Page 7:
2025-08-01 06:00:11,580 - INFO - 第 7 页获取到 82 条记录
2025-08-01 06:00:11,580 - INFO - 查询完成，共获取到 682 条记录
2025-08-01 06:00:11,580 - INFO - 获取到 682 条表单数据
2025-08-01 06:00:11,580 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-01 06:00:11,596 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:11,596 - INFO - 开始处理日期: 2025-02
2025-08-01 06:00:11,596 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:11,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:11,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:12,158 - INFO - Response - Page 1:
2025-08-01 06:00:12,361 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:12,361 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:12,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:12,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:12,861 - INFO - Response - Page 2:
2025-08-01 06:00:13,080 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:13,080 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:13,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:13,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:13,596 - INFO - Response - Page 3:
2025-08-01 06:00:13,799 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:13,799 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:13,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:13,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:14,299 - INFO - Response - Page 4:
2025-08-01 06:00:14,502 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:14,502 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:14,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:14,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:15,049 - INFO - Response - Page 5:
2025-08-01 06:00:15,252 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:15,252 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:15,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:15,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:15,721 - INFO - Response - Page 6:
2025-08-01 06:00:15,924 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:15,924 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:15,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:15,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:16,408 - INFO - Response - Page 7:
2025-08-01 06:00:16,611 - INFO - 第 7 页获取到 70 条记录
2025-08-01 06:00:16,611 - INFO - 查询完成，共获取到 670 条记录
2025-08-01 06:00:16,611 - INFO - 获取到 670 条表单数据
2025-08-01 06:00:16,611 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-01 06:00:16,627 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:16,627 - INFO - 开始处理日期: 2025-03
2025-08-01 06:00:16,627 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:16,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:16,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:17,190 - INFO - Response - Page 1:
2025-08-01 06:00:17,393 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:17,393 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:17,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:17,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:17,908 - INFO - Response - Page 2:
2025-08-01 06:00:18,111 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:18,111 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:18,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:18,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:18,658 - INFO - Response - Page 3:
2025-08-01 06:00:18,861 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:18,861 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:18,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:18,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:19,361 - INFO - Response - Page 4:
2025-08-01 06:00:19,565 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:19,565 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:19,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:19,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:20,080 - INFO - Response - Page 5:
2025-08-01 06:00:20,283 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:20,283 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:20,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:20,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:20,799 - INFO - Response - Page 6:
2025-08-01 06:00:21,002 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:21,002 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:21,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:21,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:21,486 - INFO - Response - Page 7:
2025-08-01 06:00:21,690 - INFO - 第 7 页获取到 61 条记录
2025-08-01 06:00:21,690 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 06:00:21,690 - INFO - 获取到 661 条表单数据
2025-08-01 06:00:21,690 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-01 06:00:21,705 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:21,705 - INFO - 开始处理日期: 2025-04
2025-08-01 06:00:21,705 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:21,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:21,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:22,190 - INFO - Response - Page 1:
2025-08-01 06:00:22,393 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:22,393 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:22,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:22,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:22,971 - INFO - Response - Page 2:
2025-08-01 06:00:23,174 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:23,174 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:23,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:23,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:23,690 - INFO - Response - Page 3:
2025-08-01 06:00:23,893 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:23,893 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:23,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:23,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:24,393 - INFO - Response - Page 4:
2025-08-01 06:00:24,596 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:24,596 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:24,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:24,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:25,158 - INFO - Response - Page 5:
2025-08-01 06:00:25,361 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:25,361 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:25,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:25,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:25,908 - INFO - Response - Page 6:
2025-08-01 06:00:26,111 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:26,111 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:26,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:26,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:26,627 - INFO - Response - Page 7:
2025-08-01 06:00:26,830 - INFO - 第 7 页获取到 56 条记录
2025-08-01 06:00:26,830 - INFO - 查询完成，共获取到 656 条记录
2025-08-01 06:00:26,830 - INFO - 获取到 656 条表单数据
2025-08-01 06:00:26,830 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-01 06:00:26,846 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:26,846 - INFO - 开始处理日期: 2025-05
2025-08-01 06:00:26,846 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:26,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:26,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:27,408 - INFO - Response - Page 1:
2025-08-01 06:00:27,611 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:27,611 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:27,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:27,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:28,127 - INFO - Response - Page 2:
2025-08-01 06:00:28,330 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:28,330 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:28,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:28,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:28,893 - INFO - Response - Page 3:
2025-08-01 06:00:29,096 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:29,096 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:29,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:29,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:29,674 - INFO - Response - Page 4:
2025-08-01 06:00:29,877 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:29,877 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:29,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:29,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:30,377 - INFO - Response - Page 5:
2025-08-01 06:00:30,580 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:30,580 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:30,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:30,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:31,158 - INFO - Response - Page 6:
2025-08-01 06:00:31,361 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:31,361 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:31,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:31,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:31,846 - INFO - Response - Page 7:
2025-08-01 06:00:32,049 - INFO - 第 7 页获取到 65 条记录
2025-08-01 06:00:32,049 - INFO - 查询完成，共获取到 665 条记录
2025-08-01 06:00:32,049 - INFO - 获取到 665 条表单数据
2025-08-01 06:00:32,049 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-01 06:00:32,064 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:32,064 - INFO - 开始处理日期: 2025-06
2025-08-01 06:00:32,064 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:32,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:32,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:32,611 - INFO - Response - Page 1:
2025-08-01 06:00:32,814 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:32,814 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:32,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:32,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:33,361 - INFO - Response - Page 2:
2025-08-01 06:00:33,564 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:33,564 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:33,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:33,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:34,111 - INFO - Response - Page 3:
2025-08-01 06:00:34,314 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:34,314 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:34,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:34,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:34,830 - INFO - Response - Page 4:
2025-08-01 06:00:35,033 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:35,033 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:35,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:35,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:35,580 - INFO - Response - Page 5:
2025-08-01 06:00:35,783 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:35,783 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:35,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:35,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:36,361 - INFO - Response - Page 6:
2025-08-01 06:00:36,564 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:36,564 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:36,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:36,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:37,033 - INFO - Response - Page 7:
2025-08-01 06:00:37,236 - INFO - 第 7 页获取到 61 条记录
2025-08-01 06:00:37,236 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 06:00:37,236 - INFO - 获取到 661 条表单数据
2025-08-01 06:00:37,236 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-01 06:00:37,252 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:37,252 - INFO - 开始处理日期: 2025-07
2025-08-01 06:00:37,252 - INFO - Request Parameters - Page 1:
2025-08-01 06:00:37,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:37,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:37,768 - INFO - Response - Page 1:
2025-08-01 06:00:37,971 - INFO - 第 1 页获取到 100 条记录
2025-08-01 06:00:37,971 - INFO - Request Parameters - Page 2:
2025-08-01 06:00:37,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:37,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:38,518 - INFO - Response - Page 2:
2025-08-01 06:00:38,721 - INFO - 第 2 页获取到 100 条记录
2025-08-01 06:00:38,721 - INFO - Request Parameters - Page 3:
2025-08-01 06:00:38,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:38,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:39,189 - INFO - Response - Page 3:
2025-08-01 06:00:39,393 - INFO - 第 3 页获取到 100 条记录
2025-08-01 06:00:39,393 - INFO - Request Parameters - Page 4:
2025-08-01 06:00:39,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:39,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:39,924 - INFO - Response - Page 4:
2025-08-01 06:00:40,127 - INFO - 第 4 页获取到 100 条记录
2025-08-01 06:00:40,127 - INFO - Request Parameters - Page 5:
2025-08-01 06:00:40,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:40,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:40,658 - INFO - Response - Page 5:
2025-08-01 06:00:40,861 - INFO - 第 5 页获取到 100 条记录
2025-08-01 06:00:40,861 - INFO - Request Parameters - Page 6:
2025-08-01 06:00:40,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:40,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:41,393 - INFO - Response - Page 6:
2025-08-01 06:00:41,596 - INFO - 第 6 页获取到 100 条记录
2025-08-01 06:00:41,596 - INFO - Request Parameters - Page 7:
2025-08-01 06:00:41,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 06:00:41,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 06:00:41,971 - INFO - Response - Page 7:
2025-08-01 06:00:42,174 - INFO - 第 7 页获取到 19 条记录
2025-08-01 06:00:42,174 - INFO - 查询完成，共获取到 619 条记录
2025-08-01 06:00:42,174 - INFO - 获取到 619 条表单数据
2025-08-01 06:00:42,174 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-01 06:00:42,189 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:42,189 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 06:00:42,189 - INFO - =================同步完成====================
2025-08-01 09:00:02,960 - INFO - =================使用默认全量同步=============
2025-08-01 09:00:05,100 - INFO - MySQL查询成功，共获取 4614 条记录
2025-08-01 09:00:05,100 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-08-01 09:00:05,147 - INFO - 开始处理日期: 2025-01
2025-08-01 09:00:05,147 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:05,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:05,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:06,319 - INFO - Response - Page 1:
2025-08-01 09:00:06,522 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:06,522 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:06,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:06,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:07,116 - INFO - Response - Page 2:
2025-08-01 09:00:07,319 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:07,319 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:07,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:07,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:08,335 - INFO - Response - Page 3:
2025-08-01 09:00:08,538 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:08,538 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:08,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:08,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:09,069 - INFO - Response - Page 4:
2025-08-01 09:00:09,272 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:09,272 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:09,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:09,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:09,819 - INFO - Response - Page 5:
2025-08-01 09:00:10,022 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:10,022 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:10,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:10,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:10,522 - INFO - Response - Page 6:
2025-08-01 09:00:10,725 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:10,725 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:10,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:10,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:11,194 - INFO - Response - Page 7:
2025-08-01 09:00:11,397 - INFO - 第 7 页获取到 82 条记录
2025-08-01 09:00:11,397 - INFO - 查询完成，共获取到 682 条记录
2025-08-01 09:00:11,397 - INFO - 获取到 682 条表单数据
2025-08-01 09:00:11,397 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-01 09:00:11,413 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 09:00:11,413 - INFO - 开始处理日期: 2025-02
2025-08-01 09:00:11,413 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:11,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:11,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:12,147 - INFO - Response - Page 1:
2025-08-01 09:00:12,350 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:12,350 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:12,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:12,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:12,819 - INFO - Response - Page 2:
2025-08-01 09:00:13,022 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:13,022 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:13,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:13,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:13,585 - INFO - Response - Page 3:
2025-08-01 09:00:13,788 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:13,788 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:13,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:13,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:14,303 - INFO - Response - Page 4:
2025-08-01 09:00:14,507 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:14,507 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:14,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:14,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:15,085 - INFO - Response - Page 5:
2025-08-01 09:00:15,288 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:15,288 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:15,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:15,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:15,772 - INFO - Response - Page 6:
2025-08-01 09:00:15,975 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:15,975 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:15,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:15,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:16,522 - INFO - Response - Page 7:
2025-08-01 09:00:16,725 - INFO - 第 7 页获取到 70 条记录
2025-08-01 09:00:16,725 - INFO - 查询完成，共获取到 670 条记录
2025-08-01 09:00:16,725 - INFO - 获取到 670 条表单数据
2025-08-01 09:00:16,725 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-01 09:00:16,741 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 09:00:16,741 - INFO - 开始处理日期: 2025-03
2025-08-01 09:00:16,741 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:16,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:16,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:17,178 - INFO - Response - Page 1:
2025-08-01 09:00:17,382 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:17,382 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:17,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:17,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:18,069 - INFO - Response - Page 2:
2025-08-01 09:00:18,272 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:18,272 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:18,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:18,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:18,850 - INFO - Response - Page 3:
2025-08-01 09:00:19,053 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:19,053 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:19,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:19,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:19,569 - INFO - Response - Page 4:
2025-08-01 09:00:19,772 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:19,772 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:19,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:19,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:20,413 - INFO - Response - Page 5:
2025-08-01 09:00:20,616 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:20,616 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:20,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:20,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:21,116 - INFO - Response - Page 6:
2025-08-01 09:00:21,319 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:21,319 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:21,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:21,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:21,741 - INFO - Response - Page 7:
2025-08-01 09:00:21,944 - INFO - 第 7 页获取到 61 条记录
2025-08-01 09:00:21,944 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 09:00:21,944 - INFO - 获取到 661 条表单数据
2025-08-01 09:00:21,944 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-01 09:00:21,960 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 09:00:21,960 - INFO - 开始处理日期: 2025-04
2025-08-01 09:00:21,960 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:21,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:21,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:22,475 - INFO - Response - Page 1:
2025-08-01 09:00:22,678 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:22,678 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:22,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:22,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:23,147 - INFO - Response - Page 2:
2025-08-01 09:00:23,350 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:23,350 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:23,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:23,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:23,897 - INFO - Response - Page 3:
2025-08-01 09:00:24,100 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:24,100 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:24,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:24,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:24,678 - INFO - Response - Page 4:
2025-08-01 09:00:24,881 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:24,881 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:24,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:24,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:25,335 - INFO - Response - Page 5:
2025-08-01 09:00:25,538 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:25,538 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:25,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:25,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:26,038 - INFO - Response - Page 6:
2025-08-01 09:00:26,241 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:26,241 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:26,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:26,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:26,741 - INFO - Response - Page 7:
2025-08-01 09:00:26,944 - INFO - 第 7 页获取到 56 条记录
2025-08-01 09:00:26,944 - INFO - 查询完成，共获取到 656 条记录
2025-08-01 09:00:26,944 - INFO - 获取到 656 条表单数据
2025-08-01 09:00:26,944 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-01 09:00:26,960 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 09:00:26,960 - INFO - 开始处理日期: 2025-05
2025-08-01 09:00:26,960 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:26,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:26,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:27,522 - INFO - Response - Page 1:
2025-08-01 09:00:27,725 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:27,725 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:27,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:27,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:28,241 - INFO - Response - Page 2:
2025-08-01 09:00:28,444 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:28,444 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:28,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:28,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:28,975 - INFO - Response - Page 3:
2025-08-01 09:00:29,178 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:29,178 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:29,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:29,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:29,663 - INFO - Response - Page 4:
2025-08-01 09:00:29,866 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:29,866 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:29,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:29,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:30,381 - INFO - Response - Page 5:
2025-08-01 09:00:30,585 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:30,585 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:30,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:30,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:31,053 - INFO - Response - Page 6:
2025-08-01 09:00:31,256 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:31,256 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:31,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:31,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:31,710 - INFO - Response - Page 7:
2025-08-01 09:00:31,913 - INFO - 第 7 页获取到 65 条记录
2025-08-01 09:00:31,913 - INFO - 查询完成，共获取到 665 条记录
2025-08-01 09:00:31,913 - INFO - 获取到 665 条表单数据
2025-08-01 09:00:31,913 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-01 09:00:31,928 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 09:00:31,928 - INFO - 开始处理日期: 2025-06
2025-08-01 09:00:31,928 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:31,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:31,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:32,475 - INFO - Response - Page 1:
2025-08-01 09:00:32,678 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:32,678 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:32,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:32,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:33,241 - INFO - Response - Page 2:
2025-08-01 09:00:33,444 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:33,444 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:33,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:33,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:33,960 - INFO - Response - Page 3:
2025-08-01 09:00:34,163 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:34,163 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:34,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:34,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:34,663 - INFO - Response - Page 4:
2025-08-01 09:00:34,866 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:34,866 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:34,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:34,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:35,366 - INFO - Response - Page 5:
2025-08-01 09:00:35,569 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:35,569 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:35,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:35,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:36,131 - INFO - Response - Page 6:
2025-08-01 09:00:36,335 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:36,335 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:36,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:36,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:36,850 - INFO - Response - Page 7:
2025-08-01 09:00:37,053 - INFO - 第 7 页获取到 61 条记录
2025-08-01 09:00:37,053 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 09:00:37,053 - INFO - 获取到 661 条表单数据
2025-08-01 09:00:37,053 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-01 09:00:37,069 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 09:00:37,069 - INFO - 开始处理日期: 2025-07
2025-08-01 09:00:37,069 - INFO - Request Parameters - Page 1:
2025-08-01 09:00:37,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:37,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:37,585 - INFO - Response - Page 1:
2025-08-01 09:00:37,788 - INFO - 第 1 页获取到 100 条记录
2025-08-01 09:00:37,788 - INFO - Request Parameters - Page 2:
2025-08-01 09:00:37,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:37,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:38,303 - INFO - Response - Page 2:
2025-08-01 09:00:38,506 - INFO - 第 2 页获取到 100 条记录
2025-08-01 09:00:38,506 - INFO - Request Parameters - Page 3:
2025-08-01 09:00:38,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:38,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:39,022 - INFO - Response - Page 3:
2025-08-01 09:00:39,225 - INFO - 第 3 页获取到 100 条记录
2025-08-01 09:00:39,225 - INFO - Request Parameters - Page 4:
2025-08-01 09:00:39,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:39,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:39,741 - INFO - Response - Page 4:
2025-08-01 09:00:39,944 - INFO - 第 4 页获取到 100 条记录
2025-08-01 09:00:39,944 - INFO - Request Parameters - Page 5:
2025-08-01 09:00:39,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:39,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:40,491 - INFO - Response - Page 5:
2025-08-01 09:00:40,694 - INFO - 第 5 页获取到 100 条记录
2025-08-01 09:00:40,694 - INFO - Request Parameters - Page 6:
2025-08-01 09:00:40,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:40,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:41,210 - INFO - Response - Page 6:
2025-08-01 09:00:41,413 - INFO - 第 6 页获取到 100 条记录
2025-08-01 09:00:41,413 - INFO - Request Parameters - Page 7:
2025-08-01 09:00:41,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 09:00:41,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 09:00:41,772 - INFO - Response - Page 7:
2025-08-01 09:00:41,975 - INFO - 第 7 页获取到 19 条记录
2025-08-01 09:00:41,975 - INFO - 查询完成，共获取到 619 条记录
2025-08-01 09:00:41,975 - INFO - 获取到 619 条表单数据
2025-08-01 09:00:41,975 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-01 09:00:41,975 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG5
2025-08-01 09:00:42,428 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG5
2025-08-01 09:00:42,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152823.5, 'new_value': 167179.5}, {'field': 'total_amount', 'old_value': 152823.5, 'new_value': 167179.5}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-08-01 09:00:42,428 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW5
2025-08-01 09:00:42,881 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW5
2025-08-01 09:00:42,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18207.0, 'new_value': 18615.0}, {'field': 'offline_amount', 'old_value': 86330.0, 'new_value': 88704.0}, {'field': 'total_amount', 'old_value': 104537.0, 'new_value': 107319.0}, {'field': 'order_count', 'old_value': 1448, 'new_value': 1486}]
2025-08-01 09:00:42,881 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX7
2025-08-01 09:00:43,334 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX7
2025-08-01 09:00:43,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26681.44, 'new_value': 27593.91}, {'field': 'offline_amount', 'old_value': 299680.22, 'new_value': 309132.13}, {'field': 'total_amount', 'old_value': 326361.66, 'new_value': 336726.04}, {'field': 'order_count', 'old_value': 1782, 'new_value': 1847}]
2025-08-01 09:00:43,334 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM66
2025-08-01 09:00:43,725 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM66
2025-08-01 09:00:43,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 245898.89, 'new_value': 253605.46}, {'field': 'offline_amount', 'old_value': 509104.78, 'new_value': 521424.38}, {'field': 'total_amount', 'old_value': 755003.67, 'new_value': 775029.84}, {'field': 'order_count', 'old_value': 5345, 'new_value': 5493}]
2025-08-01 09:00:43,725 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM76
2025-08-01 09:00:44,100 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM76
2025-08-01 09:00:44,100 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 269390.75, 'new_value': 271588.04}, {'field': 'offline_amount', 'old_value': 140222.87, 'new_value': 147268.75}, {'field': 'total_amount', 'old_value': 409613.62, 'new_value': 418856.79}, {'field': 'order_count', 'old_value': 3452, 'new_value': 3559}]
2025-08-01 09:00:44,100 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM86
2025-08-01 09:00:44,663 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM86
2025-08-01 09:00:44,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6061.7, 'new_value': 6259.7}, {'field': 'offline_amount', 'old_value': 15626.0, 'new_value': 17374.0}, {'field': 'total_amount', 'old_value': 21687.7, 'new_value': 23633.7}, {'field': 'order_count', 'old_value': 75, 'new_value': 81}]
2025-08-01 09:00:44,663 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM96
2025-08-01 09:00:45,272 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM96
2025-08-01 09:00:45,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57083.9, 'new_value': 58866.8}, {'field': 'offline_amount', 'old_value': 30462.14, 'new_value': 31295.94}, {'field': 'total_amount', 'old_value': 87546.04, 'new_value': 90162.74}, {'field': 'order_count', 'old_value': 418, 'new_value': 433}]
2025-08-01 09:00:45,272 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB6
2025-08-01 09:00:45,709 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB6
2025-08-01 09:00:45,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 160996.0, 'new_value': 190004.0}, {'field': 'offline_amount', 'old_value': 76682.0, 'new_value': 77924.0}, {'field': 'total_amount', 'old_value': 237678.0, 'new_value': 267928.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 111}]
2025-08-01 09:00:45,725 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY6
2025-08-01 09:00:46,288 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY6
2025-08-01 09:00:46,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46174.38, 'new_value': 46625.38}, {'field': 'offline_amount', 'old_value': 1453.0, 'new_value': 1454.0}, {'field': 'total_amount', 'old_value': 47627.38, 'new_value': 48079.38}, {'field': 'order_count', 'old_value': 301, 'new_value': 305}]
2025-08-01 09:00:46,288 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM27
2025-08-01 09:00:46,756 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM27
2025-08-01 09:00:46,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38592.6, 'new_value': 44780.6}, {'field': 'total_amount', 'old_value': 38592.6, 'new_value': 44780.6}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-08-01 09:00:46,756 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA7
2025-08-01 09:00:47,288 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA7
2025-08-01 09:00:47,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251118.0, 'new_value': 259118.0}, {'field': 'total_amount', 'old_value': 251118.0, 'new_value': 259118.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-08-01 09:00:47,288 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD7
2025-08-01 09:00:47,725 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD7
2025-08-01 09:00:47,725 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-08-01 09:00:47,725 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD
2025-08-01 09:00:48,131 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD
2025-08-01 09:00:48,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3826.98, 'new_value': 4553.17}, {'field': 'offline_amount', 'old_value': 152207.9, 'new_value': 152323.9}, {'field': 'total_amount', 'old_value': 156034.88, 'new_value': 156877.07}, {'field': 'order_count', 'old_value': 171, 'new_value': 174}]
2025-08-01 09:00:48,131 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU
2025-08-01 09:00:48,584 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU
2025-08-01 09:00:48,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139906.07, 'new_value': 148641.92}, {'field': 'offline_amount', 'old_value': 490415.21, 'new_value': 498811.53}, {'field': 'total_amount', 'old_value': 630321.28, 'new_value': 647453.45}, {'field': 'order_count', 'old_value': 5011, 'new_value': 5237}]
2025-08-01 09:00:48,584 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ
2025-08-01 09:00:49,069 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ
2025-08-01 09:00:49,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91276.0, 'new_value': 96408.4}, {'field': 'total_amount', 'old_value': 91276.0, 'new_value': 96408.4}, {'field': 'order_count', 'old_value': 222, 'new_value': 232}]
2025-08-01 09:00:49,069 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM51
2025-08-01 09:00:49,522 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM51
2025-08-01 09:00:49,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55481.0, 'new_value': 59879.0}, {'field': 'total_amount', 'old_value': 59480.0, 'new_value': 63878.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-08-01 09:00:49,522 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI1
2025-08-01 09:00:49,975 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI1
2025-08-01 09:00:49,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18095.9, 'new_value': 18349.9}, {'field': 'offline_amount', 'old_value': 222858.0, 'new_value': 226238.0}, {'field': 'total_amount', 'old_value': 240953.9, 'new_value': 244587.9}, {'field': 'order_count', 'old_value': 70, 'new_value': 73}]
2025-08-01 09:00:49,975 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML1
2025-08-01 09:00:50,459 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML1
2025-08-01 09:00:50,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5046.5, 'new_value': 5154.4}, {'field': 'offline_amount', 'old_value': 14625.8, 'new_value': 15160.8}, {'field': 'total_amount', 'old_value': 19672.3, 'new_value': 20315.2}, {'field': 'order_count', 'old_value': 191, 'new_value': 200}]
2025-08-01 09:00:50,459 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM12
2025-08-01 09:00:50,866 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM12
2025-08-01 09:00:50,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6508.49, 'new_value': 7878.49}, {'field': 'offline_amount', 'old_value': 79930.6, 'new_value': 80201.2}, {'field': 'total_amount', 'old_value': 86439.09, 'new_value': 88079.69}, {'field': 'order_count', 'old_value': 1100, 'new_value': 1130}]
2025-08-01 09:00:50,866 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN2
2025-08-01 09:00:51,288 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN2
2025-08-01 09:00:51,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262354.29, 'new_value': 269929.44}, {'field': 'total_amount', 'old_value': 262354.29, 'new_value': 269929.44}, {'field': 'order_count', 'old_value': 1411, 'new_value': 1451}]
2025-08-01 09:00:51,288 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP2
2025-08-01 09:00:51,741 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP2
2025-08-01 09:00:51,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10608.0, 'new_value': 10928.0}, {'field': 'total_amount', 'old_value': 10608.0, 'new_value': 10928.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-08-01 09:00:51,741 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT2
2025-08-01 09:00:52,303 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT2
2025-08-01 09:00:52,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450926.43, 'new_value': 464656.17}, {'field': 'total_amount', 'old_value': 450926.43, 'new_value': 464656.17}, {'field': 'order_count', 'old_value': 4221, 'new_value': 4345}]
2025-08-01 09:00:52,303 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMX
2025-08-01 09:00:52,788 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMX
2025-08-01 09:00:52,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 357582.3, 'new_value': 367582.3}, {'field': 'offline_amount', 'old_value': 367550.5, 'new_value': 376418.6}, {'field': 'total_amount', 'old_value': 725132.8, 'new_value': 744000.9}, {'field': 'order_count', 'old_value': 7748, 'new_value': 7936}]
2025-08-01 09:00:52,788 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM69
2025-08-01 09:00:53,288 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM69
2025-08-01 09:00:53,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57964.02, 'new_value': 59598.99}, {'field': 'offline_amount', 'old_value': 75140.04, 'new_value': 77237.83}, {'field': 'total_amount', 'old_value': 133104.06, 'new_value': 136836.82}, {'field': 'order_count', 'old_value': 10711, 'new_value': 11018}]
2025-08-01 09:00:53,288 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM61
2025-08-01 09:00:53,725 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM61
2025-08-01 09:00:53,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146242.0, 'new_value': 150070.0}, {'field': 'total_amount', 'old_value': 146242.0, 'new_value': 150070.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-08-01 09:00:53,725 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH1
2025-08-01 09:00:54,178 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH1
2025-08-01 09:00:54,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52008.0, 'new_value': 54732.0}, {'field': 'total_amount', 'old_value': 52008.0, 'new_value': 54732.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 150}]
2025-08-01 09:00:54,178 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM99
2025-08-01 09:00:54,694 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM99
2025-08-01 09:00:54,694 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 160188.5, 'new_value': 164664.6}, {'field': 'offline_amount', 'old_value': 110191.7, 'new_value': 113166.7}, {'field': 'total_amount', 'old_value': 270380.2, 'new_value': 277831.3}, {'field': 'order_count', 'old_value': 6686, 'new_value': 6863}]
2025-08-01 09:00:54,694 - INFO - 开始更新记录 - 表单实例ID: FINST-5TD66N91A1UWMGJECD5VTA2FFJMJ3BEXD1NCMB
2025-08-01 09:00:55,116 - INFO - 更新表单数据成功: FINST-5TD66N91A1UWMGJECD5VTA2FFJMJ3BEXD1NCMB
2025-08-01 09:00:55,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96120.0, 'new_value': 100120.0}, {'field': 'total_amount', 'old_value': 97490.0, 'new_value': 101490.0}, {'field': 'order_count', 'old_value': 5065, 'new_value': 5066}]
2025-08-01 09:00:55,116 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK1
2025-08-01 09:00:55,600 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK1
2025-08-01 09:00:55,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301580.0, 'new_value': 306858.0}, {'field': 'total_amount', 'old_value': 301580.0, 'new_value': 306858.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-08-01 09:00:55,600 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMB9
2025-08-01 09:00:55,959 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMB9
2025-08-01 09:00:55,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1844015.0, 'new_value': 1906294.0}, {'field': 'total_amount', 'old_value': 1844015.0, 'new_value': 1906294.0}, {'field': 'order_count', 'old_value': 10718, 'new_value': 11003}]
2025-08-01 09:00:55,959 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMP1
2025-08-01 09:00:56,381 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMP1
2025-08-01 09:00:56,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320759.74, 'new_value': 329772.84}, {'field': 'total_amount', 'old_value': 328913.12, 'new_value': 337926.22}, {'field': 'order_count', 'old_value': 2380, 'new_value': 2453}]
2025-08-01 09:00:56,381 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMC9
2025-08-01 09:00:56,803 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMC9
2025-08-01 09:00:56,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130697.55, 'new_value': 147304.55}, {'field': 'total_amount', 'old_value': 131150.55, 'new_value': 147757.55}, {'field': 'order_count', 'old_value': 76, 'new_value': 85}]
2025-08-01 09:00:56,803 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR1
2025-08-01 09:00:57,288 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR1
2025-08-01 09:00:57,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69024.7, 'new_value': 70460.6}, {'field': 'total_amount', 'old_value': 70516.2, 'new_value': 71952.1}, {'field': 'order_count', 'old_value': 545, 'new_value': 556}]
2025-08-01 09:00:57,288 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS1
2025-08-01 09:00:57,725 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS1
2025-08-01 09:00:57,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135031.0, 'new_value': 140179.0}, {'field': 'total_amount', 'old_value': 135031.0, 'new_value': 140179.0}, {'field': 'order_count', 'old_value': 487, 'new_value': 503}]
2025-08-01 09:00:57,725 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMD9
2025-08-01 09:00:58,194 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMD9
2025-08-01 09:00:58,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124926.0, 'new_value': 128014.0}, {'field': 'total_amount', 'old_value': 124926.0, 'new_value': 128014.0}, {'field': 'order_count', 'old_value': 6110, 'new_value': 6261}]
2025-08-01 09:00:58,194 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCME9
2025-08-01 09:00:58,709 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCME9
2025-08-01 09:00:58,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 327421.39, 'new_value': 335535.38}, {'field': 'offline_amount', 'old_value': 1953186.9, 'new_value': 2008019.0}, {'field': 'total_amount', 'old_value': 2280608.29, 'new_value': 2343554.38}, {'field': 'order_count', 'old_value': 10901, 'new_value': 11212}]
2025-08-01 09:00:58,709 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA2
2025-08-01 09:00:59,225 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA2
2025-08-01 09:00:59,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80103.0, 'new_value': 81268.0}, {'field': 'total_amount', 'old_value': 80103.0, 'new_value': 81268.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-08-01 09:00:59,241 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMJ9
2025-08-01 09:00:59,709 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMJ9
2025-08-01 09:00:59,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 229214.84, 'new_value': 229477.84}, {'field': 'offline_amount', 'old_value': 51270.0, 'new_value': 59447.0}, {'field': 'total_amount', 'old_value': 280484.84, 'new_value': 288924.84}, {'field': 'order_count', 'old_value': 14497, 'new_value': 14913}]
2025-08-01 09:00:59,709 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMN9
2025-08-01 09:01:00,131 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMN9
2025-08-01 09:01:00,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12000000.0, 'new_value': 12400000.0}, {'field': 'total_amount', 'old_value': 12000000.0, 'new_value': 12400000.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-08-01 09:01:00,131 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW2
2025-08-01 09:01:00,709 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW2
2025-08-01 09:01:00,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 901500.5, 'new_value': 933377.2}, {'field': 'total_amount', 'old_value': 901500.5, 'new_value': 933377.2}, {'field': 'order_count', 'old_value': 18818, 'new_value': 19487}]
2025-08-01 09:01:00,709 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMX2
2025-08-01 09:01:01,334 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMX2
2025-08-01 09:01:01,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83584.0, 'new_value': 84840.0}, {'field': 'offline_amount', 'old_value': 330201.0, 'new_value': 341191.0}, {'field': 'total_amount', 'old_value': 413785.0, 'new_value': 426031.0}, {'field': 'order_count', 'old_value': 1828, 'new_value': 1873}]
2025-08-01 09:01:01,334 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ2
2025-08-01 09:01:01,912 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ2
2025-08-01 09:01:01,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 842962.4, 'new_value': 870106.4}, {'field': 'total_amount', 'old_value': 842962.4, 'new_value': 870106.4}, {'field': 'order_count', 'old_value': 5851, 'new_value': 6050}]
2025-08-01 09:01:01,912 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM13
2025-08-01 09:01:02,491 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM13
2025-08-01 09:01:02,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45726.5, 'new_value': 46000.5}, {'field': 'total_amount', 'old_value': 49723.7, 'new_value': 49997.7}, {'field': 'order_count', 'old_value': 476, 'new_value': 482}]
2025-08-01 09:01:02,491 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM73
2025-08-01 09:01:02,991 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM73
2025-08-01 09:01:02,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312420.0, 'new_value': 325139.0}, {'field': 'total_amount', 'old_value': 312420.0, 'new_value': 325139.0}, {'field': 'order_count', 'old_value': 34242, 'new_value': 35664}]
2025-08-01 09:01:02,991 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMS9
2025-08-01 09:01:03,475 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMS9
2025-08-01 09:01:03,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'order_count', 'old_value': 163, 'new_value': 164}]
2025-08-01 09:01:03,475 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMT9
2025-08-01 09:01:03,959 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMT9
2025-08-01 09:01:03,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 129}]
2025-08-01 09:01:03,959 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMU9
2025-08-01 09:01:04,381 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMU9
2025-08-01 09:01:04,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 129}]
2025-08-01 09:01:04,381 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMV9
2025-08-01 09:01:04,850 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMV9
2025-08-01 09:01:04,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1950000.0, 'new_value': 2000000.0}, {'field': 'total_amount', 'old_value': 1950000.0, 'new_value': 2000000.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 139}]
2025-08-01 09:01:04,850 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMW9
2025-08-01 09:01:05,350 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMW9
2025-08-01 09:01:05,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1900000.0, 'new_value': 1950000.0}, {'field': 'total_amount', 'old_value': 1900000.0, 'new_value': 1950000.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-08-01 09:01:05,350 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMX9
2025-08-01 09:01:05,850 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMX9
2025-08-01 09:01:05,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210388.43, 'new_value': 215738.81}, {'field': 'total_amount', 'old_value': 210388.43, 'new_value': 215738.81}, {'field': 'order_count', 'old_value': 14758, 'new_value': 15126}]
2025-08-01 09:01:05,850 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMY9
2025-08-01 09:01:06,272 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMY9
2025-08-01 09:01:06,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 667557.27, 'new_value': 677881.08}, {'field': 'total_amount', 'old_value': 667557.27, 'new_value': 677881.08}, {'field': 'order_count', 'old_value': 4581, 'new_value': 4686}]
2025-08-01 09:01:06,272 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMZ9
2025-08-01 09:01:06,709 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMZ9
2025-08-01 09:01:06,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1148417.0, 'new_value': 1184817.0}, {'field': 'total_amount', 'old_value': 1148417.0, 'new_value': 1184817.0}, {'field': 'order_count', 'old_value': 25794, 'new_value': 26616}]
2025-08-01 09:01:06,709 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU
2025-08-01 09:01:07,194 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU
2025-08-01 09:01:07,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4298.0, 'new_value': 6597.0}, {'field': 'offline_amount', 'old_value': 85992.0, 'new_value': 85993.0}, {'field': 'total_amount', 'old_value': 90290.0, 'new_value': 92590.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-08-01 09:01:07,194 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM31
2025-08-01 09:01:07,662 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM31
2025-08-01 09:01:07,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 703476.32, 'new_value': 723402.32}, {'field': 'offline_amount', 'old_value': 350260.32, 'new_value': 357070.32}, {'field': 'total_amount', 'old_value': 1053736.64, 'new_value': 1080472.64}, {'field': 'order_count', 'old_value': 8619, 'new_value': 8861}]
2025-08-01 09:01:07,662 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM81
2025-08-01 09:01:08,178 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM81
2025-08-01 09:01:08,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94403.76, 'new_value': 104451.52}, {'field': 'total_amount', 'old_value': 363208.04, 'new_value': 373255.8}, {'field': 'order_count', 'old_value': 792, 'new_value': 815}]
2025-08-01 09:01:08,178 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ1
2025-08-01 09:01:08,584 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ1
2025-08-01 09:01:08,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153125.16, 'new_value': 159167.08}, {'field': 'total_amount', 'old_value': 159223.02, 'new_value': 165264.94}, {'field': 'order_count', 'old_value': 843, 'new_value': 877}]
2025-08-01 09:01:08,584 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP1
2025-08-01 09:01:09,037 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP1
2025-08-01 09:01:09,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 308591.0, 'new_value': 316136.0}, {'field': 'offline_amount', 'old_value': 335931.0, 'new_value': 344181.0}, {'field': 'total_amount', 'old_value': 644522.0, 'new_value': 660317.0}, {'field': 'order_count', 'old_value': 4241, 'new_value': 4370}]
2025-08-01 09:01:09,037 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS1
2025-08-01 09:01:09,475 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS1
2025-08-01 09:01:09,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120773.0, 'new_value': 135333.0}, {'field': 'total_amount', 'old_value': 120773.0, 'new_value': 135333.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-08-01 09:01:09,475 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM42
2025-08-01 09:01:09,912 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM42
2025-08-01 09:01:09,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199576.18, 'new_value': 211549.25}, {'field': 'total_amount', 'old_value': 287226.92, 'new_value': 299199.99}, {'field': 'order_count', 'old_value': 4276, 'new_value': 4430}]
2025-08-01 09:01:09,912 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG2
2025-08-01 09:01:10,444 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG2
2025-08-01 09:01:10,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65694.3, 'new_value': 67963.3}, {'field': 'total_amount', 'old_value': 65694.3, 'new_value': 67963.3}, {'field': 'order_count', 'old_value': 291, 'new_value': 301}]
2025-08-01 09:01:10,444 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI2
2025-08-01 09:01:10,928 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI2
2025-08-01 09:01:10,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75401.0, 'new_value': 75660.23}, {'field': 'total_amount', 'old_value': 77268.9, 'new_value': 77528.13}]
2025-08-01 09:01:10,928 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMW9
2025-08-01 09:01:11,366 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMW9
2025-08-01 09:01:11,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95585.0, 'new_value': 99286.0}, {'field': 'total_amount', 'old_value': 95585.0, 'new_value': 99286.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-08-01 09:01:11,366 - INFO - 日期 2025-07 处理完成 - 更新: 62 条，插入: 0 条，错误: 0 条
2025-08-01 09:01:11,366 - INFO - 数据同步完成！更新: 62 条，插入: 0 条，错误: 0 条
2025-08-01 09:01:11,381 - INFO - =================同步完成====================
2025-08-01 12:00:02,900 - INFO - =================使用默认全量同步=============
2025-08-01 12:00:05,076 - INFO - MySQL查询成功，共获取 4617 条记录
2025-08-01 12:00:05,077 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-01 12:00:05,125 - INFO - 开始处理日期: 2025-01
2025-08-01 12:00:05,127 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:05,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:05,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:06,469 - INFO - Response - Page 1:
2025-08-01 12:00:06,669 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:06,669 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:06,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:06,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:07,543 - INFO - Response - Page 2:
2025-08-01 12:00:07,744 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:07,744 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:07,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:07,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:08,293 - INFO - Response - Page 3:
2025-08-01 12:00:08,494 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:08,494 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:08,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:08,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:09,033 - INFO - Response - Page 4:
2025-08-01 12:00:09,233 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:09,233 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:09,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:09,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:09,777 - INFO - Response - Page 5:
2025-08-01 12:00:09,977 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:09,977 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:09,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:09,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:10,525 - INFO - Response - Page 6:
2025-08-01 12:00:10,725 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:10,725 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:10,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:10,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:11,328 - INFO - Response - Page 7:
2025-08-01 12:00:11,528 - INFO - 第 7 页获取到 82 条记录
2025-08-01 12:00:11,528 - INFO - 查询完成，共获取到 682 条记录
2025-08-01 12:00:11,528 - INFO - 获取到 682 条表单数据
2025-08-01 12:00:11,540 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-01 12:00:11,551 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 12:00:11,551 - INFO - 开始处理日期: 2025-02
2025-08-01 12:00:11,552 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:11,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:11,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:12,141 - INFO - Response - Page 1:
2025-08-01 12:00:12,342 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:12,342 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:12,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:12,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:12,897 - INFO - Response - Page 2:
2025-08-01 12:00:13,097 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:13,097 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:13,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:13,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:13,564 - INFO - Response - Page 3:
2025-08-01 12:00:13,764 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:13,764 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:13,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:13,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:14,290 - INFO - Response - Page 4:
2025-08-01 12:00:14,491 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:14,491 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:14,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:14,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:15,038 - INFO - Response - Page 5:
2025-08-01 12:00:15,239 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:15,239 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:15,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:15,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:15,841 - INFO - Response - Page 6:
2025-08-01 12:00:16,041 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:16,041 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:16,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:16,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:16,595 - INFO - Response - Page 7:
2025-08-01 12:00:16,795 - INFO - 第 7 页获取到 70 条记录
2025-08-01 12:00:16,795 - INFO - 查询完成，共获取到 670 条记录
2025-08-01 12:00:16,795 - INFO - 获取到 670 条表单数据
2025-08-01 12:00:16,807 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-01 12:00:16,819 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 12:00:16,819 - INFO - 开始处理日期: 2025-03
2025-08-01 12:00:16,819 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:16,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:16,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:17,329 - INFO - Response - Page 1:
2025-08-01 12:00:17,530 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:17,530 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:17,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:17,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:18,064 - INFO - Response - Page 2:
2025-08-01 12:00:18,264 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:18,264 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:18,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:18,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:18,784 - INFO - Response - Page 3:
2025-08-01 12:00:18,984 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:18,984 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:18,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:18,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:19,574 - INFO - Response - Page 4:
2025-08-01 12:00:19,775 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:19,775 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:19,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:19,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:20,347 - INFO - Response - Page 5:
2025-08-01 12:00:20,548 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:20,548 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:20,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:20,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:21,052 - INFO - Response - Page 6:
2025-08-01 12:00:21,252 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:21,252 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:21,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:21,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:21,730 - INFO - Response - Page 7:
2025-08-01 12:00:21,930 - INFO - 第 7 页获取到 61 条记录
2025-08-01 12:00:21,930 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 12:00:21,930 - INFO - 获取到 661 条表单数据
2025-08-01 12:00:21,943 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-01 12:00:21,954 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 12:00:21,954 - INFO - 开始处理日期: 2025-04
2025-08-01 12:00:21,954 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:21,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:21,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:22,433 - INFO - Response - Page 1:
2025-08-01 12:00:22,634 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:22,634 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:22,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:22,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:23,234 - INFO - Response - Page 2:
2025-08-01 12:00:23,434 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:23,434 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:23,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:23,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:24,059 - INFO - Response - Page 3:
2025-08-01 12:00:24,259 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:24,259 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:24,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:24,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:24,846 - INFO - Response - Page 4:
2025-08-01 12:00:25,046 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:25,046 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:25,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:25,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:25,554 - INFO - Response - Page 5:
2025-08-01 12:00:25,754 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:25,754 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:25,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:25,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:26,311 - INFO - Response - Page 6:
2025-08-01 12:00:26,511 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:26,511 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:26,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:26,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:26,961 - INFO - Response - Page 7:
2025-08-01 12:00:27,161 - INFO - 第 7 页获取到 56 条记录
2025-08-01 12:00:27,161 - INFO - 查询完成，共获取到 656 条记录
2025-08-01 12:00:27,161 - INFO - 获取到 656 条表单数据
2025-08-01 12:00:27,173 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-01 12:00:27,185 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 12:00:27,185 - INFO - 开始处理日期: 2025-05
2025-08-01 12:00:27,185 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:27,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:27,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:27,680 - INFO - Response - Page 1:
2025-08-01 12:00:27,880 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:27,880 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:27,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:27,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:28,393 - INFO - Response - Page 2:
2025-08-01 12:00:28,593 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:28,593 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:28,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:28,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:29,524 - INFO - Response - Page 3:
2025-08-01 12:00:29,724 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:29,724 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:29,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:29,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:30,232 - INFO - Response - Page 4:
2025-08-01 12:00:30,432 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:30,432 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:30,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:30,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:31,023 - INFO - Response - Page 5:
2025-08-01 12:00:31,223 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:31,223 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:31,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:31,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:31,742 - INFO - Response - Page 6:
2025-08-01 12:00:31,943 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:31,943 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:31,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:31,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:32,506 - INFO - Response - Page 7:
2025-08-01 12:00:32,706 - INFO - 第 7 页获取到 65 条记录
2025-08-01 12:00:32,706 - INFO - 查询完成，共获取到 665 条记录
2025-08-01 12:00:32,706 - INFO - 获取到 665 条表单数据
2025-08-01 12:00:32,719 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-01 12:00:32,730 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 12:00:32,730 - INFO - 开始处理日期: 2025-06
2025-08-01 12:00:32,730 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:32,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:32,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:33,272 - INFO - Response - Page 1:
2025-08-01 12:00:33,472 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:33,472 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:33,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:33,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:33,945 - INFO - Response - Page 2:
2025-08-01 12:00:34,146 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:34,146 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:34,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:34,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:34,659 - INFO - Response - Page 3:
2025-08-01 12:00:34,860 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:34,860 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:34,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:34,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:35,349 - INFO - Response - Page 4:
2025-08-01 12:00:35,549 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:35,549 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:35,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:35,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:36,045 - INFO - Response - Page 5:
2025-08-01 12:00:36,247 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:36,247 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:36,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:36,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:36,780 - INFO - Response - Page 6:
2025-08-01 12:00:36,981 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:36,981 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:36,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:36,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:37,473 - INFO - Response - Page 7:
2025-08-01 12:00:37,674 - INFO - 第 7 页获取到 61 条记录
2025-08-01 12:00:37,674 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 12:00:37,674 - INFO - 获取到 661 条表单数据
2025-08-01 12:00:37,687 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-01 12:00:37,697 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 12:00:37,697 - INFO - 开始处理日期: 2025-07
2025-08-01 12:00:37,698 - INFO - Request Parameters - Page 1:
2025-08-01 12:00:37,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:37,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:38,221 - INFO - Response - Page 1:
2025-08-01 12:00:38,421 - INFO - 第 1 页获取到 100 条记录
2025-08-01 12:00:38,421 - INFO - Request Parameters - Page 2:
2025-08-01 12:00:38,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:38,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:38,953 - INFO - Response - Page 2:
2025-08-01 12:00:39,153 - INFO - 第 2 页获取到 100 条记录
2025-08-01 12:00:39,153 - INFO - Request Parameters - Page 3:
2025-08-01 12:00:39,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:39,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:39,723 - INFO - Response - Page 3:
2025-08-01 12:00:39,923 - INFO - 第 3 页获取到 100 条记录
2025-08-01 12:00:39,923 - INFO - Request Parameters - Page 4:
2025-08-01 12:00:39,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:39,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:40,406 - INFO - Response - Page 4:
2025-08-01 12:00:40,608 - INFO - 第 4 页获取到 100 条记录
2025-08-01 12:00:40,608 - INFO - Request Parameters - Page 5:
2025-08-01 12:00:40,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:40,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:41,177 - INFO - Response - Page 5:
2025-08-01 12:00:41,377 - INFO - 第 5 页获取到 100 条记录
2025-08-01 12:00:41,377 - INFO - Request Parameters - Page 6:
2025-08-01 12:00:41,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:41,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:41,883 - INFO - Response - Page 6:
2025-08-01 12:00:42,083 - INFO - 第 6 页获取到 100 条记录
2025-08-01 12:00:42,083 - INFO - Request Parameters - Page 7:
2025-08-01 12:00:42,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:00:42,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:00:42,405 - INFO - Response - Page 7:
2025-08-01 12:00:42,606 - INFO - 第 7 页获取到 19 条记录
2025-08-01 12:00:42,606 - INFO - 查询完成，共获取到 619 条记录
2025-08-01 12:00:42,606 - INFO - 获取到 619 条表单数据
2025-08-01 12:00:42,617 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-01 12:00:42,617 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMT4
2025-08-01 12:00:43,126 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMT4
2025-08-01 12:00:43,126 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138227.24, 'new_value': 140246.04}, {'field': 'total_amount', 'old_value': 141313.04, 'new_value': 143331.84}, {'field': 'order_count', 'old_value': 641, 'new_value': 651}]
2025-08-01 12:00:43,127 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMU4
2025-08-01 12:00:43,618 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMU4
2025-08-01 12:00:43,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 919153.0, 'new_value': 955634.0}, {'field': 'total_amount', 'old_value': 919153.0, 'new_value': 955634.0}, {'field': 'order_count', 'old_value': 1239, 'new_value': 1285}]
2025-08-01 12:00:43,618 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMV4
2025-08-01 12:00:44,023 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMV4
2025-08-01 12:00:44,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144525.46, 'new_value': 149615.81}, {'field': 'offline_amount', 'old_value': 167480.67, 'new_value': 173139.28}, {'field': 'total_amount', 'old_value': 312006.13, 'new_value': 322755.09}, {'field': 'order_count', 'old_value': 8676, 'new_value': 8944}]
2025-08-01 12:00:44,023 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMW4
2025-08-01 12:00:44,594 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMW4
2025-08-01 12:00:44,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29265.0, 'new_value': 29292.0}, {'field': 'total_amount', 'old_value': 29265.0, 'new_value': 29292.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-08-01 12:00:44,594 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMX4
2025-08-01 12:00:45,008 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMX4
2025-08-01 12:00:45,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67704.64, 'new_value': 69873.7}, {'field': 'total_amount', 'old_value': 67704.64, 'new_value': 69873.7}, {'field': 'order_count', 'old_value': 8747, 'new_value': 9043}]
2025-08-01 12:00:45,008 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMY4
2025-08-01 12:00:45,498 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMY4
2025-08-01 12:00:45,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36167.0, 'new_value': 37053.0}, {'field': 'total_amount', 'old_value': 36176.9, 'new_value': 37062.9}, {'field': 'order_count', 'old_value': 376, 'new_value': 386}]
2025-08-01 12:00:45,498 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMZ4
2025-08-01 12:00:45,978 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMZ4
2025-08-01 12:00:45,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 223972.0, 'new_value': 226541.0}, {'field': 'offline_amount', 'old_value': 122987.0, 'new_value': 132411.0}, {'field': 'total_amount', 'old_value': 346959.0, 'new_value': 358952.0}, {'field': 'order_count', 'old_value': 5285, 'new_value': 5450}]
2025-08-01 12:00:45,978 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05
2025-08-01 12:00:46,525 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05
2025-08-01 12:00:46,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30623.76, 'new_value': 32253.76}, {'field': 'offline_amount', 'old_value': 15839.07, 'new_value': 16545.07}, {'field': 'total_amount', 'old_value': 46462.83, 'new_value': 48798.83}, {'field': 'order_count', 'old_value': 2101, 'new_value': 2209}]
2025-08-01 12:00:46,525 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM15
2025-08-01 12:00:46,993 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM15
2025-08-01 12:00:46,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46261.0, 'new_value': 55061.0}, {'field': 'total_amount', 'old_value': 46261.0, 'new_value': 55061.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-08-01 12:00:46,993 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM25
2025-08-01 12:00:47,478 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM25
2025-08-01 12:00:47,479 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15881.46, 'new_value': 16260.22}, {'field': 'offline_amount', 'old_value': 31560.26, 'new_value': 32842.2}, {'field': 'total_amount', 'old_value': 47441.72, 'new_value': 49102.42}, {'field': 'order_count', 'old_value': 903, 'new_value': 940}]
2025-08-01 12:00:47,479 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP7
2025-08-01 12:00:47,982 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP7
2025-08-01 12:00:47,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28425.0, 'new_value': 31005.0}, {'field': 'total_amount', 'old_value': 37524.0, 'new_value': 40104.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 82}]
2025-08-01 12:00:47,983 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM45
2025-08-01 12:00:48,432 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM45
2025-08-01 12:00:48,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135213.91, 'new_value': 137364.28}, {'field': 'total_amount', 'old_value': 135213.91, 'new_value': 137364.28}, {'field': 'order_count', 'old_value': 238, 'new_value': 245}]
2025-08-01 12:00:48,432 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM55
2025-08-01 12:00:48,941 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM55
2025-08-01 12:00:48,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95495.0, 'new_value': 97987.0}, {'field': 'total_amount', 'old_value': 100953.0, 'new_value': 103445.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 112}]
2025-08-01 12:00:48,941 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM65
2025-08-01 12:00:49,528 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM65
2025-08-01 12:00:49,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19400.0, 'new_value': 20200.0}, {'field': 'total_amount', 'old_value': 19400.0, 'new_value': 20200.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-08-01 12:00:49,528 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM75
2025-08-01 12:00:50,037 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM75
2025-08-01 12:00:50,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 394963.4, 'new_value': 400626.4}, {'field': 'total_amount', 'old_value': 394963.4, 'new_value': 400626.4}, {'field': 'order_count', 'old_value': 5374, 'new_value': 5531}]
2025-08-01 12:00:50,038 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMA5
2025-08-01 12:00:50,507 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMA5
2025-08-01 12:00:50,507 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79777.44, 'new_value': 82923.23}, {'field': 'offline_amount', 'old_value': 143939.52, 'new_value': 147375.52}, {'field': 'total_amount', 'old_value': 223716.96, 'new_value': 230298.75}, {'field': 'order_count', 'old_value': 2503, 'new_value': 2570}]
2025-08-01 12:00:50,507 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMB5
2025-08-01 12:00:50,969 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMB5
2025-08-01 12:00:50,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213114.0, 'new_value': 216389.0}, {'field': 'total_amount', 'old_value': 213114.0, 'new_value': 216389.0}, {'field': 'order_count', 'old_value': 397, 'new_value': 411}]
2025-08-01 12:00:50,970 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM74
2025-08-01 12:00:51,441 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM74
2025-08-01 12:00:51,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1896.0, 'new_value': 1958.0}, {'field': 'total_amount', 'old_value': 58958.0, 'new_value': 59020.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-08-01 12:00:51,441 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMC5
2025-08-01 12:00:52,301 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMC5
2025-08-01 12:00:52,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134923.0, 'new_value': 139998.0}, {'field': 'total_amount', 'old_value': 134923.0, 'new_value': 139998.0}, {'field': 'order_count', 'old_value': 8075, 'new_value': 8394}]
2025-08-01 12:00:52,301 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ7
2025-08-01 12:00:52,806 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ7
2025-08-01 12:00:52,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94762.0, 'new_value': 97870.0}, {'field': 'total_amount', 'old_value': 94762.0, 'new_value': 97870.0}, {'field': 'order_count', 'old_value': 798, 'new_value': 827}]
2025-08-01 12:00:52,807 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF5
2025-08-01 12:00:53,287 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF5
2025-08-01 12:00:53,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152125.65, 'new_value': 158133.55}, {'field': 'offline_amount', 'old_value': 78457.13, 'new_value': 79456.13}, {'field': 'total_amount', 'old_value': 230582.78, 'new_value': 237589.68}, {'field': 'order_count', 'old_value': 6756, 'new_value': 6943}]
2025-08-01 12:00:53,287 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH5
2025-08-01 12:00:53,721 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH5
2025-08-01 12:00:53,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84759.0, 'new_value': 87871.0}, {'field': 'total_amount', 'old_value': 84759.0, 'new_value': 87871.0}, {'field': 'order_count', 'old_value': 1563, 'new_value': 1619}]
2025-08-01 12:00:53,721 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI5
2025-08-01 12:00:54,216 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI5
2025-08-01 12:00:54,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306642.9, 'new_value': 315975.34}, {'field': 'total_amount', 'old_value': 306642.9, 'new_value': 315975.34}, {'field': 'order_count', 'old_value': 1655, 'new_value': 1706}]
2025-08-01 12:00:54,216 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR7
2025-08-01 12:00:54,718 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR7
2025-08-01 12:00:54,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63439.13, 'new_value': 64938.35}, {'field': 'offline_amount', 'old_value': 41226.15, 'new_value': 42314.31}, {'field': 'total_amount', 'old_value': 104665.28, 'new_value': 107252.66}, {'field': 'order_count', 'old_value': 5761, 'new_value': 5907}]
2025-08-01 12:00:54,719 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ5
2025-08-01 12:00:55,126 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ5
2025-08-01 12:00:55,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 642272.2, 'new_value': 648563.2}, {'field': 'total_amount', 'old_value': 642272.2, 'new_value': 648563.2}, {'field': 'order_count', 'old_value': 505, 'new_value': 513}]
2025-08-01 12:00:55,126 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM5
2025-08-01 12:00:55,582 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM5
2025-08-01 12:00:55,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 931554.91, 'new_value': 962327.61}, {'field': 'total_amount', 'old_value': 967382.37, 'new_value': 998155.07}, {'field': 'order_count', 'old_value': 6692, 'new_value': 6944}]
2025-08-01 12:00:55,582 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT7
2025-08-01 12:00:56,018 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT7
2025-08-01 12:00:56,018 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154905.14, 'new_value': 157926.54}, {'field': 'offline_amount', 'old_value': 1933586.77, 'new_value': 1980417.44}, {'field': 'total_amount', 'old_value': 2088491.91, 'new_value': 2138343.98}, {'field': 'order_count', 'old_value': 13078, 'new_value': 13507}]
2025-08-01 12:00:56,018 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN5
2025-08-01 12:00:56,522 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN5
2025-08-01 12:00:56,522 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50243.0, 'new_value': 50429.0}, {'field': 'offline_amount', 'old_value': 234657.0, 'new_value': 246139.0}, {'field': 'total_amount', 'old_value': 284900.0, 'new_value': 296568.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 220}]
2025-08-01 12:00:56,523 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM84
2025-08-01 12:00:57,057 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM84
2025-08-01 12:00:57,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94698.0, 'new_value': 126097.0}, {'field': 'total_amount', 'old_value': 125698.0, 'new_value': 157097.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 63}]
2025-08-01 12:00:57,057 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU7
2025-08-01 12:00:57,489 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU7
2025-08-01 12:00:57,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33676.0, 'new_value': 34884.0}, {'field': 'total_amount', 'old_value': 33676.0, 'new_value': 34884.0}, {'field': 'order_count', 'old_value': 1615, 'new_value': 1622}]
2025-08-01 12:00:57,489 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ5
2025-08-01 12:00:57,857 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ5
2025-08-01 12:00:57,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216760.0, 'new_value': 224480.0}, {'field': 'total_amount', 'old_value': 226410.0, 'new_value': 234130.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 41}]
2025-08-01 12:00:57,858 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS5
2025-08-01 12:00:58,331 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS5
2025-08-01 12:00:58,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24204.0, 'new_value': 25074.0}, {'field': 'total_amount', 'old_value': 24204.0, 'new_value': 25074.0}, {'field': 'order_count', 'old_value': 414, 'new_value': 429}]
2025-08-01 12:00:58,332 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT5
2025-08-01 12:00:58,758 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT5
2025-08-01 12:00:58,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25234.0, 'new_value': 26206.0}, {'field': 'total_amount', 'old_value': 32236.1, 'new_value': 33208.1}, {'field': 'order_count', 'old_value': 167, 'new_value': 172}]
2025-08-01 12:00:58,758 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM94
2025-08-01 12:00:59,230 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM94
2025-08-01 12:00:59,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600650.0, 'new_value': 660150.0}, {'field': 'total_amount', 'old_value': 720650.0, 'new_value': 780150.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 75}]
2025-08-01 12:00:59,230 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMA4
2025-08-01 12:00:59,796 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMA4
2025-08-01 12:00:59,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 569299.0, 'new_value': 627299.0}, {'field': 'total_amount', 'old_value': 679299.0, 'new_value': 737299.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 83}]
2025-08-01 12:00:59,796 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMB4
2025-08-01 12:01:00,275 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMB4
2025-08-01 12:01:00,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 562629.0, 'new_value': 617129.0}, {'field': 'total_amount', 'old_value': 702629.0, 'new_value': 757129.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-08-01 12:01:00,275 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV5
2025-08-01 12:01:00,805 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV5
2025-08-01 12:01:00,806 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1848.0, 'new_value': 5846.0}, {'field': 'offline_amount', 'old_value': 17058.9, 'new_value': 17954.9}, {'field': 'total_amount', 'old_value': 18906.9, 'new_value': 23800.9}, {'field': 'order_count', 'old_value': 20, 'new_value': 23}]
2025-08-01 12:01:00,806 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX5
2025-08-01 12:01:01,303 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX5
2025-08-01 12:01:01,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61793.9, 'new_value': 61956.5}, {'field': 'total_amount', 'old_value': 61793.9, 'new_value': 61956.5}, {'field': 'order_count', 'old_value': 149, 'new_value': 151}]
2025-08-01 12:01:01,304 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY5
2025-08-01 12:01:01,761 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMY5
2025-08-01 12:01:01,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34484.0, 'new_value': 55296.0}, {'field': 'total_amount', 'old_value': 34484.0, 'new_value': 55296.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-08-01 12:01:01,761 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM06
2025-08-01 12:01:02,353 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM06
2025-08-01 12:01:02,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10980.0, 'new_value': 11277.0}, {'field': 'total_amount', 'old_value': 12436.0, 'new_value': 12733.0}, {'field': 'order_count', 'old_value': 254, 'new_value': 260}]
2025-08-01 12:01:02,354 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMC4
2025-08-01 12:01:02,782 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMC4
2025-08-01 12:01:02,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46833.0, 'new_value': 47809.0}, {'field': 'total_amount', 'old_value': 46833.0, 'new_value': 47809.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 145}]
2025-08-01 12:01:02,783 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY7
2025-08-01 12:01:03,232 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY7
2025-08-01 12:01:03,232 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3741.97, 'new_value': 3750.97}, {'field': 'offline_amount', 'old_value': 151450.66, 'new_value': 156108.94}, {'field': 'total_amount', 'old_value': 155192.63, 'new_value': 159859.91}, {'field': 'order_count', 'old_value': 3745, 'new_value': 3843}]
2025-08-01 12:01:03,232 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM16
2025-08-01 12:01:03,890 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM16
2025-08-01 12:01:03,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237328.4, 'new_value': 245481.88}, {'field': 'total_amount', 'old_value': 237328.4, 'new_value': 245481.88}, {'field': 'order_count', 'old_value': 1308, 'new_value': 1345}]
2025-08-01 12:01:03,890 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM26
2025-08-01 12:01:04,314 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM26
2025-08-01 12:01:04,314 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17361.77, 'new_value': 18091.84}, {'field': 'offline_amount', 'old_value': 325557.7, 'new_value': 334386.2}, {'field': 'total_amount', 'old_value': 342919.47, 'new_value': 352478.04}, {'field': 'order_count', 'old_value': 2475, 'new_value': 2548}]
2025-08-01 12:01:04,314 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM36
2025-08-01 12:01:04,783 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM36
2025-08-01 12:01:04,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197249.77, 'new_value': 204026.26}, {'field': 'offline_amount', 'old_value': 488257.25, 'new_value': 499915.48}, {'field': 'total_amount', 'old_value': 685507.02, 'new_value': 703941.74}, {'field': 'order_count', 'old_value': 5378, 'new_value': 5524}]
2025-08-01 12:01:04,783 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM46
2025-08-01 12:01:05,212 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM46
2025-08-01 12:01:05,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98210.9, 'new_value': 104190.9}, {'field': 'total_amount', 'old_value': 98210.9, 'new_value': 104190.9}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-08-01 12:01:05,212 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM56
2025-08-01 12:01:05,735 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM56
2025-08-01 12:01:05,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134811.52, 'new_value': 141194.62}, {'field': 'offline_amount', 'old_value': 546269.06, 'new_value': 571016.66}, {'field': 'total_amount', 'old_value': 681080.58, 'new_value': 712211.28}, {'field': 'order_count', 'old_value': 2429, 'new_value': 2533}]
2025-08-01 12:01:05,735 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM08
2025-08-01 12:01:06,286 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM08
2025-08-01 12:01:06,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 456819.5, 'new_value': 466913.0}, {'field': 'offline_amount', 'old_value': 37465.6, 'new_value': 42117.6}, {'field': 'total_amount', 'old_value': 494285.1, 'new_value': 509030.6}, {'field': 'order_count', 'old_value': 582, 'new_value': 598}]
2025-08-01 12:01:06,287 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM28
2025-08-01 12:01:06,799 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM28
2025-08-01 12:01:06,800 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15640.28, 'new_value': 15952.62}, {'field': 'offline_amount', 'old_value': 258397.04, 'new_value': 264038.02}, {'field': 'total_amount', 'old_value': 274037.32, 'new_value': 279990.64}, {'field': 'order_count', 'old_value': 1395, 'new_value': 1442}]
2025-08-01 12:01:06,800 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8
2025-08-01 12:01:07,380 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8
2025-08-01 12:01:07,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274404.0, 'new_value': 277715.0}, {'field': 'total_amount', 'old_value': 409828.7, 'new_value': 413139.7}, {'field': 'order_count', 'old_value': 500, 'new_value': 498}]
2025-08-01 12:01:07,381 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA6
2025-08-01 12:01:07,849 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA6
2025-08-01 12:01:07,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 186911.93, 'new_value': 189637.93}, {'field': 'offline_amount', 'old_value': 59145.75, 'new_value': 60358.55}, {'field': 'total_amount', 'old_value': 246057.68, 'new_value': 249996.48}, {'field': 'order_count', 'old_value': 1589, 'new_value': 1631}]
2025-08-01 12:01:07,850 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC6
2025-08-01 12:01:08,287 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC6
2025-08-01 12:01:08,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 9758.0}, {'field': 'offline_amount', 'old_value': 157147.0, 'new_value': 164473.0}, {'field': 'total_amount', 'old_value': 157147.0, 'new_value': 174231.0}, {'field': 'order_count', 'old_value': 577, 'new_value': 604}]
2025-08-01 12:01:08,287 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMD4
2025-08-01 12:01:08,743 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMD4
2025-08-01 12:01:08,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10451.9, 'new_value': 11051.3}, {'field': 'offline_amount', 'old_value': 43160.0, 'new_value': 44272.3}, {'field': 'total_amount', 'old_value': 53611.9, 'new_value': 55323.6}, {'field': 'order_count', 'old_value': 323, 'new_value': 334}]
2025-08-01 12:01:08,744 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD6
2025-08-01 12:01:09,378 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD6
2025-08-01 12:01:09,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138650.54, 'new_value': 142508.39}, {'field': 'total_amount', 'old_value': 138650.54, 'new_value': 142508.39}, {'field': 'order_count', 'old_value': 3777, 'new_value': 3887}]
2025-08-01 12:01:09,379 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME6
2025-08-01 12:01:09,895 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME6
2025-08-01 12:01:09,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84725.6, 'new_value': 87355.43}, {'field': 'offline_amount', 'old_value': 569450.02, 'new_value': 589490.08}, {'field': 'total_amount', 'old_value': 654175.62, 'new_value': 676845.51}, {'field': 'order_count', 'old_value': 1913, 'new_value': 1981}]
2025-08-01 12:01:09,895 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCME4
2025-08-01 12:01:10,384 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCME4
2025-08-01 12:01:10,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52308.0, 'new_value': 54629.0}, {'field': 'total_amount', 'old_value': 52308.0, 'new_value': 54629.0}, {'field': 'order_count', 'old_value': 281, 'new_value': 296}]
2025-08-01 12:01:10,385 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG6
2025-08-01 12:01:10,916 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG6
2025-08-01 12:01:10,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5247.28, 'new_value': 10247.46}, {'field': 'offline_amount', 'old_value': 172761.39, 'new_value': 177761.57}, {'field': 'total_amount', 'old_value': 178008.67, 'new_value': 188009.03}, {'field': 'order_count', 'old_value': 3237, 'new_value': 3318}]
2025-08-01 12:01:10,916 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH6
2025-08-01 12:01:11,433 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH6
2025-08-01 12:01:11,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96385.75, 'new_value': 99515.47}, {'field': 'offline_amount', 'old_value': 1052887.44, 'new_value': 1079241.94}, {'field': 'total_amount', 'old_value': 1149273.19, 'new_value': 1178757.41}, {'field': 'order_count', 'old_value': 4979, 'new_value': 5111}]
2025-08-01 12:01:11,433 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI6
2025-08-01 12:01:11,948 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI6
2025-08-01 12:01:11,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286445.14, 'new_value': 297962.14}, {'field': 'total_amount', 'old_value': 286445.14, 'new_value': 297962.14}, {'field': 'order_count', 'old_value': 13210, 'new_value': 13756}]
2025-08-01 12:01:11,949 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ6
2025-08-01 12:01:12,405 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ6
2025-08-01 12:01:12,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220234.27, 'new_value': 229631.53}, {'field': 'offline_amount', 'old_value': 138293.3, 'new_value': 140984.3}, {'field': 'total_amount', 'old_value': 358527.57, 'new_value': 370615.83}, {'field': 'order_count', 'old_value': 3474, 'new_value': 3581}]
2025-08-01 12:01:12,406 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK6
2025-08-01 12:01:12,816 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK6
2025-08-01 12:01:12,816 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171855.0, 'new_value': 160871.0}, {'field': 'offline_amount', 'old_value': 52692.63, 'new_value': 69163.0}, {'field': 'total_amount', 'old_value': 224547.63, 'new_value': 230034.0}, {'field': 'order_count', 'old_value': 1631, 'new_value': 1689}]
2025-08-01 12:01:12,816 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML6
2025-08-01 12:01:13,300 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML6
2025-08-01 12:01:13,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 385463.79, 'new_value': 401251.88}, {'field': 'total_amount', 'old_value': 386805.79, 'new_value': 402593.88}, {'field': 'order_count', 'old_value': 4916, 'new_value': 5104}]
2025-08-01 12:01:13,300 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM6
2025-08-01 12:01:13,871 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM6
2025-08-01 12:01:13,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103349.56, 'new_value': 105349.56}, {'field': 'total_amount', 'old_value': 103947.56, 'new_value': 105947.56}, {'field': 'order_count', 'old_value': 115, 'new_value': 117}]
2025-08-01 12:01:13,871 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN6
2025-08-01 12:01:14,329 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN6
2025-08-01 12:01:14,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 568882.3, 'new_value': 581702.2}, {'field': 'total_amount', 'old_value': 599235.3, 'new_value': 612055.2}, {'field': 'order_count', 'old_value': 69, 'new_value': 71}]
2025-08-01 12:01:14,329 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMT8
2025-08-01 12:01:14,759 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMT8
2025-08-01 12:01:14,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126562.39, 'new_value': 130697.39}, {'field': 'offline_amount', 'old_value': 156475.4, 'new_value': 162247.4}, {'field': 'total_amount', 'old_value': 283037.79, 'new_value': 292944.79}, {'field': 'order_count', 'old_value': 6568, 'new_value': 6786}]
2025-08-01 12:01:14,759 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO6
2025-08-01 12:01:15,218 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO6
2025-08-01 12:01:15,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182462.23, 'new_value': 188241.48}, {'field': 'total_amount', 'old_value': 182462.23, 'new_value': 188241.48}, {'field': 'order_count', 'old_value': 6250, 'new_value': 6452}]
2025-08-01 12:01:15,218 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP6
2025-08-01 12:01:15,722 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP6
2025-08-01 12:01:15,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56963.0, 'new_value': 62933.0}, {'field': 'total_amount', 'old_value': 242491.0, 'new_value': 248461.0}, {'field': 'order_count', 'old_value': 1319, 'new_value': 1363}]
2025-08-01 12:01:15,722 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ6
2025-08-01 12:01:16,254 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ6
2025-08-01 12:01:16,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203006.67, 'new_value': 219025.67}, {'field': 'total_amount', 'old_value': 203006.67, 'new_value': 219025.67}, {'field': 'order_count', 'old_value': 65, 'new_value': 68}]
2025-08-01 12:01:16,254 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMF4
2025-08-01 12:01:16,788 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMF4
2025-08-01 12:01:16,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49044.0, 'new_value': 49047.0}, {'field': 'total_amount', 'old_value': 49044.0, 'new_value': 49047.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 3154}]
2025-08-01 12:01:16,789 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS6
2025-08-01 12:01:17,301 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS6
2025-08-01 12:01:17,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1965136.13, 'new_value': 2037466.89}, {'field': 'offline_amount', 'old_value': 434232.68, 'new_value': 441633.16}, {'field': 'total_amount', 'old_value': 2399368.81, 'new_value': 2479100.05}, {'field': 'order_count', 'old_value': 9226, 'new_value': 9557}]
2025-08-01 12:01:17,302 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT6
2025-08-01 12:01:17,741 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT6
2025-08-01 12:01:17,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60621.29, 'new_value': 63125.61}, {'field': 'total_amount', 'old_value': 60621.29, 'new_value': 63125.61}, {'field': 'order_count', 'old_value': 318, 'new_value': 325}]
2025-08-01 12:01:17,741 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU6
2025-08-01 12:01:18,265 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU6
2025-08-01 12:01:18,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197939.63, 'new_value': 207930.71}, {'field': 'offline_amount', 'old_value': 141924.41, 'new_value': 147108.69}, {'field': 'total_amount', 'old_value': 339864.04, 'new_value': 355039.4}, {'field': 'order_count', 'old_value': 13349, 'new_value': 14030}]
2025-08-01 12:01:18,266 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM68
2025-08-01 12:01:18,712 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM68
2025-08-01 12:01:18,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84887.92, 'new_value': 85588.27}, {'field': 'total_amount', 'old_value': 87141.42, 'new_value': 87841.77}, {'field': 'order_count', 'old_value': 322, 'new_value': 327}]
2025-08-01 12:01:18,713 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW6
2025-08-01 12:01:19,209 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW6
2025-08-01 12:01:19,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11899.89, 'new_value': 12110.89}, {'field': 'offline_amount', 'old_value': 25372.15, 'new_value': 26732.15}, {'field': 'total_amount', 'old_value': 37272.04, 'new_value': 38843.04}, {'field': 'order_count', 'old_value': 1337, 'new_value': 1367}]
2025-08-01 12:01:19,209 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX6
2025-08-01 12:01:19,640 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX6
2025-08-01 12:01:19,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168553.8, 'new_value': 174738.58}, {'field': 'offline_amount', 'old_value': 350918.25, 'new_value': 360857.1}, {'field': 'total_amount', 'old_value': 519472.05, 'new_value': 535595.68}, {'field': 'order_count', 'old_value': 18847, 'new_value': 19464}]
2025-08-01 12:01:19,640 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM78
2025-08-01 12:01:20,123 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM78
2025-08-01 12:01:20,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27429.0, 'new_value': 28264.0}, {'field': 'total_amount', 'old_value': 27429.0, 'new_value': 28264.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 136}]
2025-08-01 12:01:20,123 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM17
2025-08-01 12:01:20,615 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM17
2025-08-01 12:01:20,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 644587.96, 'new_value': 664520.5}, {'field': 'total_amount', 'old_value': 644587.96, 'new_value': 664520.5}, {'field': 'order_count', 'old_value': 8974, 'new_value': 9244}]
2025-08-01 12:01:20,616 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM37
2025-08-01 12:01:21,074 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM37
2025-08-01 12:01:21,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 624291.06, 'new_value': 644213.57}, {'field': 'total_amount', 'old_value': 624291.06, 'new_value': 644213.57}, {'field': 'order_count', 'old_value': 7027, 'new_value': 7263}]
2025-08-01 12:01:21,074 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM57
2025-08-01 12:01:21,563 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM57
2025-08-01 12:01:21,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258232.0, 'new_value': 267393.0}, {'field': 'total_amount', 'old_value': 258232.0, 'new_value': 267393.0}, {'field': 'order_count', 'old_value': 9974, 'new_value': 10341}]
2025-08-01 12:01:21,563 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMG4
2025-08-01 12:01:22,055 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMG4
2025-08-01 12:01:22,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3938.68, 'new_value': 4038.04}, {'field': 'offline_amount', 'old_value': 174067.14, 'new_value': 181064.77}, {'field': 'total_amount', 'old_value': 178005.82, 'new_value': 185102.81}, {'field': 'order_count', 'old_value': 895, 'new_value': 935}]
2025-08-01 12:01:22,056 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM67
2025-08-01 12:01:22,525 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM67
2025-08-01 12:01:22,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17647.05, 'new_value': 17871.05}, {'field': 'total_amount', 'old_value': 25769.85, 'new_value': 25993.85}, {'field': 'order_count', 'old_value': 125, 'new_value': 126}]
2025-08-01 12:01:22,525 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM87
2025-08-01 12:01:22,966 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM87
2025-08-01 12:01:22,967 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2891.0}, {'field': 'total_amount', 'old_value': 181573.48, 'new_value': 184464.48}, {'field': 'order_count', 'old_value': 266, 'new_value': 272}]
2025-08-01 12:01:22,967 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM97
2025-08-01 12:01:23,584 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM97
2025-08-01 12:01:23,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139182.75, 'new_value': 148212.45}, {'field': 'total_amount', 'old_value': 173182.75, 'new_value': 182212.45}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-08-01 12:01:23,585 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB7
2025-08-01 12:01:24,025 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB7
2025-08-01 12:01:24,025 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90925.0, 'new_value': 93145.0}, {'field': 'offline_amount', 'old_value': 448846.37, 'new_value': 469168.37}, {'field': 'total_amount', 'old_value': 539771.37, 'new_value': 562313.37}, {'field': 'order_count', 'old_value': 791, 'new_value': 814}]
2025-08-01 12:01:24,025 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC7
2025-08-01 12:01:24,513 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC7
2025-08-01 12:01:24,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90310.73, 'new_value': 92618.59}, {'field': 'total_amount', 'old_value': 90310.73, 'new_value': 92618.59}, {'field': 'order_count', 'old_value': 2703, 'new_value': 2774}]
2025-08-01 12:01:24,513 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME7
2025-08-01 12:01:25,048 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME7
2025-08-01 12:01:25,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 794112.47, 'new_value': 815857.03}, {'field': 'total_amount', 'old_value': 828613.67, 'new_value': 850358.23}, {'field': 'order_count', 'old_value': 6430, 'new_value': 6627}]
2025-08-01 12:01:25,049 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG7
2025-08-01 12:01:25,523 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG7
2025-08-01 12:01:25,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55412.0, 'new_value': 58336.0}, {'field': 'total_amount', 'old_value': 55653.0, 'new_value': 58577.0}, {'field': 'order_count', 'old_value': 209, 'new_value': 217}]
2025-08-01 12:01:25,523 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH7
2025-08-01 12:01:25,978 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH7
2025-08-01 12:01:25,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46668.0, 'new_value': 48048.0}, {'field': 'total_amount', 'old_value': 46668.0, 'new_value': 48048.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 225}]
2025-08-01 12:01:25,979 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM98
2025-08-01 12:01:26,485 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM98
2025-08-01 12:01:26,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87058.59, 'new_value': 89862.02}, {'field': 'offline_amount', 'old_value': 438080.56, 'new_value': 456007.5}, {'field': 'total_amount', 'old_value': 525139.15, 'new_value': 545869.52}, {'field': 'order_count', 'old_value': 3424, 'new_value': 3551}]
2025-08-01 12:01:26,485 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI7
2025-08-01 12:01:26,888 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI7
2025-08-01 12:01:26,889 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222212.68, 'new_value': 229490.0}, {'field': 'total_amount', 'old_value': 222212.68, 'new_value': 229490.0}, {'field': 'order_count', 'old_value': 5742, 'new_value': 5925}]
2025-08-01 12:01:26,889 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM4
2025-08-01 12:01:27,279 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM4
2025-08-01 12:01:27,279 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35276.5, 'new_value': 35516.5}, {'field': 'total_amount', 'old_value': 35276.5, 'new_value': 35516.5}, {'field': 'order_count', 'old_value': 376, 'new_value': 381}]
2025-08-01 12:01:27,279 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM5
2025-08-01 12:01:27,727 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM5
2025-08-01 12:01:27,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 699390.0, 'new_value': 704803.0}, {'field': 'total_amount', 'old_value': 693402.0, 'new_value': 698815.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 108}]
2025-08-01 12:01:27,727 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM6
2025-08-01 12:01:28,244 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM6
2025-08-01 12:01:28,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75329.1, 'new_value': 76782.35}, {'field': 'total_amount', 'old_value': 97428.0, 'new_value': 98881.25}, {'field': 'order_count', 'old_value': 927, 'new_value': 944}]
2025-08-01 12:01:28,245 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM8
2025-08-01 12:01:28,743 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM8
2025-08-01 12:01:28,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398973.68, 'new_value': 406715.86}, {'field': 'total_amount', 'old_value': 398973.68, 'new_value': 406715.86}, {'field': 'order_count', 'old_value': 1334, 'new_value': 1362}]
2025-08-01 12:01:28,744 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM9
2025-08-01 12:01:29,213 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM9
2025-08-01 12:01:29,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35458.97, 'new_value': 36412.62}, {'field': 'offline_amount', 'old_value': 29552.94, 'new_value': 30586.24}, {'field': 'total_amount', 'old_value': 65011.91, 'new_value': 66998.86}, {'field': 'order_count', 'old_value': 2960, 'new_value': 3040}]
2025-08-01 12:01:29,213 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA
2025-08-01 12:01:29,688 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA
2025-08-01 12:01:29,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41034.23, 'new_value': 42259.04}, {'field': 'total_amount', 'old_value': 42095.03, 'new_value': 43319.84}, {'field': 'order_count', 'old_value': 360, 'new_value': 368}]
2025-08-01 12:01:29,688 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMD8
2025-08-01 12:01:30,201 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMD8
2025-08-01 12:01:30,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 259562.21, 'new_value': 269108.14}, {'field': 'offline_amount', 'old_value': 201048.58, 'new_value': 207884.64}, {'field': 'total_amount', 'old_value': 460610.79, 'new_value': 476992.78}, {'field': 'order_count', 'old_value': 4773, 'new_value': 4931}]
2025-08-01 12:01:30,201 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB
2025-08-01 12:01:30,721 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB
2025-08-01 12:01:30,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34311.0, 'new_value': 35049.0}, {'field': 'total_amount', 'old_value': 34311.0, 'new_value': 35049.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 136}]
2025-08-01 12:01:30,721 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMI4
2025-08-01 12:01:31,192 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMI4
2025-08-01 12:01:31,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177409.0, 'new_value': 178697.0}, {'field': 'total_amount', 'old_value': 183408.0, 'new_value': 184696.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-08-01 12:01:31,192 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF
2025-08-01 12:01:31,671 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF
2025-08-01 12:01:31,671 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15610.0, 'new_value': 15966.0}, {'field': 'offline_amount', 'old_value': 53577.51, 'new_value': 55242.99}, {'field': 'total_amount', 'old_value': 69187.51, 'new_value': 71208.99}, {'field': 'order_count', 'old_value': 2300, 'new_value': 2372}]
2025-08-01 12:01:31,671 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG
2025-08-01 12:01:32,118 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG
2025-08-01 12:01:32,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72102.41, 'new_value': 77276.25}, {'field': 'total_amount', 'old_value': 132539.01, 'new_value': 137712.85}, {'field': 'order_count', 'old_value': 4808, 'new_value': 5005}]
2025-08-01 12:01:32,118 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH
2025-08-01 12:01:32,581 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH
2025-08-01 12:01:32,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420484.2, 'new_value': 435484.2}, {'field': 'total_amount', 'old_value': 420484.2, 'new_value': 435484.2}, {'field': 'order_count', 'old_value': 2389, 'new_value': 2469}]
2025-08-01 12:01:32,581 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI
2025-08-01 12:01:33,119 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI
2025-08-01 12:01:33,120 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55645.4, 'new_value': 56748.3}, {'field': 'offline_amount', 'old_value': 19215.2, 'new_value': 19284.7}, {'field': 'total_amount', 'old_value': 74860.6, 'new_value': 76033.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 279}]
2025-08-01 12:01:33,120 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK
2025-08-01 12:01:33,588 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK
2025-08-01 12:01:33,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157017.61, 'new_value': 161262.01}, {'field': 'total_amount', 'old_value': 157017.61, 'new_value': 161262.01}, {'field': 'order_count', 'old_value': 2541, 'new_value': 2597}]
2025-08-01 12:01:33,589 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMG8
2025-08-01 12:01:34,016 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMG8
2025-08-01 12:01:34,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117833.04, 'new_value': 124536.39}, {'field': 'offline_amount', 'old_value': 352963.09, 'new_value': 363005.0}, {'field': 'total_amount', 'old_value': 470796.13, 'new_value': 487541.39}, {'field': 'order_count', 'old_value': 5853, 'new_value': 6066}]
2025-08-01 12:01:34,016 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML
2025-08-01 12:01:34,421 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML
2025-08-01 12:01:34,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36606.55, 'new_value': 38106.19}, {'field': 'offline_amount', 'old_value': 60002.7, 'new_value': 62154.62}, {'field': 'total_amount', 'old_value': 96609.25, 'new_value': 100260.81}, {'field': 'order_count', 'old_value': 3806, 'new_value': 3947}]
2025-08-01 12:01:34,422 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM
2025-08-01 12:01:34,894 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM
2025-08-01 12:01:34,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14304.0, 'new_value': 14601.0}, {'field': 'total_amount', 'old_value': 14304.0, 'new_value': 14601.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-08-01 12:01:34,894 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMJ4
2025-08-01 12:01:35,367 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMJ4
2025-08-01 12:01:35,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48270.84, 'new_value': 49994.04}, {'field': 'total_amount', 'old_value': 48568.64, 'new_value': 50291.84}, {'field': 'order_count', 'old_value': 114, 'new_value': 121}]
2025-08-01 12:01:35,367 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN
2025-08-01 12:01:35,832 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN
2025-08-01 12:01:35,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20850.27, 'new_value': 21540.38}, {'field': 'offline_amount', 'old_value': 388200.5, 'new_value': 401625.9}, {'field': 'total_amount', 'old_value': 409050.77, 'new_value': 423166.28}, {'field': 'order_count', 'old_value': 2137, 'new_value': 2220}]
2025-08-01 12:01:35,832 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO
2025-08-01 12:01:36,346 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO
2025-08-01 12:01:36,346 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96658.71, 'new_value': 99508.11}, {'field': 'offline_amount', 'old_value': 107227.85, 'new_value': 111557.88}, {'field': 'total_amount', 'old_value': 203886.56, 'new_value': 211065.99}, {'field': 'order_count', 'old_value': 8116, 'new_value': 8401}]
2025-08-01 12:01:36,347 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ
2025-08-01 12:01:36,825 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ
2025-08-01 12:01:36,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58243.0, 'new_value': 61002.0}, {'field': 'total_amount', 'old_value': 58243.0, 'new_value': 61002.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 155}]
2025-08-01 12:01:36,825 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR
2025-08-01 12:01:37,323 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR
2025-08-01 12:01:37,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45007.0, 'new_value': 45749.0}, {'field': 'total_amount', 'old_value': 45007.0, 'new_value': 45749.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-08-01 12:01:37,323 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT
2025-08-01 12:01:37,749 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT
2025-08-01 12:01:37,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82304.0, 'new_value': 83542.0}, {'field': 'total_amount', 'old_value': 82306.0, 'new_value': 83544.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 66}]
2025-08-01 12:01:37,749 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM01
2025-08-01 12:01:38,224 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM01
2025-08-01 12:01:38,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417700.59, 'new_value': 431979.98}, {'field': 'total_amount', 'old_value': 417700.59, 'new_value': 431979.98}, {'field': 'order_count', 'old_value': 14137, 'new_value': 14580}]
2025-08-01 12:01:38,224 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM21
2025-08-01 12:01:38,825 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM21
2025-08-01 12:01:38,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120984.0, 'new_value': 127683.0}, {'field': 'total_amount', 'old_value': 120985.0, 'new_value': 127684.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-08-01 12:01:38,826 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM71
2025-08-01 12:01:39,205 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM71
2025-08-01 12:01:39,205 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12646.0, 'new_value': 13036.8}, {'field': 'offline_amount', 'old_value': 96817.47, 'new_value': 98489.82}, {'field': 'total_amount', 'old_value': 109463.47, 'new_value': 111526.62}, {'field': 'order_count', 'old_value': 910, 'new_value': 933}]
2025-08-01 12:01:39,205 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM81
2025-08-01 12:01:39,868 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM81
2025-08-01 12:01:39,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63360.75, 'new_value': 65290.06}, {'field': 'offline_amount', 'old_value': 24702.58, 'new_value': 25279.09}, {'field': 'total_amount', 'old_value': 88063.33, 'new_value': 90569.15}, {'field': 'order_count', 'old_value': 4686, 'new_value': 4823}]
2025-08-01 12:01:39,868 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM91
2025-08-01 12:01:40,344 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM91
2025-08-01 12:01:40,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50647.44, 'new_value': 52419.44}, {'field': 'total_amount', 'old_value': 50647.44, 'new_value': 52419.44}, {'field': 'order_count', 'old_value': 2231, 'new_value': 2292}]
2025-08-01 12:01:40,344 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA1
2025-08-01 12:01:40,916 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA1
2025-08-01 12:01:40,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132554.78, 'new_value': 137333.19}, {'field': 'offline_amount', 'old_value': 35926.89, 'new_value': 36468.89}, {'field': 'total_amount', 'old_value': 168481.67, 'new_value': 173802.08}, {'field': 'order_count', 'old_value': 10064, 'new_value': 10376}]
2025-08-01 12:01:40,916 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB1
2025-08-01 12:01:41,443 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB1
2025-08-01 12:01:41,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 165574.61, 'new_value': 168235.87}, {'field': 'offline_amount', 'old_value': 100104.33, 'new_value': 102117.07}, {'field': 'total_amount', 'old_value': 265678.94, 'new_value': 270352.94}, {'field': 'order_count', 'old_value': 1010, 'new_value': 1029}]
2025-08-01 12:01:41,444 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMK4
2025-08-01 12:01:41,981 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMK4
2025-08-01 12:01:41,981 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2969.0, 'new_value': 3947.0}, {'field': 'offline_amount', 'old_value': 334696.0, 'new_value': 340696.0}, {'field': 'total_amount', 'old_value': 337665.0, 'new_value': 344643.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 282}]
2025-08-01 12:01:41,982 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC1
2025-08-01 12:01:42,461 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC1
2025-08-01 12:01:42,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23588.0, 'new_value': 24533.0}, {'field': 'total_amount', 'old_value': 23588.0, 'new_value': 24533.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 70}]
2025-08-01 12:01:42,462 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD1
2025-08-01 12:01:42,978 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD1
2025-08-01 12:01:42,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198422.94, 'new_value': 309563.09}, {'field': 'total_amount', 'old_value': 211149.98, 'new_value': 322290.13}, {'field': 'order_count', 'old_value': 56, 'new_value': 88}]
2025-08-01 12:01:42,979 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME1
2025-08-01 12:01:43,395 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME1
2025-08-01 12:01:43,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90726.18, 'new_value': 92162.18}, {'field': 'total_amount', 'old_value': 92268.18, 'new_value': 93704.18}, {'field': 'order_count', 'old_value': 464, 'new_value': 472}]
2025-08-01 12:01:43,395 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF1
2025-08-01 12:01:43,850 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF1
2025-08-01 12:01:43,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184394.96, 'new_value': 191126.17}, {'field': 'total_amount', 'old_value': 184394.96, 'new_value': 191126.17}, {'field': 'order_count', 'old_value': 5570, 'new_value': 5757}]
2025-08-01 12:01:43,850 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMR4
2025-08-01 12:01:44,278 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMR4
2025-08-01 12:01:44,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25863.79, 'new_value': 25825.51}, {'field': 'total_amount', 'old_value': 29291.96, 'new_value': 29253.68}]
2025-08-01 12:01:44,278 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMM8
2025-08-01 12:01:44,820 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMM8
2025-08-01 12:01:44,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241680.87, 'new_value': 251408.63}, {'field': 'offline_amount', 'old_value': 46110.13, 'new_value': 47428.8}, {'field': 'total_amount', 'old_value': 287791.0, 'new_value': 298837.43}, {'field': 'order_count', 'old_value': 1093, 'new_value': 1131}]
2025-08-01 12:01:44,820 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM1
2025-08-01 12:01:45,339 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM1
2025-08-01 12:01:45,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86966.0, 'new_value': 91063.0}, {'field': 'total_amount', 'old_value': 86966.0, 'new_value': 91063.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 89}]
2025-08-01 12:01:45,340 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN1
2025-08-01 12:01:45,798 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN1
2025-08-01 12:01:45,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104761.7, 'new_value': 109460.9}, {'field': 'total_amount', 'old_value': 104761.7, 'new_value': 109460.9}, {'field': 'order_count', 'old_value': 363, 'new_value': 379}]
2025-08-01 12:01:45,798 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO1
2025-08-01 12:01:46,438 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO1
2025-08-01 12:01:46,438 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48938.74, 'new_value': 50357.03}, {'field': 'offline_amount', 'old_value': 368216.77, 'new_value': 377984.77}, {'field': 'total_amount', 'old_value': 417155.51, 'new_value': 428341.8}, {'field': 'order_count', 'old_value': 76993, 'new_value': 77151}]
2025-08-01 12:01:46,438 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMN8
2025-08-01 12:01:46,892 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMN8
2025-08-01 12:01:46,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32914.84, 'new_value': 33215.09}, {'field': 'offline_amount', 'old_value': 84925.76, 'new_value': 86842.82}, {'field': 'total_amount', 'old_value': 117840.6, 'new_value': 120057.91}, {'field': 'order_count', 'old_value': 1032, 'new_value': 1060}]
2025-08-01 12:01:46,893 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP1
2025-08-01 12:01:47,360 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP1
2025-08-01 12:01:47,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9771.46, 'new_value': 10322.55}, {'field': 'offline_amount', 'old_value': 116853.99, 'new_value': 120693.65}, {'field': 'total_amount', 'old_value': 126625.45, 'new_value': 131016.2}, {'field': 'order_count', 'old_value': 4198, 'new_value': 4329}]
2025-08-01 12:01:47,360 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ1
2025-08-01 12:01:47,848 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ1
2025-08-01 12:01:47,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37907.2, 'new_value': 39363.8}, {'field': 'total_amount', 'old_value': 37907.2, 'new_value': 39363.8}, {'field': 'order_count', 'old_value': 256, 'new_value': 263}]
2025-08-01 12:01:47,848 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR1
2025-08-01 12:01:48,293 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR1
2025-08-01 12:01:48,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109510.6, 'new_value': 109908.6}, {'field': 'total_amount', 'old_value': 109510.6, 'new_value': 109908.6}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-08-01 12:01:48,293 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS1
2025-08-01 12:01:48,701 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS1
2025-08-01 12:01:48,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354154.81, 'new_value': 364195.31}, {'field': 'total_amount', 'old_value': 354154.81, 'new_value': 364195.31}, {'field': 'order_count', 'old_value': 2027, 'new_value': 2086}]
2025-08-01 12:01:48,701 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT1
2025-08-01 12:01:49,174 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT1
2025-08-01 12:01:49,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130215.96, 'new_value': 134783.32}, {'field': 'offline_amount', 'old_value': 125581.68, 'new_value': 128906.05}, {'field': 'total_amount', 'old_value': 255797.64, 'new_value': 263689.37}, {'field': 'order_count', 'old_value': 10539, 'new_value': 10890}]
2025-08-01 12:01:49,175 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO8
2025-08-01 12:01:49,691 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO8
2025-08-01 12:01:49,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214552.2, 'new_value': 218846.6}, {'field': 'total_amount', 'old_value': 221522.2, 'new_value': 225816.6}, {'field': 'order_count', 'old_value': 3034, 'new_value': 3095}]
2025-08-01 12:01:49,692 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX1
2025-08-01 12:01:50,183 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX1
2025-08-01 12:01:50,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243203.0, 'new_value': 248523.0}, {'field': 'total_amount', 'old_value': 243203.0, 'new_value': 248523.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 74}]
2025-08-01 12:01:50,183 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY1
2025-08-01 12:01:50,675 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY1
2025-08-01 12:01:50,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189550.0, 'new_value': 193388.0}, {'field': 'total_amount', 'old_value': 189550.0, 'new_value': 193388.0}, {'field': 'order_count', 'old_value': 366, 'new_value': 376}]
2025-08-01 12:01:50,675 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ1
2025-08-01 12:01:51,211 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ1
2025-08-01 12:01:51,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241851.0, 'new_value': 246898.0}, {'field': 'total_amount', 'old_value': 257540.0, 'new_value': 262587.0}, {'field': 'order_count', 'old_value': 1052, 'new_value': 1080}]
2025-08-01 12:01:51,212 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM02
2025-08-01 12:01:51,673 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM02
2025-08-01 12:01:51,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45803.0, 'new_value': 46818.0}, {'field': 'total_amount', 'old_value': 45806.0, 'new_value': 46821.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 144}]
2025-08-01 12:01:51,673 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCML4
2025-08-01 12:01:52,158 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCML4
2025-08-01 12:01:52,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104091.1, 'new_value': 106581.66}, {'field': 'offline_amount', 'old_value': 77936.42, 'new_value': 80319.54}, {'field': 'total_amount', 'old_value': 182027.52, 'new_value': 186901.2}, {'field': 'order_count', 'old_value': 7946, 'new_value': 8172}]
2025-08-01 12:01:52,159 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM22
2025-08-01 12:01:52,634 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM22
2025-08-01 12:01:52,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24143.57, 'new_value': 25753.57}, {'field': 'total_amount', 'old_value': 31143.57, 'new_value': 32753.57}, {'field': 'order_count', 'old_value': 124, 'new_value': 130}]
2025-08-01 12:01:52,634 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM42
2025-08-01 12:01:53,084 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM42
2025-08-01 12:01:53,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 568123.97, 'new_value': 584134.84}, {'field': 'total_amount', 'old_value': 568123.97, 'new_value': 584134.84}, {'field': 'order_count', 'old_value': 23884, 'new_value': 24963}]
2025-08-01 12:01:53,084 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM52
2025-08-01 12:01:53,573 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM52
2025-08-01 12:01:53,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27431.49, 'new_value': 27832.45}, {'field': 'offline_amount', 'old_value': 92135.89, 'new_value': 95254.71}, {'field': 'total_amount', 'old_value': 119567.38, 'new_value': 123087.16}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1069}]
2025-08-01 12:01:53,573 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM62
2025-08-01 12:01:54,053 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM62
2025-08-01 12:01:54,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24322.81, 'new_value': 24899.59}, {'field': 'offline_amount', 'old_value': 15459.37, 'new_value': 16079.97}, {'field': 'total_amount', 'old_value': 39782.18, 'new_value': 40979.56}, {'field': 'order_count', 'old_value': 2335, 'new_value': 2410}]
2025-08-01 12:01:54,054 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM82
2025-08-01 12:01:54,594 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM82
2025-08-01 12:01:54,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123705.96, 'new_value': 127802.75}, {'field': 'total_amount', 'old_value': 123705.96, 'new_value': 127802.75}, {'field': 'order_count', 'old_value': 4022, 'new_value': 4145}]
2025-08-01 12:01:54,594 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM92
2025-08-01 12:01:55,065 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM92
2025-08-01 12:01:55,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44138.28, 'new_value': 45488.09}, {'field': 'offline_amount', 'old_value': 85278.59, 'new_value': 87658.61}, {'field': 'total_amount', 'old_value': 129416.87, 'new_value': 133146.7}, {'field': 'order_count', 'old_value': 6001, 'new_value': 6155}]
2025-08-01 12:01:55,065 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA2
2025-08-01 12:01:55,507 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA2
2025-08-01 12:01:55,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1966054.27, 'new_value': 2023505.87}, {'field': 'total_amount', 'old_value': 1966054.27, 'new_value': 2023505.87}, {'field': 'order_count', 'old_value': 3904, 'new_value': 4002}]
2025-08-01 12:01:55,507 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB2
2025-08-01 12:01:55,914 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB2
2025-08-01 12:01:55,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8361.0, 'new_value': 8811.0}, {'field': 'offline_amount', 'old_value': 20326.0, 'new_value': 20915.0}, {'field': 'total_amount', 'old_value': 28687.0, 'new_value': 29726.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 148}]
2025-08-01 12:01:55,914 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC2
2025-08-01 12:01:56,471 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC2
2025-08-01 12:01:56,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315085.4, 'new_value': 326642.1}, {'field': 'total_amount', 'old_value': 315085.4, 'new_value': 326642.1}, {'field': 'order_count', 'old_value': 9984, 'new_value': 10354}]
2025-08-01 12:01:56,471 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD2
2025-08-01 12:01:56,939 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD2
2025-08-01 12:01:56,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102587.06, 'new_value': 104555.87}, {'field': 'offline_amount', 'old_value': 99153.26, 'new_value': 103198.98}, {'field': 'total_amount', 'old_value': 201740.32, 'new_value': 207754.85}, {'field': 'order_count', 'old_value': 9421, 'new_value': 9682}]
2025-08-01 12:01:56,940 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU8
2025-08-01 12:01:57,378 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU8
2025-08-01 12:01:57,379 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5430.0, 'new_value': 5480.0}, {'field': 'offline_amount', 'old_value': 36940.0, 'new_value': 37890.0}, {'field': 'total_amount', 'old_value': 42370.0, 'new_value': 43370.0}, {'field': 'order_count', 'old_value': 518, 'new_value': 535}]
2025-08-01 12:01:57,379 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF2
2025-08-01 12:01:57,783 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF2
2025-08-01 12:01:57,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109412.0, 'new_value': 114412.0}, {'field': 'offline_amount', 'old_value': 167789.0, 'new_value': 172493.0}, {'field': 'total_amount', 'old_value': 277201.0, 'new_value': 286905.0}, {'field': 'order_count', 'old_value': 6933, 'new_value': 7176}]
2025-08-01 12:01:57,783 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMN4
2025-08-01 12:01:58,323 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMN4
2025-08-01 12:01:58,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107271.31, 'new_value': 111087.0}, {'field': 'offline_amount', 'old_value': 40714.44, 'new_value': 41926.06}, {'field': 'total_amount', 'old_value': 147985.75, 'new_value': 153013.06}, {'field': 'order_count', 'old_value': 5325, 'new_value': 5524}]
2025-08-01 12:01:58,324 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG2
2025-08-01 12:01:58,795 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG2
2025-08-01 12:01:58,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114930.8, 'new_value': 118707.8}, {'field': 'total_amount', 'old_value': 145954.7, 'new_value': 149731.7}, {'field': 'order_count', 'old_value': 183, 'new_value': 186}]
2025-08-01 12:01:58,795 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH2
2025-08-01 12:01:59,252 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH2
2025-08-01 12:01:59,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41616.0, 'new_value': 42588.0}, {'field': 'offline_amount', 'old_value': 44666.8, 'new_value': 44702.8}, {'field': 'total_amount', 'old_value': 86282.8, 'new_value': 87290.8}, {'field': 'order_count', 'old_value': 153, 'new_value': 157}]
2025-08-01 12:01:59,252 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI2
2025-08-01 12:01:59,741 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI2
2025-08-01 12:01:59,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 551210.0, 'new_value': 581990.0}, {'field': 'total_amount', 'old_value': 551210.0, 'new_value': 581990.0}, {'field': 'order_count', 'old_value': 413, 'new_value': 436}]
2025-08-01 12:01:59,742 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ2
2025-08-01 12:02:00,215 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ2
2025-08-01 12:02:00,215 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44196.0, 'new_value': 45826.49}, {'field': 'offline_amount', 'old_value': 398455.0, 'new_value': 411721.16}, {'field': 'total_amount', 'old_value': 442651.0, 'new_value': 457547.65}, {'field': 'order_count', 'old_value': 28326, 'new_value': 28836}]
2025-08-01 12:02:00,215 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX8
2025-08-01 12:02:00,779 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX8
2025-08-01 12:02:00,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202727.89, 'new_value': 208702.29}, {'field': 'total_amount', 'old_value': 202727.89, 'new_value': 208702.29}, {'field': 'order_count', 'old_value': 1047, 'new_value': 1079}]
2025-08-01 12:02:00,779 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMO4
2025-08-01 12:02:01,313 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMO4
2025-08-01 12:02:01,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34990.0, 'new_value': 35999.5}, {'field': 'offline_amount', 'old_value': 270695.7, 'new_value': 278869.3}, {'field': 'total_amount', 'old_value': 305685.7, 'new_value': 314868.8}, {'field': 'order_count', 'old_value': 9764, 'new_value': 10105}]
2025-08-01 12:02:01,313 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK2
2025-08-01 12:02:01,842 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK2
2025-08-01 12:02:01,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127471.0, 'new_value': 132067.0}, {'field': 'total_amount', 'old_value': 127471.0, 'new_value': 132067.0}, {'field': 'order_count', 'old_value': 345, 'new_value': 356}]
2025-08-01 12:02:01,842 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML2
2025-08-01 12:02:02,336 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML2
2025-08-01 12:02:02,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14089.28, 'new_value': 14418.83}, {'field': 'offline_amount', 'old_value': 52002.06, 'new_value': 53696.35}, {'field': 'total_amount', 'old_value': 66091.34, 'new_value': 68115.18}, {'field': 'order_count', 'old_value': 2341, 'new_value': 2414}]
2025-08-01 12:02:02,336 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ2
2025-08-01 12:02:02,808 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ2
2025-08-01 12:02:02,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 740747.16, 'new_value': 772455.16}, {'field': 'total_amount', 'old_value': 741794.46, 'new_value': 773502.46}, {'field': 'order_count', 'old_value': 1976, 'new_value': 2045}]
2025-08-01 12:02:02,808 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR2
2025-08-01 12:02:03,319 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR2
2025-08-01 12:02:03,319 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77991.02, 'new_value': 79965.29}, {'field': 'offline_amount', 'old_value': 267538.74, 'new_value': 275457.87}, {'field': 'total_amount', 'old_value': 345529.76, 'new_value': 355423.16}, {'field': 'order_count', 'old_value': 7321, 'new_value': 7535}]
2025-08-01 12:02:03,319 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS2
2025-08-01 12:02:03,774 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS2
2025-08-01 12:02:03,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 597763.4, 'new_value': 617110.4}, {'field': 'total_amount', 'old_value': 597763.4, 'new_value': 617110.4}, {'field': 'order_count', 'old_value': 2896, 'new_value': 2982}]
2025-08-01 12:02:03,774 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU2
2025-08-01 12:02:04,285 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU2
2025-08-01 12:02:04,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171404.6, 'new_value': 192780.4}, {'field': 'total_amount', 'old_value': 437966.9, 'new_value': 459342.7}, {'field': 'order_count', 'old_value': 11727, 'new_value': 12315}]
2025-08-01 12:02:04,285 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ8
2025-08-01 12:02:04,785 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ8
2025-08-01 12:02:04,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22993.44, 'new_value': 23308.98}, {'field': 'total_amount', 'old_value': 22993.44, 'new_value': 23308.98}, {'field': 'order_count', 'old_value': 227, 'new_value': 230}]
2025-08-01 12:02:04,785 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV2
2025-08-01 12:02:05,268 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV2
2025-08-01 12:02:05,268 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 258417.22, 'new_value': 267152.77}, {'field': 'offline_amount', 'old_value': 492940.84, 'new_value': 507106.02}, {'field': 'total_amount', 'old_value': 751358.06, 'new_value': 774258.79}, {'field': 'order_count', 'old_value': 21009, 'new_value': 21682}]
2025-08-01 12:02:05,269 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMN
2025-08-01 12:02:05,765 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMN
2025-08-01 12:02:05,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23304.0, 'new_value': 23541.0}, {'field': 'total_amount', 'old_value': 23304.0, 'new_value': 23541.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 121}]
2025-08-01 12:02:05,766 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMO
2025-08-01 12:02:06,284 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMO
2025-08-01 12:02:06,284 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40701.5, 'new_value': 41351.01}, {'field': 'offline_amount', 'old_value': 291142.59, 'new_value': 300061.49}, {'field': 'total_amount', 'old_value': 331844.09, 'new_value': 341412.5}, {'field': 'order_count', 'old_value': 10803, 'new_value': 11140}]
2025-08-01 12:02:06,284 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM09
2025-08-01 12:02:06,793 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM09
2025-08-01 12:02:06,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 398595.4, 'new_value': 423493.4}, {'field': 'offline_amount', 'old_value': 45354.0, 'new_value': 46112.0}, {'field': 'total_amount', 'old_value': 443949.4, 'new_value': 469605.4}, {'field': 'order_count', 'old_value': 7527, 'new_value': 7815}]
2025-08-01 12:02:06,793 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMP
2025-08-01 12:02:07,219 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMP
2025-08-01 12:02:07,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78044.0, 'new_value': 80734.0}, {'field': 'total_amount', 'old_value': 125202.5, 'new_value': 127892.5}, {'field': 'order_count', 'old_value': 122, 'new_value': 127}]
2025-08-01 12:02:07,220 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM19
2025-08-01 12:02:07,736 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM19
2025-08-01 12:02:07,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 760640.0, 'new_value': 781373.0}, {'field': 'total_amount', 'old_value': 760640.0, 'new_value': 781373.0}, {'field': 'order_count', 'old_value': 3766, 'new_value': 3866}]
2025-08-01 12:02:07,736 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMS
2025-08-01 12:02:08,167 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMS
2025-08-01 12:02:08,167 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203141.65, 'new_value': 209204.59}, {'field': 'offline_amount', 'old_value': 267563.1, 'new_value': 275149.19}, {'field': 'total_amount', 'old_value': 470704.75, 'new_value': 484353.78}, {'field': 'order_count', 'old_value': 16134, 'new_value': 16609}]
2025-08-01 12:02:08,167 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMT
2025-08-01 12:02:08,607 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMT
2025-08-01 12:02:08,607 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16636.81, 'new_value': 17098.06}, {'field': 'offline_amount', 'old_value': 222929.75, 'new_value': 230316.7}, {'field': 'total_amount', 'old_value': 239566.56, 'new_value': 247414.76}, {'field': 'order_count', 'old_value': 2954, 'new_value': 3050}]
2025-08-01 12:02:08,608 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM39
2025-08-01 12:02:09,053 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM39
2025-08-01 12:02:09,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344535.88, 'new_value': 353241.81}, {'field': 'total_amount', 'old_value': 344535.88, 'new_value': 353241.81}, {'field': 'order_count', 'old_value': 1252, 'new_value': 1292}]
2025-08-01 12:02:09,053 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMP4
2025-08-01 12:02:09,655 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMP4
2025-08-01 12:02:09,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1689724.0, 'new_value': 1735390.0}, {'field': 'offline_amount', 'old_value': 514663.0, 'new_value': 526984.0}, {'field': 'total_amount', 'old_value': 2204387.0, 'new_value': 2262374.0}, {'field': 'order_count', 'old_value': 1983, 'new_value': 2060}]
2025-08-01 12:02:09,655 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMU
2025-08-01 12:02:10,118 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMU
2025-08-01 12:02:10,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 379311.14, 'new_value': 392883.82}, {'field': 'offline_amount', 'old_value': 43013.91, 'new_value': 43419.31}, {'field': 'total_amount', 'old_value': 422325.05, 'new_value': 436303.13}, {'field': 'order_count', 'old_value': 17091, 'new_value': 17618}]
2025-08-01 12:02:10,119 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMV
2025-08-01 12:02:10,546 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMV
2025-08-01 12:02:10,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281193.67, 'new_value': 292491.11}, {'field': 'total_amount', 'old_value': 307614.63, 'new_value': 318912.07}, {'field': 'order_count', 'old_value': 13692, 'new_value': 14151}]
2025-08-01 12:02:10,547 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMW8
2025-08-01 12:02:11,003 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMW8
2025-08-01 12:02:11,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49043.1, 'new_value': 50710.5}, {'field': 'total_amount', 'old_value': 49043.1, 'new_value': 50710.5}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-08-01 12:02:11,003 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMW
2025-08-01 12:02:11,441 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMW
2025-08-01 12:02:11,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179947.32, 'new_value': 187115.32}, {'field': 'offline_amount', 'old_value': 608376.05, 'new_value': 631110.05}, {'field': 'total_amount', 'old_value': 788323.37, 'new_value': 818225.37}, {'field': 'order_count', 'old_value': 7307, 'new_value': 7600}]
2025-08-01 12:02:11,441 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM49
2025-08-01 12:02:11,909 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM49
2025-08-01 12:02:11,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344945.3, 'new_value': 357560.4}, {'field': 'total_amount', 'old_value': 344945.3, 'new_value': 357560.4}, {'field': 'order_count', 'old_value': 447, 'new_value': 458}]
2025-08-01 12:02:11,909 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ
2025-08-01 12:02:12,370 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ
2025-08-01 12:02:12,371 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99892.0, 'new_value': 102680.81}, {'field': 'offline_amount', 'old_value': 128835.39, 'new_value': 132111.91}, {'field': 'total_amount', 'old_value': 228727.39, 'new_value': 234792.72}, {'field': 'order_count', 'old_value': 2981, 'new_value': 3062}]
2025-08-01 12:02:12,371 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM01
2025-08-01 12:02:12,871 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM01
2025-08-01 12:02:12,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 344388.0, 'new_value': 355248.0}, {'field': 'total_amount', 'old_value': 356556.0, 'new_value': 367416.0}, {'field': 'order_count', 'old_value': 29703, 'new_value': 30608}]
2025-08-01 12:02:12,872 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM11
2025-08-01 12:02:13,272 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM11
2025-08-01 12:02:13,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95582.36, 'new_value': 97272.36}, {'field': 'offline_amount', 'old_value': 59218.27, 'new_value': 61315.06}, {'field': 'total_amount', 'old_value': 154800.63, 'new_value': 158587.42}, {'field': 'order_count', 'old_value': 9872, 'new_value': 10089}]
2025-08-01 12:02:13,273 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM21
2025-08-01 12:02:13,678 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM21
2025-08-01 12:02:13,678 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177680.2, 'new_value': 187396.3}, {'field': 'offline_amount', 'old_value': 119555.26, 'new_value': 123840.9}, {'field': 'total_amount', 'old_value': 297235.46, 'new_value': 311237.2}, {'field': 'order_count', 'old_value': 762, 'new_value': 796}]
2025-08-01 12:02:13,678 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM31
2025-08-01 12:02:14,185 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM31
2025-08-01 12:02:14,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10613.55, 'new_value': 10790.55}, {'field': 'offline_amount', 'old_value': 545653.44, 'new_value': 563693.64}, {'field': 'total_amount', 'old_value': 556266.99, 'new_value': 574484.19}, {'field': 'order_count', 'old_value': 28176, 'new_value': 29102}]
2025-08-01 12:02:14,185 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM41
2025-08-01 12:02:14,599 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM41
2025-08-01 12:02:14,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303226.5, 'new_value': 310975.0}, {'field': 'total_amount', 'old_value': 372409.0, 'new_value': 380157.5}, {'field': 'order_count', 'old_value': 7531, 'new_value': 7686}]
2025-08-01 12:02:14,600 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM51
2025-08-01 12:02:14,990 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM51
2025-08-01 12:02:14,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 233638.67, 'new_value': 240731.57}, {'field': 'offline_amount', 'old_value': 238879.45, 'new_value': 247201.72}, {'field': 'total_amount', 'old_value': 472518.12, 'new_value': 487933.29}, {'field': 'order_count', 'old_value': 19643, 'new_value': 20239}]
2025-08-01 12:02:14,990 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMY8
2025-08-01 12:02:15,472 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMY8
2025-08-01 12:02:15,472 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24533.88, 'new_value': 24741.88}, {'field': 'offline_amount', 'old_value': 199668.8, 'new_value': 203648.8}, {'field': 'total_amount', 'old_value': 224202.68, 'new_value': 228390.68}, {'field': 'order_count', 'old_value': 97, 'new_value': 99}]
2025-08-01 12:02:15,472 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM71
2025-08-01 12:02:15,903 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM71
2025-08-01 12:02:15,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194865.62, 'new_value': 200974.06}, {'field': 'total_amount', 'old_value': 194865.62, 'new_value': 200974.06}, {'field': 'order_count', 'old_value': 4902, 'new_value': 5048}]
2025-08-01 12:02:15,903 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM91
2025-08-01 12:02:16,428 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM91
2025-08-01 12:02:16,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302517.91, 'new_value': 311167.58}, {'field': 'total_amount', 'old_value': 302517.91, 'new_value': 311167.58}, {'field': 'order_count', 'old_value': 2355, 'new_value': 2430}]
2025-08-01 12:02:16,428 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA1
2025-08-01 12:02:16,927 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA1
2025-08-01 12:02:16,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166398.34, 'new_value': 173201.88}, {'field': 'offline_amount', 'old_value': 70980.14, 'new_value': 74063.82}, {'field': 'total_amount', 'old_value': 237378.48, 'new_value': 247265.7}, {'field': 'order_count', 'old_value': 14575, 'new_value': 15117}]
2025-08-01 12:02:16,927 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM79
2025-08-01 12:02:17,468 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM79
2025-08-01 12:02:17,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16147.99, 'new_value': 30651.79}, {'field': 'offline_amount', 'old_value': 380597.23, 'new_value': 381103.65}, {'field': 'total_amount', 'old_value': 396745.22, 'new_value': 411755.44}, {'field': 'order_count', 'old_value': 26024, 'new_value': 26905}]
2025-08-01 12:02:17,469 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC1
2025-08-01 12:02:18,033 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC1
2025-08-01 12:02:18,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20879.3, 'new_value': 21422.9}, {'field': 'offline_amount', 'old_value': 42626.7, 'new_value': 44759.6}, {'field': 'total_amount', 'old_value': 63506.0, 'new_value': 66182.5}, {'field': 'order_count', 'old_value': 242, 'new_value': 254}]
2025-08-01 12:02:18,033 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD1
2025-08-01 12:02:18,498 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD1
2025-08-01 12:02:18,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1104671.66, 'new_value': 1139345.41}, {'field': 'total_amount', 'old_value': 1104671.66, 'new_value': 1139345.41}, {'field': 'order_count', 'old_value': 3855, 'new_value': 3974}]
2025-08-01 12:02:18,498 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM89
2025-08-01 12:02:18,986 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM89
2025-08-01 12:02:18,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 233207.0, 'new_value': 246245.0}, {'field': 'total_amount', 'old_value': 233207.0, 'new_value': 246245.0}, {'field': 'order_count', 'old_value': 7531, 'new_value': 7950}]
2025-08-01 12:02:18,986 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME1
2025-08-01 12:02:19,461 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME1
2025-08-01 12:02:19,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 909439.63, 'new_value': 936119.72}, {'field': 'total_amount', 'old_value': 909439.63, 'new_value': 936119.72}, {'field': 'order_count', 'old_value': 17964, 'new_value': 18490}]
2025-08-01 12:02:19,461 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF1
2025-08-01 12:02:19,929 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF1
2025-08-01 12:02:19,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 371461.57, 'new_value': 381906.37}, {'field': 'total_amount', 'old_value': 371461.57, 'new_value': 381906.37}, {'field': 'order_count', 'old_value': 8269, 'new_value': 8508}]
2025-08-01 12:02:19,929 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG1
2025-08-01 12:02:20,372 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG1
2025-08-01 12:02:20,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192633.39, 'new_value': 197434.96}, {'field': 'total_amount', 'old_value': 192633.39, 'new_value': 197434.96}, {'field': 'order_count', 'old_value': 8291, 'new_value': 8526}]
2025-08-01 12:02:20,372 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI1
2025-08-01 12:02:20,971 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI1
2025-08-01 12:02:20,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 307662.75, 'new_value': 319522.29}, {'field': 'total_amount', 'old_value': 333264.95, 'new_value': 345124.49}, {'field': 'order_count', 'old_value': 7769, 'new_value': 8071}]
2025-08-01 12:02:20,971 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMJ1
2025-08-01 12:02:21,423 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMJ1
2025-08-01 12:02:21,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 181676.0, 'new_value': 187176.0}, {'field': 'offline_amount', 'old_value': 128157.0, 'new_value': 132731.0}, {'field': 'total_amount', 'old_value': 309833.0, 'new_value': 319907.0}, {'field': 'order_count', 'old_value': 12569, 'new_value': 12999}]
2025-08-01 12:02:21,423 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML1
2025-08-01 12:02:21,876 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML1
2025-08-01 12:02:21,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153260.17, 'new_value': 158828.26}, {'field': 'total_amount', 'old_value': 268483.24, 'new_value': 274051.33}, {'field': 'order_count', 'old_value': 16445, 'new_value': 16667}]
2025-08-01 12:02:21,876 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM1
2025-08-01 12:02:22,296 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM1
2025-08-01 12:02:22,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 592668.0, 'new_value': 616221.0}, {'field': 'total_amount', 'old_value': 592668.0, 'new_value': 616221.0}, {'field': 'order_count', 'old_value': 738, 'new_value': 762}]
2025-08-01 12:02:22,296 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO1
2025-08-01 12:02:22,738 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO1
2025-08-01 12:02:22,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42874.0, 'new_value': 43907.0}, {'field': 'total_amount', 'old_value': 42874.0, 'new_value': 43907.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 147}]
2025-08-01 12:02:22,738 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ1
2025-08-01 12:02:23,233 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ1
2025-08-01 12:02:23,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27150.83, 'new_value': 27726.83}, {'field': 'offline_amount', 'old_value': 30967.24, 'new_value': 31467.24}, {'field': 'total_amount', 'old_value': 58118.07, 'new_value': 59194.07}, {'field': 'order_count', 'old_value': 216, 'new_value': 221}]
2025-08-01 12:02:23,234 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU1
2025-08-01 12:02:23,631 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU1
2025-08-01 12:02:23,631 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 206649.0, 'new_value': 211805.0}, {'field': 'offline_amount', 'old_value': 1414272.0, 'new_value': 1457016.0}, {'field': 'total_amount', 'old_value': 1620921.0, 'new_value': 1668821.0}, {'field': 'order_count', 'old_value': 57359, 'new_value': 58911}]
2025-08-01 12:02:23,631 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY1
2025-08-01 12:02:24,138 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY1
2025-08-01 12:02:24,138 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12350.54, 'new_value': 12871.41}, {'field': 'offline_amount', 'old_value': 38452.0, 'new_value': 43531.0}, {'field': 'total_amount', 'old_value': 50802.54, 'new_value': 56402.41}, {'field': 'order_count', 'old_value': 208, 'new_value': 217}]
2025-08-01 12:02:24,138 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ1
2025-08-01 12:02:24,650 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ1
2025-08-01 12:02:24,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22199.0, 'new_value': 22767.0}, {'field': 'total_amount', 'old_value': 22199.0, 'new_value': 22767.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 135}]
2025-08-01 12:02:24,650 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM12
2025-08-01 12:02:25,200 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM12
2025-08-01 12:02:25,200 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57530.1, 'new_value': 58609.2}, {'field': 'offline_amount', 'old_value': 70644.1, 'new_value': 72285.9}, {'field': 'total_amount', 'old_value': 128174.2, 'new_value': 130895.1}, {'field': 'order_count', 'old_value': 5437, 'new_value': 5580}]
2025-08-01 12:02:25,200 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM22
2025-08-01 12:02:25,814 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM22
2025-08-01 12:02:25,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250981.26, 'new_value': 259409.26}, {'field': 'total_amount', 'old_value': 258948.61, 'new_value': 267376.61}, {'field': 'order_count', 'old_value': 1134, 'new_value': 1169}]
2025-08-01 12:02:25,815 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM32
2025-08-01 12:02:26,219 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM32
2025-08-01 12:02:26,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136546.0, 'new_value': 140126.0}, {'field': 'total_amount', 'old_value': 136546.0, 'new_value': 140126.0}, {'field': 'order_count', 'old_value': 487, 'new_value': 504}]
2025-08-01 12:02:26,219 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM42
2025-08-01 12:02:26,666 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM42
2025-08-01 12:02:26,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49641.53, 'new_value': 51770.53}, {'field': 'total_amount', 'old_value': 64406.53, 'new_value': 66535.53}, {'field': 'order_count', 'old_value': 79, 'new_value': 81}]
2025-08-01 12:02:26,667 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM52
2025-08-01 12:02:27,130 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM52
2025-08-01 12:02:27,130 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124906.7, 'new_value': 127186.86}, {'field': 'offline_amount', 'old_value': 437347.6, 'new_value': 450458.14}, {'field': 'total_amount', 'old_value': 562254.3, 'new_value': 577645.0}, {'field': 'order_count', 'old_value': 6134, 'new_value': 6286}]
2025-08-01 12:02:27,130 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM62
2025-08-01 12:02:27,564 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM62
2025-08-01 12:02:27,564 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12223.0, 'new_value': 12649.0}, {'field': 'offline_amount', 'old_value': 6115.0, 'new_value': 6502.0}, {'field': 'total_amount', 'old_value': 18338.0, 'new_value': 19151.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 151}]
2025-08-01 12:02:27,564 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM72
2025-08-01 12:02:28,020 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM72
2025-08-01 12:02:28,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174731.7, 'new_value': 177770.2}, {'field': 'total_amount', 'old_value': 174731.7, 'new_value': 177770.2}, {'field': 'order_count', 'old_value': 718, 'new_value': 735}]
2025-08-01 12:02:28,020 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM82
2025-08-01 12:02:28,530 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM82
2025-08-01 12:02:28,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 423102.74, 'new_value': 436858.24}, {'field': 'total_amount', 'old_value': 423102.74, 'new_value': 436858.24}, {'field': 'order_count', 'old_value': 1153, 'new_value': 1188}]
2025-08-01 12:02:28,531 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM92
2025-08-01 12:02:29,034 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM92
2025-08-01 12:02:29,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190161.0, 'new_value': 196766.0}, {'field': 'total_amount', 'old_value': 190161.0, 'new_value': 196766.0}, {'field': 'order_count', 'old_value': 15963, 'new_value': 16556}]
2025-08-01 12:02:29,035 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH
2025-08-01 12:02:29,542 - INFO - 更新表单数据成功: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH
2025-08-01 12:02:29,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 198661.33, 'new_value': 202472.69}, {'field': 'offline_amount', 'old_value': 250367.35, 'new_value': 256367.35}, {'field': 'total_amount', 'old_value': 449028.68, 'new_value': 458840.04}, {'field': 'order_count', 'old_value': 1479, 'new_value': 1524}]
2025-08-01 12:02:29,542 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB2
2025-08-01 12:02:30,182 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB2
2025-08-01 12:02:30,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447913.99, 'new_value': 458687.39}, {'field': 'total_amount', 'old_value': 447961.99, 'new_value': 458735.39}, {'field': 'order_count', 'old_value': 1308, 'new_value': 1345}]
2025-08-01 12:02:30,183 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMH9
2025-08-01 12:02:30,644 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMH9
2025-08-01 12:02:30,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445975.0, 'new_value': 453975.0}, {'field': 'total_amount', 'old_value': 472435.0, 'new_value': 480435.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 105}]
2025-08-01 12:02:30,644 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC2
2025-08-01 12:02:31,121 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC2
2025-08-01 12:02:31,121 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59758.93, 'new_value': 61599.05}, {'field': 'offline_amount', 'old_value': 49194.01, 'new_value': 51194.01}, {'field': 'total_amount', 'old_value': 108952.94, 'new_value': 112793.06}, {'field': 'order_count', 'old_value': 5469, 'new_value': 5661}]
2025-08-01 12:02:31,122 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF2
2025-08-01 12:02:31,633 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF2
2025-08-01 12:02:31,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22457.37, 'new_value': 23886.04}, {'field': 'offline_amount', 'old_value': 214213.65, 'new_value': 220057.75}, {'field': 'total_amount', 'old_value': 236671.02, 'new_value': 243943.79}, {'field': 'order_count', 'old_value': 12489, 'new_value': 12860}]
2025-08-01 12:02:31,633 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMI9
2025-08-01 12:02:32,103 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMI9
2025-08-01 12:02:32,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306842.7, 'new_value': 317238.1}, {'field': 'total_amount', 'old_value': 329028.4, 'new_value': 339423.8}, {'field': 'order_count', 'old_value': 9951, 'new_value': 10275}]
2025-08-01 12:02:32,103 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMK9
2025-08-01 12:02:32,541 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMK9
2025-08-01 12:02:32,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156530.3, 'new_value': 160616.3}, {'field': 'total_amount', 'old_value': 156602.3, 'new_value': 160688.3}, {'field': 'order_count', 'old_value': 740, 'new_value': 760}]
2025-08-01 12:02:32,541 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH2
2025-08-01 12:02:32,983 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMH2
2025-08-01 12:02:32,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74778.51, 'new_value': 79476.51}, {'field': 'total_amount', 'old_value': 152982.01, 'new_value': 157680.01}, {'field': 'order_count', 'old_value': 3907, 'new_value': 4042}]
2025-08-01 12:02:32,983 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI2
2025-08-01 12:02:33,617 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI2
2025-08-01 12:02:33,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82082.0, 'new_value': 98906.0}, {'field': 'total_amount', 'old_value': 82082.0, 'new_value': 98906.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 43}]
2025-08-01 12:02:33,617 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK2
2025-08-01 12:02:34,116 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK2
2025-08-01 12:02:34,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3699.0, 'new_value': 3804.0}, {'field': 'offline_amount', 'old_value': 29566.0, 'new_value': 29991.0}, {'field': 'total_amount', 'old_value': 33265.0, 'new_value': 33795.0}, {'field': 'order_count', 'old_value': 1197, 'new_value': 1218}]
2025-08-01 12:02:34,116 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML2
2025-08-01 12:02:34,565 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML2
2025-08-01 12:02:34,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274011.4, 'new_value': 283335.4}, {'field': 'total_amount', 'old_value': 274011.4, 'new_value': 283335.4}, {'field': 'order_count', 'old_value': 1286, 'new_value': 1330}]
2025-08-01 12:02:34,565 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM2
2025-08-01 12:02:35,019 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM2
2025-08-01 12:02:35,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16706.96, 'new_value': 16978.56}, {'field': 'offline_amount', 'old_value': 228567.0, 'new_value': 229218.0}, {'field': 'total_amount', 'old_value': 245273.96, 'new_value': 246196.56}, {'field': 'order_count', 'old_value': 121, 'new_value': 123}]
2025-08-01 12:02:35,020 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN2
2025-08-01 12:02:35,589 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN2
2025-08-01 12:02:35,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157384.0, 'new_value': 172838.0}, {'field': 'total_amount', 'old_value': 157384.0, 'new_value': 172838.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 123}]
2025-08-01 12:02:35,589 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCML9
2025-08-01 12:02:36,164 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCML9
2025-08-01 12:02:36,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266793.3, 'new_value': 272508.18}, {'field': 'total_amount', 'old_value': 266793.3, 'new_value': 272508.18}, {'field': 'order_count', 'old_value': 1063, 'new_value': 1092}]
2025-08-01 12:02:36,165 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO2
2025-08-01 12:02:36,646 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO2
2025-08-01 12:02:36,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43755.35, 'new_value': 45393.95}, {'field': 'offline_amount', 'old_value': 179025.0, 'new_value': 183985.0}, {'field': 'total_amount', 'old_value': 222780.35, 'new_value': 229378.95}, {'field': 'order_count', 'old_value': 1895, 'new_value': 1952}]
2025-08-01 12:02:36,646 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ2
2025-08-01 12:02:37,062 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ2
2025-08-01 12:02:37,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40026.0, 'new_value': 42196.0}, {'field': 'total_amount', 'old_value': 40026.0, 'new_value': 42196.0}, {'field': 'order_count', 'old_value': 337, 'new_value': 353}]
2025-08-01 12:02:37,062 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMM9
2025-08-01 12:02:37,545 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMM9
2025-08-01 12:02:37,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40508.0, 'new_value': 43659.0}, {'field': 'offline_amount', 'old_value': 28444.0, 'new_value': 29306.0}, {'field': 'total_amount', 'old_value': 68952.0, 'new_value': 72965.0}, {'field': 'order_count', 'old_value': 469, 'new_value': 491}]
2025-08-01 12:02:37,545 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR2
2025-08-01 12:02:38,016 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR2
2025-08-01 12:02:38,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 773333.68, 'new_value': 778653.48}, {'field': 'total_amount', 'old_value': 789057.68, 'new_value': 794377.48}, {'field': 'order_count', 'old_value': 765, 'new_value': 793}]
2025-08-01 12:02:38,017 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS2
2025-08-01 12:02:38,517 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS2
2025-08-01 12:02:38,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 441416.3, 'new_value': 448436.3}, {'field': 'total_amount', 'old_value': 441416.3, 'new_value': 448436.3}, {'field': 'order_count', 'old_value': 446, 'new_value': 455}]
2025-08-01 12:02:38,518 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMO9
2025-08-01 12:02:38,996 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMO9
2025-08-01 12:02:38,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80779.59, 'new_value': 83839.55}, {'field': 'total_amount', 'old_value': 80779.59, 'new_value': 83839.55}, {'field': 'order_count', 'old_value': 705, 'new_value': 742}]
2025-08-01 12:02:38,996 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT2
2025-08-01 12:02:39,541 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT2
2025-08-01 12:02:39,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49305.92, 'new_value': 50603.92}, {'field': 'total_amount', 'old_value': 49305.92, 'new_value': 50603.92}, {'field': 'order_count', 'old_value': 1031, 'new_value': 1059}]
2025-08-01 12:02:39,541 - INFO - 开始更新记录 - 表单实例ID: FINST-K7G66FA1N2SWHIR9APGKP6AW4Q143TOV7WKCMP3
2025-08-01 12:02:40,001 - INFO - 更新表单数据成功: FINST-K7G66FA1N2SWHIR9APGKP6AW4Q143TOV7WKCMP3
2025-08-01 12:02:40,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1400.0, 'new_value': 4500.0}, {'field': 'offline_amount', 'old_value': 123795.0, 'new_value': 123851.0}, {'field': 'total_amount', 'old_value': 125195.0, 'new_value': 128351.0}, {'field': 'order_count', 'old_value': 649, 'new_value': 685}]
2025-08-01 12:02:40,001 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU2
2025-08-01 12:02:40,494 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU2
2025-08-01 12:02:40,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140426.5, 'new_value': 144398.66}, {'field': 'total_amount', 'old_value': 140426.5, 'new_value': 144398.66}, {'field': 'order_count', 'old_value': 9523, 'new_value': 9760}]
2025-08-01 12:02:40,495 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY2
2025-08-01 12:02:41,010 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY2
2025-08-01 12:02:41,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80281.0, 'new_value': 104279.0}, {'field': 'total_amount', 'old_value': 80281.0, 'new_value': 104279.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 28}]
2025-08-01 12:02:41,010 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMP9
2025-08-01 12:02:41,500 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMP9
2025-08-01 12:02:41,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47686.1, 'new_value': 48411.6}, {'field': 'total_amount', 'old_value': 47686.1, 'new_value': 48411.6}, {'field': 'order_count', 'old_value': 964, 'new_value': 988}]
2025-08-01 12:02:41,501 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM03
2025-08-01 12:02:41,950 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM03
2025-08-01 12:02:41,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 273338.37, 'new_value': 283516.37}, {'field': 'total_amount', 'old_value': 408402.37, 'new_value': 418580.37}, {'field': 'order_count', 'old_value': 683, 'new_value': 703}]
2025-08-01 12:02:41,950 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM23
2025-08-01 12:02:42,455 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM23
2025-08-01 12:02:42,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 135161.0, 'new_value': 139570.0}, {'field': 'offline_amount', 'old_value': 242209.0, 'new_value': 254843.0}, {'field': 'total_amount', 'old_value': 377370.0, 'new_value': 394413.0}, {'field': 'order_count', 'old_value': 6961, 'new_value': 6999}]
2025-08-01 12:02:42,455 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM33
2025-08-01 12:02:43,059 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM33
2025-08-01 12:02:43,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9070.0, 'new_value': 9229.0}, {'field': 'offline_amount', 'old_value': 85296.5, 'new_value': 87524.0}, {'field': 'total_amount', 'old_value': 94366.5, 'new_value': 96753.0}, {'field': 'order_count', 'old_value': 769, 'new_value': 786}]
2025-08-01 12:02:43,059 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM43
2025-08-01 12:02:43,521 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM43
2025-08-01 12:02:43,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223331.0, 'new_value': 232715.0}, {'field': 'total_amount', 'old_value': 223331.0, 'new_value': 232715.0}, {'field': 'order_count', 'old_value': 22447, 'new_value': 23422}]
2025-08-01 12:02:43,521 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM53
2025-08-01 12:02:44,026 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM53
2025-08-01 12:02:44,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1323397.75, 'new_value': 1368713.35}, {'field': 'total_amount', 'old_value': 1363960.75, 'new_value': 1409276.35}, {'field': 'order_count', 'old_value': 4626, 'new_value': 4774}]
2025-08-01 12:02:44,027 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM63
2025-08-01 12:02:44,502 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM63
2025-08-01 12:02:44,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70360.0, 'new_value': 72017.0}, {'field': 'offline_amount', 'old_value': 73795.18, 'new_value': 76120.12}, {'field': 'total_amount', 'old_value': 144155.18, 'new_value': 148137.12}, {'field': 'order_count', 'old_value': 7514, 'new_value': 7748}]
2025-08-01 12:02:44,503 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM83
2025-08-01 12:02:44,936 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM83
2025-08-01 12:02:44,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196604.0, 'new_value': 200963.0}, {'field': 'total_amount', 'old_value': 196604.0, 'new_value': 200963.0}, {'field': 'order_count', 'old_value': 3661, 'new_value': 3773}]
2025-08-01 12:02:44,937 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM93
2025-08-01 12:02:45,440 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM93
2025-08-01 12:02:45,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78527.0, 'new_value': 79517.0}, {'field': 'total_amount', 'old_value': 78527.0, 'new_value': 79517.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-08-01 12:02:45,441 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA3
2025-08-01 12:02:45,927 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA3
2025-08-01 12:02:45,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156648.0, 'new_value': 163075.0}, {'field': 'total_amount', 'old_value': 156648.0, 'new_value': 163075.0}, {'field': 'order_count', 'old_value': 4445, 'new_value': 4658}]
2025-08-01 12:02:45,927 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB3
2025-08-01 12:02:46,386 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB3
2025-08-01 12:02:46,387 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163461.02, 'new_value': 168614.45}, {'field': 'offline_amount', 'old_value': 53196.37, 'new_value': 54448.0}, {'field': 'total_amount', 'old_value': 216657.39, 'new_value': 223062.45}, {'field': 'order_count', 'old_value': 12751, 'new_value': 13107}]
2025-08-01 12:02:46,387 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC3
2025-08-01 12:02:46,880 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC3
2025-08-01 12:02:46,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392499.0, 'new_value': 398187.0}, {'field': 'total_amount', 'old_value': 399174.0, 'new_value': 404862.0}, {'field': 'order_count', 'old_value': 333, 'new_value': 338}]
2025-08-01 12:02:46,881 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD3
2025-08-01 12:02:47,379 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD3
2025-08-01 12:02:47,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58028.0, 'new_value': 65008.0}, {'field': 'total_amount', 'old_value': 58028.0, 'new_value': 65008.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-08-01 12:02:47,379 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMR9
2025-08-01 12:02:47,879 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMR9
2025-08-01 12:02:47,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141145.0, 'new_value': 145304.0}, {'field': 'total_amount', 'old_value': 141145.0, 'new_value': 145304.0}, {'field': 'order_count', 'old_value': 10270, 'new_value': 10597}]
2025-08-01 12:02:47,880 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME3
2025-08-01 12:02:48,337 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME3
2025-08-01 12:02:48,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208685.56, 'new_value': 215738.87}, {'field': 'total_amount', 'old_value': 208685.56, 'new_value': 215738.87}, {'field': 'order_count', 'old_value': 85, 'new_value': 89}]
2025-08-01 12:02:48,338 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO
2025-08-01 12:02:48,841 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO
2025-08-01 12:02:48,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372855.0, 'new_value': 387609.0}, {'field': 'total_amount', 'old_value': 372855.0, 'new_value': 387609.0}, {'field': 'order_count', 'old_value': 8714, 'new_value': 9020}]
2025-08-01 12:02:48,842 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ
2025-08-01 12:02:49,340 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ
2025-08-01 12:02:49,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 984595.0, 'new_value': 1013175.0}, {'field': 'total_amount', 'old_value': 984595.0, 'new_value': 1013175.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 84}]
2025-08-01 12:02:49,340 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS
2025-08-01 12:02:49,784 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS
2025-08-01 12:02:49,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95229.0, 'new_value': 100039.0}, {'field': 'total_amount', 'old_value': 95229.0, 'new_value': 100039.0}, {'field': 'order_count', 'old_value': 354, 'new_value': 371}]
2025-08-01 12:02:49,784 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT
2025-08-01 12:02:50,281 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT
2025-08-01 12:02:50,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52653.72, 'new_value': 54960.24}, {'field': 'offline_amount', 'old_value': 54620.5, 'new_value': 56661.47}, {'field': 'total_amount', 'old_value': 107274.22, 'new_value': 111621.71}, {'field': 'order_count', 'old_value': 5781, 'new_value': 6011}]
2025-08-01 12:02:50,281 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV
2025-08-01 12:02:50,720 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV
2025-08-01 12:02:50,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49282.74, 'new_value': 49902.0}, {'field': 'offline_amount', 'old_value': 32779.0, 'new_value': 34950.0}, {'field': 'total_amount', 'old_value': 82061.74, 'new_value': 84852.0}, {'field': 'order_count', 'old_value': 1046, 'new_value': 1081}]
2025-08-01 12:02:50,721 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM0A
2025-08-01 12:02:51,200 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM0A
2025-08-01 12:02:51,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163332.39, 'new_value': 171312.02}, {'field': 'total_amount', 'old_value': 241600.02, 'new_value': 249579.65}, {'field': 'order_count', 'old_value': 16273, 'new_value': 16791}]
2025-08-01 12:02:51,201 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW
2025-08-01 12:02:51,655 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW
2025-08-01 12:02:51,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93629.74, 'new_value': 97769.66}, {'field': 'total_amount', 'old_value': 135044.07, 'new_value': 139183.99}, {'field': 'order_count', 'old_value': 8800, 'new_value': 9074}]
2025-08-01 12:02:51,655 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX
2025-08-01 12:02:52,180 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX
2025-08-01 12:02:52,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1302695.86, 'new_value': 1343919.13}, {'field': 'total_amount', 'old_value': 1302695.86, 'new_value': 1343919.13}, {'field': 'order_count', 'old_value': 3892, 'new_value': 4016}]
2025-08-01 12:02:52,181 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY
2025-08-01 12:02:52,663 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY
2025-08-01 12:02:52,663 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 135, 'new_value': 2011}]
2025-08-01 12:02:52,663 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ
2025-08-01 12:02:53,092 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ
2025-08-01 12:02:53,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400800.0, 'new_value': 412872.0}, {'field': 'total_amount', 'old_value': 421525.0, 'new_value': 433597.0}, {'field': 'order_count', 'old_value': 9331, 'new_value': 9631}]
2025-08-01 12:02:53,092 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM01
2025-08-01 12:02:53,735 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM01
2025-08-01 12:02:53,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12707.2, 'new_value': 13207.2}, {'field': 'total_amount', 'old_value': 12707.2, 'new_value': 13207.2}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-08-01 12:02:53,735 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM21
2025-08-01 12:02:54,155 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM21
2025-08-01 12:02:54,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118978.75, 'new_value': 121639.11}, {'field': 'total_amount', 'old_value': 118978.75, 'new_value': 121639.11}, {'field': 'order_count', 'old_value': 943, 'new_value': 972}]
2025-08-01 12:02:54,156 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM41
2025-08-01 12:02:54,683 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM41
2025-08-01 12:02:54,683 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15463.25, 'new_value': 15996.05}, {'field': 'offline_amount', 'old_value': 38404.81, 'new_value': 39226.41}, {'field': 'total_amount', 'old_value': 53868.06, 'new_value': 55222.46}, {'field': 'order_count', 'old_value': 3148, 'new_value': 3227}]
2025-08-01 12:02:54,683 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM51
2025-08-01 12:02:55,180 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM51
2025-08-01 12:02:55,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 363000.0, 'new_value': 375400.0}, {'field': 'total_amount', 'old_value': 363000.0, 'new_value': 375400.0}, {'field': 'order_count', 'old_value': 841, 'new_value': 871}]
2025-08-01 12:02:55,180 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM91
2025-08-01 12:02:55,743 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM91
2025-08-01 12:02:55,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31075.76, 'new_value': 32200.76}, {'field': 'offline_amount', 'old_value': 19955.66, 'new_value': 20503.66}, {'field': 'total_amount', 'old_value': 51031.42, 'new_value': 52704.42}, {'field': 'order_count', 'old_value': 1788, 'new_value': 1840}]
2025-08-01 12:02:55,744 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA1
2025-08-01 12:02:56,198 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA1
2025-08-01 12:02:56,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 575320.3, 'new_value': 611040.15}, {'field': 'total_amount', 'old_value': 575320.3, 'new_value': 611040.15}, {'field': 'order_count', 'old_value': 2330, 'new_value': 2729}]
2025-08-01 12:02:56,198 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB1
2025-08-01 12:02:56,656 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB1
2025-08-01 12:02:56,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260057.14, 'new_value': 268215.02}, {'field': 'total_amount', 'old_value': 260057.14, 'new_value': 268215.02}, {'field': 'order_count', 'old_value': 20240, 'new_value': 20921}]
2025-08-01 12:02:56,656 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC1
2025-08-01 12:02:57,146 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC1
2025-08-01 12:02:57,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1636899.0, 'new_value': 1685079.0}, {'field': 'total_amount', 'old_value': 1636899.0, 'new_value': 1685079.0}, {'field': 'order_count', 'old_value': 8684, 'new_value': 8976}]
2025-08-01 12:02:57,146 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD1
2025-08-01 12:02:57,577 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD1
2025-08-01 12:02:57,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 754730.58, 'new_value': 778449.68}, {'field': 'total_amount', 'old_value': 754730.58, 'new_value': 778449.68}, {'field': 'order_count', 'old_value': 2077, 'new_value': 2133}]
2025-08-01 12:02:57,578 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF1
2025-08-01 12:02:58,026 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF1
2025-08-01 12:02:58,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4064949.14, 'new_value': 4191769.66}, {'field': 'total_amount', 'old_value': 4064949.14, 'new_value': 4191769.66}, {'field': 'order_count', 'old_value': 8351, 'new_value': 8650}]
2025-08-01 12:02:58,026 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG1
2025-08-01 12:02:58,495 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG1
2025-08-01 12:02:58,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27281.64, 'new_value': 41124.24}, {'field': 'offline_amount', 'old_value': 562382.92, 'new_value': 562765.58}, {'field': 'total_amount', 'old_value': 589664.56, 'new_value': 603889.82}, {'field': 'order_count', 'old_value': 2012, 'new_value': 2072}]
2025-08-01 12:02:58,495 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH1
2025-08-01 12:02:58,930 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH1
2025-08-01 12:02:58,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301956.42, 'new_value': 310896.88}, {'field': 'total_amount', 'old_value': 301956.42, 'new_value': 310896.88}, {'field': 'order_count', 'old_value': 33223, 'new_value': 34286}]
2025-08-01 12:02:58,930 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI1
2025-08-01 12:02:59,420 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI1
2025-08-01 12:02:59,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12703686.74, 'new_value': 13050028.74}, {'field': 'total_amount', 'old_value': 12703686.74, 'new_value': 13050028.74}, {'field': 'order_count', 'old_value': 55728, 'new_value': 57215}]
2025-08-01 12:02:59,420 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK1
2025-08-01 12:02:59,879 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK1
2025-08-01 12:02:59,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106540.0, 'new_value': 108923.0}, {'field': 'total_amount', 'old_value': 106540.0, 'new_value': 108923.0}, {'field': 'order_count', 'old_value': 1505, 'new_value': 1545}]
2025-08-01 12:02:59,879 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML1
2025-08-01 12:03:00,318 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML1
2025-08-01 12:03:00,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340519.83, 'new_value': 354240.83}, {'field': 'total_amount', 'old_value': 340519.83, 'new_value': 354240.83}, {'field': 'order_count', 'old_value': 31458, 'new_value': 31732}]
2025-08-01 12:03:00,318 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM1
2025-08-01 12:03:00,726 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM1
2025-08-01 12:03:00,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91965.0, 'new_value': 96470.0}, {'field': 'total_amount', 'old_value': 91965.0, 'new_value': 96470.0}, {'field': 'order_count', 'old_value': 347, 'new_value': 364}]
2025-08-01 12:03:00,727 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN1
2025-08-01 12:03:01,239 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN1
2025-08-01 12:03:01,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5751539.99, 'new_value': 5925546.99}, {'field': 'total_amount', 'old_value': 5751539.99, 'new_value': 5925546.99}, {'field': 'order_count', 'old_value': 125122, 'new_value': 129105}]
2025-08-01 12:03:01,239 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO1
2025-08-01 12:03:01,724 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO1
2025-08-01 12:03:01,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376968.0, 'new_value': 390477.0}, {'field': 'total_amount', 'old_value': 376968.0, 'new_value': 390477.0}, {'field': 'order_count', 'old_value': 4933, 'new_value': 5093}]
2025-08-01 12:03:01,724 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ1
2025-08-01 12:03:02,214 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ1
2025-08-01 12:03:02,215 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48958.55, 'new_value': 50528.55}, {'field': 'offline_amount', 'old_value': 1264284.83, 'new_value': 1310097.54}, {'field': 'total_amount', 'old_value': 1313243.38, 'new_value': 1360626.09}, {'field': 'order_count', 'old_value': 6423, 'new_value': 6638}]
2025-08-01 12:03:02,215 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR1
2025-08-01 12:03:02,723 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR1
2025-08-01 12:03:02,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1066614.45, 'new_value': 1095541.77}, {'field': 'total_amount', 'old_value': 1066614.45, 'new_value': 1095541.77}, {'field': 'order_count', 'old_value': 9556, 'new_value': 9859}]
2025-08-01 12:03:02,723 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT1
2025-08-01 12:03:03,199 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT1
2025-08-01 12:03:03,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17608283.0, 'new_value': 18237150.25}, {'field': 'total_amount', 'old_value': 17608283.0, 'new_value': 18237150.25}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-08-01 12:03:03,200 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU1
2025-08-01 12:03:03,666 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU1
2025-08-01 12:03:03,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176860.0, 'new_value': 181102.0}, {'field': 'total_amount', 'old_value': 176860.0, 'new_value': 181102.0}, {'field': 'order_count', 'old_value': 766, 'new_value': 793}]
2025-08-01 12:03:03,666 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV1
2025-08-01 12:03:04,081 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV1
2025-08-01 12:03:04,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181321.0, 'new_value': 187573.7}, {'field': 'total_amount', 'old_value': 181321.0, 'new_value': 187573.7}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-08-01 12:03:04,081 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW1
2025-08-01 12:03:04,567 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW1
2025-08-01 12:03:04,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61348.0, 'new_value': 63748.0}, {'field': 'total_amount', 'old_value': 61348.0, 'new_value': 63748.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 46}]
2025-08-01 12:03:04,567 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY1
2025-08-01 12:03:05,065 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY1
2025-08-01 12:03:05,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 339791.13, 'new_value': 349959.13}, {'field': 'offline_amount', 'old_value': 152540.72, 'new_value': 157101.54}, {'field': 'total_amount', 'old_value': 492331.85, 'new_value': 507060.67}, {'field': 'order_count', 'old_value': 1629, 'new_value': 1680}]
2025-08-01 12:03:05,065 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ1
2025-08-01 12:03:05,655 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ1
2025-08-01 12:03:05,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157201.46, 'new_value': 162173.46}, {'field': 'total_amount', 'old_value': 219722.78, 'new_value': 224694.78}, {'field': 'order_count', 'old_value': 5873, 'new_value': 6015}]
2025-08-01 12:03:05,655 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM12
2025-08-01 12:03:06,103 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM12
2025-08-01 12:03:06,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157200.92, 'new_value': 160494.44}, {'field': 'total_amount', 'old_value': 157200.92, 'new_value': 160494.44}, {'field': 'order_count', 'old_value': 4284, 'new_value': 4430}]
2025-08-01 12:03:06,103 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM22
2025-08-01 12:03:06,690 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM22
2025-08-01 12:03:06,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39154.1, 'new_value': 41614.1}, {'field': 'total_amount', 'old_value': 43076.96, 'new_value': 45536.96}, {'field': 'order_count', 'old_value': 332, 'new_value': 335}]
2025-08-01 12:03:06,690 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM52
2025-08-01 12:03:07,184 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM52
2025-08-01 12:03:07,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277638.87, 'new_value': 287745.87}, {'field': 'total_amount', 'old_value': 277646.87, 'new_value': 287753.87}, {'field': 'order_count', 'old_value': 9501, 'new_value': 9837}]
2025-08-01 12:03:07,184 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM82
2025-08-01 12:03:07,694 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM82
2025-08-01 12:03:07,694 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15775.0, 'new_value': 16005.0}, {'field': 'total_amount', 'old_value': 75375.0, 'new_value': 75605.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 135}]
2025-08-01 12:03:07,694 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF2
2025-08-01 12:03:08,167 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF2
2025-08-01 12:03:08,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120669.0, 'new_value': 124830.0}, {'field': 'total_amount', 'old_value': 120669.0, 'new_value': 124830.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-08-01 12:03:08,169 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMS9
2025-08-01 12:03:08,622 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMS9
2025-08-01 12:03:08,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 299615.0, 'new_value': 306204.0}, {'field': 'total_amount', 'old_value': 299615.0, 'new_value': 306204.0}, {'field': 'order_count', 'old_value': 419, 'new_value': 428}]
2025-08-01 12:03:08,623 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9
2025-08-01 12:03:09,067 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9
2025-08-01 12:03:09,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138554.81, 'new_value': 142856.33}, {'field': 'offline_amount', 'old_value': 1116935.43, 'new_value': 1166446.6}, {'field': 'total_amount', 'old_value': 1255490.24, 'new_value': 1309302.93}, {'field': 'order_count', 'old_value': 6415, 'new_value': 6668}]
2025-08-01 12:03:09,067 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9
2025-08-01 12:03:09,648 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9
2025-08-01 12:03:09,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356275.64, 'new_value': 367569.88}, {'field': 'total_amount', 'old_value': 356275.64, 'new_value': 367569.88}, {'field': 'order_count', 'old_value': 9945, 'new_value': 10367}]
2025-08-01 12:03:09,648 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9
2025-08-01 12:03:10,103 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9
2025-08-01 12:03:10,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238922.93, 'new_value': 259360.93}, {'field': 'total_amount', 'old_value': 454898.56, 'new_value': 475336.56}, {'field': 'order_count', 'old_value': 1018, 'new_value': 1048}]
2025-08-01 12:03:10,104 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMEH
2025-08-01 12:03:10,633 - INFO - 更新表单数据成功: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMEH
2025-08-01 12:03:10,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65824.68, 'new_value': 67345.19}, {'field': 'offline_amount', 'old_value': 530316.95, 'new_value': 543682.13}, {'field': 'total_amount', 'old_value': 596141.63, 'new_value': 611027.32}, {'field': 'order_count', 'old_value': 5000, 'new_value': 5131}]
2025-08-01 12:03:10,633 - INFO - 开始更新记录 - 表单实例ID: FINST-FTF66M712A0X2EQWBFY1P4VFQCAQ2PPI3VWCM34
2025-08-01 12:03:11,077 - INFO - 更新表单数据成功: FINST-FTF66M712A0X2EQWBFY1P4VFQCAQ2PPI3VWCM34
2025-08-01 12:03:11,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4593.17, 'new_value': 4750.19}, {'field': 'total_amount', 'old_value': 4593.17, 'new_value': 4750.19}, {'field': 'order_count', 'old_value': 757, 'new_value': 783}]
2025-08-01 12:03:11,077 - INFO - 开始更新记录 - 表单实例ID: FINST-NWE664C1X87XSRR86FGCZAWFXQET2IUGFW9DMVL
2025-08-01 12:03:11,563 - INFO - 更新表单数据成功: FINST-NWE664C1X87XSRR86FGCZAWFXQET2IUGFW9DMVL
2025-08-01 12:03:11,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74729.89, 'new_value': 79859.78}, {'field': 'total_amount', 'old_value': 74729.89, 'new_value': 79859.78}, {'field': 'order_count', 'old_value': 2314, 'new_value': 2477}]
2025-08-01 12:03:11,563 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LPHXFA32FODXH88S46IW384VWFPDMMC
2025-08-01 12:03:12,040 - INFO - 更新表单数据成功: FINST-K7666JC1LPHXFA32FODXH88S46IW384VWFPDMMC
2025-08-01 12:03:12,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9684.0, 'new_value': 14700.0}, {'field': 'total_amount', 'old_value': 9684.0, 'new_value': 14700.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-08-01 12:03:12,040 - INFO - 日期 2025-07 处理完成 - 更新: 307 条，插入: 0 条，错误: 0 条
2025-08-01 12:03:12,041 - INFO - 开始处理日期: 2025-08
2025-08-01 12:03:12,041 - INFO - Request Parameters - Page 1:
2025-08-01 12:03:12,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 12:03:12,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 12:03:12,290 - INFO - Response - Page 1:
2025-08-01 12:03:12,490 - INFO - 查询完成，共获取到 0 条记录
2025-08-01 12:03:12,490 - INFO - 获取到 0 条表单数据
2025-08-01 12:03:12,492 - INFO - 当前日期 2025-08 有 3 条MySQL数据需要处理
2025-08-01 12:03:12,492 - INFO - 开始批量插入 3 条新记录
2025-08-01 12:03:12,661 - INFO - 批量插入响应状态码: 200
2025-08-01 12:03:12,662 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 04:03:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '39E6C752-3976-7D34-B618-8ED8FA7F4CC7', 'x-acs-trace-id': '37744195ba13881e0855e61ba45cf452', 'etag': '1dCiUxLTbY69/1AqHo9v7mA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 12:03:12,662 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMGT', 'FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMHT', 'FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMIT']}
2025-08-01 12:03:12,662 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-01 12:03:12,662 - INFO - 成功插入的数据ID: ['FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMGT', 'FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMHT', 'FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMIT']
2025-08-01 12:03:15,663 - INFO - 批量插入完成，共 3 条记录
2025-08-01 12:03:15,663 - INFO - 日期 2025-08 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-01 12:03:15,663 - INFO - 数据同步完成！更新: 307 条，插入: 3 条，错误: 0 条
2025-08-01 12:03:15,664 - INFO - =================同步完成====================
2025-08-01 15:00:02,610 - INFO - =================使用默认全量同步=============
2025-08-01 15:00:04,786 - INFO - MySQL查询成功，共获取 4617 条记录
2025-08-01 15:00:04,787 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-01 15:00:04,823 - INFO - 开始处理日期: 2025-01
2025-08-01 15:00:04,826 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:04,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:04,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:05,925 - INFO - Response - Page 1:
2025-08-01 15:00:06,126 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:06,126 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:06,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:06,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:07,255 - INFO - Response - Page 2:
2025-08-01 15:00:07,455 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:07,455 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:07,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:07,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:07,994 - INFO - Response - Page 3:
2025-08-01 15:00:08,195 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:08,195 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:08,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:08,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:08,769 - INFO - Response - Page 4:
2025-08-01 15:00:08,969 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:08,969 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:08,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:08,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:09,473 - INFO - Response - Page 5:
2025-08-01 15:00:09,673 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:09,673 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:09,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:09,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:10,231 - INFO - Response - Page 6:
2025-08-01 15:00:10,431 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:10,431 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:10,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:10,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:10,916 - INFO - Response - Page 7:
2025-08-01 15:00:11,116 - INFO - 第 7 页获取到 82 条记录
2025-08-01 15:00:11,116 - INFO - 查询完成，共获取到 682 条记录
2025-08-01 15:00:11,116 - INFO - 获取到 682 条表单数据
2025-08-01 15:00:11,129 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-01 15:00:11,141 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:11,141 - INFO - 开始处理日期: 2025-02
2025-08-01 15:00:11,141 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:11,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:11,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:11,742 - INFO - Response - Page 1:
2025-08-01 15:00:11,944 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:11,944 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:11,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:11,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:12,583 - INFO - Response - Page 2:
2025-08-01 15:00:12,783 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:12,783 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:12,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:12,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:13,328 - INFO - Response - Page 3:
2025-08-01 15:00:13,528 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:13,528 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:13,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:13,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:14,023 - INFO - Response - Page 4:
2025-08-01 15:00:14,223 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:14,223 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:14,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:14,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:14,755 - INFO - Response - Page 5:
2025-08-01 15:00:14,955 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:14,955 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:14,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:14,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:15,471 - INFO - Response - Page 6:
2025-08-01 15:00:15,671 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:15,671 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:15,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:15,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:16,162 - INFO - Response - Page 7:
2025-08-01 15:00:16,362 - INFO - 第 7 页获取到 70 条记录
2025-08-01 15:00:16,363 - INFO - 查询完成，共获取到 670 条记录
2025-08-01 15:00:16,363 - INFO - 获取到 670 条表单数据
2025-08-01 15:00:16,377 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-01 15:00:16,390 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:16,390 - INFO - 开始处理日期: 2025-03
2025-08-01 15:00:16,390 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:16,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:16,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:16,970 - INFO - Response - Page 1:
2025-08-01 15:00:17,170 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:17,170 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:17,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:17,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:17,722 - INFO - Response - Page 2:
2025-08-01 15:00:17,922 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:17,922 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:17,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:17,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:18,498 - INFO - Response - Page 3:
2025-08-01 15:00:18,699 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:18,699 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:18,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:18,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:19,210 - INFO - Response - Page 4:
2025-08-01 15:00:19,411 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:19,411 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:19,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:19,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:19,921 - INFO - Response - Page 5:
2025-08-01 15:00:20,122 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:20,122 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:20,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:20,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:20,648 - INFO - Response - Page 6:
2025-08-01 15:00:20,849 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:20,849 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:20,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:20,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:21,335 - INFO - Response - Page 7:
2025-08-01 15:00:21,535 - INFO - 第 7 页获取到 61 条记录
2025-08-01 15:00:21,535 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 15:00:21,535 - INFO - 获取到 661 条表单数据
2025-08-01 15:00:21,548 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-01 15:00:21,560 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:21,560 - INFO - 开始处理日期: 2025-04
2025-08-01 15:00:21,560 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:21,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:21,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:22,143 - INFO - Response - Page 1:
2025-08-01 15:00:22,344 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:22,344 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:22,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:22,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:22,987 - INFO - Response - Page 2:
2025-08-01 15:00:23,187 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:23,187 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:23,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:23,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:23,862 - INFO - Response - Page 3:
2025-08-01 15:00:24,063 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:24,063 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:24,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:24,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:24,631 - INFO - Response - Page 4:
2025-08-01 15:00:24,832 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:24,832 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:24,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:24,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:25,316 - INFO - Response - Page 5:
2025-08-01 15:00:25,517 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:25,517 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:25,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:25,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:26,046 - INFO - Response - Page 6:
2025-08-01 15:00:26,246 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:26,246 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:26,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:26,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:26,722 - INFO - Response - Page 7:
2025-08-01 15:00:26,922 - INFO - 第 7 页获取到 56 条记录
2025-08-01 15:00:26,922 - INFO - 查询完成，共获取到 656 条记录
2025-08-01 15:00:26,922 - INFO - 获取到 656 条表单数据
2025-08-01 15:00:26,934 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-01 15:00:26,945 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:26,945 - INFO - 开始处理日期: 2025-05
2025-08-01 15:00:26,945 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:26,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:26,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:27,534 - INFO - Response - Page 1:
2025-08-01 15:00:27,734 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:27,734 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:27,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:27,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:28,271 - INFO - Response - Page 2:
2025-08-01 15:00:28,472 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:28,472 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:28,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:28,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:28,970 - INFO - Response - Page 3:
2025-08-01 15:00:29,170 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:29,170 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:29,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:29,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:29,903 - INFO - Response - Page 4:
2025-08-01 15:00:30,103 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:30,103 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:30,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:30,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:30,710 - INFO - Response - Page 5:
2025-08-01 15:00:30,910 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:30,910 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:30,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:30,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:31,531 - INFO - Response - Page 6:
2025-08-01 15:00:31,731 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:31,731 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:31,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:31,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:32,218 - INFO - Response - Page 7:
2025-08-01 15:00:32,418 - INFO - 第 7 页获取到 65 条记录
2025-08-01 15:00:32,418 - INFO - 查询完成，共获取到 665 条记录
2025-08-01 15:00:32,418 - INFO - 获取到 665 条表单数据
2025-08-01 15:00:32,430 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-01 15:00:32,441 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:32,441 - INFO - 开始处理日期: 2025-06
2025-08-01 15:00:32,442 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:32,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:32,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:33,062 - INFO - Response - Page 1:
2025-08-01 15:00:33,263 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:33,263 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:33,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:33,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:33,861 - INFO - Response - Page 2:
2025-08-01 15:00:34,061 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:34,061 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:34,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:34,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:34,637 - INFO - Response - Page 3:
2025-08-01 15:00:34,837 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:34,837 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:34,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:34,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:35,344 - INFO - Response - Page 4:
2025-08-01 15:00:35,544 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:35,544 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:35,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:35,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:36,072 - INFO - Response - Page 5:
2025-08-01 15:00:36,272 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:36,272 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:36,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:36,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:36,757 - INFO - Response - Page 6:
2025-08-01 15:00:36,957 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:36,957 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:36,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:36,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:37,495 - INFO - Response - Page 7:
2025-08-01 15:00:37,695 - INFO - 第 7 页获取到 61 条记录
2025-08-01 15:00:37,695 - INFO - 查询完成，共获取到 661 条记录
2025-08-01 15:00:37,695 - INFO - 获取到 661 条表单数据
2025-08-01 15:00:37,707 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-01 15:00:37,719 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:37,719 - INFO - 开始处理日期: 2025-07
2025-08-01 15:00:37,719 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:37,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:37,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:38,253 - INFO - Response - Page 1:
2025-08-01 15:00:38,453 - INFO - 第 1 页获取到 100 条记录
2025-08-01 15:00:38,453 - INFO - Request Parameters - Page 2:
2025-08-01 15:00:38,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:38,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:39,016 - INFO - Response - Page 2:
2025-08-01 15:00:39,216 - INFO - 第 2 页获取到 100 条记录
2025-08-01 15:00:39,216 - INFO - Request Parameters - Page 3:
2025-08-01 15:00:39,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:39,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:39,743 - INFO - Response - Page 3:
2025-08-01 15:00:39,943 - INFO - 第 3 页获取到 100 条记录
2025-08-01 15:00:39,943 - INFO - Request Parameters - Page 4:
2025-08-01 15:00:39,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:39,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:40,460 - INFO - Response - Page 4:
2025-08-01 15:00:40,660 - INFO - 第 4 页获取到 100 条记录
2025-08-01 15:00:40,660 - INFO - Request Parameters - Page 5:
2025-08-01 15:00:40,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:40,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:41,219 - INFO - Response - Page 5:
2025-08-01 15:00:41,419 - INFO - 第 5 页获取到 100 条记录
2025-08-01 15:00:41,419 - INFO - Request Parameters - Page 6:
2025-08-01 15:00:41,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:41,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:41,987 - INFO - Response - Page 6:
2025-08-01 15:00:42,188 - INFO - 第 6 页获取到 100 条记录
2025-08-01 15:00:42,188 - INFO - Request Parameters - Page 7:
2025-08-01 15:00:42,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:42,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:42,553 - INFO - Response - Page 7:
2025-08-01 15:00:42,754 - INFO - 第 7 页获取到 19 条记录
2025-08-01 15:00:42,754 - INFO - 查询完成，共获取到 619 条记录
2025-08-01 15:00:42,754 - INFO - 获取到 619 条表单数据
2025-08-01 15:00:42,766 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-01 15:00:42,774 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH
2025-08-01 15:00:43,311 - INFO - 更新表单数据成功: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH
2025-08-01 15:00:43,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202472.69, 'new_value': 195241.45}, {'field': 'offline_amount', 'old_value': 256367.35, 'new_value': 264812.1}, {'field': 'total_amount', 'old_value': 458840.04, 'new_value': 460053.55}]
2025-08-01 15:00:43,316 - INFO - 日期 2025-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:43,316 - INFO - 开始处理日期: 2025-08
2025-08-01 15:00:43,316 - INFO - Request Parameters - Page 1:
2025-08-01 15:00:43,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 15:00:43,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 15:00:43,556 - INFO - Response - Page 1:
2025-08-01 15:00:43,757 - INFO - 第 1 页获取到 3 条记录
2025-08-01 15:00:43,757 - INFO - 查询完成，共获取到 3 条记录
2025-08-01 15:00:43,757 - INFO - 获取到 3 条表单数据
2025-08-01 15:00:43,759 - INFO - 当前日期 2025-08 有 3 条MySQL数据需要处理
2025-08-01 15:00:43,760 - INFO - 日期 2025-08 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:43,760 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 15:00:43,760 - INFO - =================同步完成====================
2025-08-01 18:00:03,245 - ERROR - 程序执行出错: (2013, 'Lost connection to MySQL server during query')
