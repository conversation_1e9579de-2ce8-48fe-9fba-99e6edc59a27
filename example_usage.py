# -*- coding: utf-8 -*-
"""
MySQL与宜搭数据同步框架使用示例
"""
import os
import sys
import json
from mysql2yida_sync_framework import SyncConfig, MySQL2YidaSyncClient

def create_sales_config():
    """创建销售数据同步配置"""
    print("创建销售数据同步配置...")
    
    config = SyncConfig()
    
    # 可以根据需要修改配置
    config.db_config['database'] = 'yx_business'
    config.yida_config['FORM_UUID'] = 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5'
    
    # 保存配置文件
    config.save_to_file('sales_sync_config.json')
    print("销售数据同步配置已保存到: sales_sync_config.json")

def create_device_config():
    """创建设备数据同步配置"""
    print("创建设备数据同步配置...")
    
    config = SyncConfig()
    
    # 修改为设备数据库配置
    config.db_config = {
        'host': 'localhost',
        'port': 43306,
        'user': 'root',
        'password': 'Hxp@1987!@#',
        'database': 'mydatabase',
        'charset': 'utf8mb4',
        'cursorclass': 'pymysql.cursors.DictCursor'
    }
    
    # 修改为设备表单配置
    config.yida_config['FORM_UUID'] = 'FORM-860B31FAA4B54C4EB515B4F38B94968BQZMX'
    
    # 修改SQL查询
    config.sql_query = """
        SELECT 
            project_name, shop_id, shop_entity_name, shop_entity_id, 
            device_id, device_type, device_state, is_deleted 
        FROM shop_devices_detail 
        WHERE 1=1
    """
    
    # 修改字段映射
    config.field_mapping = {
        'project_name': 'selectField_mdqto6fg',
        'shop_id': 'textField_mdqto6fh',
        'shop_entity_name': 'textField_mdqto6fi',
        'shop_entity_id': 'textField_mdqto6fj',
        'device_id': 'textField_mdqto6fk',
        'device_type': 'selectField_mdqto6fl',
        'device_state': 'selectField_mdqto6fn',
        'is_deleted': 'selectField_mdqto6fo'
    }
    
    # 修改键字段
    config.key_fields = ['shop_entity_id', 'device_id']
    
    # 修改比较字段
    config.compare_fields = ['shop_entity_name', 'device_type', 'device_state', 'is_deleted']
    
    # 修改日志前缀
    config.log_config['filename_prefix'] = 'device_sync'
    
    # 保存配置文件
    config.save_to_file('device_sync_config.json')
    print("设备数据同步配置已保存到: device_sync_config.json")

def run_sync_with_config(config_file):
    """使用指定配置文件运行同步"""
    print(f"使用配置文件 {config_file} 运行同步...")
    
    if not os.path.exists(config_file):
        print(f"配置文件 {config_file} 不存在")
        return
    
    try:
        # 加载配置
        config = SyncConfig(config_file)
        
        # 创建同步客户端
        sync_client = MySQL2YidaSyncClient(config)
        
        # 执行同步
        sync_client.sync_data()
        
        print("同步完成！")
        
    except Exception as e:
        print(f"同步失败: {str(e)}")

def show_config_info(config_file):
    """显示配置文件信息"""
    print(f"配置文件 {config_file} 信息:")
    
    if not os.path.exists(config_file):
        print(f"配置文件 {config_file} 不存在")
        return
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        print(f"  数据库: {config_data['db_config']['database']}")
        print(f"  表单UUID: {config_data['yida_config']['FORM_UUID']}")
        print(f"  键字段: {', '.join(config_data['key_fields'])}")
        print(f"  比较字段: {', '.join(config_data['compare_fields'])}")
        print(f"  字段映射数量: {len(config_data['field_mapping'])}")
        
    except Exception as e:
        print(f"读取配置文件失败: {str(e)}")

def main():
    """主函数"""
    print("MySQL与宜搭数据同步框架使用示例")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 创建销售数据同步配置")
        print("2. 创建设备数据同步配置")
        print("3. 运行销售数据同步")
        print("4. 运行设备数据同步")
        print("5. 显示销售配置信息")
        print("6. 显示设备配置信息")
        print("7. 自定义配置文件同步")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == '1':
            create_sales_config()
        elif choice == '2':
            create_device_config()
        elif choice == '3':
            run_sync_with_config('sales_sync_config.json')
        elif choice == '4':
            run_sync_with_config('device_sync_config.json')
        elif choice == '5':
            show_config_info('sales_sync_config.json')
        elif choice == '6':
            show_config_info('device_sync_config.json')
        elif choice == '7':
            config_file = input("请输入配置文件路径: ").strip()
            if config_file:
                run_sync_with_config(config_file)
        elif choice == '0':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
